<div class="sidebar Introduction">
  <p>The Document Counter portlet displays the number of documents according to defined criteria such as type or status. (e.g.: number of orders pending), and displays KPIs.</p>
</div>
           
<h1>Description</h1>

<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Field
	  </th>
	  <th>
		Description
	  </th>
	  <th>
		Mandatory	  
	</th>
	</tr>
  </thead>
  <tbody>
	<tr>
	 <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Title</span>
		</p>
	  </td>
	 <td>
		[@2pttitle]
	  </td>
	 <td>
		Mandatory	  
		</td>
	</tr>
	<tr>
	 <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Description</span>
		</p>
	  </td>
	 <td>
		[@2ptdesc]
	  </td>
	 <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
	 <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Dashboard title</span>
		</p>
	  </td>
	 <td>
	 [@2ptchartdash]
		
	  </td>
	 <td>
		<p>Optional</p>
	  </td>
	</tr>
  </tbody>
</table>
          
      
<h1>Configuration</h1>
              
          
            <table frame="border" rules="all" style="margin:25px;">
              <thead>
                <tr>
                  <th>
                    Field
                  </th>
                  <th>
                    Description
                  </th>
                  <th>
                    Mandatory                  
					</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">List</span>
                    </p>
                  </td>
                 <td>
				 [@2ptchartlist]
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Filter</span>
                    </p>
                  </td>
                 <td>
				 [@2ptdisfil]
                    
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Only display documents belonging to the workflow of the logged-in user</span>
                    </p>
                  </td>
                 <td>
				 [@2ptdispldocwkf]
                    
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Invoice flow direction</span>
                    </p>
                  </td>
                 <td>
                    <p>Only available if you selected <span class="guilabel" style="font-weight:bold;">List</span>: <span class="guilabel" style="font-weight:bold;">InvoiceIndex</span>.</p>
                    <p>Select the type of documents to count:</p>
                    
                      <ul>
                       <li>
                          <p><span class="guilabel" style="font-weight:bold;">Indifferent</span>: Count invoices issued and received by the contact's scope.</p>
                        </li>
                       <li>
                          <p><span class="guilabel" style="font-weight:bold;">Sending</span>: Only count invoices issued by the contact's scope.</p>
                        </li>
                       <li>
                          <p><span class="guilabel" style="font-weight:bold;">Receiving</span>: Only count invoices received by the contact's scope.</p>
                        </li>
                      </ul>
                   
                  </td>
                 <td>
                    Mandatory                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Label</span>
                    </p>
                  </td>
                 <td>
                    <p>Enter the text that will be displayed above the counter. This is a free text field.</p>
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Icon</span>
                    </p>
                  </td>
                 <td>
                    [@2pticon]
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Color</span>
                    </p>
                  </td>
                 <td>
                    <p>Select the color used to display the result. On the front-office portal, the result is shown on a green background.</p>
                    <div style="background: rgb(238, 238, 238); border: 1px solid rgb(204, 204, 204); padding: 5px 10px; margin: 5px 10px;">
						<strong>Note</strong>
						<p>You can configure the background color in the associated .css stylesheet (refer to attribute "class" tab "label"; 6 bootstrap standard colors).</p>
                          
                    </div>
                    <p>Default color: gray.</p>
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Percentage label</span>
                    </p>
                  </td>
                 <td>
                    <p>Enter the text to display next to the percentage resulting of the ratio of both queries.</p>
                    <p>Default value: of all documents.</p>
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Display percentage</span>
                    </p>
                  </td>
                 <td>
                    <p>Check the box to display the percentage bar including the percentage label.</p>
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Refresh the counter at the request of the user</span>
                    </p>
                  </td>
                 <td> 
				 [@2ptrefreshcounter]
                    
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Update time</span>
                    </p>
                  </td>
                 <td>
				 [@2ptcharttime]
                    
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Authorize the use of date range</span>
                    </p>
                  </td>
                 <td>
				 [@2ptautdaterange]
                    
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Date on which to perform the additional filter</span>
                    </p>
                  </td>
                 <td>
				 [@2ptdateaddfilter]
                    
                  </td>
                 <td>
                    <p>Mandatory, if you check <span class="guilabel" style="font-weight:bold;">Authorize the use of date range</span></p>
                  </td>
                </tr>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Apply the additional filter to the percentage calculation</span>
                    </p>
                  </td>
                 <td>
                    <p>Check the box to apply the date range to the second query used to calculate the percentage.</p>
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
              </tbody>
            </table>
          
       
        
         
<h1>Link</h1>
              
          
            <table frame="border" rules="all" style="margin:25px;">
              <thead>
                <tr>
                  <th>
                    Field
                  </th>
                  <th>
                    Description
                  </th>
                  <th>
                    Mandatory                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                 <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Portlet to open</span>
                    </p>
                  </td>
                 <td>
				 [@2pttoopen]
                    <div style="background: rgb(252, 232, 218); border: 1px solid rgb(247, 185, 145); padding: 5px 10px; margin: 5px 10px;">	
	<strong>Prerequisites</strong>
<ul>
<li>
If the user is a customer user, the number displayed on the application is identical to the one obtained by clicking on the <strong>Count</strong> button of the configuration dialog box.
</li>
<li>
If the user is a partner contact, they only see the documents that they have created or received. Consequently, the number displayed will be less than or equal to the total obtained by clicking the <strong>Count</strong> button of the configuration dialog box.
</li>
</ul>
</div>
                  </td>
                 <td>
                    <p>Optional</p>
                  </td>
                </tr>
              </tbody>
            </table>
          
        
<h1>Rights</h1>
              
          
           
            <p>
           Right available in the General tab of the right matrix.
            </p>
           
             <table frame="border" rules="all" style="margin:25px;">
                <thead>
                  <tr>
                    <th>
                      Right
                    </th>
                    <th>
                     Checkbox name
                    </th>
                    <th>
                      Description
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                   <td>
                      <p>
                        <span class="bold">
                          <strong>Miniature counter management</strong>
                        </span>
                      </p>
                    </td>
                   <td>
                      <p>Update</p>
                    </td>
                   <td>
                      <p>Access the miniature counter settings. The <span class="guilabel" style="font-weight:bold;">Settings</span> icon is located in the breadcrumbs sidebar.</p>
                    </td>
                  </tr>
                </tbody>
              </table>
           
          
        
      
   
  

