<p>In this dialog box you can add or edit a company&rsquo;s information.</p>

<h1>General</h1>

<h2>Company Details</h2>

<ul>
	<li><strong>Code:</strong> Company code. This internal and non-editable code is unique in the application. It is made up of three alphanumeric characters, which are used as a prefix in the codes of all the partners linked to the company.</li>
	<li><strong>Name:</strong> Name of the company.</li>
	<li><strong>Description: </strong>Description of the company. Free text field.</li>
</ul>

<h2>Identification</h2>

<ul>
	<li><strong>ID:</strong> Specify the ID of the company. This unique alphanumeric identification code is used for the exchanges with the company&rsquo;s partners:

	<ul>
		<li>in all the documents linked to the company,</li>
		<li>in the EDI message flow.<br />Some examples of IDs include: the company&rsquo;s VAT number (used for companies using GIS), the Global Location Number (GLN) code of the company, or the internal SAP code.<br /><strong>Warning: </strong>This field can me modified in case of mistake during its creation. It is highly recommended to refrain from modifying it afterwards.</li>
	</ul>
	</li>
	<li><strong>Government reference:</strong> French SIREN code if the company is based in France.</li>
	<li><strong>VAT code: </strong>Intra-community VAT number. This code is used for invoicing purposes. The tax identification number is provided by the tax authority after obtaining the government reference: it must be present in the invoices issued for European transactions.</li>
	<li><strong>Reference: </strong>VAT registration number in the Registrar of Companies (<i>Registre du commerce et des soci&eacute;t&eacute;s</i> in France). The reference is used for invoicing purposes. This government-issued reference allows to create the list of companies and organizations with whom the invoices are exchanged &ndash; as required by French law.</li>
</ul>

<div style="margin-left:15.0pt;">
<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note:</strong> References may vary between countries. If required, comply with current regulations.</div>
</div>

<ul>
	<li><strong>Registered company name:</strong> Specify the official or trademark name of the company.</li>
	<li><strong>Secondary ID: </strong>Specify the ID that can be used in case the ID above cannot be identified in the exchanges.</li>
</ul>

<h2>Address</h2>

<p>Address displayed in the different documents (orders, delivery slips, etc.).</p>

<ul>
	<li><strong>Address:</strong> Number and street name.</li>
	<li><strong>Postal code: </strong>Code of the city.</li>
	<li><strong>City: </strong>Name of the city.</li>
	<li><strong>Country: </strong>Country where the company is based.</li>
</ul>

<h2>Contact Information</h2>

<p>Contact information of the company&rsquo;s representative.</p>

<ul>
	<li><strong>Contact:</strong> Name of the company&rsquo;s representative.</li>
	<li><strong>Phone: </strong>Phone number of the company&rsquo;s representative.</li>
	<li><strong>Fax:</strong> Fax number of the company&rsquo;s representative.</li>
	<li><strong>Email: </strong>E-mail address of the company&rsquo;s representative.</li>
	<li><strong>Legal capital:</strong> Value of the company&#39;s outstanding equity shares.</li>
	<li><strong>Legal structure: </strong>Business structure of the company.</li>
</ul>

<h2>Archiving</h2>

<ul>
	<li><strong>Allow archiving:</strong> Check the box to allow the application to archive invoices. If you check the box, you must fill in the field below.</li>
	<li><strong>Archive owner: </strong>Specify the company&rsquo;s representative that will be used for billing on Generix&rsquo;s side.</li>
</ul>

<h1>Freetext</h1>

<p>In this tab you can create partner profiles with customized fields that to be filled in when creating a company&rsquo;s partners.<br />For each profile, you can create several customized fields. They will be displayed in the <i>Details</i> tab when adding a partner.</p>

<h3>Using this tab</h3>

<ol>
	<li>Create a profile: click the add button, specify the name of the new profile, and click the check mark button.</li>
	<li>Select a profile from the <b>Profile</b> drop-down list.<br /><strong>Note:</strong> The <i>Partner</i> profile can be edited but not deleted. If a partner is assigned a profile that is not in this list, the <i>Partner</i> profile will be assigned to them.</li>
	<li>Select the type of free text field you want to create:<br /><em>freeText:</em> Short text field. Character limit: 255.<br /><em>freeLongText:</em> Long text field. Character limit: 4000.<br /><em>freeBoolean: </em>Yes/No drop-down list.<br /><em>freeDouble:</em> Numeric field. Character limit: 255.<br /><em>freeDate:</em> Date field in dd/mm/yyyy format.</li>
	<li>Configure the field.<br /><em>Name:</em> Specify the label of the field.<br /><em>Rendered: </em>Check the box to display the field in the <i>Details</i> tab when adding a partner.<br /><em>Required: </em>Check the box to make the field mandatory.<br /><strong>Note:</strong> When importing partners with an excel file, make sure the required field is filled in. If the profile has mandatory free text fields, they must be filled in to be able to create the partner.<br /><em>Read only:</em> Check the box to make the filed non-editable after creating a partner.</li>
	<li>Click <strong>OK </strong>to save the field.</li>
	<li>Drag the fields up or down the list to order them.</li>
	<li>To delete a profile, select the profile from the drop-down list and click the <strong>Delete </strong>button.<br /><strong>Note: </strong>The <em>Partner </em>profile cannot be deleted.</li>
</ol>

<h1>Notifications</h1>

<p>In this tab you can set the frequency of notifications generated by the Doc Notification business task.</p>

<ul>
	<li><strong>Schedule:</strong> Select the frequency of notifications.</li>
</ul>

<div style="margin-left:15.0pt;">
<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note: </strong>Keep the default value (Every week from Sunday to Saturday) or deselect all days of the week to see &ldquo;every day of the week&rdquo;. Otherwise, the notifications that are generated on days that are not part of the schedule will not be sent.</div>
</div>

<h1>eDocument</h1>

<p>In this tab you can configure exchanges and the timestamp server.</p>

<h2>Exchange</h2>

<p>In this section you can authorize electronic document exchanges between a company and its partners. For the documents to be sent, the exchanges must be authorized and listed.</p>

<p>These values are mostly used during the execution of the edoc exchange controls business task, which checks for the presence of the sender and recipient in the customer&#39;s environment and ensures that both parties are authorized to exchange documents. If the exchanges are not authorized, the documents are placed in the dead queue.</p>

<p>For each company partner, you must set an exchange par type of document.</p>

<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note: </strong>The exchanges must be authorized in the other direction on the partner side.</div>

<p>To create a new exchange:</p>

<ol>
	<li>Click <strong>New exchange</strong> and select either <em>Sending </em>(the entity sends a document) or <em>Receiving </em>(the entity receives&nbsp;a document).</li>
	<li>Fill in the fields:<br /><strong>Document type:</strong> Select the type of document of the exchange.<br /><strong>Sending to/Receiving from: </strong>Select the recipient or the sender, respectively. This information will be displayed in the <strong>Recipient </strong>column.<br /><strong>Date period:</strong> Select the start date (mandatory) and the end date (optional but must be on or after the start date) of the exchange period.</li>
	<li>Click <strong>OK </strong>to finish the exchange authorization.</li>
</ol>

<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note:</strong> The <strong>Save </strong>button in the dialog box does not allow to save an exchange in process of being created</div>

<h2>Timestamp Server</h2>

<p>In this section you can set the timestamp server (generally, only from the company).</p>

<p><strong>Requirements: </strong>Create authentication certificates in the <strong>Keystores </strong>tab.</p>

<p>The values in this tab and the <strong>Keystores </strong>tab are mainly used by the following business tasks:</p>

<ul>
	<li>Doc signature and Doc check signature to send and receive documents that need signing or have been signed.</li>
	<li>Archive for the tax digitization.</li>
</ul>

<p>Fill in the fields:</p>

<ul>
	<li><strong>URL:</strong> Web address of the timestamp server. E.g.: &nbsp;<em>https://horodatage-test.certinomis.com:8443/shv2-server/timestamp</em>.</li>
	<li><strong>Keystore:</strong> Select the certificate that allows to authenticate the company on the timestamp server. The certificate must be added in the <strong>Keystores </strong>tab with a <em>TIMESTAMP_SERVER </em><strong>Use</strong>.</li>
	<li><strong>Truststore:</strong> Select the root certificate authority that allows to identify the timestamp server at the company. The certificate must be added in the <strong>Keystores </strong>tab with a <em>TIMESTAMP_SERVER</em> <strong>Use</strong>.</li>
	<li><strong>Policy OID:</strong> Specify the identifier of the timestamp generation policy.</li>
</ul>

<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note:</strong> If the<strong> Policy OID</strong> is changed by the supplier, make sure you update the timestamp server configuration.</div>

<h2>Archive Storage</h2>

<ul>
	<li><strong>Archive option:</strong> Select the platform where archives will be stored: <em>Generix </em>or <em>AWS</em>. If you select <em>None</em>, nothing will be archived.</li>
</ul>

<h1>Keystores</h1>

<p>In this tab you can import and manage electronic certificates and private keys of a company, its partners and the timestamp authority.<br />Certificates can be imported in four formats:</p>

<ul>
	<li>through<strong> JKS/PKCS#12</strong> for .jks and .p12 files,</li>
	<li>through <strong>PKCS#11</strong> (RGS-2*) for data allowing to communicate with a cryptographic facility,</li>
	<li>through <strong>PEM/CER/CRT</strong> for single (public or private) certificates, and</li>
	<li>through <strong>PKCS#7</strong> for certificate containers.</li>
</ul>

<p>Once a certificate is imported in the application, you can see all its details by clicking on it from the list.</p>

<h2>JKS/PKCS#12, PEM/CER/CRT, and PKCS#7</h2>

<ul>
	<li><strong>Common name:</strong> Specify the name of the certificate. Certificates are listed by common name. This name can be reused for the certificate that replaces it when this one expires.</li>
	<li><strong>Use:</strong> Select the use given to the certificate that will be imported:
	<ul>
		<li><em>AS2:</em> Signature certificate used for AS2 messages.</li>
		<li><em>AUTHENTICATION:</em> Certificate used for single sign on (SSO) purposes.</li>
		<li><em>SIGNATURE:</em> Certificate used to sign documents.</li>
		<li>TIMESTAMP_SERVER: Certificate used for timestamping purposes.</li>
	</ul>
	</li>
	<li><strong>Description:</strong> Specify the description of the certificate.</li>
	<li><strong>Alias:</strong> By default, the alias is automatically dilled in. When a file contains several certificates, specify the alias to identify the certificate to be used.</li>
	<li><strong>Password:</strong> Specify the password that will be assigned to the document containing the certificate. E.g.: A .jks can be created through keytool.exe from the .cer provided by the certificate authority.</li>
	<li><strong>Use period:</strong> Select the validity start date (mandatory) and the end date (optional but must be on or after the start date) of the certificate.</li>
	<li><strong>File:</strong> Click Import to select the JKS or PKCS12 certificate.</li>
</ul>

<h2>PKCS#11</h2>

<ul>
	<li><strong>Common name: </strong>Specify the name of the certificate. Certificates are listed by common name. This name can be reused for the certificate that replaces it when this one expires.</li>
	<li><strong>Use:</strong> Select the use given to the certificate that will be imported:
	<ul>
		<li><em>AS2:</em> Signature certificate used for AS2 messages.</li>
		<li><em>AUTHENTICATION:</em> Certificate used for single sign on (SSO) purposes.</li>
		<li><em>SIGNATURE:</em> Certificate used to sign documents.</li>
		<li><em>TIMESTAMP_SERVER:</em> Certificate used for timestamping purposes.</li>
	</ul>
	</li>
	<li><strong>Description:</strong> Specify the description of the certificate.</li>
	<li><strong>Slot index:</strong> Specify the PKCS#11 slot index. Default value: 0.</li>
	<li><strong>Label:</strong> Specify the name of the certificate contained in the PKCS#11.</li>
	<li><strong>Library:</strong> Specify the patch to the PKCS#11 driver delivered by the provider.</li>
	<li><strong>Password (PIN code): </strong>Specify the PIN code of the PKCS#11 user.</li>
</ul>

<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note:</strong> Certain solutions are blocked after a given number of tries.</div>

<h1>Templates</h1>

<p>In this tab you can link BIRT templates to business documents (invoices, orders, etc.) to be able to view them in the portal.</p>

<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Important:</strong> If the templates are not configured, it won&rsquo;t be possible to view the documents.</div>

<p>To add a template:</p>

<ol>
	<li>Click <strong>Add</strong>.</li>
	<li>Fill in the fields.</li>
</ol>

<ul>
	<li><strong>Document collection: </strong>Select the MongoDB collection from the drop-down list to determine what BIRT template is used to see a document. If you select a collection, do not select a document type below.</li>
	<li><strong>Document type: </strong>Select the type that uses the template. If you select a document type, do not select a document collection above.</li>
	<li><strong>Document sub-type: </strong>Select the sub-type that uses the template. If you select a document sub-type, do not select a document collection above.</li>
</ul>

<ul>
	<li><strong>Format:</strong> Select the format in which the template is displayed:

	<ul>
		<li><em>Generic format: </em>Both Excel and PDF.</li>
		<li><em>Excel: </em>To export a CSV file.</li>
		<li><em>PDF:</em> To print the document.</li>
	</ul>
	</li>
	<li><strong>Template:</strong> Select the template.</li>
</ul>

<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note: </strong>Templates must be uploaded from <strong>Repository</strong> &gt; <strong>Templates </strong>(for a company&rsquo;s environment) or <strong>System </strong>&gt; <strong>Parameters </strong>&gt; <strong>Templates </strong>(for an application instance). The former will be listed under <strong>Environment</strong>, and the latter under <strong>Global</strong>.</div>

<h1>Channels</h1>

<p>In this tab you can configure different means of data transfer: <strong>Authentication</strong>. This channel allows to configure external authentication modes.</p>

<p>To create a new channel:</p>

<ol>
	<li>Select the channel from the drop-down list.</li>
	<li>Click <strong>Add</strong>.</li>
	<li>Fill in the fields (see details after procedure).</li>
	<li>Click OK to save the configuration and return to the list of channels.</li>
	<li>To limit access to a channel to only the user who created it, enable the chain button. To allow access to a channel to the partners of the user who created it, disable the chain button. This button is reserved for Security Update and Administrator roles.</li>
	<li>To edit a channel, click the Edit button.</li>
	<li>To delete a channel, click the Delete button.</li>
</ol>

<ul>
	<li><strong>Name:</strong> Name of the channel. This field is mandatory.</li>
	<li><strong>Description:</strong> Description of the channel. This field is optional.</li>
	<li><strong>Authentication:</strong> Select the authentication method.
	<ul>
		<li><strong>ActiveDirectory:</strong> Unlike SSO, allows users to log on to the application with LDAP and by checking the application&rsquo;s security database.</li>
		<li><strong>Carrefour:</strong> Allows to authenticate customer users and partner contacts through WebServices LDAP Carrefour France.
		<ul>
			<li><strong>URL:</strong> Specify the URL of the Carrefour WS LDAP.</li>
			<li><strong>Auth. service: </strong>Select the name of the Carrefour WS used for authentication.</li>
			<li><strong>Generix application:</strong> Specify the name of the LDAP application containing the user roles.</li>
			<li><strong>BU XPath: </strong>Path to the name of the BU the user belongs to. Path applies to the XML representation of the person retrieved by LDAP.</li>
			<li><strong>BU Collection:</strong> RTE collection containing the Carrefour BU.</li>
			<li><strong>BU Name field: </strong>Name of the field in the RTE collection containing the name of the BU.</li>
			<li><strong>BU Code field:</strong> Name of the field representing the organization&rsquo;s ID code.</li>
		</ul>
		</li>
		<li><strong>LDAP: </strong>Allows to connect and find the necessary information to connect to a portal.</li>
		<li><strong>SSO: </strong>Allows users to log on to the application once with a single set of credentials using an IdP (identity provider).
		<ul>
			<li><strong>URL IDP (Identity provider): </strong>Specify the URL of the service that authenticates user identities and issues consumable security tokens.</li>
			<li><strong>Authentication certificate: </strong>Select the certificate used to authenticate the user. The certificate must be added in the Keystores tab.</li>
			<li><strong>Role Mapping</strong><br />In this section you can map a regular expression to a role. To add a mapping line, click <strong>Add</strong>.
			<ul>
				<li><strong>Name: </strong>Specify the name of the mapping line.</li>
				<li><strong>RegEx:</strong> Specify the regular expression.</li>
				<li><strong>Role:</strong> Select a role from the drop-down list. Roles must be created in <strong>Security </strong>&gt; <strong>Back Office Roles</strong>.</li>
			</ul>
			</li>
		</ul>
		</li>
	</ul>
	</li>
</ul>

<h1>Logistics</h1>

<p>In this tab you can configure the company&rsquo;s serial shipping container code (SSCC).</p>

<ul>
	<li><strong>SSCC Number</strong>

	<ul>
		<li><strong>SSCC generation:</strong> Check the box to fill in the fields and generate an SSCC.</li>
		<li><strong>GS1 company prefix:</strong> Specify the 7-to-10-digit GS1 code of the company. The combined length of the GS1 Company Prefix and Serial Reference is always 16 digits.</li>
		<li><strong>Extension:</strong> Select the extension digit of the SSCC number.</li>
		<li><strong>Serial reference:</strong> Specify the unique identifier of the shipping container. A Serial Reference cannot be reused for a minimum of 12 months. The combined length of the GS1 Company Prefix and Serial Reference is always 16 digits.</li>
		<li><strong>SSCC: </strong>Automatically generated SSCC that will be used by the company&rsquo;s partners.</li>
	</ul>
	</li>
</ul>

<h1>Banking</h1>

<p>In this tab you can see, add and delete the company&rsquo;s bank details. The banking details listed below can be added from this screen or the front office, and can be used to pay provider invoices.<br />To add a line in the list:</p>

<ol>
	<li>Click on <strong>&hellip;</strong> on the right of the table headers, and select <strong>Add</strong>.</li>
	<li>Fill in the fields.</li>
	<li>Click <strong>Save</strong>.</li>
	<li>To delete a line, click on <strong>&hellip; </strong>on the right hand side of the line, and select <strong>Delete</strong>.</li>
</ol>
