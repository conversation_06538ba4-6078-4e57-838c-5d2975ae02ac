<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets" xmlns:p="http://primefaces.org/ui" xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:pt="http://xmlns.jcp.org/jsf/passthrought"
	xmlns:b="http://byzaneo.com/ui" xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx"
	template="/resources/template/#{gnxSessionHandler.template}/default.layout.xhtml">
	<!-- TITLE -->
	<ui:define name="title">
		<p:outputPanel styleClass="psInstanceTitle">
			<gnx:instanceTitle id="instanceTitle" instance="#{gnxIntegrationISHandler.instance}" service="integration">
				<f:facet name="breadcrumb">
					<li><h:outputText value="#{xtdBusinessProcessHandler.project.name}" title="#{xtdBusinessProcessHandler.project.code}" /></li>
					<li><h:outputLink
							value="#{request.contextPath}/ui/admin/instance/integration/business-list.jsf?iid=#{cc.attrs.instance.id}&amp;pjid=#{xtdBusinessProcessHandler.project.id}">
							<h:outputText value="#{itglbls.process}" />
						</h:outputLink></li>
					<li class="active"><h:outputText value="#{xtdBusinessProcessHandler.process.name} #{xtdBusinessProcessHandler.process.userVersion}"
							rendered="#{xtdBusinessProcessHandler.process.name!=null}"/> <h:outputText value="#{itglbls.process}"
							rendered="#{xtdBusinessProcessHandler.process.name==null}" /></li>
				</f:facet>
			</gnx:instanceTitle>
		</p:outputPanel>
	</ui:define>
	<!-- SIDEBAR -->
	<ui:define name="sidebar">
		<gnx:instanceSidebar id="instanceMenu" instance="#{gnxIntegrationISHandler.instance}" service="integration" />
	</ui:define>
	<!-- CONTENT -->
	<ui:define name="content">
		<h:form id="integrationForm" styleClass="form-horizontal" pt:role="form">
			<!-- 			<p:hotkey bind="ctrl+s" actionListener="#{xtdBusinessProcessHandler.onSaveProcess}" process="@this @(.wDiagram)" -->
			<!-- 				onstart="if (#{xtdBusinessProcessHandler.process!=null and xtdBusinessProcessHandler.process.id==null}) {PF('wItgProcessSaveAsDlg').show();return false;}" -->
			<!-- 				update="-messages itgPropsPnl itgBpName" oncomplete="if (#{xtdBusinessProcessHandler.process!=null and xtdBusinessProcessHandler.process.id!=null}) {PF('wItgProcessUnlockDlg').show();}" /> -->
			<!-- * HEADER * -->
			<ui:include src="header.inc.xhtml">
				<ui:param name="project" value="#{xtdBusinessProcessHandler.project}" />
				<ui:param name="activeIndex" value="0" />
			</ui:include>
			<!-- * PROCESS * -->
			<p:outputPanel id="itgBpmPnl" styleClass="psItgBpmPnl" rendered="#{xtdBusinessProcessHandler.process!=null}">
				<!-- menu -->
				<p:menubar style="margin: 6px 0;" styleClass="itg-process-menubar" autoDisplay="false">
					<p:submenu label="#{itglbls.process}" icon="ui-icon-document">
						<!-- new -->
						<p:menuitem icon="ui-icon-document" value="#{itglbls.new}" immediate="true"
							process="@this" update="@(.psItgProcessCreateDlg)"
							oncomplete="PF('wItgProcessCreateDlg').show()"/>
							 <!-- open -->
						<p:menuitem icon="ui-icon-folder-open" value="#{itglbls.open}"
							url="business-list.jsf?iid=#{gnxIntegrationISHandler.instance.id}&amp;pjid=#{xtdBusinessProcessHandler.project.id}" />
						<!-- save -->
						<p:menuitem icon="ui-icon-disk" value="#{itglbls.save}" actionListener="#{xtdBusinessProcessHandler.onSaveProcess}"
							process="@this itgBusinessDgr" update="-messages @(.psItgBpmPnl)"
							rendered="#{xtdBusinessProcessHandler.process.id!=null and not xtdBusinessProcessHandler.readonly}" oncomplete="PF('wItgProcessUnlockDlg').show()"
							title="Ctrl+s" />
						<!-- locking -->
						<p:separator rendered="#{xtdBusinessProcessHandler.lockable or xtdBusinessProcessHandler.unlockable}" />
						<p:menuitem icon="ui-icon-locked" value="#{itglbls.lock}" actionListener="#{xtdBusinessProcessHandler.onLockProcess(gnxSessionHandler.user)}"
							process="@this" update="-messages @(.psItgBpmPnl)" rendered="#{xtdBusinessProcessHandler.lockable}" />
						<p:menuitem icon="ui-icon-unlocked" value="#{itglbls.unlock}" onclick="PF('wItgProcessUnlockDlg').show()"
							rendered="#{not xtdBusinessProcessHandler.lockable}" />
						<!-- resources -->
						<p:separator />
						<p:menuitem icon="ui-icon-folder-collapsed" value="#{itglbls.resources}..." url="#"
							disabled="#{xtdBusinessProcessHandler.process.id==null or xtdBusinessProcessHandler.readonly}"
							onclick="jQuery('.jqsOpenResourcesDialog').click()"/>
						<!-- validation -->
						<p:menuitem icon="ui-icon-check" value="#{itglbls.validate}" actionListener="#{xtdBusinessProcessHandler.onValidateProcess}"
							process="@this itgBusinessDgr" update="-messages itgProcValidPnl" oncomplete="PF('wItgProcessValidDlg').show()"
							disabled="#{xtdBusinessProcessHandler.readonly}" />
						<!-- trigger -->
						<p:separator />
						<p:menuitem icon="ui-icon-clock" value="#{itglbls.triggers}..." url="#" disabled="#{xtdBusinessProcessHandler.process.id==null}"
							onclick="jQuery('.jqsOpenTriggersDialog').click()" />
						<!-- deployment -->
						<p:separator />
						<p:menuitem icon="ui-icon-transferthick-e-w" value="#{itglbls.deployment}..." url="#"
							disabled="#{xtdBusinessProcessHandler.process.id==null or xtdBusinessProcessHandler.readonly}" onclick="jQuery('.jqsOpenDeployDialog').click()" />
						<!-- export -->
						<p:separator />
						<p:submenu label="#{itglbls.export}" icon="ui-icon-arrowthickstop-1-s" rendered="#{xtdBusinessProcessHandler.process.id!=null}">
							<p:menuitem value="#{itglbls.io_bpmn2}" actionListener="#{xtdBusinessProcessHandler.onExportXmlProcess}" process="@this itgBusinessDgr"
								update="-messages itgProcXmlPnl" oncomplete="PF('wItgProcessXptDlg').show()" />
							<p:menuitem value="#{itglbls.io_process}" 
								actionListener="#{xtdBusinessProcessHandler.prepareForExport(gnxIntegrationISHandler.instance.group.name)}"
								oncomplete="if (!args.validationFailed){jQuery('.jqsItgBpExpBtn').click()}" />
						</p:submenu>
						<!-- Properties -->
						<p:separator />
						<p:menuitem icon="fa fa-check-square icon-forget-primefaces-background-image" value="#{itglbls.properties}" url="#"
								actionListener="#{xtdBusinessProcessHandler.onSelectProcessProperties}" immediate="true"
								rendered="#{xtdBusinessProcessHandler.process.name!=null}"
								onclick="jQuery('.jqsOpenPropertiesDialog').click()" />
					</p:submenu>
					<p:submenu label="#{itglbls.edition}" icon="ui-icon-pencil" rendered="#{not xtdBusinessProcessHandler.readonly}">
						<p:menuitem icon="ui-icon-squaresmall-plus" value="#{itglbls.select_all}" url="#" onclick="PF('wDiagram').selectAll();return false;"
							title="Ctrl+a" />
						<p:separator />
						<p:menuitem icon="ui-icon-arrowreturnthick-1-w" value="#{itglbls.undo}" url="#" onclick="PF('wDiagram').undo();return false;" title="Ctrl+z" />
						<p:menuitem icon="ui-icon-arrowreturnthick-1-e" value="#{itglbls.redo}" url="#" onclick="PF('wDiagram').redo();return false;" title="Ctrl+y" />
						<p:separator />
						<p:menuitem icon="ui-icon-scissors" value="#{itglbls.cut}" url="#" onclick="PF('wDiagram').cut();return false;" title="Ctrl+x" />
						<p:menuitem icon="ui-icon-copy" value="#{itglbls.copy}" url="#" onclick="PF('wDiagram').copy();return false;" title="Ctrl+c" />
						<p:menuitem icon="ui-icon-clipboard" value="#{itglbls.paste}" url="#" onclick="PF('wDiagram').paste();return false;" title="Ctrl+v" />
						<p:separator />
						<p:menuitem icon="ui-icon-trash" value="#{itglbls.delete}" url="#" onclick="PF('wDiagram').remove();return false;" title="Delete" />
					</p:submenu>
					<p:submenu label="Navigate" icon="ui-icon-arrow-4" rendered="#{xtdBusinessProcessHandler.process!=null}">
						<p:menuitem icon="ui-icon-arrow-1-n" value="#{itglbls.page_up}" url="#" onclick="PF('wDiagram').movePageUp();return false;" />
						<p:menuitem icon="ui-icon-arrow-1-s" value="#{itglbls.page_down}" url="#" onclick="PF('wDiagram').movePageDown();return false;" />
						<p:menuitem icon="ui-icon-arrow-1-w" value="#{itglbls.page_left}" url="#" onclick="PF('wDiagram').movePageLeft();return false;" />
						<p:menuitem icon="ui-icon-arrow-1-e" value="#{itglbls.page_right}" url="#" onclick="PF('wDiagram').movePageRight();return false;" />
					</p:submenu>
					<p:submenu label="View" icon="ui-icon-search" rendered="#{xtdBusinessProcessHandler.process!=null}">
						<p:menuitem icon="ui-icon-zoomout" value="#{itglbls.zoom_out}" url="#" onclick="PF('wDiagram').zoomOut();return false;" />
						<p:menuitem icon="ui-icon-zoomin" value="#{itglbls.zoom_in}" url="#" onclick="PF('wDiagram').zoomIn();return false;" />
						<p:menuitem icon="ui-icon-cancel" value="#{itglbls.zoom_reset}" url="#" onclick="PF('wDiagram').zoomReset();return false;" />
						<p:menuitem icon="ui-icon-arrow-4-diag" value="#{itglbls.zoom_fit}" url="#" onclick="PF('wDiagram').zoomFit();return false;" />
					</p:submenu>
					<f:facet name="options">
						<h:outputText id="itgBpName" value="#{xtdBusinessProcessHandler.process.nameAndUserVersion}" styleClass="psItgBpName bold"
							style="line-height: 25px;" />
						<h:outputText id="prcLocked" styleClass="ui-icon ui-icon-locked" style="display:inline-block"
							rendered="#{xtdBusinessProcessHandler.process.locked}" />
						<p:tooltip for="prcLocked">
							<h:outputFormat value=" #{itglbls.locked_by} ">
								<f:param value="#{xtdBusinessProcessHandler.process.lockUser.fullnameOrLogin}" />
							</h:outputFormat>
							<h:outputText value="#{xtdBusinessProcessHandler.process.lockDate}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium" timeStyle="short" timeZone="#{secSessionHandler.timeZone}" />
							</h:outputText>
						</p:tooltip>
						<h:outputText styleClass="ui-icon ui-icon-unlocked" style="display:inline-block" rendered="#{not xtdBusinessProcessHandler.process.locked}"
							title="#{itglbls.unlocked}" />
						<h:panelGroup layout="block" style="width:1px;height:1px;visibility:hidden;">
							<p:spacer height="100%" width="5px" />

							<p:commandButton icon="ui-icon-disk" title="#{itglbls.save}" actionListener="#{xtdBusinessProcessHandler.onSaveProcess}"
								process="@this itgBusinessDgr" update="-messages @(.psItgBpmPnl)" oncomplete="PF('wItgProcessUnlockDlg').show()"
								rendered="#{xtdBusinessProcessHandler.process!=null and not empty xtdBusinessProcessHandler.process.id and not xtdBusinessProcessHandler.readonly}"
								widgetVar="wJqsSaveButton" styleClass="jQsSaveButton" />
						</h:panelGroup>

						<p:hotkey bind="ctrl+s" handler="$('.jQsSaveAsButton').click();" />
						<p:hotkey bind="ctrl+s" handler="$('.jQsSaveButton').click();" />
					</f:facet>
				</p:menubar>
				<!-- context menu -->
				<p:contextMenu for="itgBusinessDgr" rendered="#{not xtdBusinessProcessHandler.readonly}">
					<p:menuitem icon="ui-icon-pencil" value="#{itglbls.edit}" url="#" onclick="PF('wDiagram').selectNode();return false;" />
					<p:separator />
					<p:menuitem icon="ui-icon-check" value="#{itglbls.validate}" actionListener="#{xtdBusinessProcessHandler.onValidateProcess}"
						process="@this itgBusinessDgr" update="-messages itgProcValidPnl" disabled="#{xtdBusinessProcessHandler.process==null}"
						oncomplete="PF('wItgProcessValidDlg').show()" />
					<p:separator />
					<p:menuitem icon="ui-icon-squaresmall-plus" value="#{itglbls.select_all}" url="#" onclick="PF('wDiagram').selectAll();return false;"
						title="Ctrl+a" />
					<p:separator />
					<p:menuitem icon="ui-icon-arrowreturnthick-1-w" value="#{itglbls.undo}" url="#" onclick="PF('wDiagram').undo();return false;" title="Ctrl+z" />
					<p:menuitem icon="ui-icon-arrowreturnthick-1-e" value="#{itglbls.redo}" url="#" onclick="PF('wDiagram').redo();return false;" title="Ctrl+y" />
					<p:separator />
					<p:menuitem icon="ui-icon-scissors" value="#{itglbls.cut}" url="#" onclick="PF('wDiagram').cut();return false;" title="Ctrl+x" />
					<p:menuitem icon="ui-icon-copy" value="#{itglbls.copy}" url="#" onclick="PF('wDiagram').copy();return false;" title="Ctrl+c" />
					<p:menuitem icon="ui-icon-clipboard" value="#{itglbls.paste}" url="#" onclick="PF('wDiagram').paste();return false;" title="Ctrl+v" />
					<p:separator />
					<p:menuitem icon="ui-icon-trash" value="#{itglbls.delete}" url="#" onclick="PF('wDiagram').remove();return false;"
						disabled="#{xtdBusinessProcessHandler.readonly}" title="Delete" />
				</p:contextMenu>
				<!-- layout -->
				<pe:layout fullPage="false" style="width:100%;height:700px;" options="#{xtdBusinessProcessHandler.layoutOptions}"
					state="#{xtdBusinessProcessHandler.layoutState}" rendered="#{xtdBusinessProcessHandler.process!=null}">
					<pe:layoutPane id="itgProcCLyt" position="center" styleContent="overflow:hidden;">
						<b:diagram id="itgBusinessDgr" definition="Business" value="#{xtdBusinessProcessHandler.process.diagramModel}"
							converter="businessModelConverter" immediate="true" forOverview="#{xtdBusinessProcessHandler.readonly ? null : 'itgBusinessOvw'}"
							widgetVar="wDiagram" style="border:none;height:688px;" readonly="#{xtdBusinessProcessHandler.readonly}">
							<p:ajax event="selectNodeDbl" listener="#{xtdBusinessProcessHandler.onSelectProcessNode}" immediate="true" process="@this itgBusinessDgr"
								update="@none" oncomplete="jQuery('.jqsOpenNodeTaskDialog').click()" />
							<p:ajax event="selectLinkDbl" listener="#{xtdBusinessProcessHandler.onSelectProcessLink}" immediate="true" process="@this"
								update="itgProcEditPnl" oncomplete="PF('wItgProcEditDlg').show()" />
							<p:ajax event="selectBackgroundDbl" listener="#{xtdBusinessProcessHandler.onSelectProcessProperties}" immediate="true" process="@this"
								update="@none" oncomplete="jQuery('.jqsOpenPropertiesDialog').click()" />
						</b:diagram>
					</pe:layoutPane>
					<pe:layoutPane id="itgProcELyt" position="east" rendered="#{not xtdBusinessProcessHandler.readonly}">
						<pe:layoutPane id="itgProcENLyt" position="north" styleContent="overflow:hidden;">
							<b:diagramOverview id="itgBusinessOvw" style="height:138px;" />
						</pe:layoutPane>
						<pe:layoutPane id="itgProcECLyt" position="center" styleContent="overflow:hidden;background-color:#FAFAFA;">
							<b:diagram id="diagShapePlt" definition="Business" palette="shapes" widgetVar="wPaletteShape" style="height:138px;" />
						</pe:layoutPane>
						<pe:layoutPane id="itgProcESLyt" position="south" styleContent="overflow:auto;background-color:#FAFAFA;">
							<b:diagram id="diagTypePlt" definition="Business" palette="types" value="#{xtdBusinessProcessHandler.taskTypeModel}"
								converter="taskTypeModelConverter" widgetVar="wPaletteType" style="height:488px;" />
						</pe:layoutPane>
					</pe:layoutPane>
				</pe:layout>
			</p:outputPanel>
			<!-- * PROCESS NOT FOUND * -->
			<p:outputPanel styleClass="psItgBpmPnl bg-danger" style="margin:20px auto;" rendered="#{xtdBusinessProcessHandler.process==null}">
				<h:outputText value="#{itglbls.process_not_found}"/>
			</p:outputPanel>
			<!-- PROCESS: EDITON DIALOG -->
			<p:dialog id="itgProcEditDlg"
				header="#{itglbls.process_property} : #{xtdBusinessProcessHandler.process.name} #{xtdBusinessProcessHandler.process.userVersion}" modal="true"
				width="800" style="min-height:300px;" widgetVar="wItgProcEditDlg" dynamic="true" closable="true" styleClass="psItgProcEditDlg">
				<p:outputPanel id="itgProcEditPnl" styleClass="psItgSelectedPnl">
					<!-- LINK -->
					<p:outputPanel id="itgLinkPnl" rendered="#{xtdBusinessProcessHandler.link!=null}">
						<!-- header -->
						<p:panelGrid style="margin-bottom:10px;">
							<p:row>
								<p:column>
									<p:inplace styleClass="dialog-title" editor="true" emptyLabel="#{itglbls.link}" disabled="#{xtdBusinessProcessHandler.readonly}">
										<p:inputText value="#{xtdBusinessProcessHandler.link.label}" validator="xssValidator" style="width:auto;font-size:medium;" required="false" label="#{labels.name}" />
									</p:inplace>
								</p:column>
								<p:column styleClass="right">
									<p:commandButton icon="ui-icon-disk" value="#{labels.save}" actionListener="#{xtdBusinessProcessHandler.onSaveProcessLink}"
										process="itgLinkPnl" update="-messages itgBpmPnl" oncomplete="handleCloseDialog(xhr, status, args, PF('wItgProcEditDlg'))"
										rendered="#{not xtdBusinessProcessHandler.readonly}" />
								</p:column>
							</p:row>
						</p:panelGrid>
						<!-- content -->
						<p:outputPanel styleClass="form-group">
							<p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.disable}" for="itgLinkDefaultFlowIpt"/>
							<p:outputPanel styleClass="col-sm-9">
								<p:selectBooleanCheckbox id="itgLinkDefaultFlowIpt" value="#{xtdBusinessProcessHandler.link.defaultFlow}"
														 converter="processExpressionConverter" itemLabel="#{itglbls.default_flow}"
														 styleClass="form-control" />
							</p:outputPanel>
						</p:outputPanel>
						<p:outputPanel styleClass="form-group">
							<p:outputLabel styleClass="col-sm-3 control-label" value="#{itglbls.condition}" for="itgLinkConditionIpt" />
							<p:outputPanel styleClass="col-sm-9">
								<p:inputTextarea id="itgLinkConditionIpt" styleClass="form-control" value="#{xtdBusinessProcessHandler.link.condition}" validator="xssValidator" rows="5"/>
							</p:outputPanel>
						</p:outputPanel>
					</p:outputPanel>
				</p:outputPanel>
			</p:dialog>
			<!-- PROCESS: VALIDATION -->
			<p:dialog id="itgProcessValidDlg" header="#{itglbls.validation_process}" modal="true" width="600" widgetVar="wItgProcessValidDlg">
				<p:outputPanel id="itgProcValidPnl">
					<gnx:logs id="itgProcValidLogs" title="#{xtdBusinessProcessHandler.logs.status=='OK' ? itglbls.validation_ok : itglbls.validation_ko}"
						value="#{xtdBusinessProcessHandler.logs}" renderDuration="false" renderStatus="false" rendered="#{xtdBusinessProcessHandler.logs!=null}" />
				</p:outputPanel>
			</p:dialog>
			<!-- PROCESS: EXPORT - BPMN -->
			<p:dialog id="itgProcessXptDlg" header="#{itglbls.process} - Export BPMN" modal="true" width="900" height="600" widgetVar="wItgProcessXptDlg">
				<p:outputPanel id="itgProcXmlPnl" styleClass="psItgProcXmlPnl">
					<h:panelGrid columns="2" columnClasses="left,right">
						<h:outputText value="#{xtdBusinessProcessHandler.process.name}" styleClass="title1" />
						<p:commandButton value="#{labels.download}" ajax="false" icon="ui-icon-arrowthickstop-1-s" rendered="#{not empty xtdBusinessProcessHandler.xml}">
							<p:fileDownload value="#{xtdBusinessProcessHandler.xmlStream}" />
						</p:commandButton>
					</h:panelGrid>
					<p:messages rendered="#{empty xtdBusinessProcessHandler.xml}" />
					<pe:codeMirror value="#{xtdBusinessProcessHandler.xml}" mode="xml" readOnly="true" rendered="#{not empty xtdBusinessProcessHandler.xml}" />
				</p:outputPanel>
			</p:dialog>
			<!-- PROCESS: CREATE DIALOG -->
			<p:dialog id="itgProcessCreateDlg" header="#{itglbls.new}" modal="true" widgetVar="wItgProcessCreateDlg" dynamic="true" width="400" styleClass="psItgProcessCreateDlg">
				<f:facet name="footer">
					<p:commandButton icon="ui-icon-disk" value="#{labels.create}" actionListener="#{xtdBusinessProcessHandler.onCreateProcess}"
					    update="-messages @(.psItgBpmPnl) @(.psInstanceTitle)" oncomplete="PF('wItgProcessCreateDlg').hide()"
					    process="@this itgProcessCreatePnl">
						<f:attribute name="createFrom" value="fromProcess"/>
					 </p:commandButton>
					<p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" type="button" onclick="PF('wItgProcessCreateDlg').hide()" styleClass="mls" />
				</f:facet>
				<h:panelGroup id="itgProcessCreatePnl">
					<p:outputPanel styleClass="form-group">
						<p:outputLabel styleClass="col-sm-2 control-label" value="#{labels.name}" for="itgBPNameIpt" />
						<p:outputPanel styleClass="col-sm-10">
							<p:inputText id="itgBPNameIpt" value="#{xtdBusinessProcessHandler.newProcessName}" validator="xssValidator" required="true" label="#{labels.name}" styleClass="form-control"/>
						</p:outputPanel>
					</p:outputPanel>

					<p:outputPanel styleClass="form-group">
						<p:outputLabel styleClass="col-sm-2 control-label" for="itgBPVersionIpt" value="#{itglbls.version}" />
						<p:outputPanel styleClass="col-sm-10">
							<p:inputText id="itgBPVersionIpt" value="#{xtdBusinessProcessHandler.newProcessVersion}" validator="xssValidator" required="true" label="#{itglbls.version}" styleClass="form-control"/>
						</p:outputPanel>
					</p:outputPanel>
				</h:panelGroup>
			</p:dialog>
			<!-- PROCESS: UNLOCK DIALOG -->
			<p:confirmDialog id="itgProcessUnlockDlg" header="#{itglbls.unlock}" message="#{itglbls.unlock_process_confim}" modal="true" width="400"
				widgetVar="wItgProcessUnlockDlg" dynamic="true" styleClass="ui-confirm-dialog-noicon" closable="false">
				<h:panelGrid columnClasses="right">
					<h:panelGroup>
						<p:commandButton value="#{labels.accept}" styleClass="mls" icon="ui-icon-check" actionListener="#{xtdBusinessProcessHandler.onUnlockProcess}"
							process="@this" update="-messages itgBpmPnl" oncomplete="PF('wItgProcessUnlockDlg').hide()" />
						<p:commandButton value="#{labels.cancel}" type="button" styleClass="mls" icon="ui-icon-close" onclick="PF('wItgProcessUnlockDlg').hide()" />
					</h:panelGroup>
				</h:panelGrid>
			</p:confirmDialog>
			<!-- HIDDEN ACTIONS -->
			<h:panelGroup layout="block" style="width:1px;height:1px;visibility:hidden;">
				<!-- TASK DIALOG -->
				<p:commandButton value=" " immediate="true" process="@this" update="@none" actionListener="#{xtdBusinessProcessHandler.onOpenDialogProcessNode}" styleClass="jqsOpenNodeTaskDialog">
					<p:ajax event="dialogReturn" listener="#{xtdBusinessProcessHandler.onSaveProcessNode}" process="@this" update="@(.psItgBpmPnl)" />
				</p:commandButton>
				<!-- DEPLOY DIALOG -->
				<p:commandButton id="itgBpDeploy" value=" " immediate="true" process="@this" update="@none"
					actionListener="#{xtdBusinessProcessHandler.onOpenDeployments}"
					oncomplete="reloadOnClose()"
					styleClass="jqsOpenDeployDialog" />
				<!-- TRIGGERS DIALOG -->
				<p:commandButton id="itgBpTriggers" value=" " immediate="true" process="@this" update="@none"
					actionListener="#{xtdBusinessProcessHandler.onOpenTriggers}"
					oncomplete="reloadOnClose()"
					styleClass="jqsOpenTriggersDialog" />
				<!-- RESOURCES DIALOG -->
				<p:commandButton id="itgBpResources" value=" " immediate="true" process="@this" update="@none"
					actionListener="#{gnxIntegrationISHandler.onOpenResources}"
					oncomplete="reloadOnClose()"
					styleClass="jqsOpenResourcesDialog" />
				<!-- PROCESS: EXPORT -->
				<p:commandButton id="itgBpExpBtn" value=" " ajax="false" styleClass="jqsItgBpExpBtn">
					<p:fileDownload value="#{xtdBusinessProcessHandler.exportedArchive}" />
				</p:commandButton>
				<!-- PROCESS: RELOAD -->
				<p:commandButton id="itgBpReloadBtn" value=" "
					actionListener="#{xtdBusinessProcessHandler.onReloadProcess(false)}"  process="@this" update="@(.psItgBpmPnl)"
					styleClass="jqsItgBpReloadBtn"/>
				<!-- PROPERTIES DIALOG -->
				<p:commandButton id="itgBpProperties" value=" " immediate="true" process="@this" update="@none"
					actionListener="#{xtdBusinessProcessHandler.onOpenProperties}" styleClass="jqsOpenPropertiesDialog">
					<p:ajax event="dialogReturn" listener="#{xtdBusinessProcessHandler.onSaveProcessDialogReturn}" process="@this" update="-messages itgBpName @(.psInstanceTitle)" />
				</p:commandButton>
			</h:panelGroup>
		</h:form>
<script type="text/javascript" >
function reloadOnClose() {
	$('A.ui-dialog-titlebar-close').bind("click", function(){ $('.jqsItgBpReloadBtn').click(); });
}
</script>
	</ui:define>
</ui:composition>