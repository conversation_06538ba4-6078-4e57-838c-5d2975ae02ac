<!--
 | column: the column holding the portlets
 | header: is header column
 | colidx: index of the column
 | colfirst: is first column
 | collast: is last column
 -->
<p:dataList xmlns="http://www.w3.org/1999/xhtml"
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:b="http://byzaneo.com/ui"
	xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx"
	id="portlets" value="#{column.portlets}" var="portlet" varStatus="portletStatus" type="definition">
	<p:panel id="grlColPanel" widgetVar="wGrlColPanel" styleClass="portal-portlet-panel" style="margin-bottom: #{portletStatus.last ? 0 : 15}px;">
		<!-- MOVING -->
		<!-- move: west -->
		<p:commandButton icon="fa fa-angle-left" title="#{labels.move_left}" type="button"
			onclick="movePortlet([{name:'layoutIndex', value:#{layoutIndex}}, {name:'col', value:#{colidx}},{name:'row', value:#{portletStatus.index}},{name:'dir', value:'west'}])"
			styleClass="direction direction-west"
			rendered="#{not colfirst}" />
		<!-- move: east -->
		<p:commandButton icon="fa fa-angle-right" title="#{labels.move_rigth}" type="button"
			onclick="movePortlet([{name:'layoutIndex', value:#{layoutIndex}}, {name:'col', value:#{colidx}},{name:'row', value:#{portletStatus.index}},{name:'dir', value:'east'}])"
			styleClass="direction direction-east"
			rendered="#{not collast}" />
		<!-- move: north -->
		<p:commandButton icon="fa fa-angle-up" title="#{labels.move_up}" type="button"
			onclick="movePortlet([{name:'layoutIndex', value:#{layoutIndex}}, {name:'col', value:#{colidx}},{name:'row', value:#{portletStatus.index}},{name:'dir', value:'#{portletStatus.first ? 'header' : 'north'}'}])"
			styleClass="direction direction-north"
			rendered="#{not portletStatus.first}" />
		<!-- move: south -->
		<p:commandButton icon="fa fa-angle-down" title="#{labels.move_down}" type="button"
			onclick="movePortlet([{name:'layoutIndex', value:#{layoutIndex}}, {name:'col', value:#{colidx}},{name:'row', value:#{portletStatus.index}},{name:'dir', value:'#{portletStatus.last ? 'columns' : 'south'}'}])"
			styleClass="direction direction-south"
			rendered="#{header or not portletStatus.last}" />
		<!-- configuration -->
		<p:commandButton id="ptlColConfBtn" title="#{labels.configuration_portlet}" icon="fa fa-wrench" type="button" styleClass="config"/>
		<p:overlayPanel id="ptlColConf" for="ptlColConfBtn" styleClass="left" style="min-width: 250px;right:0;" my="right top">
			<h:panelGrid portletumns="1" styleClass="right">
				<h:panelGroup>
					<!-- clear -->
					<p:commandButton icon="fa fa-eraser" value="#{labels.clear}"
						disabled="#{portlet.contentId==null}"
						actionListener="#{gnxPortalISHandler.onRemovePortletContent(portlet)}"
						immediate="true" process="@this" update="@(.psPpgColumnsLayoutPnl)"
						onclick="if ( !confirm('#{labels.clear_portlet_confirm}') ) { return false; }" />
					<!-- removes -->
					<p:commandButton icon="fa fa-trash-o" value="#{comlbls.delete}" type="button" styleClass="mls"
						onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; } else { deletePortlet([{name:'layoutIndex', value:#{layoutIndex}}, {name:'col', value:#{colidx}},{name:'row', value:#{portletStatus.index}}]); }" />
					<!-- copy -->
					<p:commandButton icon="fa fa-files-o" value="#{labels.copy}"
						rendered="#{portlet.contentId!=null and portlet.content!=null}"
						actionListener="#{gnxPortalISHandler.onCopyPortlet(portlet)}"
						immediate="true" process="@this" update="@(.psPpgColumnsLayoutPnl)"
						styleClass="mls"/>
				</h:panelGroup>
			</h:panelGrid>
			<!-- styles -->
			<p:outputPanel styleClass="form-group">
				<p:outputLabel styleClass="portlet-sm-4 control-label" value="#{labels.style}" for="pltStyleIpt" />
				<p:outputPanel styleClass="portlet-sm-8"><p:inputText id="pltStyleIpt" value="#{portlet.style}" validator="xssValidator" styleClass="form-control" /></p:outputPanel>
			</p:outputPanel>
			<p:outputPanel styleClass="form-group">
				<p:outputLabel styleClass="portlet-sm-4 control-label" value="#{labels.styleClass}" for="pltStyleClassIpt" />
				<p:outputPanel styleClass="portlet-sm-8"><p:inputText id="pltStyleClassIpt" value="#{portlet.styleClass}" validator="xssValidator" styleClass="form-control" /></p:outputPanel>
			</p:outputPanel>
		</p:overlayPanel>
		<!-- CONTENT -->
		<p:outputPanel style="margin-top:50px;" styleClass="center">
			<!-- CREATE -->
			<p:outputPanel rendered="#{portlet.contentId==null and portlet.content==null}">
				<h:panelGrid columns="3" style="width:auto;margin: 0 auto;">
					<b:selectManyMenu id="pltPortletTypeSlc"
						widgetVar="wPltPortletTypeSlc"
						value="#{gnxPortalISHandler.type}"
						escape="true"
						multiple="false"
						enableFiltering="false"
						enableCaseInsensitiveFiltering="false"
						includeSelectAllOption="false"
						enableClickableOptGroups="false"
						enableCollapsibleOptGroups="true"
						converter="taskTypeConverter"
						nonSelectedText="#{labels.portlet}"
						label="#{labels.type}"
						required="true"
						buttonWidth="150">
						<f:selectItems value="#{gnxPortalISHandler.getTypeItems(tskTypeHandler.getTypeItems('portal', labels, gnxPortalISHandler.editMode.includedTaskRegex))}" />
					</b:selectManyMenu>
					<p:commandButton icon="fa fa-gear" process="@this @parent" update="@none" title="#{labels.edit}"
						actionListener="#{gnxPortalISHandler.onCreatePortletContent(portlet)}" styleClass="mls"
						style="height:2.3em">
						<p:ajax event="dialogReturn" listener="#{gnxPortalISHandler.onEditedPortletContent}" process="@this" update="@(.psPpgColumnsLayoutPnl)" />
					</p:commandButton>
					<p:commandButton icon="fa fa-clipboard" process="@this @parent" update="@(.psPpgColumnsLayoutPnl)" title="#{labels.paste}"
						rendered="#{gnxPortalISHandler.portletToCopy!=null and gnxPortalISHandler.portletToCopy.contentId!=null and gnxPortalISHandler.portletToCopy.content!=null}"
						actionListener="#{gnxPortalISHandler.onPastePortlet(portlet)}" styleClass="mls"
						style="height:2.3em">
					</p:commandButton>
				</h:panelGrid>
			</p:outputPanel>
			<!-- EDIT -->
			<p:outputPanel rendered="#{portlet.contentId!=null and portlet.content!=null}" styleClass="center">
				<p:commandLink id="portletEditLnk" title="#{labels.edit}" actionListener="#{gnxPortalISHandler.onEditPortletContent(portlet)}"
					process="@this" update="@none" styleClass="title2">
					<p:ajax event="dialogReturn" listener="#{gnxPortalISHandler.onEditedPortletContent}" process="@this" update="@(.psPpgColumnsLayoutPnl)" />
					<h:outputText value="#{gnxHandler.label(portlet.content.title, gnxPortalISHandler.defaultLanguage)}" />
				</p:commandLink>
				<p:tooltip for="portletEditLnk" style="white-space:pre-wrap;">
					<h:outputText value="#{gnxHandler.label(portlet.content.description, gnxPortalISHandler.defaultLanguage)}" />
				</p:tooltip>
			</p:outputPanel>
			<!-- NOT FOUND -->
			<p:outputPanel rendered="#{portlet.contentId!=null and portlet.content==null}" styleClass="center">
				<h:panelGroup layout="block" style="width:auto;margin: 0 auto;display:inline-block;">
					<h:panelGroup layout="block" styleClass="ui-messages-error-icon" style="margin:5px 10px;" />
					<h:outputText value="Error while loading the portlet content" style="font-size:1.5em;font-weight:bold;" />
				</h:panelGroup>
			</p:outputPanel>
		</p:outputPanel>
		<!-- FOOTER -->
		<h:panelGroup style="position:absolute;bottom:-4px;right:4px;">
			<h:outputLabel value="#{portlet.content!=null ? portlet.content.type : labels.portlet}" style="font-size:0.7em;color:#aaa;" />
		</h:panelGroup>
	</p:panel>
</p:dataList>