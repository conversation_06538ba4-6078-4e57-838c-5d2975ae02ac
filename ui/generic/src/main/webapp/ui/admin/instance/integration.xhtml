<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets" xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
                xmlns:p="http://primefaces.org/ui"
                xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx"
                template="/resources/template/#{gnxSessionHandler.template}/default.layout.xhtml">
	<!-- TITLE -->
	<ui:define name="title">
		<gnx:instanceTitle id="instanceTitle" instance="#{gnxIntegrationISHandler.instance}" service="integration" />
	</ui:define>
	<!-- SIDEBAR -->
	<ui:define name="sidebar">
		<gnx:instanceSidebar id="instanceMenu" instance="#{gnxIntegrationISHandler.instance}" service="integration" />
	</ui:define>
	<!-- CONTENT -->
	<ui:define name="content">
		<h:form id="integrationForm" pt:role="form" styleClass="form-horizontal">
			<input type="hidden" name="iid" id="iid" value="#{gnxIntegrationISHandler.instance.id}" />
            <p:outputPanel>
                <p:panelGrid>
                    <p:row>
                        <p:column styleClass="right">
                            <p:inputText id="processFilter"
                                         onkeyup="if (event.keyCode == 13 ) { callFilteredProcesses(); return false; }"
                                         pt:placeholder="#{labels.search_by_process}"
                                         value="#{gnxIntegrationISHandler.processFilterValue}"
                                         validator="xssValidator"
                                         styleClass="#{gnxIntegrationISHandler.processFilterValue==null ? 'searchProcessFilterStyleInitial': 'searchProcessFilterStyle'}"/>
                            <p:remoteCommand name="callFilteredProcesses"
                                             actionListener="#{gnxIntegrationISHandler.getAllProcessesForAnEnvironment()}"
                                             process="@this processFilter"
                                             update="@(.psItgBpmsPnl) @(.psWorkspacePanel) @(.refreshButtonStyle) processFilter"
                                             global="true"/>
                            <p:outputPanel styleClass="refreshButtonStyle">
                                <p:commandButton id="refreshButton" icon="ui-icon-refresh" value="#{labels.refresh_button}"
                                                 process="@this" style="width:180px"
                                                 update="@(.psItgBpmsPnl) @(.psWorkspacePanel) @(.refreshButtonStyle) processFilter"
                                                 rendered="#{gnxIntegrationISHandler.processFilterValue!=null}"
                                                 actionListener="#{gnxIntegrationISHandler.reset()}"/>
                            </p:outputPanel>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </p:outputPanel>
            <p:outputPanel styleClass="psWorkspacePanel">
				<!-- ** PROJECT LIST ** -->
                <p:dataTable id="projectTable" var="project" value="#{gnxIntegrationISHandler.getWorkspaceProjects()}"
                             rows="50"
                             paginator="true"
                             paginatorPosition="bottom" paginatorAlwaysVisible="true" rowKey="#{project.id}"
                             selection="#{gnxIntegrationISHandler.project}"
                             selectionMode="single" sortBy="#{project.code}" sortOrder="ascending"
                             filteredValue="#{gnxIntegrationISHandler.filtered}" widgetVar="wProjectTable"
                             styleClass="datatable-hide-filters" emptyMessage="#{labels.no_records_found}"
                             rendered="#{gnxIntegrationISHandler.processFilterValue==null}">
                    <p:ajax event="rowSelect" immediate="true" process="@none" update="@none" oncomplete="goToProject()" />
                    <f:facet name="header">
                        <p:panelGrid>
                            <p:row>
                                <p:column styleClass="left">
                                    <!-- <h:outputText value="#{labels.integration}" styleClass="title1"/> -->
                                </p:column>
                                <p:column styleClass="right">
                                    <p:inputText id="globalFilter"
                                                 onkeyup="PF('wProjectTable').filter()"
                                                 style="width:150px" value="#{gnxIntegrationISHandler.projectFilterValue}"
                                                 validator="xssValidator"
                                                 pt:placeholder="#{labels.search_by_project}"/>
                                    <p:defaultCommand target="globalFilter"/>
                                    <p:commandButton icon="ui-icon-plusthick" value="#{comlbls.add}"
                                                     actionListener="#{gnxIntegrationISHandler.onAddProject}" immediate="true"
                                                     process="@this" update="@(.psItgProjectDlg)" oncomplete="PF('wItgProjectDlg').show()"
                                                     styleClass="mls"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </f:facet>
                    <p:column headerText="#{labels.code}" sortBy="#{project.code}" filterBy="#{project.code}" style="width:15%;"
                              styleClass="bold">
                        <h:outputText value="#{project.code}"/>
                    </p:column>
                    <p:column headerText="#{labels.name}" sortBy="#{project.name}" filterBy="#{project.name}" style="width:15%;">
                        <h:outputText value="#{project.name}"/>
                    </p:column>
                    <p:column headerText="#{labels.description}" sortBy="#{project.description}" filterBy="#{project.description}"
                              filterMatchMode="contains">
                        <h:outputText value="#{project.description}" style="white-space:pre-wrap;"/>
                    </p:column>
                    <p:column headerText="#{labels.creation}" sortBy="#{project.creationDate}" styleClass="center" style="width:10%;">
                        <h:outputText value="#{project.creationDate}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date" dateStyle="medium"/>
                        </h:outputText>
                    </p:column>
                    <p:column headerText="#{labels.modification}" sortBy="#{project.modificationDate}" styleClass="center"
                              style="width:10%;">
                        <h:outputText value="#{project.modificationDate}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date" dateStyle="medium"/>
                        </h:outputText>
                    </p:column>
                    <p:column style="width:60px;">
                        <p:commandButton icon="ui-icon-pencil" title="#{comlbls.edit}" immediate="true" process="@this"
                                         update="@(.psItgProjectDlg)"
                                         oncomplete="PF('wItgProjectDlg').show()" styleClass="mls">
                            <f:setPropertyActionListener target="#{gnxIntegrationISHandler.project}" value="#{project}"/>
                        </p:commandButton>
                    </p:column>
                </p:dataTable>
            </p:outputPanel>
            <p:outputPanel id="itgBpmsPnl" styleClass="psItgBpmsPnl">
                <p:dataTable id="itgProcessTbl" value="#{gnxIntegrationISHandler.getAllProcessesForAnEnvironment()}"
                             var="process"
                             rows="20" paginator="true" paginatorPosition="bottom" rowKey="#{process.id}"
                             selectionMode="single" sortBy="#{process.name}" sortOrder="ascending"
                             widgetVar="wItgProcessTbl" rendered="#{gnxIntegrationISHandler.processFilterValue!=null}"
                             styleClass="datatable-hide-filters filterProcess"
                             rowStyleClass="#{process.locked and !process.isLockedBy(secSessionHandler.user) ? 'grey' : null}"
                             emptyMessage="#{labels.no_records_found}">
                    <p:ajax event="rowSelect" process="@none" update="@none" oncomplete="goToProcess()"/>
                    <p:column sortBy="#{process.locked}" styleClass="center" style="width:40px;">
                        <h:outputText id="prcLocked" styleClass="ui-icon ui-icon-locked" style="display:inline-block"
                                      rendered="#{process.locked}"/>
                        <p:tooltip for="prcLocked">
                            <h:outputFormat value=" #{itglbls.locked_by} ">
                                <f:param value="#{process.lockUser.fullnameOrLogin}"/>
                            </h:outputFormat>
                            <h:outputText value="#{process.lockDate}">
                                <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium"
                                                   timeStyle="short" timeZone="#{secSessionHandler.timeZone}"/>
                            </h:outputText>
                        </p:tooltip>
                        <h:outputText styleClass="ui-icon ui-icon-unlocked" style="display:inline-block"
                                      rendered="#{not process.locked}" title="#{itglbls.unlocked}"/>
                    </p:column>
                    <p:column headerText="#{labels.name}" sortBy="#{process.name}" filterBy="#{process.name}"
                              styleClass="bold">
                        <h:outputText value="#{process.name}"/>
                    </p:column>
                    <p:column headerText="#{itglbls.version}" sortBy="#{process.userVersion}"
                              filterBy="#{process.userVersion}" styleClass="center" style="width:15%;">
                        <h:outputText value="#{process.userVersion}"/>
                    </p:column>
                    <p:column headerText="#{labels.creation}" sortBy="#{process.creationDate}"
                              styleClass="center nowrap" style="width:10%;">
                        <h:outputText value="#{process.creationDate}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium"
                                               timeStyle="short" timeZone="#{secSessionHandler.timeZone}"/>
                        </h:outputText>
                    </p:column>
                    <p:column headerText="#{labels.modification}" sortBy="#{process.modificationDate}"
                              styleClass="center nowrap" style="width:10%;">
                        <h:outputText value="#{process.modificationDate}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium"
                                               timeStyle="short" timeZone="#{secSessionHandler.timeZone}"/>
                        </h:outputText>
                    </p:column>
                    <p:column style="width:40px;" styleClass="right">
                        <p:commandButton icon="ui-icon-trash" title="#{labels.delete}"
                                         action="#{gnxBusinessProcessHandler.setProcess(process)}"
                                         process="@this" update="@(.psItgProcessRmvDlg)"
                                         oncomplete="PF('wItgProcessRmvDlg').show()"
                                         disabled="#{process.locked and !process.isLockedBy(secSessionHandler.user)}"
                                         style="height:22px;width:22px;">
                        </p:commandButton>
                    </p:column>
                </p:dataTable>

            </p:outputPanel>
            <!-- PROJECT DIALOG  -->
            <p:dialog id="itgProjectDlg" styleClass="psItgProjectDlg"
                      header="#{gnxIntegrationISHandler.project.code!= null ? itglbls.edit_project : itglbls.add_project}" modal="true"
                      width="600" widgetVar="wItgProjectDlg" dynamic="true">
                <f:facet name="footer">
                    <p:commandButton icon="ui-icon-disk" value="#{labels.save}" actionListener="#{gnxIntegrationISHandler.onSaveProject}" process="@this itgProjectPnl"
                        update="-messages itgProjectPnl projectTable" oncomplete="handleCloseDialog(xhr, status, args, PF('wItgProjectDlg'))" />
                    <p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" type="button" onclick="PF('wItgProjectDlg').hide()" styleClass="mls" />
                </f:facet>
                <p:outputPanel id="itgProjectPnl" styleClass="psItgProjectPnl" rendered="#{gnxIntegrationISHandler.project!=null}">
                    <p:outputPanel styleClass="form-group">
                        <p:outputLabel styleClass="col-sm-2 control-label" for="itgProjectCodeIpt" value="#{labels.code}" />
                        <p:outputPanel styleClass="col-md-10">
                            <p:inputText id="itgProjectCodeIpt" value="#{gnxIntegrationISHandler.project.code}" required="true" label="#{labels.code}"
                                onkeyup="this.value = this.value.toUpperCase()" styleClass="form-control" disabled="#{gnxIntegrationISHandler.project.id!=null}"
                                readonly="#{gnxIntegrationISHandler.project.id!=null}" validatorMessage="#{itglbls.validation_code_project}" validator="xssValidator" >
                                <f:validateRegex pattern="[A-Z0-9_]{3,16}"  />
                            </p:inputText>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:outputPanel styleClass="form-group">
                        <p:outputLabel styleClass="col-sm-2 control-label" for="itgProjectNameIpt" value="#{labels.name}" />
                        <p:outputPanel styleClass="col-md-10">
                            <p:inputText id="itgProjectNameIpt" value="#{gnxIntegrationISHandler.project.name}" validator="xssValidator" required="true" styleClass="form-control" />
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:outputPanel styleClass="form-group">
                        <p:outputLabel styleClass="col-sm-2 control-label" for="itgProjectDescIpt" value="#{labels.description}" />
                        <p:outputPanel styleClass="col-md-10">
                            <p:inputTextarea id="itgProjectDescIpt" value="#{gnxIntegrationISHandler.project.description}" validator="xssValidator"  label="#{labels.description}" rows="3"
                                styleClass="form-control" style="resize: none;max-height:200px;" />
                        </p:outputPanel>
                    </p:outputPanel>
                </p:outputPanel>
            </p:dialog>
            <script type="text/javascript">
                function goToProject() {
                    var pjid = PF('wProjectTable').selection[0];
                    if (pjid == null)
                        return;
                    PF('wProjectTable').unselectAllRows();
                    window.location.href = 'integration/business-list.jsf?iid=#{gnxIntegrationISHandler.instance.id}&amp;pjid='
                            + pjid;
                    return false;
                }

                function goToProcess() {
                    var pcid = PF('wItgProcessTbl').selection[0];
                    if (pcid == null) return;
                    PF('wItgProcessTbl').unselectAllRows();
                    window.location.href = 'integration/business.jsf?iid=#{gnxIntegrationISHandler.instance.id}&amp;pcid=' + pcid;
                    return false;
                }
            </script>
		</h:form>
	</ui:define>
</ui:composition>