<p:outputPanel xmlns="http://www.w3.org/1999/xhtml"
               xmlns:h="http://xmlns.jcp.org/jsf/html"
               xmlns:f="http://xmlns.jcp.org/jsf/core"
               xmlns:p="http://primefaces.org/ui"
               xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
               xmlns:b="http://byzaneo.com/ui"
               class="toolbar">

    <div class="inner">
        <div class="tools">

            <!-- Partner User-->
            <p:outputPanel styleClass="account" rendered="#{gnxSessionHandler.partnerUser}">
                <!-- User Icon -->
                <h:outputLink rendered="#{gnxSessionHandler.isGranted('Menu_User_Information', 'READ')}"
                              value="#{request.contextPath}/ui/customer/profile.jsf?uid=#{secSessionHandler.uid}"/>
                <h:outputLink rendered="#{not gnxSessionHandler.isGranted('Menu_User_Information', 'READ')}"
                              value="#"/>
                <i class="fa fa-user"></i>
                <ul class="toolbar-dropdown #{!gnxSessionHandler.isGranted('Menu_Basket', 'READ')?'no-cart':''}">
                    <!-- User Profile -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_User_Information', 'READ')}">
                        <li role="menuitem">
                            <a href="#{request.contextPath}/ui/customer/profile.jsf?uid=#{secSessionHandler.uid}">
                                <i class="fa fa-id-card"></i>
                                <span class="menuUserText">#{secSessionHandler.userFullname}</span>
                            </a>
                        </li>
                    </p:menuitem>
                    <!-- Company Profile -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_Company_Information', 'READ')}">
                        <li role="menuitem">
                            <a href="#{request.contextPath}/ui/customer/company.jsf">
                                <i class="fa fa-home"></i>
                                <span class="menuUserText">#{gnxSessionHandler.user.primaryGroup.fullname}</span>
                            </a>
                        </li>
                    </p:menuitem>
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_User_Information', 'READ') or gnxSessionHandler.isGranted('Menu_Company_Information', 'READ')}"> <!--Check rendering option to have a clean style-->
                        <li class="sub-menu-separator"/>
                    </p:menuitem>
                    <!-- My users -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_Rights_Users', 'READ')}">
	                    <li role="menuitem">
	                          <a href="#{request.contextPath}/ui/customer/clientUsers.jsf">
	                              <i class="fa fa-users"></i>
	                              <span class="menuUserText">#{labels.clientUsers}</span>
	                          </a>
	                    </li>
                    </p:menuitem>
                    <!-- Notifications -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Communication', 'CREATE')}">
                        <li role="menuitem">
                            <a href="#{request.contextPath}/ui/notifications.jsf">
                                <i class="fa fa-bullhorn"></i>
                                <span class="menuUserText">#{labels.communication}</span>
                            </a>
                        </li>
                    </p:menuitem>
                    <!-- Process -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Integration','READ') or gnxSessionHandler.isGranted('Menu_Process_Deployment','READ')
						or gnxSessionHandler.isGranted('Menu_Process_Trigger','READ') or gnxSessionHandler.isGranted('Menu_Process_Execution','READ')}">
                    <li role="menuitem">
                        <a href="#{request.contextPath}/ui/reports.jsf">
                            <i class="fa fa-share-alt"></i>
                            <span class="menuUserText">#{labels.monitoring}</span>
                        </a>
                    </li>
                    </p:menuitem>
                    <!-- Campaign -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Campaigns', 'READ')}">
                    <li role="menuitem">
                        <a href="#{request.contextPath}/ui/campaign.jsf">
                            <i class="fa fa-wifi"></i>
                            <span class="menuUserText">#{labels.deployment}</span>
                        </a>
                    </li>
                    </p:menuitem>
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_Rights_Users', 'READ') or gnxSessionHandler.isGranted('Integration', 'READ') or gnxSessionHandler.isGranted('Campaigns', 'READ')}">
                    	<li class="sub-menu-separator"/>
                    </p:menuitem>
                    <p:menuitem rendered="#{secSessionHandler.user!=null and gnxSessionHandler.switchedUser==null and gnxSessionHandler.isGranted('Portlet_Switch_User', 'EXECUTE')}">
	                    <li role="menuitem">
				        	<a href="javascript:void(0);" onclick="PF('wHdaSwitchUserDlg').show()">
				        		<i class="fa fa-retweet fa-lg"/>
		                        <span class="menuUserText">#{labels.switch_user}</span>
				        	</a>
			        	</li>
		        	</p:menuitem>
                    <!-- Logout -->
                    <li role="menuitem" class="#{gnxSessionHandler.switchedUser == null ? '': 'disabled-link'}">
                        <a href="#{gnxSessionHandler.logoutUrl}">
                            <i class="fa fa-lock lockIcon"></i>
                            <span class="menuUserText">#{labels.logout}</span>
                        </a>
                    </li>
                </ul>
            </p:outputPanel>

            <!-- Client User -->
            <p:outputPanel styleClass="account" rendered="#{gnxSessionHandler.companyUser}">
                <a href="#{request.contextPath}/ui/customer/profile.jsf?uid=#{secSessionHandler.uid}"/>
                <i class="fa fa-user"></i>
                <ul class="toolbar-dropdown #{!gnxSessionHandler.isGranted('Menu_Basket', 'READ')?'no-cart':''}">
                    <!-- User Profile -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_User_Information', 'READ')}">
	                    <li role="menuitem">
	                        <a href="#{request.contextPath}/ui/customer/profile.jsf?uid=#{secSessionHandler.uid}">
	                            <i class="fa fa-id-card"></i>
	                            <span class="menuUserText">#{secSessionHandler.userFullname}</span>
	                        </a>
	                    </li>
                    </p:menuitem>
                    <!-- Company Profile -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_Company_Information', 'READ')}">
                        <li role="menuitem">
                            <a href="#{request.contextPath}/ui/customer/company.jsf">
                                <i class="fa fa-home"></i>
                                <span class="menuUserText">#{gnxSessionHandler.user.primaryGroup.fullname}</span>
                            </a>
                        </li>
                    </p:menuitem>
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_User_Information', 'READ') or gnxSessionHandler.isGranted('Menu_Company_Information', 'READ')}">
                    	<li class="sub-menu-separator"/>
                    </p:menuitem>
                    <!-- Partners List -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_Rights_Clients', 'READ')}">
	                    <li role="menuitem">
	                        <a href="#{request.contextPath}/ui/customer/clientPartners.jsf">
	                            <i class="fa fa-users"></i>
	                            <span class="menuUserText">#{gnxSessionHandler.formatWithClientParam(labels.clientPartners,false)}</span>
	                        </a>
	                    </li>
                    </p:menuitem>
                    <!-- Notifications -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Communication', 'CREATE')}">
                        <li role="menuitem">
                            <a href="#{request.contextPath}/ui/notifications.jsf">
                                <i class="fa fa-bullhorn"></i>
                                <span class="menuUserText">#{labels.communication}</span>
                            </a>
                        </li>
                    </p:menuitem>
                    <!-- Process -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Integration','READ') or gnxSessionHandler.isGranted('Menu_Process_Deployment','READ')
						or gnxSessionHandler.isGranted('Menu_Process_Trigger','READ') or gnxSessionHandler.isGranted('Menu_Process_Execution','READ')}">
                    <li role="menuitem">
                        <a href="#{request.contextPath}/ui/reports.jsf">
                            <i class="fa fa-share-alt"></i>
                            <span class="menuUserText">#{labels.monitoring}</span>
                        </a>
                    </li>
                    </p:menuitem>
                    <!-- Campaign -->
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Campaigns', 'READ')}">
                    <li role="menuitem">
                        <a href="#{request.contextPath}/ui/campaign.jsf">
                            <i class="fa fa-wifi"></i>
                            <span class="menuUserText">#{labels.deployment}</span>
                        </a>
                    </li>
                    </p:menuitem>
                    <p:menuitem rendered="#{gnxSessionHandler.isGranted('Menu_Rights_Clients', 'READ') or (gnxSessionHandler.companyUser and  gnxSessionHandler.isGranted('Communication', 'READ')) or gnxSessionHandler.isGranted('Integration', 'READ') or gnxSessionHandler.isGranted('Campaigns', 'READ')}">
                    	<li class="sub-menu-separator"/>
                    </p:menuitem>
                    <p:menuitem rendered="#{secSessionHandler.user!=null and gnxSessionHandler.switchedUser==null and (empty gnxSessionHandler.getUserRoles() or gnxSessionHandler.isGranted('Portlet_Switch_User', 'EXECUTE'))}">
			        	<li role="menuitem">
				        	<a href="javascript:void(0);" onclick="PF('wHdaSwitchUserDlg').show()">
				        		<i class="fa fa-retweet fa-lg"/>
		                        <span class="menuUserText">#{labels.switch_user}</span>
				        	</a>
			        	</li>
		        	</p:menuitem>
                    <!-- Logout -->
                    <li role="menuitem" class="#{gnxSessionHandler.switchedUser == null ? '': 'disabled-link'}">
                        <a href="#{gnxSessionHandler.logoutUrl}">
                            <i class="fa fa-lock lockIcon"></i>
                            <span class="menuUserText">#{labels.logout}</span>
                        </a>
                    </li>
                </ul>
            </p:outputPanel>

            <!--  MESSAGING + UNREAD MAIL COUNTER -->
            <p:outputPanel id="messaging" styleClass="account ps-messaging" rendered="#{gnxSessionHandler.companyUser and gnxSessionHandler.isGranted('Messaging', 'READ')}">
                <a href="#{request.contextPath}/ui/mails.jsf"/>
                <i class="fa fa-envelope "></i>
                <span class="count">#{gnxMessagingISHandler.getCounterOfAllNoneProcessMails()}</span>
            </p:outputPanel>

            <!-- CART -->
            <p:outputPanel styleClass="cart" rendered="#{gnxSessionHandler.isGranted('Menu_Basket', 'READ')}">
                <h:outputText styleClass="fa fa-shopping-bag"/>

                <h:outputText rendered="#{gnxSessionHandler.isPartnerUser()}" styleClass="count" value="#{gnxCartHandler.getNumberOfArticles()}"/>
                <h:outputText rendered="#{gnxSessionHandler.isPartnerUser()}" styleClass="subtotal" value="#{'€'.concat(gnxCartHandler.getTotalAmount())}" />

                <h:outputText rendered="#{gnxSessionHandler.isCompanyUser()}" styleClass="count" value="#{gnxCartHandler.getNumberOfActiveCarts()}"/>
                <h:outputText rendered="#{gnxSessionHandler.isCompanyUser()}" styleClass="subtotal" value="#{'€'.concat(gnxCartHandler.totalCartsAmount)}" />

                <p:outputPanel styleClass="toolbar-dropdown cartDetails" rendered="#{gnxCartHandler.resolveLorealOrderEditionTask() != null}">
                    <p:outputPanel styleClass="toolbar-dropdown-group">
                        <!-- NUMBER OF ARTICLES -->
                        <p:outputPanel styleClass="column" rendered="#{gnxSessionHandler.isPartnerUser()}">
                            <h:outputText styleClass="text-lg" value="#{gnxCartHandler.getNumberOfArticles().toString().concat(' ').concat(labels.cart_numberOfArticles)}"/>
                        </p:outputPanel>
                        <p:outputPanel styleClass="column" rendered="#{gnxSessionHandler.isCompanyUser()}">
                            <h:outputText styleClass="text-lg" value="#{gnxCartHandler.getNumberOfActiveCarts().toString().concat(' ').concat(labels.active_carts)}"/>
                        </p:outputPanel>
                        <!-- TOTAL AMOUNT -->
                        <p:outputPanel styleClass="column text-left" rendered="#{gnxSessionHandler.isPartnerUser()}">
                            <h:outputText styleClass="text-lg text-medium" value="#{labels.cart_totalAmount.concat('€').concat(gnxCartHandler.getTotalAmount())}"/>
                        </p:outputPanel>
                        <p:outputPanel styleClass="column text-left" rendered="#{gnxSessionHandler.isCompanyUser()}">
                            <h:outputText styleClass="text-lg text-medium" value="#{labels.total_carts.concat('€').concat(gnxCartHandler.totalCartsAmount)}"/>
                        </p:outputPanel>
                    </p:outputPanel>
                    <!-- CART VALIDITY DATE -->
                    <p:outputPanel rendered="#{gnxSessionHandler.isPartnerUser() and gnxCartHandler.isCartActive()}" styleClass="toolbar-dropdown-group">
                    	<h:outputText styleClass="text-lg" value="#{labels.validity_date.concat(' ')}" />
                        <h:outputText styleClass="text-lg" rendered="#{gnxCartHandler.isCartActive()}" value="#{gnxCartHandler.getValidityDate()}" >
                             <f:convertDateTime locale="#{gnxSessionHandler.locale}" timeZone="#{secSessionHandler.timeZone}" pattern="#{gnxSessionHandler.userDateFormat}" />
                        </h:outputText>
                    </p:outputPanel>
                    <!-- CART BUTTONS -->
                    <p:outputPanel styleClass="toolbar-dropdown-group" rendered="#{gnxSessionHandler.isPartnerUser() and gnxCartHandler.isCartActive()}">
                        <p:outputPanel styleClass="column">
                            <p:commandLink styleClass="btn btn-sm btn-block btn-secondary" oncomplete="$('.redirectionBtn').click();"
                                           value="#{labels.cart_view}" actionListener="#{gnxCartHandler.goToStep2()}">
                            </p:commandLink>
                        </p:outputPanel>
                        <p:outputPanel styleClass="column">
                            <p:commandLink styleClass="btn btn-sm btn-block btn-success" oncomplete="$('.redirectionBtn').click();"
                                           value="#{labels.cart_checkout}" actionListener="#{gnxCartHandler.goToStep3()}">
                            </p:commandLink>
                        </p:outputPanel>
                    </p:outputPanel>
                    <!-- hidden button for directing to step1 / step2 / step3 -->
                    <p:button value="#{lrlxcblordvdtlbls.button_follow_order}"
                              style="display:none;"
                              styleClass="redirectionBtn"
                              target="_parent" outcome="/ui/portal">
                              <f:param name="page" value="#{gnxCartHandler.goToNextPortlet(gnxPortalSessionHandler.portal)}" />
                    </p:button>
                    <!-- hidden button for updating the cart (when saving the cart for example) -->
                    <p:commandButton id="cartRefreshBtn" update="@(.cart)" style="display:none;" />
                </p:outputPanel>
            </p:outputPanel>
        </div>
    </div>
            <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Maven+Pro:400,500,700,900" />
    <!-- SWITCH USER DIALOG -->
		<p:dialog id="hdaSwitchUserDlg"
			header="#{labels.switch_user}" widgetVar="wHdaSwitchUserDlg"
			styleClass="responsive-dialog"
            rendered="#{secSessionHandler.user!=null and gnxSessionHandler.switchedUser==null and gnxSessionHandler.isGranted('Portlet_Switch_User', 'EXECUTE')}"
			dynamic="true" modal="true" closable="false" width="400"
			onShow="registerConfirmDialogHotkeys(PF('wHdaSwitchUserDlg'));"
			appendTo="@(body)">
			<h:form id="fformtoto" styleClass="form-horizontal">
			<p:outputPanel id="swuFrm">
				<p:outputPanel styleClass="row" rendered="#{gnxSessionHandler.instance==null}">
	            	<p:outputPanel styleClass="col-sm-6 margin-top-1x">
                               <p:outputLabel for="swuInstance" value="#{labels.instance}"/>
                               <p:selectOneMenu id="swuInstance" value="#{gnxSessionHandler.switchInstance}" required="true" converter="instanceConverter">
								<p:ajax process="@this" update="swuUser" />
								<f:selectItem itemValue="#{null}" itemLabel=""/>
								<f:selectItems value="#{gnxInstancesHandler.instances}" var="instance" itemValue="#{instance}" itemLabel="#{instance.code} - #{gnxHandler.label(instance.name, gnxSessionHandler.locale, instance.getConfiguration().getDefaultLanguage() )}" />
							</p:selectOneMenu>
                               <b:message for="swuInstance"/>
	            	</p:outputPanel>
		        </p:outputPanel>
		        <p:outputPanel styleClass="col-sm-6 margin-top-1x">
                       <p:outputLabel for="swuUser" value="#{labels.user}"/>
                       <p:autoComplete id="swuUser" value="#{gnxSessionHandler.switchUser}" converter="userConverter"
								completeMethod="#{gnxSessionHandler.onCompleteUser}" var="user" itemLabel="#{gnxSessionHandler.getUserLabel(user)}" itemValue="#{user}"
								forceSelection="true" required="true" disabled="#{gnxSessionHandler.switchInstance==null}">
								  <p:ajax event="query" oncomplete="autoCompleteList_posFix()" />
								</p:autoComplete>
                                <b:message for="swuUser"/>
		       </p:outputPanel>
		     </p:outputPanel>
			 <p:outputPanel>
	             	<p:separator class="mt-4 mb-3"/>
                    <p:outputPanel styleClass="text-right responsive-buttons">
	             		<p:commandButton icon="ui-icon-check" value="OK" styleClass="btn btn-primary"
						process="@this swuFrm" update="swuFrm" action="#{gnxSessionHandler.onSwitchOnUser}"
						oncomplete="handleCloseDialog(xhr, status, args, PF('wHdaSwitchUserDlg'))" />
						<p:commandButton icon="ui-icon-cancel" value="#{comlbls.cancel}" type="button" onclick="PF('wHdaSwitchUserDlg').hide()" styleClass="btn btn-secondary" />
	             	</p:outputPanel>
				</p:outputPanel>
			</h:form>
		</p:dialog>
</p:outputPanel>