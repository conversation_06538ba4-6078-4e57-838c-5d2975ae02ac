package com.byzaneo.portal.test.layout;

import static org.junit.jupiter.api.Assertions.*;

import com.byzaneo.portal.bean.Portlet;

import org.junit.jupiter.api.Test;
import com.byzaneo.portal.layout.columns.Column;
import com.byzaneo.portal.layout.columns.Column.Device;
import com.byzaneo.portal.layout.columns.ColumnsLayout;
import com.byzaneo.portal.layout.columns.ColumnsLayout.Direction;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Aug 29, 2016
 * @since 9.0.0
 */
class ColumnsLayoutTest {

  /**
   * Test method for {@link com.byzaneo.portal.layout.columns.ColumnsLayout#isHeaded()}.
   */
  @Test
  void isHeaded() {
    ColumnsLayout layout = new ColumnsLayout(Device.sm, 3, 3, 3, 3);
    assertFalse(layout.isHeaded());
    layout.setHeader(new Column(Device.lg, 1));
    assertFalse(layout.isHeaded());
    layout.getHeader()
        .getPortlets()
        .add(new Portlet());
    assertTrue(layout.isHeaded());
  }

  /**
   * Test method for
   * {@link com.byzaneo.portal.layout.columns.ColumnsLayout#addColumn(com.byzaneo.portal.layout.columns.Column.Device, int)}.
   */
  @Test
  void addColumn() {
    ColumnsLayout layout = new ColumnsLayout(Device.md, 5, 3);
    assertEquals(2, layout.getColumnCount());
    layout.addColumn(Device.xs, 3);
    assertEquals(3, layout.getColumnCount());
  }

  /**
   * Test method for {@link com.byzaneo.portal.layout.columns.ColumnsLayout#addPortlet(int, int, boolean)}.
   */
  @Test
  void addPortlet() {
    ColumnsLayout layout = new ColumnsLayout(Device.md, 6, 6);
    assertEquals(2, layout.getColumnCount());
    layout.addPortlet(0, -1, false);
    assertFalse(layout.getColumns()
        .get(0)
        .isEmpty());
    layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .setName("A");
    layout.addPortlet(0, 0, true);
    assertEquals(2, layout.getColumns()
        .get(0)
        .getPortlets()
        .size());
    assertNull(layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .getName());
    layout.addPortlet(1, 0, true);
    assertFalse(layout.getColumns()
        .get(1)
        .isEmpty());
  }

  /**
   * Test method for {@link com.byzaneo.portal.layout.columns.ColumnsLayout#removePortlet(int, int)}.
   */
  @Test
  void removePortlet() {
    ColumnsLayout layout = new ColumnsLayout(Device.md, 6, 6);
    assertEquals(2, layout.getColumnCount());
    layout.addPortlet(0, -1, false);
    assertFalse(layout.getColumns()
        .get(0)
        .isEmpty());
    layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .setName("A");
    layout.addPortlet(0, 0, false);
    layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .setName("B");
    assertEquals(2, layout.getColumns()
        .get(0)
        .getPortlets()
        .size());
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    assertEquals("B", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .getName());
    layout.removePortlet(0, 1);
    assertEquals(1, layout.getColumns()
        .get(0)
        .getPortlets()
        .size());
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    layout.removePortlet(0, 0);
    assertTrue(layout.getColumns()
        .get(0)
        .isEmpty());

  }

  /**
   * Test method for
   * {@link com.byzaneo.portal.layout.columns.ColumnsLayout#movePortlet(int, int, com.byzaneo.portal.layout.columns.ColumnsLayout.Direction)}.
   */
  @Test
  void movePortlet() {
    ColumnsLayout layout = new ColumnsLayout(Device.md, 6, 6);
    assertEquals(2, layout.getColumnCount());
    layout.addPortlet(0, -1, false);
    assertFalse(layout.getColumns()
        .get(0)
        .isEmpty());
    layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .setName("A");
    layout.addPortlet(0, 0, false);
    layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .setName("B");
    assertEquals(2, layout.getColumns()
        .get(0)
        .getPortlets()
        .size());
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    assertEquals("B", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .getName());

    layout.movePortlet(0, 0, Direction.east);
    assertEquals("A", layout.getColumns()
        .get(1)
        .getPortlets()
        .get(0)
        .getName());

    layout.movePortlet(0, 1, Direction.west);
    assertEquals("B", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .getName());

    layout.movePortlet(1, 0, Direction.north);
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    assertEquals("B", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .getName());

    layout.movePortlet(0, 0, Direction.south);
    assertEquals("B", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(0)
        .getName());
    assertEquals("A", layout.getColumns()
        .get(0)
        .getPortlets()
        .get(1)
        .getName());

    layout.movePortlet(0, 0, Direction.header);
    assertTrue(layout.isHeaded());
    assertEquals("B", layout.getHeader()
        .getPortlets()
        .get(0)
        .getName());
  }

}
