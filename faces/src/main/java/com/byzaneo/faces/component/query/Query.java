/*
 * Copyright 2011-2012 PrimeFaces Extensions.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * $Id$
 */

package com.byzaneo.faces.component.query;

import static com.byzaneo.faces.util.FacesHelper.resolveLocale;
import static java.util.Arrays.asList;
import static java.util.Collections.unmodifiableCollection;
import static org.primefaces.util.Constants.RequestParams.PARTIAL_BEHAVIOR_EVENT_PARAM;

import java.util.*;

import javax.el.MethodExpression;
import javax.faces.application.*;
import javax.faces.component.UINamingContainer;
import javax.faces.component.behavior.ClientBehaviorHolder;
import javax.faces.component.html.HtmlInputTextarea;
import javax.faces.context.FacesContext;
import javax.faces.event.*;

import org.primefaces.component.api.Widget;
import org.primefaces.event.SelectEvent;

import com.byzaneo.faces.event.CompleteEvent;
import com.byzaneo.faces.model.query.QueryModel;

/**
 * Component class for the <code>Query</code> component.
 *
 * <AUTHOR> Lemesle <<EMAIL>>
 * @company Byzaneo
 * @date Aug 7, 2014
 * @since 1.1 FCS-27
 */
@ResourceDependencies({
    @ResourceDependency(library = "primefaces", name = "jquery/jquery.js"),
    @ResourceDependency(library = "primefaces", name = "jquery/jquery-plugins.js"),
    @ResourceDependency(library = "primefaces", name = "primefaces.js")
    // , @ResourceDependency(library = "bootstrap", name = "js/bootstrap.min.js")
    // , @ResourceDependency(library = "bootstrap", name = "css/bootstrap.min.css")
    // Advanced Mode
    , @ResourceDependency(library = "byzaneo", name = "lib/jquery/jquery.aop.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/jquery/jquery.selection.min.js")
    // Basic Mode
    , @ResourceDependency(library = "byzaneo", name = "lib/multiselect/bootstrap-multiselect.min.css"),
    @ResourceDependency(library = "byzaneo", name = "lib/multiselect/bootstrap-multiselect.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/moment/moment.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/moment/moment-timezone.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/daterangepicker/daterangepicker.min.css"),
    @ResourceDependency(library = "byzaneo", name = "lib/daterangepicker/daterangepicker.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/touchspin/touchspin.min.css"),
    @ResourceDependency(library = "byzaneo", name = "lib/touchspin/touchspin.min.js"),
    @ResourceDependency(library = "byzaneo", name = "lib/lodash/lodash.min.js")
    // Production
    , @ResourceDependency(library = "byzaneo", name = "byzaneo.css"), @ResourceDependency(library = "byzaneo", name = "byzaneo.js")
    // Development
    // , @ResourceDependency(library = "byzaneo", name = "core/core.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/01-namespace/01-namespace.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/01-grammar.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/02-parser.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/03-operatorUtil.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/04-util.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/05-token.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/06-myParser.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/07-autocomplete.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/08-bqlAutocomplete.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/09-dropdown.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/02-bql/10-dropdownAutocomplete.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/03-mode/01-mode.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/03-mode/02-basicMode.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/03-mode/03-advancedMode.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/03-mode/04-quickSearchOnlyMode.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/01-field.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/02-dateField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/03-multiselectField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/04-inputField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/05-autocompleteField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/06-booleanField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/07-integerField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/09-moreField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/10-choiceField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/11-dateFieldMultiple.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/12-searchField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/04-field/13-doubleField.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/05-layout/01-templates.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/05-layout/02-layout.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/05-layout/03-templateExample.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/query.js"),
    // @ResourceDependency(library = "byzaneo", name = "query/06-css/01-autocomplete.css"),
    // @ResourceDependency(library = "byzaneo", name = "query/06-css/02-query.css"),
    // @ResourceDependency(library = "byzaneo", name = "query/07-helper/ComponentHelper.js")

})
public class Query extends HtmlInputTextarea implements ClientBehaviorHolder, Widget {

  public static final String TYPE = "com.byzaneo.faces.component.Query";
  public static final String FAMILY = "com.byzaneo.faces.component";
  private static final String DEFAULT_RENDERER = "com.byzaneo.faces.component.QueryRenderer";

  public static final String EVENT_CHANGE = "change";
  public static final String EVENT_HIGHLIGHT_COMPLETE = "highlightComplete";
  public static final String EVENT_BLUR = "blur";
  public static final String EVENT_FOCUS = "focus";
  public static final String EVENT_SELECT_FIELD = "selectField";
  public static final String EVENT_CLEAR_FIELD = "clearField";
  public static final String EVENT_SUBMIT = "submit";
  public static final String EVENT_ERROR = "error";
  public static final String EVENT_TOGGLE = "toggle";
  public static final String EVENT_ENTER_KEY_PRESS = "enterKeyPressed";
  public static final String EVENT_CLICK = "click";

  public static final String STYLE_CLASS = "search-entry advanced-search ajs-dirty-warning-exempt";

  /* available mode */
  public enum Mode {
    advanced,
    basic,
    quickSearchOnly;
  }

  /* display orientation of fields */
  public enum Layout {
    horizontal,
    vertical;
  }

  private static final Collection<String> EVENT_NAMES = unmodifiableCollection(asList(
      EVENT_CHANGE,
      EVENT_HIGHLIGHT_COMPLETE,
      EVENT_BLUR,
      EVENT_FOCUS,
      EVENT_SELECT_FIELD,
      EVENT_CLEAR_FIELD,
      EVENT_SUBMIT,
      EVENT_ERROR,
      EVENT_TOGGLE,
      EVENT_ENTER_KEY_PRESS,
      EVENT_CLICK));

  /**
   * Properties that are tracked by state saving.
   */
  protected enum PropertyKeys {
    widgetVar,
    /**
     * This disables editing of the editor content by the user. If the special value "nocursor" is given (instead of simply true), focusing
     * of the editor is also disallowed.
     */
    readOnly,
    /** Method providing suggestions. */
    completeMethod,
    /** Defines if the content of the component should be escaped or not. */
    escape,
    /** Defines if the suggestions should be escaped or not. */
    escapeSuggestions,
    /** Model containing bql suggestions. */
    model,
    /** Help icon rendering option. */
    helpRendered,
    /** Help icon link. */
    helpLink,
    /** Component mode. */
    mode,
    /** Possibility of changing mode. */
    switchMode,
    /** Component layout. */
    layout,
    /** Component template style. */
    template,
    /** Locale to be used for the default components labels value. */
    locale,
    /** Default locale to be used */
    defaultLocale,
    /** Advanced mode label. */
    advancedModeLabel,
    /** Basic mode label. */
    basicModeLabel,
    /** Number of fields displayed when the component startup. */
    minDisplayedFields,
    /** Fields displayed when the component startup. */
    displayedFields,
    /** Switch mode button style. */
    buttonStyle,
    /** Switch mode button ID. */
    switchButtonId,
    /** DateFormat pattern. */
    patternDate,
    /** Time Zone. */
    timeZone,
    /** Use DST Savings. */
    useDaylightTime,
    /** Show the more button. */
    moreButton,
    /** class of the container where the more button is added */
    moreContainerClass,
    /** Show the search button. */
    searchButton,
    /** Button search icon. */
    searchButtonIcon,
    /** Button search label. */
    searchButtonLabel,
    /** Add a field for the text search. */
    textSearch,
    /** Text search label. */
    textSearchLabel,
    /** time zone Id (ex: 'Europe/Paris') */
    timeZoneID;

    private final String toString;

    private PropertyKeys() {
      this(null);
    }

    private PropertyKeys(final String toString) {
      this.toString = toString;
    }

    @Override
    public String toString() {
      return (this.toString != null) ? this.toString : super.toString();
    }
  }

  private List<Object> selectedFields;

  private Locale calculatedLocale;
  private Locale calculatedDefaultLocale;
  private Collection<String> suggestions = null;

  /* -- CONSTRUCTOR -- */

  public Query() {
    setRendererType(DEFAULT_RENDERER);
  }

  /* -- IMPLEMENTATION -- */

  @Override
  public Collection<String> getEventNames() {
    return EVENT_NAMES;
  }

  @Override
  public String getFamily() {
    return FAMILY;
  }

  /* -- ATTRIBUTES -- */

  public String getWidgetVar() {
    return (String) getStateHelper().eval(PropertyKeys.widgetVar, null);
  }

  public void setWidgetVar(final String widgetVar) {
    getStateHelper().put(PropertyKeys.widgetVar, widgetVar);
  }

  public MethodExpression getCompleteMethod() {
    return (MethodExpression) getStateHelper().eval(PropertyKeys.completeMethod, null);
  }

  public void setCompleteMethod(final MethodExpression completeMethod) {
    getStateHelper().put(PropertyKeys.completeMethod, completeMethod);
  }

  public boolean isEscape() {
    return (Boolean) getStateHelper().eval(PropertyKeys.escape, true);
  }

  public void setEscape(final boolean escape) {
    getStateHelper().put(PropertyKeys.escape, escape);
  }

  public boolean isEscapeSuggestions() {
    return (Boolean) getStateHelper().eval(PropertyKeys.escapeSuggestions, true);
  }

  public void setEscapeSuggestions(final boolean suggestions) {
    getStateHelper().put(PropertyKeys.escapeSuggestions, suggestions);
  }

  public QueryModel getModel() {
    return (QueryModel) getStateHelper().eval(PropertyKeys.model, null);
  }

  public void setModel(final QueryModel model) {
    getStateHelper().put(PropertyKeys.model, model);
  }

  public boolean isHelpRendered() {
    return (Boolean) getStateHelper().eval(PropertyKeys.helpRendered, true);
  }

  public void setHelpRendered(final boolean helpRendered) {
    getStateHelper().put(PropertyKeys.helpRendered, helpRendered);
  }

  public String getHelpLink() {
    return (String) getStateHelper().eval(PropertyKeys.helpLink, "http://wiki.byzaneo.com/x/AgAu");
  }

  public void setHelpLink(final String helpLink) {
    getStateHelper().put(PropertyKeys.helpLink, helpLink);
  }

  public String getMode() {
    return (String) getStateHelper().eval(PropertyKeys.mode, Mode.advanced.toString());
  }

  public void setMode(final String mode) {
    getStateHelper().put(PropertyKeys.mode, mode);
  }

  public boolean isSwitchMode() {
    return (boolean) getStateHelper().eval(PropertyKeys.switchMode, false);
  }

  public void setSwitchMode(final boolean switchMode) {
    getStateHelper().put(PropertyKeys.switchMode, switchMode);
  }

  public String getLayout() {
    return (String) getStateHelper().eval(PropertyKeys.layout, Layout.horizontal.toString());
  }

  public void setLayout(final String layout) {
    getStateHelper().put(PropertyKeys.layout, layout);
  }

  public String getTemplate() {
    return (String) getStateHelper().eval(PropertyKeys.template, null);
  }

  public void setTemplate(final String template) {
    getStateHelper().put(PropertyKeys.template, template);
  }

  public Object getLocale() {
    return (Object) getStateHelper().eval(PropertyKeys.locale, null);
  }

  public void setLocale(Object _locale) {
    getStateHelper().put(PropertyKeys.locale, _locale);
  }

  public Object getDefaultLocale() {
    return (Object) getStateHelper().eval(PropertyKeys.defaultLocale, null);
  }

  public void setDefaultLocale(Object _defaultLocale) {
    getStateHelper().put(PropertyKeys.defaultLocale, _defaultLocale);
  }

  public String getAdvancedModeLabel() {
    return (String) getStateHelper().eval(PropertyKeys.advancedModeLabel, null);
  }

  public void setAdvancedModeLabel(final String advancedLabel) {
    getStateHelper().put(PropertyKeys.advancedModeLabel, advancedLabel);
  }

  public String getBasicModeLabel() {
    return (String) getStateHelper().eval(PropertyKeys.basicModeLabel, null);
  }

  public void setBasicModeLabel(final String basicLabel) {
    getStateHelper().put(PropertyKeys.basicModeLabel, basicLabel);
  }

  public int getMinDisplayedFields() {
    return (int) getStateHelper().eval(PropertyKeys.minDisplayedFields, -1);
  }

  public void setMinDisplayedFields(final int minDisplayedFields) {
    getStateHelper().put(PropertyKeys.minDisplayedFields, minDisplayedFields);
  }

  public String getDisplayedFields() {
    return (String) getStateHelper().eval(PropertyKeys.displayedFields, null);
  }

  public void setDisplayedFields(final String displayedFields) {
    getStateHelper().put(PropertyKeys.displayedFields, displayedFields);
  }

  public String getButtonStyle() {
    return (String) getStateHelper().eval(PropertyKeys.buttonStyle, "btn btn-primary");
  }

  public void setButtonStyle(final String buttonStyle) {
    getStateHelper().put(PropertyKeys.buttonStyle, buttonStyle);
  }

  public String getSwitchButtonId() {
    return (String) getStateHelper().eval(PropertyKeys.switchButtonId, null);
  }

  public void setSwitchButtonId(final String switchButtonId) {
    getStateHelper().put(PropertyKeys.switchButtonId, switchButtonId);
  }

  public String getPatternDate() {
    return (String) getStateHelper().eval(PropertyKeys.patternDate, "YYYY-MM-DD");
  }

  public void setPatternDate(final String patternDate) {
    getStateHelper().put(PropertyKeys.patternDate, patternDate);
  }

  public TimeZone getTimeZone() {
    return (TimeZone) getStateHelper().eval(PropertyKeys.timeZone, TimeZone.getTimeZone("GMT"));
  }

  public void setTimeZone(final TimeZone timeZone) {
    getStateHelper().put(PropertyKeys.timeZone, timeZone);
  }

  public boolean isUseDaylightTime() {
    return (boolean) getStateHelper().eval(PropertyKeys.useDaylightTime, false);
  }

  public void setUseDaylightTime(final boolean useDaylightTime) {
    getStateHelper().put(PropertyKeys.useDaylightTime, useDaylightTime);
  }

  public boolean isMoreButton() {
    return (boolean) getStateHelper().eval(PropertyKeys.moreButton, true);
  }

  public void setMoreButton(final boolean moreButton) {
    getStateHelper().put(PropertyKeys.moreButton, moreButton);
  }

  public String getMoreContainerClass() {
    return (String) getStateHelper().eval(PropertyKeys.moreContainerClass, "");
  }

  public void setMoreContainerClass(final String moreContainerClass) {
    getStateHelper().put(PropertyKeys.moreContainerClass, moreContainerClass);
  }

  public boolean isSearchButton() {
    return (boolean) getStateHelper().eval(PropertyKeys.searchButton, false);
  }

  public void setSearchButton(final boolean searchButton) {
    getStateHelper().put(PropertyKeys.searchButton, searchButton);
  }

  public String getSearchButtonIcon() {
    return (String) getStateHelper().eval(PropertyKeys.searchButtonIcon, "glyphicon glyphicon-search");
  }

  public void setSearchButtonIcon(final String searchButtonIcon) {
    getStateHelper().put(PropertyKeys.searchButtonIcon, searchButtonIcon);
  }

  public String getSearchButtonLabel() {
    return (String) getStateHelper().eval(PropertyKeys.searchButtonLabel, "");
  }

  public void setSearchButtonLabel(final String searchButtonLabel) {
    getStateHelper().put(PropertyKeys.searchButtonLabel, searchButtonLabel);
  }

  public boolean isTextSearch() {
    return (boolean) getStateHelper().eval(PropertyKeys.textSearch, false);
  }

  public void setTextSearch(final boolean textSearch) {
    getStateHelper().put(PropertyKeys.textSearch, textSearch);
  }

  public String getTextSearchLabel() {
    return (String) getStateHelper().eval(PropertyKeys.textSearchLabel, "");
  }

  public void setTextSearchLabel(final String textSearchLabel) {
    getStateHelper().put(PropertyKeys.textSearchLabel, textSearchLabel);
  }

  public String getTimeZoneID() {
    return (String) getStateHelper().eval(PropertyKeys.timeZoneID, null);
  }

  public void setTimeZoneID(final String timeZoneID) {
    getStateHelper().put(PropertyKeys.timeZoneID, timeZoneID);
  }

  /* -- ATTRIBUTES -- */

  public List<Object> getSelectedFields() {
    return selectedFields == null ? selectedFields = new ArrayList<>() : selectedFields;
  }

  public Object getSelectedField() {
    return getSelectedFields().isEmpty() ? null : selectedFields.get(0);
  }

  public void setSelectedField(Object selectedField) {
    this.getSelectedFields()
        .clear();
    this.getSelectedFields()
        .add(selectedField);
  }

  public boolean isSelectRequest(FacesContext context) {
    return context.getExternalContext()
        .getRequestParameterMap()
        .containsKey(this.getClientId(context));
  }

  public String getSelectedNodeKey(FacesContext context) {
    return context.getExternalContext()
        .getRequestParameterMap()
        .get(this.getClientId(context));
  }

  /* -- UTILS -- */

  @Override
  public String resolveWidgetVar() {
    final FacesContext context = FacesContext.getCurrentInstance();
    final String userWidgetVar = (String) getAttributes().get(PropertyKeys.widgetVar.toString());

    if (userWidgetVar != null) {
      return userWidgetVar;
    }

    return "widget_" + getClientId(context).replaceAll("-|" + UINamingContainer.getSeparatorChar(context), "_");
  }

  /* -- EVENTS -- */

  /** @see javax.faces.component.UIComponentBase#queueEvent(javax.faces.event.FacesEvent) */
  @Override
  public void queueEvent(FacesEvent event) {
    if (!(event instanceof AjaxBehaviorEvent)) {
      super.queueEvent(event);
      return;
    }

    final FacesContext context = getFacesContext();
    final Map<String, String> params = context.getExternalContext()
        .getRequestParameterMap();
    final String eventName = params.get(PARTIAL_BEHAVIOR_EVENT_PARAM);

    if (this.getEventNames()
        .contains(eventName)) {
      final String clientId = this.getClientId(context);
      final AjaxBehaviorEvent behaviorEvent = (AjaxBehaviorEvent) event;
      final Object value = this.getConvertedValue(context, this.getSubmittedValue());
      switch (eventName) {
      case EVENT_SELECT_FIELD:
        this.resolveSelection(value, params.get(clientId));
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), this.getSelectedFields()));
        break;
      case EVENT_CLEAR_FIELD:
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), value));
        break;
      case EVENT_CHANGE:
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), value));
        break;
      case EVENT_ERROR:
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), params.get(clientId)));
        break;
      case EVENT_TOGGLE:
        this.setMode(params.get(clientId));
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), this.getMode()));
        break;
      case EVENT_ENTER_KEY_PRESS:
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), value));
        break;
      case EVENT_CLICK:
        super.queueEvent(new SelectEvent(this, behaviorEvent.getBehavior(), value));
        break;
      default:
        break;
      }

    }
  }

  /**
   * @param value the diagram value
   * @param selectedValue the request selection value
   */
  protected void resolveSelection(Object value, String selectedValue) {
    List<Object> selected = this.getSelectedFields();
    selected.clear();
    if (selectedValue.isEmpty()) {
      return;
    }

    String valueXml = value.toString();
    if (!valueXml.contains(selectedValue)) return;
    selected.add(selectedValue);
  }

  @SuppressWarnings("unchecked")
  @Override
  public void broadcast(final FacesEvent event) throws AbortProcessingException {
    super.broadcast(event);

    final FacesContext facesContext = FacesContext.getCurrentInstance();
    final MethodExpression completeMethod = getCompleteMethod();

    if (completeMethod != null && event instanceof CompleteEvent) {
      suggestions = (Collection<String>) completeMethod.invoke(
          facesContext.getELContext(),
          new Object[] { event });

      if (suggestions == null) {
        suggestions = new ArrayList<String>();
      }

      facesContext.renderResponse();
    }
  }

  public Collection<String> getSuggestions() {
    return this.suggestions;
  }

  /* -- I18N -- */

  public Locale calculateLocale() {
    if (this.calculatedLocale == null)
      this.calculatedLocale = resolveLocale(getLocale(), getClientId());
    return calculatedLocale;
  }

  public Locale calculateDefaultLocale() {
    if (this.calculatedDefaultLocale == null)
      this.calculatedDefaultLocale = new Locale("en");
    return calculatedDefaultLocale;
  }

  public String getTimeZoneJS() {
    int offsetInMillis;
    // Takes the maximum default gap
    if (!this.isUseDaylightTime())
      offsetInMillis = this.getTimeZone()
          .getRawOffset() + this.getTimeZone()
              .getDSTSavings();
    // Use the default mecanism
    else
      offsetInMillis = this.getTimeZone()
          .getOffset(Calendar.ZONE_OFFSET);
    String offset = String.format("%02d:%02d", Math.abs(offsetInMillis / 3600000), Math.abs((offsetInMillis / 60000) % 60));
    return (offsetInMillis >= 0 ? "+" : "-") + offset;
  }
}