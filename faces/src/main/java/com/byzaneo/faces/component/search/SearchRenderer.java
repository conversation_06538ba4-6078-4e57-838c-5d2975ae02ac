/*
 * Copyright 2011-2012 PrimeFaces Extensions.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * $Id$
 */
package com.byzaneo.faces.component.search;

import static org.apache.commons.lang3.CharEncoding.UTF_8;
import static org.apache.commons.lang3.StringEscapeUtils.escapeJson;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.primefaces.util.ComponentUtils.getValueToRender;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;

import javax.faces.component.UIComponent;
import javax.faces.context.*;
import javax.faces.event.*;

import com.byzaneo.commons.util.FormatHelper;
import org.apache.commons.lang.StringUtils;
import org.primefaces.renderkit.InputRenderer;
import org.primefaces.util.HTML;

import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.GsonHelper;
import com.byzaneo.faces.component.selectmanymenu.SelectManyMenuRenderer;
import com.byzaneo.faces.event.*;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.faces.util.*;
import com.google.gson.JsonElement;
import org.slf4j.Logger;

/**
 * Renderer for the {@link Search} component.
 *
 * <AUTHOR> Andraschko / last modified by $Author$
 * @version $Revision$
 * @since 0.3
 */
public class SearchRenderer extends InputRenderer {

  private final static Logger log = getLogger(SearchRenderer.class);

  private static final String LABELS_SEARCH = "labels.search";
  private static final String TEXTAREA_SUFFIX = "-textarea";
  private static final String HIDDEN_QUICK_SEARCH_SUFFIX = "-quicksearch-hidden";
  private static final String HIDDEN_FILTER_SUFFIX = "-filter-hidden";

  @Override
  public void decode(final FacesContext facesContext, final UIComponent component) {
    final Search search = (Search) component;

    if (search.isReadonly() || search.isDisabled()) {
      return;
    }

    // set value
    final String clientId = search.getClientId(facesContext);
    final Map<String, String> params = facesContext.getExternalContext()
        .getRequestParameterMap();
    if (params.containsKey(clientId + TEXTAREA_SUFFIX)) {
      search.setSubmittedValue(params.get(clientId + TEXTAREA_SUFFIX));
    }

    if (params.containsKey(clientId + HIDDEN_QUICK_SEARCH_SUFFIX)) {
      search.setQuickSearchValue(params.get(clientId + HIDDEN_QUICK_SEARCH_SUFFIX));
    }

    if (params.containsKey(clientId + HIDDEN_FILTER_SUFFIX)) {
      search.setFilterName(params.get(clientId + HIDDEN_FILTER_SUFFIX));
    }

    // decode behaviors
    decodeBehaviors(facesContext, component);

    // complete event
    final String fieldName = getParameter(params, clientId + "_fieldname");
    if (fieldName != null) {
      final CompleteEvent autoCompleteEvent = new CompleteEvent(
          search,
          fieldName,
          getParameter(params, clientId + "_fieldvalue"),
          getParameter(params, clientId + "_predicate"),
          getParameter(params, clientId + "_field"));
      autoCompleteEvent.setPhaseId(PhaseId.APPLY_REQUEST_VALUES);
      search.queueEvent(autoCompleteEvent);
    }

    // count event
    final String bql = getParameterBql(params, clientId + "_bql");
    if (bql != null) {
      final FacesEvent countEvent = new CountEvent(
          search,
          bql);
      countEvent.setPhaseId(PhaseId.APPLY_REQUEST_VALUES);
      search.queueEvent(countEvent);
    }
  }

  @Override
  public void encodeEnd(final FacesContext context, final UIComponent component) throws IOException {
    final Search search = (Search) component;

    final Map<String, String> params = context.getExternalContext()
        .getRequestParameterMap();
    final String fieldName = params.get(search.getClientId(context) + "_fieldname");
    final String bql = params.get(search.getClientId(context) + "_bql");

    if (fieldName != null) {
      encodeSuggestions(context, search, search.getSuggestions());
    }
    else if (bql != null) {
      encodeCount(context, search, search.getCount());
    }
    else {
      encodeMarkup(context, search);
      encodeScript(context, search);
    }
  }

  protected void encodeMarkup(final FacesContext context, final Search search) throws IOException {
    final ResponseWriter writer = context.getResponseWriter();
    final String clientId = search.getClientId(context);
    final String inputStyleClasses = inputStyleClass(search);
    final String searchCommand = search.getSearchCommand();
    search.getCountMethod();
    final String localStorageFull = MessageHelper.getMessage("labels.gnx-labels." + "canNotSaveFilter", "",
        (Locale) search.getLocale());
    final String filterSuccessfullySaved = MessageHelper.getMessage("labels.gnx-labels." + "filterSuccessfullySaved", "",
        (Locale) search.getLocale());
    writer.startElement("div", search);
    writer.writeAttribute("class", "row search-row", null);
    // > Container
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId, null);
    writer.writeAttribute("class", "dropdown fcs-search col-lg-9 advSearch-group", null);
    writer.writeAttribute("style", "min-width: 79%;", null);
    // -> Input Group
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId + "-input", null);
    writer.writeAttribute("class", "input-group", null);

    // --> Text Input
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-text", null);
    writer.writeAttribute("class", "form-control fcs-search-text " + inputStyleClasses, null);
    if (search.getQuickSearchDisable()) {
      String tooltip = MessageHelper.getMessage(
          LABELS_SEARCH + ".quick_search_disable_tooltip",
          "The quick search is disabled, please use the advanced search via the arrow on the right", search.calculateLocale());
      writer.writeAttribute("title", tooltip, null);
    }
    if (search.isDisabled() || search.getQuickSearchDisable())
      writer.writeAttribute("disabled", "disabled", null);
    if (search.isReadonly())
      writer.writeAttribute("readonly", "readonly", null);
    if (search.getStyle() != null)
      writer.writeAttribute("style", search.getStyle(), null);
    if (search.isRequired())
      writer.writeAttribute("required", "true", null);
    writer.endElement("input");

    // --> Hidden input for text input
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + HIDDEN_QUICK_SEARCH_SUFFIX, null);
    writer.writeAttribute("name", clientId + HIDDEN_QUICK_SEARCH_SUFFIX, null);
    writer.writeAttribute("hidden", "hidden", null);
    if (search.getQuickSearchValue() != null)
      writer.writeAttribute("value", search.getQuickSearchValue(), null);
    writer.endElement("input");

    // --> BQL Input
    writer.startElement("span", search);
    writer.writeAttribute("id", clientId + "-bqlerrormsg", null);
    writer.writeAttribute("class", "fa fa-check-circle-o hidden", null); // hidden class used in Bootstrap3
    writer.writeAttribute("hidden", "hidden", null); // for Bootstrap4
    writer.endElement("span");
    writer.startElement("span", search);
    writer.writeAttribute("id", clientId + "-bql-label", null);
    writer.writeAttribute("class", "bql-label hidden", null); // hidden class used in Bootstrap3
    writer.writeAttribute("hidden", "hidden", null); // for Bootstrap4
    writer.endElement("span");
    writer.startElement("textarea", search);
    writer.writeAttribute("id", clientId + TEXTAREA_SUFFIX, null);
    writer.writeAttribute("name", clientId + TEXTAREA_SUFFIX, null);
    writer.writeAttribute("rows", 1, null);
    writer.writeAttribute("class", "form-control fcs-search-bql " + inputStyleClasses + " hidden", "styleClass"); // hidden class used in
                                                                                                                  // Bootstrap3
    writer.writeAttribute("hidden", "hidden", null); // for Bootstrap4
    writer.writeAttribute("maxlenght", "2000", null);
    if (search.isDisabled())
      writer.writeAttribute("disabled", "disabled", null);
    if (search.isReadonly())
      writer.writeAttribute("readonly", "readonly", null);
    if (search.getStyle() != null)
      writer.writeAttribute("style", search.getStyle(), null);
    if (search.isRequired())
      writer.writeAttribute("aria-required", "true", null);

    renderPassThruAttributes(context, search, HTML.INPUT_TEXTAREA_ATTRS);
    renderDomEvents(context, search, HTML.INPUT_TEXT_EVENTS);
    if (getCurrentInstance().getApplicationContext()
        .getConfig()
        .isClientSideValidationEnabled()) {
      renderValidationMetadata(context, search);
    }

    final String valueToRender = getValueToRender(context, search);
    if (valueToRender != null) {
      if (search.isEscape()) {
        writer.writeText(valueToRender, null);
      }
      else {
        writer.write(valueToRender);
      }
    }
    writer.endElement("textarea");

    // --> Input Group Buttons
    writer.startElement("span", search);
    writer.writeAttribute("class", "input-group-btn", null);

    // Reset filter fields
    writer.startElement("button", search);
    writer.writeAttribute("id", clientId + "-resetfilter", null);
    writer.writeAttribute("type", "button", null);
    writer.writeAttribute("class", "btn btn-primary", null);
    writer.startElement("span", search);
    writer.writeAttribute("class", "fa fa-times", null);
    writer.endElement("span");
    writer.endElement("button");

    // ---> Toggle Button
    writer.startElement("button", search);
    writer.writeAttribute("id", clientId + "-toggle", null);
    writer.writeAttribute("class", "btn btn-default", null);
    writer.writeAttribute("data-toggle", "dropdown", null);
    writer.writeAttribute("aria-haspopup", "true", null);
    writer.writeAttribute("aria-expanded", "false", null);
    writer.writeAttribute("data-target", "#" + clientId, null);
    if (search.isDisabled())
      writer.writeAttribute("disabled", "disabled", null);
    writer.startElement("span", search);
    writer.writeAttribute("class", "caret", null);
    writer.endElement("span");
    writer.endElement("button");

    // ---> Search Button
    if (searchCommand != null) {
      if (!search.getQuickSearchDisable()) {
        writer.startElement("button", search);
        writer.writeAttribute("id", clientId + "-search", null);
        writer.writeAttribute("class", "btn btn-primary", null);
        writer.writeAttribute("type", "button", null);
        writer.writeAttribute("onclick", searchCommand, null);
        if (search.isDisabled())
          writer.writeAttribute("disabled", "disabled", null);
        writer.startElement("span", search);
        writer.writeAttribute("class", "fa fa-search", null);
        writer.endElement("span");
        writer.endElement("button");
      }
    }

    // <-- Input Group Buttons
    writer.endElement("span");

    // <- Input Group
    writer.endElement("div");

    // -> Panel
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId + "-panel", null);
    writer.writeAttribute("class", "panel panel-default dropdown-menu ui-shadow", null);

    // --> Panel Header
    writer.startElement("div", search);
    writer.writeAttribute("class", "panel-heading card-header", null); // panel-heading for Bootstrap3, card-header for Bootstrap4

    // ---> Close Panel Button
    writer.startElement("button", search);
    writer.writeAttribute("id", clientId + "-close", null);
    writer.writeAttribute("class", "close", null);
    writer.writeAttribute("type", "button", null);
    writer.startElement("span", search);
    writer.writeAttribute("aria-hidden", "true", null);
    writer.write("&#215;");
    writer.endElement("span");
    writer.endElement("button");

    // ---> Order By
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId + "-content-header", null);
    writer.writeAttribute("class", "col-md-6 text-right", null);
    writer.writeAttribute("style", "float:right;", null);
    writer.endElement("div");
    // <--- Order By

    // <-- Panel Header
    writer.endElement("div");

    // --> Panel Body
    writer.startElement("div", search);
    /* container for fields */
    writer.writeAttribute("id", clientId + "-search-criteria", null);
    writer.writeAttribute("class", "panel-body card-body", null); // panel-body for Bootstrap3, card-body for Bootstrap4

    // <-- Panel Body
    writer.endElement("div");

    // --> Panel Footer
    writer.startElement("div", search);
    writer.writeAttribute("class", "panel-footer card-footer", null); // panel-footer for Bootstrap3, card-footer for Bootstrap4
    writer.startElement("div", search);
    writer.writeAttribute("class", "row", null);

    // ---> Filters
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId + "-footer-input-filter", null);
    writer.writeAttribute("class", "col-md-12 col-xl-5 ", null);
    writer.writeAttribute("style", "padding-left: 0; min-width: 370px;", null);
    writer.startElement("div", search);
    writer.writeAttribute("class", "input-group input-filter", null);

    // filter's name
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-filter-name", null);
    writer.writeAttribute("class", "form-control", null);
    writer.writeAttribute("placeholder", "Filter's name", null);
    writer.writeAttribute("maxlength", "20", null);
    writer.endElement("input");

    // filter's hidden input
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + HIDDEN_FILTER_SUFFIX, null);
    writer.writeAttribute("name", clientId + HIDDEN_FILTER_SUFFIX, null);
    writer.writeAttribute("hidden", "hidden", null);
    if (search.getFilterName() != null)
      writer.writeAttribute("value", search.getFilterName(), null);
    writer.endElement("input");

    writer.startElement("div", search);
    writer.writeAttribute("class", "input-group-btn input-filter-btn", null);
    // save filter
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-filter-save", null);
    writer.writeAttribute("onclick",
        "PF('wSearchComponent')._checkExistence(" + "'" + localStorageFull + "'" + "," + "'" + filterSuccessfullySaved + "'" + ")",
        null);
    writer.writeAttribute("type", "button", null);
    writer.writeAttribute("class", "btn btn-default btn-secondary", null);
    writer.endElement("input");
    // delete filter
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-filter-delete", null);
    writer.writeAttribute("type", "button", null);
    writer.writeAttribute("class", "btn btn-default btn-secondary btn-filter-delete", null);
    writer.endElement("input");
    // filter list
    writer.startElement("table", search);
    // ... saved filters
    writer.endElement("table");
    writer.endElement("div");
    writer.endElement("div");
    writer.endElement("div");

    // ---> Content Footer
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId + "-content-footer", null);
    writer.writeAttribute("class", "col-md-6 col-xl-4 content-footer", null);
    // BQL/FullText
    /*
     * writer.startElement("input", search); writer.writeAttribute("id", clientId+"-toggle-bql", null); writer.writeAttribute("value",
     * "Query", null); writer.writeAttribute("type", "button", null); writer.writeAttribute("class", "btn btn-default", null);
     * writer.endElement("input");
     */
    // Reinit fields
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-reinit", null);
    writer.writeAttribute("type", "button", null);
    writer.writeAttribute("class", "btn btn-default btn-secondary", null);
    writer.writeAttribute("style", "margin-right:5px;", null);
    writer.endElement("input");
    writer.endElement("div");

    // ---> Count
    writer.startElement("div", search);
    writer.writeAttribute("id", clientId + "-footer-count", null);
    writer.writeAttribute("class", "col-md-4 col-xl-2", null);
    writer.startElement("div", search);
    writer.writeAttribute("class", "input-group input-count", null);
    // count action
    writer.startElement("div", search);
    writer.writeAttribute("class", "input-group-btn input-count-btn", null);
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-count-action", null);
    writer.writeAttribute("type", "button", null);
    writer.writeAttribute("class", "btn btn-default btn-secondary", null);
    writer.endElement("input");
    writer.endElement("div");
    // show result
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-count-result", null);
    writer.writeAttribute("placeholder", "0", null);
    writer.writeAttribute("class", "form-control", null);
    writer.writeAttribute("style", "padding-right: 16px;text-align:right; padding-left: 0", null);
    writer.endElement("input");
    writer.endElement("div");
    writer.endElement("div");

    // ---> Search
    if (searchCommand != null) {
      writer.startElement("div", search);
      writer.writeAttribute("id", clientId + "-footer-search-button", null);
      writer.writeAttribute("class", "col-md-2 col-xl-1 text-right", null);
      writer.writeAttribute("style", "padding-right: 0px; padding-left: 0", null);
      writer.startElement("button", search);
      writer.writeAttribute("id", clientId + "-search", null);
      writer.writeAttribute("class", "btn btn-primary searchButtonClass", null);
      writer.writeAttribute("type", "button", null);
      writer.writeAttribute("onclick", "PF('statusDialog').show() ; " + searchCommand, null);
      if (search.isDisabled())
        writer.writeAttribute("disabled", "disabled", null);
      writer.startElement("span", search);
      writer.writeAttribute("class", "fa fa-search", null);
      writer.endElement("span");
      writer.endElement("button");
      writer.endElement("div");
    }

    // <-- Panel Footer
    writer.endElement("div");
    writer.endElement("div");

    // <- Panel
    writer.endElement("div");

    // < Container
    writer.endElement("div");
    // -> div between Search bar and Filter list
    writer.startElement("div", search);
    writer.writeAttribute("class", "col-md-1", null);
    writer.writeAttribute("style", "width:3%;", null);
    writer.endElement("div");
    // < div between Search bar and Filter list
    // > Filter list
    writer.startElement("div", search);
    writer.writeAttribute("class", "col-lg-2 dropdown fcs-search", null);
    writer.writeAttribute("id", clientId + "-filter-list-div", null);
    // writer.writeAttribute("style", "float:left;", null);
    writer.writeAttribute("style", "min-width: 18%;", null);
    writer.startElement("div", search);
    writer.writeAttribute("class", "input-group advSearch-group", null);
    // -> Filter list name
    writer.startElement("input", search);
    writer.writeAttribute("id", clientId + "-filter-list-title", null);
    writer.writeAttribute("class", "form-control", null);
    writer.writeAttribute("type", "button", null);
    writer.endElement("input");
    // -> Filter list button
    writer.startElement("div", search);
    writer.writeAttribute("class", "input-group-btn dropdown", null);
    writer.startElement("button", search);
    writer.writeAttribute("id", clientId + "-filter-list-btn", null);
    writer.writeAttribute("class", "btn btn-default dropdown-toggle", null);
    writer.writeAttribute("type", "button", null);
    writer.writeAttribute("data-toggle", "dropdown", null);
    writer.writeAttribute("aria-haspopup", "true", null);
    writer.writeAttribute("aria-expanded", "false", null);
    writer.writeAttribute("data-target", '#' + clientId + "-filter-list-div", null);
    writer.startElement("span", search);
    writer.writeAttribute("class", "caret", null);
    writer.endElement("span");
    writer.endElement("button");

    writer.endElement("div");
    // < Filter list
    writer.endElement("div");
    // -> ul - list of filters
    writer.startElement("ul", search);
    writer.writeAttribute("class", "panel panel-default dropdown-menu ui-shadow", null);
    writer.writeAttribute("class", "list-of-filter", null);
    writer.writeAttribute("style", "max-height: 265px; overflow: auto;", null);
    writer.writeURIAttribute("id", clientId + "-list-saved-filter", null);
    writer.endElement("ul");
    // < ul list of filters
    writer.endElement("div");

    writer.endElement("div");
  }

  protected void encodeScript(final FacesContext context, final Search search) throws IOException {
    WidgetBuilder wb = WidgetBuilder.get(context);
    wb.initWithDomReady(Search.class.getSimpleName(), search.resolveWidgetVar(), search.getClientId(), "search")
        .attr("readonly", search.isReadonly())
        .attr("disabled", search.isDisabled())
        .attr("valid", search.isValid())
        .attr("autocomplete", search.getCompleteMethod() != null)
        .attr("locale", search.calculateLocale()
            .toString())
        .attr("minDisplayedFields", search.getMinDisplayedFields())
        .attr("patternDate", search.getPatternDate())
        .attr("timeZone", search.getTimeZoneJS())
        .attr("useDaylightTime", search.isUseDaylightTime())
        .attr("portletId", search.getPortletId())
        .attr("autoCompleteEnabled", search.getAutoCompleteEnabled())
        .attr("timeZoneID", search.getTimeZoneID());

    final QueryModel model = search.getModel();
    if (model != null) {
      JsonElement json = GsonHelper.getGson()
          .toJsonTree(model);
      wb.attr("bqlFieldNames", escapeJson(json.getAsJsonObject()
          .get("queryFields")
          .toString()))
          .attr("bqlFunctionNames", escapeJson(json.getAsJsonObject()
              .get("queryFunctionNames")
              .toString()))
          .attr("bqlReservedWords", escapeJson(json.getAsJsonObject()
              .get("queryReservedWords")
              .toString()))
          .attr("isCaseSensitive", model.isCaseSensitive());
    }

    // Add translate messagekey of the components
    wb.attr("labelSearch", escapeJson(FacesHelper.getMessagesByLocal(LABELS_SEARCH, search.calculateLocale())));
    wb.attr("labelSelectManyMenu",
        escapeJson(FacesHelper.getMessagesByLocal(SelectManyMenuRenderer.LABELS_SELECT_MANY_MENU, search.calculateLocale())));

    encodeClientBehaviors(context, search);

    wb.finish();
  }

  protected void encodeSuggestions(final FacesContext context, final Search search, final Collection<String> suggestions)
      throws IOException {
    if (suggestions == null)
      return;
    final ResponseWriter writer = context.getResponseWriter();
    writer.startElement("ul", search);
    for (String suggestion : suggestions) {
      if (suggestion == null)
        continue;
      writer.startElement("li", null);
      if (search.isEscapeSuggestions()) {
        writer.writeText(suggestion, null);
      }
      else {
        writer.write(suggestion);
      }
      writer.endElement("li");
    }
    writer.endElement("ul");
  }

  protected void encodeCount(final FacesContext context, final Search search, final long count) throws IOException {
    final ResponseWriter writer = context.getResponseWriter();
    writer.write(String.valueOf(count));
  }

  protected String inputStyleClass(Search search) {
    String defaultClass = Search.STYLE_CLASS;
    defaultClass = search.isValid() ? defaultClass : defaultClass + " ui-state-error";
    defaultClass = !search.isDisabled() ? defaultClass : defaultClass + " ui-state-disabled";

    String styleClass = search.getStyleClass();
    styleClass = styleClass == null ? defaultClass : defaultClass + " " + styleClass;

    return styleClass;
  }

  private String getParameterBql(Map<String, String> params, String key) {
    try {
      if (params.get(key) == null)
        return null;
      String bql = FormatHelper.encodeSpecialCharacters(params.get(key));
      return URLDecoder.decode(bql, UTF_8);
    }
    catch (Exception e) {
      log.error("Error occurred while decoding string :", e);
      return null;
    }
  }

  private String getParameter(Map<String, String> params, String key) {
    try {
      if (params.get(key) == null)
        return null;
      return URLDecoder.decode(params.get(key), UTF_8);
    }
    catch (Exception e) {
      log.error("Error occurred while decoding string :", e);
      return null;
    }
  }
}
