/*! bpmn-js - bpmn-viewer v0.11.0 | (c) 2014, 2015 camunda Services GmbH | bpmn.io/license */
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;"undefined"!=typeof window?t=window:"undefined"!=typeof global?t=global:"undefined"!=typeof self&&(t=self),t.BpmnJS=e()}}(function(){var e;return function t(e,n,r){function i(o,s){if(!n[o]){if(!e[o]){var c="function"==typeof require&&require;if(!s&&c)return c(o,!0);if(a)return a(o,!0);var u=new Error("Cannot find module '"+o+"'");throw u.code="MODULE_NOT_FOUND",u}var l=n[o]={exports:{}};e[o][0].call(l.exports,function(t){var n=e[o][1][t];return i(n?n:t)},l,l.exports,t,e,n,r)}return n[o].exports}for(var a="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){"use strict";function r(e,t){var n=e.get("eventBus");t.forEach(function(e){n.on(e.event,e.handler)})}function i(e){var t=/unparsable content <([^>]+)> detected([\s\S]*)$/,n=t.exec(e.message);return n&&(e.message="unparsable content <"+n[1]+"> detected; this may indicate an invalid BPMN 2.0 diagram file"+n[2]),e}function a(e){return e+(l(e)?"px":"")}function o(e){this.options=e=s({},g,e||{});var t=e.container;t.get&&(t=t.get(0)),u(t)&&(t=f(t));var n=this.container=p('<div class="bjs-container"></div>');t.appendChild(n),s(n.style,{width:a(e.width),height:a(e.height),position:e.position});var r="iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAMAAADypuvZAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAADBQTFRFiMte9PrwldFwfcZPqtqN0+zEyOe1XLgjvuKncsJAZ70y6fXh3vDT////UrQV////G2zN+AAAABB0Uk5T////////////////////AOAjXRkAAAHDSURBVHjavJZJkoUgDEBJmAX8979tM8u3E6x20VlYJfFFMoL4vBDxATxZcakIOJTWSmxvKWVIkJ8jHvlRv1F2LFrVISCZI+tCtQx+XfewgVTfyY3plPiQEAzI3zWy+kR6NBhFBYeBuscJLOUuA2WVLpCjVIaFzrNQZArxAZKUQm6gsj37L9Cb7dnIBUKxENaaMJQqMpDXvSL+ktxdGRm2IsKgJGGPg7atwUG5CcFUEuSv+CwQqizTrvDTNXdMU2bMiDWZd8d7QIySWVRsb2vBBioxOFt4OinPBapL+neAb5KL5IJ8szOza2/DYoipUCx+CjO0Bpsv0V6mktNZ+k8rlABlWG0FrOpKYVo8DT3dBeLEjUBAj7moDogVii7nSS9QzZnFcOVBp1g2PyBQ3Vr5aIapN91VJy33HTJLC1iX2FY6F8gRdaAeIEfVONgtFCzZTmoLEdOjBDfsIOA6128gw3eu1shAajdZNAORxuQDJN5A5PbEG6gNIu24QJD5iNyRMZIr6bsHbCtCU/OaOaSvgkUyDMdDa1BXGf5HJ1To+/Ym6mCKT02Y+/Sa126ZKyd3jxhzpc1r8zVL6YM1Qy/kR4ABAFJ6iQUnivhAAAAAAElFTkSuQmCC",i='<a href="http://bpmn.io" target="_blank" class="bjs-powered-by" title="Powered by bpmn.io" style="position: absolute; bottom: 15px; right: 15px; z-index: 100"><img src="data:image/png;base64,'+r+'"></a>';n.appendChild(p(i))}var s=e(168),c=e(172),u=e(165),l=e(162),p=e(181),f=e(182),h=e(183),d=e(35),m=e(14),y=e(8),g={width:"100%",height:"100%",position:"relative",container:"body"};o.prototype.importXML=function(e,t){var n=this;this.moddle=this.createModdle(),this.moddle.fromXML(e,"bpmn:Definitions",function(e,r,a){if(e)return e=i(e),t(e);var o=a.warnings;n.importDefinitions(r,function(e,n){return e?t(e):void t(null,o.concat(n||[]))})})},o.prototype.saveXML=function(e,t){t||(t=e,e={});var n=this.definitions;return n?void this.moddle.toXML(n,e,t):t(new Error("no definitions loaded"))},o.prototype.createModdle=function(){return new m(this.options.moddleExtensions)},o.prototype.saveSVG=function(e,t){t||(t=e,e={});var n=this.get("canvas"),r=n.getDefaultLayer(),i=n._svg.select("defs"),a=r.innerSVG(),o=i&&i.outerSVG()||"",s=r.getBBox(),c='<?xml version="1.0" encoding="utf-8"?>\n<!-- created with bpmn-js / http://bpmn.io -->\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+s.width+'" height="'+s.height+'" viewBox="'+s.x+" "+s.y+" "+s.width+" "+s.height+'" version="1.1">'+o+a+"</svg>";t(null,c)},o.prototype.get=function(e){if(!this.diagram)throw new Error("no diagram loaded");return this.diagram.get(e)},o.prototype.invoke=function(e){if(!this.diagram)throw new Error("no diagram loaded");return this.diagram.invoke(e)},o.prototype.importDefinitions=function(e,t){try{this.diagram&&this.clear(),this.definitions=e;var n=this.diagram=this._createDiagram(this.options);this._init(n),y.importBpmnDiagram(n,e,t)}catch(r){t(r)}},o.prototype._init=function(e){r(e,this.__listeners||[])},o.prototype._createDiagram=function(e){var t=[].concat(e.modules||this.getModules(),e.additionalModules||[]);return t.unshift({bpmnjs:["value",this],moddle:["value",this.moddle]}),e=c(e,"additionalModules"),e=s(e,{canvas:{container:this.container},modules:t}),new d(e)},o.prototype.getModules=function(){return this._modules},o.prototype.clear=function(){var e=this.diagram;e&&e.destroy()},o.prototype.destroy=function(){this.clear(),h(this.container)},o.prototype.on=function(e,t){var n=this.diagram,r=this.__listeners=this.__listeners||[];r.push({event:e,handler:t}),n&&n.get("eventBus").on(e,t)},o.prototype._modules=[e(2),e(55),e(51)],t.exports=o},{14:14,162:162,165:165,168:168,172:172,181:181,182:182,183:183,2:2,35:35,51:51,55:55,8:8}],2:[function(e,t,n){t.exports={__depends__:[e(5),e(10)]}},{10:10,5:5}],3:[function(e,t,n){"use strict";function r(e,t,n){function r(e,t){X[e]=t}function i(e){return X[e]}function y(e){function t(e,t){var n=s({fill:"black",strokeWidth:1,strokeLinecap:"round",strokeDasharray:"none"},t.attrs),i=t.ref||{x:0,y:0},a=t.scale||1;"none"===n.strokeDasharray&&(n.strokeDasharray=[1e4,1]);var o=t.element.attr(n).marker(0,0,20,20,i.x,i.y).attr({markerWidth:20*a,markerHeight:20*a});return r(e,o)}t("sequenceflow-end",{element:e.path("M 1 5 L 11 10 L 1 15 Z"),ref:{x:11,y:10},scale:.5}),t("messageflow-start",{element:e.circle(6,6,3.5),attrs:{fill:"white",stroke:"black"},ref:{x:6,y:6}}),t("messageflow-end",{element:e.path("m 1 5 l 0 -3 l 7 3 l -7 3 z"),attrs:{fill:"white",stroke:"black",strokeLinecap:"butt"},ref:{x:8.5,y:5}}),t("data-association-end",{element:e.path("M 1 5 L 11 10 L 1 15"),attrs:{fill:"white",stroke:"black"},ref:{x:11,y:10},scale:.5}),t("conditional-flow-marker",{element:e.path("M 0 10 L 8 6 L 16 10 L 8 14 Z"),attrs:{fill:"white",stroke:"black"},ref:{x:-1,y:10},scale:.5}),t("conditional-default-flow-marker",{element:e.path("M 1 4 L 5 16"),attrs:{stroke:"black"},ref:{x:-5,y:10},scale:.5})}function g(e,n,r){return a(n)||(r=n,n=[]),t.style(n||[],s(r,e||{}))}function v(e,t,n,r,i){o(r)&&(i=r,r=0),r=r||0,i=g(i,{stroke:"black",strokeWidth:2,fill:"white"});var a=t/2,s=n/2;return e.circle(a,s,Math.round((t+n)/4-r)).attr(i)}function x(e,t,n,r,i,a){return o(i)&&(a=i,i=0),i=i||0,a=g(a,{stroke:"black",strokeWidth:2,fill:"white"}),e.rect(i,i,t-2*i,n-2*i,r).attr(a)}function b(e,t,n,r){var i=t/2,a=n/2,o=[i,0,t,a,i,n,0,a];return r=g(r,{stroke:"black",strokeWidth:2,fill:"white"}),e.polygon(o).attr(r)}function E(e,t,n){return n=g(n,["no-fill"],{stroke:"black",strokeWidth:2,fill:"none"}),m(t,n).appendTo(e)}function w(e,t,n){return n=g(n,["no-fill"],{strokeWidth:2,stroke:"black"}),e.path(t).attr(n)}function A(e){return function(t,n){return Q[e](t,n)}}function S(e){return Q[e]}function _(e,t){var n=L(e),r=j(n);return F(n,"bpmn:MessageEventDefinition")?S("bpmn:MessageEventDefinition")(t,e,r):F(n,"bpmn:TimerEventDefinition")?S("bpmn:TimerEventDefinition")(t,e,r):F(n,"bpmn:ConditionalEventDefinition")?S("bpmn:ConditionalEventDefinition")(t,e):F(n,"bpmn:SignalEventDefinition")?S("bpmn:SignalEventDefinition")(t,e,r):F(n,"bpmn:CancelEventDefinition")&&F(n,"bpmn:TerminateEventDefinition",{parallelMultiple:!1})?S("bpmn:MultipleEventDefinition")(t,e,r):F(n,"bpmn:CancelEventDefinition")&&F(n,"bpmn:TerminateEventDefinition",{parallelMultiple:!0})?S("bpmn:ParallelMultipleEventDefinition")(t,e,r):F(n,"bpmn:EscalationEventDefinition")?S("bpmn:EscalationEventDefinition")(t,e,r):F(n,"bpmn:LinkEventDefinition")?S("bpmn:LinkEventDefinition")(t,e,r):F(n,"bpmn:ErrorEventDefinition")?S("bpmn:ErrorEventDefinition")(t,e,r):F(n,"bpmn:CancelEventDefinition")?S("bpmn:CancelEventDefinition")(t,e,r):F(n,"bpmn:CompensateEventDefinition")?S("bpmn:CompensateEventDefinition")(t,e,r):F(n,"bpmn:TerminateEventDefinition")?S("bpmn:TerminateEventDefinition")(t,e,r):null}function T(e,t,n){return K.createText(e,t||"",n).addClass("djs-label")}function C(e,t,n){var r=L(t);return T(e,r.name,{box:t,align:n,padding:5})}function R(e,t,n){var r=L(t);return r.name||(t.hidden=!0),T(e,r.name,{box:t,align:n,style:{fontSize:"11px"}})}function N(e,t,n){var r=T(e,t,{box:{height:30,width:n.height},align:"center-middle"}),i=-1*n.height;r.transform("rotate(270) translate("+i+",0)")}function M(e){for(var t=e.waypoints,n="m  "+t[0].x+","+t[0].y,r=1;r<t.length;r++)n+="L"+t[r].x+","+t[r].y+" ";return n}function D(e,t,n){var r,i=L(t),a=l(n,"SubProcessMarker");return r=a?{seq:-21,parallel:-22,compensation:-42,loop:-18,adhoc:10}:{seq:-3,parallel:-6,compensation:-27,loop:0,adhoc:10},c(n,function(n){S(n)(e,t,r)}),"bpmn:AdHocSubProcess"===i.$type&&S("AdhocMarker")(e,t,r),i.loopCharacteristics&&void 0===i.loopCharacteristics.isSequential?void S("LoopMarker")(e,t,r):(i.loopCharacteristics&&void 0!==i.loopCharacteristics.isSequential&&!i.loopCharacteristics.isSequential&&S("ParallelMarker")(e,t,r),i.loopCharacteristics&&i.loopCharacteristics.isSequential&&S("SequentialMarker")(e,t,r),void(i.isForCompensation&&S("CompensationMarker")(e,t,r)))}function P(e,t){var n=t.type,r=Q[n];return r?r(e,t):f.prototype.drawShape.apply(this,[e,t])}function k(e,t){var n=t.type,r=Q[n];return r?r(e,t):f.prototype.drawConnection.apply(this,[e,t])}function O(e,t){var r=(t.height-16)/t.height,i=n.getScaledPath("DATA_OBJECT_COLLECTION_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.451,my:r}});w(e,i,{strokeWidth:2})}function B(e,t){return e.isCollection||e.elementObjectRef&&e.elementObjectRef.isCollection}function I(e){return e.businessObject.di}function L(e){return e.businessObject}function F(e,t,n){function r(e,t){return u(t,function(t,n){return e[n]==t})}return p(e.eventDefinitions,function(i){return i.$type===t&&r(e,n)})}function j(e){return"bpmn:IntermediateThrowEvent"===e.$type||"bpmn:EndEvent"===e.$type}function V(e){return e.join(",").replace(/,?([A-z]),?/g,"$1")}function G(e){var t=e.x+e.width/2,n=e.y+e.height/2,r=e.width/2,i=[["M",t,n],["m",0,-r],["a",r,r,0,1,1,0,2*r],["a",r,r,0,1,1,0,-2*r],["z"]];return V(i)}function U(e){var t=q,n=e.x,r=e.y,i=e.width,a=e.height,o=[["M",n+t,r],["l",i-2*t,0],["a",t,t,0,0,1,t,t],["l",0,a-2*t],["a",t,t,0,0,1,-t,t],["l",2*t-i,0],["a",t,t,0,0,1,-t,-t],["l",0,2*t-a],["a",t,t,0,0,1,t,-t],["z"]];return V(o)}function W(e){var t=e.width,n=e.height,r=e.x,i=e.y,a=t/2,o=n/2,s=[["M",r+a,i],["l",a,o],["l",-a,o],["l",-a,-o],["z"]];return V(s)}function z(e){var t=e.x,n=e.y,r=e.width,i=e.height,a=[["M",t,n],["l",r,0],["l",0,i],["l",-r,0],["z"]];return V(a)}function $(e){var t=L(e);return t.$instanceOf("bpmn:Event")?G(e):t.$instanceOf("bpmn:Activity")?U(e):t.$instanceOf("bpmn:Gateway")?W(e):z(e)}f.call(this,t);var q=10,H=3,Y={fontFamily:"Arial, sans-serif",fontSize:"12px"},K=new h({style:Y,size:{width:100}}),X={},Q={"bpmn:Event":function(e,t,n){return v(e,t.width,t.height,n)},"bpmn:StartEvent":function(e,t){var n={},r=L(t);r.isInterrupting||(n={strokeDasharray:"6",strokeLinecap:"round"});var i=S("bpmn:Event")(e,t,n);return _(t,e),i},"bpmn:MessageEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_MESSAGE",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:t.width,containerHeight:t.height,position:{mx:.235,my:.315}}),a=r?"black":"white",o=r?"white":"black",s=w(e,i,{strokeWidth:1,fill:a,stroke:o});return s},"bpmn:TimerEventDefinition":function(e,t){var r=v(e,t.width,t.height,.2*t.height,{strokeWidth:2}),i=n.getScaledPath("EVENT_TIMER_WH",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.5}});w(e,i,{strokeWidth:2,strokeLinecap:"square"});for(var a=0;12>a;a++){var o=n.getScaledPath("EVENT_TIMER_LINE",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.5}}),s=t.width/2,c=t.height/2;w(e,o,{strokeWidth:1,strokeLinecap:"square",transform:"rotate("+30*a+","+c+","+s+")"})}return r},"bpmn:EscalationEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_ESCALATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.555}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a})},"bpmn:ConditionalEventDefinition":function(e,t){var r=n.getScaledPath("EVENT_CONDITIONAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.222}});return w(e,r,{strokeWidth:1})},"bpmn:LinkEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_LINK",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.57,my:.263}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a})},"bpmn:ErrorEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_ERROR",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:t.width,containerHeight:t.height,position:{mx:.2,my:.722}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a})},"bpmn:CancelEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_CANCEL_45",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.638,my:-.055}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a}).transform("rotate(45)")},"bpmn:CompensateEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.201,my:.472}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a})},"bpmn:SignalEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_SIGNAL",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:t.width,containerHeight:t.height,position:{mx:.5,my:.2}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a})},"bpmn:MultipleEventDefinition":function(e,t,r){var i=n.getScaledPath("EVENT_MULTIPLE",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:t.width,containerHeight:t.height,position:{mx:.222,my:.36}}),a=r?"black":"none";return w(e,i,{strokeWidth:1,fill:a})},"bpmn:ParallelMultipleEventDefinition":function(e,t){var r=n.getScaledPath("EVENT_PARALLEL_MULTIPLE",{xScaleFactor:1.2,yScaleFactor:1.2,containerWidth:t.width,containerHeight:t.height,position:{mx:.458,my:.194}});return w(e,r,{strokeWidth:1})},"bpmn:EndEvent":function(e,t){var n=S("bpmn:Event")(e,t,{strokeWidth:4});return _(t,e,!0),n},"bpmn:TerminateEventDefinition":function(e,t){var n=v(e,t.width,t.height,8,{strokeWidth:4,fill:"black"});return n},"bpmn:IntermediateEvent":function(e,t){var n=S("bpmn:Event")(e,t,{strokeWidth:1});return v(e,t.width,t.height,H,{strokeWidth:1,fill:"none"}),_(t,e),n},"bpmn:IntermediateCatchEvent":A("bpmn:IntermediateEvent"),"bpmn:IntermediateThrowEvent":A("bpmn:IntermediateEvent"),"bpmn:Activity":function(e,t,n){return x(e,t.width,t.height,q,n)},"bpmn:Task":function(e,t,n){var r=S("bpmn:Activity")(e,t,n);return C(e,t,"center-middle"),D(e,t),r},"bpmn:ServiceTask":function(e,t){var r=S("bpmn:Task")(e,t),i=n.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:12,y:18}});w(e,i,{strokeWidth:1,fill:"none"});var a=n.getScaledPath("TASK_TYPE_SERVICE_FILL",{abspos:{x:17.2,y:18}});w(e,a,{strokeWidth:0,stroke:"none",fill:"white"});var o=n.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:17,y:22}});return w(e,o,{strokeWidth:1,fill:"white"}),r},"bpmn:UserTask":function(e,t){var r=S("bpmn:Task")(e,t),i=15,a=12,o=n.getScaledPath("TASK_TYPE_USER_1",{abspos:{x:i,y:a}});w(e,o,{strokeWidth:.5,fill:"none"});var s=n.getScaledPath("TASK_TYPE_USER_2",{abspos:{x:i,y:a}});w(e,s,{strokeWidth:.5,fill:"none"});var c=n.getScaledPath("TASK_TYPE_USER_3",{abspos:{x:i,y:a}});return w(e,c,{strokeWidth:.5,fill:"black"}),r},"bpmn:ManualTask":function(e,t){var r=S("bpmn:Task")(e,t),i=n.getScaledPath("TASK_TYPE_MANUAL",{abspos:{x:17,y:15}});return w(e,i,{strokeWidth:.25,fill:"white",stroke:"black"}),r},"bpmn:SendTask":function(e,t){var r=S("bpmn:Task")(e,t),i=n.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:1,yScaleFactor:1,containerWidth:21,containerHeight:14,position:{mx:.285,my:.357}});return w(e,i,{strokeWidth:1,fill:"black",stroke:"white"}),r},"bpmn:ReceiveTask":function(e,t){var r,i=L(t),a=S("bpmn:Task")(e,t);return i.instantiate?(v(e,28,28,4.4,{strokeWidth:1}),r=n.getScaledPath("TASK_TYPE_INSTANTIATING_SEND",{abspos:{x:7.77,y:9.52}})):r=n.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:21,containerHeight:14,position:{mx:.3,my:.4}}),w(e,r,{strokeWidth:1}),a},"bpmn:ScriptTask":function(e,t){var r=S("bpmn:Task")(e,t),i=n.getScaledPath("TASK_TYPE_SCRIPT",{abspos:{x:15,y:20}});return w(e,i,{strokeWidth:1}),r},"bpmn:BusinessRuleTask":function(e,t){var r=S("bpmn:Task")(e,t),i=n.getScaledPath("TASK_TYPE_BUSINESS_RULE_HEADER",{abspos:{x:8,y:8}}),a=w(e,i);a.attr({strokeWidth:1,fill:"AAA"});var o=n.getScaledPath("TASK_TYPE_BUSINESS_RULE_MAIN",{abspos:{x:8,y:8}}),s=w(e,o);return s.attr({strokeWidth:1}),r},"bpmn:SubProcess":function(e,t,n){var r=S("bpmn:Activity")(e,t,n),i=L(t),a=d.isExpanded(i),o=!!i.triggeredByEvent;return o&&r.attr({strokeDasharray:"1,2"}),C(e,t,a?"center-top":"center-middle"),a?D(e,t):D(e,t,["SubProcessMarker"]),r},"bpmn:AdHocSubProcess":function(e,t){return S("bpmn:SubProcess")(e,t)},"bpmn:Transaction":function(e,n){var r=S("bpmn:SubProcess")(e,n),i=t.style(["no-fill","no-events"]);return x(e,n.width,n.height,q-2,H,i),r},"bpmn:CallActivity":function(e,t){return S("bpmn:Task")(e,t,{strokeWidth:5})},"bpmn:Participant":function(e,t){var n=S("bpmn:Lane")(e,t,{fill:"White"}),r=d.isExpanded(t);if(r){E(e,[{x:30,y:0},{x:30,y:t.height}]);var i=L(t).name;N(e,i,t)}else{var a=L(t).name;T(e,a,{box:t,align:"center-middle"})}var o=!!L(t).participantMultiplicity;return o&&S("ParticipantMultiplicityMarker")(e,t),n},"bpmn:Lane":function(e,t,n){var r=x(e,t.width,t.height,0,n||{fill:"none"}),i=L(t);if("bpmn:Lane"===i.$type){var a=i.name;N(e,a,t)}return r},"bpmn:InclusiveGateway":function(e,t){var n=b(e,t.width,t.height);return v(e,t.width,t.height,.24*t.height,{strokeWidth:2.5,fill:"none"}),n},"bpmn:ExclusiveGateway":function(e,t){var r=b(e,t.width,t.height),i=n.getScaledPath("GATEWAY_EXCLUSIVE",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:t.width,containerHeight:t.height,position:{mx:.32,my:.3}});return I(t).isMarkerVisible&&w(e,i,{strokeWidth:1,fill:"black"}),r},"bpmn:ComplexGateway":function(e,t){var r=b(e,t.width,t.height),i=n.getScaledPath("GATEWAY_COMPLEX",{xScaleFactor:.5,yScaleFactor:.5,containerWidth:t.width,containerHeight:t.height,position:{mx:.46,my:.26}});return w(e,i,{strokeWidth:1,fill:"black"}),r},"bpmn:ParallelGateway":function(e,t){var r=b(e,t.width,t.height),i=n.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.6,yScaleFactor:.6,containerWidth:t.width,containerHeight:t.height,position:{mx:.46,my:.2}});return w(e,i,{strokeWidth:1,fill:"black"}),r},"bpmn:EventBasedGateway":function(e,t){function r(){var r=n.getScaledPath("GATEWAY_EVENT_BASED",{xScaleFactor:.18,yScaleFactor:.18,containerWidth:t.width,containerHeight:t.height,position:{mx:.36,my:.44}});w(e,r,{strokeWidth:2,fill:"none"})}var i=L(t),a=b(e,t.width,t.height);v(e,t.width,t.height,.2*t.height,{strokeWidth:1,fill:"none"});var o=i.eventGatewayType,s=!!i.instantiate;if("Parallel"===o){var c=n.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:t.width,containerHeight:t.height,position:{mx:.474,my:.296}}),u=w(e,c);u.attr({strokeWidth:1,fill:"none"})}else if("Exclusive"===o){if(!s){var l=v(e,t.width,t.height,.26*t.height);l.attr({strokeWidth:1,fill:"none"})}r()}return a},"bpmn:Gateway":function(e,t){return b(e,t.width,t.height)},"bpmn:SequenceFlow":function(e,t){var n=M(t),r=w(e,n,{strokeLinejoin:"round",markerEnd:i("sequenceflow-end")}),a=L(t),o=t.source.businessObject;return a.conditionExpression&&o.$instanceOf("bpmn:Task")&&r.attr({markerStart:i("conditional-flow-marker")}),o["default"]&&o.$instanceOf("bpmn:Gateway")&&o["default"]===a&&r.attr({markerStart:i("conditional-default-flow-marker")}),r},"bpmn:Association":function(e,t,n){return n=s({strokeDasharray:"1,6",strokeLinecap:"round",strokeLinejoin:"round"},n||{}),E(e,t.waypoints,n)},"bpmn:DataInputAssociation":function(e,t){return S("bpmn:Association")(e,t,{markerEnd:i("data-association-end")})},"bpmn:DataOutputAssociation":function(e,t){return S("bpmn:Association")(e,t,{markerEnd:i("data-association-end")})},"bpmn:MessageFlow":function(e,t){var r=L(t),a=I(t),o=M(t),s=w(e,o,{markerEnd:i("messageflow-end"),markerStart:i("messageflow-start"),strokeDasharray:"10, 12",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5px"});if(r.messageRef){var c=s.getPointAtLength(s.getTotalLength()/2),u=n.getScaledPath("MESSAGE_FLOW_MARKER",{abspos:{x:c.x,y:c.y}}),l={strokeWidth:1};"initiating"===a.messageVisibleKind?(l.fill="white",l.stroke="black"):(l.fill="#888",l.stroke="white"),w(e,u,l)}return s},"bpmn:DataObject":function(e,t){var r=n.getScaledPath("DATA_OBJECT_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:.474,my:.296}}),i=w(e,r,{fill:"white"}),a=L(t);return B(a)&&O(e,t),i},"bpmn:DataObjectReference":A("bpmn:DataObject"),"bpmn:DataInput":function(e,t){var r=n.getRawPath("DATA_ARROW"),i=S("bpmn:DataObject")(e,t);return w(e,r,{strokeWidth:1}),i},"bpmn:DataOutput":function(e,t){var r=n.getRawPath("DATA_ARROW"),i=S("bpmn:DataObject")(e,t);return w(e,r,{strokeWidth:1,fill:"black"}),i},"bpmn:DataStoreReference":function(e,t){var r=n.getScaledPath("DATA_STORE",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:0,my:.133}}),i=w(e,r,{strokeWidth:2,fill:"white"});return i},"bpmn:BoundaryEvent":function(e,t){var n=L(t),r=n.cancelActivity,i={strokeWidth:1};r||(i.strokeDasharray="6",i.strokeLinecap="round");var a=S("bpmn:Event")(e,t,i);return v(e,t.width,t.height,H,s(i,{fill:"none"})),_(t,e),a},"bpmn:Group":function(e,t){return x(e,t.width,t.height,q,{strokeWidth:1,strokeDasharray:"8,3,1,3",fill:"none",pointerEvents:"none"})},label:function(e,t){return R(e,t,"")},"bpmn:TextAnnotation":function(e,t){var r={fill:"none",stroke:"none"},i=x(e,t.width,t.height,0,0,r),a=n.getScaledPath("TEXT_ANNOTATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:0,my:0}});w(e,a);var o=L(t).text||"";return T(e,o,{box:t,align:"left-middle",padding:5}),i},ParticipantMultiplicityMarker:function(e,t){var r=n.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:t.width/2/t.width,my:(t.height-15)/t.height}});w(e,r)},SubProcessMarker:function(e,t){var r=x(e,14,14,0,{strokeWidth:1});r.transform("translate("+(t.width/2-7.5)+","+(t.height-20)+")");var i=n.getScaledPath("MARKER_SUB_PROCESS",{xScaleFactor:1.5,yScaleFactor:1.5,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2-7.5)/t.width,my:(t.height-20)/t.height}});w(e,i)},ParallelMarker:function(e,t,r){var i=n.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+r.parallel)/t.width,my:(t.height-20)/t.height}});w(e,i)},SequentialMarker:function(e,t,r){var i=n.getScaledPath("MARKER_SEQUENTIAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+r.seq)/t.width,my:(t.height-19)/t.height}});w(e,i)},CompensationMarker:function(e,t,r){var i=n.getScaledPath("MARKER_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+r.compensation)/t.width,my:(t.height-13)/t.height}});w(e,i,{strokeWidth:1})},LoopMarker:function(e,t,r){var i=n.getScaledPath("MARKER_LOOP",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+r.loop)/t.width,my:(t.height-7)/t.height}});w(e,i,{strokeWidth:1,fill:"none",strokeLinecap:"round",strokeMiterlimit:.5})},AdhocMarker:function(e,t,r){var i=n.getScaledPath("MARKER_ADHOC",{xScaleFactor:1,yScaleFactor:1,containerWidth:t.width,containerHeight:t.height,position:{mx:(t.width/2+r.adhoc)/t.width,my:(t.height-15)/t.height}});w(e,i,{strokeWidth:1,fill:"black"})}};e.on("canvas.init",function(e){y(e.svg)}),this.drawShape=P,this.drawConnection=k,this.getShapePath=$}var i=e(72),a=e(159),o=e(163),s=e(168),c=e(77),u=e(74),l=e(79),p=e(82),f=e(43),h=e(64),d=e(11),m=f.createLine;i(r,f),r.$inject=["eventBus","styles","pathMap"],t.exports=r},{11:11,159:159,163:163,168:168,43:43,64:64,72:72,74:74,77:77,79:79,82:82}],4:[function(e,t,n){"use strict";function r(){this.pathMap={EVENT_MESSAGE:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:36,width:36,heightElements:[6,14],widthElements:[10.5,21]},EVENT_SIGNAL:{d:"M {mx},{my} l {e.x0},{e.y0} l -{e.x1},0 Z",height:36,width:36,heightElements:[18],widthElements:[10,20]},EVENT_ESCALATION:{d:"m {mx},{my} c -{e.x1},{e.y0} -{e.x3},{e.y1} -{e.x5},{e.y4} {e.x1},-{e.y3} {e.x3},-{e.y5} {e.x5},-{e.y6} {e.x0},{e.y3} {e.x2},{e.y5} {e.x4},{e.y6} -{e.x0},-{e.y0} -{e.x2},-{e.y1} -{e.x4},-{e.y4} z",height:36,width:36,heightElements:[2.382,4.764,4.926,6.589333,7.146,13.178667,19.768],widthElements:[2.463,2.808,4.926,5.616,7.389,8.424]},EVENT_CONDITIONAL:{d:"M {e.x0},{e.y0} l {e.x1},0 l 0,{e.y2} l -{e.x1},0 Z M {e.x2},{e.y3} l {e.x0},0 M {e.x2},{e.y4} l {e.x0},0 M {e.x2},{e.y5} l {e.x0},0 M {e.x2},{e.y6} l {e.x0},0 M {e.x2},{e.y7} l {e.x0},0 M {e.x2},{e.y8} l {e.x0},0 ",height:36,width:36,heightElements:[8.5,14.5,18,11.5,14.5,17.5,20.5,23.5,26.5],widthElements:[10.5,14.5,12.5]},EVENT_LINK:{d:"m {mx},{my} 0,{e.y0} -{e.x1},0 0,{e.y1} {e.x1},0 0,{e.y0} {e.x0},-{e.y2} -{e.x0},-{e.y2} z",height:36,width:36,heightElements:[4.4375,6.75,7.8125],widthElements:[9.84375,13.5]},EVENT_ERROR:{d:"m {mx},{my} {e.x0},-{e.y0} {e.x1},-{e.y1} {e.x2},{e.y2} {e.x3},-{e.y3} -{e.x4},{e.y4} -{e.x5},-{e.y5} z",height:36,width:36,heightElements:[.023,8.737,8.151,16.564,10.591,8.714],widthElements:[.085,6.672,6.97,4.273,5.337,6.636]},EVENT_CANCEL_45:{d:"m {mx},{my} -{e.x1},0 0,{e.x0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:36,width:36,heightElements:[4.75,8.5],widthElements:[4.75,8.5]},EVENT_COMPENSATION:{d:"m {mx},{my} {e.x0},-{e.y0} 0,{e.y1} z m {e.x0},0 {e.x0},-{e.y0} 0,{e.y1} z",height:36,width:36,heightElements:[5,10],widthElements:[10]},EVENT_TIMER_WH:{d:"M {mx},{my} l {e.x0},-{e.y0} m -{e.x0},{e.y0} l {e.x1},{e.y1} ",height:36,width:36,heightElements:[10,2],widthElements:[3,7]},EVENT_TIMER_LINE:{d:"M {mx},{my} m {e.x0},{e.y0} l -{e.x1},{e.y1} ",height:36,width:36,heightElements:[10,3],widthElements:[0,0]},EVENT_MULTIPLE:{d:"m {mx},{my} {e.x1},-{e.y0} {e.x1},{e.y0} -{e.x0},{e.y1} -{e.x2},0 z",height:36,width:36,heightElements:[6.28099,12.56199],widthElements:[3.1405,9.42149,12.56198]},EVENT_PARALLEL_MULTIPLE:{d:"m {mx},{my} {e.x0},0 0,{e.y1} {e.x1},0 0,{e.y0} -{e.x1},0 0,{e.y1} -{e.x0},0 0,-{e.y1} -{e.x1},0 0,-{e.y0} {e.x1},0 z",height:36,width:36,heightElements:[2.56228,7.68683],widthElements:[2.56228,7.68683]},GATEWAY_EXCLUSIVE:{d:"m {mx},{my} {e.x0},{e.y0} {e.x1},{e.y0} {e.x2},0 {e.x4},{e.y2} {e.x4},{e.y1} {e.x2},0 {e.x1},{e.y3} {e.x0},{e.y3} {e.x3},0 {e.x5},{e.y1} {e.x5},{e.y2} {e.x3},0 z",height:17.5,width:17.5,heightElements:[8.5,6.5312,-6.5312,-8.5],widthElements:[6.5,-6.5,3,-3,5,-5]},GATEWAY_PARALLEL:{d:"m {mx},{my} 0,{e.y1} -{e.x1},0 0,{e.y0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:30,width:30,heightElements:[5,12.5],widthElements:[5,12.5]},GATEWAY_EVENT_BASED:{d:"m {mx},{my} {e.x0},{e.y0} {e.x0},{e.y1} {e.x1},{e.y2} {e.x2},0 z",height:11,width:11,heightElements:[-6,6,12,-12],widthElements:[9,-3,-12]},GATEWAY_COMPLEX:{d:"m {mx},{my} 0,{e.y0} -{e.x0},-{e.y1} -{e.x1},{e.y2} {e.x0},{e.y1} -{e.x2},0 0,{e.y3} {e.x2},0  -{e.x0},{e.y1} l {e.x1},{e.y2} {e.x0},-{e.y1} 0,{e.y0} {e.x3},0 0,-{e.y0} {e.x0},{e.y1} {e.x1},-{e.y2} -{e.x0},-{e.y1} {e.x2},0 0,-{e.y3} -{e.x2},0 {e.x0},-{e.y1} -{e.x1},-{e.y2} -{e.x0},{e.y1} 0,-{e.y0} -{e.x3},0 z",height:17.125,width:17.125,heightElements:[4.875,3.4375,2.125,3],widthElements:[3.4375,2.125,4.875,3]},DATA_OBJECT_PATH:{d:"m 0,0 {e.x1},0 {e.x0},{e.y0} 0,{e.y1} -{e.x2},0 0,-{e.y2} {e.x1},0 0,{e.y0} {e.x0},0",height:61,width:51,heightElements:[10,50,60],widthElements:[10,40,50,60]},DATA_OBJECT_COLLECTION_PATH:{d:"m {mx}, {my} m  0 15  l 0 -15 m  4 15  l 0 -15 m  4 15  l 0 -15 ",height:61,width:51,heightElements:[12],widthElements:[1,6,12,15]},DATA_ARROW:{d:"m 5,9 9,0 0,-3 5,5 -5,5 0,-3 -9,0 z",height:61,width:51,heightElements:[],widthElements:[]},DATA_STORE:{d:"m  {mx},{my} l  0,{e.y2} c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 l  0,-{e.y2} c -{e.x0},-{e.y1} -{e.x1},-{e.y1} -{e.x2},0c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1} {e.x2},0m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0",height:61,width:61,heightElements:[7,10,45],widthElements:[2,58,60]},TEXT_ANNOTATION:{d:"m {mx}, {my} m 10,0 l -10,0 l 0,{e.y0} l 10,0",height:30,width:10,heightElements:[30],widthElements:[10]},MARKER_SUB_PROCESS:{d:"m{mx},{my} m 7,2 l 0,10 m -5,-5 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_PARALLEL:{d:"m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",height:10,width:10,heightElements:[],widthElements:[]},MARKER_SEQUENTIAL:{d:"m{mx},{my} m 0,3 l 10,0 m -10,3 l 10,0 m -10,3 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_COMPENSATION:{d:"m {mx},{my} 8,-5 0,10 z m 9,0 8,-5 0,10 z",height:10,width:21,heightElements:[],widthElements:[]},MARKER_LOOP:{d:"m {mx},{my} c 3.526979,0 6.386161,-2.829858 6.386161,-6.320661 0,-3.490806 -2.859182,-6.320661 -6.386161,-6.320661 -3.526978,0 -6.38616,2.829855 -6.38616,6.320661 0,1.745402 0.714797,3.325567 1.870463,4.469381 0.577834,0.571908 1.265885,1.034728 2.029916,1.35457 l -0.718163,-3.909793 m 0.718163,3.909793 -3.885211,0.802902",height:13.9,width:13.7,heightElements:[],widthElements:[]},MARKER_ADHOC:{d:"m {mx},{my} m 0.84461,2.64411 c 1.05533,-1.23780996 2.64337,-2.07882 4.29653,-1.97997996 2.05163,0.0805 3.85579,1.15803 5.76082,1.79107 1.06385,0.34139996 2.24454,0.1438 3.18759,-0.43767 0.61743,-0.33642 1.2775,-0.64078 1.7542,-1.17511 0,0.56023 0,1.12046 0,1.6807 -0.98706,0.96237996 -2.29792,1.62393996 -3.6918,1.66181996 -1.24459,0.0927 -2.46671,-0.2491 -3.59505,-0.74812 -1.35789,-0.55965 -2.75133,-1.33436996 -4.27027,-1.18121996 -1.37741,0.14601 -2.41842,1.13685996 -3.44288,1.96782996 z",height:4,width:15,heightElements:[],widthElements:[]},TASK_TYPE_SEND:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:14,width:21,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_SCRIPT:{d:"m {mx},{my} c 9.966553,-6.27276 -8.000926,-7.91932 2.968968,-14.938 l -8.802728,0 c -10.969894,7.01868 6.997585,8.66524 -2.968967,14.938 z m -7,-12 l 5,0 m -4.5,3 l 4.5,0 m -3,3 l 5,0m -4,3 l 5,0",height:15,width:12.6,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_USER_1:{d:"m {mx},{my} c 0.909,-0.845 1.594,-2.049 1.594,-3.385 0,-2.554 -1.805,-4.62199999 -4.357,-4.62199999 -2.55199998,0 -4.28799998,2.06799999 -4.28799998,4.62199999 0,1.348 0.974,2.562 1.89599998,3.405 -0.52899998,0.187 -5.669,2.097 -5.794,4.7560005 v 6.718 h 17 v -6.718 c 0,-2.2980005 -5.5279996,-4.5950005 -6.0509996,-4.7760005 zm -8,6 l 0,5.5 m 11,0 l 0,-5"},TASK_TYPE_USER_2:{d:"m {mx},{my} m 2.162,1.009 c 0,2.4470005 -2.158,4.4310005 -4.821,4.4310005 -2.66499998,0 -4.822,-1.981 -4.822,-4.4310005 "},TASK_TYPE_USER_3:{d:"m {mx},{my} m -6.9,-3.80 c 0,0 2.25099998,-2.358 4.27399998,-1.177 2.024,1.181 4.221,1.537 4.124,0.965 -0.098,-0.57 -0.117,-3.79099999 -4.191,-4.13599999 -3.57499998,0.001 -4.20799998,3.36699999 -4.20699998,4.34799999 z"},TASK_TYPE_MANUAL:{d:"m {mx},{my} c 0.234,-0.01 5.604,0.008 8.029,0.004 0.808,0 1.271,-0.172 1.417,-0.752 0.227,-0.898 -0.334,-1.314 -1.338,-1.316 -2.467,-0.01 -7.886,-0.004 -8.108,-0.004 -0.014,-0.079 0.016,-0.533 0,-0.61 0.195,-0.042 8.507,0.006 9.616,0.002 0.877,-0.007 1.35,-0.438 1.353,-1.208 0.003,-0.768 -0.479,-1.09 -1.35,-1.091 -2.968,-0.002 -9.619,-0.013 -9.619,-0.013 v -0.591 c 0,0 5.052,-0.016 7.225,-0.016 0.888,-0.002 1.354,-0.416 1.351,-1.193 -0.006,-0.761 -0.492,-1.196 -1.361,-1.196 -3.473,-0.005 -10.86,-0.003 -11.0829995,-0.003 -0.022,-0.047 -0.045,-0.094 -0.069,-0.139 0.3939995,-0.319 2.0409995,-1.626 2.4149995,-2.017 0.469,-0.4870005 0.519,-1.1650005 0.162,-1.6040005 -0.414,-0.511 -0.973,-0.5 -1.48,-0.236 -1.4609995,0.764 -6.5999995,3.6430005 -7.7329995,4.2710005 -0.9,0.499 -1.516,1.253 -1.882,2.19 -0.37000002,0.95 -0.17,2.01 -0.166,2.979 0.004,0.718 -0.27300002,1.345 -0.055,2.063 0.629,2.087 2.425,3.312 4.859,3.318 4.6179995,0.014 9.2379995,-0.139 13.8569995,-0.158 0.755,-0.004 1.171,-0.301 1.182,-1.033 0.012,-0.754 -0.423,-0.969 -1.183,-0.973 -1.778,-0.01 -5.824,-0.004 -6.04,-0.004 10e-4,-0.084 0.003,-0.586 10e-4,-0.67 z"
},TASK_TYPE_INSTANTIATING_SEND:{d:"m {mx},{my} l 0,8.4 l 12.6,0 l 0,-8.4 z l 6.3,3.6 l 6.3,-3.6"},TASK_TYPE_SERVICE:{d:"m {mx},{my} v -1.71335 c 0.352326,-0.0705 0.703932,-0.17838 1.047628,-0.32133 0.344416,-0.14465 0.665822,-0.32133 0.966377,-0.52145 l 1.19431,1.18005 1.567487,-1.57688 -1.195028,-1.18014 c 0.403376,-0.61394 0.683079,-1.29908 0.825447,-2.01824 l 1.622133,-0.01 v -2.2196 l -1.636514,0.01 c -0.07333,-0.35153 -0.178319,-0.70024 -0.323564,-1.04372 -0.145244,-0.34406 -0.321407,-0.6644 -0.522735,-0.96217 l 1.131035,-1.13631 -1.583305,-1.56293 -1.129598,1.13589 c -0.614052,-0.40108 -1.302883,-0.68093 -2.022633,-0.82247 l 0.0093,-1.61852 h -2.241173 l 0.0042,1.63124 c -0.353763,0.0736 -0.705369,0.17977 -1.049785,0.32371 -0.344415,0.14437 -0.665102,0.32092 -0.9635006,0.52046 l -1.1698628,-1.15823 -1.5667691,1.5792 1.1684265,1.15669 c -0.4026573,0.61283 -0.68308,1.29797 -0.8247287,2.01713 l -1.6588041,0.003 v 2.22174 l 1.6724648,-0.006 c 0.073327,0.35077 0.1797598,0.70243 0.3242851,1.04472 0.1452428,0.34448 0.3214064,0.6644 0.5227339,0.96066 l -1.1993431,1.19723 1.5840256,1.56011 1.1964668,-1.19348 c 0.6140517,0.40346 1.3028827,0.68232 2.0233517,0.82331 l 7.19e-4,1.69892 h 2.226848 z m 0.221462,-3.9957 c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_SERVICE_FILL:{d:"m {mx},{my} c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_BUSINESS_RULE_HEADER:{d:"m {mx},{my} 0,4 20,0 0,-4 z"},TASK_TYPE_BUSINESS_RULE_MAIN:{d:"m {mx},{my} 0,12 20,0 0,-12 zm 0,8 l 20,0 m -13,-4 l 0,8"},MESSAGE_FLOW_MARKER:{d:"m {mx},{my} m -10.5 ,-7 l 0,14 l 21,0 l 0,-14 z l 10.5,6 l 10.5,-6"}},this.getRawPath=function(e){return this.pathMap[e].d},this.getScaledPath=function(e,t){var n,r,a=this.pathMap[e];t.abspos?(n=t.abspos.x,r=t.abspos.y):(n=t.containerWidth*t.position.mx,r=t.containerHeight*t.position.my);var o={};if(t.position){for(var s=t.containerHeight/a.height*t.yScaleFactor,c=t.containerWidth/a.width*t.xScaleFactor,u=0;u<a.heightElements.length;u++)o["y"+u]=a.heightElements[u]*s;for(var l=0;l<a.widthElements.length;l++)o["x"+l]=a.widthElements[l]*c}var p=i.format(a.d,{mx:n,my:r,e:o});return p}}var i=e(67);t.exports=r},{67:67}],5:[function(e,t,n){t.exports={renderer:["type",e(3)],pathMap:["type",e(4)]}},{3:3,4:4}],6:[function(e,t,n){"use strict";function r(e,t){return s({id:e.id,type:e.$type,businessObject:e},t)}function i(e){return c(e,function(e){return{x:e.x,y:e.y}})}function a(e,t,n){return new Error("element "+d(t)+" referenced by "+d(e)+"#"+n+" not yet drawn")}function o(e,t,n,r){this._eventBus=e,this._canvas=t,this._elementFactory=n,this._elementRegistry=r}var s=e(168),c=e(80),u=e(12),l=e(13).is,p=u.hasExternalLabel,f=u.getExternalLabelBounds,h=e(11).isExpanded,d=e(9).elementToString;o.$inject=["eventBus","canvas","elementFactory","elementRegistry"],t.exports=o,o.prototype.add=function(e,t){var n,a=e.di;if(a.$instanceOf("bpmndi:BPMNPlane"))n=this._elementFactory.createRoot(r(e)),this._canvas.setRootElement(n);else if(a.$instanceOf("bpmndi:BPMNShape")){var o=!h(e),s=t&&(t.hidden||t.collapsed),c=e.di.bounds;n=this._elementFactory.createShape(r(e,{collapsed:o,hidden:s,x:Math.round(c.x),y:Math.round(c.y),width:Math.round(c.width),height:Math.round(c.height)})),l(e,"bpmn:BoundaryEvent")&&this._attachBoundary(e,n),this._canvas.addShape(n,t)}else{if(!a.$instanceOf("bpmndi:BPMNEdge"))throw new Error("unknown di "+d(a)+" for element "+d(e));var u=this._getSource(e),f=this._getTarget(e);n=this._elementFactory.createConnection(r(e,{source:u,target:f,waypoints:i(e.di.waypoint)})),this._canvas.addConnection(n,t)}return p(e)&&this.addLabel(e,n),this._eventBus.fire("bpmnElement.added",{element:n}),n},o.prototype._attachBoundary=function(e,t){var n=e.attachedToRef;if(!n)throw new Error("missing "+d(e)+"#attachedToRef");var r=this._elementRegistry.get(n.id),i=r&&r.attachers;if(!r)throw a(e,n,"attachedToRef");t.host=r,i||(r.attachers=i=[]),-1===i.indexOf(t)&&i.push(t)},o.prototype.addLabel=function(e,t){var n=f(e,t),i=this._elementFactory.createLabel(r(e,{id:e.id+"_label",labelTarget:t,type:"label",hidden:t.hidden,x:Math.round(n.x),y:Math.round(n.y),width:Math.round(n.width),height:Math.round(n.height)}));return this._canvas.addShape(i,t.parent)},o.prototype._getEnd=function(e,t){var n,r,i=e.$type;if(r=e[t+"Ref"],"source"===t&&"bpmn:DataInputAssociation"===i&&(r=r&&r[0]),("source"===t&&"bpmn:DataOutputAssociation"===i||"target"===t&&"bpmn:DataInputAssociation"===i)&&(r=e.$parent),n=r&&this._getElement(r))return n;throw r?a(e,r,t+"Ref"):new Error(d(e)+"#"+t+"Ref not specified")},o.prototype._getSource=function(e){return this._getEnd(e,"source")},o.prototype._getTarget=function(e){return this._getEnd(e,"target")},o.prototype._getElement=function(e){return this._elementRegistry.get(e.id)}},{11:11,12:12,13:13,168:168,80:80,9:9}],7:[function(e,t,n){"use strict";function r(e,t){return e.$instanceOf(t)}function i(e){return s(e.rootElements,function(e){return r(e,"bpmn:Process")||r(e,"bpmn:Collaboration")})}function a(e){function t(e,t){return function(n){e(n,t)}}function n(t,n){var r=t.gfx;if(r)throw new Error("already rendered "+l(t));return e.element(t,n)}function a(t,n){return e.root(t,n)}function s(e,t){try{return e.di&&n(e,t)}catch(r){u(r.message,{element:e,error:r}),console.error("failed to import "+l(e)),console.error(r)}}function u(t,n){e.error(t,n)}function f(e){var t=e.bpmnElement;t?t.di?u("multiple DI elements defined for "+l(t),{element:t}):(p.bind(t,"di"),t.di=e):u("no bpmnElement referenced in "+l(e),{element:e})}function h(e){d(e.plane)}function d(e){f(e),c(e.planeElement,m)}function m(e){f(e)}function y(e,t){var n=e.diagrams;if(t&&-1===n.indexOf(t))throw new Error("diagram not part of bpmn:Definitions");if(!t&&n&&n.length&&(t=n[0]),t){h(t);var o=t.plane;if(!o)throw new Error("no plane for "+l(t));var s=o.bpmnElement;if(!s){if(s=i(e),!s)return u("no process or collaboration present to display");u("correcting missing bpmnElement on "+l(o)+" to "+l(s)),o.bpmnElement=s,f(o)}var c=a(s,o);if(r(s,"bpmn:Process"))v(s,c);else{if(!r(s,"bpmn:Collaboration"))throw new Error("unsupported bpmnElement for "+l(o)+" : "+l(s));V(s,c),x(e.rootElements,c)}g(U)}}function g(e){c(e,function(e){e()})}function v(e,t){I(e,t),C(e.ioSpecification,t),T(e.artifacts,t),G.push(e)}function x(e){var n=o(e,function(e){return r(e,"bpmn:Process")&&e.laneSets&&-1===G.indexOf(e)});n.forEach(t(v))}function b(e,t){s(e,t)}function E(e,n){c(e,t(b,n))}function w(e,t){s(e,t)}function A(e,t){s(e,t)}function S(e,t){s(e,t)}function _(e,t){s(e,t)}function T(e,t){c(e,function(e){r(e,"bpmn:Association")?U.push(function(){_(e,t)}):_(e,t)})}function C(e,n){e&&(c(e.dataInputs,t(A,n)),c(e.dataOutputs,t(S,n)))}function R(e,t){I(e,t),T(e.artifacts,t)}function N(e,t){var n=s(e,t);r(e,"bpmn:SubProcess")&&R(e,n||t)}function M(e,t){s(e,t)}function D(e,t){s(e,t)}function P(e,t){s(e,t)}function k(e,t){var n=s(e,t);if(e.childLaneSet)O(e.childLaneSet,n||t);else{var r=o(e.flowNodeRef,function(e){return"bpmn:BoundaryEvent"!==e.$type});F(r,n||t)}}function O(e,n){c(e.lanes,t(k,n))}function B(e,n){c(e,t(O,n))}function I(e,t){e.laneSets?(B(e.laneSets,t),L(e.flowElements)):F(e.flowElements,t)}function L(e,t){c(e,function(e){r(e,"bpmn:SequenceFlow")?U.push(function(){M(e,t)}):r(e,"bpmn:BoundaryEvent")?U.unshift(function(){P(e,t)}):r(e,"bpmn:DataObject")||(r(e,"bpmn:DataStoreReference")?D(e,t):r(e,"bpmn:DataObjectReference")&&D(e,t))})}function F(e,n){c(e,function(e){r(e,"bpmn:SequenceFlow")?U.push(function(){M(e,n)}):r(e,"bpmn:BoundaryEvent")?U.unshift(function(){P(e,n)}):r(e,"bpmn:FlowNode")?(N(e,n),r(e,"bpmn:Activity")&&(C(e.ioSpecification,n),U.push(function(){c(e.dataInputAssociations,t(w,n)),c(e.dataOutputAssociations,t(w,n))}))):r(e,"bpmn:DataObject")||(r(e,"bpmn:DataStoreReference")?D(e,n):r(e,"bpmn:DataObjectReference")?D(e,n):u("unrecognized flowElement "+l(e)+" in context "+(n?l(n.businessObject):null),{element:e,context:n}))})}function j(e,t){var n=s(e,t),r=e.processRef;r&&v(r,n||t)}function V(e){c(e.participants,t(j)),T(e.artifacts),U.push(function(){E(e.messageFlows)})}var G=[],U=[];return{handleDefinitions:y}}var o=e(75),s=e(76),c=e(77),u=e(192),l=e(9).elementToString,p=new u({name:"bpmnElement",enumerable:!0},{name:"di"});t.exports=a},{192:192,75:75,76:76,77:77,9:9}],8:[function(e,t,n){"use strict";function r(e,t,n){function r(e){var t={root:function(e){return o.add(e)},element:function(e,t){return o.add(e,t)},error:function(e,t){c.push({message:e,context:t})}},n=new i(t);n.handleDefinitions(e)}var a,o=e.get("bpmnImporter"),s=e.get("eventBus"),c=[];s.fire("import.start");try{r(t)}catch(u){a=u}s.fire(a?"import.error":"import.success",{error:a,warnings:c}),n(a,c)}var i=e(7);t.exports.importBpmnDiagram=r},{7:7}],9:[function(e,t,n){"use strict";t.exports.elementToString=function(e){return e?"<"+e.$type+(e.id?' id="'+e.id:"")+'" />':"<null>"}},{}],10:[function(e,t,n){t.exports={bpmnImporter:["type",e(6)]}},{6:6}],11:[function(e,t,n){"use strict";var r=e(13).is,i=e(13).getBusinessObject;t.exports.isExpanded=function(e){return r(e,"bpmn:CallActivity")?!1:r(e,"bpmn:SubProcess")?i(e).di.isExpanded:r(e,"bpmn:Participant")?!!i(e).processRef:!0}},{13:13}],12:[function(e,t,n){"use strict";var r=e(168),i=t.exports.DEFAULT_LABEL_SIZE={width:90,height:20};t.exports.hasExternalLabel=function(e){return e.$instanceOf("bpmn:Event")||e.$instanceOf("bpmn:Gateway")||e.$instanceOf("bpmn:DataStoreReference")||e.$instanceOf("bpmn:DataObjectReference")||e.$instanceOf("bpmn:SequenceFlow")||e.$instanceOf("bpmn:MessageFlow")};var a=t.exports.getWaypointsMid=function(e){var t=e.length/2-1,n=e[Math.floor(t)],r=e[Math.ceil(t+.01)];return{x:n.x+(r.x-n.x)/2,y:n.y+(r.y-n.y)/2}},o=t.exports.getExternalLabelMid=function(e){return e.waypoints?a(e.waypoints):{x:e.x+e.width/2,y:e.y+e.height+i.height/2}};t.exports.getExternalLabelBounds=function(e,t){var n,a,s,c=e.di,u=c.label;return u&&u.bounds?(s=u.bounds,a={width:Math.max(i.width,s.width),height:s.height},n={x:s.x+s.width/2,y:s.y+s.height/2}):(n=o(t),a=i),r({x:n.x-a.width/2,y:n.y-a.height/2},a)}},{168:168}],13:[function(e,t,n){"use strict";function r(e,t){var n=i(e);return n&&n.$instanceOf(t)}function i(e){return e&&e.businessObject||e}t.exports.is=r,t.exports.getBusinessObject=i},{}],14:[function(e,t,n){t.exports=e(16)},{16:16}],15:[function(e,t,n){"use strict";function r(e,t){s.call(this,e,t)}var i=e(165),a=e(160),o=e(168),s=e(22),c=e(18),u=e(19);r.prototype=Object.create(s.prototype),t.exports=r,r.prototype.fromXML=function(e,t,n,r){i(t)||(r=n,n=t,t="bpmn:Definitions"),a(n)&&(r=n,n={});var s=new c(o({model:this,lax:!0},n)),u=s.handler(t);s.fromXML(e,u,r)},r.prototype.toXML=function(e,t,n){a(t)&&(n=t,t={});var r=new u(t);try{var i=r.toXML(e);n(null,i)}catch(o){n(o)}}},{160:160,165:165,168:168,18:18,19:19,22:22}],16:[function(e,t,n){"use strict";var r=e(168),i=e(15),a={bpmn:e(31),bpmndi:e(32),dc:e(33),di:e(34)};t.exports=function(e,t){return new i(r({},a,e),t)}},{15:15,168:168,31:31,32:32,33:33,34:34}],17:[function(e,t,n){"use strict";function r(e){return e.charAt(0).toUpperCase()+e.slice(1)}function i(e){return e.charAt(0).toLowerCase()+e.slice(1)}function a(e){return e.xml&&"lowerCase"===e.xml.tagAlias}function o(e){return e.xml&&e.xml.serialize}t.exports.aliasToName=function(e,t){return a(t)?r(e):e},t.exports.nameToAlias=function(e,t){return a(t)?i(e):e},t.exports.DEFAULT_NS_MAP={xsi:"http://www.w3.org/2001/XMLSchema-instance"};var s=t.exports.XSI_TYPE="xsi:type";t.exports.serializeAsType=function(e){return o(e)===s},t.exports.serializeAsProperty=function(e){return"property"===o(e)}},{}],18:[function(e,t,n){"use strict";function r(e){var t=e.attributes;return y(t,function(e,t,n){var r,i;return t.local?(i=S(t.name,t.prefix),r=i.name):r=t.prefix,e[r]=t.value,e},{})}function i(e,t,n){var r,i=S(t.value),a=e.ns[i.prefix||""],o=i.localName,s=a&&n.getPackage(a);s&&(r=s.xml&&s.xml.typePrefix,r&&0===o.indexOf(r)&&(o=o.slice(r.length)),t.value=s.prefix+":"+o)}function a(e,t,n){var r,o;if(r=e.uri||n){var s=t.getPackage(r);o=s?s.prefix:e.prefix,e.prefix=o,e.uri=r}g(e.attributes,function(n){n.uri===M&&"type"===n.local&&i(e,n,t),a(n,t,null)})}function o(e){x(this,e);var t=this.elementsById={},n=this.references=[],r=this.warnings=[];this.addReference=function(e){n.push(e)},this.addElement=function(e,n){if(!e||!n)throw new Error("[xml-reader] id or ctx must not be null");t[e]=n},this.addWarning=function(e){r.push(e)}}function s(){}function c(){}function u(){}function l(e,t){this.property=e,this.context=t}function p(e,t){this.element=t,this.propertyDesc=e}function f(){}function h(e,t,n){this.model=e,this.type=e.getType(t),this.context=n}function d(e,t,n){this.model=e,this.context=n}function m(e){e instanceof A&&(e={model:e}),x(this,{lax:!1},e)}var y=e(81),g=e(77),v=e(76),x=e(168),b=e(85),E=e(21),w=e(20).parser,A=e(22),S=e(27).parseName,_=e(30),T=_.coerceType,C=_.isSimple,R=e(17),N=R.XSI_TYPE,M=R.DEFAULT_NS_MAP.xsi,D=R.serializeAsType,P=R.aliasToName;s.prototype.handleEnd=function(){},s.prototype.handleText=function(){},s.prototype.handleNode=function(){},c.prototype=new s,c.prototype.handleNode=function(){return this},u.prototype=new s,u.prototype.handleText=function(e){this.body=(this.body||"")+e},l.prototype=new u,l.prototype.handleNode=function(e){if(this.element)throw new Error("expected no sub nodes");return this.element=this.createReference(e),this},l.prototype.handleEnd=function(){this.element.id=this.body},l.prototype.createReference=function(){return{property:this.property.ns.name,id:""}},p.prototype=new u,p.prototype.handleEnd=function(){var e=this.body,t=this.element,n=this.propertyDesc;e=T(n.type,e),n.isMany?t.get(n.name).push(e):t.set(n.name,e)},f.prototype=Object.create(u.prototype),f.prototype.handleNode=function(e){var t,n=this,r=this.element;return r?n=this.handleChild(e):(r=this.element=this.createElement(e),t=r.id,t&&this.context.addElement(t,r)),n},h.prototype=new f,h.prototype.addReference=function(e){this.context.addReference(e)},h.prototype.handleEnd=function(){var e=this.body,t=this.element,n=t.$descriptor,r=n.bodyProperty;r&&void 0!==e&&(e=T(r.type,e),t.set(r.name,e))},h.prototype.createElement=function(e){var t=r(e),n=this.type,i=n.$descriptor,a=this.context,o=new n({});return g(t,function(e,t){var n=i.propertiesByName[t];n&&n.isReference?a.addReference({element:o,property:n.ns.name,id:e}):(n&&(e=T(n.type,e)),o.set(t,e))}),o},h.prototype.getPropertyForNode=function(e){var t,n,r,i=S(e.local,e.prefix),a=this.type,o=this.model,s=a.$descriptor,c=i.name,u=s.propertiesByName[c];if(u)return D(u)&&(r=e.attributes[N])?(t=r.value,n=o.getType(t),x({},u,{effectiveType:n.$descriptor.name})):u;var l=o.getPackage(i.prefix);if(l){if(t=i.prefix+":"+P(i.localName,s.$pkg),n=o.getType(t),u=v(s.properties,function(e){return!e.isVirtual&&!e.isReference&&!e.isAttribute&&n.hasType(e.type)}))return x({},u,{effectiveType:n.$descriptor.name})}else if(u=v(s.properties,function(e){return!e.isReference&&!e.isAttribute&&"Element"===e.type}))return u;throw new Error("unrecognized element <"+i.name+">")},h.prototype.toString=function(){return"ElementDescriptor["+this.type.$descriptor.name+"]"},h.prototype.valueHandler=function(e,t){return new p(e,t)},h.prototype.referenceHandler=function(e){return new l(e,this.context)},h.prototype.handler=function(e){return"Element"===e?new d(this.model,e,this.context):new h(this.model,e,this.context)},h.prototype.handleChild=function(e){var t,n,r,i;if(t=this.getPropertyForNode(e),r=this.element,n=t.effectiveType||t.type,C(n))return this.valueHandler(t,r);i=t.isReference?this.referenceHandler(t).handleNode(e):this.handler(n).handleNode(e);var a=i.element;return void 0!==a&&(t.isMany?r.get(t.name).push(a):r.set(t.name,a),t.isReference?(x(a,{element:r}),this.context.addReference(a)):a.$parent=r),i},d.prototype=Object.create(f.prototype),d.prototype.createElement=function(e){var t=e.name,n=e.prefix,r=e.ns[n],i=e.attributes;return this.model.createAny(t,r,i)},d.prototype.handleChild=function(e){var t,n=new d(this.model,"Element",this.context).handleNode(e),r=this.element,i=n.element;return void 0!==i&&(t=r.$children=r.$children||[],t.push(i),i.$parent=r),n},d.prototype.handleText=function(e){this.body=this.body||""+e},d.prototype.handleEnd=function(){this.body&&(this.element.$body=this.body)},m.prototype.fromXML=function(e,t,n){function r(){var e,t,n=f.elementsById,r=f.references;for(e=0;t=r[e];e++){var i=t.element,a=n[t.id],o=i.$descriptor.propertiesByName[t.property];if(a||f.addWarning({message:"unresolved reference <"+t.id+">",element:t.element,property:t.property,value:t.id}),o.isMany){var s=i.get(o.name),c=s.indexOf(t);a?s[c]=a:s.splice(c,1)}else i.set(o.name,a)}}function i(e){d.pop().handleEnd()}function s(e){var t=d.peek();a(e,l);try{d.push(t.handleNode(e))}catch(n){var r=this.line,i=this.column,o="unparsable content <"+e.name+"> detected\n	line: "+r+"\n	column: "+i+"\n	nested error: "+n.message;if(!p)throw console.error("could not parse document"),console.error(n),new Error(o);f.addWarning({message:o,error:n}),console.warn("could not parse node"),console.warn(n),d.push(new c)}}function u(e){d.peek().handleText(e)}var l=this.model,p=this.lax,f=new o({parseRoot:t}),h=new w(!0,{xmlns:!0,trim:!0}),d=new E;t.context=f,d.push(t),h.onopentag=s,h.oncdata=h.ontext=u,h.onclosetag=i,h.onend=r,b(function(){var r;try{h.write(e).close()}catch(i){r=i}n(r,r?void 0:t.element,f)})},m.prototype.handler=function(e){return new h(this.model,e)},t.exports=m,t.exports.ElementHandler=h},{168:168,17:17,20:20,21:21,22:22,27:27,30:30,76:76,77:77,81:81,85:85}],19:[function(e,t,n){"use strict";function r(e){return b(e)?e:(e.prefix?e.prefix+":":"")+e.localName}function i(e,t){return t.isGeneric?t.name:w({localName:T(t.ns.localName,t.$pkg)},e)}function a(e,t){return w({localName:t.ns.localName},e)}function o(e){var t=e.$descriptor;return E(t.properties,function(t){var n=t.name;if(!e.hasOwnProperty(n))return!1;var r=e[n];return r===t["default"]?!1:t.isMany?r.length:!0})}function s(e){return e=b(e)?e:""+e,e.replace(M,function(e){return"&#"+k[e]+";"})}function c(e){return E(e,function(e){return e.isAttr})}function u(e){return E(e,function(e){return!e.isAttr})}function l(e,t){this.ns=t}function p(){}function f(e){this.ns=e}function h(e,t){this.body=[],this.attrs=[],this.parent=e,this.ns=t}function d(e,t){h.call(this,e,t)}function m(){this.value="",this.write=function(e){this.value+=e}}function y(e,t){var n=[""];this.append=function(t){return e.write(t),this},this.appendNewLine=function(){return t&&e.write("\n"),this},this.appendIndent=function(){return t&&e.write(n.join("  ")),this},this.indent=function(){return n.push(""),this},this.unindent=function(){return n.pop(),this}}function g(e){function t(t,n){var r=n||new m,i=new y(r,e.format);return e.preamble&&i.append(N),(new h).build(t).serializeTo(i),n?void 0:r.value}return e=w({format:!1,preamble:!0},e||{}),{toXML:t}}var v=e(80),x=e(77),b=e(165),E=e(75),w=e(168),A=e(30),S=e(27).parseName,_=e(17),T=_.nameToAlias,C=_.serializeAsType,R=_.serializeAsProperty,N='<?xml version="1.0" encoding="UTF-8"?>\n',M=/(<|>|'|"|&|\n\r|\n)/g,D=_.DEFAULT_NS_MAP,P=_.XSI_TYPE,k={"\n":"10","\n\r":"10",'"':"34","'":"39","<":"60",">":"62","&":"38"};l.prototype.build=function(e){return this.element=e,this},l.prototype.serializeTo=function(e){e.appendIndent().append("<"+r(this.ns)+">"+this.element.id+"</"+r(this.ns)+">").appendNewLine()},p.prototype.serializeValue=p.prototype.serializeTo=function(e){var t=this.escape;t&&e.append("<![CDATA["),e.append(this.value),t&&e.append("]]>")},p.prototype.build=function(e,t){return this.value=t,"String"===e.type&&M.test(t)&&(this.escape=!0),this},f.prototype=new p,f.prototype.serializeTo=function(e){e.appendIndent().append("<"+r(this.ns)+">"),this.serializeValue(e),e.append("</"+r(this.ns)+">").appendNewLine()},h.prototype.build=function(e){this.element=e;var t=this.parseNsAttributes(e);if(this.ns||(this.ns=this.nsTagName(e.$descriptor)),e.$descriptor.isGeneric)this.parseGeneric(e);else{var n=o(e);this.parseAttributes(c(n)),this.parseContainments(u(n)),this.parseGenericAttributes(e,t)}return this},h.prototype.nsTagName=function(e){var t=this.logNamespaceUsed(e.ns);return i(t,e)},h.prototype.nsPropertyTagName=function(e){var t=this.logNamespaceUsed(e.ns);return a(t,e)},h.prototype.isLocalNs=function(e){return e.uri===this.ns.uri},h.prototype.nsAttributeName=function(e){var t;b(e)?t=S(e):e.ns&&(t=e.ns);var n=this.logNamespaceUsed(t);return this.isLocalNs(n)?{localName:t.localName}:w({localName:t.localName},n)},h.prototype.parseGeneric=function(e){var t=this,n=this.body,r=this.attrs;x(e,function(e,i){"$body"===i?n.push((new p).build({type:"String"},e)):"$children"===i?x(e,function(e){n.push(new h(t).build(e))}):0!==i.indexOf("$")&&r.push({name:i,value:s(e)})})},h.prototype.parseNsAttributes=function(e){var t=this,n=e.$attrs,r=[];return x(n,function(e,n){var i=S(n);"xmlns"===i.prefix?t.logNamespace({prefix:i.localName,uri:e}):i.prefix||"xmlns"!==i.localName?r.push({name:n,value:e}):t.logNamespace({uri:e})}),r},h.prototype.parseGenericAttributes=function(e,t){var n=this;x(t,function(t){if(t.name!==P)try{n.addAttribute(n.nsAttributeName(t.name),t.value)}catch(r){console.warn("[writer] missing namespace information for ",t.name,"=",t.value,"on",e,r)}})},h.prototype.parseContainments=function(e){var t=this,n=this.body,r=this.element;x(e,function(e){var i=r.get(e.name),a=e.isReference,o=e.isMany,s=t.nsPropertyTagName(e);if(o||(i=[i]),e.isBody)n.push((new p).build(e,i[0]));else if(A.isSimple(e.type))x(i,function(t){n.push(new f(s).build(e,t))});else if(a)x(i,function(e){n.push(new l(t,s).build(e))});else{var c=C(e),u=R(e);x(i,function(e){var r;r=c?new d(t,s):u?new h(t,s):new h(t),n.push(r.build(e))})}})},h.prototype.getNamespaces=function(){return this.parent?this.namespaces=this.parent.getNamespaces():this.namespaces||(this.namespaces={prefixMap:{},uriMap:{},used:{}}),this.namespaces},h.prototype.logNamespace=function(e){var t=this.getNamespaces(),n=t.uriMap[e.uri];return n||(t.uriMap[e.uri]=e),t.prefixMap[e.prefix]=e.uri,e},h.prototype.logNamespaceUsed=function(e){var t=this.element,n=t.$model,r=this.getNamespaces(),i=e.prefix,a=e.uri||D[i]||r.prefixMap[i]||(n?(n.getPackage(i)||{}).uri:null);if(!a)throw new Error("no namespace uri given for prefix <"+e.prefix+">");return e=r.uriMap[a],e||(e=this.logNamespace({prefix:i,uri:a})),r.used[e.uri]||(r.used[e.uri]=e),e},h.prototype.parseAttributes=function(e){var t=this,n=this.element;x(e,function(e){t.logNamespaceUsed(e.ns);var r=n.get(e.name);e.isReference&&(r=r.id),t.addAttribute(t.nsAttributeName(e),r)})},h.prototype.addAttribute=function(e,t){var n=this.attrs;b(t)&&(t=s(t)),n.push({name:e,value:t})},h.prototype.serializeAttributes=function(e){function t(){return v(a.used,function(e){var t="xmlns"+(e.prefix?":"+e.prefix:"");return{name:t,value:e.uri}})}var n=this.attrs,i=!this.parent,a=this.namespaces;i&&(n=t().concat(n)),x(n,function(t){e.append(" ").append(r(t.name)).append('="').append(t.value).append('"')})},h.prototype.serializeTo=function(e){var t=this.body.length,n=!(1===this.body.length&&this.body[0]instanceof p);e.appendIndent().append("<"+r(this.ns)),this.serializeAttributes(e),e.append(t?">":" />"),t&&(n&&e.appendNewLine().indent(),x(this.body,function(t){t.serializeTo(e)}),n&&e.unindent().appendIndent(),e.append("</"+r(this.ns)+">")),e.appendNewLine()},d.prototype=new h,d.prototype.build=function(e){var t=e.$descriptor;this.element=e,this.typeNs=this.nsTagName(t);var n=this.typeNs,r=e.$model.getPackage(n.uri),i=r.xml&&r.xml.typePrefix||"";return this.addAttribute(this.nsAttributeName(P),(n.prefix?n.prefix+":":"")+i+t.ns.localName),h.prototype.build.call(this,e)},d.prototype.isLocalNs=function(e){return e.uri===this.typeNs.uri},t.exports=g},{165:165,168:168,17:17,27:27,30:30,75:75,77:77,80:80}],20:[function(e,t,n){(function(t){!function(n){function r(e,t){if(!(this instanceof r))return new r(e,t);var i=this;a(i),i.q=i.c="",i.bufferCheckPosition=n.MAX_BUFFER_LENGTH,i.opt=t||{},i.opt.lowercase=i.opt.lowercase||i.opt.lowercasetags,i.looseCase=i.opt.lowercase?"toLowerCase":"toUpperCase",i.tags=[],i.closed=i.closedRoot=i.sawRoot=!1,i.tag=i.error=null,i.strict=!!e,i.noscript=!(!e&&!i.opt.noscript),i.state=z.BEGIN,i.ENTITIES=Object.create(n.ENTITIES),i.attribList=[],i.opt.xmlns&&(i.ns=Object.create(G)),i.trackPosition=i.opt.position!==!1,i.trackPosition&&(i.position=i.line=i.column=0),h(i,"onready")}function i(e){for(var t=Math.max(n.MAX_BUFFER_LENGTH,10),r=0,i=0,a=C.length;a>i;i++){var o=e[C[i]].length;if(o>t)switch(C[i]){case"textNode":m(e);break;case"cdata":d(e,"oncdata",e.cdata),e.cdata="";break;case"script":d(e,"onscript",e.script),e.script="";break;default:g(e,"Max buffer length exceeded: "+C[i])}r=Math.max(r,o)}e.bufferCheckPosition=n.MAX_BUFFER_LENGTH-r+e.position}function a(e){for(var t=0,n=C.length;n>t;t++)e[C[t]]=""}function o(e){m(e),""!==e.cdata&&(d(e,"oncdata",e.cdata),e.cdata=""),""!==e.script&&(d(e,"onscript",e.script),e.script="")}function s(e,t){return new c(e,t)}function c(e,t){if(!(this instanceof c))return new c(e,t);R.apply(this),this._parser=new r(e,t),this.writable=!0,this.readable=!0;var n=this;this._parser.onend=function(){n.emit("end")},this._parser.onerror=function(e){n.emit("error",e),n._parser.error=null},this._decoder=null,M.forEach(function(e){Object.defineProperty(n,"on"+e,{get:function(){return n._parser["on"+e]},set:function(t){return t?void n.on(e,t):(n.removeAllListeners(e),n._parser["on"+e]=t)},enumerable:!0,configurable:!1})})}function u(e){return e.split("").reduce(function(e,t){return e[t]=!0,e},{})}function l(e){return"[object RegExp]"===Object.prototype.toString.call(e)}function p(e,t){return l(e)?!!t.match(e):e[t]}function f(e,t){return!p(e,t)}function h(e,t,n){e[t]&&e[t](n)}function d(e,t,n){e.textNode&&m(e),h(e,t,n)}function m(e){e.textNode=y(e.opt,e.textNode),e.textNode&&h(e,"ontext",e.textNode),e.textNode=""}function y(e,t){return e.trim&&(t=t.trim()),e.normalize&&(t=t.replace(/\s+/g," ")),t}function g(e,t){return m(e),e.trackPosition&&(t+="\nLine: "+e.line+"\nColumn: "+e.column+"\nChar: "+e.c),t=new Error(t),e.error=t,h(e,"onerror",t),e}function v(e){return e.closedRoot||x(e,"Unclosed root tag"),e.state!==z.BEGIN&&e.state!==z.TEXT&&g(e,"Unexpected end"),m(e),e.c="",e.closed=!0,h(e,"onend"),r.call(e,e.strict,e.opt),e}function x(e,t){if("object"!=typeof e||!(e instanceof r))throw new Error("bad call to strictFail");e.strict&&g(e,t)}function b(e){e.strict||(e.tagName=e.tagName[e.looseCase]());var t=e.tags[e.tags.length-1]||e,n=e.tag={name:e.tagName,attributes:{}};e.opt.xmlns&&(n.ns=t.ns),e.attribList.length=0}function E(e,t){var n=e.indexOf(":"),r=0>n?["",e]:e.split(":"),i=r[0],a=r[1];return t&&"xmlns"===e&&(i="xmlns",a=""),{prefix:i,local:a}}function w(e){if(e.strict||(e.attribName=e.attribName[e.looseCase]()),-1!==e.attribList.indexOf(e.attribName)||e.tag.attributes.hasOwnProperty(e.attribName))return e.attribName=e.attribValue="";if(e.opt.xmlns){var t=E(e.attribName,!0),n=t.prefix,r=t.local;if("xmlns"===n)if("xml"===r&&e.attribValue!==j)x(e,"xml: prefix must be bound to "+j+"\nActual: "+e.attribValue);else if("xmlns"===r&&e.attribValue!==V)x(e,"xmlns: prefix must be bound to "+V+"\nActual: "+e.attribValue);else{var i=e.tag,a=e.tags[e.tags.length-1]||e;i.ns===a.ns&&(i.ns=Object.create(a.ns)),i.ns[r]=e.attribValue}e.attribList.push([e.attribName,e.attribValue])}else e.tag.attributes[e.attribName]=e.attribValue,d(e,"onattribute",{name:e.attribName,value:e.attribValue});e.attribName=e.attribValue=""}function A(e,t){if(e.opt.xmlns){var n=e.tag,r=E(e.tagName);n.prefix=r.prefix,n.local=r.local,n.uri=n.ns[r.prefix]||"",n.prefix&&!n.uri&&(x(e,"Unbound namespace prefix: "+JSON.stringify(e.tagName)),n.uri=r.prefix);var i=e.tags[e.tags.length-1]||e;n.ns&&i.ns!==n.ns&&Object.keys(n.ns).forEach(function(t){d(e,"onopennamespace",{prefix:t,uri:n.ns[t]})});for(var a=0,o=e.attribList.length;o>a;a++){var s=e.attribList[a],c=s[0],u=s[1],l=E(c,!0),p=l.prefix,f=l.local,h=""==p?"":n.ns[p]||"",m={name:c,value:u,prefix:p,local:f,uri:h};p&&"xmlns"!=p&&!h&&(x(e,"Unbound namespace prefix: "+JSON.stringify(p)),m.uri=p),e.tag.attributes[c]=m,d(e,"onattribute",m)}e.attribList.length=0}e.tag.isSelfClosing=!!t,e.sawRoot=!0,e.tags.push(e.tag),d(e,"onopentag",e.tag),t||(e.noscript||"script"!==e.tagName.toLowerCase()?e.state=z.TEXT:e.state=z.SCRIPT,e.tag=null,e.tagName=""),e.attribName=e.attribValue="",e.attribList.length=0}function S(e){if(!e.tagName)return x(e,"Weird empty close tag."),e.textNode+="</>",void(e.state=z.TEXT);if(e.script){if("script"!==e.tagName)return e.script+="</"+e.tagName+">",e.tagName="",void(e.state=z.SCRIPT);d(e,"onscript",e.script),e.script=""}var t=e.tags.length,n=e.tagName;e.strict||(n=n[e.looseCase]());for(var r=n;t--;){var i=e.tags[t];if(i.name===r)break;x(e,"Unexpected close tag")}if(0>t)return x(e,"Unmatched closing tag: "+e.tagName),e.textNode+="</"+e.tagName+">",void(e.state=z.TEXT);e.tagName=n;for(var a=e.tags.length;a-->t;){var o=e.tag=e.tags.pop();e.tagName=e.tag.name,d(e,"onclosetag",e.tagName);var s={};for(var c in o.ns)s[c]=o.ns[c];var u=e.tags[e.tags.length-1]||e;e.opt.xmlns&&o.ns!==u.ns&&Object.keys(o.ns).forEach(function(t){var n=o.ns[t];d(e,"onclosenamespace",{prefix:t,uri:n})})}0===t&&(e.closedRoot=!0),e.tagName=e.attribValue=e.attribName="",e.attribList.length=0,e.state=z.TEXT}function _(e){var t,n=e.entity,r=n.toLowerCase(),i="";return e.ENTITIES[n]?e.ENTITIES[n]:e.ENTITIES[r]?e.ENTITIES[r]:(n=r,"#"===n.charAt(0)&&("x"===n.charAt(1)?(n=n.slice(2),t=parseInt(n,16),i=t.toString(16)):(n=n.slice(1),t=parseInt(n,10),i=t.toString(10))),n=n.replace(/^0+/,""),i.toLowerCase()!==n?(x(e,"Invalid character entity"),"&"+e.entity+";"):String.fromCodePoint(t))}function T(e){var t=this;if(this.error)throw this.error;if(t.closed)return g(t,"Cannot write after close. Assign an onready handler.");if(null===e)return v(t);for(var n=0,r="";t.c=r=e.charAt(n++);)switch(t.trackPosition&&(t.position++,"\n"===r?(t.line++,t.column=0):t.column++),t.state){case z.BEGIN:"<"===r?(t.state=z.OPEN_WAKA,t.startTagPosition=t.position):f(D,r)&&(x(t,"Non-whitespace before first tag."),t.textNode=r,t.state=z.TEXT);continue;case z.TEXT:if(t.sawRoot&&!t.closedRoot){for(var a=n-1;r&&"<"!==r&&"&"!==r;)r=e.charAt(n++),r&&t.trackPosition&&(t.position++,"\n"===r?(t.line++,t.column=0):t.column++);t.textNode+=e.substring(a,n-1)}"<"===r?(t.state=z.OPEN_WAKA,t.startTagPosition=t.position):(!f(D,r)||t.sawRoot&&!t.closedRoot||x(t,"Text data outside of root node."),"&"===r?t.state=z.TEXT_ENTITY:t.textNode+=r);continue;case z.SCRIPT:"<"===r?t.state=z.SCRIPT_ENDING:t.script+=r;continue;case z.SCRIPT_ENDING:"/"===r?t.state=z.CLOSE_TAG:(t.script+="<"+r,t.state=z.SCRIPT);continue;case z.OPEN_WAKA:if("!"===r)t.state=z.SGML_DECL,t.sgmlDecl="";else if(p(D,r));else if(p(U,r))t.state=z.OPEN_TAG,t.tagName=r;else if("/"===r)t.state=z.CLOSE_TAG,t.tagName="";else if("?"===r)t.state=z.PROC_INST,t.procInstName=t.procInstBody="";else{if(x(t,"Unencoded <"),t.startTagPosition+1<t.position){var o=t.position-t.startTagPosition;r=new Array(o).join(" ")+r}t.textNode+="<"+r,t.state=z.TEXT}continue;case z.SGML_DECL:(t.sgmlDecl+r).toUpperCase()===L?(d(t,"onopencdata"),t.state=z.CDATA,t.sgmlDecl="",t.cdata=""):t.sgmlDecl+r==="--"?(t.state=z.COMMENT,t.comment="",t.sgmlDecl=""):(t.sgmlDecl+r).toUpperCase()===F?(t.state=z.DOCTYPE,(t.doctype||t.sawRoot)&&x(t,"Inappropriately located doctype declaration"),t.doctype="",t.sgmlDecl=""):">"===r?(d(t,"onsgmldeclaration",t.sgmlDecl),t.sgmlDecl="",t.state=z.TEXT):p(O,r)?(t.state=z.SGML_DECL_QUOTED,t.sgmlDecl+=r):t.sgmlDecl+=r;continue;case z.SGML_DECL_QUOTED:r===t.q&&(t.state=z.SGML_DECL,
t.q=""),t.sgmlDecl+=r;continue;case z.DOCTYPE:">"===r?(t.state=z.TEXT,d(t,"ondoctype",t.doctype),t.doctype=!0):(t.doctype+=r,"["===r?t.state=z.DOCTYPE_DTD:p(O,r)&&(t.state=z.DOCTYPE_QUOTED,t.q=r));continue;case z.DOCTYPE_QUOTED:t.doctype+=r,r===t.q&&(t.q="",t.state=z.DOCTYPE);continue;case z.DOCTYPE_DTD:t.doctype+=r,"]"===r?t.state=z.DOCTYPE:p(O,r)&&(t.state=z.DOCTYPE_DTD_QUOTED,t.q=r);continue;case z.DOCTYPE_DTD_QUOTED:t.doctype+=r,r===t.q&&(t.state=z.DOCTYPE_DTD,t.q="");continue;case z.COMMENT:"-"===r?t.state=z.COMMENT_ENDING:t.comment+=r;continue;case z.COMMENT_ENDING:"-"===r?(t.state=z.COMMENT_ENDED,t.comment=y(t.opt,t.comment),t.comment&&d(t,"oncomment",t.comment),t.comment=""):(t.comment+="-"+r,t.state=z.COMMENT);continue;case z.COMMENT_ENDED:">"!==r?(x(t,"Malformed comment"),t.comment+="--"+r,t.state=z.COMMENT):t.state=z.TEXT;continue;case z.CDATA:"]"===r?t.state=z.CDATA_ENDING:t.cdata+=r;continue;case z.CDATA_ENDING:"]"===r?t.state=z.CDATA_ENDING_2:(t.cdata+="]"+r,t.state=z.CDATA);continue;case z.CDATA_ENDING_2:">"===r?(t.cdata&&d(t,"oncdata",t.cdata),d(t,"onclosecdata"),t.cdata="",t.state=z.TEXT):"]"===r?t.cdata+="]":(t.cdata+="]]"+r,t.state=z.CDATA);continue;case z.PROC_INST:"?"===r?t.state=z.PROC_INST_ENDING:p(D,r)?t.state=z.PROC_INST_BODY:t.procInstName+=r;continue;case z.PROC_INST_BODY:if(!t.procInstBody&&p(D,r))continue;"?"===r?t.state=z.PROC_INST_ENDING:t.procInstBody+=r;continue;case z.PROC_INST_ENDING:">"===r?(d(t,"onprocessinginstruction",{name:t.procInstName,body:t.procInstBody}),t.procInstName=t.procInstBody="",t.state=z.TEXT):(t.procInstBody+="?"+r,t.state=z.PROC_INST_BODY);continue;case z.OPEN_TAG:p(W,r)?t.tagName+=r:(b(t),">"===r?A(t):"/"===r?t.state=z.OPEN_TAG_SLASH:(f(D,r)&&x(t,"Invalid character in tag name"),t.state=z.ATTRIB));continue;case z.OPEN_TAG_SLASH:">"===r?(A(t,!0),S(t)):(x(t,"Forward-slash in opening tag not followed by >"),t.state=z.ATTRIB);continue;case z.ATTRIB:if(p(D,r))continue;">"===r?A(t):"/"===r?t.state=z.OPEN_TAG_SLASH:p(U,r)?(t.attribName=r,t.attribValue="",t.state=z.ATTRIB_NAME):x(t,"Invalid attribute name");continue;case z.ATTRIB_NAME:"="===r?t.state=z.ATTRIB_VALUE:">"===r?(x(t,"Attribute without value"),t.attribValue=t.attribName,w(t),A(t)):p(D,r)?t.state=z.ATTRIB_NAME_SAW_WHITE:p(W,r)?t.attribName+=r:x(t,"Invalid attribute name");continue;case z.ATTRIB_NAME_SAW_WHITE:if("="===r)t.state=z.ATTRIB_VALUE;else{if(p(D,r))continue;x(t,"Attribute without value"),t.tag.attributes[t.attribName]="",t.attribValue="",d(t,"onattribute",{name:t.attribName,value:""}),t.attribName="",">"===r?A(t):p(U,r)?(t.attribName=r,t.state=z.ATTRIB_NAME):(x(t,"Invalid attribute name"),t.state=z.ATTRIB)}continue;case z.ATTRIB_VALUE:if(p(D,r))continue;p(O,r)?(t.q=r,t.state=z.ATTRIB_VALUE_QUOTED):(x(t,"Unquoted attribute value"),t.state=z.ATTRIB_VALUE_UNQUOTED,t.attribValue=r);continue;case z.ATTRIB_VALUE_QUOTED:if(r!==t.q){"&"===r?t.state=z.ATTRIB_VALUE_ENTITY_Q:t.attribValue+=r;continue}w(t),t.q="",t.state=z.ATTRIB_VALUE_CLOSED;continue;case z.ATTRIB_VALUE_CLOSED:p(D,r)?t.state=z.ATTRIB:">"===r?A(t):"/"===r?t.state=z.OPEN_TAG_SLASH:p(U,r)?(x(t,"No whitespace between attributes"),t.attribName=r,t.attribValue="",t.state=z.ATTRIB_NAME):x(t,"Invalid attribute name");continue;case z.ATTRIB_VALUE_UNQUOTED:if(f(I,r)){"&"===r?t.state=z.ATTRIB_VALUE_ENTITY_U:t.attribValue+=r;continue}w(t),">"===r?A(t):t.state=z.ATTRIB;continue;case z.CLOSE_TAG:if(t.tagName)">"===r?S(t):p(W,r)?t.tagName+=r:t.script?(t.script+="</"+t.tagName,t.tagName="",t.state=z.SCRIPT):(f(D,r)&&x(t,"Invalid tagname in closing tag"),t.state=z.CLOSE_TAG_SAW_WHITE);else{if(p(D,r))continue;f(U,r)?t.script?(t.script+="</"+r,t.state=z.SCRIPT):x(t,"Invalid tagname in closing tag."):t.tagName=r}continue;case z.CLOSE_TAG_SAW_WHITE:if(p(D,r))continue;">"===r?S(t):x(t,"Invalid characters in closing tag");continue;case z.TEXT_ENTITY:case z.ATTRIB_VALUE_ENTITY_Q:case z.ATTRIB_VALUE_ENTITY_U:switch(t.state){case z.TEXT_ENTITY:var s=z.TEXT,c="textNode";break;case z.ATTRIB_VALUE_ENTITY_Q:var s=z.ATTRIB_VALUE_QUOTED,c="attribValue";break;case z.ATTRIB_VALUE_ENTITY_U:var s=z.ATTRIB_VALUE_UNQUOTED,c="attribValue"}";"===r?(t[c]+=_(t),t.entity="",t.state=s):p(B,r)?t.entity+=r:(x(t,"Invalid character entity"),t[c]+="&"+t.entity+r,t.entity="",t.state=s);continue;default:throw new Error(t,"Unknown state: "+t.state)}return t.position>=t.bufferCheckPosition&&i(t),t}n.parser=function(e,t){return new r(e,t)},n.SAXParser=r,n.SAXStream=c,n.createStream=s,n.MAX_BUFFER_LENGTH=65536;var C=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];n.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(e){function t(){this.__proto__=e}return t.prototype=e,new t}),Object.getPrototypeOf||(Object.getPrototypeOf=function(e){return e.__proto__}),Object.keys||(Object.keys=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}),r.prototype={end:function(){v(this)},write:T,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){o(this)}};try{var R=e("stream").Stream}catch(N){var R=function(){}}var M=n.EVENTS.filter(function(e){return"error"!==e&&"end"!==e});c.prototype=Object.create(R.prototype,{constructor:{value:c}}),c.prototype.write=function(n){if("function"==typeof t&&"function"==typeof t.isBuffer&&t.isBuffer(n)){if(!this._decoder){var r=e("string_decoder").StringDecoder;this._decoder=new r("utf8")}n=this._decoder.write(n)}return this._parser.write(n.toString()),this.emit("data",n),!0},c.prototype.end=function(e){return e&&e.length&&this.write(e),this._parser.end(),!0},c.prototype.on=function(e,t){var n=this;return n._parser["on"+e]||-1===M.indexOf(e)||(n._parser["on"+e]=function(){var t=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);t.splice(0,0,e),n.emit.apply(n,t)}),R.prototype.on.call(n,e,t)};var D="\r\n	 ",P="0124356789",k="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",O="'\"",B=P+k+"#",I=D+">",L="[CDATA[",F="DOCTYPE",j="http://www.w3.org/XML/1998/namespace",V="http://www.w3.org/2000/xmlns/",G={xml:j,xmlns:V};D=u(D),P=u(P),k=u(k);var U=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,W=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040\.\d-]/;O=u(O),B=u(B),I=u(I);var z=0;n.STATE={BEGIN:z++,TEXT:z++,TEXT_ENTITY:z++,OPEN_WAKA:z++,SGML_DECL:z++,SGML_DECL_QUOTED:z++,DOCTYPE:z++,DOCTYPE_QUOTED:z++,DOCTYPE_DTD:z++,DOCTYPE_DTD_QUOTED:z++,COMMENT_STARTING:z++,COMMENT:z++,COMMENT_ENDING:z++,COMMENT_ENDED:z++,CDATA:z++,CDATA_ENDING:z++,CDATA_ENDING_2:z++,PROC_INST:z++,PROC_INST_BODY:z++,PROC_INST_ENDING:z++,OPEN_TAG:z++,OPEN_TAG_SLASH:z++,ATTRIB:z++,ATTRIB_NAME:z++,ATTRIB_NAME_SAW_WHITE:z++,ATTRIB_VALUE:z++,ATTRIB_VALUE_QUOTED:z++,ATTRIB_VALUE_CLOSED:z++,ATTRIB_VALUE_UNQUOTED:z++,ATTRIB_VALUE_ENTITY_Q:z++,ATTRIB_VALUE_ENTITY_U:z++,CLOSE_TAG:z++,CLOSE_TAG_SAW_WHITE:z++,SCRIPT:z++,SCRIPT_ENDING:z++},n.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,"int":8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(n.ENTITIES).forEach(function(e){var t=n.ENTITIES[e],r="number"==typeof t?String.fromCharCode(t):t;n.ENTITIES[e]=r});for(var z in n.STATE)n.STATE[n.STATE[z]]=z;z=n.STATE,String.fromCodePoint||!function(){var e=String.fromCharCode,t=Math.floor,n=function(){var n,r,i=16384,a=[],o=-1,s=arguments.length;if(!s)return"";for(var c="";++o<s;){var u=Number(arguments[o]);if(!isFinite(u)||0>u||u>1114111||t(u)!=u)throw RangeError("Invalid code point: "+u);65535>=u?a.push(u):(u-=65536,n=(u>>10)+55296,r=u%1024+56320,a.push(n,r)),(o+1==s||a.length>i)&&(c+=e.apply(null,a),a.length=0)}return c};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:n,configurable:!0,writable:!0}):String.fromCodePoint=n}()}("undefined"==typeof n?sax={}:n)}).call(this,void 0)},{undefined:void 0}],21:[function(t,n,r){!function(t){"use strict";function i(){this.data=[null],this.top=0}function a(){return new i}i.prototype.clear=function(){return this.data=[null],this.top=0,this},i.prototype.length=function(){return this.top},i.prototype.peek=function(){return this.data[this.top]},i.prototype.pop=function(){return this.top>0?(this.top--,this.data.pop()):void 0},i.prototype.push=function(e){return this.data[++this.top]=e,this},"undefined"!=typeof r?n.exports=a:"function"==typeof e?e(function(){return a}):t.stack=a}(this)},{}],22:[function(e,t,n){t.exports=e(26)},{26:26}],23:[function(e,t,n){"use strict";function r(){}r.prototype.get=function(e){return this.$model.properties.get(this,e)},r.prototype.set=function(e,t){this.$model.properties.set(this,e,t)},t.exports=r},{}],24:[function(e,t,n){"use strict";function r(e){this.ns=e,this.name=e.name,this.allTypes=[],this.properties=[],this.propertiesByName={}}var i=e(174),a=e(168),o=e(77),s=e(27).parseName;t.exports=r,r.prototype.build=function(){return i(this,["ns","name","allTypes","properties","propertiesByName","bodyProperty"])},r.prototype.addProperty=function(e,t){this.addNamedProperty(e,!0);var n=this.properties;void 0!==t?n.splice(t,0,e):n.push(e)},r.prototype.replaceProperty=function(e,t){var n=e.ns,r=this.properties,i=this.propertiesByName,a=e.name!==t.name;if(e.isBody){if(!t.isBody)throw new Error("property <"+t.ns.name+"> must be body property to refine <"+e.ns.name+">");this.setBodyProperty(t,!1)}this.addNamedProperty(t,a);var o=r.indexOf(e);if(-1===o)throw new Error("property <"+n.name+"> not found in property list");r[o]=t,i[n.name]=i[n.localName]=t},r.prototype.redefineProperty=function(e){var t=e.ns.prefix,n=e.redefines.split("#"),r=s(n[0],t),i=s(n[1],r.prefix).name,a=this.propertiesByName[i];if(!a)throw new Error("refined property <"+i+"> not found");this.replaceProperty(a,e),delete e.redefines},r.prototype.addNamedProperty=function(e,t){var n=e.ns,r=this.propertiesByName;t&&(this.assertNotDefined(e,n.name),this.assertNotDefined(e,n.localName)),r[n.name]=r[n.localName]=e},r.prototype.removeNamedProperty=function(e){var t=e.ns,n=this.propertiesByName;delete n[t.name],delete n[t.localName]},r.prototype.setBodyProperty=function(e,t){if(t&&this.bodyProperty)throw new Error("body property defined multiple times (<"+this.bodyProperty.ns.name+">, <"+e.ns.name+">)");this.bodyProperty=e},r.prototype.addIdProperty=function(e){var t=s(e,this.ns.prefix),n={name:t.localName,type:"String",isAttr:!0,ns:t};this.addProperty(n,0)},r.prototype.assertNotDefined=function(e,t){var n=e.name,r=this.propertiesByName[n];if(r)throw new Error("property <"+n+"> already defined; override of <"+r.definedBy.ns.name+"#"+r.ns.name+"> by <"+e.definedBy.ns.name+"#"+e.ns.name+"> not allowed without redefines")},r.prototype.hasProperty=function(e){return this.propertiesByName[e]},r.prototype.addTrait=function(e){var t=this.allTypes;-1===t.indexOf(e)&&(o(e.properties,function(t){t=a({},t,{name:t.ns.localName}),Object.defineProperty(t,"definedBy",{value:e}),t.redefines?this.redefineProperty(t):(t.isBody&&this.setBodyProperty(t),this.addProperty(t))},this),t.push(e))}},{168:168,174:174,27:27,77:77}],25:[function(e,t,n){"use strict";function r(e,t){this.model=e,this.properties=t}var i=e(77),a=e(23);t.exports=r,r.prototype.createType=function(e){function t(e){r.define(this,"$type",{value:s,enumerable:!0}),r.define(this,"$attrs",{value:{}}),r.define(this,"$parent",{writable:!0}),i(e,function(e,t){this.set(t,e)},this)}var n=this.model,r=this.properties,o=Object.create(a.prototype);i(e.properties,function(e){e.isMany||void 0===e["default"]||(o[e.name]=e["default"])}),r.defineModel(o,n),r.defineDescriptor(o,e);var s=e.ns.name;return t.prototype=o,t.hasType=o.$instanceOf=this.model.hasType,r.defineModel(t,n),r.defineDescriptor(t,e),t}},{23:23,77:77}],26:[function(e,t,n){"use strict";function r(e,t){t=t||{},this.properties=new l(this),this.factory=new c(this,this.properties),this.registry=new u(e,this.properties,t),this.typeCache={}}var i=e(165),a=e(163),o=e(77),s=e(76),c=e(25),u=e(29),l=e(28),p=e(27).parseName;t.exports=r,r.prototype.create=function(e,t){var n=this.getType(e);if(!n)throw new Error("unknown type <"+e+">");return new n(t)},r.prototype.getType=function(e){var t=this.typeCache,n=i(e)?e:e.ns.name,r=t[n];return r||(e=this.registry.getEffectiveDescriptor(n),r=t[n]=this.factory.createType(e)),r},r.prototype.createAny=function(e,t,n){var r=p(e),i={$type:e},s={name:e,isGeneric:!0,ns:{prefix:r.prefix,localName:r.localName,uri:t}};return this.properties.defineDescriptor(i,s),this.properties.defineModel(i,this),this.properties.define(i,"$parent",{enumerable:!1,writable:!0}),o(n,function(e,t){a(e)&&void 0!==e.value?i[e.name]=e.value:i[t]=e}),i},r.prototype.getPackage=function(e){return this.registry.getPackage(e)},r.prototype.getPackages=function(){return this.registry.getPackages()},r.prototype.getElementDescriptor=function(e){return e.$descriptor},r.prototype.hasType=function(e,t){void 0===t&&(t=e,e=this);var n=e.$model.getElementDescriptor(e);return!!s(n.allTypes,function(e){return e.name===t})},r.prototype.getPropertyDescriptor=function(e,t){return this.getElementDescriptor(e).propertiesByName[t]}},{163:163,165:165,25:25,27:27,28:28,29:29,76:76,77:77}],27:[function(e,t,n){"use strict";t.exports.parseName=function(e,t){var n,r,i=e.split(/:/);if(1===i.length)n=e,r=t;else{if(2!==i.length)throw new Error("expected <prefix:localName> or <localName>, got "+e);n=i[1],r=i[0]}return e=(r?r+":":"")+n,{name:e,prefix:r,localName:n}}},{}],28:[function(e,t,n){"use strict";function r(e){this.model=e}t.exports=r,r.prototype.set=function(e,t,n){var r=this.model.getPropertyDescriptor(e,t);r?Object.defineProperty(e,r.name,{enumerable:!r.isReference,writable:!0,value:n}):e.$attrs[t]=n},r.prototype.get=function(e,t){var n=this.model.getPropertyDescriptor(e,t);if(!n)return e.$attrs[t];var r=n.name;return!e[r]&&n.isMany&&Object.defineProperty(e,r,{enumerable:!n.isReference,writable:!0,value:[]}),e[r]},r.prototype.define=function(e,t,n){Object.defineProperty(e,t,n)},r.prototype.defineDescriptor=function(e,t){this.define(e,"$descriptor",{value:t})},r.prototype.defineModel=function(e,t){this.define(e,"$model",{value:t})}},{}],29:[function(e,t,n){"use strict";function r(e,t,n){this.options=i({generateId:"id"},n||{}),this.packageMap={},this.typeMap={},this.packages=[],this.properties=t,a(e,this.registerPackage,this)}var i=e(168),a=e(77),o=e(30),s=e(24),c=e(27).parseName,u=o.isBuiltIn;t.exports=r,r.prototype.getPackage=function(e){return this.packageMap[e]},r.prototype.getPackages=function(){return this.packages},r.prototype.registerPackage=function(e){e=i({},e),a(e.types,function(t){this.registerType(t,e)},this),this.packageMap[e.uri]=this.packageMap[e.prefix]=e,this.packages.push(e)},r.prototype.registerType=function(e,t){e=i({},e,{superClass:(e.superClass||[]).slice(),"extends":(e["extends"]||[]).slice(),properties:(e.properties||[]).slice()});var n=c(e.name,t.prefix),r=n.name,o={};a(e.properties,function(e){var t=c(e.name,n.prefix),r=t.name;u(e.type)||(e.type=c(e.type,t.prefix).name),i(e,{ns:t,name:r}),o[r]=e}),i(e,{ns:n,name:r,propertiesByName:o}),a(e["extends"],function(e){var t=this.typeMap[e];t.traits=t.traits||[],t.traits.push(r)},this),this.definePackage(e,t),this.typeMap[r]=e},r.prototype.mapTypes=function(e,t){function n(n){var r=c(n,u(n)?"":e.prefix);i.mapTypes(r,t)}var r=u(e.name)?{name:e.name}:this.typeMap[e.name],i=this;if(!r)throw new Error("unknown type <"+e.name+">");a(r.superClass,n),t(r),a(r.traits,n)},r.prototype.getEffectiveDescriptor=function(e){var t=c(e),n=new s(t);this.mapTypes(t,function(e){n.addTrait(e)});var r=this.options.generateId;r&&!n.hasProperty(r)&&n.addIdProperty(r);var i=n.build();return this.definePackage(i,i.allTypes[i.allTypes.length-1].$pkg),i},r.prototype.definePackage=function(e,t){this.properties.define(e,"$pkg",{value:t})}},{168:168,24:24,27:27,30:30,77:77}],30:[function(e,t,n){"use strict";var r={String:!0,Boolean:!0,Integer:!0,Real:!0,Element:!0},i={String:function(e){return e},Boolean:function(e){return"true"===e},Integer:function(e){return parseInt(e,10)},Real:function(e){return parseFloat(e,10)}};t.exports.coerceType=function(e,t){var n=i[e];return n?n(t):t},t.exports.isBuiltIn=function(e){return!!r[e]},t.exports.isSimple=function(e){return!!i[e]}},{}],31:[function(e,t,n){t.exports={name:"BPMN20",uri:"http://www.omg.org/spec/BPMN/20100524/MODEL",associations:[],types:[{name:"Interface",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"operations",type:"Operation",isMany:!0},{name:"implementationRef",type:"String",isAttr:!0}]},{name:"Operation",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"inMessageRef",type:"Message",isAttr:!0,isReference:!0},{name:"outMessageRef",type:"Message",isAttr:!0,isReference:!0},{name:"errorRefs",type:"Error",isMany:!0,isReference:!0},{name:"implementationRef",type:"String",isAttr:!0}]},{name:"EndPoint",superClass:["RootElement"]},{name:"Auditing",superClass:["BaseElement"]},{name:"GlobalTask",superClass:["CallableElement"],properties:[{name:"resources",type:"ResourceRole",isMany:!0}]},{name:"Monitoring",superClass:["BaseElement"]},{name:"Performer",superClass:["ResourceRole"]},{name:"Process",superClass:["FlowElementsContainer","CallableElement"],properties:[{name:"processType",type:"ProcessType",isAttr:!0},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"properties",type:"Property",isMany:!0},{name:"artifacts",type:"Artifact",isMany:!0},{name:"resources",type:"ResourceRole",isMany:!0},{name:"correlationSubscriptions",type:"CorrelationSubscription",isMany:!0},{name:"supports",type:"Process",isMany:!0,isReference:!0},{name:"definitionalCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"isExecutable",isAttr:!0,type:"Boolean"}]},{name:"LaneSet",superClass:["BaseElement"],properties:[{name:"lanes",type:"Lane",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Lane",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"childLaneSet",type:"LaneSet",xml:{serialize:"xsi:type"}},{name:"partitionElementRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"flowNodeRef",type:"FlowNode",isMany:!0,isReference:!0},{name:"partitionElement",type:"BaseElement"}]},{name:"GlobalManualTask",superClass:["GlobalTask"]},{name:"ManualTask",superClass:["Task"]},{name:"UserTask",superClass:["Task"],properties:[{name:"renderings",type:"Rendering",isMany:!0},{name:"implementation",isAttr:!0,type:"String"}]},{name:"Rendering",superClass:["BaseElement"]},{name:"HumanPerformer",superClass:["Performer"]},{name:"PotentialOwner",superClass:["HumanPerformer"]},{name:"GlobalUserTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"renderings",type:"Rendering",isMany:!0}]},{name:"Gateway",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"gatewayDirection",type:"GatewayDirection","default":"Unspecified",isAttr:!0}]},{name:"EventBasedGateway",superClass:["Gateway"],properties:[{name:"instantiate","default":!1,isAttr:!0,type:"Boolean"},{name:"eventGatewayType",type:"EventBasedGatewayType",isAttr:!0,"default":"Exclusive"}]},{name:"ComplexGateway",superClass:["Gateway"],properties:[{name:"activationCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ExclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"InclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ParallelGateway",superClass:["Gateway"]},{name:"RootElement",isAbstract:!0,superClass:["BaseElement"]},{name:"Relationship",superClass:["BaseElement"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"direction",type:"RelationshipDirection",isAttr:!0},{name:"source",isMany:!0,isReference:!0,type:"Element"},{name:"target",isMany:!0,isReference:!0,type:"Element"}]},{name:"BaseElement",isAbstract:!0,properties:[{name:"id",isAttr:!0,type:"String"},{name:"documentation",type:"Documentation",isMany:!0},{name:"extensionDefinitions",type:"ExtensionDefinition",isMany:!0,isReference:!0},{name:"extensionElements",type:"ExtensionElements"}]},{name:"Extension",properties:[{name:"mustUnderstand","default":!1,isAttr:!0,type:"Boolean"},{name:"definition",type:"ExtensionDefinition"}]},{name:"ExtensionDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"extensionAttributeDefinitions",type:"ExtensionAttributeDefinition",isMany:!0}]},{name:"ExtensionAttributeDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"type",isAttr:!0,type:"String"},{name:"isReference","default":!1,isAttr:!0,type:"Boolean"},{name:"extensionDefinition",type:"ExtensionDefinition",isAttr:!0,isReference:!0}]},{name:"ExtensionElements",properties:[{name:"valueRef",isAttr:!0,isReference:!0,type:"Element"},{name:"values",type:"Element",isMany:!0},{name:"extensionAttributeDefinition",type:"ExtensionAttributeDefinition",isAttr:!0,isReference:!0}]},{name:"Documentation",superClass:["BaseElement"],properties:[{name:"text",type:"String",isBody:!0},{name:"textFormat","default":"text/plain",isAttr:!0,type:"String"}]},{name:"Event",isAbstract:!0,superClass:["FlowNode","InteractionNode"],properties:[{name:"properties",type:"Property",isMany:!0}]},{name:"IntermediateCatchEvent",superClass:["CatchEvent"]},{name:"IntermediateThrowEvent",superClass:["ThrowEvent"]},{name:"EndEvent",superClass:["ThrowEvent"]},{name:"StartEvent",superClass:["CatchEvent"],properties:[{name:"isInterrupting","default":!0,isAttr:!0,type:"Boolean"}]},{name:"ThrowEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"inputSet",type:"InputSet"},{name:"eventDefinitionRefs",type:"EventDefinition",isMany:!0,isReference:!0},{name:"dataInputAssociation",type:"DataInputAssociation",isMany:!0},{name:"dataInputs",type:"DataInput",isMany:!0},{name:"eventDefinitions",type:"EventDefinition",isMany:!0}]},{name:"CatchEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"parallelMultiple",isAttr:!0,type:"Boolean","default":!1},{name:"outputSet",type:"OutputSet"},{name:"eventDefinitionRefs",type:"EventDefinition",isMany:!0,isReference:!0},{name:"dataOutputAssociation",type:"DataOutputAssociation",isMany:!0},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"eventDefinitions",type:"EventDefinition",isMany:!0}]},{name:"BoundaryEvent",superClass:["CatchEvent"],properties:[{name:"cancelActivity","default":!0,isAttr:!0,type:"Boolean"},{name:"attachedToRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"EventDefinition",isAbstract:!0,superClass:["RootElement"]},{name:"CancelEventDefinition",superClass:["EventDefinition"]},{name:"ErrorEventDefinition",superClass:["EventDefinition"],properties:[{name:"errorRef",type:"Error",isAttr:!0,isReference:!0}]},{name:"TerminateEventDefinition",superClass:["EventDefinition"]},{name:"EscalationEventDefinition",superClass:["EventDefinition"],properties:[{name:"escalationRef",type:"Escalation",isAttr:!0,isReference:!0}]},{name:"Escalation",properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"escalationCode",isAttr:!0,type:"String"}],superClass:["RootElement"]},{name:"CompensateEventDefinition",superClass:["EventDefinition"],properties:[{name:"waitForCompletion",isAttr:!0,type:"Boolean"},{name:"activityRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"TimerEventDefinition",superClass:["EventDefinition"],properties:[{name:"timeDate",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeCycle",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeDuration",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"LinkEventDefinition",superClass:["EventDefinition"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"target",type:"LinkEventDefinition",isAttr:!0,isReference:!0},{name:"source",type:"LinkEventDefinition",isMany:!0,isReference:!0}]},{name:"MessageEventDefinition",superClass:["EventDefinition"],properties:[{name:"messageRef",type:"Message",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"ConditionalEventDefinition",superClass:["EventDefinition"],properties:[{name:"condition",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"SignalEventDefinition",superClass:["EventDefinition"],properties:[{name:"signalRef",type:"Signal",isAttr:!0,isReference:!0}]},{name:"Signal",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ImplicitThrowEvent",superClass:["ThrowEvent"]},{name:"DataState",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"ItemAwareElement",superClass:["BaseElement"],properties:[{name:"itemSubjectRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"dataState",type:"DataState"}]},{name:"DataAssociation",superClass:["BaseElement"],properties:[{name:"transformation",type:"FormalExpression"},{name:"assignment",type:"Assignment",isMany:!0},{name:"sourceRef",type:"ItemAwareElement",isMany:!0,isReference:!0},{name:"targetRef",type:"ItemAwareElement",isReference:!0}]},{name:"DataInput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"},{name:"inputSetRefs",type:"InputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"inputSetWithOptional",type:"InputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"inputSetWithWhileExecuting",type:"InputSet",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"DataOutput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"},{name:"outputSetRefs",type:"OutputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"outputSetWithOptional",type:"OutputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"outputSetWithWhileExecuting",type:"OutputSet",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"InputSet",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"dataInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"optionalInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"whileExecutingInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"outputSetRefs",type:"OutputSet",isMany:!0,isReference:!0}]},{name:"OutputSet",superClass:["BaseElement"],properties:[{name:"dataOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"inputSetRefs",type:"InputSet",isMany:!0,isReference:!0},{name:"optionalOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"whileExecutingOutputRefs",type:"DataOutput",isMany:!0,isReference:!0}]},{name:"Property",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"DataInputAssociation",superClass:["DataAssociation"]},{name:"DataOutputAssociation",superClass:["DataAssociation"]},{name:"InputOutputSpecification",superClass:["BaseElement"],properties:[{name:"dataInputs",type:"DataInput",isMany:!0},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"inputSets",type:"InputSet",isMany:!0},{name:"outputSets",type:"OutputSet",isMany:!0}]},{name:"DataObject",superClass:["FlowElement","ItemAwareElement"],properties:[{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"}]},{name:"InputOutputBinding",properties:[{name:"inputDataRef",type:"InputSet",isAttr:!0,isReference:!0},{name:"outputDataRef",type:"OutputSet",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"Assignment",superClass:["BaseElement"],properties:[{name:"from",type:"Expression",xml:{serialize:"xsi:type"}},{name:"to",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"DataStore",superClass:["RootElement","ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"capacity",isAttr:!0,type:"Integer"},{name:"isUnlimited","default":!0,isAttr:!0,type:"Boolean"}]},{name:"DataStoreReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataStoreRef",type:"DataStore",isAttr:!0,isReference:!0}]},{name:"DataObjectReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataObjectRef",type:"DataObject",isAttr:!0,isReference:!0}]},{name:"ConversationLink",superClass:["BaseElement"],properties:[{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ConversationAssociation",superClass:["BaseElement"],properties:[{name:"innerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0},{name:"outerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0}]},{name:"CallConversation",superClass:["ConversationNode"],properties:[{name:"calledCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"Conversation",superClass:["ConversationNode"]
},{name:"SubConversation",superClass:["ConversationNode"],properties:[{name:"conversationNodes",type:"ConversationNode",isMany:!0}]},{name:"ConversationNode",isAbstract:!0,superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRefs",type:"Participant",isMany:!0,isReference:!0},{name:"messageFlowRefs",type:"MessageFlow",isMany:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0}]},{name:"GlobalConversation",superClass:["Collaboration"]},{name:"PartnerEntity",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"PartnerRole",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"CorrelationProperty",superClass:["RootElement"],properties:[{name:"correlationPropertyRetrievalExpression",type:"CorrelationPropertyRetrievalExpression",isMany:!0},{name:"name",isAttr:!0,type:"String"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Error",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"errorCode",isAttr:!0,type:"String"}]},{name:"CorrelationKey",superClass:["BaseElement"],properties:[{name:"correlationPropertyRef",type:"CorrelationProperty",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Expression",superClass:["BaseElement"],isAbstract:!0},{name:"FormalExpression",superClass:["Expression"],properties:[{name:"language",isAttr:!0,type:"String"},{name:"body",type:"String",isBody:!0},{name:"evaluatesToTypeRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Message",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"itemRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"ItemDefinition",superClass:["RootElement"],properties:[{name:"itemKind",type:"ItemKind",isAttr:!0},{name:"structureRef",type:"String",isAttr:!0},{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"},{name:"import",type:"Import",isAttr:!0,isReference:!0}]},{name:"FlowElement",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"categoryValueRef",type:"CategoryValue",isMany:!0,isReference:!0}]},{name:"SequenceFlow",superClass:["FlowElement"],properties:[{name:"isImmediate",isAttr:!0,type:"Boolean"},{name:"conditionExpression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"sourceRef",type:"FlowNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"FlowNode",isAttr:!0,isReference:!0}]},{name:"FlowElementsContainer",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"laneSets",type:"LaneSet",isMany:!0},{name:"flowElements",type:"FlowElement",isMany:!0}]},{name:"CallableElement",isAbstract:!0,superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"supportedInterfaceRefs",type:"Interface",isMany:!0,isReference:!0},{name:"ioBinding",type:"InputOutputBinding",isMany:!0,xml:{serialize:"property"}}]},{name:"FlowNode",isAbstract:!0,superClass:["FlowElement"],properties:[{name:"incoming",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"outgoing",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"lanes",type:"Lane",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"CorrelationPropertyRetrievalExpression",superClass:["BaseElement"],properties:[{name:"messagePath",type:"FormalExpression"},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"CorrelationPropertyBinding",superClass:["BaseElement"],properties:[{name:"dataPath",type:"FormalExpression"},{name:"correlationPropertyRef",type:"CorrelationProperty",isAttr:!0,isReference:!0}]},{name:"Resource",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"resourceParameters",type:"ResourceParameter",isMany:!0}]},{name:"ResourceParameter",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isRequired",isAttr:!0,type:"Boolean"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"CorrelationSubscription",superClass:["BaseElement"],properties:[{name:"correlationKeyRef",type:"CorrelationKey",isAttr:!0,isReference:!0},{name:"correlationPropertyBinding",type:"CorrelationPropertyBinding",isMany:!0}]},{name:"MessageFlow",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"MessageFlowAssociation",superClass:["BaseElement"],properties:[{name:"innerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0},{name:"outerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0}]},{name:"InteractionNode",isAbstract:!0,properties:[{name:"incomingConversationLinks",type:"ConversationLink",isVirtual:!0,isMany:!0,isReference:!0},{name:"outgoingConversationLinks",type:"ConversationLink",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"Participant",superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"interfaceRefs",type:"Interface",isMany:!0,isReference:!0},{name:"participantMultiplicity",type:"ParticipantMultiplicity"},{name:"endPointRefs",type:"EndPoint",isMany:!0,isReference:!0},{name:"processRef",type:"Process",isAttr:!0,isReference:!0}]},{name:"ParticipantAssociation",superClass:["BaseElement"],properties:[{name:"innerParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"outerParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"ParticipantMultiplicity",properties:[{name:"minimum","default":0,isAttr:!0,type:"Integer"},{name:"maximum","default":1,isAttr:!0,type:"Integer"}]},{name:"Collaboration",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"choreographyRef",type:"Choreography",isMany:!0,isReference:!0},{name:"artifacts",type:"Artifact",isMany:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0},{name:"messageFlowAssociations",type:"MessageFlowAssociation",isMany:!0},{name:"conversationAssociations",type:"ConversationAssociation"},{name:"participants",type:"Participant",isMany:!0},{name:"messageFlows",type:"MessageFlow",isMany:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"conversations",type:"ConversationNode",isMany:!0},{name:"conversationLinks",type:"ConversationLink",isMany:!0}]},{name:"ChoreographyActivity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"participantRefs",type:"Participant",isMany:!0,isReference:!0},{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"loopType",type:"ChoreographyLoopType","default":"None",isAttr:!0}]},{name:"CallChoreography",superClass:["ChoreographyActivity"],properties:[{name:"calledChoreographyRef",type:"Choreography",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"SubChoreography",superClass:["ChoreographyActivity","FlowElementsContainer"],properties:[{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"ChoreographyTask",superClass:["ChoreographyActivity"],properties:[{name:"messageFlowRef",type:"MessageFlow",isMany:!0,isReference:!0}]},{name:"Choreography",superClass:["FlowElementsContainer","Collaboration"]},{name:"GlobalChoreographyTask",superClass:["Choreography"],properties:[{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"TextAnnotation",superClass:["Artifact"],properties:[{name:"text",type:"String"},{name:"textFormat","default":"text/plain",isAttr:!0,type:"String"}]},{name:"Group",superClass:["Artifact"],properties:[{name:"categoryValueRef",type:"CategoryValue",isAttr:!0,isReference:!0}]},{name:"Association",superClass:["Artifact"],properties:[{name:"associationDirection",type:"AssociationDirection",isAttr:!0},{name:"sourceRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"targetRef",type:"BaseElement",isAttr:!0,isReference:!0}]},{name:"Category",superClass:["RootElement"],properties:[{name:"categoryValue",type:"CategoryValue",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Artifact",isAbstract:!0,superClass:["BaseElement"]},{name:"CategoryValue",superClass:["BaseElement"],properties:[{name:"categorizedFlowElements",type:"FlowElement",isVirtual:!0,isMany:!0,isReference:!0},{name:"value",isAttr:!0,type:"String"}]},{name:"Activity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"isForCompensation","default":!1,isAttr:!0,type:"Boolean"},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"boundaryEventRefs",type:"BoundaryEvent",isMany:!0,isReference:!0},{name:"properties",type:"Property",isMany:!0},{name:"dataInputAssociations",type:"DataInputAssociation",isMany:!0},{name:"dataOutputAssociations",type:"DataOutputAssociation",isMany:!0},{name:"startQuantity","default":1,isAttr:!0,type:"Integer"},{name:"resources",type:"ResourceRole",isMany:!0},{name:"completionQuantity","default":1,isAttr:!0,type:"Integer"},{name:"loopCharacteristics",type:"LoopCharacteristics"}]},{name:"ServiceTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"SubProcess",superClass:["Activity","FlowElementsContainer","InteractionNode"],properties:[{name:"triggeredByEvent","default":!1,isAttr:!0,type:"Boolean"},{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"LoopCharacteristics",isAbstract:!0,superClass:["BaseElement"]},{name:"MultiInstanceLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"isSequential","default":!1,isAttr:!0,type:"Boolean"},{name:"behavior",type:"MultiInstanceBehavior","default":"All",isAttr:!0},{name:"loopCardinality",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopDataInputRef",type:"ItemAwareElement",isAttr:!0,isReference:!0},{name:"loopDataOutputRef",type:"ItemAwareElement",isAttr:!0,isReference:!0},{name:"inputDataItem",type:"DataInput"},{name:"outputDataItem",type:"DataOutput"},{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"complexBehaviorDefinition",type:"ComplexBehaviorDefinition",isMany:!0},{name:"oneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0},{name:"noneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0}]},{name:"StandardLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"testBefore","default":!1,isAttr:!0,type:"Boolean"},{name:"loopCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopMaximum",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"CallActivity",superClass:["Activity"],properties:[{name:"calledElement",type:"String",isAttr:!0}]},{name:"Task",superClass:["Activity","InteractionNode"]},{name:"SendTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ReceiveTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"instantiate","default":!1,isAttr:!0,type:"Boolean"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ScriptTask",superClass:["Task"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"script",type:"String"}]},{name:"BusinessRuleTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"AdHocSubProcess",superClass:["SubProcess"],properties:[{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"ordering",type:"AdHocOrdering",isAttr:!0},{name:"cancelRemainingInstances","default":!0,isAttr:!0,type:"Boolean"}]},{name:"Transaction",superClass:["SubProcess"],properties:[{name:"protocol",isAttr:!0,type:"String"},{name:"method",isAttr:!0,type:"String"}]},{name:"GlobalScriptTask",superClass:["GlobalTask"],properties:[{name:"scriptLanguage",isAttr:!0,type:"String"},{name:"script",isAttr:!0,type:"String"}]},{name:"GlobalBusinessRuleTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"ComplexBehaviorDefinition",superClass:["BaseElement"],properties:[{name:"condition",type:"FormalExpression"},{name:"event",type:"ImplicitThrowEvent"}]},{name:"ResourceRole",superClass:["BaseElement"],properties:[{name:"resourceRef",type:"Resource",isReference:!0},{name:"resourceParameterBindings",type:"ResourceParameterBinding",isMany:!0},{name:"resourceAssignmentExpression",type:"ResourceAssignmentExpression"},{name:"name",isAttr:!0,type:"String"}]},{name:"ResourceParameterBinding",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"parameterRef",type:"ResourceParameter",isAttr:!0,isReference:!0}]},{name:"ResourceAssignmentExpression",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"Import",properties:[{name:"importType",isAttr:!0,type:"String"},{name:"location",isAttr:!0,type:"String"},{name:"namespace",isAttr:!0,type:"String"}]},{name:"Definitions",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"targetNamespace",isAttr:!0,type:"String"},{name:"expressionLanguage","default":"http://www.w3.org/1999/XPath",isAttr:!0,type:"String"},{name:"typeLanguage","default":"http://www.w3.org/2001/XMLSchema",isAttr:!0,type:"String"},{name:"imports",type:"Import",isMany:!0},{name:"extensions",type:"Extension",isMany:!0},{name:"rootElements",type:"RootElement",isMany:!0},{name:"diagrams",isMany:!0,type:"bpmndi:BPMNDiagram"},{name:"exporter",isAttr:!0,type:"String"},{name:"relationships",type:"Relationship",isMany:!0},{name:"exporterVersion",isAttr:!0,type:"String"}]}],emumerations:[{name:"ProcessType",literalValues:[{name:"None"},{name:"Public"},{name:"Private"}]},{name:"GatewayDirection",literalValues:[{name:"Unspecified"},{name:"Converging"},{name:"Diverging"},{name:"Mixed"}]},{name:"EventBasedGatewayType",literalValues:[{name:"Parallel"},{name:"Exclusive"}]},{name:"RelationshipDirection",literalValues:[{name:"None"},{name:"Forward"},{name:"Backward"},{name:"Both"}]},{name:"ItemKind",literalValues:[{name:"Physical"},{name:"Information"}]},{name:"ChoreographyLoopType",literalValues:[{name:"None"},{name:"Standard"},{name:"MultiInstanceSequential"},{name:"MultiInstanceParallel"}]},{name:"AssociationDirection",literalValues:[{name:"None"},{name:"One"},{name:"Both"}]},{name:"MultiInstanceBehavior",literalValues:[{name:"None"},{name:"One"},{name:"All"},{name:"Complex"}]},{name:"AdHocOrdering",literalValues:[{name:"Parallel"},{name:"Sequential"}]}],prefix:"bpmn",xml:{tagAlias:"lowerCase",typePrefix:"t"}}},{}],32:[function(e,t,n){t.exports={name:"BPMNDI",uri:"http://www.omg.org/spec/BPMN/20100524/DI",types:[{name:"BPMNDiagram",properties:[{name:"plane",type:"BPMNPlane",redefines:"di:Diagram#rootElement"},{name:"labelStyle",type:"BPMNLabelStyle",isMany:!0}],superClass:["di:Diagram"]},{name:"BPMNPlane",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"}],superClass:["di:Plane"]},{name:"BPMNShape",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"isHorizontal",isAttr:!0,type:"Boolean"},{name:"isExpanded",isAttr:!0,type:"Boolean"},{name:"isMarkerVisible",isAttr:!0,type:"Boolean"},{name:"label",type:"BPMNLabel"},{name:"isMessageVisible",isAttr:!0,type:"Boolean"},{name:"participantBandKind",type:"ParticipantBandKind",isAttr:!0},{name:"choreographyActivityShape",type:"BPMNShape",isAttr:!0,isReference:!0}],superClass:["di:LabeledShape"]},{name:"BPMNEdge",properties:[{name:"label",type:"BPMNLabel"},{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"sourceElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#source"},{name:"targetElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#target"},{name:"messageVisibleKind",type:"MessageVisibleKind",isAttr:!0,"default":"initiating"}],superClass:["di:LabeledEdge"]},{name:"BPMNLabel",properties:[{name:"labelStyle",type:"BPMNLabelStyle",isAttr:!0,isReference:!0,redefines:"di:DiagramElement#style"}],superClass:["di:Label"]},{name:"BPMNLabelStyle",properties:[{name:"font",type:"dc:Font"}],superClass:["di:Style"]}],emumerations:[{name:"ParticipantBandKind",literalValues:[{name:"top_initiating"},{name:"middle_initiating"},{name:"bottom_initiating"},{name:"top_non_initiating"},{name:"middle_non_initiating"},{name:"bottom_non_initiating"}]},{name:"MessageVisibleKind",literalValues:[{name:"initiating"},{name:"non_initiating"}]}],associations:[],prefix:"bpmndi"}},{}],33:[function(e,t,n){t.exports={name:"DC",uri:"http://www.omg.org/spec/DD/20100524/DC",types:[{name:"Boolean"},{name:"Integer"},{name:"Real"},{name:"String"},{name:"Font",properties:[{name:"name",type:"String",isAttr:!0},{name:"size",type:"Real",isAttr:!0},{name:"isBold",type:"Boolean",isAttr:!0},{name:"isItalic",type:"Boolean",isAttr:!0},{name:"isUnderline",type:"Boolean",isAttr:!0},{name:"isStrikeThrough",type:"Boolean",isAttr:!0}]},{name:"Point",properties:[{name:"x",type:"Real","default":"0",isAttr:!0},{name:"y",type:"Real","default":"0",isAttr:!0}]},{name:"Bounds",properties:[{name:"x",type:"Real","default":"0",isAttr:!0},{name:"y",type:"Real","default":"0",isAttr:!0},{name:"width",type:"Real",isAttr:!0},{name:"height",type:"Real",isAttr:!0}]}],prefix:"dc",associations:[]}},{}],34:[function(e,t,n){t.exports={name:"DI",uri:"http://www.omg.org/spec/DD/20100524/DI",types:[{name:"DiagramElement",isAbstract:!0,properties:[{name:"extension",type:"Extension"},{name:"owningDiagram",type:"Diagram",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"owningElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"modelElement",isReadOnly:!0,isVirtual:!0,isReference:!0,type:"Element"},{name:"style",type:"Style",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"ownedElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isMany:!0}]},{name:"Node",isAbstract:!0,superClass:["DiagramElement"]},{name:"Edge",isAbstract:!0,superClass:["DiagramElement"],properties:[{name:"source",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"target",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"waypoint",isUnique:!1,isMany:!0,type:"dc:Point",xml:{serialize:"xsi:type"}}]},{name:"Diagram",isAbstract:!0,properties:[{name:"rootElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0},{name:"name",isAttr:!0,type:"String"},{name:"documentation",isAttr:!0,type:"String"},{name:"resolution",isAttr:!0,type:"Real"},{name:"ownedStyle",type:"Style",isReadOnly:!0,isVirtual:!0,isMany:!0}]},{name:"Shape",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Plane",isAbstract:!0,superClass:["Node"],properties:[{name:"planeElement",type:"DiagramElement",subsettedProperty:"DiagramElement-ownedElement",isMany:!0}]},{name:"LabeledEdge",isAbstract:!0,superClass:["Edge"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isVirtual:!0,isMany:!0}]},{name:"LabeledShape",isAbstract:!0,superClass:["Shape"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isVirtual:!0,isMany:!0}]},{name:"Label",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Style",isAbstract:!0},{name:"Extension",properties:[{name:"values",type:"Element",isMany:!0}]}],associations:[],prefix:"di",xml:{tagAlias:"lowerCase"}}},{}],35:[function(e,t,n){t.exports=e(36)},{36:36}],36:[function(e,t,n){"use strict";function r(e){function t(e){return i.indexOf(e)>=0}function n(e){i.push(e)}function r(e){t(e)||((e.__depends__||[]).forEach(r),t(e)||(n(e),(e.__init__||[]).forEach(function(e){a.push(e)})))}var i=[],a=[];e.forEach(r);var s=new o.Injector(i);return a.forEach(function(e){try{s["string"==typeof e?"get":"invoke"](e)}catch(t){throw console.error("Failed to instantiate component"),console.error(t.stack),t}}),s}function i(t){t=t||{};var n={config:["value",t]},i=e(42),a=[n,i].concat(t.modules||[]);return r(a)}function a(e,t){this.injector=t=t||i(e),this.get=t.get,this.invoke=t.invoke,this.get("eventBus").fire("diagram.init")}var o=e(69);t.exports=a,a.prototype.destroy=function(){this.get("eventBus").fire("diagram.destroy")}},{42:42,69:69}],37:[function(e,t,n){"use strict";function r(e,t){return Math.round(e*t)/t}function i(e){return u(e)?e+"px":e}function a(e){e=l({},{width:"100%",height:"100%"},e);var t=e.container||document.body,n=document.createElement("div");return n.setAttribute("class","djs-container"),l(n.style,{position:"relative",overflow:"hidden",width:i(e.width),height:i(e.height)}),t.appendChild(n),n}function o(e,t){return e.group().attr({"class":t})}function s(e,t,n,r){this._eventBus=t,this._elementRegistry=r,this._graphicsFactory=n,this._init(e||{})}function c(e,t){var n="matrix("+t.a+","+t.b+","+t.c+","+t.d+","+t.e+","+t.f+")";e.setAttribute("transform",n)}var u=e(162),l=e(168),p=e(77),f=e(74),h=e(57),d=e(67),m="base",y={shape:["x","y","width","height"],connection:["waypoints"]};s.$inject=["config.canvas","eventBus","graphicsFactory","elementRegistry"],t.exports=s,s.prototype._init=function(e){var t=this._eventBus,n=a(e),r=d.createSnapAt("100%","100%",n),i=o(r,"viewport"),s=this;this._container=n,this._svg=r,this._viewport=i,this._layers={},t.on("diagram.init",function(e){t.fire("canvas.init",{svg:r,viewport:i})}),t.on("diagram.destroy",function(){var e=s._container.parentNode;e&&e.removeChild(n),t.fire("canvas.destroy",{svg:s._svg,viewport:s._viewport}),s._svg.remove(),s._svg=s._container=s._layers=s._viewport=null})},s.prototype.getDefaultLayer=function(){return this.getLayer(m)},s.prototype.getLayer=function(e){if(!e)throw new Error("must specify a name");var t=this._layers[e];return t||(t=this._layers[e]=o(this._viewport,"layer-"+e)),t},s.prototype.getContainer=function(){return this._container},s.prototype._updateMarker=function(e,t,n){var r;e.id||(e=this._elementRegistry.get(e)),r=this._elementRegistry._elements[e.id],r&&(p([r.gfx,r.secondaryGfx],function(e){e&&e[n?"addClass":"removeClass"](t)}),this._eventBus.fire("element.marker.update",{element:e,gfx:r.gfx,marker:t,add:!!n}))},s.prototype.addMarker=function(e,t){this._updateMarker(e,t,!0)},s.prototype.removeMarker=function(e,t){this._updateMarker(e,t,!1)},s.prototype.hasMarker=function(e,t){e.id||(e=this._elementRegistry.get(e));var n=this.getGraphics(e);return n&&n.hasClass(t)},s.prototype.toggleMarker=function(e,t){this.hasMarker(e,t)?this.removeMarker(e,t):this.addMarker(e,t)},s.prototype.getRootElement=function(){return this._rootElement||this.setRootElement({id:"__implicitroot"}),this._rootElement},s.prototype.setRootElement=function(e,t){this._ensureValid("root",e);var n=this._rootElement,r=this._elementRegistry,i=this._eventBus;if(n){if(!t)throw new Error("rootElement already set, need to specify override");i.fire("root.remove",{element:n}),i.fire("root.removed",{element:n}),r.remove(n)}var a=this.getDefaultLayer();return i.fire("root.add",{element:e}),r.add(e,a,this._svg),i.fire("root.added",{element:e,gfx:a}),this._rootElement=e,e},s.prototype._ensureValid=function(e,t){if(!t.id)throw new Error("element must have an id");if(this._elementRegistry.get(t.id))throw new Error("element with id "+t.id+" already exists");var n=y[e],r=f(n,function(e){return"undefined"!=typeof t[e]});if(!r)throw new Error("must supply { "+n.join(", ")+" } with "+e)},s.prototype._setParent=function(e,t,n){h.add(t.children,e,n),e.parent=t},s.prototype._addElement=function(e,t,n){n=n||this.getRootElement();var r,i=this._eventBus,a=this._graphicsFactory;this._ensureValid(e,t),i.fire(e+".add",{element:t,parent:n}),this._setParent(t,n,r);var o=a.create(e,t);return this._elementRegistry.add(t,o),a.update(e,t,o),i.fire(e+".added",{element:t,gfx:o}),t},s.prototype.addShape=function(e,t){return this._addElement("shape",e,t)},s.prototype.addConnection=function(e,t){return this._addElement("connection",e,t)},s.prototype._removeElement=function(e,t){var n=this._elementRegistry,r=this._graphicsFactory,i=this._eventBus;return(e=n.get(e.id||e))?(i.fire(t+".remove",{element:e}),r.remove(e),h.remove(e.parent&&e.parent.children,e),e.parent=null,i.fire(t+".removed",{element:e}),n.remove(e),e):void 0},s.prototype.removeShape=function(e){return this._removeElement(e,"shape")},s.prototype.removeConnection=function(e){return this._removeElement(e,"connection")},s.prototype.sendToFront=function(e,t){t!==!1&&(t=!0),t&&e.parent&&this.sendToFront(e.parent),p(e.children,function(e){this.sendToFront(e,!1)},this);var n=this.getGraphics(e),r=n.parent();n.remove().appendTo(r)},s.prototype.getGraphics=function(e,t){return this._elementRegistry.getGraphics(e,t)},s.prototype._fireViewboxChange=function(){this._eventBus.fire("canvas.viewbox.changed",{viewbox:this.viewbox(!1)})},s.prototype.viewbox=function(e){if(void 0===e&&this._cachedViewbox)return this._cachedViewbox;var t,n,i,a,o,s=this._viewport,c=this.getSize();return e?(i=Math.min(c.width/e.width,c.height/e.height),n=(new d.Matrix).scale(i).translate(-e.x,-e.y),s.transform(n),this._fireViewboxChange(),e):(t=this.getDefaultLayer().getBBox(!0),n=s.transform().localMatrix,i=r(n.a,1e3),a=r(-n.e||0,1e3),o=r(-n.f||0,1e3),e=this._cachedViewbox={x:a?a/i:0,y:o?o/i:0,width:c.width/i,height:c.height/i,scale:i,inner:{width:t.width,height:t.height,x:t.x,y:t.y},outer:c})},s.prototype.scroll=function(e){var t=this._viewport.node,n=t.getCTM();return e&&(e=l({dx:0,dy:0},e||{}),n=this._svg.node.createSVGMatrix().translate(e.dx,e.dy).multiply(n),c(t,n),this._fireViewboxChange()),{x:n.e,y:n.f}},s.prototype.zoom=function(e,t){if("fit-viewport"===e)return this._fitViewport(t);var n,i,a=this.viewbox();return void 0===e?a.scale:("object"!=typeof t&&(n=a.outer,t={x:n.width/2,y:n.height/2}),i=this._setZoom(e,t),this._fireViewboxChange(),r(i.a,1e3))},s.prototype._fitViewport=function(e){var t,n,r=this.viewbox(),i=r.outer,a=r.inner;return a.x>=0&&a.y>=0&&a.x+a.width<=i.width&&a.y+a.height<=i.height&&!e?n={x:0,y:0,width:Math.max(a.width+a.x,i.width),height:Math.max(a.height+a.y,i.height)}:(t=Math.min(1,i.width/a.width,i.height/a.height),n={x:a.x+(e?a.width/2-i.width/t/2:0),y:a.y+(e?a.height/2-i.height/t/2:0),width:i.width/t,height:i.height/t}),this.viewbox(n),this.viewbox().scale},s.prototype._setZoom=function(e,t){var n,r,i,a,o,s=this._svg.node,u=this._viewport.node,p=s.createSVGMatrix(),f=s.createSVGPoint();i=u.getCTM();var h=i.a;return t?(n=l(f,t),r=n.matrixTransform(i.inverse()),a=p.translate(r.x,r.y).scale(1/h*e).translate(-r.x,-r.y),o=i.multiply(a)):o=p.scale(e),c(this._viewport.node,o),o},s.prototype.getSize=function(){return{width:this._container.clientWidth,height:this._container.clientHeight}},s.prototype.getAbsoluteBBox=function(e){var t,n=this.viewbox();if(e.waypoints){var r=this.getGraphics(e),i=r.getBBox(!0);t=r.getBBox(),t.x-=i.x,t.y-=i.y,t.width+=2*i.x,t.height+=2*i.y}else t=e;var a=t.x*n.scale-n.x*n.scale,o=t.y*n.scale-n.y*n.scale,s=t.width*n.scale,c=t.height*n.scale;return{x:a,y:o,width:s,height:c}}},{162:162,168:168,57:57,67:67,74:74,77:77}],38:[function(e,t,n){"use strict";function r(){this._uid=12}var i=e(56);t.exports=r,r.prototype.createRoot=function(e){return this.create("root",e)},r.prototype.createLabel=function(e){return this.create("label",e)},r.prototype.createShape=function(e){return this.create("shape",e)},r.prototype.createConnection=function(e){return this.create("connection",e)},r.prototype.create=function(e,t){return t=t||{},t.id||(t.id=e+"_"+this._uid++),i.create(e,t)}},{56:56}],39:[function(e,t,n){"use strict";function r(){this._elements={}}var i="data-element-id";t.exports=r,r.prototype.add=function(e,t,n){var r=e.id;this._validateId(r),t.attr(i,r),n&&n.attr(i,r),this._elements[r]={element:e,gfx:t,secondaryGfx:n}},r.prototype.remove=function(e){var t=this._elements,n=e.id||e,r=n&&t[n];r&&(r.gfx.attr(i,null),r.secondaryGfx&&r.secondaryGfx.attr(i,null),delete t[n])},r.prototype.updateId=function(e,t){this._validateId(t),"string"==typeof e&&(e=this.get(e));var n=this.getGraphics(e),r=this.getGraphics(e,!0);this.remove(e),e.id=t,this.add(e,n,r)},r.prototype.get=function(e){var t;t="string"==typeof e?e:e&&e.attr(i);var n=this._elements[t];return n&&n.element},r.prototype.filter=function(e){var t=[];return this.forEach(function(n,r){e(n,r)&&t.push(n)}),t},r.prototype.getAll=function(){return this.filter(function(e){return e})},r.prototype.forEach=function(e){var t=this._elements;Object.keys(t).forEach(function(n){var r=t[n],i=r.element,a=r.gfx;return e(i,a)})},r.prototype.getGraphics=function(e,t){var n=e.id||e,r=this._elements[n];return r&&(t?r.secondaryGfx:r.gfx)},r.prototype._validateId=function(e){if(!e)throw new Error("element must have an id");if(this._elements[e])throw new Error("element with id "+e+" already added")}},{}],40:[function(e,t,n){"use strict";function r(){this._listeners={};var e=this;this.on("diagram.destroy",1,function(){e._listeners=null})}function i(){}var a=e(160),o=e(159),s=e(162),c=e(168),u=1e3;t.exports=r,r.prototype.on=function(e,t,n){if(e=o(e)?e:[e],a(t)&&(n=t,t=u),!s(t))throw new Error("priority must be a number");var r=this,i={priority:t,callback:n};e.forEach(function(e){r._addListener(e,i)})},r.prototype.once=function(e,t){function n(){t.apply(r,arguments),r.off(e,n)}var r=this;this.on(e,n)},r.prototype.off=function(e,t){var n,r,i=this._getListeners(e);if(t)for(r=i.length-1;n=i[r];r--)n.callback===t&&i.splice(r,1);else i.length=0},r.prototype.fire=function(e,t){var n,r,a,o,s,c,u;if(u=Array.prototype.slice.call(arguments),"object"==typeof e&&(n=e,e=n.type),!e)throw new Error("no event type specified");if(a=this._listeners[e]){t instanceof i?n=t:(n=new i,n.init(t)),u[0]=n,r=n.type;try{for(e!==r&&(n.type=e),o=0;(s=a[o])&&!n.cancelBubble;o++)try{c=n.returnValue=s.callback.apply(null,u),void 0!==c&&n.stopPropagation(),c===!1&&n.preventDefault()}catch(l){if(!this.handleError(l))throw console.error("unhandled error in event listener"),console.error(l.stack),l}}finally{e!==r&&(n.type=r)}return void 0===c&&n.defaultPrevented&&(c=!1),c}},r.prototype.handleError=function(e){return this.fire("error",{error:e})===!1},r.prototype._addListener=function(e,t){var n,r,i=this._getListeners(e);for(r=0;n=i[r];r++)if(n.priority<t.priority)return void i.splice(r,0,t);i.push(t)},r.prototype._getListeners=function(e){var t=this._listeners[e];return t||(this._listeners[e]=t=[]),t},t.exports.Event=i,i.prototype.stopPropagation=function(){this.cancelBubble=!0},i.prototype.preventDefault=function(){this.defaultPrevented=!0},i.prototype.init=function(e){c(this,e||{})}},{159:159,160:160,162:162,168:168}],41:[function(e,t,n){"use strict";function r(e,t){this._renderer=e,this._elementRegistry=t}var i=e(77),a=e(81),o=e(60),s=e(179);r.$inject=["renderer","elementRegistry"],t.exports=r,r.prototype._getChildren=function(e){var t,n=this._elementRegistry.getGraphics(e);return e.parent?(t=o.getChildren(n),t||(t=n.parent().group().attr("class","djs-children"))):t=n,t},r.prototype._clear=function(e){var t=o.getVisual(e);return s(t.node),t},r.prototype._createContainer=function(e,t){var n=t.group().attr("class","djs-group"),r=n.group().attr("class","djs-element djs-"+e);
return r.group().attr("class","djs-visual"),r},r.prototype.create=function(e,t){var n=this._getChildren(t.parent);return this._createContainer(e,n)},r.prototype.updateContainments=function(e){var t,n=this,r=this._elementRegistry;t=a(e,function(e,t){return t.parent&&(e[t.parent.id]=t.parent),e},{}),i(t,function(e){var t=n._getChildren(e),a=e.children;a&&i(a.slice().reverse(),function(e){var n=r.getGraphics(e);n.parent().prependTo(t)})})},r.prototype.update=function(e,t,n){if(t.parent){var r=this._clear(n);if("shape"===e)this._renderer.drawShape(r,t),n.translate(t.x,t.y);else{if("connection"!==e)throw new Error("unknown type: "+e);this._renderer.drawConnection(r,t)}n.attr("display",t.hidden?"none":"block")}},r.prototype.remove=function(e){var t=this._elementRegistry.getGraphics(e);t.parent().remove()}},{179:179,60:60,77:77,81:81}],42:[function(e,t,n){t.exports={__depends__:[e(45)],__init__:["canvas"],canvas:["type",e(37)],elementRegistry:["type",e(39)],elementFactory:["type",e(38)],eventBus:["type",e(40)],graphicsFactory:["type",e(41)]}},{37:37,38:38,39:39,40:40,41:41,45:45}],43:[function(e,t,n){"use strict";function r(e){this.CONNECTION_STYLE=e.style(["no-fill"],{strokeWidth:5,stroke:"fuchsia"}),this.SHAPE_STYLE=e.style({fill:"white",stroke:"fuchsia",strokeWidth:2})}function i(e){return e.join(",").replace(/,?([A-z]),?/g,"$1")}function a(e){for(var t,n="",r=0;t=e[r];r++)n+=t.x+","+t.y+" ";return n}function o(e,t){return c.create("polyline",{points:a(e)}).attr(t||{})}function s(e,t){return e.attr({points:a(t)})}var c=e(67);t.exports=r,r.$inject=["styles"],r.prototype.drawShape=function(e,t){return e.rect(0,0,t.width||0,t.height||0).attr(this.SHAPE_STYLE)},r.prototype.drawConnection=function(e,t){return o(t.waypoints,this.CONNECTION_STYLE).appendTo(e)},r.prototype.getShapePath=function(e){var t=e.x,n=e.y,r=e.width,a=e.height,o=[["M",t,n],["l",r,0],["l",0,a],["l",-r,0],["z"]];return i(o)},r.prototype.getConnectionPath=function(e){var t,n,r=e.waypoints,a=[];for(t=0;n=r[t];t++)n=n.original||n,a.push([0===t?"M":"L",n.x,n.y]);return i(a)},t.exports.createLine=o,t.exports.updateLine=s},{67:67}],44:[function(e,t,n){"use strict";function r(){var e={"no-fill":{fill:"none"},"no-border":{strokeOpacity:0},"no-events":{pointerEvents:"none"}};this.cls=function(e,t,n){var r=this.style(t,n);return a(r,{"class":e})},this.style=function(t,n){i(t)||n||(n=t,t=[]);var r=o(t,function(t,n){return a(t,e[n]||{})},{});return n?a(r,n):r}}var i=e(159),a=e(168),o=e(81);t.exports=r},{159:159,168:168,81:81}],45:[function(e,t,n){t.exports={renderer:["type",e(43)],styles:["type",e(44)]}},{43:43,44:44}],46:[function(e,t,n){"use strict";function r(e,t,n){function r(n,r){var i,a=r.delegateTarget||r.target,o=a&&new l(a),s=t.get(o);o&&s&&(i=e.fire(n,{element:s,gfx:o,originalEvent:r}),i===!1&&(r.stopPropagation(),r.preventDefault()))}function o(e){var t=y[e];return t||(t=y[e]=function(t){u(t)&&r(e,t)}),t}function p(e,t,n){var r=o(n);r.$delegate=a.bind(e,v,t,r)}function f(e,t,n){a.unbind(e,t,o(n).$delegate)}function h(e){i(g,function(t,n){p(e.node,n,t)})}function d(e){i(g,function(t,n){f(e.node,n,t)})}var m=n.cls("djs-hit",["no-fill","no-border"],{stroke:"white",strokeWidth:15}),y={},g={mouseover:"element.hover",mouseout:"element.out",click:"element.click",dblclick:"element.dblclick",mousedown:"element.mousedown",mouseup:"element.mouseup"},v="svg, .djs-element";e.on("canvas.destroy",function(e){d(e.svg)}),e.on("canvas.init",function(e){h(e.svg)}),e.on(["shape.added","connection.added"],function(e){var t,n,r=e.element,i=e.gfx;r.waypoints?(t=s(r.waypoints),n="connection"):(t=l.create("rect",{x:0,y:0,width:r.width,height:r.height}),n="shape"),t.attr(m).appendTo(i.node)}),e.on("shape.changed",function(e){var t=e.element,n=e.gfx,r=n.select(".djs-hit");r.attr({width:t.width,height:t.height})}),e.on("connection.changed",function(e){var t=e.element,n=e.gfx,r=n.select(".djs-hit");c(r,t.waypoints)}),this.fire=r,this.mouseHandler=o,this.registerEvent=p,this.unregisterEvent=f}var i=e(77),a=e(180),o=e(43),s=o.createLine,c=o.updateLine,u=e(62).isPrimaryButton,l=e(67);r.$inject=["eventBus","elementRegistry","styles"],t.exports=r},{180:180,43:43,62:62,67:67,77:77}],47:[function(e,t,n){t.exports={__init__:["interactionEvents"],interactionEvents:["type",e(46)]}},{46:46}],48:[function(e,t,n){"use strict";function r(e,t,n){function r(e,t){return e.rect(10,10,0,0).attr(c)}function a(e,t){e.attr({x:-s,y:-s,width:t.width+2*s,height:t.height+2*s})}function o(e,t){var n=i(t);e.attr({x:n.x-s,y:n.y-s,width:n.width+2*s,height:n.height+2*s})}var s=6,c=t.cls("djs-outline",["no-fill"]);e.on(["shape.added","shape.changed"],function(e){var t=e.element,n=e.gfx,i=n.select(".djs-outline");i||(i=r(n,t)),a(i,t)}),e.on(["connection.added","connection.changed"],function(e){var t=e.element,n=e.gfx,i=n.select(".djs-outline");i||(i=r(n,t)),o(i,t)})}var i=e(58).getBBox;r.$inject=["eventBus","styles","elementRegistry"],t.exports=r},{58:58}],49:[function(e,t,n){"use strict";t.exports={__init__:["outline"],outline:["type",e(48)]}},{48:48}],50:[function(e,t,n){"use strict";function r(e){var t=d('<div class="djs-overlay-container" style="position: absolute; width: 0; height: 0;" />');return e.insertBefore(t,e.firstChild),t}function i(e,t,n){l(e.style,{left:t+"px",top:n+"px"})}function a(e,t){e.style.display=t===!1?"none":""}function o(e,t,n,i){this._eventBus=t,this._canvas=n,this._elementRegistry=i,this._ids=v,this._overlayDefaults={show:{minZoom:.7,maxZoom:5}},this._overlays={},this._overlayContainers={},this._overlayRoot=r(n.getContainer()),this._init(e)}var s=e(159),c=e(165),u=e(163),l=e(168),p=e(77),f=e(75),h=e(84),d=e(181),m=e(178),y=e(183),g=e(58).getBBox,v=new(e(61))("ov");o.$inject=["config.overlays","eventBus","canvas","elementRegistry"],t.exports=o,o.prototype.get=function(e){if(c(e)&&(e={id:e}),e.element){var t=this._getOverlayContainer(e.element,!0);return t?e.type?f(t.overlays,{type:e.type}):t.overlays.slice():[]}return e.type?f(this._overlays,{type:e.type}):e.id?this._overlays[e.id]:null},o.prototype.add=function(e,t,n){if(u(t)&&(n=t,t=null),e.id||(e=this._elementRegistry.get(e)),!n.position)throw new Error("must specifiy overlay position");if(!n.html)throw new Error("must specifiy overlay html");if(!e)throw new Error("invalid element specified");var r=this._ids.next();return n=l({},this._overlayDefaults,n,{id:r,type:t,element:e,html:n.html}),this._addOverlay(n),r},o.prototype.remove=function(e){var t=this.get(e)||[];s(t)||(t=[t]);var n=this;p(t,function(e){var t=n._getOverlayContainer(e.element,!0);if(e&&(y(e.html),y(e.htmlContainer),delete e.htmlContainer,delete e.element,delete n._overlays[e.id]),t){var r=t.overlays.indexOf(e);-1!==r&&t.overlays.splice(r,1)}})},o.prototype.show=function(){a(this._overlayRoot)},o.prototype.hide=function(){a(this._overlayRoot,!1)},o.prototype._updateOverlayContainer=function(e){var t=e.element,n=e.html,r=t.x,a=t.y;if(t.waypoints){var o=g(t);r=o.x,a=o.y}i(n,r,a)},o.prototype._updateOverlay=function(e){var t=e.position,n=e.htmlContainer,r=e.element,a=t.left,o=t.top;if(void 0!==t.right){var s;s=r.waypoints?g(r).width:r.width,a=-1*t.right+s}if(void 0!==t.bottom){var c;c=r.waypoints?g(r).height:r.height,o=-1*t.bottom+c}i(n,a||0,o||0)},o.prototype._createOverlayContainer=function(e){var t=d('<div class="djs-overlays djs-overlays-'+e.id+'" style="position: absolute" />');this._overlayRoot.appendChild(t);var n={html:t,element:e,overlays:[]};return this._updateOverlayContainer(n),n},o.prototype._updateRoot=function(e){var t=e.scale||1,n=e.scale||1,r="matrix("+t+",0,0,"+n+","+-1*e.x*t+","+-1*e.y*n+")";this._overlayRoot.style.transform=r,this._overlayRoot.style["-ms-transform"]=r},o.prototype._getOverlayContainer=function(e,t){var n=e&&e.id||e,r=this._overlayContainers[n];return r||t||(r=this._overlayContainers[n]=this._createOverlayContainer(e)),r},o.prototype._addOverlay=function(e){var t,n,r=e.id,i=e.element,a=e.html;a.get&&(a=a.get(0)),c(a)&&(a=d(a)),n=this._getOverlayContainer(i),t=d('<div class="djs-overlay" data-overlay-id="'+r+'" style="position: absolute">'),t.appendChild(a),e.type&&m(t).add("djs-overlay-"+e.type),e.htmlContainer=t,n.overlays.push(e),n.html.appendChild(t),this._overlays[r]=e,this._updateOverlay(e)},o.prototype._updateOverlayVisibilty=function(e){p(this._overlays,function(t){var n=t.show,r=t.htmlContainer,i=!0;n&&((n.minZoom>e.scale||n.maxZoom<e.scale)&&(i=!1),a(r,i))})},o.prototype._init=function(e){var t=this._eventBus,n=this,r=function(e){n._updateRoot(e),n._updateOverlayVisibilty(e),n.show()};e&&e.deferUpdate===!1||(r=h(r,300)),t.on("canvas.viewbox.changed",function(e){n.hide(),r(e.viewbox)}),t.on(["shape.remove","connection.remove"],function(e){var t=n.get({element:e.element});p(t,function(e){n.remove(e.id)})}),t.on(["element.changed"],function(e){var t=e.element,r=n._getOverlayContainer(t,!0);r&&(p(r.overlays,function(e){n._updateOverlay(e)}),n._updateOverlayContainer(r))}),t.on("element.marker.update",function(e){var t=n._getOverlayContainer(e.element,!0);t&&m(t.html)[e.add?"add":"remove"](e.marker)})}},{159:159,163:163,165:165,168:168,178:178,181:181,183:183,58:58,61:61,75:75,77:77,84:84}],51:[function(e,t,n){t.exports={__init__:["overlays"],overlays:["type",e(50)]}},{50:50}],52:[function(e,t,n){"use strict";function r(e){this._eventBus=e,this._selectedElements=[];var t=this;e.on(["shape.remove","connection.remove"],function(e){var n=e.element;t.deselect(n)})}var i=e(159),a=e(77);r.$inject=["eventBus"],t.exports=r,r.prototype.deselect=function(e){var t=this._selectedElements,n=t.indexOf(e);if(-1!==n){var r=t.slice();t.splice(n,1),this._eventBus.fire("selection.changed",{oldSelection:r,newSelection:t})}},r.prototype.get=function(){return this._selectedElements},r.prototype.isSelected=function(e){return-1!==this._selectedElements.indexOf(e)},r.prototype.select=function(e,t){var n=this._selectedElements,r=n.slice();i(e)||(e=e?[e]:[]),t?a(e,function(e){-1===n.indexOf(e)&&n.push(e)}):this._selectedElements=n=e.slice(),this._eventBus.fire("selection.changed",{oldSelection:r,newSelection:n})}},{159:159,77:77}],53:[function(e,t,n){"use strict";function r(e,t,n){e.on("create.end",500,function(e){e.context.canExecute&&t.select(e.context.shape)}),e.on("connect.end",500,function(e){e.context.canExecute&&e.context.target&&t.select(e.context.target)}),e.on("shape.move.end",500,function(e){var n=e.previousSelection||[],r=e.context.shape;-1===n.indexOf(r)&&t.select(r)}),e.on("element.click",function(e){var r=e.element;r===n.getRootElement()&&(r=null);var a=t.isSelected(r),o=t.get().length>1,s=i(e);return a&&o?s?t.deselect(r):t.select(r):void(a?t.deselect(r):t.select(r,s))})}var i=e(62).hasPrimaryModifier;r.$inject=["eventBus","selection","canvas"],t.exports=r},{62:62}],54:[function(e,t,n){"use strict";function r(e,t,n,r,s){function c(e,n){t.addMarker(e,n)}function u(e,n){t.removeMarker(e,n)}this._multiSelectionBox=null,e.on("element.hover",function(e){c(e.element,a)}),e.on("element.out",function(e){u(e.element,a)}),e.on("selection.changed",function(e){function t(e){u(e,o)}function n(e){c(e,o)}var r=e.oldSelection,a=e.newSelection;i(r,function(e){-1===a.indexOf(e)&&t(e)}),i(a,function(e){-1===r.indexOf(e)&&n(e)})})}var i=e(77),a="hover",o="selected";r.$inject=["eventBus","canvas","selection","graphicsFactory","styles"],t.exports=r},{77:77}],55:[function(e,t,n){t.exports={__init__:["selectionVisuals","selectionBehavior"],__depends__:[e(47),e(49)],selection:["type",e(52)],selectionVisuals:["type",e(54)],selectionBehavior:["type",e(53)]}},{47:47,49:49,52:52,53:53,54:54}],56:[function(e,t,n){"use strict";function r(){Object.defineProperty(this,"businessObject",{writable:!0}),p.bind(this,"parent"),f.bind(this,"label"),d.bind(this,"outgoing"),m.bind(this,"incoming")}function i(){r.call(this),p.bind(this,"children"),h.bind(this,"host"),h.bind(this,"attachers")}function a(){i.call(this)}function o(){i.call(this),f.bind(this,"labelTarget")}function s(){r.call(this),d.bind(this,"source"),m.bind(this,"target")}var c=e(168),u=e(72),l=e(192),p=new l({name:"children",enumerable:!0,collection:!0},{name:"parent"}),f=new l({name:"label",enumerable:!0},{name:"labelTarget"}),h=new l({name:"attachers",collection:!0},{name:"host"}),d=new l({name:"outgoing",collection:!0},{name:"source"}),m=new l({name:"incoming",collection:!0},{name:"target"});u(i,r),u(a,i),u(o,i),u(s,r);var y={connection:s,shape:i,label:o,root:a};t.exports.create=function(e,t){var n=y[e];if(!n)throw new Error("unknown type: <"+e+">");return c(new n,t)},t.exports.Base=r,t.exports.Root=a,t.exports.Shape=i,t.exports.Connection=s,t.exports.Label=o},{168:168,192:192,72:72}],57:[function(e,t,n){"use strict";t.exports.remove=function(e,t){if(e&&t){var n=e.indexOf(t);if(-1!==n)return e.splice(n,1),t}},t.exports.add=function(e,t,n){if(e&&t){isNaN(n)&&(n=-1);var r=e.indexOf(t);if(-1!==r){if(r===n)return;if(-1===n)return;e.splice(r,1)}-1!==n?e.splice(n,0,t):e.push(t)}},t.exports.indexOf=function(e,t){return e&&t?e.indexOf(t):-1}},{}],58:[function(e,t,n){"use strict";function r(e,t,n){var r=!n||-1===e.indexOf(t);return r&&e.push(t),r}function i(e,t,n){n=n||0,d(e,function(e,r){var a=t(e,r,n);p(a)&&a.length&&i(a,t,n+1)})}function a(e,t,n){var a=[],o=[];return i(e,function(e,i,s){r(a,e,t);var c=e.children;return(-1===n||n>s)&&c&&r(o,c,t)?c:void 0}),a}function o(e,t){return a(e,!t,1)}function s(e,t){return a(e,!t,-1)}function c(e){function t(e){r[e.source.id]&&r[e.target.id]&&(r[e.id]=e),a[e.source.id]&&a[e.target.id]&&(c[e.id]=s[e.id]=e),o[e.id]=e}function n(e){return s[e.id]=e,e.waypoints?void(c[e.id]=o[e.id]=e):(a[e.id]=e,d(e.incoming,t),d(e.outgoing,t),e.children)}var r=h(e,function(e){return e.id}),a={},o={},s={},c={};return i(e,n),{allShapes:a,allConnections:o,topLevel:r,enclosedConnections:c,enclosedElements:s}}function u(e,t){t=!!t,p(e)||(e=[e]);var n,r,i,a;return d(e,function(e){var o=e;e.waypoints&&!t&&(o=u(e.waypoints,!0));var s=o.x,c=o.y,l=o.height||0,p=o.width||0;(n>s||void 0===n)&&(n=s),(r>c||void 0===r)&&(r=c),(s+p>i||void 0===i)&&(i=s+p),(c+l>a||void 0===a)&&(a=c+l)}),{x:n,y:r,height:a-r,width:i-n}}function l(e,t){var n={};return d(e,function(e){var r=e;r.waypoints&&(r=u(r)),!f(t.y)&&r.x>t.x&&(n[e.id]=e),!f(t.x)&&r.y>t.y&&(n[e.id]=e),r.x>t.x&&r.y>t.y&&(f(t.width)&&f(t.height)&&r.width+r.x<t.width+t.x&&r.height+r.y<t.height+t.y?n[e.id]=e:f(t.width)&&f(t.height)||(n[e.id]=e))}),n}var p=e(159),f=e(162),h=e(78),d=e(77);t.exports.eachElement=i,t.exports.selfAndDirectChildren=o,t.exports.selfAndAllChildren=s,t.exports.getBBox=u,t.exports.getEnclosedElements=l,t.exports.getClosure=c},{159:159,162:162,77:77,78:78}],59:[function(e,t,n){"use strict";function r(e){return e&&e.preventDefault()}function i(e,t){e&&(e.stopPropagation&&e.stopPropagation(),t&&e.stopImmediatePropagation&&e.stopImmediatePropagation())}function a(e){return e.originalEvent||e.srcEvent}function o(e,t){c(e,t),s(e)}function s(e){r(e),r(a(e))}function c(e,t){i(e,t),i(a(e),t)}function u(e){return e.pointers&&e.pointers.length&&(e=e.pointers[0]),e.touches&&e.touches.length&&(e=e.touches[0]),e?{x:e.clientX,y:e.clientY}:null}t.exports.getOriginal=a,t.exports.stopEvent=o,t.exports.preventDefault=s,t.exports.stopPropagation=c,t.exports.toPoint=u},{}],60:[function(e,t,n){"use strict";function r(e){return e.select(".djs-visual")}function i(e){return e.parent().children()[1]}function a(e){return r(e).select("*").getBBox()}t.exports.getVisual=r,t.exports.getChildren=i,t.exports.getBBox=a},{}],61:[function(e,t,n){"use strict";function r(e){this._counter=0,this._prefix=(e?e+"-":"")+Math.floor(1e9*Math.random())+"-"}t.exports=r,r.prototype.next=function(){return this._prefix+ ++this._counter}},{}],62:[function(e,t,n){"use strict";function r(e){return!(i(e)||e).button}var i=e(59).getOriginal,a=e(63).isMac;t.exports.isPrimaryButton=r,t.exports.isMac=a,t.exports.hasPrimaryModifier=function(e){var t=i(e)||e;return r(e)?a()?t.altKey:t.ctrlKey:!1},t.exports.hasSecondaryModifier=function(e){var t=i(e)||e;return r(e)&&t.shiftKey}},{59:59,63:63}],63:[function(e,t,n){"use strict";t.exports.isMac=function(){return/mac/i.test(navigator.platform)}},{}],64:[function(e,t,n){"use strict";function r(e){var t=e.split("-");return{horizontal:t[0]||"center",vertical:t[1]||"top"}}function i(e){return p(e)?f({top:0,left:0,right:0,bottom:0},e):{top:e,left:e,right:e,bottom:e}}function a(e,t){return t.textContent=e,h(t.getBBox(),["width","height"])}function o(e,t,n){for(var r,i=e.shift(),o=i;;){if(r=a(o,n),r.width=o?r.width:0," "===o||""===o||r.width<Math.round(t)||o.length<4)return s(e,o,i,r);o=u(o,r.width,t)}}function s(e,t,n,r){if(t.length<n.length){var i=e[0]||"",a=n.slice(t.length).trim();i=/-\s*$/.test(a)?a.replace(/-\s*$/,"")+i.replace(/^\s+/,""):a+" "+i,e[0]=i}return{width:r.width,height:r.height,text:t}}function c(e,t){var n,r=e.split(/(\s|-)/g),i=[],a=0;if(r.length>1)for(;n=r.shift();){if(!(n.length+a<t)){"-"===n&&i.pop();break}i.push(n),a+=n.length}return i.join("")}function u(e,t,n){var r=Math.max(e.length*(n/t),1),i=c(e,r);return i||(i=e.slice(0,Math.max(Math.round(r-1),1))),i}function l(e){this._config=f({},{size:x,padding:v,style:{},align:"center-top"},e||{})}var p=e(163),f=e(168),h=e(174),d=e(77),m=e(81),y=e(171),g=e(67),v=0,x={width:150,height:50};l.prototype.createText=function(e,t,n){for(var a=y({},this._config.size,n.box||{}),s=y({},this._config.style,n.style||{}),c=r(n.align||this._config.align),u=i(void 0!==n.padding?n.padding:this._config.padding),l=t.split(/\r?\n/g),p=[],f=a.width-u.left-u.right,h=e.paper.text(0,0,"").attr(s).node;l.length;)p.push(o(l,f,h));var v,x,b=m(p,function(e,t,n){return e+t.height},0);switch(c.vertical){case"middle":v=(a.height-b)/2-p[0].height/4;break;default:v=u.top}var E=e.text().attr(s);return d(p,function(e){switch(v+=e.height,c.horizontal){case"left":x=u.left;break;case"right":x=f-u.right-e.width;break;default:x=Math.max((f-e.width)/2+u.left,0)}var t=g.create("tspan",{x:x,y:v}).node;t.textContent=e.text,E.append(t)}),h.parentNode.removeChild(h),E},t.exports=l},{163:163,168:168,171:171,174:174,67:67,77:77,81:81}],65:[function(t,n,r){!function(t){var r,i,a="0.4.2",o="hasOwnProperty",s=/[\.\/]/,c=/\s*,\s*/,u="*",l=function(e,t){return e-t},p={n:{}},f=function(){for(var e=0,t=this.length;t>e;e++)if("undefined"!=typeof this[e])return this[e]},h=function(){for(var e=this.length;--e;)if("undefined"!=typeof this[e])return this[e]},d=function(e,t){e=String(e);var n,a=i,o=Array.prototype.slice.call(arguments,2),s=d.listeners(e),c=0,u=[],p={},m=[],y=r;m.firstDefined=f,m.lastDefined=h,r=e,i=0;for(var g=0,v=s.length;v>g;g++)"zIndex"in s[g]&&(u.push(s[g].zIndex),s[g].zIndex<0&&(p[s[g].zIndex]=s[g]));for(u.sort(l);u[c]<0;)if(n=p[u[c++]],m.push(n.apply(t,o)),i)return i=a,m;for(g=0;v>g;g++)if(n=s[g],"zIndex"in n)if(n.zIndex==u[c]){if(m.push(n.apply(t,o)),i)break;do if(c++,n=p[u[c]],n&&m.push(n.apply(t,o)),i)break;while(n)}else p[n.zIndex]=n;else if(m.push(n.apply(t,o)),i)break;return i=a,r=y,m};d._events=p,d.listeners=function(e){var t,n,r,i,a,o,c,l,f=e.split(s),h=p,d=[h],m=[];for(i=0,a=f.length;a>i;i++){for(l=[],o=0,c=d.length;c>o;o++)for(h=d[o].n,n=[h[f[i]],h[u]],r=2;r--;)t=n[r],t&&(l.push(t),m=m.concat(t.f||[]));d=l}return m},d.on=function(e,t){if(e=String(e),"function"!=typeof t)return function(){};for(var n=e.split(c),r=0,i=n.length;i>r;r++)!function(e){for(var n,r=e.split(s),i=p,a=0,o=r.length;o>a;a++)i=i.n,i=i.hasOwnProperty(r[a])&&i[r[a]]||(i[r[a]]={n:{}});for(i.f=i.f||[],a=0,o=i.f.length;o>a;a++)if(i.f[a]==t){n=!0;break}!n&&i.f.push(t)}(n[r]);return function(e){+e==+e&&(t.zIndex=+e)}},d.f=function(e){var t=[].slice.call(arguments,1);return function(){d.apply(null,[e,null].concat(t).concat([].slice.call(arguments,0)))}},d.stop=function(){i=1},d.nt=function(e){return e?new RegExp("(?:\\.|\\/|^)"+e+"(?:\\.|\\/|$)").test(r):r},d.nts=function(){return r.split(s)},d.off=d.unbind=function(e,t){if(!e)return void(d._events=p={n:{}});var n=e.split(c);if(n.length>1)for(var r=0,i=n.length;i>r;r++)d.off(n[r],t);else{n=e.split(s);var a,l,f,r,i,h,m,y=[p];for(r=0,i=n.length;i>r;r++)for(h=0;h<y.length;h+=f.length-2){if(f=[h,1],a=y[h].n,n[r]!=u)a[n[r]]&&f.push(a[n[r]]);else for(l in a)a[o](l)&&f.push(a[l]);y.splice.apply(y,f)}for(r=0,i=y.length;i>r;r++)for(a=y[r];a.n;){if(t){if(a.f){for(h=0,m=a.f.length;m>h;h++)if(a.f[h]==t){a.f.splice(h,1);break}!a.f.length&&delete a.f}for(l in a.n)if(a.n[o](l)&&a.n[l].f){var g=a.n[l].f;for(h=0,m=g.length;m>h;h++)if(g[h]==t){g.splice(h,1);break}!g.length&&delete a.n[l].f}}else{delete a.f;for(l in a.n)a.n[o](l)&&a.n[l].f&&delete a.n[l].f}a=a.n}}},d.once=function(e,t){var n=function(){return d.unbind(e,n),t.apply(this,arguments)};return d.on(e,n)},d.version=a,d.toString=function(){return"You are running Eve "+a},"undefined"!=typeof n&&n.exports?n.exports=d:"function"==typeof e&&e.amd?e("eve",[],function(){return d}):t.eve=d}(this)},{}],66:[function(t,n,r){!function(i,a){if("function"==typeof e&&e.amd)e(["eve"],function(e){return a(i,e)});else if("undefined"!=typeof r){var o=t(65);n.exports=a(i,o)}else a(i,i.eve)}(window||this,function(e,t){var n=function(t){var n={},r=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.oRequestAnimationFrame||e.msRequestAnimationFrame||function(e){setTimeout(e,16)},i=Array.isArray||function(e){return e instanceof Array||"[object Array]"==Object.prototype.toString.call(e)},a=0,o="M"+(+new Date).toString(36),s=function(){return o+(a++).toString(36)},c=Date.now||function(){return+new Date},u=function(e){var t=this;if(null==e)return t.s;var n=t.s-e;t.b+=t.dur*n,t.B+=t.dur*n,t.s=e},l=function(e){var t=this;return null==e?t.spd:void(t.spd=e)},p=function(e){var t=this;return null==e?t.dur:(t.s=t.s*e/t.dur,void(t.dur=e))},f=function(){var e=this;delete n[e.id],e.update(),t("mina.stop."+e.id,e)},h=function(){var e=this;e.pdif||(delete n[e.id],e.update(),e.pdif=e.get()-e.b)},d=function(){var e=this;e.pdif&&(e.b=e.get()-e.pdif,delete e.pdif,n[e.id]=e)},m=function(){var e,t=this;if(i(t.start)){e=[];for(var n=0,r=t.start.length;r>n;n++)e[n]=+t.start[n]+(t.end[n]-t.start[n])*t.easing(t.s)}else e=+t.start+(t.end-t.start)*t.easing(t.s);t.set(e)},y=function(){var e=0;for(var i in n)if(n.hasOwnProperty(i)){var a=n[i],o=a.get();e++,a.s=(o-a.b)/(a.dur/a.spd),a.s>=1&&(delete n[i],a.s=1,e--,function(e){setTimeout(function(){t("mina.finish."+e.id,e)})}(a)),a.update()}e&&r(y)},g=function(e,t,i,a,o,c,v){var x={id:s(),start:e,end:t,b:i,s:0,dur:a-i,spd:1,get:o,set:c,easing:v||g.linear,status:u,speed:l,duration:p,stop:f,pause:h,resume:d,update:m};n[x.id]=x;var b,E=0;for(b in n)if(n.hasOwnProperty(b)&&(E++,2==E))break;return 1==E&&r(y),x};return g.time=c,g.getById=function(e){return n[e]||null},g.linear=function(e){return e},g.easeout=function(e){return Math.pow(e,1.7)},g.easein=function(e){return Math.pow(e,.48)},g.easeinout=function(e){if(1==e)return 1;if(0==e)return 0;var t=.48-e/1.04,n=Math.sqrt(.1734+t*t),r=n-t,i=Math.pow(Math.abs(r),1/3)*(0>r?-1:1),a=-n-t,o=Math.pow(Math.abs(a),1/3)*(0>a?-1:1),s=i+o+.5;return 3*(1-s)*s*s+s*s*s},g.backin=function(e){if(1==e)return 1;var t=1.70158;return e*e*((t+1)*e-t)},g.backout=function(e){if(0==e)return 0;e-=1;var t=1.70158;return e*e*((t+1)*e+t)+1},g.elastic=function(e){return e==!!e?e:Math.pow(2,-10*e)*Math.sin((e-.075)*(2*Math.PI)/.3)+1},g.bounce=function(e){var t,n=7.5625,r=2.75;return 1/r>e?t=n*e*e:2/r>e?(e-=1.5/r,t=n*e*e+.75):2.5/r>e?(e-=2.25/r,t=n*e*e+.9375):(e-=2.625/r,t=n*e*e+.984375),t},e.mina=g,g}("undefined"==typeof t?function(){}:t),r=function(e){function n(e,t){if(e){if(e.tagName)return w(e);if(i(e,"array")&&n.set)return n.set.apply(n,e);if(e instanceof v)return e;if(null==t)return e=A.doc.querySelector(e),w(e)}return e=null==e?"100%":e,t=null==t?"100%":t,new E(e,t)}function r(e,t){if(t){if("#text"==e&&(e=A.doc.createTextNode(t.text||"")),"string"==typeof e&&(e=r(e)),"string"==typeof t)return"xlink:"==t.substring(0,6)?e.getAttributeNS(z,t.substring(6)):"xml:"==t.substring(0,4)?e.getAttributeNS($,t.substring(4)):e.getAttribute(t);for(var n in t)if(t[S](n)){var i=_(t[n]);i?"xlink:"==n.substring(0,6)?e.setAttributeNS(z,n.substring(6),i):"xml:"==n.substring(0,4)?e.setAttributeNS($,n.substring(4),i):e.setAttribute(n,i):e.removeAttribute(n)}}else e=A.doc.createElementNS($,e);return e}function i(e,t){return t=_.prototype.toLowerCase.call(t),"finite"==t?isFinite(e):"array"==t&&(e instanceof Array||Array.isArray&&Array.isArray(e))?!0:"null"==t&&null===e||t==typeof e&&null!==e||"object"==t&&e===Object(e)||O.call(e).slice(8,-1).toLowerCase()==t}function a(e){if("function"==typeof e||Object(e)!==e)return e;var t=new e.constructor;for(var n in e)e[S](n)&&(t[n]=a(e[n]));return t}function o(e,t){for(var n=0,r=e.length;r>n;n++)if(e[n]===t)return e.push(e.splice(n,1)[0])}function s(e,t,n){function r(){var i=Array.prototype.slice.call(arguments,0),a=i.join("␀"),s=r.cache=r.cache||{},c=r.count=r.count||[];return s[S](a)?(o(c,a),n?n(s[a]):s[a]):(c.length>=1e3&&delete s[c.shift()],c.push(a),s[a]=e.apply(t,i),n?n(s[a]):s[a])}return r}function c(e,t,n,r,i,a){if(null==i){var o=e-n,s=t-r;return o||s?(180+180*R.atan2(-s,-o)/P+360)%360:0}return c(e,t,i,a)-c(n,r,i,a)}function u(e){return e%360*P/180}function l(e){return 180*e/P%360}function p(e){var t=[];return e=e.replace(/(?:^|\s)(\w+)\(([^)]+)\)/g,function(e,n,r){return r=r.split(/\s*,\s*|\s+/),"rotate"==n&&1==r.length&&r.push(0,0),"scale"==n&&(r.length>2?r=r.slice(0,2):2==r.length&&r.push(0,0),1==r.length&&r.push(r[0],0,0)),"skewX"==n?t.push(["m",1,0,R.tan(u(r[0])),1,0,0]):"skewY"==n?t.push(["m",1,R.tan(u(r[0])),0,1,0,0]):t.push([n.charAt(0)].concat(r)),e}),t}function f(e,t){var r=J(e),i=new n.Matrix;if(r)for(var a=0,o=r.length;o>a;a++){var s,c,u,l,p,f=r[a],h=f.length,d=_(f[0]).toLowerCase(),m=f[0]!=d,y=m?i.invert():0;"t"==d&&2==h?i.translate(f[1],0):"t"==d&&3==h?m?(s=y.x(0,0),c=y.y(0,0),u=y.x(f[1],f[2]),l=y.y(f[1],f[2]),i.translate(u-s,l-c)):i.translate(f[1],f[2]):"r"==d?2==h?(p=p||t,i.rotate(f[1],p.x+p.width/2,p.y+p.height/2)):4==h&&(m?(u=y.x(f[2],f[3]),l=y.y(f[2],f[3]),i.rotate(f[1],u,l)):i.rotate(f[1],f[2],f[3])):"s"==d?2==h||3==h?(p=p||t,i.scale(f[1],f[h-1],p.x+p.width/2,p.y+p.height/2)):4==h?m?(u=y.x(f[2],f[3]),l=y.y(f[2],f[3]),i.scale(f[1],f[1],u,l)):i.scale(f[1],f[1],f[2],f[3]):5==h&&(m?(u=y.x(f[3],f[4]),l=y.y(f[3],f[4]),i.scale(f[1],f[2],u,l)):i.scale(f[1],f[2],f[3],f[4])):"m"==d&&7==h&&i.add(f[1],f[2],f[3],f[4],f[5],f[6])}return i}function h(e){var t=e.node.ownerSVGElement&&w(e.node.ownerSVGElement)||e.node.parentNode&&w(e.node.parentNode)||n.select("svg")||n(0,0),r=t.select("defs"),i=null==r?!1:r.node;return i||(i=b("defs",t.node).node),i}function d(e){return e.node.ownerSVGElement&&w(e.node.ownerSVGElement)||n.select("svg")}function m(e,t,n){function i(e){if(null==e)return k;if(e==+e)return e;r(u,{width:e});try{return u.getBBox().width}catch(t){return 0}}function a(e){if(null==e)return k;if(e==+e)return e;r(u,{height:e});try{return u.getBBox().height}catch(t){return 0}}function o(r,i){null==t?c[r]=i(e.attr(r)||0):r==t&&(c=i(null==n?e.attr(r)||0:n))}var s=d(e).node,c={},u=s.querySelector(".svg---mgr");switch(u||(u=r("rect"),r(u,{x:-9e9,y:-9e9,width:10,height:10,"class":"svg---mgr",fill:"none"}),s.appendChild(u)),e.type){case"rect":o("rx",i),o("ry",a);case"image":o("width",i),o("height",a);case"text":o("x",i),o("y",a);break;case"circle":o("cx",i),o("cy",a),o("r",i);break;case"ellipse":o("cx",i),o("cy",a),o("rx",i),o("ry",a);break;case"line":o("x1",i),o("x2",i),o("y1",a),o("y2",a);break;case"marker":o("refX",i),o("markerWidth",i),o("refY",a),o("markerHeight",a);break;case"radialGradient":o("fx",i),o("fy",a);break;case"tspan":o("dx",i),o("dy",a);break;default:o(t,i)}return s.removeChild(u),c}function y(e){i(e,"array")||(e=Array.prototype.slice.call(arguments,0));for(var t=0,n=0,r=this.node;this[t];)delete this[t++];for(t=0;t<e.length;t++)"set"==e[t].type?e[t].forEach(function(e){r.appendChild(e.node)}):r.appendChild(e[t].node);var a=r.childNodes;for(t=0;t<a.length;t++)this[n++]=w(a[t]);return this}function v(e){if(e.snap in q)return q[e.snap];var t;try{t=e.ownerSVGElement}catch(n){}this.node=e,t&&(this.paper=new E(t)),this.type=e.tagName;var r=this.id=W(this);if(this.anims={},this._={transform:[]},e.snap=r,q[r]=this,"g"==this.type&&(this.add=y),this.type in{g:1,mask:1,pattern:1,symbol:1})for(var i in E.prototype)E.prototype[S](i)&&(this[i]=E.prototype[i])}function x(e){this.node=e}function b(e,t){var n=r(e);t.appendChild(n);var i=w(n);return i}function E(e,t){var n,i,a,o=E.prototype;if(e&&"svg"==e.tagName){if(e.snap in q)return q[e.snap];var s=e.ownerDocument;n=new v(e),i=e.getElementsByTagName("desc")[0],a=e.getElementsByTagName("defs")[0],i||(i=r("desc"),i.appendChild(s.createTextNode("Created with Snap")),n.node.appendChild(i)),a||(a=r("defs"),n.node.appendChild(a)),n.defs=a;for(var c in o)o[S](c)&&(n[c]=o[c]);n.paper=n.root=n}else n=b("svg",A.doc.body),r(n.node,{height:t,version:1.1,width:e,xmlns:$});return n}function w(e){return e?e instanceof v||e instanceof x?e:e.tagName&&"svg"==e.tagName.toLowerCase()?new E(e):e.tagName&&"object"==e.tagName.toLowerCase()&&"image/svg+xml"==e.type?new E(e.contentDocument.getElementsByTagName("svg")[0]):new v(e):e}n.version="0.3.0",n.toString=function(){return"Snap v"+this.version},n._={};var A={win:e.window,doc:e.window.document};n._.glob=A;var S="hasOwnProperty",_=String,T=parseFloat,C=parseInt,R=Math,N=R.max,M=R.min,D=R.abs,P=(R.pow,R.PI),k=(R.round,""),O=Object.prototype.toString,B=/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\))\s*$/i,I=(n._.separator=/[,\s]+/,/[\s]*,[\s]*/),L={hs:1,rg:1},F=/([a-z])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/gi,j=/([rstm])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/gi,V=/(-?\d*\.?\d*(?:e[\-+]?\\d+)?)[\s]*,?[\s]*/gi,G=0,U="S"+(+new Date).toString(36),W=function(e){return(e&&e.type?e.type:k)+U+(G++).toString(36)},z="http://www.w3.org/1999/xlink",$="http://www.w3.org/2000/svg",q={};n.url=function(e){return"url('#"+e+"')"};n._.$=r,n._.id=W,n.format=function(){var e=/\{([^\}]+)\}/g,t=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,n=function(e,n,r){var i=r;return n.replace(t,function(e,t,n,r,a){t=t||r,i&&(t in i&&(i=i[t]),"function"==typeof i&&a&&(i=i()))}),i=(null==i||i==r?e:i)+""};return function(t,r){return _(t).replace(e,function(e,t){return n(e,t,r)})}}(),n._.clone=a,n._.cacher=s,n.rad=u,n.deg=l,n.angle=c,n.is=i,n.snapTo=function(e,t,n){if(n=i(n,"finite")?n:10,i(e,"array")){for(var r=e.length;r--;)if(D(e[r]-t)<=n)return e[r]}else{e=+e;var a=t%e;if(n>a)return t-a;if(a>e-n)return t-a+e}return t},n.getRGB=s(function(e){if(!e||(e=_(e)).indexOf("-")+1)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:X};if("none"==e)return{r:-1,g:-1,b:-1,hex:"none",toString:X};if(!(L[S](e.toLowerCase().substring(0,2))||"#"==e.charAt())&&(e=H(e)),!e)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:X};var t,r,a,o,s,c,u=e.match(B);return u?(u[2]&&(a=C(u[2].substring(5),16),r=C(u[2].substring(3,5),16),t=C(u[2].substring(1,3),16)),u[3]&&(a=C((s=u[3].charAt(3))+s,16),r=C((s=u[3].charAt(2))+s,16),t=C((s=u[3].charAt(1))+s,16)),u[4]&&(c=u[4].split(I),t=T(c[0]),"%"==c[0].slice(-1)&&(t*=2.55),r=T(c[1]),"%"==c[1].slice(-1)&&(r*=2.55),a=T(c[2]),"%"==c[2].slice(-1)&&(a*=2.55),"rgba"==u[1].toLowerCase().slice(0,4)&&(o=T(c[3])),c[3]&&"%"==c[3].slice(-1)&&(o/=100)),u[5]?(c=u[5].split(I),t=T(c[0]),"%"==c[0].slice(-1)&&(t/=100),r=T(c[1]),"%"==c[1].slice(-1)&&(r/=100),a=T(c[2]),"%"==c[2].slice(-1)&&(a/=100),("deg"==c[0].slice(-3)||"°"==c[0].slice(-1))&&(t/=360),"hsba"==u[1].toLowerCase().slice(0,4)&&(o=T(c[3])),c[3]&&"%"==c[3].slice(-1)&&(o/=100),n.hsb2rgb(t,r,a,o)):u[6]?(c=u[6].split(I),t=T(c[0]),"%"==c[0].slice(-1)&&(t/=100),r=T(c[1]),"%"==c[1].slice(-1)&&(r/=100),a=T(c[2]),"%"==c[2].slice(-1)&&(a/=100),("deg"==c[0].slice(-3)||"°"==c[0].slice(-1))&&(t/=360),
"hsla"==u[1].toLowerCase().slice(0,4)&&(o=T(c[3])),c[3]&&"%"==c[3].slice(-1)&&(o/=100),n.hsl2rgb(t,r,a,o)):(t=M(R.round(t),255),r=M(R.round(r),255),a=M(R.round(a),255),o=M(N(o,0),1),u={r:t,g:r,b:a,toString:X},u.hex="#"+(16777216|a|r<<8|t<<16).toString(16).slice(1),u.opacity=i(o,"finite")?o:1,u)):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:X}},n),n.hsb=s(function(e,t,r){return n.hsb2rgb(e,t,r).hex}),n.hsl=s(function(e,t,r){return n.hsl2rgb(e,t,r).hex}),n.rgb=s(function(e,t,n,r){if(i(r,"finite")){var a=R.round;return"rgba("+[a(e),a(t),a(n),+r.toFixed(2)]+")"}return"#"+(16777216|n|t<<8|e<<16).toString(16).slice(1)});var H=function(e){var t=A.doc.getElementsByTagName("head")[0]||A.doc.getElementsByTagName("svg")[0],n="rgb(255, 0, 0)";return(H=s(function(e){if("red"==e.toLowerCase())return n;t.style.color=n,t.style.color=e;var r=A.doc.defaultView.getComputedStyle(t,k).getPropertyValue("color");return r==n?null:r}))(e)},Y=function(){return"hsb("+[this.h,this.s,this.b]+")"},K=function(){return"hsl("+[this.h,this.s,this.l]+")"},X=function(){return 1==this.opacity||null==this.opacity?this.hex:"rgba("+[this.r,this.g,this.b,this.opacity]+")"},Q=function(e,t,r){if(null==t&&i(e,"object")&&"r"in e&&"g"in e&&"b"in e&&(r=e.b,t=e.g,e=e.r),null==t&&i(e,string)){var a=n.getRGB(e);e=a.r,t=a.g,r=a.b}return(e>1||t>1||r>1)&&(e/=255,t/=255,r/=255),[e,t,r]},Z=function(e,t,r,a){e=R.round(255*e),t=R.round(255*t),r=R.round(255*r);var o={r:e,g:t,b:r,opacity:i(a,"finite")?a:1,hex:n.rgb(e,t,r),toString:X};return i(a,"finite")&&(o.opacity=a),o};n.color=function(e){var t;return i(e,"object")&&"h"in e&&"s"in e&&"b"in e?(t=n.hsb2rgb(e),e.r=t.r,e.g=t.g,e.b=t.b,e.opacity=1,e.hex=t.hex):i(e,"object")&&"h"in e&&"s"in e&&"l"in e?(t=n.hsl2rgb(e),e.r=t.r,e.g=t.g,e.b=t.b,e.opacity=1,e.hex=t.hex):(i(e,"string")&&(e=n.getRGB(e)),i(e,"object")&&"r"in e&&"g"in e&&"b"in e&&!("error"in e)?(t=n.rgb2hsl(e),e.h=t.h,e.s=t.s,e.l=t.l,t=n.rgb2hsb(e),e.v=t.b):(e={hex:"none"},e.r=e.g=e.b=e.h=e.s=e.v=e.l=-1,e.error=1)),e.toString=X,e},n.hsb2rgb=function(e,t,n,r){i(e,"object")&&"h"in e&&"s"in e&&"b"in e&&(n=e.b,t=e.s,e=e.h,r=e.o),e*=360;var a,o,s,c,u;return e=e%360/60,u=n*t,c=u*(1-D(e%2-1)),a=o=s=n-u,e=~~e,a+=[u,c,0,0,c,u][e],o+=[c,u,u,c,0,0][e],s+=[0,0,c,u,u,c][e],Z(a,o,s,r)},n.hsl2rgb=function(e,t,n,r){i(e,"object")&&"h"in e&&"s"in e&&"l"in e&&(n=e.l,t=e.s,e=e.h),(e>1||t>1||n>1)&&(e/=360,t/=100,n/=100),e*=360;var a,o,s,c,u;return e=e%360/60,u=2*t*(.5>n?n:1-n),c=u*(1-D(e%2-1)),a=o=s=n-u/2,e=~~e,a+=[u,c,0,0,c,u][e],o+=[c,u,u,c,0,0][e],s+=[0,0,c,u,u,c][e],Z(a,o,s,r)},n.rgb2hsb=function(e,t,n){n=Q(e,t,n),e=n[0],t=n[1],n=n[2];var r,i,a,o;return a=N(e,t,n),o=a-M(e,t,n),r=0==o?null:a==e?(t-n)/o:a==t?(n-e)/o+2:(e-t)/o+4,r=(r+360)%6*60/360,i=0==o?0:o/a,{h:r,s:i,b:a,toString:Y}},n.rgb2hsl=function(e,t,n){n=Q(e,t,n),e=n[0],t=n[1],n=n[2];var r,i,a,o,s,c;return o=N(e,t,n),s=M(e,t,n),c=o-s,r=0==c?null:o==e?(t-n)/c:o==t?(n-e)/c+2:(e-t)/c+4,r=(r+360)%6*60/360,a=(o+s)/2,i=0==c?0:.5>a?c/(2*a):c/(2-2*a),{h:r,s:i,l:a,toString:K}},n.parsePathString=function(e){if(!e)return null;var t=n.path(e);if(t.arr)return n.path.clone(t.arr);var r={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},a=[];return i(e,"array")&&i(e[0],"array")&&(a=n.path.clone(e)),a.length||_(e).replace(F,function(e,t,n){var i=[],o=t.toLowerCase();if(n.replace(V,function(e,t){t&&i.push(+t)}),"m"==o&&i.length>2&&(a.push([t].concat(i.splice(0,2))),o="l",t="m"==t?"l":"L"),"o"==o&&1==i.length&&a.push([t,i[0]]),"r"==o)a.push([t].concat(i));else for(;i.length>=r[o]&&(a.push([t].concat(i.splice(0,r[o]))),r[o]););}),a.toString=n.path.toString,t.arr=n.path.clone(a),a};var J=n.parseTransformString=function(e){if(!e)return null;var t=[];return i(e,"array")&&i(e[0],"array")&&(t=n.path.clone(e)),t.length||_(e).replace(j,function(e,n,r){var i=[];n.toLowerCase();r.replace(V,function(e,t){t&&i.push(+t)}),t.push([n].concat(i))}),t.toString=n.path.toString,t};n._.svgTransform2string=p,n._.rgTransform=/^[a-z][\s]*-?\.?\d/i,n._.transform2matrix=f,n._unit2px=m;A.doc.contains||A.doc.compareDocumentPosition?function(e,t){var n=9==e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e==r||!(!r||1!=r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t;)if(t=t.parentNode,t==e)return!0;return!1};n._.getSomeDefs=h,n._.getSomeSVG=d,n.select=function(e){return e=_(e).replace(/([^\\]):/g,"$1\\:"),w(A.doc.querySelector(e))},n.selectAll=function(e){for(var t=A.doc.querySelectorAll(e),r=(n.set||Array)(),i=0;i<t.length;i++)r.push(w(t[i]));return r},setInterval(function(){for(var e in q)if(q[S](e)){var t=q[e],n=t.node;("svg"!=t.type&&!n.ownerSVGElement||"svg"==t.type&&(!n.parentNode||"ownerSVGElement"in n.parentNode&&!n.ownerSVGElement))&&delete q[e]}},1e4),v.prototype.attr=function(e,n){var r=this;r.node;if(!e)return r;if(i(e,"string")){if(!(arguments.length>1))return t("snap.util.getattr."+e,r).firstDefined();var a={};a[e]=n,e=a}for(var o in e)e[S](o)&&t("snap.util.attr."+o,r,e[o]);return r},n.parse=function(e){var t=A.doc.createDocumentFragment(),n=!0,r=A.doc.createElement("div");if(e=_(e),e.match(/^\s*<\s*svg(?:\s|>)/)||(e="<svg>"+e+"</svg>",n=!1),r.innerHTML=e,e=r.getElementsByTagName("svg")[0])if(n)t=e;else{for(;e.firstChild;)t.appendChild(e.firstChild);r.innerHTML=k}return new x(t)},n.fragment=function(){for(var e=Array.prototype.slice.call(arguments,0),t=A.doc.createDocumentFragment(),r=0,i=e.length;i>r;r++){var a=e[r];a.node&&a.node.nodeType&&t.appendChild(a.node),a.nodeType&&t.appendChild(a),"string"==typeof a&&t.appendChild(n.parse(a).node)}return new x(t)},n._.make=b,n._.wrap=w,E.prototype.el=function(e,t){var n=b(e,this.node);return t&&n.attr(t),n},t.on("snap.util.getattr",function(){var e=t.nt();e=e.substring(e.lastIndexOf(".")+1);var n=e.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()});return ee[S](n)?this.node.ownerDocument.defaultView.getComputedStyle(this.node,null).getPropertyValue(n):r(this.node,e)});var ee={"alignment-baseline":0,"baseline-shift":0,clip:0,"clip-path":0,"clip-rule":0,color:0,"color-interpolation":0,"color-interpolation-filters":0,"color-profile":0,"color-rendering":0,cursor:0,direction:0,display:0,"dominant-baseline":0,"enable-background":0,fill:0,"fill-opacity":0,"fill-rule":0,filter:0,"flood-color":0,"flood-opacity":0,font:0,"font-family":0,"font-size":0,"font-size-adjust":0,"font-stretch":0,"font-style":0,"font-variant":0,"font-weight":0,"glyph-orientation-horizontal":0,"glyph-orientation-vertical":0,"image-rendering":0,kerning:0,"letter-spacing":0,"lighting-color":0,marker:0,"marker-end":0,"marker-mid":0,"marker-start":0,mask:0,opacity:0,overflow:0,"pointer-events":0,"shape-rendering":0,"stop-color":0,"stop-opacity":0,stroke:0,"stroke-dasharray":0,"stroke-dashoffset":0,"stroke-linecap":0,"stroke-linejoin":0,"stroke-miterlimit":0,"stroke-opacity":0,"stroke-width":0,"text-anchor":0,"text-decoration":0,"text-rendering":0,"unicode-bidi":0,visibility:0,"word-spacing":0,"writing-mode":0};t.on("snap.util.attr",function(e){var n=t.nt(),i={};n=n.substring(n.lastIndexOf(".")+1),i[n]=e;var a=n.replace(/-(\w)/gi,function(e,t){return t.toUpperCase()}),o=n.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()});ee[S](o)?this.node.style[a]=null==e?k:e:r(this.node,i)}),function(e){}(E.prototype),n.ajax=function(e,n,r,a){var o=new XMLHttpRequest,s=W();if(o){if(i(n,"function"))a=r,r=n,n=null;else if(i(n,"object")){var c=[];for(var u in n)n.hasOwnProperty(u)&&c.push(encodeURIComponent(u)+"="+encodeURIComponent(n[u]));n=c.join("&")}return o.open(n?"POST":"GET",e,!0),n&&(o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.setRequestHeader("Content-type","application/x-www-form-urlencoded")),r&&(t.once("snap.ajax."+s+".0",r),t.once("snap.ajax."+s+".200",r),t.once("snap.ajax."+s+".304",r)),o.onreadystatechange=function(){4==o.readyState&&t("snap.ajax."+s+"."+o.status,a,o)},4==o.readyState?o:(o.send(n),o)}},n.load=function(e,t,r){n.ajax(e,function(e){var i=n.parse(e.responseText);r?t.call(r,i):t(i)})};var te=function(e){var t=e.getBoundingClientRect(),n=e.ownerDocument,r=n.body,i=n.documentElement,a=i.clientTop||r.clientTop||0,o=i.clientLeft||r.clientLeft||0,s=t.top+(g.win.pageYOffset||i.scrollTop||r.scrollTop)-a,c=t.left+(g.win.pageXOffset||i.scrollLeft||r.scrollLeft)-o;return{y:s,x:c}};return n.getElementByPoint=function(e,t){var n=this,r=(n.canvas,A.doc.elementFromPoint(e,t));if(A.win.opera&&"svg"==r.tagName){var i=te(r),a=r.createSVGRect();a.x=e-i.x,a.y=t-i.y,a.width=a.height=1;var o=r.getIntersectionList(a,null);o.length&&(r=o[o.length-1])}return r?w(r):null},n.plugin=function(e){e(n,v,E,A,x)},A.win.Snap=n,n}(e||this);return r.plugin(function(r,i,a,o,s){function c(e,t){if(null==t){var n=!0;if(t="linearGradient"==e.type||"radialGradient"==e.type?e.node.getAttribute("gradientTransform"):"pattern"==e.type?e.node.getAttribute("patternTransform"):e.node.getAttribute("transform"),!t)return new r.Matrix;t=r._.svgTransform2string(t)}else t=r._.rgTransform.test(t)?d(t).replace(/\.{3}|\u2026/g,e._.transform||E):r._.svgTransform2string(t),h(t,"array")&&(t=r.path?r.path.toString.call(t):d(t)),e._.transform=t;var i=r._.transform2matrix(t,e.getBBox(1));return n?i:void(e.matrix=i)}function u(e){function t(e,t){var n=y(e.node,t);n=n&&n.match(a),n=n&&n[2],n&&"#"==n.charAt()&&(n=n.substring(1),n&&(s[n]=(s[n]||[]).concat(function(n){var r={};r[t]=URL(n),y(e.node,r)})))}function n(e){var t=y(e.node,"xlink:href");t&&"#"==t.charAt()&&(t=t.substring(1),t&&(s[t]=(s[t]||[]).concat(function(t){e.attr("xlink:href","#"+t)})))}for(var r,i=e.selectAll("*"),a=/^\s*url\(("|'|)(.*)\1\)\s*$/,o=[],s={},c=0,u=i.length;u>c;c++){r=i[c],t(r,"fill"),t(r,"stroke"),t(r,"filter"),t(r,"mask"),t(r,"clip-path"),n(r);var l=y(r.node,"id");l&&(y(r.node,{id:r.id}),o.push({old:l,id:r.id}))}for(c=0,u=o.length;u>c;c++){var p=s[o[c].old];if(p)for(var f=0,h=p.length;h>f;f++)p[f](o[c].id)}}function l(e,t,n){return function(r){var i=r.slice(e,t);return 1==i.length&&(i=i[0]),n?n(i):i}}function p(e){return function(){var t=e?"<"+this.type:"",n=this.node.attributes,r=this.node.childNodes;if(e)for(var i=0,a=n.length;a>i;i++)t+=" "+n[i].name+'="'+n[i].value.replace(/"/g,'\\"')+'"';if(r.length){for(e&&(t+=">"),i=0,a=r.length;a>i;i++)3==r[i].nodeType?t+=r[i].nodeValue:1==r[i].nodeType&&(t+=b(r[i]).toString());e&&(t+="</"+this.type+">")}else e&&(t+="/>");return t}}var f=i.prototype,h=r.is,d=String,m=r._unit2px,y=r._.$,g=r._.make,v=r._.getSomeDefs,x="hasOwnProperty",b=r._.wrap;f.getBBox=function(e){if(!r.Matrix||!r.path)return this.node.getBBox();var t=this,n=new r.Matrix;if(t.removed)return r._.box();for(;"use"==t.type;)if(e||(n=n.add(t.transform().localMatrix.translate(t.attr("x")||0,t.attr("y")||0))),t.original)t=t.original;else{var i=t.attr("xlink:href");t=t.original=t.node.ownerDocument.getElementById(i.substring(i.indexOf("#")+1))}var a=t._,o=r.path.get[t.type]||r.path.get.deflt;try{return e?(a.bboxwt=o?r.path.getBBox(t.realPath=o(t)):r._.box(t.node.getBBox()),r._.box(a.bboxwt)):(t.realPath=o(t),t.matrix=t.transform().localMatrix,a.bbox=r.path.getBBox(r.path.map(t.realPath,n.add(t.matrix))),r._.box(a.bbox))}catch(s){return r._.box()}};var w=function(){return this.string};f.transform=function(e){var t=this._;if(null==e){for(var n,i=this,a=new r.Matrix(this.node.getCTM()),o=c(this),s=[o],u=new r.Matrix,l=o.toTransformString(),p=d(o)==d(this.matrix)?d(t.transform):l;"svg"!=i.type&&(i=i.parent());)s.push(c(i));for(n=s.length;n--;)u.add(s[n]);return{string:p,globalMatrix:a,totalMatrix:u,localMatrix:o,diffMatrix:a.clone().add(o.invert()),global:a.toTransformString(),total:u.toTransformString(),local:l,toString:w}}return e instanceof r.Matrix?(this.matrix=e,this._.transform=e.toTransformString()):c(this,e),this.node&&("linearGradient"==this.type||"radialGradient"==this.type?y(this.node,{gradientTransform:this.matrix}):"pattern"==this.type?y(this.node,{patternTransform:this.matrix}):y(this.node,{transform:this.matrix})),this},f.parent=function(){return b(this.node.parentNode)},f.append=f.add=function(e){if(e){if("set"==e.type){var t=this;return e.forEach(function(e){t.add(e)}),this}e=b(e),this.node.appendChild(e.node),e.paper=this.paper}return this},f.appendTo=function(e){return e&&(e=b(e),e.append(this)),this},f.prepend=function(e){if(e){if("set"==e.type){var t,n=this;return e.forEach(function(e){t?t.after(e):n.prepend(e),t=e}),this}e=b(e);var r=e.parent();this.node.insertBefore(e.node,this.node.firstChild),this.add&&this.add(),e.paper=this.paper,this.parent()&&this.parent().add(),r&&r.add()}return this},f.prependTo=function(e){return e=b(e),e.prepend(this),this},f.before=function(e){if("set"==e.type){var t=this;return e.forEach(function(e){var n=e.parent();t.node.parentNode.insertBefore(e.node,t.node),n&&n.add()}),this.parent().add(),this}e=b(e);var n=e.parent();return this.node.parentNode.insertBefore(e.node,this.node),this.parent()&&this.parent().add(),n&&n.add(),e.paper=this.paper,this},f.after=function(e){e=b(e);var t=e.parent();return this.node.nextSibling?this.node.parentNode.insertBefore(e.node,this.node.nextSibling):this.node.parentNode.appendChild(e.node),this.parent()&&this.parent().add(),t&&t.add(),e.paper=this.paper,this},f.insertBefore=function(e){e=b(e);var t=this.parent();return e.node.parentNode.insertBefore(this.node,e.node),this.paper=e.paper,t&&t.add(),e.parent()&&e.parent().add(),this},f.insertAfter=function(e){e=b(e);var t=this.parent();return e.node.parentNode.insertBefore(this.node,e.node.nextSibling),this.paper=e.paper,t&&t.add(),e.parent()&&e.parent().add(),this},f.remove=function(){var e=this.parent();return this.node.parentNode&&this.node.parentNode.removeChild(this.node),delete this.paper,this.removed=!0,e&&e.add(),this},f.select=function(e){return e=d(e).replace(/([^\\]):/g,"$1\\:"),b(this.node.querySelector(e))},f.selectAll=function(e){for(var t=this.node.querySelectorAll(e),n=(r.set||Array)(),i=0;i<t.length;i++)n.push(b(t[i]));return n},f.asPX=function(e,t){return null==t&&(t=this.attr(e)),+m(this,e,t)},f.use=function(){var e,t=this.node.id;return t||(t=this.id,y(this.node,{id:t})),e="linearGradient"==this.type||"radialGradient"==this.type||"pattern"==this.type?g(this.type,this.node.parentNode):g("use",this.node.parentNode),y(e.node,{"xlink:href":"#"+t}),e.original=this,e},f.clone=function(){var e=b(this.node.cloneNode(!0));return y(e.node,"id")&&y(e.node,{id:e.id}),u(e),e.insertAfter(this),e},f.toDefs=function(){var e=v(this);return e.appendChild(this.node),this},f.pattern=f.toPattern=function(e,t,n,r){var i=g("pattern",v(this));return null==e&&(e=this.getBBox()),h(e,"object")&&"x"in e&&(t=e.y,n=e.width,r=e.height,e=e.x),y(i.node,{x:e,y:t,width:n,height:r,patternUnits:"userSpaceOnUse",id:i.id,viewBox:[e,t,n,r].join(" ")}),i.node.appendChild(this.node),i},f.marker=function(e,t,n,r,i,a){var o=g("marker",v(this));return null==e&&(e=this.getBBox()),h(e,"object")&&"x"in e&&(t=e.y,n=e.width,r=e.height,i=e.refX||e.cx,a=e.refY||e.cy,e=e.x),y(o.node,{viewBox:[e,t,n,r].join(" "),markerWidth:n,markerHeight:r,orient:"auto",refX:i||0,refY:a||0,id:o.id}),o.node.appendChild(this.node),o};var A=function(e,t,r,i){"function"!=typeof r||r.length||(i=r,r=n.linear),this.attr=e,this.dur=t,r&&(this.easing=r),i&&(this.callback=i)};r._.Animation=A,r.animation=function(e,t,n,r){return new A(e,t,n,r)},f.inAnim=function(){var e=this,t=[];for(var n in e.anims)e.anims[x](n)&&!function(e){t.push({anim:new A(e._attrs,e.dur,e.easing,e._callback),mina:e,curStatus:e.status(),status:function(t){return e.status(t)},stop:function(){e.stop()}})}(e.anims[n]);return t},r.animate=function(e,r,i,a,o,s){"function"!=typeof o||o.length||(s=o,o=n.linear);var c=n.time(),u=n(e,r,c,c+a,n.time,i,o);return s&&t.once("mina.finish."+u.id,s),u},f.stop=function(){for(var e=this.inAnim(),t=0,n=e.length;n>t;t++)e[t].stop();return this},f.animate=function(e,r,i,a){"function"!=typeof i||i.length||(a=i,i=n.linear),e instanceof A&&(a=e.callback,i=e.easing,r=i.dur,e=e.attr);var o,s,c,u,p=[],f=[],m={},y=this;for(var g in e)if(e[x](g)){y.equal?(u=y.equal(g,d(e[g])),o=u.from,s=u.to,c=u.f):(o=+y.attr(g),s=+e[g]);var v=h(o,"array")?o.length:1;m[g]=l(p.length,p.length+v,c),p=p.concat(o),f=f.concat(s)}var b=n.time(),E=n(p,f,b,b+r,n.time,function(e){var t={};for(var n in m)m[x](n)&&(t[n]=m[n](e));y.attr(t)},i);return y.anims[E.id]=E,E._attrs=e,E._callback=a,t("snap.animcreated."+y.id,E),t.once("mina.finish."+E.id,function(){delete y.anims[E.id],a&&a.call(y)}),t.once("mina.stop."+E.id,function(){delete y.anims[E.id]}),y};var S={};f.data=function(e,n){var i=S[this.id]=S[this.id]||{};if(0==arguments.length)return t("snap.data.get."+this.id,this,i,null),i;if(1==arguments.length){if(r.is(e,"object")){for(var a in e)e[x](a)&&this.data(a,e[a]);return this}return t("snap.data.get."+this.id,this,i[e],e),i[e]}return i[e]=n,t("snap.data.set."+this.id,this,n,e),this},f.removeData=function(e){return null==e?S[this.id]={}:S[this.id]&&delete S[this.id][e],this},f.outerSVG=f.toString=p(1),f.innerSVG=p(),f.toDataURL=function(){if(e&&e.btoa){var t=this.getBBox(),n=r.format('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="{width}" height="{height}" viewBox="{x} {y} {width} {height}">{contents}</svg>',{x:+t.x.toFixed(3),y:+t.y.toFixed(3),width:+t.width.toFixed(3),height:+t.height.toFixed(3),contents:this.outerSVG()});return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(n)))}},s.prototype.select=f.select,s.prototype.selectAll=f.selectAll}),r.plugin(function(e,t,n,r,i){function a(e,t,n,r,i,a){return null==t&&"[object SVGMatrix]"==o.call(e)?(this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.e=e.e,void(this.f=e.f)):void(null!=e?(this.a=+e,this.b=+t,this.c=+n,this.d=+r,this.e=+i,this.f=+a):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0))}var o=Object.prototype.toString,s=String,c=Math,u="";!function(t){function n(e){return e[0]*e[0]+e[1]*e[1]}function r(e){var t=c.sqrt(n(e));e[0]&&(e[0]/=t),e[1]&&(e[1]/=t)}t.add=function(e,t,n,r,i,o){var s,c,u,l,p=[[],[],[]],f=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],h=[[e,n,i],[t,r,o],[0,0,1]];for(e&&e instanceof a&&(h=[[e.a,e.c,e.e],[e.b,e.d,e.f],[0,0,1]]),s=0;3>s;s++)for(c=0;3>c;c++){for(l=0,u=0;3>u;u++)l+=f[s][u]*h[u][c];p[s][c]=l}return this.a=p[0][0],this.b=p[1][0],this.c=p[0][1],this.d=p[1][1],this.e=p[0][2],this.f=p[1][2],this},t.invert=function(){var e=this,t=e.a*e.d-e.b*e.c;return new a(e.d/t,-e.b/t,-e.c/t,e.a/t,(e.c*e.f-e.d*e.e)/t,(e.b*e.e-e.a*e.f)/t)},t.clone=function(){return new a(this.a,this.b,this.c,this.d,this.e,this.f)},t.translate=function(e,t){return this.add(1,0,0,1,e,t)},t.scale=function(e,t,n,r){return null==t&&(t=e),(n||r)&&this.add(1,0,0,1,n,r),this.add(e,0,0,t,0,0),(n||r)&&this.add(1,0,0,1,-n,-r),this},t.rotate=function(t,n,r){t=e.rad(t),n=n||0,r=r||0;var i=+c.cos(t).toFixed(9),a=+c.sin(t).toFixed(9);return this.add(i,a,-a,i,n,r),this.add(1,0,0,1,-n,-r)},t.x=function(e,t){return e*this.a+t*this.c+this.e},t.y=function(e,t){return e*this.b+t*this.d+this.f},t.get=function(e){return+this[s.fromCharCode(97+e)].toFixed(4)},t.toString=function(){return"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")"},t.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},t.determinant=function(){return this.a*this.d-this.b*this.c},t.split=function(){var t={};t.dx=this.e,t.dy=this.f;var i=[[this.a,this.c],[this.b,this.d]];t.scalex=c.sqrt(n(i[0])),r(i[0]),t.shear=i[0][0]*i[1][0]+i[0][1]*i[1][1],i[1]=[i[1][0]-i[0][0]*t.shear,i[1][1]-i[0][1]*t.shear],t.scaley=c.sqrt(n(i[1])),r(i[1]),t.shear/=t.scaley,this.determinant()<0&&(t.scalex=-t.scalex);var a=-i[0][1],o=i[1][1];return 0>o?(t.rotate=e.deg(c.acos(o)),0>a&&(t.rotate=360-t.rotate)):t.rotate=e.deg(c.asin(a)),t.isSimple=!(+t.shear.toFixed(9)||t.scalex.toFixed(9)!=t.scaley.toFixed(9)&&t.rotate),t.isSuperSimple=!+t.shear.toFixed(9)&&t.scalex.toFixed(9)==t.scaley.toFixed(9)&&!t.rotate,t.noRotation=!+t.shear.toFixed(9)&&!t.rotate,t},t.toTransformString=function(e){var t=e||this.split();return+t.shear.toFixed(9)?"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]:(t.scalex=+t.scalex.toFixed(4),t.scaley=+t.scaley.toFixed(4),t.rotate=+t.rotate.toFixed(4),(t.dx||t.dy?"t"+[+t.dx.toFixed(4),+t.dy.toFixed(4)]:u)+(1!=t.scalex||1!=t.scaley?"s"+[t.scalex,t.scaley,0,0]:u)+(t.rotate?"r"+[+t.rotate.toFixed(4),0,0]:u))}}(a.prototype),e.Matrix=a,e.matrix=function(e,t,n,r,i,o){return new a(e,t,n,r,i,o)}}),r.plugin(function(e,n,r,i,a){function o(r){return function(i){if(t.stop(),i instanceof a&&1==i.node.childNodes.length&&("radialGradient"==i.node.firstChild.tagName||"linearGradient"==i.node.firstChild.tagName||"pattern"==i.node.firstChild.tagName)&&(i=i.node.firstChild,h(this).appendChild(i),i=p(i)),i instanceof n)if("radialGradient"==i.type||"linearGradient"==i.type||"pattern"==i.type){i.node.id||m(i.node,{id:i.id});var o=y(i.node.id)}else o=i.attr(r);else if(o=e.color(i),o.error){var s=e(h(this).ownerSVGElement).gradient(i);s?(s.node.id||m(s.node,{id:s.id}),o=y(s.node.id)):o=i}else o=g(o);var c={};c[r]=o,m(this.node,c),this.node.style[r]=x}}function s(e){t.stop(),e==+e&&(e+="px"),this.node.style.fontSize=e}function c(e){for(var t=[],n=e.childNodes,r=0,i=n.length;i>r;r++){var a=n[r];3==a.nodeType&&t.push(a.nodeValue),"tspan"==a.tagName&&(1==a.childNodes.length&&3==a.firstChild.nodeType?t.push(a.firstChild.nodeValue):t.push(c(a)))}return t}function u(){return t.stop(),this.node.style.fontSize}var l=e._.make,p=e._.wrap,f=e.is,h=e._.getSomeDefs,d=/^url\(#?([^)]+)\)$/,m=e._.$,y=e.url,g=String,v=e._.separator,x="";t.on("snap.util.attr.mask",function(e){if(e instanceof n||e instanceof a){if(t.stop(),e instanceof a&&1==e.node.childNodes.length&&(e=e.node.firstChild,h(this).appendChild(e),e=p(e)),"mask"==e.type)var r=e;else r=l("mask",h(this)),r.node.appendChild(e.node);!r.node.id&&m(r.node,{id:r.id}),m(this.node,{mask:y(r.id)})}}),function(e){t.on("snap.util.attr.clip",e),t.on("snap.util.attr.clip-path",e),t.on("snap.util.attr.clipPath",e)}(function(e){if(e instanceof n||e instanceof a){if(t.stop(),"clipPath"==e.type)var r=e;else r=l("clipPath",h(this)),r.node.appendChild(e.node),!r.node.id&&m(r.node,{id:r.id});m(this.node,{"clip-path":y(r.node.id||r.id)})}}),t.on("snap.util.attr.fill",o("fill")),t.on("snap.util.attr.stroke",o("stroke"));var b=/^([lr])(?:\(([^)]*)\))?(.*)$/i;t.on("snap.util.grad.parse",function(e){e=g(e);var t=e.match(b);if(!t)return null;var n=t[1],r=t[2],i=t[3];return r=r.split(/\s*,\s*/).map(function(e){return+e==e?+e:e}),1==r.length&&0==r[0]&&(r=[]),i=i.split("-"),i=i.map(function(e){e=e.split(":");var t={color:e[0]};return e[1]&&(t.offset=parseFloat(e[1])),t}),{type:n,params:r,stops:i}}),t.on("snap.util.attr.d",function(n){t.stop(),f(n,"array")&&f(n[0],"array")&&(n=e.path.toString.call(n)),n=g(n),n.match(/[ruo]/i)&&(n=e.path.toAbsolute(n)),m(this.node,{d:n})})(-1),t.on("snap.util.attr.#text",function(e){t.stop(),e=g(e);for(var n=i.doc.createTextNode(e);this.node.firstChild;)this.node.removeChild(this.node.firstChild);this.node.appendChild(n)})(-1),t.on("snap.util.attr.path",function(e){t.stop(),this.attr({d:e})})(-1),t.on("snap.util.attr.class",function(e){t.stop(),this.node.className.baseVal=e})(-1),t.on("snap.util.attr.viewBox",function(e){var n;n=f(e,"object")&&"x"in e?[e.x,e.y,e.width,e.height].join(" "):f(e,"array")?e.join(" "):e,m(this.node,{viewBox:n}),t.stop()})(-1),t.on("snap.util.attr.transform",function(e){this.transform(e),t.stop()})(-1),t.on("snap.util.attr.r",function(e){"rect"==this.type&&(t.stop(),m(this.node,{rx:e,ry:e}))})(-1),t.on("snap.util.attr.textpath",function(e){if(t.stop(),"text"==this.type){var r,i,a;if(!e&&this.textPath){for(i=this.textPath;i.node.firstChild;)this.node.appendChild(i.node.firstChild);return i.remove(),void delete this.textPath}if(f(e,"string")){var o=h(this),s=p(o.parentNode).path(e);o.appendChild(s.node),r=s.id,s.attr({id:r})}else e=p(e),e instanceof n&&(r=e.attr("id"),r||(r=e.id,e.attr({id:r})));if(r)if(i=this.textPath,a=this.node,i)i.attr({"xlink:href":"#"+r});else{for(i=m("textPath",{"xlink:href":"#"+r});a.firstChild;)i.appendChild(a.firstChild);a.appendChild(i),this.textPath=p(i)}}})(-1),t.on("snap.util.attr.text",function(e){if("text"==this.type){for(var n=this.node,r=function(e){var t=m("tspan");if(f(e,"array"))for(var n=0;n<e.length;n++)t.appendChild(r(e[n]));else t.appendChild(i.doc.createTextNode(e));return t.normalize&&t.normalize(),t};n.firstChild;)n.removeChild(n.firstChild);for(var a=r(e);a.firstChild;)n.appendChild(a.firstChild)}t.stop()})(-1),t.on("snap.util.attr.fontSize",s)(-1),t.on("snap.util.attr.font-size",s)(-1),t.on("snap.util.getattr.transform",function(){return t.stop(),this.transform()})(-1),t.on("snap.util.getattr.textpath",function(){return t.stop(),this.textPath})(-1),function(){function n(n){return function(){t.stop();var r=i.doc.defaultView.getComputedStyle(this.node,null).getPropertyValue("marker-"+n);return"none"==r?r:e(i.doc.getElementById(r.match(d)[1]))}}function r(e){return function(n){t.stop();var r="marker"+e.charAt(0).toUpperCase()+e.substring(1);if(""==n||!n)return void(this.node.style[r]="none");if("marker"==n.type){var i=n.node.id;return i||m(n.node,{id:n.id}),void(this.node.style[r]=y(i))}}}t.on("snap.util.getattr.marker-end",n("end"))(-1),t.on("snap.util.getattr.markerEnd",n("end"))(-1),t.on("snap.util.getattr.marker-start",n("start"))(-1),t.on("snap.util.getattr.markerStart",n("start"))(-1),t.on("snap.util.getattr.marker-mid",n("mid"))(-1),t.on("snap.util.getattr.markerMid",n("mid"))(-1),t.on("snap.util.attr.marker-end",r("end"))(-1),t.on("snap.util.attr.markerEnd",r("end"))(-1),t.on("snap.util.attr.marker-start",r("start"))(-1),t.on("snap.util.attr.markerStart",r("start"))(-1),t.on("snap.util.attr.marker-mid",r("mid"))(-1),t.on("snap.util.attr.markerMid",r("mid"))(-1)}(),t.on("snap.util.getattr.r",function(){return"rect"==this.type&&m(this.node,"rx")==m(this.node,"ry")?(t.stop(),m(this.node,"rx")):void 0})(-1),t.on("snap.util.getattr.text",function(){if("text"==this.type||"tspan"==this.type){t.stop();var e=c(this.node);return 1==e.length?e[0]:e}})(-1),t.on("snap.util.getattr.#text",function(){return this.node.textContent})(-1),t.on("snap.util.getattr.viewBox",function(){t.stop();var n=m(this.node,"viewBox");return n?(n=n.split(v),e._.box(+n[0],+n[1],+n[2],+n[3])):void 0})(-1),t.on("snap.util.getattr.points",function(){var e=m(this.node,"points");return t.stop(),e?e.split(v):void 0})(-1),t.on("snap.util.getattr.path",function(){var e=m(this.node,"d");return t.stop(),e})(-1),t.on("snap.util.getattr.class",function(){return this.node.className.baseVal})(-1),t.on("snap.util.getattr.fontSize",u)(-1),t.on("snap.util.getattr.font-size",u)(-1)}),r.plugin(function(n,r,i,a,o){var s=i.prototype,c=n.is;s.rect=function(e,t,n,r,i,a){var o;return null==a&&(a=i),c(e,"object")&&"[object Object]"==e?o=e:null!=e&&(o={x:e,y:t,width:n,height:r},null!=i&&(o.rx=i,o.ry=a)),this.el("rect",o)},s.circle=function(e,t,n){var r;return c(e,"object")&&"[object Object]"==e?r=e:null!=e&&(r={cx:e,cy:t,r:n}),this.el("circle",r)};var u=function(){function e(){this.parentNode.removeChild(this)}return function(t,n){var r=a.doc.createElement("img"),i=a.doc.body;r.style.cssText="position:absolute;left:-9999em;top:-9999em",r.onload=function(){n.call(r),r.onload=r.onerror=null,i.removeChild(r)},r.onerror=e,i.appendChild(r),r.src=t}}();s.image=function(e,t,r,i,a){var o=this.el("image");if(c(e,"object")&&"src"in e)o.attr(e);else if(null!=e){var s={"xlink:href":e,preserveAspectRatio:"none"};null!=t&&null!=r&&(s.x=t,s.y=r),null!=i&&null!=a?(s.width=i,s.height=a):u(e,function(){n._.$(o.node,{width:this.offsetWidth,height:this.offsetHeight})}),n._.$(o.node,s)}return o},s.ellipse=function(e,t,n,r){var i;return c(e,"object")&&"[object Object]"==e?i=e:null!=e&&(i={cx:e,cy:t,rx:n,ry:r}),this.el("ellipse",i)},s.path=function(e){var t;return c(e,"object")&&!c(e,"array")?t=e:e&&(t={d:e}),this.el("path",t)},s.group=s.g=function(e){var t=this.el("g");return 1==arguments.length&&e&&!e.type?t.attr(e):arguments.length&&t.add(Array.prototype.slice.call(arguments,0)),t},s.svg=function(e,t,n,r,i,a,o,s){var u={};return c(e,"object")&&null==t?u=e:(null!=e&&(u.x=e),null!=t&&(u.y=t),null!=n&&(u.width=n),null!=r&&(u.height=r),null!=i&&null!=a&&null!=o&&null!=s&&(u.viewBox=[i,a,o,s])),this.el("svg",u)},s.mask=function(e){var t=this.el("mask");return 1==arguments.length&&e&&!e.type?t.attr(e):arguments.length&&t.add(Array.prototype.slice.call(arguments,0)),t},s.ptrn=function(e,t,n,r,i,a,o,s){if(c(e,"object"))var u=e;else u={patternUnits:"userSpaceOnUse"},e&&(u.x=e),t&&(u.y=t),null!=n&&(u.width=n),null!=r&&(u.height=r),null!=i&&null!=a&&null!=o&&null!=s&&(u.viewBox=[i,a,o,s]);return this.el("pattern",u)},s.use=function(e){return null!=e?(e instanceof r&&(e.attr("id")||e.attr({id:n._.id(e)}),e=e.attr("id")),"#"==String(e).charAt()&&(e=e.substring(1)),this.el("use",{"xlink:href":"#"+e})):r.prototype.use.call(this)},s.symbol=function(e,t,n,r){var i={};return null!=e&&null!=t&&null!=n&&null!=r&&(i.viewBox=[e,t,n,r]),this.el("symbol",i)},s.text=function(e,t,n){var r={};return c(e,"object")?r=e:null!=e&&(r={x:e,y:t,text:n||""}),this.el("text",r)},s.line=function(e,t,n,r){var i={};return c(e,"object")?i=e:null!=e&&(i={x1:e,x2:n,y1:t,y2:r}),this.el("line",i)},s.polyline=function(e){arguments.length>1&&(e=Array.prototype.slice.call(arguments,0));var t={};return c(e,"object")&&!c(e,"array")?t=e:null!=e&&(t={points:e}),this.el("polyline",t)},s.polygon=function(e){arguments.length>1&&(e=Array.prototype.slice.call(arguments,0));var t={};return c(e,"object")&&!c(e,"array")?t=e:null!=e&&(t={points:e}),this.el("polygon",t)},function(){function r(){return this.selectAll("stop")}function i(e,t){var r=l("stop"),i={offset:+t+"%"};return e=n.color(e),i["stop-color"]=e.hex,e.opacity<1&&(i["stop-opacity"]=e.opacity),l(r,i),this.node.appendChild(r),this}function a(){if("linearGradient"==this.type){var e=l(this.node,"x1")||0,t=l(this.node,"x2")||1,r=l(this.node,"y1")||0,i=l(this.node,"y2")||0;return n._.box(e,r,math.abs(t-e),math.abs(i-r))}var a=this.node.cx||.5,o=this.node.cy||.5,s=this.node.r||0;return n._.box(a-s,o-s,2*s,2*s)}function o(e,n){function r(e,t){for(var n=(t-p)/(e-f),r=f;e>r;r++)o[r].offset=+(+p+n*(r-f)).toFixed(2);f=e,p=t}var i,a=t("snap.util.grad.parse",null,n).firstDefined();if(!a)return null;a.params.unshift(e),i="l"==a.type.toLowerCase()?c.apply(0,a.params):u.apply(0,a.params),a.type!=a.type.toLowerCase()&&l(i.node,{gradientUnits:"userSpaceOnUse"});var o=a.stops,s=o.length,p=0,f=0;s--;for(var h=0;s>h;h++)"offset"in o[h]&&r(h,o[h].offset);for(o[s].offset=o[s].offset||100,r(s,o[s].offset),h=0;s>=h;h++){var d=o[h];i.addStop(d.color,d.offset)}return i}function c(e,t,o,s,c){var u=n._.make("linearGradient",e);return u.stops=r,u.addStop=i,u.getBBox=a,null!=t&&l(u.node,{x1:t,y1:o,x2:s,y2:c}),u}function u(e,t,o,s,c,u){var p=n._.make("radialGradient",e);return p.stops=r,p.addStop=i,p.getBBox=a,null!=t&&l(p.node,{cx:t,cy:o,r:s}),null!=c&&null!=u&&l(p.node,{fx:c,fy:u}),p}var l=n._.$;s.gradient=function(e){return o(this.defs,e)},s.gradientLinear=function(e,t,n,r){return c(this.defs,e,t,n,r)},s.gradientRadial=function(e,t,n,r,i){return u(this.defs,e,t,n,r,i)},s.toString=function(){var e,t=this.node.ownerDocument,r=t.createDocumentFragment(),i=t.createElement("div"),a=this.node.cloneNode(!0);return r.appendChild(i),i.appendChild(a),n._.$(a,{xmlns:"http://www.w3.org/2000/svg"}),e=i.innerHTML,r.removeChild(r.firstChild),e},s.toDataURL=function(){return e&&e.btoa?"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(this))):void 0},s.clear=function(){for(var e,t=this.node.firstChild;t;)e=t.nextSibling,"defs"!=t.tagName?t.parentNode.removeChild(t):s.clear.call({node:t}),t=e}}()}),r.plugin(function(e,t,n,r){function i(e){var t=i.ps=i.ps||{};return t[e]?t[e].sleep=100:t[e]={sleep:100},setTimeout(function(){for(var n in t)t[F](n)&&n!=e&&(t[n].sleep--,!t[n].sleep&&delete t[n])}),t[e]}function a(e,t,n,r){
return null==e&&(e=t=n=r=0),null==t&&(t=e.y,n=e.width,r=e.height,e=e.x),{x:e,y:t,width:n,w:n,height:r,h:r,x2:e+n,y2:t+r,cx:e+n/2,cy:t+r/2,r1:G.min(n,r)/2,r2:G.max(n,r)/2,r0:G.sqrt(n*n+r*r)/2,path:S(e,t,n,r),vb:[e,t,n,r].join(" ")}}function o(){return this.join(",").replace(j,"$1")}function s(e){var t=L(e);return t.toString=o,t}function c(e,t,n,r,i,a,o,s,c){return null==c?m(e,t,n,r,i,a,o,s):l(e,t,n,r,i,a,o,s,y(e,t,n,r,i,a,o,s,c))}function u(n,r){function i(e){return+(+e).toFixed(3)}return e._.cacher(function(e,a,o){e instanceof t&&(e=e.attr("d")),e=P(e);for(var s,u,p,f,h,d="",m={},y=0,g=0,v=e.length;v>g;g++){if(p=e[g],"M"==p[0])s=+p[1],u=+p[2];else{if(f=c(s,u,p[1],p[2],p[3],p[4],p[5],p[6]),y+f>a){if(r&&!m.start){if(h=c(s,u,p[1],p[2],p[3],p[4],p[5],p[6],a-y),d+=["C"+i(h.start.x),i(h.start.y),i(h.m.x),i(h.m.y),i(h.x),i(h.y)],o)return d;m.start=d,d=["M"+i(h.x),i(h.y)+"C"+i(h.n.x),i(h.n.y),i(h.end.x),i(h.end.y),i(p[5]),i(p[6])].join(),y+=f,s=+p[5],u=+p[6];continue}if(!n&&!r)return h=c(s,u,p[1],p[2],p[3],p[4],p[5],p[6],a-y)}y+=f,s=+p[5],u=+p[6]}d+=p.shift()+p}return m.end=d,h=n?y:r?m:l(s,u,p[0],p[1],p[2],p[3],p[4],p[5],1)},null,e._.clone)}function l(e,t,n,r,i,a,o,s,c){var u=1-c,l=$(u,3),p=$(u,2),f=c*c,h=f*c,d=l*e+3*p*c*n+3*u*c*c*i+h*o,m=l*t+3*p*c*r+3*u*c*c*a+h*s,y=e+2*c*(n-e)+f*(i-2*n+e),g=t+2*c*(r-t)+f*(a-2*r+t),v=n+2*c*(i-n)+f*(o-2*i+n),x=r+2*c*(a-r)+f*(s-2*a+r),b=u*e+c*n,E=u*t+c*r,w=u*i+c*o,A=u*a+c*s,S=90-180*G.atan2(y-v,g-x)/U;return{x:d,y:m,m:{x:y,y:g},n:{x:v,y:x},start:{x:b,y:E},end:{x:w,y:A},alpha:S}}function p(t,n,r,i,o,s,c,u){e.is(t,"array")||(t=[t,n,r,i,o,s,c,u]);var l=D.apply(null,t);return a(l.min.x,l.min.y,l.max.x-l.min.x,l.max.y-l.min.y)}function f(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function h(e,t){return e=a(e),t=a(t),f(t,e.x,e.y)||f(t,e.x2,e.y)||f(t,e.x,e.y2)||f(t,e.x2,e.y2)||f(e,t.x,t.y)||f(e,t.x2,t.y)||f(e,t.x,t.y2)||f(e,t.x2,t.y2)||(e.x<t.x2&&e.x>t.x||t.x<e.x2&&t.x>e.x)&&(e.y<t.y2&&e.y>t.y||t.y<e.y2&&t.y>e.y)}function d(e,t,n,r,i){var a=-3*t+9*n-9*r+3*i,o=e*a+6*t-12*n+6*r;return e*o-3*t+3*n}function m(e,t,n,r,i,a,o,s,c){null==c&&(c=1),c=c>1?1:0>c?0:c;for(var u=c/2,l=12,p=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],f=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],h=0,m=0;l>m;m++){var y=u*p[m]+u,g=d(y,e,n,i,o),v=d(y,t,r,a,s),x=g*g+v*v;h+=f[m]*G.sqrt(x)}return u*h}function y(e,t,n,r,i,a,o,s,c){if(!(0>c||m(e,t,n,r,i,a,o,s)<c)){var u,l=1,p=l/2,f=l-p,h=.01;for(u=m(e,t,n,r,i,a,o,s,f);q(u-c)>h;)p/=2,f+=(c>u?1:-1)*p,u=m(e,t,n,r,i,a,o,s,f);return f}}function g(e,t,n,r,i,a,o,s){if(!(z(e,n)<W(i,o)||W(e,n)>z(i,o)||z(t,r)<W(a,s)||W(t,r)>z(a,s))){var c=(e*r-t*n)*(i-o)-(e-n)*(i*s-a*o),u=(e*r-t*n)*(a-s)-(t-r)*(i*s-a*o),l=(e-n)*(a-s)-(t-r)*(i-o);if(l){var p=c/l,f=u/l,h=+p.toFixed(2),d=+f.toFixed(2);if(!(h<+W(e,n).toFixed(2)||h>+z(e,n).toFixed(2)||h<+W(i,o).toFixed(2)||h>+z(i,o).toFixed(2)||d<+W(t,r).toFixed(2)||d>+z(t,r).toFixed(2)||d<+W(a,s).toFixed(2)||d>+z(a,s).toFixed(2)))return{x:p,y:f}}}}function v(e,t,n){var r=p(e),i=p(t);if(!h(r,i))return n?0:[];for(var a=m.apply(0,e),o=m.apply(0,t),s=~~(a/8),c=~~(o/8),u=[],f=[],d={},y=n?0:[],v=0;s+1>v;v++){var x=l.apply(0,e.concat(v/s));u.push({x:x.x,y:x.y,t:v/s})}for(v=0;c+1>v;v++)x=l.apply(0,t.concat(v/c)),f.push({x:x.x,y:x.y,t:v/c});for(v=0;s>v;v++)for(var b=0;c>b;b++){var E=u[v],w=u[v+1],A=f[b],S=f[b+1],_=q(w.x-E.x)<.001?"y":"x",T=q(S.x-A.x)<.001?"y":"x",C=g(E.x,E.y,w.x,w.y,A.x,A.y,S.x,S.y);if(C){if(d[C.x.toFixed(4)]==C.y.toFixed(4))continue;d[C.x.toFixed(4)]=C.y.toFixed(4);var R=E.t+q((C[_]-E[_])/(w[_]-E[_]))*(w.t-E.t),N=A.t+q((C[T]-A[T])/(S[T]-A[T]))*(S.t-A.t);R>=0&&1>=R&&N>=0&&1>=N&&(n?y++:y.push({x:C.x,y:C.y,t1:R,t2:N}))}}return y}function x(e,t){return E(e,t)}function b(e,t){return E(e,t,1)}function E(e,t,n){e=P(e),t=P(t);for(var r,i,a,o,s,c,u,l,p,f,h=n?0:[],d=0,m=e.length;m>d;d++){var y=e[d];if("M"==y[0])r=s=y[1],i=c=y[2];else{"C"==y[0]?(p=[r,i].concat(y.slice(1)),r=p[6],i=p[7]):(p=[r,i,r,i,s,c,s,c],r=s,i=c);for(var g=0,x=t.length;x>g;g++){var b=t[g];if("M"==b[0])a=u=b[1],o=l=b[2];else{"C"==b[0]?(f=[a,o].concat(b.slice(1)),a=f[6],o=f[7]):(f=[a,o,a,o,u,l,u,l],a=u,o=l);var E=v(p,f,n);if(n)h+=E;else{for(var w=0,A=E.length;A>w;w++)E[w].segment1=d,E[w].segment2=g,E[w].bez1=p,E[w].bez2=f;h=h.concat(E)}}}}}return h}function w(e,t,n){var r=A(e);return f(r,t,n)&&E(e,[["M",t,n],["H",r.x2+10]],1)%2==1}function A(e){var t=i(e);if(t.bbox)return L(t.bbox);if(!e)return a();e=P(e);for(var n,r=0,o=0,s=[],c=[],u=0,l=e.length;l>u;u++)if(n=e[u],"M"==n[0])r=n[1],o=n[2],s.push(r),c.push(o);else{var p=D(r,o,n[1],n[2],n[3],n[4],n[5],n[6]);s=s.concat(p.min.x,p.max.x),c=c.concat(p.min.y,p.max.y),r=n[5],o=n[6]}var f=W.apply(0,s),h=W.apply(0,c),d=z.apply(0,s),m=z.apply(0,c),y=a(f,h,d-f,m-h);return t.bbox=L(y),y}function S(e,t,n,r,i){if(i)return[["M",+e+ +i,t],["l",n-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-n,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]];var a=[["M",e,t],["l",n,0],["l",0,r],["l",-n,0],["z"]];return a.toString=o,a}function _(e,t,n,r,i){if(null==i&&null==r&&(r=n),e=+e,t=+t,n=+n,r=+r,null!=i)var a=Math.PI/180,s=e+n*Math.cos(-r*a),c=e+n*Math.cos(-i*a),u=t+n*Math.sin(-r*a),l=t+n*Math.sin(-i*a),p=[["M",s,u],["A",n,n,0,+(i-r>180),0,c,l]];else p=[["M",e,t],["m",0,-r],["a",n,r,0,1,1,0,2*r],["a",n,r,0,1,1,0,-2*r],["z"]];return p.toString=o,p}function T(t){var n=i(t),r=String.prototype.toLowerCase;if(n.rel)return s(n.rel);e.is(t,"array")&&e.is(t&&t[0],"array")||(t=e.parsePathString(t));var a=[],c=0,u=0,l=0,p=0,f=0;"M"==t[0][0]&&(c=t[0][1],u=t[0][2],l=c,p=u,f++,a.push(["M",c,u]));for(var h=f,d=t.length;d>h;h++){var m=a[h]=[],y=t[h];if(y[0]!=r.call(y[0]))switch(m[0]=r.call(y[0]),m[0]){case"a":m[1]=y[1],m[2]=y[2],m[3]=y[3],m[4]=y[4],m[5]=y[5],m[6]=+(y[6]-c).toFixed(3),m[7]=+(y[7]-u).toFixed(3);break;case"v":m[1]=+(y[1]-u).toFixed(3);break;case"m":l=y[1],p=y[2];default:for(var g=1,v=y.length;v>g;g++)m[g]=+(y[g]-(g%2?c:u)).toFixed(3)}else{m=a[h]=[],"m"==y[0]&&(l=y[1]+c,p=y[2]+u);for(var x=0,b=y.length;b>x;x++)a[h][x]=y[x]}var E=a[h].length;switch(a[h][0]){case"z":c=l,u=p;break;case"h":c+=+a[h][E-1];break;case"v":u+=+a[h][E-1];break;default:c+=+a[h][E-2],u+=+a[h][E-1]}}return a.toString=o,n.rel=s(a),a}function C(t){var n=i(t);if(n.abs)return s(n.abs);if(I(t,"array")&&I(t&&t[0],"array")||(t=e.parsePathString(t)),!t||!t.length)return[["M",0,0]];var r,a=[],c=0,u=0,l=0,p=0,f=0;"M"==t[0][0]&&(c=+t[0][1],u=+t[0][2],l=c,p=u,f++,a[0]=["M",c,u]);for(var h,d,m=3==t.length&&"M"==t[0][0]&&"R"==t[1][0].toUpperCase()&&"Z"==t[2][0].toUpperCase(),y=f,g=t.length;g>y;y++){if(a.push(h=[]),d=t[y],r=d[0],r!=r.toUpperCase())switch(h[0]=r.toUpperCase(),h[0]){case"A":h[1]=d[1],h[2]=d[2],h[3]=d[3],h[4]=d[4],h[5]=d[5],h[6]=+d[6]+c,h[7]=+d[7]+u;break;case"V":h[1]=+d[1]+u;break;case"H":h[1]=+d[1]+c;break;case"R":for(var v=[c,u].concat(d.slice(1)),x=2,b=v.length;b>x;x++)v[x]=+v[x]+c,v[++x]=+v[x]+u;a.pop(),a=a.concat(O(v,m));break;case"O":a.pop(),v=_(c,u,d[1],d[2]),v.push(v[0]),a=a.concat(v);break;case"U":a.pop(),a=a.concat(_(c,u,d[1],d[2],d[3])),h=["U"].concat(a[a.length-1].slice(-2));break;case"M":l=+d[1]+c,p=+d[2]+u;default:for(x=1,b=d.length;b>x;x++)h[x]=+d[x]+(x%2?c:u)}else if("R"==r)v=[c,u].concat(d.slice(1)),a.pop(),a=a.concat(O(v,m)),h=["R"].concat(d.slice(-2));else if("O"==r)a.pop(),v=_(c,u,d[1],d[2]),v.push(v[0]),a=a.concat(v);else if("U"==r)a.pop(),a=a.concat(_(c,u,d[1],d[2],d[3])),h=["U"].concat(a[a.length-1].slice(-2));else for(var E=0,w=d.length;w>E;E++)h[E]=d[E];if(r=r.toUpperCase(),"O"!=r)switch(h[0]){case"Z":c=+l,u=+p;break;case"H":c=h[1];break;case"V":u=h[1];break;case"M":l=h[h.length-2],p=h[h.length-1];default:c=h[h.length-2],u=h[h.length-1]}}return a.toString=o,n.abs=s(a),a}function R(e,t,n,r){return[e,t,n,r,n,r]}function N(e,t,n,r,i,a){var o=1/3,s=2/3;return[o*e+s*n,o*t+s*r,o*i+s*n,o*a+s*r,i,a]}function M(t,n,r,i,a,o,s,c,u,l){var p,f=120*U/180,h=U/180*(+a||0),d=[],m=e._.cacher(function(e,t,n){var r=e*G.cos(n)-t*G.sin(n),i=e*G.sin(n)+t*G.cos(n);return{x:r,y:i}});if(l)S=l[0],_=l[1],w=l[2],A=l[3];else{p=m(t,n,-h),t=p.x,n=p.y,p=m(c,u,-h),c=p.x,u=p.y;var y=(G.cos(U/180*a),G.sin(U/180*a),(t-c)/2),g=(n-u)/2,v=y*y/(r*r)+g*g/(i*i);v>1&&(v=G.sqrt(v),r=v*r,i=v*i);var x=r*r,b=i*i,E=(o==s?-1:1)*G.sqrt(q((x*b-x*g*g-b*y*y)/(x*g*g+b*y*y))),w=E*r*g/i+(t+c)/2,A=E*-i*y/r+(n+u)/2,S=G.asin(((n-A)/i).toFixed(9)),_=G.asin(((u-A)/i).toFixed(9));S=w>t?U-S:S,_=w>c?U-_:_,0>S&&(S=2*U+S),0>_&&(_=2*U+_),s&&S>_&&(S-=2*U),!s&&_>S&&(_-=2*U)}var T=_-S;if(q(T)>f){var C=_,R=c,N=u;_=S+f*(s&&_>S?1:-1),c=w+r*G.cos(_),u=A+i*G.sin(_),d=M(c,u,r,i,a,0,s,R,N,[_,C,w,A])}T=_-S;var D=G.cos(S),P=G.sin(S),k=G.cos(_),O=G.sin(_),B=G.tan(T/4),I=4/3*r*B,L=4/3*i*B,F=[t,n],j=[t+I*P,n-L*D],V=[c+I*O,u-L*k],W=[c,u];if(j[0]=2*F[0]-j[0],j[1]=2*F[1]-j[1],l)return[j,V,W].concat(d);d=[j,V,W].concat(d).join().split(",");for(var z=[],$=0,H=d.length;H>$;$++)z[$]=$%2?m(d[$-1],d[$],h).y:m(d[$],d[$+1],h).x;return z}function D(e,t,n,r,i,a,o,s){for(var c,u,l,p,f,h,d,m,y=[],g=[[],[]],v=0;2>v;++v)if(0==v?(u=6*e-12*n+6*i,c=-3*e+9*n-9*i+3*o,l=3*n-3*e):(u=6*t-12*r+6*a,c=-3*t+9*r-9*a+3*s,l=3*r-3*t),q(c)<1e-12){if(q(u)<1e-12)continue;p=-l/u,p>0&&1>p&&y.push(p)}else d=u*u-4*l*c,m=G.sqrt(d),0>d||(f=(-u+m)/(2*c),f>0&&1>f&&y.push(f),h=(-u-m)/(2*c),h>0&&1>h&&y.push(h));for(var x,b=y.length,E=b;b--;)p=y[b],x=1-p,g[0][b]=x*x*x*e+3*x*x*p*n+3*x*p*p*i+p*p*p*o,g[1][b]=x*x*x*t+3*x*x*p*r+3*x*p*p*a+p*p*p*s;return g[0][E]=e,g[1][E]=t,g[0][E+1]=o,g[1][E+1]=s,g[0].length=g[1].length=E+2,{min:{x:W.apply(0,g[0]),y:W.apply(0,g[1])},max:{x:z.apply(0,g[0]),y:z.apply(0,g[1])}}}function P(e,t){var n=!t&&i(e);if(!t&&n.curve)return s(n.curve);for(var r=C(e),a=t&&C(t),o={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},c={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},u=(function(e,t,n){var r,i;if(!e)return["C",t.x,t.y,t.x,t.y,t.x,t.y];switch(!(e[0]in{T:1,Q:1})&&(t.qx=t.qy=null),e[0]){case"M":t.X=e[1],t.Y=e[2];break;case"A":e=["C"].concat(M.apply(0,[t.x,t.y].concat(e.slice(1))));break;case"S":"C"==n||"S"==n?(r=2*t.x-t.bx,i=2*t.y-t.by):(r=t.x,i=t.y),e=["C",r,i].concat(e.slice(1));break;case"T":"Q"==n||"T"==n?(t.qx=2*t.x-t.qx,t.qy=2*t.y-t.qy):(t.qx=t.x,t.qy=t.y),e=["C"].concat(N(t.x,t.y,t.qx,t.qy,e[1],e[2]));break;case"Q":t.qx=e[1],t.qy=e[2],e=["C"].concat(N(t.x,t.y,e[1],e[2],e[3],e[4]));break;case"L":e=["C"].concat(R(t.x,t.y,e[1],e[2]));break;case"H":e=["C"].concat(R(t.x,t.y,e[1],t.y));break;case"V":e=["C"].concat(R(t.x,t.y,t.x,e[1]));break;case"Z":e=["C"].concat(R(t.x,t.y,t.X,t.Y))}return e}),l=function(e,t){if(e[t].length>7){e[t].shift();for(var n=e[t];n.length;)f[t]="A",a&&(h[t]="A"),e.splice(t++,0,["C"].concat(n.splice(0,6)));e.splice(t,1),g=z(r.length,a&&a.length||0)}},p=function(e,t,n,i,o){e&&t&&"M"==e[o][0]&&"M"!=t[o][0]&&(t.splice(o,0,["M",i.x,i.y]),n.bx=0,n.by=0,n.x=e[o][1],n.y=e[o][2],g=z(r.length,a&&a.length||0))},f=[],h=[],d="",m="",y=0,g=z(r.length,a&&a.length||0);g>y;y++){r[y]&&(d=r[y][0]),"C"!=d&&(f[y]=d,y&&(m=f[y-1])),r[y]=u(r[y],o,m),"A"!=f[y]&&"C"==d&&(f[y]="C"),l(r,y),a&&(a[y]&&(d=a[y][0]),"C"!=d&&(h[y]=d,y&&(m=h[y-1])),a[y]=u(a[y],c,m),"A"!=h[y]&&"C"==d&&(h[y]="C"),l(a,y)),p(r,a,o,c,y),p(a,r,c,o,y);var v=r[y],x=a&&a[y],b=v.length,E=a&&x.length;o.x=v[b-2],o.y=v[b-1],o.bx=V(v[b-4])||o.x,o.by=V(v[b-3])||o.y,c.bx=a&&(V(x[E-4])||c.x),c.by=a&&(V(x[E-3])||c.y),c.x=a&&x[E-2],c.y=a&&x[E-1]}return a||(n.curve=s(r)),a?[r,a]:r}function k(e,t){if(!t)return e;var n,r,i,a,o,s,c;for(e=P(e),i=0,o=e.length;o>i;i++)for(c=e[i],a=1,s=c.length;s>a;a+=2)n=t.x(c[a],c[a+1]),r=t.y(c[a],c[a+1]),c[a]=n,c[a+1]=r;return e}function O(e,t){for(var n=[],r=0,i=e.length;i-2*!t>r;r+=2){var a=[{x:+e[r-2],y:+e[r-1]},{x:+e[r],y:+e[r+1]},{x:+e[r+2],y:+e[r+3]},{x:+e[r+4],y:+e[r+5]}];t?r?i-4==r?a[3]={x:+e[0],y:+e[1]}:i-2==r&&(a[2]={x:+e[0],y:+e[1]},a[3]={x:+e[2],y:+e[3]}):a[0]={x:+e[i-2],y:+e[i-1]}:i-4==r?a[3]=a[2]:r||(a[0]={x:+e[r],y:+e[r+1]}),n.push(["C",(-a[0].x+6*a[1].x+a[2].x)/6,(-a[0].y+6*a[1].y+a[2].y)/6,(a[1].x+6*a[2].x-a[3].x)/6,(a[1].y+6*a[2].y-a[3].y)/6,a[2].x,a[2].y])}return n}var B=t.prototype,I=e.is,L=e._.clone,F="hasOwnProperty",j=/,?([a-z]),?/gi,V=parseFloat,G=Math,U=G.PI,W=G.min,z=G.max,$=G.pow,q=G.abs,H=u(1),Y=u(),K=u(0,1),X=e._unit2px,Q={path:function(e){return e.attr("path")},circle:function(e){var t=X(e);return _(t.cx,t.cy,t.r)},ellipse:function(e){var t=X(e);return _(t.cx||0,t.cy||0,t.rx,t.ry)},rect:function(e){var t=X(e);return S(t.x||0,t.y||0,t.width,t.height,t.rx,t.ry)},image:function(e){var t=X(e);return S(t.x||0,t.y||0,t.width,t.height)},line:function(e){return"M"+[e.attr("x1")||0,e.attr("y1")||0,e.attr("x2"),e.attr("y2")]},polyline:function(e){return"M"+e.attr("points")},polygon:function(e){return"M"+e.attr("points")+"z"},deflt:function(e){var t=e.node.getBBox();return S(t.x,t.y,t.width,t.height)}};e.path=i,e.path.getTotalLength=H,e.path.getPointAtLength=Y,e.path.getSubpath=function(e,t,n){if(this.getTotalLength(e)-n<1e-6)return K(e,t).end;var r=K(e,n,1);return t?K(r,t).end:r},B.getTotalLength=function(){return this.node.getTotalLength?this.node.getTotalLength():void 0},B.getPointAtLength=function(e){return Y(this.attr("d"),e)},B.getSubpath=function(t,n){return e.path.getSubpath(this.attr("d"),t,n)},e._.box=a,e.path.findDotsAtSegment=l,e.path.bezierBBox=p,e.path.isPointInsideBBox=f,e.path.isBBoxIntersect=h,e.path.intersection=x,e.path.intersectionNumber=b,e.path.isPointInside=w,e.path.getBBox=A,e.path.get=Q,e.path.toRelative=T,e.path.toAbsolute=C,e.path.toCubic=P,e.path.map=k,e.path.toString=o,e.path.clone=s}),r.plugin(function(e,n,r,i){for(var a=n.prototype,o="hasOwnProperty",s=("createTouch"in i.doc),c=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","touchstart","touchmove","touchend","touchcancel"],u={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},l=(function(e,t){var n="y"==e?"scrollTop":"scrollLeft",r=t&&t.node?t.node.ownerDocument:i.doc;return r[n in r.documentElement?"documentElement":"body"][n]}),p=function(){this.returnValue=!1},f=function(){return this.originalEvent.preventDefault()},h=function(){this.cancelBubble=!0},d=function(){return this.originalEvent.stopPropagation()},m=function(){return i.doc.addEventListener?function(e,t,n,r){var i=s&&u[t]?u[t]:t,a=function(i){var a=l("y",r),c=l("x",r);if(s&&u[o](t))for(var p=0,h=i.targetTouches&&i.targetTouches.length;h>p;p++)if(i.targetTouches[p].target==e||e.contains(i.targetTouches[p].target)){var m=i;i=i.targetTouches[p],i.originalEvent=m,i.preventDefault=f,i.stopPropagation=d;break}var y=i.clientX+c,g=i.clientY+a;return n.call(r,i,y,g)};return t!==i&&e.addEventListener(t,a,!1),e.addEventListener(i,a,!1),function(){return t!==i&&e.removeEventListener(t,a,!1),e.removeEventListener(i,a,!1),!0}}:i.doc.attachEvent?function(e,t,n,r){var i=function(e){e=e||r.node.ownerDocument.window.event;var t=l("y",r),i=l("x",r),a=e.clientX+i,o=e.clientY+t;return e.preventDefault=e.preventDefault||p,e.stopPropagation=e.stopPropagation||h,n.call(r,e,a,o)};e.attachEvent("on"+t,i);var a=function(){return e.detachEvent("on"+t,i),!0};return a}:void 0}(),y=[],g=function(e){for(var n,r=e.clientX,i=e.clientY,a=l("y"),o=l("x"),c=y.length;c--;){if(n=y[c],s){for(var u,p=e.touches&&e.touches.length;p--;)if(u=e.touches[p],u.identifier==n.el._drag.id||n.el.node.contains(u.target)){r=u.clientX,i=u.clientY,(e.originalEvent?e.originalEvent:e).preventDefault();break}}else e.preventDefault();var f=n.el.node;f.nextSibling,f.parentNode,f.style.display;r+=o,i+=a,t("snap.drag.move."+n.el.id,n.move_scope||n.el,r-n.el._drag.x,i-n.el._drag.y,r,i,e)}},v=function(n){e.unmousemove(g).unmouseup(v);for(var r,i=y.length;i--;)r=y[i],r.el._drag={},t("snap.drag.end."+r.el.id,r.end_scope||r.start_scope||r.move_scope||r.el,n);y=[]},x=c.length;x--;)!function(t){e[t]=a[t]=function(n,r){return e.is(n,"function")&&(this.events=this.events||[],this.events.push({name:t,f:n,unbind:m(this.node||document,t,n,r||this)})),this},e["un"+t]=a["un"+t]=function(e){for(var n=this.events||[],r=n.length;r--;)if(n[r].name==t&&(n[r].f==e||!e))return n[r].unbind(),n.splice(r,1),!n.length&&delete this.events,this;return this}}(c[x]);a.hover=function(e,t,n,r){return this.mouseover(e,n).mouseout(t,r||n)},a.unhover=function(e,t){return this.unmouseover(e).unmouseout(t)};var b=[];a.drag=function(n,r,i,a,o,s){function c(c,u,l){(c.originalEvent||c).preventDefault(),this._drag.x=u,this._drag.y=l,this._drag.id=c.identifier,!y.length&&e.mousemove(g).mouseup(v),y.push({el:this,move_scope:a,start_scope:o,end_scope:s}),r&&t.on("snap.drag.start."+this.id,r),n&&t.on("snap.drag.move."+this.id,n),i&&t.on("snap.drag.end."+this.id,i),t("snap.drag.start."+this.id,o||a||this,u,l,c)}if(!arguments.length){var u;return this.drag(function(e,t){this.attr({transform:u+(u?"T":"t")+[e,t]})},function(){u=this.transform().local})}return this._drag={},b.push({el:this,start:c}),this.mousedown(c),this},a.undrag=function(){for(var n=b.length;n--;)b[n].el==this&&(this.unmousedown(b[n].start),b.splice(n,1),t.unbind("snap.drag.*."+this.id));return!b.length&&e.unmousemove(g).unmouseup(v),this}}),r.plugin(function(e,n,r,i){var a=(n.prototype,r.prototype),o=/^\s*url\((.+)\)/,s=String,c=e._.$;e.filter={},a.filter=function(t){var r=this;"svg"!=r.type&&(r=r.paper);var i=e.parse(s(t)),a=e._.id(),o=(r.node.offsetWidth,r.node.offsetHeight,c("filter"));return c(o,{id:a,filterUnits:"userSpaceOnUse"}),o.appendChild(i.node),r.defs.appendChild(o),new n(o)},t.on("snap.util.getattr.filter",function(){t.stop();var n=c(this.node,"filter");if(n){var r=s(n).match(o);return r&&e.select(r[1])}}),t.on("snap.util.attr.filter",function(r){if(r instanceof n&&"filter"==r.type){t.stop();var i=r.node.id;i||(c(r.node,{id:r.id}),i=r.id),c(this.node,{filter:e.url(i)})}r&&"none"!=r||(t.stop(),this.node.removeAttribute("filter"))}),e.filter.blur=function(t,n){null==t&&(t=2);var r=null==n?t:[t,n];return e.format('<feGaussianBlur stdDeviation="{def}"/>',{def:r})},e.filter.blur.toString=function(){return this()},e.filter.shadow=function(t,n,r,i,a){return"string"==typeof r&&(i=r,a=i,r=4),"string"!=typeof i&&(a=i,i="#000"),i=i||"#000",null==r&&(r=4),null==a&&(a=1),null==t&&(t=0,n=2),null==n&&(n=t),i=e.color(i),e.format('<feGaussianBlur in="SourceAlpha" stdDeviation="{blur}"/><feOffset dx="{dx}" dy="{dy}" result="offsetblur"/><feFlood flood-color="{color}"/><feComposite in2="offsetblur" operator="in"/><feComponentTransfer><feFuncA type="linear" slope="{opacity}"/></feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge>',{color:i,dx:t,dy:n,blur:r,opacity:a})},e.filter.shadow.toString=function(){return this()},e.filter.grayscale=function(t){return null==t&&(t=1),e.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {b} {h} 0 0 0 0 0 1 0"/>',{a:.2126+.7874*(1-t),b:.7152-.7152*(1-t),c:.0722-.0722*(1-t),d:.2126-.2126*(1-t),e:.7152+.2848*(1-t),f:.0722-.0722*(1-t),g:.2126-.2126*(1-t),h:.0722+.9278*(1-t)})},e.filter.grayscale.toString=function(){return this()},e.filter.sepia=function(t){return null==t&&(t=1),e.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {h} {i} 0 0 0 0 0 1 0"/>',{a:.393+.607*(1-t),b:.769-.769*(1-t),c:.189-.189*(1-t),d:.349-.349*(1-t),e:.686+.314*(1-t),f:.168-.168*(1-t),g:.272-.272*(1-t),h:.534-.534*(1-t),i:.131+.869*(1-t)})},e.filter.sepia.toString=function(){return this()},e.filter.saturate=function(t){return null==t&&(t=1),e.format('<feColorMatrix type="saturate" values="{amount}"/>',{amount:1-t})},e.filter.saturate.toString=function(){return this()},e.filter.hueRotate=function(t){return t=t||0,e.format('<feColorMatrix type="hueRotate" values="{angle}"/>',{angle:t})},e.filter.hueRotate.toString=function(){return this()},e.filter.invert=function(t){return null==t&&(t=1),e.format('<feComponentTransfer><feFuncR type="table" tableValues="{amount} {amount2}"/><feFuncG type="table" tableValues="{amount} {amount2}"/><feFuncB type="table" tableValues="{amount} {amount2}"/></feComponentTransfer>',{amount:t,amount2:1-t})},e.filter.invert.toString=function(){return this()},e.filter.brightness=function(t){return null==t&&(t=1),e.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}"/><feFuncG type="linear" slope="{amount}"/><feFuncB type="linear" slope="{amount}"/></feComponentTransfer>',{amount:t})},e.filter.brightness.toString=function(){return this()},e.filter.contrast=function(t){return null==t&&(t=1),e.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}" intercept="{amount2}"/><feFuncG type="linear" slope="{amount}" intercept="{amount2}"/><feFuncB type="linear" slope="{amount}" intercept="{amount2}"/></feComponentTransfer>',{amount:t,amount2:.5-t/2})},e.filter.contrast.toString=function(){return this()}}),r})},{65:65}],67:[function(e,t,n){"use strict";var r=t.exports=e(66);r.plugin(function(e,t){t.prototype.children=function(){for(var t=[],n=this.node.childNodes,r=0,i=n.length;i>r;r++)t[r]=new e(n[r]);return t}}),r.plugin(function(e,t,n,r){function i(e){return e.split(/\s+/)}function a(e){return e.join(" ")}function o(e){return i(e.attr("class")||"")}function s(e,t){e.attr("class",a(t))}t.prototype.addClass=function(e){var t,n,r=o(this),a=i(e);for(t=0,n;n=a[t];t++)-1===r.indexOf(n)&&r.push(n);return s(this,r),this},t.prototype.hasClass=function(e){if(!e)throw new Error("[snapsvg] syntax: hasClass(clsStr)");return-1!==o(this).indexOf(e)},t.prototype.removeClass=function(e){var t,n,r,a=o(this),c=i(e);for(t=0,n;n=c[t];t++)r=a.indexOf(n),-1!==r&&a.splice(r,1);return s(this,a),this}}),r.plugin(function(e,t,n,r){t.prototype.translate=function(t,n){var r=new e.Matrix;return r.translate(t,n),this.transform(r)}}),r.plugin(function(e){e.create=function(t,n){return e._.wrap(e._.$(t,n))}}),r.plugin(function(e,t,n,r){e.createSnapAt=function(t,n,r){var i=document.createElementNS("http://www.w3.org/2000/svg","svg");return i.setAttribute("width",t),i.setAttribute("height",n),r||(r=document.body),r.appendChild(i),new e(i)}})},{66:66}],68:[function(e,t,n){var r=function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=function(){var e=Array.prototype.slice.call(arguments);1===e.length&&r(e[0])&&(e=e[0]);var t=e.pop();return t.$inject=e,t},a=/^function\s*[^\(]*\(\s*([^\)]*)\)/m,o=/\/\*([^\*]*)\*\//m,s=function(e){if("function"!=typeof e)throw new Error('Cannot annotate "'+e+'". Expected a function!');var t=e.toString().match(a);return t[1]&&t[1].split(",").map(function(e){return t=e.match(o),t?t[1].trim():e.trim()})||[]};n.annotate=i,n.parse=s,n.isArray=r},{}],69:[function(e,t,n){t.exports={annotate:e(68).annotate,Module:e(71),Injector:e(70)}},{68:68,70:70,71:71}],70:[function(e,t,n){var r=e(71),i=e(68).parse,a=e(68).annotate,o=e(68).isArray,s=function(e,t){t=t||{get:function(e){throw n.push(e),p('No provider for "'+e+'"!')}};var n=[],c=this._providers=Object.create(t._providers||null),u=this._instances=Object.create(null),l=u.injector=this,p=function(e){var t=n.join(" -> ");return n.length=0,new Error(t?e+" (Resolving: "+t+")":e)},f=function(e){if(!c[e]&&-1!==e.indexOf(".")){for(var r=e.split("."),i=f(r.shift());r.length;)i=i[r.shift()];return i}if(Object.hasOwnProperty.call(u,e))return u[e];if(Object.hasOwnProperty.call(c,e)){if(-1!==n.indexOf(e))throw n.push(e),p("Cannot resolve circular dependency!");return n.push(e),u[e]=c[e][0](c[e][1]),n.pop(),u[e]}return t.get(e)},h=function(e){var t=Object.create(e.prototype),n=d(e,t);return"object"==typeof n?n:t},d=function(e,t){if("function"!=typeof e){if(!o(e))throw new Error('Cannot invoke "'+e+'". Expected a function!');e=a(e.slice())}var n=e.$inject&&e.$inject||i(e),r=n.map(function(e){return f(e)});return e.apply(t,r)},m=function(e){return a(function(t){return e.get(t)})},y=function(e,t){if(t&&t.length){var n,r,i,a,o=Object.create(null),u=Object.create(null),p=[],f=[],h=[];for(var d in c)n=c[d],-1!==t.indexOf(d)&&("private"===n[2]?(r=p.indexOf(n[3]),-1===r?(i=n[3].createChild([],t),a=m(i),p.push(n[3]),f.push(i),h.push(a),o[d]=[a,d,"private",i]):o[d]=[h[r],d,"private",f[r]]):o[d]=[n[2],n[1]],u[d]=!0),"factory"!==n[2]&&"type"!==n[2]||!n[1].$scope||t.forEach(function(e){-1!==n[1].$scope.indexOf(e)&&(o[d]=[n[2],n[1]],u[e]=!0)});t.forEach(function(e){if(!u[e])throw new Error('No provider for "'+e+'". Cannot use provider from the parent!')}),e.unshift(o)}return new s(e,l)},g={factory:d,type:h,value:function(e){return e}};e.forEach(function(e){function t(e,t){return"value"!==e&&o(t)&&(t=a(t.slice())),t}if(e instanceof r)e.forEach(function(e){var n=e[0],r=e[1],i=e[2];c[n]=[g[r],t(r,i),r]});else if("object"==typeof e)if(e.__exports__){var n=Object.keys(e).reduce(function(t,n){return"__"!==n.substring(0,2)&&(t[n]=e[n]),t},Object.create(null)),i=new s((e.__modules__||[]).concat([n]),l),u=a(function(e){return i.get(e)});e.__exports__.forEach(function(e){c[e]=[u,e,"private",i]})}else Object.keys(e).forEach(function(n){if("private"===e[n][2])return void(c[n]=e[n]);var r=e[n][0],i=e[n][1];c[n]=[g[r],t(r,i),r]})}),this.get=f,this.invoke=d,this.instantiate=h,this.createChild=y};t.exports=s},{68:68,71:71}],71:[function(e,t,n){var r=function(){var e=[];this.factory=function(t,n){return e.push([t,"factory",n]),this},this.value=function(t,n){return e.push([t,"value",n]),this},this.type=function(t,n){return e.push([t,"type",n]),this},this.forEach=function(t){e.forEach(t)}};t.exports=r},{}],72:[function(e,t,n){"function"==typeof Object.create?t.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},{}],73:[function(e,t,n){function r(e){var t=e?e.length:0;return t?e[t-1]:void 0}t.exports=r},{}],74:[function(e,t,n){function r(e,t,n){var r=s(e)?i:o;return n&&c(e,t,n)&&(t=void 0),("function"!=typeof t||void 0!==n)&&(t=a(t,n,3)),r(e,t)}var i=e(90),a=e(98),o=e(103),s=e(159),c=e(148);t.exports=r},{103:103,148:148,159:159,90:90,98:98}],75:[function(e,t,n){function r(e,t,n){var r=s(e)?i:o;return t=a(t,n,3),r(e,t)}var i=e(91),a=e(98),o=e(104),s=e(159);t.exports=r},{104:104,159:159,91:91,98:98}],76:[function(e,t,n){var r=e(102),i=e(136),a=i(r);t.exports=a},{102:102,136:136}],77:[function(e,t,n){var r=e(89),i=e(102),a=e(137),o=a(r,i);t.exports=o},{102:102,137:137,89:89}],78:[function(e,t,n){var r=e(131),i=Object.prototype,a=i.hasOwnProperty,o=r(function(e,t,n){a.call(e,n)?e[n].push(t):e[n]=[t]});t.exports=o},{131:131}],79:[function(e,t,n){function r(e,t,n,r){var f=e?a(e):0;return c(f)||(e=l(e),f=e.length),n="number"!=typeof n||r&&s(t,n,r)?0:0>n?p(f+n,0):n||0,"string"==typeof e||!o(e)&&u(e)?f>=n&&e.indexOf(t,n)>-1:!!f&&i(e,t,n)>-1}var i=e(112),a=e(142),o=e(159),s=e(148),c=e(150),u=e(165),l=e(175),p=Math.max;t.exports=r},{112:112,142:142,148:148,150:150,159:159,165:165,175:175}],80:[function(e,t,n){function r(e,t,n){var r=s(e)?i:o;return t=a(t,n,3),r(e,t)}var i=e(92),a=e(98),o=e(116),s=e(159);t.exports=r},{116:116,159:159,92:92,98:98}],81:[function(e,t,n){var r=e(94),i=e(102),a=e(138),o=a(r,i);t.exports=o},{102:102,138:138,94:94}],82:[function(e,t,n){function r(e,t,n){var r=s(e)?i:o;return n&&c(e,t,n)&&(t=void 0),("function"!=typeof t||void 0!==n)&&(t=a(t,n,3)),r(e,t)}var i=e(95),a=e(98),o=e(125),s=e(159),c=e(148);t.exports=r},{125:125,148:148,159:159,95:95,98:98}],83:[function(e,t,n){var r=e(144),i=r(Date,"now"),a=i||function(){return(new Date).getTime()};t.exports=a},{144:144}],84:[function(e,t,n){function r(e,t,n){function r(){g&&clearTimeout(g),h&&clearTimeout(h),x=0,h=g=v=void 0}function c(t,n){n&&clearTimeout(n),h=g=v=void 0,t&&(x=a(),d=e.apply(y,f),g||h||(f=y=void 0))}function u(){var e=t-(a()-m);0>=e||e>t?c(v,h):g=setTimeout(u,e)}function l(){c(E,g)}function p(){if(f=arguments,m=a(),y=this,v=E&&(g||!w),b===!1)var n=w&&!g;else{h||w||(x=m);var r=b-(m-x),i=0>=r||r>b;i?(h&&(h=clearTimeout(h)),x=m,d=e.apply(y,f)):h||(h=setTimeout(l,r))}return i&&g?g=clearTimeout(g):g||t===b||(g=setTimeout(u,t)),n&&(i=!0,d=e.apply(y,f)),!i||g||h||(f=y=void 0),d}var f,h,d,m,y,g,v,x=0,b=!1,E=!0;if("function"!=typeof e)throw new TypeError(o);if(t=0>t?0:+t||0,n===!0){var w=!0;E=!1}else i(n)&&(w=!!n.leading,b="maxWait"in n&&s(+n.maxWait||0,t),E="trailing"in n?!!n.trailing:E);return p.cancel=r,p}var i=e(163),a=e(83),o="Expected a function",s=Math.max;t.exports=r},{163:163,83:83}],85:[function(e,t,n){var r=e(100),i=e(86),a=i(function(e,t){return r(e,1,t)});t.exports=a},{100:100,86:86}],86:[function(e,t,n){function r(e,t){if("function"!=typeof e)throw new TypeError(i);return t=a(void 0===t?e.length-1:+t||0,0),function(){for(var n=arguments,r=-1,i=a(n.length-t,0),o=Array(i);++r<i;)o[r]=n[t+r];switch(t){case 0:return e.call(this,o);case 1:return e.call(this,n[0],o);case 2:return e.call(this,n[0],n[1],o)}var s=Array(t+1);for(r=-1;++r<t;)s[r]=n[r];return s[t]=o,e.apply(this,s)}}var i="Expected a function",a=Math.max;t.exports=r},{}],87:[function(e,t,n){(function(n){function r(e){var t=e?e.length:0;for(this.data={hash:s(null),set:new o};t--;)this.push(e[t])}var i=e(130),a=e(144),o=a(n,"Set"),s=a(Object,"create");r.prototype.push=i,t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{130:130,144:144}],88:[function(e,t,n){function r(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}t.exports=r},{}],89:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length;++n<r&&t(e[n],n,e)!==!1;);return e}t.exports=r},{}],90:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}t.exports=r},{}],91:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length,i=-1,a=[];++n<r;){var o=e[n];t(o,n,e)&&(a[++i]=o)}return a}t.exports=r},{}],92:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}t.exports=r},{}],93:[function(e,t,n){function r(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}t.exports=r},{}],94:[function(e,t,n){function r(e,t,n,r){var i=-1,a=e.length;for(r&&a&&(n=e[++i]);++i<a;)n=t(n,e[i],i,e);return n}t.exports=r},{}],95:[function(e,t,n){function r(e,t){for(var n=-1,r=e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}t.exports=r},{}],96:[function(e,t,n){function r(e,t,n){for(var r=-1,a=i(t),o=a.length;++r<o;){var s=a[r],c=e[s],u=n(c,t[s],s,e,t);(u===u?u===c:c!==c)&&(void 0!==c||s in e)||(e[s]=u)}return e}var i=e(169);t.exports=r},{169:169}],97:[function(e,t,n){function r(e,t){return null==t?e:i(t,a(t),e)}var i=e(99),a=e(169);t.exports=r},{169:169,99:99}],98:[function(e,t,n){function r(e,t,n){var r=typeof e;return"function"==r?void 0===t?e:o(e,t,n):null==e?s:"object"==r?i(e):void 0===t?c(e):a(e,t)}var i=e(117),a=e(118),o=e(128),s=e(176),c=e(177);t.exports=r},{117:117,118:118,128:128,176:176,177:177}],99:[function(e,t,n){function r(e,t,n){n||(n={});for(var r=-1,i=t.length;++r<i;){var a=t[r];n[a]=e[a]}return n}t.exports=r},{}],100:[function(e,t,n){function r(e,t,n){if("function"!=typeof e)throw new TypeError(i);return setTimeout(function(){e.apply(void 0,n)},t)}var i="Expected a function";t.exports=r},{}],101:[function(e,t,n){function r(e,t){var n=e?e.length:0,r=[];if(!n)return r;var c=-1,u=i,l=!0,p=l&&t.length>=s?o(t):null,f=t.length;p&&(u=a,l=!1,t=p);e:for(;++c<n;){var h=e[c];if(l&&h===h){for(var d=f;d--;)if(t[d]===h)continue e;r.push(h)}else u(t,h,0)<0&&r.push(h)}return r}var i=e(112),a=e(129),o=e(135),s=200;t.exports=r},{112:112,129:129,135:135}],102:[function(e,t,n){var r=e(110),i=e(133),a=i(r);t.exports=a},{110:110,133:133}],103:[function(e,t,n){function r(e,t){var n=!0;return i(e,function(e,r,i){return n=!!t(e,r,i)}),n}var i=e(102);t.exports=r},{102:102}],104:[function(e,t,n){function r(e,t){var n=[];return i(e,function(e,r,i){t(e,r,i)&&n.push(e)}),n}var i=e(102);t.exports=r},{102:102}],105:[function(e,t,n){function r(e,t,n,r){var i;return n(e,function(e,n,a){return t(e,n,a)?(i=r?n:e,!1):void 0}),i}t.exports=r},{}],106:[function(e,t,n){function r(e,t,n){for(var r=e.length,i=n?r:-1;n?i--:++i<r;)if(t(e[i],i,e))return i;
return-1}t.exports=r},{}],107:[function(e,t,n){function r(e,t,n,u){u||(u=[]);for(var l=-1,p=e.length;++l<p;){var f=e[l];c(f)&&s(f)&&(n||o(f)||a(f))?t?r(f,t,n,u):i(u,f):n||(u[u.length]=f)}return u}var i=e(93),a=e(158),o=e(159),s=e(146),c=e(151);t.exports=r},{146:146,151:151,158:158,159:159,93:93}],108:[function(e,t,n){var r=e(134),i=r();t.exports=i},{134:134}],109:[function(e,t,n){function r(e,t){return i(e,t,a)}var i=e(108),a=e(170);t.exports=r},{108:108,170:170}],110:[function(e,t,n){function r(e,t){return i(e,t,a)}var i=e(108),a=e(169);t.exports=r},{108:108,169:169}],111:[function(e,t,n){function r(e,t,n){if(null!=e){void 0!==n&&n in i(e)&&(t=[n]);for(var r=0,a=t.length;null!=e&&a>r;)e=e[t[r++]];return r&&r==a?e:void 0}}var i=e(156);t.exports=r},{156:156}],112:[function(e,t,n){function r(e,t,n){if(t!==t)return i(e,n);for(var r=n-1,a=e.length;++r<a;)if(e[r]===t)return r;return-1}var i=e(145);t.exports=r},{145:145}],113:[function(e,t,n){function r(e,t,n,s,c,u){return e===t?!0:null==e||null==t||!a(e)&&!o(t)?e!==e&&t!==t:i(e,t,r,n,s,c,u)}var i=e(114),a=e(163),o=e(151);t.exports=r},{114:114,151:151,163:163}],114:[function(e,t,n){function r(e,t,n,r,f,m,y){var g=s(e),v=s(t),x=l,b=l;g||(x=d.call(e),x==u?x=p:x!=p&&(g=c(e))),v||(b=d.call(t),b==u?b=p:b!=p&&(v=c(t)));var E=x==p,w=b==p,A=x==b;if(A&&!g&&!E)return a(e,t,x);if(!f){var S=E&&h.call(e,"__wrapped__"),_=w&&h.call(t,"__wrapped__");if(S||_)return n(S?e.value():e,_?t.value():t,r,f,m,y)}if(!A)return!1;m||(m=[]),y||(y=[]);for(var T=m.length;T--;)if(m[T]==e)return y[T]==t;m.push(e),y.push(t);var C=(g?i:o)(e,t,n,r,f,m,y);return m.pop(),y.pop(),C}var i=e(139),a=e(140),o=e(141),s=e(159),c=e(166),u="[object Arguments]",l="[object Array]",p="[object Object]",f=Object.prototype,h=f.hasOwnProperty,d=f.toString;t.exports=r},{139:139,140:140,141:141,159:159,166:166}],115:[function(e,t,n){function r(e,t,n){var r=t.length,o=r,s=!n;if(null==e)return!o;for(e=a(e);r--;){var c=t[r];if(s&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++r<o;){c=t[r];var u=c[0],l=e[u],p=c[1];if(s&&c[2]){if(void 0===l&&!(u in e))return!1}else{var f=n?n(l,p,u):void 0;if(!(void 0===f?i(p,l,n,!0):f))return!1}}return!0}var i=e(113),a=e(156);t.exports=r},{113:113,156:156}],116:[function(e,t,n){function r(e,t){var n=-1,r=a(e)?Array(e.length):[];return i(e,function(e,i,a){r[++n]=t(e,i,a)}),r}var i=e(102),a=e(146);t.exports=r},{102:102,146:146}],117:[function(e,t,n){function r(e){var t=a(e);if(1==t.length&&t[0][2]){var n=t[0][0],r=t[0][1];return function(e){return null==e?!1:e[n]===r&&(void 0!==r||n in o(e))}}return function(e){return i(e,t)}}var i=e(115),a=e(143),o=e(156);t.exports=r},{115:115,143:143,156:156}],118:[function(e,t,n){function r(e,t){var n=s(e),r=c(e)&&u(t),h=e+"";return e=f(e),function(s){if(null==s)return!1;var c=h;if(s=p(s),(n||!r)&&!(c in s)){if(s=1==e.length?s:i(s,o(e,0,-1)),null==s)return!1;c=l(e),s=p(s)}return s[c]===t?void 0!==t||c in s:a(t,s[c],void 0,!0)}}var i=e(111),a=e(113),o=e(124),s=e(159),c=e(149),u=e(152),l=e(73),p=e(156),f=e(157);t.exports=r},{111:111,113:113,124:124,149:149,152:152,156:156,157:157,159:159,73:73}],119:[function(e,t,n){function r(e,t,n,f,h){if(!c(e))return e;var d=s(t)&&(o(t)||l(t)),m=d?void 0:p(t);return i(m||t,function(i,o){if(m&&(o=i,i=t[o]),u(i))f||(f=[]),h||(h=[]),a(e,t,o,r,n,f,h);else{var s=e[o],c=n?n(s,i,o,e,t):void 0,l=void 0===c;l&&(c=i),void 0===c&&(!d||o in e)||!l&&(c===c?c===s:s!==s)||(e[o]=c)}}),e}var i=e(89),a=e(120),o=e(159),s=e(146),c=e(163),u=e(151),l=e(166),p=e(169);t.exports=r},{120:120,146:146,151:151,159:159,163:163,166:166,169:169,89:89}],120:[function(e,t,n){function r(e,t,n,r,p,f,h){for(var d=f.length,m=t[n];d--;)if(f[d]==m)return void(e[n]=h[d]);var y=e[n],g=p?p(y,m,n,e,t):void 0,v=void 0===g;v&&(g=m,s(m)&&(o(m)||u(m))?g=o(y)?y:s(y)?i(y):[]:c(m)||a(m)?g=a(y)?l(y):c(y)?y:{}:v=!1),f.push(m),h.push(g),v?e[n]=r(g,m,p,f,h):(g===g?g!==y:y===y)&&(e[n]=g)}var i=e(88),a=e(158),o=e(159),s=e(146),c=e(164),u=e(166),l=e(167);t.exports=r},{146:146,158:158,159:159,164:164,166:166,167:167,88:88}],121:[function(e,t,n){function r(e){return function(t){return null==t?void 0:t[e]}}t.exports=r},{}],122:[function(e,t,n){function r(e){var t=e+"";return e=a(e),function(n){return i(n,e,t)}}var i=e(111),a=e(157);t.exports=r},{111:111,157:157}],123:[function(e,t,n){function r(e,t,n,r,i){return i(e,function(e,i,a){n=r?(r=!1,e):t(n,e,i,a)}),n}t.exports=r},{}],124:[function(e,t,n){function r(e,t,n){var r=-1,i=e.length;t=null==t?0:+t||0,0>t&&(t=-t>i?0:i+t),n=void 0===n||n>i?i:+n||0,0>n&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=Array(i);++r<i;)a[r]=e[r+t];return a}t.exports=r},{}],125:[function(e,t,n){function r(e,t){var n;return i(e,function(e,r,i){return n=t(e,r,i),!n}),!!n}var i=e(102);t.exports=r},{102:102}],126:[function(e,t,n){function r(e){return null==e?"":e+""}t.exports=r},{}],127:[function(e,t,n){function r(e,t){for(var n=-1,r=t.length,i=Array(r);++n<r;)i[n]=e[t[n]];return i}t.exports=r},{}],128:[function(e,t,n){function r(e,t,n){if("function"!=typeof e)return i;if(void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 3:return function(n,r,i){return e.call(t,n,r,i)};case 4:return function(n,r,i,a){return e.call(t,n,r,i,a)};case 5:return function(n,r,i,a,o){return e.call(t,n,r,i,a,o)}}return function(){return e.apply(t,arguments)}}var i=e(176);t.exports=r},{176:176}],129:[function(e,t,n){function r(e,t){var n=e.data,r="string"==typeof t||i(t)?n.set.has(t):n.hash[t];return r?0:-1}var i=e(163);t.exports=r},{163:163}],130:[function(e,t,n){function r(e){var t=this.data;"string"==typeof e||i(e)?t.set.add(e):t.hash[e]=!0}var i=e(163);t.exports=r},{163:163}],131:[function(e,t,n){function r(e,t){return function(n,r,s){var c=t?t():{};if(r=i(r,s,3),o(n))for(var u=-1,l=n.length;++u<l;){var p=n[u];e(c,p,r(p,u,n),n)}else a(n,function(t,n,i){e(c,t,r(t,n,i),i)});return c}}var i=e(98),a=e(102),o=e(159);t.exports=r},{102:102,159:159,98:98}],132:[function(e,t,n){function r(e){return o(function(t,n){var r=-1,o=null==t?0:n.length,s=o>2?n[o-2]:void 0,c=o>2?n[2]:void 0,u=o>1?n[o-1]:void 0;for("function"==typeof s?(s=i(s,u,5),o-=2):(s="function"==typeof u?u:void 0,o-=s?1:0),c&&a(n[0],n[1],c)&&(s=3>o?void 0:s,o=1);++r<o;){var l=n[r];l&&e(t,l,s)}return t})}var i=e(128),a=e(148),o=e(86);t.exports=r},{128:128,148:148,86:86}],133:[function(e,t,n){function r(e,t){return function(n,r){var s=n?i(n):0;if(!a(s))return e(n,r);for(var c=t?s:-1,u=o(n);(t?c--:++c<s)&&r(u[c],c,u)!==!1;);return n}}var i=e(142),a=e(150),o=e(156);t.exports=r},{142:142,150:150,156:156}],134:[function(e,t,n){function r(e){return function(t,n,r){for(var a=i(t),o=r(t),s=o.length,c=e?s:-1;e?c--:++c<s;){var u=o[c];if(n(a[u],u,a)===!1)break}return t}}var i=e(156);t.exports=r},{156:156}],135:[function(e,t,n){(function(n){function r(e){return s&&o?new i(e):null}var i=e(87),a=e(144),o=a(n,"Set"),s=a(Object,"create");t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{144:144,87:87}],136:[function(e,t,n){function r(e,t){return function(n,r,c){if(r=i(r,c,3),s(n)){var u=o(n,r,t);return u>-1?n[u]:void 0}return a(n,r,e)}}var i=e(98),a=e(105),o=e(106),s=e(159);t.exports=r},{105:105,106:106,159:159,98:98}],137:[function(e,t,n){function r(e,t){return function(n,r,o){return"function"==typeof r&&void 0===o&&a(n)?e(n,r):t(n,i(r,o,3))}}var i=e(128),a=e(159);t.exports=r},{128:128,159:159}],138:[function(e,t,n){function r(e,t){return function(n,r,s,c){var u=arguments.length<3;return"function"==typeof r&&void 0===c&&o(n)?e(n,r,s,u):a(n,i(r,c,4),s,u,t)}}var i=e(98),a=e(123),o=e(159);t.exports=r},{123:123,159:159,98:98}],139:[function(e,t,n){function r(e,t,n,r,a,o,s){var c=-1,u=e.length,l=t.length;if(u!=l&&!(a&&l>u))return!1;for(;++c<u;){var p=e[c],f=t[c],h=r?r(a?f:p,a?p:f,c):void 0;if(void 0!==h){if(h)continue;return!1}if(a){if(!i(t,function(e){return p===e||n(p,e,r,a,o,s)}))return!1}else if(p!==f&&!n(p,f,r,a,o,s))return!1}return!0}var i=e(95);t.exports=r},{95:95}],140:[function(e,t,n){function r(e,t,n){switch(n){case i:case a:return+e==+t;case o:return e.name==t.name&&e.message==t.message;case s:return e!=+e?t!=+t:e==+t;case c:case u:return e==t+""}return!1}var i="[object Boolean]",a="[object Date]",o="[object Error]",s="[object Number]",c="[object RegExp]",u="[object String]";t.exports=r},{}],141:[function(e,t,n){function r(e,t,n,r,a,s,c){var u=i(e),l=u.length,p=i(t),f=p.length;if(l!=f&&!a)return!1;for(var h=l;h--;){var d=u[h];if(!(a?d in t:o.call(t,d)))return!1}for(var m=a;++h<l;){d=u[h];var y=e[d],g=t[d],v=r?r(a?g:y,a?y:g,d):void 0;if(!(void 0===v?n(y,g,r,a,s,c):v))return!1;m||(m="constructor"==d)}if(!m){var x=e.constructor,b=t.constructor;if(x!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof b&&b instanceof b))return!1}return!0}var i=e(169),a=Object.prototype,o=a.hasOwnProperty;t.exports=r},{169:169}],142:[function(e,t,n){var r=e(121),i=r("length");t.exports=i},{121:121}],143:[function(e,t,n){function r(e){for(var t=a(e),n=t.length;n--;)t[n][2]=i(t[n][1]);return t}var i=e(152),a=e(173);t.exports=r},{152:152,173:173}],144:[function(e,t,n){function r(e,t){var n=null==e?void 0:e[t];return i(n)?n:void 0}var i=e(161);t.exports=r},{161:161}],145:[function(e,t,n){function r(e,t,n){for(var r=e.length,i=t+(n?0:-1);n?i--:++i<r;){var a=e[i];if(a!==a)return i}return-1}t.exports=r},{}],146:[function(e,t,n){function r(e){return null!=e&&a(i(e))}var i=e(142),a=e(150);t.exports=r},{142:142,150:150}],147:[function(e,t,n){function r(e,t){return e="number"==typeof e||i.test(e)?+e:-1,t=null==t?a:t,e>-1&&e%1==0&&t>e}var i=/^\d+$/,a=9007199254740991;t.exports=r},{}],148:[function(e,t,n){function r(e,t,n){if(!o(n))return!1;var r=typeof t;if("number"==r?i(n)&&a(t,n.length):"string"==r&&t in n){var s=n[t];return e===e?e===s:s!==s}return!1}var i=e(146),a=e(147),o=e(163);t.exports=r},{146:146,147:147,163:163}],149:[function(e,t,n){function r(e,t){var n=typeof e;if("string"==n&&s.test(e)||"number"==n)return!0;if(i(e))return!1;var r=!o.test(e);return r||null!=t&&e in a(t)}var i=e(159),a=e(156),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\n\\]|\\.)*?\1)\]/,s=/^\w*$/;t.exports=r},{156:156,159:159}],150:[function(e,t,n){function r(e){return"number"==typeof e&&e>-1&&e%1==0&&i>=e}var i=9007199254740991;t.exports=r},{}],151:[function(e,t,n){function r(e){return!!e&&"object"==typeof e}t.exports=r},{}],152:[function(e,t,n){function r(e){return e===e&&!i(e)}var i=e(163);t.exports=r},{163:163}],153:[function(e,t,n){function r(e,t){e=i(e);for(var n=-1,r=t.length,a={};++n<r;){var o=t[n];o in e&&(a[o]=e[o])}return a}var i=e(156);t.exports=r},{156:156}],154:[function(e,t,n){function r(e,t){var n={};return i(e,function(e,r,i){t(e,r,i)&&(n[r]=e)}),n}var i=e(109);t.exports=r},{109:109}],155:[function(e,t,n){function r(e){for(var t=c(e),n=t.length,r=n&&e.length,u=!!r&&s(r)&&(a(e)||i(e)),p=-1,f=[];++p<n;){var h=t[p];(u&&o(h,r)||l.call(e,h))&&f.push(h)}return f}var i=e(158),a=e(159),o=e(147),s=e(150),c=e(170),u=Object.prototype,l=u.hasOwnProperty;t.exports=r},{147:147,150:150,158:158,159:159,170:170}],156:[function(e,t,n){function r(e){return i(e)?e:Object(e)}var i=e(163);t.exports=r},{163:163}],157:[function(e,t,n){function r(e){if(a(e))return e;var t=[];return i(e).replace(o,function(e,n,r,i){t.push(r?i.replace(s,"$1"):n||e)}),t}var i=e(126),a=e(159),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\n\\]|\\.)*?)\2)\]/g,s=/\\(\\)?/g;t.exports=r},{126:126,159:159}],158:[function(e,t,n){function r(e){return a(e)&&i(e)&&s.call(e,"callee")&&!c.call(e,"callee")}var i=e(146),a=e(151),o=Object.prototype,s=o.hasOwnProperty,c=o.propertyIsEnumerable;t.exports=r},{146:146,151:151}],159:[function(e,t,n){var r=e(144),i=e(150),a=e(151),o="[object Array]",s=Object.prototype,c=s.toString,u=r(Array,"isArray"),l=u||function(e){return a(e)&&i(e.length)&&c.call(e)==o};t.exports=l},{144:144,150:150,151:151}],160:[function(e,t,n){function r(e){return i(e)&&s.call(e)==a}var i=e(163),a="[object Function]",o=Object.prototype,s=o.toString;t.exports=r},{163:163}],161:[function(e,t,n){function r(e){return null==e?!1:i(e)?l.test(c.call(e)):a(e)&&o.test(e)}var i=e(160),a=e(151),o=/^\[object .+?Constructor\]$/,s=Object.prototype,c=Function.prototype.toString,u=s.hasOwnProperty,l=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=r},{151:151,160:160}],162:[function(e,t,n){function r(e){return"number"==typeof e||i(e)&&s.call(e)==a}var i=e(151),a="[object Number]",o=Object.prototype,s=o.toString;t.exports=r},{151:151}],163:[function(e,t,n){function r(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}t.exports=r},{}],164:[function(e,t,n){function r(e){var t;if(!o(e)||l.call(e)!=s||a(e)||!u.call(e,"constructor")&&(t=e.constructor,"function"==typeof t&&!(t instanceof t)))return!1;var n;return i(e,function(e,t){n=t}),void 0===n||u.call(e,n)}var i=e(109),a=e(158),o=e(151),s="[object Object]",c=Object.prototype,u=c.hasOwnProperty,l=c.toString;t.exports=r},{109:109,151:151,158:158}],165:[function(e,t,n){function r(e){return"string"==typeof e||i(e)&&s.call(e)==a}var i=e(151),a="[object String]",o=Object.prototype,s=o.toString;t.exports=r},{151:151}],166:[function(e,t,n){function r(e){return a(e)&&i(e.length)&&!!N[D.call(e)]}var i=e(150),a=e(151),o="[object Arguments]",s="[object Array]",c="[object Boolean]",u="[object Date]",l="[object Error]",p="[object Function]",f="[object Map]",h="[object Number]",d="[object Object]",m="[object RegExp]",y="[object Set]",g="[object String]",v="[object WeakMap]",x="[object ArrayBuffer]",b="[object Float32Array]",E="[object Float64Array]",w="[object Int8Array]",A="[object Int16Array]",S="[object Int32Array]",_="[object Uint8Array]",T="[object Uint8ClampedArray]",C="[object Uint16Array]",R="[object Uint32Array]",N={};N[b]=N[E]=N[w]=N[A]=N[S]=N[_]=N[T]=N[C]=N[R]=!0,N[o]=N[s]=N[x]=N[c]=N[u]=N[l]=N[p]=N[f]=N[h]=N[d]=N[m]=N[y]=N[g]=N[v]=!1;var M=Object.prototype,D=M.toString;t.exports=r},{150:150,151:151}],167:[function(e,t,n){function r(e){return i(e,a(e))}var i=e(99),a=e(170);t.exports=r},{170:170,99:99}],168:[function(e,t,n){var r=e(96),i=e(97),a=e(132),o=a(function(e,t,n){return n?r(e,t,n):i(e,t)});t.exports=o},{132:132,96:96,97:97}],169:[function(e,t,n){var r=e(144),i=e(146),a=e(163),o=e(155),s=r(Object,"keys"),c=s?function(e){var t=null==e?void 0:e.constructor;return"function"==typeof t&&t.prototype===e||"function"!=typeof e&&i(e)?o(e):a(e)?s(e):[]}:o;t.exports=c},{144:144,146:146,155:155,163:163}],170:[function(e,t,n){function r(e){if(null==e)return[];c(e)||(e=Object(e));var t=e.length;t=t&&s(t)&&(a(e)||i(e))&&t||0;for(var n=e.constructor,r=-1,u="function"==typeof n&&n.prototype===e,p=Array(t),f=t>0;++r<t;)p[r]=r+"";for(var h in e)f&&o(h,t)||"constructor"==h&&(u||!l.call(e,h))||p.push(h);return p}var i=e(158),a=e(159),o=e(147),s=e(150),c=e(163),u=Object.prototype,l=u.hasOwnProperty;t.exports=r},{147:147,150:150,158:158,159:159,163:163}],171:[function(e,t,n){var r=e(119),i=e(132),a=i(r);t.exports=a},{119:119,132:132}],172:[function(e,t,n){var r=e(92),i=e(101),a=e(107),o=e(128),s=e(170),c=e(153),u=e(154),l=e(86),p=l(function(e,t){if(null==e)return{};if("function"!=typeof t[0]){var t=r(a(t),String);return c(e,i(s(e),t))}var n=o(t[0],t[1],3);return u(e,function(e,t,r){return!n(e,t,r)})});t.exports=p},{101:101,107:107,128:128,153:153,154:154,170:170,86:86,92:92}],173:[function(e,t,n){function r(e){e=a(e);for(var t=-1,n=i(e),r=n.length,o=Array(r);++t<r;){var s=n[t];o[t]=[s,e[s]]}return o}var i=e(169),a=e(156);t.exports=r},{156:156,169:169}],174:[function(e,t,n){var r=e(107),i=e(128),a=e(153),o=e(154),s=e(86),c=s(function(e,t){return null==e?{}:"function"==typeof t[0]?o(e,i(t[0],t[1],3)):a(e,r(t))});t.exports=c},{107:107,128:128,153:153,154:154,86:86}],175:[function(e,t,n){function r(e){return i(e,a(e))}var i=e(127),a=e(169);t.exports=r},{127:127,169:169}],176:[function(e,t,n){function r(e){return e}t.exports=r},{}],177:[function(e,t,n){function r(e){return o(e)?i(e):a(e)}var i=e(121),a=e(122),o=e(149);t.exports=r},{121:121,122:122,149:149}],178:[function(e,t,n){t.exports=e(184)},{184:184}],179:[function(e,t,n){t.exports=function(e){for(var t;e.childNodes.length;)t=e.childNodes[0],e.removeChild(t);return e}},{}],180:[function(e,t,n){t.exports=e(187)},{187:187}],181:[function(e,t,n){t.exports=e(191)},{191:191}],182:[function(e,t,n){t.exports=e(190)},{190:190}],183:[function(e,t,n){t.exports=function(e){e.parentNode&&e.parentNode.removeChild(e)}},{}],184:[function(e,t,n){function r(e){if(!e||!e.nodeType)throw new Error("A DOM element reference is required");this.el=e,this.list=e.classList}var i=e(185),a=/\s+/,o=Object.prototype.toString;t.exports=function(e){return new r(e)},r.prototype.add=function(e){if(this.list)return this.list.add(e),this;var t=this.array(),n=i(t,e);return~n||t.push(e),this.el.className=t.join(" "),this},r.prototype.remove=function(e){if("[object RegExp]"==o.call(e))return this.removeMatching(e);if(this.list)return this.list.remove(e),this;var t=this.array(),n=i(t,e);return~n&&t.splice(n,1),this.el.className=t.join(" "),this},r.prototype.removeMatching=function(e){for(var t=this.array(),n=0;n<t.length;n++)e.test(t[n])&&this.remove(t[n]);return this},r.prototype.toggle=function(e,t){return this.list?("undefined"!=typeof t?t!==this.list.toggle(e,t)&&this.list.toggle(e):this.list.toggle(e),this):("undefined"!=typeof t?t?this.add(e):this.remove(e):this.has(e)?this.remove(e):this.add(e),this)},r.prototype.array=function(){var e=this.el.getAttribute("class")||"",t=e.replace(/^\s+|\s+$/g,""),n=t.split(a);return""===n[0]&&n.shift(),n},r.prototype.has=r.prototype.contains=function(e){return this.list?this.list.contains(e):!!~i(this.array(),e)}},{185:185}],185:[function(e,t,n){t.exports=function(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0;n<e.length;++n)if(e[n]===t)return n;return-1}},{}],186:[function(e,t,n){var r=e(189);t.exports=function(e,t,n,i){for(e=n?{parentNode:e}:e,i=i||document;(e=e.parentNode)&&e!==document;){if(r(e,t))return e;if(e===i)return}}},{189:189}],187:[function(e,t,n){var r=e(186),i=e(188);n.bind=function(e,t,n,a,o){return i.bind(e,n,function(n){var i=n.target||n.srcElement;n.delegateTarget=r(i,t,!0,e),n.delegateTarget&&a.call(e,n)},o)},n.unbind=function(e,t,n,r){i.unbind(e,t,n,r)}},{186:186,188:188}],188:[function(e,t,n){var r=window.addEventListener?"addEventListener":"attachEvent",i=window.removeEventListener?"removeEventListener":"detachEvent",a="addEventListener"!==r?"on":"";n.bind=function(e,t,n,i){return e[r](a+t,n,i||!1),n},n.unbind=function(e,t,n,r){return e[i](a+t,n,r||!1),n}},{}],189:[function(e,t,n){function r(e,t){if(!e||1!==e.nodeType)return!1;if(o)return o.call(e,t);for(var n=i.all(t,e.parentNode),r=0;r<n.length;++r)if(n[r]==e)return!0;return!1}var i=e(190),a=Element.prototype,o=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.msMatchesSelector||a.oMatchesSelector;t.exports=r},{190:190}],190:[function(e,t,n){function r(e,t){return t.querySelector(e)}n=t.exports=function(e,t){return t=t||document,r(e,t)},n.all=function(e,t){return t=t||document,t.querySelectorAll(e)},n.engine=function(e){if(!e.one)throw new Error(".one callback required");if(!e.all)throw new Error(".all callback required");return r=e.one,n.all=e.all,n}},{}],191:[function(e,t,n){function r(e,t){if("string"!=typeof e)throw new TypeError("String expected");t||(t=document);var n=/<([\w:]+)/.exec(e);if(!n)return t.createTextNode(e);e=e.replace(/^\s+|\s+$/g,"");var r=n[1];if("body"==r){var i=t.createElement("html");return i.innerHTML=e,i.removeChild(i.lastChild)}var a=o[r]||o._default,s=a[0],c=a[1],u=a[2],i=t.createElement("div");for(i.innerHTML=c+e+u;s--;)i=i.lastChild;if(i.firstChild==i.lastChild)return i.removeChild(i.firstChild);for(var l=t.createDocumentFragment();i.firstChild;)l.appendChild(i.removeChild(i.firstChild));return l}t.exports=r;var i=document.createElement("div");i.innerHTML='  <link/><table></table><a href="/a">a</a><input type="checkbox"/>';var a=!i.getElementsByTagName("link").length;i=void 0;var o={legend:[1,"<fieldset>","</fieldset>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],_default:a?[1,"X<div>","</div>"]:[0,"",""]};o.td=o.th=[3,"<table><tbody><tr>","</tr></tbody></table>"],o.option=o.optgroup=[1,'<select multiple="multiple">',"</select>"],o.thead=o.tbody=o.colgroup=o.caption=o.tfoot=[1,"<table>","</table>"],o.polyline=o.ellipse=o.polygon=o.circle=o.text=o.line=o.path=o.rect=o.g=[1,'<svg xmlns="http://www.w3.org/2000/svg" version="1.1">',"</svg>"]},{}],192:[function(e,t,n){t.exports=e(194),t.exports.Collection=e(193)},{193:193,194:194}],193:[function(e,t,n){"use strict";function r(e,t,n,r){var i=n.inverse;return e.remove=function(e){var n=this.indexOf(e);return-1!==n&&(this.splice(n,1),t.unset(e,i,r)),e},e.contains=function(e){return-1!==this.indexOf(e)},e.add=function(e){this.contains(e)||(this.push(e),t.set(e,i,r))},e}t.exports.extend=r},{}],194:[function(e,t,n){"use strict";function r(e,t){return Object.prototype.hasOwnProperty.call(e,t.name||t)}function i(e,t,n){Object.defineProperty(n,t.name,{enumerable:t.enumerable,value:s.extend(n[t.name]||[],e,t,n)})}function a(e,t,n){var r=t.inverse,i=n[t.name];Object.defineProperty(n,t.name,{enumerable:t.enumerable,get:function(){return i},set:function(t){if(t!==i){var a=i;i=null,a&&e.unset(a,r,n),i=t,e.set(i,r,n)}}})}function o(e,t){return this instanceof o?(e.inverse=t,t.inverse=e,this.props={},this.props[e.name]=e,void(this.props[t.name]=t)):new o(e,t)}var s=e(193);o.prototype.bind=function(e,t){if("string"==typeof t){if(!this.props[t])throw new Error("no property <"+t+"> in ref");t=this.props[t]}t.collection?i(this,t,e):a(this,t,e)},o.prototype.ensureBound=function(e,t){r(e,t)||this.bind(e,t)},o.prototype.unset=function(e,t,n){e&&(this.ensureBound(e,t),t.collection?e[t.name].remove(n):e[t.name]=void 0)},o.prototype.set=function(e,t,n){e&&(this.ensureBound(e,t),t.collection?e[t.name].add(n):e[t.name]=n)},t.exports=o},{193:193}]},{},[1])(1)});