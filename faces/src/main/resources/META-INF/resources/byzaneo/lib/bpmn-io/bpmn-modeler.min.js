/*! bpmn-js - bpmn-modeler v0.11.0 | (c) 2014, 2015 camunda Services GmbH | bpmn.io/license */
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;"undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.BpmnJS=t()}}(function(){var t;return function e(t,n,i){function r(a,s){if(!n[a]){if(!t[a]){var c="function"==typeof require&&require;if(!s&&c)return c(a,!0);if(o)return o(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var p=n[a]={exports:{}};t[a][0].call(p.exports,function(e){var n=t[a][1][e];return r(n?n:e)},p,p.exports,e,t,n,i)}return n[a].exports}for(var o="function"==typeof require&&require,a=0;a<i.length;a++)r(i[a]);return r}({1:[function(t,e,n){"use strict";function i(t){s.call(this,t)}var r=t(207),o=t(53),a=t(205),s=t(2),c='<?xml version="1.0" encoding="UTF-8"?><bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" targetNamespace="http://bpmn.io/schema/bpmn" id="Definitions_1"><bpmn:process id="Process_1" isExecutable="false"><bpmn:startEvent id="StartEvent_1"/></bpmn:process><bpmndi:BPMNDiagram id="BPMNDiagram_1"><bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1"><bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1"><dc:Bounds height="36.0" width="36.0" x="173.0" y="102.0"/></bpmndi:BPMNShape></bpmndi:BPMNPlane></bpmndi:BPMNDiagram></bpmn:definitions>';r(i,s),i.prototype.createDiagram=function(t){return this.importXML(c,t)},i.prototype.createModdle=function(){var t=s.prototype.createModdle.call(this);return o.extend(t,new a([32,36,1])),t},i.prototype._interactionModules=[t(14),t(182),t(178),t(179)],i.prototype._modelingModules=[t(139),t(96),t(152),t(166),t(114),t(10),t(42),t(32),t(8),t(36)],i.prototype._modules=[].concat(i.prototype._modules,i.prototype._interactionModules,i.prototype._modelingModules),e.exports=i},{10:10,114:114,139:139,14:14,152:152,166:166,178:178,179:179,182:182,2:2,205:205,207:207,32:32,36:36,42:42,53:53,8:8,96:96}],2:[function(t,e,n){"use strict";function i(t,e){var n=t.get("eventBus");e.forEach(function(t){n.on(t.event,t.handler)})}function r(t){var e=/unparsable content <([^>]+)> detected([\s\S]*)$/,n=e.exec(t.message);return n&&(t.message="unparsable content <"+n[1]+"> detected; this may indicate an invalid BPMN 2.0 diagram file"+n[2]),t}function o(t){return t+(p(t)?"px":"")}function a(t){this.options=t=s({},v,t||{});var e=t.container;e.get&&(e=e.get(0)),u(e)&&(e=h(e));var n=this.container=l('<div class="bjs-container"></div>');e.appendChild(n),s(n.style,{width:o(t.width),height:o(t.height),position:t.position});var i="iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAMAAADypuvZAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAADBQTFRFiMte9PrwldFwfcZPqtqN0+zEyOe1XLgjvuKncsJAZ70y6fXh3vDT////UrQV////G2zN+AAAABB0Uk5T////////////////////AOAjXRkAAAHDSURBVHjavJZJkoUgDEBJmAX8979tM8u3E6x20VlYJfFFMoL4vBDxATxZcakIOJTWSmxvKWVIkJ8jHvlRv1F2LFrVISCZI+tCtQx+XfewgVTfyY3plPiQEAzI3zWy+kR6NBhFBYeBuscJLOUuA2WVLpCjVIaFzrNQZArxAZKUQm6gsj37L9Cb7dnIBUKxENaaMJQqMpDXvSL+ktxdGRm2IsKgJGGPg7atwUG5CcFUEuSv+CwQqizTrvDTNXdMU2bMiDWZd8d7QIySWVRsb2vBBioxOFt4OinPBapL+neAb5KL5IJ8szOza2/DYoipUCx+CjO0Bpsv0V6mktNZ+k8rlABlWG0FrOpKYVo8DT3dBeLEjUBAj7moDogVii7nSS9QzZnFcOVBp1g2PyBQ3Vr5aIapN91VJy33HTJLC1iX2FY6F8gRdaAeIEfVONgtFCzZTmoLEdOjBDfsIOA6128gw3eu1shAajdZNAORxuQDJN5A5PbEG6gNIu24QJD5iNyRMZIr6bsHbCtCU/OaOaSvgkUyDMdDa1BXGf5HJ1To+/Ym6mCKT02Y+/Sa126ZKyd3jxhzpc1r8zVL6YM1Qy/kR4ABAFJ6iQUnivhAAAAAAElFTkSuQmCC",r='<a href="http://bpmn.io" target="_blank" class="bjs-powered-by" title="Powered by bpmn.io" style="position: absolute; bottom: 15px; right: 15px; z-index: 100"><img src="data:image/png;base64,'+i+'"></a>';n.appendChild(l(r))}var s=t(339),c=t(343),u=t(336),p=t(333),l=t(356),h=t(359),d=t(360),f=t(76),m=t(51),y=t(45),v={width:"100%",height:"100%",position:"relative",container:"body"};a.prototype.importXML=function(t,e){var n=this;this.moddle=this.createModdle(),this.moddle.fromXML(t,"bpmn:Definitions",function(t,i,o){if(t)return t=r(t),e(t);var a=o.warnings;n.importDefinitions(i,function(t,n){return t?e(t):void e(null,a.concat(n||[]))})})},a.prototype.saveXML=function(t,e){e||(e=t,t={});var n=this.definitions;return n?void this.moddle.toXML(n,t,e):e(new Error("no definitions loaded"))},a.prototype.createModdle=function(){return new m(this.options.moddleExtensions)},a.prototype.saveSVG=function(t,e){e||(e=t,t={});var n=this.get("canvas"),i=n.getDefaultLayer(),r=n._svg.select("defs"),o=i.innerSVG(),a=r&&r.outerSVG()||"",s=i.getBBox(),c='<?xml version="1.0" encoding="utf-8"?>\n<!-- created with bpmn-js / http://bpmn.io -->\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+s.width+'" height="'+s.height+'" viewBox="'+s.x+" "+s.y+" "+s.width+" "+s.height+'" version="1.1">'+a+o+"</svg>";e(null,c)},a.prototype.get=function(t){if(!this.diagram)throw new Error("no diagram loaded");return this.diagram.get(t)},a.prototype.invoke=function(t){if(!this.diagram)throw new Error("no diagram loaded");return this.diagram.invoke(t)},a.prototype.importDefinitions=function(t,e){try{this.diagram&&this.clear(),this.definitions=t;var n=this.diagram=this._createDiagram(this.options);this._init(n),y.importBpmnDiagram(n,t,e)}catch(i){e(i)}},a.prototype._init=function(t){i(t,this.__listeners||[])},a.prototype._createDiagram=function(t){var e=[].concat(t.modules||this.getModules(),t.additionalModules||[]);return e.unshift({bpmnjs:["value",this],moddle:["value",this.moddle]}),t=c(t,"additionalModules"),t=s(t,{canvas:{container:this.container},modules:e}),new f(t)},a.prototype.getModules=function(){return this._modules},a.prototype.clear=function(){var t=this.diagram;t&&t.destroy()},a.prototype.destroy=function(){this.clear(),d(this.container)},a.prototype.on=function(t,e){var n=this.diagram,i=this.__listeners=this.__listeners||[];i.push({event:t,handler:e}),n&&n.get("eventBus").on(t,e)},a.prototype._modules=[t(3),t(159),t(143)],e.exports=a},{143:143,159:159,3:3,333:333,336:336,339:339,343:343,356:356,359:359,360:360,45:45,51:51,76:76}],3:[function(t,e,n){e.exports={__depends__:[t(6),t(47)]}},{47:47,6:6}],4:[function(t,e,n){"use strict";function i(t,e,n){function i(t,e){K[t]=e}function r(t){return K[t]}function y(t){function e(t,e){var n=s({fill:"black",strokeWidth:1,strokeLinecap:"round",strokeDasharray:"none"},e.attrs),r=e.ref||{x:0,y:0},o=e.scale||1;"none"===n.strokeDasharray&&(n.strokeDasharray=[1e4,1]);var a=e.element.attr(n).marker(0,0,20,20,r.x,r.y).attr({markerWidth:20*o,markerHeight:20*o});return i(t,a)}e("sequenceflow-end",{element:t.path("M 1 5 L 11 10 L 1 15 Z"),ref:{x:11,y:10},scale:.5}),e("messageflow-start",{element:t.circle(6,6,3.5),attrs:{fill:"white",stroke:"black"},ref:{x:6,y:6}}),e("messageflow-end",{element:t.path("m 1 5 l 0 -3 l 7 3 l -7 3 z"),attrs:{fill:"white",stroke:"black",strokeLinecap:"butt"},ref:{x:8.5,y:5}}),e("data-association-end",{element:t.path("M 1 5 L 11 10 L 1 15"),attrs:{fill:"white",stroke:"black"},ref:{x:11,y:10},scale:.5}),e("conditional-flow-marker",{element:t.path("M 0 10 L 8 6 L 16 10 L 8 14 Z"),attrs:{fill:"white",stroke:"black"},ref:{x:-1,y:10},scale:.5}),e("conditional-default-flow-marker",{element:t.path("M 1 4 L 5 16"),attrs:{stroke:"black"},ref:{x:-5,y:10},scale:.5})}function v(t,n,i){return o(n)||(i=n,n=[]),e.style(n||[],s(i,t||{}))}function g(t,e,n,i,r){a(i)&&(r=i,i=0),i=i||0,r=v(r,{stroke:"black",strokeWidth:2,fill:"white"});var o=e/2,s=n/2;return t.circle(o,s,Math.round((e+n)/4-i)).attr(r)}function x(t,e,n,i,r,o){return a(r)&&(o=r,r=0),r=r||0,o=v(o,{stroke:"black",strokeWidth:2,fill:"white"}),t.rect(r,r,e-2*r,n-2*r,i).attr(o)}function b(t,e,n,i){var r=e/2,o=n/2,a=[r,0,e,o,r,n,0,o];return i=v(i,{stroke:"black",strokeWidth:2,fill:"white"}),t.polygon(a).attr(i)}function E(t,e,n){return n=v(n,["no-fill"],{stroke:"black",strokeWidth:2,fill:"none"}),m(e,n).appendTo(t)}function w(t,e,n){return n=v(n,["no-fill"],{strokeWidth:2,stroke:"black"}),t.path(e).attr(n)}function _(t){return function(e,n){return Z[t](e,n)}}function S(t){return Z[t]}function A(t,e){var n=L(t),i=F(n);return j(n,"bpmn:MessageEventDefinition")?S("bpmn:MessageEventDefinition")(e,t,i):j(n,"bpmn:TimerEventDefinition")?S("bpmn:TimerEventDefinition")(e,t,i):j(n,"bpmn:ConditionalEventDefinition")?S("bpmn:ConditionalEventDefinition")(e,t):j(n,"bpmn:SignalEventDefinition")?S("bpmn:SignalEventDefinition")(e,t,i):j(n,"bpmn:CancelEventDefinition")&&j(n,"bpmn:TerminateEventDefinition",{parallelMultiple:!1})?S("bpmn:MultipleEventDefinition")(e,t,i):j(n,"bpmn:CancelEventDefinition")&&j(n,"bpmn:TerminateEventDefinition",{parallelMultiple:!0})?S("bpmn:ParallelMultipleEventDefinition")(e,t,i):j(n,"bpmn:EscalationEventDefinition")?S("bpmn:EscalationEventDefinition")(e,t,i):j(n,"bpmn:LinkEventDefinition")?S("bpmn:LinkEventDefinition")(e,t,i):j(n,"bpmn:ErrorEventDefinition")?S("bpmn:ErrorEventDefinition")(e,t,i):j(n,"bpmn:CancelEventDefinition")?S("bpmn:CancelEventDefinition")(e,t,i):j(n,"bpmn:CompensateEventDefinition")?S("bpmn:CompensateEventDefinition")(e,t,i):j(n,"bpmn:TerminateEventDefinition")?S("bpmn:TerminateEventDefinition")(e,t,i):null}function T(t,e,n){return X.createText(t,e||"",n).addClass("djs-label")}function C(t,e,n){var i=L(e);return T(t,i.name,{box:e,align:n,padding:5})}function N(t,e,n){var i=L(e);return i.name||(e.hidden=!0),T(t,i.name,{box:e,align:n,style:{fontSize:"11px"}})}function M(t,e,n){var i=T(t,e,{box:{height:30,width:n.height},align:"center-middle"}),r=-1*n.height;i.transform("rotate(270) translate("+r+",0)")}function R(t){for(var e=t.waypoints,n="m  "+e[0].x+","+e[0].y,i=1;i<e.length;i++)n+="L"+e[i].x+","+e[i].y+" ";return n}function P(t,e,n){var i,r=L(e),o=p(n,"SubProcessMarker");return i=o?{seq:-21,parallel:-22,compensation:-42,loop:-18,adhoc:10}:{seq:-3,parallel:-6,compensation:-27,loop:0,adhoc:10},c(n,function(n){S(n)(t,e,i)}),"bpmn:AdHocSubProcess"===r.$type&&S("AdhocMarker")(t,e,i),r.loopCharacteristics&&void 0===r.loopCharacteristics.isSequential?void S("LoopMarker")(t,e,i):(r.loopCharacteristics&&void 0!==r.loopCharacteristics.isSequential&&!r.loopCharacteristics.isSequential&&S("ParallelMarker")(t,e,i),r.loopCharacteristics&&r.loopCharacteristics.isSequential&&S("SequentialMarker")(t,e,i),void(r.isForCompensation&&S("CompensationMarker")(t,e,i)))}function D(t,e){var n=e.type,i=Z[n];return i?i(t,e):h.prototype.drawShape.apply(this,[t,e])}function k(t,e){var n=e.type,i=Z[n];return i?i(t,e):h.prototype.drawConnection.apply(this,[t,e])}function B(t,e){var i=(e.height-16)/e.height,r=n.getScaledPath("DATA_OBJECT_COLLECTION_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.451,my:i}});w(t,r,{strokeWidth:2})}function O(t,e){return t.isCollection||t.elementObjectRef&&t.elementObjectRef.isCollection}function I(t){return t.businessObject.di}function L(t){return t.businessObject}function j(t,e,n){function i(t,e){return u(e,function(e,n){return t[n]==e})}return l(t.eventDefinitions,function(r){return r.$type===e&&i(t,n)})}function F(t){return"bpmn:IntermediateThrowEvent"===t.$type||"bpmn:EndEvent"===t.$type}function z(t){return t.join(",").replace(/,?([A-z]),?/g,"$1")}function V(t){var e=t.x+t.width/2,n=t.y+t.height/2,i=t.width/2,r=[["M",e,n],["m",0,-i],["a",i,i,0,1,1,0,2*i],["a",i,i,0,1,1,0,-2*i],["z"]];return z(r)}function $(t){var e=q,n=t.x,i=t.y,r=t.width,o=t.height,a=[["M",n+e,i],["l",r-2*e,0],["a",e,e,0,0,1,e,e],["l",0,o-2*e],["a",e,e,0,0,1,-e,e],["l",2*e-r,0],["a",e,e,0,0,1,-e,-e],["l",0,2*e-o],["a",e,e,0,0,1,e,-e],["z"]];return z(a)}function G(t){var e=t.width,n=t.height,i=t.x,r=t.y,o=e/2,a=n/2,s=[["M",i+o,r],["l",o,a],["l",-o,a],["l",-o,-a],["z"]];return z(s)}function W(t){var e=t.x,n=t.y,i=t.width,r=t.height,o=[["M",e,n],["l",i,0],["l",0,r],["l",-i,0],["z"]];return z(o)}function U(t){var e=L(t);return e.$instanceOf("bpmn:Event")?V(t):e.$instanceOf("bpmn:Activity")?$(t):e.$instanceOf("bpmn:Gateway")?G(t):W(t)}h.call(this,e);var q=10,H=3,Y={fontFamily:"Arial, sans-serif",fontSize:"12px"},X=new d({style:Y,size:{width:100}}),K={},Z={"bpmn:Event":function(t,e,n){return g(t,e.width,e.height,n)},"bpmn:StartEvent":function(t,e){var n={},i=L(e);i.isInterrupting||(n={strokeDasharray:"6",strokeLinecap:"round"});var r=S("bpmn:Event")(t,e,n);return A(e,t),r},"bpmn:MessageEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_MESSAGE",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:e.width,containerHeight:e.height,position:{mx:.235,my:.315}}),o=i?"black":"white",a=i?"white":"black",s=w(t,r,{strokeWidth:1,fill:o,stroke:a});return s},"bpmn:TimerEventDefinition":function(t,e){var i=g(t,e.width,e.height,.2*e.height,{strokeWidth:2}),r=n.getScaledPath("EVENT_TIMER_WH",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:e.width,containerHeight:e.height,position:{mx:.5,my:.5}});w(t,r,{strokeWidth:2,strokeLinecap:"square"});for(var o=0;12>o;o++){var a=n.getScaledPath("EVENT_TIMER_LINE",{xScaleFactor:.75,yScaleFactor:.75,containerWidth:e.width,containerHeight:e.height,position:{mx:.5,my:.5}}),s=e.width/2,c=e.height/2;w(t,a,{strokeWidth:1,strokeLinecap:"square",transform:"rotate("+30*o+","+c+","+s+")"})}return i},"bpmn:EscalationEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_ESCALATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.5,my:.555}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o})},"bpmn:ConditionalEventDefinition":function(t,e){var i=n.getScaledPath("EVENT_CONDITIONAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.5,my:.222}});return w(t,i,{strokeWidth:1})},"bpmn:LinkEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_LINK",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.57,my:.263}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o})},"bpmn:ErrorEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_ERROR",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:e.width,containerHeight:e.height,position:{mx:.2,my:.722}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o})},"bpmn:CancelEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_CANCEL_45",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.638,my:-.055}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o}).transform("rotate(45)")},"bpmn:CompensateEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.201,my:.472}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o})},"bpmn:SignalEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_SIGNAL",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:e.width,containerHeight:e.height,position:{mx:.5,my:.2}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o})},"bpmn:MultipleEventDefinition":function(t,e,i){var r=n.getScaledPath("EVENT_MULTIPLE",{xScaleFactor:1.1,yScaleFactor:1.1,containerWidth:e.width,containerHeight:e.height,position:{mx:.222,my:.36}}),o=i?"black":"none";return w(t,r,{strokeWidth:1,fill:o})},"bpmn:ParallelMultipleEventDefinition":function(t,e){var i=n.getScaledPath("EVENT_PARALLEL_MULTIPLE",{xScaleFactor:1.2,yScaleFactor:1.2,containerWidth:e.width,containerHeight:e.height,position:{mx:.458,my:.194}});return w(t,i,{strokeWidth:1})},"bpmn:EndEvent":function(t,e){var n=S("bpmn:Event")(t,e,{strokeWidth:4});return A(e,t,!0),n},"bpmn:TerminateEventDefinition":function(t,e){var n=g(t,e.width,e.height,8,{strokeWidth:4,fill:"black"});return n},"bpmn:IntermediateEvent":function(t,e){var n=S("bpmn:Event")(t,e,{strokeWidth:1});return g(t,e.width,e.height,H,{strokeWidth:1,fill:"none"}),A(e,t),n},"bpmn:IntermediateCatchEvent":_("bpmn:IntermediateEvent"),"bpmn:IntermediateThrowEvent":_("bpmn:IntermediateEvent"),"bpmn:Activity":function(t,e,n){return x(t,e.width,e.height,q,n)},"bpmn:Task":function(t,e,n){var i=S("bpmn:Activity")(t,e,n);return C(t,e,"center-middle"),P(t,e),i},"bpmn:ServiceTask":function(t,e){var i=S("bpmn:Task")(t,e),r=n.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:12,y:18}});w(t,r,{strokeWidth:1,fill:"none"});var o=n.getScaledPath("TASK_TYPE_SERVICE_FILL",{abspos:{x:17.2,y:18}});w(t,o,{strokeWidth:0,stroke:"none",fill:"white"});var a=n.getScaledPath("TASK_TYPE_SERVICE",{abspos:{x:17,y:22}});return w(t,a,{strokeWidth:1,fill:"white"}),i},"bpmn:UserTask":function(t,e){var i=S("bpmn:Task")(t,e),r=15,o=12,a=n.getScaledPath("TASK_TYPE_USER_1",{abspos:{x:r,y:o}});w(t,a,{strokeWidth:.5,fill:"none"});var s=n.getScaledPath("TASK_TYPE_USER_2",{abspos:{x:r,y:o}});w(t,s,{strokeWidth:.5,fill:"none"});var c=n.getScaledPath("TASK_TYPE_USER_3",{abspos:{x:r,y:o}});return w(t,c,{strokeWidth:.5,fill:"black"}),i},"bpmn:ManualTask":function(t,e){var i=S("bpmn:Task")(t,e),r=n.getScaledPath("TASK_TYPE_MANUAL",{abspos:{x:17,y:15}});return w(t,r,{strokeWidth:.25,fill:"white",stroke:"black"}),i},"bpmn:SendTask":function(t,e){var i=S("bpmn:Task")(t,e),r=n.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:1,yScaleFactor:1,containerWidth:21,containerHeight:14,position:{mx:.285,my:.357}});return w(t,r,{strokeWidth:1,fill:"black",stroke:"white"}),i},"bpmn:ReceiveTask":function(t,e){var i,r=L(e),o=S("bpmn:Task")(t,e);return r.instantiate?(g(t,28,28,4.4,{strokeWidth:1}),i=n.getScaledPath("TASK_TYPE_INSTANTIATING_SEND",{abspos:{x:7.77,y:9.52}})):i=n.getScaledPath("TASK_TYPE_SEND",{xScaleFactor:.9,yScaleFactor:.9,containerWidth:21,containerHeight:14,position:{mx:.3,my:.4}}),w(t,i,{strokeWidth:1}),o},"bpmn:ScriptTask":function(t,e){var i=S("bpmn:Task")(t,e),r=n.getScaledPath("TASK_TYPE_SCRIPT",{abspos:{x:15,y:20}});return w(t,r,{strokeWidth:1}),i},"bpmn:BusinessRuleTask":function(t,e){var i=S("bpmn:Task")(t,e),r=n.getScaledPath("TASK_TYPE_BUSINESS_RULE_HEADER",{abspos:{x:8,y:8}}),o=w(t,r);o.attr({strokeWidth:1,fill:"AAA"});var a=n.getScaledPath("TASK_TYPE_BUSINESS_RULE_MAIN",{abspos:{x:8,y:8}}),s=w(t,a);return s.attr({strokeWidth:1}),i},"bpmn:SubProcess":function(t,e,n){var i=S("bpmn:Activity")(t,e,n),r=L(e),o=f.isExpanded(r),a=!!r.triggeredByEvent;return a&&i.attr({strokeDasharray:"1,2"}),C(t,e,o?"center-top":"center-middle"),o?P(t,e):P(t,e,["SubProcessMarker"]),i},"bpmn:AdHocSubProcess":function(t,e){return S("bpmn:SubProcess")(t,e)},"bpmn:Transaction":function(t,n){var i=S("bpmn:SubProcess")(t,n),r=e.style(["no-fill","no-events"]);return x(t,n.width,n.height,q-2,H,r),i},"bpmn:CallActivity":function(t,e){return S("bpmn:Task")(t,e,{strokeWidth:5})},"bpmn:Participant":function(t,e){var n=S("bpmn:Lane")(t,e,{fill:"White"}),i=f.isExpanded(e);if(i){E(t,[{x:30,y:0},{x:30,y:e.height}]);var r=L(e).name;M(t,r,e)}else{var o=L(e).name;T(t,o,{box:e,align:"center-middle"})}var a=!!L(e).participantMultiplicity;return a&&S("ParticipantMultiplicityMarker")(t,e),n},"bpmn:Lane":function(t,e,n){var i=x(t,e.width,e.height,0,n||{fill:"none"}),r=L(e);if("bpmn:Lane"===r.$type){var o=r.name;M(t,o,e)}return i},"bpmn:InclusiveGateway":function(t,e){var n=b(t,e.width,e.height);return g(t,e.width,e.height,.24*e.height,{strokeWidth:2.5,fill:"none"}),n},"bpmn:ExclusiveGateway":function(t,e){var i=b(t,e.width,e.height),r=n.getScaledPath("GATEWAY_EXCLUSIVE",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:e.width,containerHeight:e.height,position:{mx:.32,my:.3}});return I(e).isMarkerVisible&&w(t,r,{strokeWidth:1,fill:"black"}),i},"bpmn:ComplexGateway":function(t,e){var i=b(t,e.width,e.height),r=n.getScaledPath("GATEWAY_COMPLEX",{xScaleFactor:.5,yScaleFactor:.5,containerWidth:e.width,containerHeight:e.height,position:{mx:.46,my:.26}});return w(t,r,{strokeWidth:1,fill:"black"}),i},"bpmn:ParallelGateway":function(t,e){var i=b(t,e.width,e.height),r=n.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.6,yScaleFactor:.6,containerWidth:e.width,containerHeight:e.height,position:{mx:.46,my:.2}});return w(t,r,{strokeWidth:1,fill:"black"}),i},"bpmn:EventBasedGateway":function(t,e){function i(){var i=n.getScaledPath("GATEWAY_EVENT_BASED",{xScaleFactor:.18,yScaleFactor:.18,containerWidth:e.width,containerHeight:e.height,position:{mx:.36,my:.44}});w(t,i,{strokeWidth:2,fill:"none"})}var r=L(e),o=b(t,e.width,e.height);g(t,e.width,e.height,.2*e.height,{strokeWidth:1,fill:"none"});var a=r.eventGatewayType,s=!!r.instantiate;if("Parallel"===a){var c=n.getScaledPath("GATEWAY_PARALLEL",{xScaleFactor:.4,yScaleFactor:.4,containerWidth:e.width,containerHeight:e.height,position:{mx:.474,my:.296}}),u=w(t,c);u.attr({strokeWidth:1,fill:"none"})}else if("Exclusive"===a){if(!s){var p=g(t,e.width,e.height,.26*e.height);p.attr({strokeWidth:1,fill:"none"})}i()}return o},"bpmn:Gateway":function(t,e){return b(t,e.width,e.height)},"bpmn:SequenceFlow":function(t,e){var n=R(e),i=w(t,n,{strokeLinejoin:"round",markerEnd:r("sequenceflow-end")}),o=L(e),a=e.source.businessObject;return o.conditionExpression&&a.$instanceOf("bpmn:Task")&&i.attr({markerStart:r("conditional-flow-marker")}),a["default"]&&a.$instanceOf("bpmn:Gateway")&&a["default"]===o&&i.attr({markerStart:r("conditional-default-flow-marker")}),i},"bpmn:Association":function(t,e,n){return n=s({strokeDasharray:"1,6",strokeLinecap:"round",strokeLinejoin:"round"},n||{}),E(t,e.waypoints,n)},"bpmn:DataInputAssociation":function(t,e){return S("bpmn:Association")(t,e,{markerEnd:r("data-association-end")})},"bpmn:DataOutputAssociation":function(t,e){return S("bpmn:Association")(t,e,{markerEnd:r("data-association-end")})},"bpmn:MessageFlow":function(t,e){var i=L(e),o=I(e),a=R(e),s=w(t,a,{markerEnd:r("messageflow-end"),markerStart:r("messageflow-start"),strokeDasharray:"10, 12",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5px"});if(i.messageRef){var c=s.getPointAtLength(s.getTotalLength()/2),u=n.getScaledPath("MESSAGE_FLOW_MARKER",{abspos:{x:c.x,y:c.y}}),p={strokeWidth:1};"initiating"===o.messageVisibleKind?(p.fill="white",p.stroke="black"):(p.fill="#888",p.stroke="white"),w(t,u,p)}return s},"bpmn:DataObject":function(t,e){var i=n.getScaledPath("DATA_OBJECT_PATH",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:.474,my:.296}}),r=w(t,i,{fill:"white"}),o=L(e);return O(o)&&B(t,e),r},"bpmn:DataObjectReference":_("bpmn:DataObject"),"bpmn:DataInput":function(t,e){var i=n.getRawPath("DATA_ARROW"),r=S("bpmn:DataObject")(t,e);return w(t,i,{strokeWidth:1}),r},"bpmn:DataOutput":function(t,e){var i=n.getRawPath("DATA_ARROW"),r=S("bpmn:DataObject")(t,e);return w(t,i,{strokeWidth:1,fill:"black"}),r},"bpmn:DataStoreReference":function(t,e){var i=n.getScaledPath("DATA_STORE",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:0,my:.133}}),r=w(t,i,{strokeWidth:2,fill:"white"});return r},"bpmn:BoundaryEvent":function(t,e){var n=L(e),i=n.cancelActivity,r={strokeWidth:1};i||(r.strokeDasharray="6",r.strokeLinecap="round");var o=S("bpmn:Event")(t,e,r);return g(t,e.width,e.height,H,s(r,{fill:"none"})),A(e,t),o},"bpmn:Group":function(t,e){return x(t,e.width,e.height,q,{strokeWidth:1,strokeDasharray:"8,3,1,3",fill:"none",pointerEvents:"none"})},label:function(t,e){return N(t,e,"")},"bpmn:TextAnnotation":function(t,e){var i={fill:"none",stroke:"none"},r=x(t,e.width,e.height,0,0,i),o=n.getScaledPath("TEXT_ANNOTATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:0,my:0}});w(t,o);var a=L(e).text||"";return T(t,a,{box:e,align:"left-middle",padding:5}),r},ParticipantMultiplicityMarker:function(t,e){var i=n.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:e.width/2/e.width,my:(e.height-15)/e.height}});w(t,i)},SubProcessMarker:function(t,e){var i=x(t,14,14,0,{strokeWidth:1});i.transform("translate("+(e.width/2-7.5)+","+(e.height-20)+")");var r=n.getScaledPath("MARKER_SUB_PROCESS",{xScaleFactor:1.5,yScaleFactor:1.5,containerWidth:e.width,containerHeight:e.height,position:{mx:(e.width/2-7.5)/e.width,my:(e.height-20)/e.height}});w(t,r)},ParallelMarker:function(t,e,i){var r=n.getScaledPath("MARKER_PARALLEL",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:(e.width/2+i.parallel)/e.width,my:(e.height-20)/e.height}});w(t,r)},SequentialMarker:function(t,e,i){var r=n.getScaledPath("MARKER_SEQUENTIAL",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:(e.width/2+i.seq)/e.width,my:(e.height-19)/e.height}});w(t,r)},CompensationMarker:function(t,e,i){var r=n.getScaledPath("MARKER_COMPENSATION",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:(e.width/2+i.compensation)/e.width,my:(e.height-13)/e.height}});w(t,r,{strokeWidth:1})},LoopMarker:function(t,e,i){var r=n.getScaledPath("MARKER_LOOP",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:(e.width/2+i.loop)/e.width,my:(e.height-7)/e.height}});w(t,r,{strokeWidth:1,fill:"none",strokeLinecap:"round",strokeMiterlimit:.5})},AdhocMarker:function(t,e,i){var r=n.getScaledPath("MARKER_ADHOC",{xScaleFactor:1,yScaleFactor:1,containerWidth:e.width,containerHeight:e.height,position:{mx:(e.width/2+i.adhoc)/e.width,my:(e.height-15)/e.height}});w(t,r,{strokeWidth:1,fill:"black"})}};t.on("canvas.init",function(t){y(t.svg)}),this.drawShape=D,this.drawConnection=k,this.getShapePath=U}var r=t(207),o=t(330),a=t(334),s=t(339),c=t(218),u=t(215),p=t(220),l=t(224),h=t(87),d=t(196),f=t(48),m=h.createLine;r(i,h),i.$inject=["eventBus","styles","pathMap"],e.exports=i},{196:196,207:207,215:215,218:218,220:220,224:224,330:330,334:334,339:339,48:48,87:87}],5:[function(t,e,n){"use strict";function i(){this.pathMap={EVENT_MESSAGE:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",height:36,width:36,heightElements:[6,14],widthElements:[10.5,21]},EVENT_SIGNAL:{d:"M {mx},{my} l {e.x0},{e.y0} l -{e.x1},0 Z",height:36,width:36,heightElements:[18],widthElements:[10,20]},EVENT_ESCALATION:{d:"m {mx},{my} c -{e.x1},{e.y0} -{e.x3},{e.y1} -{e.x5},{e.y4} {e.x1},-{e.y3} {e.x3},-{e.y5} {e.x5},-{e.y6} {e.x0},{e.y3} {e.x2},{e.y5} {e.x4},{e.y6} -{e.x0},-{e.y0} -{e.x2},-{e.y1} -{e.x4},-{e.y4} z",height:36,width:36,heightElements:[2.382,4.764,4.926,6.589333,7.146,13.178667,19.768],widthElements:[2.463,2.808,4.926,5.616,7.389,8.424]},EVENT_CONDITIONAL:{d:"M {e.x0},{e.y0} l {e.x1},0 l 0,{e.y2} l -{e.x1},0 Z M {e.x2},{e.y3} l {e.x0},0 M {e.x2},{e.y4} l {e.x0},0 M {e.x2},{e.y5} l {e.x0},0 M {e.x2},{e.y6} l {e.x0},0 M {e.x2},{e.y7} l {e.x0},0 M {e.x2},{e.y8} l {e.x0},0 ",height:36,width:36,heightElements:[8.5,14.5,18,11.5,14.5,17.5,20.5,23.5,26.5],widthElements:[10.5,14.5,12.5]},EVENT_LINK:{d:"m {mx},{my} 0,{e.y0} -{e.x1},0 0,{e.y1} {e.x1},0 0,{e.y0} {e.x0},-{e.y2} -{e.x0},-{e.y2} z",height:36,width:36,heightElements:[4.4375,6.75,7.8125],widthElements:[9.84375,13.5]},EVENT_ERROR:{d:"m {mx},{my} {e.x0},-{e.y0} {e.x1},-{e.y1} {e.x2},{e.y2} {e.x3},-{e.y3} -{e.x4},{e.y4} -{e.x5},-{e.y5} z",height:36,width:36,heightElements:[.023,8.737,8.151,16.564,10.591,8.714],widthElements:[.085,6.672,6.97,4.273,5.337,6.636]},EVENT_CANCEL_45:{d:"m {mx},{my} -{e.x1},0 0,{e.x0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:36,width:36,heightElements:[4.75,8.5],widthElements:[4.75,8.5]},EVENT_COMPENSATION:{d:"m {mx},{my} {e.x0},-{e.y0} 0,{e.y1} z m {e.x0},0 {e.x0},-{e.y0} 0,{e.y1} z",height:36,width:36,heightElements:[5,10],widthElements:[10]},EVENT_TIMER_WH:{d:"M {mx},{my} l {e.x0},-{e.y0} m -{e.x0},{e.y0} l {e.x1},{e.y1} ",height:36,width:36,heightElements:[10,2],widthElements:[3,7]},EVENT_TIMER_LINE:{d:"M {mx},{my} m {e.x0},{e.y0} l -{e.x1},{e.y1} ",height:36,width:36,heightElements:[10,3],widthElements:[0,0]},EVENT_MULTIPLE:{d:"m {mx},{my} {e.x1},-{e.y0} {e.x1},{e.y0} -{e.x0},{e.y1} -{e.x2},0 z",height:36,width:36,heightElements:[6.28099,12.56199],widthElements:[3.1405,9.42149,12.56198]},EVENT_PARALLEL_MULTIPLE:{d:"m {mx},{my} {e.x0},0 0,{e.y1} {e.x1},0 0,{e.y0} -{e.x1},0 0,{e.y1} -{e.x0},0 0,-{e.y1} -{e.x1},0 0,-{e.y0} {e.x1},0 z",height:36,width:36,heightElements:[2.56228,7.68683],widthElements:[2.56228,7.68683]},GATEWAY_EXCLUSIVE:{d:"m {mx},{my} {e.x0},{e.y0} {e.x1},{e.y0} {e.x2},0 {e.x4},{e.y2} {e.x4},{e.y1} {e.x2},0 {e.x1},{e.y3} {e.x0},{e.y3} {e.x3},0 {e.x5},{e.y1} {e.x5},{e.y2} {e.x3},0 z",height:17.5,width:17.5,heightElements:[8.5,6.5312,-6.5312,-8.5],widthElements:[6.5,-6.5,3,-3,5,-5]},GATEWAY_PARALLEL:{d:"m {mx},{my} 0,{e.y1} -{e.x1},0 0,{e.y0} {e.x1},0 0,{e.y1} {e.x0},0 0,-{e.y1} {e.x1},0 0,-{e.y0} -{e.x1},0 0,-{e.y1} -{e.x0},0 z",height:30,width:30,heightElements:[5,12.5],widthElements:[5,12.5]},GATEWAY_EVENT_BASED:{d:"m {mx},{my} {e.x0},{e.y0} {e.x0},{e.y1} {e.x1},{e.y2} {e.x2},0 z",height:11,width:11,heightElements:[-6,6,12,-12],widthElements:[9,-3,-12]},GATEWAY_COMPLEX:{d:"m {mx},{my} 0,{e.y0} -{e.x0},-{e.y1} -{e.x1},{e.y2} {e.x0},{e.y1} -{e.x2},0 0,{e.y3} {e.x2},0  -{e.x0},{e.y1} l {e.x1},{e.y2} {e.x0},-{e.y1} 0,{e.y0} {e.x3},0 0,-{e.y0} {e.x0},{e.y1} {e.x1},-{e.y2} -{e.x0},-{e.y1} {e.x2},0 0,-{e.y3} -{e.x2},0 {e.x0},-{e.y1} -{e.x1},-{e.y2} -{e.x0},{e.y1} 0,-{e.y0} -{e.x3},0 z",height:17.125,width:17.125,heightElements:[4.875,3.4375,2.125,3],widthElements:[3.4375,2.125,4.875,3]},DATA_OBJECT_PATH:{d:"m 0,0 {e.x1},0 {e.x0},{e.y0} 0,{e.y1} -{e.x2},0 0,-{e.y2} {e.x1},0 0,{e.y0} {e.x0},0",height:61,width:51,heightElements:[10,50,60],widthElements:[10,40,50,60]},DATA_OBJECT_COLLECTION_PATH:{d:"m {mx}, {my} m  0 15  l 0 -15 m  4 15  l 0 -15 m  4 15  l 0 -15 ",height:61,width:51,heightElements:[12],widthElements:[1,6,12,15]},DATA_ARROW:{d:"m 5,9 9,0 0,-3 5,5 -5,5 0,-3 -9,0 z",height:61,width:51,heightElements:[],widthElements:[]},DATA_STORE:{d:"m  {mx},{my} l  0,{e.y2} c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 l  0,-{e.y2} c -{e.x0},-{e.y1} -{e.x1},-{e.y1} -{e.x2},0c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0 m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1} {e.x2},0m  -{e.x2},{e.y0}c  {e.x0},{e.y1} {e.x1},{e.y1}  {e.x2},0",height:61,width:61,heightElements:[7,10,45],widthElements:[2,58,60]},TEXT_ANNOTATION:{d:"m {mx}, {my} m 10,0 l -10,0 l 0,{e.y0} l 10,0",height:30,width:10,heightElements:[30],widthElements:[10]},MARKER_SUB_PROCESS:{d:"m{mx},{my} m 7,2 l 0,10 m -5,-5 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_PARALLEL:{d:"m{mx},{my} m 3,2 l 0,10 m 3,-10 l 0,10 m 3,-10 l 0,10",height:10,width:10,heightElements:[],widthElements:[]},MARKER_SEQUENTIAL:{d:"m{mx},{my} m 0,3 l 10,0 m -10,3 l 10,0 m -10,3 l 10,0",height:10,width:10,heightElements:[],widthElements:[]},MARKER_COMPENSATION:{d:"m {mx},{my} 8,-5 0,10 z m 9,0 8,-5 0,10 z",height:10,width:21,heightElements:[],widthElements:[]},MARKER_LOOP:{d:"m {mx},{my} c 3.526979,0 6.386161,-2.829858 6.386161,-6.320661 0,-3.490806 -2.859182,-6.320661 -6.386161,-6.320661 -3.526978,0 -6.38616,2.829855 -6.38616,6.320661 0,1.745402 0.714797,3.325567 1.870463,4.469381 0.577834,0.571908 1.265885,1.034728 2.029916,1.35457 l -0.718163,-3.909793 m 0.718163,3.909793 -3.885211,0.802902",height:13.9,width:13.7,heightElements:[],widthElements:[]},MARKER_ADHOC:{d:"m {mx},{my} m 0.84461,2.64411 c 1.05533,-1.23780996 2.64337,-2.07882 4.29653,-1.97997996 2.05163,0.0805 3.85579,1.15803 5.76082,1.79107 1.06385,0.34139996 2.24454,0.1438 3.18759,-0.43767 0.61743,-0.33642 1.2775,-0.64078 1.7542,-1.17511 0,0.56023 0,1.12046 0,1.6807 -0.98706,0.96237996 -2.29792,1.62393996 -3.6918,1.66181996 -1.24459,0.0927 -2.46671,-0.2491 -3.59505,-0.74812 -1.35789,-0.55965 -2.75133,-1.33436996 -4.27027,-1.18121996 -1.37741,0.14601 -2.41842,1.13685996 -3.44288,1.96782996 z",height:4,width:15,heightElements:[],widthElements:[]},TASK_TYPE_SEND:{d:"m {mx},{my} l 0,{e.y1} l {e.x1},0 l 0,-{e.y1} z l {e.x0},{e.y0} l {e.x0},-{e.y0}",
height:14,width:21,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_SCRIPT:{d:"m {mx},{my} c 9.966553,-6.27276 -8.000926,-7.91932 2.968968,-14.938 l -8.802728,0 c -10.969894,7.01868 6.997585,8.66524 -2.968967,14.938 z m -7,-12 l 5,0 m -4.5,3 l 4.5,0 m -3,3 l 5,0m -4,3 l 5,0",height:15,width:12.6,heightElements:[6,14],widthElements:[10.5,21]},TASK_TYPE_USER_1:{d:"m {mx},{my} c 0.909,-0.845 1.594,-2.049 1.594,-3.385 0,-2.554 -1.805,-4.62199999 -4.357,-4.62199999 -2.55199998,0 -4.28799998,2.06799999 -4.28799998,4.62199999 0,1.348 0.974,2.562 1.89599998,3.405 -0.52899998,0.187 -5.669,2.097 -5.794,4.7560005 v 6.718 h 17 v -6.718 c 0,-2.2980005 -5.5279996,-4.5950005 -6.0509996,-4.7760005 zm -8,6 l 0,5.5 m 11,0 l 0,-5"},TASK_TYPE_USER_2:{d:"m {mx},{my} m 2.162,1.009 c 0,2.4470005 -2.158,4.4310005 -4.821,4.4310005 -2.66499998,0 -4.822,-1.981 -4.822,-4.4310005 "},TASK_TYPE_USER_3:{d:"m {mx},{my} m -6.9,-3.80 c 0,0 2.25099998,-2.358 4.27399998,-1.177 2.024,1.181 4.221,1.537 4.124,0.965 -0.098,-0.57 -0.117,-3.79099999 -4.191,-4.13599999 -3.57499998,0.001 -4.20799998,3.36699999 -4.20699998,4.34799999 z"},TASK_TYPE_MANUAL:{d:"m {mx},{my} c 0.234,-0.01 5.604,0.008 8.029,0.004 0.808,0 1.271,-0.172 1.417,-0.752 0.227,-0.898 -0.334,-1.314 -1.338,-1.316 -2.467,-0.01 -7.886,-0.004 -8.108,-0.004 -0.014,-0.079 0.016,-0.533 0,-0.61 0.195,-0.042 8.507,0.006 9.616,0.002 0.877,-0.007 1.35,-0.438 1.353,-1.208 0.003,-0.768 -0.479,-1.09 -1.35,-1.091 -2.968,-0.002 -9.619,-0.013 -9.619,-0.013 v -0.591 c 0,0 5.052,-0.016 7.225,-0.016 0.888,-0.002 1.354,-0.416 1.351,-1.193 -0.006,-0.761 -0.492,-1.196 -1.361,-1.196 -3.473,-0.005 -10.86,-0.003 -11.0829995,-0.003 -0.022,-0.047 -0.045,-0.094 -0.069,-0.139 0.3939995,-0.319 2.0409995,-1.626 2.4149995,-2.017 0.469,-0.4870005 0.519,-1.1650005 0.162,-1.6040005 -0.414,-0.511 -0.973,-0.5 -1.48,-0.236 -1.4609995,0.764 -6.5999995,3.6430005 -7.7329995,4.2710005 -0.9,0.499 -1.516,1.253 -1.882,2.19 -0.37000002,0.95 -0.17,2.01 -0.166,2.979 0.004,0.718 -0.27300002,1.345 -0.055,2.063 0.629,2.087 2.425,3.312 4.859,3.318 4.6179995,0.014 9.2379995,-0.139 13.8569995,-0.158 0.755,-0.004 1.171,-0.301 1.182,-1.033 0.012,-0.754 -0.423,-0.969 -1.183,-0.973 -1.778,-0.01 -5.824,-0.004 -6.04,-0.004 10e-4,-0.084 0.003,-0.586 10e-4,-0.67 z"},TASK_TYPE_INSTANTIATING_SEND:{d:"m {mx},{my} l 0,8.4 l 12.6,0 l 0,-8.4 z l 6.3,3.6 l 6.3,-3.6"},TASK_TYPE_SERVICE:{d:"m {mx},{my} v -1.71335 c 0.352326,-0.0705 0.703932,-0.17838 1.047628,-0.32133 0.344416,-0.14465 0.665822,-0.32133 0.966377,-0.52145 l 1.19431,1.18005 1.567487,-1.57688 -1.195028,-1.18014 c 0.403376,-0.61394 0.683079,-1.29908 0.825447,-2.01824 l 1.622133,-0.01 v -2.2196 l -1.636514,0.01 c -0.07333,-0.35153 -0.178319,-0.70024 -0.323564,-1.04372 -0.145244,-0.34406 -0.321407,-0.6644 -0.522735,-0.96217 l 1.131035,-1.13631 -1.583305,-1.56293 -1.129598,1.13589 c -0.614052,-0.40108 -1.302883,-0.68093 -2.022633,-0.82247 l 0.0093,-1.61852 h -2.241173 l 0.0042,1.63124 c -0.353763,0.0736 -0.705369,0.17977 -1.049785,0.32371 -0.344415,0.14437 -0.665102,0.32092 -0.9635006,0.52046 l -1.1698628,-1.15823 -1.5667691,1.5792 1.1684265,1.15669 c -0.4026573,0.61283 -0.68308,1.29797 -0.8247287,2.01713 l -1.6588041,0.003 v 2.22174 l 1.6724648,-0.006 c 0.073327,0.35077 0.1797598,0.70243 0.3242851,1.04472 0.1452428,0.34448 0.3214064,0.6644 0.5227339,0.96066 l -1.1993431,1.19723 1.5840256,1.56011 1.1964668,-1.19348 c 0.6140517,0.40346 1.3028827,0.68232 2.0233517,0.82331 l 7.19e-4,1.69892 h 2.226848 z m 0.221462,-3.9957 c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_SERVICE_FILL:{d:"m {mx},{my} c -1.788948,0.7502 -3.8576,-0.0928 -4.6097055,-1.87438 -0.7521065,-1.78321 0.090598,-3.84627 1.8802645,-4.59604 1.78823,-0.74936 3.856881,0.0929 4.608987,1.87437 0.752106,1.78165 -0.0906,3.84612 -1.879546,4.59605 z"},TASK_TYPE_BUSINESS_RULE_HEADER:{d:"m {mx},{my} 0,4 20,0 0,-4 z"},TASK_TYPE_BUSINESS_RULE_MAIN:{d:"m {mx},{my} 0,12 20,0 0,-12 zm 0,8 l 20,0 m -13,-4 l 0,8"},MESSAGE_FLOW_MARKER:{d:"m {mx},{my} m -10.5 ,-7 l 0,14 l 21,0 l 0,-14 z l 10.5,6 l 10.5,-6"}},this.getRawPath=function(t){return this.pathMap[t].d},this.getScaledPath=function(t,e){var n,i,o=this.pathMap[t];e.abspos?(n=e.abspos.x,i=e.abspos.y):(n=e.containerWidth*e.position.mx,i=e.containerHeight*e.position.my);var a={};if(e.position){for(var s=e.containerHeight/o.height*e.yScaleFactor,c=e.containerWidth/o.width*e.xScaleFactor,u=0;u<o.heightElements.length;u++)a["y"+u]=o.heightElements[u]*s;for(var p=0;p<o.widthElements.length;p++)a["x"+p]=o.widthElements[p]*c}var l=r.format(o.d,{mx:n,my:i,e:a});return l}}var r=t(200);e.exports=i},{200:200}],6:[function(t,e,n){e.exports={renderer:["type",t(4)],pathMap:["type",t(5)]}},{4:4,5:5}],7:[function(t,e,n){"use strict";function i(t,e,n,i,r,o,a){t.registerProvider(this),this._contextPad=t,this._modeling=e,this._elementFactory=n,this._connect=i,this._create=r,this._bpmnReplace=o,this._canvas=a}function r(t,e,n){var i=t.$instanceOf(e),r=!1,o=t.eventDefinitions||[];return a(o,function(t){t.$type===n&&(r=!0)}),i&&r}var o=t(339),a=t(218),s=t(50).is;i.$inject=["contextPad","modeling","elementFactory","connect","create","bpmnReplace","canvas"],i.prototype.getContextPadEntries=function(t){function e(t,e,n){l.start(t,e,n)}function n(e){t.waypoints?u.removeConnection(t):u.removeShape(t)}function i(t){var e=5,n=f.getContainer(),i=c.getPad(t).html,r=n.getBoundingClientRect(),o=i.getBoundingClientRect(),a=o.top-r.top,s=o.left-r.left,u={x:s,y:a+o.height+e};return u}function a(t,e,n){function i(e,i){var r=p.createShape(o({type:t},n));h.start(e,r,i)}var r=t.replace(/^bpmn\:/,"");return{group:"model",className:e,title:"Append "+r,action:{dragstart:i,click:i}}}var c=this._contextPad,u=this._modeling,p=this._elementFactory,l=this._connect,h=this._create,d=this._bpmnReplace,f=this._canvas,m={};if("label"===t.type)return m;var y=t.businessObject;return s(y,"bpmn:FlowNode")&&(s(y,"bpmn:EndEvent")||s(y,"bpmn:EventBasedGateway")||r(y,"bpmn:IntermediateThrowEvent","bpmn:LinkEventDefinition")||o(m,{"append.end-event":a("bpmn:EndEvent","icon-end-event-none"),"append.gateway":a("bpmn:ExclusiveGateway","icon-gateway-xor"),"append.append-task":a("bpmn:Task","icon-task"),"append.intermediate-event":a("bpmn:IntermediateThrowEvent","icon-intermediate-event-none")}),s(y,"bpmn:EventBasedGateway")&&o(m,{"append.receive-task":a("bpmn:ReceiveTask","icon-receive-task"),"append.message-intermediate-event":a("bpmn:IntermediateCatchEvent","icon-intermediate-event-catch-message",{_eventDefinitionType:"bpmn:MessageEventDefinition"}),"append.timer-intermediate-event":a("bpmn:IntermediateCatchEvent","icon-intermediate-event-catch-timer",{_eventDefinitionType:"bpmn:TimerEventDefinition"}),"append.condtion-intermediate-event":a("bpmn:IntermediateCatchEvent","icon-intermediate-event-catch-condition",{_eventDefinitionType:"bpmn:ConditionalEventDefinition"}),"append.signal-intermediate-event":a("bpmn:IntermediateCatchEvent","icon-intermediate-event-catch-signal",{_eventDefinitionType:"bpmn:SignalEventDefinition"})}),o(m,{replace:{group:"edit",className:"icon-screw-wrench",title:"Change type",action:{click:function(t,e){d.openChooser(i(e),e)}}}})),(s(y,"bpmn:FlowNode")||s(y,"bpmn:InteractionNode"))&&o(m,{"append.text-annotation":a("bpmn:TextAnnotation","icon-text-annotation"),connect:{group:"connect",className:"icon-connection-multi",title:"Connect using Sequence/MessageFlow",action:{click:e,dragstart:e}}}),o(m,{"delete":{group:"edit",className:"icon-trash",title:"Remove",action:{click:n,dragstart:n}}}),m},e.exports=i},{218:218,339:339,50:50}],8:[function(t,e,n){e.exports={__depends__:[t(73),t(102),t(159),t(100),t(104),t(39)],__init__:["contextPadProvider"],contextPadProvider:["type",t(7)]}},{100:100,102:102,104:104,159:159,39:39,7:7,73:73}],9:[function(t,e,n){"use strict";function i(t,e,n,i,r,o,a){t.addListener(function(s,c){if(65===s&&t.isCmd(c)){var u=o.getRootElement(),p=a.filter(function(t){return t!=u});return r.select(p),!0}if(!t.hasModifier(c)){if(83===s)return e.activateSelection(),!0;if(76===s)return n.activateSelection(),!0;var l=r.get();return 69===s?(l.length&&i.activate(l[0]),!0):void 0}})}i.$inject=["keyboard","spaceTool","lassoTool","directEditing","selection","canvas","elementRegistry"],e.exports=i},{}],10:[function(t,e,n){e.exports={__depends__:[t(110)],__init__:["bpmnKeyBindings"],bpmnKeyBindings:["type",t(9)]}},{110:110,9:9}],11:[function(t,e,n){"use strict";function i(t,e,n,i,o){n.registerProvider(this),i.registerHandler("element.updateLabel",r),t.on("element.dblclick",function(t){n.activate(t.element)}),t.on(["element.mousedown","drag.activate","canvas.viewbox.changed"],function(t){n.complete()}),t.on(["commandStack.changed"],function(){n.cancel()}),"ontouchstart"in document.documentElement||t.on("create.end",500,function(t){var e=t.shape,i=t.context.canExecute;i&&(a(e,"bpmn:Task")||a(e,"bpmn:TextAnnotation")||a(e,"bpmn:SubProcess")&&!s(e))&&n.activate(e)}),this._canvas=e,this._commandStack=i}var r=t(13),o=t(12),a=t(50).is,s=t(48).isExpanded,c={width:150,height:50};i.$inject=["eventBus","canvas","directEditing","commandStack","injector"],e.exports=i,i.prototype.activate=function(t){var e=o.getLabel(t);if(void 0!==e){var n=this.getEditingBBox(t);return(a(t,"bpmn:Participant")&&s(t)||a(t,"bpmn:Lane"))&&(n.width=c.width,n.height=c.height,n.x=n.x+10-n.width/2,n.y=n.mid.y-n.height/2),a(t,"bpmn:SubProcess")&&s(t)&&(n.height=c.height,n.x=n.mid.x-n.width/2,n.y=n.y+10-n.height/2),{bounds:n,text:e}}},i.prototype.getEditingBBox=function(t,e){var n=t.label||t,i=this._canvas.getAbsoluteBBox(n),r={x:i.x+i.width/2,y:i.y+i.height/2};return n.labelTarget&&(i.width=Math.max(i.width,c.width),i.height=Math.max(i.height,c.height),i.x=r.x-i.width/2),i.mid=r,i},i.prototype.update=function(t,e){this._commandStack.execute("element.updateLabel",{element:t,newLabel:e})}},{12:12,13:13,48:48,50:50}],12:[function(t,e,n){"use strict";function i(t){return t.$instanceOf("bpmn:FlowElement")||t.$instanceOf("bpmn:Participant")||t.$instanceOf("bpmn:Lane")||t.$instanceOf("bpmn:SequenceFlow")||t.$instanceOf("bpmn:MessageFlow")?"name":t.$instanceOf("bpmn:TextAnnotation")?"text":void 0}e.exports.getLabel=function(t){var e=t.businessObject,n=i(e);return n?e[n]||"":void 0},e.exports.setLabel=function(t,e){var n=t.businessObject,r=i(n);r&&(n[r]=e);var o=t.label||t;return o.hidden=!1,o}},{}],13:[function(t,e,n){"use strict";function i(t){function e(e,n){var i=r.setLabel(e,n);t.fire("element.changed",{element:i})}function n(t){return t.oldLabel=r.getLabel(t.element),e(t.element,t.newLabel)}function i(t){return e(t.element,t.oldLabel)}function o(t){return!0}this.execute=n,this.revert=i,this.canExecute=o}var r=t(12);i.$inject=["eventBus"],e.exports=i},{12:12}],14:[function(t,e,n){e.exports={__depends__:[t(80),t(98),t(73)],__init__:["labelEditingProvider"],labelEditingProvider:["type",t(11)]}},{11:11,73:73,80:80,98:98}],15:[function(t,e,n){"use strict";function i(t){this._model=t}var r=t(221),o=t(339),a=t(345);i.$inject=["moddle"],i.prototype._needsId=function(t){return t.$instanceOf("bpmn:RootElement")||t.$instanceOf("bpmn:FlowElement")||t.$instanceOf("bpmn:MessageFlow")||t.$instanceOf("bpmn:Artifact")||t.$instanceOf("bpmn:Participant")||t.$instanceOf("bpmn:Process")||t.$instanceOf("bpmn:Collaboration")||t.$instanceOf("bpmndi:BPMNShape")||t.$instanceOf("bpmndi:BPMNEdge")||t.$instanceOf("bpmndi:BPMNDiagram")||t.$instanceOf("bpmndi:BPMNPlane")},i.prototype._ensureId=function(t){var e=(t.$type||"").replace(/^[^:]*:/g,"")+"_";!t.id&&this._needsId(t)&&(t.id=this._model.ids.nextPrefixed(e,t))},i.prototype.create=function(t,e){var n=this._model.create(t,e||{});return this._ensureId(n),n},i.prototype.createDiLabel=function(){return this.create("bpmndi:BPMNLabel",{bounds:this.createDiBounds()})},i.prototype.createDiShape=function(t,e,n){return this.create("bpmndi:BPMNShape",o({bpmnElement:t,bounds:this.createDiBounds(e)},n))},i.prototype.createDiBounds=function(t){return this.create("dc:Bounds",t)},i.prototype.createDiWaypoints=function(t){return r(t,function(t){return this.createDiWaypoint(t)},this)},i.prototype.createDiWaypoint=function(t){return this.create("dc:Point",a(t,["x","y"]))},i.prototype.createDiEdge=function(t,e,n){return this.create("bpmndi:BPMNEdge",o({bpmnElement:t},n))},i.prototype.createDiPlane=function(t){return this.create("bpmndi:BPMNPlane",{bpmnElement:t})},e.exports=i},{221:221,339:339,345:345}],16:[function(t,e,n){"use strict";function i(t,e,n){u.call(this,t),this.postExecute(["shape.create","connection.create"],function(t){var n,i=t.context,r=i.shape||i.connection,o=r.businessObject;s(o)&&(n=c(r),e.createLabel(r,n,{id:o.id+"_label",businessObject:o}))}),this.executed(["label.create","shape.moved"],function(t){var e=t.context.shape,i=e.businessObject,o=i.di;e.labelTarget&&(o.label||(o.label=n.create("bpmndi:BPMNLabel",{bounds:n.create("dc:Bounds")})),r(o.label.bounds,{x:e.x,y:e.y,width:e.width,height:e.height}))})}var r=t(339),o=t(207),a=t(49),s=a.hasExternalLabel,c=a.getExternalLabelMid,u=t(78);o(i,u),i.$inject=["eventBus","modeling","bpmnFactory"],e.exports=i},{207:207,339:339,49:49,78:78}],17:[function(t,e,n){"use strict";function i(){}function r(t){var e=t.host,n=-10;return h(l(t),e,n)}function o(t,e,n){var i=t&&t[e];return i?i.original||i:l(n)}var a=t(207),s=t(339),c=t(172),u=t(175),p=t(174),l=p.getMid,h=p.getOrientation,d=t(50).is;a(i,c),e.exports=i,i.prototype.layoutConnection=function(t,e){var n,i,a,c,p=t.source,l=t.target,h=t.waypoints;if(n=o(h,0,p),i=o(h,h&&h.length-1,l),d(t,"bpmn:MessageFlow"))a={preferredLayouts:["straight","v:v"]};else if(d(t,"bpmn:SequenceFlow"))if(d(p,"bpmn:BoundaryEvent")){var f=r(p);/left|right/.test(f)?a={preferredLayouts:["h:v"]}:/top|bottom/.test(f)&&(a={preferredLayouts:["v:h"]})}else a=d(p,"bpmn:Gateway")?{preferredLayouts:["v:h"]}:d(l,"bpmn:Gateway")?{preferredLayouts:["h:v"]}:{preferredLayouts:["h:h"]};return a&&(a=s(a,e),c=u.repairConnection(p,l,n,i,h,a)),c||[n,i]}},{172:172,174:174,175:175,207:207,339:339,50:50}],18:[function(t,e,n){"use strict";function i(t,e,n){function i(t){var e,i=t.context;i.cropped||(e=i.connection,e.waypoints=n.getCroppedWaypoints(e),i.cropped=!0)}function r(t){l.updateParent(t.context.shape||t.context.connection)}function o(t){var e=t.context,n=e.oldRoot,i=n.children;a(i,function(t){l.updateParent(t)})}function s(t){l.updateBounds(t.context.shape)}function c(t){l.updateConnection(t.context.connection)}function u(t){l.updateConnectionWaypoints(t.context.connection)}function p(t){l.updateAttachment(t.context)}h.call(this,t),this._bpmnFactory=e;var l=this;this.executed(["connection.layout","connection.create","connection.reconnectEnd","connection.reconnectStart"],i),this.reverted(["connection.layout"],function(t){delete t.context.cropped}),this.executed(["shape.move","shape.create","shape.delete","connection.create","connection.move","connection.delete"],r),this.reverted(["shape.move","shape.create","shape.delete","connection.create","connection.move","connection.delete"],r),this.executed(["canvas.updateRoot"],o),this.reverted(["canvas.updateRoot"],o),this.executed(["shape.move","shape.create","shape.resize"],s),this.reverted(["shape.move","shape.create","shape.resize"],s),this.executed(["connection.create","connection.move","connection.delete","connection.reconnectEnd","connection.reconnectStart"],c),this.reverted(["connection.create","connection.move","connection.delete","connection.reconnectEnd","connection.reconnectStart"],c),this.executed(["connection.layout","connection.move","connection.updateWaypoints","connection.reconnectEnd","connection.reconnectStart"],u),this.reverted(["connection.layout","connection.move","connection.updateWaypoints","connection.reconnectEnd","connection.reconnectStart"],u),this.executed(["shape.attach"],p),this.reverted(["shape.attach"],p)}function r(t){for(;t&&!l(t,"bpmn:Definitions");)t=t.$parent;return t}var o=t(339),a=t(218),s=t(207),c=t(184),u=t(176),p=t(50).getBusinessObject,l=t(50).is,h=t(78);s(i,h),e.exports=i,i.$inject=["eventBus","bpmnFactory","connectionDocking"],i.prototype.updateAttachment=function(t){var e=t.shape,n=e.businessObject,i=e.host;n.attachedToRef=i&&i.businessObject},i.prototype.updateParent=function(t){if(!(t instanceof u.Label)){var e=t.parent,n=t.businessObject,i=e&&e.businessObject,r=i&&i.di;this.updateSemanticParent(n,i),this.updateDiParent(n.di,r)}},i.prototype.updateBounds=function(t){var e=t.businessObject.di,n=t instanceof u.Label?this._getLabel(e).bounds:e.bounds;o(n,{x:t.x,y:t.y,width:t.width,height:t.height})},i.prototype.updateDiParent=function(t,e){if(e&&!l(e,"bpmndi:BPMNPlane")&&(e=e.$parent),t.$parent!==e){var n=(e||t.$parent).get("planeElement");e?(n.push(t),t.$parent=e):(c.remove(n,t),t.$parent=null)}},i.prototype.updateSemanticParent=function(t,e){var n;if(t.$parent!==e){if(l(t,"bpmn:FlowElement"))e&&l(e,"bpmn:Participant")&&(e=e.processRef),n="flowElements";else if(l(t,"bpmn:Artifact")){for(;e&&!l(e,"bpmn:Process")&&!l(e,"bpmn:SubProcess")&&!l(e,"bpmn:Collaboration");){if(l(e,"bpmn:Participant")){e=e.processRef;break}e=e.$parent}n="artifacts"}else if(l(t,"bpmn:MessageFlow"))n="messageFlows";else if(l(t,"bpmn:Participant")){n="participants";var i,o=t.processRef;o&&(i=r(t.$parent||e),t.$parent&&(c.remove(i.get("rootElements"),o),o.$parent=null),e&&(c.add(i.get("rootElements"),o),o.$parent=i))}if(!n)throw new Error("no parent for ",t,e);var a;t.$parent&&(a=t.$parent.get(n),c.remove(a,t)),e?(a=e.get(n),a.push(t),t.$parent=e):t.$parent=null}},i.prototype.updateConnectionWaypoints=function(t){t.businessObject.di.set("waypoint",this._bpmnFactory.createDiWaypoints(t.waypoints))},i.prototype.updateConnection=function(t){var e=p(t),n=p(t.source),i=p(t.target),r=l(e,"bpmn:SequenceFlow");e.sourceRef!==n&&(r&&(c.remove(e.sourceRef&&e.sourceRef.get("outgoing"),e),n&&n.get("outgoing")&&n.get("outgoing").push(e)),e.sourceRef=n),e.targetRef!==i&&(r&&(c.remove(e.targetRef&&e.targetRef.get("incoming"),e),i&&i.get("incoming")&&i.get("incoming").push(e)),e.targetRef=i),e.di.set("waypoint",this._bpmnFactory.createDiWaypoints(t.waypoints))},i.prototype._getLabel=function(t){return t.label||(t.label=this._bpmnFactory.createDiLabel()),t.label}},{176:176,184:184,207:207,218:218,339:339,50:50,78:78}],19:[function(t,e,n){"use strict";function i(t,e){a.call(this),this._bpmnFactory=t,this._moddle=e}var r=t(339),o=t(207),a=t(82),s=t(49);o(i,a),i.$inject=["bpmnFactory","moddle"],e.exports=i,i.prototype.baseCreate=a.prototype.create,i.prototype.create=function(t,e){if("label"===t)return this.baseCreate(t,r({type:"label"},s.DEFAULT_LABEL_SIZE,e));e=e||{};var n,i=e.businessObject;if(!i){if(!e.type)throw new Error("no shape type specified");i=this._bpmnFactory.create(e.type)}if(i.di||("root"===t?i.di=this._bpmnFactory.createDiPlane(i,[],{id:i.id+"_di"}):"connection"===t?i.di=this._bpmnFactory.createDiEdge(i,[],{id:i.id+"_di"}):i.di=this._bpmnFactory.createDiShape(i,{},{id:i.id+"_di"})),e.isExpanded&&(i.di.isExpanded=e.isExpanded),i.$instanceOf("bpmn:ExclusiveGateway")&&(i.di.isMarkerVisible=!0),e._eventDefinitionType){var o=i.get("eventDefinitions")||[],a=this._moddle.create(e._eventDefinitionType);o.push(a),i.eventDefinitions=o}return n=this._getDefaultSize(i),e=r({businessObject:i,id:i.id},n,e),this.baseCreate(t,e)},i.prototype._getDefaultSize=function(t){if(t.$instanceOf("bpmn:SubProcess")){var e=t.di.isExpanded===!0;return e?{width:350,height:200}:{width:100,height:80}}return t.$instanceOf("bpmn:Task")?{width:100,height:80}:t.$instanceOf("bpmn:Gateway")?{width:50,height:50}:t.$instanceOf("bpmn:Event")?{width:36,height:36}:t.$instanceOf("bpmn:Participant")?{width:600,height:250}:{width:100,height:80}},i.prototype.createParticipantShape=function(t){var e=this.createShape({type:"bpmn:Participant"});return t||(e.businessObject.processRef=this._bpmnFactory.create("bpmn:Process")),e}},{207:207,339:339,49:49,82:82}],20:[function(t,e,n){"use strict";function i(t,e,n,i){o.call(this,t,e,n),this._bpmnRules=i}var r=t(207),o=t(115),a=t(31),s=t(30);r(i,o),i.$inject=["eventBus","elementFactory","commandStack","bpmnRules"],e.exports=i,i.prototype.getHandlers=function(){var t=o.prototype.getHandlers.call(this);return t["element.updateProperties"]=a,t["canvas.updateRoot"]=s,t},i.prototype.updateLabel=function(t,e){this._commandStack.execute("element.updateLabel",{element:t,newLabel:e})};var c=t(21).getSharedParent;i.prototype.connect=function(t,e,n){var i=this._bpmnRules;return n||(n=i.canConnectMessageFlow(t,e)?{type:"bpmn:MessageFlow"}:i.canConnectSequenceFlow(t,e)?{type:"bpmn:SequenceFlow"}:{type:"bpmn:Association"}),this.createConnection(t,e,n,c(t,e))},i.prototype.updateProperties=function(t,e){this._commandStack.execute("element.updateProperties",{element:t,properties:e})},i.prototype.makeCollaboration=function(){var t=this._create("root",{type:"bpmn:Collaboration"}),e={newRoot:t};return this._commandStack.execute("canvas.updateRoot",e),t},i.prototype.makeProcess=function(){var t=this._create("root",{type:"bpmn:Process"}),e={newRoot:t};this._commandStack.execute("canvas.updateRoot",e)}},{115:115,207:207,21:21,30:30,31:31}],21:[function(t,e,n){"use strict";function i(t){for(var e=[];t;)t=t.parent,t&&e.push(t);return e}function r(t,e){var n=i(t),r=i(e);return o(n,function(t){return-1!==r.indexOf(t)})}var o=t(217);e.exports.getParents=i,e.exports.getSharedParent=r},{217:217}],22:[function(t,e,n){"use strict";function i(t,e,n){a.call(this,t),this.preExecute("shape.append",function(t){var e=t.source,n=t.shape;t.position||(o(n,"bpmn:TextAnnotation")?t.position={x:e.x+e.width/2+75,y:e.y-50-n.height/2}:t.position={x:e.x+e.width+80+n.width/2,y:e.y+e.height/2})},!0)}var r=t(207),o=t(50).is,a=t(78);i.$inject=["eventBus","elementFactory","bpmnRules"],r(i,a),e.exports=i},{207:207,50:50,78:78}],23:[function(t,e,n){"use strict";function i(t,e,n,i){o.call(this,t),this.preExecute("shape.create",function(t){var e,r,o=t.shape,s=t.host,c={cancelActivity:!0};s&&a(o,"bpmn:IntermediateThrowEvent")&&(c.attachedToRef=s.businessObject,e=i.create("bpmn:BoundaryEvent",c),r={type:"bpmn:BoundaryEvent",businessObject:e},t.shape=n.createShape(r))},!0)}var r=t(207),o=t(78),a=t(50).is;i.$inject=["eventBus","modeling","elementFactory","bpmnFactory"],r(i,o),e.exports=i},{207:207,50:50,78:78}],24:[function(t,e,n){"use strict";function i(t){return a({},t)}function r(t,e,n){s.call(this,t),this.preExecute("shape.create",function(t){var n=t.parent,i=t.shape;e.canInsert(i,n)&&(t.targetFlow=n,t.parent=n.parent)},!0),this.postExecute("shape.create",function(t){var r,o,a,s,u,p,l,h,d=t.shape,f=t.targetFlow,m=t.position;f&&(u=f.waypoints,s=c(u,m),s&&(p=u.slice(0,s.index),l=u.slice(s.index+(s.bendpoint?1:0)),h=s.bendpoint?u[s.index]:m,p.push(i(h)),l.unshift(i(h))),r=f.source,o=f.target,e.canConnect(r,d,f)&&(n.reconnectEnd(f,d,p||i(m)),a=!0),e.canConnect(d,o,f)&&(a?n.connect(d,o,{type:f.type,waypoints:l}):n.reconnectStart(f,d,l||i(m))))},!0)}var o=t(207),a=t(339),s=t(78),c=t(191).getApproxIntersection;o(r,s),r.$inject=["eventBus","bpmnRules","modeling"],e.exports=r},{191:191,207:207,339:339,78:78}],25:[function(t,e,n){"use strict";function i(t,e,n,i){o.call(this,t),this.preExecute("shape.create",function(t){var n=t.parent,i=t.shape,r=t.position;if(a(n,"bpmn:Process")&&a(i,"bpmn:Participant")){var o=e.makeCollaboration();t.position=r,t.parent=o,t.processRoot=n}},!0),this.execute("shape.create",function(t){var e=t.processRoot,n=t.shape;e&&(t.oldProcessRef=n.businessObject.processRef,n.businessObject.processRef=e.businessObject)},!0),this.revert("shape.create",function(t){var e=t.processRoot,n=t.shape;e&&(n.businessObject.processRef=t.oldProcessRef)},!0),this.postExecute("shape.create",function(t){var n=t.processRoot,i=t.shape;if(n){var r=n.children.slice();e.moveShapes(r,{x:0,y:0},i)}},!0)}var r=t(207),o=t(78),a=t(50).is;i.$inject=["eventBus","modeling","elementFactory","bpmnFactory"],r(i,o),e.exports=i},{207:207,50:50,78:78}],26:[function(t,e,n){"use strict";function i(t,e){function n(t,n){e.add({position:{x:t.x+5,y:t.y+5},type:"error",timeout:2e3,html:"<div>"+n+"</div>"})}t.on(["shape.move.rejected","create.rejected"],function(t){var e=t.context,i=e.shape,o=e.target;r(o,"bpmn:Collaboration")&&r(i,"bpmn:FlowNode")&&n(t,"flow elements must be children of pools/participants")})}var r=t(50).is;i.$inject=["eventBus","tooltips"],e.exports=i},{50:50}],27:[function(t,e,n){"use strict";function i(t,e){o.call(this,t),this.preExecute("shape.delete",function(t){var e=t.shape,n=e.parent;a(e,"bpmn:Participant")&&(t.collaborationRoot=n)},!0),this.postExecute("shape.delete",function(t){var n=t.collaborationRoot;n&&!n.businessObject.participants.length&&e.makeProcess()},!0)}var r=t(207),o=t(78),a=t(50).is;i.$inject=["eventBus","modeling"],r(i,o),e.exports=i},{207:207,50:50,78:78}],28:[function(t,e,n){"use strict";function i(t,e,n){function i(t){var i,r,o=t.source,a=t.target;s(t,"bpmn:SequenceFlow")&&(n.canConnectSequenceFlow(o,a)||(r=!0),n.canConnectMessageFlow(o,a)&&(i="bpmn:MessageFlow")),s(t,"bpmn:MessageFlow")&&(n.canConnectMessageFlow(o,a)||(r=!0),n.canConnectSequenceFlow(o,a)&&(i="bpmn:SequenceFlow")),s(t,"bpmn:Association")&&!n.canConnectAssociation(o,a)&&(r=!0),r&&e.removeConnection(t),i&&e.createConnection(o,a,{type:i,waypoints:t.waypoints.slice()},c(o,a))}a.call(this,t),this.postExecuted("shapes.move",function(t){var e=t.closure,n=e.allConnections;r(n,i)},!0),this.postExecuted(["connection.reconnectStart","connection.reconnectEnd"],function(t){var e=t.context.connection;i(e)})}var r=t(218),o=t(207),a=t(78),s=t(50).is,c=t(21).getSharedParent;o(i,a),i.$inject=["eventBus","modeling","bpmnRules"],e.exports=i},{207:207,21:21,218:218,50:50,78:78}],29:[function(t,e,n){e.exports={__init__:["appendBehavior","createParticipantBehavior","createBoundaryEventBehavior","createOnFlowBehavior","replaceConnectionBehavior","removeBehavior","modelingFeedback"],appendBehavior:["type",t(22)],createParticipantBehavior:["type",t(25)],createBoundaryEventBehavior:["type",t(23)],createOnFlowBehavior:["type",t(24)],replaceConnectionBehavior:["type",t(28)],removeBehavior:["type",t(27)],modelingFeedback:["type",t(26)]}},{22:22,23:23,24:24,25:25,26:26,27:27,28:28}],30:[function(t,e,n){"use strict";function i(t,e){this._canvas=t,this._modeling=e}var r=t(184);i.$inject=["canvas","modeling"],e.exports=i,i.prototype.execute=function(t){var e=this._canvas,n=t.newRoot,i=n.businessObject,o=e.getRootElement(),a=o.businessObject,s=a.$parent,c=a.di;e.setRootElement(n,!0),r.add(s.rootElements,i),i.$parent=s,r.remove(s.rootElements,a),a.$parent=null,a.di=null,c.bpmnElement=i,i.di=c,t.oldRoot=o},i.prototype.revert=function(t){var e=this._canvas,n=t.newRoot,i=n.businessObject,o=t.oldRoot,a=o.businessObject,s=i.$parent,c=i.di;e.setRootElement(o,!0),r.remove(s.rootElements,i),i.$parent=null,r.add(s.rootElements,a),a.$parent=s,i.di=null,c.bpmnElement=a,a.di=c}},{184:184}],31:[function(t,e,n){"use strict";function i(t){this._elementRegistry=t}function r(t,e){return a(e,function(e,n){return e[n]=t.get(n),e},{})}function o(t,e){c(e,function(e,n){t.set(n,e)})}var a=t(346),s=t(340),c=t(218),u="default",p="name",l="id";i.$inject=["elementRegistry"],e.exports=i,i.prototype.execute=function(t){var e=t.element,n=[e];if(!e)throw new Error("element required");var i=this._elementRegistry,a=e.businessObject,c=t.properties,h=t.oldProperties||r(a,s(c));return l in c&&i.updateId(e,c[l]),u in c&&(c[u]&&n.push(i.get(c[u].id)),a[u]&&n.push(i.get(a[u].id))),p in c&&e.label&&n.push(e.label),o(a,c),t.oldProperties=h,t.changed=n,n},i.prototype.revert=function(t){var e=t.element,n=t.oldProperties,i=e.businessObject,r=this._elementRegistry;return o(i,n),l in n&&r.updateId(e,n[l]),t.changed}},{218:218,340:340,346:346}],32:[function(t,e,n){e.exports={__init__:["modeling","bpmnUpdater","bpmnLabelSupport"],__depends__:[t(14),t(34),t(29),t(80),t(168),t(112),t(91),t(159),t(98)],bpmnFactory:["type",t(15)],bpmnUpdater:["type",t(18)],elementFactory:["type",t(19)],modeling:["type",t(20)],bpmnLabelSupport:["type",t(16)],layouter:["type",t(17)],connectionDocking:["type",t(173)]}},{112:112,14:14,15:15,159:159,16:16,168:168,17:17,173:173,18:18,19:19,20:20,29:29,34:34,80:80,91:91,98:98}],33:[function(t,e,n){"use strict";function i(t){$.call(this,t)}function r(t){return!t||v(t)}function o(t,e){return t===e}function a(t){for(var e=z(t);e&&!F(e,"bpmn:Process");){if(F(e,"bpmn:Participant"))return e.processRef||e;e=e.$parent}return e}function s(t,e){var n=a(t),i=a(e);return n===i}function c(t){return F(t,"bpmn:InteractionNode")&&(!F(t,"bpmn:Event")||F(t,"bpmn:ThrowEvent")&&d(t,"bpmn:MessageEventDefinition"))}function u(t){return F(t,"bpmn:InteractionNode")&&(!F(t,"bpmn:Event")||F(t,"bpmn:CatchEvent")&&d(t,"bpmn:MessageEventDefinition"))}function p(t){var e=z(t);if(F(e,"bpmn:Participant"))return null;for(;e;)if(e=e.$parent,F(e,"bpmn:FlowElementsContainer"))return e;return e}function l(t,e){var n=p(t),i=p(e);return n&&n===i}function h(t,e){var n=z(t);return!!O(n.eventDefinitions||[],function(t){return F(t,e)})}function d(t,e){var n=z(t);return(n.eventDefinitions||[]).every(function(t){return F(t,e)})}function f(t){return F(t,"bpmn:FlowNode")&&!F(t,"bpmn:EndEvent")&&!(F(t,"bpmn:IntermediateThrowEvent")&&h(t,"bpmn:LinkEventDefinition"))}function m(t){return F(t,"bpmn:FlowNode")&&!F(t,"bpmn:StartEvent")&&!F(t,"bpmn:BoundaryEvent")&&!(F(t,"bpmn:IntermediateCatchEvent")&&h(t,"bpmn:LinkEventDefinition"))}function y(t){return F(t,"bpmn:ReceiveTask")||F(t,"bpmn:IntermediateCatchEvent")&&(h(t,"bpmn:MessageEventDefinition")||h(t,"bpmn:TimerEventDefinition")||h(t,"bpmn:ConditionalEventDefinition")||h(t,"bpmn:SignalEventDefinition"))}function v(t){return t.labelTarget}function g(t){return t.waypoints}function x(t,e){var n=j(e);return-1!==n.indexOf(t)}function b(t,e,n){return r(t)||r(e)?null:o(t,e)?!1:M(t,e)||R(t,e)?!0:F(n,"bpmn:Association")?N(t,e):!1}function E(t,e){return v(t)&&!g(e)?!0:F(t,"bpmn:Participant")?F(e,"bpmn:Process")||F(e,"bpmn:Collaboration"):F(t,"bpmn:BoundaryEvent")?!1:F(t,"bpmn:FlowElement")?F(e,"bpmn:FlowElementsContainer")?V(e)!==!1:F(e,"bpmn:Participant"):F(t,"bpmn:Artifact")?F(e,"bpmn:Collaboration")||F(e,"bpmn:Participant")||F(e,"bpmn:Process"):F(t,"bpmn:MessageFlow")?F(e,"bpmn:Collaboration"):!1}function w(t){return!v(t)&&F(t,"bpmn:BoundaryEvent")}function _(t){return w(t)||F(t,"bpmn:IntermediateThrowEvent")&&!t.parent}function S(t,e,n,i){if(n)return!1;if(1!==t.length)return!1;var r=t[0];return v(r)?!1:_(r)?e?F(e,"bpmn:Activity")?i&&!G(i,e)?!1:"attach":!1:!0:!1}function A(t,e){return D(t)?I(t,w)?!1:e?t.every(function(t){return E(t,e)}):!0:!1}function T(t,e,n,i){return e?v(e)?null:o(n,e)?!1:n&&x(n,e)?!1:E(t,e,i)||P(t,e,i):!1}function C(t,e){return F(t,"bpmn:SubProcess")?!!V(t)&&(!e||e.width>=100&&e.height>=80):F(t,"bpmn:Participant")?!e||e.width>=100&&e.height>=80:F(t,"bpmn:TextAnnotation")?!0:!1}function N(t,e){return g(t)||g(e)?!1:!x(e,t)&&!x(t,e)}function M(t,e){return c(t)&&u(e)&&!s(t,e)}function R(t,e){return f(t)&&m(e)&&l(t,e)&&!(F(t,"bpmn:EventBasedGateway")&&!y(e))}function P(t,e,n){return(F(e,"bpmn:SequenceFlow")||F(e,"bpmn:MessageFlow"))&&F(t,"bpmn:FlowNode")&&!F(t,"bpmn:BoundaryEvent")&&E(t,e.parent,n)}function D(t){return 1===B(k(t,function(t){return t.parent&&t.parent.id}))}var k=t(219),B=t(223),O=t(217),I=t(214),L=t(207),j=t(21).getParents,F=t(50).is,z=t(50).getBusinessObject,V=t(48).isExpanded,$=t(153),G=t(41).getBoundaryAttachment;L(i,$),i.$inject=["eventBus"],e.exports=i,i.prototype.init=function(){this.addRule("connection.create",function(t){var e=t.source,n=t.target;return b(e,n)}),this.addRule("connection.reconnectStart",function(t){var e=t.connection,n=t.hover||t.source,i=e.target;return b(n,i,e)}),this.addRule("connection.reconnectEnd",function(t){var e=t.connection,n=e.source,i=t.hover||t.target;return b(n,i,e)}),this.addRule("connection.updateWaypoints",function(t){
return null}),this.addRule("shape.resize",function(t){var e=t.shape,n=t.newBounds;return C(e,n)}),this.addRule("shapes.move",function(t){var e=t.target,n=t.shapes,i=t.position;return S(n,e,null,i)||A(n,e,i)}),this.addRule(["shape.create","shape.append"],function(t){var e=t.target,n=t.shape,i=t.source,r=t.position;return S([n],e,i,r)||T(n,e,i,r)})},i.prototype.canConnectMessageFlow=M,i.prototype.canConnectSequenceFlow=R,i.prototype.canConnectAssociation=N,i.prototype.canMove=A,i.prototype.canAttach=S,i.prototype.canDrop=E,i.prototype.canInsert=P,i.prototype.canCreate=T,i.prototype.canConnect=b,i.prototype.canResize=C},{153:153,207:207,21:21,214:214,217:217,219:219,223:223,41:41,48:48,50:50}],34:[function(t,e,n){e.exports={__depends__:[t(155)],__init__:["bpmnRules"],bpmnRules:["type",t(33)]}},{155:155,33:33}],35:[function(t,e,n){"use strict";function i(t,e,n,i,r){this._create=e,this._elementFactory=n,this._spaceTool=i,this._lassoTool=r,t.registerProvider(this)}var r=t(339);e.exports=i,i.$inject=["palette","create","elementFactory","spaceTool","lassoTool"],i.prototype.getPaletteEntries=function(t){function e(t,e,n,i,s){function c(e){var n=a.createShape(r({type:t},s));s&&(n.businessObject.di.isExpanded=s.isExpanded),o.start(e,n)}var u=t.replace(/^bpmn\:/,"");return{group:e,className:n,title:i||"Create "+u,action:{dragstart:c,click:c}}}function n(t,e){o.start(t,a.createParticipantShape(e))}var i={},o=this._create,a=this._elementFactory,s=this._spaceTool,c=this._lassoTool;return r(i,{"lasso-tool":{group:"tools",className:"icon-lasso-tool",title:"Activate the lasso tool",action:{click:function(t){c.activateSelection(t)}}},"space-tool":{group:"tools",className:"icon-space-tool",title:"Activate the create/remove space tool",action:{click:function(t){s.activateSelection(t)}}},"tool-separator":{group:"tools",separator:!0},"create.start-event":e("bpmn:StartEvent","event","icon-start-event-none"),"create.intermediate-event":e("bpmn:IntermediateThrowEvent","event","icon-intermediate-event-none"),"create.end-event":e("bpmn:EndEvent","event","icon-end-event-none"),"create.exclusive-gateway":e("bpmn:ExclusiveGateway","gateway","icon-gateway-xor"),"create.task":e("bpmn:Task","activity","icon-task"),"create.subprocess-expanded":e("bpmn:SubProcess","activity","icon-subprocess-expanded","Create expanded SubProcess",{isExpanded:!0}),"create.participant-expanded":{group:"collaboration",className:"icon-participant",title:"Create Pool/Participant",action:{dragstart:n,click:n}}}),i}},{339:339}],36:[function(t,e,n){e.exports={__depends__:[t(145),t(104),t(166),t(114)],__init__:["paletteProvider"],paletteProvider:["type",t(35)]}},{104:104,114:114,145:145,166:166,35:35}],37:[function(t,e,n){"use strict";function i(t,e,n,i,c,E,w){function _(n,r){var o=r.type,u=n.businessObject,p=t.create(o),l={type:o,businessObject:p};if(r.eventDefinition){var h=p.get("eventDefinitions"),d=e.create(r.eventDefinition);h.push(d)}return s(p,a(r,b)),v(u,"bpmn:Activity")&&(l.width=n.width,l.height=n.height),v(u,"bpmn:SubProcess")&&(l.isExpanded=x(u)),p.name=u.name,p.loopCharacteristics=u.loopCharacteristics,l=i.replaceElement(n,l),c.select(l),l}function S(t,n){var i,o=M.getLoopEntries(N);n.active?i=void 0:r(o,function(t){var r=t.options;n.id===t.id&&(i=e.create(r.loopCharacteristics),r.isSequential&&(i.isSequential=r.isSequential))}),E.updateProperties(N,{loopCharacteristics:i})}function A(t){N=t;var e,n,i,r=g(t),o=r.loopCharacteristics;o&&(e=o.isSequential,n=void 0===o.isSequential,i=void 0!==o.isSequential&&!o.isSequential);var a=[{id:"toggle-parallel-mi",className:"icon-parallel-mi-marker",active:i,action:S,options:{loopCharacteristics:"bpmn:MultiInstanceLoopCharacteristics",isSequential:!1}},{id:"toggle-sequential-mi",className:"icon-sequential-mi-marker",active:e,action:S,options:{loopCharacteristics:"bpmn:MultiInstanceLoopCharacteristics",isSequential:!0}},{id:"toggle-loop",className:"icon-loop-marker",active:n,action:S,options:{loopCharacteristics:"bpmn:StandardLoopCharacteristics"}}];return a}function T(t){var e=g(t),n=v(e,"bpmn:AdHocSubProcess"),i={id:"toggle-adhoc",className:"icon-ad-hoc-marker",active:n,action:function(e,i){return n?_(t,{type:"bpmn:SubProcess"}):_(t,{type:"bpmn:AdHocSubProcess"})}};return i}function C(t){function e(t){var e,n=t.target,i=s.eventDefinitions&&s.eventDefinitions[0].$type;"bpmn:BoundaryEvent"===s.$type&&(e=n.cancelActivity!==!1);var r=n.eventDefinition==i,o=s.$type==n.type,a=s.cancelActivity==e;return!r&&o||!o||!(r&&o&&a)}function n(t,e){var n=o(t,e);r(n,function(t){var e=i(t);a.push(e)})}function i(e){return{label:e.label,className:e.className,id:e.actionName,action:function(){return _(t,e.target)}}}var a=[],s=t.businessObject;return v(s,"bpmn:StartEvent")?n(u,e):v(s,"bpmn:IntermediateCatchEvent")||v(s,"bpmn:IntermediateThrowEvent")?n(p,e):v(s,"bpmn:EndEvent")?n(l,e):v(s,"bpmn:Gateway")?n(h,function(t){return t.target.type!==s.$type}):v(s,"bpmn:Transaction")?n(m,e):v(s,"bpmn:SubProcess")&&x(s)?n(f,e):v(s,"bpmn:AdHocSubProcess")&&!x(s)?n(d,function(t){return"bpmn:SubProcess"!==t.target.type}):v(s,"bpmn:BoundaryEvent")?n(y,e):v(s,"bpmn:FlowNode")&&n(d,function(t){return t.target.type!==s.$type}),a}var N,M=this;this.openChooser=function(t,e){var i=this.getReplaceOptions(e),r=[];v(e,"bpmn:Activity")&&(r=r.concat(this.getLoopEntries(e))),v(e,"bpmn:SubProcess")&&!v(e,"bpmn:Transaction")&&r.push(this.getAdHocEntry(e)),n.open({className:"replace-menu",element:e,position:t,headerEntries:r,entries:i})},this.getReplaceOptions=C,this.getLoopEntries=A,this.getAdHocEntry=T,this.replaceElement=_}var r=t(218),o=t(216),a=t(345),s=t(339),c=t(38),u=c.START_EVENT,p=c.INTERMEDIATE_EVENT,l=c.END_EVENT,h=c.GATEWAY,d=c.TASK,f=c.SUBPROCESS_EXPANDED,m=c.TRANSACTION,y=c.BOUNDARY_EVENT,v=t(50).is,g=t(50).getBusinessObject,x=t(48).isExpanded,b=["cancelActivity","instantiate","eventGatewayType"];i.$inject=["bpmnFactory","moddle","popupMenu","replace","selection","modeling","eventBus"],e.exports=i},{216:216,218:218,339:339,345:345,38:38,48:48,50:50}],38:[function(t,e,n){"use strict";e.exports.START_EVENT=[{label:"Start Event",actionName:"replace-with-none-start",className:"icon-start-event-none",target:{type:"bpmn:StartEvent"}},{label:"Intermediate Throw Event",actionName:"replace-with-none-intermediate-throwing",className:"icon-intermediate-event-none",target:{type:"bpmn:IntermediateThrowEvent"}},{label:"End Event",actionName:"replace-with-none-end",className:"icon-end-event-none",target:{type:"bpmn:EndEvent"}},{label:"Message Start Event",actionName:"replace-with-message-start",className:"icon-start-event-message",target:{type:"bpmn:StartEvent",eventDefinition:"bpmn:MessageEventDefinition"}},{label:"Timer Start Event",actionName:"replace-with-timer-start",className:"icon-start-event-timer",target:{type:"bpmn:StartEvent",eventDefinition:"bpmn:TimerEventDefinition"}},{label:"Conditional Start Event",actionName:"replace-with-conditional-start",className:"icon-start-event-condition",target:{type:"bpmn:StartEvent",eventDefinition:"bpmn:ConditionalEventDefinition"}},{label:"Signal Start Event",actionName:"replace-with-signal-start",className:"icon-start-event-signal",target:{type:"bpmn:StartEvent",eventDefinition:"bpmn:SignalEventDefinition"}}],e.exports.INTERMEDIATE_EVENT=[{label:"Start Event",actionName:"replace-with-none-start",className:"icon-start-event-none",target:{type:"bpmn:StartEvent"}},{label:"Intermediate Throw Event",actionName:"replace-with-none-intermediate-throw",className:"icon-intermediate-event-none",target:{type:"bpmn:IntermediateThrowEvent"}},{label:"End Event",actionName:"replace-with-none-end",className:"icon-end-event-none",target:{type:"bpmn:EndEvent"}},{label:"Message Intermediate Catch Event",actionName:"replace-with-message-intermediate-catch",className:"icon-intermediate-event-catch-message",target:{type:"bpmn:IntermediateCatchEvent",eventDefinition:"bpmn:MessageEventDefinition"}},{label:"Message Intermediate Throw Event",actionName:"replace-with-message-intermediate-throw",className:"icon-intermediate-event-throw-message",target:{type:"bpmn:IntermediateThrowEvent",eventDefinition:"bpmn:MessageEventDefinition"}},{label:"Timer Intermediate Catch Event",actionName:"replace-with-timer-intermediate-catch",className:"icon-intermediate-event-catch-timer",target:{type:"bpmn:IntermediateCatchEvent",eventDefinition:"bpmn:TimerEventDefinition"}},{label:"Escalation Intermediate Catch Event",actionName:"replace-with-escalation-intermediate-catch",className:"icon-intermediate-event-catch-escalation",target:{type:"bpmn:IntermediateCatchEvent",eventDefinition:"bpmn:EscalationEventDefinition"}},{label:"Conditional Intermediate Catch Event",actionName:"replace-with-conditional-intermediate-catch",className:"icon-intermediate-event-catch-condition",target:{type:"bpmn:IntermediateCatchEvent",eventDefinition:"bpmn:ConditionalEventDefinition"}},{label:"Link Intermediate Catch Event",actionName:"replace-with-link-intermediate-catch",className:"icon-intermediate-event-catch-link",target:{type:"bpmn:IntermediateCatchEvent",eventDefinition:"bpmn:LinkEventDefinition"}},{label:"Link Intermediate Throw Event",actionName:"replace-with-link-intermediate-throw",className:"icon-intermediate-event-throw-link",target:{type:"bpmn:IntermediateThrowEvent",eventDefinition:"bpmn:LinkEventDefinition"}},{label:"Compensation Intermediate Throw Event",actionName:"replace-with-compensation-intermediate-throw",className:"icon-intermediate-event-throw-compensation",target:{type:"bpmn:IntermediateThrowEvent",eventDefinition:"bpmn:CompensateEventDefinition"}},{label:"Signal Intermediate Catch Event",actionName:"replace-with-signal-intermediate-catch",className:"icon-intermediate-event-catch-signal",target:{type:"bpmn:IntermediateCatchEvent",eventDefinition:"bpmn:SignalEventDefinition"}},{label:"Signal Intermediate Throw Event",actionName:"replace-with-signal-intermediate-throw",className:"icon-intermediate-event-throw-signal",target:{type:"bpmn:IntermediateThrowEvent",eventDefinition:"bpmn:SignalEventDefinition"}}],e.exports.END_EVENT=[{label:"Start Event",actionName:"replace-with-none-start",className:"icon-start-event-none",target:{type:"bpmn:StartEvent"}},{label:"Intermediate Throw Event",actionName:"replace-with-none-intermediate-throw",className:"icon-intermediate-event-none",target:{type:"bpmn:IntermediateThrowEvent"}},{label:"End Event",actionName:"replace-with-none-end",className:"icon-end-event-none",target:{type:"bpmn:EndEvent"}},{label:"Message End Event",actionName:"replace-with-message-end",className:"icon-end-event-message",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:MessageEventDefinition"}},{label:"Escalation End Event",actionName:"replace-with-escalation-end",className:"icon-end-event-escalation",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:EscalationEventDefinition"}},{label:"Error End Event",actionName:"replace-with-error-end",className:"icon-end-event-error",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:ErrorEventDefinition"}},{label:"Cancel End Event",actionName:"replace-with-cancel-end",className:"icon-end-event-cancel",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:CancelEventDefinition"}},{label:"Compensation End Event",actionName:"replace-with-compensation-end",className:"icon-end-event-compensation",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:CompensateEventDefinition"}},{label:"Signal End Event",actionName:"replace-with-signal-end",className:"icon-end-event-signal",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:SignalEventDefinition"}},{label:"Terminate End Event",actionName:"replace-with-terminate-end",className:"icon-end-event-terminate",target:{type:"bpmn:EndEvent",eventDefinition:"bpmn:TerminateEventDefinition"}}],e.exports.GATEWAY=[{label:"Exclusive Gateway",actionName:"replace-with-exclusive-gateway",className:"icon-gateway-xor",target:{type:"bpmn:ExclusiveGateway"}},{label:"Parallel Gateway",actionName:"replace-with-parallel-gateway",className:"icon-gateway-parallel",target:{type:"bpmn:ParallelGateway"}},{label:"Inclusive Gateway",actionName:"replace-with-inclusive-gateway",className:"icon-gateway-or",target:{type:"bpmn:InclusiveGateway"}},{label:"Complex Gateway",actionName:"replace-with-complex-gateway",className:"icon-gateway-complex",target:{type:"bpmn:ComplexGateway"}},{label:"Event based Gateway",actionName:"replace-with-event-based-gateway",className:"icon-gateway-eventbased",target:{type:"bpmn:EventBasedGateway",instantiate:!1,eventGatewayType:"Exclusive"}}],e.exports.SUBPROCESS_EXPANDED=[{label:"Transaction",actionName:"replace-with-transaction",className:"icon-transaction",target:{type:"bpmn:Transaction",isExpanded:!0}}],e.exports.TRANSACTION=[{label:"Sub Process",actionName:"replace-with-subprocess",className:"icon-subprocess-expanded",target:{type:"bpmn:SubProcess"}}],e.exports.TASK=[{label:"Task",actionName:"replace-with-task",className:"icon-task",target:{type:"bpmn:Task"}},{label:"Send Task",actionName:"replace-with-send-task",className:"icon-send",target:{type:"bpmn:SendTask"}},{label:"Receive Task",actionName:"replace-with-receive-task",className:"icon-receive",target:{type:"bpmn:ReceiveTask"}},{label:"User Task",actionName:"replace-with-user-task",className:"icon-user",target:{type:"bpmn:UserTask"}},{label:"Manual Task",actionName:"replace-with-manual-task",className:"icon-manual",target:{type:"bpmn:ManualTask"}},{label:"Business Rule Task",actionName:"replace-with-rule-task",className:"icon-business-rule",target:{type:"bpmn:BusinessRuleTask"}},{label:"Service Task",actionName:"replace-with-service-task",className:"icon-service",target:{type:"bpmn:ServiceTask"}},{label:"Script Task",actionName:"replace-with-script-task",className:"icon-script",target:{type:"bpmn:ScriptTask"}},{label:"Call Activity",actionName:"replace-with-call-activity",className:"icon-call-activity",target:{type:"bpmn:CallActivity"}},{label:"Sub Process (collapsed)",actionName:"replace-with-collapsed-subprocess",className:"icon-subprocess-collapsed",target:{type:"bpmn:SubProcess",isExpanded:!1}}],e.exports.BOUNDARY_EVENT=[{label:"Message Boundary Event",actionName:"replace-with-message-boundary",className:"icon-intermediate-event-catch-message",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:MessageEventDefinition"}},{label:"Timer Boundary Event",actionName:"replace-with-timer-boundary",className:"icon-intermediate-event-catch-timer",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:TimerEventDefinition"}},{label:"Escalation Boundary Event",actionName:"replace-with-escalation-boundary",className:"icon-intermediate-event-catch-escalation",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:EscalationEventDefinition"}},{label:"Conditional Boundary Event",actionName:"replace-with-conditional-boundary",className:"icon-intermediate-event-catch-condition",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:ConditionalEventDefinition"}},{label:"Error Boundary Event",actionName:"replace-with-error-boundary",className:"icon-intermediate-event-catch-error",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:ErrorEventDefinition"}},{label:"Signal Boundary Event",actionName:"replace-with-signal-boundary",className:"icon-intermediate-event-catch-signal",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:SignalEventDefinition"}},{label:"Message Boundary Event (non-interrupting)",actionName:"replace-with-non-interrupting-message-boundary",className:"icon-intermediate-event-catch-non-interrupting-message",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:MessageEventDefinition",cancelActivity:!1}},{label:"Timer Boundary Event (non-interrupting)",actionName:"replace-with-non-interrupting-timer-boundary",className:"icon-intermediate-event-catch-non-interrupting-timer",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:TimerEventDefinition",cancelActivity:!1}},{label:"Escalation Boundary Event (non-interrupting)",actionName:"replace-with-non-interrupting-escalation-boundary",className:"icon-intermediate-event-catch-non-interrupting-escalation",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:EscalationEventDefinition",cancelActivity:!1}},{label:"Conditional Boundary Event (non-interrupting)",actionName:"replace-with-non-interrupting-conditional-boundary",className:"icon-intermediate-event-catch-non-interrupting-condition",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:ConditionalEventDefinition",cancelActivity:!1}},{label:"Signal Boundary Event (non-interrupting)",actionName:"replace-with-non-interrupting-signal-boundary",className:"icon-intermediate-event-catch-non-interrupting-signal",target:{type:"bpmn:BoundaryEvent",eventDefinition:"bpmn:SignalEventDefinition",cancelActivity:!1}}]},{}],39:[function(t,e,n){e.exports={__depends__:[t(147),t(149),t(159)],bpmnReplace:["type",t(37)]}},{147:147,149:149,159:159,37:37}],40:[function(t,e,n){"use strict";function i(t,e,n){function i(t,e,i){return"attach"===n.canAttach([t],e,null,i)}h.call(this,t,e),t.on("create.start",function(t){var n=t.context,i=n.shape,o=e.getRootElement();p(i,"bpmn:Participant")&&p(o,"bpmn:Process")&&r(n,i,o.children)}),t.on(["create.move","create.end"],1500,function(t){var e=t.context,n=e.shape,i=e.participantSnapBox;!g(t)&&i&&o(i,n,t)}),t.on("shape.move.start",function(t){var n=t.context,i=n.shape,o=e.getRootElement();p(i,"bpmn:Participant")&&p(o,"bpmn:Process")&&r(n,i,o.children)}),t.on(["create.move","create.end"],1500,function(t){var e=t.context,n=e.target,r=e.shape;n&&!g(t)&&i(r,n,t)&&a(t,r,n)}),t.on(["shape.move.move","shape.move.end"],1500,function(t){var e=t.context,n=e.target,r=e.shape;n&&!g(t)&&i(r,n,t)&&a(t,r,n)}),t.on("resize.start",1500,function(t){var e=t.context,n=e.shape;p(n,"bpmn:SubProcess")&&l(n)&&(e.minDimensions={width:140,height:120}),p(n,"bpmn:Participant")&&(e.minDimensions={width:300,height:150},e.childrenBoxPadding={left:50,right:35}),p(n,"bpmn:TextAnnotation")&&(e.minDimensions={width:50,height:50})})}function r(t,e,n){if(n.length){var i=u(n.filter(function(t){return!t.labelTarget&&!t.waypoints}));i.x-=50,i.y-=20,i.width+=70,i.height+=40,e.width=Math.max(e.width,i.width),e.height=Math.max(e.height,i.height),t.participantSnapBox=i}}function o(t,e,n,i){i=i||0;var r=e.width/2-i,o=e.height/2,a={x:n.x-r-i,y:n.y-o},s={x:n.x+r+i,y:n.y+o},c=t,u=v(t);a.x>=c.x?x(n,"x",c.x+i+r):s.x<=u.x&&x(n,"x",u.x-i-r),a.y>=c.y?x(n,"y",c.y+o):s.y<=u.y&&x(n,"y",u.y-o)}function a(t,e,n){var i=E.asTRBL(n),r=b(t,n);/top/.test(r)?x(t,"y",i.top):/bottom/.test(r)&&x(t,"y",i.bottom),/left/.test(r)?x(t,"x",i.left):/right/.test(r)&&x(t,"x",i.right)}var s=t(207),c=t(218),u=t(186).getBBox,p=t(21).is,l=t(48).isExpanded,h=t(162),d=t(161),p=t(50).is,f=Math.round,m=d.mid,y=d.topLeft,v=d.bottomRight,g=d.isSnapped,x=d.setSnapped,b=t(41).getBoundaryAttachment;s(i,h),i.$inject=["eventBus","canvas","bpmnRules"],e.exports=i,i.prototype.initSnap=function(t){var e,n,i,r,o,a=t.context,s=t.shape;o=h.prototype.initSnap.call(this,t),p(s,"bpmn:Participant")&&o.setSnapLocations(["top-left","bottom-right","mid"]),s&&(e=m(s,t),n={width:s.width,height:s.height,x:isNaN(s.x)?f(e.x-s.width/2):s.x,y:isNaN(s.y)?f(e.y-s.height/2):s.y},i=y(n),r=v(n),o.setSnapOrigin("top-left",{x:i.x-t.x,y:i.y-t.y}),o.setSnapOrigin("bottom-right",{x:r.x-t.x,y:r.y-t.y}),c(s.outgoing,function(e){var n=e.waypoints[0];n=n.original||n,o.setSnapOrigin(e.id+"-docking",{x:n.x-t.x,y:n.y-t.y})}),c(s.incoming,function(e){var n=e.waypoints[e.waypoints.length-1];n=n.original||n,o.setSnapOrigin(e.id+"-docking",{x:n.x-t.x,y:n.y-t.y})}));var u=a.source;u&&o.addDefaultSnap("mid",m(u))},i.prototype.addTargetSnaps=function(t,e,n){p(e,"bpmn:BoundaryEvent")&&(n=n.parent),p(n,"bpmn:SequenceFlow")&&this.addTargetSnaps(t,e,n.parent);var i=this.getSiblings(e,n)||[];c(i,function(e){t.add("mid",m(e)),p(e,"bpmn:Participant")&&(t.add("top-left",y(e)),t.add("bottom-right",v(e)))}),c(e.incoming,function(e){-1===i.indexOf(e.source)&&t.add("mid",m(e.source));var n=e.waypoints[0];t.add(e.id+"-docking",n.original||n)}),c(e.outgoing,function(e){-1===i.indexOf(e.target)&&t.add("mid",m(e.target));var n=e.waypoints[e.waypoints.length-1];t.add(e.id+"-docking",n.original||n)})};var E=t(174)},{161:161,162:162,174:174,186:186,207:207,21:21,218:218,41:41,48:48,50:50}],41:[function(t,e,n){"use strict";var i=t(174).getOrientation;e.exports.getBoundaryAttachment=function(t,e){var n=i(t,e,-15);return"intersect"!==n?n:null}},{174:174}],42:[function(t,e,n){e.exports={__init__:["snapping"],snapping:["type",t(40)]}},{40:40}],43:[function(t,e,n){"use strict";function i(t,e){return s({id:t.id,type:t.$type,businessObject:t},e)}function r(t){return c(t,function(t){return{x:t.x,y:t.y}})}function o(t,e,n){return new Error("element "+f(e)+" referenced by "+f(t)+"#"+n+" not yet drawn")}function a(t,e,n,i){this._eventBus=t,this._canvas=e,this._elementFactory=n,this._elementRegistry=i}var s=t(339),c=t(221),u=t(49),p=t(50).is,l=u.hasExternalLabel,h=u.getExternalLabelBounds,d=t(48).isExpanded,f=t(46).elementToString;a.$inject=["eventBus","canvas","elementFactory","elementRegistry"],e.exports=a,a.prototype.add=function(t,e){var n,o=t.di;if(o.$instanceOf("bpmndi:BPMNPlane"))n=this._elementFactory.createRoot(i(t)),this._canvas.setRootElement(n);else if(o.$instanceOf("bpmndi:BPMNShape")){var a=!d(t),s=e&&(e.hidden||e.collapsed),c=t.di.bounds;n=this._elementFactory.createShape(i(t,{collapsed:a,hidden:s,x:Math.round(c.x),y:Math.round(c.y),width:Math.round(c.width),height:Math.round(c.height)})),p(t,"bpmn:BoundaryEvent")&&this._attachBoundary(t,n),this._canvas.addShape(n,e)}else{if(!o.$instanceOf("bpmndi:BPMNEdge"))throw new Error("unknown di "+f(o)+" for element "+f(t));var u=this._getSource(t),h=this._getTarget(t);n=this._elementFactory.createConnection(i(t,{source:u,target:h,waypoints:r(t.di.waypoint)})),this._canvas.addConnection(n,e)}return l(t)&&this.addLabel(t,n),this._eventBus.fire("bpmnElement.added",{element:n}),n},a.prototype._attachBoundary=function(t,e){var n=t.attachedToRef;if(!n)throw new Error("missing "+f(t)+"#attachedToRef");var i=this._elementRegistry.get(n.id),r=i&&i.attachers;if(!i)throw o(t,n,"attachedToRef");e.host=i,r||(i.attachers=r=[]),-1===r.indexOf(e)&&r.push(e)},a.prototype.addLabel=function(t,e){var n=h(t,e),r=this._elementFactory.createLabel(i(t,{id:t.id+"_label",labelTarget:e,type:"label",hidden:e.hidden,x:Math.round(n.x),y:Math.round(n.y),width:Math.round(n.width),height:Math.round(n.height)}));return this._canvas.addShape(r,e.parent)},a.prototype._getEnd=function(t,e){var n,i,r=t.$type;if(i=t[e+"Ref"],"source"===e&&"bpmn:DataInputAssociation"===r&&(i=i&&i[0]),("source"===e&&"bpmn:DataOutputAssociation"===r||"target"===e&&"bpmn:DataInputAssociation"===r)&&(i=t.$parent),n=i&&this._getElement(i))return n;throw i?o(t,i,e+"Ref"):new Error(f(t)+"#"+e+"Ref not specified")},a.prototype._getSource=function(t){return this._getEnd(t,"source")},a.prototype._getTarget=function(t){return this._getEnd(t,"target")},a.prototype._getElement=function(t){return this._elementRegistry.get(t.id)}},{221:221,339:339,46:46,48:48,49:49,50:50}],44:[function(t,e,n){"use strict";function i(t,e){return t.$instanceOf(e)}function r(t){return s(t.rootElements,function(t){return i(t,"bpmn:Process")||i(t,"bpmn:Collaboration")})}function o(t){function e(t,e){return function(n){t(n,e)}}function n(e,n){var i=e.gfx;if(i)throw new Error("already rendered "+p(e));return t.element(e,n)}function o(e,n){return t.root(e,n)}function s(t,e){try{return t.di&&n(t,e)}catch(i){u(i.message,{element:t,error:i}),console.error("failed to import "+p(t)),console.error(i)}}function u(e,n){t.error(e,n)}function h(t){var e=t.bpmnElement;e?e.di?u("multiple DI elements defined for "+p(e),{element:e}):(l.bind(e,"di"),e.di=t):u("no bpmnElement referenced in "+p(t),{element:t})}function d(t){f(t.plane)}function f(t){h(t),c(t.planeElement,m)}function m(t){h(t)}function y(t,e){var n=t.diagrams;if(e&&-1===n.indexOf(e))throw new Error("diagram not part of bpmn:Definitions");if(!e&&n&&n.length&&(e=n[0]),e){d(e);var a=e.plane;if(!a)throw new Error("no plane for "+p(e));var s=a.bpmnElement;if(!s){if(s=r(t),!s)return u("no process or collaboration present to display");u("correcting missing bpmnElement on "+p(a)+" to "+p(s)),a.bpmnElement=s,h(a)}var c=o(s,a);if(i(s,"bpmn:Process"))g(s,c);else{if(!i(s,"bpmn:Collaboration"))throw new Error("unsupported bpmnElement for "+p(a)+" : "+p(s));z(s,c),x(t.rootElements,c)}v($)}}function v(t){c(t,function(t){t()})}function g(t,e){I(t,e),C(t.ioSpecification,e),T(t.artifacts,e),V.push(t)}function x(t){var n=a(t,function(t){return i(t,"bpmn:Process")&&t.laneSets&&-1===V.indexOf(t)});n.forEach(e(g))}function b(t,e){s(t,e)}function E(t,n){c(t,e(b,n))}function w(t,e){s(t,e)}function _(t,e){s(t,e)}function S(t,e){s(t,e)}function A(t,e){s(t,e)}function T(t,e){c(t,function(t){i(t,"bpmn:Association")?$.push(function(){A(t,e)}):A(t,e)})}function C(t,n){t&&(c(t.dataInputs,e(_,n)),c(t.dataOutputs,e(S,n)))}function N(t,e){I(t,e),T(t.artifacts,e)}function M(t,e){var n=s(t,e);i(t,"bpmn:SubProcess")&&N(t,n||e)}function R(t,e){s(t,e)}function P(t,e){s(t,e)}function D(t,e){s(t,e)}function k(t,e){var n=s(t,e);if(t.childLaneSet)B(t.childLaneSet,n||e);else{var i=a(t.flowNodeRef,function(t){return"bpmn:BoundaryEvent"!==t.$type});j(i,n||e)}}function B(t,n){c(t.lanes,e(k,n))}function O(t,n){c(t,e(B,n))}function I(t,e){t.laneSets?(O(t.laneSets,e),L(t.flowElements)):j(t.flowElements,e)}function L(t,e){c(t,function(t){i(t,"bpmn:SequenceFlow")?$.push(function(){R(t,e)}):i(t,"bpmn:BoundaryEvent")?$.unshift(function(){D(t,e)}):i(t,"bpmn:DataObject")||(i(t,"bpmn:DataStoreReference")?P(t,e):i(t,"bpmn:DataObjectReference")&&P(t,e))})}function j(t,n){c(t,function(t){i(t,"bpmn:SequenceFlow")?$.push(function(){R(t,n)}):i(t,"bpmn:BoundaryEvent")?$.unshift(function(){D(t,n)}):i(t,"bpmn:FlowNode")?(M(t,n),i(t,"bpmn:Activity")&&(C(t.ioSpecification,n),$.push(function(){c(t.dataInputAssociations,e(w,n)),c(t.dataOutputAssociations,e(w,n))}))):i(t,"bpmn:DataObject")||(i(t,"bpmn:DataStoreReference")?P(t,n):i(t,"bpmn:DataObjectReference")?P(t,n):u("unrecognized flowElement "+p(t)+" in context "+(n?p(n.businessObject):null),{element:t,context:n}))})}function F(t,e){var n=s(t,e),i=t.processRef;i&&g(i,n||e)}function z(t){c(t.participants,e(F)),T(t.artifacts),$.push(function(){E(t.messageFlows)})}var V=[],$=[];return{handleDefinitions:y}}var a=t(216),s=t(217),c=t(218),u=t(369),p=t(46).elementToString,l=new u({name:"bpmnElement",enumerable:!0},{name:"di"});e.exports=o},{216:216,217:217,218:218,369:369,46:46}],45:[function(t,e,n){"use strict";function i(t,e,n){function i(t){var e={root:function(t){return a.add(t)},element:function(t,e){return a.add(t,e)},error:function(t,e){c.push({message:t,context:e})}},n=new r(e);n.handleDefinitions(t)}var o,a=t.get("bpmnImporter"),s=t.get("eventBus"),c=[];s.fire("import.start");try{i(e)}catch(u){o=u}s.fire(o?"import.error":"import.success",{error:o,warnings:c}),n(o,c)}var r=t(44);e.exports.importBpmnDiagram=i},{44:44}],46:[function(t,e,n){"use strict";e.exports.elementToString=function(t){return t?"<"+t.$type+(t.id?' id="'+t.id:"")+'" />':"<null>"}},{}],47:[function(t,e,n){e.exports={bpmnImporter:["type",t(43)]}},{43:43}],48:[function(t,e,n){"use strict";var i=t(50).is,r=t(50).getBusinessObject;e.exports.isExpanded=function(t){return i(t,"bpmn:CallActivity")?!1:i(t,"bpmn:SubProcess")?r(t).di.isExpanded:i(t,"bpmn:Participant")?!!r(t).processRef:!0}},{50:50}],49:[function(t,e,n){"use strict";var i=t(339),r=e.exports.DEFAULT_LABEL_SIZE={width:90,height:20};e.exports.hasExternalLabel=function(t){return t.$instanceOf("bpmn:Event")||t.$instanceOf("bpmn:Gateway")||t.$instanceOf("bpmn:DataStoreReference")||t.$instanceOf("bpmn:DataObjectReference")||t.$instanceOf("bpmn:SequenceFlow")||t.$instanceOf("bpmn:MessageFlow")};var o=e.exports.getWaypointsMid=function(t){var e=t.length/2-1,n=t[Math.floor(e)],i=t[Math.ceil(e+.01)];return{x:n.x+(i.x-n.x)/2,y:n.y+(i.y-n.y)/2}},a=e.exports.getExternalLabelMid=function(t){return t.waypoints?o(t.waypoints):{x:t.x+t.width/2,y:t.y+t.height+r.height/2}};e.exports.getExternalLabelBounds=function(t,e){var n,o,s,c=t.di,u=c.label;return u&&u.bounds?(s=u.bounds,o={width:Math.max(r.width,s.width),height:s.height},n={x:s.x+s.width/2,y:s.y+s.height/2}):(n=a(e),o=r),i({x:n.x-o.width/2,y:n.y-o.height/2},o)}},{339:339}],50:[function(t,e,n){"use strict";function i(t,e){var n=r(t);return n&&n.$instanceOf(e)}function r(t){return t&&t.businessObject||t}e.exports.is=i,e.exports.getBusinessObject=r},{}],51:[function(t,e,n){e.exports=t(54)},{54:54}],52:[function(t,e,n){"use strict";function i(t,e){s.call(this,t,e)}var r=t(336),o=t(331),a=t(339),s=t(60),c=t(56),u=t(57);i.prototype=Object.create(s.prototype),e.exports=i,i.prototype.fromXML=function(t,e,n,i){r(e)||(i=n,n=e,e="bpmn:Definitions"),o(n)&&(i=n,n={});var s=new c(a({model:this,lax:!0},n)),u=s.handler(e);s.fromXML(t,u,i)},i.prototype.toXML=function(t,e,n){o(e)&&(n=e,e={});var i=new u(e);try{var r=i.toXML(t);n(null,r)}catch(a){n(a)}}},{331:331,336:336,339:339,56:56,57:57,60:60}],53:[function(t,e,n){"use strict";var i=/^(.*:)?id$/;e.exports.extend=function(t,e){var n=t.properties.set;return t.ids||(t.properties.set=function(e,r,o){if(i.test(r)){var a=t.ids.assigned(o);if(a&&a!==e)throw new Error("id <"+o+"> already used");t.ids.claim(o,e)}n.call(this,e,r,o)}),t.ids=e,t}},{}],54:[function(t,e,n){"use strict";var i=t(339),r=t(52),o={bpmn:t(69),bpmndi:t(70),dc:t(71),di:t(72)};e.exports=function(t,e){return new r(i({},o,t),e)}},{339:339,52:52,69:69,70:70,71:71,72:72}],55:[function(t,e,n){"use strict";function i(t){return t.charAt(0).toUpperCase()+t.slice(1)}function r(t){return t.charAt(0).toLowerCase()+t.slice(1)}function o(t){return t.xml&&"lowerCase"===t.xml.tagAlias}function a(t){return t.xml&&t.xml.serialize}e.exports.aliasToName=function(t,e){return o(e)?i(t):t},e.exports.nameToAlias=function(t,e){return o(e)?r(t):t},e.exports.DEFAULT_NS_MAP={xsi:"http://www.w3.org/2001/XMLSchema-instance"};var s=e.exports.XSI_TYPE="xsi:type";e.exports.serializeAsType=function(t){return a(t)===s},e.exports.serializeAsProperty=function(t){return"property"===a(t)}},{}],56:[function(t,e,n){"use strict";function i(t){var e=t.attributes;return y(e,function(t,e,n){var i,r;return e.local?(r=S(e.name,e.prefix),i=r.name):i=e.prefix,t[i]=e.value,t},{})}function r(t,e,n){var i,r=S(e.value),o=t.ns[r.prefix||""],a=r.localName,s=o&&n.getPackage(o);s&&(i=s.xml&&s.xml.typePrefix,i&&0===a.indexOf(i)&&(a=a.slice(i.length)),e.value=s.prefix+":"+a)}function o(t,e,n){var i,a;if(i=t.uri||n){var s=e.getPackage(i);a=s?s.prefix:t.prefix,t.prefix=a,t.uri=i}v(t.attributes,function(n){n.uri===R&&"type"===n.local&&r(t,n,e),o(n,e,null)})}function a(t){x(this,t);var e=this.elementsById={},n=this.references=[],i=this.warnings=[];this.addReference=function(t){n.push(t)},this.addElement=function(t,n){if(!t||!n)throw new Error("[xml-reader] id or ctx must not be null");e[t]=n},this.addWarning=function(t){i.push(t)}}function s(){}function c(){}function u(){}function p(t,e){this.property=t,this.context=e}function l(t,e){this.element=e,this.propertyDesc=t}function h(){}function d(t,e,n){this.model=t,this.type=t.getType(e),this.context=n}function f(t,e,n){this.model=t,this.context=n}function m(t){t instanceof _&&(t={model:t}),x(this,{lax:!1},t)}var y=t(222),v=t(218),g=t(217),x=t(339),b=t(229),E=t(59),w=t(58).parser,_=t(60),S=t(65).parseName,A=t(68),T=A.coerceType,C=A.isSimple,N=t(55),M=N.XSI_TYPE,R=N.DEFAULT_NS_MAP.xsi,P=N.serializeAsType,D=N.aliasToName;s.prototype.handleEnd=function(){},s.prototype.handleText=function(){},s.prototype.handleNode=function(){},c.prototype=new s,c.prototype.handleNode=function(){return this},u.prototype=new s,u.prototype.handleText=function(t){this.body=(this.body||"")+t},p.prototype=new u,p.prototype.handleNode=function(t){if(this.element)throw new Error("expected no sub nodes");return this.element=this.createReference(t),this},p.prototype.handleEnd=function(){this.element.id=this.body},p.prototype.createReference=function(){
return{property:this.property.ns.name,id:""}},l.prototype=new u,l.prototype.handleEnd=function(){var t=this.body,e=this.element,n=this.propertyDesc;t=T(n.type,t),n.isMany?e.get(n.name).push(t):e.set(n.name,t)},h.prototype=Object.create(u.prototype),h.prototype.handleNode=function(t){var e,n=this,i=this.element;return i?n=this.handleChild(t):(i=this.element=this.createElement(t),e=i.id,e&&this.context.addElement(e,i)),n},d.prototype=new h,d.prototype.addReference=function(t){this.context.addReference(t)},d.prototype.handleEnd=function(){var t=this.body,e=this.element,n=e.$descriptor,i=n.bodyProperty;i&&void 0!==t&&(t=T(i.type,t),e.set(i.name,t))},d.prototype.createElement=function(t){var e=i(t),n=this.type,r=n.$descriptor,o=this.context,a=new n({});return v(e,function(t,e){var n=r.propertiesByName[e];n&&n.isReference?o.addReference({element:a,property:n.ns.name,id:t}):(n&&(t=T(n.type,t)),a.set(e,t))}),a},d.prototype.getPropertyForNode=function(t){var e,n,i,r=S(t.local,t.prefix),o=this.type,a=this.model,s=o.$descriptor,c=r.name,u=s.propertiesByName[c];if(u)return P(u)&&(i=t.attributes[M])?(e=i.value,n=a.getType(e),x({},u,{effectiveType:n.$descriptor.name})):u;var p=a.getPackage(r.prefix);if(p){if(e=r.prefix+":"+D(r.localName,s.$pkg),n=a.getType(e),u=g(s.properties,function(t){return!t.isVirtual&&!t.isReference&&!t.isAttribute&&n.hasType(t.type)}))return x({},u,{effectiveType:n.$descriptor.name})}else if(u=g(s.properties,function(t){return!t.isReference&&!t.isAttribute&&"Element"===t.type}))return u;throw new Error("unrecognized element <"+r.name+">")},d.prototype.toString=function(){return"ElementDescriptor["+this.type.$descriptor.name+"]"},d.prototype.valueHandler=function(t,e){return new l(t,e)},d.prototype.referenceHandler=function(t){return new p(t,this.context)},d.prototype.handler=function(t){return"Element"===t?new f(this.model,t,this.context):new d(this.model,t,this.context)},d.prototype.handleChild=function(t){var e,n,i,r;if(e=this.getPropertyForNode(t),i=this.element,n=e.effectiveType||e.type,C(n))return this.valueHandler(e,i);r=e.isReference?this.referenceHandler(e).handleNode(t):this.handler(n).handleNode(t);var o=r.element;return void 0!==o&&(e.isMany?i.get(e.name).push(o):i.set(e.name,o),e.isReference?(x(o,{element:i}),this.context.addReference(o)):o.$parent=i),r},f.prototype=Object.create(h.prototype),f.prototype.createElement=function(t){var e=t.name,n=t.prefix,i=t.ns[n],r=t.attributes;return this.model.createAny(e,i,r)},f.prototype.handleChild=function(t){var e,n=new f(this.model,"Element",this.context).handleNode(t),i=this.element,r=n.element;return void 0!==r&&(e=i.$children=i.$children||[],e.push(r),r.$parent=i),n},f.prototype.handleText=function(t){this.body=this.body||""+t},f.prototype.handleEnd=function(){this.body&&(this.element.$body=this.body)},m.prototype.fromXML=function(t,e,n){function i(){var t,e,n=h.elementsById,i=h.references;for(t=0;e=i[t];t++){var r=e.element,o=n[e.id],a=r.$descriptor.propertiesByName[e.property];if(o||h.addWarning({message:"unresolved reference <"+e.id+">",element:e.element,property:e.property,value:e.id}),a.isMany){var s=r.get(a.name),c=s.indexOf(e);o?s[c]=o:s.splice(c,1)}else r.set(a.name,o)}}function r(t){f.pop().handleEnd()}function s(t){var e=f.peek();o(t,p);try{f.push(e.handleNode(t))}catch(n){var i=this.line,r=this.column,a="unparsable content <"+t.name+"> detected\n	line: "+i+"\n	column: "+r+"\n	nested error: "+n.message;if(!l)throw console.error("could not parse document"),console.error(n),new Error(a);h.addWarning({message:a,error:n}),console.warn("could not parse node"),console.warn(n),f.push(new c)}}function u(t){f.peek().handleText(t)}var p=this.model,l=this.lax,h=new a({parseRoot:e}),d=new w(!0,{xmlns:!0,trim:!0}),f=new E;e.context=h,f.push(e),d.onopentag=s,d.oncdata=d.ontext=u,d.onclosetag=r,d.onend=i,b(function(){var i;try{d.write(t).close()}catch(r){i=r}n(i,i?void 0:e.element,h)})},m.prototype.handler=function(t){return new d(this.model,t)},e.exports=m,e.exports.ElementHandler=d},{217:217,218:218,222:222,229:229,339:339,55:55,58:58,59:59,60:60,65:65,68:68}],57:[function(t,e,n){"use strict";function i(t){return b(t)?t:(t.prefix?t.prefix+":":"")+t.localName}function r(t,e){return e.isGeneric?e.name:w({localName:T(e.ns.localName,e.$pkg)},t)}function o(t,e){return w({localName:e.ns.localName},t)}function a(t){var e=t.$descriptor;return E(e.properties,function(e){var n=e.name;if(!t.hasOwnProperty(n))return!1;var i=t[n];return i===e["default"]?!1:e.isMany?i.length:!0})}function s(t){return t=b(t)?t:""+t,t.replace(R,function(t){return"&#"+k[t]+";"})}function c(t){return E(t,function(t){return t.isAttr})}function u(t){return E(t,function(t){return!t.isAttr})}function p(t,e){this.ns=e}function l(){}function h(t){this.ns=t}function d(t,e){this.body=[],this.attrs=[],this.parent=t,this.ns=e}function f(t,e){d.call(this,t,e)}function m(){this.value="",this.write=function(t){this.value+=t}}function y(t,e){var n=[""];this.append=function(e){return t.write(e),this},this.appendNewLine=function(){return e&&t.write("\n"),this},this.appendIndent=function(){return e&&t.write(n.join("  ")),this},this.indent=function(){return n.push(""),this},this.unindent=function(){return n.pop(),this}}function v(t){function e(e,n){var i=n||new m,r=new y(i,t.format);return t.preamble&&r.append(M),(new d).build(e).serializeTo(r),n?void 0:i.value}return t=w({format:!1,preamble:!0},t||{}),{toXML:e}}var g=t(221),x=t(218),b=t(336),E=t(216),w=t(339),_=t(68),S=t(65).parseName,A=t(55),T=A.nameToAlias,C=A.serializeAsType,N=A.serializeAsProperty,M='<?xml version="1.0" encoding="UTF-8"?>\n',R=/(<|>|'|"|&|\n\r|\n)/g,P=A.DEFAULT_NS_MAP,D=A.XSI_TYPE,k={"\n":"10","\n\r":"10",'"':"34","'":"39","<":"60",">":"62","&":"38"};p.prototype.build=function(t){return this.element=t,this},p.prototype.serializeTo=function(t){t.appendIndent().append("<"+i(this.ns)+">"+this.element.id+"</"+i(this.ns)+">").appendNewLine()},l.prototype.serializeValue=l.prototype.serializeTo=function(t){var e=this.escape;e&&t.append("<![CDATA["),t.append(this.value),e&&t.append("]]>")},l.prototype.build=function(t,e){return this.value=e,"String"===t.type&&R.test(e)&&(this.escape=!0),this},h.prototype=new l,h.prototype.serializeTo=function(t){t.appendIndent().append("<"+i(this.ns)+">"),this.serializeValue(t),t.append("</"+i(this.ns)+">").appendNewLine()},d.prototype.build=function(t){this.element=t;var e=this.parseNsAttributes(t);if(this.ns||(this.ns=this.nsTagName(t.$descriptor)),t.$descriptor.isGeneric)this.parseGeneric(t);else{var n=a(t);this.parseAttributes(c(n)),this.parseContainments(u(n)),this.parseGenericAttributes(t,e)}return this},d.prototype.nsTagName=function(t){var e=this.logNamespaceUsed(t.ns);return r(e,t)},d.prototype.nsPropertyTagName=function(t){var e=this.logNamespaceUsed(t.ns);return o(e,t)},d.prototype.isLocalNs=function(t){return t.uri===this.ns.uri},d.prototype.nsAttributeName=function(t){var e;b(t)?e=S(t):t.ns&&(e=t.ns);var n=this.logNamespaceUsed(e);return this.isLocalNs(n)?{localName:e.localName}:w({localName:e.localName},n)},d.prototype.parseGeneric=function(t){var e=this,n=this.body,i=this.attrs;x(t,function(t,r){"$body"===r?n.push((new l).build({type:"String"},t)):"$children"===r?x(t,function(t){n.push(new d(e).build(t))}):0!==r.indexOf("$")&&i.push({name:r,value:s(t)})})},d.prototype.parseNsAttributes=function(t){var e=this,n=t.$attrs,i=[];return x(n,function(t,n){var r=S(n);"xmlns"===r.prefix?e.logNamespace({prefix:r.localName,uri:t}):r.prefix||"xmlns"!==r.localName?i.push({name:n,value:t}):e.logNamespace({uri:t})}),i},d.prototype.parseGenericAttributes=function(t,e){var n=this;x(e,function(e){if(e.name!==D)try{n.addAttribute(n.nsAttributeName(e.name),e.value)}catch(i){console.warn("[writer] missing namespace information for ",e.name,"=",e.value,"on",t,i)}})},d.prototype.parseContainments=function(t){var e=this,n=this.body,i=this.element;x(t,function(t){var r=i.get(t.name),o=t.isReference,a=t.isMany,s=e.nsPropertyTagName(t);if(a||(r=[r]),t.isBody)n.push((new l).build(t,r[0]));else if(_.isSimple(t.type))x(r,function(e){n.push(new h(s).build(t,e))});else if(o)x(r,function(t){n.push(new p(e,s).build(t))});else{var c=C(t),u=N(t);x(r,function(t){var i;i=c?new f(e,s):u?new d(e,s):new d(e),n.push(i.build(t))})}})},d.prototype.getNamespaces=function(){return this.parent?this.namespaces=this.parent.getNamespaces():this.namespaces||(this.namespaces={prefixMap:{},uriMap:{},used:{}}),this.namespaces},d.prototype.logNamespace=function(t){var e=this.getNamespaces(),n=e.uriMap[t.uri];return n||(e.uriMap[t.uri]=t),e.prefixMap[t.prefix]=t.uri,t},d.prototype.logNamespaceUsed=function(t){var e=this.element,n=e.$model,i=this.getNamespaces(),r=t.prefix,o=t.uri||P[r]||i.prefixMap[r]||(n?(n.getPackage(r)||{}).uri:null);if(!o)throw new Error("no namespace uri given for prefix <"+t.prefix+">");return t=i.uriMap[o],t||(t=this.logNamespace({prefix:r,uri:o})),i.used[t.uri]||(i.used[t.uri]=t),t},d.prototype.parseAttributes=function(t){var e=this,n=this.element;x(t,function(t){e.logNamespaceUsed(t.ns);var i=n.get(t.name);t.isReference&&(i=i.id),e.addAttribute(e.nsAttributeName(t),i)})},d.prototype.addAttribute=function(t,e){var n=this.attrs;b(e)&&(e=s(e)),n.push({name:t,value:e})},d.prototype.serializeAttributes=function(t){function e(){return g(o.used,function(t){var e="xmlns"+(t.prefix?":"+t.prefix:"");return{name:e,value:t.uri}})}var n=this.attrs,r=!this.parent,o=this.namespaces;r&&(n=e().concat(n)),x(n,function(e){t.append(" ").append(i(e.name)).append('="').append(e.value).append('"')})},d.prototype.serializeTo=function(t){var e=this.body.length,n=!(1===this.body.length&&this.body[0]instanceof l);t.appendIndent().append("<"+i(this.ns)),this.serializeAttributes(t),t.append(e?">":" />"),e&&(n&&t.appendNewLine().indent(),x(this.body,function(e){e.serializeTo(t)}),n&&t.unindent().appendIndent(),t.append("</"+i(this.ns)+">")),t.appendNewLine()},f.prototype=new d,f.prototype.build=function(t){var e=t.$descriptor;this.element=t,this.typeNs=this.nsTagName(e);var n=this.typeNs,i=t.$model.getPackage(n.uri),r=i.xml&&i.xml.typePrefix||"";return this.addAttribute(this.nsAttributeName(D),(n.prefix?n.prefix+":":"")+r+e.ns.localName),d.prototype.build.call(this,t)},f.prototype.isLocalNs=function(t){return t.uri===this.typeNs.uri},e.exports=v},{216:216,218:218,221:221,336:336,339:339,55:55,65:65,68:68}],58:[function(t,e,n){(function(e){!function(n){function i(t,e){if(!(this instanceof i))return new i(t,e);var r=this;o(r),r.q=r.c="",r.bufferCheckPosition=n.MAX_BUFFER_LENGTH,r.opt=e||{},r.opt.lowercase=r.opt.lowercase||r.opt.lowercasetags,r.looseCase=r.opt.lowercase?"toLowerCase":"toUpperCase",r.tags=[],r.closed=r.closedRoot=r.sawRoot=!1,r.tag=r.error=null,r.strict=!!t,r.noscript=!(!t&&!r.opt.noscript),r.state=W.BEGIN,r.ENTITIES=Object.create(n.ENTITIES),r.attribList=[],r.opt.xmlns&&(r.ns=Object.create(V)),r.trackPosition=r.opt.position!==!1,r.trackPosition&&(r.position=r.line=r.column=0),d(r,"onready")}function r(t){for(var e=Math.max(n.MAX_BUFFER_LENGTH,10),i=0,r=0,o=C.length;o>r;r++){var a=t[C[r]].length;if(a>e)switch(C[r]){case"textNode":m(t);break;case"cdata":f(t,"oncdata",t.cdata),t.cdata="";break;case"script":f(t,"onscript",t.script),t.script="";break;default:v(t,"Max buffer length exceeded: "+C[r])}i=Math.max(i,a)}t.bufferCheckPosition=n.MAX_BUFFER_LENGTH-i+t.position}function o(t){for(var e=0,n=C.length;n>e;e++)t[C[e]]=""}function a(t){m(t),""!==t.cdata&&(f(t,"oncdata",t.cdata),t.cdata=""),""!==t.script&&(f(t,"onscript",t.script),t.script="")}function s(t,e){return new c(t,e)}function c(t,e){if(!(this instanceof c))return new c(t,e);N.apply(this),this._parser=new i(t,e),this.writable=!0,this.readable=!0;var n=this;this._parser.onend=function(){n.emit("end")},this._parser.onerror=function(t){n.emit("error",t),n._parser.error=null},this._decoder=null,R.forEach(function(t){Object.defineProperty(n,"on"+t,{get:function(){return n._parser["on"+t]},set:function(e){return e?void n.on(t,e):(n.removeAllListeners(t),n._parser["on"+t]=e)},enumerable:!0,configurable:!1})})}function u(t){return t.split("").reduce(function(t,e){return t[e]=!0,t},{})}function p(t){return"[object RegExp]"===Object.prototype.toString.call(t)}function l(t,e){return p(t)?!!e.match(t):t[e]}function h(t,e){return!l(t,e)}function d(t,e,n){t[e]&&t[e](n)}function f(t,e,n){t.textNode&&m(t),d(t,e,n)}function m(t){t.textNode=y(t.opt,t.textNode),t.textNode&&d(t,"ontext",t.textNode),t.textNode=""}function y(t,e){return t.trim&&(e=e.trim()),t.normalize&&(e=e.replace(/\s+/g," ")),e}function v(t,e){return m(t),t.trackPosition&&(e+="\nLine: "+t.line+"\nColumn: "+t.column+"\nChar: "+t.c),e=new Error(e),t.error=e,d(t,"onerror",e),t}function g(t){return t.closedRoot||x(t,"Unclosed root tag"),t.state!==W.BEGIN&&t.state!==W.TEXT&&v(t,"Unexpected end"),m(t),t.c="",t.closed=!0,d(t,"onend"),i.call(t,t.strict,t.opt),t}function x(t,e){if("object"!=typeof t||!(t instanceof i))throw new Error("bad call to strictFail");t.strict&&v(t,e)}function b(t){t.strict||(t.tagName=t.tagName[t.looseCase]());var e=t.tags[t.tags.length-1]||t,n=t.tag={name:t.tagName,attributes:{}};t.opt.xmlns&&(n.ns=e.ns),t.attribList.length=0}function E(t,e){var n=t.indexOf(":"),i=0>n?["",t]:t.split(":"),r=i[0],o=i[1];return e&&"xmlns"===t&&(r="xmlns",o=""),{prefix:r,local:o}}function w(t){if(t.strict||(t.attribName=t.attribName[t.looseCase]()),-1!==t.attribList.indexOf(t.attribName)||t.tag.attributes.hasOwnProperty(t.attribName))return t.attribName=t.attribValue="";if(t.opt.xmlns){var e=E(t.attribName,!0),n=e.prefix,i=e.local;if("xmlns"===n)if("xml"===i&&t.attribValue!==F)x(t,"xml: prefix must be bound to "+F+"\nActual: "+t.attribValue);else if("xmlns"===i&&t.attribValue!==z)x(t,"xmlns: prefix must be bound to "+z+"\nActual: "+t.attribValue);else{var r=t.tag,o=t.tags[t.tags.length-1]||t;r.ns===o.ns&&(r.ns=Object.create(o.ns)),r.ns[i]=t.attribValue}t.attribList.push([t.attribName,t.attribValue])}else t.tag.attributes[t.attribName]=t.attribValue,f(t,"onattribute",{name:t.attribName,value:t.attribValue});t.attribName=t.attribValue=""}function _(t,e){if(t.opt.xmlns){var n=t.tag,i=E(t.tagName);n.prefix=i.prefix,n.local=i.local,n.uri=n.ns[i.prefix]||"",n.prefix&&!n.uri&&(x(t,"Unbound namespace prefix: "+JSON.stringify(t.tagName)),n.uri=i.prefix);var r=t.tags[t.tags.length-1]||t;n.ns&&r.ns!==n.ns&&Object.keys(n.ns).forEach(function(e){f(t,"onopennamespace",{prefix:e,uri:n.ns[e]})});for(var o=0,a=t.attribList.length;a>o;o++){var s=t.attribList[o],c=s[0],u=s[1],p=E(c,!0),l=p.prefix,h=p.local,d=""==l?"":n.ns[l]||"",m={name:c,value:u,prefix:l,local:h,uri:d};l&&"xmlns"!=l&&!d&&(x(t,"Unbound namespace prefix: "+JSON.stringify(l)),m.uri=l),t.tag.attributes[c]=m,f(t,"onattribute",m)}t.attribList.length=0}t.tag.isSelfClosing=!!e,t.sawRoot=!0,t.tags.push(t.tag),f(t,"onopentag",t.tag),e||(t.noscript||"script"!==t.tagName.toLowerCase()?t.state=W.TEXT:t.state=W.SCRIPT,t.tag=null,t.tagName=""),t.attribName=t.attribValue="",t.attribList.length=0}function S(t){if(!t.tagName)return x(t,"Weird empty close tag."),t.textNode+="</>",void(t.state=W.TEXT);if(t.script){if("script"!==t.tagName)return t.script+="</"+t.tagName+">",t.tagName="",void(t.state=W.SCRIPT);f(t,"onscript",t.script),t.script=""}var e=t.tags.length,n=t.tagName;t.strict||(n=n[t.looseCase]());for(var i=n;e--;){var r=t.tags[e];if(r.name===i)break;x(t,"Unexpected close tag")}if(0>e)return x(t,"Unmatched closing tag: "+t.tagName),t.textNode+="</"+t.tagName+">",void(t.state=W.TEXT);t.tagName=n;for(var o=t.tags.length;o-->e;){var a=t.tag=t.tags.pop();t.tagName=t.tag.name,f(t,"onclosetag",t.tagName);var s={};for(var c in a.ns)s[c]=a.ns[c];var u=t.tags[t.tags.length-1]||t;t.opt.xmlns&&a.ns!==u.ns&&Object.keys(a.ns).forEach(function(e){var n=a.ns[e];f(t,"onclosenamespace",{prefix:e,uri:n})})}0===e&&(t.closedRoot=!0),t.tagName=t.attribValue=t.attribName="",t.attribList.length=0,t.state=W.TEXT}function A(t){var e,n=t.entity,i=n.toLowerCase(),r="";return t.ENTITIES[n]?t.ENTITIES[n]:t.ENTITIES[i]?t.ENTITIES[i]:(n=i,"#"===n.charAt(0)&&("x"===n.charAt(1)?(n=n.slice(2),e=parseInt(n,16),r=e.toString(16)):(n=n.slice(1),e=parseInt(n,10),r=e.toString(10))),n=n.replace(/^0+/,""),r.toLowerCase()!==n?(x(t,"Invalid character entity"),"&"+t.entity+";"):String.fromCodePoint(e))}function T(t){var e=this;if(this.error)throw this.error;if(e.closed)return v(e,"Cannot write after close. Assign an onready handler.");if(null===t)return g(e);for(var n=0,i="";e.c=i=t.charAt(n++);)switch(e.trackPosition&&(e.position++,"\n"===i?(e.line++,e.column=0):e.column++),e.state){case W.BEGIN:"<"===i?(e.state=W.OPEN_WAKA,e.startTagPosition=e.position):h(P,i)&&(x(e,"Non-whitespace before first tag."),e.textNode=i,e.state=W.TEXT);continue;case W.TEXT:if(e.sawRoot&&!e.closedRoot){for(var o=n-1;i&&"<"!==i&&"&"!==i;)i=t.charAt(n++),i&&e.trackPosition&&(e.position++,"\n"===i?(e.line++,e.column=0):e.column++);e.textNode+=t.substring(o,n-1)}"<"===i?(e.state=W.OPEN_WAKA,e.startTagPosition=e.position):(!h(P,i)||e.sawRoot&&!e.closedRoot||x(e,"Text data outside of root node."),"&"===i?e.state=W.TEXT_ENTITY:e.textNode+=i);continue;case W.SCRIPT:"<"===i?e.state=W.SCRIPT_ENDING:e.script+=i;continue;case W.SCRIPT_ENDING:"/"===i?e.state=W.CLOSE_TAG:(e.script+="<"+i,e.state=W.SCRIPT);continue;case W.OPEN_WAKA:if("!"===i)e.state=W.SGML_DECL,e.sgmlDecl="";else if(l(P,i));else if(l($,i))e.state=W.OPEN_TAG,e.tagName=i;else if("/"===i)e.state=W.CLOSE_TAG,e.tagName="";else if("?"===i)e.state=W.PROC_INST,e.procInstName=e.procInstBody="";else{if(x(e,"Unencoded <"),e.startTagPosition+1<e.position){var a=e.position-e.startTagPosition;i=new Array(a).join(" ")+i}e.textNode+="<"+i,e.state=W.TEXT}continue;case W.SGML_DECL:(e.sgmlDecl+i).toUpperCase()===L?(f(e,"onopencdata"),e.state=W.CDATA,e.sgmlDecl="",e.cdata=""):e.sgmlDecl+i==="--"?(e.state=W.COMMENT,e.comment="",e.sgmlDecl=""):(e.sgmlDecl+i).toUpperCase()===j?(e.state=W.DOCTYPE,(e.doctype||e.sawRoot)&&x(e,"Inappropriately located doctype declaration"),e.doctype="",e.sgmlDecl=""):">"===i?(f(e,"onsgmldeclaration",e.sgmlDecl),e.sgmlDecl="",e.state=W.TEXT):l(B,i)?(e.state=W.SGML_DECL_QUOTED,e.sgmlDecl+=i):e.sgmlDecl+=i;continue;case W.SGML_DECL_QUOTED:i===e.q&&(e.state=W.SGML_DECL,e.q=""),e.sgmlDecl+=i;continue;case W.DOCTYPE:">"===i?(e.state=W.TEXT,f(e,"ondoctype",e.doctype),e.doctype=!0):(e.doctype+=i,"["===i?e.state=W.DOCTYPE_DTD:l(B,i)&&(e.state=W.DOCTYPE_QUOTED,e.q=i));continue;case W.DOCTYPE_QUOTED:e.doctype+=i,i===e.q&&(e.q="",e.state=W.DOCTYPE);continue;case W.DOCTYPE_DTD:e.doctype+=i,"]"===i?e.state=W.DOCTYPE:l(B,i)&&(e.state=W.DOCTYPE_DTD_QUOTED,e.q=i);continue;case W.DOCTYPE_DTD_QUOTED:e.doctype+=i,i===e.q&&(e.state=W.DOCTYPE_DTD,e.q="");continue;case W.COMMENT:"-"===i?e.state=W.COMMENT_ENDING:e.comment+=i;continue;case W.COMMENT_ENDING:"-"===i?(e.state=W.COMMENT_ENDED,e.comment=y(e.opt,e.comment),e.comment&&f(e,"oncomment",e.comment),e.comment=""):(e.comment+="-"+i,e.state=W.COMMENT);continue;case W.COMMENT_ENDED:">"!==i?(x(e,"Malformed comment"),e.comment+="--"+i,e.state=W.COMMENT):e.state=W.TEXT;continue;case W.CDATA:"]"===i?e.state=W.CDATA_ENDING:e.cdata+=i;continue;case W.CDATA_ENDING:"]"===i?e.state=W.CDATA_ENDING_2:(e.cdata+="]"+i,e.state=W.CDATA);continue;case W.CDATA_ENDING_2:">"===i?(e.cdata&&f(e,"oncdata",e.cdata),f(e,"onclosecdata"),e.cdata="",e.state=W.TEXT):"]"===i?e.cdata+="]":(e.cdata+="]]"+i,e.state=W.CDATA);continue;case W.PROC_INST:"?"===i?e.state=W.PROC_INST_ENDING:l(P,i)?e.state=W.PROC_INST_BODY:e.procInstName+=i;continue;case W.PROC_INST_BODY:if(!e.procInstBody&&l(P,i))continue;"?"===i?e.state=W.PROC_INST_ENDING:e.procInstBody+=i;continue;case W.PROC_INST_ENDING:">"===i?(f(e,"onprocessinginstruction",{name:e.procInstName,body:e.procInstBody}),e.procInstName=e.procInstBody="",e.state=W.TEXT):(e.procInstBody+="?"+i,e.state=W.PROC_INST_BODY);continue;case W.OPEN_TAG:l(G,i)?e.tagName+=i:(b(e),">"===i?_(e):"/"===i?e.state=W.OPEN_TAG_SLASH:(h(P,i)&&x(e,"Invalid character in tag name"),e.state=W.ATTRIB));continue;case W.OPEN_TAG_SLASH:">"===i?(_(e,!0),S(e)):(x(e,"Forward-slash in opening tag not followed by >"),e.state=W.ATTRIB);continue;case W.ATTRIB:if(l(P,i))continue;">"===i?_(e):"/"===i?e.state=W.OPEN_TAG_SLASH:l($,i)?(e.attribName=i,e.attribValue="",e.state=W.ATTRIB_NAME):x(e,"Invalid attribute name");continue;case W.ATTRIB_NAME:"="===i?e.state=W.ATTRIB_VALUE:">"===i?(x(e,"Attribute without value"),e.attribValue=e.attribName,w(e),_(e)):l(P,i)?e.state=W.ATTRIB_NAME_SAW_WHITE:l(G,i)?e.attribName+=i:x(e,"Invalid attribute name");continue;case W.ATTRIB_NAME_SAW_WHITE:if("="===i)e.state=W.ATTRIB_VALUE;else{if(l(P,i))continue;x(e,"Attribute without value"),e.tag.attributes[e.attribName]="",e.attribValue="",f(e,"onattribute",{name:e.attribName,value:""}),e.attribName="",">"===i?_(e):l($,i)?(e.attribName=i,e.state=W.ATTRIB_NAME):(x(e,"Invalid attribute name"),e.state=W.ATTRIB)}continue;case W.ATTRIB_VALUE:if(l(P,i))continue;l(B,i)?(e.q=i,e.state=W.ATTRIB_VALUE_QUOTED):(x(e,"Unquoted attribute value"),e.state=W.ATTRIB_VALUE_UNQUOTED,e.attribValue=i);continue;case W.ATTRIB_VALUE_QUOTED:if(i!==e.q){"&"===i?e.state=W.ATTRIB_VALUE_ENTITY_Q:e.attribValue+=i;continue}w(e),e.q="",e.state=W.ATTRIB_VALUE_CLOSED;continue;case W.ATTRIB_VALUE_CLOSED:l(P,i)?e.state=W.ATTRIB:">"===i?_(e):"/"===i?e.state=W.OPEN_TAG_SLASH:l($,i)?(x(e,"No whitespace between attributes"),e.attribName=i,e.attribValue="",e.state=W.ATTRIB_NAME):x(e,"Invalid attribute name");continue;case W.ATTRIB_VALUE_UNQUOTED:if(h(I,i)){"&"===i?e.state=W.ATTRIB_VALUE_ENTITY_U:e.attribValue+=i;continue}w(e),">"===i?_(e):e.state=W.ATTRIB;continue;case W.CLOSE_TAG:if(e.tagName)">"===i?S(e):l(G,i)?e.tagName+=i:e.script?(e.script+="</"+e.tagName,e.tagName="",e.state=W.SCRIPT):(h(P,i)&&x(e,"Invalid tagname in closing tag"),e.state=W.CLOSE_TAG_SAW_WHITE);else{if(l(P,i))continue;h($,i)?e.script?(e.script+="</"+i,e.state=W.SCRIPT):x(e,"Invalid tagname in closing tag."):e.tagName=i}continue;case W.CLOSE_TAG_SAW_WHITE:if(l(P,i))continue;">"===i?S(e):x(e,"Invalid characters in closing tag");continue;case W.TEXT_ENTITY:case W.ATTRIB_VALUE_ENTITY_Q:case W.ATTRIB_VALUE_ENTITY_U:switch(e.state){case W.TEXT_ENTITY:var s=W.TEXT,c="textNode";break;case W.ATTRIB_VALUE_ENTITY_Q:var s=W.ATTRIB_VALUE_QUOTED,c="attribValue";break;case W.ATTRIB_VALUE_ENTITY_U:var s=W.ATTRIB_VALUE_UNQUOTED,c="attribValue"}";"===i?(e[c]+=A(e),e.entity="",e.state=s):l(O,i)?e.entity+=i:(x(e,"Invalid character entity"),e[c]+="&"+e.entity+i,e.entity="",e.state=s);continue;default:throw new Error(e,"Unknown state: "+e.state)}return e.position>=e.bufferCheckPosition&&r(e),e}n.parser=function(t,e){return new i(t,e)},n.SAXParser=i,n.SAXStream=c,n.createStream=s,n.MAX_BUFFER_LENGTH=65536;var C=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];n.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(t){function e(){this.__proto__=t}return e.prototype=t,new e}),Object.getPrototypeOf||(Object.getPrototypeOf=function(t){return t.__proto__}),Object.keys||(Object.keys=function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}),i.prototype={end:function(){g(this)},write:T,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){a(this)}};try{var N=t("stream").Stream}catch(M){var N=function(){}}var R=n.EVENTS.filter(function(t){return"error"!==t&&"end"!==t});c.prototype=Object.create(N.prototype,{constructor:{value:c}}),c.prototype.write=function(n){if("function"==typeof e&&"function"==typeof e.isBuffer&&e.isBuffer(n)){if(!this._decoder){var i=t("string_decoder").StringDecoder;this._decoder=new i("utf8")}n=this._decoder.write(n)}return this._parser.write(n.toString()),this.emit("data",n),!0},c.prototype.end=function(t){return t&&t.length&&this.write(t),this._parser.end(),!0},c.prototype.on=function(t,e){var n=this;return n._parser["on"+t]||-1===R.indexOf(t)||(n._parser["on"+t]=function(){var e=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);e.splice(0,0,t),n.emit.apply(n,e)}),N.prototype.on.call(n,t,e)};var P="\r\n	 ",D="0124356789",k="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",B="'\"",O=D+k+"#",I=P+">",L="[CDATA[",j="DOCTYPE",F="http://www.w3.org/XML/1998/namespace",z="http://www.w3.org/2000/xmlns/",V={xml:F,xmlns:z};P=u(P),D=u(D),k=u(k);var $=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,G=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040\.\d-]/;B=u(B),O=u(O),I=u(I);var W=0;n.STATE={BEGIN:W++,TEXT:W++,TEXT_ENTITY:W++,OPEN_WAKA:W++,SGML_DECL:W++,SGML_DECL_QUOTED:W++,DOCTYPE:W++,DOCTYPE_QUOTED:W++,DOCTYPE_DTD:W++,DOCTYPE_DTD_QUOTED:W++,COMMENT_STARTING:W++,COMMENT:W++,COMMENT_ENDING:W++,COMMENT_ENDED:W++,CDATA:W++,CDATA_ENDING:W++,CDATA_ENDING_2:W++,PROC_INST:W++,PROC_INST_BODY:W++,PROC_INST_ENDING:W++,OPEN_TAG:W++,OPEN_TAG_SLASH:W++,ATTRIB:W++,ATTRIB_NAME:W++,ATTRIB_NAME_SAW_WHITE:W++,ATTRIB_VALUE:W++,ATTRIB_VALUE_QUOTED:W++,ATTRIB_VALUE_CLOSED:W++,ATTRIB_VALUE_UNQUOTED:W++,ATTRIB_VALUE_ENTITY_Q:W++,ATTRIB_VALUE_ENTITY_U:W++,CLOSE_TAG:W++,CLOSE_TAG_SAW_WHITE:W++,SCRIPT:W++,SCRIPT_ENDING:W++},n.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,"int":8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(n.ENTITIES).forEach(function(t){var e=n.ENTITIES[t],i="number"==typeof e?String.fromCharCode(e):e;n.ENTITIES[t]=i});for(var W in n.STATE)n.STATE[n.STATE[W]]=W;W=n.STATE,String.fromCodePoint||!function(){var t=String.fromCharCode,e=Math.floor,n=function(){var n,i,r=16384,o=[],a=-1,s=arguments.length;if(!s)return"";for(var c="";++a<s;){var u=Number(arguments[a]);if(!isFinite(u)||0>u||u>1114111||e(u)!=u)throw RangeError("Invalid code point: "+u);65535>=u?o.push(u):(u-=65536,n=(u>>10)+55296,i=u%1024+56320,o.push(n,i)),(a+1==s||o.length>r)&&(c+=t.apply(null,o),o.length=0)}return c};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:n,configurable:!0,writable:!0}):String.fromCodePoint=n}()}("undefined"==typeof n?sax={}:n)}).call(this,void 0)},{undefined:void 0}],59:[function(e,n,i){!function(e){"use strict";function r(){this.data=[null],this.top=0}function o(){return new r}r.prototype.clear=function(){return this.data=[null],this.top=0,this},r.prototype.length=function(){return this.top},r.prototype.peek=function(){return this.data[this.top]},r.prototype.pop=function(){return this.top>0?(this.top--,this.data.pop()):void 0},r.prototype.push=function(t){return this.data[++this.top]=t,this},"undefined"!=typeof i?n.exports=o:"function"==typeof t?t(function(){return o}):e.stack=o}(this)},{}],60:[function(t,e,n){e.exports=t(64)},{64:64}],61:[function(t,e,n){"use strict";function i(){}i.prototype.get=function(t){return this.$model.properties.get(this,t)},i.prototype.set=function(t,e){this.$model.properties.set(this,t,e)},e.exports=i},{}],62:[function(t,e,n){"use strict";function i(t){this.ns=t,this.name=t.name,this.allTypes=[],this.properties=[],this.propertiesByName={}}var r=t(345),o=t(339),a=t(218),s=t(65).parseName;e.exports=i,i.prototype.build=function(){return r(this,["ns","name","allTypes","properties","propertiesByName","bodyProperty"])},i.prototype.addProperty=function(t,e){this.addNamedProperty(t,!0);var n=this.properties;void 0!==e?n.splice(e,0,t):n.push(t)},i.prototype.replaceProperty=function(t,e){var n=t.ns,i=this.properties,r=this.propertiesByName,o=t.name!==e.name;if(t.isBody){if(!e.isBody)throw new Error("property <"+e.ns.name+"> must be body property to refine <"+t.ns.name+">");this.setBodyProperty(e,!1)}this.addNamedProperty(e,o);var a=i.indexOf(t);if(-1===a)throw new Error("property <"+n.name+"> not found in property list");i[a]=e,r[n.name]=r[n.localName]=e},i.prototype.redefineProperty=function(t){var e=t.ns.prefix,n=t.redefines.split("#"),i=s(n[0],e),r=s(n[1],i.prefix).name,o=this.propertiesByName[r];if(!o)throw new Error("refined property <"+r+"> not found");this.replaceProperty(o,t),delete t.redefines},i.prototype.addNamedProperty=function(t,e){var n=t.ns,i=this.propertiesByName;e&&(this.assertNotDefined(t,n.name),this.assertNotDefined(t,n.localName)),i[n.name]=i[n.localName]=t},i.prototype.removeNamedProperty=function(t){var e=t.ns,n=this.propertiesByName;delete n[e.name],delete n[e.localName]},i.prototype.setBodyProperty=function(t,e){if(e&&this.bodyProperty)throw new Error("body property defined multiple times (<"+this.bodyProperty.ns.name+">, <"+t.ns.name+">)");this.bodyProperty=t},i.prototype.addIdProperty=function(t){var e=s(t,this.ns.prefix),n={name:e.localName,type:"String",isAttr:!0,ns:e};this.addProperty(n,0)},i.prototype.assertNotDefined=function(t,e){var n=t.name,i=this.propertiesByName[n];if(i)throw new Error("property <"+n+"> already defined; override of <"+i.definedBy.ns.name+"#"+i.ns.name+"> by <"+t.definedBy.ns.name+"#"+t.ns.name+"> not allowed without redefines")},i.prototype.hasProperty=function(t){return this.propertiesByName[t]},i.prototype.addTrait=function(t){var e=this.allTypes;-1===e.indexOf(t)&&(a(t.properties,function(e){e=o({},e,{name:e.ns.localName}),Object.defineProperty(e,"definedBy",{value:t}),e.redefines?this.redefineProperty(e):(e.isBody&&this.setBodyProperty(e),
this.addProperty(e))},this),e.push(t))}},{218:218,339:339,345:345,65:65}],63:[function(t,e,n){"use strict";function i(t,e){this.model=t,this.properties=e}var r=t(218),o=t(61);e.exports=i,i.prototype.createType=function(t){function e(t){i.define(this,"$type",{value:s,enumerable:!0}),i.define(this,"$attrs",{value:{}}),i.define(this,"$parent",{writable:!0}),r(t,function(t,e){this.set(e,t)},this)}var n=this.model,i=this.properties,a=Object.create(o.prototype);r(t.properties,function(t){t.isMany||void 0===t["default"]||(a[t.name]=t["default"])}),i.defineModel(a,n),i.defineDescriptor(a,t);var s=t.ns.name;return e.prototype=a,e.hasType=a.$instanceOf=this.model.hasType,i.defineModel(e,n),i.defineDescriptor(e,t),e}},{218:218,61:61}],64:[function(t,e,n){"use strict";function i(t,e){e=e||{},this.properties=new p(this),this.factory=new c(this,this.properties),this.registry=new u(t,this.properties,e),this.typeCache={}}var r=t(336),o=t(334),a=t(218),s=t(217),c=t(63),u=t(67),p=t(66),l=t(65).parseName;e.exports=i,i.prototype.create=function(t,e){var n=this.getType(t);if(!n)throw new Error("unknown type <"+t+">");return new n(e)},i.prototype.getType=function(t){var e=this.typeCache,n=r(t)?t:t.ns.name,i=e[n];return i||(t=this.registry.getEffectiveDescriptor(n),i=e[n]=this.factory.createType(t)),i},i.prototype.createAny=function(t,e,n){var i=l(t),r={$type:t},s={name:t,isGeneric:!0,ns:{prefix:i.prefix,localName:i.localName,uri:e}};return this.properties.defineDescriptor(r,s),this.properties.defineModel(r,this),this.properties.define(r,"$parent",{enumerable:!1,writable:!0}),a(n,function(t,e){o(t)&&void 0!==t.value?r[t.name]=t.value:r[e]=t}),r},i.prototype.getPackage=function(t){return this.registry.getPackage(t)},i.prototype.getPackages=function(){return this.registry.getPackages()},i.prototype.getElementDescriptor=function(t){return t.$descriptor},i.prototype.hasType=function(t,e){void 0===e&&(e=t,t=this);var n=t.$model.getElementDescriptor(t);return!!s(n.allTypes,function(t){return t.name===e})},i.prototype.getPropertyDescriptor=function(t,e){return this.getElementDescriptor(t).propertiesByName[e]}},{217:217,218:218,334:334,336:336,63:63,65:65,66:66,67:67}],65:[function(t,e,n){"use strict";e.exports.parseName=function(t,e){var n,i,r=t.split(/:/);if(1===r.length)n=t,i=e;else{if(2!==r.length)throw new Error("expected <prefix:localName> or <localName>, got "+t);n=r[1],i=r[0]}return t=(i?i+":":"")+n,{name:t,prefix:i,localName:n}}},{}],66:[function(t,e,n){"use strict";function i(t){this.model=t}e.exports=i,i.prototype.set=function(t,e,n){var i=this.model.getPropertyDescriptor(t,e);i?Object.defineProperty(t,i.name,{enumerable:!i.isReference,writable:!0,value:n}):t.$attrs[e]=n},i.prototype.get=function(t,e){var n=this.model.getPropertyDescriptor(t,e);if(!n)return t.$attrs[e];var i=n.name;return!t[i]&&n.isMany&&Object.defineProperty(t,i,{enumerable:!n.isReference,writable:!0,value:[]}),t[i]},i.prototype.define=function(t,e,n){Object.defineProperty(t,e,n)},i.prototype.defineDescriptor=function(t,e){this.define(t,"$descriptor",{value:e})},i.prototype.defineModel=function(t,e){this.define(t,"$model",{value:e})}},{}],67:[function(t,e,n){"use strict";function i(t,e,n){this.options=r({generateId:"id"},n||{}),this.packageMap={},this.typeMap={},this.packages=[],this.properties=e,o(t,this.registerPackage,this)}var r=t(339),o=t(218),a=t(68),s=t(62),c=t(65).parseName,u=a.isBuiltIn;e.exports=i,i.prototype.getPackage=function(t){return this.packageMap[t]},i.prototype.getPackages=function(){return this.packages},i.prototype.registerPackage=function(t){t=r({},t),o(t.types,function(e){this.registerType(e,t)},this),this.packageMap[t.uri]=this.packageMap[t.prefix]=t,this.packages.push(t)},i.prototype.registerType=function(t,e){t=r({},t,{superClass:(t.superClass||[]).slice(),"extends":(t["extends"]||[]).slice(),properties:(t.properties||[]).slice()});var n=c(t.name,e.prefix),i=n.name,a={};o(t.properties,function(t){var e=c(t.name,n.prefix),i=e.name;u(t.type)||(t.type=c(t.type,e.prefix).name),r(t,{ns:e,name:i}),a[i]=t}),r(t,{ns:n,name:i,propertiesByName:a}),o(t["extends"],function(t){var e=this.typeMap[t];e.traits=e.traits||[],e.traits.push(i)},this),this.definePackage(t,e),this.typeMap[i]=t},i.prototype.mapTypes=function(t,e){function n(n){var i=c(n,u(n)?"":t.prefix);r.mapTypes(i,e)}var i=u(t.name)?{name:t.name}:this.typeMap[t.name],r=this;if(!i)throw new Error("unknown type <"+t.name+">");o(i.superClass,n),e(i),o(i.traits,n)},i.prototype.getEffectiveDescriptor=function(t){var e=c(t),n=new s(e);this.mapTypes(e,function(t){n.addTrait(t)});var i=this.options.generateId;i&&!n.hasProperty(i)&&n.addIdProperty(i);var r=n.build();return this.definePackage(r,r.allTypes[r.allTypes.length-1].$pkg),r},i.prototype.definePackage=function(t,e){this.properties.define(t,"$pkg",{value:e})}},{218:218,339:339,62:62,65:65,68:68}],68:[function(t,e,n){"use strict";var i={String:!0,Boolean:!0,Integer:!0,Real:!0,Element:!0},r={String:function(t){return t},Boolean:function(t){return"true"===t},Integer:function(t){return parseInt(t,10)},Real:function(t){return parseFloat(t,10)}};e.exports.coerceType=function(t,e){var n=r[t];return n?n(e):e},e.exports.isBuiltIn=function(t){return!!i[t]},e.exports.isSimple=function(t){return!!r[t]}},{}],69:[function(t,e,n){e.exports={name:"BPMN20",uri:"http://www.omg.org/spec/BPMN/20100524/MODEL",associations:[],types:[{name:"Interface",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"operations",type:"Operation",isMany:!0},{name:"implementationRef",type:"String",isAttr:!0}]},{name:"Operation",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"inMessageRef",type:"Message",isAttr:!0,isReference:!0},{name:"outMessageRef",type:"Message",isAttr:!0,isReference:!0},{name:"errorRefs",type:"Error",isMany:!0,isReference:!0},{name:"implementationRef",type:"String",isAttr:!0}]},{name:"EndPoint",superClass:["RootElement"]},{name:"Auditing",superClass:["BaseElement"]},{name:"GlobalTask",superClass:["CallableElement"],properties:[{name:"resources",type:"ResourceRole",isMany:!0}]},{name:"Monitoring",superClass:["BaseElement"]},{name:"Performer",superClass:["ResourceRole"]},{name:"Process",superClass:["FlowElementsContainer","CallableElement"],properties:[{name:"processType",type:"ProcessType",isAttr:!0},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"properties",type:"Property",isMany:!0},{name:"artifacts",type:"Artifact",isMany:!0},{name:"resources",type:"ResourceRole",isMany:!0},{name:"correlationSubscriptions",type:"CorrelationSubscription",isMany:!0},{name:"supports",type:"Process",isMany:!0,isReference:!0},{name:"definitionalCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"isExecutable",isAttr:!0,type:"Boolean"}]},{name:"LaneSet",superClass:["BaseElement"],properties:[{name:"lanes",type:"Lane",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Lane",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"childLaneSet",type:"LaneSet",xml:{serialize:"xsi:type"}},{name:"partitionElementRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"flowNodeRef",type:"FlowNode",isMany:!0,isReference:!0},{name:"partitionElement",type:"BaseElement"}]},{name:"GlobalManualTask",superClass:["GlobalTask"]},{name:"ManualTask",superClass:["Task"]},{name:"UserTask",superClass:["Task"],properties:[{name:"renderings",type:"Rendering",isMany:!0},{name:"implementation",isAttr:!0,type:"String"}]},{name:"Rendering",superClass:["BaseElement"]},{name:"HumanPerformer",superClass:["Performer"]},{name:"PotentialOwner",superClass:["HumanPerformer"]},{name:"GlobalUserTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"renderings",type:"Rendering",isMany:!0}]},{name:"Gateway",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"gatewayDirection",type:"GatewayDirection","default":"Unspecified",isAttr:!0}]},{name:"EventBasedGateway",superClass:["Gateway"],properties:[{name:"instantiate","default":!1,isAttr:!0,type:"Boolean"},{name:"eventGatewayType",type:"EventBasedGatewayType",isAttr:!0,"default":"Exclusive"}]},{name:"ComplexGateway",superClass:["Gateway"],properties:[{name:"activationCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ExclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"InclusiveGateway",superClass:["Gateway"],properties:[{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0}]},{name:"ParallelGateway",superClass:["Gateway"]},{name:"RootElement",isAbstract:!0,superClass:["BaseElement"]},{name:"Relationship",superClass:["BaseElement"],properties:[{name:"type",isAttr:!0,type:"String"},{name:"direction",type:"RelationshipDirection",isAttr:!0},{name:"source",isMany:!0,isReference:!0,type:"Element"},{name:"target",isMany:!0,isReference:!0,type:"Element"}]},{name:"BaseElement",isAbstract:!0,properties:[{name:"id",isAttr:!0,type:"String"},{name:"documentation",type:"Documentation",isMany:!0},{name:"extensionDefinitions",type:"ExtensionDefinition",isMany:!0,isReference:!0},{name:"extensionElements",type:"ExtensionElements"}]},{name:"Extension",properties:[{name:"mustUnderstand","default":!1,isAttr:!0,type:"Boolean"},{name:"definition",type:"ExtensionDefinition"}]},{name:"ExtensionDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"extensionAttributeDefinitions",type:"ExtensionAttributeDefinition",isMany:!0}]},{name:"ExtensionAttributeDefinition",properties:[{name:"name",isAttr:!0,type:"String"},{name:"type",isAttr:!0,type:"String"},{name:"isReference","default":!1,isAttr:!0,type:"Boolean"},{name:"extensionDefinition",type:"ExtensionDefinition",isAttr:!0,isReference:!0}]},{name:"ExtensionElements",properties:[{name:"valueRef",isAttr:!0,isReference:!0,type:"Element"},{name:"values",type:"Element",isMany:!0},{name:"extensionAttributeDefinition",type:"ExtensionAttributeDefinition",isAttr:!0,isReference:!0}]},{name:"Documentation",superClass:["BaseElement"],properties:[{name:"text",type:"String",isBody:!0},{name:"textFormat","default":"text/plain",isAttr:!0,type:"String"}]},{name:"Event",isAbstract:!0,superClass:["FlowNode","InteractionNode"],properties:[{name:"properties",type:"Property",isMany:!0}]},{name:"IntermediateCatchEvent",superClass:["CatchEvent"]},{name:"IntermediateThrowEvent",superClass:["ThrowEvent"]},{name:"EndEvent",superClass:["ThrowEvent"]},{name:"StartEvent",superClass:["CatchEvent"],properties:[{name:"isInterrupting","default":!0,isAttr:!0,type:"Boolean"}]},{name:"ThrowEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"inputSet",type:"InputSet"},{name:"eventDefinitionRefs",type:"EventDefinition",isMany:!0,isReference:!0},{name:"dataInputAssociation",type:"DataInputAssociation",isMany:!0},{name:"dataInputs",type:"DataInput",isMany:!0},{name:"eventDefinitions",type:"EventDefinition",isMany:!0}]},{name:"CatchEvent",isAbstract:!0,superClass:["Event"],properties:[{name:"parallelMultiple",isAttr:!0,type:"Boolean","default":!1},{name:"outputSet",type:"OutputSet"},{name:"eventDefinitionRefs",type:"EventDefinition",isMany:!0,isReference:!0},{name:"dataOutputAssociation",type:"DataOutputAssociation",isMany:!0},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"eventDefinitions",type:"EventDefinition",isMany:!0}]},{name:"BoundaryEvent",superClass:["CatchEvent"],properties:[{name:"cancelActivity","default":!0,isAttr:!0,type:"Boolean"},{name:"attachedToRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"EventDefinition",isAbstract:!0,superClass:["RootElement"]},{name:"CancelEventDefinition",superClass:["EventDefinition"]},{name:"ErrorEventDefinition",superClass:["EventDefinition"],properties:[{name:"errorRef",type:"Error",isAttr:!0,isReference:!0}]},{name:"TerminateEventDefinition",superClass:["EventDefinition"]},{name:"EscalationEventDefinition",superClass:["EventDefinition"],properties:[{name:"escalationRef",type:"Escalation",isAttr:!0,isReference:!0}]},{name:"Escalation",properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"escalationCode",isAttr:!0,type:"String"}],superClass:["RootElement"]},{name:"CompensateEventDefinition",superClass:["EventDefinition"],properties:[{name:"waitForCompletion",isAttr:!0,type:"Boolean"},{name:"activityRef",type:"Activity",isAttr:!0,isReference:!0}]},{name:"TimerEventDefinition",superClass:["EventDefinition"],properties:[{name:"timeDate",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeCycle",type:"Expression",xml:{serialize:"xsi:type"}},{name:"timeDuration",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"LinkEventDefinition",superClass:["EventDefinition"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"target",type:"LinkEventDefinition",isAttr:!0,isReference:!0},{name:"source",type:"LinkEventDefinition",isMany:!0,isReference:!0}]},{name:"MessageEventDefinition",superClass:["EventDefinition"],properties:[{name:"messageRef",type:"Message",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"ConditionalEventDefinition",superClass:["EventDefinition"],properties:[{name:"condition",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"SignalEventDefinition",superClass:["EventDefinition"],properties:[{name:"signalRef",type:"Signal",isAttr:!0,isReference:!0}]},{name:"Signal",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ImplicitThrowEvent",superClass:["ThrowEvent"]},{name:"DataState",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"ItemAwareElement",superClass:["BaseElement"],properties:[{name:"itemSubjectRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"dataState",type:"DataState"}]},{name:"DataAssociation",superClass:["BaseElement"],properties:[{name:"transformation",type:"FormalExpression"},{name:"assignment",type:"Assignment",isMany:!0},{name:"sourceRef",type:"ItemAwareElement",isMany:!0,isReference:!0},{name:"targetRef",type:"ItemAwareElement",isReference:!0}]},{name:"DataInput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"},{name:"inputSetRefs",type:"InputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"inputSetWithOptional",type:"InputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"inputSetWithWhileExecuting",type:"InputSet",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"DataOutput",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"},{name:"outputSetRefs",type:"OutputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"outputSetWithOptional",type:"OutputSet",isVirtual:!0,isMany:!0,isReference:!0},{name:"outputSetWithWhileExecuting",type:"OutputSet",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"InputSet",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"dataInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"optionalInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"whileExecutingInputRefs",type:"DataInput",isMany:!0,isReference:!0},{name:"outputSetRefs",type:"OutputSet",isMany:!0,isReference:!0}]},{name:"OutputSet",superClass:["BaseElement"],properties:[{name:"dataOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"inputSetRefs",type:"InputSet",isMany:!0,isReference:!0},{name:"optionalOutputRefs",type:"DataOutput",isMany:!0,isReference:!0},{name:"whileExecutingOutputRefs",type:"DataOutput",isMany:!0,isReference:!0}]},{name:"Property",superClass:["ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"}]},{name:"DataInputAssociation",superClass:["DataAssociation"]},{name:"DataOutputAssociation",superClass:["DataAssociation"]},{name:"InputOutputSpecification",superClass:["BaseElement"],properties:[{name:"dataInputs",type:"DataInput",isMany:!0},{name:"dataOutputs",type:"DataOutput",isMany:!0},{name:"inputSets",type:"InputSet",isMany:!0},{name:"outputSets",type:"OutputSet",isMany:!0}]},{name:"DataObject",superClass:["FlowElement","ItemAwareElement"],properties:[{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"}]},{name:"InputOutputBinding",properties:[{name:"inputDataRef",type:"InputSet",isAttr:!0,isReference:!0},{name:"outputDataRef",type:"OutputSet",isAttr:!0,isReference:!0},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"Assignment",superClass:["BaseElement"],properties:[{name:"from",type:"Expression",xml:{serialize:"xsi:type"}},{name:"to",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"DataStore",superClass:["RootElement","ItemAwareElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"capacity",isAttr:!0,type:"Integer"},{name:"isUnlimited","default":!0,isAttr:!0,type:"Boolean"}]},{name:"DataStoreReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataStoreRef",type:"DataStore",isAttr:!0,isReference:!0}]},{name:"DataObjectReference",superClass:["ItemAwareElement","FlowElement"],properties:[{name:"dataObjectRef",type:"DataObject",isAttr:!0,isReference:!0}]},{name:"ConversationLink",superClass:["BaseElement"],properties:[{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"ConversationAssociation",superClass:["BaseElement"],properties:[{name:"innerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0},{name:"outerConversationNodeRef",type:"ConversationNode",isAttr:!0,isReference:!0}]},{name:"CallConversation",superClass:["ConversationNode"],properties:[{name:"calledCollaborationRef",type:"Collaboration",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"Conversation",superClass:["ConversationNode"]},{name:"SubConversation",superClass:["ConversationNode"],properties:[{name:"conversationNodes",type:"ConversationNode",isMany:!0}]},{name:"ConversationNode",isAbstract:!0,superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRefs",type:"Participant",isMany:!0,isReference:!0},{name:"messageFlowRefs",type:"MessageFlow",isMany:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0}]},{name:"GlobalConversation",superClass:["Collaboration"]},{name:"PartnerEntity",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"PartnerRole",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"participantRef",type:"Participant",isMany:!0,isReference:!0}]},{name:"CorrelationProperty",superClass:["RootElement"],properties:[{name:"correlationPropertyRetrievalExpression",type:"CorrelationPropertyRetrievalExpression",isMany:!0},{name:"name",isAttr:!0,type:"String"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Error",superClass:["RootElement"],properties:[{name:"structureRef",type:"ItemDefinition",isAttr:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"},{name:"errorCode",isAttr:!0,type:"String"}]},{name:"CorrelationKey",superClass:["BaseElement"],properties:[{name:"correlationPropertyRef",type:"CorrelationProperty",isMany:!0,isReference:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Expression",superClass:["BaseElement"],isAbstract:!0},{name:"FormalExpression",superClass:["Expression"],properties:[{name:"language",isAttr:!0,type:"String"},{name:"body",type:"String",isBody:!0},{name:"evaluatesToTypeRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"Message",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"itemRef",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"ItemDefinition",superClass:["RootElement"],properties:[{name:"itemKind",type:"ItemKind",isAttr:!0},{name:"structureRef",type:"String",isAttr:!0},{name:"isCollection","default":!1,isAttr:!0,type:"Boolean"},{name:"import",type:"Import",isAttr:!0,isReference:!0}]},{name:"FlowElement",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"auditing",type:"Auditing"},{name:"monitoring",type:"Monitoring"},{name:"categoryValueRef",type:"CategoryValue",isMany:!0,isReference:!0}]},{name:"SequenceFlow",superClass:["FlowElement"],properties:[{name:"isImmediate",isAttr:!0,type:"Boolean"},{name:"conditionExpression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"sourceRef",type:"FlowNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"FlowNode",isAttr:!0,isReference:!0}]},{name:"FlowElementsContainer",isAbstract:!0,superClass:["BaseElement"],properties:[{name:"laneSets",type:"LaneSet",isMany:!0},{name:"flowElements",type:"FlowElement",isMany:!0}]},{name:"CallableElement",isAbstract:!0,superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"supportedInterfaceRefs",type:"Interface",isMany:!0,isReference:!0},{name:"ioBinding",type:"InputOutputBinding",isMany:!0,xml:{serialize:"property"}}]},{name:"FlowNode",isAbstract:!0,superClass:["FlowElement"],properties:[{name:"incoming",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"outgoing",type:"SequenceFlow",isMany:!0,isReference:!0},{name:"lanes",type:"Lane",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"CorrelationPropertyRetrievalExpression",superClass:["BaseElement"],properties:[{name:"messagePath",type:"FormalExpression"},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"CorrelationPropertyBinding",superClass:["BaseElement"],properties:[{name:"dataPath",type:"FormalExpression"},{name:"correlationPropertyRef",type:"CorrelationProperty",isAttr:!0,isReference:!0}]},{name:"Resource",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"resourceParameters",type:"ResourceParameter",isMany:!0}]},{name:"ResourceParameter",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isRequired",isAttr:!0,type:"Boolean"},{name:"type",type:"ItemDefinition",isAttr:!0,isReference:!0}]},{name:"CorrelationSubscription",superClass:["BaseElement"],properties:[{name:"correlationKeyRef",type:"CorrelationKey",isAttr:!0,isReference:!0},{name:"correlationPropertyBinding",type:"CorrelationPropertyBinding",isMany:!0}]},{name:"MessageFlow",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"sourceRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"targetRef",type:"InteractionNode",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"MessageFlowAssociation",superClass:["BaseElement"],properties:[{name:"innerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0},{name:"outerMessageFlowRef",type:"MessageFlow",isAttr:!0,isReference:!0}]},{name:"InteractionNode",isAbstract:!0,properties:[{name:"incomingConversationLinks",type:"ConversationLink",isVirtual:!0,isMany:!0,isReference:!0},{name:"outgoingConversationLinks",type:"ConversationLink",isVirtual:!0,isMany:!0,isReference:!0}]},{name:"Participant",superClass:["InteractionNode","BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"interfaceRefs",type:"Interface",isMany:!0,isReference:!0},{name:"participantMultiplicity",type:"ParticipantMultiplicity"},{name:"endPointRefs",type:"EndPoint",isMany:!0,isReference:!0},{name:"processRef",type:"Process",isAttr:!0,isReference:!0}]},{name:"ParticipantAssociation",superClass:["BaseElement"],properties:[{name:"innerParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"outerParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"ParticipantMultiplicity",properties:[{name:"minimum","default":0,isAttr:!0,type:"Integer"},{name:"maximum","default":1,isAttr:!0,type:"Integer"}]},{name:"Collaboration",superClass:["RootElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"isClosed",isAttr:!0,type:"Boolean"},{name:"choreographyRef",type:"Choreography",isMany:!0,isReference:!0},{name:"artifacts",type:"Artifact",isMany:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0},{name:"messageFlowAssociations",type:"MessageFlowAssociation",isMany:!0},{name:"conversationAssociations",type:"ConversationAssociation"},{name:"participants",type:"Participant",isMany:!0},{name:"messageFlows",type:"MessageFlow",isMany:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"conversations",type:"ConversationNode",isMany:!0},{name:"conversationLinks",type:"ConversationLink",isMany:!0}]},{name:"ChoreographyActivity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"participantRefs",type:"Participant",isMany:!0,isReference:!0},{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0},{name:"correlationKeys",type:"CorrelationKey",isMany:!0},{name:"loopType",type:"ChoreographyLoopType","default":"None",isAttr:!0}]},{name:"CallChoreography",superClass:["ChoreographyActivity"],properties:[{name:"calledChoreographyRef",type:"Choreography",isAttr:!0,isReference:!0},{name:"participantAssociations",type:"ParticipantAssociation",isMany:!0}]},{name:"SubChoreography",superClass:["ChoreographyActivity","FlowElementsContainer"],properties:[{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"ChoreographyTask",superClass:["ChoreographyActivity"],properties:[{name:"messageFlowRef",type:"MessageFlow",isMany:!0,isReference:!0}]},{name:"Choreography",superClass:["FlowElementsContainer","Collaboration"]},{name:"GlobalChoreographyTask",superClass:["Choreography"],properties:[{name:"initiatingParticipantRef",type:"Participant",isAttr:!0,isReference:!0}]},{name:"TextAnnotation",superClass:["Artifact"],properties:[{name:"text",type:"String"},{name:"textFormat","default":"text/plain",isAttr:!0,type:"String"}]},{name:"Group",superClass:["Artifact"],properties:[{name:"categoryValueRef",type:"CategoryValue",isAttr:!0,isReference:!0}]},{name:"Association",superClass:["Artifact"],properties:[{name:"associationDirection",type:"AssociationDirection",isAttr:!0},{name:"sourceRef",type:"BaseElement",isAttr:!0,isReference:!0},{name:"targetRef",type:"BaseElement",isAttr:!0,isReference:!0}]},{name:"Category",superClass:["RootElement"],properties:[{name:"categoryValue",type:"CategoryValue",isMany:!0},{name:"name",isAttr:!0,type:"String"}]},{name:"Artifact",isAbstract:!0,superClass:["BaseElement"]},{name:"CategoryValue",superClass:["BaseElement"],properties:[{name:"categorizedFlowElements",type:"FlowElement",isVirtual:!0,isMany:!0,isReference:!0},{name:"value",isAttr:!0,type:"String"}]},{name:"Activity",isAbstract:!0,superClass:["FlowNode"],properties:[{name:"isForCompensation","default":!1,isAttr:!0,type:"Boolean"},{name:"default",type:"SequenceFlow",isAttr:!0,isReference:!0},{name:"ioSpecification",type:"InputOutputSpecification",xml:{serialize:"property"}},{name:"boundaryEventRefs",type:"BoundaryEvent",isMany:!0,isReference:!0},{name:"properties",type:"Property",isMany:!0},{name:"dataInputAssociations",type:"DataInputAssociation",isMany:!0},{name:"dataOutputAssociations",type:"DataOutputAssociation",isMany:!0},{name:"startQuantity","default":1,isAttr:!0,type:"Integer"},{name:"resources",type:"ResourceRole",isMany:!0},{name:"completionQuantity","default":1,isAttr:!0,type:"Integer"},{name:"loopCharacteristics",type:"LoopCharacteristics"}]},{name:"ServiceTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0}]},{name:"SubProcess",superClass:["Activity","FlowElementsContainer","InteractionNode"],properties:[{name:"triggeredByEvent","default":!1,isAttr:!0,type:"Boolean"},{name:"artifacts",type:"Artifact",isMany:!0}]},{name:"LoopCharacteristics",isAbstract:!0,superClass:["BaseElement"]},{name:"MultiInstanceLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"isSequential","default":!1,isAttr:!0,type:"Boolean"},{name:"behavior",type:"MultiInstanceBehavior","default":"All",isAttr:!0},{name:"loopCardinality",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopDataInputRef",type:"ItemAwareElement",isAttr:!0,isReference:!0},{name:"loopDataOutputRef",type:"ItemAwareElement",isAttr:!0,isReference:!0},{name:"inputDataItem",type:"DataInput"},{name:"outputDataItem",type:"DataOutput"},{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"complexBehaviorDefinition",type:"ComplexBehaviorDefinition",isMany:!0},{name:"oneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0},{name:"noneBehaviorEventRef",type:"EventDefinition",isAttr:!0,isReference:!0}]},{name:"StandardLoopCharacteristics",superClass:["LoopCharacteristics"],properties:[{name:"testBefore","default":!1,isAttr:!0,type:"Boolean"},{name:"loopCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"loopMaximum",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"CallActivity",superClass:["Activity"],properties:[{name:"calledElement",type:"String",isAttr:!0}]},{name:"Task",superClass:["Activity","InteractionNode"]},{name:"SendTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ReceiveTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"},{name:"instantiate","default":!1,isAttr:!0,type:"Boolean"},{name:"operationRef",type:"Operation",isAttr:!0,isReference:!0},{name:"messageRef",type:"Message",isAttr:!0,isReference:!0}]},{name:"ScriptTask",superClass:["Task"],properties:[{name:"scriptFormat",isAttr:!0,type:"String"},{name:"script",type:"String"}]},{name:"BusinessRuleTask",superClass:["Task"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"AdHocSubProcess",superClass:["SubProcess"],properties:[{name:"completionCondition",type:"Expression",xml:{serialize:"xsi:type"}},{name:"ordering",type:"AdHocOrdering",isAttr:!0},{name:"cancelRemainingInstances","default":!0,isAttr:!0,type:"Boolean"}]},{name:"Transaction",superClass:["SubProcess"],properties:[{name:"protocol",isAttr:!0,type:"String"},{name:"method",isAttr:!0,type:"String"}]},{name:"GlobalScriptTask",superClass:["GlobalTask"],properties:[{name:"scriptLanguage",isAttr:!0,type:"String"},{name:"script",isAttr:!0,type:"String"}]},{name:"GlobalBusinessRuleTask",superClass:["GlobalTask"],properties:[{name:"implementation",isAttr:!0,type:"String"}]},{name:"ComplexBehaviorDefinition",superClass:["BaseElement"],properties:[{name:"condition",type:"FormalExpression"},{name:"event",type:"ImplicitThrowEvent"}]},{name:"ResourceRole",superClass:["BaseElement"],properties:[{name:"resourceRef",type:"Resource",isReference:!0},{name:"resourceParameterBindings",type:"ResourceParameterBinding",isMany:!0},{name:"resourceAssignmentExpression",type:"ResourceAssignmentExpression"},{name:"name",isAttr:!0,type:"String"}]},{name:"ResourceParameterBinding",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}},{name:"parameterRef",type:"ResourceParameter",isAttr:!0,isReference:!0}]},{name:"ResourceAssignmentExpression",properties:[{name:"expression",type:"Expression",xml:{serialize:"xsi:type"}}]},{name:"Import",properties:[{name:"importType",isAttr:!0,type:"String"},{name:"location",isAttr:!0,type:"String"},{name:"namespace",isAttr:!0,type:"String"}]},{name:"Definitions",superClass:["BaseElement"],properties:[{name:"name",isAttr:!0,type:"String"},{name:"targetNamespace",
isAttr:!0,type:"String"},{name:"expressionLanguage","default":"http://www.w3.org/1999/XPath",isAttr:!0,type:"String"},{name:"typeLanguage","default":"http://www.w3.org/2001/XMLSchema",isAttr:!0,type:"String"},{name:"imports",type:"Import",isMany:!0},{name:"extensions",type:"Extension",isMany:!0},{name:"rootElements",type:"RootElement",isMany:!0},{name:"diagrams",isMany:!0,type:"bpmndi:BPMNDiagram"},{name:"exporter",isAttr:!0,type:"String"},{name:"relationships",type:"Relationship",isMany:!0},{name:"exporterVersion",isAttr:!0,type:"String"}]}],emumerations:[{name:"ProcessType",literalValues:[{name:"None"},{name:"Public"},{name:"Private"}]},{name:"GatewayDirection",literalValues:[{name:"Unspecified"},{name:"Converging"},{name:"Diverging"},{name:"Mixed"}]},{name:"EventBasedGatewayType",literalValues:[{name:"Parallel"},{name:"Exclusive"}]},{name:"RelationshipDirection",literalValues:[{name:"None"},{name:"Forward"},{name:"Backward"},{name:"Both"}]},{name:"ItemKind",literalValues:[{name:"Physical"},{name:"Information"}]},{name:"ChoreographyLoopType",literalValues:[{name:"None"},{name:"Standard"},{name:"MultiInstanceSequential"},{name:"MultiInstanceParallel"}]},{name:"AssociationDirection",literalValues:[{name:"None"},{name:"One"},{name:"Both"}]},{name:"MultiInstanceBehavior",literalValues:[{name:"None"},{name:"One"},{name:"All"},{name:"Complex"}]},{name:"AdHocOrdering",literalValues:[{name:"Parallel"},{name:"Sequential"}]}],prefix:"bpmn",xml:{tagAlias:"lowerCase",typePrefix:"t"}}},{}],70:[function(t,e,n){e.exports={name:"BPMNDI",uri:"http://www.omg.org/spec/BPMN/20100524/DI",types:[{name:"BPMNDiagram",properties:[{name:"plane",type:"BPMNPlane",redefines:"di:Diagram#rootElement"},{name:"labelStyle",type:"BPMNLabelStyle",isMany:!0}],superClass:["di:Diagram"]},{name:"BPMNPlane",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"}],superClass:["di:Plane"]},{name:"BPMNShape",properties:[{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"isHorizontal",isAttr:!0,type:"Boolean"},{name:"isExpanded",isAttr:!0,type:"Boolean"},{name:"isMarkerVisible",isAttr:!0,type:"Boolean"},{name:"label",type:"BPMNLabel"},{name:"isMessageVisible",isAttr:!0,type:"Boolean"},{name:"participantBandKind",type:"ParticipantBandKind",isAttr:!0},{name:"choreographyActivityShape",type:"BPMNShape",isAttr:!0,isReference:!0}],superClass:["di:LabeledShape"]},{name:"BPMNEdge",properties:[{name:"label",type:"BPMNLabel"},{name:"bpmnElement",isAttr:!0,isReference:!0,type:"bpmn:BaseElement",redefines:"di:DiagramElement#modelElement"},{name:"sourceElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#source"},{name:"targetElement",isAttr:!0,isReference:!0,type:"di:DiagramElement",redefines:"di:Edge#target"},{name:"messageVisibleKind",type:"MessageVisibleKind",isAttr:!0,"default":"initiating"}],superClass:["di:LabeledEdge"]},{name:"BPMNLabel",properties:[{name:"labelStyle",type:"BPMNLabelStyle",isAttr:!0,isReference:!0,redefines:"di:DiagramElement#style"}],superClass:["di:Label"]},{name:"BPMNLabelStyle",properties:[{name:"font",type:"dc:Font"}],superClass:["di:Style"]}],emumerations:[{name:"ParticipantBandKind",literalValues:[{name:"top_initiating"},{name:"middle_initiating"},{name:"bottom_initiating"},{name:"top_non_initiating"},{name:"middle_non_initiating"},{name:"bottom_non_initiating"}]},{name:"MessageVisibleKind",literalValues:[{name:"initiating"},{name:"non_initiating"}]}],associations:[],prefix:"bpmndi"}},{}],71:[function(t,e,n){e.exports={name:"DC",uri:"http://www.omg.org/spec/DD/20100524/DC",types:[{name:"Boolean"},{name:"Integer"},{name:"Real"},{name:"String"},{name:"Font",properties:[{name:"name",type:"String",isAttr:!0},{name:"size",type:"Real",isAttr:!0},{name:"isBold",type:"Boolean",isAttr:!0},{name:"isItalic",type:"Boolean",isAttr:!0},{name:"isUnderline",type:"Boolean",isAttr:!0},{name:"isStrikeThrough",type:"Boolean",isAttr:!0}]},{name:"Point",properties:[{name:"x",type:"Real","default":"0",isAttr:!0},{name:"y",type:"Real","default":"0",isAttr:!0}]},{name:"Bounds",properties:[{name:"x",type:"Real","default":"0",isAttr:!0},{name:"y",type:"Real","default":"0",isAttr:!0},{name:"width",type:"Real",isAttr:!0},{name:"height",type:"Real",isAttr:!0}]}],prefix:"dc",associations:[]}},{}],72:[function(t,e,n){e.exports={name:"DI",uri:"http://www.omg.org/spec/DD/20100524/DI",types:[{name:"DiagramElement",isAbstract:!0,properties:[{name:"extension",type:"Extension"},{name:"owningDiagram",type:"Diagram",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"owningElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"modelElement",isReadOnly:!0,isVirtual:!0,isReference:!0,type:"Element"},{name:"style",type:"Style",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"ownedElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isMany:!0}]},{name:"Node",isAbstract:!0,superClass:["DiagramElement"]},{name:"Edge",isAbstract:!0,superClass:["DiagramElement"],properties:[{name:"source",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"target",type:"DiagramElement",isReadOnly:!0,isVirtual:!0,isReference:!0},{name:"waypoint",isUnique:!1,isMany:!0,type:"dc:Point",xml:{serialize:"xsi:type"}}]},{name:"Diagram",isAbstract:!0,properties:[{name:"rootElement",type:"DiagramElement",isReadOnly:!0,isVirtual:!0},{name:"name",isAttr:!0,type:"String"},{name:"documentation",isAttr:!0,type:"String"},{name:"resolution",isAttr:!0,type:"Real"},{name:"ownedStyle",type:"Style",isReadOnly:!0,isVirtual:!0,isMany:!0}]},{name:"Shape",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Plane",isAbstract:!0,superClass:["Node"],properties:[{name:"planeElement",type:"DiagramElement",subsettedProperty:"DiagramElement-ownedElement",isMany:!0}]},{name:"LabeledEdge",isAbstract:!0,superClass:["Edge"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isVirtual:!0,isMany:!0}]},{name:"LabeledShape",isAbstract:!0,superClass:["Shape"],properties:[{name:"ownedLabel",type:"Label",isReadOnly:!0,subsettedProperty:"DiagramElement-ownedElement",isVirtual:!0,isMany:!0}]},{name:"Label",isAbstract:!0,superClass:["Node"],properties:[{name:"bounds",type:"dc:Bounds"}]},{name:"Style",isAbstract:!0},{name:"Extension",properties:[{name:"values",type:"Element",isMany:!0}]}],associations:[],prefix:"di",xml:{tagAlias:"lowerCase"}}},{}],73:[function(t,e,n){e.exports={__depends__:[t(108)],__init__:["directEditing"],directEditing:["type",t(74)]}},{108:108,74:74}],74:[function(t,e,n){"use strict";function i(t,e){this._eventBus=t,this._providers=[],this._textbox=new a({container:e.getContainer(),keyHandler:r(this._handleKey,this)})}var r=t(227),o=t(217),a=t(75);i.$inject=["eventBus","canvas"],i.prototype.registerProvider=function(t){this._providers.push(t)},i.prototype.isActive=function(){return!!this._active},i.prototype.cancel=function(){this._active&&(this._fire("cancel"),this.close())},i.prototype._fire=function(t){this._eventBus.fire("directEditing."+t,{active:this._active})},i.prototype.close=function(){this._textbox.destroy(),this._fire("deactivate"),this._active=null},i.prototype.complete=function(){var t=this._active;if(t){var e=this.getValue();e!==t.context.text&&t.provider.update(t.element,e,t.context.text),this._fire("complete"),this.close()}},i.prototype.getValue=function(){return this._textbox.getValue()},i.prototype._handleKey=function(t){t.stopPropagation();var e=t.keyCode||t.charCode;return 27===e?(t.preventDefault(),this.cancel()):13!==e||t.shiftKey?void 0:(t.preventDefault(),this.complete())},i.prototype.activate=function(t){this.isActive()&&this.cancel();var e,n=o(this._providers,function(n){return(e=n.activate(t))?n:null});return e&&(this._textbox.create(e.bounds,e.style,e.text),this._active={element:t,context:e,provider:n},this._fire("activate")),!!e},e.exports=i},{217:217,227:227,75:75}],75:[function(t,e,n){"use strict";function i(t){t.stopPropagation()}function r(t){this.container=t.container,this.textarea=document.createElement("textarea"),this.keyHandler=t.keyHandler||function(){}}var o=t(339),a=t(357),s=t(360);e.exports=r,r.prototype.create=function(t,e,n){var r=this.textarea,s=this.container;o(r.style,{width:t.width+"px",height:t.height+"px",left:t.x+"px",top:t.y+"px",position:"absolute",textAlign:"center",boxSizing:"border-box"},e||{}),r.value=n,r.title="Press SHIFT+Enter for line feed",a.bind(r,"keydown",this.keyHandler),a.bind(r,"mousedown",i),s.appendChild(r),setTimeout(function(){r.parent&&r.select(),r.focus()},100)},r.prototype.destroy=function(){var t=this.textarea;t.value="",a.unbind(t,"keydown",this.keyHandler),a.unbind(t,"mousedown",i),s(t)},r.prototype.getValue=function(){return this.textarea.value}},{339:339,357:357,360:360}],76:[function(t,e,n){e.exports=t(77)},{77:77}],77:[function(t,e,n){"use strict";function i(t){function e(t){return r.indexOf(t)>=0}function n(t){r.push(t)}function i(t){e(t)||((t.__depends__||[]).forEach(i),e(t)||(n(t),(t.__init__||[]).forEach(function(t){o.push(t)})))}var r=[],o=[];t.forEach(i);var s=new a.Injector(r);return o.forEach(function(t){try{s["string"==typeof t?"get":"invoke"](t)}catch(e){throw console.error("Failed to instantiate component"),console.error(e.stack),e}}),s}function r(e){e=e||{};var n={config:["value",e]},r=t(86),o=[n,r].concat(e.modules||[]);return i(o)}function o(t,e){this.injector=e=e||r(t),this.get=e.get,this.invoke=e.invoke,this.get("eventBus").fire("diagram.init")}var a=t(202);e.exports=o,o.prototype.destroy=function(){this.get("eventBus").fire("diagram.destroy")}},{202:202,86:86}],78:[function(t,e,n){"use strict";function i(t){this._eventBus=t}function r(t){return function(e){return t(e.context,e.command,e)}}var o=t(218),a=t(331),s=t(330),c=t(333),u=1e3;i.$inject=["eventBus"],e.exports=i,i.prototype.on=function(t,e,n,i,p){if((a(e)||c(e))&&(p=i,i=n,n=e,e=null),a(n)&&(p=i,i=n,n=u),!a(i))throw new Error("handlerFn must be a function");s(t)||(t=[t]);var l=this._eventBus;o(t,function(t){var o=["commandStack",t,e].filter(function(t){return t}).join(".");l.on(o,n,p?r(i):i)})};var p=["canExecute","preExecute","preExecuted","execute","executed","postExecute","postExecuted","revert","reverted"];o(p,function(t){i.prototype[t]=function(e,n,i,r){(a(e)||c(e))&&(r=i,i=n,n=e,e=null),this.on(e,t,n,i,r)}})},{218:218,330:330,331:331,333:333}],79:[function(t,e,n){"use strict";function i(t,e){this._handlerMap={},this._stack=[],this._stackIdx=-1,this._currentExecution={actions:[],dirty:[]},this._injector=e,this._eventBus=t,this._uid=1}var r=t(211),o=t(330),a=t(339),s=t(84).Event;i.$inject=["eventBus","injector"],e.exports=i,i.prototype.execute=function(t,e){if(!t)throw new Error("command required");var n={command:t,context:e};this._pushAction(n),this._internalExecute(n),this._popAction(n)},i.prototype.canExecute=function(t,e){var n={command:t,context:e},i=this._getHandler(t);if(!i)return!1;var r=this._fire(t,"canExecute",n);return void 0===r&&i.canExecute&&(r=i.canExecute(e)),r},i.prototype.clear=function(){this._stack.length=0,this._stackIdx=-1,this._fire("changed")},i.prototype.undo=function(){var t,e=this._getUndoAction();if(e){for(this._pushAction(e);e&&(this._internalUndo(e),t=this._getUndoAction(),t&&t.id===e.id);)e=t;this._popAction()}},i.prototype.redo=function(){var t,e=this._getRedoAction();if(e){for(this._pushAction(e);e&&(this._internalExecute(e,!0),t=this._getRedoAction(),t&&t.id===e.id);)e=t;this._popAction()}},i.prototype.register=function(t,e){this._setHandler(t,e)},i.prototype.registerHandler=function(t,e){if(!t||!e)throw new Error("command and handlerCls must be defined");var n=this._injector.instantiate(e);this.register(t,n)},i.prototype.canUndo=function(){return!!this._getUndoAction()},i.prototype.canRedo=function(){return!!this._getRedoAction()},i.prototype._getRedoAction=function(){return this._stack[this._stackIdx+1]},i.prototype._getUndoAction=function(){return this._stack[this._stackIdx]},i.prototype._internalUndo=function(t){var e=t.command,n=t.context,i=this._getHandler(e);this._fire(e,"revert",t),this._markDirty(i.revert(n)),this._revertedAction(t),this._fire(e,"reverted",t)},i.prototype._fire=function(t,e,n){arguments.length<3&&(n=e,e=null);var i,r,o,c=e?[t+"."+e,e]:[t];for(n=a(new s,n),i=0;(r=c[i])&&(o=this._eventBus.fire("commandStack."+r,n),!n.cancelBubble);i++);return o},i.prototype._createId=function(){return this._uid++},i.prototype._internalExecute=function(t,e){var n=t.command,i=t.context,r=this._getHandler(n);if(!r)throw new Error("no command handler registered for <"+n+">");this._pushAction(t),e||(this._fire(n,"preExecute",t),r.preExecute&&r.preExecute(i),this._fire(n,"preExecuted",t)),this._fire(n,"execute",t),this._markDirty(r.execute(i)),this._executedAction(t,e),this._fire(n,"executed",t),e||(this._fire(n,"postExecute",t),r.postExecute&&r.postExecute(i),this._fire(n,"postExecuted",t)),this._popAction(t)},i.prototype._pushAction=function(t){var e=this._currentExecution,n=e.actions,i=n[0];t.id||(t.id=i&&i.id||this._createId()),n.push(t)},i.prototype._popAction=function(){var t=this._currentExecution,e=t.actions,n=t.dirty;e.pop(),e.length||(this._eventBus.fire("elements.changed",{elements:r(n)}),n.length=0,this._fire("changed"))},i.prototype._markDirty=function(t){var e=this._currentExecution;t&&(t=o(t)?t:[t],e.dirty=e.dirty.concat(t))},i.prototype._executedAction=function(t,e){var n=++this._stackIdx;e||this._stack.splice(n,this._stack.length,t)},i.prototype._revertedAction=function(t){this._stackIdx--},i.prototype._getHandler=function(t){return this._handlerMap[t]},i.prototype._setHandler=function(t,e){if(!t||!e)throw new Error("command and handler required");if(this._handlerMap[t])throw new Error("overriding handler for command <"+t+">");this._handlerMap[t]=e}},{211:211,330:330,339:339,84:84}],80:[function(t,e,n){e.exports={commandStack:["type",t(79)]}},{79:79}],81:[function(t,e,n){"use strict";function i(t,e){return Math.round(t*e)/e}function r(t){return u(t)?t+"px":t}function o(t){t=p({},{width:"100%",height:"100%"},t);var e=t.container||document.body,n=document.createElement("div");return n.setAttribute("class","djs-container"),p(n.style,{position:"relative",overflow:"hidden",width:r(t.width),height:r(t.height)}),e.appendChild(n),n}function a(t,e){return t.group().attr({"class":e})}function s(t,e,n,i){this._eventBus=e,this._elementRegistry=i,this._graphicsFactory=n,this._init(t||{})}function c(t,e){var n="matrix("+e.a+","+e.b+","+e.c+","+e.d+","+e.e+","+e.f+")";t.setAttribute("transform",n)}var u=t(333),p=t(339),l=t(218),h=t(215),d=t(184),f=t(200),m="base",y={shape:["x","y","width","height"],connection:["waypoints"]};s.$inject=["config.canvas","eventBus","graphicsFactory","elementRegistry"],e.exports=s,s.prototype._init=function(t){var e=this._eventBus,n=o(t),i=f.createSnapAt("100%","100%",n),r=a(i,"viewport"),s=this;this._container=n,this._svg=i,this._viewport=r,this._layers={},e.on("diagram.init",function(t){e.fire("canvas.init",{svg:i,viewport:r})}),e.on("diagram.destroy",function(){var t=s._container.parentNode;t&&t.removeChild(n),e.fire("canvas.destroy",{svg:s._svg,viewport:s._viewport}),s._svg.remove(),s._svg=s._container=s._layers=s._viewport=null})},s.prototype.getDefaultLayer=function(){return this.getLayer(m)},s.prototype.getLayer=function(t){if(!t)throw new Error("must specify a name");var e=this._layers[t];return e||(e=this._layers[t]=a(this._viewport,"layer-"+t)),e},s.prototype.getContainer=function(){return this._container},s.prototype._updateMarker=function(t,e,n){var i;t.id||(t=this._elementRegistry.get(t)),i=this._elementRegistry._elements[t.id],i&&(l([i.gfx,i.secondaryGfx],function(t){t&&t[n?"addClass":"removeClass"](e)}),this._eventBus.fire("element.marker.update",{element:t,gfx:i.gfx,marker:e,add:!!n}))},s.prototype.addMarker=function(t,e){this._updateMarker(t,e,!0)},s.prototype.removeMarker=function(t,e){this._updateMarker(t,e,!1)},s.prototype.hasMarker=function(t,e){t.id||(t=this._elementRegistry.get(t));var n=this.getGraphics(t);return n&&n.hasClass(e)},s.prototype.toggleMarker=function(t,e){this.hasMarker(t,e)?this.removeMarker(t,e):this.addMarker(t,e)},s.prototype.getRootElement=function(){return this._rootElement||this.setRootElement({id:"__implicitroot"}),this._rootElement},s.prototype.setRootElement=function(t,e){this._ensureValid("root",t);var n=this._rootElement,i=this._elementRegistry,r=this._eventBus;if(n){if(!e)throw new Error("rootElement already set, need to specify override");r.fire("root.remove",{element:n}),r.fire("root.removed",{element:n}),i.remove(n)}var o=this.getDefaultLayer();return r.fire("root.add",{element:t}),i.add(t,o,this._svg),r.fire("root.added",{element:t,gfx:o}),this._rootElement=t,t},s.prototype._ensureValid=function(t,e){if(!e.id)throw new Error("element must have an id");if(this._elementRegistry.get(e.id))throw new Error("element with id "+e.id+" already exists");var n=y[t],i=h(n,function(t){return"undefined"!=typeof e[t]});if(!i)throw new Error("must supply { "+n.join(", ")+" } with "+t)},s.prototype._setParent=function(t,e,n){d.add(e.children,t,n),t.parent=e},s.prototype._addElement=function(t,e,n){n=n||this.getRootElement();var i,r=this._eventBus,o=this._graphicsFactory;this._ensureValid(t,e),r.fire(t+".add",{element:e,parent:n}),this._setParent(e,n,i);var a=o.create(t,e);return this._elementRegistry.add(e,a),o.update(t,e,a),r.fire(t+".added",{element:e,gfx:a}),e},s.prototype.addShape=function(t,e){return this._addElement("shape",t,e)},s.prototype.addConnection=function(t,e){return this._addElement("connection",t,e)},s.prototype._removeElement=function(t,e){var n=this._elementRegistry,i=this._graphicsFactory,r=this._eventBus;return(t=n.get(t.id||t))?(r.fire(e+".remove",{element:t}),i.remove(t),d.remove(t.parent&&t.parent.children,t),t.parent=null,r.fire(e+".removed",{element:t}),n.remove(t),t):void 0},s.prototype.removeShape=function(t){return this._removeElement(t,"shape")},s.prototype.removeConnection=function(t){return this._removeElement(t,"connection")},s.prototype.sendToFront=function(t,e){e!==!1&&(e=!0),e&&t.parent&&this.sendToFront(t.parent),l(t.children,function(t){this.sendToFront(t,!1)},this);var n=this.getGraphics(t),i=n.parent();n.remove().appendTo(i)},s.prototype.getGraphics=function(t,e){return this._elementRegistry.getGraphics(t,e)},s.prototype._fireViewboxChange=function(){this._eventBus.fire("canvas.viewbox.changed",{viewbox:this.viewbox(!1)})},s.prototype.viewbox=function(t){if(void 0===t&&this._cachedViewbox)return this._cachedViewbox;var e,n,r,o,a,s=this._viewport,c=this.getSize();return t?(r=Math.min(c.width/t.width,c.height/t.height),n=(new f.Matrix).scale(r).translate(-t.x,-t.y),s.transform(n),this._fireViewboxChange(),t):(e=this.getDefaultLayer().getBBox(!0),n=s.transform().localMatrix,r=i(n.a,1e3),o=i(-n.e||0,1e3),a=i(-n.f||0,1e3),t=this._cachedViewbox={x:o?o/r:0,y:a?a/r:0,width:c.width/r,height:c.height/r,scale:r,inner:{width:e.width,height:e.height,x:e.x,y:e.y},outer:c})},s.prototype.scroll=function(t){var e=this._viewport.node,n=e.getCTM();return t&&(t=p({dx:0,dy:0},t||{}),n=this._svg.node.createSVGMatrix().translate(t.dx,t.dy).multiply(n),c(e,n),this._fireViewboxChange()),{x:n.e,y:n.f}},s.prototype.zoom=function(t,e){if("fit-viewport"===t)return this._fitViewport(e);var n,r,o=this.viewbox();return void 0===t?o.scale:("object"!=typeof e&&(n=o.outer,e={x:n.width/2,y:n.height/2}),r=this._setZoom(t,e),this._fireViewboxChange(),i(r.a,1e3))},s.prototype._fitViewport=function(t){var e,n,i=this.viewbox(),r=i.outer,o=i.inner;return o.x>=0&&o.y>=0&&o.x+o.width<=r.width&&o.y+o.height<=r.height&&!t?n={x:0,y:0,width:Math.max(o.width+o.x,r.width),height:Math.max(o.height+o.y,r.height)}:(e=Math.min(1,r.width/o.width,r.height/o.height),n={x:o.x+(t?o.width/2-r.width/e/2:0),y:o.y+(t?o.height/2-r.height/e/2:0),width:r.width/e,height:r.height/e}),this.viewbox(n),this.viewbox().scale},s.prototype._setZoom=function(t,e){var n,i,r,o,a,s=this._svg.node,u=this._viewport.node,l=s.createSVGMatrix(),h=s.createSVGPoint();r=u.getCTM();var d=r.a;return e?(n=p(h,e),i=n.matrixTransform(r.inverse()),o=l.translate(i.x,i.y).scale(1/d*t).translate(-i.x,-i.y),a=r.multiply(o)):a=l.scale(t),c(this._viewport.node,a),a},s.prototype.getSize=function(){return{width:this._container.clientWidth,height:this._container.clientHeight}},s.prototype.getAbsoluteBBox=function(t){var e,n=this.viewbox();if(t.waypoints){var i=this.getGraphics(t),r=i.getBBox(!0);e=i.getBBox(),e.x-=r.x,e.y-=r.y,e.width+=2*r.x,e.height+=2*r.y}else e=t;var o=e.x*n.scale-n.x*n.scale,a=e.y*n.scale-n.y*n.scale,s=e.width*n.scale,c=e.height*n.scale;return{x:o,y:a,width:s,height:c}}},{184:184,200:200,215:215,218:218,333:333,339:339}],82:[function(t,e,n){"use strict";function i(){this._uid=12}var r=t(176);e.exports=i,i.prototype.createRoot=function(t){return this.create("root",t)},i.prototype.createLabel=function(t){return this.create("label",t)},i.prototype.createShape=function(t){return this.create("shape",t)},i.prototype.createConnection=function(t){return this.create("connection",t)},i.prototype.create=function(t,e){return e=e||{},e.id||(e.id=t+"_"+this._uid++),r.create(t,e)}},{176:176}],83:[function(t,e,n){"use strict";function i(){this._elements={}}var r="data-element-id";e.exports=i,i.prototype.add=function(t,e,n){var i=t.id;this._validateId(i),e.attr(r,i),n&&n.attr(r,i),this._elements[i]={element:t,gfx:e,secondaryGfx:n}},i.prototype.remove=function(t){var e=this._elements,n=t.id||t,i=n&&e[n];i&&(i.gfx.attr(r,null),i.secondaryGfx&&i.secondaryGfx.attr(r,null),delete e[n])},i.prototype.updateId=function(t,e){this._validateId(e),"string"==typeof t&&(t=this.get(t));var n=this.getGraphics(t),i=this.getGraphics(t,!0);this.remove(t),t.id=e,this.add(t,n,i)},i.prototype.get=function(t){var e;e="string"==typeof t?t:t&&t.attr(r);var n=this._elements[e];return n&&n.element},i.prototype.filter=function(t){var e=[];return this.forEach(function(n,i){t(n,i)&&e.push(n)}),e},i.prototype.getAll=function(){return this.filter(function(t){return t})},i.prototype.forEach=function(t){var e=this._elements;Object.keys(e).forEach(function(n){var i=e[n],r=i.element,o=i.gfx;return t(r,o)})},i.prototype.getGraphics=function(t,e){var n=t.id||t,i=this._elements[n];return i&&(e?i.secondaryGfx:i.gfx)},i.prototype._validateId=function(t){if(!t)throw new Error("element must have an id");if(this._elements[t])throw new Error("element with id "+t+" already added")}},{}],84:[function(t,e,n){"use strict";function i(){this._listeners={};var t=this;this.on("diagram.destroy",1,function(){t._listeners=null})}function r(){}var o=t(331),a=t(330),s=t(333),c=t(339),u=1e3;e.exports=i,i.prototype.on=function(t,e,n){if(t=a(t)?t:[t],o(e)&&(n=e,e=u),!s(e))throw new Error("priority must be a number");var i=this,r={priority:e,callback:n};t.forEach(function(t){i._addListener(t,r)})},i.prototype.once=function(t,e){function n(){e.apply(i,arguments),i.off(t,n)}var i=this;this.on(t,n)},i.prototype.off=function(t,e){var n,i,r=this._getListeners(t);if(e)for(i=r.length-1;n=r[i];i--)n.callback===e&&r.splice(i,1);else r.length=0},i.prototype.fire=function(t,e){var n,i,o,a,s,c,u;if(u=Array.prototype.slice.call(arguments),"object"==typeof t&&(n=t,t=n.type),!t)throw new Error("no event type specified");if(o=this._listeners[t]){e instanceof r?n=e:(n=new r,n.init(e)),u[0]=n,i=n.type;try{for(t!==i&&(n.type=t),a=0;(s=o[a])&&!n.cancelBubble;a++)try{c=n.returnValue=s.callback.apply(null,u),void 0!==c&&n.stopPropagation(),c===!1&&n.preventDefault()}catch(p){if(!this.handleError(p))throw console.error("unhandled error in event listener"),console.error(p.stack),p}}finally{t!==i&&(n.type=i)}return void 0===c&&n.defaultPrevented&&(c=!1),c}},i.prototype.handleError=function(t){return this.fire("error",{error:t})===!1},i.prototype._addListener=function(t,e){var n,i,r=this._getListeners(t);for(i=0;n=r[i];i++)if(n.priority<e.priority)return void r.splice(i,0,e);r.push(e)},i.prototype._getListeners=function(t){var e=this._listeners[t];return e||(this._listeners[t]=e=[]),e},e.exports.Event=r,r.prototype.stopPropagation=function(){this.cancelBubble=!0},r.prototype.preventDefault=function(){this.defaultPrevented=!0},r.prototype.init=function(t){c(this,t||{})}},{330:330,331:331,333:333,339:339}],85:[function(t,e,n){"use strict";function i(t,e){this._renderer=t,this._elementRegistry=e}var r=t(218),o=t(222),a=t(189),s=t(353);i.$inject=["renderer","elementRegistry"],e.exports=i,i.prototype._getChildren=function(t){var e,n=this._elementRegistry.getGraphics(t);return t.parent?(e=a.getChildren(n),e||(e=n.parent().group().attr("class","djs-children"))):e=n,e},i.prototype._clear=function(t){var e=a.getVisual(t);return s(e.node),e},i.prototype._createContainer=function(t,e){var n=e.group().attr("class","djs-group"),i=n.group().attr("class","djs-element djs-"+t);return i.group().attr("class","djs-visual"),i},i.prototype.create=function(t,e){var n=this._getChildren(e.parent);return this._createContainer(t,n)},i.prototype.updateContainments=function(t){var e,n=this,i=this._elementRegistry;e=o(t,function(t,e){return e.parent&&(t[e.parent.id]=e.parent),t},{}),r(e,function(t){var e=n._getChildren(t),o=t.children;o&&r(o.slice().reverse(),function(t){var n=i.getGraphics(t);n.parent().prependTo(e)})})},i.prototype.update=function(t,e,n){if(e.parent){var i=this._clear(n);if("shape"===t)this._renderer.drawShape(i,e),n.translate(e.x,e.y);else{if("connection"!==t)throw new Error("unknown type: "+t);this._renderer.drawConnection(i,e)}n.attr("display",e.hidden?"none":"block")}},i.prototype.remove=function(t){var e=this._elementRegistry.getGraphics(t);e.parent().remove()}},{189:189,218:218,222:222,353:353}],86:[function(t,e,n){e.exports={__depends__:[t(89)],__init__:["canvas"],canvas:["type",t(81)],elementRegistry:["type",t(83)],elementFactory:["type",t(82)],eventBus:["type",t(84)],graphicsFactory:["type",t(85)]}},{81:81,82:82,83:83,84:84,85:85,89:89}],87:[function(t,e,n){"use strict";function i(t){this.CONNECTION_STYLE=t.style(["no-fill"],{strokeWidth:5,stroke:"fuchsia"}),this.SHAPE_STYLE=t.style({fill:"white",stroke:"fuchsia",strokeWidth:2})}function r(t){return t.join(",").replace(/,?([A-z]),?/g,"$1")}function o(t){for(var e,n="",i=0;e=t[i];i++)n+=e.x+","+e.y+" ";return n}function a(t,e){return c.create("polyline",{points:o(t)}).attr(e||{})}function s(t,e){return t.attr({points:o(e)})}var c=t(200);e.exports=i,i.$inject=["styles"],i.prototype.drawShape=function(t,e){return t.rect(0,0,e.width||0,e.height||0).attr(this.SHAPE_STYLE)},i.prototype.drawConnection=function(t,e){return a(e.waypoints,this.CONNECTION_STYLE).appendTo(t)},i.prototype.getShapePath=function(t){var e=t.x,n=t.y,i=t.width,o=t.height,a=[["M",e,n],["l",i,0],["l",0,o],["l",-i,0],["z"]];return r(a)},i.prototype.getConnectionPath=function(t){var e,n,i=t.waypoints,o=[];for(e=0;n=i[e];e++)n=n.original||n,o.push([0===e?"M":"L",n.x,n.y]);return r(o)},e.exports.createLine=a,e.exports.updateLine=s},{200:200}],88:[function(t,e,n){"use strict";function i(){var t={"no-fill":{fill:"none"},"no-border":{strokeOpacity:0},"no-events":{pointerEvents:"none"}};this.cls=function(t,e,n){var i=this.style(e,n);return o(i,{"class":t})},this.style=function(e,n){r(e)||n||(n=e,e=[]);var i=a(e,function(e,n){return o(e,t[n]||{})},{});return n?o(i,n):i}}var r=t(330),o=t(339),a=t(222);e.exports=i},{222:222,330:330,339:339}],89:[function(t,e,n){e.exports={renderer:["type",t(87)],styles:["type",t(88)]}},{87:87,88:88}],90:[function(t,e,n){"use strict";function i(t,e,n,i){m.call(this,t),t.on("shape.move.start",f,function(t){var e=t.context,n=e.shapes;e.shapes=o(n)}),t.on("shape.move.start",d,function(t){var e=t.context,i=e.shapes,o=e.movedAttachers=r(i);a(o,function(t){n.makeDraggable(e,t,!0)})}),this.postExecuted(["shapes.move"],function(t){var n=t.context,i=n.closure,o=i.enclosedElements,s=r(i.enclosedElements);a(s,function(t){o[t.id]||e.moveShape(t,n.delta,n.newParent)})}),this.postExecuted(["shapes.move"],function(t){var n,i=t.context,r=i.shapes,o=i.newHost;r.length>1||(n=o?r:c(r,function(t){return!!t.host}),a(n,function(t){e.updateAttachment(t,o)}))}),this.postExecute(["shape.replace"],function(t){var n=t.context,r=n.oldShape,o=n.newShape;l(r.attachers,function(t){var n=i.allowed("shapes.move",{target:o,shapes:[t]});"attach"===n?e.updateAttachment(t,o):e.removeShape(t)})}),this.preExecute(["shape.delete"],function(t){var n=t.context.shape;l(n.attachers,function(t){e.removeShape(t)})})}function r(t){return s(p(t,function(t){return t.attachers||[]}))}function o(t){var e=u(t,"id");return c(t,function(t){for(;t;){if(t.host&&e[t.host.id])return!1;t=t.parent}return!0})}var a=t(218),s=t(208),c=t(216),u=t(219),p=t(221),l=t(195).saveClear,h=t(207),d=250,f=1500,m=t(78);h(i,m),i.$inject=["eventBus","modeling","moveVisuals","rules"],e.exports=i},{195:195,207:207,208:208,216:216,218:218,219:219,221:221,78:78}],91:[function(t,e,n){e.exports={__depends__:[t(139),t(112)],__init__:["attachSupport"],attachSupport:["type",t(90)]}},{112:112,139:139,90:90}],92:[function(t,e,n){"use strict";function i(t,e,n,i,f,m,y){function v(t){f.update("connection",t.connection,t.connectionGfx)}function g(t){return t.filter(function(e,n){return!r.pointsOnLine(t[n-1],t[n+1],e)})}var x;try{x=t.get("connectionDocking")}catch(b){}this.start=function(t,e,r,o){var a,s,c=e.waypoints,u=n.getGraphics(e);a=o||0!==r?o||r!==c.length-1?p:h:l,s={connection:e,bendpointIndex:r,insert:o,type:a},i.activate(t,"bendpoint.move",{data:{connection:e,connectionGfx:u,context:s}})},e.on("bendpoint.move.start",function(t){var e=t.context,i=e.connection,r=i.waypoints,a=r.slice(),s=e.insert,c=e.bendpointIndex;e.originalWaypoints=r,s&&a.splice(c,0,null),i.waypoints=a,e.draggerGfx=o.addBendpoint(n.getLayer("overlays")),e.draggerGfx.addClass("djs-dragging"),n.addMarker(i,u)}),e.on("bendpoint.move.hover",function(t){t.context.hover=t.hover,n.addMarker(t.hover,c)}),e.on(["bendpoint.move.out","bendpoint.move.cleanup"],function(t){var e=t.context.hover;e&&(n.removeMarker(e,c),n.removeMarker(e,t.context.target?a:s))}),e.on("bendpoint.move.move",function(t){var e,i,r=t.context,o=r.type,c=t.connection;c.waypoints[r.bendpointIndex]={x:t.x,y:t.y},x&&(r.hover&&(o===l&&(e=r.hover),o===h&&(i=r.hover)),c.waypoints=x.getCroppedWaypoints(c,e,i));var u=r.allowed=m.allowed(r.type,r);u?r.hover&&(n.removeMarker(r.hover,s),n.addMarker(r.hover,a),r.target=r.hover):u===!1&&r.hover&&(n.removeMarker(r.hover,a),n.addMarker(r.hover,s),r.target=null),r.draggerGfx.translate(t.x,t.y),v(t)}),e.on(["bendpoint.move.end","bendpoint.move.cancel"],function(t){var e=t.context,i=e.connection;e.draggerGfx.remove(),e.newWaypoints=i.waypoints.slice(),i.waypoints=e.originalWaypoints,n.removeMarker(i,u)}),e.on("bendpoint.move.end",function(t){var e=t.context,n=e.newWaypoints,i=e.bendpointIndex,r=n[i],o=e.allowed;if(r.x=d(r.x),r.y=d(r.y),o===!0&&e.type===l)y.reconnectStart(e.connection,e.target,r);else if(o===!0&&e.type===h)y.reconnectEnd(e.connection,e.target,r);else{if(o===!1||e.type!==p)return v(t),!1;y.updateWaypoints(e.connection,g(n))}}),e.on("bendpoint.move.cancel",function(t){v(t)})}var r=t(188),o=t(94),a="connect-ok",s="connect-not-ok",c="connect-hover",u="djs-updating",p="connection.updateWaypoints",l="connection.reconnectStart",h="connection.reconnectEnd",d=Math.round;i.$inject=["injector","eventBus","canvas","dragging","graphicsFactory","rules","modeling"],e.exports=i},{188:188,94:94}],93:[function(t,e,n){"use strict";function i(t){function e(t,e){return s.snapTo(t,e)}function n(t){return o(t,["x","y"])}function i(t){return t.width?{x:c(t.width/2+t.x),y:c(t.height/2+t.y)}:void 0}function u(t){var e=t.snapPoints,n=t.connection.waypoints,i=t.bendpointIndex,r=[n[i-1],n[i+1]];return e||(t.snapPoints=e={horizontal:[],vertical:[]},a(r,function(t){t&&(t=t.original||t,e.horizontal.push(t.y),e.vertical.push(t.x))})),e}t.on("bendpoint.move.start",function(t){t.context.snapStart=n(t);
}),t.on("bendpoint.move.move",1500,function(t){var n,o,a=t.context,s=u(a),c=a.snapStart,p=a.target,l=p&&i(p),h=c.x+t.dx,d=c.y+t.dy;if(s){n=e(l?s.vertical.concat([l.x]):s.vertical,h),o=e(l?s.horizontal.concat([l.y]):s.horizontal,d);var f=h-n,m=d-o;r(t,{dx:t.dx-f,dy:t.dy-m,x:t.x-f,y:t.y-m})}})}var r=t(339),o=t(345),a=t(218),s=t(200),c=Math.round;i.$inject=["eventBus"],e.exports=i},{200:200,218:218,339:339,345:345}],94:[function(t,e,n){"use strict";var i=t(187),r=e.exports.BENDPOINT_CLS="djs-bendpoint";e.exports.toCanvasCoordinates=function(t,e){var n,r=i.toPoint(e),o=t._container.getBoundingClientRect();n={x:o.left,y:o.top};var a=t.viewbox();return{x:a.x+(r.x-n.x)/a.scale,y:a.y+(r.y-n.y)/a.scale}},e.exports.addBendpoint=function(t){var e=t.group().addClass(r);return e.circle(0,0,4).addClass("djs-visual"),e.circle(0,0,10).addClass("djs-hit"),e}},{187:187}],95:[function(t,e,n){"use strict";function i(t,e,n,i,c){function u(t,e){var i=o.toCanvasCoordinates(n,e);return s(t,i)}function p(t,e){var n=e.waypoints,i=u(n,t);i&&c.start(t,e,i.index,!i.bendpoint)}function l(t,e){var i=n.getLayer("overlays"),o=i.select(".djs-bendpoints[data-element-id="+t.id+"]");return!o&&e&&(o=i.group().addClass("djs-bendpoints").attr("data-element-id",t.id),r.bind(o.node,"mousedown",function(e){p(e,t)})),o}function h(t,e){e.waypoints.forEach(function(e,n){o.addBendpoint(t).translate(e.x,e.y)}),o.addBendpoint(t).addClass("floating")}function d(t){t.selectAll("."+a).forEach(function(t){t.remove()})}function f(t){var e=l(t);return e||(e=l(t,!0),h(e,t)),e}function m(t){var e=l(t);e&&(d(e),h(e,t))}e.on("connection.changed",function(t){m(t.element)}),e.on("connection.remove",function(t){var e=l(t.element);e&&e.remove()}),e.on("element.marker.update",function(t){var e,n=t.element;n.waypoints&&(e=f(n),e[t.add?"addClass":"removeClass"](t.marker))}),e.on("element.mousemove",function(t){var e,n,i,r=t.element,o=r.waypoints;if(o){if(e=l(r,!0),n=e.select(".floating"),!n)return;i=u(o,t.originalEvent),i&&n.translate(i.point.x,i.point.y)}}),e.on("element.mousedown",function(t){var e=t.originalEvent,n=t.element,i=n.waypoints;i&&p(e,n,i)}),e.on("selection.changed",function(t){var e=t.newSelection,n=e[0];n&&n.waypoints&&f(n)}),e.on("element.hover",function(t){var e=t.element;e.waypoints&&(f(e),i.registerEvent(t.gfx.node,"mousemove","element.mousemove"))}),e.on("element.out",function(t){i.unregisterEvent(t.gfx.node,"mousemove","element.mousemove")})}var r=t(357),o=t(94),a=o.BENDPOINT_CLS,s=t(191).getApproxIntersection;i.$inject=["injector","eventBus","canvas","interactionEvents","bendpointMove"],e.exports=i},{191:191,357:357,94:94}],96:[function(t,e,n){e.exports={__depends__:[t(106),t(155)],__init__:["bendpoints","bendpointSnapping"],bendpoints:["type",t(95)],bendpointMove:["type",t(92)],bendpointSnapping:["type",t(93)]}},{106:106,155:155,92:92,93:93,95:95}],97:[function(t,e,n){"use strict";function i(t,e,n){t.on("element.changed",function(n){var i=n.element;n.gfx||(n.gfx=e.getGraphics(i)),n.gfx&&(i.waypoints?t.fire("connection.changed",n):t.fire("shape.changed",n))}),t.on("elements.changed",function(e){var i=e.elements;i.forEach(function(e){t.fire("element.changed",{element:e})}),n.updateContainments(i)}),t.on("shape.changed",function(t){n.update("shape",t.element,t.gfx)}),t.on("connection.changed",function(t){n.update("connection",t.element,t.gfx)})}i.$inject=["eventBus","elementRegistry","graphicsFactory"],e.exports=i},{}],98:[function(t,e,n){e.exports={__init__:["changeSupport"],changeSupport:["type",t(97)]}},{97:97}],99:[function(t,e,n){"use strict";function i(t,e,n,i,s,c){function u(t,e){return i.allowed("connection.create",{source:t,target:e})}function p(t,e,n,i){var o=c.getShapePath(n),a=i&&c.getShapePath(i),s=c.getConnectionPath({waypoints:[t,e]});return t=r.getElementLineIntersection(o,s,!0)||t,e=i&&r.getElementLineIntersection(a,s,!1)||e,[t,e]}t.on("connect.move",function(t){var e,n,i,o=t.context,a=o.source,s=o.target,c=o.visual;e=r.getMid(a),n={x:t.x,y:t.y},i=p(e,n,a,s),c.attr("points",[i[0].x,i[0].y,i[1].x,i[1].y])}),t.on("connect.hover",function(t){var e,n=t.context,i=n.source,r=t.hover;e=n.canExecute=u(i,r),null!==e&&(n.target=r,s.addMarker(r,e?o:a))}),t.on(["connect.out","connect.cleanup"],function(t){var e=t.context;e.target&&s.removeMarker(e.target,e.canExecute?o:a),e.target=null}),t.on("connect.cleanup",function(t){var e=t.context;e.visual&&e.visual.remove()}),t.on("connect.start",function(t){var e,n=t.context;e=s.getDefaultLayer().polyline().attr({stroke:"#333",strokeDasharray:[1],strokeWidth:2,"pointer-events":"none"}),n.visual=e}),t.on("connect.end",function(t){var e=t.context,i=e.source,r=e.target,o=e.canExecute||u(i,r);return o?void n.connect(i,r):!1}),this.start=function(t,n,i){e.activate(t,"connect",{autoActivate:i,data:{shape:n,context:{source:n}}})}}var r=t(174),o="connect-ok",a="connect-not-ok";i.$inject=["eventBus","dragging","modeling","rules","canvas","renderer"],e.exports=i},{174:174}],100:[function(t,e,n){e.exports={__depends__:[t(159),t(155),t(106)],connect:["type",t(99)]}},{106:106,155:155,159:159,99:99}],101:[function(t,e,n){"use strict";function i(t,e){this._providers=[],this._eventBus=t,this._overlays=e,this._current=null,this._init()}var r=t(331),o=t(218),a=t(355),s=t(353),c=t(357),u=t(351),p=t(359),l=t(352),h=t(356),d=".entry";i.$inject=["eventBus","overlays"],i.prototype._init=function(){var t=this._eventBus,e=this;t.on("selection.changed",function(t){var n=t.newSelection;1===n.length?e.open(n[0]):e.close()})},i.prototype.registerProvider=function(t){this._providers.push(t)},i.prototype.getEntries=function(t){var e={};return o(this._providers,function(n){var i=n.getContextPadEntries(t);o(i,function(t,n){e[n]=t})}),e},i.prototype.trigger=function(t,e,n){var i,o,a,s=this._current,c=s.element,p=s.entries,l=e.delegateTarget||e.target;if(!l)return e.preventDefault();if(i=p[u(l,"data-action")],o=i.action,a=e.originalEvent||e,r(o)){if("click"===t)return o(a,c,n)}else if(o[t])return o[t](a,c,n);e.preventDefault()},i.prototype.open=function(t){if(this._current&&this._current.open){if(this._current.element===t)return;this.close()}this._updateAndOpen(t)},i.prototype._updateAndOpen=function(t){var e=this.getEntries(t),n=this.getPad(t),i=n.html;s(i),o(e,function(t,e){var n,r=t.group||"default",o=h(t.html||'<div class="entry" draggable="true"></div>');u(o,"data-action",e),n=p("[data-group="+r+"]",i),n||(n=h('<div class="group" data-group="'+r+'"></div>'),i.appendChild(n)),n.appendChild(o),t.className&&l(o).add(t.className),t.title&&u(o,"title",t.title),t.imageUrl&&o.appendChild(h('<img src="'+t.imageUrl+'">'))}),l(i).add("open"),this._current={element:t,pad:n,entries:e,open:!0},this._eventBus.fire("contextPad.open",{current:this._current})},i.prototype.getPad=function(t){var e=this,n=this._overlays,i=n.get({element:t,type:"context-pad"});if(!i.length){var r=h('<div class="djs-context-pad"></div>');a.bind(r,d,"click",function(t){e.trigger("click",t)}),a.bind(r,d,"dragstart",function(t){e.trigger("dragstart",t)}),c.bind(r,"mousedown",function(t){t.stopPropagation()}),n.add(t,"context-pad",{position:{right:-9,top:-6},html:r}),i=n.get({element:t,type:"context-pad"}),this._eventBus.fire("contextPad.create",{element:t,pad:i[0]})}return i[0]},i.prototype.close=function(){var t;this._current&&(this._current.open&&(t=this._current.pad.html,l(t).remove("open")),this._current.open=!1,this._eventBus.fire("contextPad.close",{current:this._current}))},i.prototype.isOpen=function(){return this._current&&this._current.open},e.exports=i},{218:218,331:331,351:351,352:352,353:353,355:355,356:356,357:357,359:359}],102:[function(t,e,n){e.exports={__depends__:[t(108),t(143)],contextPad:["type",t(101)]}},{101:101,108:108,143:143}],103:[function(t,e,n){"use strict";function i(t,e,n,i,s,c,u){function p(t,e,i,r){return i?n.allowed("shape.append",{source:i,shape:t,target:e,position:r}):n.allowed("shape.create",{shape:t,target:e,position:r})}function l(t,e){[a,r,o].forEach(function(n){n===e?s.addMarker(t,n):s.removeMarker(t,n)})}function h(t){var e,n,i;return e=s.getDefaultLayer().group().attr(u.cls("djs-drag-group",["no-events"])),n=e.group().addClass("djs-dragger"),n.translate(t.width/-2,t.height/-2),i=n.group().addClass("djs-visual"),c.drawShape(i,t),e}t.on("create.move",function(t){var e=t.context,n=e.shape,i=e.visual;i||(i=e.visual=h(n)),i.translate(t.x,t.y);var s,c=t.hover,u={x:t.x,y:t.y};s=e.canExecute=c&&p(e.shape,c,e.source,u),c&&null!==s&&(e.target=c,"attach"===s?l(c,a):l(c,e.canExecute?r:o))}),t.on(["create.end","create.out","create.cleanup"],function(t){var e=t.context,n=e.target;n&&l(n,null)}),t.on("create.end",function(t){var e,n=t.context,r=n.source,o=n.shape,a=n.target,s=n.canExecute,c={x:t.x,y:t.y};return s?(r?o=i.appendShape(r,o,c,a):(e="attach"===s,o=i.createShape(o,c,a,e)),void(n.shape=o)):!1}),t.on("create.cleanup",function(t){var e=t.context;e.visual&&e.visual.remove()}),this.start=function(t,n,i){e.activate(t,"create",{cursor:"grabbing",autoActivate:!0,data:{shape:n,context:{shape:n,source:i}}})}}var r="drop-ok",o="drop-not-ok",a="attach-ok";i.$inject=["eventBus","dragging","rules","modeling","canvas","renderer","styles"],e.exports=i},{}],104:[function(t,e,n){e.exports={__depends__:[t(106),t(159),t(155)],create:["type",t(103)]}},{103:103,106:106,155:155,159:159}],105:[function(t,e,n){"use strict";function i(t){t instanceof MouseEvent?l.stopEvent(t,!0):l.preventDefault(t)}function r(t){return Math.sqrt(Math.pow(t.x,2)+Math.pow(t.y,2))}function o(t,e){return{x:t.x-e.x,y:t.y-e.y}}function a(t,e){return{x:t.x+e.x,y:t.y+e.y}}function s(t,e,n){function s(t){var n=e.viewbox(),i=e._container.getBoundingClientRect();return{x:n.x+c((t.x-i.left)/n.scale),y:n.y+c((t.y-i.top)/n.scale)}}function m(t){var n=e.viewbox();return{x:c(t.x/n.scale),y:c(t.y/n.scale)}}function y(e){var n=u(new f,C.payload,C.data);return t.fire("drag."+e,n)===!1?!1:t.fire(C.prefix+"."+e,n)}function v(t,e){var s=C.payload,c=C.globalStart,p=l.toPoint(t),h=o(p,c),f=C.localStart,v=m(h),g=a(f,v);if(!C.active&&(e||r(h)>C.threshold)){if(u(s,{x:f.x,y:f.y,dx:0,dy:0},{originalEvent:t}),!1===y("start"))return S();C.active=!0,C.keepSelection||(s.previousSelection=n.get(),n.select(null)),C.cursor&&d.set(C.cursor)}i(t),C.active&&(u(s,{x:g.x,y:g.y,dx:v.x,dy:v.y},{originalEvent:t}),y("move"))}function g(t){var e=!0;C.active&&(t&&(C.payload.originalEvent=t,i(t)),e=y("end")),e===!1&&y("rejected"),A(e!==!0)}function x(t){27===t.which&&(t.preventDefault(),S())}function b(t){var e;C.active&&(e=h.install(),setTimeout(e,400)),g(t)}function E(t){v(t)}function w(t){var e=C.payload;e.hoverGfx=t.gfx,e.hover=t.element,y("hover")}function _(t){y("out");var e=C.payload;e.hoverGfx=null,e.hover=null}function S(t){C&&(C.active&&y("cancel"),A(t))}function A(e){y("cleanup"),d.unset(),p.unbind(document,"mousemove",v),p.unbind(document,"mousedown",b,!0),p.unbind(document,"mouseup",b,!0),p.unbind(document,"keyup",x),p.unbind(document,"touchstart",E,!0),p.unbind(document,"touchcancel",S,!0),p.unbind(document,"touchmove",v,!0),p.unbind(document,"touchend",g,!0),t.off("element.hover",w),t.off("element.out",_);var i=C.payload.previousSelection;e!==!1&&i&&!n.get().length&&n.select(i),C=null}function T(e,n,r,o){C&&S(!1),"string"==typeof n&&(o=r,r=n,n=null),o=u({},N,o||{});var a,c,h=o.data||{};e?(a=l.getOriginal(e)||e,c=l.toPoint(e),i(e)):(a=null,c={x:0,y:0}),n||(n=s(c)),C=u({prefix:r,data:h,payload:{},globalStart:c,localStart:n},o),o.manual||("undefined"!=typeof TouchEvent&&a instanceof TouchEvent?(p.bind(document,"touchstart",E,!0),p.bind(document,"touchcancel",S,!0),p.bind(document,"touchmove",v,!0),p.bind(document,"touchend",g,!0)):(p.bind(document,"mousemove",v),p.bind(document,"mousedown",b,!0),p.bind(document,"mouseup",b,!0)),p.bind(document,"keyup",x),t.on("element.hover",w),t.on("element.out",_)),y("activate"),o.autoActivate&&v(e,!0)}var C,N={threshold:5};t.on("diagram.destroy",S),this.activate=T,this.move=v,this.hover=w,this.out=_,this.end=g,this.cancel=S,this.active=function(){return C},this.setOptions=function(t){u(N,t)}}var c=Math.round,u=t(339),p=t(357),l=t(187),h=t(183),d=t(185),f=t(84).Event;s.$inject=["eventBus","canvas","selection"],e.exports=s},{183:183,185:185,187:187,339:339,357:357,84:84}],106:[function(t,e,n){e.exports={__depends__:[t(159)],dragging:["type",t(105)]}},{105:105,159:159}],107:[function(t,e,n){"use strict";function i(t,e,n){function i(n,i){var r,o=i.delegateTarget||i.target,a=o&&new p(o),s=e.get(a);a&&s&&(r=t.fire(n,{element:s,gfx:a,originalEvent:i}),r===!1&&(i.stopPropagation(),i.preventDefault()))}function a(t){var e=y[t];return e||(e=y[t]=function(e){u(e)&&i(t,e)}),e}function l(t,e,n){var i=a(n);i.$delegate=o.bind(t,g,e,i)}function h(t,e,n){o.unbind(t,e,a(n).$delegate)}function d(t){r(v,function(e,n){l(t.node,n,e)})}function f(t){r(v,function(e,n){h(t.node,n,e)})}var m=n.cls("djs-hit",["no-fill","no-border"],{stroke:"white",strokeWidth:15}),y={},v={mouseover:"element.hover",mouseout:"element.out",click:"element.click",dblclick:"element.dblclick",mousedown:"element.mousedown",mouseup:"element.mouseup"},g="svg, .djs-element";t.on("canvas.destroy",function(t){f(t.svg)}),t.on("canvas.init",function(t){d(t.svg)}),t.on(["shape.added","connection.added"],function(t){var e,n,i=t.element,r=t.gfx;i.waypoints?(e=s(i.waypoints),n="connection"):(e=p.create("rect",{x:0,y:0,width:i.width,height:i.height}),n="shape"),e.attr(m).appendTo(r.node)}),t.on("shape.changed",function(t){var e=t.element,n=t.gfx,i=n.select(".djs-hit");i.attr({width:e.width,height:e.height})}),t.on("connection.changed",function(t){var e=t.element,n=t.gfx,i=n.select(".djs-hit");c(i,e.waypoints)}),this.fire=i,this.mouseHandler=a,this.registerEvent=l,this.unregisterEvent=h}var r=t(218),o=t(355),a=t(87),s=a.createLine,c=a.updateLine,u=t(193).isPrimaryButton,p=t(200);i.$inject=["eventBus","elementRegistry","styles"],e.exports=i},{193:193,200:200,218:218,355:355,87:87}],108:[function(t,e,n){e.exports={__init__:["interactionEvents"],interactionEvents:["type",t(107)]}},{107:107}],109:[function(t,e,n){"use strict";function i(t,e,n,i,r,o,a){var s=this;this._commandStack=n,this._modeling=i,this._selection=r,this._eventBus=e,this._zoomScroll=o,this._canvas=a,this._listeners=[],this._keyHandler=function(t){var e,n,i=t.target,r=s._listeners,o=t.keyCode||t.charCode||-1;if(!c(i,"input, textarea"))for(e=0;n=r[e];e++)n(o,t)&&(t.preventDefault(),t.stopPropagation())},e.on("diagram.destroy",function(){s._fire("destroy"),s.unbind(),s._listeners=null}),e.on("diagram.init",function(){s._fire("init"),t&&t.bindTo&&s.bind(t.bindTo)}),this._init()}function r(t){return t.ctrlKey||t.metaKey||t.shiftKey||t.altKey}function o(t){return t.ctrlKey||t.metaKey}function a(t){return t.shiftKey}var s=t(357),c=t(358);i.$inject=["config.keyboard","eventBus","commandStack","modeling","selection","zoomScroll","canvas"],e.exports=i,i.prototype.bind=function(t){this._node=t,s.bind(t,"keydown",this._keyHandler,!0),this._fire("bind")},i.prototype.getBinding=function(){return this._node},i.prototype.unbind=function(){var t=this._node;t&&(this._fire("unbind"),s.unbind(t,"keydown",this._keyHandler,!0)),this._node=null},i.prototype._fire=function(t){this._eventBus.fire("keyboard."+t,{node:this._node,listeners:this._listeners})},i.prototype._init=function(){function t(t,e){return o(e)&&!a(e)&&90===t?(u.undo(),!0):void 0}function e(t,e){return o(e)&&(89===t||90===t&&a(e))?(u.redo(),!0):void 0}function n(t,e){return 107!==t&&187!==t&&171!==t&&61!==t||!o(e)?void 0:(h.stepZoom(1),!0)}function i(t,e){return 109!==t&&189!==t&&173!==t||!o(e)?void 0:(h.stepZoom(-1),!0)}function r(t,e){return 96!==t&&48!==t||!o(e)?void 0:(d.zoom(1),!0)}function s(t,e){if(46===t){var n=l.get();return n.length&&p.removeElements(n.slice()),!0}}var c=this._listeners,u=this._commandStack,p=this._modeling,l=this._selection,h=this._zoomScroll,d=this._canvas;c.push(t),c.push(e),c.push(s),c.push(n),c.push(i),c.push(r)},i.prototype.addListener=function(t){this._listeners.push(t)},i.prototype.hasModifier=r,i.prototype.isCmd=o,i.prototype.isShift=a},{357:357,358:358}],110:[function(t,e,n){e.exports={__init__:["keyboard"],keyboard:["type",t(109)]}},{109:109}],111:[function(t,e,n){"use strict";function i(t,e,n){p.call(this,t),t.on("shape.move.start",u,function(t){var e=t.context,n=e.shapes;e.shapes=r(n)}),t.on("shape.move.start",c,function(t){var e=t.context,i=e.shapes,r=[];o(i,function(t){var n=t.label;n&&!n.hidden&&-1===e.shapes.indexOf(n)&&r.push(n),t.labelTarget&&r.push(t)}),o(r,function(t){n.makeDraggable(e,t,!0)})}),this.postExecute(["shapes.move"],function(t){var n=t.context,i=n.closure,r=i.enclosedElements;o(r,function(t){t.label&&!r[t.label.id]&&e.moveShape(t.label,n.delta,t.parent)})})}function r(t){return a(t,function(e){return-1===t.indexOf(e.labelTarget)})}var o=t(218),a=t(216),s=t(207),c=250,u=1500,p=t(78);s(i,p),i.$inject=["eventBus","modeling","moveVisuals"],e.exports=i},{207:207,216:216,218:218,78:78}],112:[function(t,e,n){e.exports={__depends__:[t(139)],__init__:["labelSupport"],labelSupport:["type",t(111)]}},{111:111,139:139}],113:[function(t,e,n){"use strict";function i(t,e,n,i,o){this._selection=o,this._dragging=n;var a=this,u={create:function(t){var n,i=e.getDefaultLayer();n=t.frame=c.create("rect",{"class":"djs-lasso-overlay",width:1,height:1,x:0,y:0}),n.appendTo(i)},update:function(t){var e=t.frame,n=t.bbox;e.attr({x:n.x,y:n.y,width:n.width,height:n.height})},remove:function(t){t.frame&&t.frame.remove()}};t.on("lasso.selection.end",function(t){setTimeout(function(){a.activateLasso(t.originalEvent,!0)})}),t.on("lasso.end",function(t){var e=r(t),n=i.filter(function(t){return t});a.select(n,e)}),t.on("lasso.start",function(t){var e=t.context;e.bbox=r(t),u.create(e)}),t.on("lasso.move",function(t){var e=t.context;e.bbox=r(t),u.update(e)}),t.on("lasso.end",function(t){var e=t.context;u.remove(e)}),t.on("lasso.cleanup",function(t){var e=t.context;u.remove(e)}),t.on("element.mousedown",1500,function(t){s(t)&&(a.activateLasso(t.originalEvent),t.stopPropagation())})}function r(t){var e,n={x:t.x-t.dx,y:t.y-t.dy},i={x:t.x,y:t.y};return e=n.x<=i.x&&n.y<i.y||n.x<i.x&&n.y<=i.y?{x:n.x,y:n.y,width:i.x-n.x,height:i.y-n.y}:n.x>=i.x&&n.y<i.y||n.x>i.x&&n.y<=i.y?{x:i.x,y:n.y,width:n.x-i.x,height:i.y-n.y}:n.x<=i.x&&n.y>i.y||n.x<i.x&&n.y>=i.y?{x:n.x,y:i.y,width:i.x-n.x,height:n.y-i.y}:n.x>=i.x&&n.y>i.y||n.x>i.x&&n.y>=i.y?{x:i.x,y:i.y,width:n.x-i.x,height:n.y-i.y}:{x:i.x,y:i.y,width:0,height:0}}var o=t(347),a=t(186).getEnclosedElements,s=t(193).hasPrimaryModifier,c=t(200);i.$inject=["eventBus","canvas","dragging","elementRegistry","selection"],e.exports=i,i.prototype.activateLasso=function(t,e){this._dragging.activate(t,"lasso",{autoActivate:e,cursor:"crosshair",data:{context:{}}})},i.prototype.activateSelection=function(t){this._dragging.activate(t,"lasso.selection",{cursor:"crosshair"})},i.prototype.select=function(t,e){var n=a(t,e);this._selection.select(o(n))}},{186:186,193:193,200:200,347:347}],114:[function(t,e,n){"use strict";e.exports={__init__:["lassoTool"],lassoTool:["type",t(113)]}},{113:113}],115:[function(t,e,n){"use strict";function i(t,e,n){this._eventBus=t,this._elementFactory=e,this._commandStack=n;var i=this;t.on("diagram.init",function(){i.registerHandlers(n)})}var r=t(218),o=t(176);i.$inject=["eventBus","elementFactory","commandStack"],e.exports=i,i.prototype.getHandlers=function(){return{"shape.append":t(116),"shape.create":t(120),"shape.delete":t(123),"shape.move":t(126),"shape.attach":t(117),"shapes.move":t(127),"shape.resize":t(131),"shape.replace":t(130),spaceTool:t(132),"label.create":t(119),"connection.create":t(118),"connection.delete":t(121),"connection.move":t(125),"connection.layout":t(124),"connection.updateWaypoints":t(134),"connection.reconnectStart":t(129),"connection.reconnectEnd":t(129),"elements.delete":t(122),"element.updateAnchors":t(133)}},i.prototype.registerHandlers=function(t){r(this.getHandlers(),function(e,n){t.registerHandler(n,e)})},i.prototype.moveShape=function(t,e,n,i){var r={shape:t,delta:e,newParent:n,hints:i||{}};this._commandStack.execute("shape.move",r)},i.prototype.updateAttachment=function(t,e){var n={shape:t,newHost:e};this._commandStack.execute("shape.attach",n)},i.prototype.moveShapes=function(t,e,n,i,r){"object"==typeof i&&(r=i,i=void 0);var o,a=n;i===!0&&(o=n,a=n.parent),i===!1&&(o=null);var s={shapes:t,delta:e,newParent:a,newHost:o,hints:r||{}};this._commandStack.execute("shapes.move",s)},i.prototype.updateAnchors=function(t,e){var n={element:t,delta:e};this._commandStack.execute("element.updateAnchors",n)},i.prototype.moveConnection=function(t,e,n,i){var r={connection:t,delta:e,newParent:n,hints:i||{}};this._commandStack.execute("connection.move",r)},i.prototype.layoutConnection=function(t,e){var n={connection:t,hints:e||{}};this._commandStack.execute("connection.layout",n)},i.prototype.createConnection=function(t,e,n,i){n=this._create("connection",n);var r={source:t,target:e,parent:i,connection:n};return this._commandStack.execute("connection.create",r),r.connection},i.prototype.createShape=function(t,e,n,i){t=this._create("shape",t);var r={position:e,shape:t,parent:n,host:t.host};return i&&(r.parent=n.parent,r.host=n),this._commandStack.execute("shape.create",r),r.shape},i.prototype.createLabel=function(t,e,n,i){n=this._create("label",n);var r={labelTarget:t,position:e,parent:i,shape:n};return this._commandStack.execute("label.create",r),r.shape},i.prototype.appendShape=function(t,e,n,i,r,o){e=this._create("shape",e);var a={source:t,position:n,parent:i,shape:e,connection:r,connectionParent:o};return this._commandStack.execute("shape.append",a),a.shape},i.prototype.removeElements=function(t){var e={elements:t};this._commandStack.execute("elements.delete",e)},i.prototype.removeShape=function(t){var e={shape:t};this._commandStack.execute("shape.delete",e)},i.prototype.removeConnection=function(t){var e={connection:t};this._commandStack.execute("connection.delete",e)},i.prototype.replaceShape=function(t,e,n){var i={oldShape:t,newData:e,options:n};return this._commandStack.execute("shape.replace",i),i.newShape},i.prototype.resizeShape=function(t,e){var n={shape:t,newBounds:e};this._commandStack.execute("shape.resize",n)},i.prototype.createSpace=function(t,e,n,i){var r={movingShapes:t,resizingShapes:e,delta:n,direction:i};this._commandStack.execute("spaceTool",r)},i.prototype.updateWaypoints=function(t,e){var n={connection:t,newWaypoints:e};this._commandStack.execute("connection.updateWaypoints",n)},i.prototype.reconnectStart=function(t,e,n){var i={connection:t,newSource:e,dockingOrPoints:n};this._commandStack.execute("connection.reconnectStart",i)},i.prototype.reconnectEnd=function(t,e,n){var i={connection:t,newTarget:e,dockingOrPoints:n};this._commandStack.execute("connection.reconnectEnd",i)},i.prototype.connect=function(t,e,n){return this.createConnection(t,e,n||{},t.parent)},i.prototype._create=function(t,e){return e instanceof o.Base?e:this._elementFactory.create(t,e)}},{116:116,117:117,118:118,119:119,120:120,121:121,122:122,123:123,124:124,125:125,126:126,127:127,129:129,130:130,131:131,132:132,133:133,134:134,176:176,218:218}],116:[function(t,e,n){"use strict";function i(t){this._modeling=t}function r(t,e){return o(t.outgoing,function(t){return t.target===e})}var o=t(214),a=t(207);a(i,t(128)),i.$inject=["modeling"],e.exports=i,i.prototype.preExecute=function(t){if(!t.source)throw new Error("source required");var e=t.parent||t.source.parent,n=this._modeling.createShape(t.shape,t.position,e);t.shape=n},i.prototype.postExecute=function(t){var e=t.connectionParent||t.shape.parent;r(t.source,t.shape)||this._modeling.connect(t.source,t.shape,t.connection,e)}},{128:128,207:207,214:214}],117:[function(t,e,n){"use strict";function i(t){this._modeling=t}function r(t,e,n){var i=t.indexOf(e),r=t.indexOf(n);-1===i||-1===r||r>i||(t.splice(r,1),i=t.indexOf(e),t.splice(i,1,e,n))}function o(t){var e,n=t.shape,i=t.newHost,o=t.oldHost,a=n.parent,s=a.children;if(i){var c=(e=n.label)?[o,n,e]:[o,n];t.elements=p(c,function(t){return{shape:t,idx:s.indexOf(t)}}),r(s,i,n),e&&r(s,n,e)}}function a(t){var e=t.shape,n=t.oldHost,i=e.parent,r=i.children,o=t.elements;n&&u(o,function(t){r.splice(r.indexOf(t.shape),1),r.splice(t.idx,0,t.shape)})}function s(t,e){var n=t&&t.attachers,i=-1;return n&&(i=n.indexOf(e),-1!==i&&n.splice(i,1)),i}function c(t,e,n){if(t){var i=t.attachers;i||(t.attachers=i=[]),i.splice(n||i.length,0,e)}}var u=t(218),p=t(221);e.exports=i,i.$inject=["modeling"],i.prototype.execute=function(t){var e=t.shape,n=t.newHost,i=e.host;return t.oldHost=i,t.attacherIdx=s(i,e),c(n,e),o(t),e.host=n,e},i.prototype.revert=function(t){var e=t.shape,n=t.newHost,i=t.oldHost,r=t.attacherIdx;return e.host=i,s(n,e),c(i,e,r),a(t),e}},{218:218,221:221}],118:[function(t,e,n){"use strict";function i(t,e){this._canvas=t,this._layouter=e}i.$inject=["canvas","layouter"],e.exports=i,i.prototype.execute=function(t){var e=t.source,n=t.target,i=t.parent;if(!e||!n)throw new Error("source and target required");if(!i)throw new Error("parent required");var r=t.connection;return r.source=e,r.target=n,r.waypoints||(r.waypoints=this._layouter.layoutConnection(r)),this._canvas.addConnection(r,i),r},i.prototype.revert=function(t){var e=t.connection;this._canvas.removeConnection(e),e.source=null,e.target=null}},{}],119:[function(t,e,n){"use strict";function i(t){o.call(this,t)}var r=t(207),o=t(120);r(i,o),i.$inject=["canvas"],e.exports=i;var a=o.prototype.execute;i.prototype.execute=function(t){return this.ensureValidDimensions(t),a.call(this,t)};var s=o.prototype.revert;i.prototype.revert=function(t){return t.shape.labelTarget=null,s.call(this,t)},i.prototype.ensureValidDimensions=function(t){var e=t.shape;["width","height"].forEach(function(t){"undefined"==typeof e[t]&&(e[t]=0)})},i.prototype.getParent=function(t){return t.parent||t.labelTarget&&t.labelTarget.parent},i.prototype.addElement=function(t,e,n){t.labelTarget=n.labelTarget,this._canvas.addShape(t,e,!0)}},{120:120,207:207}],120:[function(t,e,n){"use strict";function i(t,e){this._canvas=t,this._modeling=e}var r=t(339);i.$inject=["canvas","modeling"],e.exports=i,i.prototype.execute=function(t){var e=this.getParent(t),n=t.shape;return this.setPosition(n,t),this.addElement(n,e,t),n},i.prototype.revert=function(t){this._canvas.removeShape(t.shape)},i.prototype.postExecute=function(t){var e=this._modeling,n=t.shape,i=t.host;i&&e.updateAttachment(n,i,!0)},i.prototype.getParent=function(t){var e=t.parent;if(!e)throw new Error("parent required");return e},i.prototype.getPosition=function(t){if(!t.position)throw new Error("no position given");return t.position},i.prototype.addElement=function(t,e){this._canvas.addShape(t,e)},i.prototype.setPosition=function(t,e){var n=this.getPosition(e);r(t,{x:n.x-t.width/2,y:n.y-t.height/2})}},{339:339}],121:[function(t,e,n){"use strict";function i(t,e){this._canvas=t,this._modeling=e}var r=t(184);i.$inject=["canvas","modeling"],e.exports=i,i.prototype.preExecute=function(t){var e=t.connection;e.label&&this._modeling.removeShape(e.label)},i.prototype.execute=function(t){var e=t.connection,n=e.parent;t.parent=n,t.parentIndex=r.indexOf(n.children,e),t.source=e.source,t.target=e.target,this._canvas.removeConnection(e),e.source=null,e.target=null,e.label=null},i.prototype.revert=function(t){var e=t.connection,n=t.parent,i=t.parentIndex;e.source=t.source,e.target=t.target,r.add(n.children,e,i),this._canvas.addConnection(e,n)}},{184:184}],122:[function(t,e,n){"use strict";function i(t,e){this._modeling=t,this._elementRegistry=e}var r=t(218),o=t(207);o(i,t(128)),i.$inject=["modeling","elementRegistry"],e.exports=i,i.prototype.postExecute=function(t){var e=this._modeling,n=this._elementRegistry,i=t.elements;r(i,function(t){n.get(t.id)&&(t.waypoints?e.removeConnection(t):e.removeShape(t))})}},{128:128,207:207,218:218}],123:[function(t,e,n){"use strict";function i(t,e){this._canvas=t,this._modeling=e}var r=t(218),o=t(184),a=t(195).saveClear;i.$inject=["canvas","modeling"],e.exports=i,i.prototype.preExecute=function(t){var e=this._modeling,n=t.shape,i=n.label;n.labelTarget&&(t.labelTarget=n.labelTarget,n.labelTarget=null),i&&this._modeling.removeShape(i),a(n.incoming,function(t){e.removeConnection(t)}),a(n.outgoing,function(t){e.removeConnection(t)}),a(n.children,function(t){e.removeShape(t)})},i.prototype.execute=function(t){var e=this._canvas,n=t.shape,i=n.parent,r=n.host;t.parent=i,t.parentIndex=o.indexOf(i.children,n),r&&(t.hostIndex=o.indexOf(r.attachers,n),o.remove(r&&r.attachers,n)),n.label=null,e.removeShape(n)},i.prototype.revert=function(t){var e=this._canvas,n=t.shape,i=t.parent,a=t.parentIndex,s=t.labelTarget,c=n.attachers,u=n.host;o.add(i.children,n,a),u&&o.add(u&&u.attachers,n,t.hostIndex),s&&(s.label=n),e.addShape(n,i),c&&r(c,function(t){e.addShape(t,i)})}},{184:184,195:195,218:218}],124:[function(t,e,n){"use strict";function i(t,e){this._layouter=t,this._canvas=e}function r(t){function e(t,n){var i=t.indexOf(n);if(0>i&&n){var r=n.parent;i=e(t,r)}return i}var n=t.parent.children,i=n.indexOf(t),r=e(n,t.source),o=e(n,t.target),a=Math.max(r+1,o+1,i);return a>i&&(n.splice(a,0,t),n.splice(i,1)),a}var o=t(339);i.$inject=["layouter","canvas"],e.exports=i,i.prototype.execute=function(t){var e=t.connection,n=e.parent,i=n.children,a=i.indexOf(e);return o(t,{oldWaypoints:e.waypoints,oldIndex:a}),r(e),e.waypoints=this._layouter.layoutConnection(e,t.hints),e},i.prototype.revert=function(t){var e=t.connection,n=e.parent,i=n.children,r=i.indexOf(e),o=t.oldIndex;return e.waypoints=t.oldWaypoints,o!==r&&(i.splice(r,1),i.splice(o,0,e)),e}},{339:339}],125:[function(t,e,n){"use strict";function i(){}var r=t(218),o=t(184);e.exports=i,i.prototype.execute=function(t){var e=t.hints.updateAnchors!==!1,n=t.connection,i=t.delta,a=this.getNewParent(n,t),s=n.parent;return t.oldParent=s,t.oldParentIndex=o.indexOf(s.children,n),r(n.waypoints,function(t){t.x+=i.x,t.y+=i.y,e&&t.original&&(t.original.x+=i.x,t.original.y+=i.y)}),n.parent=a,n},i.prototype.revert=function(t){var e=t.hints.updateAnchors!==!1,n=t.connection,i=t.oldParent,a=t.oldParentIndex,s=t.delta;return o.add(i.children,n,a),n.parent=i,r(n.waypoints,function(t){t.x-=s.x,t.y-=s.y,e&&t.original&&(t.original.x-=s.x,t.original.y-=s.y)}),n},i.prototype.getNewParent=function(t,e){return e.newParent||t.parent}},{184:184,218:218}],126:[function(t,e,n){"use strict";function i(t){this._modeling=t,this._helper=new a(t)}var r=t(339),o=t(218),a=t(135),s=t(184);i.$inject=["modeling"],e.exports=i,i.prototype.execute=function(t){var e=t.shape,n=t.delta,i=this.getNewParent(t),o=e.parent;return t.oldParent=o,t.oldParentIndex=s.indexOf(o.children,e),r(e,{parent:i,x:e.x+n.x,y:e.y+n.y}),e},i.prototype.postExecute=function(t){var e=t.shape,n=t.delta,i=this._modeling;t.hints.updateAnchors!==!1&&i.updateAnchors(e,n),t.hints.layout!==!1&&(o(e.incoming,function(t){i.layoutConnection(t,{endChanged:!0})}),o(e.outgoing,function(t){i.layoutConnection(t,{startChanged:!0})})),t.hints.recurse!==!1&&this.moveChildren(t)},i.prototype.revert=function(t){var e=t.shape,n=t.oldParent,i=t.oldParentIndex,o=t.delta;return s.add(n.children,e,i),r(e,{parent:n,x:e.x-o.x,y:e.y-o.y}),e},i.prototype.moveChildren=function(t){var e=t.delta,n=t.shape;this._helper.moveRecursive(n.children,e,null)},i.prototype.getNewParent=function(t){return t.newParent||t.shape.parent}},{135:135,184:184,218:218,339:339}],127:[function(t,e,n){"use strict";function i(t){this._helper=new r(t)}var r=t(135);i.$inject=["modeling"],e.exports=i,i.prototype.preExecute=function(t){t.closure=this._helper.getClosure(t.shapes)},i.prototype.postExecute=function(t){this._helper.moveClosure(t.closure,t.delta,t.newParent,t.newHost)},i.prototype.execute=function(t){},i.prototype.revert=function(t){}},{135:135}],128:[function(t,e,n){"use strict";function i(){}e.exports=i,i.prototype.execute=function(){},i.prototype.revert=function(){}},{}],129:[function(t,e,n){"use strict";function i(){}var r=t(330);i.$inject=[],e.exports=i,i.prototype.execute=function(t){
var e,n=t.newSource,i=t.newTarget,o=t.connection,a=t.dockingOrPoints,s=o.waypoints;if(!n&&!i)throw new Error("newSource or newTarget are required");if(n&&i)throw new Error("must specify either newSource or newTarget");return t.oldWaypoints=s,r(a)?e=a:(e=s.slice(),e.splice(n?0:-1,1,a)),n&&(t.oldSource=o.source,o.source=n),i&&(t.oldTarget=o.target,o.target=i),o.waypoints=e,o},i.prototype.revert=function(t){var e=t.newSource,n=t.newTarget,i=t.connection;return e&&(i.source=t.oldSource),n&&(i.target=t.oldTarget),i.waypoints=t.oldWaypoints,i}},{330:330}],130:[function(t,e,n){"use strict";function i(t,e){this._modeling=t,this._rules=e}var r=t(218);i.$inject=["modeling","rules"],e.exports=i,i.prototype.preExecute=function(t){var e,n=this._modeling,i=this._rules,o=t.oldShape,a=t.newData,s={x:a.x,y:a.y};e=t.newShape=t.newShape||n.createShape(a,s,o.parent),o.host&&n.updateAttachment(e,o.host),n.moveShapes(o.children,{x:0,y:0},e);var c=o.incoming.slice(),u=o.outgoing.slice();r(c,function(t){var r=t.waypoints,o=r[r.length-1],a=i.allowed("connection.reconnectEnd",{source:t.source,target:e,connection:t});a&&n.reconnectEnd(t,e,o)}),r(u,function(t){var r=t.waypoints,o=r[0],a=i.allowed("connection.reconnectStart",{source:e,target:t.target,connection:t});a&&n.reconnectStart(t,e,o)})},i.prototype.postExecute=function(t){var e=this._modeling,n=t.oldShape;e.removeShape(n)},i.prototype.execute=function(t){},i.prototype.revert=function(t){}},{218:218}],131:[function(t,e,n){"use strict";function i(t){this._modeling=t}var r=t(339),o=t(218);i.$inject=["modeling"],e.exports=i,i.prototype.execute=function(t){var e=t.shape,n=t.newBounds;if(void 0===n.x||void 0===n.y||void 0===n.width||void 0===n.height)throw new Error("newBounds must have {x, y, width, height} properties");if(n.width<10||n.height<10)throw new Error("width and height cannot be less than 10px");return t.oldBounds={width:e.width,height:e.height,x:e.x,y:e.y},r(e,{width:n.width,height:n.height,x:n.x,y:n.y}),e},i.prototype.postExecute=function(t){var e=t.shape,n=this._modeling;o(e.incoming,function(t){n.layoutConnection(t,{endChanged:!0})}),o(e.outgoing,function(t){n.layoutConnection(t,{startChanged:!0})})},i.prototype.revert=function(t){var e=t.shape,n=t.oldBounds;return r(e,{width:n.width,height:n.height,x:n.x,y:n.y}),e}},{218:218,339:339}],132:[function(t,e,n){"use strict";function i(t){this._modeling=t}var r=t(218),o=t(165);i.$inject=["modeling"],e.exports=i,i.prototype.preExecute=function(t){var e=this._modeling,n=t.resizingShapes,i=t.delta,a=t.direction;r(n,function(t){var n=o.resizeBounds(t,a,i);e.resizeShape(t,n)})},i.prototype.postExecute=function(t){var e=this._modeling,n=t.movingShapes,i=t.delta;e.moveShapes(n,i)},i.prototype.execute=function(t){},i.prototype.revert=function(t){}},{165:165,218:218}],133:[function(t,e,n){"use strict";function i(){}var r=t(218),o=t(339);e.exports=i,i.prototype.execute=function(t){return this.updateAnchors(t.element,t.delta)},i.prototype.revert=function(t){var e=t.delta,n={x:-1*e.x,y:-1*e.y};return this.updateAnchors(t.element,n)},i.prototype.updateAnchors=function(t,e){function n(t,e){return{x:t.x+e.x,y:t.y+e.y}}function i(t){var i=t.original;t.original=o(i||{},n(i||t,e))}var a=[];return r(t.incoming,function(t){var e=t.waypoints;i(e[e.length-1]),a.push(t)}),r(t.outgoing,function(t){var e=t.waypoints;i(e[0]),a.push(t)}),a}},{218:218,339:339}],134:[function(t,e,n){"use strict";function i(){}e.exports=i,i.prototype.execute=function(t){var e=t.connection,n=t.newWaypoints;return t.oldWaypoints=e.waypoints,e.waypoints=n,e},i.prototype.revert=function(t){var e=t.connection,n=t.oldWaypoints;return e.waypoints=n,e}},{}],135:[function(t,e,n){"use strict";function i(t){this._modeling=t}var r=t(218),o=t(186);e.exports=i,i.prototype.moveRecursive=function(t,e,n){return this.moveClosure(this.getClosure(t),e,n)},i.prototype.moveClosure=function(t,e,n,i){var o=this._modeling,a=t.allShapes,s=t.allConnections,c=t.enclosedConnections,u=t.topLevel;r(a,function(t){o.moveShape(t,e,u[t.id]&&n,{recurse:!1,layout:!1})}),r(s,function(t){var i=!!a[t.source.id],r=!!a[t.target.id];c[t.id]&&i&&r?o.moveConnection(t,e,u[t.id]&&n,{updateAnchors:!1}):o.layoutConnection(t,{startChanged:i,endChanged:r})})},i.prototype.getClosure=function(t){return o.getClosure(t)}},{186:186,218:218}],136:[function(t,e,n){e.exports={__depends__:[t(80),t(98),t(155)],__init__:["modeling"],modeling:["type",t(115)],layouter:["type",t(172)]}},{115:115,155:155,172:172,80:80,98:98}],137:[function(t,e,n){"use strict";function i(t){return{x:t.x+d(t.width/2),y:t.y+d(t.height/2)}}function r(t,e,n,r,s){function c(t,e,n,i){return s.allowed("shapes.move",{shapes:t,delta:e,position:n,target:i})}function f(t,n,r){if(!n.waypoints&&n.parent){var o=i(n);e.activate(t,o,"shape.move",{cursor:"grabbing",autoActivate:r,data:{shape:n,context:{}}})}}t.on("shape.move.start",l,function(t){var e=t.context,n=t.shape,i=r.get().slice();-1===i.indexOf(n)&&(i=[n]),i=o(i),a(e,{shapes:i,shape:n})}),t.on("shape.move.start",p,function(t){var e,n=t.context,i=n.shapes;return e=n.canExecute=c(i),e?void 0:(t.stopPropagation(),!1)}),t.on("shape.move.move",u,function(t){var e,n=t.context,i=n.shapes,r=t.hover,o={x:t.dx,y:t.dy},a={x:t.x,y:t.y};return e=c(i,o,a,r),n.delta=o,n.canExecute=e,null===e?void(n.target=null):void(n.target=r)}),t.on("shape.move.end",function(t){var e=t.context,i=e.delta,r=e.canExecute,o="attach"===r;return r?(i.x=d(i.x),i.y=d(i.y),void n.moveShapes(e.shapes,i,e.target,o)):!1}),t.on("element.mousedown",function(t){var e=h(t);if(!e)throw new Error("must supply DOM mousedown event");f(e,t.element)}),this.start=f}function o(t){var e=c(t,"id");return s(t,function(t){for(;t=t.parent;)if(e[t.id])return!1;return!0})}var a=t(339),s=t(216),c=t(219),u=500,p=1250,l=1500,h=t(187).getOriginal,d=Math.round;r.$inject=["eventBus","dragging","modeling","selection","rules"],e.exports=r},{187:187,216:216,219:219,339:339}],138:[function(t,e,n){"use strict";function i(t,e,n,i){function s(t){return e.getGraphics(t)}function c(t){var e=p.selfAndDirectChildren(t,!0),n=r(e);return n}function y(t){var e=p.selfAndAllChildren(t,!0),n=u(e,function(t){return(t.incoming||[]).concat(t.outgoing||[])});return o(e.concat(n),!0)}function v(t,e){[m,d,f].forEach(function(i){i===e?n.addMarker(t,i):n.removeMarker(t,i)})}function g(t,e){var r=t.dragGroup;r||(r=t.dragGroup=n.getDefaultLayer().group().attr(i.cls("djs-drag-group",["no-events"])));var o=s(e),a=o.clone(),c=o.getBBox();a.attr(i.cls("djs-dragger",[],{x:c.x,y:c.y})),r.add(a)}function x(t,e,i){g(t,e),i&&n.addMarker(e,h),t.allDraggedElements?t.allDraggedElements.push(e):t.allDraggedElements=[e]}this.makeDraggable=x,t.on("shape.move.start",l,function(t){var e=t.context,i=e.shapes,r=e.allDraggedElements,s=c(i);s.forEach(function(t){g(e,t)}),r=r?o(r,y(i)):y(i),a(r,function(t){n.addMarker(t,h)}),e.allDraggedElements=r}),t.on("shape.move.move",l,function(t){var e=t.context,n=e.dragGroup,i=e.target,r=e.canExecute;i&&("attach"===r?v(i,m):v(i,e.canExecute?d:f)),n.translate(t.dx,t.dy)}),t.on(["shape.move.out","shape.move.cleanup"],function(t){var e=t.context,n=e.target;n&&v(n,null)}),t.on("shape.move.cleanup",function(t){var e=t.context,i=e.allDraggedElements,r=e.dragGroup;a(i,function(t){n.removeMarker(t,h)}),r&&r.remove()})}function r(t){var e=s(t,function(e){if(e.waypoints){var n=c(t,e.source),i=c(t,e.target);return n&&i}return!0});return e}var o=t(208),a=t(218),s=t(216),c=t(217),u=t(221),p=t(186),l=500,h="djs-dragging",d="drop-ok",f="drop-not-ok",m="attach-ok";i.$inject=["eventBus","elementRegistry","canvas","styles"],e.exports=i},{186:186,208:208,216:216,217:217,218:218,221:221}],139:[function(t,e,n){e.exports={__depends__:[t(108),t(159),t(141),t(155),t(106)],__init__:["move","moveVisuals"],move:["type",t(137)],moveVisuals:["type",t(138)]}},{106:106,108:108,137:137,138:138,141:141,155:155,159:159}],140:[function(t,e,n){"use strict";function i(t,e,n){function i(t,e){return t.rect(10,10,0,0).attr(c)}function o(t,e){t.attr({x:-s,y:-s,width:e.width+2*s,height:e.height+2*s})}function a(t,e){var n=r(e);t.attr({x:n.x-s,y:n.y-s,width:n.width+2*s,height:n.height+2*s})}var s=6,c=e.cls("djs-outline",["no-fill"]);t.on(["shape.added","shape.changed"],function(t){var e=t.element,n=t.gfx,r=n.select(".djs-outline");r||(r=i(n,e)),o(r,e)}),t.on(["connection.added","connection.changed"],function(t){var e=t.element,n=t.gfx,r=n.select(".djs-outline");r||(r=i(n,e)),a(r,e)})}var r=t(186).getBBox;i.$inject=["eventBus","styles","elementRegistry"],e.exports=i},{186:186}],141:[function(t,e,n){"use strict";e.exports={__init__:["outline"],outline:["type",t(140)]}},{140:140}],142:[function(t,e,n){"use strict";function i(t){var e=f('<div class="djs-overlay-container" style="position: absolute; width: 0; height: 0;" />');return t.insertBefore(e,t.firstChild),e}function r(t,e,n){p(t.style,{left:e+"px",top:n+"px"})}function o(t,e){t.style.display=e===!1?"none":""}function a(t,e,n,r){this._eventBus=e,this._canvas=n,this._elementRegistry=r,this._ids=g,this._overlayDefaults={show:{minZoom:.7,maxZoom:5}},this._overlays={},this._overlayContainers={},this._overlayRoot=i(n.getContainer()),this._init(t)}var s=t(330),c=t(336),u=t(334),p=t(339),l=t(218),h=t(216),d=t(228),f=t(356),m=t(352),y=t(360),v=t(186).getBBox,g=new(t(190))("ov");a.$inject=["config.overlays","eventBus","canvas","elementRegistry"],e.exports=a,a.prototype.get=function(t){if(c(t)&&(t={id:t}),t.element){var e=this._getOverlayContainer(t.element,!0);return e?t.type?h(e.overlays,{type:t.type}):e.overlays.slice():[]}return t.type?h(this._overlays,{type:t.type}):t.id?this._overlays[t.id]:null},a.prototype.add=function(t,e,n){if(u(e)&&(n=e,e=null),t.id||(t=this._elementRegistry.get(t)),!n.position)throw new Error("must specifiy overlay position");if(!n.html)throw new Error("must specifiy overlay html");if(!t)throw new Error("invalid element specified");var i=this._ids.next();return n=p({},this._overlayDefaults,n,{id:i,type:e,element:t,html:n.html}),this._addOverlay(n),i},a.prototype.remove=function(t){var e=this.get(t)||[];s(e)||(e=[e]);var n=this;l(e,function(t){var e=n._getOverlayContainer(t.element,!0);if(t&&(y(t.html),y(t.htmlContainer),delete t.htmlContainer,delete t.element,delete n._overlays[t.id]),e){var i=e.overlays.indexOf(t);-1!==i&&e.overlays.splice(i,1)}})},a.prototype.show=function(){o(this._overlayRoot)},a.prototype.hide=function(){o(this._overlayRoot,!1)},a.prototype._updateOverlayContainer=function(t){var e=t.element,n=t.html,i=e.x,o=e.y;if(e.waypoints){var a=v(e);i=a.x,o=a.y}r(n,i,o)},a.prototype._updateOverlay=function(t){var e=t.position,n=t.htmlContainer,i=t.element,o=e.left,a=e.top;if(void 0!==e.right){var s;s=i.waypoints?v(i).width:i.width,o=-1*e.right+s}if(void 0!==e.bottom){var c;c=i.waypoints?v(i).height:i.height,a=-1*e.bottom+c}r(n,o||0,a||0)},a.prototype._createOverlayContainer=function(t){var e=f('<div class="djs-overlays djs-overlays-'+t.id+'" style="position: absolute" />');this._overlayRoot.appendChild(e);var n={html:e,element:t,overlays:[]};return this._updateOverlayContainer(n),n},a.prototype._updateRoot=function(t){var e=t.scale||1,n=t.scale||1,i="matrix("+e+",0,0,"+n+","+-1*t.x*e+","+-1*t.y*n+")";this._overlayRoot.style.transform=i,this._overlayRoot.style["-ms-transform"]=i},a.prototype._getOverlayContainer=function(t,e){var n=t&&t.id||t,i=this._overlayContainers[n];return i||e||(i=this._overlayContainers[n]=this._createOverlayContainer(t)),i},a.prototype._addOverlay=function(t){var e,n,i=t.id,r=t.element,o=t.html;o.get&&(o=o.get(0)),c(o)&&(o=f(o)),n=this._getOverlayContainer(r),e=f('<div class="djs-overlay" data-overlay-id="'+i+'" style="position: absolute">'),e.appendChild(o),t.type&&m(e).add("djs-overlay-"+t.type),t.htmlContainer=e,n.overlays.push(t),n.html.appendChild(e),this._overlays[i]=t,this._updateOverlay(t)},a.prototype._updateOverlayVisibilty=function(t){l(this._overlays,function(e){var n=e.show,i=e.htmlContainer,r=!0;n&&((n.minZoom>t.scale||n.maxZoom<t.scale)&&(r=!1),o(i,r))})},a.prototype._init=function(t){var e=this._eventBus,n=this,i=function(t){n._updateRoot(t),n._updateOverlayVisibilty(t),n.show()};t&&t.deferUpdate===!1||(i=d(i,300)),e.on("canvas.viewbox.changed",function(t){n.hide(),i(t.viewbox)}),e.on(["shape.remove","connection.remove"],function(t){var e=n.get({element:t.element});l(e,function(t){n.remove(t.id)})}),e.on(["element.changed"],function(t){var e=t.element,i=n._getOverlayContainer(e,!0);i&&(l(i.overlays,function(t){n._updateOverlay(t)}),n._updateOverlayContainer(i))}),e.on("element.marker.update",function(t){var e=n._getOverlayContainer(t.element,!0);e&&m(e.html)[t.add?"add":"remove"](t.marker)})}},{186:186,190:190,216:216,218:218,228:228,330:330,334:334,336:336,339:339,352:352,356:356,360:360}],143:[function(t,e,n){e.exports={__init__:["overlays"],overlays:["type",t(142)]}},{142:142}],144:[function(t,e,n){"use strict";function i(t,e){this._eventBus=t,this._canvas=e,this._providers=[]}var r=t(331),o=t(218),a=t(356),s=t(359),c=t(351),u=t(353),p=t(352),l=t(358),h=t(355),d=t(357),f=".djs-palette-toggle",m=".entry",y=f+", "+m;i.$inject=["eventBus","canvas"],e.exports=i,i.prototype.registerProvider=function(t){this._providers.push(t),this._container||this._init(),this._update()},i.prototype.getEntries=function(){var t={};return o(this._providers,function(e){var n=e.getPaletteEntries();o(n,function(e,n){t[n]=e})}),t},i.prototype._init=function(){var t=this._canvas.getContainer(),e=this._container=a(i.HTML_MARKUP),n=this;t.appendChild(e),h.bind(e,y,"click",function(t){var e=t.delegateTarget;return l(e,f)?n.toggle():void n.trigger("click",t)}),d.bind(e,"mousedown",function(t){t.stopPropagation()}),h.bind(e,m,"dragstart",function(t){n.trigger("dragstart",t)}),this._eventBus.fire("palette.create",{html:e})},i.prototype._update=function(){var t=s(".djs-palette-entries",this._container),e=this._entries=this.getEntries();u(t),o(e,function(e,n){var i=e.group||"default",r=s("[data-group="+i+"]",t);r||(r=a('<div class="group" data-group="'+i+'"></div>'),t.appendChild(r));var o=e.html||(e.separator?'<hr class="separator" />':'<div class="entry" draggable="true"></div>'),u=a(o);r.appendChild(u),e.separator||(c(u,"data-action",n),e.title&&c(u,"title",e.title),e.className&&p(u).add(e.className),e.imageUrl&&u.appendChild(a('<img src="'+e.imageUrl+'">')))}),this.open(!0)},i.prototype.trigger=function(t,e,n){var i,o,a,s=this._entries,u=e.delegateTarget||e.target;if(!u)return e.preventDefault();if(i=s[c(u,"data-action")],o=i.action,a=e.originalEvent||e,r(o)){if("click"===t)return o(a,n)}else if(o[t])return o[t](a,n);e.preventDefault()},i.prototype.close=function(){p(this._container).remove("open")},i.prototype.open=function(){p(this._container).add("open")},i.prototype.toggle=function(t){this.isOpen()?this.close():this.open()},i.prototype.isOpen=function(){return this._container&&p(this._container).has("open")},i.HTML_MARKUP='<div class="djs-palette"><div class="djs-palette-entries"></div><div class="djs-palette-toggle"></div></div>'},{218:218,331:331,351:351,352:352,353:353,355:355,356:356,357:357,358:358,359:359}],145:[function(t,e,n){e.exports={__init__:["palette"],palette:["type",t(144)]}},{144:144}],146:[function(t,e,n){"use strict";function i(t,e,n){this._eventBus=t,this._canvas=e,this._modeling=n}var r=t(218),o=t(339),a=t(217),s=t(355),c=t(356),u=t(352),p=t(351),l=t(360),h="data-id";i.$inject=["eventBus","canvas","modeling"],i.prototype.open=function(t){var e=t.className||"popup-menu",n=t.position,i=t.entries,r=t.headerEntries;if(!n)throw new Error("the position argument is missing");if(!i)throw new Error("the entries argument is missing");this.isOpen()&&this.close();var o=this._canvas,a=o.getContainer(),s=this._createContainer(e,n);if(r){var c=this._createEntries(r,"djs-popup-header");s.appendChild(c)}var u=this._createEntries(i,"djs-popup-body");return s.appendChild(u),this._attachContainer(s,a),this._current={container:s,menu:t},this},i.prototype.close=function(){this.isOpen()&&(this._unbindHandlers(),l(this._current.container),this._current=null)},i.prototype.isOpen=function(){return!!this._current},i.prototype.trigger=function(t){t.preventDefault();var e=t.delegateTarget||t.target,n=p(e,h),i=this._getEntry(n);if(i.action){var r=i.action.call(null,t,i);return this.close(),r}},i.prototype._getEntry=function(t){var e=this._current.menu,n={id:t},i=a(e.entries,n)||a(e.headerEntries,n);if(!i)throw new Error("entry not found");return i},i.prototype._createContainer=function(t,e){var n=c('<div class="djs-popup">');return o(n.style,{position:"absolute",left:e.x+"px",top:e.y+"px"}),u(n).add(t),n},i.prototype._attachContainer=function(t,e){var n=this;s.bind(t,".entry","click",function(t){n.trigger(t)});var i=this._canvas.zoom();t.style.transformOrigin="top left",t.style.transform="scale("+i+")",e.appendChild(t),this._bindHandlers()},i.prototype._createEntries=function(t,e){var n=c("<div>"),i=this;return u(n).add(e),r(t,function(t){var e=i._createEntry(t,n);n.appendChild(e)}),n},i.prototype._createEntry=function(t){if(!t.id)throw new Error("every entry must have the id property set");var e=c("<div>"),n=u(e);if(n.add("entry"),t.className&&n.add(t.className),p(e,h,t.id),t.label){var i=c("<span>");i.textContent=t.label,e.appendChild(i)}return t.imageUrl&&e.appendChild(c('<img src="'+t.imageUrl+'" />')),t.active===!0&&n.add("active"),t.disabled===!0&&n.add("disabled"),e},i.prototype._bindHandlers=function(){function t(){n.close()}var e=this._eventBus,n=this;e.once("contextPad.close",t),e.once("canvas.viewbox.changed",t),e.once("commandStack.changed",t)},i.prototype._unbindHandlers=function(){function t(){n.close()}var e=this._eventBus,n=this;e.off("contextPad.close",t),e.off("canvas.viewbox.changed",t),e.off("commandStack.changed",t)},e.exports=i},{217:217,218:218,339:339,351:351,352:352,355:355,356:356,360:360}],147:[function(t,e,n){"use strict";e.exports={__init__:["popupMenu"],popupMenu:["type",t(146)]}},{146:146}],148:[function(t,e,n){"use strict";function i(t){this._modeling=t}e.exports=i,i.$inject=["modeling"],i.prototype.replaceElement=function(t,e,n){var i=this._modeling,r=null;return t.waypoints||(e.x=t.x+(e.width||t.width)/2,e.y=t.y+(e.height||t.height)/2,r=i.replaceShape(t,e,n)),r}},{}],149:[function(t,e,n){"use strict";e.exports={__init__:["replace"],replace:["type",t(148)]}},{148:148}],150:[function(t,e,n){"use strict";function i(t,e,n,i,w,_,S){function A(t){var e=a(t,["newBounds","shape","delta","direction"]);return n.allowed("shape.resize",e)}function T(t){return t.waypoints?!1:"label"===t.type?!1:!0}function C(t,e){var n,i,r,o;return"object"==typeof e?(n=e.left||E,i=e.right||E,r=e.top||E,o=e.bottom||E):n=i=r=o=e||E,{x:t.x-n,y:t.y-r,width:t.width+n+i,height:t.height+r+o}}function N(t,e){var n=o(t.children,T);return n.length?C(p.getBBox(n),e):void 0}function M(t){var e,n=t.shape,i=t.direction;return e=N(n,t.childrenBoxPadding),c.getMinResizeBounds(i,n,t.minDimensions||{width:b,height:b},e)}function R(t,e,n){var i,r;if("string"==typeof n&&(n={direction:n}),i=s({shape:e},n),r=i.direction,!r)throw new Error("must provide a direction (nw|se|ne|sw)");S.activate(t,"resize",{autoActivate:!0,cursor:"resize-"+(/nw|se/.test(r)?"nwse":"nesw"),data:{shape:e,context:i}})}function P(t,e,n){function i(e){l(e)&&R(e,t,n)}u.bind(e.node,"mousedown",i),u.bind(e.node,"touchstart",i)}function D(t,e,n,i,r){var o=t.group().addClass(x).addClass(x+"-"+r),a=-m+f;o.rect(a,a,m,m).addClass(x+"-visual"),o.rect(a,a,y,y).addClass(x+"-hit");var s=(new d.Matrix).translate(e,n).rotate(i,0,0);return o.transform(s),o}function k(t,e,n){var i;i="nw"===n?D(e,0,0,0,n):"ne"===n?D(e,t.width,0,90,n):"se"===n?D(e,t.width,t.height,180,n):D(e,0,t.height,270,n),P(t,i,n)}function B(t){if(A({shape:t})){var n=e.getGraphics(t);k(t,n,"nw"),k(t,n,"ne"),k(t,n,"se"),k(t,n,"sw")}}function O(t){var n=e.getGraphics(t),i=n.selectAll("."+x);r(i,function(t){t.remove()})}var I={create:function(t){var e,n=w.getDefaultLayer(),i=t.shape;e=t.frame=d.create("rect",{"class":"djs-resize-overlay",width:i.width+10,height:i.height+10,x:i.x-5,y:i.y-5}),e.appendTo(n)},update:function(t){var e=t.frame,n=t.newBounds;n.width>5&&e.attr({x:n.x,width:n.width}),n.height>5&&e.attr({y:n.y,height:n.height}),e[t.canExecute?"removeClass":"addClass"](g)},remove:function(t){t.frame&&t.frame.remove()}};t.on("resize.start",function(t){var e=t.context,n=e.shape,i=e.minBounds;void 0===i&&(e.minBounds=M(e)),w.addMarker(n,v),I.create(e)}),t.on("resize.move",function(t){var e,n=t.context,i=n.shape,r=n.direction,o=n.minBounds;e={x:t.dx,y:t.dy},n.delta=e,n.newBounds=c.resizeBounds(i,r,e),o&&(n.newBounds=c.ensureMinBounds(n.newBounds,o)),n.canExecute=A(n),I.update(n)}),t.on("resize.end",function(t){var e=t.context,n=e.shape,r=e.newBounds;r.x=h(r.x),r.y=h(r.y),r.width=h(r.width),r.height=h(r.height),e.canExecute&&i.resizeShape(n,e.newBounds)}),t.on("resize.cleanup",function(t){var e=t.context,n=e.shape;w.removeMarker(n,v),I.remove(e)}),t.on("selection.changed",function(t){var e=t.oldSelection,n=t.newSelection;r(e,O),1===n.length&&r(n,B)}),t.on("shape.changed",function(t){var e=t.element;O(e),_.isSelected(e)&&B(e)}),this.activate=R}var r=t(218),o=t(216),a=t(345),s=t(339),c=t(151),u=t(357),p=t(186),l=t(193).isPrimaryButton,h=Math.round,d=t(200),f=-2,m=5,y=20,v="djs-resizing",g="resize-not-ok",x="djs-resizer",b=10,E=20;i.$inject=["eventBus","elementRegistry","rules","modeling","canvas","selection","dragging"],e.exports=i},{151:151,186:186,193:193,200:200,216:216,218:218,339:339,345:345,357:357}],151:[function(t,e,n){"use strict";function i(t){return{top:t.y,left:t.x,bottom:t.y+t.height,right:t.x+t.width}}var r=Math.max,o=Math.min;e.exports.resizeBounds=function(t,e,n){var i=n.x,r=n.y;switch(e){case"nw":return{x:t.x+i,y:t.y+r,width:t.width-i,height:t.height-r};case"sw":return{x:t.x+i,y:t.y,width:t.width-i,height:t.height+r};case"ne":return{x:t.x,y:t.y+r,width:t.width+i,height:t.height-r};case"se":return{x:t.x,y:t.y,width:t.width+i,height:t.height+r};default:throw new Error("unrecognized direction: "+e)}},e.exports.reattachPoint=function(t,e,n){var i=t.width/e.width,r=t.height/e.height;return{x:Math.round(e.x+e.width/2)-Math.floor((t.x+t.width/2-n.x)/i),y:Math.round(e.y+e.height/2)-Math.floor((t.y+t.height/2-n.y)/r)}},e.exports.ensureMinBounds=function(t,e){var n={x:Math.min(t.x,e.x),y:Math.min(t.y,e.y)},i={x:Math.max(t.x+t.width,e.x+e.width),y:Math.max(t.y+t.height,e.y+e.height)};return{x:n.x,y:n.y,width:i.x-n.x,height:i.y-n.y}},e.exports.getMinResizeBounds=function(t,e,n,a){var s=i(e),c={top:/n/.test(t)?s.bottom-n.height:s.top,left:/w/.test(t)?s.right-n.width:s.left,bottom:/s/.test(t)?s.top+n.height:s.bottom,right:/e/.test(t)?s.left+n.width:s.right},u=a?i(a):c,p={top:o(c.top,u.top),left:o(c.left,u.left),bottom:r(c.bottom,u.bottom),right:r(c.right,u.right)};return{x:p.left,y:p.top,width:p.right-p.left,height:p.bottom-p.top}}},{}],152:[function(t,e,n){e.exports={__depends__:[t(136),t(155),t(106)],__init__:["resize"],resize:["type",t(150)]}},{106:106,136:136,150:150,155:155}],153:[function(t,e,n){"use strict";function i(t){o.call(this,t),this.init()}var r=t(207),o=t(78);i.$inject=["eventBus"],r(i,o),e.exports=i,i.prototype.addRule=function(t,e,n){var i=this;"string"==typeof t&&(t=[t]),t.forEach(function(t){i.canExecute(t,e,function(t,e,i){return n(t)},!0)})}},{207:207,78:78}],154:[function(t,e,n){"use strict";function i(t){this._commandStack=t}i.$inject=["commandStack"],e.exports=i,i.prototype.allowed=function(t,e){var n=this._commandStack.canExecute(t,e);return void 0===n?!0:n}},{}],155:[function(t,e,n){e.exports={__depends__:[t(80)],__init__:["rules"],rules:["type",t(154)]}},{154:154,80:80}],156:[function(t,e,n){"use strict";function i(t){this._eventBus=t,this._selectedElements=[];var e=this;t.on(["shape.remove","connection.remove"],function(t){var n=t.element;e.deselect(n)})}var r=t(330),o=t(218);i.$inject=["eventBus"],e.exports=i,i.prototype.deselect=function(t){var e=this._selectedElements,n=e.indexOf(t);if(-1!==n){var i=e.slice();e.splice(n,1),this._eventBus.fire("selection.changed",{oldSelection:i,newSelection:e})}},i.prototype.get=function(){return this._selectedElements},i.prototype.isSelected=function(t){return-1!==this._selectedElements.indexOf(t)},i.prototype.select=function(t,e){var n=this._selectedElements,i=n.slice();r(t)||(t=t?[t]:[]),e?o(t,function(t){-1===n.indexOf(t)&&n.push(t)}):this._selectedElements=n=t.slice(),this._eventBus.fire("selection.changed",{oldSelection:i,newSelection:n})}},{218:218,330:330}],157:[function(t,e,n){"use strict";function i(t,e,n){t.on("create.end",500,function(t){t.context.canExecute&&e.select(t.context.shape)}),t.on("connect.end",500,function(t){t.context.canExecute&&t.context.target&&e.select(t.context.target)}),t.on("shape.move.end",500,function(t){var n=t.previousSelection||[],i=t.context.shape;-1===n.indexOf(i)&&e.select(i)}),t.on("element.click",function(t){var i=t.element;i===n.getRootElement()&&(i=null);var o=e.isSelected(i),a=e.get().length>1,s=r(t);return o&&a?s?e.deselect(i):e.select(i):void(o?e.deselect(i):e.select(i,s))})}var r=t(193).hasPrimaryModifier;i.$inject=["eventBus","selection","canvas"],e.exports=i},{193:193}],158:[function(t,e,n){"use strict";function i(t,e,n,i,s){function c(t,n){e.addMarker(t,n)}function u(t,n){e.removeMarker(t,n)}this._multiSelectionBox=null,t.on("element.hover",function(t){c(t.element,o)}),t.on("element.out",function(t){u(t.element,o)}),t.on("selection.changed",function(t){function e(t){u(t,a)}function n(t){c(t,a)}var i=t.oldSelection,o=t.newSelection;r(i,function(t){-1===o.indexOf(t)&&e(t)}),r(o,function(t){-1===i.indexOf(t)&&n(t)})})}var r=t(218),o="hover",a="selected";i.$inject=["eventBus","canvas","selection","graphicsFactory","styles"],e.exports=i},{218:218}],159:[function(t,e,n){e.exports={__init__:["selectionVisuals","selectionBehavior"],__depends__:[t(108),t(141)],selection:["type",t(156)],selectionVisuals:["type",t(158)],selectionBehavior:["type",t(157)]}},{108:108,141:141,156:156,157:157,158:158}],160:[function(t,e,n){"use strict";function i(){this._targets={},this._snapOrigins={},this._snapLocations=[],this._defaultSnaps={}}function r(t){this._snapValues={}}var o=t(218),a=t(161).snapTo;i.prototype.getSnapOrigin=function(t){return this._snapOrigins[t]},i.prototype.setSnapOrigin=function(t,e){this._snapOrigins[t]=e,-1===this._snapLocations.indexOf(t)&&this._snapLocations.push(t)},i.prototype.addDefaultSnap=function(t,e){var n=this._defaultSnaps[t];n||(n=this._defaultSnaps[t]=[]),n.push(e)},i.prototype.getSnapLocations=function(){return this._snapLocations},i.prototype.setSnapLocations=function(t){this._snapLocations=t},i.prototype.pointsForTarget=function(t){var e=t.id||t,n=this._targets[e];return n||(n=this._targets[e]=new r,n.initDefaults(this._defaultSnaps)),n},e.exports=i,r.prototype.add=function(t,e){var n=this._snapValues[t];n||(n=this._snapValues[t]={x:[],y:[]}),-1===n.x.indexOf(e.x)&&n.x.push(e.x),-1===n.y.indexOf(e.y)&&n.y.push(e.y)},r.prototype.snap=function(t,e,n,i){var r=this._snapValues[e];return r&&a(t[n],r[n],i)},r.prototype.initDefaults=function(t){var e=this;o(t||{},function(t,n){o(t,function(t){e.add(n,t)})})}},{161:161,218:218}],161:[function(t,e,n){"use strict";function i(t,e,n){n=void 0===n?10:n;var i,r;for(i=0;i<e.length;i++)if(r=e[i],s(r-t)<=n)return r}function r(t){return{x:t.x,y:t.y}}function o(t,e){return!t||isNaN(t.x)||isNaN(t.y)?e:{x:c(t.x+t.width/2),y:c(t.y+t.height/2)}}function a(t){return{x:t.x+t.width,y:t.y+t.height}}var s=Math.abs,c=Math.round;e.exports.snapTo=i,e.exports.topLeft=r,e.exports.mid=o,e.exports.bottomRight=a,e.exports.isSnapped=function(t,e){var n=t.snapped;return n?"string"==typeof e?n[e]:n.x&&n.y:!1},e.exports.setSnapped=function(t,e,n){if("string"!=typeof e)throw new Error("axis must be in [x, y]");if("number"!=typeof n&&n!==!1)throw new Error("value must be Number or false");var i,r=t[e],o=t.snapped=t.snapped||{};return n===!1?o[e]=!1:(o[e]=!0,i=n-r,t[e]+=i,t["d"+e]+=i),r}},{}],162:[function(t,e,n){"use strict";function i(t,e){this._canvas=e;var n=this;t.on(["shape.move.start","create.start"],function(t){n.initSnap(t)}),t.on(["shape.move.move","shape.move.end","create.move","create.end"],function(t){t.originalEvent&&t.originalEvent.ctrlKey||p(t)||n.snap(t)}),t.on(["shape.move.cleanup","create.cleanup"],function(t){n.hide()}),this._asyncHide=a(this.hide,1e3)}var r=t(216),o=t(218),a=t(228),s=t(161).mid,c=t(160),u=t(161),p=u.isSnapped,l=u.setSnapped;i.$inject=["eventBus","canvas"],e.exports=i,i.prototype.initSnap=function(t){var e=t.context,n=e.shape,i=e.snapContext;i||(i=e.snapContext=new c);var r=s(n,t);return i.setSnapOrigin("mid",{x:r.x-t.x,y:r.y-t.y}),i},i.prototype.snap=function(t){var e=t.context,n=e.snapContext,i=e.shape,r=e.target,a=n.getSnapLocations();if(r){var s=n.pointsForTarget(r);s.initialized||(this.addTargetSnaps(s,i,r),s.initialized=!0);var c={x:p(t,"x"),y:p(t,"y")};o(a,function(e){var i=n.getSnapOrigin(e),r={x:t.x+i.x,y:t.y+i.y};return o(["x","y"],function(t){var n;c[t]||(n=s.snap(r,e,t,7),void 0!==n&&(c[t]={value:n,originValue:n-i[t]}))}),c.x&&c.y?!1:void 0}),this.showSnapLine("vertical",c.x&&c.x.value),this.showSnapLine("horizontal",c.y&&c.y.value),o(["x","y"],function(e){var n=c[e];"object"==typeof n&&l(t,e,n.originValue)})}},i.prototype._createLine=function(t){var e=this._canvas.getLayer("snap"),n=e.path("M0,0 L0,0").addClass("djs-snap-line");return{update:function(e){"number"!=typeof e?n.attr({display:"none"}):"horizontal"===t?n.attr({path:"M-100000,"+e+" L+100000,"+e,display:""}):n.attr({path:"M "+e+",-100000 L "+e+", +100000",display:""})}}},i.prototype._createSnapLines=function(){this._snapLines={horizontal:this._createLine("horizontal"),vertical:this._createLine("vertical")}},i.prototype.showSnapLine=function(t,e){var n=this.getSnapLine(t);n&&n.update(e),this._asyncHide()},i.prototype.getSnapLine=function(t){return this._snapLines||this._createSnapLines(),this._snapLines[t]},i.prototype.hide=function(){o(this._snapLines,function(t){t.update()})},i.prototype.addTargetSnaps=function(t,e,n){var i=this.getSiblings(e,n);o(i,function(e){t.add("mid",s(e))})},i.prototype.getSiblings=function(t,e){return e&&r(e.children,function(e){return!e.hidden&&!e.labelTarget&&!e.waypoints&&e.host!==t&&e!==t})}},{160:160,161:161,216:216,218:218,228:228}],163:[function(t,e,n){"use strict";function i(t,e,n,i,p){function l(t){var e={shape:t};return p.allowed("shape.resize",e)}function h(t,n){e.activate(t,"spaceTool.selection",{cursor:"crosshair",autoActivate:n,data:{context:{crosshair:{}}}})}function d(t){e.activate(t,"spaceTool",{autoActivate:!0,cursor:"crosshair",data:{context:{}}})}function f(t,e){var i=s(t.dx)>s(t.dy)?"x":"y",c=t["d"+i],u=t[i]-c,p=[],h=[];return s(c)<5?!1:(a(t)&&(c*=-1),n.forEach(function(t){var e=t[[i]],n=e+t[m[i]];if(t.parent&&!t.waypoints)return c>0&&e>u?p.push(t):0>c&&u>n?p.push(t):u>e&&n>u&&l(t)?h.push(t):void 0}),e.axis=i,e.direction=r.getDirection(i,c),e.movingShapes=p,e.resizingShapes=h,o.set("resize-"+("x"===i?"ew":"ns")),!0)}t.on("spaceTool.selection.end",function(t){setTimeout(function(){d(t.originalEvent)})});var m={x:"width",y:"height"},y={x:"y",y:"x"};t.on("spaceTool.move",u,function(t){var e=t.context;e.initialized||(e.initialized=f(t,e))}),t.on("spaceTool.end",function(t){var e=t.context,n=e.axis,r=e.direction,o=e.movingShapes,a=e.resizingShapes;if(e.initialized){var s={x:c(t.dx),y:c(t.dy)};return s[y[n]]=0,i.createSpace(o,a,s,r)}}),this.activateSelection=h,this.activateMakeSpace=d}var r=t(165),o=t(185),a=t(193).hasPrimaryModifier,s=Math.abs,c=Math.round,u=1500;i.$inject=["eventBus","dragging","elementRegistry","modeling","rules"],e.exports=i},{165:165,185:185,193:193}],164:[function(t,e,n){"use strict";function i(t,e,n,i){function a(t){return e.getGraphics(t)}function s(t,e){var n=a(t),r=n.clone(),o=n.getBBox();r.attr(i.cls("djs-dragger",[],{x:o.x,y:o.y})),e.add(r)}t.on("spaceTool.selection.start",function(t){var e=n.getLayer("space"),r=t.context,o={x:"M 0,-10000 L 0,10000",y:"M -10000,0 L 10000,0"},a=e.group().attr(i.cls("djs-crosshair-group",["no-events"]));
a.path(o.x).addClass("djs-crosshair"),a.path(o.y).addClass("djs-crosshair"),r.crosshairGroup=a}),t.on("spaceTool.selection.move",function(t){var e=t.context.crosshairGroup;e.translate(t.x,t.y)}),t.on("spaceTool.selection.cleanup",function(t){var e=t.context,n=e.crosshairGroup;n&&n.remove()}),t.on("spaceTool.move",function(t){var e=t.context,a=e.line,c=e.axis,u=e.movingShapes;if(e.initialized){if(!e.dragGroup){var p=n.getLayer("space");a=p.path("M0,0 L0,0").addClass("djs-crosshair"),e.line=a;var l=n.getDefaultLayer().group().attr(i.cls("djs-drag-group",["no-events"]));r(u,function(t){s(t,l),n.addMarker(t,o)}),e.dragGroup=l}var h={x:"M"+t.x+", -10000 L"+t.x+", 10000",y:"M -10000, "+t.y+" L 10000, "+t.y};a.attr({path:h[c],display:""});var d={x:"y",y:"x"},f={x:t.dx,y:t.dy};f[d[e.axis]]=0,e.dragGroup.translate(f.x,f.y)}}),t.on("spaceTool.cleanup",function(t){var e=t.context,i=e.movingShapes,a=e.line,s=e.dragGroup;r(i,function(t){n.removeMarker(t,o)}),s&&(a.remove(),s.remove())})}var r=t(218),o="djs-dragging";i.$inject=["eventBus","elementRegistry","canvas","styles"],e.exports=i},{218:218}],165:[function(t,e,n){"use strict";function i(t,e){if("x"===t){if(e>0)return"e";if(0>e)return"w"}if("y"===t){if(e>0)return"s";if(0>e)return"n"}return null}e.exports.getDirection=i,e.exports.resizeBounds=function(t,e,n){var i=n.x,r=n.y;switch(e){case"n":return{x:t.x,y:t.y+r,width:t.width,height:t.height-r};case"s":return{x:t.x,y:t.y,width:t.width,height:t.height+r};case"w":return{x:t.x+i,y:t.y,width:t.width-i,height:t.height};case"e":return{x:t.x,y:t.y,width:t.width+i,height:t.height};default:throw new Error("unrecognized direction: "+e)}}},{}],166:[function(t,e,n){e.exports={__init__:["spaceToolVisuals"],__depends__:[t(106),t(136),t(155)],spaceTool:["type",t(163)],spaceToolVisuals:["type",t(164)]}},{106:106,136:136,155:155,163:163,164:164}],167:[function(t,e,n){"use strict";function i(t){var e=l('<div class="djs-tooltip-container" style="position: absolute; width: 0; height: 0;" />');return t.insertBefore(e,t.firstChild),e}function r(t,e,n){c(t.style,{left:e+"px",top:n+"px"})}function o(t,e){t.style.display=e===!1?"none":""}function a(t,e,n){this._eventBus=e,this._canvas=n,this._ids=y,this._tooltipDefaults={show:{minZoom:.7,maxZoom:5}},this._tooltips={},this._tooltipRoot=i(n.getContainer());var r=this;m.bind(this._tooltipRoot,g,"mousedown",function(t){t.stopPropagation()}),m.bind(this._tooltipRoot,g,"mouseover",function(t){r.trigger("mouseover",t)}),m.bind(this._tooltipRoot,g,"mouseout",function(t){r.trigger("mouseout",t)}),this._init(t)}var s=t(336),c=t(339),u=t(218),p=t(228),l=t(356),h=t(351),d=t(352),f=t(360),m=t(355),y=new(t(190))("tt"),v="djs-tooltip",g="."+v;a.$inject=["config.tooltips","eventBus","canvas"],e.exports=a,a.prototype.add=function(t){if(!t.position)throw new Error("must specifiy tooltip position");if(!t.html)throw new Error("must specifiy tooltip html");var e=this._ids.next();return t=c({},this._tooltipDefaults,t,{id:e}),this._addTooltip(t),t.timeout&&this.setTimeout(t),e},a.prototype.trigger=function(t,e){var n=e.delegateTarget||e.target,i=this.get(h(n,"data-tooltip-id"));i&&("mouseover"===t&&i.timeout&&this.clearTimeout(i),"mouseout"===t&&i.timeout&&(i.timeout=1e3,this.setTimeout(i)))},a.prototype.get=function(t){return"string"!=typeof t&&(t=t.id),this._tooltips[t]},a.prototype.clearTimeout=function(t){if(t=this.get(t)){var e=t.removeTimer;e&&(clearTimeout(e),t.removeTimer=null)}},a.prototype.setTimeout=function(t){if(t=this.get(t)){this.clearTimeout(t);var e=this;t.removeTimer=setTimeout(function(){e.remove(t)},t.timeout)}},a.prototype.remove=function(t){var e=this.get(t);e&&(f(e.html),f(e.htmlContainer),delete e.htmlContainer,delete this._tooltips[e.id])},a.prototype.show=function(){o(this._tooltipRoot)},a.prototype.hide=function(){o(this._tooltipRoot,!1)},a.prototype._updateRoot=function(t){var e=t.scale||1,n=t.scale||1,i="matrix("+e+",0,0,"+n+","+-1*t.x*e+","+-1*t.y*n+")";this._tooltipRoot.style.transform=i,this._tooltipRoot.style["-ms-transform"]=i},a.prototype._addTooltip=function(t){var e,n=t.id,i=t.html,r=this._tooltipRoot;i.get&&(i=i.get(0)),s(i)&&(i=l(i)),e=l('<div data-tooltip-id="'+n+'" class="'+v+'" style="position: absolute">'),e.appendChild(i),t.type&&d(e).add("djs-tooltip-"+t.type),t.className&&d(e).add(t.className),t.htmlContainer=e,r.appendChild(e),this._tooltips[n]=t,this._updateTooltip(t)},a.prototype._updateTooltip=function(t){var e=t.position,n=t.htmlContainer;r(n,e.x,e.y)},a.prototype._updateTooltipVisibilty=function(t){u(this._tooltips,function(e){var n=e.show,i=e.htmlContainer,r=!0;n&&((n.minZoom>t.scale||n.maxZoom<t.scale)&&(r=!1),o(i,r))})},a.prototype._init=function(t){var e=this,n=function(t){e._updateRoot(t),e._updateTooltipVisibilty(t),e.show()};t&&t.deferUpdate===!1||(n=p(n,300)),this._eventBus.on("canvas.viewbox.changed",function(t){e.hide(),n(t.viewbox)})}},{190:190,218:218,228:228,336:336,339:339,351:351,352:352,355:355,356:356,360:360}],168:[function(t,e,n){e.exports={__init__:["tooltips"],tooltips:["type",t(167)]}},{167:167}],169:[function(t,e,n){"use strict";function i(t,e){var n=this;e.on("canvas.init",function(t){n.addBBoxMarker(t.svg)})}i.$inject=["canvas","eventBus"],e.exports=i,i.prototype.addBBoxMarker=function(t){var e={fill:"none","class":"outer-bound-marker"};t.rect(-1e4,-1e4,10,10).attr(e),t.rect(1e4,1e4,10,10).attr(e)}},{}],170:[function(t,e,n){"use strict";function i(){}function r(t,e){try{return e.get(t)}catch(n){return null}}function o(t){function e(t){h.stopEvent(t,!0)}function n(n){s(m,function(n){c.bind(t,n,e,!0)})}function r(n){setTimeout(function(){s(m,function(n){c.unbind(t,n,e,!0)})},500)}c.bind(t,"touchstart",n,!0),c.bind(t,"touchend",r,!0),c.bind(t,"touchcancel",r,!0);var o=new p.Manager(t,{inputClass:p.TouchInput,recognizers:[]}),a=new p.Tap,u=new p.Pan({threshold:10}),l=new p.Press,d=new p.Pinch,f=new p.Tap({event:"doubletap",taps:2});return d.requireFailure(u),d.requireFailure(l),o.add([u,l,d,f,a]),o.reset=function(t){var e=this.recognizers,n=this.session;n.stopped||(i("recognizer","stop"),o.stop(t),setTimeout(function(){var t,r;for(i("recognizer","reset"),t=0;r=e[t];t++)r.reset(),r.state=8;n.curRecognizer=null},0))},o.on("hammer.input",function(t){t.srcEvent.defaultPrevented&&o.reset(!0)}),o}function a(t,e,n,a,s,c){function p(t){return function(e){i("element",t,e),s.fire(t,e)}}function m(t){var e=u(t,"svg, .djs-element",!0);return e&&new l(e)}function y(t){function n(t){function n(t){var n=t.deltaX-o,i=t.deltaY-a;e.scroll({dx:n,dy:i}),o=t.deltaX,a=t.deltaY}function r(t){v.off("panmove",n),v.off("panend",r),v.off("pancancel",r),i("canvas","grab end")}i("canvas","grab start");var o=0,a=0;v.on("panmove",n),v.on("panend",r),v.on("pancancel",r)}function r(t){var r=m(t.target),o=r&&a.get(r);return x&&e.getRootElement()!==o?(i("element","move start",o,t,!0),x.start(t,o,!0)):void n(t)}function s(t){function n(t){var n=1-(1-t.scale)/1.5,i=Math.max(d,Math.min(f,n*o));e.zoom(i,a),h.stopEvent(t,!0)}function r(t){v.off("pinchmove",n),v.off("pinchend",r),v.off("pinchcancel",r),v.reset(!0),i("canvas","zoom end")}i("canvas","zoom start");var o=e.zoom(),a=t.center;v.on("pinchmove",n),v.on("pinchend",r),v.on("pinchcancel",r)}v=o(t),v.on("doubletap",p("element.dblclick")),v.on("tap",p("element.click")),v.on("panstart",r),v.on("press",r),v.on("pinchstart",s)}var v,g=r("dragging",t),x=r("move",t),b=r("contextPad",t),E=r("palette",t);g&&n.on("drag.move",function(t){var e=h.toPoint(t.originalEvent),n=document.elementFromPoint(e.x,e.y),i=m(n),r=i&&a.get(i);r!==t.hover&&(t.hover&&g.out(t),r&&(g.hover({element:r,gfx:i}),t.hover=r,t.hoverGfx=i))}),b&&n.on("contextPad.create",function(t){var e=t.pad.html,n=o(e);n.on("panstart",function(t){i("context-pad","panstart",t),b.trigger("dragstart",t,!0)}),n.on("press",function(t){i("context-pad","press",t),b.trigger("dragstart",t,!0)}),n.on("tap",function(t){i("context-pad","tap",t),b.trigger("click",t)})}),E&&n.on("palette.create",function(t){var e=t.html,n=o(e);n.on("panstart",function(t){i("palette","panstart",t),E.trigger("dragstart",t,!0)}),n.on("press",function(t){i("palette","press",t),E.trigger("dragstart",t,!0)}),n.on("tap",function(t){i("palette","tap",t),E.trigger("click",t)})}),n.on("canvas.init",function(t){y(t.svg.node)})}var s=t(218),c=t(357),u=t(354),p=t(198),l=t(200),h=t(187),d=.2,f=4,m=["mousedown","mouseup","mouseover","mouseout","click","dblclick"];a.$inject=["injector","canvas","eventBus","elementRegistry","interactionEvents","touchFix"],e.exports=a},{187:187,198:198,200:200,218:218,354:354,357:357}],171:[function(t,e,n){e.exports={__depends__:[t(108)],__init__:["touchInteractionEvents"],touchInteractionEvents:["type",t(170)],touchFix:["type",t(169)]}},{108:108,169:169,170:170}],172:[function(t,e,n){"use strict";function i(){}var r=t(174).getMid;e.exports=i,i.prototype.layoutConnection=function(t,e){return[r(t.source),r(t.target)]}},{174:174}],173:[function(t,e,n){"use strict";function i(t){return o({original:t.point.original||t.point},t.actual)}function r(t,e){this._elementRegistry=t,this._renderer=e}var o=t(339),a=t(174);r.$inject=["elementRegistry","renderer"],e.exports=r,r.prototype.getCroppedWaypoints=function(t,e,n){e=e||t.source,n=n||t.target;var r=this.getDockingPoint(t,e,!0),o=this.getDockingPoint(t,n),a=t.waypoints.slice(r.idx+1,o.idx);return a.unshift(i(r)),a.push(i(o)),a},r.prototype.getDockingPoint=function(t,e,n){var i,r,o,a=t.waypoints;return i=n?0:a.length-1,r=a[i],o=this._getIntersection(e,t,n),{point:r,actual:o||r,idx:i}},r.prototype._getIntersection=function(t,e,n){var i=this._getShapePath(t),r=this._getConnectionPath(e);return a.getElementLineIntersection(i,r,n)},r.prototype._getConnectionPath=function(t){return this._renderer.getConnectionPath(t)},r.prototype._getShapePath=function(t){return this._renderer.getShapePath(t)},r.prototype._getGfx=function(t){return this._elementRegistry.getGraphics(t)}},{174:174,339:339}],174:[function(t,e,n){"use strict";function i(t,e){return Math.abs(t.x-e.x)<=v?"h":Math.abs(t.y-e.y)<=v?"v":!1}function r(t){return{x:Math.round(t.x),y:Math.round(t.y)}}function o(t,e){return t.x===e.x&&t.y===e.y}function a(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function s(t){return{top:t.y,right:t.x+(t.width||0),bottom:t.y+(t.height||0),left:t.x}}function c(t){return r({x:t.x+(t.width||0)/2,y:t.y+(t.height||0)/2})}function u(t,e,n){n=n||0,f(n)||(n={x:n,y:n});var i=s(t),r=s(e),o=i.bottom+n.y<=r.top,a=i.left-n.x>=r.right,c=i.top-n.y>=r.bottom,u=i.right+n.x<=r.left,p=o?"top":c?"bottom":null,l=u?"left":a?"right":null;return l&&p?p+"-"+l:l||p||"intersect"}function p(t,e,n,i){d(n)&&(i=n,n=0);var r=u(t,e,n);return-1!==i.indexOf(r)}function l(t,e,n){var i=h(t,e);return 1===i.length?r(i[0]):2===i.length&&a(i[0],i[1])<1?r(i[0]):i.length>1?(i=m(i,function(t){var e=Math.floor(100*t.t2)||1;return e=100-e,e=(10>e?"0":"")+e,t.segment2+"#"+e}),r(i[n?0:i.length-1])):null}function h(t,e){return y.path.intersection(t,e)}var d=t(330),f=t(334),m=t(225),y=t(200),v=2;e.exports.pointsAligned=i,e.exports.roundPoint=r,e.exports.pointsEqual=o,e.exports.pointDistance=a,e.exports.asTRBL=s,e.exports.getMid=c,e.exports.getOrientation=u,e.exports.hasAnyOrientation=p,e.exports.getElementLineIntersection=l,e.exports.getIntersections=h},{200:200,225:225,330:330,334:334}],175:[function(t,e,n){"use strict";function i(t,e,n){return t>=e&&n>=t}function r(t,e){switch(t){case"intersect":return null;case"top":case"bottom":return"v:v";case"left":case"right":return"h:h";default:return e}}var o=t(330),a=t(217),s=t(212),c=t(339),u=t(174),p=t(188),l=u.getOrientation,h=u.getMid,d=u.pointsAligned,f=p.pointInRect,m=p.pointDistance,y=20,v={"h:h":20,"v:v":20,"h:v":-10,"v:h":-10};e.exports.getBendpoints=function(t,e,n){n=n||"h:h";var i,r;if("h:v"===n)return[{x:e.x,y:t.y}];if("v:h"===n)return[{x:t.x,y:e.y}];if("h:h"===n)return i=Math.round((e.x-t.x)/2+t.x),[{x:i,y:t.y},{x:i,y:e.y}];if("v:v"===n)return r=Math.round((e.y-t.y)/2+t.y),[{x:t.x,y:r},{x:e.x,y:r}];throw new Error("unknown directions: <"+n+">: directions must be specified as {a direction}:{b direction} (direction in h|v)")},e.exports.connectPoints=function(t,e,n){var i=[];return d(t,e)||(i=this.getBendpoints(t,e,n)),i.unshift(t),i.push(e),i},e.exports.connectRectangles=function(t,e,n,i,o){var a=o&&o.preferredLayouts||[],c=s(a,"straight")[0]||"h:h",u=v[c]||0,p=l(t,e,u),d=r(p,c);if(n=n||h(t),i=i||h(e),d){if("h:h"===d)switch(p){case"top-right":case"right":case"bottom-right":n={original:n,x:t.x,y:n.y},i={original:i,x:e.x+e.width,y:i.y};break;case"top-left":case"left":case"bottom-left":n={original:n,x:t.x+t.width,y:n.y},i={original:i,x:e.x,y:i.y}}if("v:v"===d)switch(p){case"top-left":case"top":case"top-right":n={original:n,x:n.x,y:t.y+t.height},i={original:i,x:i.x,y:e.y};break;case"bottom-left":case"bottom":case"bottom-right":n={original:n,x:n.x,y:t.y},i={original:i,x:i.x,y:e.y+e.height}}return this.connectPoints(n,i,d)}},e.exports.repairConnection=function(t,e,n,i,r,a){o(n)&&(r=n,a=i,n=h(t),i=h(e)),a=c({preferredLayouts:[]},a);var s,u=a.preferredLayouts,p=-1!==u.indexOf("straight");return!r||r.length<3?p&&(s=this.layoutStraight(t,e,n,i,a)):a.endChanged?(s=this._repairConnectionSide(e,t,i,r.slice().reverse()),s=s&&s.reverse()):s=a.startChanged?this._repairConnectionSide(t,e,n,r):r,s||(s=this.connectRectangles(t,e,n,i,a)),s};var g=Math.max,x=Math.min;e.exports.layoutStraight=function(t,e,n,r,o){var a,s,c,u,p,l;return a=g(t.x+10,e.x+10),s=x(t.x+t.width-10,e.x+e.width-10),s>a&&(t.width===e.width&&(o.endChanged&&i(r.x,a,s)?c=r.x:i(n.x,a,s)&&(c=n.x)),void 0===c&&(c=t.width<e.width&&i(n.x,a,s)?n.x:t.width>e.width&&i(r.x,a,s)?r.x:(a+s)/2)),u=g(t.y+10,e.y+10),p=x(t.y+t.height-10,e.y+e.height-10),p>u&&(t.height===e.height&&(o.endChanged&&i(r.y,u,p)?l=r.y:i(n.y,u,p)&&(l=n.y)),void 0===l&&(l=t.height<=e.height&&i(n.y,u,p)?n.y:e.height<=t.height&&i(r.y,u,p)?r.y:(u+p)/2)),void 0===c&&void 0===l?null:[{x:void 0!==c?c:n.x,y:void 0!==l?l:n.y},{x:void 0!==c?c:r.x,y:void 0!==l?l:r.y}]},e.exports._repairConnectionSide=function(t,e,n,i){function r(t,e,n){return n.length<3?!0:n.length>4?!1:!!a(n,function(t,e){var i=n[e-1];return i&&m(t,i)<3})}function o(t,e,n){var i=d(e,t);switch(i){case"v":return{x:t.x,y:n.y};case"h":return{x:n.x,y:t.y}}return{x:t.x,y:t.y}}function s(t,e,n){var i;for(i=t.length-2;0!==i;i--)if(f(t[i],e,y)||f(t[i],n,y))return t.slice(i);return t}if(r(t,e,i))return null;var c,u=i[0],p=i.slice();return p[0]=n,p[1]=o(p[1],u,n),c=s(p,t,e),c!==p?this._repairConnectionSide(t,e,n,c):p}},{174:174,188:188,212:212,217:217,330:330,339:339}],176:[function(t,e,n){"use strict";function i(){Object.defineProperty(this,"businessObject",{writable:!0}),l.bind(this,"parent"),h.bind(this,"label"),f.bind(this,"outgoing"),m.bind(this,"incoming")}function r(){i.call(this),l.bind(this,"children"),d.bind(this,"host"),d.bind(this,"attachers")}function o(){r.call(this)}function a(){r.call(this),h.bind(this,"labelTarget")}function s(){i.call(this),f.bind(this,"source"),m.bind(this,"target")}var c=t(339),u=t(207),p=t(369),l=new p({name:"children",enumerable:!0,collection:!0},{name:"parent"}),h=new p({name:"label",enumerable:!0},{name:"labelTarget"}),d=new p({name:"attachers",collection:!0},{name:"host"}),f=new p({name:"outgoing",collection:!0},{name:"source"}),m=new p({name:"incoming",collection:!0},{name:"target"});u(r,i),u(o,r),u(a,r),u(s,i);var y={connection:s,shape:r,label:a,root:o};e.exports.create=function(t,e){var n=y[t];if(!n)throw new Error("unknown type: <"+t+">");return c(new n,e)},e.exports.Base=i,e.exports.Root=o,e.exports.Shape=r,e.exports.Connection=s,e.exports.Label=a},{207:207,339:339,369:369}],177:[function(t,e,n){"use strict";function i(t,e){return{x:t.x-e.x,y:t.y-e.y}}function r(t){return Math.sqrt(Math.pow(t.x,2)+Math.pow(t.y,2))}function o(t,e){function n(t){var n=h.start,o=u.toPoint(t),c=i(o,n);if(!h.dragging&&r(c)>p&&(h.dragging=!0,s.install(),a.set("move")),h.dragging){var l=h.last||h.start;c=i(o,l),e.scroll({dx:c.x,dy:c.y}),h.last=o}t.preventDefault()}function o(t){c.unbind(document,"mousemove",n),c.unbind(document,"mouseup",o),h=null,a.unset(),u.stopEvent(t)}function l(t){t.button||t.ctrlKey||t.shiftKey||t.altKey||(h={start:u.toPoint(t)},c.bind(document,"mousemove",n),c.bind(document,"mouseup",o),u.stopEvent(t))}var h,d=e._container;c.bind(d,"mousedown",l)}var a=t(185),s=t(183),c=t(357),u=t(187),p=15;o.$inject=["eventBus","canvas"],e.exports=o},{183:183,185:185,187:187,357:357}],178:[function(t,e,n){e.exports={__init__:["moveCanvas"],moveCanvas:["type",t(177)]}},{177:177}],179:[function(t,e,n){e.exports={__depends__:[t(171)]}},{171:171}],180:[function(t,e,n){"use strict";function i(t,e){this._canvas=e;var n=this;t.on("canvas.init",function(t){n._init(e._container)})}var r=t(357),o=t(193).hasPrimaryModifier,a=t(193).hasSecondaryModifier,s=t(194).isMac,c=t(181).getStepRange,u=t(181).cap,p=t(192).log10,l={min:.2,max:4},h=10;i.prototype.scroll=function(t){this._canvas.scroll(t)},i.prototype.reset=function(){this._canvas.zoom("fit-viewport")},i.prototype.zoom=function(t,e){var n=this._canvas,i=n.zoom(),r=Math.pow(1+Math.abs(t),t>0?1:-1);n.zoom(u(l,i*r),e)},i.prototype.stepZoom=function(t,e){var n=this._canvas,i=c(l,h);t=t>0?1:-1;var r=p(n.zoom()),o=Math.round(r/i)*i;o+=i*t;var a=Math.pow(10,o);n.zoom(u(l,a),e)},i.prototype._init=function(t){var e=this;r.bind(t,"wheel",function(n){n.preventDefault();var i,r=o(n),c=a(n);if(r||c){i=s?0===n.deltaMode?1.25:50:0===n.deltaMode?.025:.5;var u={};c?u.dx=i*(n.deltaX||n.deltaY):u.dy=i*n.deltaY,e.scroll(u)}else{i=0===n.deltaMode?.025:.5;var p=t.getBoundingClientRect(),l={x:n.clientX-p.left,y:n.clientY-p.top};e.zoom(n.deltaY*i/-5,l)}})},i.$inject=["eventBus","canvas"],e.exports=i},{181:181,192:192,193:193,194:194,357:357}],181:[function(t,e,n){"use strict";var i=t(192).log10;e.exports.getStepRange=function(t,e){var n=i(t.min),r=i(t.max),o=Math.abs(n)+Math.abs(r);return o/e},e.exports.cap=function(t,e){return Math.max(t.min,Math.min(t.max,e))}},{192:192}],182:[function(t,e,n){e.exports={__init__:["zoomScroll"],zoomScroll:["type",t(180)]}},{180:180}],183:[function(t,e,n){"use strict";function i(t){s(t),r(!1)}function r(t){a[t?"bind":"unbind"](document.body,"click",i,!0)}function o(){return r(!0),function(){r(!1)}}var a=t(357),s=t(187).stopEvent;e.exports.install=o},{187:187,357:357}],184:[function(t,e,n){"use strict";e.exports.remove=function(t,e){if(t&&e){var n=t.indexOf(e);if(-1!==n)return t.splice(n,1),e}},e.exports.add=function(t,e,n){if(t&&e){isNaN(n)&&(n=-1);var i=t.indexOf(e);if(-1!==i){if(i===n)return;if(-1===n)return;t.splice(i,1)}-1!==n?t.splice(n,0,e):t.push(e)}},e.exports.indexOf=function(t,e){return t&&e?t.indexOf(e):-1}},{}],185:[function(t,e,n){"use strict";var i=t(352),r=/^djs-cursor-.*$/;e.exports.set=function(t){var e=i(document.body);e.removeMatching(r),t&&e.add("djs-cursor-"+t)},e.exports.unset=function(){this.set(null)}},{352:352}],186:[function(t,e,n){"use strict";function i(t,e,n){var i=!n||-1===t.indexOf(e);return i&&t.push(e),i}function r(t,e,n){n=n||0,f(t,function(t,i){var o=e(t,i,n);l(o)&&o.length&&r(o,e,n+1)})}function o(t,e,n){var o=[],a=[];return r(t,function(t,r,s){i(o,t,e);var c=t.children;return(-1===n||n>s)&&c&&i(a,c,e)?c:void 0}),o}function a(t,e){return o(t,!e,1)}function s(t,e){return o(t,!e,-1)}function c(t){function e(t){i[t.source.id]&&i[t.target.id]&&(i[t.id]=t),o[t.source.id]&&o[t.target.id]&&(c[t.id]=s[t.id]=t),a[t.id]=t}function n(t){return s[t.id]=t,t.waypoints?void(c[t.id]=a[t.id]=t):(o[t.id]=t,f(t.incoming,e),f(t.outgoing,e),t.children)}var i=d(t,function(t){return t.id}),o={},a={},s={},c={};return r(t,n),{allShapes:o,allConnections:a,topLevel:i,enclosedConnections:c,enclosedElements:s}}function u(t,e){e=!!e,l(t)||(t=[t]);var n,i,r,o;return f(t,function(t){var a=t;t.waypoints&&!e&&(a=u(t.waypoints,!0));var s=a.x,c=a.y,p=a.height||0,l=a.width||0;(n>s||void 0===n)&&(n=s),(i>c||void 0===i)&&(i=c),(s+l>r||void 0===r)&&(r=s+l),(c+p>o||void 0===o)&&(o=c+p)}),{x:n,y:i,height:o-i,width:r-n}}function p(t,e){var n={};return f(t,function(t){var i=t;i.waypoints&&(i=u(i)),!h(e.y)&&i.x>e.x&&(n[t.id]=t),!h(e.x)&&i.y>e.y&&(n[t.id]=t),i.x>e.x&&i.y>e.y&&(h(e.width)&&h(e.height)&&i.width+i.x<e.width+e.x&&i.height+i.y<e.height+e.y?n[t.id]=t:h(e.width)&&h(e.height)||(n[t.id]=t))}),n}var l=t(330),h=t(333),d=t(219),f=t(218);e.exports.eachElement=r,e.exports.selfAndDirectChildren=a,e.exports.selfAndAllChildren=s,e.exports.getBBox=u,e.exports.getEnclosedElements=p,e.exports.getClosure=c},{218:218,219:219,330:330,333:333}],187:[function(t,e,n){"use strict";function i(t){return t&&t.preventDefault()}function r(t,e){t&&(t.stopPropagation&&t.stopPropagation(),e&&t.stopImmediatePropagation&&t.stopImmediatePropagation())}function o(t){return t.originalEvent||t.srcEvent}function a(t,e){c(t,e),s(t)}function s(t){i(t),i(o(t))}function c(t,e){r(t,e),r(o(t),e)}function u(t){return t.pointers&&t.pointers.length&&(t=t.pointers[0]),t.touches&&t.touches.length&&(t=t.touches[0]),t?{x:t.clientX,y:t.clientY}:null}e.exports.getOriginal=o,e.exports.stopEvent=a,e.exports.preventDefault=s,e.exports.stopPropagation=c,e.exports.toPoint=u},{}],188:[function(t,e,n){"use strict";var i=e.exports.pointDistance=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))};e.exports.pointsOnLine=function(t,e,n){if(!t||!e||!n)return!1;var r=(e.x-t.x)*(n.y-t.y)-(e.y-t.y)*(n.x-t.x),o=i(t,e);return Math.abs(r/o)<5},e.exports.pointInRect=function(t,e,n){return n=n||0,t.x>e.x-n&&t.y>e.y-n&&t.x<e.x+e.width+n&&t.y<e.y+e.height+n}},{}],189:[function(t,e,n){"use strict";function i(t){return t.select(".djs-visual")}function r(t){return t.parent().children()[1]}function o(t){return i(t).select("*").getBBox()}e.exports.getVisual=i,e.exports.getChildren=r,e.exports.getBBox=o},{}],190:[function(t,e,n){"use strict";function i(t){this._counter=0,this._prefix=(t?t+"-":"")+Math.floor(1e9*Math.random())+"-"}e.exports=i,i.prototype.next=function(){return this._prefix+ ++this._counter}},{}],191:[function(t,e,n){"use strict";function i(t,e){var n=t.x,i=t.y;return[["M",n,i],["m",0,-e],["a",e,e,0,1,1,0,2*e],["a",e,e,0,1,1,0,-2*e],["z"]]}function r(t){var e=[];return t.forEach(function(t,n){e.push([0===n?"M":"L",t.x,t.y])}),e}function o(t,e){var n,i;for(n=0;i=t[n];n++)if(s(i,e)<=l)return{point:t[n],bendpoint:!0,index:n};return null}function a(t,e){var n,o=c.path.intersection(i(e,l),r(t)),a=o[0],s=o[o.length-1];return a?a!==s?a.segment2!==s.segment2?(n=p(a.segment2,s.segment2)-1,{point:t[n],bendpoint:!0,index:n}):{point:{x:u(a.x+s.x)/2,y:u(a.y+s.y)/2},index:a.segment2}:{point:{x:u(a.x),y:u(a.y)},index:a.segment2}:null}var s=t(188).pointDistance,c=t(200),u=Math.round,p=Math.max,l=10;e.exports.getApproxIntersection=function(t,e){return o(t,e)||a(t,e)}},{188:188,200:200}],192:[function(t,e,n){"use strict";function i(t){return Math.log(t)/Math.log(10)}e.exports.log10=i},{}],193:[function(t,e,n){"use strict";function i(t){return!(r(t)||t).button}var r=t(187).getOriginal,o=t(194).isMac;e.exports.isPrimaryButton=i,e.exports.isMac=o,e.exports.hasPrimaryModifier=function(t){var e=r(t)||t;return i(t)?o()?e.altKey:e.ctrlKey:!1},e.exports.hasSecondaryModifier=function(t){var e=r(t)||t;return i(t)&&e.shiftKey}},{187:187,194:194}],194:[function(t,e,n){"use strict";e.exports.isMac=function(){return/mac/i.test(navigator.platform)}},{}],195:[function(t,e,n){"use strict";e.exports.saveClear=function(t,e){if("function"!=typeof e)throw new Error("removeFn iterator must be a function");if(t){for(var n;n=t[0];)e(n);return t}}},{}],196:[function(t,e,n){"use strict";function i(t){var e=t.split("-");return{horizontal:e[0]||"center",vertical:e[1]||"top"}}function r(t){return l(t)?h({top:0,left:0,right:0,bottom:0},t):{top:t,left:t,right:t,bottom:t}}function o(t,e){return e.textContent=t,d(e.getBBox(),["width","height"])}function a(t,e,n){for(var i,r=t.shift(),a=r;;){if(i=o(a,n),i.width=a?i.width:0," "===a||""===a||i.width<Math.round(e)||a.length<4)return s(t,a,r,i);a=u(a,i.width,e)}}function s(t,e,n,i){if(e.length<n.length){var r=t[0]||"",o=n.slice(e.length).trim();r=/-\s*$/.test(o)?o.replace(/-\s*$/,"")+r.replace(/^\s+/,""):o+" "+r,t[0]=r}return{width:i.width,height:i.height,text:e}}function c(t,e){var n,i=t.split(/(\s|-)/g),r=[],o=0;if(i.length>1)for(;n=i.shift();){if(!(n.length+o<e)){"-"===n&&r.pop();break}r.push(n),o+=n.length}return r.join("")}function u(t,e,n){var i=Math.max(t.length*(n/e),1),r=c(t,i);return r||(r=t.slice(0,Math.max(Math.round(i-1),1))),r}function p(t){this._config=h({},{size:x,padding:g,style:{},align:"center-top"},t||{})}var l=t(334),h=t(339),d=t(345),f=t(218),m=t(222),y=t(342),v=t(200),g=0,x={width:150,height:50};p.prototype.createText=function(t,e,n){for(var o=y({},this._config.size,n.box||{}),s=y({},this._config.style,n.style||{}),c=i(n.align||this._config.align),u=r(void 0!==n.padding?n.padding:this._config.padding),p=e.split(/\r?\n/g),l=[],h=o.width-u.left-u.right,d=t.paper.text(0,0,"").attr(s).node;p.length;)l.push(a(p,h,d));var g,x,b=m(l,function(t,e,n){return t+e.height},0);switch(c.vertical){case"middle":g=(o.height-b)/2-l[0].height/4;break;default:g=u.top}var E=t.text().attr(s);return f(l,function(t){switch(g+=t.height,c.horizontal){case"left":x=u.left;break;case"right":x=h-u.right-t.width;break;default:x=Math.max((h-t.width)/2+u.left,0)}var e=v.create("tspan",{x:x,y:g}).node;e.textContent=t.text,E.append(e)}),d.parentNode.removeChild(d),E},e.exports=p},{200:200,218:218,222:222,334:334,339:339,342:342,345:345}],197:[function(e,n,i){!function(e){var i,r,o="0.4.2",a="hasOwnProperty",s=/[\.\/]/,c=/\s*,\s*/,u="*",p=function(t,e){return t-e},l={n:{}},h=function(){for(var t=0,e=this.length;e>t;t++)if("undefined"!=typeof this[t])return this[t]},d=function(){for(var t=this.length;--t;)if("undefined"!=typeof this[t])return this[t]},f=function(t,e){t=String(t);var n,o=r,a=Array.prototype.slice.call(arguments,2),s=f.listeners(t),c=0,u=[],l={},m=[],y=i;m.firstDefined=h,m.lastDefined=d,i=t,r=0;for(var v=0,g=s.length;g>v;v++)"zIndex"in s[v]&&(u.push(s[v].zIndex),s[v].zIndex<0&&(l[s[v].zIndex]=s[v]));for(u.sort(p);u[c]<0;)if(n=l[u[c++]],m.push(n.apply(e,a)),r)return r=o,m;for(v=0;g>v;v++)if(n=s[v],"zIndex"in n)if(n.zIndex==u[c]){if(m.push(n.apply(e,a)),r)break;do if(c++,n=l[u[c]],n&&m.push(n.apply(e,a)),r)break;while(n)}else l[n.zIndex]=n;else if(m.push(n.apply(e,a)),r)break;return r=o,i=y,m};f._events=l,f.listeners=function(t){var e,n,i,r,o,a,c,p,h=t.split(s),d=l,f=[d],m=[];for(r=0,o=h.length;o>r;r++){for(p=[],a=0,c=f.length;c>a;a++)for(d=f[a].n,n=[d[h[r]],d[u]],i=2;i--;)e=n[i],e&&(p.push(e),m=m.concat(e.f||[]));f=p}return m},f.on=function(t,e){if(t=String(t),"function"!=typeof e)return function(){};for(var n=t.split(c),i=0,r=n.length;r>i;i++)!function(t){for(var n,i=t.split(s),r=l,o=0,a=i.length;a>o;o++)r=r.n,r=r.hasOwnProperty(i[o])&&r[i[o]]||(r[i[o]]={n:{}});for(r.f=r.f||[],o=0,a=r.f.length;a>o;o++)if(r.f[o]==e){n=!0;break}!n&&r.f.push(e)}(n[i]);return function(t){+t==+t&&(e.zIndex=+t)}},f.f=function(t){var e=[].slice.call(arguments,1);return function(){f.apply(null,[t,null].concat(e).concat([].slice.call(arguments,0)))}},f.stop=function(){r=1},f.nt=function(t){return t?new RegExp("(?:\\.|\\/|^)"+t+"(?:\\.|\\/|$)").test(i):i},f.nts=function(){return i.split(s)},f.off=f.unbind=function(t,e){if(!t)return void(f._events=l={n:{}});var n=t.split(c);if(n.length>1)for(var i=0,r=n.length;r>i;i++)f.off(n[i],e);else{n=t.split(s);var o,p,h,i,r,d,m,y=[l];for(i=0,r=n.length;r>i;i++)for(d=0;d<y.length;d+=h.length-2){if(h=[d,1],o=y[d].n,n[i]!=u)o[n[i]]&&h.push(o[n[i]]);else for(p in o)o[a](p)&&h.push(o[p]);y.splice.apply(y,h)}for(i=0,r=y.length;r>i;i++)for(o=y[i];o.n;){if(e){if(o.f){for(d=0,m=o.f.length;m>d;d++)if(o.f[d]==e){o.f.splice(d,1);break}!o.f.length&&delete o.f}for(p in o.n)if(o.n[a](p)&&o.n[p].f){var v=o.n[p].f;for(d=0,m=v.length;m>d;d++)if(v[d]==e){v.splice(d,1);break}!v.length&&delete o.n[p].f}}else{delete o.f;for(p in o.n)o.n[a](p)&&o.n[p].f&&delete o.n[p].f}o=o.n}}},f.once=function(t,e){var n=function(){return f.unbind(t,n),e.apply(this,arguments)};return f.on(t,n)},f.version=o,f.toString=function(){return"You are running Eve "+o},"undefined"!=typeof n&&n.exports?n.exports=f:"function"==typeof t&&t.amd?t("eve",[],function(){return f}):e.eve=f}(this)},{}],198:[function(e,n,i){!function(e,i,r,o){"use strict";function a(t,e,n){return setTimeout(h(t,n),e)}function s(t,e,n){return Array.isArray(t)?(c(t,n[e],n),!0):!1}function c(t,e,n){var i;if(t)if(t.forEach)t.forEach(e,n);else if(t.length!==o)for(i=0;i<t.length;)e.call(n,t[i],i,t),i++;else for(i in t)t.hasOwnProperty(i)&&e.call(n,t[i],i,t)}function u(t,e,n){for(var i=Object.keys(e),r=0;r<i.length;)(!n||n&&t[i[r]]===o)&&(t[i[r]]=e[i[r]]),r++;return t}function p(t,e){return u(t,e,!0)}function l(t,e,n){var i,r=e.prototype;i=t.prototype=Object.create(r),i.constructor=t,i._super=r,n&&u(i,n)}function h(t,e){return function(){return t.apply(e,arguments)}}function d(t,e){return typeof t==ht?t.apply(e?e[0]||o:o,e):t}function f(t,e){return t===o?e:t}function m(t,e,n){c(x(e),function(e){t.addEventListener(e,n,!1)})}function y(t,e,n){c(x(e),function(e){t.removeEventListener(e,n,!1)})}function v(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function g(t,e){return t.indexOf(e)>-1}function x(t){return t.trim().split(/\s+/g)}function b(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var i=0;i<t.length;){if(n&&t[i][n]==e||!n&&t[i]===e)return i;i++}return-1}function E(t){return Array.prototype.slice.call(t,0)}function w(t,e,n){for(var i=[],r=[],o=0;o<t.length;){var a=e?t[o][e]:t[o];b(r,a)<0&&i.push(t[o]),r[o]=a,o++}return n&&(i=e?i.sort(function(t,n){return t[e]>n[e]}):i.sort()),i}function _(t,e){for(var n,i,r=e[0].toUpperCase()+e.slice(1),a=0;a<pt.length;){if(n=pt[a],i=n?n+r:e,i in t)return i;a++}return o}function S(){return yt++}function A(t){var e=t.ownerDocument;return e.defaultView||e.parentWindow}function T(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){d(t.options.enable,[t])&&n.handler(e)},this.init()}function C(t){var e,n=t.options.inputClass;return new(e=n?n:xt?V:bt?W:gt?q:z)(t,N)}function N(t,e,n){var i=n.pointers.length,r=n.changedPointers.length,o=e&Tt&&i-r===0,a=e&(Nt|Mt)&&i-r===0;n.isFirst=!!o,n.isFinal=!!a,o&&(t.session={}),n.eventType=e,M(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function M(t,e){var n=t.session,i=e.pointers,r=i.length;n.firstInput||(n.firstInput=D(e)),r>1&&!n.firstMultiple?n.firstMultiple=D(e):1===r&&(n.firstMultiple=!1);var o=n.firstInput,a=n.firstMultiple,s=a?a.center:o.center,c=e.center=k(i);e.timeStamp=mt(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=L(s,c),e.distance=I(s,c),R(n,e),e.offsetDirection=O(e.deltaX,e.deltaY),e.scale=a?F(a.pointers,i):1,e.rotation=a?j(a.pointers,i):0,P(n,e);var u=t.element;v(e.srcEvent.target,u)&&(u=e.srcEvent.target),e.target=u}function R(t,e){var n=e.center,i=t.offsetDelta||{},r=t.prevDelta||{},o=t.prevInput||{};(e.eventType===Tt||o.eventType===Nt)&&(r=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},i=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=r.x+(n.x-i.x),e.deltaY=r.y+(n.y-i.y)}function P(t,e){var n,i,r,a,s=t.lastInterval||e,c=e.timeStamp-s.timeStamp;if(e.eventType!=Mt&&(c>At||s.velocity===o)){var u=s.deltaX-e.deltaX,p=s.deltaY-e.deltaY,l=B(c,u,p);i=l.x,r=l.y,n=ft(l.x)>ft(l.y)?l.x:l.y,a=O(u,p),t.lastInterval=e}else n=s.velocity,i=s.velocityX,r=s.velocityY,a=s.direction;e.velocity=n,e.velocityX=i,e.velocityY=r,e.direction=a}function D(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:dt(t.pointers[n].clientX),clientY:dt(t.pointers[n].clientY)},n++;return{timeStamp:mt(),pointers:e,center:k(e),deltaX:t.deltaX,deltaY:t.deltaY}}function k(t){var e=t.length;if(1===e)return{x:dt(t[0].clientX),y:dt(t[0].clientY)};for(var n=0,i=0,r=0;e>r;)n+=t[r].clientX,i+=t[r].clientY,r++;return{x:dt(n/e),y:dt(i/e)}}function B(t,e,n){return{x:e/t||0,y:n/t||0}}function O(t,e){return t===e?Rt:ft(t)>=ft(e)?t>0?Pt:Dt:e>0?kt:Bt}function I(t,e,n){n||(n=jt);
var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return Math.sqrt(i*i+r*r)}function L(t,e,n){n||(n=jt);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return 180*Math.atan2(r,i)/Math.PI}function j(t,e){return L(e[1],e[0],Ft)-L(t[1],t[0],Ft)}function F(t,e){return I(e[0],e[1],Ft)/I(t[0],t[1],Ft)}function z(){this.evEl=Vt,this.evWin=$t,this.allow=!0,this.pressed=!1,T.apply(this,arguments)}function V(){this.evEl=Ut,this.evWin=qt,T.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}function $(){this.evTarget=Yt,this.evWin=Xt,this.started=!1,T.apply(this,arguments)}function G(t,e){var n=E(t.touches),i=E(t.changedTouches);return e&(Nt|Mt)&&(n=w(n.concat(i),"identifier",!0)),[n,i]}function W(){this.evTarget=Zt,this.targetIds={},T.apply(this,arguments)}function U(t,e){var n=E(t.touches),i=this.targetIds;if(e&(Tt|Ct)&&1===n.length)return i[n[0].identifier]=!0,[n,n];var r,o,a=E(t.changedTouches),s=[],c=this.target;if(o=n.filter(function(t){return v(t.target,c)}),e===Tt)for(r=0;r<o.length;)i[o[r].identifier]=!0,r++;for(r=0;r<a.length;)i[a[r].identifier]&&s.push(a[r]),e&(Nt|Mt)&&delete i[a[r].identifier],r++;return s.length?[w(o.concat(s),"identifier",!0),s]:void 0}function q(){T.apply(this,arguments);var t=h(this.handler,this);this.touch=new W(this.manager,t),this.mouse=new z(this.manager,t)}function H(t,e){this.manager=t,this.set(e)}function Y(t){if(g(t,ie))return ie;var e=g(t,re),n=g(t,oe);return e&&n?re+" "+oe:e||n?e?re:oe:g(t,ne)?ne:ee}function X(t){this.id=S(),this.manager=null,this.options=p(t||{},this.defaults),this.options.enable=f(this.options.enable,!0),this.state=ae,this.simultaneous={},this.requireFail=[]}function K(t){return t&le?"cancel":t&ue?"end":t&ce?"move":t&se?"start":""}function Z(t){return t==Bt?"down":t==kt?"up":t==Pt?"left":t==Dt?"right":""}function Q(t,e){var n=e.manager;return n?n.get(t):t}function J(){X.apply(this,arguments)}function tt(){J.apply(this,arguments),this.pX=null,this.pY=null}function et(){J.apply(this,arguments)}function nt(){X.apply(this,arguments),this._timer=null,this._input=null}function it(){J.apply(this,arguments)}function rt(){J.apply(this,arguments)}function ot(){X.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function at(t,e){return e=e||{},e.recognizers=f(e.recognizers,at.defaults.preset),new st(t,e)}function st(t,e){e=e||{},this.options=p(e,at.defaults),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.element=t,this.input=C(this),this.touchAction=new H(this,this.options.touchAction),ct(this,!0),c(e.recognizers,function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}function ct(t,e){var n=t.element;c(t.options.cssProps,function(t,i){n.style[_(n.style,i)]=e?t:""})}function ut(t,e){var n=i.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}var pt=["","webkit","moz","MS","ms","o"],lt=i.createElement("div"),ht="function",dt=Math.round,ft=Math.abs,mt=Date.now,yt=1,vt=/mobile|tablet|ip(ad|hone|od)|android/i,gt="ontouchstart"in e,xt=_(e,"PointerEvent")!==o,bt=gt&&vt.test(navigator.userAgent),Et="touch",wt="pen",_t="mouse",St="kinect",At=25,Tt=1,Ct=2,Nt=4,Mt=8,Rt=1,Pt=2,Dt=4,kt=8,Bt=16,Ot=Pt|Dt,It=kt|Bt,Lt=Ot|It,jt=["x","y"],Ft=["clientX","clientY"];T.prototype={handler:function(){},init:function(){this.evEl&&m(this.element,this.evEl,this.domHandler),this.evTarget&&m(this.target,this.evTarget,this.domHandler),this.evWin&&m(A(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&y(this.element,this.evEl,this.domHandler),this.evTarget&&y(this.target,this.evTarget,this.domHandler),this.evWin&&y(A(this.element),this.evWin,this.domHandler)}};var zt={mousedown:Tt,mousemove:Ct,mouseup:Nt},Vt="mousedown",$t="mousemove mouseup";l(z,T,{handler:function(t){var e=zt[t.type];e&Tt&&0===t.button&&(this.pressed=!0),e&Ct&&1!==t.which&&(e=Nt),this.pressed&&this.allow&&(e&Nt&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:_t,srcEvent:t}))}});var Gt={pointerdown:Tt,pointermove:Ct,pointerup:Nt,pointercancel:Mt,pointerout:Mt},Wt={2:Et,3:wt,4:_t,5:St},Ut="pointerdown",qt="pointermove pointerup pointercancel";e.MSPointerEvent&&(Ut="MSPointerDown",qt="MSPointerMove MSPointerUp MSPointerCancel"),l(V,T,{handler:function(t){var e=this.store,n=!1,i=t.type.toLowerCase().replace("ms",""),r=Gt[i],o=Wt[t.pointerType]||t.pointerType,a=o==Et,s=b(e,t.pointerId,"pointerId");r&Tt&&(0===t.button||a)?0>s&&(e.push(t),s=e.length-1):r&(Nt|Mt)&&(n=!0),0>s||(e[s]=t,this.callback(this.manager,r,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(s,1))}});var Ht={touchstart:Tt,touchmove:Ct,touchend:Nt,touchcancel:Mt},Yt="touchstart",Xt="touchstart touchmove touchend touchcancel";l($,T,{handler:function(t){var e=Ht[t.type];if(e===Tt&&(this.started=!0),this.started){var n=G.call(this,t,e);e&(Nt|Mt)&&n[0].length-n[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:Et,srcEvent:t})}}});var Kt={touchstart:Tt,touchmove:Ct,touchend:Nt,touchcancel:Mt},Zt="touchstart touchmove touchend touchcancel";l(W,T,{handler:function(t){var e=Kt[t.type],n=U.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:Et,srcEvent:t})}}),l(q,T,{handler:function(t,e,n){var i=n.pointerType==Et,r=n.pointerType==_t;if(i)this.mouse.allow=!1;else if(r&&!this.mouse.allow)return;e&(Nt|Mt)&&(this.mouse.allow=!0),this.callback(t,e,n)},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var Qt=_(lt.style,"touchAction"),Jt=Qt!==o,te="compute",ee="auto",ne="manipulation",ie="none",re="pan-x",oe="pan-y";H.prototype={set:function(t){t==te&&(t=this.compute()),Jt&&(this.manager.element.style[Qt]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return c(this.manager.recognizers,function(e){d(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),Y(t.join(" "))},preventDefaults:function(t){if(!Jt){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)return void e.preventDefault();var i=this.actions,r=g(i,ie),o=g(i,oe),a=g(i,re);return r||o&&n&Ot||a&&n&It?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var ae=1,se=2,ce=4,ue=8,pe=ue,le=16,he=32;X.prototype={defaults:{},set:function(t){return u(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(s(t,"recognizeWith",this))return this;var e=this.simultaneous;return t=Q(t,this),e[t.id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return s(t,"dropRecognizeWith",this)?this:(t=Q(t,this),delete this.simultaneous[t.id],this)},requireFailure:function(t){if(s(t,"requireFailure",this))return this;var e=this.requireFail;return t=Q(t,this),-1===b(e,t)&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(s(t,"dropRequireFailure",this))return this;t=Q(t,this);var e=b(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){function e(e){n.manager.emit(n.options.event+(e?K(i):""),t)}var n=this,i=this.state;ue>i&&e(!0),e(),i>=ue&&e(!0)},tryEmit:function(t){return this.canEmit()?this.emit(t):void(this.state=he)},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(he|ae)))return!1;t++}return!0},recognize:function(t){var e=u({},t);return d(this.options.enable,[this,e])?(this.state&(pe|le|he)&&(this.state=ae),this.state=this.process(e),void(this.state&(se|ce|ue|le)&&this.tryEmit(e))):(this.reset(),void(this.state=he))},process:function(t){},getTouchAction:function(){},reset:function(){}},l(J,X,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,n=t.eventType,i=e&(se|ce),r=this.attrTest(t);return i&&(n&Mt||!r)?e|le:i||r?n&Nt?e|ue:e&se?e|ce:se:he}}),l(tt,J,{defaults:{event:"pan",threshold:10,pointers:1,direction:Lt},getTouchAction:function(){var t=this.options.direction,e=[];return t&Ot&&e.push(oe),t&It&&e.push(re),e},directionTest:function(t){var e=this.options,n=!0,i=t.distance,r=t.direction,o=t.deltaX,a=t.deltaY;return r&e.direction||(e.direction&Ot?(r=0===o?Rt:0>o?Pt:Dt,n=o!=this.pX,i=Math.abs(t.deltaX)):(r=0===a?Rt:0>a?kt:Bt,n=a!=this.pY,i=Math.abs(t.deltaY))),t.direction=r,n&&i>e.threshold&&r&e.direction},attrTest:function(t){return J.prototype.attrTest.call(this,t)&&(this.state&se||!(this.state&se)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Z(t.direction);e&&this.manager.emit(this.options.event+e,t),this._super.emit.call(this,t)}}),l(et,J,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[ie]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&se)},emit:function(t){if(this._super.emit.call(this,t),1!==t.scale){var e=t.scale<1?"in":"out";this.manager.emit(this.options.event+e,t)}}}),l(nt,X,{defaults:{event:"press",pointers:1,time:500,threshold:5},getTouchAction:function(){return[ee]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,r=t.deltaTime>e.time;if(this._input=t,!i||!n||t.eventType&(Nt|Mt)&&!r)this.reset();else if(t.eventType&Tt)this.reset(),this._timer=a(function(){this.state=pe,this.tryEmit()},e.time,this);else if(t.eventType&Nt)return pe;return he},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===pe&&(t&&t.eventType&Nt?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=mt(),this.manager.emit(this.options.event,this._input)))}}),l(it,J,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[ie]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&se)}}),l(rt,J,{defaults:{event:"swipe",threshold:10,velocity:.65,direction:Ot|It,pointers:1},getTouchAction:function(){return tt.prototype.getTouchAction.call(this)},attrTest:function(t){var e,n=this.options.direction;return n&(Ot|It)?e=t.velocity:n&Ot?e=t.velocityX:n&It&&(e=t.velocityY),this._super.attrTest.call(this,t)&&n&t.direction&&t.distance>this.options.threshold&&ft(e)>this.options.velocity&&t.eventType&Nt},emit:function(t){var e=Z(t.direction);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),l(ot,X,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:2,posThreshold:10},getTouchAction:function(){return[ne]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,r=t.deltaTime<e.time;if(this.reset(),t.eventType&Tt&&0===this.count)return this.failTimeout();if(i&&r&&n){if(t.eventType!=Nt)return this.failTimeout();var o=this.pTime?t.timeStamp-this.pTime<e.interval:!0,s=!this.pCenter||I(this.pCenter,t.center)<e.posThreshold;this.pTime=t.timeStamp,this.pCenter=t.center,s&&o?this.count+=1:this.count=1,this._input=t;var c=this.count%e.taps;if(0===c)return this.hasRequireFailures()?(this._timer=a(function(){this.state=pe,this.tryEmit()},e.interval,this),se):pe}return he},failTimeout:function(){return this._timer=a(function(){this.state=he},this.options.interval,this),he},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==pe&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),at.VERSION="2.0.4",at.defaults={domEvents:!1,touchAction:te,enable:!0,inputTarget:null,inputClass:null,preset:[[it,{enable:!1}],[et,{enable:!1},["rotate"]],[rt,{direction:Ot}],[tt,{direction:Ot},["swipe"]],[ot],[ot,{event:"doubletap",taps:2},["tap"]],[nt]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var de=1,fe=2;st.prototype={set:function(t){return u(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?fe:de},recognize:function(t){var e=this.session;if(!e.stopped){this.touchAction.preventDefaults(t);var n,i=this.recognizers,r=e.curRecognizer;(!r||r&&r.state&pe)&&(r=e.curRecognizer=null);for(var o=0;o<i.length;)n=i[o],e.stopped===fe||r&&n!=r&&!n.canRecognizeWith(r)?n.reset():n.recognize(t),!r&&n.state&(se|ce|ue)&&(r=e.curRecognizer=n),o++}},get:function(t){if(t instanceof X)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event==t)return e[n];return null},add:function(t){if(s(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(s(t,"remove",this))return this;var e=this.recognizers;return t=this.get(t),e.splice(b(e,t),1),this.touchAction.update(),this},on:function(t,e){var n=this.handlers;return c(x(t),function(t){n[t]=n[t]||[],n[t].push(e)}),this},off:function(t,e){var n=this.handlers;return c(x(t),function(t){e?n[t].splice(b(n[t],e),1):delete n[t]}),this},emit:function(t,e){this.options.domEvents&&ut(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var i=0;i<n.length;)n[i](e),i++}},destroy:function(){this.element&&ct(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},u(at,{INPUT_START:Tt,INPUT_MOVE:Ct,INPUT_END:Nt,INPUT_CANCEL:Mt,STATE_POSSIBLE:ae,STATE_BEGAN:se,STATE_CHANGED:ce,STATE_ENDED:ue,STATE_RECOGNIZED:pe,STATE_CANCELLED:le,STATE_FAILED:he,DIRECTION_NONE:Rt,DIRECTION_LEFT:Pt,DIRECTION_RIGHT:Dt,DIRECTION_UP:kt,DIRECTION_DOWN:Bt,DIRECTION_HORIZONTAL:Ot,DIRECTION_VERTICAL:It,DIRECTION_ALL:Lt,Manager:st,Input:T,TouchAction:H,TouchInput:W,MouseInput:z,PointerEventInput:V,TouchMouseInput:q,SingleTouchInput:$,Recognizer:X,AttrRecognizer:J,Tap:ot,Pan:tt,Swipe:rt,Pinch:et,Rotate:it,Press:nt,on:m,off:y,each:c,merge:p,extend:u,inherit:l,bindFn:h,prefixed:_}),typeof t==ht&&t.amd?t(function(){return at}):"undefined"!=typeof n&&n.exports?n.exports=at:e[r]=at}(window,document,"Hammer")},{}],199:[function(e,n,i){!function(r,o){if("function"==typeof t&&t.amd)t(["eve"],function(t){return o(r,t)});else if("undefined"!=typeof i){var a=e(197);n.exports=o(r,a)}else o(r,r.eve)}(window||this,function(t,e){var n=function(e){var n={},i=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame||function(t){setTimeout(t,16)},r=Array.isArray||function(t){return t instanceof Array||"[object Array]"==Object.prototype.toString.call(t)},o=0,a="M"+(+new Date).toString(36),s=function(){return a+(o++).toString(36)},c=Date.now||function(){return+new Date},u=function(t){var e=this;if(null==t)return e.s;var n=e.s-t;e.b+=e.dur*n,e.B+=e.dur*n,e.s=t},p=function(t){var e=this;return null==t?e.spd:void(e.spd=t)},l=function(t){var e=this;return null==t?e.dur:(e.s=e.s*t/e.dur,void(e.dur=t))},h=function(){var t=this;delete n[t.id],t.update(),e("mina.stop."+t.id,t)},d=function(){var t=this;t.pdif||(delete n[t.id],t.update(),t.pdif=t.get()-t.b)},f=function(){var t=this;t.pdif&&(t.b=t.get()-t.pdif,delete t.pdif,n[t.id]=t)},m=function(){var t,e=this;if(r(e.start)){t=[];for(var n=0,i=e.start.length;i>n;n++)t[n]=+e.start[n]+(e.end[n]-e.start[n])*e.easing(e.s)}else t=+e.start+(e.end-e.start)*e.easing(e.s);e.set(t)},y=function(){var t=0;for(var r in n)if(n.hasOwnProperty(r)){var o=n[r],a=o.get();t++,o.s=(a-o.b)/(o.dur/o.spd),o.s>=1&&(delete n[r],o.s=1,t--,function(t){setTimeout(function(){e("mina.finish."+t.id,t)})}(o)),o.update()}t&&i(y)},v=function(t,e,r,o,a,c,g){var x={id:s(),start:t,end:e,b:r,s:0,dur:o-r,spd:1,get:a,set:c,easing:g||v.linear,status:u,speed:p,duration:l,stop:h,pause:d,resume:f,update:m};n[x.id]=x;var b,E=0;for(b in n)if(n.hasOwnProperty(b)&&(E++,2==E))break;return 1==E&&i(y),x};return v.time=c,v.getById=function(t){return n[t]||null},v.linear=function(t){return t},v.easeout=function(t){return Math.pow(t,1.7)},v.easein=function(t){return Math.pow(t,.48)},v.easeinout=function(t){if(1==t)return 1;if(0==t)return 0;var e=.48-t/1.04,n=Math.sqrt(.1734+e*e),i=n-e,r=Math.pow(Math.abs(i),1/3)*(0>i?-1:1),o=-n-e,a=Math.pow(Math.abs(o),1/3)*(0>o?-1:1),s=r+a+.5;return 3*(1-s)*s*s+s*s*s},v.backin=function(t){if(1==t)return 1;var e=1.70158;return t*t*((e+1)*t-e)},v.backout=function(t){if(0==t)return 0;t-=1;var e=1.70158;return t*t*((e+1)*t+e)+1},v.elastic=function(t){return t==!!t?t:Math.pow(2,-10*t)*Math.sin((t-.075)*(2*Math.PI)/.3)+1},v.bounce=function(t){var e,n=7.5625,i=2.75;return 1/i>t?e=n*t*t:2/i>t?(t-=1.5/i,e=n*t*t+.75):2.5/i>t?(t-=2.25/i,e=n*t*t+.9375):(t-=2.625/i,e=n*t*t+.984375),e},t.mina=v,v}("undefined"==typeof e?function(){}:e),i=function(t){function n(t,e){if(t){if(t.tagName)return w(t);if(r(t,"array")&&n.set)return n.set.apply(n,t);if(t instanceof v)return t;if(null==e)return t=_.doc.querySelector(t),w(t)}return t=null==t?"100%":t,e=null==e?"100%":e,new E(t,e)}function i(t,e){if(e){if("#text"==t&&(t=_.doc.createTextNode(e.text||"")),"string"==typeof t&&(t=i(t)),"string"==typeof e)return"xlink:"==e.substring(0,6)?t.getAttributeNS(W,e.substring(6)):"xml:"==e.substring(0,4)?t.getAttributeNS(U,e.substring(4)):t.getAttribute(e);for(var n in e)if(e[S](n)){var r=A(e[n]);r?"xlink:"==n.substring(0,6)?t.setAttributeNS(W,n.substring(6),r):"xml:"==n.substring(0,4)?t.setAttributeNS(U,n.substring(4),r):t.setAttribute(n,r):t.removeAttribute(n)}}else t=_.doc.createElementNS(U,t);return t}function r(t,e){return e=A.prototype.toLowerCase.call(e),"finite"==e?isFinite(t):"array"==e&&(t instanceof Array||Array.isArray&&Array.isArray(t))?!0:"null"==e&&null===t||e==typeof t&&null!==t||"object"==e&&t===Object(t)||B.call(t).slice(8,-1).toLowerCase()==e}function o(t){if("function"==typeof t||Object(t)!==t)return t;var e=new t.constructor;for(var n in t)t[S](n)&&(e[n]=o(t[n]));return e}function a(t,e){for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return t.push(t.splice(n,1)[0])}function s(t,e,n){function i(){var r=Array.prototype.slice.call(arguments,0),o=r.join("␀"),s=i.cache=i.cache||{},c=i.count=i.count||[];return s[S](o)?(a(c,o),n?n(s[o]):s[o]):(c.length>=1e3&&delete s[c.shift()],c.push(o),s[o]=t.apply(e,r),n?n(s[o]):s[o])}return i}function c(t,e,n,i,r,o){if(null==r){var a=t-n,s=e-i;return a||s?(180+180*N.atan2(-s,-a)/D+360)%360:0}return c(t,e,r,o)-c(n,i,r,o)}function u(t){return t%360*D/180}function p(t){return 180*t/D%360}function l(t){var e=[];return t=t.replace(/(?:^|\s)(\w+)\(([^)]+)\)/g,function(t,n,i){return i=i.split(/\s*,\s*|\s+/),"rotate"==n&&1==i.length&&i.push(0,0),"scale"==n&&(i.length>2?i=i.slice(0,2):2==i.length&&i.push(0,0),1==i.length&&i.push(i[0],0,0)),"skewX"==n?e.push(["m",1,0,N.tan(u(i[0])),1,0,0]):"skewY"==n?e.push(["m",1,N.tan(u(i[0])),0,1,0,0]):e.push([n.charAt(0)].concat(i)),t}),e}function h(t,e){var i=J(t),r=new n.Matrix;if(i)for(var o=0,a=i.length;a>o;o++){var s,c,u,p,l,h=i[o],d=h.length,f=A(h[0]).toLowerCase(),m=h[0]!=f,y=m?r.invert():0;"t"==f&&2==d?r.translate(h[1],0):"t"==f&&3==d?m?(s=y.x(0,0),c=y.y(0,0),u=y.x(h[1],h[2]),p=y.y(h[1],h[2]),r.translate(u-s,p-c)):r.translate(h[1],h[2]):"r"==f?2==d?(l=l||e,r.rotate(h[1],l.x+l.width/2,l.y+l.height/2)):4==d&&(m?(u=y.x(h[2],h[3]),p=y.y(h[2],h[3]),r.rotate(h[1],u,p)):r.rotate(h[1],h[2],h[3])):"s"==f?2==d||3==d?(l=l||e,r.scale(h[1],h[d-1],l.x+l.width/2,l.y+l.height/2)):4==d?m?(u=y.x(h[2],h[3]),p=y.y(h[2],h[3]),r.scale(h[1],h[1],u,p)):r.scale(h[1],h[1],h[2],h[3]):5==d&&(m?(u=y.x(h[3],h[4]),p=y.y(h[3],h[4]),r.scale(h[1],h[2],u,p)):r.scale(h[1],h[2],h[3],h[4])):"m"==f&&7==d&&r.add(h[1],h[2],h[3],h[4],h[5],h[6])}return r}function d(t){var e=t.node.ownerSVGElement&&w(t.node.ownerSVGElement)||t.node.parentNode&&w(t.node.parentNode)||n.select("svg")||n(0,0),i=e.select("defs"),r=null==i?!1:i.node;return r||(r=b("defs",e.node).node),r}function f(t){return t.node.ownerSVGElement&&w(t.node.ownerSVGElement)||n.select("svg")}function m(t,e,n){function r(t){if(null==t)return k;if(t==+t)return t;i(u,{width:t});try{return u.getBBox().width}catch(e){return 0}}function o(t){if(null==t)return k;if(t==+t)return t;i(u,{height:t});try{return u.getBBox().height}catch(e){return 0}}function a(i,r){null==e?c[i]=r(t.attr(i)||0):i==e&&(c=r(null==n?t.attr(i)||0:n))}var s=f(t).node,c={},u=s.querySelector(".svg---mgr");switch(u||(u=i("rect"),i(u,{x:-9e9,y:-9e9,width:10,height:10,"class":"svg---mgr",fill:"none"}),s.appendChild(u)),t.type){case"rect":a("rx",r),a("ry",o);case"image":a("width",r),a("height",o);case"text":a("x",r),a("y",o);break;case"circle":a("cx",r),a("cy",o),a("r",r);break;case"ellipse":a("cx",r),a("cy",o),a("rx",r),a("ry",o);break;case"line":a("x1",r),a("x2",r),a("y1",o),a("y2",o);break;case"marker":a("refX",r),a("markerWidth",r),a("refY",o),a("markerHeight",o);break;case"radialGradient":a("fx",r),a("fy",o);break;case"tspan":a("dx",r),a("dy",o);break;default:a(e,r)}return s.removeChild(u),c}function y(t){r(t,"array")||(t=Array.prototype.slice.call(arguments,0));for(var e=0,n=0,i=this.node;this[e];)delete this[e++];for(e=0;e<t.length;e++)"set"==t[e].type?t[e].forEach(function(t){i.appendChild(t.node)}):i.appendChild(t[e].node);var o=i.childNodes;for(e=0;e<o.length;e++)this[n++]=w(o[e]);return this}function v(t){if(t.snap in q)return q[t.snap];var e;try{e=t.ownerSVGElement}catch(n){}this.node=t,e&&(this.paper=new E(e)),this.type=t.tagName;var i=this.id=G(this);if(this.anims={},this._={transform:[]},t.snap=i,q[i]=this,"g"==this.type&&(this.add=y),this.type in{g:1,mask:1,pattern:1,symbol:1})for(var r in E.prototype)E.prototype[S](r)&&(this[r]=E.prototype[r])}function x(t){this.node=t}function b(t,e){var n=i(t);e.appendChild(n);var r=w(n);return r}function E(t,e){var n,r,o,a=E.prototype;if(t&&"svg"==t.tagName){if(t.snap in q)return q[t.snap];var s=t.ownerDocument;n=new v(t),r=t.getElementsByTagName("desc")[0],o=t.getElementsByTagName("defs")[0],r||(r=i("desc"),r.appendChild(s.createTextNode("Created with Snap")),n.node.appendChild(r)),o||(o=i("defs"),n.node.appendChild(o)),n.defs=o;for(var c in a)a[S](c)&&(n[c]=a[c]);n.paper=n.root=n}else n=b("svg",_.doc.body),i(n.node,{height:e,version:1.1,width:t,xmlns:U});return n}function w(t){return t?t instanceof v||t instanceof x?t:t.tagName&&"svg"==t.tagName.toLowerCase()?new E(t):t.tagName&&"object"==t.tagName.toLowerCase()&&"image/svg+xml"==t.type?new E(t.contentDocument.getElementsByTagName("svg")[0]):new v(t):t}n.version="0.3.0",n.toString=function(){return"Snap v"+this.version},n._={};var _={win:t.window,doc:t.window.document};n._.glob=_;var S="hasOwnProperty",A=String,T=parseFloat,C=parseInt,N=Math,M=N.max,R=N.min,P=N.abs,D=(N.pow,N.PI),k=(N.round,""),B=Object.prototype.toString,O=/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\))\s*$/i,I=(n._.separator=/[,\s]+/,/[\s]*,[\s]*/),L={hs:1,rg:1},j=/([a-z])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/gi,F=/([rstm])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/gi,z=/(-?\d*\.?\d*(?:e[\-+]?\\d+)?)[\s]*,?[\s]*/gi,V=0,$="S"+(+new Date).toString(36),G=function(t){return(t&&t.type?t.type:k)+$+(V++).toString(36)},W="http://www.w3.org/1999/xlink",U="http://www.w3.org/2000/svg",q={};n.url=function(t){return"url('#"+t+"')"};n._.$=i,n._.id=G,n.format=function(){var t=/\{([^\}]+)\}/g,e=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,n=function(t,n,i){var r=i;return n.replace(e,function(t,e,n,i,o){e=e||i,r&&(e in r&&(r=r[e]),"function"==typeof r&&o&&(r=r()))}),r=(null==r||r==i?t:r)+""};return function(e,i){return A(e).replace(t,function(t,e){return n(t,e,i)})}}(),n._.clone=o,n._.cacher=s,n.rad=u,n.deg=p,n.angle=c,n.is=r,n.snapTo=function(t,e,n){if(n=r(n,"finite")?n:10,r(t,"array")){for(var i=t.length;i--;)if(P(t[i]-e)<=n)return t[i]}else{t=+t;var o=e%t;if(n>o)return e-o;if(o>t-n)return e-o+t}return e},n.getRGB=s(function(t){if(!t||(t=A(t)).indexOf("-")+1)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:K};if("none"==t)return{r:-1,g:-1,b:-1,hex:"none",toString:K};if(!(L[S](t.toLowerCase().substring(0,2))||"#"==t.charAt())&&(t=H(t)),!t)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:K};var e,i,o,a,s,c,u=t.match(O);return u?(u[2]&&(o=C(u[2].substring(5),16),i=C(u[2].substring(3,5),16),e=C(u[2].substring(1,3),16)),u[3]&&(o=C((s=u[3].charAt(3))+s,16),i=C((s=u[3].charAt(2))+s,16),e=C((s=u[3].charAt(1))+s,16)),u[4]&&(c=u[4].split(I),e=T(c[0]),"%"==c[0].slice(-1)&&(e*=2.55),i=T(c[1]),"%"==c[1].slice(-1)&&(i*=2.55),o=T(c[2]),"%"==c[2].slice(-1)&&(o*=2.55),"rgba"==u[1].toLowerCase().slice(0,4)&&(a=T(c[3])),c[3]&&"%"==c[3].slice(-1)&&(a/=100)),u[5]?(c=u[5].split(I),e=T(c[0]),"%"==c[0].slice(-1)&&(e/=100),i=T(c[1]),"%"==c[1].slice(-1)&&(i/=100),o=T(c[2]),"%"==c[2].slice(-1)&&(o/=100),("deg"==c[0].slice(-3)||"°"==c[0].slice(-1))&&(e/=360),"hsba"==u[1].toLowerCase().slice(0,4)&&(a=T(c[3])),c[3]&&"%"==c[3].slice(-1)&&(a/=100),n.hsb2rgb(e,i,o,a)):u[6]?(c=u[6].split(I),e=T(c[0]),"%"==c[0].slice(-1)&&(e/=100),i=T(c[1]),"%"==c[1].slice(-1)&&(i/=100),o=T(c[2]),"%"==c[2].slice(-1)&&(o/=100),("deg"==c[0].slice(-3)||"°"==c[0].slice(-1))&&(e/=360),"hsla"==u[1].toLowerCase().slice(0,4)&&(a=T(c[3])),c[3]&&"%"==c[3].slice(-1)&&(a/=100),n.hsl2rgb(e,i,o,a)):(e=R(N.round(e),255),i=R(N.round(i),255),o=R(N.round(o),255),a=R(M(a,0),1),u={r:e,g:i,b:o,toString:K},u.hex="#"+(16777216|o|i<<8|e<<16).toString(16).slice(1),u.opacity=r(a,"finite")?a:1,u)):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:K}},n),n.hsb=s(function(t,e,i){return n.hsb2rgb(t,e,i).hex}),n.hsl=s(function(t,e,i){return n.hsl2rgb(t,e,i).hex}),n.rgb=s(function(t,e,n,i){if(r(i,"finite")){var o=N.round;return"rgba("+[o(t),o(e),o(n),+i.toFixed(2)]+")"}return"#"+(16777216|n|e<<8|t<<16).toString(16).slice(1)});var H=function(t){var e=_.doc.getElementsByTagName("head")[0]||_.doc.getElementsByTagName("svg")[0],n="rgb(255, 0, 0)";return(H=s(function(t){if("red"==t.toLowerCase())return n;e.style.color=n,e.style.color=t;var i=_.doc.defaultView.getComputedStyle(e,k).getPropertyValue("color");return i==n?null:i}))(t)},Y=function(){return"hsb("+[this.h,this.s,this.b]+")"},X=function(){return"hsl("+[this.h,this.s,this.l]+")"},K=function(){return 1==this.opacity||null==this.opacity?this.hex:"rgba("+[this.r,this.g,this.b,this.opacity]+")"},Z=function(t,e,i){if(null==e&&r(t,"object")&&"r"in t&&"g"in t&&"b"in t&&(i=t.b,e=t.g,t=t.r),null==e&&r(t,string)){var o=n.getRGB(t);t=o.r,e=o.g,i=o.b}return(t>1||e>1||i>1)&&(t/=255,e/=255,i/=255),[t,e,i]},Q=function(t,e,i,o){t=N.round(255*t),e=N.round(255*e),i=N.round(255*i);var a={r:t,g:e,b:i,opacity:r(o,"finite")?o:1,hex:n.rgb(t,e,i),toString:K};return r(o,"finite")&&(a.opacity=o),a};n.color=function(t){var e;return r(t,"object")&&"h"in t&&"s"in t&&"b"in t?(e=n.hsb2rgb(t),t.r=e.r,t.g=e.g,t.b=e.b,t.opacity=1,t.hex=e.hex):r(t,"object")&&"h"in t&&"s"in t&&"l"in t?(e=n.hsl2rgb(t),t.r=e.r,t.g=e.g,t.b=e.b,t.opacity=1,t.hex=e.hex):(r(t,"string")&&(t=n.getRGB(t)),r(t,"object")&&"r"in t&&"g"in t&&"b"in t&&!("error"in t)?(e=n.rgb2hsl(t),t.h=e.h,t.s=e.s,t.l=e.l,e=n.rgb2hsb(t),t.v=e.b):(t={hex:"none"},t.r=t.g=t.b=t.h=t.s=t.v=t.l=-1,t.error=1)),t.toString=K,t},n.hsb2rgb=function(t,e,n,i){r(t,"object")&&"h"in t&&"s"in t&&"b"in t&&(n=t.b,e=t.s,t=t.h,i=t.o),t*=360;var o,a,s,c,u;return t=t%360/60,u=n*e,c=u*(1-P(t%2-1)),o=a=s=n-u,t=~~t,o+=[u,c,0,0,c,u][t],a+=[c,u,u,c,0,0][t],s+=[0,0,c,u,u,c][t],Q(o,a,s,i)},n.hsl2rgb=function(t,e,n,i){r(t,"object")&&"h"in t&&"s"in t&&"l"in t&&(n=t.l,e=t.s,t=t.h),(t>1||e>1||n>1)&&(t/=360,e/=100,n/=100),t*=360;var o,a,s,c,u;return t=t%360/60,u=2*e*(.5>n?n:1-n),c=u*(1-P(t%2-1)),o=a=s=n-u/2,t=~~t,o+=[u,c,0,0,c,u][t],a+=[c,u,u,c,0,0][t],s+=[0,0,c,u,u,c][t],Q(o,a,s,i)},n.rgb2hsb=function(t,e,n){n=Z(t,e,n),t=n[0],e=n[1],n=n[2];var i,r,o,a;return o=M(t,e,n),a=o-R(t,e,n),i=0==a?null:o==t?(e-n)/a:o==e?(n-t)/a+2:(t-e)/a+4,i=(i+360)%6*60/360,r=0==a?0:a/o,{h:i,s:r,b:o,toString:Y}},n.rgb2hsl=function(t,e,n){n=Z(t,e,n),t=n[0],e=n[1],n=n[2];var i,r,o,a,s,c;return a=M(t,e,n),s=R(t,e,n),c=a-s,i=0==c?null:a==t?(e-n)/c:a==e?(n-t)/c+2:(t-e)/c+4,i=(i+360)%6*60/360,o=(a+s)/2,r=0==c?0:.5>o?c/(2*o):c/(2-2*o),{h:i,s:r,l:o,toString:X}},n.parsePathString=function(t){if(!t)return null;var e=n.path(t);if(e.arr)return n.path.clone(e.arr);var i={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},o=[];return r(t,"array")&&r(t[0],"array")&&(o=n.path.clone(t)),o.length||A(t).replace(j,function(t,e,n){var r=[],a=e.toLowerCase();if(n.replace(z,function(t,e){e&&r.push(+e)}),"m"==a&&r.length>2&&(o.push([e].concat(r.splice(0,2))),a="l",e="m"==e?"l":"L"),"o"==a&&1==r.length&&o.push([e,r[0]]),"r"==a)o.push([e].concat(r));else for(;r.length>=i[a]&&(o.push([e].concat(r.splice(0,i[a]))),i[a]););}),o.toString=n.path.toString,e.arr=n.path.clone(o),o};var J=n.parseTransformString=function(t){if(!t)return null;var e=[];return r(t,"array")&&r(t[0],"array")&&(e=n.path.clone(t)),e.length||A(t).replace(F,function(t,n,i){var r=[];n.toLowerCase();i.replace(z,function(t,e){e&&r.push(+e)}),e.push([n].concat(r))}),e.toString=n.path.toString,e};n._.svgTransform2string=l,n._.rgTransform=/^[a-z][\s]*-?\.?\d/i,n._.transform2matrix=h,n._unit2px=m;_.doc.contains||_.doc.compareDocumentPosition?function(t,e){var n=9==t.nodeType?t.documentElement:t,i=e&&e.parentNode;return t==i||!(!i||1!=i.nodeType||!(n.contains?n.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))}:function(t,e){if(e)for(;e;)if(e=e.parentNode,e==t)return!0;return!1};n._.getSomeDefs=d,n._.getSomeSVG=f,n.select=function(t){return t=A(t).replace(/([^\\]):/g,"$1\\:"),w(_.doc.querySelector(t))},n.selectAll=function(t){for(var e=_.doc.querySelectorAll(t),i=(n.set||Array)(),r=0;r<e.length;r++)i.push(w(e[r]));return i},setInterval(function(){for(var t in q)if(q[S](t)){var e=q[t],n=e.node;("svg"!=e.type&&!n.ownerSVGElement||"svg"==e.type&&(!n.parentNode||"ownerSVGElement"in n.parentNode&&!n.ownerSVGElement))&&delete q[t]}},1e4),v.prototype.attr=function(t,n){var i=this;i.node;if(!t)return i;if(r(t,"string")){if(!(arguments.length>1))return e("snap.util.getattr."+t,i).firstDefined();var o={};o[t]=n,t=o}for(var a in t)t[S](a)&&e("snap.util.attr."+a,i,t[a]);return i},n.parse=function(t){var e=_.doc.createDocumentFragment(),n=!0,i=_.doc.createElement("div");if(t=A(t),t.match(/^\s*<\s*svg(?:\s|>)/)||(t="<svg>"+t+"</svg>",n=!1),i.innerHTML=t,t=i.getElementsByTagName("svg")[0])if(n)e=t;else{for(;t.firstChild;)e.appendChild(t.firstChild);i.innerHTML=k}return new x(e)},n.fragment=function(){for(var t=Array.prototype.slice.call(arguments,0),e=_.doc.createDocumentFragment(),i=0,r=t.length;r>i;i++){var o=t[i];o.node&&o.node.nodeType&&e.appendChild(o.node),o.nodeType&&e.appendChild(o),"string"==typeof o&&e.appendChild(n.parse(o).node)}return new x(e)},n._.make=b,n._.wrap=w,E.prototype.el=function(t,e){var n=b(t,this.node);return e&&n.attr(e),n},e.on("snap.util.getattr",function(){var t=e.nt();t=t.substring(t.lastIndexOf(".")+1);var n=t.replace(/[A-Z]/g,function(t){return"-"+t.toLowerCase()});return tt[S](n)?this.node.ownerDocument.defaultView.getComputedStyle(this.node,null).getPropertyValue(n):i(this.node,t)});var tt={"alignment-baseline":0,"baseline-shift":0,clip:0,"clip-path":0,"clip-rule":0,color:0,"color-interpolation":0,"color-interpolation-filters":0,"color-profile":0,"color-rendering":0,cursor:0,direction:0,display:0,"dominant-baseline":0,"enable-background":0,fill:0,"fill-opacity":0,"fill-rule":0,filter:0,"flood-color":0,"flood-opacity":0,font:0,"font-family":0,"font-size":0,"font-size-adjust":0,"font-stretch":0,"font-style":0,
"font-variant":0,"font-weight":0,"glyph-orientation-horizontal":0,"glyph-orientation-vertical":0,"image-rendering":0,kerning:0,"letter-spacing":0,"lighting-color":0,marker:0,"marker-end":0,"marker-mid":0,"marker-start":0,mask:0,opacity:0,overflow:0,"pointer-events":0,"shape-rendering":0,"stop-color":0,"stop-opacity":0,stroke:0,"stroke-dasharray":0,"stroke-dashoffset":0,"stroke-linecap":0,"stroke-linejoin":0,"stroke-miterlimit":0,"stroke-opacity":0,"stroke-width":0,"text-anchor":0,"text-decoration":0,"text-rendering":0,"unicode-bidi":0,visibility:0,"word-spacing":0,"writing-mode":0};e.on("snap.util.attr",function(t){var n=e.nt(),r={};n=n.substring(n.lastIndexOf(".")+1),r[n]=t;var o=n.replace(/-(\w)/gi,function(t,e){return e.toUpperCase()}),a=n.replace(/[A-Z]/g,function(t){return"-"+t.toLowerCase()});tt[S](a)?this.node.style[o]=null==t?k:t:i(this.node,r)}),function(t){}(E.prototype),n.ajax=function(t,n,i,o){var a=new XMLHttpRequest,s=G();if(a){if(r(n,"function"))o=i,i=n,n=null;else if(r(n,"object")){var c=[];for(var u in n)n.hasOwnProperty(u)&&c.push(encodeURIComponent(u)+"="+encodeURIComponent(n[u]));n=c.join("&")}return a.open(n?"POST":"GET",t,!0),n&&(a.setRequestHeader("X-Requested-With","XMLHttpRequest"),a.setRequestHeader("Content-type","application/x-www-form-urlencoded")),i&&(e.once("snap.ajax."+s+".0",i),e.once("snap.ajax."+s+".200",i),e.once("snap.ajax."+s+".304",i)),a.onreadystatechange=function(){4==a.readyState&&e("snap.ajax."+s+"."+a.status,o,a)},4==a.readyState?a:(a.send(n),a)}},n.load=function(t,e,i){n.ajax(t,function(t){var r=n.parse(t.responseText);i?e.call(i,r):e(r)})};var et=function(t){var e=t.getBoundingClientRect(),n=t.ownerDocument,i=n.body,r=n.documentElement,o=r.clientTop||i.clientTop||0,a=r.clientLeft||i.clientLeft||0,s=e.top+(g.win.pageYOffset||r.scrollTop||i.scrollTop)-o,c=e.left+(g.win.pageXOffset||r.scrollLeft||i.scrollLeft)-a;return{y:s,x:c}};return n.getElementByPoint=function(t,e){var n=this,i=(n.canvas,_.doc.elementFromPoint(t,e));if(_.win.opera&&"svg"==i.tagName){var r=et(i),o=i.createSVGRect();o.x=t-r.x,o.y=e-r.y,o.width=o.height=1;var a=i.getIntersectionList(o,null);a.length&&(i=a[a.length-1])}return i?w(i):null},n.plugin=function(t){t(n,v,E,_,x)},_.win.Snap=n,n}(t||this);return i.plugin(function(i,r,o,a,s){function c(t,e){if(null==e){var n=!0;if(e="linearGradient"==t.type||"radialGradient"==t.type?t.node.getAttribute("gradientTransform"):"pattern"==t.type?t.node.getAttribute("patternTransform"):t.node.getAttribute("transform"),!e)return new i.Matrix;e=i._.svgTransform2string(e)}else e=i._.rgTransform.test(e)?f(e).replace(/\.{3}|\u2026/g,t._.transform||E):i._.svgTransform2string(e),d(e,"array")&&(e=i.path?i.path.toString.call(e):f(e)),t._.transform=e;var r=i._.transform2matrix(e,t.getBBox(1));return n?r:void(t.matrix=r)}function u(t){function e(t,e){var n=y(t.node,e);n=n&&n.match(o),n=n&&n[2],n&&"#"==n.charAt()&&(n=n.substring(1),n&&(s[n]=(s[n]||[]).concat(function(n){var i={};i[e]=URL(n),y(t.node,i)})))}function n(t){var e=y(t.node,"xlink:href");e&&"#"==e.charAt()&&(e=e.substring(1),e&&(s[e]=(s[e]||[]).concat(function(e){t.attr("xlink:href","#"+e)})))}for(var i,r=t.selectAll("*"),o=/^\s*url\(("|'|)(.*)\1\)\s*$/,a=[],s={},c=0,u=r.length;u>c;c++){i=r[c],e(i,"fill"),e(i,"stroke"),e(i,"filter"),e(i,"mask"),e(i,"clip-path"),n(i);var p=y(i.node,"id");p&&(y(i.node,{id:i.id}),a.push({old:p,id:i.id}))}for(c=0,u=a.length;u>c;c++){var l=s[a[c].old];if(l)for(var h=0,d=l.length;d>h;h++)l[h](a[c].id)}}function p(t,e,n){return function(i){var r=i.slice(t,e);return 1==r.length&&(r=r[0]),n?n(r):r}}function l(t){return function(){var e=t?"<"+this.type:"",n=this.node.attributes,i=this.node.childNodes;if(t)for(var r=0,o=n.length;o>r;r++)e+=" "+n[r].name+'="'+n[r].value.replace(/"/g,'\\"')+'"';if(i.length){for(t&&(e+=">"),r=0,o=i.length;o>r;r++)3==i[r].nodeType?e+=i[r].nodeValue:1==i[r].nodeType&&(e+=b(i[r]).toString());t&&(e+="</"+this.type+">")}else t&&(e+="/>");return e}}var h=r.prototype,d=i.is,f=String,m=i._unit2px,y=i._.$,v=i._.make,g=i._.getSomeDefs,x="hasOwnProperty",b=i._.wrap;h.getBBox=function(t){if(!i.Matrix||!i.path)return this.node.getBBox();var e=this,n=new i.Matrix;if(e.removed)return i._.box();for(;"use"==e.type;)if(t||(n=n.add(e.transform().localMatrix.translate(e.attr("x")||0,e.attr("y")||0))),e.original)e=e.original;else{var r=e.attr("xlink:href");e=e.original=e.node.ownerDocument.getElementById(r.substring(r.indexOf("#")+1))}var o=e._,a=i.path.get[e.type]||i.path.get.deflt;try{return t?(o.bboxwt=a?i.path.getBBox(e.realPath=a(e)):i._.box(e.node.getBBox()),i._.box(o.bboxwt)):(e.realPath=a(e),e.matrix=e.transform().localMatrix,o.bbox=i.path.getBBox(i.path.map(e.realPath,n.add(e.matrix))),i._.box(o.bbox))}catch(s){return i._.box()}};var w=function(){return this.string};h.transform=function(t){var e=this._;if(null==t){for(var n,r=this,o=new i.Matrix(this.node.getCTM()),a=c(this),s=[a],u=new i.Matrix,p=a.toTransformString(),l=f(a)==f(this.matrix)?f(e.transform):p;"svg"!=r.type&&(r=r.parent());)s.push(c(r));for(n=s.length;n--;)u.add(s[n]);return{string:l,globalMatrix:o,totalMatrix:u,localMatrix:a,diffMatrix:o.clone().add(a.invert()),global:o.toTransformString(),total:u.toTransformString(),local:p,toString:w}}return t instanceof i.Matrix?(this.matrix=t,this._.transform=t.toTransformString()):c(this,t),this.node&&("linearGradient"==this.type||"radialGradient"==this.type?y(this.node,{gradientTransform:this.matrix}):"pattern"==this.type?y(this.node,{patternTransform:this.matrix}):y(this.node,{transform:this.matrix})),this},h.parent=function(){return b(this.node.parentNode)},h.append=h.add=function(t){if(t){if("set"==t.type){var e=this;return t.forEach(function(t){e.add(t)}),this}t=b(t),this.node.appendChild(t.node),t.paper=this.paper}return this},h.appendTo=function(t){return t&&(t=b(t),t.append(this)),this},h.prepend=function(t){if(t){if("set"==t.type){var e,n=this;return t.forEach(function(t){e?e.after(t):n.prepend(t),e=t}),this}t=b(t);var i=t.parent();this.node.insertBefore(t.node,this.node.firstChild),this.add&&this.add(),t.paper=this.paper,this.parent()&&this.parent().add(),i&&i.add()}return this},h.prependTo=function(t){return t=b(t),t.prepend(this),this},h.before=function(t){if("set"==t.type){var e=this;return t.forEach(function(t){var n=t.parent();e.node.parentNode.insertBefore(t.node,e.node),n&&n.add()}),this.parent().add(),this}t=b(t);var n=t.parent();return this.node.parentNode.insertBefore(t.node,this.node),this.parent()&&this.parent().add(),n&&n.add(),t.paper=this.paper,this},h.after=function(t){t=b(t);var e=t.parent();return this.node.nextSibling?this.node.parentNode.insertBefore(t.node,this.node.nextSibling):this.node.parentNode.appendChild(t.node),this.parent()&&this.parent().add(),e&&e.add(),t.paper=this.paper,this},h.insertBefore=function(t){t=b(t);var e=this.parent();return t.node.parentNode.insertBefore(this.node,t.node),this.paper=t.paper,e&&e.add(),t.parent()&&t.parent().add(),this},h.insertAfter=function(t){t=b(t);var e=this.parent();return t.node.parentNode.insertBefore(this.node,t.node.nextSibling),this.paper=t.paper,e&&e.add(),t.parent()&&t.parent().add(),this},h.remove=function(){var t=this.parent();return this.node.parentNode&&this.node.parentNode.removeChild(this.node),delete this.paper,this.removed=!0,t&&t.add(),this},h.select=function(t){return t=f(t).replace(/([^\\]):/g,"$1\\:"),b(this.node.querySelector(t))},h.selectAll=function(t){for(var e=this.node.querySelectorAll(t),n=(i.set||Array)(),r=0;r<e.length;r++)n.push(b(e[r]));return n},h.asPX=function(t,e){return null==e&&(e=this.attr(t)),+m(this,t,e)},h.use=function(){var t,e=this.node.id;return e||(e=this.id,y(this.node,{id:e})),t="linearGradient"==this.type||"radialGradient"==this.type||"pattern"==this.type?v(this.type,this.node.parentNode):v("use",this.node.parentNode),y(t.node,{"xlink:href":"#"+e}),t.original=this,t},h.clone=function(){var t=b(this.node.cloneNode(!0));return y(t.node,"id")&&y(t.node,{id:t.id}),u(t),t.insertAfter(this),t},h.toDefs=function(){var t=g(this);return t.appendChild(this.node),this},h.pattern=h.toPattern=function(t,e,n,i){var r=v("pattern",g(this));return null==t&&(t=this.getBBox()),d(t,"object")&&"x"in t&&(e=t.y,n=t.width,i=t.height,t=t.x),y(r.node,{x:t,y:e,width:n,height:i,patternUnits:"userSpaceOnUse",id:r.id,viewBox:[t,e,n,i].join(" ")}),r.node.appendChild(this.node),r},h.marker=function(t,e,n,i,r,o){var a=v("marker",g(this));return null==t&&(t=this.getBBox()),d(t,"object")&&"x"in t&&(e=t.y,n=t.width,i=t.height,r=t.refX||t.cx,o=t.refY||t.cy,t=t.x),y(a.node,{viewBox:[t,e,n,i].join(" "),markerWidth:n,markerHeight:i,orient:"auto",refX:r||0,refY:o||0,id:a.id}),a.node.appendChild(this.node),a};var _=function(t,e,i,r){"function"!=typeof i||i.length||(r=i,i=n.linear),this.attr=t,this.dur=e,i&&(this.easing=i),r&&(this.callback=r)};i._.Animation=_,i.animation=function(t,e,n,i){return new _(t,e,n,i)},h.inAnim=function(){var t=this,e=[];for(var n in t.anims)t.anims[x](n)&&!function(t){e.push({anim:new _(t._attrs,t.dur,t.easing,t._callback),mina:t,curStatus:t.status(),status:function(e){return t.status(e)},stop:function(){t.stop()}})}(t.anims[n]);return e},i.animate=function(t,i,r,o,a,s){"function"!=typeof a||a.length||(s=a,a=n.linear);var c=n.time(),u=n(t,i,c,c+o,n.time,r,a);return s&&e.once("mina.finish."+u.id,s),u},h.stop=function(){for(var t=this.inAnim(),e=0,n=t.length;n>e;e++)t[e].stop();return this},h.animate=function(t,i,r,o){"function"!=typeof r||r.length||(o=r,r=n.linear),t instanceof _&&(o=t.callback,r=t.easing,i=r.dur,t=t.attr);var a,s,c,u,l=[],h=[],m={},y=this;for(var v in t)if(t[x](v)){y.equal?(u=y.equal(v,f(t[v])),a=u.from,s=u.to,c=u.f):(a=+y.attr(v),s=+t[v]);var g=d(a,"array")?a.length:1;m[v]=p(l.length,l.length+g,c),l=l.concat(a),h=h.concat(s)}var b=n.time(),E=n(l,h,b,b+i,n.time,function(t){var e={};for(var n in m)m[x](n)&&(e[n]=m[n](t));y.attr(e)},r);return y.anims[E.id]=E,E._attrs=t,E._callback=o,e("snap.animcreated."+y.id,E),e.once("mina.finish."+E.id,function(){delete y.anims[E.id],o&&o.call(y)}),e.once("mina.stop."+E.id,function(){delete y.anims[E.id]}),y};var S={};h.data=function(t,n){var r=S[this.id]=S[this.id]||{};if(0==arguments.length)return e("snap.data.get."+this.id,this,r,null),r;if(1==arguments.length){if(i.is(t,"object")){for(var o in t)t[x](o)&&this.data(o,t[o]);return this}return e("snap.data.get."+this.id,this,r[t],t),r[t]}return r[t]=n,e("snap.data.set."+this.id,this,n,t),this},h.removeData=function(t){return null==t?S[this.id]={}:S[this.id]&&delete S[this.id][t],this},h.outerSVG=h.toString=l(1),h.innerSVG=l(),h.toDataURL=function(){if(t&&t.btoa){var e=this.getBBox(),n=i.format('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="{width}" height="{height}" viewBox="{x} {y} {width} {height}">{contents}</svg>',{x:+e.x.toFixed(3),y:+e.y.toFixed(3),width:+e.width.toFixed(3),height:+e.height.toFixed(3),contents:this.outerSVG()});return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(n)))}},s.prototype.select=h.select,s.prototype.selectAll=h.selectAll}),i.plugin(function(t,e,n,i,r){function o(t,e,n,i,r,o){return null==e&&"[object SVGMatrix]"==a.call(t)?(this.a=t.a,this.b=t.b,this.c=t.c,this.d=t.d,this.e=t.e,void(this.f=t.f)):void(null!=t?(this.a=+t,this.b=+e,this.c=+n,this.d=+i,this.e=+r,this.f=+o):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0))}var a=Object.prototype.toString,s=String,c=Math,u="";!function(e){function n(t){return t[0]*t[0]+t[1]*t[1]}function i(t){var e=c.sqrt(n(t));t[0]&&(t[0]/=e),t[1]&&(t[1]/=e)}e.add=function(t,e,n,i,r,a){var s,c,u,p,l=[[],[],[]],h=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],d=[[t,n,r],[e,i,a],[0,0,1]];for(t&&t instanceof o&&(d=[[t.a,t.c,t.e],[t.b,t.d,t.f],[0,0,1]]),s=0;3>s;s++)for(c=0;3>c;c++){for(p=0,u=0;3>u;u++)p+=h[s][u]*d[u][c];l[s][c]=p}return this.a=l[0][0],this.b=l[1][0],this.c=l[0][1],this.d=l[1][1],this.e=l[0][2],this.f=l[1][2],this},e.invert=function(){var t=this,e=t.a*t.d-t.b*t.c;return new o(t.d/e,-t.b/e,-t.c/e,t.a/e,(t.c*t.f-t.d*t.e)/e,(t.b*t.e-t.a*t.f)/e)},e.clone=function(){return new o(this.a,this.b,this.c,this.d,this.e,this.f)},e.translate=function(t,e){return this.add(1,0,0,1,t,e)},e.scale=function(t,e,n,i){return null==e&&(e=t),(n||i)&&this.add(1,0,0,1,n,i),this.add(t,0,0,e,0,0),(n||i)&&this.add(1,0,0,1,-n,-i),this},e.rotate=function(e,n,i){e=t.rad(e),n=n||0,i=i||0;var r=+c.cos(e).toFixed(9),o=+c.sin(e).toFixed(9);return this.add(r,o,-o,r,n,i),this.add(1,0,0,1,-n,-i)},e.x=function(t,e){return t*this.a+e*this.c+this.e},e.y=function(t,e){return t*this.b+e*this.d+this.f},e.get=function(t){return+this[s.fromCharCode(97+t)].toFixed(4)},e.toString=function(){return"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")"},e.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},e.determinant=function(){return this.a*this.d-this.b*this.c},e.split=function(){var e={};e.dx=this.e,e.dy=this.f;var r=[[this.a,this.c],[this.b,this.d]];e.scalex=c.sqrt(n(r[0])),i(r[0]),e.shear=r[0][0]*r[1][0]+r[0][1]*r[1][1],r[1]=[r[1][0]-r[0][0]*e.shear,r[1][1]-r[0][1]*e.shear],e.scaley=c.sqrt(n(r[1])),i(r[1]),e.shear/=e.scaley,this.determinant()<0&&(e.scalex=-e.scalex);var o=-r[0][1],a=r[1][1];return 0>a?(e.rotate=t.deg(c.acos(a)),0>o&&(e.rotate=360-e.rotate)):e.rotate=t.deg(c.asin(o)),e.isSimple=!(+e.shear.toFixed(9)||e.scalex.toFixed(9)!=e.scaley.toFixed(9)&&e.rotate),e.isSuperSimple=!+e.shear.toFixed(9)&&e.scalex.toFixed(9)==e.scaley.toFixed(9)&&!e.rotate,e.noRotation=!+e.shear.toFixed(9)&&!e.rotate,e},e.toTransformString=function(t){var e=t||this.split();return+e.shear.toFixed(9)?"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]:(e.scalex=+e.scalex.toFixed(4),e.scaley=+e.scaley.toFixed(4),e.rotate=+e.rotate.toFixed(4),(e.dx||e.dy?"t"+[+e.dx.toFixed(4),+e.dy.toFixed(4)]:u)+(1!=e.scalex||1!=e.scaley?"s"+[e.scalex,e.scaley,0,0]:u)+(e.rotate?"r"+[+e.rotate.toFixed(4),0,0]:u))}}(o.prototype),t.Matrix=o,t.matrix=function(t,e,n,i,r,a){return new o(t,e,n,i,r,a)}}),i.plugin(function(t,n,i,r,o){function a(i){return function(r){if(e.stop(),r instanceof o&&1==r.node.childNodes.length&&("radialGradient"==r.node.firstChild.tagName||"linearGradient"==r.node.firstChild.tagName||"pattern"==r.node.firstChild.tagName)&&(r=r.node.firstChild,d(this).appendChild(r),r=l(r)),r instanceof n)if("radialGradient"==r.type||"linearGradient"==r.type||"pattern"==r.type){r.node.id||m(r.node,{id:r.id});var a=y(r.node.id)}else a=r.attr(i);else if(a=t.color(r),a.error){var s=t(d(this).ownerSVGElement).gradient(r);s?(s.node.id||m(s.node,{id:s.id}),a=y(s.node.id)):a=r}else a=v(a);var c={};c[i]=a,m(this.node,c),this.node.style[i]=x}}function s(t){e.stop(),t==+t&&(t+="px"),this.node.style.fontSize=t}function c(t){for(var e=[],n=t.childNodes,i=0,r=n.length;r>i;i++){var o=n[i];3==o.nodeType&&e.push(o.nodeValue),"tspan"==o.tagName&&(1==o.childNodes.length&&3==o.firstChild.nodeType?e.push(o.firstChild.nodeValue):e.push(c(o)))}return e}function u(){return e.stop(),this.node.style.fontSize}var p=t._.make,l=t._.wrap,h=t.is,d=t._.getSomeDefs,f=/^url\(#?([^)]+)\)$/,m=t._.$,y=t.url,v=String,g=t._.separator,x="";e.on("snap.util.attr.mask",function(t){if(t instanceof n||t instanceof o){if(e.stop(),t instanceof o&&1==t.node.childNodes.length&&(t=t.node.firstChild,d(this).appendChild(t),t=l(t)),"mask"==t.type)var i=t;else i=p("mask",d(this)),i.node.appendChild(t.node);!i.node.id&&m(i.node,{id:i.id}),m(this.node,{mask:y(i.id)})}}),function(t){e.on("snap.util.attr.clip",t),e.on("snap.util.attr.clip-path",t),e.on("snap.util.attr.clipPath",t)}(function(t){if(t instanceof n||t instanceof o){if(e.stop(),"clipPath"==t.type)var i=t;else i=p("clipPath",d(this)),i.node.appendChild(t.node),!i.node.id&&m(i.node,{id:i.id});m(this.node,{"clip-path":y(i.node.id||i.id)})}}),e.on("snap.util.attr.fill",a("fill")),e.on("snap.util.attr.stroke",a("stroke"));var b=/^([lr])(?:\(([^)]*)\))?(.*)$/i;e.on("snap.util.grad.parse",function(t){t=v(t);var e=t.match(b);if(!e)return null;var n=e[1],i=e[2],r=e[3];return i=i.split(/\s*,\s*/).map(function(t){return+t==t?+t:t}),1==i.length&&0==i[0]&&(i=[]),r=r.split("-"),r=r.map(function(t){t=t.split(":");var e={color:t[0]};return t[1]&&(e.offset=parseFloat(t[1])),e}),{type:n,params:i,stops:r}}),e.on("snap.util.attr.d",function(n){e.stop(),h(n,"array")&&h(n[0],"array")&&(n=t.path.toString.call(n)),n=v(n),n.match(/[ruo]/i)&&(n=t.path.toAbsolute(n)),m(this.node,{d:n})})(-1),e.on("snap.util.attr.#text",function(t){e.stop(),t=v(t);for(var n=r.doc.createTextNode(t);this.node.firstChild;)this.node.removeChild(this.node.firstChild);this.node.appendChild(n)})(-1),e.on("snap.util.attr.path",function(t){e.stop(),this.attr({d:t})})(-1),e.on("snap.util.attr.class",function(t){e.stop(),this.node.className.baseVal=t})(-1),e.on("snap.util.attr.viewBox",function(t){var n;n=h(t,"object")&&"x"in t?[t.x,t.y,t.width,t.height].join(" "):h(t,"array")?t.join(" "):t,m(this.node,{viewBox:n}),e.stop()})(-1),e.on("snap.util.attr.transform",function(t){this.transform(t),e.stop()})(-1),e.on("snap.util.attr.r",function(t){"rect"==this.type&&(e.stop(),m(this.node,{rx:t,ry:t}))})(-1),e.on("snap.util.attr.textpath",function(t){if(e.stop(),"text"==this.type){var i,r,o;if(!t&&this.textPath){for(r=this.textPath;r.node.firstChild;)this.node.appendChild(r.node.firstChild);return r.remove(),void delete this.textPath}if(h(t,"string")){var a=d(this),s=l(a.parentNode).path(t);a.appendChild(s.node),i=s.id,s.attr({id:i})}else t=l(t),t instanceof n&&(i=t.attr("id"),i||(i=t.id,t.attr({id:i})));if(i)if(r=this.textPath,o=this.node,r)r.attr({"xlink:href":"#"+i});else{for(r=m("textPath",{"xlink:href":"#"+i});o.firstChild;)r.appendChild(o.firstChild);o.appendChild(r),this.textPath=l(r)}}})(-1),e.on("snap.util.attr.text",function(t){if("text"==this.type){for(var n=this.node,i=function(t){var e=m("tspan");if(h(t,"array"))for(var n=0;n<t.length;n++)e.appendChild(i(t[n]));else e.appendChild(r.doc.createTextNode(t));return e.normalize&&e.normalize(),e};n.firstChild;)n.removeChild(n.firstChild);for(var o=i(t);o.firstChild;)n.appendChild(o.firstChild)}e.stop()})(-1),e.on("snap.util.attr.fontSize",s)(-1),e.on("snap.util.attr.font-size",s)(-1),e.on("snap.util.getattr.transform",function(){return e.stop(),this.transform()})(-1),e.on("snap.util.getattr.textpath",function(){return e.stop(),this.textPath})(-1),function(){function n(n){return function(){e.stop();var i=r.doc.defaultView.getComputedStyle(this.node,null).getPropertyValue("marker-"+n);return"none"==i?i:t(r.doc.getElementById(i.match(f)[1]))}}function i(t){return function(n){e.stop();var i="marker"+t.charAt(0).toUpperCase()+t.substring(1);if(""==n||!n)return void(this.node.style[i]="none");if("marker"==n.type){var r=n.node.id;return r||m(n.node,{id:n.id}),void(this.node.style[i]=y(r))}}}e.on("snap.util.getattr.marker-end",n("end"))(-1),e.on("snap.util.getattr.markerEnd",n("end"))(-1),e.on("snap.util.getattr.marker-start",n("start"))(-1),e.on("snap.util.getattr.markerStart",n("start"))(-1),e.on("snap.util.getattr.marker-mid",n("mid"))(-1),e.on("snap.util.getattr.markerMid",n("mid"))(-1),e.on("snap.util.attr.marker-end",i("end"))(-1),e.on("snap.util.attr.markerEnd",i("end"))(-1),e.on("snap.util.attr.marker-start",i("start"))(-1),e.on("snap.util.attr.markerStart",i("start"))(-1),e.on("snap.util.attr.marker-mid",i("mid"))(-1),e.on("snap.util.attr.markerMid",i("mid"))(-1)}(),e.on("snap.util.getattr.r",function(){return"rect"==this.type&&m(this.node,"rx")==m(this.node,"ry")?(e.stop(),m(this.node,"rx")):void 0})(-1),e.on("snap.util.getattr.text",function(){if("text"==this.type||"tspan"==this.type){e.stop();var t=c(this.node);return 1==t.length?t[0]:t}})(-1),e.on("snap.util.getattr.#text",function(){return this.node.textContent})(-1),e.on("snap.util.getattr.viewBox",function(){e.stop();var n=m(this.node,"viewBox");return n?(n=n.split(g),t._.box(+n[0],+n[1],+n[2],+n[3])):void 0})(-1),e.on("snap.util.getattr.points",function(){var t=m(this.node,"points");return e.stop(),t?t.split(g):void 0})(-1),e.on("snap.util.getattr.path",function(){var t=m(this.node,"d");return e.stop(),t})(-1),e.on("snap.util.getattr.class",function(){return this.node.className.baseVal})(-1),e.on("snap.util.getattr.fontSize",u)(-1),e.on("snap.util.getattr.font-size",u)(-1)}),i.plugin(function(n,i,r,o,a){var s=r.prototype,c=n.is;s.rect=function(t,e,n,i,r,o){var a;return null==o&&(o=r),c(t,"object")&&"[object Object]"==t?a=t:null!=t&&(a={x:t,y:e,width:n,height:i},null!=r&&(a.rx=r,a.ry=o)),this.el("rect",a)},s.circle=function(t,e,n){var i;return c(t,"object")&&"[object Object]"==t?i=t:null!=t&&(i={cx:t,cy:e,r:n}),this.el("circle",i)};var u=function(){function t(){this.parentNode.removeChild(this)}return function(e,n){var i=o.doc.createElement("img"),r=o.doc.body;i.style.cssText="position:absolute;left:-9999em;top:-9999em",i.onload=function(){n.call(i),i.onload=i.onerror=null,r.removeChild(i)},i.onerror=t,r.appendChild(i),i.src=e}}();s.image=function(t,e,i,r,o){var a=this.el("image");if(c(t,"object")&&"src"in t)a.attr(t);else if(null!=t){var s={"xlink:href":t,preserveAspectRatio:"none"};null!=e&&null!=i&&(s.x=e,s.y=i),null!=r&&null!=o?(s.width=r,s.height=o):u(t,function(){n._.$(a.node,{width:this.offsetWidth,height:this.offsetHeight})}),n._.$(a.node,s)}return a},s.ellipse=function(t,e,n,i){var r;return c(t,"object")&&"[object Object]"==t?r=t:null!=t&&(r={cx:t,cy:e,rx:n,ry:i}),this.el("ellipse",r)},s.path=function(t){var e;return c(t,"object")&&!c(t,"array")?e=t:t&&(e={d:t}),this.el("path",e)},s.group=s.g=function(t){var e=this.el("g");return 1==arguments.length&&t&&!t.type?e.attr(t):arguments.length&&e.add(Array.prototype.slice.call(arguments,0)),e},s.svg=function(t,e,n,i,r,o,a,s){var u={};return c(t,"object")&&null==e?u=t:(null!=t&&(u.x=t),null!=e&&(u.y=e),null!=n&&(u.width=n),null!=i&&(u.height=i),null!=r&&null!=o&&null!=a&&null!=s&&(u.viewBox=[r,o,a,s])),this.el("svg",u)},s.mask=function(t){var e=this.el("mask");return 1==arguments.length&&t&&!t.type?e.attr(t):arguments.length&&e.add(Array.prototype.slice.call(arguments,0)),e},s.ptrn=function(t,e,n,i,r,o,a,s){if(c(t,"object"))var u=t;else u={patternUnits:"userSpaceOnUse"},t&&(u.x=t),e&&(u.y=e),null!=n&&(u.width=n),null!=i&&(u.height=i),null!=r&&null!=o&&null!=a&&null!=s&&(u.viewBox=[r,o,a,s]);return this.el("pattern",u)},s.use=function(t){return null!=t?(t instanceof i&&(t.attr("id")||t.attr({id:n._.id(t)}),t=t.attr("id")),"#"==String(t).charAt()&&(t=t.substring(1)),this.el("use",{"xlink:href":"#"+t})):i.prototype.use.call(this)},s.symbol=function(t,e,n,i){var r={};return null!=t&&null!=e&&null!=n&&null!=i&&(r.viewBox=[t,e,n,i]),this.el("symbol",r)},s.text=function(t,e,n){var i={};return c(t,"object")?i=t:null!=t&&(i={x:t,y:e,text:n||""}),this.el("text",i)},s.line=function(t,e,n,i){var r={};return c(t,"object")?r=t:null!=t&&(r={x1:t,x2:n,y1:e,y2:i}),this.el("line",r)},s.polyline=function(t){arguments.length>1&&(t=Array.prototype.slice.call(arguments,0));var e={};return c(t,"object")&&!c(t,"array")?e=t:null!=t&&(e={points:t}),this.el("polyline",e)},s.polygon=function(t){arguments.length>1&&(t=Array.prototype.slice.call(arguments,0));var e={};return c(t,"object")&&!c(t,"array")?e=t:null!=t&&(e={points:t}),this.el("polygon",e)},function(){function i(){return this.selectAll("stop")}function r(t,e){var i=p("stop"),r={offset:+e+"%"};return t=n.color(t),r["stop-color"]=t.hex,t.opacity<1&&(r["stop-opacity"]=t.opacity),p(i,r),this.node.appendChild(i),this}function o(){if("linearGradient"==this.type){var t=p(this.node,"x1")||0,e=p(this.node,"x2")||1,i=p(this.node,"y1")||0,r=p(this.node,"y2")||0;return n._.box(t,i,math.abs(e-t),math.abs(r-i))}var o=this.node.cx||.5,a=this.node.cy||.5,s=this.node.r||0;return n._.box(o-s,a-s,2*s,2*s)}function a(t,n){function i(t,e){for(var n=(e-l)/(t-h),i=h;t>i;i++)a[i].offset=+(+l+n*(i-h)).toFixed(2);h=t,l=e}var r,o=e("snap.util.grad.parse",null,n).firstDefined();if(!o)return null;o.params.unshift(t),r="l"==o.type.toLowerCase()?c.apply(0,o.params):u.apply(0,o.params),o.type!=o.type.toLowerCase()&&p(r.node,{gradientUnits:"userSpaceOnUse"});var a=o.stops,s=a.length,l=0,h=0;s--;for(var d=0;s>d;d++)"offset"in a[d]&&i(d,a[d].offset);for(a[s].offset=a[s].offset||100,i(s,a[s].offset),d=0;s>=d;d++){var f=a[d];r.addStop(f.color,f.offset)}return r}function c(t,e,a,s,c){var u=n._.make("linearGradient",t);return u.stops=i,u.addStop=r,u.getBBox=o,null!=e&&p(u.node,{x1:e,y1:a,x2:s,y2:c}),u}function u(t,e,a,s,c,u){var l=n._.make("radialGradient",t);return l.stops=i,l.addStop=r,l.getBBox=o,null!=e&&p(l.node,{cx:e,cy:a,r:s}),null!=c&&null!=u&&p(l.node,{fx:c,fy:u}),l}var p=n._.$;s.gradient=function(t){return a(this.defs,t)},s.gradientLinear=function(t,e,n,i){return c(this.defs,t,e,n,i)},s.gradientRadial=function(t,e,n,i,r){return u(this.defs,t,e,n,i,r)},s.toString=function(){var t,e=this.node.ownerDocument,i=e.createDocumentFragment(),r=e.createElement("div"),o=this.node.cloneNode(!0);return i.appendChild(r),r.appendChild(o),n._.$(o,{xmlns:"http://www.w3.org/2000/svg"}),t=r.innerHTML,i.removeChild(i.firstChild),t},s.toDataURL=function(){return t&&t.btoa?"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(this))):void 0},s.clear=function(){for(var t,e=this.node.firstChild;e;)t=e.nextSibling,"defs"!=e.tagName?e.parentNode.removeChild(e):s.clear.call({node:e}),e=t}}()}),i.plugin(function(t,e,n,i){function r(t){var e=r.ps=r.ps||{};return e[t]?e[t].sleep=100:e[t]={sleep:100},setTimeout(function(){for(var n in e)e[j](n)&&n!=t&&(e[n].sleep--,!e[n].sleep&&delete e[n])}),e[t]}function o(t,e,n,i){return null==t&&(t=e=n=i=0),null==e&&(e=t.y,n=t.width,i=t.height,t=t.x),{x:t,y:e,width:n,w:n,height:i,h:i,x2:t+n,y2:e+i,cx:t+n/2,cy:e+i/2,r1:V.min(n,i)/2,r2:V.max(n,i)/2,r0:V.sqrt(n*n+i*i)/2,path:S(t,e,n,i),vb:[t,e,n,i].join(" ")}}function a(){return this.join(",").replace(F,"$1")}function s(t){var e=L(t);return e.toString=a,e}function c(t,e,n,i,r,o,a,s,c){return null==c?m(t,e,n,i,r,o,a,s):p(t,e,n,i,r,o,a,s,y(t,e,n,i,r,o,a,s,c))}function u(n,i){function r(t){return+(+t).toFixed(3)}return t._.cacher(function(t,o,a){t instanceof e&&(t=t.attr("d")),t=D(t);for(var s,u,l,h,d,f="",m={},y=0,v=0,g=t.length;g>v;v++){if(l=t[v],"M"==l[0])s=+l[1],u=+l[2];else{if(h=c(s,u,l[1],l[2],l[3],l[4],l[5],l[6]),y+h>o){if(i&&!m.start){if(d=c(s,u,l[1],l[2],l[3],l[4],l[5],l[6],o-y),f+=["C"+r(d.start.x),r(d.start.y),r(d.m.x),r(d.m.y),r(d.x),r(d.y)],a)return f;m.start=f,f=["M"+r(d.x),r(d.y)+"C"+r(d.n.x),r(d.n.y),r(d.end.x),r(d.end.y),r(l[5]),r(l[6])].join(),y+=h,s=+l[5],u=+l[6];continue}if(!n&&!i)return d=c(s,u,l[1],l[2],l[3],l[4],l[5],l[6],o-y)}y+=h,s=+l[5],u=+l[6]}f+=l.shift()+l}return m.end=f,d=n?y:i?m:p(s,u,l[0],l[1],l[2],l[3],l[4],l[5],1)},null,t._.clone)}function p(t,e,n,i,r,o,a,s,c){var u=1-c,p=U(u,3),l=U(u,2),h=c*c,d=h*c,f=p*t+3*l*c*n+3*u*c*c*r+d*a,m=p*e+3*l*c*i+3*u*c*c*o+d*s,y=t+2*c*(n-t)+h*(r-2*n+t),v=e+2*c*(i-e)+h*(o-2*i+e),g=n+2*c*(r-n)+h*(a-2*r+n),x=i+2*c*(o-i)+h*(s-2*o+i),b=u*t+c*n,E=u*e+c*i,w=u*r+c*a,_=u*o+c*s,S=90-180*V.atan2(y-g,v-x)/$;return{x:f,y:m,m:{x:y,y:v},n:{x:g,y:x},start:{x:b,y:E},end:{x:w,y:_},alpha:S}}function l(e,n,i,r,a,s,c,u){t.is(e,"array")||(e=[e,n,i,r,a,s,c,u]);var p=P.apply(null,e);return o(p.min.x,p.min.y,p.max.x-p.min.x,p.max.y-p.min.y)}function h(t,e,n){return e>=t.x&&e<=t.x+t.width&&n>=t.y&&n<=t.y+t.height}function d(t,e){return t=o(t),e=o(e),h(e,t.x,t.y)||h(e,t.x2,t.y)||h(e,t.x,t.y2)||h(e,t.x2,t.y2)||h(t,e.x,e.y)||h(t,e.x2,e.y)||h(t,e.x,e.y2)||h(t,e.x2,e.y2)||(t.x<e.x2&&t.x>e.x||e.x<t.x2&&e.x>t.x)&&(t.y<e.y2&&t.y>e.y||e.y<t.y2&&e.y>t.y)}function f(t,e,n,i,r){var o=-3*e+9*n-9*i+3*r,a=t*o+6*e-12*n+6*i;return t*a-3*e+3*n}function m(t,e,n,i,r,o,a,s,c){null==c&&(c=1),c=c>1?1:0>c?0:c;for(var u=c/2,p=12,l=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],h=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],d=0,m=0;p>m;m++){var y=u*l[m]+u,v=f(y,t,n,r,a),g=f(y,e,i,o,s),x=v*v+g*g;d+=h[m]*V.sqrt(x)}return u*d}function y(t,e,n,i,r,o,a,s,c){if(!(0>c||m(t,e,n,i,r,o,a,s)<c)){var u,p=1,l=p/2,h=p-l,d=.01;for(u=m(t,e,n,i,r,o,a,s,h);q(u-c)>d;)l/=2,h+=(c>u?1:-1)*l,u=m(t,e,n,i,r,o,a,s,h);return h}}function v(t,e,n,i,r,o,a,s){if(!(W(t,n)<G(r,a)||G(t,n)>W(r,a)||W(e,i)<G(o,s)||G(e,i)>W(o,s))){var c=(t*i-e*n)*(r-a)-(t-n)*(r*s-o*a),u=(t*i-e*n)*(o-s)-(e-i)*(r*s-o*a),p=(t-n)*(o-s)-(e-i)*(r-a);if(p){var l=c/p,h=u/p,d=+l.toFixed(2),f=+h.toFixed(2);if(!(d<+G(t,n).toFixed(2)||d>+W(t,n).toFixed(2)||d<+G(r,a).toFixed(2)||d>+W(r,a).toFixed(2)||f<+G(e,i).toFixed(2)||f>+W(e,i).toFixed(2)||f<+G(o,s).toFixed(2)||f>+W(o,s).toFixed(2)))return{x:l,y:h}}}}function g(t,e,n){var i=l(t),r=l(e);if(!d(i,r))return n?0:[];for(var o=m.apply(0,t),a=m.apply(0,e),s=~~(o/8),c=~~(a/8),u=[],h=[],f={},y=n?0:[],g=0;s+1>g;g++){var x=p.apply(0,t.concat(g/s));u.push({x:x.x,y:x.y,t:g/s})}for(g=0;c+1>g;g++)x=p.apply(0,e.concat(g/c)),h.push({x:x.x,y:x.y,t:g/c});for(g=0;s>g;g++)for(var b=0;c>b;b++){var E=u[g],w=u[g+1],_=h[b],S=h[b+1],A=q(w.x-E.x)<.001?"y":"x",T=q(S.x-_.x)<.001?"y":"x",C=v(E.x,E.y,w.x,w.y,_.x,_.y,S.x,S.y);if(C){if(f[C.x.toFixed(4)]==C.y.toFixed(4))continue;f[C.x.toFixed(4)]=C.y.toFixed(4);var N=E.t+q((C[A]-E[A])/(w[A]-E[A]))*(w.t-E.t),M=_.t+q((C[T]-_[T])/(S[T]-_[T]))*(S.t-_.t);N>=0&&1>=N&&M>=0&&1>=M&&(n?y++:y.push({x:C.x,y:C.y,t1:N,t2:M}))}}return y}function x(t,e){return E(t,e)}function b(t,e){return E(t,e,1)}function E(t,e,n){t=D(t),e=D(e);for(var i,r,o,a,s,c,u,p,l,h,d=n?0:[],f=0,m=t.length;m>f;f++){var y=t[f];if("M"==y[0])i=s=y[1],r=c=y[2];else{"C"==y[0]?(l=[i,r].concat(y.slice(1)),i=l[6],r=l[7]):(l=[i,r,i,r,s,c,s,c],i=s,r=c);for(var v=0,x=e.length;x>v;v++){var b=e[v];if("M"==b[0])o=u=b[1],a=p=b[2];else{"C"==b[0]?(h=[o,a].concat(b.slice(1)),o=h[6],a=h[7]):(h=[o,a,o,a,u,p,u,p],o=u,a=p);var E=g(l,h,n);if(n)d+=E;else{for(var w=0,_=E.length;_>w;w++)E[w].segment1=f,E[w].segment2=v,E[w].bez1=l,E[w].bez2=h;d=d.concat(E)}}}}}return d}function w(t,e,n){var i=_(t);return h(i,e,n)&&E(t,[["M",e,n],["H",i.x2+10]],1)%2==1}function _(t){var e=r(t);if(e.bbox)return L(e.bbox);if(!t)return o();t=D(t);for(var n,i=0,a=0,s=[],c=[],u=0,p=t.length;p>u;u++)if(n=t[u],"M"==n[0])i=n[1],a=n[2],s.push(i),c.push(a);else{var l=P(i,a,n[1],n[2],n[3],n[4],n[5],n[6]);s=s.concat(l.min.x,l.max.x),c=c.concat(l.min.y,l.max.y),i=n[5],a=n[6]}var h=G.apply(0,s),d=G.apply(0,c),f=W.apply(0,s),m=W.apply(0,c),y=o(h,d,f-h,m-d);return e.bbox=L(y),y}function S(t,e,n,i,r){if(r)return[["M",+t+ +r,e],["l",n-2*r,0],["a",r,r,0,0,1,r,r],["l",0,i-2*r],["a",r,r,0,0,1,-r,r],["l",2*r-n,0],["a",r,r,0,0,1,-r,-r],["l",0,2*r-i],["a",r,r,0,0,1,r,-r],["z"]];var o=[["M",t,e],["l",n,0],["l",0,i],["l",-n,0],["z"]];return o.toString=a,o}function A(t,e,n,i,r){if(null==r&&null==i&&(i=n),t=+t,e=+e,n=+n,i=+i,null!=r)var o=Math.PI/180,s=t+n*Math.cos(-i*o),c=t+n*Math.cos(-r*o),u=e+n*Math.sin(-i*o),p=e+n*Math.sin(-r*o),l=[["M",s,u],["A",n,n,0,+(r-i>180),0,c,p]];else l=[["M",t,e],["m",0,-i],["a",n,i,0,1,1,0,2*i],["a",n,i,0,1,1,0,-2*i],["z"]];return l.toString=a,l}function T(e){var n=r(e),i=String.prototype.toLowerCase;if(n.rel)return s(n.rel);t.is(e,"array")&&t.is(e&&e[0],"array")||(e=t.parsePathString(e));var o=[],c=0,u=0,p=0,l=0,h=0;"M"==e[0][0]&&(c=e[0][1],u=e[0][2],p=c,l=u,h++,o.push(["M",c,u]));for(var d=h,f=e.length;f>d;d++){var m=o[d]=[],y=e[d];if(y[0]!=i.call(y[0]))switch(m[0]=i.call(y[0]),m[0]){case"a":m[1]=y[1],m[2]=y[2],m[3]=y[3],m[4]=y[4],m[5]=y[5],m[6]=+(y[6]-c).toFixed(3),m[7]=+(y[7]-u).toFixed(3);break;case"v":m[1]=+(y[1]-u).toFixed(3);break;case"m":p=y[1],l=y[2];default:for(var v=1,g=y.length;g>v;v++)m[v]=+(y[v]-(v%2?c:u)).toFixed(3)}else{m=o[d]=[],"m"==y[0]&&(p=y[1]+c,l=y[2]+u);for(var x=0,b=y.length;b>x;x++)o[d][x]=y[x]}var E=o[d].length;switch(o[d][0]){case"z":c=p,u=l;break;case"h":c+=+o[d][E-1];break;case"v":u+=+o[d][E-1];break;default:c+=+o[d][E-2],u+=+o[d][E-1]}}return o.toString=a,n.rel=s(o),o}function C(e){var n=r(e);if(n.abs)return s(n.abs);if(I(e,"array")&&I(e&&e[0],"array")||(e=t.parsePathString(e)),!e||!e.length)return[["M",0,0]];
var i,o=[],c=0,u=0,p=0,l=0,h=0;"M"==e[0][0]&&(c=+e[0][1],u=+e[0][2],p=c,l=u,h++,o[0]=["M",c,u]);for(var d,f,m=3==e.length&&"M"==e[0][0]&&"R"==e[1][0].toUpperCase()&&"Z"==e[2][0].toUpperCase(),y=h,v=e.length;v>y;y++){if(o.push(d=[]),f=e[y],i=f[0],i!=i.toUpperCase())switch(d[0]=i.toUpperCase(),d[0]){case"A":d[1]=f[1],d[2]=f[2],d[3]=f[3],d[4]=f[4],d[5]=f[5],d[6]=+f[6]+c,d[7]=+f[7]+u;break;case"V":d[1]=+f[1]+u;break;case"H":d[1]=+f[1]+c;break;case"R":for(var g=[c,u].concat(f.slice(1)),x=2,b=g.length;b>x;x++)g[x]=+g[x]+c,g[++x]=+g[x]+u;o.pop(),o=o.concat(B(g,m));break;case"O":o.pop(),g=A(c,u,f[1],f[2]),g.push(g[0]),o=o.concat(g);break;case"U":o.pop(),o=o.concat(A(c,u,f[1],f[2],f[3])),d=["U"].concat(o[o.length-1].slice(-2));break;case"M":p=+f[1]+c,l=+f[2]+u;default:for(x=1,b=f.length;b>x;x++)d[x]=+f[x]+(x%2?c:u)}else if("R"==i)g=[c,u].concat(f.slice(1)),o.pop(),o=o.concat(B(g,m)),d=["R"].concat(f.slice(-2));else if("O"==i)o.pop(),g=A(c,u,f[1],f[2]),g.push(g[0]),o=o.concat(g);else if("U"==i)o.pop(),o=o.concat(A(c,u,f[1],f[2],f[3])),d=["U"].concat(o[o.length-1].slice(-2));else for(var E=0,w=f.length;w>E;E++)d[E]=f[E];if(i=i.toUpperCase(),"O"!=i)switch(d[0]){case"Z":c=+p,u=+l;break;case"H":c=d[1];break;case"V":u=d[1];break;case"M":p=d[d.length-2],l=d[d.length-1];default:c=d[d.length-2],u=d[d.length-1]}}return o.toString=a,n.abs=s(o),o}function N(t,e,n,i){return[t,e,n,i,n,i]}function M(t,e,n,i,r,o){var a=1/3,s=2/3;return[a*t+s*n,a*e+s*i,a*r+s*n,a*o+s*i,r,o]}function R(e,n,i,r,o,a,s,c,u,p){var l,h=120*$/180,d=$/180*(+o||0),f=[],m=t._.cacher(function(t,e,n){var i=t*V.cos(n)-e*V.sin(n),r=t*V.sin(n)+e*V.cos(n);return{x:i,y:r}});if(p)S=p[0],A=p[1],w=p[2],_=p[3];else{l=m(e,n,-d),e=l.x,n=l.y,l=m(c,u,-d),c=l.x,u=l.y;var y=(V.cos($/180*o),V.sin($/180*o),(e-c)/2),v=(n-u)/2,g=y*y/(i*i)+v*v/(r*r);g>1&&(g=V.sqrt(g),i=g*i,r=g*r);var x=i*i,b=r*r,E=(a==s?-1:1)*V.sqrt(q((x*b-x*v*v-b*y*y)/(x*v*v+b*y*y))),w=E*i*v/r+(e+c)/2,_=E*-r*y/i+(n+u)/2,S=V.asin(((n-_)/r).toFixed(9)),A=V.asin(((u-_)/r).toFixed(9));S=w>e?$-S:S,A=w>c?$-A:A,0>S&&(S=2*$+S),0>A&&(A=2*$+A),s&&S>A&&(S-=2*$),!s&&A>S&&(A-=2*$)}var T=A-S;if(q(T)>h){var C=A,N=c,M=u;A=S+h*(s&&A>S?1:-1),c=w+i*V.cos(A),u=_+r*V.sin(A),f=R(c,u,i,r,o,0,s,N,M,[A,C,w,_])}T=A-S;var P=V.cos(S),D=V.sin(S),k=V.cos(A),B=V.sin(A),O=V.tan(T/4),I=4/3*i*O,L=4/3*r*O,j=[e,n],F=[e+I*D,n-L*P],z=[c+I*B,u-L*k],G=[c,u];if(F[0]=2*j[0]-F[0],F[1]=2*j[1]-F[1],p)return[F,z,G].concat(f);f=[F,z,G].concat(f).join().split(",");for(var W=[],U=0,H=f.length;H>U;U++)W[U]=U%2?m(f[U-1],f[U],d).y:m(f[U],f[U+1],d).x;return W}function P(t,e,n,i,r,o,a,s){for(var c,u,p,l,h,d,f,m,y=[],v=[[],[]],g=0;2>g;++g)if(0==g?(u=6*t-12*n+6*r,c=-3*t+9*n-9*r+3*a,p=3*n-3*t):(u=6*e-12*i+6*o,c=-3*e+9*i-9*o+3*s,p=3*i-3*e),q(c)<1e-12){if(q(u)<1e-12)continue;l=-p/u,l>0&&1>l&&y.push(l)}else f=u*u-4*p*c,m=V.sqrt(f),0>f||(h=(-u+m)/(2*c),h>0&&1>h&&y.push(h),d=(-u-m)/(2*c),d>0&&1>d&&y.push(d));for(var x,b=y.length,E=b;b--;)l=y[b],x=1-l,v[0][b]=x*x*x*t+3*x*x*l*n+3*x*l*l*r+l*l*l*a,v[1][b]=x*x*x*e+3*x*x*l*i+3*x*l*l*o+l*l*l*s;return v[0][E]=t,v[1][E]=e,v[0][E+1]=a,v[1][E+1]=s,v[0].length=v[1].length=E+2,{min:{x:G.apply(0,v[0]),y:G.apply(0,v[1])},max:{x:W.apply(0,v[0]),y:W.apply(0,v[1])}}}function D(t,e){var n=!e&&r(t);if(!e&&n.curve)return s(n.curve);for(var i=C(t),o=e&&C(e),a={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},c={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},u=(function(t,e,n){var i,r;if(!t)return["C",e.x,e.y,e.x,e.y,e.x,e.y];switch(!(t[0]in{T:1,Q:1})&&(e.qx=e.qy=null),t[0]){case"M":e.X=t[1],e.Y=t[2];break;case"A":t=["C"].concat(R.apply(0,[e.x,e.y].concat(t.slice(1))));break;case"S":"C"==n||"S"==n?(i=2*e.x-e.bx,r=2*e.y-e.by):(i=e.x,r=e.y),t=["C",i,r].concat(t.slice(1));break;case"T":"Q"==n||"T"==n?(e.qx=2*e.x-e.qx,e.qy=2*e.y-e.qy):(e.qx=e.x,e.qy=e.y),t=["C"].concat(M(e.x,e.y,e.qx,e.qy,t[1],t[2]));break;case"Q":e.qx=t[1],e.qy=t[2],t=["C"].concat(M(e.x,e.y,t[1],t[2],t[3],t[4]));break;case"L":t=["C"].concat(N(e.x,e.y,t[1],t[2]));break;case"H":t=["C"].concat(N(e.x,e.y,t[1],e.y));break;case"V":t=["C"].concat(N(e.x,e.y,e.x,t[1]));break;case"Z":t=["C"].concat(N(e.x,e.y,e.X,e.Y))}return t}),p=function(t,e){if(t[e].length>7){t[e].shift();for(var n=t[e];n.length;)h[e]="A",o&&(d[e]="A"),t.splice(e++,0,["C"].concat(n.splice(0,6)));t.splice(e,1),v=W(i.length,o&&o.length||0)}},l=function(t,e,n,r,a){t&&e&&"M"==t[a][0]&&"M"!=e[a][0]&&(e.splice(a,0,["M",r.x,r.y]),n.bx=0,n.by=0,n.x=t[a][1],n.y=t[a][2],v=W(i.length,o&&o.length||0))},h=[],d=[],f="",m="",y=0,v=W(i.length,o&&o.length||0);v>y;y++){i[y]&&(f=i[y][0]),"C"!=f&&(h[y]=f,y&&(m=h[y-1])),i[y]=u(i[y],a,m),"A"!=h[y]&&"C"==f&&(h[y]="C"),p(i,y),o&&(o[y]&&(f=o[y][0]),"C"!=f&&(d[y]=f,y&&(m=d[y-1])),o[y]=u(o[y],c,m),"A"!=d[y]&&"C"==f&&(d[y]="C"),p(o,y)),l(i,o,a,c,y),l(o,i,c,a,y);var g=i[y],x=o&&o[y],b=g.length,E=o&&x.length;a.x=g[b-2],a.y=g[b-1],a.bx=z(g[b-4])||a.x,a.by=z(g[b-3])||a.y,c.bx=o&&(z(x[E-4])||c.x),c.by=o&&(z(x[E-3])||c.y),c.x=o&&x[E-2],c.y=o&&x[E-1]}return o||(n.curve=s(i)),o?[i,o]:i}function k(t,e){if(!e)return t;var n,i,r,o,a,s,c;for(t=D(t),r=0,a=t.length;a>r;r++)for(c=t[r],o=1,s=c.length;s>o;o+=2)n=e.x(c[o],c[o+1]),i=e.y(c[o],c[o+1]),c[o]=n,c[o+1]=i;return t}function B(t,e){for(var n=[],i=0,r=t.length;r-2*!e>i;i+=2){var o=[{x:+t[i-2],y:+t[i-1]},{x:+t[i],y:+t[i+1]},{x:+t[i+2],y:+t[i+3]},{x:+t[i+4],y:+t[i+5]}];e?i?r-4==i?o[3]={x:+t[0],y:+t[1]}:r-2==i&&(o[2]={x:+t[0],y:+t[1]},o[3]={x:+t[2],y:+t[3]}):o[0]={x:+t[r-2],y:+t[r-1]}:r-4==i?o[3]=o[2]:i||(o[0]={x:+t[i],y:+t[i+1]}),n.push(["C",(-o[0].x+6*o[1].x+o[2].x)/6,(-o[0].y+6*o[1].y+o[2].y)/6,(o[1].x+6*o[2].x-o[3].x)/6,(o[1].y+6*o[2].y-o[3].y)/6,o[2].x,o[2].y])}return n}var O=e.prototype,I=t.is,L=t._.clone,j="hasOwnProperty",F=/,?([a-z]),?/gi,z=parseFloat,V=Math,$=V.PI,G=V.min,W=V.max,U=V.pow,q=V.abs,H=u(1),Y=u(),X=u(0,1),K=t._unit2px,Z={path:function(t){return t.attr("path")},circle:function(t){var e=K(t);return A(e.cx,e.cy,e.r)},ellipse:function(t){var e=K(t);return A(e.cx||0,e.cy||0,e.rx,e.ry)},rect:function(t){var e=K(t);return S(e.x||0,e.y||0,e.width,e.height,e.rx,e.ry)},image:function(t){var e=K(t);return S(e.x||0,e.y||0,e.width,e.height)},line:function(t){return"M"+[t.attr("x1")||0,t.attr("y1")||0,t.attr("x2"),t.attr("y2")]},polyline:function(t){return"M"+t.attr("points")},polygon:function(t){return"M"+t.attr("points")+"z"},deflt:function(t){var e=t.node.getBBox();return S(e.x,e.y,e.width,e.height)}};t.path=r,t.path.getTotalLength=H,t.path.getPointAtLength=Y,t.path.getSubpath=function(t,e,n){if(this.getTotalLength(t)-n<1e-6)return X(t,e).end;var i=X(t,n,1);return e?X(i,e).end:i},O.getTotalLength=function(){return this.node.getTotalLength?this.node.getTotalLength():void 0},O.getPointAtLength=function(t){return Y(this.attr("d"),t)},O.getSubpath=function(e,n){return t.path.getSubpath(this.attr("d"),e,n)},t._.box=o,t.path.findDotsAtSegment=p,t.path.bezierBBox=l,t.path.isPointInsideBBox=h,t.path.isBBoxIntersect=d,t.path.intersection=x,t.path.intersectionNumber=b,t.path.isPointInside=w,t.path.getBBox=_,t.path.get=Z,t.path.toRelative=T,t.path.toAbsolute=C,t.path.toCubic=D,t.path.map=k,t.path.toString=a,t.path.clone=s}),i.plugin(function(t,n,i,r){for(var o=n.prototype,a="hasOwnProperty",s=("createTouch"in r.doc),c=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","touchstart","touchmove","touchend","touchcancel"],u={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},p=(function(t,e){var n="y"==t?"scrollTop":"scrollLeft",i=e&&e.node?e.node.ownerDocument:r.doc;return i[n in i.documentElement?"documentElement":"body"][n]}),l=function(){this.returnValue=!1},h=function(){return this.originalEvent.preventDefault()},d=function(){this.cancelBubble=!0},f=function(){return this.originalEvent.stopPropagation()},m=function(){return r.doc.addEventListener?function(t,e,n,i){var r=s&&u[e]?u[e]:e,o=function(r){var o=p("y",i),c=p("x",i);if(s&&u[a](e))for(var l=0,d=r.targetTouches&&r.targetTouches.length;d>l;l++)if(r.targetTouches[l].target==t||t.contains(r.targetTouches[l].target)){var m=r;r=r.targetTouches[l],r.originalEvent=m,r.preventDefault=h,r.stopPropagation=f;break}var y=r.clientX+c,v=r.clientY+o;return n.call(i,r,y,v)};return e!==r&&t.addEventListener(e,o,!1),t.addEventListener(r,o,!1),function(){return e!==r&&t.removeEventListener(e,o,!1),t.removeEventListener(r,o,!1),!0}}:r.doc.attachEvent?function(t,e,n,i){var r=function(t){t=t||i.node.ownerDocument.window.event;var e=p("y",i),r=p("x",i),o=t.clientX+r,a=t.clientY+e;return t.preventDefault=t.preventDefault||l,t.stopPropagation=t.stopPropagation||d,n.call(i,t,o,a)};t.attachEvent("on"+e,r);var o=function(){return t.detachEvent("on"+e,r),!0};return o}:void 0}(),y=[],v=function(t){for(var n,i=t.clientX,r=t.clientY,o=p("y"),a=p("x"),c=y.length;c--;){if(n=y[c],s){for(var u,l=t.touches&&t.touches.length;l--;)if(u=t.touches[l],u.identifier==n.el._drag.id||n.el.node.contains(u.target)){i=u.clientX,r=u.clientY,(t.originalEvent?t.originalEvent:t).preventDefault();break}}else t.preventDefault();var h=n.el.node;h.nextSibling,h.parentNode,h.style.display;i+=a,r+=o,e("snap.drag.move."+n.el.id,n.move_scope||n.el,i-n.el._drag.x,r-n.el._drag.y,i,r,t)}},g=function(n){t.unmousemove(v).unmouseup(g);for(var i,r=y.length;r--;)i=y[r],i.el._drag={},e("snap.drag.end."+i.el.id,i.end_scope||i.start_scope||i.move_scope||i.el,n);y=[]},x=c.length;x--;)!function(e){t[e]=o[e]=function(n,i){return t.is(n,"function")&&(this.events=this.events||[],this.events.push({name:e,f:n,unbind:m(this.node||document,e,n,i||this)})),this},t["un"+e]=o["un"+e]=function(t){for(var n=this.events||[],i=n.length;i--;)if(n[i].name==e&&(n[i].f==t||!t))return n[i].unbind(),n.splice(i,1),!n.length&&delete this.events,this;return this}}(c[x]);o.hover=function(t,e,n,i){return this.mouseover(t,n).mouseout(e,i||n)},o.unhover=function(t,e){return this.unmouseover(t).unmouseout(e)};var b=[];o.drag=function(n,i,r,o,a,s){function c(c,u,p){(c.originalEvent||c).preventDefault(),this._drag.x=u,this._drag.y=p,this._drag.id=c.identifier,!y.length&&t.mousemove(v).mouseup(g),y.push({el:this,move_scope:o,start_scope:a,end_scope:s}),i&&e.on("snap.drag.start."+this.id,i),n&&e.on("snap.drag.move."+this.id,n),r&&e.on("snap.drag.end."+this.id,r),e("snap.drag.start."+this.id,a||o||this,u,p,c)}if(!arguments.length){var u;return this.drag(function(t,e){this.attr({transform:u+(u?"T":"t")+[t,e]})},function(){u=this.transform().local})}return this._drag={},b.push({el:this,start:c}),this.mousedown(c),this},o.undrag=function(){for(var n=b.length;n--;)b[n].el==this&&(this.unmousedown(b[n].start),b.splice(n,1),e.unbind("snap.drag.*."+this.id));return!b.length&&t.unmousemove(v).unmouseup(g),this}}),i.plugin(function(t,n,i,r){var o=(n.prototype,i.prototype),a=/^\s*url\((.+)\)/,s=String,c=t._.$;t.filter={},o.filter=function(e){var i=this;"svg"!=i.type&&(i=i.paper);var r=t.parse(s(e)),o=t._.id(),a=(i.node.offsetWidth,i.node.offsetHeight,c("filter"));return c(a,{id:o,filterUnits:"userSpaceOnUse"}),a.appendChild(r.node),i.defs.appendChild(a),new n(a)},e.on("snap.util.getattr.filter",function(){e.stop();var n=c(this.node,"filter");if(n){var i=s(n).match(a);return i&&t.select(i[1])}}),e.on("snap.util.attr.filter",function(i){if(i instanceof n&&"filter"==i.type){e.stop();var r=i.node.id;r||(c(i.node,{id:i.id}),r=i.id),c(this.node,{filter:t.url(r)})}i&&"none"!=i||(e.stop(),this.node.removeAttribute("filter"))}),t.filter.blur=function(e,n){null==e&&(e=2);var i=null==n?e:[e,n];return t.format('<feGaussianBlur stdDeviation="{def}"/>',{def:i})},t.filter.blur.toString=function(){return this()},t.filter.shadow=function(e,n,i,r,o){return"string"==typeof i&&(r=i,o=r,i=4),"string"!=typeof r&&(o=r,r="#000"),r=r||"#000",null==i&&(i=4),null==o&&(o=1),null==e&&(e=0,n=2),null==n&&(n=e),r=t.color(r),t.format('<feGaussianBlur in="SourceAlpha" stdDeviation="{blur}"/><feOffset dx="{dx}" dy="{dy}" result="offsetblur"/><feFlood flood-color="{color}"/><feComposite in2="offsetblur" operator="in"/><feComponentTransfer><feFuncA type="linear" slope="{opacity}"/></feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge>',{color:r,dx:e,dy:n,blur:i,opacity:o})},t.filter.shadow.toString=function(){return this()},t.filter.grayscale=function(e){return null==e&&(e=1),t.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {b} {h} 0 0 0 0 0 1 0"/>',{a:.2126+.7874*(1-e),b:.7152-.7152*(1-e),c:.0722-.0722*(1-e),d:.2126-.2126*(1-e),e:.7152+.2848*(1-e),f:.0722-.0722*(1-e),g:.2126-.2126*(1-e),h:.0722+.9278*(1-e)})},t.filter.grayscale.toString=function(){return this()},t.filter.sepia=function(e){return null==e&&(e=1),t.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {h} {i} 0 0 0 0 0 1 0"/>',{a:.393+.607*(1-e),b:.769-.769*(1-e),c:.189-.189*(1-e),d:.349-.349*(1-e),e:.686+.314*(1-e),f:.168-.168*(1-e),g:.272-.272*(1-e),h:.534-.534*(1-e),i:.131+.869*(1-e)})},t.filter.sepia.toString=function(){return this()},t.filter.saturate=function(e){return null==e&&(e=1),t.format('<feColorMatrix type="saturate" values="{amount}"/>',{amount:1-e})},t.filter.saturate.toString=function(){return this()},t.filter.hueRotate=function(e){return e=e||0,t.format('<feColorMatrix type="hueRotate" values="{angle}"/>',{angle:e})},t.filter.hueRotate.toString=function(){return this()},t.filter.invert=function(e){return null==e&&(e=1),t.format('<feComponentTransfer><feFuncR type="table" tableValues="{amount} {amount2}"/><feFuncG type="table" tableValues="{amount} {amount2}"/><feFuncB type="table" tableValues="{amount} {amount2}"/></feComponentTransfer>',{amount:e,amount2:1-e})},t.filter.invert.toString=function(){return this()},t.filter.brightness=function(e){return null==e&&(e=1),t.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}"/><feFuncG type="linear" slope="{amount}"/><feFuncB type="linear" slope="{amount}"/></feComponentTransfer>',{amount:e})},t.filter.brightness.toString=function(){return this()},t.filter.contrast=function(e){return null==e&&(e=1),t.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}" intercept="{amount2}"/><feFuncG type="linear" slope="{amount}" intercept="{amount2}"/><feFuncB type="linear" slope="{amount}" intercept="{amount2}"/></feComponentTransfer>',{amount:e,amount2:.5-e/2})},t.filter.contrast.toString=function(){return this()}}),i})},{197:197}],200:[function(t,e,n){"use strict";var i=e.exports=t(199);i.plugin(function(t,e){e.prototype.children=function(){for(var e=[],n=this.node.childNodes,i=0,r=n.length;r>i;i++)e[i]=new t(n[i]);return e}}),i.plugin(function(t,e,n,i){function r(t){return t.split(/\s+/)}function o(t){return t.join(" ")}function a(t){return r(t.attr("class")||"")}function s(t,e){t.attr("class",o(e))}e.prototype.addClass=function(t){var e,n,i=a(this),o=r(t);for(e=0,n;n=o[e];e++)-1===i.indexOf(n)&&i.push(n);return s(this,i),this},e.prototype.hasClass=function(t){if(!t)throw new Error("[snapsvg] syntax: hasClass(clsStr)");return-1!==a(this).indexOf(t)},e.prototype.removeClass=function(t){var e,n,i,o=a(this),c=r(t);for(e=0,n;n=c[e];e++)i=o.indexOf(n),-1!==i&&o.splice(i,1);return s(this,o),this}}),i.plugin(function(t,e,n,i){e.prototype.translate=function(e,n){var i=new t.Matrix;return i.translate(e,n),this.transform(i)}}),i.plugin(function(t){t.create=function(e,n){return t._.wrap(t._.$(e,n))}}),i.plugin(function(t,e,n,i){t.createSnapAt=function(e,n,i){var r=document.createElementNS("http://www.w3.org/2000/svg","svg");return r.setAttribute("width",e),r.setAttribute("height",n),i||(i=document.body),i.appendChild(r),new t(r)}})},{199:199}],201:[function(t,e,n){var i=function(t){return"[object Array]"===Object.prototype.toString.call(t)},r=function(){var t=Array.prototype.slice.call(arguments);1===t.length&&i(t[0])&&(t=t[0]);var e=t.pop();return e.$inject=t,e},o=/^function\s*[^\(]*\(\s*([^\)]*)\)/m,a=/\/\*([^\*]*)\*\//m,s=function(t){if("function"!=typeof t)throw new Error('Cannot annotate "'+t+'". Expected a function!');var e=t.toString().match(o);return e[1]&&e[1].split(",").map(function(t){return e=t.match(a),e?e[1].trim():t.trim()})||[]};n.annotate=r,n.parse=s,n.isArray=i},{}],202:[function(t,e,n){e.exports={annotate:t(201).annotate,Module:t(204),Injector:t(203)}},{201:201,203:203,204:204}],203:[function(t,e,n){var i=t(204),r=t(201).parse,o=t(201).annotate,a=t(201).isArray,s=function(t,e){e=e||{get:function(t){throw n.push(t),l('No provider for "'+t+'"!')}};var n=[],c=this._providers=Object.create(e._providers||null),u=this._instances=Object.create(null),p=u.injector=this,l=function(t){var e=n.join(" -> ");return n.length=0,new Error(e?t+" (Resolving: "+e+")":t)},h=function(t){if(!c[t]&&-1!==t.indexOf(".")){for(var i=t.split("."),r=h(i.shift());i.length;)r=r[i.shift()];return r}if(Object.hasOwnProperty.call(u,t))return u[t];if(Object.hasOwnProperty.call(c,t)){if(-1!==n.indexOf(t))throw n.push(t),l("Cannot resolve circular dependency!");return n.push(t),u[t]=c[t][0](c[t][1]),n.pop(),u[t]}return e.get(t)},d=function(t){var e=Object.create(t.prototype),n=f(t,e);return"object"==typeof n?n:e},f=function(t,e){if("function"!=typeof t){if(!a(t))throw new Error('Cannot invoke "'+t+'". Expected a function!');t=o(t.slice())}var n=t.$inject&&t.$inject||r(t),i=n.map(function(t){return h(t)});return t.apply(e,i)},m=function(t){return o(function(e){return t.get(e)})},y=function(t,e){if(e&&e.length){var n,i,r,o,a=Object.create(null),u=Object.create(null),l=[],h=[],d=[];for(var f in c)n=c[f],-1!==e.indexOf(f)&&("private"===n[2]?(i=l.indexOf(n[3]),-1===i?(r=n[3].createChild([],e),o=m(r),l.push(n[3]),h.push(r),d.push(o),a[f]=[o,f,"private",r]):a[f]=[d[i],f,"private",h[i]]):a[f]=[n[2],n[1]],u[f]=!0),"factory"!==n[2]&&"type"!==n[2]||!n[1].$scope||e.forEach(function(t){-1!==n[1].$scope.indexOf(t)&&(a[f]=[n[2],n[1]],u[t]=!0)});e.forEach(function(t){if(!u[t])throw new Error('No provider for "'+t+'". Cannot use provider from the parent!')}),t.unshift(a)}return new s(t,p)},v={factory:f,type:d,value:function(t){return t}};t.forEach(function(t){function e(t,e){return"value"!==t&&a(e)&&(e=o(e.slice())),e}if(t instanceof i)t.forEach(function(t){var n=t[0],i=t[1],r=t[2];c[n]=[v[i],e(i,r),i]});else if("object"==typeof t)if(t.__exports__){var n=Object.keys(t).reduce(function(e,n){return"__"!==n.substring(0,2)&&(e[n]=t[n]),e},Object.create(null)),r=new s((t.__modules__||[]).concat([n]),p),u=o(function(t){return r.get(t)});t.__exports__.forEach(function(t){c[t]=[u,t,"private",r]})}else Object.keys(t).forEach(function(n){if("private"===t[n][2])return void(c[n]=t[n]);var i=t[n][0],r=t[n][1];c[n]=[v[i],e(i,r),i]})}),this.get=h,this.invoke=f,this.instantiate=d,this.createChild=y};e.exports=s},{201:201,204:204}],204:[function(t,e,n){var i=function(){var t=[];this.factory=function(e,n){return t.push([e,"factory",n]),this},this.value=function(e,n){return t.push([e,"value",n]),this},this.type=function(e,n){return t.push([e,"type",n]),this},this.forEach=function(e){t.forEach(e)}};e.exports=i},{}],205:[function(t,e,n){"use strict";function i(t){t=t||[128,36,1],this._seed=t.length?r.rack(t[0],t[1],t[2]):t}var r=t(206);e.exports=i,i.prototype.next=function(t){return this._seed(t||!0)},i.prototype.nextPrefixed=function(t,e){var n;do n=t+this.next(!0);while(this.assigned(n));return this.claim(n,e),n},i.prototype.claim=function(t,e){this._seed.set(t,e||!0)},i.prototype.assigned=function(t){return this._seed.get(t)||!1}},{206:206}],206:[function(t,e,n){var i=e.exports=function(t,e){if(e||(e=16),void 0===t&&(t=128),0>=t)return"0";for(var n=Math.log(Math.pow(2,t))/Math.log(e),r=2;n===1/0;r*=2)n=Math.log(Math.pow(2,t/r))/Math.log(e)*r;for(var o=n-Math.floor(n),a="",r=0;r<Math.floor(n);r++){var s=Math.floor(Math.random()*e).toString(e);a=s+a}if(o){var c=Math.pow(e,o),s=Math.floor(Math.random()*c).toString(e);a=s+a}var u=parseInt(a,e);return u!==1/0&&u>=Math.pow(2,t)?i(t,e):a};i.rack=function(t,e,n){var r=function(r){var a=0;do{if(a++>10){if(!n)throw new Error("too many ID collisions, use more bits");t+=n}var s=i(t,e)}while(Object.hasOwnProperty.call(o,s));return o[s]=r,s},o=r.hats={};return r.get=function(t){return r.hats[t]},r.set=function(t,e){return r.hats[t]=e,r},r.bits=t||128,r.base=e||16,r}},{}],207:[function(t,e,n){"function"==typeof Object.create?e.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},{}],208:[function(t,e,n){function i(t,e,n){var i=t?t.length:0;return n&&o(t,e,n)&&(e=!1),i?r(t,e):[]}var r=t(255),o=t(310);e.exports=i},{255:255,310:310}],209:[function(t,e,n){function i(t){var e=t?t.length:0;return e?t[e-1]:void 0}e.exports=i},{}],210:[function(t,e,n){function i(t,e,n,i){var c=t?t.length:0;return c?(null!=e&&"boolean"!=typeof e&&(i=n,n=a(t,e,i)?void 0:e,e=!1),n=null==n?n:r(n,i,3),e?s(t,n):o(t,n)):[]}var r=t(244),o=t(278),a=t(310),s=t(325);e.exports=i},{244:244,278:278,310:310,325:325}],211:[function(t,e,n){e.exports=t(210)},{210:210}],212:[function(t,e,n){var i=t(249),r=t(308),o=t(230),a=o(function(t,e){return r(t)?i(t,e):[]});e.exports=a},{230:230,249:249,308:308}],213:[function(t,e,n){function i(t){if(c(t)&&!s(t)&&!(t instanceof r)){if(t instanceof o)return t;if(l.call(t,"__chain__")&&l.call(t,"__wrapped__"))return u(t)}return new o(t)}var r=t(231),o=t(232),a=t(264),s=t(330),c=t(314),u=t(328),p=Object.prototype,l=p.hasOwnProperty;i.prototype=a.prototype,e.exports=i},{231:231,232:232,264:264,314:314,328:328,330:330}],214:[function(t,e,n){e.exports=t(224)},{224:224}],215:[function(t,e,n){function i(t,e,n){var i=s(t)?r:a;return n&&c(t,e,n)&&(e=void 0),("function"!=typeof e||void 0!==n)&&(e=o(e,n,3)),i(t,e)}var r=t(236),o=t(244),a=t(251),s=t(330),c=t(310);e.exports=i},{236:236,244:244,251:251,310:310,330:330}],216:[function(t,e,n){function i(t,e,n){var i=s(t)?r:a;return e=o(e,n,3),i(t,e)}var r=t(237),o=t(244),a=t(252),s=t(330);e.exports=i},{237:237,244:244,252:252,330:330}],217:[function(t,e,n){var i=t(250),r=t(293),o=r(i);e.exports=o},{250:250,293:293}],218:[function(t,e,n){var i=t(235),r=t(250),o=t(294),a=o(i,r);e.exports=a},{235:235,250:250,294:294}],219:[function(t,e,n){var i=t(286),r=Object.prototype,o=r.hasOwnProperty,a=i(function(t,e,n){o.call(t,n)?t[n].push(e):t[n]=[e]});e.exports=a},{286:286}],220:[function(t,e,n){function i(t,e,n,i){var h=t?o(t):0;return c(h)||(t=p(t),h=t.length),n="number"!=typeof n||i&&s(e,n,i)?0:0>n?l(h+n,0):n||0,"string"==typeof t||!a(t)&&u(t)?h>=n&&t.indexOf(e,n)>-1:!!h&&r(t,e,n)>-1}var r=t(260),o=t(304),a=t(330),s=t(310),c=t(313),u=t(336),p=t(347),l=Math.max;e.exports=i},{260:260,304:304,310:310,313:313,330:330,336:336,347:347}],221:[function(t,e,n){function i(t,e,n){var i=s(t)?r:a;return e=o(e,n,3),i(t,e)}var r=t(238),o=t(244),a=t(265),s=t(330);e.exports=i},{238:238,244:244,265:265,330:330}],222:[function(t,e,n){var i=t(240),r=t(250),o=t(297),a=o(i,r);e.exports=a},{240:240,250:250,297:297}],223:[function(t,e,n){function i(t){var e=t?r(t):0;return o(e)?e:a(t).length}var r=t(304),o=t(313),a=t(340);e.exports=i},{304:304,313:313,340:340}],224:[function(t,e,n){function i(t,e,n){var i=s(t)?r:a;return n&&c(t,e,n)&&(e=void 0),("function"!=typeof e||void 0!==n)&&(e=o(e,n,3)),i(t,e)}var r=t(241),o=t(244),a=t(275),s=t(330),c=t(310);e.exports=i},{241:241,244:244,275:275,310:310,330:330}],225:[function(t,e,n){function i(t,e,n){if(null==t)return[];n&&c(t,e,n)&&(e=void 0);var i=-1;e=r(e,n,3);var u=o(t,function(t,n,r){return{criteria:e(t,n,r),index:++i,value:t}});return a(u,s)}var r=t(244),o=t(265),a=t(276),s=t(283),c=t(310);e.exports=i},{244:244,265:265,276:276,283:283,310:310}],226:[function(t,e,n){var i=t(306),r=i(Date,"now"),o=r||function(){return(new Date).getTime()};e.exports=o},{306:306}],227:[function(t,e,n){var i=t(298),r=t(322),o=t(230),a=1,s=32,c=o(function(t,e,n){var o=a;if(n.length){var u=r(n,c.placeholder);o|=s}return i(t,o,e,n,u)});c.placeholder={},e.exports=c},{230:230,298:298,322:322}],228:[function(t,e,n){function i(t,e,n){function i(){v&&clearTimeout(v),d&&clearTimeout(d),x=0,d=v=g=void 0}function c(e,n){n&&clearTimeout(n),d=v=g=void 0,e&&(x=o(),f=t.apply(y,h),v||d||(h=y=void 0))}function u(){var t=e-(o()-m);0>=t||t>e?c(g,d):v=setTimeout(u,t)}function p(){c(E,v)}function l(){if(h=arguments,m=o(),y=this,g=E&&(v||!w),b===!1)var n=w&&!v;else{d||w||(x=m);var i=b-(m-x),r=0>=i||i>b;r?(d&&(d=clearTimeout(d)),x=m,f=t.apply(y,h)):d||(d=setTimeout(p,i))}return r&&v?v=clearTimeout(v):v||e===b||(v=setTimeout(u,e)),n&&(r=!0,f=t.apply(y,h)),!r||v||d||(h=y=void 0),f}var h,d,f,m,y,v,g,x=0,b=!1,E=!0;if("function"!=typeof t)throw new TypeError(a);if(e=0>e?0:+e||0,n===!0){var w=!0;E=!1}else r(n)&&(w=!!n.leading,b="maxWait"in n&&s(+n.maxWait||0,e),E="trailing"in n?!!n.trailing:E);return l.cancel=i,l}var r=t(334),o=t(226),a="Expected a function",s=Math.max;e.exports=i},{226:226,334:334}],229:[function(t,e,n){var i=t(248),r=t(230),o=r(function(t,e){return i(t,1,e)});e.exports=o},{230:230,248:248}],230:[function(t,e,n){function i(t,e){if("function"!=typeof t)throw new TypeError(r);return e=o(void 0===e?t.length-1:+e||0,0),function(){for(var n=arguments,i=-1,r=o(n.length-e,0),a=Array(r);++i<r;)a[i]=n[e+i];switch(e){case 0:return t.call(this,a);case 1:return t.call(this,n[0],a);case 2:return t.call(this,n[0],n[1],a)}var s=Array(e+1);for(i=-1;++i<e;)s[i]=n[i];return s[e]=a,t.apply(this,s)}}var r="Expected a function",o=Math.max;e.exports=i},{}],231:[function(t,e,n){function i(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=a,this.__views__=[]}var r=t(247),o=t(264),a=Number.POSITIVE_INFINITY;i.prototype=r(o.prototype),i.prototype.constructor=i,e.exports=i},{247:247,264:264}],232:[function(t,e,n){function i(t,e,n){this.__wrapped__=t,this.__actions__=n||[],this.__chain__=!!e}var r=t(247),o=t(264);i.prototype=r(o.prototype),i.prototype.constructor=i,e.exports=i},{247:247,264:264}],233:[function(t,e,n){(function(n){function i(t){var e=t?t.length:0;for(this.data={hash:s(null),set:new a};e--;)this.push(t[e])}var r=t(282),o=t(306),a=o(n,"Set"),s=o(Object,"create");i.prototype.push=r,e.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{282:282,306:306}],234:[function(t,e,n){function i(t,e){var n=-1,i=t.length;for(e||(e=Array(i));++n<i;)e[n]=t[n];return e}e.exports=i},{}],235:[function(t,e,n){function i(t,e){for(var n=-1,i=t.length;++n<i&&e(t[n],n,t)!==!1;);return t}e.exports=i},{}],236:[function(t,e,n){function i(t,e){for(var n=-1,i=t.length;++n<i;)if(!e(t[n],n,t))return!1;return!0}e.exports=i},{}],237:[function(t,e,n){function i(t,e){for(var n=-1,i=t.length,r=-1,o=[];++n<i;){var a=t[n];e(a,n,t)&&(o[++r]=a)}return o}e.exports=i},{}],238:[function(t,e,n){function i(t,e){for(var n=-1,i=t.length,r=Array(i);++n<i;)r[n]=e(t[n],n,t);return r}e.exports=i},{}],239:[function(t,e,n){function i(t,e){for(var n=-1,i=e.length,r=t.length;++n<i;)t[r+n]=e[n];return t}e.exports=i},{}],240:[function(t,e,n){function i(t,e,n,i){var r=-1,o=t.length;for(i&&o&&(n=t[++r]);++r<o;)n=e(n,t[r],r,t);return n}e.exports=i},{}],241:[function(t,e,n){function i(t,e){for(var n=-1,i=t.length;++n<i;)if(e(t[n],n,t))return!0;return!1}e.exports=i},{}],242:[function(t,e,n){function i(t,e,n){for(var i=-1,o=r(e),a=o.length;++i<a;){var s=o[i],c=t[s],u=n(c,e[s],s,t,e);(u===u?u===c:c!==c)&&(void 0!==c||s in t)||(t[s]=u)}return t}var r=t(340);e.exports=i},{340:340}],243:[function(t,e,n){function i(t,e){return null==e?t:r(e,o(e),t)}var r=t(246),o=t(340);e.exports=i},{246:246,340:340}],244:[function(t,e,n){function i(t,e,n){var i=typeof t;return"function"==i?void 0===e?t:a(t,e,n):null==t?s:"object"==i?r(t):void 0===e?c(t):o(t,e)}var r=t(266),o=t(267),a=t(280),s=t(348),c=t(350);e.exports=i},{266:266,267:267,280:280,348:348,350:350}],245:[function(t,e,n){function i(t,e){if(t!==e){var n=null===t,i=void 0===t,r=t===t,o=null===e,a=void 0===e,s=e===e;if(t>e&&!o||!r||n&&!a&&s||i&&s)return 1;if(e>t&&!n||!s||o&&!i&&r||a&&r)return-1}return 0}e.exports=i},{}],246:[function(t,e,n){function i(t,e,n){n||(n={});for(var i=-1,r=e.length;++i<r;){var o=e[i];n[o]=t[o]}return n}e.exports=i},{}],247:[function(t,e,n){var i=t(334),r=function(){function t(){}return function(e){if(i(e)){t.prototype=e;var n=new t;t.prototype=void 0}return n||{}}}();e.exports=r},{334:334}],248:[function(t,e,n){function i(t,e,n){if("function"!=typeof t)throw new TypeError(r);return setTimeout(function(){t.apply(void 0,n)},e)}var r="Expected a function";e.exports=i},{}],249:[function(t,e,n){function i(t,e){var n=t?t.length:0,i=[];if(!n)return i;var c=-1,u=r,p=!0,l=p&&e.length>=s?a(e):null,h=e.length;l&&(u=o,p=!1,e=l);t:for(;++c<n;){var d=t[c];if(p&&d===d){for(var f=h;f--;)if(e[f]===d)continue t;i.push(d)}else u(e,d,0)<0&&i.push(d)}return i}var r=t(260),o=t(281),a=t(291),s=200;e.exports=i},{260:260,281:281,291:291}],250:[function(t,e,n){var i=t(258),r=t(288),o=r(i);e.exports=o},{258:258,288:288}],251:[function(t,e,n){function i(t,e){var n=!0;return r(t,function(t,i,r){return n=!!e(t,i,r)}),n}var r=t(250);e.exports=i},{250:250}],252:[function(t,e,n){function i(t,e){var n=[];return r(t,function(t,i,r){e(t,i,r)&&n.push(t)}),n}var r=t(250);e.exports=i},{250:250}],253:[function(t,e,n){function i(t,e,n,i){var r;return n(t,function(t,n,o){return e(t,n,o)?(r=i?n:t,!1):void 0}),r}e.exports=i},{}],254:[function(t,e,n){function i(t,e,n){for(var i=t.length,r=n?i:-1;n?r--:++r<i;)if(e(t[r],r,t))return r;return-1}e.exports=i},{}],255:[function(t,e,n){function i(t,e,n,u){u||(u=[]);for(var p=-1,l=t.length;++p<l;){var h=t[p];c(h)&&s(h)&&(n||a(h)||o(h))?e?i(h,e,n,u):r(u,h):n||(u[u.length]=h)}return u}var r=t(239),o=t(329),a=t(330),s=t(308),c=t(314);e.exports=i},{239:239,308:308,314:314,329:329,330:330}],256:[function(t,e,n){var i=t(289),r=i();e.exports=r},{289:289}],257:[function(t,e,n){function i(t,e){return r(t,e,o)}var r=t(256),o=t(341);e.exports=i},{256:256,341:341}],258:[function(t,e,n){function i(t,e){return r(t,e,o)}var r=t(256),o=t(340);e.exports=i},{256:256,340:340}],259:[function(t,e,n){function i(t,e,n){if(null!=t){void 0!==n&&n in r(t)&&(e=[n]);for(var i=0,o=e.length;null!=t&&o>i;)t=t[e[i++]];return i&&i==o?t:void 0}}var r=t(326);e.exports=i},{326:326}],260:[function(t,e,n){function i(t,e,n){if(e!==e)return r(t,n);for(var i=n-1,o=t.length;++i<o;)if(t[i]===e)return i;return-1}var r=t(307);e.exports=i},{307:307}],261:[function(t,e,n){function i(t,e,n,s,c,u){return t===e?!0:null==t||null==e||!o(t)&&!a(e)?t!==t&&e!==e:r(t,e,i,n,s,c,u)}var r=t(262),o=t(334),a=t(314);e.exports=i},{262:262,314:314,334:334}],262:[function(t,e,n){function i(t,e,n,i,h,m,y){var v=s(t),g=s(e),x=p,b=p;v||(x=f.call(t),x==u?x=l:x!=l&&(v=c(t))),g||(b=f.call(e),b==u?b=l:b!=l&&(g=c(e)));var E=x==l,w=b==l,_=x==b;if(_&&!v&&!E)return o(t,e,x);if(!h){var S=E&&d.call(t,"__wrapped__"),A=w&&d.call(e,"__wrapped__");if(S||A)return n(S?t.value():t,A?e.value():e,i,h,m,y)}if(!_)return!1;m||(m=[]),y||(y=[]);for(var T=m.length;T--;)if(m[T]==t)return y[T]==e;m.push(t),y.push(e);var C=(v?r:a)(t,e,n,i,h,m,y);return m.pop(),y.pop(),C}var r=t(299),o=t(300),a=t(301),s=t(330),c=t(337),u="[object Arguments]",p="[object Array]",l="[object Object]",h=Object.prototype,d=h.hasOwnProperty,f=h.toString;e.exports=i},{299:299,300:300,301:301,330:330,337:337}],263:[function(t,e,n){function i(t,e,n){var i=e.length,a=i,s=!n;if(null==t)return!a;for(t=o(t);i--;){var c=e[i];if(s&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++i<a;){c=e[i];var u=c[0],p=t[u],l=c[1];if(s&&c[2]){if(void 0===p&&!(u in t))return!1}else{var h=n?n(p,l,u):void 0;if(!(void 0===h?r(l,p,n,!0):h))return!1}}return!0}var r=t(261),o=t(326);e.exports=i},{261:261,326:326}],264:[function(t,e,n){function i(){}e.exports=i},{}],265:[function(t,e,n){function i(t,e){var n=-1,i=o(t)?Array(t.length):[];return r(t,function(t,r,o){i[++n]=e(t,r,o)}),i}var r=t(250),o=t(308);e.exports=i},{250:250,308:308}],266:[function(t,e,n){function i(t){var e=o(t);if(1==e.length&&e[0][2]){
var n=e[0][0],i=e[0][1];return function(t){return null==t?!1:t[n]===i&&(void 0!==i||n in a(t))}}return function(t){return r(t,e)}}var r=t(263),o=t(305),a=t(326);e.exports=i},{263:263,305:305,326:326}],267:[function(t,e,n){function i(t,e){var n=s(t),i=c(t)&&u(e),d=t+"";return t=h(t),function(s){if(null==s)return!1;var c=d;if(s=l(s),(n||!i)&&!(c in s)){if(s=1==t.length?s:r(s,a(t,0,-1)),null==s)return!1;c=p(t),s=l(s)}return s[c]===e?void 0!==e||c in s:o(e,s[c],void 0,!0)}}var r=t(259),o=t(261),a=t(274),s=t(330),c=t(311),u=t(315),p=t(209),l=t(326),h=t(327);e.exports=i},{209:209,259:259,261:261,274:274,311:311,315:315,326:326,327:327,330:330}],268:[function(t,e,n){function i(t,e,n,h,d){if(!c(t))return t;var f=s(e)&&(a(e)||p(e)),m=f?void 0:l(e);return r(m||e,function(r,a){if(m&&(a=r,r=e[a]),u(r))h||(h=[]),d||(d=[]),o(t,e,a,i,n,h,d);else{var s=t[a],c=n?n(s,r,a,t,e):void 0,p=void 0===c;p&&(c=r),void 0===c&&(!f||a in t)||!p&&(c===c?c===s:s!==s)||(t[a]=c)}}),t}var r=t(235),o=t(269),a=t(330),s=t(308),c=t(334),u=t(314),p=t(337),l=t(340);e.exports=i},{235:235,269:269,308:308,314:314,330:330,334:334,337:337,340:340}],269:[function(t,e,n){function i(t,e,n,i,l,h,d){for(var f=h.length,m=e[n];f--;)if(h[f]==m)return void(t[n]=d[f]);var y=t[n],v=l?l(y,m,n,t,e):void 0,g=void 0===v;g&&(v=m,s(m)&&(a(m)||u(m))?v=a(y)?y:s(y)?r(y):[]:c(m)||o(m)?v=o(y)?p(y):c(y)?y:{}:g=!1),h.push(m),d.push(v),g?t[n]=i(v,m,l,h,d):(v===v?v!==y:y===y)&&(t[n]=v)}var r=t(234),o=t(329),a=t(330),s=t(308),c=t(335),u=t(337),p=t(338);e.exports=i},{234:234,308:308,329:329,330:330,335:335,337:337,338:338}],270:[function(t,e,n){function i(t){return function(e){return null==e?void 0:e[t]}}e.exports=i},{}],271:[function(t,e,n){function i(t){var e=t+"";return t=o(t),function(n){return r(n,t,e)}}var r=t(259),o=t(327);e.exports=i},{259:259,327:327}],272:[function(t,e,n){function i(t,e,n,i,r){return r(t,function(t,r,o){n=i?(i=!1,t):e(n,t,r,o)}),n}e.exports=i},{}],273:[function(t,e,n){var i=t(348),r=t(317),o=r?function(t,e){return r.set(t,e),t}:i;e.exports=o},{317:317,348:348}],274:[function(t,e,n){function i(t,e,n){var i=-1,r=t.length;e=null==e?0:+e||0,0>e&&(e=-e>r?0:r+e),n=void 0===n||n>r?r:+n||0,0>n&&(n+=r),r=e>n?0:n-e>>>0,e>>>=0;for(var o=Array(r);++i<r;)o[i]=t[i+e];return o}e.exports=i},{}],275:[function(t,e,n){function i(t,e){var n;return r(t,function(t,i,r){return n=e(t,i,r),!n}),!!n}var r=t(250);e.exports=i},{250:250}],276:[function(t,e,n){function i(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}e.exports=i},{}],277:[function(t,e,n){function i(t){return null==t?"":t+""}e.exports=i},{}],278:[function(t,e,n){function i(t,e){var n=-1,i=r,c=t.length,u=!0,p=u&&c>=s,l=p?a():null,h=[];l?(i=o,u=!1):(p=!1,l=e?[]:h);t:for(;++n<c;){var d=t[n],f=e?e(d,n,t):d;if(u&&d===d){for(var m=l.length;m--;)if(l[m]===f)continue t;e&&l.push(f),h.push(d)}else i(l,f,0)<0&&((e||p)&&l.push(f),h.push(d))}return h}var r=t(260),o=t(281),a=t(291),s=200;e.exports=i},{260:260,281:281,291:291}],279:[function(t,e,n){function i(t,e){for(var n=-1,i=e.length,r=Array(i);++n<i;)r[n]=t[e[n]];return r}e.exports=i},{}],280:[function(t,e,n){function i(t,e,n){if("function"!=typeof t)return r;if(void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 3:return function(n,i,r){return t.call(e,n,i,r)};case 4:return function(n,i,r,o){return t.call(e,n,i,r,o)};case 5:return function(n,i,r,o,a){return t.call(e,n,i,r,o,a)}}return function(){return t.apply(e,arguments)}}var r=t(348);e.exports=i},{348:348}],281:[function(t,e,n){function i(t,e){var n=t.data,i="string"==typeof e||r(e)?n.set.has(e):n.hash[e];return i?0:-1}var r=t(334);e.exports=i},{334:334}],282:[function(t,e,n){function i(t){var e=this.data;"string"==typeof t||r(t)?e.set.add(t):e.hash[t]=!0}var r=t(334);e.exports=i},{334:334}],283:[function(t,e,n){function i(t,e){return r(t.criteria,e.criteria)||t.index-e.index}var r=t(245);e.exports=i},{245:245}],284:[function(t,e,n){function i(t,e,n){for(var i=n.length,o=-1,a=r(t.length-i,0),s=-1,c=e.length,u=Array(c+a);++s<c;)u[s]=e[s];for(;++o<i;)u[n[o]]=t[o];for(;a--;)u[s++]=t[o++];return u}var r=Math.max;e.exports=i},{}],285:[function(t,e,n){function i(t,e,n){for(var i=-1,o=n.length,a=-1,s=r(t.length-o,0),c=-1,u=e.length,p=Array(s+u);++a<s;)p[a]=t[a];for(var l=a;++c<u;)p[l+c]=e[c];for(;++i<o;)p[l+n[i]]=t[a++];return p}var r=Math.max;e.exports=i},{}],286:[function(t,e,n){function i(t,e){return function(n,i,s){var c=e?e():{};if(i=r(i,s,3),a(n))for(var u=-1,p=n.length;++u<p;){var l=n[u];t(c,l,i(l,u,n),n)}else o(n,function(e,n,r){t(c,e,i(e,n,r),r)});return c}}var r=t(244),o=t(250),a=t(330);e.exports=i},{244:244,250:250,330:330}],287:[function(t,e,n){function i(t){return a(function(e,n){var i=-1,a=null==e?0:n.length,s=a>2?n[a-2]:void 0,c=a>2?n[2]:void 0,u=a>1?n[a-1]:void 0;for("function"==typeof s?(s=r(s,u,5),a-=2):(s="function"==typeof u?u:void 0,a-=s?1:0),c&&o(n[0],n[1],c)&&(s=3>a?void 0:s,a=1);++i<a;){var p=n[i];p&&t(e,p,s)}return e})}var r=t(280),o=t(310),a=t(230);e.exports=i},{230:230,280:280,310:310}],288:[function(t,e,n){function i(t,e){return function(n,i){var s=n?r(n):0;if(!o(s))return t(n,i);for(var c=e?s:-1,u=a(n);(e?c--:++c<s)&&i(u[c],c,u)!==!1;);return n}}var r=t(304),o=t(313),a=t(326);e.exports=i},{304:304,313:313,326:326}],289:[function(t,e,n){function i(t){return function(e,n,i){for(var o=r(e),a=i(e),s=a.length,c=t?s:-1;t?c--:++c<s;){var u=a[c];if(n(o[u],u,o)===!1)break}return e}}var r=t(326);e.exports=i},{326:326}],290:[function(t,e,n){(function(n){function i(t,e){function i(){var r=this&&this!==n&&this instanceof i?o:t;return r.apply(e,arguments)}var o=r(t);return i}var r=t(292);e.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{292:292}],291:[function(t,e,n){(function(n){function i(t){return s&&a?new r(t):null}var r=t(233),o=t(306),a=o(n,"Set"),s=o(Object,"create");e.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{233:233,306:306}],292:[function(t,e,n){function i(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=r(t.prototype),i=t.apply(n,e);return o(i)?i:n}}var r=t(247),o=t(334);e.exports=i},{247:247,334:334}],293:[function(t,e,n){function i(t,e){return function(n,i,c){if(i=r(i,c,3),s(n)){var u=a(n,i,e);return u>-1?n[u]:void 0}return o(n,i,t)}}var r=t(244),o=t(253),a=t(254),s=t(330);e.exports=i},{244:244,253:253,254:254,330:330}],294:[function(t,e,n){function i(t,e){return function(n,i,a){return"function"==typeof i&&void 0===a&&o(n)?t(n,i):e(n,r(i,a,3))}}var r=t(280),o=t(330);e.exports=i},{280:280,330:330}],295:[function(t,e,n){(function(n){function i(t,e,E,w,_,S,A,T,C,N){function M(){for(var f=arguments.length,m=f,y=Array(f);m--;)y[m]=arguments[m];if(w&&(y=o(y,w,_)),S&&(y=a(y,S,A)),k||O){var x=M.placeholder,L=p(y,x);if(f-=L.length,N>f){var j=T?r(T):void 0,F=b(N-f,0),z=k?L:void 0,V=k?void 0:L,$=k?y:void 0,G=k?void 0:y;e|=k?v:g,e&=~(k?g:v),B||(e&=~(h|d));var W=[t,e,E,$,z,G,V,j,C,F],U=i.apply(void 0,W);return c(t)&&l(U,W),U.placeholder=x,U}}var q=P?E:this,H=D?q[t]:t;return T&&(y=u(y,T)),R&&C<y.length&&(y.length=C),this&&this!==n&&this instanceof M&&(H=I||s(t)),H.apply(q,y)}var R=e&x,P=e&h,D=e&d,k=e&m,B=e&f,O=e&y,I=D?void 0:s(t);return M}var r=t(234),o=t(284),a=t(285),s=t(292),c=t(312),u=t(321),p=t(322),l=t(323),h=1,d=2,f=4,m=8,y=16,v=32,g=64,x=128,b=Math.max;e.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{234:234,284:284,285:285,292:292,312:312,321:321,322:322,323:323}],296:[function(t,e,n){(function(n){function i(t,e,i,a){function s(){for(var e=-1,r=arguments.length,o=-1,p=a.length,l=Array(p+r);++o<p;)l[o]=a[o];for(;r--;)l[o++]=arguments[++e];var h=this&&this!==n&&this instanceof s?u:t;return h.apply(c?i:this,l)}var c=e&o,u=r(t);return s}var r=t(292),o=1;e.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{292:292}],297:[function(t,e,n){function i(t,e){return function(n,i,s,c){var u=arguments.length<3;return"function"==typeof i&&void 0===c&&a(n)?t(n,i,s,u):o(n,r(i,c,4),s,u,e)}}var r=t(244),o=t(272),a=t(330);e.exports=i},{244:244,272:272,330:330}],298:[function(t,e,n){function i(t,e,n,i,v,g,x,b){var E=e&h;if(!E&&"function"!=typeof t)throw new TypeError(m);var w=i?i.length:0;if(w||(e&=~(d|f),i=v=void 0),w-=v?v.length:0,e&f){var _=i,S=v;i=v=void 0}var A=E?void 0:c(t),T=[t,e,n,i,v,_,S,g,x,b];if(A&&(u(T,A),e=T[1],b=T[9]),T[9]=null==b?E?0:t.length:y(b-w,0)||0,e==l)var C=o(T[0],T[2]);else C=e!=d&&e!=(l|d)||T[4].length?a.apply(void 0,T):s.apply(void 0,T);var N=A?r:p;return N(C,T)}var r=t(273),o=t(290),a=t(295),s=t(296),c=t(302),u=t(316),p=t(323),l=1,h=2,d=32,f=64,m="Expected a function",y=Math.max;e.exports=i},{273:273,290:290,295:295,296:296,302:302,316:316,323:323}],299:[function(t,e,n){function i(t,e,n,i,o,a,s){var c=-1,u=t.length,p=e.length;if(u!=p&&!(o&&p>u))return!1;for(;++c<u;){var l=t[c],h=e[c],d=i?i(o?h:l,o?l:h,c):void 0;if(void 0!==d){if(d)continue;return!1}if(o){if(!r(e,function(t){return l===t||n(l,t,i,o,a,s)}))return!1}else if(l!==h&&!n(l,h,i,o,a,s))return!1}return!0}var r=t(241);e.exports=i},{241:241}],300:[function(t,e,n){function i(t,e,n){switch(n){case r:case o:return+t==+e;case a:return t.name==e.name&&t.message==e.message;case s:return t!=+t?e!=+e:t==+e;case c:case u:return t==e+""}return!1}var r="[object Boolean]",o="[object Date]",a="[object Error]",s="[object Number]",c="[object RegExp]",u="[object String]";e.exports=i},{}],301:[function(t,e,n){function i(t,e,n,i,o,s,c){var u=r(t),p=u.length,l=r(e),h=l.length;if(p!=h&&!o)return!1;for(var d=p;d--;){var f=u[d];if(!(o?f in e:a.call(e,f)))return!1}for(var m=o;++d<p;){f=u[d];var y=t[f],v=e[f],g=i?i(o?v:y,o?y:v,f):void 0;if(!(void 0===g?n(y,v,i,o,s,c):g))return!1;m||(m="constructor"==f)}if(!m){var x=t.constructor,b=e.constructor;if(x!=b&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof b&&b instanceof b))return!1}return!0}var r=t(340),o=Object.prototype,a=o.hasOwnProperty;e.exports=i},{340:340}],302:[function(t,e,n){var i=t(317),r=t(349),o=i?function(t){return i.get(t)}:r;e.exports=o},{317:317,349:349}],303:[function(t,e,n){function i(t){for(var e=t.name+"",n=r[e],i=n?n.length:0;i--;){var o=n[i],a=o.func;if(null==a||a==t)return o.name}return e}var r=t(320);e.exports=i},{320:320}],304:[function(t,e,n){var i=t(270),r=i("length");e.exports=r},{270:270}],305:[function(t,e,n){function i(t){for(var e=o(t),n=e.length;n--;)e[n][2]=r(e[n][1]);return e}var r=t(315),o=t(344);e.exports=i},{315:315,344:344}],306:[function(t,e,n){function i(t,e){var n=null==t?void 0:t[e];return r(n)?n:void 0}var r=t(332);e.exports=i},{332:332}],307:[function(t,e,n){function i(t,e,n){for(var i=t.length,r=e+(n?0:-1);n?r--:++r<i;){var o=t[r];if(o!==o)return r}return-1}e.exports=i},{}],308:[function(t,e,n){function i(t){return null!=t&&o(r(t))}var r=t(304),o=t(313);e.exports=i},{304:304,313:313}],309:[function(t,e,n){function i(t,e){return t="number"==typeof t||r.test(t)?+t:-1,e=null==e?o:e,t>-1&&t%1==0&&e>t}var r=/^\d+$/,o=9007199254740991;e.exports=i},{}],310:[function(t,e,n){function i(t,e,n){if(!a(n))return!1;var i=typeof e;if("number"==i?r(n)&&o(e,n.length):"string"==i&&e in n){var s=n[e];return t===t?t===s:s!==s}return!1}var r=t(308),o=t(309),a=t(334);e.exports=i},{308:308,309:309,334:334}],311:[function(t,e,n){function i(t,e){var n=typeof t;if("string"==n&&s.test(t)||"number"==n)return!0;if(r(t))return!1;var i=!a.test(t);return i||null!=e&&t in o(e)}var r=t(330),o=t(326),a=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\n\\]|\\.)*?\1)\]/,s=/^\w*$/;e.exports=i},{326:326,330:330}],312:[function(t,e,n){function i(t){var e=a(t),n=s[e];if("function"!=typeof n||!(e in r.prototype))return!1;if(t===n)return!0;var i=o(n);return!!i&&t===i[0]}var r=t(231),o=t(302),a=t(303),s=t(213);e.exports=i},{213:213,231:231,302:302,303:303}],313:[function(t,e,n){function i(t){return"number"==typeof t&&t>-1&&t%1==0&&r>=t}var r=9007199254740991;e.exports=i},{}],314:[function(t,e,n){function i(t){return!!t&&"object"==typeof t}e.exports=i},{}],315:[function(t,e,n){function i(t){return t===t&&!r(t)}var r=t(334);e.exports=i},{334:334}],316:[function(t,e,n){function i(t,e){var n=t[1],i=e[1],m=n|i,y=l>m,v=i==l&&n==p||i==l&&n==h&&t[7].length<=e[8]||i==(l|h)&&n==p;if(!y&&!v)return t;i&c&&(t[2]=e[2],m|=n&c?0:u);var g=e[3];if(g){var x=t[3];t[3]=x?o(x,g,e[4]):r(g),t[4]=x?s(t[3],d):r(e[4])}return g=e[5],g&&(x=t[5],t[5]=x?a(x,g,e[6]):r(g),t[6]=x?s(t[5],d):r(e[6])),g=e[7],g&&(t[7]=r(g)),i&l&&(t[8]=null==t[8]?e[8]:f(t[8],e[8])),null==t[9]&&(t[9]=e[9]),t[0]=e[0],t[1]=m,t}var r=t(234),o=t(284),a=t(285),s=t(322),c=1,u=4,p=8,l=128,h=256,d="__lodash_placeholder__",f=Math.min;e.exports=i},{234:234,284:284,285:285,322:322}],317:[function(t,e,n){(function(n){var i=t(306),r=i(n,"WeakMap"),o=r&&new r;e.exports=o}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{306:306}],318:[function(t,e,n){function i(t,e){t=r(t);for(var n=-1,i=e.length,o={};++n<i;){var a=e[n];a in t&&(o[a]=t[a])}return o}var r=t(326);e.exports=i},{326:326}],319:[function(t,e,n){function i(t,e){var n={};return r(t,function(t,i,r){e(t,i,r)&&(n[i]=t)}),n}var r=t(257);e.exports=i},{257:257}],320:[function(t,e,n){var i={};e.exports=i},{}],321:[function(t,e,n){function i(t,e){for(var n=t.length,i=a(e.length,n),s=r(t);i--;){var c=e[i];t[i]=o(c,n)?s[c]:void 0}return t}var r=t(234),o=t(309),a=Math.min;e.exports=i},{234:234,309:309}],322:[function(t,e,n){function i(t,e){for(var n=-1,i=t.length,o=-1,a=[];++n<i;)t[n]===e&&(t[n]=r,a[++o]=n);return a}var r="__lodash_placeholder__";e.exports=i},{}],323:[function(t,e,n){var i=t(273),r=t(226),o=150,a=16,s=function(){var t=0,e=0;return function(n,s){var c=r(),u=a-(c-e);if(e=c,u>0){if(++t>=o)return n}else t=0;return i(n,s)}}();e.exports=s},{226:226,273:273}],324:[function(t,e,n){function i(t){for(var e=c(t),n=e.length,i=n&&t.length,u=!!i&&s(i)&&(o(t)||r(t)),l=-1,h=[];++l<n;){var d=e[l];(u&&a(d,i)||p.call(t,d))&&h.push(d)}return h}var r=t(329),o=t(330),a=t(309),s=t(313),c=t(341),u=Object.prototype,p=u.hasOwnProperty;e.exports=i},{309:309,313:313,329:329,330:330,341:341}],325:[function(t,e,n){function i(t,e){for(var n,i=-1,r=t.length,o=-1,a=[];++i<r;){var s=t[i],c=e?e(s,i,t):s;i&&n===c||(n=c,a[++o]=s)}return a}e.exports=i},{}],326:[function(t,e,n){function i(t){return r(t)?t:Object(t)}var r=t(334);e.exports=i},{334:334}],327:[function(t,e,n){function i(t){if(o(t))return t;var e=[];return r(t).replace(a,function(t,n,i,r){e.push(i?r.replace(s,"$1"):n||t)}),e}var r=t(277),o=t(330),a=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\n\\]|\\.)*?)\2)\]/g,s=/\\(\\)?/g;e.exports=i},{277:277,330:330}],328:[function(t,e,n){function i(t){return t instanceof r?t.clone():new o(t.__wrapped__,t.__chain__,a(t.__actions__))}var r=t(231),o=t(232),a=t(234);e.exports=i},{231:231,232:232,234:234}],329:[function(t,e,n){function i(t){return o(t)&&r(t)&&s.call(t,"callee")&&!c.call(t,"callee")}var r=t(308),o=t(314),a=Object.prototype,s=a.hasOwnProperty,c=a.propertyIsEnumerable;e.exports=i},{308:308,314:314}],330:[function(t,e,n){var i=t(306),r=t(313),o=t(314),a="[object Array]",s=Object.prototype,c=s.toString,u=i(Array,"isArray"),p=u||function(t){return o(t)&&r(t.length)&&c.call(t)==a};e.exports=p},{306:306,313:313,314:314}],331:[function(t,e,n){function i(t){return r(t)&&s.call(t)==o}var r=t(334),o="[object Function]",a=Object.prototype,s=a.toString;e.exports=i},{334:334}],332:[function(t,e,n){function i(t){return null==t?!1:r(t)?p.test(c.call(t)):o(t)&&a.test(t)}var r=t(331),o=t(314),a=/^\[object .+?Constructor\]$/,s=Object.prototype,c=Function.prototype.toString,u=s.hasOwnProperty,p=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=i},{314:314,331:331}],333:[function(t,e,n){function i(t){return"number"==typeof t||r(t)&&s.call(t)==o}var r=t(314),o="[object Number]",a=Object.prototype,s=a.toString;e.exports=i},{314:314}],334:[function(t,e,n){function i(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}e.exports=i},{}],335:[function(t,e,n){function i(t){var e;if(!a(t)||p.call(t)!=s||o(t)||!u.call(t,"constructor")&&(e=t.constructor,"function"==typeof e&&!(e instanceof e)))return!1;var n;return r(t,function(t,e){n=e}),void 0===n||u.call(t,n)}var r=t(257),o=t(329),a=t(314),s="[object Object]",c=Object.prototype,u=c.hasOwnProperty,p=c.toString;e.exports=i},{257:257,314:314,329:329}],336:[function(t,e,n){function i(t){return"string"==typeof t||r(t)&&s.call(t)==o}var r=t(314),o="[object String]",a=Object.prototype,s=a.toString;e.exports=i},{314:314}],337:[function(t,e,n){function i(t){return o(t)&&r(t.length)&&!!M[P.call(t)]}var r=t(313),o=t(314),a="[object Arguments]",s="[object Array]",c="[object Boolean]",u="[object Date]",p="[object Error]",l="[object Function]",h="[object Map]",d="[object Number]",f="[object Object]",m="[object RegExp]",y="[object Set]",v="[object String]",g="[object WeakMap]",x="[object ArrayBuffer]",b="[object Float32Array]",E="[object Float64Array]",w="[object Int8Array]",_="[object Int16Array]",S="[object Int32Array]",A="[object Uint8Array]",T="[object Uint8ClampedArray]",C="[object Uint16Array]",N="[object Uint32Array]",M={};M[b]=M[E]=M[w]=M[_]=M[S]=M[A]=M[T]=M[C]=M[N]=!0,M[a]=M[s]=M[x]=M[c]=M[u]=M[p]=M[l]=M[h]=M[d]=M[f]=M[m]=M[y]=M[v]=M[g]=!1;var R=Object.prototype,P=R.toString;e.exports=i},{313:313,314:314}],338:[function(t,e,n){function i(t){return r(t,o(t))}var r=t(246),o=t(341);e.exports=i},{246:246,341:341}],339:[function(t,e,n){var i=t(242),r=t(243),o=t(287),a=o(function(t,e,n){return n?i(t,e,n):r(t,e)});e.exports=a},{242:242,243:243,287:287}],340:[function(t,e,n){var i=t(306),r=t(308),o=t(334),a=t(324),s=i(Object,"keys"),c=s?function(t){var e=null==t?void 0:t.constructor;return"function"==typeof e&&e.prototype===t||"function"!=typeof t&&r(t)?a(t):o(t)?s(t):[]}:a;e.exports=c},{306:306,308:308,324:324,334:334}],341:[function(t,e,n){function i(t){if(null==t)return[];c(t)||(t=Object(t));var e=t.length;e=e&&s(e)&&(o(t)||r(t))&&e||0;for(var n=t.constructor,i=-1,u="function"==typeof n&&n.prototype===t,l=Array(e),h=e>0;++i<e;)l[i]=i+"";for(var d in t)h&&a(d,e)||"constructor"==d&&(u||!p.call(t,d))||l.push(d);return l}var r=t(329),o=t(330),a=t(309),s=t(313),c=t(334),u=Object.prototype,p=u.hasOwnProperty;e.exports=i},{309:309,313:313,329:329,330:330,334:334}],342:[function(t,e,n){var i=t(268),r=t(287),o=r(i);e.exports=o},{268:268,287:287}],343:[function(t,e,n){var i=t(238),r=t(249),o=t(255),a=t(280),s=t(341),c=t(318),u=t(319),p=t(230),l=p(function(t,e){if(null==t)return{};if("function"!=typeof e[0]){var e=i(o(e),String);return c(t,r(s(t),e))}var n=a(e[0],e[1],3);return u(t,function(t,e,i){return!n(t,e,i)})});e.exports=l},{230:230,238:238,249:249,255:255,280:280,318:318,319:319,341:341}],344:[function(t,e,n){function i(t){t=o(t);for(var e=-1,n=r(t),i=n.length,a=Array(i);++e<i;){var s=n[e];a[e]=[s,t[s]]}return a}var r=t(340),o=t(326);e.exports=i},{326:326,340:340}],345:[function(t,e,n){var i=t(255),r=t(280),o=t(318),a=t(319),s=t(230),c=s(function(t,e){return null==t?{}:"function"==typeof e[0]?a(t,r(e[0],e[1],3)):o(t,i(e))});e.exports=c},{230:230,255:255,280:280,318:318,319:319}],346:[function(t,e,n){function i(t,e,n,i){var h=c(t)||l(t);if(e=o(e,i,4),null==n)if(h||p(t)){var d=t.constructor;n=h?c(t)?new d:[]:a(u(d)?d.prototype:void 0)}else n={};return(h?r:s)(t,function(t,i,r){return e(n,t,i,r)}),n}var r=t(235),o=t(244),a=t(247),s=t(258),c=t(330),u=t(331),p=t(334),l=t(337);e.exports=i},{235:235,244:244,247:247,258:258,330:330,331:331,334:334,337:337}],347:[function(t,e,n){function i(t){return r(t,o(t))}var r=t(279),o=t(340);e.exports=i},{279:279,340:340}],348:[function(t,e,n){function i(t){return t}e.exports=i},{}],349:[function(t,e,n){function i(){}e.exports=i},{}],350:[function(t,e,n){function i(t){return a(t)?r(t):o(t)}var r=t(270),o=t(271),a=t(311);e.exports=i},{270:270,271:271,311:311}],351:[function(t,e,n){e.exports=function(t,e,n){return 2==arguments.length?t.getAttribute(e):null===n?t.removeAttribute(e):(t.setAttribute(e,n),t)}},{}],352:[function(t,e,n){e.exports=t(361)},{361:361}],353:[function(t,e,n){e.exports=function(t){for(var e;t.childNodes.length;)e=t.childNodes[0],t.removeChild(e);return t}},{}],354:[function(t,e,n){e.exports=t(363)},{363:363}],355:[function(t,e,n){e.exports=t(364)},{364:364}],356:[function(t,e,n){e.exports=t(368)},{368:368}],357:[function(t,e,n){e.exports=t(365)},{365:365}],358:[function(t,e,n){e.exports=t(366)},{366:366}],359:[function(t,e,n){e.exports=t(367)},{367:367}],360:[function(t,e,n){e.exports=function(t){t.parentNode&&t.parentNode.removeChild(t)}},{}],361:[function(t,e,n){function i(t){if(!t||!t.nodeType)throw new Error("A DOM element reference is required");this.el=t,this.list=t.classList}var r=t(362),o=/\s+/,a=Object.prototype.toString;e.exports=function(t){return new i(t)},i.prototype.add=function(t){if(this.list)return this.list.add(t),this;var e=this.array(),n=r(e,t);return~n||e.push(t),this.el.className=e.join(" "),this},i.prototype.remove=function(t){if("[object RegExp]"==a.call(t))return this.removeMatching(t);if(this.list)return this.list.remove(t),this;var e=this.array(),n=r(e,t);return~n&&e.splice(n,1),this.el.className=e.join(" "),this},i.prototype.removeMatching=function(t){for(var e=this.array(),n=0;n<e.length;n++)t.test(e[n])&&this.remove(e[n]);return this},i.prototype.toggle=function(t,e){return this.list?("undefined"!=typeof e?e!==this.list.toggle(t,e)&&this.list.toggle(t):this.list.toggle(t),this):("undefined"!=typeof e?e?this.add(t):this.remove(t):this.has(t)?this.remove(t):this.add(t),this)},i.prototype.array=function(){var t=this.el.getAttribute("class")||"",e=t.replace(/^\s+|\s+$/g,""),n=e.split(o);return""===n[0]&&n.shift(),n},i.prototype.has=i.prototype.contains=function(t){return this.list?this.list.contains(t):!!~r(this.array(),t)}},{362:362}],362:[function(t,e,n){e.exports=function(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0;n<t.length;++n)if(t[n]===e)return n;return-1}},{}],363:[function(t,e,n){var i=t(366);e.exports=function(t,e,n,r){for(t=n?{parentNode:t}:t,r=r||document;(t=t.parentNode)&&t!==document;){if(i(t,e))return t;if(t===r)return}}},{366:366}],364:[function(t,e,n){var i=t(363),r=t(365);n.bind=function(t,e,n,o,a){return r.bind(t,n,function(n){var r=n.target||n.srcElement;n.delegateTarget=i(r,e,!0,t),n.delegateTarget&&o.call(t,n)},a)},n.unbind=function(t,e,n,i){r.unbind(t,e,n,i)}},{363:363,365:365}],365:[function(t,e,n){var i=window.addEventListener?"addEventListener":"attachEvent",r=window.removeEventListener?"removeEventListener":"detachEvent",o="addEventListener"!==i?"on":"";n.bind=function(t,e,n,r){return t[i](o+e,n,r||!1),n},n.unbind=function(t,e,n,i){return t[r](o+e,n,i||!1),n}},{}],366:[function(t,e,n){function i(t,e){if(!t||1!==t.nodeType)return!1;if(a)return a.call(t,e);for(var n=r.all(e,t.parentNode),i=0;i<n.length;++i)if(n[i]==t)return!0;return!1}var r=t(367),o=Element.prototype,a=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.msMatchesSelector||o.oMatchesSelector;e.exports=i},{367:367}],367:[function(t,e,n){function i(t,e){return e.querySelector(t)}n=e.exports=function(t,e){return e=e||document,i(t,e)},n.all=function(t,e){return e=e||document,e.querySelectorAll(t)},n.engine=function(t){if(!t.one)throw new Error(".one callback required");if(!t.all)throw new Error(".all callback required");return i=t.one,n.all=t.all,n}},{}],368:[function(t,e,n){function i(t,e){if("string"!=typeof t)throw new TypeError("String expected");e||(e=document);var n=/<([\w:]+)/.exec(t);if(!n)return e.createTextNode(t);t=t.replace(/^\s+|\s+$/g,"");var i=n[1];if("body"==i){var r=e.createElement("html");return r.innerHTML=t,r.removeChild(r.lastChild)}var o=a[i]||a._default,s=o[0],c=o[1],u=o[2],r=e.createElement("div");for(r.innerHTML=c+t+u;s--;)r=r.lastChild;if(r.firstChild==r.lastChild)return r.removeChild(r.firstChild);for(var p=e.createDocumentFragment();r.firstChild;)p.appendChild(r.removeChild(r.firstChild));return p}e.exports=i;var r=document.createElement("div");r.innerHTML='  <link/><table></table><a href="/a">a</a><input type="checkbox"/>';var o=!r.getElementsByTagName("link").length;r=void 0;var a={legend:[1,"<fieldset>","</fieldset>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],_default:o?[1,"X<div>","</div>"]:[0,"",""]};a.td=a.th=[3,"<table><tbody><tr>","</tr></tbody></table>"],a.option=a.optgroup=[1,'<select multiple="multiple">',"</select>"],a.thead=a.tbody=a.colgroup=a.caption=a.tfoot=[1,"<table>","</table>"],a.polyline=a.ellipse=a.polygon=a.circle=a.text=a.line=a.path=a.rect=a.g=[1,'<svg xmlns="http://www.w3.org/2000/svg" version="1.1">',"</svg>"]},{}],369:[function(t,e,n){e.exports=t(371),e.exports.Collection=t(370)},{370:370,371:371}],370:[function(t,e,n){"use strict";function i(t,e,n,i){var r=n.inverse;return t.remove=function(t){var n=this.indexOf(t);return-1!==n&&(this.splice(n,1),e.unset(t,r,i)),t},t.contains=function(t){return-1!==this.indexOf(t)},t.add=function(t){this.contains(t)||(this.push(t),e.set(t,r,i))},t}e.exports.extend=i},{}],371:[function(t,e,n){"use strict";function i(t,e){return Object.prototype.hasOwnProperty.call(t,e.name||e)}function r(t,e,n){Object.defineProperty(n,e.name,{enumerable:e.enumerable,value:s.extend(n[e.name]||[],t,e,n)})}function o(t,e,n){var i=e.inverse,r=n[e.name];Object.defineProperty(n,e.name,{enumerable:e.enumerable,get:function(){return r},set:function(e){if(e!==r){var o=r;r=null,o&&t.unset(o,i,n),r=e,t.set(r,i,n)}}})}function a(t,e){return this instanceof a?(t.inverse=e,e.inverse=t,this.props={},this.props[t.name]=t,void(this.props[e.name]=e)):new a(t,e)}var s=t(370);a.prototype.bind=function(t,e){if("string"==typeof e){if(!this.props[e])throw new Error("no property <"+e+"> in ref");e=this.props[e]}e.collection?r(this,e,t):o(this,e,t)},a.prototype.ensureBound=function(t,e){i(t,e)||this.bind(t,e)},a.prototype.unset=function(t,e,n){t&&(this.ensureBound(t,e),e.collection?t[e.name].remove(n):t[e.name]=void 0)},a.prototype.set=function(t,e,n){t&&(this.ensureBound(t,e),e.collection?t[e.name].add(n):t[e.name]=n)},e.exports=a},{370:370}]},{},[1])(1)});