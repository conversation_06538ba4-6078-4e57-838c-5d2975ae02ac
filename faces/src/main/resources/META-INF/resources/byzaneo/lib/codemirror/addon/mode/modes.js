CodeMirror.defineMode("clike",function(e,t){function c(e,t){var n=e.next();if(u[n]){var a=u[n](e,t);if(a!==!1)return a}if(n=='"'||n=="'")return t.tokenize=h(n),t.tokenize(e,t);if(/[\[\]{}\(\),;\:\.]/.test(n))return l=n,null;if(/\d/.test(n))return e.eatWhile(/[\w\.]/),"number";if(n=="/"){if(e.eat("*"))return t.tokenize=p,p(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(f.test(n))return e.eatWhile(f),"operator";e.eatWhile(/[\w\$_]/);var c=e.current();return r.propertyIsEnumerable(c)?(s.propertyIsEnumerable(c)&&(l="newstatement"),"keyword"):i.propertyIsEnumerable(c)?(s.propertyIsEnumerable(c)&&(l="newstatement"),"builtin"):o.propertyIsEnumerable(c)?"atom":"variable"}function h(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r&&!a)n.tokenize=null;return"string"}}function p(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=null;break}n=r=="*"}return"comment"}function d(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function v(e,t,n){return e.context=new d(e.indented,t,n,null,e.context)}function m(e){var t=e.context.type;if(t==")"||t=="]"||t=="}")e.indented=e.context.indented;return e.context=e.context.prev}var n=e.indentUnit,r=t.keywords||{},i=t.builtin||{},s=t.blockKeywords||{},o=t.atoms||{},u=t.hooks||{},a=t.multiLineStrings,f=/[+\-*&%=<>!?|\/]/,l;return{startState:function(e){return{tokenize:null,context:new d((e||0)-n,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,t){var n=t.context;e.sol()&&(n.align==null&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0);if(e.eatSpace())return null;l=null;var r=(t.tokenize||c)(e,t);if(r=="comment"||r=="meta")return r;n.align==null&&(n.align=!0);if(l!=";"&&l!=":"||n.type!="statement")if(l=="{")v(t,e.column(),"}");else if(l=="[")v(t,e.column(),"]");else if(l=="(")v(t,e.column(),")");else if(l=="}"){while(n.type=="statement")n=m(t);n.type=="}"&&(n=m(t));while(n.type=="statement")n=m(t)}else l==n.type?m(t):(n.type=="}"||n.type=="top"||n.type=="statement"&&l=="newstatement")&&v(t,e.column(),"statement");else m(t);return t.startOfLine=!1,r},indent:function(e,t){if(e.tokenize==p)return CodeMirror.Pass;if(e.tokenize!=c&&e.tokenize!=null)return 0;var r=e.context,i=t&&t.charAt(0);r.type=="statement"&&i=="}"&&(r=r.prev);var s=i==r.type;return r.type=="statement"?r.indented+(i=="{"?0:n):r.align?r.column+(s?0:1):r.indented+(s?0:n)},electricChars:"{}"}}),function(){function e(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function n(e,t){return t.startOfLine?(e.skipToEnd(),"meta"):!1}function r(e,t){var n;while((n=e.next())!=null)if(n=='"'&&!e.eat('"')){t.tokenize=null;break}return"string"}function i(e,t){for(var n=0;n<e.length;++n)CodeMirror.defineMIME(e[n],t)}var t="auto if break int case long char register continue return default short do sizeof double static else struct entry switch extern typedef float union for unsigned goto while enum void const signed volatile";i(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:e(t),blockKeywords:e("case do else for if switch while struct"),atoms:e("null"),hooks:{"#":n}}),i(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:e(t+" asm dynamic_cast namespace reinterpret_cast try bool explicit new "+"static_cast typeid catch operator template typename class friend private "+"this using const_cast inline public throw virtual delete mutable protected "+"wchar_t"),blockKeywords:e("catch class do else finally for if struct switch try while"),atoms:e("true false null"),hooks:{"#":n}}),CodeMirror.defineMIME("text/x-java",{name:"clike",keywords:e("abstract assert boolean break byte case catch char class const continue default do double else enum extends final finally float for goto if implements import instanceof int interface long native new package private protected public return short static strictfp super switch synchronized this throw throws transient try void volatile while"),blockKeywords:e("catch class do else finally for if switch try while"),atoms:e("true false null"),hooks:{"@":function(e,t){return e.eatWhile(/[\w\$_]/),"meta"}}}),CodeMirror.defineMIME("text/x-csharp",{name:"clike",keywords:e("abstract as base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),blockKeywords:e("catch class do else finally for foreach if struct switch try while"),builtin:e("Boolean Byte Char DateTime DateTimeOffset Decimal Double Guid Int16 Int32 Int64 Object SByte Single String TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),atoms:e("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=r,r(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),CodeMirror.defineMIME("text/x-scala",{name:"clike",keywords:e("abstract case catch class def do else extends false final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try trye type val var while with yield _ : = => <- <: <% >: # @ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector :: #:: Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),blockKeywords:e("catch class do else finally for forSome if match switch try while"),atoms:e("true false null"),hooks:{"@":function(e,t){return e.eatWhile(/[\w\$_]/),"meta"}}})}(),CodeMirror.defineMode("clojure",function(e,t){function h(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function y(e,t,n){this.indent=e,this.type=t,this.prev=n}function b(e,t,n){e.indentStack=new y(t,n,e.indentStack)}function w(e){e.indentStack=e.indentStack.prev}function E(e,t){return e==="0"&&t.eat(/x/i)?(t.eatWhile(g.hex),!0):((e=="+"||e=="-")&&g.digit.test(t.peek())&&(t.eat(g.sign),e=t.next()),g.digit.test(e)?(t.eat(e),t.eatWhile(g.digit),"."==t.peek()&&(t.eat("."),t.eatWhile(g.digit)),t.eat(g.exponent)&&(t.eat(g.sign),t.eatWhile(g.digit)),!0):!1)}var n="builtin",r="comment",i="string",s="tag",o="atom",u="number",a="bracket",f="keyword",l=2,c=1,p=h("true false nil"),d=h("defn defn- def def- defonce defmulti defmethod defmacro defstruct deftype defprotocol defrecord defproject deftest slice defalias defhinted defmacro- defn-memo defnk defnk defonce- defunbound defunbound- defvar defvar- let letfn do case cond condp for loop recur when when-not when-let when-first if if-let if-not . .. -> ->> doto and or dosync doseq dotimes dorun doall load import unimport ns in-ns refer try catch finally throw with-open with-local-vars binding gen-class gen-and-load-class gen-and-save-class handler-case handle"),v=h("* *1 *2 *3 *agent* *allow-unresolved-vars* *assert *clojure-version* *command-line-args* *compile-files* *compile-path* *e *err* *file* *flush-on-newline* *in* *macro-meta* *math-context* *ns* *out* *print-dup* *print-length* *print-level* *print-meta* *print-readably* *read-eval* *source-path* *use-context-classloader* *warn-on-reflection* + - / < <= = == > >= accessor aclone agent agent-errors aget alength alias all-ns alter alter-meta! alter-var-root amap ancestors and apply areduce array-map aset aset-boolean aset-byte aset-char aset-double aset-float aset-int aset-long aset-short assert assoc assoc! assoc-in associative? atom await await-for await1 bases bean bigdec bigint binding bit-and bit-and-not bit-clear bit-flip bit-not bit-or bit-set bit-shift-left bit-shift-right bit-test bit-xor boolean boolean-array booleans bound-fn bound-fn* butlast byte byte-array bytes case cast char char-array char-escape-string char-name-string char? chars chunk chunk-append chunk-buffer chunk-cons chunk-first chunk-next chunk-rest chunked-seq? class class? clear-agent-errors clojure-version coll? comment commute comp comparator compare compare-and-set! compile complement concat cond condp conj conj! cons constantly construct-proxy contains? count counted? create-ns create-struct cycle dec decimal? declare definline defmacro defmethod defmulti defn defn- defonce defstruct delay delay? deliver deref derive descendants destructure disj disj! dissoc dissoc! distinct distinct? doall doc dorun doseq dosync dotimes doto double double-array doubles drop drop-last drop-while empty empty? ensure enumeration-seq eval even? every? extend extend-protocol extend-type extends? extenders false? ffirst file-seq filter find find-doc find-ns find-var first float float-array float? floats flush fn fn? fnext for force format future future-call future-cancel future-cancelled? future-done? future? gen-class gen-interface gensym get get-in get-method get-proxy-class get-thread-bindings get-validator hash hash-map hash-set identical? identity if-let if-not ifn? import in-ns inc init-proxy instance? int int-array integer? interleave intern interpose into into-array ints io! isa? iterate iterator-seq juxt key keys keyword keyword? last lazy-cat lazy-seq let letfn line-seq list list* list? load load-file load-reader load-string loaded-libs locking long long-array longs loop macroexpand macroexpand-1 make-array make-hierarchy map map? mapcat max max-key memfn memoize merge merge-with meta method-sig methods min min-key mod name namespace neg? newline next nfirst nil? nnext not not-any? not-empty not-every? not= ns ns-aliases ns-imports ns-interns ns-map ns-name ns-publics ns-refers ns-resolve ns-unalias ns-unmap nth nthnext num number? odd? or parents partial partition pcalls peek persistent! pmap pop pop! pop-thread-bindings pos? pr pr-str prefer-method prefers primitives-classnames print print-ctor print-doc print-dup print-method print-namespace-doc print-simple print-special-doc print-str printf println println-str prn prn-str promise proxy proxy-call-with-super proxy-mappings proxy-name proxy-super push-thread-bindings pvalues quot rand rand-int range ratio? rational? rationalize re-find re-groups re-matcher re-matches re-pattern re-seq read read-line read-string reify reduce ref ref-history-count ref-max-history ref-min-history ref-set refer refer-clojure release-pending-sends rem remove remove-method remove-ns repeat repeatedly replace replicate require reset! reset-meta! resolve rest resultset-seq reverse reversible? rseq rsubseq satisfies? second select-keys send send-off seq seq? seque sequence sequential? set set-validator! set? short short-array shorts shutdown-agents slurp some sort sort-by sorted-map sorted-map-by sorted-set sorted-set-by sorted? special-form-anchor special-symbol? split-at split-with str stream? string? struct struct-map subs subseq subvec supers swap! symbol symbol? sync syntax-symbol-anchor take take-last take-nth take-while test the-ns time to-array to-array-2d trampoline transient tree-seq true? type unchecked-add unchecked-dec unchecked-divide unchecked-inc unchecked-multiply unchecked-negate unchecked-remainder unchecked-subtract underive unquote unquote-splicing update-in update-proxy use val vals var-get var-set var? vary-meta vec vector vector? when when-first when-let when-not while with-bindings with-bindings* with-in-str with-loading-context with-local-vars with-meta with-open with-out-str with-precision xml-seq"),m=h("ns fn def defn defmethod bound-fn if if-not case condp when while when-not when-first do future comment doto locking proxy with-open with-precision reify deftype defrecord defprotocol extend extend-protocol extend-type try catch let letfn binding loop for doseq dotimes when-let if-let defstruct struct-map assoc testing deftest handler-case handle dotrace deftrace"),g={digit:/\d/,digit_or_colon:/[\d:]/,hex:/[0-9a-f]/i,sign:/[+-]/,exponent:/e/i,keyword_char:/[^\s\(\[\;\)\]]/,basic:/[\w\$_\-]/,lang_keyword:/[\w*+!\-_?:\/]/};return{startState:function(){return{indentStack:null,indentation:0,mode:!1}},token:function(e,t){t.indentStack==null&&e.sol()&&(t.indentation=e.indentation());if(e.eatSpace())return null;var s=null;switch(t.mode){case"string":var c,h=!1;while((c=e.next())!=null){if(c=='"'&&!h){t.mode=!1;break}h=!h&&c=="\\"}s=i;break;default:var y=e.next();if(y=='"')t.mode="string",s=i;else if(y=="'"&&!g.digit_or_colon.test(e.peek()))s=o;else if(y==";")e.skipToEnd(),s=r;else if(E(y,e))s=u;else if(y=="("||y=="["){var S="",x=e.column(),T;if(y=="(")while((T=e.eat(g.keyword_char))!=null)S+=T;S.length>0&&(m.propertyIsEnumerable(S)||/^(?:def|with)/.test(S))?b(t,x+l,y):(e.eatSpace(),e.eol()||e.peek()==";"?b(t,x+1,y):b(t,x+e.current().length,y)),e.backUp(e.current().length-1),s=a}else if(y==")"||y=="]")s=a,t.indentStack!=null&&t.indentStack.type==(y==")"?"(":"[")&&w(t);else{if(y==":")return e.eatWhile(g.lang_keyword),o;e.eatWhile(g.basic),d&&d.propertyIsEnumerable(e.current())?s=f:v&&v.propertyIsEnumerable(e.current())?s=n:p&&p.propertyIsEnumerable(e.current())?s=o:s=null}}return s},indent:function(e,t){return e.indentStack==null?e.indentation:e.indentStack.indent}}}),CodeMirror.defineMIME("text/x-clojure","clojure"),CodeMirror.defineMode("coffeescript",function(e){function n(e){return new RegExp("^(("+e.join(")|(")+"))\\b")}function y(e,n){if(e.sol()){var c=n.scopes[0].offset;if(e.eatSpace()){var h=e.indentation();return h>c?"indent":h<c?"dedent":null}c>0&&S(e,n)}if(e.eatSpace())return null;var m=e.peek();if(e.match("####"))return e.skipToEnd(),"comment";if(e.match("###"))return n.tokenize=w,n.tokenize(e,n);if(m==="#")return e.skipToEnd(),"comment";if(e.match(/^-?[0-9\.]/,!1)){var y=!1;e.match(/^-?\d*\.\d+(e[\+\-]?\d+)?/i)&&(y=!0),e.match(/^-?\d+\.\d*/)&&(y=!0),e.match(/^-?\.\d+/)&&(y=!0);if(y)return e.peek()=="."&&e.backUp(1),"number";var E=!1;e.match(/^-?0x[0-9a-f]+/i)&&(E=!0),e.match(/^-?[1-9]\d*(e[\+\-]?\d+)?/)&&(E=!0),e.match(/^-?0(?![\dx])/i)&&(E=!0);if(E)return"number"}if(e.match(d))return n.tokenize=b(e.current(),"string"),n.tokenize(e,n);if(e.match(v)){if(e.current()!="/"||e.match(/^.*\//,!1))return n.tokenize=b(e.current(),"string-2"),n.tokenize(e,n);e.backUp(1)}return e.match(u)||e.match(o)?"punctuation":e.match(s)||e.match(r)||e.match(l)?"operator":e.match(i)?"punctuation":e.match(g)?"atom":e.match(p)?"keyword":e.match(a)?"variable":e.match(f)?"property":(e.next(),t)}function b(n,r){var i=n.length==1;return function(o,u){while(!o.eol()){o.eatWhile(/[^'"\/\\]/);if(o.eat("\\")){o.next();if(i&&o.eol())return r}else{if(o.match(n))return u.tokenize=y,r;o.eat(/['"\/]/)}}return i&&(e.mode.singleLineStringErrors?r=t:u.tokenize=y),r}}function w(e,t){while(!e.eol()){e.eatWhile(/[^#]/);if(e.match("###")){t.tokenize=y;break}e.eatWhile("#")}return"comment"}function E(t,n,r){r=r||"coffee";var i=0;if(r==="coffee"){for(var s=0;s<n.scopes.length;s++)if(n.scopes[s].type==="coffee"){i=n.scopes[s].offset+e.indentUnit;break}}else i=t.column()+t.current().length;n.scopes.unshift({offset:i,type:r})}function S(e,t){if(t.scopes.length==1)return;if(t.scopes[0].type==="coffee"){var n=e.indentation(),r=-1;for(var i=0;i<t.scopes.length;++i)if(n===t.scopes[i].offset){r=i;break}if(r===-1)return!0;while(t.scopes[0].offset!==n)t.scopes.shift();return!1}return t.scopes.shift(),!1}function x(e,n){var r=n.tokenize(e,n),i=e.current();if(i===".")return r=n.tokenize(e,n),i=e.current(),r==="variable"?"variable":t;i==="return"&&(n.dedent+=1),((i==="->"||i==="=>")&&!n.lambda&&n.scopes[0].type=="coffee"&&e.peek()===""||r==="indent")&&E(e,n);var s="[({".indexOf(i);return s!==-1&&E(e,n,"])}".slice(s,s+1)),c.exec(i)&&E(e,n),i=="then"&&S(e,n),r==="dedent"&&S(e,n)?t:(s="])}".indexOf(i),s!==-1&&S(e,n)?t:(n.dedent>0&&e.eol()&&n.scopes[0].type=="coffee"&&(n.scopes.length>1&&n.scopes.shift(),n.dedent-=1),r))}var t="error",r=new RegExp("^[\\+\\-\\*/%&|\\^~<>!?]"),i=new RegExp("^[\\(\\)\\[\\]\\{\\},:`=;\\.]"),s=new RegExp("^((->)|(=>)|(\\+\\+)|(\\+\\=)|(\\-\\-)|(\\-\\=)|(\\*\\*)|(\\*\\=)|(\\/\\/)|(\\/\\=)|(==)|(!=)|(<=)|(>=)|(<>)|(<<)|(>>)|(//))"),o=new RegExp("^((\\.\\.)|(\\+=)|(\\-=)|(\\*=)|(%=)|(/=)|(&=)|(\\|=)|(\\^=))"),u=new RegExp("^((\\.\\.\\.)|(//=)|(>>=)|(<<=)|(\\*\\*=))"),a=new RegExp("^[_A-Za-z$][_A-Za-z$0-9]*"),f=new RegExp("^(@|this.)[_A-Za-z$][_A-Za-z$0-9]*"),l=n(["and","or","not","is","isnt","in","instanceof","typeof"]),c=["for","while","loop","if","unless","else","switch","try","catch","finally","class"],h=["break","by","continue","debugger","delete","do","in","of","new","return","then","this","throw","when","until"],p=n(c.concat(h));c=n(c);var d=new RegExp("^('{3}|\"{3}|['\"])"),v=new RegExp("^(/{3}|/)"),m=["Infinity","NaN","undefined","null","true","false","on","off","yes","no"],g=n(m),T={startState:function(e){return{tokenize:y,scopes:[{offset:e||0,type:"coffee"}],lastToken:null,lambda:!1,dedent:0}},token:function(e,t){var n=x(e,t);return t.lastToken={style:n,content:e.current()},e.eol()&&e.lambda&&(t.lambda=!1),n},indent:function(e,t){return e.tokenize!=y?0:e.scopes[0].offset}};return T}),CodeMirror.defineMIME("text/x-coffeescript","coffeescript"),CodeMirror.defineMode("commonlisp",function(e){function s(e){var t;while(t=e.next())if(t=="\\")e.next();else if(!r.test(t)){e.backUp(1);break}return e.current()}function o(e,t){if(e.eatSpace())return i="ws",null;if(e.match(n))return"number";var r=e.next();r=="\\"&&(r=e.next());if(r=='"')return(t.tokenize=u)(e,t);if(r=="(")return i="open","bracket";if(r==")"||r=="]")return i="close","bracket";if(r==";")return e.skipToEnd(),i="ws","comment";if(/['`,@]/.test(r))return null;if(r=="|")return e.skipTo("|")?(e.next(),"symbol"):(e.skipToEnd(),"error");if(r=="#"){var r=e.next();return r=="["?(i="open","bracket"):/[+\-=\.']/.test(r)?null:/\d/.test(r)&&e.match(/^\d*#/)?null:r=="|"?(t.tokenize=a)(e,t):r==":"?(s(e),"meta"):"error"}var o=s(e);return o=="."?null:(i="symbol",o=="nil"||o=="t"?"atom":o.charAt(0)==":"?"keyword":o.charAt(0)=="&"?"variable-2":"variable")}function u(e,t){var n=!1,r;while(r=e.next()){if(r=='"'&&!n){t.tokenize=o;break}n=!n&&r=="\\"}return"string"}function a(e,t){var n,r;while(n=e.next()){if(n=="#"&&r=="|"){t.tokenize=o;break}r=n}return i="ws","comment"}var t=/^with|^def|^do|^prog|case$|^cond$|bind$|when$|unless$/,n=/^(?:[+\-]?(?:\d+|\d*\.\d+)(?:[efd][+\-]?\d+)?|[+\-]?\d+(?:\/[+\-]?\d+)?|#b[+\-]?[01]+|#o[+\-]?[0-7]+|#x[+\-]?[\da-f]+)/,r=/[^\s'`,@()\[\]";]/,i;return{startState:function(){return{ctx:{prev:null,start:0,indentTo:0},tokenize:o}},token:function(n,r){n.sol()&&typeof r.ctx.indentTo!="number"&&(r.ctx.indentTo=r.ctx.start+1),i=null;var s=r.tokenize(n,r);return i!="ws"&&(r.ctx.indentTo==null?i=="symbol"&&t.test(n.current())?r.ctx.indentTo=r.ctx.start+e.indentUnit:r.ctx.indentTo="next":r.ctx.indentTo=="next"&&(r.ctx.indentTo=n.column())),i=="open"?r.ctx={prev:r.ctx,start:n.column(),indentTo:null}:i=="close"&&(r.ctx=r.ctx.prev||r.ctx),s},indent:function(e,t){var n=e.ctx.indentTo;return typeof n=="number"?n:e.ctx.start+1}}}),CodeMirror.defineMIME("text/x-common-lisp","commonlisp"),CodeMirror.defineMode("css",function(e){function a(e){var t={};for(var n=0;n<e.length;++n)t[e[n]]=!0;return t}function f(e,t){return n=t,e}function l(e,t){var r=e.next();if(r=="@")return e.eatWhile(/[\w\\\-]/),f("def",e.current());if(r=="/"&&e.eat("*"))return t.tokenize=c,c(e,t);if(r=="<"&&e.eat("!"))return t.tokenize=h,h(e,t);if(r=="=")f(null,"compare");else{if(!(r!="~"&&r!="|"||!e.eat("=")))return f(null,"compare");if(r=='"'||r=="'")return t.tokenize=p(r),t.tokenize(e,t);if(r=="#")return e.eatWhile(/[\w\\\-]/),f("atom","hash");if(r=="!")return e.match(/^\s*\w*/),f("keyword","important");if(/\d/.test(r))return e.eatWhile(/[\w.%]/),f("number","unit");if(r!=="-")return/[,+>*\/]/.test(r)?f(null,"select-op"):r=="."&&e.match(/^\w+/)?f("qualifier",n):r==":"?f("operator",r):/[;{}\[\]\(\)]/.test(r)?f(null,r):(e.eatWhile(/[\w\\\-]/),f("property","variable"));if(/\d/.test(e.peek()))return e.eatWhile(/[\w.%]/),f("number","unit");if(e.match(/^[^-]+-/))return f("meta",n)}}function c(e,t){var n=!1,r;while((r=e.next())!=null){if(n&&r=="/"){t.tokenize=l;break}n=r=="*"}return f("comment","comment")}function h(e,t){var n=0,r;while((r=e.next())!=null){if(n>=2&&r==">"){t.tokenize=l;break}n=r=="-"?n+1:0}return f("comment","comment")}function p(e){return function(t,n){var r=!1,i;while((i=t.next())!=null){if(i==e&&!r)break;r=!r&&i=="\\"}return r||(n.tokenize=l),f("string","string")}}var t=e.indentUnit,n,r=a(["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"]),i=a(["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid"]),s=a(["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid-cell","grid-column","grid-column-align","grid-column-sizing","grid-column-span","grid-columns","grid-flow","grid-row","grid-row-align","grid-row-sizing","grid-row-span","grid-rows","grid-template","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marker-offset","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","play-during","position","presentation-level","punctuation-trim","quotes","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-shadow","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","word-break","word-spacing","word-wrap","z-index"]),o=a(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua"]),u=a(["above","absolute","activeborder","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","auto","avoid","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break-all","break-word","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","compact","condensed","contain","content","content-box","context-menu","continuous","copy","cover","crop","cross","crosshair","currentcolor","cursive","dashed","decimal","decimal-leading-zero","default","default-button","destination-atop","destination-in","destination-out","destination-over","devanagari","disc","discard","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ew-resize","expanded","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","footnotes","forwards","from","geometricPrecision","georgian","graytext","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-table","inset","inside","intrinsic","invert","italic","justify","kannada","katakana","katakana-iroha","khmer","landscape","lao","large","larger","left","level","lighter","line-through","linear","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","malayalam","match","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","myanmar","n-resize","narrower","navy","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","nw-resize","nwse-resize","oblique","octal","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","overlay","overline","padding","padding-box","painted","paused","persian","plus-darker","plus-lighter","pointer","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radio","read-only","read-write","read-write-plaintext-only","relative","repeat","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","round","row-resize","rtl","run-in","running","s-resize","sans-serif","scroll","scrollbar","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","semi-condensed","semi-expanded","separate","serif","show","sidama","single","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","solid","somali","source-atop","source-in","source-out","source-over","space","square","square-button","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","transparent","ultra-condensed","ultra-expanded","underline","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","white","wider","window","windowframe","windowtext","x-large","x-small","xor","xx-large","xx-small","yellow"]);return{startState:function(e){return{tokenize:l,baseIndent:e||0,stack:[]}},token:function(e,t){if(e.eatSpace())return null;var a=t.tokenize(e,t),f=t.stack[t.stack.length-1];return a=="property"?f=="propertyValue"?u[e.current()]?a="string-2":o[e.current()]?a="keyword":a="variable-2":f=="rule"?s[e.current()]||(a+=" error"):!f||f=="@media{"?a="tag":f=="@media"?r[e.current()]?a="attribute":/^(only|not)$/i.test(e.current())?a="keyword":e.current().toLowerCase()=="and"?a="error":i[e.current()]?a="error":a="attribute error":f=="@mediaType"?r[e.current()]?a="attribute":e.current().toLowerCase()=="and"?a="operator":/^(only|not)$/i.test(e.current())?a="error":i[e.current()]?a="error":a="error":f=="@mediaType("?s[e.current()]||(r[e.current()]?a="error":e.current().toLowerCase()=="and"?a="operator":/^(only|not)$/i.test(e.current())?a="error":a+=" error"):a="error":a=="atom"?!f||f=="@media{"?a="builtin":f=="propertyValue"?/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e.current())||(a+=" error"):a="error":f=="@media"&&n=="{"&&(a="error"),n=="{"?f=="@media"||f=="@mediaType"?(t.stack.pop(),t.stack[t.stack.length-1]="@media{"):t.stack.push("rule"):n=="}"?(t.stack.pop(),f=="propertyValue"&&t.stack.pop()):n=="@media"?t.stack.push("@media"):f=="@media"&&/\b(keyword|attribute)\b/.test(a)?t.stack.push("@mediaType"):f=="@mediaType"&&e.current()==","?t.stack.pop():f=="@mediaType"&&n=="("?t.stack.push("@mediaType("):f=="@mediaType("&&n==")"?t.stack.pop():f=="rule"&&n==":"?t.stack.push("propertyValue"):f=="propertyValue"&&n==";"&&t.stack.pop(),a},indent:function(e,n){var r=e.stack.length;return/^\}/.test(n)&&(r-=e.stack[e.stack.length-1]=="propertyValue"?2:1),e.baseIndent+r*t},electricChars:"}"}}),CodeMirror.defineMIME("text/css","css"),CodeMirror.defineMode("diff",function(){var e={"+":"tag","-":"string","@":"meta"};return{token:function(t){var n=t.string.search(/[\t ]+?$/);if(!t.sol()||n===0)return t.skipToEnd(),("error "+(e[t.string.charAt(0)]||"")).replace(/ $/,"");var r=e[t.peek()]||t.skipToEnd();return n===-1?t.skipToEnd():t.pos=n,r}}}),CodeMirror.defineMIME("text/x-diff","diff"),CodeMirror.defineMode("ecl",function(e){function t(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function n(e,t){return t.startOfLine?(e.skipToEnd(),"meta"):!1}function r(e,t){var n;while((n=e.next())!=null)if(n=='"'&&!e.eat('"')){t.tokenize=null;break}return"string"}function m(e,t){var n=e.next();if(h[n]){var r=h[n](e,t);if(r!==!1)return r}if(n=='"'||n=="'")return t.tokenize=g(n),t.tokenize(e,t);if(/[\[\]{}\(\),;\:\.]/.test(n))return v=n,null;if(/\d/.test(n))return e.eatWhile(/[\w\.]/),"number";if(n=="/"){if(e.eat("*"))return t.tokenize=y,y(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(d.test(n))return e.eatWhile(d),"operator";e.eatWhile(/[\w\$_]/);var i=e.current().toLowerCase();if(s.propertyIsEnumerable(i))return l.propertyIsEnumerable(i)&&(v="newstatement"),"keyword";if(o.propertyIsEnumerable(i))return l.propertyIsEnumerable(i)&&(v="newstatement"),"variable";if(u.propertyIsEnumerable(i))return l.propertyIsEnumerable(i)&&(v="newstatement"),"variable-2";if(a.propertyIsEnumerable(i))return l.propertyIsEnumerable(i)&&(v="newstatement"),"variable-3";if(f.propertyIsEnumerable(i))return l.propertyIsEnumerable(i)&&(v="newstatement"),"builtin";var p=i.length-1;while(p>=0&&(!isNaN(i[p])||i[p]=="_"))--p;if(p>0){var m=i.substr(0,p+1);if(a.propertyIsEnumerable(m))return l.propertyIsEnumerable(m)&&(v="newstatement"),"variable-3"}return c.propertyIsEnumerable(i)?"atom":null}function g(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r&&!p)n.tokenize=m;return"string"}}function y(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=m;break}n=r=="*"}return"comment"}function b(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function w(e,t,n){return e.context=new b(e.indented,t,n,null,e.context)}function E(e){var t=e.context.type;if(t==")"||t=="]"||t=="}")e.indented=e.context.indented;return e.context=e.context.prev}var i=e.indentUnit,s=t("abs acos allnodes ascii asin asstring atan atan2 ave case choose choosen choosesets clustersize combine correlation cos cosh count covariance cron dataset dedup define denormalize distribute distributed distribution ebcdic enth error evaluate event eventextra eventname exists exp failcode failmessage fetch fromunicode getisvalid global graph group hash hash32 hash64 hashcrc hashmd5 having if index intformat isvalid iterate join keyunicode length library limit ln local log loop map matched matchlength matchposition matchtext matchunicode max merge mergejoin min nolocal nonempty normalize parse pipe power preload process project pull random range rank ranked realformat recordof regexfind regexreplace regroup rejected rollup round roundup row rowdiff sample set sin sinh sizeof soapcall sort sorted sqrt stepped stored sum table tan tanh thisnode topn tounicode transfer trim truncate typeof ungroup unicodeorder variance which workunit xmldecode xmlencode xmltext xmlunicode"),o=t("apply assert build buildindex evaluate fail keydiff keypatch loadxml nothor notify output parallel sequential soapcall wait"),u=t("__compressed__ all and any as atmost before beginc++ best between case const counter csv descend encrypt end endc++ endmacro except exclusive expire export extend false few first flat from full function group header heading hole ifblock import in interface joined keep keyed last left limit load local locale lookup macro many maxcount maxlength min skew module named nocase noroot noscan nosort not of only opt or outer overwrite packed partition penalty physicallength pipe quote record relationship repeat return right scan self separator service shared skew skip sql store terminator thor threshold token transform trim true type unicodeorder unsorted validate virtual whole wild within xml xpath"),a=t("ascii big_endian boolean data decimal ebcdic integer pattern qstring real record rule set of string token udecimal unicode unsigned varstring varunicode"),f=t("checkpoint deprecated failcode failmessage failure global independent onwarning persist priority recovery stored success wait when"),l=t("catch class do else finally for if switch try while"),c=t("true false null"),h={"#":n},p,d=/[+\-*&%=<>!?|\/]/,v;return{startState:function(e){return{tokenize:null,context:new b((e||0)-i,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,t){var n=t.context;e.sol()&&(n.align==null&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0);if(e.eatSpace())return null;v=null;var r=(t.tokenize||m)(e,t);if(r=="comment"||r=="meta")return r;n.align==null&&(n.align=!0);if(v!=";"&&v!=":"||n.type!="statement")if(v=="{")w(t,e.column(),"}");else if(v=="[")w(t,e.column(),"]");else if(v=="(")w(t,e.column(),")");else if(v=="}"){while(n.type=="statement")n=E(t);n.type=="}"&&(n=E(t));while(n.type=="statement")n=E(t)}else v==n.type?E(t):(n.type=="}"||n.type=="top"||n.type=="statement"&&v=="newstatement")&&w(t,e.column(),"statement");else E(t);return t.startOfLine=!1,r},indent:function(e,t){if(e.tokenize!=m&&e.tokenize!=null)return 0;var n=e.context,r=t&&t.charAt(0);n.type=="statement"&&r=="}"&&(n=n.prev);var s=r==n.type;return n.type=="statement"?n.indented+(r=="{"?0:i):n.align?n.column+(s?0:1):n.indented+(s?0:i)},electricChars:"{}"}}),CodeMirror.defineMIME("text/x-ecl","ecl"),CodeMirror.defineMIME("text/x-erlang","erlang"),CodeMirror.defineMode("erlang",function(e,t){function n(e,t,n){n=="record"?e.context="record":e.context=!1,n!="whitespace"&&n!="comment"&&(e.lastToken=t.current());switch(n){case"atom":return"atom";case"attribute":return"attribute";case"builtin":return"builtin";case"comment":return"comment";case"fun":return"meta";case"function":return"tag";case"guard":return"property";case"keyword":return"keyword";case"macro":return"variable-2";case"number":return"number";case"operator":return"operator";case"record":return"bracket";case"string":return"string";case"type":return"def";case"variable":return"variable";case"error":return"error";case"separator":return null;case"open_paren":return null;case"close_paren":return null;default:return null}}function S(e,t){return-1<t.indexOf(e)}function x(e,t){var n=e.start,r=t.length;if(r<=n){var i=e.string.slice(n-r,n);return i==t}return!1}function T(e,t){if(e.eatSpace())return n(t,e,"whitespace");if((P(t).token==""||P(t).token==".")&&e.peek()=="-"){e.next();if(e.eat(p)&&e.eatWhile(g))return S(e.current(),r)?n(t,e,"type"):n(t,e,"attribute");e.backUp(1)}var h=e.next();if(h=="%")return e.skipToEnd(),n(t,e,"comment");if(h=="?")return e.eatWhile(g),n(t,e,"macro");if(h=="#")return e.eatWhile(g),n(t,e,"record");if(h=="$")return e.next()=="\\"&&(e.eatWhile(m)||e.next()),n(t,e,"string");if(h=="'")return L(e)?n(t,e,"atom"):n(t,e,"error");if(h=='"')return k(e)?n(t,e,"string"):n(t,e,"error");if(d.test(h))return e.eatWhile(g),n(t,e,"variable");if(p.test(h)){e.eatWhile(g);if(e.peek()=="/")return e.next(),e.eatWhile(v)?n(t,e,"fun"):(e.backUp(1),n(t,e,"atom"));var T=e.current();return S(T,i)?(H(t,e),n(t,e,"keyword")):e.peek()=="("?S(T,c)&&(!x(e,":")||x(e,"erlang:"))?n(t,e,"builtin"):n(t,e,"function"):S(T,l)?n(t,e,"guard"):S(T,o)?n(t,e,"operator"):e.peek()==":"?T=="erlang"?n(t,e,"builtin"):n(t,e,"function"):n(t,e,"atom")}return v.test(h)?(e.eatWhile(v),e.eat("#")?e.eatWhile(v):(e.eat(".")&&e.eatWhile(v),e.eat(/[eE]/)&&(e.eat(/[-+]/),e.eatWhile(v))),n(t,e,"number")):N(e,b,a)?(H(t,e),n(t,e,"open_paren")):N(e,w,f)?(H(t,e),n(t,e,"close_paren")):C(e,E,s)?(t.context==0&&H(t,e),n(t,e,"separator")):C(e,y,u)?n(t,e,"operator"):n(t,e,null)}function N(e,t,n){if(e.current().length==1&&t.test(e.current())){e.backUp(1);while(t.test(e.peek())){e.next();if(S(e.current(),n))return!0}e.backUp(e.current().length-1)}return!1}function C(e,t,n){if(e.current().length==1&&t.test(e.current())){while(t.test(e.peek()))e.next();while(0<e.current().length){if(S(e.current(),n))return!0;e.backUp(1)}e.next()}return!1}function k(e){return A(e,'"',"\\")}function L(e){return A(e,"'","\\")}function A(e,t,n){while(!e.eol()){var r=e.next();if(r==t)return!0;r==n&&e.next()}return!1}function O(e){this.token=e?e.current():"",this.column=e?e.column():0,this.indent=e?e.indentation():0}function M(t,n){var r=e.indentUnit,i=["after","catch"],s=P(t).token,o=_(n,/[^a-z]/);return S(s,a)?P(t).column+s.length:s=="."||s==""?0:s=="->"?o=="end"?P(t,2).column:P(t,2).token=="fun"?P(t,2).column+r:P(t).indent+r:S(o,i)?P(t).indent:P(t).column+r}function _(e,t){var n=e.match(t);return n?e.slice(0,n.index):e}function D(e){return e.tokenStack.pop()}function P(e,t){var n=e.tokenStack.length,r=t?t:1;return n<r?new O:e.tokenStack[n-r]}function H(e,t){var n=t.current(),r=P(e).token;return S(n,h)?!1:j(r,n)?(D(e),!1):B(r,n)?(D(e),H(e,t)):(e.tokenStack.push(new O(t)),!0)}function B(e,t){switch(e+" "+t){case"when ->":return!0;case"-> end":return!0;case"-> .":return!0;case". .":return!0;default:return!1}}function j(e,t){switch(e+" "+t){case"( )":return!0;case"[ ]":return!0;case"{ }":return!0;case"<< >>":return!0;case"begin end":return!0;case"case end":return!0;case"fun end":return!0;case"if end":return!0;case"receive end":return!0;case"try end":return!0;case"-> ;":return!0;default:return!1}}var r=["-type","-spec","-export_type","-opaque"],i=["after","begin","catch","case","cond","end","fun","if","let","of","query","receive","try","when"],s=["->",";",":",".",","],o=["and","andalso","band","bnot","bor","bsl","bsr","bxor","div","not","or","orelse","rem","xor"],u=["+","-","*","/",">",">=","<","=<","=:=","==","=/=","/=","||","<-"],a=["<<","(","[","{"],f=["}","]",")",">>"],l=["is_atom","is_binary","is_bitstring","is_boolean","is_float","is_function","is_integer","is_list","is_number","is_pid","is_port","is_record","is_reference","is_tuple","atom","binary","bitstring","boolean","function","integer","list","number","pid","port","record","reference","tuple"],c=["abs","adler32","adler32_combine","alive","apply","atom_to_binary","atom_to_list","binary_to_atom","binary_to_existing_atom","binary_to_list","binary_to_term","bit_size","bitstring_to_list","byte_size","check_process_code","contact_binary","crc32","crc32_combine","date","decode_packet","delete_module","disconnect_node","element","erase","exit","float","float_to_list","garbage_collect","get","get_keys","group_leader","halt","hd","integer_to_list","internal_bif","iolist_size","iolist_to_binary","is_alive","is_atom","is_binary","is_bitstring","is_boolean","is_float","is_function","is_integer","is_list","is_number","is_pid","is_port","is_process_alive","is_record","is_reference","is_tuple","length","link","list_to_atom","list_to_binary","list_to_bitstring","list_to_existing_atom","list_to_float","list_to_integer","list_to_pid","list_to_tuple","load_module","make_ref","module_loaded","monitor_node","node","node_link","node_unlink","nodes","notalive","now","open_port","pid_to_list","port_close","port_command","port_connect","port_control","pre_loaded","process_flag","process_info","processes","purge_module","put","register","registered","round","self","setelement","size","spawn","spawn_link","spawn_monitor","spawn_opt","split_binary","statistics","term_to_binary","time","throw","tl","trunc","tuple_size","tuple_to_list","unlink","unregister","whereis"],h=[",",":","catch","after","of","cond","let","query"],p=/[a-z_]/,d=/[A-Z_]/,v=/[0-9]/,m=/[0-7]/,g=/[a-z_A-Z0-9]/,y=/[\+\-\*\/<>=\|:]/,b=/[<\(\[\{]/,w=/[>\)\]\}]/,E=/[\->\.,:;]/;return{startState:function(){return{tokenStack:[],context:!1,lastToken:null}},token:function(e,t){return T(e,t)},indent:function(e,t){return M(e,t)}}}),CodeMirror.defineMode("gfm",function(e,t){function r(e){return e.code=!1,null}var n=0,i={startState:function(){return{code:!1,codeBlock:!1,ateSpace:!1}},copyState:function(e){return{code:e.code,codeBlock:e.codeBlock,ateSpace:e.ateSpace}},token:function(e,t){if(t.codeBlock)return e.match(/^```/)?(t.codeBlock=!1,null):(e.skipToEnd(),null);e.sol()&&(t.code=!1);if(e.sol()&&e.match(/^```/))return e.skipToEnd(),t.codeBlock=!0,null;if(e.peek()==="`"){e.next();var r=e.pos;e.eatWhile("`");var i=1+e.pos-r;return t.code?i===n&&(t.code=!1):(n=i,t.code=!0),null}if(t.code)return e.next(),null;if(e.eatSpace())return t.ateSpace=!0,null;if(e.sol()||t.ateSpace){t.ateSpace=!1;if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+@)?(?:[a-f0-9]{7,40}\b)/))return"link";if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+)?#[0-9]+\b/))return"link"}return e.match(/^((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?\u00ab\u00bb\u201c\u201d\u2018\u2019]))/i)?"link":(e.next(),null)},blankLine:r};return CodeMirror.defineMIME("gfmBase",{name:"markdown",underscoresBreakWords:!1,fencedCodeBlocks:!0}),CodeMirror.overlayMode(CodeMirror.getMode(e,"gfmBase"),i)}),CodeMirror.defineMode("go",function(e,t){function a(e,t){var n=e.next();if(n=='"'||n=="'"||n=="`")return t.tokenize=f(n),t.tokenize(e,t);if(/[\d\.]/.test(n))return n=="."?e.match(/^[0-9]+([eE][\-+]?[0-9]+)?/):n=="0"?e.match(/^[xX][0-9a-fA-F]+/)||e.match(/^0[0-7]+/):e.match(/^[0-9]*\.?[0-9]*([eE][\-+]?[0-9]+)?/),"number";if(/[\[\]{}\(\),;\:\.]/.test(n))return u=n,null;if(n=="/"){if(e.eat("*"))return t.tokenize=l,l(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(o.test(n))return e.eatWhile(o),"operator";e.eatWhile(/[\w\$_]/);var s=e.current();if(r.propertyIsEnumerable(s)){if(s=="case"||s=="default")u="case";return"keyword"}return i.propertyIsEnumerable(s)?"atom":"variable"}function f(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r&&e!="`")n.tokenize=a;return"string"}}function l(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=a;break}n=r=="*"}return"comment"}function c(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function h(e,t,n){return e.context=new c(e.indented,t,n,null,e.context)}function p(e){var t=e.context.type;if(t==")"||t=="]"||t=="}")e.indented=e.context.indented;return e.context=e.context.prev}var n=e.indentUnit,r={"break":!0,"case":!0,chan:!0,"const":!0,"continue":!0,"default":!0,defer:!0,"else":!0,fallthrough:!0,"for":!0,func:!0,go:!0,"goto":!0,"if":!0,"import":!0,"interface":!0,map:!0,"package":!0,range:!0,"return":!0,select:!0,struct:!0,"switch":!0,type:!0,"var":!0,bool:!0,"byte":!0,complex64:!0,complex128:!0,float32:!0,float64:!0,int8:!0,int16:!0,int32:!0,int64:!0,string:!0,uint8:!0,uint16:!0,uint32:!0,uint64:!0,"int":!0,uint:!0,uintptr:!0},i={"true":!0,"false":!0,iota:!0,nil:!0,append:!0,cap:!0,close:!0,complex:!0,copy:!0,imag:!0,len:!0,make:!0,"new":!0,panic:!0,print:!0,println:!0,real:!0,recover:!0},s={"else":!0,"for":!0,func:!0,"if":!0,"interface":!0,select:!0,struct:!0,"switch":!0},o=/[+\-*&^%:=<>!|\/]/,u;return{startState:function(e){return{tokenize:null,context:new c((e||0)-n,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,t){var n=t.context;e.sol()&&(n.align==null&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0,n.type=="case"&&(n.type="}"));if(e.eatSpace())return null;u=null;var r=(t.tokenize||a)(e,t);return r=="comment"?r:(n.align==null&&(n.align=!0),u=="{"?h(t,e.column(),"}"):u=="["?h(t,e.column(),"]"):u=="("?h(t,e.column(),")"):u=="case"?n.type="case":u=="}"&&n.type=="}"?n=p(t):u==n.type&&p(t),t.startOfLine=!1,r)},indent:function(e,t){if(e.tokenize!=a&&e.tokenize!=null)return 0;var r=e.context,i=t&&t.charAt(0);if(r.type=="case"&&/^(?:case|default)\b/.test(t))return e.context.type="}",r.indented;var s=i==r.type;return r.align?r.column+(s?0:1):r.indented+(s?0:n)},electricChars:"{}:"}}),CodeMirror.defineMIME("text/x-go","go"),CodeMirror.defineMode("groovy",function(e,t){function n(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function u(e,t){var n=e.next();if(n=='"'||n=="'")return a(n,e,t);if(/[\[\]{}\(\),;\:\.]/.test(n))return o=n,null;if(/\d/.test(n))return e.eatWhile(/[\w\.]/),e.eat(/eE/)&&(e.eat(/\+\-/),e.eatWhile(/\d/)),"number";if(n=="/"){if(e.eat("*"))return t.tokenize.push(l),l(e,t);if(e.eat("/"))return e.skipToEnd(),"comment";if(c(t.lastToken))return a(n,e,t)}if(n=="-"&&e.eat(">"))return o="->",null;if(/[+\-*&%=<>!?|\/~]/.test(n))return e.eatWhile(/[+\-*&%=<>|~]/),"operator";e.eatWhile(/[\w\$_]/);if(n=="@")return e.eatWhile(/[\w\$_\.]/),"meta";if(t.lastToken==".")return"property";if(e.eat(":"))return o="proplabel","property";var u=e.current();return s.propertyIsEnumerable(u)?"atom":r.propertyIsEnumerable(u)?(i.propertyIsEnumerable(u)&&(o="newstatement"),"keyword"):"variable"}function a(e,t,n){function i(t,n){var i=!1,s,o=!r;while((s=t.next())!=null){if(s==e&&!i){if(!r)break;if(t.match(e+e)){o=!0;break}}if(e=='"'&&s=="$"&&!i&&t.eat("{"))return n.tokenize.push(f()),"string";i=!i&&s=="\\"}return o&&n.tokenize.pop(),"string"}var r=!1;if(e!="/"&&t.eat(e)){if(!t.eat(e))return"string";r=!0}return n.tokenize.push(i),i(t,n)}function f(){function t(t,n){if(t.peek()=="}"){e--;if(e==0)return n.tokenize.pop(),n.tokenize[n.tokenize.length-1](t,n)}else t.peek()=="{"&&e++;return u(t,n)}var e=1;return t.isBase=!0,t}function l(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize.pop();break}n=r=="*"}return"comment"}function c(e){return!e||e=="operator"||e=="->"||/[\.\[\{\(,;:]/.test(e)||e=="newstatement"||e=="keyword"||e=="proplabel"}function h(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function p(e,t,n){return e.context=new h(e.indented,t,n,null,e.context)}function d(e){var t=e.context.type;if(t==")"||t=="]"||t=="}")e.indented=e.context.indented;return e.context=e.context.prev}var r=n("abstract as assert boolean break byte case catch char class const continue def default do double else enum extends final finally float for goto if implements import in instanceof int interface long native new package private protected public return short static strictfp super switch synchronized threadsafe throw throws transient try void volatile while"),i=n("catch class do else finally for if switch try while enum interface def"),s=n("null true false this"),o;return u.isBase=!0,{startState:function(t){return{tokenize:[u],context:new h((t||0)-e.indentUnit,0,"top",!1),indented:0,startOfLine:!0,lastToken:null}},token:function(e,t){var n=t.context;e.sol()&&(n.align==null&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0,n.type=="statement"&&!c(t.lastToken)&&(d(t),n=t.context));if(e.eatSpace())return null;o=null;var r=t.tokenize[t.tokenize.length-1](e,t);if(r=="comment")return r;n.align==null&&(n.align=!0);if(o!=";"&&o!=":"||n.type!="statement")if(o=="->"&&n.type=="statement"&&n.prev.type=="}")d(t),t.context.align=!1;else if(o=="{")p(t,e.column(),"}");else if(o=="[")p(t,e.column(),"]");else if(o=="(")p(t,e.column(),")");else if(o=="}"){while(n.type=="statement")n=d(t);n.type=="}"&&(n=d(t));while(n.type=="statement")n=d(t)}else o==n.type?d(t):(n.type=="}"||n.type=="top"||n.type=="statement"&&o=="newstatement")&&p(t,e.column(),"statement");else d(t);return t.startOfLine=!1,t.lastToken=o||r,r},indent:function(t,n){if(!t.tokenize[t.tokenize.length-1].isBase)return 0;var r=n&&n.charAt(0),i=t.context;i.type=="statement"&&!c(t.lastToken)&&(i=i.prev);var s=r==i.type;return i.type=="statement"?i.indented+(r=="{"?0:e.indentUnit):i.align?i.column+(s?0:1):i.indented+(s?0:e.indentUnit)},electricChars:"{}"}}),CodeMirror.defineMIME("text/x-groovy","groovy"),CodeMirror.defineMode("haskell",function(e,t){function n(e,t,n){return t(n),n(e,t)}function h(e,t){if(e.eatWhile(c))return null;var h=e.next();if(l.test(h)){if(h=="{"&&e.eat("-")){var v="comment";return e.eat("#")&&(v="meta"),n(e,t,p(v,1))}return null}if(h=="'")return e.eat("\\")?e.next():e.next(),e.eat("'")?"string":"error";if(h=='"')return n(e,t,d);if(i.test(h))return e.eatWhile(a),e.eat(".")?"qualifier":"variable-2";if(r.test(h))return e.eatWhile(a),"variable";if(s.test(h)){if(h=="0"){if(e.eat(/[xX]/))return e.eatWhile(o),"integer";if(e.eat(/[oO]/))return e.eatWhile(u),"number"}e.eatWhile(s);var v="number";return e.eat(".")&&(v="number",e.eatWhile(s)),e.eat(/[eE]/)&&(v="number",e.eat(/[-+]/),e.eatWhile(s)),v}if(f.test(h)){if(h=="-"&&e.eat(/-/)){e.eatWhile(/-/);if(!e.eat(f))return e.skipToEnd(),"comment"}var v="variable";return h==":"&&(v="variable-2"),e.eatWhile(f),v}return"error"}function p(e,t){return t==0?h:function(n,r){var i=t;while(!n.eol()){var s=n.next();if(s=="{"&&n.eat("-"))++i;else if(s=="-"&&n.eat("}")){--i;if(i==0)return r(h),e}}return r(p(e,i)),e}}function d(e,t){while(!e.eol()){var n=e.next();if(n=='"')return t(h),"string";if(n=="\\"){if(e.eol()||e.eat(c))return t(v),"string";e.eat("&")||e.next()}}return t(h),"error"}function v(e,t){return e.eat("\\")?n(e,t,d):(e.next(),t(h),"error")}var r=/[a-z_]/,i=/[A-Z]/,s=/[0-9]/,o=/[0-9A-Fa-f]/,u=/[0-7]/,a=/[a-z_A-Z0-9']/,f=/[-!#$%&*+.\/<=>?@\\^|~:]/,l=/[(),;[\]`{}]/,c=/[ \t\v\f]/,m=function(){function t(t){return function(){for(var n=0;n<arguments.length;n++)e[arguments[n]]=t}}var e={};return t("keyword")("case","class","data","default","deriving","do","else","foreign","if","import","in","infix","infixl","infixr","instance","let","module","newtype","of","then","type","where","_"),t("keyword")("..",":","::","=","\\",'"',"<-","->","@","~","=>"),t("builtin")("!!","$!","$","&&","+","++","-",".","/","/=","<","<=","=<<","==",">",">=",">>",">>=","^","^^","||","*","**"),t("builtin")("Bool","Bounded","Char","Double","EQ","Either","Enum","Eq","False","FilePath","Float","Floating","Fractional","Functor","GT","IO","IOError","Int","Integer","Integral","Just","LT","Left","Maybe","Monad","Nothing","Num","Ord","Ordering","Rational","Read","ReadS","Real","RealFloat","RealFrac","Right","Show","ShowS","String","True"),t("builtin")("abs","acos","acosh","all","and","any","appendFile","asTypeOf","asin","asinh","atan","atan2","atanh","break","catch","ceiling","compare","concat","concatMap","const","cos","cosh","curry","cycle","decodeFloat","div","divMod","drop","dropWhile","either","elem","encodeFloat","enumFrom","enumFromThen","enumFromThenTo","enumFromTo","error","even","exp","exponent","fail","filter","flip","floatDigits","floatRadix","floatRange","floor","fmap","foldl","foldl1","foldr","foldr1","fromEnum","fromInteger","fromIntegral","fromRational","fst","gcd","getChar","getContents","getLine","head","id","init","interact","ioError","isDenormalized","isIEEE","isInfinite","isNaN","isNegativeZero","iterate","last","lcm","length","lex","lines","log","logBase","lookup","map","mapM","mapM_","max","maxBound","maximum","maybe","min","minBound","minimum","mod","negate","not","notElem","null","odd","or","otherwise","pi","pred","print","product","properFraction","putChar","putStr","putStrLn","quot","quotRem","read","readFile","readIO","readList","readLn","readParen","reads","readsPrec","realToFrac","recip","rem","repeat","replicate","return","reverse","round","scaleFloat","scanl","scanl1","scanr","scanr1","seq","sequence","sequence_","show","showChar","showList","showParen","showString","shows","showsPrec","significand","signum","sin","sinh","snd","span","splitAt","sqrt","subtract","succ","sum","tail","take","takeWhile","tan","tanh","toEnum","toInteger","toRational","truncate","uncurry","undefined","unlines","until","unwords","unzip","unzip3","userError","words","writeFile","zip","zip3","zipWith","zipWith3"),e}();return{startState:function(){return{f:h}},copyState:function(e){return{f:e.f}},token:function(e,t){var n=t.f(e,function(e){t.f=e}),r=e.current();return r in m?m[r]:n}}}),CodeMirror.defineMIME("text/x-haskell","haskell"),CodeMirror.defineMode("haxe",function(e,t){function s(e,t,n){return t.tokenize=n,n(e,t)}function o(e,t){var n=!1,r;while((r=e.next())!=null){if(r==t&&!n)return!1;n=!n&&r=="\\"}return n}function f(e,t,n){return u=e,a=n,t}function l(e,t){var n=e.next();if(n=='"'||n=="'")return s(e,t,c(n));if(/[\[\]{}\(\),;\:\.]/.test(n))return f(n);if(n=="0"&&e.eat(/x/i))return e.eatWhile(/[\da-f]/i),f("number","number");if(/\d/.test(n)||n=="-"&&e.eat(/\d/))return e.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),f("number","number");if(t.reAllowed&&n=="~"&&e.eat(/\//))return o(e,"/"),e.eatWhile(/[gimsu]/),f("regexp","string-2");if(n=="/")return e.eat("*")?s(e,t,h):e.eat("/")?(e.skipToEnd(),f("comment","comment")):(e.eatWhile(i),f("operator",null,e.current()));if(n=="#")return e.skipToEnd(),f("conditional","meta");if(n=="@")return e.eat(/:/),e.eatWhile(/[\w_]/),f("metadata","meta");if(i.test(n))return e.eatWhile(i),f("operator",null,e.current());var u;if(/[A-Z]/.test(n))return e.eatWhile(/[\w_<>]/),u=e.current(),f("type","variable-3",u);e.eatWhile(/[\w_]/);var u=e.current(),a=r.propertyIsEnumerable(u)&&r[u];return a&&t.kwAllowed?f(a.type,a.style,u):f("variable","variable",u)}function c(e){return function(t,n){return o(t,e)||(n.tokenize=l),f("string","string")}}function h(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=l;break}n=r=="*"}return f("comment","comment")}function d(e,t,n,r,i,s){this.indented=e,this.column=t,this.type=n,this.prev=i,this.info=s,r!=null&&(this.align=r)}function v(e,t){for(var n=e.localVars;n;n=n.next)if(n.name==t)return!0}function m(e,t,n,r,i){var s=e.cc;b.state=e,b.stream=i,b.marked=null,b.cc=s,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);for(;;){var o=s.length?s.pop():A;if(o(n,r)){while(s.length&&s[s.length-1].lex)s.pop()();return b.marked?b.marked:n=="variable"&&v(e,r)?"variable-2":n=="variable"&&g(e,r)?"variable-3":t}}}function g(e,t){if(/[a-z]/.test(t.charAt(0)))return!1;var n=e.importedtypes.length;for(var r=0;r<n;r++)if(e.importedtypes[r]==t)return!0}function y(e){var t=b.state;for(var n=t.importedtypes;n;n=n.next)if(n.name==e)return;t.importedtypes={name:e,next:t.importedtypes}}function w(){for(var e=arguments.length-1;e>=0;e--)b.cc.push(arguments[e])}function E(){return w.apply(null,arguments),!0}function S(e){var t=b.state;if(t.context){b.marked="def";for(var n=t.localVars;n;n=n.next)if(n.name==e)return;t.localVars={name:e,next:t.localVars}}}function T(){b.state.context||(b.state.localVars=x),b.state.context={prev:b.state.context,vars:b.state.localVars}}function N(){b.state.localVars=b.state.context.vars,b.state.context=b.state.context.prev}function C(e,t){var n=function(){var n=b.state;n.lexical=new d(n.indented,b.stream.column(),e,null,n.lexical,t)};return n.lex=!0,n}function k(){var e=b.state;e.lexical.prev&&(e.lexical.type==")"&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function L(e){return function(n){return n==e?E():e==";"?w():E(arguments.callee)}}function A(e){return e=="@"?E(P):e=="var"?E(C("vardef"),z,L(";"),k):e=="keyword a"?E(C("form"),O,A,k):e=="keyword b"?E(C("form"),A,k):e=="{"?E(C("}"),T,U,k,N):e==";"?E():e=="attribute"?E(D):e=="function"?E($):e=="for"?E(C("form"),L("("),C(")"),X,L(")"),k,A,k):e=="variable"?E(C("stat"),F):e=="switch"?E(C("form"),O,C("}","switch"),L("{"),U,k,k):e=="case"?E(O,L(":")):e=="default"?E(L(":")):e=="catch"?E(C("form"),T,L("("),G,L(")"),A,k,N):e=="import"?E(B,L(";")):e=="typedef"?E(j):w(C("stat"),O,L(";"),k)}function O(e){return p.hasOwnProperty(e)?E(_):e=="function"?E($):e=="keyword c"?E(M):e=="("?E(C(")"),M,L(")"),k,_):e=="operator"?E(O):e=="["?E(C("]"),R(O,"]"),k,_):e=="{"?E(C("}"),R(q,"}"),k,_):E()}function M(e){return e.match(/[;\}\)\],]/)?w():w(O)}function _(e,t){if(e=="operator"&&/\+\+|--/.test(t))return E(_);if(e=="operator"||e==":")return E(O);if(e==";")return;if(e=="(")return E(C(")"),R(O,")"),k,_);if(e==".")return E(I,_);if(e=="[")return E(C("]"),O,L("]"),k,_)}function D(e,t){if(e=="attribute")return E(D);if(e=="function")return E($);if(e=="var")return E(z)}function P(e,t){if(e==":")return E(P);if(e=="variable")return E(P);if(e=="(")return E(C(")"),comasep(H,")"),k,A)}function H(e,t){if(typ=="variable")return E()}function B(e,t){if(e=="variable"&&/[A-Z]/.test(t.charAt(0)))return y(t),E();if(e=="variable"||e=="property"||e==".")return E(B)}function j(e,t){if(e=="variable"&&/[A-Z]/.test(t.charAt(0)))return y(t),E()}function F(e){return e==":"?E(k,A):w(_,L(";"),k)}function I(e){if(e=="variable")return b.marked="property",E()}function q(e){e=="variable"&&(b.marked="property");if(p.hasOwnProperty(e))return E(L(":"),O)}function R(e,t){function n(r){return r==","?E(e,n):r==t?E():E(L(t))}return function(i){return i==t?E():w(e,n)}}function U(e){return e=="}"?E():w(A,U)}function z(e,t){return e=="variable"?(S(t),E(J,W)):E()}function W(e,t){if(t=="=")return E(O,W);if(e==",")return E(z)}function X(e,t){return e=="variable"&&S(t),E(C(")"),T,V,O,k,A,N)}function V(e,t){if(t=="in")return E()}function $(e,t){if(e=="variable")return S(t),E($);if(t=="new")return E($);if(e=="(")return E(C(")"),T,R(G,")"),k,J,A,N)}function J(e,t){if(e==":")return E(K)}function K(e,t){if(e=="type")return E();if(e=="variable")return E();if(e=="{")return E(C("}"),R(Q,"}"),k)}function Q(e,t){if(e=="variable")return E(J)}function G(e,t){if(e=="variable")return S(t),E(J)}var n=e.indentUnit,r=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),n=e("keyword b"),r=e("keyword c"),i=e("operator"),s={type:"atom",style:"atom"},o={type:"attribute",style:"attribute"},u=e("typedef");return{"if":t,"while":t,"else":n,"do":n,"try":n,"return":r,"break":r,"continue":r,"new":r,"throw":r,"var":e("var"),inline:o,"static":o,using:e("import"),"public":o,"private":o,cast:e("cast"),"import":e("import"),macro:e("macro"),"function":e("function"),"catch":e("catch"),untyped:e("untyped"),callback:e("cb"),"for":e("for"),"switch":e("switch"),"case":e("case"),"default":e("default"),"in":i,never:e("property_access"),trace:e("trace"),"class":u,"enum":u,"interface":u,typedef:u,"extends":u,"implements":u,dynamic:u,"true":s,"false":s,"null":s}}(),i=/[+\-*&%=<>!?|]/,u,a,p={atom:!0,number:!0,variable:!0,string:!0,regexp:!0},b={state:null,column:null,marked:null,cc:null},x={name:"this",next:null};return k.lex=!0,{startState:function(e){var r=["Int","Float","String","Void","Std","Bool","Dynamic","Array"];return{tokenize:l,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new d((e||0)-n,0,"block",!1),localVars:t.localVars,importedtypes:r,context:t.localVars&&{vars:t.localVars},indented:0}},token:function(e,t){e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation());if(e.eatSpace())return null;var n=t.tokenize(e,t);return u=="comment"?n:(t.reAllowed=u=="operator"||u=="keyword c"||!!u.match(/^[\[{}\(,;:]$/),t.kwAllowed=u!=".",m(t,n,u,a,e))},indent:function(e,t){if(e.tokenize!=l)return 0;var r=t&&t.charAt(0),i=e.lexical;i.type=="stat"&&r=="}"&&(i=i.prev);var s=i.type,o=r==s;return s=="vardef"?i.indented+4:s=="form"&&r=="{"?i.indented:s=="stat"||s=="form"?i.indented+n:i.info=="switch"&&!o?i.indented+(/^(?:case|default)\b/.test(t)?n:2*n):i.align?i.column+(o?0:1):i.indented+(o?0:n)},electricChars:"{}"}}),CodeMirror.defineMIME("text/x-haxe","haxe"),CodeMirror.defineMode("htmlembedded",function(e,t){function o(e,t){return e.match(n,!1)?(t.token=u,i.token(e,t.scriptState)):s.token(e,t.htmlState)}function u(e,t){return e.match(r,!1)?(t.token=o,s.token(e,t.htmlState)):i.token(e,t.scriptState)}var n=t.scriptStartRegex||/^<%/i,r=t.scriptEndRegex||/^%>/i,i,s;return{startState:function(){return i=i||CodeMirror.getMode(e,t.scriptingModeSpec),s=s||CodeMirror.getMode(e,"htmlmixed"),{token:t.startOpen?u:o,htmlState:s.startState(),scriptState:i.startState()}},token:function(e,t){return t.token(e,t)},indent:function(e,t){return e.token==o?s.indent(e.htmlState,t):i.indent(e.scriptState,t)},copyState:function(e){return{token:e.token,htmlState:CodeMirror.copyState(s,e.htmlState),scriptState:CodeMirror.copyState(i,e.scriptState)}},electricChars:"/{}:",innerMode:function(e){return e.token==u?{state:e.scriptState,mode:i}:{state:e.htmlState,mode:s}}}},"htmlmixed"),CodeMirror.defineMIME("application/x-ejs",{name:"htmlembedded",scriptingModeSpec:"javascript"}),CodeMirror.defineMIME("application/x-aspx",{name:"htmlembedded",scriptingModeSpec:"text/x-csharp"}),CodeMirror.defineMIME("application/x-jsp",{name:"htmlembedded",scriptingModeSpec:"text/x-java"}),CodeMirror.defineMIME("application/x-erb",{name:"htmlembedded",scriptingModeSpec:"ruby"}),CodeMirror.defineMode("htmlmixed",function(e){function i(e,i){var s=t.token(e,i.htmlState);return s=="tag"&&e.current()==">"&&i.htmlState.context&&(/^script$/i.test(i.htmlState.context.tagName)?(i.token=o,i.localState=n.startState(t.indent(i.htmlState,""))):/^style$/i.test(i.htmlState.context.tagName)&&(i.token=u,i.localState=r.startState(t.indent(i.htmlState,"")))),s}function s(e,t,n){var r=e.current(),i=r.search(t),s;if(i>-1)e.backUp(r.length-i);else if(s=r.match(/<\/?$/))e.backUp(r[0].length),e.match(t,!1)||e.match(r[0]);return n}function o(e,t){return e.match(/^<\/\s*script\s*>/i,!1)?(t.token=i,t.localState=null,i(e,t)):s(e,/<\/\s*script\s*>/,n.token(e,t.localState))}function u(e,t){return e.match(/^<\/\s*style\s*>/i,!1)?(t.token=i,t.localState=null,i(e,t)):s(e,/<\/\s*style\s*>/,r.token(e,t.localState))}var t=CodeMirror.getMode(e,{name:"xml",htmlMode:!0}),n=CodeMirror.getMode(e,"javascript"),r=CodeMirror.getMode(e,"css");return{startState:function(){var e=t.startState();return{token:i,localState:null,mode:"html",htmlState:e}},copyState:function(e){if(e.localState)var i=CodeMirror.copyState(e.token==u?r:n,e.localState);return{token:e.token,localState:i,mode:e.mode,htmlState:CodeMirror.copyState(t,e.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(e,s){return e.token==i||/^\s*<\//.test(s)?t.indent(e.htmlState,s):e.token==o?n.indent(e.localState,s):r.indent(e.localState,s)},electricChars:"/{}:",innerMode:function(e){var s=e.token==i?t:e.token==o?n:r;return{state:e.localState||e.htmlState,mode:s}}}},"xml","javascript","css"),CodeMirror.defineMIME("text/html","htmlmixed"),CodeMirror.defineMode("javascript",function(e,t){function o(e,t,n){return t.tokenize=n,n(e,t)}function u(e,t){var n=!1,r;while((r=e.next())!=null){if(r==t&&!n)return!1;n=!n&&r=="\\"}return n}function l(e,t,n){return a=e,f=n,t}function c(e,t){var n=e.next();if(n=='"'||n=="'")return o(e,t,h(n));if(/[\[\]{}\(\),;\:\.]/.test(n))return l(n);if(n=="0"&&e.eat(/x/i))return e.eatWhile(/[\da-f]/i),l("number","number");if(/\d/.test(n)||n=="-"&&e.eat(/\d/))return e.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),l("number","number");if(n=="/")return e.eat("*")?o(e,t,p):e.eat("/")?(e.skipToEnd(),l("comment","comment")):t.reAllowed?(u(e,"/"),e.eatWhile(/[gimy]/),l("regexp","string-2")):(e.eatWhile(s),l("operator",null,e.current()));if(n=="#")return e.skipToEnd(),l("error","error");if(s.test(n))return e.eatWhile(s),l("operator",null,e.current());e.eatWhile(/[\w\$_]/);var r=e.current(),a=i.propertyIsEnumerable(r)&&i[r];return a&&t.kwAllowed?l(a.type,a.style,r):l("variable","variable",r)}function h(e){return function(t,n){return u(t,e)||(n.tokenize=c),l("string","string")}}function p(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=c;break}n=r=="*"}return l("comment","comment")}function v(e,t,n,r,i,s){this.indented=e,this.column=t,this.type=n,this.prev=i,this.info=s,r!=null&&(this.align=r)}function m(e,t){for(var n=e.localVars;n;n=n.next)if(n.name==t)return!0}function g(e,t,n,i,s){var o=e.cc;y.state=e,y.stream=s,y.marked=null,y.cc=o,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);for(;;){var u=o.length?o.pop():r?A:L;if(u(n,i)){while(o.length&&o[o.length-1].lex)o.pop()();return y.marked?y.marked:n=="variable"&&m(e,i)?"variable-2":t}}}function b(){for(var e=arguments.length-1;e>=0;e--)y.cc.push(arguments[e])}function w(){return b.apply(null,arguments),!0}function E(e){var t=y.state;if(t.context){y.marked="def";for(var n=t.localVars;n;n=n.next)if(n.name==e)return;t.localVars={name:e,next:t.localVars}}}function x(){y.state.context={prev:y.state.context,vars:y.state.localVars},y.state.localVars=S}function T(){y.state.localVars=y.state.context.vars,y.state.context=y.state.context.prev}function N(e,t){var n=function(){var n=y.state;n.lexical=new v(n.indented,y.stream.column(),e,null,n.lexical,t)};return n.lex=!0,n}function C(){var e=y.state;e.lexical.prev&&(e.lexical.type==")"&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function k(e){return function(n){return n==e?w():e==";"?b():w(arguments.callee)}}function L(e){return e=="var"?w(N("vardef"),j,k(";"),C):e=="keyword a"?w(N("form"),A,L,C):e=="keyword b"?w(N("form"),L,C):e=="{"?w(N("}"),B,C):e==";"?w():e=="function"?w(z):e=="for"?w(N("form"),k("("),N(")"),I,k(")"),C,L,C):e=="variable"?w(N("stat"),_):e=="switch"?w(N("form"),A,N("}","switch"),k("{"),B,C,C):e=="case"?w(A,k(":")):e=="default"?w(k(":")):e=="catch"?w(N("form"),x,k("("),W,k(")"),L,C,T):b(N("stat"),A,k(";"),C)}function A(e){return d.hasOwnProperty(e)?w(M):e=="function"?w(z):e=="keyword c"?w(O):e=="("?w(N(")"),O,k(")"),C,M):e=="operator"?w(A):e=="["?w(N("]"),H(A,"]"),C,M):e=="{"?w(N("}"),H(P,"}"),C,M):w()}function O(e){return e.match(/[;\}\)\],]/)?b():b(A)}function M(e,t){if(e=="operator"&&/\+\+|--/.test(t))return w(M);if(e=="operator"&&t=="?")return w(A,k(":"),A);if(e==";")return;if(e=="(")return w(N(")"),H(A,")"),C,M);if(e==".")return w(D,M);if(e=="[")return w(N("]"),A,k("]"),C,M)}function _(e){return e==":"?w(C,L):b(M,k(";"),C)}function D(e){if(e=="variable")return y.marked="property",w()}function P(e){e=="variable"&&(y.marked="property");if(d.hasOwnProperty(e))return w(k(":"),A)}function H(e,t){function n(r){return r==","?w(e,n):r==t?w():w(k(t))}return function(i){return i==t?w():b(e,n)}}function B(e){return e=="}"?w():b(L,B)}function j(e,t){return e=="variable"?(E(t),w(F)):w()}function F(e,t){if(t=="=")return w(A,F);if(e==",")return w(j)}function I(e){return e=="var"?w(j,k(";"),R):e==";"?w(R):e=="variable"?w(q):w(R)}function q(e,t){return t=="in"?w(A):w(M,R)}function R(e,t){return e==";"?w(U):t=="in"?w(A):w(A,k(";"),U)}function U(e){e!=")"&&w(A)}function z(e,t){if(e=="variable")return E(t),w(z);if(e=="(")return w(N(")"),x,H(W,")"),C,L,T)}function W(e,t){if(e=="variable")return E(t),w()}var n=e.indentUnit,r=t.json,i=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),n=e("keyword b"),r=e("keyword c"),i=e("operator"),s={type:"atom",style:"atom"};return{"if":t,"while":t,"with":t,"else":n,"do":n,"try":n,"finally":n,"return":r,"break":r,"continue":r,"new":r,"delete":r,"throw":r,"var":e("var"),"const":e("var"),let:e("var"),"function":e("function"),"catch":e("catch"),"for":e("for"),"switch":e("switch"),"case":e("case"),"default":e("default"),"in":i,"typeof":i,"instanceof":i,"true":s,"false":s,"null":s,"undefined":s,NaN:s,Infinity:s}}(),s=/[+\-*&%=<>!?|]/,a,f,d={atom:!0,number:!0,variable:!0,string:!0,regexp:!0},y={state:null,column:null,marked:null,cc:null},S={name:"this",next:{name:"arguments"}};return C.lex=!0,{startState:function(e){return{tokenize:c,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new v((e||0)-n,0,"block",!1),localVars:t.localVars,context:t.localVars&&{vars:t.localVars},indented:0}},token:function(e,t){e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation());if(e.eatSpace())return null;var n=t.tokenize(e,t);return a=="comment"?n:(t.reAllowed=a=="operator"||a=="keyword c"||!!a.match(/^[\[{}\(,;:]$/),t.kwAllowed=a!=".",g(t,n,a,f,e))},indent:function(e,t){if(e.tokenize==p)return CodeMirror.Pass;if(e.tokenize!=c)return 0;var r=t&&t.charAt(0),i=e.lexical;i.type=="stat"&&r=="}"&&(i=i.prev);var s=i.type,o=r==s;return s=="vardef"?i.indented+4:s=="form"&&r=="{"?i.indented:s=="stat"||s=="form"?i.indented+n:i.info=="switch"&&!o?i.indented+(/^(?:case|default)\b/.test(t)?n:2*n):i.align?i.column+(o?0:1):i.indented+(o?0:n)},electricChars:":{}"}}),CodeMirror.defineMIME("text/javascript","javascript"),CodeMirror.defineMIME("application/json",{name:"javascript",json:!0}),CodeMirror.defineMode("jinja2",function(e,t){function r(e,t){var n=e.next();if(n=="{")if(n=e.eat(/\{|%|#/))return e.eat("-"),t.tokenize=i(n),"tag"}function i(e){return e=="{"&&(e="}"),function(t,i){var s=t.next();return(s==e||s=="-"&&t.eat(e))&&t.eat("}")?(i.tokenize=r,"tag"):t.match(n)?"keyword":e=="#"?"comment":"string"}}var n=["block","endblock","for","endfor","in","true","false","loop","none","self","super","if","as","not","and","else","import","with","without","context"];return n=new RegExp("^(("+n.join(")|(")+"))\\b"),{startState:function(){return{tokenize:r}},token:function(e,t){return t.tokenize(e,t)}}}),CodeMirror.defineMode("less",function(e){function r(e,t){return n=t,e}function s(e){for(var t=0;t<i.length;t++)if(e===i[t])return!0}function u(e,t){var i=e.next();if(i=="@")return e.eatWhile(/[\w\-]/),r("meta",e.current());if(i=="/"&&e.eat("*"))return t.tokenize=f,f(e,t);if(i=="<"&&e.eat("!"))return t.tokenize=l,l(e,t);if(i=="=")r(null,"compare");else{if(i=="|"&&e.eat("="))return r(null,"compare");if(i=='"'||i=="'")return t.tokenize=c(i),t.tokenize(e,t);if(i=="/"){if(e.eat("/"))return t.tokenize=a,a(e,t);if(n=="string"||n=="(")return r("string","string");if(t.stack[t.stack.length-1]!=undefined)return r(null,i);e.eatWhile(/[\a-zA-Z0-9\-_.\s]/);if(/\/|\)|#/.test(e.peek()||e.eatSpace()&&e.peek()==")")||e.eol())return r("string","string")}else{if(i=="!")return e.match(/^\s*\w*/),r("keyword","important");if(/\d/.test(i))return e.eatWhile(/[\w.%]/),r("number","unit");if(/[,+<>*\/]/.test(i))return e.peek()=="="||n=="a"?r("string","string"):r(null,"select-op");if(!/[;{}:\[\]()~\|]/.test(i)){if(i==".")return n=="("||n=="string"?r("string","string"):(e.eatWhile(/[\a-zA-Z0-9\-_]/),e.peek()==" "&&e.eatSpace(),e.peek()==")"?r("number","unit"):r("tag","tag"));if(i=="#")return e.eatWhile(/[A-Za-z0-9]/),e.current().length==4||e.current().length==7?e.current().match(/[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3}/,false)!=null?e.current().substring(1)!=e.current().match(/[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3}/,false)?r("atom","tag"):(e.eatSpace(),/[\/<>.(){!$%^&*_\-\\?=+\|#'~`]/.test(e.peek())?r("atom","tag"):e.peek()=="}"?r("number","unit"):/[a-zA-Z\\]/.test(e.peek())?r("atom","tag"):e.eol()?r("atom","tag"):r("number","unit")):(e.eatWhile(/[\w\\\-]/),r("atom","tag")):(e.eatWhile(/[\w\\\-]/),r("atom","tag"));if(i=="&")return e.eatWhile(/[\w\-]/),r(null,i);e.eatWhile(/[\w\\\-_%.{]/);if(n=="string")return r("string","string");if(e.current().match(/(^http$|^https$)/)!=null)return e.eatWhile(/[\w\\\-_%.{:\/]/),r("string","string");if(e.peek()=="<"||e.peek()==">")return r("tag","tag");if(/\(/.test(e.peek()))return r(null,i);if(e.peek()=="/"&&t.stack[t.stack.length-1]!=undefined)return r("string","string");if(e.current().match(/\-\d|\-.\d/))return r("number","unit");if(s(e.current().toLowerCase()))return r("tag","tag");if(/\/|[\s\)]/.test(e.peek()||e.eol()||e.eatSpace()&&e.peek()=="/")&&e.current().indexOf(".")!==-1)return e.current().substring(e.current().length-1,e.current().length)=="{"?(e.backUp(1),r("tag","tag")):(e.eatSpace(),/[{<>.a-zA-Z\/]/.test(e.peek())||e.eol()?r("tag","tag"):r("string","string"));if(e.eol()||e.peek()=="["||e.peek()=="#"||n=="tag")return e.current().substring(e.current().length-1,e.current().length)=="{"&&e.backUp(1),r("tag","tag");if(n=="compare"||n=="a"||n=="(")return r("string","string");if(n=="|"||e.current()=="-"||n=="[")return r(null,i);if(e.peek()==":"){e.next();var u=e.peek()==":"?!0:!1;if(!u){var h=e.pos,p=e.current().length;e.eatWhile(/[a-z\\\-]/);var d=e.pos;if(e.current().substring(p-1).match(o)!=null)return e.backUp(d-(h-1)),r("tag","tag");e.backUp(d-(h-1))}else e.backUp(1);return u?r("tag","tag"):r("variable","variable")}return r("variable","variable")}if(i==":")return e.eatWhile(/[a-z\\\-]/),o.test(e.current())?r("tag","tag"):e.peek()==":"?(e.next(),e.eatWhile(/[a-z\\\-]/),e.current().match(/\:\:\-(o|ms|moz|webkit)\-/)?r("string","string"):o.test(e.current().substring(1))?r("tag","tag"):r(null,i)):r(null,i);if(i!="~")return r(null,i);if(n=="r")return r("string","string")}}}function a(e,t){return e.skipToEnd(),t.tokenize=u,r("comment","comment")}function f(e,t){var n=!1,i;while((i=e.next())!=null){if(n&&i=="/"){t.tokenize=u;break}n=i=="*"}return r("comment","comment")}function l(e,t){var n=0,i;while((i=e.next())!=null){if(n>=2&&i==">"){t.tokenize=u;break}n=i=="-"?n+1:0}return r("comment","comment")}function c(e){return function(t,n){var i=!1,s;while((s=t.next())!=null){if(s==e&&!i)break;i=!i&&s=="\\"}return i||(n.tokenize=u),r("string","string")}}var t=e.indentUnit,n,i="a abbr acronym address applet area article aside audio b base basefont bdi bdo big blockquote body br button canvas caption cite code col colgroup command datalist dd del details dfn dir div dl dt em embed fieldset figcaption figure font footer form frame frameset h1 h2 h3 h4 h5 h6 head header hgroup hr html i iframe img input ins keygen kbd label legend li link map mark menu meta meter nav noframes noscript object ol optgroup option output p param pre progress q rp rt ruby s samp script section select small source span strike strong style sub summary sup table tbody td textarea tfoot th thead time title tr track tt u ul var video wbr".split(" "),o=/(^\:root$|^\:nth\-child$|^\:nth\-last\-child$|^\:nth\-of\-type$|^\:nth\-last\-of\-type$|^\:first\-child$|^\:last\-child$|^\:first\-of\-type$|^\:last\-of\-type$|^\:only\-child$|^\:only\-of\-type$|^\:empty$|^\:link|^\:visited$|^\:active$|^\:hover$|^\:focus$|^\:target$|^\:lang$|^\:enabled^\:disabled$|^\:checked$|^\:first\-line$|^\:first\-letter$|^\:before$|^\:after$|^\:not$|^\:required$|^\:invalid$)/;return{startState:function(e){return{tokenize:u,baseIndent:e||0,stack:[]}},token:function(e,t){if(e.eatSpace())return null;var r=t.tokenize(e,t),i=t.stack[t.stack.length-1];if(n=="hash"&&i=="rule")r="atom";else if(r=="variable")if(i=="rule")r=null;else if(!i||i=="@media{")r=e.current()=="when"?"variable":/[\s,|\s\)|\s]/.test(e.peek())?"tag":n;return i=="rule"&&/^[\{\};]$/.test(n)&&t.stack.pop(),n=="{"?i=="@media"?t.stack[t.stack.length-1]="@media{":t.stack.push("{"):n=="}"?t.stack.pop():n=="@media"?t.stack.push("@media"):i=="{"&&n!="comment"&&t.stack.push("rule"),r},indent:function(e,n){var r=e.stack.length;return/^\}/.test(n)&&(r-=e.stack[e.stack.length-1]=="rule"?2:1),e.baseIndent+r*t},electricChars:"}"}}),CodeMirror.defineMIME("text/x-less","less"),CodeMirror.mimeModes.hasOwnProperty("text/css")||CodeMirror.defineMIME("text/css","less"),CodeMirror.defineMode("lua",function(e,t){function r(e){return new RegExp("^(?:"+e.join("|")+")","i")}function i(e){return new RegExp("^(?:"+e.join("|")+")$","i")}function c(e){var t=0;while(e.eat("="))++t;return e.eat("["),t}function h(e,t){var n=e.next();return n=="-"&&e.eat("-")?e.eat("[")?(t.cur=p(c(e),"comment"))(e,t):(e.skipToEnd(),"comment"):n=='"'||n=="'"?(t.cur=d(n))(e,t):n=="["&&/[\[=]/.test(e.peek())?(t.cur=p(c(e),"string"))(e,t):/\d/.test(n)?(e.eatWhile(/[\w.%]/),"number"):/[\w_]/.test(n)?(e.eatWhile(/[\w\\\-_.]/),"variable"):null}function p(e,t){return function(n,r){var i=null,s;while((s=n.next())!=null)if(i==null)s=="]"&&(i=0);else if(s=="=")++i;else{if(s=="]"&&i==e){r.cur=h;break}i=null}return t}}function d(e){return function(t,n){var r=!1,i;while((i=t.next())!=null){if(i==e&&!r)break;r=!r&&i=="\\"}return r||(n.cur=h),"string"}}var n=e.indentUnit,s=i(t.specials||[]),o=i(["_G","_VERSION","assert","collectgarbage","dofile","error","getfenv","getmetatable","ipairs","load","loadfile","loadstring","module","next","pairs","pcall","print","rawequal","rawget","rawset","require","select","setfenv","setmetatable","tonumber","tostring","type","unpack","xpcall","coroutine.create","coroutine.resume","coroutine.running","coroutine.status","coroutine.wrap","coroutine.yield","debug.debug","debug.getfenv","debug.gethook","debug.getinfo","debug.getlocal","debug.getmetatable","debug.getregistry","debug.getupvalue","debug.setfenv","debug.sethook","debug.setlocal","debug.setmetatable","debug.setupvalue","debug.traceback","close","flush","lines","read","seek","setvbuf","write","io.close","io.flush","io.input","io.lines","io.open","io.output","io.popen","io.read","io.stderr","io.stdin","io.stdout","io.tmpfile","io.type","io.write","math.abs","math.acos","math.asin","math.atan","math.atan2","math.ceil","math.cos","math.cosh","math.deg","math.exp","math.floor","math.fmod","math.frexp","math.huge","math.ldexp","math.log","math.log10","math.max","math.min","math.modf","math.pi","math.pow","math.rad","math.random","math.randomseed","math.sin","math.sinh","math.sqrt","math.tan","math.tanh","os.clock","os.date","os.difftime","os.execute","os.exit","os.getenv","os.remove","os.rename","os.setlocale","os.time","os.tmpname","package.cpath","package.loaded","package.loaders","package.loadlib","package.path","package.preload","package.seeall","string.byte","string.char","string.dump","string.find","string.format","string.gmatch","string.gsub","string.len","string.lower","string.match","string.rep","string.reverse","string.sub","string.upper","table.concat","table.insert","table.maxn","table.remove","table.sort"]),u=i(["and","break","elseif","false","nil","not","or","return","true","function","end","if","then","else","do","while","repeat","until","for","in","local"]),a=i(["function","if","repeat","do","\\(","{"]),f=i(["end","until","\\)","}"]),l=r(["end","until","\\)","}","else","elseif"]);return{startState:function(e){return{basecol:e||0,indentDepth:0,cur:h}},token:function(e,t){if(e.eatSpace())return null;var n=t.cur(e,t),r=e.current();return n=="variable"&&(u.test(r)?n="keyword":o.test(r)?n="builtin":s.test(r)&&(n="variable-2")),n!="comment"&&n!="string"&&(a.test(r)?++t.indentDepth:f.test(r)&&--t.indentDepth),n},indent:function(e,t){var r=l.test(t);return e.basecol+n*(e.indentDepth-(r?1:0))}}}),CodeMirror.defineMIME("text/x-lua","lua"),CodeMirror.defineMode("markdown",function(e,t){function k(e,t,n){return t.f=t.inline=n,n(e,t)}function L(e,t,n){return t.f=t.block=n,n(e,t)}function A(e){return e.linkTitle=!1,e.em=!1,e.strong=!1,e.quote=!1,!n&&e.f==M&&(e.f=B,e.block=O),null}function O(e,n){n.list!==!1&&n.indentationDiff>=0?(n.indentationDiff<4&&(n.indentation-=n.indentationDiff),n.list=null):n.list=!1;if(n.indentationDiff>=4)return n.indentation-=4,e.skipToEnd(),l;if(e.eatSpace())return null;if(e.peek()==="#"||u&&e.match(N))n.header=!0;else if(e.eat(">"))n.indentation++,n.quote=!0;else{if(e.peek()==="[")return k(e,n,F);if(e.match(S,!0))return p;if(e.match(x,!0)||e.match(T,!0))n.indentation+=4,n.list=!0;else if(t.fencedCodeBlocks&&e.match(/^```([\w+#]*)/,!0))return n.localMode=s(RegExp.$1),n.localMode&&(n.localState=n.localMode.startState()),L(e,n,_),l}return k(e,n,n.inline)}function M(e,t){var i=r.token(e,t.htmlState);return n&&i==="tag"&&t.htmlState.type!=="openTag"&&!t.htmlState.context&&(t.f=B,t.block=O),t.md_inside&&e.current().indexOf(">")!=-1&&(t.f=B,t.block=O,t.htmlState.context=undefined),i}function _(e,t){return e.sol()&&e.match(/^```/,!0)?(t.localMode=t.localState=null,t.f=B,t.block=O,l):t.localMode?t.localMode.token(e,t.localState):(e.skipToEnd(),l)}function D(e,t){return e.match(codeBlockRE,!0)?(t.f=B,t.block=O,k(e,t,t.inline),l):(e.skipToEnd(),l)}function P(e){var t=[];return e.strong?t.push(e.em?E:w):e.em&&t.push(b),e.linkText&&t.push(g),e.code&&t.push(l),e.header&&t.push(f),e.quote&&t.push(c),e.list!==!1&&t.push(h),t.length?t.join(" "):null}function H(e,t){return e.match(C,!0)?P(t):undefined}function B(e,n){var r=n.text(e,n);if(typeof r!="undefined")return r;if(n.list)return n.list=null,h;var i=e.next();if(i==="\\")return e.next(),P(n);if(n.linkTitle){n.linkTitle=!1;var s=i;i==="("&&(s=")"),s=(s+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1");var u="^\\s*(?:[^"+s+"\\\\]+|\\\\\\\\|\\\\.)"+s;if(e.match(new RegExp(u),!0))return y}if(i==="`"){var a=P(n),f=e.pos;e.eatWhile("`");var l=1+e.pos-f;return n.code?l===o?(n.code=!1,a):P(n):(o=l,n.code=!0,P(n))}if(n.code)return P(n);if(i==="!"&&e.match(/\[.*\] ?(?:\(|\[)/,!1))return e.match(/\[.*\]/),n.inline=n.f=j,d;if(i==="["&&e.match(/.*\](\(| ?\[)/,!1))return n.linkText=!0,P(n);if(i==="]"&&n.linkText){var c=P(n);return n.linkText=!1,n.inline=n.f=j,c}if(i==="<"&&e.match(/^(https?|ftps?):\/\/(?:[^\\>]|\\.)+>/,!0))return k(e,n,U(v,">"));if(i==="<"&&e.match(/^[^> \\]+@(?:[^\\>]|\\.)+>/,!0))return k(e,n,U(m,">"));if(i==="<"&&e.match(/^\w/,!1)){var p=!1;if(e.string.indexOf(">")!=-1){var g=e.string.substring(1,e.string.indexOf(">"));/markdown\s*=\s*('|"){0,1}1('|"){0,1}/.test(g)&&(n.md_inside=!0)}return e.backUp(1),L(e,n,M)}if(i==="<"&&e.match(/^\/\w*?>/))return n.md_inside=!1,"tag";var b=!1;if(!t.underscoresBreakWords&&i==="_"&&e.peek()!=="_"&&e.match(/(\w)/,!1)){var w=e.pos-2;if(w>=0){var E=e.string.charAt(w);E!=="_"&&E.match(/(\w)/,!1)&&(b=!0)}}var a=P(n);if(i==="*"||i==="_"&&!b){if(n.strong===i&&e.eat(i))return n.strong=!1,a;if(!n.strong&&e.eat(i))return n.strong=i,P(n);if(n.em===i)return n.em=!1,a;if(!n.em)return n.em=i,P(n)}else if(i===" ")if(e.eat("*")||e.eat("_")){if(e.peek()===" ")return P(n);e.backUp(1)}return P(n)}function j(e,t){if(e.eatSpace())return null;var n=e.next();return n==="("||n==="["?k(e,t,U(y,n==="("?")":"]")):"error"}function F(e,t){return e.match(/^[^\]]*\]:/,!0)?(t.f=I,g):k(e,t,B)}function I(e,t){return e.eatSpace()?null:(e.match(/^[^\s]+/,!0),e.peek()===undefined?t.linkTitle=!0:e.match(/^(?:\s+(?:"(?:[^"\\]|\\\\|\\.)+"|'(?:[^'\\]|\\\\|\\.)+'|\((?:[^)\\]|\\\\|\\.)+\)))?/,!0),t.f=t.inline=B,y)}function R(e){return q[e]||(e=(e+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1"),q[e]=new RegExp("^(?:[^\\\\]|\\\\.)*?("+e+")")),q[e]}function U(e,t,n){return n=n||B,function(r,i){return r.match(R(t)),i.inline=i.f=n,e}}var n=CodeMirror.mimeModes.hasOwnProperty("text/html"),r=CodeMirror.getMode(e,n?"text/html":"text/plain"),i={html:"htmlmixed",js:"javascript",json:"application/json",c:"text/x-csrc","c++":"text/x-c++src",java:"text/x-java",csharp:"text/x-csharp","c#":"text/x-csharp"},s=function(){var t,n={},r={},s,o=CodeMirror.listModes();for(t=0;t<o.length;t++)n[o[t]]=o[t];var u=CodeMirror.listMIMEs();for(t=0;t<u.length;t++)s=u[t].mime,r[s]=u[t].mime;for(var a in i)if(i[a]in n||i[a]in r)n[a]=i[a];return function(t){return n[t]?CodeMirror.getMode(e,n[t]):null}}();t.underscoresBreakWords===undefined&&(t.underscoresBreakWords=!0),t.fencedCodeBlocks===undefined&&(t.fencedCodeBlocks=!1);var o=0,u=!1,a=!1,f="header",l="comment",c="quote",h="string",p="hr",d="tag",v="link",m="link",g="link",y="string",b="em",w="strong",E="emstrong",S=/^([*\-=_])(?:\s*\1){2,}\s*$/,x=/^[*\-+]\s+/,T=/^[0-9]+\.\s+/,N=/^(?:\={1,}|-{1,})$/,C=/^[^!\[\]*_\\<>` "'(]+/,q=[];return{startState:function(){return u=!1,a=!1,{f:O,block:O,htmlState:CodeMirror.startState(r),indentation:0,inline:B,text:H,linkText:!1,linkTitle:!1,em:!1,strong:!1,header:!1,list:!1,quote:!1}},copyState:function(e){return{f:e.f,block:e.block,htmlState:CodeMirror.copyState(r,e.htmlState),indentation:e.indentation,localMode:e.localMode,localState:e.localMode?CodeMirror.copyState(e.localMode,e.localState):null,inline:e.inline,text:e.text,linkTitle:e.linkTitle,em:e.em,strong:e.strong,header:e.header,list:e.list,quote:e.quote,md_inside:e.md_inside}},token:function(e,t){if(e.sol()){if(e.match(/^\s*$/,!0))return u=!1,A(t);a&&(u=!0,a=!1),a=!0,t.header=!1,t.code=!1,t.f=t.block;var n=e.match(/^\s*/,!0)[0].replace(/\t/g,"    ").length,r=Math.floor((n-t.indentation)/4)*4;r>4&&(r=4),n=t.indentation+r,t.indentationDiff=n-t.indentation,t.indentation=n;if(n>0)return null}return t.f(e,t)},blankLine:A,getType:P}},"xml"),CodeMirror.defineMIME("text/x-markdown","markdown"),CodeMirror.defineMode("mysql",function(e){function r(e){return new RegExp("^(?:"+e.join("|")+")$","i")}function u(e,t){var r=e.next();n=null;if(r=="$"||r=="?")return e.match(/^[\w\d]*/),"variable-2";if(r=="<"&&!e.match(/^[\s\u00a0=]/,!1))return e.match(/^[^\s\u00a0>]*>?/),"atom";if(r=='"'||r=="'")return t.tokenize=a(r),t.tokenize(e,t);if(r=="`")return t.tokenize=f(r),t.tokenize(e,t);if(/[{}\(\),\.;\[\]]/.test(r))return n=r,null;if(r!="-"){if(o.test(r))return e.eatWhile(o),null;if(r==":")return e.eatWhile(/[\w\d\._\-]/),"atom";e.eatWhile(/[_\w\d]/);if(e.eat(":"))return e.eatWhile(/[\w\d_\-]/),"atom";var l=e.current(),c;return i.test(l)?null:s.test(l)?"keyword":"variable"}var u=e.next();if(u=="-")return e.skipToEnd(),"comment"}function a(e){return function(t,n){var r=!1,i;while((i=t.next())!=null){if(i==e&&!r){n.tokenize=u;break}r=!r&&i=="\\"}return"string"}}function f(e){return function(t,n){var r=!1,i;while((i=t.next())!=null){if(i==e&&!r){n.tokenize=u;break}r=!r&&i=="\\"}return"variable-2"}}function l(e,t,n){e.context={prev:e.context,indent:e.indent,col:n,type:t}}function c(e){e.indent=e.context.indent,e.context=e.context.prev}var t=e.indentUnit,n,i=r(["str","lang","langmatches","datatype","bound","sameterm","isiri","isuri","isblank","isliteral","union","a"]),s=r(["ACCESSIBLE","ALTER","AS","BEFORE","BINARY","BY","CASE","CHARACTER","COLUMN","CONTINUE","CROSS","CURRENT_TIMESTAMP","DATABASE","DAY_MICROSECOND","DEC","DEFAULT","DESC","DISTINCT","DOUBLE","EACH","ENCLOSED","EXIT","FETCH","FLOAT8","FOREIGN","GRANT","HIGH_PRIORITY","HOUR_SECOND","IN","INNER","INSERT","INT2","INT8","INTO","JOIN","KILL","LEFT","LINEAR","LOCALTIME","LONG","LOOP","MATCH","MEDIUMTEXT","MINUTE_SECOND","NATURAL","NULL","OPTIMIZE","OR","OUTER","PRIMARY","RANGE","READ_WRITE","REGEXP","REPEAT","RESTRICT","RIGHT","SCHEMAS","SENSITIVE","SHOW","SPECIFIC","SQLSTATE","SQL_CALC_FOUND_ROWS","STARTING","TERMINATED","TINYINT","TRAILING","UNDO","UNLOCK","USAGE","UTC_DATE","VALUES","VARCHARACTER","WHERE","WRITE","ZEROFILL","ALL","AND","ASENSITIVE","BIGINT","BOTH","CASCADE","CHAR","COLLATE","CONSTRAINT","CREATE","CURRENT_TIME","CURSOR","DAY_HOUR","DAY_SECOND","DECLARE","DELETE","DETERMINISTIC","DIV","DUAL","ELSEIF","EXISTS","FALSE","FLOAT4","FORCE","FULLTEXT","HAVING","HOUR_MINUTE","IGNORE","INFILE","INSENSITIVE","INT1","INT4","INTERVAL","ITERATE","KEYS","LEAVE","LIMIT","LOAD","LOCK","LONGTEXT","MASTER_SSL_VERIFY_SERVER_CERT","MEDIUMINT","MINUTE_MICROSECOND","MODIFIES","NO_WRITE_TO_BINLOG","ON","OPTIONALLY","OUT","PRECISION","PURGE","READS","REFERENCES","RENAME","REQUIRE","REVOKE","SCHEMA","SELECT","SET","SPATIAL","SQLEXCEPTION","SQL_BIG_RESULT","SSL","TABLE","TINYBLOB","TO","TRUE","UNIQUE","UPDATE","USING","UTC_TIMESTAMP","VARCHAR","WHEN","WITH","YEAR_MONTH","ADD","ANALYZE","ASC","BETWEEN","BLOB","CALL","CHANGE","CHECK","CONDITION","CONVERT","CURRENT_DATE","CURRENT_USER","DATABASES","DAY_MINUTE","DECIMAL","DELAYED","DESCRIBE","DISTINCTROW","DROP","ELSE","ESCAPED","EXPLAIN","FLOAT","FOR","FROM","GROUP","HOUR_MICROSECOND","IF","INDEX","INOUT","INT","INT3","INTEGER","IS","KEY","LEADING","LIKE","LINES","LOCALTIMESTAMP","LONGBLOB","LOW_PRIORITY","MEDIUMBLOB","MIDDLEINT","MOD","NOT","NUMERIC","OPTION","ORDER","OUTFILE","PROCEDURE","READ","REAL","RELEASE","REPLACE","RETURN","RLIKE","SECOND_MICROSECOND","SEPARATOR","SMALLINT","SQL","SQLWARNING","SQL_SMALL_RESULT","STRAIGHT_JOIN","THEN","TINYTEXT","TRIGGER","UNION","UNSIGNED","USE","UTC_TIME","VARBINARY","VARYING","WHILE","XOR","FULL","COLUMNS","MIN","MAX","STDEV","COUNT"]),o=/[*+\-<>=&|]/;return{startState:function(e){return{tokenize:u,context:null,indent:0,col:0}},token:function(e,t){e.sol()&&(t.context&&t.context.align==null&&(t.context.align=!1),t.indent=e.indentation());if(e.eatSpace())return null;var r=t.tokenize(e,t);r!="comment"&&t.context&&t.context.align==null&&t.context.type!="pattern"&&(t.context.align=!0);if(n=="(")l(t,")",e.column());else if(n=="[")l(t,"]",e.column());else if(n=="{")l(t,"}",e.column());else if(/[\]\}\)]/.test(n)){while(t.context&&t.context.type=="pattern")c(t);t.context&&n==t.context.type&&c(t)}else n=="."&&t.context&&t.context.type=="pattern"?c(t):/atom|string|variable/.test(r)&&t.context&&(/[\}\]]/.test(t.context.type)?l(t,"pattern",e.column()):t.context.type=="pattern"&&!t.context.align&&(t.context.align=!0,t.context.col=e.column()));return r},indent:function(e,n){var r=n&&n.charAt(0),i=e.context;if(/[\]\}]/.test(r))while(i&&i.type=="pattern")i=i.prev;var s=i&&r==i.type;return i?i.type=="pattern"?i.col:i.align?i.col+(s?0:1):i.indent+(s?0:t):0}}}),CodeMirror.defineMIME("text/x-mysql","mysql"),CodeMirror.defineMode("ntriples",function(){function t(t,n){var r=t.location,i;r==e.PRE_SUBJECT&&n=="<"?i=e.WRITING_SUB_URI:r==e.PRE_SUBJECT&&n=="_"?i=e.WRITING_BNODE_URI:r==e.PRE_PRED&&n=="<"?i=e.WRITING_PRED_URI:r==e.PRE_OBJ&&n=="<"?i=e.WRITING_OBJ_URI:r==e.PRE_OBJ&&n=="_"?i=e.WRITING_OBJ_BNODE:r==e.PRE_OBJ&&n=='"'?i=e.WRITING_OBJ_LITERAL:r==e.WRITING_SUB_URI&&n==">"?i=e.PRE_PRED:r==e.WRITING_BNODE_URI&&n==" "?i=e.PRE_PRED:r==e.WRITING_PRED_URI&&n==">"?i=e.PRE_OBJ:r==e.WRITING_OBJ_URI&&n==">"?i=e.POST_OBJ:r==e.WRITING_OBJ_BNODE&&n==" "?i=e.POST_OBJ:r==e.WRITING_OBJ_LITERAL&&n=='"'?i=e.POST_OBJ:r==e.WRITING_LIT_LANG&&n==" "?i=e.POST_OBJ:r==e.WRITING_LIT_TYPE&&n==">"?i=e.POST_OBJ:r==e.WRITING_OBJ_LITERAL&&n=="@"?i=e.WRITING_LIT_LANG:r==e.WRITING_OBJ_LITERAL&&n=="^"?i=e.WRITING_LIT_TYPE:n!=" "||r!=e.PRE_SUBJECT&&r!=e.PRE_PRED&&r!=e.PRE_OBJ&&r!=e.POST_OBJ?r==e.POST_OBJ&&n=="."?i=e.PRE_SUBJECT:i=e.ERROR:i=r,t.location=i}var e={PRE_SUBJECT:0,WRITING_SUB_URI:1,WRITING_BNODE_URI:2,PRE_PRED:3,WRITING_PRED_URI:4,PRE_OBJ:5,WRITING_OBJ_URI:6,WRITING_OBJ_BNODE:7,WRITING_OBJ_LITERAL:8,WRITING_LIT_LANG:9,WRITING_LIT_TYPE:10,POST_OBJ:11,ERROR:12},n=function(e){return e!=" "},r=function(e){return e!=">"};return{startState:function(){return{location:e.PRE_SUBJECT,uris:[],anchors:[],bnodes:[],langs:[],types:[]}},token:function(e,n){var r=e.next();if(r=="<"){t(n,r);var i="";return e.eatWhile(function(e){return e!="#"&&e!=">"?(i+=e,!0):!1}),n.uris.push(i),e.match("#",!1)?"variable":(e.next(),t(n,">"),"variable")}if(r=="#"){var s="";return e.eatWhile(function(e){return e!=">"&&e!=" "?(s+=e,!0):!1}),n.anchors.push(s),"variable-2"}if(r==">")return t(n,">"),"variable";if(r=="_"){t(n,r);var o="";return e.eatWhile(function(e){return e!=" "?(o+=e,!0):!1}),n.bnodes.push(o),e.next(),t(n," "),"builtin"}if(r=='"')return t(n,r),e.eatWhile(function(e){return e!='"'}),e.next(),e.peek()!="@"&&e.peek()!="^"&&t(n,'"'),"string";if(r=="@"){t(n,"@");var u="";return e.eatWhile(function(e){return e!=" "?(u+=e,!0):!1}),n.langs.push(u),e.next(),t(n," "),"string-2"}if(r=="^"){e.next(),t(n,"^");var a="";return e.eatWhile(function(e){return e!=">"?(a+=e,!0):!1}),n.types.push(a),e.next(),t(n,">"),"variable"}r==" "&&t(n,r),r=="."&&t(n,r)}}}),CodeMirror.defineMIME("text/n-triples","ntriples"),CodeMirror.defineMode("ocaml",function(e){function n(e,n){var s=e.sol(),o=e.next();if(o==='"')return n.tokenize=r,n.tokenize(e,n);if(o==="("&&e.eat("*"))return n.commentLevel++,n.tokenize=i,n.tokenize(e,n);if(o==="~")return e.eatWhile(/\w/),"variable-2";if(o==="`")return e.eatWhile(/\w/),"quote";if(/\d/.test(o))return e.eatWhile(/[\d]/),e.eat(".")&&e.eatWhile(/[\d]/),"number";if(/[+\-*&%=<>!?|]/.test(o))return"operator";e.eatWhile(/\w/);var u=e.current();return t[u]||"variable"}function r(e,t){var r,i=!1,s=!1;while((r=e.next())!=null){if(r==='"'&&!s){i=!0;break}s=!s&&r==="\\"}return i&&!s&&(t.tokenize=n),"string"}function i(e,t){var r,i;while(t.commentLevel>0&&(i=e.next())!=null)r==="("&&i==="*"&&t.commentLevel++,r==="*"&&i===")"&&t.commentLevel--,r=i;return t.commentLevel<=0&&(t.tokenize=n),"comment"}var t={"true":"atom","false":"atom",let:"keyword",rec:"keyword","in":"keyword",of:"keyword",and:"keyword",succ:"keyword","if":"keyword",then:"keyword","else":"keyword","for":"keyword",to:"keyword","while":"keyword","do":"keyword",done:"keyword",fun:"keyword","function":"keyword",val:"keyword",type:"keyword",mutable:"keyword",match:"keyword","with":"keyword","try":"keyword",raise:"keyword",begin:"keyword",end:"keyword",open:"builtin",trace:"builtin",ignore:"builtin",exit:"builtin",print_string:"builtin",print_endline:"builtin"};return{startState:function(){return{tokenize:n,commentLevel:0}},token:function(e,t){return e.eatSpace()?null:t.tokenize(e,t)}}}),CodeMirror.defineMIME("text/x-ocaml","ocaml"),CodeMirror.defineMode("pascal",function(e){function t(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function s(e,t){var s=e.next();if(s=="#"&&t.startOfLine)return e.skipToEnd(),"meta";if(s=='"'||s=="'")return t.tokenize=o(s),t.tokenize(e,t);if(s=="("&&e.eat("*"))return t.tokenize=u,u(e,t);if(/[\[\]{}\(\),;\:\.]/.test(s))return null;if(/\d/.test(s))return e.eatWhile(/[\w\.]/),"number";if(s=="/"&&e.eat("/"))return e.skipToEnd(),"comment";if(i.test(s))return e.eatWhile(i),"operator";e.eatWhile(/[\w\$_]/);var a=e.current();return n.propertyIsEnumerable(a)?"keyword":r.propertyIsEnumerable(a)?"atom":"variable"}function o(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r)n.tokenize=null;return"string"}}function u(e,t){var n=!1,r;while(r=e.next()){if(r==")"&&n){t.tokenize=null;break}n=r=="*"}return"comment"}var n=t("and array begin case const div do downto else end file for forward integer boolean char function goto if in label mod nil not of or packed procedure program record repeat set string then to type until var while with"),r={"null":!0},i=/[+\-*&%=<>!?|\/]/;return{startState:function(e){return{tokenize:null}},token:function(e,t){if(e.eatSpace())return null;var n=(t.tokenize||s)(e,t);return n=="comment"||n=="meta"?n:n},electricChars:"{}"}}),CodeMirror.defineMIME("text/x-pascal","pascal"),CodeMirror.defineMode("perl",function(e,t){function s(e,t,n,r,i){return t.chain=null,t.style=null,t.tail=null,t.tokenize=function(e,t){var s=!1,o,a=0;while(o=e.next()){if(o===n[a]&&!s)return n[++a]!==undefined?(t.chain=n[a],t.style=r,t.tail=i):i&&e.eatWhile(i),t.tokenize=u,r;s=!s&&o=="\\"}return r},t.tokenize(e,t)}function o(e,t,n){return t.tokenize=function(e,t){return e.string==n&&(t.tokenize=u),e.skipToEnd(),"string"},t.tokenize(e,t)}function u(e,t){if(e.eatSpace())return null;if(t.chain)return s(e,t,t.chain,t.style,t.tail);if(e.match(/^\-?[\d\.]/,!1)&&e.match(/^(\-?(\d*\.\d+(e[+-]?\d+)?|\d+\.\d*)|0x[\da-fA-F]+|0b[01]+|\d+(e[+-]?\d+)?)/))return"number";if(e.match(/^<<(?=\w)/))return e.eatWhile(/\w/),o(e,t,e.current().substr(2));if(e.sol()&&e.match(/^\=item(?!\w)/))return o(e,t,"=cut");var u=e.next();if(u=='"'||u=="'"){if(e.prefix(3)=="<<"+u){var a=e.pos;e.eatWhile(/\w/);var f=e.current().substr(1);if(f&&e.eat(u))return o(e,t,f);e.pos=a}return s(e,t,[u],"string")}if(u=="q"){var l=e.look(-2);if(!l||!/\w/.test(l)){l=e.look(0);if(l=="x"){l=e.look(1);if(l=="(")return e.eatSuffix(2),s(e,t,[")"],r,i);if(l=="[")return e.eatSuffix(2),s(e,t,["]"],r,i);if(l=="{")return e.eatSuffix(2),s(e,t,["}"],r,i);if(l=="<")return e.eatSuffix(2),s(e,t,[">"],r,i);if(/[\^'"!~\/]/.test(l))return e.eatSuffix(1),s(e,t,[e.eat(l)],r,i)}else if(l=="q"){l=e.look(1);if(l=="(")return e.eatSuffix(2),s(e,t,[")"],"string");if(l=="[")return e.eatSuffix(2),s(e,t,["]"],"string");if(l=="{")return e.eatSuffix(2),s(e,t,["}"],"string");if(l=="<")return e.eatSuffix(2),s(e,t,[">"],"string");if(/[\^'"!~\/]/.test(l))return e.eatSuffix(1),s(e,t,[e.eat(l)],"string")}else if(l=="w"){l=e.look(1);if(l=="(")return e.eatSuffix(2),s(e,t,[")"],"bracket");if(l=="[")return e.eatSuffix(2),s(e,t,["]"],"bracket");if(l=="{")return e.eatSuffix(2),s(e,t,["}"],"bracket");if(l=="<")return e.eatSuffix(2),s(e,t,[">"],"bracket");if(/[\^'"!~\/]/.test(l))return e.eatSuffix(1),s(e,t,[e.eat(l)],"bracket")}else if(l=="r"){l=e.look(1);if(l=="(")return e.eatSuffix(2),s(e,t,[")"],r,i);if(l=="[")return e.eatSuffix(2),s(e,t,["]"],r,i);if(l=="{")return e.eatSuffix(2),s(e,t,["}"],r,i);if(l=="<")return e.eatSuffix(2),s(e,t,[">"],r,i);if(/[\^'"!~\/]/.test(l))return e.eatSuffix(1),s(e,t,[e.eat(l)],r,i)}else if(/[\^'"!~\/(\[{<]/.test(l)){if(l=="(")return e.eatSuffix(1),s(e,t,[")"],"string");if(l=="[")return e.eatSuffix(1),s(e,t,["]"],"string");if(l=="{")return e.eatSuffix(1),s(e,t,["}"],"string");if(l=="<")return e.eatSuffix(1),s(e,t,[">"],"string");if(/[\^'"!~\/]/.test(l))return s(e,t,[e.eat(l)],"string")}}}if(u=="m"){var l=e.look(-2);if(!l||!/\w/.test(l)){l=e.eat(/[(\[{<\^'"!~\/]/);if(l){if(/[\^'"!~\/]/.test(l))return s(e,t,[l],r,i);if(l=="(")return s(e,t,[")"],r,i);if(l=="[")return s(e,t,["]"],r,i);if(l=="{")return s(e,t,["}"],r,i);if(l=="<")return s(e,t,[">"],r,i)}}}if(u=="s"){var l=/[\/>\]})\w]/.test(e.look(-2));if(!l){l=e.eat(/[(\[{<\^'"!~\/]/);if(l)return l=="["?s(e,t,["]","]"],r,i):l=="{"?s(e,t,["}","}"],r,i):l=="<"?s(e,t,[">",">"],r,i):l=="("?s(e,t,[")",")"],r,i):s(e,t,[l,l],r,i)}}if(u=="y"){var l=/[\/>\]})\w]/.test(e.look(-2));if(!l){l=e.eat(/[(\[{<\^'"!~\/]/);if(l)return l=="["?s(e,t,["]","]"],r,i):l=="{"?s(e,t,["}","}"],r,i):l=="<"?s(e,t,[">",">"],r,i):l=="("?s(e,t,[")",")"],r,i):s(e,t,[l,l],r,i)}}if(u=="t"){var l=/[\/>\]})\w]/.test(e.look(-2));if(!l){l=e.eat("r");if(l){l=e.eat(/[(\[{<\^'"!~\/]/);if(l)return l=="["?s(e,t,["]","]"],r,i):l=="{"?s(e,t,["}","}"],r,i):l=="<"?s(e,t,[">",">"],r,i):l=="("?s(e,t,[")",")"],r,i):s(e,t,[l,l],r,i)}}}if(u=="`")return s(e,t,[u],"variable-2");if(u=="/")return/~\s*$/.test(e.prefix())?s(e,t,[u],r,i):"operator";if(u=="$"){var a=e.pos;if(e.eatWhile(/\d/)||e.eat("{")&&e.eatWhile(/\d/)&&e.eat("}"))return"variable-2";e.pos=a}if(/[$@%]/.test(u)){var a=e.pos;if(e.eat("^")&&e.eat(/[A-Z]/)||!/[@$%&]/.test(e.look(-2))&&e.eat(/[=|\\\-#?@;:&`~\^!\[\]*'"$+.,\/<>()]/)){var l=e.current();if(n[l])return"variable-2"}e.pos=a}if(/[$@%&]/.test(u))if(e.eatWhile(/[\w$\[\]]/)||e.eat("{")&&e.eatWhile(/[\w$\[\]]/)&&e.eat("}")){var l=e.current();return n[l]?"variable-2":"variable"}if(u=="#"&&e.look(-2)!="$")return e.skipToEnd(),"comment";if(/[:+\-\^*$&%@=<>!?|\/~\.]/.test(u)){var a=e.pos;e.eatWhile(/[:+\-\^*$&%@=<>!?|\/~\.]/);if(n[e.current()])return"operator";e.pos=a}if(u=="_"&&e.pos==1){if(e.suffix(6)=="_END__")return s(e,t,["\0"],"comment");if(e.suffix(7)=="_DATA__")return s(e,t,["\0"],"variable-2");if(e.suffix(7)=="_C__")return s(e,t,["\0"],"string")}if(/\w/.test(u)){var a=e.pos;if(e.look(-2)=="{"&&(e.look(0)=="}"||e.eatWhile(/\w/)&&e.look(0)=="}"))return"string";e.pos=a}if(/[A-Z]/.test(u)){var c=e.look(-2),a=e.pos;e.eatWhile(/[A-Z_]/);if(!/[\da-z]/.test(e.look(0))){var l=n[e.current()];return l?(l[1]&&(l=l[0]),c!=":"?l==1?"keyword":l==2?"def":l==3?"atom":l==4?"operator":l==5?"variable-2":"meta":"meta"):"meta"}e.pos=a}if(/[a-zA-Z_]/.test(u)){var c=e.look(-2);e.eatWhile(/\w/);var l=n[e.current()];return l?(l[1]&&(l=l[0]),c!=":"?l==1?"keyword":l==2?"def":l==3?"atom":l==4?"operator":l==5?"variable-2":"meta":"meta"):"meta"}return null}var n={"->":4,"++":4,"--":4,"**":4,"=~":4,"!~":4,"*":4,"/":4,"%":4,x:4,"+":4,"-":4,".":4,"<<":4,">>":4,"<":4,">":4,"<=":4,">=":4,lt:4,gt:4,le:4,ge:4,"==":4,"!=":4,"<=>":4,eq:4,ne:4,cmp:4,"~~":4,"&":4,"|":4,"^":4,"&&":4,"||":4,"//":4,"..":4,"...":4,"?":4,":":4,"=":4,"+=":4,"-=":4,"*=":4,",":4,"=>":4,"::":4,not:4,and:4,or:4,xor:4,BEGIN:[5,1],END:[5,1],PRINT:[5,1],PRINTF:[5,1],GETC:[5,1],READ:[5,1],READLINE:[5,1],DESTROY:[5,1],TIE:[5,1],TIEHANDLE:[5,1],UNTIE:[5,1],STDIN:5,STDIN_TOP:5,STDOUT:5,STDOUT_TOP:5,STDERR:5,STDERR_TOP:5,$ARG:5,$_:5,"@ARG":5,"@_":5,$LIST_SEPARATOR:5,'$"':5,$PROCESS_ID:5,$PID:5,$$:5,$REAL_GROUP_ID:5,$GID:5,"$(":5,$EFFECTIVE_GROUP_ID:5,$EGID:5,"$)":5,$PROGRAM_NAME:5,$0:5,$SUBSCRIPT_SEPARATOR:5,$SUBSEP:5,"$;":5,$REAL_USER_ID:5,$UID:5,"$<":5,$EFFECTIVE_USER_ID:5,$EUID:5,"$>":5,$a:5,$b:5,$COMPILING:5,"$^C":5,$DEBUGGING:5,"$^D":5,"${^ENCODING}":5,$ENV:5,"%ENV":5,$SYSTEM_FD_MAX:5,"$^F":5,"@F":5,"${^GLOBAL_PHASE}":5,"$^H":5,"%^H":5,"@INC":5,"%INC":5,$INPLACE_EDIT:5,"$^I":5,"$^M":5,$OSNAME:5,"$^O":5,"${^OPEN}":5,$PERLDB:5,"$^P":5,$SIG:5,"%SIG":5,$BASETIME:5,"$^T":5,"${^TAINT}":5,"${^UNICODE}":5,"${^UTF8CACHE}":5,"${^UTF8LOCALE}":5,$PERL_VERSION:5,"$^V":5,"${^WIN32_SLOPPY_STAT}":5,$EXECUTABLE_NAME:5,"$^X":5,$1:5,$MATCH:5,"$&":5,"${^MATCH}":5,$PREMATCH:5,"$`":5,"${^PREMATCH}":5,$POSTMATCH:5,"$'":5,"${^POSTMATCH}":5,$LAST_PAREN_MATCH:5,"$+":5,$LAST_SUBMATCH_RESULT:5,"$^N":5,"@LAST_MATCH_END":5,"@+":5,"%LAST_PAREN_MATCH":5,"%+":5,"@LAST_MATCH_START":5,"@-":5,"%LAST_MATCH_START":5,"%-":5,$LAST_REGEXP_CODE_RESULT:5,"$^R":5,"${^RE_DEBUG_FLAGS}":5,"${^RE_TRIE_MAXBUF}":5,$ARGV:5,"@ARGV":5,ARGV:5,ARGVOUT:5,$OUTPUT_FIELD_SEPARATOR:5,$OFS:5,"$,":5,$INPUT_LINE_NUMBER:5,$NR:5,"$.":5,$INPUT_RECORD_SEPARATOR:5,$RS:5,"$/":5,$OUTPUT_RECORD_SEPARATOR:5,$ORS:5,"$\\":5,$OUTPUT_AUTOFLUSH:5,"$|":5,$ACCUMULATOR:5,"$^A":5,$FORMAT_FORMFEED:5,"$^L":5,$FORMAT_PAGE_NUMBER:5,"$%":5,$FORMAT_LINES_LEFT:5,"$-":5,$FORMAT_LINE_BREAK_CHARACTERS:5,"$:":5,$FORMAT_LINES_PER_PAGE:5,"$=":5,$FORMAT_TOP_NAME:5,"$^":5,$FORMAT_NAME:5,"$~":5,"${^CHILD_ERROR_NATIVE}":5,$EXTENDED_OS_ERROR:5,"$^E":5,$EXCEPTIONS_BEING_CAUGHT:5,"$^S":5,$WARNING:5,"$^W":5,"${^WARNING_BITS}":5,$OS_ERROR:5,$ERRNO:5,"$!":5,"%OS_ERROR":5,"%ERRNO":5,"%!":5,$CHILD_ERROR:5,"$?":5,$EVAL_ERROR:5,"$@":5,$OFMT:5,"$#":5,"$*":5,$ARRAY_BASE:5,"$[":5,$OLD_PERL_VERSION:5,"$]":5,"if":[1,1],elsif:[1,1],"else":[1,1],"while":[1,1],unless:[1,1],"for":[1,1],foreach:[1,1],abs:1,accept:1,alarm:1,atan2:1,bind:1,binmode:1,bless:1,bootstrap:1,"break":1,caller:1,chdir:1,chmod:1,chomp:1,chop:1,chown:1,chr:1,chroot:1,close:1,closedir:1,connect:1,"continue":[1,1],cos:1,crypt:1,dbmclose:1,dbmopen:1,"default":1,defined:1,"delete":1,die:1,"do":1,dump:1,each:1,endgrent:1,endhostent:1,endnetent:1,endprotoent:1,endpwent:1,endservent:1,eof:1,eval:1,exec:1,exists:1,exit:1,exp:1,fcntl:1,fileno:1,flock:1,fork:1,format:1,formline:1,getc:1,getgrent:1,getgrgid:1,getgrnam:1,gethostbyaddr:1,gethostbyname:1,gethostent:1,getlogin:1,getnetbyaddr:1,getnetbyname:1,getnetent:1,getpeername:1,getpgrp:1,getppid:1,getpriority:1,getprotobyname:1,getprotobynumber:1,getprotoent:1,getpwent:1,getpwnam:1,getpwuid:1,getservbyname:1,getservbyport:1,getservent:1,getsockname:1,getsockopt:1,given:1,glob:1,gmtime:1,"goto":1,grep:1,hex:1,"import":1,index:1,"int":1,ioctl:1,join:1,keys:1,kill:1,last:1,lc:1,lcfirst:1,length:1,link:1,listen:1,local:2,localtime:1,lock:1,log:1,lstat:1,m:null,map:1,mkdir:1,msgctl:1,msgget:1,msgrcv:1,msgsnd:1,my:2,"new":1,next:1,no:1,oct:1,open:1,opendir:1,ord:1,our:2,pack:1,"package":1,pipe:1,pop:1,pos:1,print:1,printf:1,prototype:1,push:1,q:null,qq:null,qr:null,quotemeta:null,qw:null,qx:null,rand:1,read:1,readdir:1,readline:1,readlink:1,readpipe:1,recv:1,redo:1,ref:1,rename:1,require:1,reset:1,"return":1,reverse:1,rewinddir:1,rindex:1,rmdir:1,s:null,say:1,scalar:1,seek:1,seekdir:1,select:1,semctl:1,semget:1,semop:1,send:1,setgrent:1,sethostent:1,setnetent:1,setpgrp:1,setpriority:1,setprotoent:1,setpwent:1,setservent:1,setsockopt:1,shift:1,shmctl:1,shmget:1,shmread:1,shmwrite:1,shutdown:1,sin:1,sleep:1,socket:1,socketpair:1,sort:1,splice:1,split:1,sprintf:1,sqrt:1,srand:1,stat:1,state:1,study:1,sub:1,substr:1,symlink:1,syscall:1,sysopen:1,sysread:1,sysseek:1,system:1,syswrite:1,tell:1,telldir:1,tie:1,tied:1,time:1,times:1,tr:null,truncate:1,uc:1,ucfirst:1,umask:1,undef:1,unlink:1,unpack:1,unshift:1,untie:1,use:1,utime:1,values:1,vec:1,wait:1,waitpid:1,wantarray:1,warn:1,when:1,write:1,y:null},r="string-2",i=/[goseximacplud]/;return{startState:function(){return{tokenize:u,chain:null,style:null,tail:null}},token:function(e,t){return(t.tokenize||u)(e,t)},electricChars:"{}"}}),CodeMirror.defineMIME("text/x-perl","perl"),CodeMirror.StringStream.prototype.look=function(e){return this.string.charAt(this.pos+(e||0))},CodeMirror.StringStream.prototype.prefix=function(e){if(e){var t=this.pos-e;return this.string.substr(t>=0?t:0,e)}return this.string.substr(0,this.pos-1)},CodeMirror.StringStream.prototype.suffix=function(e){var t=this.string.length,n=t-this.pos+1;return this.string.substr(this.pos,e&&e<t?e:n)},CodeMirror.StringStream.prototype.nsuffix=function(e){var t=this.pos,n=e||this.string.length-this.pos+1;return this.pos+=n,this.string.substr(t,n)},CodeMirror.StringStream.prototype.eatSuffix=function(e){var t=this.pos+e,n;t<=0?this.pos=0:t>=(n=this.string.length-1)?this.pos=n:this.pos=t},function(){function e(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function t(e){return function(t,n){return t.match(e)?n.tokenize=null:t.skipToEnd(),"string"}}var n={name:"clike",keywords:e("abstract and array as break case catch class clone const continue declare default do else elseif enddeclare endfor endforeach endif endswitch endwhile extends final for foreach function global goto if implements interface instanceof namespace new or private protected public static switch throw trait try use var while xor die echo empty exit eval include include_once isset list require require_once return print unset __halt_compiler self static parent"),blockKeywords:e("catch do else elseif for foreach if switch try while"),atoms:e("true false null TRUE FALSE NULL"),multiLineStrings:!0,hooks:{$:function(e,t){return e.eatWhile(/[\w\$_]/),"variable-2"},"<":function(e,n){return e.match(/<</)?(e.eatWhile(/[\w\.]/),n.tokenize=t(e.current().slice(3)),n.tokenize(e,n)):!1},"#":function(e,t){while(!e.eol()&&!e.match("?>",!1))e.next();return"comment"},"/":function(e,t){if(e.eat("/")){while(!e.eol()&&!e.match("?>",!1))e.next();return"comment"}return!1}}};CodeMirror.defineMode("php",function(e,t){function u(e,t){var n=t.curMode==o;e.sol()&&t.pending!='"'&&(t.pending=null);if(t.curMode==r){if(e.match(/^<\?\w*/))return t.curMode=o,t.curState=t.php,t.curClose="?>","meta";if(t.pending=='"'){while(!e.eol()&&e.next()!='"');var a="string"}else if(t.pending&&e.pos<t.pending.end){e.pos=t.pending.end;var a=t.pending.style}else var a=r.token(e,t.curState);t.pending=null;var f=e.current(),l=f.search(/<\?/);return l!=-1?(a=="string"&&/\"$/.test(f)&&!/\?>/.test(f)?t.pending='"':t.pending={end:e.pos,style:a},e.backUp(f.length-l)):a=="tag"&&e.current()==">"&&t.curState.context&&(/^script$/i.test(t.curState.context.tagName)?(t.curMode=i,t.curState=i.startState(r.indent(t.curState,"")),t.curClose=/^<\/\s*script\s*>/i):/^style$/i.test(t.curState.context.tagName)&&(t.curMode=s,t.curState=s.startState(r.indent(t.curState,"")),t.curClose=/^<\/\s*style\s*>/i)),a}return(!n||t.php.tokenize==null)&&e.match(t.curClose,n)?(t.curMode=r,t.curState=t.html,t.curClose=null,n?"meta":u(e,t)):t.curMode.token(e,t.curState)}var r=CodeMirror.getMode(e,{name:"xml",htmlMode:!0}),i=CodeMirror.getMode(e,"javascript"),s=CodeMirror.getMode(e,"css"),o=CodeMirror.getMode(e,n);return{startState:function(){var e=r.startState();return{html:e,php:o.startState(),curMode:t.startOpen?o:r,curState:t.startOpen?o.startState():e,curClose:t.startOpen?/^\?>/:null,mode:t.startOpen?"php":"html",pending:null}},copyState:function(e){var t=e.html,n=CodeMirror.copyState(r,t),i=e.php,s=CodeMirror.copyState(o,i),u;return e.curState==t?u=n:e.curState==i?u=s:u=CodeMirror.copyState(e.curMode,e.curState),{html:n,php:s,curMode:e.curMode,curState:u,curClose:e.curClose,mode:e.mode,pending:e.pending}},token:u,indent:function(e,t){return e.curMode!=o&&/^\s*<\//.test(t)||e.curMode==o&&/^\?>/.test(t)?r.indent(e.html,t):e.curMode.indent(e.curState,t)},electricChars:"/{}:",innerMode:function(e){return{state:e.curState,mode:e.curMode}}}},"xml","clike","javascript","css"),CodeMirror.defineMIME("application/x-httpd-php","php"),CodeMirror.defineMIME("application/x-httpd-php-open",{name:"php",startOpen:!0}),CodeMirror.defineMIME("text/x-php",n)}(),CodeMirror.defineMode("pig",function(e,t){function a(e,t,n){return t.tokenize=n,n(e,t)}function l(e,t){return f=e,t}function c(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=p;break}n=r=="*"}return l("comment","comment")}function h(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r&&!o)n.tokenize=p;return l("string","error")}}function p(e,t){var n=e.next();return n=='"'||n=="'"?a(e,t,h(n)):/[\[\]{}\(\),;\.]/.test(n)?l(n):/\d/.test(n)?(e.eatWhile(/[\w\.]/),l("number","number")):n=="/"?e.eat("*")?a(e,t,c):(e.eatWhile(u),l("operator","operator")):n=="-"?e.eat("-")?(e.skipToEnd(),l("comment","comment")):(e.eatWhile(u),l("operator","operator")):u.test(n)?(e.eatWhile(u),l("operator","operator")):(e.eatWhile(/[\w\$_]/),r&&r.propertyIsEnumerable(e.current().toUpperCase())&&!e.eat(")")&&!e.eat(".")?("keyword","keyword"):i&&i.propertyIsEnumerable(e.current().toUpperCase())?("keyword","variable-2"):s&&s.propertyIsEnumerable(e.current().toUpperCase())?("keyword","variable-3"):l("variable","pig-word"))}var n=e.indentUnit,r=t.keywords,i=t.builtins,s=t.types,o=t.multiLineStrings,u=/[*+\-%<>=&?:\/!|]/,f;return{startState:function(e){return{tokenize:p,startOfLine:!0}},token:function(e,t){if(e.eatSpace())return null;var n=t.tokenize(e,t);return n}}}),function(){function e(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}var t="ABS ACOS ARITY ASIN ATAN AVG BAGSIZE BINSTORAGE BLOOM BUILDBLOOM CBRT CEIL CONCAT COR COS COSH COUNT COUNT_STAR COV CONSTANTSIZE CUBEDIMENSIONS DIFF DISTINCT DOUBLEABS DOUBLEAVG DOUBLEBASE DOUBLEMAX DOUBLEMIN DOUBLEROUND DOUBLESUM EXP FLOOR FLOATABS FLOATAVG FLOATMAX FLOATMIN FLOATROUND FLOATSUM GENERICINVOKER INDEXOF INTABS INTAVG INTMAX INTMIN INTSUM INVOKEFORDOUBLE INVOKEFORFLOAT INVOKEFORINT INVOKEFORLONG INVOKEFORSTRING INVOKER ISEMPTY JSONLOADER JSONMETADATA JSONSTORAGE LAST_INDEX_OF LCFIRST LOG LOG10 LOWER LONGABS LONGAVG LONGMAX LONGMIN LONGSUM MAX MIN MAPSIZE MONITOREDUDF NONDETERMINISTIC OUTPUTSCHEMA  PIGSTORAGE PIGSTREAMING RANDOM REGEX_EXTRACT REGEX_EXTRACT_ALL REPLACE ROUND SIN SINH SIZE SQRT STRSPLIT SUBSTRING SUM STRINGCONCAT STRINGMAX STRINGMIN STRINGSIZE TAN TANH TOBAG TOKENIZE TOMAP TOP TOTUPLE TRIM TEXTLOADER TUPLESIZE UCFIRST UPPER UTF8STORAGECONVERTER ",n="VOID IMPORT RETURNS DEFINE LOAD FILTER FOREACH ORDER CUBE DISTINCT COGROUP JOIN CROSS UNION SPLIT INTO IF OTHERWISE ALL AS BY USING INNER OUTER ONSCHEMA PARALLEL PARTITION GROUP AND OR NOT GENERATE FLATTEN ASC DESC IS STREAM THROUGH STORE MAPREDUCE SHIP CACHE INPUT OUTPUT STDERROR STDIN STDOUT LIMIT SAMPLE LEFT RIGHT FULL EQ GT LT GTE LTE NEQ MATCHES TRUE FALSE ",r="BOOLEAN INT LONG FLOAT DOUBLE CHARARRAY BYTEARRAY BAG TUPLE MAP ";CodeMirror.defineMIME("text/x-pig",{name:"pig",builtins:e(t),keywords:e(n),types:e(r)})}(),CodeMirror.defineMode("plsql",function(e,t){function f(e,t,n){return t.tokenize=n,n(e,t)}function c(e,t){return l=e,t}function h(e,t){var n=e.next();return n=='"'||n=="'"?f(e,t,p(n)):/[\[\]{}\(\),;\.]/.test(n)?c(n):/\d/.test(n)?(e.eatWhile(/[\w\.]/),c("number","number")):n=="/"?e.eat("*")?f(e,t,d):(e.eatWhile(a),c("operator","operator")):n=="-"?e.eat("-")?(e.skipToEnd(),c("comment","comment")):(e.eatWhile(a),c("operator","operator")):n=="@"||n=="$"?(e.eatWhile(/[\w\d\$_]/),c("word","variable")):a.test(n)?(e.eatWhile(a),c("operator","operator")):(e.eatWhile(/[\w\$_]/),r&&r.propertyIsEnumerable(e.current().toLowerCase())?c("keyword","keyword"):i&&i.propertyIsEnumerable(e.current().toLowerCase())?c("keyword","builtin"):s&&s.propertyIsEnumerable(e.current().toLowerCase())?c("keyword","variable-2"):o&&o.propertyIsEnumerable(e.current().toLowerCase())?c("keyword","variable-3"):c("word","variable"))}function p(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r&&!u)n.tokenize=h;return c("string","plsql-string")}}function d(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=h;break}n=r=="*"}return c("comment","plsql-comment")}var n=e.indentUnit,r=t.keywords,i=t.functions,s=t.types,o=t.sqlplus,u=t.multiLineStrings,a=/[+\-*&%=<>!?:\/|]/,l;return{startState:function(e){return{tokenize:h,startOfLine:!0}},token:function(e,t){if(e.eatSpace())return null;var n=t.tokenize(e,t);return n}}}),function(){function e(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}var t="abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work",n="abs acos add_months ascii asin atan atan2 average bfilename ceil chartorowid chr concat convert cos cosh count decode deref dual dump dup_val_on_index empty error exp false floor found glb greatest hextoraw initcap instr instrb isopen last_day least lenght lenghtb ln lower lpad ltrim lub make_ref max min mod months_between new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null nvl others power rawtohex reftohex round rowcount rowidtochar rpad rtrim sign sin sinh soundex sqlcode sqlerrm sqrt stddev substr substrb sum sysdate tan tanh to_char to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid upper user userenv variance vsize",r="bfile blob character clob dec float int integer mlslabel natural naturaln nchar nclob number numeric nvarchar2 real rowtype signtype smallint string varchar varchar2",i="appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap";CodeMirror.defineMIME("text/x-plsql",{name:"plsql",keywords:e(t),functions:e(n),types:e(r),sqlplus:e(i)})}(),CodeMirror.defineMode("properties",function(){return{token:function(e,t){var n=e.sol()||t.afterSection,r=e.eol();t.afterSection=!1,n&&(t.nextMultiline?(t.inMultiline=!0,t.nextMultiline=!1):t.position="def"),r&&!t.nextMultiline&&(t.inMultiline=!1,t.position="def");if(n)while(e.eatSpace());var i=e.next();return!n||i!=="#"&&i!=="!"&&i!==";"?n&&i==="["?(t.afterSection=!0,e.skipTo("]"),e.eat("]"),"header"):i==="="||i===":"?(t.position="quote",null):(i==="\\"&&t.position==="quote"&&e.next()!=="u"&&(t.nextMultiline=!0),t.position):(t.position="comment",e.skipToEnd(),"comment")},startState:function(){return{position:"def",nextMultiline:!1,inMultiline:!1,afterSection:!1}}}}),CodeMirror.defineMIME("text/x-properties","properties"),CodeMirror.defineMIME("text/x-ini","properties"),CodeMirror.defineMode("python",function(e,t){function r(e){return new RegExp("^(("+e.join(")|(")+"))\\b")}function b(e,t){if(e.sol()){var r=t.scopes[0].offset;if(e.eatSpace()){var c=e.indentation();return c>r?y="indent":c<r&&(y="dedent"),null}r>0&&S(e,t)}if(e.eatSpace())return null;var h=e.peek();if(h==="#")return e.skipToEnd(),"comment";if(e.match(/^[0-9\.]/,!1)){var p=!1;e.match(/^\d*\.\d+(e[\+\-]?\d+)?/i)&&(p=!0),e.match(/^\d+\.\d*/)&&(p=!0),e.match(/^\.\d+/)&&(p=!0);if(p)return e.eat(/J/i),"number";var d=!1;e.match(/^0x[0-9a-f]+/i)&&(d=!0),e.match(/^0b[01]+/i)&&(d=!0),e.match(/^0o[0-7]+/i)&&(d=!0),e.match(/^[1-9]\d*(e[\+\-]?\d+)?/)&&(e.eat(/J/i),d=!0),e.match(/^0(?![\dx])/i)&&(d=!0);if(d)return e.eat(/L/i),"number"}return e.match(v)?(t.tokenize=w(e.current()),t.tokenize(e,t)):e.match(a)||e.match(u)?null:e.match(o)||e.match(i)||e.match(l)?"operator":e.match(s)?null:e.match(m)?"keyword":e.match(g)?"builtin":e.match(f)?"variable":(e.next(),n)}function w(e){while("rub".indexOf(e.charAt(0).toLowerCase())>=0)e=e.substr(1);var r=e.length==1,i="string";return function(o,u){while(!o.eol()){o.eatWhile(/[^'"\\]/);if(o.eat("\\")){o.next();if(r&&o.eol())return i}else{if(o.match(e))return u.tokenize=b,i;o.eat(/['"]/)}}if(r){if(t.singleLineStringErrors)return n;u.tokenize=b}return i}}function E(t,n,r){r=r||"py";var i=0;if(r==="py"){if(n.scopes[0].type!=="py"){n.scopes[0].offset=t.indentation();return}for(var s=0;s<n.scopes.length;++s)if(n.scopes[s].type==="py"){i=n.scopes[s].offset+e.indentUnit;break}}else i=t.column()+t.current().length;n.scopes.unshift({offset:i,type:r})}function S(e,t,n){n=n||"py";if(t.scopes.length==1)return;if(t.scopes[0].type==="py"){var r=e.indentation(),i=-1;for(var s=0;s<t.scopes.length;++s)if(r===t.scopes[s].offset){i=s;break}if(i===-1)return!0;while(t.scopes[0].offset!==r)t.scopes.shift();return!1}return n==="py"?(t.scopes[0].offset=e.indentation(),!1):t.scopes[0].type!=n?!0:(t.scopes.shift(),!1)}function x(e,t){y=null;var r=t.tokenize(e,t),i=e.current();if(i===".")return r=e.match(f,!1)?null:n,r===null&&t.lastToken==="meta"&&(r="meta"),r;if(i==="@")return e.match(f,!1)?"meta":n;(r==="variable"||r==="builtin")&&t.lastToken==="meta"&&(r="meta");if(i==="pass"||i==="return")t.dedent+=1;i==="lambda"&&(t.lambda=!0),(i===":"&&!t.lambda&&t.scopes[0].type=="py"||y==="indent")&&E(e,t);var s="[({".indexOf(i);return s!==-1&&E(e,t,"])}".slice(s,s+1)),y==="dedent"&&S(e,t)?n:(s="])}".indexOf(i),s!==-1&&S(e,t,i)?n:(t.dedent>0&&e.eol()&&t.scopes[0].type=="py"&&(t.scopes.length>1&&t.scopes.shift(),t.dedent-=1),r))}var n="error",i=new RegExp("^[\\+\\-\\*/%&|\\^~<>!]"),s=new RegExp("^[\\(\\)\\[\\]\\{\\}@,:`=;\\.]"),o=new RegExp("^((==)|(!=)|(<=)|(>=)|(<>)|(<<)|(>>)|(//)|(\\*\\*))"),u=new RegExp("^((\\+=)|(\\-=)|(\\*=)|(%=)|(/=)|(&=)|(\\|=)|(\\^=))"),a=new RegExp("^((//=)|(>>=)|(<<=)|(\\*\\*=))"),f=new RegExp("^[_A-Za-z][_A-Za-z0-9]*"),l=r(["and","or","not","is","in"]),c=["as","assert","break","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","lambda","pass","raise","return","try","while","with","yield"],h=["abs","all","any","bin","bool","bytearray","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip","__import__","NotImplemented","Ellipsis","__debug__"],p={builtins:["apply","basestring","buffer","cmp","coerce","execfile","file","intern","long","raw_input","reduce","reload","unichr","unicode","xrange","False","True","None"],keywords:["exec","print"]},d={builtins:["ascii","bytes","exec","print"],keywords:["nonlocal","False","True","None"]};if(!t.version||parseInt(t.version,10)!==3){c=c.concat(p.keywords),h=h.concat(p.builtins);var v=new RegExp("^(([rub]|(ur)|(br))?('{3}|\"{3}|['\"]))","i")}else{c=c.concat(d.keywords),h=h.concat(d.builtins);var v=new RegExp("^(([rb]|(br))?('{3}|\"{3}|['\"]))","i")}var m=r(c),g=r(h),y=null,T={startState:function(e){return{tokenize:b,scopes:[{offset:e||0,type:"py"}],lastToken:null,lambda:!1,dedent:0}},token:function(e,t){var n=x(e,t);return t.lastToken=n,e.eol()&&e.lambda&&(t.lambda=!1),n},indent:function(e,t){return e.tokenize!=b?0:e.scopes[0].offset}};return T}),CodeMirror.defineMIME("text/x-python","python"),CodeMirror.defineMode("r",function(e){function t(e){var t=e.split(" "),n={};for(var r=0;r<t.length;++r)n[t[r]]=!0;return n}function a(e,t){u=null;var a=e.next();if(a=="#")return e.skipToEnd(),"comment";if(a=="0"&&e.eat("x"))return e.eatWhile(/[\da-f]/i),"number";if(a=="."&&e.eat(/\d/))return e.match(/\d*(?:e[+\-]?\d+)?/),"number";if(/\d/.test(a))return e.match(/\d*(?:\.\d+)?(?:e[+\-]\d+)?L?/),"number";if(a=="'"||a=='"')return t.tokenize=f(a),"string";if(a=="."&&e.match(/.[.\d]+/))return"keyword";if(/[\w\.]/.test(a)&&a!="_"){e.eatWhile(/[\w\.]/);var l=e.current();return n.propertyIsEnumerable(l)?"atom":i.propertyIsEnumerable(l)?(s.propertyIsEnumerable(l)&&(u="block"),"keyword"):r.propertyIsEnumerable(l)?"builtin":"variable"}return a=="%"?(e.skipTo("%")&&e.next(),"variable-2"):a=="<"&&e.eat("-")?"arrow":a=="="&&t.ctx.argList?"arg-is":o.test(a)?a=="$"?"dollar":(e.eatWhile(o),"operator"):/[\(\){}\[\];]/.test(a)?(u=a,a==";"?"semi":null):null}function f(e){return function(t,n){if(t.eat("\\")){var r=t.next();return r=="x"?t.match(/^[a-f0-9]{2}/i):(r=="u"||r=="U")&&t.eat("{")&&t.skipTo("}")?t.next():r=="u"?t.match(/^[a-f0-9]{4}/i):r=="U"?t.match(/^[a-f0-9]{8}/i):/[0-7]/.test(r)&&t.match(/^[0-7]{1,2}/),"string-2"}var i;while((i=t.next())!=null){if(i==e){n.tokenize=a;break}if(i=="\\"){t.backUp(1);break}}return"string"}}function l(e,t,n){e.ctx={type:t,indent:e.indent,align:null,column:n.column(),prev:e.ctx}}function c(e){e.indent=e.ctx.indent,e.ctx=e.ctx.prev}var n=t("NULL NA Inf NaN NA_integer_ NA_real_ NA_complex_ NA_character_"),r=t("list quote bquote eval return call parse deparse"),i=t("if else repeat while function for in next break"),s=t("if else repeat while function for"),o=/[+\-*\/^<>=!&|~$:]/,u;return{startState:function(t){return{tokenize:a,ctx:{type:"top",indent:-e.indentUnit,align:!1},indent:0,afterIdent:!1}},token:function(e,t){e.sol()&&(t.ctx.align==null&&(t.ctx.align=!1),t.indent=e.indentation());if(e.eatSpace())return null;var n=t.tokenize(e,t);n!="comment"&&t.ctx.align==null&&(t.ctx.align=!0);var r=t.ctx.type;return(u==";"||u=="{"||u=="}")&&r=="block"&&c(t),u=="{"?l(t,"}",e):u=="("?(l(t,")",e),t.afterIdent&&(t.ctx.argList=!0)):u=="["?l(t,"]",e):u=="block"?l(t,"block",e):u==r&&c(t),t.afterIdent=n=="variable"||n=="keyword",n},indent:function(t,n){if(t.tokenize!=a)return 0;var r=n&&n.charAt(0),i=t.ctx,s=r==i.type;return i.type=="block"?i.indent+(r=="{"?0:e.indentUnit):i.align?i.column+(s?0:1):i.indent+(s?0:e.indentUnit)}}}),CodeMirror.defineMIME("text/x-rsrc","r"),CodeMirror.defineMode("changes",function(e,t){var n=/^-+$/,r=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)  ?\d{1,2} \d{2}:\d{2}(:\d{2})? [A-Z]{3,4} \d{4} - /,i=/^[\w+.-]+@[\w.-]+/;return{token:function(e){if(e.sol()){if(e.match(n))return"tag";if(e.match(r))return"tag"}return e.match(i)?"string":(e.next(),null)}}}),CodeMirror.defineMIME("text/x-rpm-changes","changes"),CodeMirror.defineMode("spec",function(e,t){var n=/^(i386|i586|i686|x86_64|ppc64|ppc|ia64|s390x|s390|sparc64|sparcv9|sparc|noarch|alphaev6|alpha|hppa|mipsel)/,r=/^(Name|Version|Release|License|Summary|Url|Group|Source|BuildArch|BuildRequires|BuildRoot|AutoReqProv|Provides|Requires(\(\w+\))?|Obsoletes|Conflicts|Recommends|Source\d*|Patch\d*|ExclusiveArch|NoSource|Supplements):/,i=/^%(debug_package|package|description|prep|build|install|files|clean|changelog|preun|postun|pre|post|triggerin|triggerun|pretrans|posttrans|verifyscript|check|triggerpostun|triggerprein|trigger)/,s=/^%(ifnarch|ifarch|if)/,o=/^%(else|endif)/,u=/^(\!|\?|\<\=|\<|\>\=|\>|\=\=|\&\&|\|\|)/;return{startState:function(){return{controlFlow:!1,macroParameters:!1,section:!1}},token:function(e,t){var a=e.peek();if(a=="#")return e.skipToEnd(),"comment";if(e.sol()){if(e.match(r))return"preamble";if(e.match(i))return"section"}if(e.match(/^\$\w+/))return"def";if(e.match(/^\$\{\w+\}/))return"def";if(e.match(o))return"keyword";if(e.match(s))return t.controlFlow=!0,"keyword";if(t.controlFlow){if(e.match(u))return"operator";if(e.match(/^(\d+)/))return"number";e.eol()&&(t.controlFlow=!1)}if(e.match(n))return"number";if(e.match(/^%[\w]+/))return e.match(/^\(/)&&(t.macroParameters=!0),"macro";if(t.macroParameters){if(e.match(/^\d+/))return"number";if(e.match(/^\)/))return t.macroParameters=!1,"macro"}return e.match(/^%\{\??[\w \-]+\}/)?"macro":(e.next(),null)}}}),CodeMirror.defineMIME("text/x-rpm-spec","spec"),CodeMirror.defineMode("rst",function(e,t){function n(e,t,n){e.fn=t,r(e,n)}function r(e,t){e.ctx=t||{}}function i(e,t){if(t&&typeof t!="string"){var r=t.current();t=r[r.length-1]}n(e,x,{back:t})}function s(e){if(e){var t=CodeMirror.listModes();for(var n in t)if(t[n]==e)return!0}return!1}function o(t){return s(t)?CodeMirror.getMode(e,t):null}function x(e,t){function c(e){return s||!t.ctx.back||e.test(t.ctx.back)}function h(t){return e.eol()||e.match(t,!1)}function p(t){return e.match(t)&&c(/\W/)&&h(/\W/)}var r,s,o;if(e.eat(/\\/))return r=e.next(),i(t,r),null;s=e.sol();if(s&&(r=e.eat(f))){for(o=0;e.eat(r);o++);if(o>=3&&e.match(/^\s*$/))return i(t,null),"header";e.backUp(o+1)}if(s&&e.match(m))return e.eol()||n(t,N),"meta";if(e.match(g)){if(!u)n(t,L);else{var l=u;n(t,L,{mode:l,local:l.startState()})}return"meta"}if(s&&e.match(S,!1)){if(!a)return n(t,L),"meta";var l=a;return n(t,L,{mode:l,local:l.startState()}),null}if(p(d))return i(t,e),"footnote";if(p(v))return i(t,e),"citation";r=e.next();if(c(y)){if(!(r!==":"&&r!=="|"||!e.eat(/\S/))){var b;return r===":"?b="builtin":b="atom",n(t,T,{ch:r,wide:!1,prev:null,token:b}),b}if(r==="*"||r==="`"){var w=r,E=!1;r=e.next(),r==w&&(E=!0,r=e.next());if(r&&!/\s/.test(r)){var b;return w==="*"?b=E?"strong":"em":b=E?"string":"string-2",n(t,T,{ch:w,wide:E,prev:null,token:b}),b}}}return i(t,r),null}function T(e,t){function o(e){return t.ctx.prev=e,s}var r=e.next(),s=t.ctx.token;if(r!=t.ctx.ch)return o(r);if(/\s/.test(t.ctx.prev))return o(r);if(t.ctx.wide){r=e.next();if(r!=t.ctx.ch)return o(r)}return!e.eol()&&!b.test(e.peek())?(t.ctx.wide&&e.backUp(1),o(r)):(n(t,x),i(t,r),s)}function N(e,t){var r=null;if(e.match(l))r="attribute";else if(e.match(c))r="link";else if(e.match(h))r="quote";else{if(!e.match(p))return e.eatSpace(),e.eol()?(i(t,e),null):(e.skipToEnd(),n(t,k),"comment");r="quote"}return n(t,C,{start:!0}),r}function C(e,t){var n="body";return!t.ctx.start||e.sol()?A(e,t,n):(e.skipToEnd(),r(t),n)}function k(e,t){return A(e,t,"comment")}function L(e,t){return u?e.sol()?(e.eatSpace()||i(t,e),null):u.token(e,t.ctx.local):A(e,t,"meta")}function A(e,t,n){return e.eol()||e.eatSpace()?(e.skipToEnd(),n):(i(t,e),null)}var u=o(t.verbatim),a=o("python"),f=/^[!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]/,l=/^\s*\w([-:.\w]*\w)?::(\s|$)/,c=/^\s*_[\w-]+:(\s|$)/,h=/^\s*\[(\d+|#)\](\s|$)/,p=/^\s*\[[A-Za-z][\w-]*\](\s|$)/,d=/^\[(\d+|#)\]_/,v=/^\[[A-Za-z][\w-]*\]_/,m=/^\.\.(\s|$)/,g=/^::\s*$/,y=/^[-\s"([{</:]/,b=/^[-\s`'")\]}>/:.,;!?\\_]/,w=/^\s*((\d+|[A-Za-z#])[.)]|\((\d+|[A-Z-a-z#])\))\s/,E=/^\s*[-\+\*]\s/,S=/^\s+(>>>|In \[\d+\]:)\s/;return{startState:function(){return{fn:x,ctx:{}}},copyState:function(e){return{fn:e.fn,ctx:e.ctx}},token:function(e,t){var n=t.fn(e,t);return n}}},"python"),CodeMirror.defineMIME("text/x-rst","rst"),CodeMirror.defineMode("ruby",function(e,t){function n(e){var t={};for(var n=0,r=e.length;n<r;++n)t[e[n]]=!0;return t}function a(e,t,n){return n.tokenize.push(e),e(t,n)}function f(e,t){u=null;if(e.sol()&&e.match("=begin")&&e.eol())return t.tokenize.push(p),"comment";if(e.eatSpace())return null;var n=e.next(),r;if(n=="`"||n=="'"||n=='"'||n=="/"&&!e.eol()&&e.peek()!=" ")return a(c(n,"string",n=='"'||n=="`"),e,t);if(n=="%"){var i,s=!1;e.eat("s")?i="atom":e.eat(/[WQ]/)?(i="string",s=!0):e.eat(/[wxqr]/)&&(i="string");var f=e.eat(/[^\w\s]/);return f?(o.propertyIsEnumerable(f)&&(f=o[f]),a(c(f,i,s,!0),e,t)):"operator"}if(n=="#")return e.skipToEnd(),"comment";if(n=="<"&&(r=e.match(/^<-?[\`\"\']?([a-zA-Z_?]\w*)[\`\"\']?(?:;|$)/)))return a(h(r[1]),e,t);if(n=="0")return e.eat("x")?e.eatWhile(/[\da-fA-F]/):e.eat("b")?e.eatWhile(/[01]/):e.eatWhile(/[0-7]/),"number";if(/\d/.test(n))return e.match(/^[\d_]*(?:\.[\d_]+)?(?:[eE][+\-]?[\d_]+)?/),"number";if(n=="?"){while(e.match(/^\\[CM]-/));return e.eat("\\")?e.eatWhile(/\w/):e.next(),"string"}return n==":"?e.eat("'")?a(c("'","atom",!1),e,t):e.eat('"')?a(c('"',"atom",!0),e,t):(e.eatWhile(/[\w\?]/),"atom"):n=="@"?(e.eat("@"),e.eatWhile(/[\w\?]/),"variable-2"):n=="$"?(e.next(),e.eatWhile(/[\w\?]/),"variable-3"):/\w/.test(n)?(e.eatWhile(/[\w\?]/),e.eat(":")?"atom":"ident"):n!="|"||!t.varList&&t.lastTok!="{"&&t.lastTok!="do"?/[\(\)\[\]{}\\;]/.test(n)?(u=n,null):n=="-"&&e.eat(">")?"arrow":/[=+\-\/*:\.^%<>~|]/.test(n)?(e.eatWhile(/[=+\-\/*:\.^%<>~|]/),"operator"):null:(u="|",null)}function l(){var e=1;return function(t,n){if(t.peek()=="}"){e--;if(e==0)return n.tokenize.pop(),n.tokenize[n.tokenize.length-1](t,n)}else t.peek()=="{"&&e++;return f(t,n)}}function c(e,t,n,r){return function(i,s){var o=!1,u;while((u=i.next())!=null){if(u==e&&(r||!o)){s.tokenize.pop();break}if(n&&u=="#"&&!o&&i.eat("{")){s.tokenize.push(l(arguments.callee));break}o=!o&&u=="\\"}return t}}function h(e){return function(t,n){return t.match(e)?n.tokenize.pop():t.skipToEnd(),"string"}}function p(e,t){return e.sol()&&e.match("=end")&&e.eol()&&t.tokenize.pop(),e.skipToEnd(),"comment"}var r=n(["alias","and","BEGIN","begin","break","case","class","def","defined?","do","else","elsif","END","end","ensure","false","for","if","in","module","next","not","or","redo","rescue","retry","return","self","super","then","true","undef","unless","until","when","while","yield","nil","raise","throw","catch","fail","loop","callcc","caller","lambda","proc","public","protected","private","require","load","require_relative","extend","autoload"]),i=n(["def","class","case","for","while","do","module","then","catch","loop","proc","begin"]),s=n(["end","until"]),o={"[":"]","{":"}","(":")"},u;return{startState:function(){return{tokenize:[f],indented:0,context:{type:"top",indented:-e.indentUnit},continuedLine:!1,lastTok:null,varList:!1}},token:function(e,t){e.sol()&&(t.indented=e.indentation());var n=t.tokenize[t.tokenize.length-1](e,t),o;if(n=="ident"){var a=e.current();n=r.propertyIsEnumerable(e.current())?"keyword":/^[A-Z]/.test(a)?"tag":t.lastTok=="def"||t.lastTok=="class"||t.varList?"def":"variable",i.propertyIsEnumerable(a)?o="indent":s.propertyIsEnumerable(a)?o="dedent":(a=="if"||a=="unless")&&e.column()==e.indentation()&&(o="indent")}if(u||n&&n!="comment")t.lastTok=a||u||n;return u=="|"&&(t.varList=!t.varList),o=="indent"||/[\(\[\{]/.test(u)?t.context={prev:t.context,type:u||n,indented:t.indented}:(o=="dedent"||/[\)\]\}]/.test(u))&&t.context.prev&&(t.context=t.context.prev),e.eol()&&(t.continuedLine=u=="\\"||n=="operator"),n},indent:function(t,n){if(t.tokenize[t.tokenize.length-1]!=f)return 0;var r=n&&n.charAt(0),i=t.context,s=i.type==o[r]||i.type=="keyword"&&/^(?:end|until|else|elsif|when|rescue)\b/.test(n);return i.indented+(s?0:e.indentUnit)+(t.continuedLine?e.indentUnit:0)},electricChars:"}de"}}),CodeMirror.defineMIME("text/x-ruby","ruby"),CodeMirror.defineMode("rust",function(){function u(e,t){return s=e,t}function a(e,t){var n=e.next();if(n=='"')return t.tokenize=f,t.tokenize(e,t);if(n=="'")return s="atom",e.eat("\\")?e.skipTo("'")?(e.next(),"string"):"error":(e.next(),e.eat("'")?"string":"error");if(n=="/"){if(e.eat("/"))return e.skipToEnd(),"comment";if(e.eat("*"))return t.tokenize=l(1),t.tokenize(e,t)}if(n=="#")return e.eat("[")?(s="open-attr",null):(e.eatWhile(/\w/),u("macro","meta"));if(n==":"&&e.match(":<"))return u("op",null);if(n.match(/\d/)||n=="."&&e.eat(/\d/)){var r=!1;return!e.match(/^x[\da-f]+/i)&&!e.match(/^b[01]+/)&&(e.eatWhile(/\d/),e.eat(".")&&(r=!0,e.eatWhile(/\d/)),e.match(/^e[+\-]?\d+/i)&&(r=!0)),r?e.match(/^f(?:32|64)/):e.match(/^[ui](?:8|16|32|64)/),u("atom","number")}return n.match(/[()\[\]{}:;,]/)?u(n,null):n=="-"&&e.eat(">")?u("->",null):n.match(i)?(e.eatWhile(i),u("op",null)):(e.eatWhile(/\w/),o=e.current(),e.match(/^::\w/)?(e.backUp(1),u("prefix","variable-2")):t.keywords.propertyIsEnumerable(o)?u(t.keywords[o],o.match(/true|false/)?"atom":"keyword"):u("name","variable"))}function f(e,t){var n,r=!1;while(n=e.next()){if(n=='"'&&!r)return t.tokenize=a,u("atom","string");r=!r&&n=="\\"}return u("op","string")}function l(e){return function(t,n){var r=null,i;while(i=t.next()){if(i=="/"&&r=="*"){if(e==1){n.tokenize=a;break}return n.tokenize=l(e-1),n.tokenize(t,n)}if(i=="*"&&r=="/")return n.tokenize=l(e+1),n.tokenize(t,n);r=i}return"comment"}}function h(){for(var e=arguments.length-1;e>=0;e--)c.cc.push(arguments[e])}function p(){return h.apply(null,arguments),!0}function d(e,t){var n=function(){var n=c.state;n.lexical={indented:n.indented,column:c.stream.column(),type:e,prev:n.lexical,info:t}};return n.lex=!0,n}function v(){var e=c.state;e.lexical.prev&&(e.lexical.type==")"&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function m(){c.state.keywords=r}function g(){c.state.keywords=n}function y(e,t){function n(r){return r==","?p(e,n):r==t?p():p(n)}return function(r){return r==t?p():h(e,n)}}function b(e,t){return p(d("stat",t),e,v,w)}function w(e){return e=="}"?p():e=="let"?b(L,"let"):e=="fn"?b(_):e=="type"?p(d("stat"),D,E,v,w):e=="enum"?b(P):e=="mod"?b(B):e=="iface"?b(j):e=="impl"?b(F):e=="open-attr"?p(d("]"),y(S,"]"),v):e=="ignore"||e.match(/[\]\);,]/)?p(w):h(d("stat"),S,v,E,w)}function E(e){return e==";"?p():h()}function S(e){return e=="atom"||e=="name"?p(x):e=="{"?p(d("}"),N,v):e.match(/[\[\(]/)?Q(e,S):e.match(/[\]\)\};,]/)?h():e=="if-style"?p(S,S):e=="else-style"||e=="op"?p(S):e=="for"?p(W,O,M,S,S):e=="alt"?p(S,V):e=="fn"?p(_):e=="macro"?p(K):p()}function x(e){return o=="."?p(T):o=="::<"?p(I,x):e=="op"||o==":"?p(S):e=="("||e=="["?Q(e,S):h()}function T(e){return o.match(/^\w+$/)?(c.marked="variable",p(x)):h(S)}function N(e){if(e=="op"){if(o=="|")return p(k,v,d("}","block"),w);if(o=="||")return p(v,d("}","block"),w)}return o=="mutable"||o.match(/^\w+$/)&&c.stream.peek()==":"&&!c.stream.match("::",!1)?h(C(S)):h(w)}function C(e){function t(n){return o=="mutable"||o=="with"?(c.marked="keyword",p(t)):o.match(/^\w*$/)?(c.marked="variable",p(t)):n==":"?p(e,t):n=="}"?p():p(t)}return t}function k(e){return e=="name"?(c.marked="def",p(k)):e=="op"&&o=="|"?p():p(k)}function L(e){return e.match(/[\]\)\};]/)?p():o=="="?p(S,A):e==","?p(L):h(W,O,L)}function A(e){return e.match(/[\]\)\};,]/)?h(L):h(S,A)}function O(e){return e==":"?p(m,R,g):h()}function M(e){return e=="name"&&o=="in"?(c.marked="keyword",p()):h()}function _(e){return o=="@"||o=="~"?(c.marked="keyword",p(_)):e=="name"?(c.marked="def",p(_)):o=="<"?p(I,_):e=="{"?h(S):e=="("?p(d(")"),y(q,")"),v,_):e=="->"?p(m,R,g,_):e==";"?p():p(_)}function D(e){return e=="name"?(c.marked="def",p(D)):o=="<"?p(I,D):o=="="?p(m,R,g):p(D)}function P(e){return e=="name"?(c.marked="def",p(P)):o=="<"?p(I,P):o=="="?p(m,R,g,E):e=="{"?p(d("}"),m,H,g,v):p(P)}function H(e){return e=="}"?p():e=="("?p(d(")"),y(R,")"),v,H):(o.match(/^\w+$/)&&(c.marked="def"),p(H))}function B(e){return e=="name"?(c.marked="def",p(B)):e=="{"?p(d("}"),w,v):h()}function j(e){return e=="name"?(c.marked="def",p(j)):o=="<"?p(I,j):e=="{"?p(d("}"),w,v):h()}function F(e){return o=="<"?p(I,F):o=="of"||o=="for"?(c.marked="keyword",p(R,F)):e=="name"?(c.marked="def",p(F)):e=="{"?p(d("}"),w,v):h()}function I(e){return o==">"?p():o==","?p(I):o==":"?p(R,I):h(R,I)}function q(e){return e=="name"?(c.marked="def",p(q)):e==":"?p(m,R,g):h()}function R(e){return e=="name"?(c.marked="variable-3",p(U)):o=="mutable"?(c.marked="keyword",p(R)):e=="atom"?p(U):e=="op"||e=="obj"?p(R):e=="fn"?p(z):e=="{"?p(d("{"),C(R),v):Q(e,R)}function U(e){return o=="<"?p(I):h()}function z(e){return e=="("?p(d("("),y(R,")"),v,z):e=="->"?p(R):h()}function W(e){return e=="name"?(c.marked="def",p(X)):e=="atom"?p(X):e=="op"?p(W):e.match(/[\]\)\};,]/)?h():Q(e,W)}function X(e){return e=="op"&&o=="."?p():o=="to"?(c.marked="keyword",p(W)):h()}function V(e){return e=="{"?p(d("}","alt"),$,v):h()}function $(e){return e=="}"?p():e=="|"?p($):o=="when"?(c.marked="keyword",p(S,J)):e.match(/[\]\);,]/)?p($):h(W,J)}function J(e){return e=="{"?p(d("}","alt"),w,v,$):h($)}function K(e){return e.match(/[\[\(\{]/)?Q(e,S):h()}function Q(e,t){return e=="["?p(d("]"),y(t,"]"),v):e=="("?p(d(")"),y(t,")"),v):e=="{"?p(d("}"),y(t,"}"),v):p()}function G(e,t,n){var r=e.cc;c.state=e,c.stream=t,c.marked=null,c.cc=r;for(;;){var i=r.length?r.pop():w;if(i(s)){while(r.length&&r[r.length-1].lex)r.pop()();return c.marked||n}}}var e=4,t=2,n={"if":"if-style","while":"if-style","else":"else-style","do":"else-style",ret:"else-style",fail:"else-style","break":"atom",cont:"atom","const":"let",resource:"fn",let:"let",fn:"fn","for":"for",alt:"alt",iface:"iface",impl:"impl",type:"type","enum":"enum",mod:"mod",as:"op","true":"atom","false":"atom",assert:"op",check:"op",claim:"op","native":"ignore",unsafe:"ignore","import":"else-style","export":"else-style",copy:"op",log:"op",log_err:"op",use:"op",bind:"op",self:"atom"},r=function(){var e={fn:"fn",block:"fn",obj:"obj"},t="bool uint int i8 i16 i32 i64 u8 u16 u32 u64 float f32 f64 str char".split(" ");for(var n=0,r=t.length;n<r;++n)e[t[n]]="atom";return e}(),i=/[+\-*&%=<>!?|\.@]/,s,o,c={state:null,stream:null,marked:null,cc:null};return v.lex=m.lex=g.lex=!0,{startState:function(){return{tokenize:a,cc:[],lexical:{indented:-e,column:0,type:"top",align:!1},keywords:n,indented:0}},token:function(e,t){e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation());if(e.eatSpace())return null;s=o=null;var n=t.tokenize(e,t);return n=="comment"?n:(t.lexical.hasOwnProperty("align")||(t.lexical.align=!0),s=="prefix"?n:(o||(o=e.current()),G(t,e,n)))},indent:function(n,r){if(n.tokenize!=a)return 0;var i=r&&r.charAt(0),s=n.lexical,o=s.type,u=i==o;return o=="stat"?s.indented+e:s.align?s.column+(u?0:1):s.indented+(u?0:s.info=="alt"?t:e)},electricChars:"{}"}}),CodeMirror.defineMIME("text/x-rustsrc","rust"),CodeMirror.defineMode("scheme",function(e,t){function c(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function d(e,t,n){this.indent=e,this.type=t,this.prev=n}function v(e,t,n){e.indentStack=new d(t,n,e.indentStack)}function m(e){e.indentStack=e.indentStack.prev}function E(e){return e.match(g)}function S(e){return e.match(y)}function x(e,t){return t===!0&&e.backUp(1),e.match(w)}function T(e){return e.match(b)}var n="builtin",r="comment",i="string",s="atom",o="number",u="bracket",a="keyword",f=2,l=1,h=c("\u03bb case-lambda call/cc class define-class exit-handler field import inherit init-field interface let*-values let-values let/ec mixin opt-lambda override protect provide public rename require require-for-syntax syntax syntax-case syntax-error unit/sig unless when with-syntax and begin call-with-current-continuation call-with-input-file call-with-output-file case cond define define-syntax delay do dynamic-wind else for-each if lambda let let* let-syntax letrec letrec-syntax map or syntax-rules abs acos angle append apply asin assoc assq assv atan boolean? caar cadr call-with-input-file call-with-output-file call-with-values car cdddar cddddr cdr ceiling char->integer char-alphabetic? char-ci<=? char-ci<? char-ci=? char-ci>=? char-ci>? char-downcase char-lower-case? char-numeric? char-ready? char-upcase char-upper-case? char-whitespace? char<=? char<? char=? char>=? char>? char? close-input-port close-output-port complex? cons cos current-input-port current-output-port denominator display eof-object? eq? equal? eqv? eval even? exact->inexact exact? exp expt #f floor force gcd imag-part inexact->exact inexact? input-port? integer->char integer? interaction-environment lcm length list list->string list->vector list-ref list-tail list? load log magnitude make-polar make-rectangular make-string make-vector max member memq memv min modulo negative? newline not null-environment null? number->string number? numerator odd? open-input-file open-output-file output-port? pair? peek-char port? positive? procedure? quasiquote quote quotient rational? rationalize read read-char real-part real? remainder reverse round scheme-report-environment set! set-car! set-cdr! sin sqrt string string->list string->number string->symbol string-append string-ci<=? string-ci<? string-ci=? string-ci>=? string-ci>? string-copy string-fill! string-length string-ref string-set! string<=? string<? string=? string>=? string>? string? substring symbol->string symbol? #t tan transcript-off transcript-on truncate values vector vector->list vector-fill! vector-length vector-ref vector-set! with-input-from-file with-output-to-file write write-char zero?"),p=c("define let letrec let* lambda"),g=new RegExp(/^(?:[-+]i|[-+][01]+#*(?:\/[01]+#*)?i|[-+]?[01]+#*(?:\/[01]+#*)?@[-+]?[01]+#*(?:\/[01]+#*)?|[-+]?[01]+#*(?:\/[01]+#*)?[-+](?:[01]+#*(?:\/[01]+#*)?)?i|[-+]?[01]+#*(?:\/[01]+#*)?)(?=[()\s;"]|$)/i),y=new RegExp(/^(?:[-+]i|[-+][0-7]+#*(?:\/[0-7]+#*)?i|[-+]?[0-7]+#*(?:\/[0-7]+#*)?@[-+]?[0-7]+#*(?:\/[0-7]+#*)?|[-+]?[0-7]+#*(?:\/[0-7]+#*)?[-+](?:[0-7]+#*(?:\/[0-7]+#*)?)?i|[-+]?[0-7]+#*(?:\/[0-7]+#*)?)(?=[()\s;"]|$)/i),b=new RegExp(/^(?:[-+]i|[-+][\da-f]+#*(?:\/[\da-f]+#*)?i|[-+]?[\da-f]+#*(?:\/[\da-f]+#*)?@[-+]?[\da-f]+#*(?:\/[\da-f]+#*)?|[-+]?[\da-f]+#*(?:\/[\da-f]+#*)?[-+](?:[\da-f]+#*(?:\/[\da-f]+#*)?)?i|[-+]?[\da-f]+#*(?:\/[\da-f]+#*)?)(?=[()\s;"]|$)/i),w=new RegExp(/^(?:[-+]i|[-+](?:(?:(?:\d+#+\.?#*|\d+\.\d*#*|\.\d+#*|\d+)(?:[esfdl][-+]?\d+)?)|\d+#*\/\d+#*)i|[-+]?(?:(?:(?:\d+#+\.?#*|\d+\.\d*#*|\.\d+#*|\d+)(?:[esfdl][-+]?\d+)?)|\d+#*\/\d+#*)@[-+]?(?:(?:(?:\d+#+\.?#*|\d+\.\d*#*|\.\d+#*|\d+)(?:[esfdl][-+]?\d+)?)|\d+#*\/\d+#*)|[-+]?(?:(?:(?:\d+#+\.?#*|\d+\.\d*#*|\.\d+#*|\d+)(?:[esfdl][-+]?\d+)?)|\d+#*\/\d+#*)[-+](?:(?:(?:\d+#+\.?#*|\d+\.\d*#*|\.\d+#*|\d+)(?:[esfdl][-+]?\d+)?)|\d+#*\/\d+#*)?i|(?:(?:(?:\d+#+\.?#*|\d+\.\d*#*|\.\d+#*|\d+)(?:[esfdl][-+]?\d+)?)|\d+#*\/\d+#*))(?=[()\s;"]|$)/i);return{startState:function(){return{indentStack:null,indentation:0,mode:!1,sExprComment:!1}},token:function(e,t){t.indentStack==null&&e.sol()&&(t.indentation=e.indentation());if(e.eatSpace())return null;var a=null;switch(t.mode){case"string":var l,c=!1;while((l=e.next())!=null){if(l=='"'&&!c){t.mode=!1;break}c=!c&&l=="\\"}a=i;break;case"comment":var l,d=!1;while((l=e.next())!=null){if(l=="#"&&d){t.mode=!1;break}d=l=="|"}a=r;break;case"s-expr-comment":t.mode=!1;if(e.peek()!="("&&e.peek()!="["){e.eatWhile(/[^/s]/),a=r;break}t.sExprComment=0;default:var g=e.next();if(g=='"')t.mode="string",a=i;else if(g=="'")a=s;else if(g=="#")if(e.eat("|"))t.mode="comment",a=r;else if(e.eat(/[tf]/i))a=s;else if(e.eat(";"))t.mode="s-expr-comment",a=r;else{var y=null,b=!1,w=!0;e.eat(/[ei]/i)?b=!0:e.backUp(1),e.match(/^#b/i)?y=E:e.match(/^#o/i)?y=S:e.match(/^#x/i)?y=T:e.match(/^#d/i)?y=x:e.match(/^[-+0-9.]/,!1)?(w=!1,y=x):b||e.eat("#"),y!=null&&(w&&!b&&e.match(/^#[ei]/i),y(e)&&(a=o))}else if(/^[-+0-9.]/.test(g)&&x(e,!0))a=o;else if(g==";")e.skipToEnd(),a=r;else if(g=="("||g=="["){var N="",C=e.column(),k;while((k=e.eat(/[^\s\(\[\;\)\]]/))!=null)N+=k;N.length>0&&p.propertyIsEnumerable(N)?v(t,C+f,g):(e.eatSpace(),e.eol()||e.peek()==";"?v(t,C+1,g):v(t,C+e.current().length,g)),e.backUp(e.current().length-1),typeof t.sExprComment=="number"&&t.sExprComment++,a=u}else g==")"||g=="]"?(a=u,t.indentStack!=null&&t.indentStack.type==(g==")"?"(":"[")&&(m(t),typeof t.sExprComment=="number"&&--t.sExprComment==0&&(a=r,t.sExprComment=!1))):(e.eatWhile(/[\w\$_\-!$%&*+\.\/:<=>?@\^~]/),h&&h.propertyIsEnumerable(e.current())?a=n:a="variable")}return typeof t.sExprComment=="number"?r:a},indent:function(e,t){return e.indentStack==null?e.indentation:e.indentStack.indent}}}),CodeMirror.defineMIME("text/x-scheme","scheme"),CodeMirror.defineMode("shell",function(e){function n(e,n){var r=n.split(" ");for(var i=0;i<r.length;i++)t[r[i]]=e}function r(e,n){var r=e.sol(),u=e.next();if(u==="'"||u==='"'||u==="`")return n.tokens.unshift(i(u)),o(e,n);if(u==="#")return r&&e.eat("!")?(e.skipToEnd(),"meta"):(e.skipToEnd(),"comment");if(u==="$")return n.tokens.unshift(s),o(e,n);if(u==="+"||u==="=")return"operator";if(u==="-")return e.eat("-"),e.eatWhile(/\w/),"attribute";if(/\d/.test(u)){e.eatWhile(/\d/);if(!/\w/.test(e.peek()))return"number"}e.eatWhile(/\w/);var a=e.current();return e.peek()==="="&&/\w+/.test(a)?"def":t.hasOwnProperty(a)?t[a]:null}function i(e){return function(t,n){var r,i=!1,o=!1;while((r=t.next())!=null){if(r===e&&!o){i=!0;break}if(r==="$"&&!o&&e!=="'"){o=!0,t.backUp(1),n.tokens.unshift(s);break}o=!o&&r==="\\"}return(i||!o)&&n.tokens.shift(),e==="`"||e===")"?"quote":"string"}}function o(e,t){return(t.tokens[0]||r)(e,t)}var t={};n("atom","true false"),n("keyword","if then do else elif while until for in esac fi fin fil done exit set unset export function"),n("builtin","ab awk bash beep cat cc cd chown chmod chroot clear cp curl cut diff echo find gawk gcc get git grep kill killall ln ls make mkdir openssl mv nc node npm ping ps restart rm rmdir sed service sh shopt shred source sort sleep ssh start stop su sudo tee telnet top touch vi vim wall wc wget who write yes zsh");var s=function(e,t){t.tokens.length>1&&e.eat("$");var n=e.next(),r=/\w/;return n==="{"&&(r=/[^}]/),n==="("?(t.tokens[0]=i(")"),o(e,t)):(/\d/.test(n)||(e.eatWhile(r),e.eat("}")),t.tokens.shift(),"def")};return{startState:function(){return{tokens:[]}},token:function(e,t){return e.eatSpace()?null:o(e,t)}}}),CodeMirror.defineMIME("text/x-sh","shell"),CodeMirror.defineMode("sieve",function(e){function t(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function s(e,t){var i=e.next();if(i=="/"&&e.eat("*"))return t.tokenize=u,u(e,t);if(i==="#")return e.skipToEnd(),"comment";if(i=='"')return t.tokenize=a(i),t.tokenize(e,t);if(i==="{")return t._indent++,null;if(i==="}")return t._indent--,null;if(/[{}\(\),;]/.test(i))return null;if(/\d/.test(i))return e.eatWhile(/[\d]/),e.eat(/[KkMmGg]/),"number";if(i==":")return e.eatWhile(/[a-zA-Z_]/),e.eatWhile(/[a-zA-Z0-9_]/),"operator";e.eatWhile(/[\w\$_]/);var s=e.current();if(s=="text"&&e.eat(":"))return t.tokenize=o,"string";if(n.propertyIsEnumerable(s))return"keyword";if(r.propertyIsEnumerable(s))return"atom"}function o(e,t){return t._multiLineString=!0,e.sol()?(e.next()=="."&&e.eol()&&(t._multiLineString=!1,t.tokenize=s),"string"):(e.eatSpace(),e.peek()=="#"?(e.skipToEnd(),"comment"):(e.skipToEnd(),"string"))}function u(e,t){var n=!1,r;while((r=e.next())!=null){if(n&&r=="/"){t.tokenize=s;break}n=r=="*"}return"comment"}function a(e){return function(t,n){var r=!1,i;while((i=t.next())!=null){if(i==e&&!r)break;r=!r&&i=="\\"}return r||(n.tokenize=s),"string"}}var n=t("if elsif else stop require"),r=t("true false not"),i=e.indentUnit;return{startState:function(e){return{tokenize:s,baseIndent:e||0,_indent:0}},token:function(e,t){return e.eatSpace()?null:(t.tokenize||s)(e,t)},indent:function(e,t){return e.baseIndent+e._indent*i},electricChars:"}"}}),CodeMirror.defineMIME("application/sieve","sieve"),CodeMirror.defineMode("smalltalk",function(e,t){var n=/[+\-/\\*~<>=@%|&?!.:;^]/,r=/true|false|nil|self|super|thisContext/,i=function(e,t){this.next=e,this.parent=t},s=function(e,t,n){this.name=e,this.context=t,this.eos=n},o=function(){this.context=new i(u,null),this.expectVariable=!0,this.indentation=0,this.userIndentationDelta=0};o.prototype.userIndent=function(t){this.userIndentationDelta=t>0?t/e.indentUnit-this.indentation:0};var u=function(e,t,o){var u=new s(null,t,!1),c=e.next();return c==='"'?u=a(e,new i(a,t)):c==="'"?u=f(e,new i(f,t)):c==="#"?(e.eatWhile(/[^ .]/),u.name="string-2"):c==="$"?(e.eatWhile(/[^ ]/),u.name="string-2"):c==="|"&&o.expectVariable?u.context=new i(l,t):/[\[\]{}()]/.test(c)?(u.name="bracket",u.eos=/[\[{(]/.test(c),c==="["?o.indentation++:c==="]"&&(o.indentation=Math.max(0,o.indentation-1))):n.test(c)?(e.eatWhile(n),u.name="operator",u.eos=c!==";"):/\d/.test(c)?(e.eatWhile(/[\w\d]/),u.name="number"):/[\w_]/.test(c)?(e.eatWhile(/[\w\d_]/),u.name=o.expectVariable?r.test(e.current())?"keyword":"variable":null):u.eos=o.expectVariable,u},a=function(e,t){return e.eatWhile(/[^"]/),new s("comment",e.eat('"')?t.parent:t,!0)},f=function(e,t){return e.eatWhile(/[^']/),new s("string",e.eat("'")?t.parent:t,!1)},l=function(e,t,n){var r=new s(null,t,!1),i=e.next();return i==="|"?(r.context=t.parent,r.eos=!0):(e.eatWhile(/[^|]/),r.name="variable"),r};return{startState:function(){return new o},token:function(e,t){t.userIndent(e.indentation());if(e.eatSpace())return null;var n=t.context.next(e,t.context,t);return t.context=n.context,t.expectVariable=n.eos,t.lastToken=n,n.name},blankLine:function(e){e.userIndent(0)},indent:function(t,n){var r=t.context.next===u&&n&&n.charAt(0)==="]"?-1:t.userIndentationDelta;return(t.indentation+r)*e.indentUnit},electricChars:"]"}}),CodeMirror.defineMIME("text/x-stsrc",{name:"smalltalk"}),CodeMirror.defineMode("smarty",function(e,t){function u(e,t){return r=t,e}function a(e,t){function n(n){return t.tokenize=n,n(e,t)}return e.match(s,!0)?e.eat("*")?n(c("comment","*"+o)):(t.tokenize=f,"tag"):(e.next(),null)}function f(e,t){if(e.match(o,!0))return t.tokenize=a,u("tag",null);var s=e.next();if(s=="$")return e.eatWhile(i.validIdentifier),u("variable-2","variable");if(s==".")return u("operator","property");if(i.stringChar.test(s))return t.tokenize=l(s),u("string","string");if(i.operatorChars.test(s))return e.eatWhile(i.operatorChars),u("operator","operator");if(s=="["||s=="]")return u("bracket","bracket");if(/\d/.test(s))return e.eatWhile(/\d/),u("number","number");if(t.last=="variable"){if(s=="@")return e.eatWhile(i.validIdentifier),u("property","property");if(s=="|")return e.eatWhile(i.validIdentifier),u("qualifier","modifier")}else{if(t.last=="whitespace")return e.eatWhile(i.validIdentifier),u("attribute","modifier");if(t.last=="property")return e.eatWhile(i.validIdentifier),u("property",null);if(/\s/.test(s))return r="whitespace",null}var f="";s!="/"&&(f+=s);var c="";while(c=e.eat(i.validIdentifier))f+=c;var h,p;for(h=0,p=n.length;h<p;h++)if(n[h]==f)return u("keyword","keyword");return/\s/.test(s)?null:u("tag","tag")}function l(e){return function(t,n){while(!t.eol())if(t.next()==e){n.tokenize=f;break}return"string"}}function c(e,t){return function(n,r){while(!n.eol()){if(n.match(t)){r.tokenize=a;break}n.next()}return e}}var n=["debug","extends","function","include","literal"],r,i={operatorChars:/[+\-*&%=<>!?]/,validIdentifier:/[a-zA-Z0-9\_]/,stringChar:/[\'\"]/},s=typeof e.mode.leftDelimiter!="undefined"?e.mode.leftDelimiter:"{",o=typeof e.mode.rightDelimiter!="undefined"?e.mode.rightDelimiter:"}";return{startState:function(){return{tokenize:a,mode:"smarty",last:null}},token:function(e,t){var n=t.tokenize(e,t);return t.last=r,n},electricChars:""}}),CodeMirror.defineMIME("text/x-smarty","smarty"),CodeMirror.defineMode("sparql",function(e){function r(e){return new RegExp("^(?:"+e.join("|")+")$","i")}function u(e,t){var r=e.next();n=null;if(r=="$"||r=="?")return e.match(/^[\w\d]*/),"variable-2";if(r=="<"&&!e.match(/^[\s\u00a0=]/,!1))return e.match(/^[^\s\u00a0>]*>?/),"atom";if(r=='"'||r=="'")return t.tokenize=a(r),t.tokenize(e,t);if(/[{}\(\),\.;\[\]]/.test(r))return n=r,null;if(r=="#")return e.skipToEnd(),"comment";if(o.test(r))return e.eatWhile(o),null;if(r==":")return e.eatWhile(/[\w\d\._\-]/),"atom";e.eatWhile(/[_\w\d]/);if(e.eat(":"))return e.eatWhile(/[\w\d_\-]/),"atom";var u=e.current(),f;return i.test(u)?null:s.test(u)?"keyword":"variable"}function a(e){return function(t,n){var r=!1,i;while((i=t.next())!=null){if(i==e&&!r){n.tokenize=u;break}r=!r&&i=="\\"}return"string"}}function f(e,t,n){e.context={prev:e.context,indent:e.indent,col:n,type:t}}function l(e){e.indent=e.context.indent,e.context=e.context.prev}var t=e.indentUnit,n,i=r(["str","lang","langmatches","datatype","bound","sameterm","isiri","isuri","isblank","isliteral","union","a"]),s=r(["base","prefix","select","distinct","reduced","construct","describe","ask","from","named","where","order","limit","offset","filter","optional","graph","by","asc","desc"]),o=/[*+\-<>=&|]/;return{startState:function(e){return{tokenize:u,context:null,indent:0,col:0}},token:function(e,t){e.sol()&&(t.context&&t.context.align==null&&(t.context.align=!1),t.indent=e.indentation());if(e.eatSpace())return null;var r=t.tokenize(e,t);r!="comment"&&t.context&&t.context.align==null&&t.context.type!="pattern"&&(t.context.align=!0);if(n=="(")f(t,")",e.column());else if(n=="[")f(t,"]",e.column());else if(n=="{")f(t,"}",e.column());else if(/[\]\}\)]/.test(n)){while(t.context&&t.context.type=="pattern")l(t);t.context&&n==t.context.type&&l(t)}else n=="."&&t.context&&t.context.type=="pattern"?l(t):/atom|string|variable/.test(r)&&t.context&&(/[\}\]]/.test(t.context.type)?f(t,"pattern",e.column()):t.context.type=="pattern"&&!t.context.align&&(t.context.align=!0,t.context.col=e.column()));return r},indent:function(e,n){var r=n&&n.charAt(0),i=e.context;if(/[\]\}]/.test(r))while(i&&i.type=="pattern")i=i.prev;var s=i&&r==i.type;return i?i.type=="pattern"?i.col:i.align?i.col+(s?0:1):i.indent+(s?0:t):0}}}),CodeMirror.defineMIME("application/x-sparql-query","sparql"),CodeMirror.defineMode("stex",function(e,t){function n(e,t){e.cmdState.push(t)}function r(e){return e.cmdState.length>0?e.cmdState[e.cmdState.length-1]:null}function i(e){if(e.cmdState.length>0){var t=e.cmdState.pop();t.closeBracket()}}function s(e){var t=e.cmdState;for(var n=t.length-1;n>=0;n--){var r=t[n];if(r.name=="DEFAULT")continue;return r.styleIdentifier()}return null}function o(e,t,n,r){return function(){this.name=e,this.bracketNo=0,this.style=t,this.styles=r,this.brackets=n,this.styleIdentifier=function(e){return this.bracketNo<=this.styles.length?this.styles[this.bracketNo-1]:null},this.openBracket=function(e){return this.bracketNo++,"bracket"},this.closeBracket=function(e){}}}function a(e,t){e.f=t}function f(e,t){if(e.match(/^\\[a-zA-Z@]+/)){var i=e.current();i=i.substr(1,i.length-1);var o;return u.hasOwnProperty(i)?o=u[i]:o=u.DEFAULT,o=new o,n(t,o),a(t,c),o.style}if(e.match(/^\\[$&%#{}_]/))return"tag";if(e.match(/^\\[,;!\/]/))return"tag";var f=e.next();if(f=="%")return e.eol()||a(t,l),"comment";if(f=="}"||f=="]")return o=r(t),o?(o.closeBracket(f),a(t,c),"bracket"):"error";return f=="{"||f=="["?(o=u.DEFAULT,o=new o,n(t,o),"bracket"):/\d/.test(f)?(e.eatWhile(/[\w.%]/),"atom"):(e.eatWhile(/[\w-_]/),s(t))}function l(e,t){return e.skipToEnd(),a(t,f),"comment"}function c(e,t){var n=e.peek();if(n=="{"||n=="["){var s=r(t),o=s.openBracket(n);return e.eat(n),a(t,f),"bracket"}return/[ \t\r]/.test(n)?(e.eat(n),null):(a(t,f),s=r(t),s&&i(t),f(e,t))}var u=new Array;return u.importmodule=o("importmodule","tag","{[",["string","builtin"]),u.documentclass=o("documentclass","tag","{[",["","atom"]),u.usepackage=o("documentclass","tag","[",["atom"]),u.begin=o("documentclass","tag","[",["atom"]),u.end=o("documentclass","tag","[",["atom"]),u.DEFAULT=function(){this.name="DEFAULT",this.style="tag",this.styleIdentifier=function(e){},this.openBracket=function(e){},this.closeBracket=function(e){}},{startState:function(){return{f:f,cmdState:[]}},copyState:function(e){return{f:e.f,cmdState:e.cmdState.slice(0,e.cmdState.length)}},token:function(e,t){var n=t.f(e,t),r=e.current();return n}}}),CodeMirror.defineMIME("text/x-stex","stex"),CodeMirror.defineMIME("text/x-latex","stex"),CodeMirror.defineMode("tiddlywiki",function(e,t){function y(e,t,n){return t.tokenize=n,n(e,t)}function b(e,t){var n=!1,r;while((r=e.next())!=null){if(r==t&&!n)return!1;n=!n&&r=="\\"}return n}function S(e,t,n){return w=e,E=n,t}function x(e,t){var n=e.sol(),i,v;t.block=!1,i=e.peek();if(n&&/[<\/\*{}\-]/.test(i)){if(e.match(d))return t.block=!0,y(e,t,k);if(e.match(f))return S("quote","quote");if(e.match(u)||e.match(a))return S("code","comment");if(e.match(l)||e.match(c)||e.match(h)||e.match(p))return S("code","comment");if(e.match(o))return S("hr","hr")}i=e.next();if(n&&/[\/\*!#;:>|]/.test(i)){if(i=="!")return e.skipToEnd(),S("header","header");if(i=="*")return e.eatWhile("*"),S("list","comment");if(i=="#")return e.eatWhile("#"),S("list","comment");if(i==";")return e.eatWhile(";"),S("list","comment");if(i==":")return e.eatWhile(":"),S("list","comment");if(i==">")return e.eatWhile(">"),S("quote","quote");if(i=="|")return S("table","header")}if(i=="{"&&e.match(/\{\{/))return y(e,t,k);if(/[hf]/i.test(i)&&/[ti]/i.test(e.peek())&&e.match(/\b(ttps?|tp|ile):\/\/[\-A-Z0-9+&@#\/%?=~_|$!:,.;]*[A-Z0-9+&@#\/%=~_|$]/i))return S("link","link");if(i=='"')return S("string","string");if(i=="~")return S("text","brace");if(/[\[\]]/.test(i)&&e.peek()==i)return e.next(),S("brace","brace");if(i=="@")return e.eatWhile(s),S("link","link");if(/\d/.test(i))return e.eatWhile(/\d/),S("number","number");if(i=="/"){if(e.eat("%"))return y(e,t,N);if(e.eat("/"))return y(e,t,L)}if(i=="_"&&e.eat("_"))return y(e,t,A);if(i=="-"&&e.eat("-")){if(e.peek()!=" ")return y(e,t,O);if(e.peek()==" ")return S("text","brace")}if(i=="'"&&e.eat("'"))return y(e,t,C);if(i!="<")return S(i);if(e.eat("<"))return y(e,t,M);e.eatWhile(/[\w\$_]/);var m=e.current(),g=r.propertyIsEnumerable(m)&&r[m];return g?S(g.type,g.style,m):S("text",null,m)}function T(e){return function(t,n){return b(t,e)||(n.tokenize=x),S("string","string")}}function N(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=x;break}n=r=="%"}return S("comment","comment")}function C(e,t){var n=!1,r;while(r=e.next()){if(r=="'"&&n){t.tokenize=x;break}n=r=="'"}return S("text","strong")}function k(e,t){var n,r=t.block;return r&&e.current()?S("code","comment"):!r&&e.match(g)?(t.tokenize=x,S("code","comment")):r&&e.sol()&&e.match(v)?(t.tokenize=x,S("code","comment")):(n=e.next(),r?S("code","comment"):S("code","comment"))}function L(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=x;break}n=r=="/"}return S("text","em")}function A(e,t){var n=!1,r;while(r=e.next()){if(r=="_"&&n){t.tokenize=x;break}n=r=="_"}return S("text","underlined")}function O(e,t){var n=!1,r,i;while(r=e.next()){if(r=="-"&&n){t.tokenize=x;break}n=r=="-"}return S("text","strikethrough")}function M(e,t){var n,r,s,o;return e.current()=="<<"?S("brace","macro"):(n=e.next(),n?n==">"&&e.peek()==">"?(e.next(),t.tokenize=x,S("brace","macro")):(e.eatWhile(/[\w\$_]/),s=e.current(),o=i.propertyIsEnumerable(s)&&i[s],o?S(o.type,o.style,s):S("macro",null,s)):(t.tokenize=x,S(n)))}var n=e.indentUnit,r=function(){function e(e){return{type:e,style:"text"}}return{}}(),i=function(){function e(e){return{type:e,style:"macro"}}return{allTags:e("allTags"),closeAll:e("closeAll"),list:e("list"),newJournal:e("newJournal"),newTiddler:e("newTiddler"),permaview:e("permaview"),saveChanges:e("saveChanges"),search:e("search"),slider:e("slider"),tabs:e("tabs"),tag:e("tag"),tagging:e("tagging"),tags:e("tags"),tiddler:e("tiddler"),timeline:e("timeline"),today:e("today"),version:e("version"),option:e("option"),"with":e("with"),filter:e("filter")}}(),s=/[\w_\-]/i,o=/^\-\-\-\-+$/,u=/^\/\*\*\*$/,a=/^\*\*\*\/$/,f=/^<<<$/,l=/^\/\/\{\{\{$/,c=/^\/\/\}\}\}$/,h=/^<!--\{\{\{-->$/,p=/^<!--\}\}\}-->$/,d=/^\{\{\{$/,v=/^\}\}\}$/,m=/\{\{\{/,g=/.*?\}\}\}/,w,E;return{startState:function(e){return{tokenize:x,indented:0,level:0}},token:function(e,t){if(e.eatSpace())return null;var n=t.tokenize(e,t);return n},electricChars:""}}),CodeMirror.defineMIME("text/x-tiddlywiki","tiddlywiki"),CodeMirror.defineMode("tiki",function(e,t){function n(e,t,n){return function(r,s){while(!r.eol()){if(r.match(t)){s.tokenize=i;break}r.next()}return n&&(s.tokenize=n),e}}function r(e,t){return function(t,n){while(!t.eol())t.next();return n.tokenize=i,e}}function i(e,t){function s(n){return t.tokenize=n,n(e,t)}var o=e.sol(),u=e.next();switch(u){case"{":var f=e.eat("/")?"closeTag":"openTag";e.eatSpace();var l="",c;while(c=e.eat(/[^\s\u00a0=\"\'\/?(}]/))l+=c;return t.tokenize=a,"tag";case"_":if(e.eat("_"))return s(n("strong","__",i));break;case"'":if(e.eat("'"))return s(n("em","''",i));break;case"(":if(e.eat("("))return s(n("variable-2","))",i));break;case"[":return s(n("variable-3","]",i));case"|":if(e.eat("|"))return s(n("comment","||"));break;case"-":if(e.eat("="))return s(n("header string","=-",i));if(e.eat("-"))return s(n("error tw-deleted","--",i));break;case"=":if(e.match("=="))return s(n("tw-underline","===",i));break;case":":if(e.eat(":"))return s(n("comment","::"));break;case"^":return s(n("tw-box","^"));case"~":if(e.match("np~"))return s(n("meta","~/np~"))}if(o)switch(u){case"!":return e.match("!!!!!")?s(r("header string")):e.match("!!!!")?s(r("header string")):e.match("!!!")?s(r("header string")):e.match("!!")?s(r("header string")):s(r("header string"));case"*":case"#":case"+":return s(r("tw-listitem bracket"))}return null}function a(e,t){var n=e.next(),r=e.peek();return n=="}"?(t.tokenize=i,"tag"):n=="("||n==")"?"bracket":n=="="?(u="equals",r==">"&&(n=e.next(),r=e.peek()),/[\'\"]/.test(r)||(t.tokenize=l()),"operator"):/[\'\"]/.test(n)?(t.tokenize=f(n),t.tokenize(e,t)):(e.eatWhile(/[^\s\u00a0=\"\'\/?]/),"keyword")}function f(e){return function(t,n){while(!t.eol())if(t.next()==e){n.tokenize=a;break}return"string"}}function l(){return function(e,t){while(!e.eol()){var n=e.next(),r=e.peek();if(n==" "||n==","||/[ )}]/.test(r)){t.tokenize=a;break}}return"string"}}function p(){for(var e=arguments.length-1;e>=0;e--)c.cc.push(arguments[e])}function d(){return p.apply(null,arguments),!0}function v(e,t){var n=c.context&&c.context.noIndent;c.context={prev:c.context,pluginName:e,indent:c.indented,startOfLine:t,noIndent:n}}function m(){c.context&&(c.context=c.context.prev)}function g(e){if(e=="openPlugin")return c.pluginName=o,d(w,y(c.startOfLine));if(e=="closePlugin"){var t=!1;return c.context?(t=c.context.pluginName!=o,m()):t=!0,t&&(h="error"),d(b(t))}return e=="string"?((!c.context||c.context.name!="!cdata")&&v("!cdata"),c.tokenize==i&&m(),d()):d()}function y(e){return function(t){return t=="selfclosePlugin"||t=="endPlugin"?d():t=="endPlugin"?(v(c.pluginName,e),d()):d()}}function b(e){return function(t){return e&&(h="error"),t=="endPlugin"?d():p()}}function w(e){return e=="keyword"?(h="attribute",d(w)):e=="equals"?d(E,w):p()}function E(e){return e=="keyword"?(h="string",d()):e=="string"?d(S):p()}function S(e){return e=="string"?d(S):p()}var s=e.indentUnit,o,u,c,h;return{startState:function(){return{tokenize:i,cc:[],indented:0,startOfLine:!0,pluginName:null,context:null}},token:function(e,t){e.sol()&&(t.startOfLine=!0,t.indented=e.indentation());if(e.eatSpace())return null;h=u=o=null;var n=t.tokenize(e,t);if((n||u)&&n!="comment"){c=t;for(;;){var r=t.cc.pop()||g;if(r(u||n))break}}return t.startOfLine=!1,h||n},indent:function(e,t){var n=e.context;if(n&&n.noIndent)return 0;n&&/^{\//.test(t)&&(n=n.prev);while(n&&!n.startOfLine)n=n.prev;return n?n.indent+s:0},electricChars:"/"}}),CodeMirror.defineMIME("text/tiki","tiki"),CodeMirror.defineMode("vb",function(e,t){function r(e){return new RegExp("^(("+e.join(")|(")+"))\\b","i")}function N(e,t){t.currentIndent++}function C(e,t){t.currentIndent--}function k(e,t){if(e.eatSpace())return null;var r=e.peek();if(r==="'")return e.skipToEnd(),"comment";if(e.match(/^((&H)|(&O))?[0-9\.a-f]/i,!1)){var l=!1;e.match(/^\d*\.\d+F?/i)?l=!0:e.match(/^\d+\.\d*F?/)?l=!0:e.match(/^\.\d+F?/)&&(l=!0);if(l)return e.eat(/J/i),"number";var c=!1;e.match(/^&H[0-9a-f]+/i)?c=!0:e.match(/^&O[0-7]+/i)?c=!0:e.match(/^[1-9]\d*F?/)?(e.eat(/J/i),c=!0):e.match(/^0(?![\dx])/i)&&(c=!0);if(c)return e.eat(/L/i),"number"}return e.match(y)?(t.tokenize=L(e.current()),t.tokenize(e,t)):e.match(a)||e.match(u)?null:e.match(o)||e.match(i)||e.match(p)?"operator":e.match(s)?null:e.match(x)?(N(e,t),t.doInCurrentLine=!0,"keyword"):e.match(b)?(t.doInCurrentLine?t.doInCurrentLine=!1:N(e,t),"keyword"):e.match(w)?"keyword":e.match(S)?(C(e,t),C(e,t),"keyword"):e.match(E)?(C(e,t),"keyword"):e.match(g)?"keyword":e.match(m)?"keyword":e.match(f)?"variable":(e.next(),n)}function L(e){var r=e.length==1,i="string";return function(o,u){while(!o.eol()){o.eatWhile(/[^'"]/);if(o.match(e))return u.tokenize=k,i;o.eat(/['"]/)}if(r){if(t.singleLineStringErrors)return n;u.tokenize=k}return i}}function A(e,t){var r=t.tokenize(e,t),i=e.current();if(i===".")return r=t.tokenize(e,t),i=e.current(),r==="variable"?"variable":n;var s="[({".indexOf(i);return s!==-1&&N(e,t),T==="dedent"&&C(e,t)?n:(s="])}".indexOf(i),s!==-1&&C(e,t)?n:r)}var n="error",i=new RegExp("^[\\+\\-\\*/%&\\\\|\\^~<>!]"),s=new RegExp("^[\\(\\)\\[\\]\\{\\}@,:`=;\\.]"),o=new RegExp("^((==)|(<>)|(<=)|(>=)|(<>)|(<<)|(>>)|(//)|(\\*\\*))"),u=new RegExp("^((\\+=)|(\\-=)|(\\*=)|(%=)|(/=)|(&=)|(\\|=)|(\\^=))"),a=new RegExp("^((//=)|(>>=)|(<<=)|(\\*\\*=))"),f=new RegExp("^[_A-Za-z][_A-Za-z0-9]*"),l=["class","module","sub","enum","select","while","if","function","get","set","property","try"],c=["else","elseif","case","catch"],h=["next","loop"],p=r(["and","or","not","xor","in"]),d=["as","dim","break","continue","optional","then","until","goto","byval","byref","new","handles","property","return","const","private","protected","friend","public","shared","static","true","false"],v=["integer","string","double","decimal","boolean","short","char","float","single"],m=r(d),g=r(v),y='"',b=r(l),w=r(c),E=r(h),S=r(["end"]),x=r(["do"]),T=null,O={electricChars:"dDpPtTfFeE ",startState:function(e){return{tokenize:k,lastToken:null,currentIndent:0,nextLineIndent:0,doInCurrentLine:!1}},token:function(e,t){e.sol()&&(t.currentIndent+=t.nextLineIndent,t.nextLineIndent=0,t.doInCurrentLine=0);var n=A(e,t);return t.lastToken={style:n,content:e.current()},n},indent:function(t,n){var r=n.replace(/^\s+|\s+$/g,"");return r.match(E)||r.match(S)||r.match(w)?e.indentUnit*(t.currentIndent-1):t.currentIndent<0?0:t.currentIndent*e.indentUnit}};return O}),CodeMirror.defineMIME("text/x-vb","vb"),CodeMirror.defineMode("vbscript",function(){var e=/^(?:Call|Case|CDate|Clear|CInt|CLng|Const|CStr|Description|Dim|Do|Each|Else|ElseIf|End|Err|Error|Exit|False|For|Function|If|LCase|Loop|LTrim|Next|Nothing|Now|Number|On|Preserve|Quit|ReDim|Resume|RTrim|Select|Set|Sub|Then|To|Trim|True|UBound|UCase|Until|VbCr|VbCrLf|VbLf|VbTab)$/im;return{token:function(t){if(t.eatSpace())return null;var n=t.next();if(n=="'")return t.skipToEnd(),"comment";if(n=='"')return t.skipTo('"'),"string";if(/\w/.test(n)){t.eatWhile(/\w/);if(e.test(t.current()))return"keyword"}return null}}}),CodeMirror.defineMIME("text/vbscript","vbscript"),CodeMirror.defineMode("velocity",function(e){function t(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function a(e,t,n){return t.tokenize=n,n(e,t)}function f(e,t){var n=t.beforeParams;t.beforeParams=!1;var u=e.next();if(u!='"'&&u!="'"||!t.inParams){if(/[\[\]{}\(\),;\.]/.test(u))return u=="("&&n?t.inParams=!0:u==")"&&(t.inParams=!1),null;if(/\d/.test(u))return e.eatWhile(/[\w\.]/),"number";if(u=="#"&&e.eat("*"))return a(e,t,c);if(u=="#"&&e.match(/ *\[ *\[/))return a(e,t,h);if(u=="#"&&e.eat("#"))return e.skipToEnd(),"comment";if(u=="$")return e.eatWhile(/[\w\d\$_\.{}]/),s&&s.propertyIsEnumerable(e.current().toLowerCase())?"keyword":(t.beforeParams=!0,"builtin");if(o.test(u))return e.eatWhile(o),"operator";e.eatWhile(/[\w\$_{}]/);var f=e.current().toLowerCase();return r&&r.propertyIsEnumerable(f)?"keyword":i&&i.propertyIsEnumerable(f)||e.current().match(/^#[a-z0-9_]+ *$/i)&&e.peek()=="("?(t.beforeParams=!0,"keyword"):null}return a(e,t,l(u))}function l(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}return s&&(n.tokenize=f),"string"}}function c(e,t){var n=!1,r;while(r=e.next()){if(r=="#"&&n){t.tokenize=f;break}n=r=="*"}return"comment"}function h(e,t){var n=0,r;while(r=e.next()){if(r=="#"&&n==2){t.tokenize=f;break}r=="]"?n++:r!=" "&&(n=0)}return"meta"}var n=e.indentUnit,r=t("#end #else #break #stop #[[ #]] #{end} #{else} #{break} #{stop}"),i=t("#if #elseif #foreach #set #include #parse #macro #define #evaluate #{if} #{elseif} #{foreach} #{set} #{include} #{parse} #{macro} #{define} #{evaluate}"),s=t("$foreach.count $foreach.hasNext $foreach.first $foreach.last $foreach.topmost $foreach.parent $velocityCount"),o=/[+\-*&%=<>!?:\/|]/,u=!0;return{startState:function(e){return{tokenize:f,beforeParams:!1,inParams:!1}},token:function(e,t){return e.eatSpace()?null:t.tokenize(e,t)}}}),CodeMirror.defineMIME("text/velocity","velocity"),CodeMirror.defineMode("verilog",function(e,t){function l(e,t){var n=e.next();if(o[n]){var u=o[n](e,t);if(u!==!1)return u}if(n=='"')return t.tokenize=c(n),t.tokenize(e,t);if(/[\[\]{}\(\),;\:\.]/.test(n))return f=n,null;if(/[\d']/.test(n))return e.eatWhile(/[\w\.']/),"number";if(n=="/"){if(e.eat("*"))return t.tokenize=h,h(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(a.test(n))return e.eatWhile(a),"operator";e.eatWhile(/[\w\$_]/);var l=e.current();return r.propertyIsEnumerable(l)?(i.propertyIsEnumerable(l)&&(f="newstatement"),"keyword"):s.propertyIsEnumerable(l)?"atom":"variable"}function c(e){return function(t,n){var r=!1,i,s=!1;while((i=t.next())!=null){if(i==e&&!r){s=!0;break}r=!r&&i=="\\"}if(s||!r&&!u)n.tokenize=l;return"string"}}function h(e,t){var n=!1,r;while(r=e.next()){if(r=="/"&&n){t.tokenize=l;break}n=r=="*"}return"comment"}function p(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function d(e,t,n){return e.context=new p(e.indented,t,n,null,e.context)}function v(e){var t=e.context.type;if(t==")"||t=="]"||t=="}")e.indented=e.context.indented;return e.context=e.context.prev}var n=e.indentUnit,r=t.keywords||{},i=t.blockKeywords||{},s=t.atoms||{},o=t.hooks||{},u=t.multiLineStrings,a=/[&|~><!\)\(*#%@+\/=?\:;}{,\.\^\-\[\]]/,f;return{startState:function(e){return{tokenize:null,context:new p((e||0)-n,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,t){var n=t.context;e.sol()&&(n.align==null&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0);if(e.eatSpace())return null;f=null;var r=(t.tokenize||l)(e,t);if(r=="comment"||r=="meta")return r;n.align==null&&(n.align=!0);if(f!=";"&&f!=":"||n.type!="statement")if(f=="{")d(t,e.column(),"}");else if(f=="[")d(t,e.column(),"]");else if(f=="(")d(t,e.column(),")");else if(f=="}"){while(n.type=="statement")n=v(t);n.type=="}"&&(n=v(t));while(n.type=="statement")n=v(t)}else f==n.type?v(t):(n.type=="}"||n.type=="top"||n.type=="statement"&&f=="newstatement")&&d(t,e.column(),"statement");else v(t);return t.startOfLine=!1,r},indent:function(e,t){if(e.tokenize!=l&&e.tokenize!=null)return 0;var r=t&&t.charAt(0),i=e.context,s=r==i.type;return i.type=="statement"?i.indented+(r=="{"?0:n):i.align?i.column+(s?0:1):i.indented+(s?0:n)},electricChars:"{}"}}),function(){function e(e){var t={},n=e.split(" ");for(var r=0;r<n.length;++r)t[n[r]]=!0;return t}function r(e,t){return e.eatWhile(/[\w\$_]/),"meta"}function i(e,t){var n;while((n=e.next())!=null)if(n=='"'&&!e.eat('"')){t.tokenize=null;break}return"string"}var t="always and assign automatic begin buf bufif0 bufif1 case casex casez cell cmos config deassign default defparam design disable edge else end endcase endconfig endfunction endgenerate endmodule endprimitive endspecify endtable endtask event for force forever fork function generate genvar highz0 highz1 if ifnone incdir include initial inout input instance integer join large liblist library localparam macromodule medium module nand negedge nmos nor noshowcancelled not notif0 notif1 or output parameter pmos posedge primitive pull0 pull1 pulldown pullup pulsestyle_onevent pulsestyle_ondetect rcmos real realtime reg release repeat rnmos rpmos rtran rtranif0 rtranif1 scalared showcancelled signed small specify specparam strong0 strong1 supply0 supply1 table task time tran tranif0 tranif1 tri tri0 tri1 triand trior trireg unsigned use vectored wait wand weak0 weak1 while wire wor xnor xor",n="begin bufif0 bufif1 case casex casez config else end endcase endconfig endfunction endgenerate endmodule endprimitive endspecify endtable endtask for forever function generate if ifnone macromodule module primitive repeat specify table task while";CodeMirror.defineMIME("text/x-verilog",{name:"verilog",keywords:e(t),blockKeywords:e(n),atoms:e("null"),hooks:{"`":r,$:r}})}(),CodeMirror.defineMode("xml",function(e,t){function u(e,t){function n(n){return t.tokenize=n,n(e,t)}var r=e.next();if(r=="<"){if(e.eat("!"))return e.eat("[")?e.match("CDATA[")?n(l("atom","]]>")):null:e.match("--")?n(l("comment","-->")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),n(c(1))):null;if(e.eat("?"))return e.eatWhile(/[\w\._\-]/),t.tokenize=l("meta","?>"),"meta";o=e.eat("/")?"closeTag":"openTag",e.eatSpace(),s="";var i;while(i=e.eat(/[^\s\u00a0=<>\"\'\/?]/))s+=i;return t.tokenize=a,"tag"}if(r=="&"){var u;return e.eat("#")?e.eat("x")?u=e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):u=e.eatWhile(/[\d]/)&&e.eat(";"):u=e.eatWhile(/[\w\.\-:]/)&&e.eat(";"),u?"atom":"error"}return e.eatWhile(/[^&<]/),null}function a(e,t){var n=e.next();return n==">"||n=="/"&&e.eat(">")?(t.tokenize=u,o=n==">"?"endTag":"selfcloseTag","tag"):n=="="?(o="equals",null):/[\'\"]/.test(n)?(t.tokenize=f(n),t.tokenize(e,t)):(e.eatWhile(/[^\s\u00a0=<>\"\'\/?]/),"word")}function f(e){return function(t,n){while(!t.eol())if(t.next()==e){n.tokenize=a;break}return"string"}}function l(e,t){return function(n,r){while(!n.eol()){if(n.match(t)){r.tokenize=u;break}n.next()}return e}}function c(e){return function(t,n){var r;while((r=t.next())!=null){if(r=="<")return n.tokenize=c(e+1),n.tokenize(t,n);if(r==">"){if(e==1){n.tokenize=u;break}return n.tokenize=c(e-1),n.tokenize(t,n)}}return"meta"}}function d(){for(var e=arguments.length-1;e>=0;e--)h.cc.push(arguments[e])}function v(){return d.apply(null,arguments),!0}function m(e,t){var n=r.doNotIndent.hasOwnProperty(e)||h.context&&h.context.noIndent;h.context={prev:h.context,tagName:e,indent:h.indented,startOfLine:t,noIndent:n}}function g(){h.context&&(h.context=h.context.prev)}function y(e){if(e=="openTag")return h.tagName=s,v(S,b(h.startOfLine));if(e=="closeTag"){var t=!1;return h.context?h.context.tagName!=s&&(r.implicitlyClosed.hasOwnProperty(h.context.tagName.toLowerCase())&&g(),t=!h.context||h.context.tagName!=s):t=!0,t&&(p="error"),v(w(t))}return v()}function b(e){return function(t){return t=="selfcloseTag"||t=="endTag"&&r.autoSelfClosers.hasOwnProperty(h.tagName.toLowerCase())?(E(h.tagName.toLowerCase()),v()):t=="endTag"?(E(h.tagName.toLowerCase()),m(h.tagName,e),v()):v()}}function w(e){return function(t){return e&&(p="error"),t=="endTag"?(g(),v()):(p="error",v(arguments.callee))}}function E(e){var t;for(;;){if(!h.context)return;t=h.context.tagName.toLowerCase();if(!r.contextGrabbers.hasOwnProperty(t)||!r.contextGrabbers[t].hasOwnProperty(e))return;g()}}function S(e){return e=="word"?(p="attribute",v(x,S)):e=="endTag"||e=="selfcloseTag"?d():(p="error",v(S))}function x(e){return e=="equals"?v(T,S):(r.allowMissing||(p="error"),e=="endTag"||e=="selfcloseTag"?d():v())}function T(e){return e=="string"?v(N):e=="word"&&r.allowUnquoted?(p="string",v()):(p="error",e=="endTag"||e=="selfCloseTag"?d():v())}function N(e){return e=="string"?v(N):d()}var n=e.indentUnit,r=t.htmlMode?{autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0}:{autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1},i=t.alignCDATA,s,o,h,p;return{startState:function(){return{tokenize:u,cc:[],indented:0,startOfLine:!0,tagName:null,context:null}},token:function(e,t){e.sol()&&(t.startOfLine=!0,t.indented=e.indentation());if(e.eatSpace())return null;p=o=s=null;var n=t.tokenize(e,t);t.type=o;if((n||o)&&n!="comment"){h=t;for(;;){var r=t.cc.pop()||y;if(r(o||n))break}}return t.startOfLine=!1,p||n},indent:function(e,t,r){var s=e.context;if(e.tokenize!=a&&e.tokenize!=u||s&&s.noIndent)return r?r.match(/^(\s*)/)[0].length:0;if(i&&/<!\[CDATA\[/.test(t))return 0;s&&/^<\//.test(t)&&(s=s.prev);while(s&&!s.startOfLine)s=s.prev;return s?s.indent+n:0},electricChars:"/"}}),CodeMirror.defineMIME("text/xml","xml"),CodeMirror.defineMIME("application/xml","xml"),CodeMirror.mimeModes.hasOwnProperty("text/html")||CodeMirror.defineMIME("text/html",{name:"xml",htmlMode:!0}),CodeMirror.defineMode("xquery",function(e,t){function s(e,t,n){return r=e,i=n,t}function o(e,t,n){return t.tokenize=n,n(e,t)}function u(e,t){var r=e.next(),i=!1,u=E(e);if(r=="<"){if(e.match("!--",!0))return o(e,t,p);if(e.match("![CDATA",!1))return t.tokenize=d,s("tag","tag");if(e.match("?",!1))return o(e,t,v);var h=e.eat("/");e.eatSpace();var g="",y;while(y=e.eat(/[^\s\u00a0=<>\"\'\/?]/))g+=y;return o(e,t,c(g,h))}if(r=="{")return x(t,{type:"codeblock"}),s("","");if(r=="}")return T(t),s("","");if(m(t))return r==">"?s("tag","tag"):r=="/"&&e.eat(">")?(T(t),s("tag","tag")):s("word","variable");if(/\d/.test(r))return e.match(/^\d*(?:\.\d*)?(?:E[+\-]?\d+)?/),s("number","atom");if(r==="("&&e.eat(":"))return x(t,{type:"comment"}),o(e,t,a);if(!!u||r!=='"'&&r!=="'"){if(r==="$")return o(e,t,l);if(r===":"&&e.eat("="))return s("operator","keyword");if(r==="(")return x(t,{type:"paren"}),s("","");if(r===")")return T(t),s("","");if(r==="[")return x(t,{type:"bracket"}),s("","");if(r==="]")return T(t),s("","");var w=n.propertyIsEnumerable(r)&&n[r];if(u&&r==='"')while(e.next()!=='"');if(u&&r==="'")while(e.next()!=="'");w||e.eatWhile(/[\w\$_-]/);var S=e.eat(":");!e.eat(":")&&S&&e.eatWhile(/[\w\$_-]/),e.match(/^[ \t]*\(/,!1)&&(i=!0);var N=e.current();return w=n.propertyIsEnumerable(N)&&n[N],i&&!w&&(w={type:"function_call",style:"variable def"}),b(t)?(T(t),s("word","variable",N)):((N=="element"||N=="attribute"||w.type=="axis_specifier")&&x(t,{type:"xmlconstructor"}),w?s(w.type,w.style,N):s("word","variable",N))}return o(e,t,f(r))}function a(e,t){var n=!1,r=!1,i=0,o;while(o=e.next()){if(o==")"&&n){if(!(i>0)){T(t);break}i--}else o==":"&&r&&i++;n=o==":",r=o=="("}return s("comment","comment")}function f(e,t){return function(n,r){var i;if(w(r)&&n.current()==e)return T(r),t&&(r.tokenize=t),s("string","string");x(r,{type:"string",name:e,tokenize:f(e,t)});if(n.match("{",!1)&&g(r))return r.tokenize=u,s("string","string");while(i=n.next()){if(i==e){T(r),t&&(r.tokenize=t);break}if(n.match("{",!1)&&g(r))return r.tokenize=u,s("string","string")}return s("string","string")}}function l(e,t){var n=/[\w\$_-]/;if(e.eat('"')){while(e.next()!=='"');e.eat(":")}else e.eatWhile(n),e.match(":=",!1)||e.eat(":");return e.eatWhile(n),t.tokenize=u,s("variable","variable")}function c(e,t){return function(n,r){return n.eatSpace(),t&&n.eat(">")?(T(r),r.tokenize=u,s("tag","tag")):(n.eat("/")||x(r,{type:"tag",name:e,tokenize:u}),n.eat(">")?(r.tokenize=u,s("tag","tag")):(r.tokenize=h,s("tag","tag")))}}function h(e,t){var n=e.next();if(n=="/"&&e.eat(">"))return g(t)&&T(t),m(t)&&T(t),s("tag","tag");if(n==">")return g(t)&&T(t),s("tag","tag");if(n=="=")return s("","");if(n=='"'||n=="'")return o(e,t,f(n,h));g(t)||x(t,{type:"attribute",name:name,tokenize:h}),e.eat(/[a-zA-Z_:]/),e.eatWhile(/[-a-zA-Z0-9_:.]/),e.eatSpace();if(e.match(">",!1)||e.match("/",!1))T(t),t.tokenize=u;return s("attribute","attribute")}function p(e,t){var n;while(n=e.next())if(n=="-"&&e.match("->",!0))return t.tokenize=u,s("comment","comment")}function d(e,t){var n;while(n=e.next())if(n=="]"&&e.match("]",!0))return t.tokenize=u,s("comment","comment")}function v(e,t){var n;while(n=e.next())if(n=="?"&&e.match(">",!0))return t.tokenize=u,s("comment","comment meta")}function m(e){return S(e,"tag")}function g(e){return S(e,"attribute")}function y(e){return S(e,"codeblock")}function b(e){return S(e,"xmlconstructor")}function w(e){return S(e,"string")}function E(e){return e.current()==='"'?e.match(/^[^\"]+\"\:/,!1):e.current()==="'"?e.match(/^[^\"]+\'\:/,!1):!1}function S(e,t){return e.stack.length&&e.stack[e.stack.length-1].type==t}function x(e,t){e.stack.push(t)}function T(e){var t=e.stack.pop(),n=e.stack.length&&e.stack[e.stack.length-1].tokenize;e.tokenize=n||u}var n=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),n=e("keyword b"),r=e("keyword c"),i=e("operator"),s={type:"atom",style:"atom"},o={type:"punctuation",style:""},u={type:"axis_specifier",style:"qualifier"},a={"if":t,"switch":t,"while":t,"for":t,"else":n,then:n,"try":n,"finally":n,"catch":n,element:r,attribute:r,let:r,"implements":r,"import":r,module:r,namespace:r,"return":r,"super":r,"this":r,"throws":r,where:r,"private":r,",":o,"null":s,"fn:false()":s,"fn:true()":s},f=["after","ancestor","ancestor-or-self","and","as","ascending","assert","attribute","before","by","case","cast","child","comment","declare","default","define","descendant","descendant-or-self","descending","document","document-node","element","else","eq","every","except","external","following","following-sibling","follows","for","function","if","import","in","instance","intersect","item","let","module","namespace","node","node","of","only","or","order","parent","precedes","preceding","preceding-sibling","processing-instruction","ref","return","returns","satisfies","schema","schema-element","self","some","sortby","stable","text","then","to","treat","typeswitch","union","variable","version","where","xquery","empty-sequence"];for(var l=0,c=f.length;l<c;l++)a[f[l]]=e(f[l]);var h=["xs:string","xs:float","xs:decimal","xs:double","xs:integer","xs:boolean","xs:date","xs:dateTime","xs:time","xs:duration","xs:dayTimeDuration","xs:time","xs:yearMonthDuration","numeric","xs:hexBinary","xs:base64Binary","xs:anyURI","xs:QName","xs:byte","xs:boolean","xs:anyURI","xf:yearMonthDuration"];for(var l=0,c=h.length;l<c;l++)a[h[l]]=s;var p=["eq","ne","lt","le","gt","ge",":=","=",">",">=","<","<=",".","|","?","and","or","div","idiv","mod","*","/","+","-"];for(var l=0,c=p.length;l<c;l++)a[p[l]]=i;var d=["self::","attribute::","child::","descendant::","descendant-or-self::","parent::","ancestor::","ancestor-or-self::","following::","preceding::","following-sibling::","preceding-sibling::"];for(var l=0,c=d.length;l<c;l++)a[d[l]]=u;return a}(),r,i;return{startState:function(e){return{tokenize:u,cc:[],stack:[]}},token:function(e,t){if(e.eatSpace())return null;var n=t.tokenize(e,t);return n}}}),CodeMirror.defineMIME("application/xquery","xquery"),CodeMirror.defineMode("yaml",function(){var e=["true","false","on","off","yes","no"],t=new RegExp("\\b(("+e.join(")|(")+"))$","i");return{token:function(e,n){var r=e.peek(),i=n.escaped;n.escaped=!1;if(r=="#")return e.skipToEnd(),"comment";if(n.literal&&e.indentation()>n.keyCol)return e.skipToEnd(),"string";n.literal&&(n.literal=!1);if(e.sol()){n.keyCol=0,n.pair=!1,n.pairStart=!1;if(e.match(/---/))return"def";if(e.match(/\.\.\./))return"def";if(e.match(/\s*-\s+/))return"meta"}if(!n.pair&&e.match(/^\s*([a-z0-9\._-])+(?=\s*:)/i))return n.pair=!0,n.keyCol=e.indentation(),"atom";if(n.pair&&e.match(/^:\s*/))return n.pairStart=!0,"meta";if(e.match(/^(\{|\}|\[|\])/))return r=="{"?n.inlinePairs++:r=="}"?n.inlinePairs--:r=="["?n.inlineList++:n.inlineList--,"meta";if(n.inlineList>0&&!i&&r==",")return e.next(),"meta";if(n.inlinePairs>0&&!i&&r==",")return n.keyCol=0,n.pair=!1,n.pairStart=!1,e.next(),"meta";if(n.pairStart){if(e.match(/^\s*(\||\>)\s*/))return n.literal=!0,"meta";if(e.match(/^\s*(\&|\*)[a-z0-9\._-]+\b/i))return"variable-2";if(n.inlinePairs==0&&e.match(/^\s*-?[0-9\.\,]+\s?$/))return"number";if(n.inlinePairs>0&&e.match(/^\s*-?[0-9\.\,]+\s?(?=(,|}))/))return"number";if(e.match(t))return"keyword"}return n.pairStart=!1,n.escaped=r=="\\",e.next(),null},startState:function(){return{pair:!1,pairStart:!1,keyCol:0,inlinePairs:0,inlineList:0,literal:!1,escaped:!1}}}}),CodeMirror.defineMIME("text/x-yaml","yaml")