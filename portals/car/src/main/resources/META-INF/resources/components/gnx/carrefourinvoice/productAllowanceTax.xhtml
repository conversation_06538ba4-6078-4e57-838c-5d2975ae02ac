<p:outputPanel xmlns="http://www.w3.org/1999/xhtml"
               xmlns:f="http://xmlns.jcp.org/jsf/core"
               xmlns:h="http://xmlns.jcp.org/jsf/html"
               xmlns:pe="http://primefaces.org/ui/extensions"
               xmlns:p="http://primefaces.org/ui"
               xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
               styleClass="psAllChrg">
    <ui:param name="section" value="#{cc.attrs.value.wizard.currentSection}" />
    <p:outputPanel>
        <p:outputPanel styleClass="form-group col-sm-12" style="padding-left:0px">
            <p:selectOneMenu id="refAllowOrChrgLine"
                             value="#{section.currentTax}"
                             converter="#{section.taxConverter}"
                             disabled="#{section.allowOrChrgLineEdit>0}"
                             styleClass="selectonemenu-resize psSctTax">
                <p:ajax event="change" listener="#{cc.attrs.value.wizard.currentSection.onSelectAllowOrChargeLine()}"
                        process="@this @(.psAllChrg)" update="@this @(.psAllChrg)" global="false"
                        oncomplete="$('[id$=allowOrChargeDlg] .ui-datatable-data tr').last().find('span.ui-icon-pencil').each(function() {$(this).click();});"/>
                <f:selectItem itemLabel="#{resourceBundle.add_tax}"/>
                <f:selectItems value="#{section.listOfTaxLines}"
                               var="tax" itemValue="#{tax}" itemLabel="#{tax.allowanceOrChargeDescription.listOfDescription.value}"/>
            </p:selectOneMenu>
        </p:outputPanel>
    </p:outputPanel>
    <p:remoteCommand name="rc" update="@(.psActions)" process="@this" global="false"/>
    
    <p:dataTable id="allowOrChargeDlg"
                 value="#{section.name == 'InvoiceLinesSectionEdition'
                            ? section.item.invoicePricingDetail.itemAllowancesOrCharges.allowOrCharge
                            : null}"
                 var="alwcChrg"
                 emptyMessage="#{labels.no_records_found}"
                 editable="true"
                 resizableColumns="true"
                 styleClass="psAllowOrChargeDlg">
        <p:ajax event="rowEdit" listener="#{cc.attrs.value.wizard.currentSection.onRowEditAllOrChrgLine}"
                oncomplete=" if(!args.validationFailed) {rc()}" process="@this" update="@(.psAllowOrChargeDlg) @(.psSctTax) @(.addAllowBtn) @(.iptNetPrice) @(.psBtnSave) @(.psBtnCancel)" global="false"/>
        <p:ajax event="rowEditCancel" listener="#{cc.attrs.value.wizard.currentSection.onRowCancelLine}"
                oncomplete=" if(!args.validationFailed) {rc()}" process="@this" update="@(.psAllowOrChargeDlg) @(.psActions) @(.psSctTax) @(.addAllowBtn) @(.iptNetPrice) @(.psBtnSave) @(.psBtnCancel)" global="false"/>
        <p:ajax event="rowEditInit" listener="#{cc.attrs.value.wizard.currentSection.onRowEditInitAllOrChrgLine}"
                oncomplete="rc()" process="@this" update="@(.psSctTax) @(.addAllowBtn) @(.iptNetPrice) @(.psBtnSave) @(.psBtnCancel)" global="false"/>
		<p:column styleClass="center actions-column" exportable="false">
		    <f:facet name="header">
		  	  <p:outputPanel styleClass="psActions">
	            <p:commandButton id="actions"
				                title="#{labels.actions}"
				                icon="fa fa-ellipsis-h"
				                type="button"
				                styleClass="globalActionsButton"/>            
	            <p:tieredMenu styleClass="xcbltask-tieredmenu global-actions"  overlay="true" trigger="actions" my="left top" at="left bottom">
		           <p:menuitem	 
		                value="#{resourceBundle.add_allowance}"
						disabled="#{section.allowOrChrgLineEdit>0}"
						actionListener="#{section.onSelectAllowOrChargeLine()}"
						process="@this @(.psAllChrg) @(.addAllowBtn) @(.xcbltask-tieredmenu)"
						update="@(.psAllChrg)"
						styleClass="reset-btn btn-grey addAllowBtn"
						oncomplete="$('[id$=allowOrChargeDlg] .ui-datatable-data tr').last().find('span.ui-icon-pencil').each(function() {$(this).click();}); rc()"
						onstart="jQuery('.global-actions').hide();"/>
							        	
	             </p:tieredMenu>
		       </p:outputPanel>
	          </f:facet>
	           <p:rowEditor style="float:left;"/>
	            <p:commandButton actionListener="#{section.onRemoveLineAllowOrCharge(alwcChrg)}"
                             process="@this"
                             update="@(.psAllowOrChargeDlg) @(.psSctTax) @(.addAllowBtn) @(.iptNetPrice) @(.psBtnSave) @(.psBtnCancel)"
                             global="false"
                             title="#{resourceBundle.alloworchrg_remove}"
                             icon="ui-icon-trash"
                             styleClass="jqsRmvBtn"
                             style="float:left;border:none;background-color:transparent;height:16px;width:16px;"/> 
        </p:column> 
        <p:column headerText="#{resourceBundle.tax_code}" width="15%">
            <p:cellEditor>
                <f:facet name="output">
                    <h:outputText
                            value="#{alwcChrg.indicatorCoded == 'LINE_ITEM_ALLOWANCE' ? '' : alwcChrg.allowanceOrChargeDescription.refID}"/>
                </f:facet>
                <f:facet name="input">
                    <p:inputText value="#{alwcChrg.allowanceOrChargeDescription.refID}"
                                 validator="xssValidator"
                                 required="true"
                                 requiredMessage="#{resourceBundle.tax_code} : #{gnxxcblcomlbls.value_required}"
                                 disabled="#{alwcChrg.indicatorCoded == 'LINE_ITEM_ALLOWANCE' or section.isCodeEANDisabled()}"
                                 style="width: 100%"/>
                </f:facet>
            </p:cellEditor>
        </p:column>
        <p:column headerText="#{gnxxcblinvlbls.description}" width="35%">
            <p:cellEditor>
                <f:facet name="output">
                    <h:outputText value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}"/>
                </f:facet>
                <f:facet name="input">
                    <p:inputText rendered="#{alwcChrg.indicatorCoded == 'LINE_ITEM_ALLOWANCE'}"
                                 value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}"
                                 validator="xssValidator"
                                 required="true"
                                 requiredMessage="#{gnxxcblinvlbls.description} : #{gnxxcblcomlbls.value_required}"
                                 styleClass="text-center"
                                 style="width: 100%"/>
                    <h:outputText rendered="#{alwcChrg.indicatorCoded != 'LINE_ITEM_ALLOWANCE'}"
                                  value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}"/>
                </f:facet>
            </p:cellEditor>
        </p:column>
        <p:column headerText="#{resourceBundle.type}" width="15%" >
            <h:outputText value="#{gnxxcblinvlbls[alwcChrg.indicatorCoded]}"/>
        </p:column>
        <p:column headerText="#{resourceBundle.percent}" width="10%" >
            <p:cellEditor id="alwcChrgPercentId">
                <f:facet name="output">
                    <h:outputText
                            value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}%"/>
                </f:facet>
                <f:facet name="input">
                    <pe:inputNumber
                            value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}"
                            required="true"
                            requiredMessage="#{resourceBundle.percent} : #{gnxxcblcomlbls.value_required}"
                            disabled="#{section.isPercentageOrAmountDisabled()}"
                            minValue="0"
                            maxValue="100.00"
                            symbol="%"
                            symbolPosition="suffix"
                            styleClass="input-number-resize">
                        <p:ajax event="change" listener="#{cc.attrs.value.wizard.currentSection.onPercentageModified(alwcChrg)}"
                                process="@this" update="alwcChrgAmountId" global="false"/>
                    </pe:inputNumber>
                    <h:outputText value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}%"
                                  rendered="#{not section.isPercentageEditable(alwcChrg.indicatorCoded)}"/>
                </f:facet>
            </p:cellEditor>
        </p:column>
        <p:column headerText="#{gnxxcblinvlbls.monetary_amount}" width="15%">
            <p:cellEditor id="alwcChrgAmountId">
                <f:facet name="output">
                    <h:outputText value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount.value}"
                                  styleClass="text-right">
                        <f:converter converterId="gnxBigDecimalConverter"/>
                        <f:attribute name="scale" value="#{section.detailPrecision}"/>
                    </h:outputText>
                </f:facet>
                <f:facet name="input">
                    <pe:inputNumber value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount.value}"
                                    required="true"
                                    requiredMessage="#{gnxxcblinvlbls.monetary_amount} : #{gnxxcblcomlbls.value_required}"
                                    disabled="#{alwcChrg.indicatorCoded == 'LINE_ITEM_ALLOWANCE' and section.isPercentageOrAmountDisabled()}"
                                    decimalPlaces="#{section.detailPrecision}"
                                    styleClass="input-number-resize">
                        <p:ajax event="change" listener="#{cc.attrs.value.wizard.currentSection.onMonetaryAmmountModified(alwcChrg)}"
                                process="@this" update="alwcChrgPercentId" global="false"/>
                    </pe:inputNumber>
                </f:facet>
            </p:cellEditor>
        </p:column>
    </p:dataTable>
    <p:messages globalOnly="true" autoUpdate="true"/>
</p:outputPanel>