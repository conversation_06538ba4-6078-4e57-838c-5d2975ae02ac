package com.byzaneo.generix.specifics.portal.task;

import com.byzaneo.amazon.service.*;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.ApplicationHandler;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.faces.event.*;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.generix.archivage.service.ArchivedInvoiceService;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.cloud.bean.CloudProcessReport;
import com.byzaneo.generix.cloud.bean.CloudProcessReport.Status;
import com.byzaneo.generix.edocument.SafeboxException;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.exception.ArchiveDownloadFailedException;
import com.byzaneo.generix.edocument.service.IndexDataService;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.xtrade.task.AbstractIndexableDocumentTask;
import com.byzaneo.generix.xtrade.util.ColumnValue;
import com.byzaneo.query.Query;
import com.byzaneo.query.Query.ContextKey;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.mongo.MongoIndexableDocument;
import com.byzaneo.xtrade.ui.AbstractIndexableDataModel;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.model.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import java.io.*;
import java.nio.file.FileSystems;
import java.time.Instant;
import java.util.*;

import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.cloud.bean.CloudProcessReport.ProcessType.EXPORT_JOB;
import static com.byzaneo.query.builder.Clauses.ilike;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.task.util.TaskHelper.error;
import static com.byzaneo.task.util.TaskHelper.info;
import static java.lang.System.currentTimeMillis;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.primefaces.model.SortOrder.ASCENDING;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;

@Task(name = "CarrefourArchivedFax", library = "portal/specifics", version = "1.0.0")
@TaskViewModel(labelFamily = "crfaxtsklbls")
public class CarrefourArchivedFaxTask extends AbstractIndexableDocumentTask<CarrefourArchivedFaxes> {

  private static final String DESCRIPTOR_PATH = "descriptors/carrefourfax-archive.xml";
  private static final long serialVersionUID = 4016038533182882132L;
  private static final Logger log = getLogger(CarrefourArchivedFaxTask.class);
  private static final String typeMongoField = "_type";
  private static final String faxTypeMongoValue = "FAX";
  private static final String indexablePrefix = "indexable.";

  protected transient ArchivedInvoiceService archivedInvoiceService;

  @Autowired
  @Qualifier(IndexDataService.SERVICE_NAME)
  private transient IndexDataService indexDataService;

  @Autowired
  @Qualifier(S3ResolverService.SERVICE_NAME)
  private transient S3ResolverService s3ResolverService;

  @PostConstruct
  public void init() {
    if (this.archivedInvoiceService == null) {
      this.archivedInvoiceService = getBean(ArchivedInvoiceService.class, ArchivedInvoiceService.SERVICE_NAME);
    }
  }

  @Override
  protected LazyDataModel<CarrefourArchivedFaxes> resolveDataModel() {
    return new CarrefourArchivedFaxDataModel(resolveSearchQuery());
  }

  @Override
  public void setBql(String bql) {
    StringBuilder newBql = new StringBuilder();
    if (isNotBlank(bql))
      newBql.append(bql);
    if (bql != null && bql.contains("_text_")) {
      String val = bql.replace("\"_text_\" ~ ", "");
      List<String> matches = this.getDescriptor()
          .getProperties()
          .stream()
          .filter(p -> p.getType() == String.class)
          .map(p -> p.getName() + " ~ " + val)
          .collect(toList());
      newBql = new StringBuilder();
      for (int i = 0; i < matches.size(); i++) {
        newBql.append(matches.get(i));
        if (i < matches.size() - 1)
          newBql.append(" OR ");
      }
    }
    super.setBql(StringUtils.replaceEach(newBql.toString(), new String[] { "[\'", "\']", "[", "]" },
        new String[] { ".", "", ".", "" }));
  }

  @Override
  protected String getQueryFiedOwners() {
    return "_owner";
  }

  @Override
  protected String getQueryFiedFrom() {
    return "_from";
  }

  @Override
  protected String getQueryFiedTo() {
    return "_to";
  }

  public Collection<String> onAutocompleteStart(final CompleteEvent event) {

    String fieldName = event.getFieldName()
        .replaceAll("\"", "");

    fieldName = StringUtils.replaceEach(fieldName,
        new String[] { "from", "to", "owners", "archiveUid", "safeBox", "reference", "type" },
        new String[] { "_from", "_to", "_owner", "archive_uid", "safe_box", "_ref", "_type" });

    List<AggregationOperation> operations = createGroupPipeline(fieldName, 10);
    List<DBObject> results = this.indexDataService.aggregate(
        "archives",
        DBObject.class,
        createBuilder(resolveBaseQuery())
            .and(ilike(fieldName, event.getFieldValue()))
            .query(),
        operations.toArray(new AggregationOperation[0]));

    return results.stream()
        .map(dbo -> dbo.get("_id"))
        .filter(Objects::nonNull)
        .map(Object::toString)
        .collect(toList());

  }

  @Override
  protected Query resolveBaseQuery() {
    QueryBuilder qb = QueryBuilder.createBuilder(super.resolveBaseQuery());
    qb.and(Clauses.equal(typeMongoField, faxTypeMongoValue));
    qb.and(Clauses.equal(ArchivedIndexable.ENTITY_CLASS, "com.byzaneo.xtrade.dao.mongo.MongoIndexableDocument"));
    return qb.query();
  }

  @Override
  public QueryModel getSearchModel() {
    return super.getSearchModelAdvancedSearch();
  }

  @Override
  public long onCountAdvancedSearch(final CountEvent event) {
    QueryBuilder qb = createBuilder(resolveBaseQuery());
    qb.append(StringUtils.replaceEach(event.getBql(), new String[] { "[\'", "\']", "[", "]" },
        new String[] { ".", "", ".", "" }));
    return eDocumentService.countArchiveDocuments(qb.query());
  }

  @Override
  public Map<Object, String> getValues(String label) {
    if ("status".equals(label)) {
      List<String> availablesStatuses = this.indexDataService.aggregate("archives", DBObject.class, resolveBaseQuery(), group("_state"))
          .stream()
          .map(dbo -> dbo.get("_id"))
          .filter(Objects::nonNull)
          .map(Object::toString)
          .collect(toList());
      Map<Object, String> map = new HashMap<Object, String>();

      Iterator<String> it = availablesStatuses.iterator();
      while (it.hasNext()) {
        Object object = it.next();
        map.put(object, label(object.toString()));
      }
      return map;
    }
    return super.getValues(label);
  }

  public void showDocumentFiles(Indexable indexable) {
    CarrefourArchivedFaxes archive = (CarrefourArchivedFaxes) indexable;
    getDocumentFilesFromArchive(archive);
  }

  @Override
  public void onSelect(Instance instance, CarrefourArchivedFaxes indexable, PropertyDescriptor property) {
    viewArchivedFax(instance, indexable);
  }

  public void onPrintIndexableFaxFromArchive(Instance instance, Indexable indexable) {
    CarrefourArchivedFaxes archive = (CarrefourArchivedFaxes) indexable;
    viewArchivedFax(instance, archive);
  }

  public void onBulkExport(Instance instance) {

    User user = getCurrentUser();

    if (archivedInvoiceService.existProcessInProgress(user)) {
      error("archtsklbls.processAlreadyInProgress");
      log.error("There is already a bulk process in progress for user '{}'", user.getLogin());
      return;
    }

    Query searchQuery = resolveSearchQuery();
    List<String> filePaths = eDocumentService.searchArchivesUid(CarrefourArchivedFaxes.class, searchQuery);

    // create a new process report
    CloudProcessReport processReport = new CloudProcessReport();
    processReport.setProcessType(EXPORT_JOB);
    processReport.setStart(Date.from(Instant.now()));
    processReport.setStatus(Status.IN_PROGRESS);
    processReport.setUser(user);
    processReport.setInstance(instance);
    processReport.setFilter(QueryHelper.toBql(searchQuery));
    processReport.setDescriptorPath(DESCRIPTOR_PATH);

    boolean isOK = archivedInvoiceService.batchOperation(instance, user, filePaths, processReport);
    if (isOK) {
      info(this, "archtsklbls.bulkExportInfoMessage");
    }
  }

  public boolean canRenderBulkExport() {
    S3Service s3 = this.s3ResolverService.initS3Service(company);
    if (s3 == null || s3 instanceof GenerixS3ServiceImpl) {
      return false;
    }
    else if (archivedInvoiceService != null && s3 instanceof AmazonS3ServiceImpl) {
      return true;
    }
    return false;
  }

  private void viewArchivedFax(Instance instance, CarrefourArchivedFaxes selectedArchivedFax) {
    try {
      safeboxService.getArchiveFromS3(selectedArchivedFax, this.getCompany());
      this.viewer = getViewerArchivedFax(instance, selectedArchivedFax.getIndexable(), TransformService.BirtOutoutFileType.HTML)
          .orElse(null);
    }
    catch (IOException | ArchiveDownloadFailedException e) {
      log.error(e.getMessage());
      TaskHelper.error(this, e, "Failed to retrieve archive : %s, Please contact your administrator", e.getMessage());
      getCurrentInstance().addCallbackParam("validationFailed", true);
    }
    catch (SafeboxException e) {
      log.error("Failed to retrieve archive", e);
      getCurrentInstance().addCallbackParam("validationFailed", true);
      this.facesContext = FacesContext.getCurrentInstance();
      FacesMessage fm = MessageHelper.toFacesMessage(e.getMessage(), FacesMessage.SEVERITY_ERROR);
      this.facesContext.addMessage(null, fm);
    }
    this.selected = null;
    this.document = null;
  }

  protected class CarrefourArchivedFaxDataModel extends AbstractIndexableDataModel<CarrefourArchivedFaxes> {

    /**
     * 
     */
    private static final long serialVersionUID = -1059710936895432080L;

    private transient String sortField;
    private transient SortOrder sortOrder;

    public CarrefourArchivedFaxDataModel(Query query) {
      super(documentService, query, getSearch(), CarrefourArchivedFaxes.class);
    }

    @Override
    public Page<CarrefourArchivedFaxes> load(PageRequest pageable, Map<String, Object> filters) {
      query.getContext()
          .put(ContextKey.PARTNER_SCOPE, getUserCodes());
      Page<CarrefourArchivedFaxes> archivedDocuments = eDocumentService.searchArchives(CarrefourArchivedFaxes.class, query, pageable);
      return archivedDocuments;
    }

    @Override
    public List<CarrefourArchivedFaxes> load(int first, int pageSize, String sortField, SortOrder sortOrder,
        Map<String, Object> filters) {
      this.sortField = sortField;
      this.sortOrder = sortOrder;
      return super.load(first, pageSize, sortField, sortOrder, filters);
    }

    public String getSortField() {
      return sortField;
    }

    public SortOrder getSortOrder() {
      return sortOrder;
    }

  }

  @Override
  protected BeanDescriptor createDescriptor() {
    try {
      BeanDescriptor bd = beanService.fromClasspath(DESCRIPTOR_PATH);
      return bd;
    }
    catch (IOException ex) {
      error(this, ex, "{0}", getRootCauseMessage(ex));
      log.error("Failed to create beandescriptor", ex);
      return null;
    }
  }

  @Override
  public ColumnValue getColumnValue(CarrefourArchivedFaxes bean, PropertyDescriptor property) {
    final ColumnValue cell = new ColumnValue();
    if (bean == null || property == null || !this.isRendered((CarrefourArchivedFaxes) bean, property))
      return cell;
    final MongoIndexableDocument indexable = ((CarrefourArchivedFaxes) bean).getIndexable();
    if (this.containsValue(property)) {
      Object beanToDisplay = property.getName()
          .startsWith(indexablePrefix) ? indexable : bean;
      PropertyDescriptor propertyForIndexableDocument = new PropertyDescriptor(property.getName()
          .startsWith(indexablePrefix)
              ? property.getName()
                  .substring(indexablePrefix.length())
              : property.getName());
      propertyForIndexableDocument.setAlias(property.getAlias());
      propertyForIndexableDocument.setDefaultValue(property.getDefaultValue());
      propertyForIndexableDocument.setType(property.getType());

      // - value -
      if (property.isDate()) {
        cell.setValue(property.getName()
            .startsWith(indexablePrefix) ? propertyForIndexableDocument.getValue(beanToDisplay, null)
                : property.getValue(beanToDisplay, null));
      }
      else {
        final String value = property.getName()
            .startsWith(indexablePrefix) ? propertyForIndexableDocument.getDisplayValue(beanToDisplay)
                : property.getDisplayValue(beanToDisplay);
        cell.setValue(isMultipleValues(property)
            ? this.label(value)
            : value);
      }
      // - style -
      cell.setStyle("");
    }
    else {
      cell.setValue(this.getLabelStatus(indexable, property));
      cell.setStyle(this.getBootstrapStyleClass(indexable, property));
    }
    // - converter -
    if (property.isDate())
      cell.setConverter(this.currentSession()
          .getDateTimeConverter());
    else cell.setConverter(getManagedBean(ApplicationHandler.class, ApplicationHandler.MANAGED_BEAN_NAME)
        .getConverter(property.getConverter()));
    return cell;
  }

  @Override
  protected File downloadFile(FileType fileType, Instance instance) {
    int archivedDocumentItemsCount = Long.valueOf(eDocumentService.countArchiveDocuments(resolveSearchQuery()))
        .intValue();
    if (archivedDocumentItemsCount == 0) {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      this.facesContext = FacesContext.getCurrentInstance();
      FacesMessage fm = MessageHelper.toFacesMessage("edctsklbls.ddl_no_file_warning_message", FacesMessage.SEVERITY_WARN);
      this.facesContext.addMessage(null, fm);
      return null;
    }
    int asynchronousLimit = (int) getAsynchronousProcessLimit();
    String archiveFileName = "archive-carrefour-fax" + currentTimeMillis();
    String sortField = ((CarrefourArchivedFaxDataModel) this.dataModel).getSortField();
    Direction sortOrder = ASCENDING.equals(((CarrefourArchivedFaxDataModel) this.dataModel).getSortOrder()) ? ASC : DESC;
    return startDownloadProcess(fileType, instance, archivedDocumentItemsCount, asynchronousLimit, archiveFileName, sortField, sortOrder);
  }

  @Override
  protected File processingDownloadFile(FileType fileType, int count, String downloadFileName, BeanDescriptor desc, Query searchQuery,
      String sortField,
      Direction sortOrder,
      Locale userLocale, String userDateFormat, Instance instance, boolean isCompanyUser, List<Document> documentsToUpdate) throws ServiceException {
    File tempFile = null;
    int limitFileSize = fileType == PDF ? pdfLimitFileSize : csvLimitFileSize;
    int totalFileNumber = (count % limitFileSize) == 0 ? (count / limitFileSize) : (count / limitFileSize) + 1;
    StringBuilder fileName = new StringBuilder(downloadFileName);
    for (int i = 0; i < totalFileNumber; i++) {
      fileName = fileName.delete(fileName.length() - 3, fileName.length() - 1)
          .append("_")
          .append(i);
      final int size = i < totalFileNumber ? limitFileSize : count - i * limitFileSize;
      switch (fileType) {
      case CSV:
        String csvFullPath = getDownloadDirectory() + FileSystems.getDefault()
            .getSeparator() + fileName.toString() + FileType.CSV.getExtension();
        tempFile = new File(csvFullPath);
        try (OutputStream is = new FileOutputStream(tempFile)) {
          org.springframework.data.domain.PageRequest pageable = isNotBlank(sortField)
              ? org.springframework.data.domain.PageRequest.of(i, size, sortOrder, sortField)
              : org.springframework.data.domain.PageRequest.of(i, size);
          this.eDocumentService.exportArchiveCarrefourFaxItems(desc, searchQuery, pageable, is,
              userLocale,
              isCompanyUser, getInstanceCode());
          log.info("End processing file {}", csvFullPath);
        }
        catch (IOException e) {
          log.error("Failed to export recaplist", e);
          error(e, "Error exporting the edocument checklist");
          throw new ServiceException(e);
        }
        break;
      default:
        log.warn("File type {} not supported", fileType);
        break;
      }
    }
    return totalFileNumber == 1 ? tempFile : zipDownloadFolder(TEMP_DIR + downloadFileName + ".zip", getDownloadDirectory());
  }

  @Override
  public String getPathDescriptor() {
    return pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  /* -- VIEW -- */
  protected Optional<DocumentViewer> getViewerArchivedFax(Instance instance, MongoIndexableDocument indexable,
      TransformService.BirtOutoutFileType type) {
    Document document = new Document(indexable.getReference(), indexable.getOwners(), indexable.getType(), indexable.getFrom(),
        indexable.getTo(), indexable.getStatus());
    File fax = new File(indexable.getReference());
    document.addFile(new DocumentFile(fax, FileType.TXT, null, null, document, null));
    final Optional<DocumentViewer> view = this.faxViewerService.view(document,
        indexable, instance, null, this.selectedLanguage, DOCUMENT_VIEWER_BASE_NAME, false, false, null, type);
    return viewSanityCheck(view);
  }

}
