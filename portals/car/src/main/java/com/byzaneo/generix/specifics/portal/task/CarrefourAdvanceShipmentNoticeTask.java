package com.byzaneo.generix.specifics.portal.task;

import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.JSFHelper.sendFile;
import static com.byzaneo.commons.ui.util.MessageHelper.addMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.generix.edocument.service.EDocumentService.ENCODE_FILE_NAME_PREFIX;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.cloneASN;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.createASNNumberByDocument;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.createNextPartialASN;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.getBuyerOrderNumber;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.initASNWithOrder;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.initASNWithOrderDetails;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.initASNWithOrderResponse;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.initShipmentReference;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.isSellerAddressNotPresent;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.saveBackOrderByItemDetailLine;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.updateSellerParty;
import static com.byzaneo.generix.ui.FileUploadDialogHandler.openFileUploadDialog;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.computeAndSetBackOrderForNewOrPartialAsn;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.concatBuyerOrderNumber;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.containsPullOrders;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.decodeFileName;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getASNCarrier;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getASNCarrierIdent;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getASNStatus;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getAttachedFiles;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getBuyerLineItemNum;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getLastASN;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getMaxDocumentSizeInBytes;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getShipmentIdentifier;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getShipmentReference;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getTransportRoutingType;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getUserTypeLabels;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.isType;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.populateChildrenIndex;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.setShippingReference;
import static com.byzaneo.portal.util.PortalHelper.findFirstPageByPortletContentId;
import static com.byzaneo.portal.util.PortalHelper.findPage;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.ilike;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.security.spring.UserDetailsService.getAuthenticatedUser;
import static com.byzaneo.task.util.TaskHelper.error;
import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static com.byzaneo.task.util.TaskHelper.warn;
import static com.byzaneo.xtrade.api.DocumentType.DESADV;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static java.lang.String.valueOf;
import static java.util.Arrays.asList;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.MapUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.util.Assert.notNull;

import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.*;
import java.util.stream.Stream;

import javax.faces.application.FacesMessage;
import javax.faces.convert.Converter;
import javax.faces.model.SelectItem;
import javax.xml.datatype.XMLGregorianCalendar;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.event.*;
import org.primefaces.model.UploadedFile;
import org.springframework.beans.factory.annotation.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.SpringContextHelper;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.exception.*;
import com.byzaneo.generix.edocument.exception.asn.BuyerOrderNumberNotFoundException;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.generix.service.TransformService;
import com.byzaneo.generix.service.TransformService.BirtOutoutFileType;
import com.byzaneo.generix.service.repository.bean.Carrier;
import com.byzaneo.generix.service.repository.bean.Carrier.*;
import com.byzaneo.generix.service.repository.service.RepositoryService;
import com.byzaneo.generix.ui.FileUploadDialogConfiguration;
import com.byzaneo.generix.ui.instance.PortalISHandler;
import com.byzaneo.generix.xcbl.api.LabelService;
import com.byzaneo.generix.xcbl.portal.task.*;
import com.byzaneo.generix.xcbl.portal.task.AdvanceShipmentNoticeTask.UserType;
import com.byzaneo.generix.xcbl.portal.task.converter.CarrierConverter;
import com.byzaneo.generix.xcbl.portal.task.util.BeanIssueXcblUtils;
import com.byzaneo.generix.xcbl.service.XcblService;
import com.byzaneo.generix.xcbl.ui.SessionCacheHandler;
import com.byzaneo.generix.xcbl.ui.SessionCacheHandler.LineItem;
import com.byzaneo.portal.bean.Portal;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;

@Task(name = "CarrefourAdvanceShipmentNotice", library = "portal/specifics", version = "1.0.0")
@TaskViewModel(labelFamily = CarrefourAdvanceShipmentNoticeTask.LABEL_FAMILY)
public class CarrefourAdvanceShipmentNoticeTask extends AbstractXcblDocumentTask<AdvanceShipmentNotice> {

  private static final long serialVersionUID = 1L;

  private static final String COMMON_LABEL_FAMILY = "gnxxcblcomlbls";

  public static final String LABEL_FAMILY = "crasnlbls";

  public static final int PACKAGE_NUMBER_ORDER = 0;

  public static final int TRACKING_NUMBER_ORDER = 1;

  public static final String PACKAGE_NUMBER = "PackageNumber";

  public static final String TRACKING_NUMBER = "TrackingNumber";

  public static final String ENTITY_ID = "_entity_id";

  public static final String ATTACHED_FILES_ACTION_NAME = "addFileToEDocument";

  public static final String NOT_APPLICABLE = "NotApplicable";

  public static final String USER_FRIENDLY_MESSAGE_NAME = "desadv";

  @Autowired
  @Qualifier(RepositoryService.SERVICE_NAME)
  private transient RepositoryService repositoryService;

  @Autowired
  private transient XcblService xcblService;

  @Autowired
  private transient AsnService asnService;

  @Autowired
  private transient TransformService transformService;

  @Autowired
  @Qualifier(DocumentViewerService.SERVICE_NAME)
  private transient DocumentViewerService taskVisualisationService;

  @Autowired
  private transient OrderService orderService;

  private transient OrderIndex order;

  private transient Boolean pullOrder;

  private transient Boolean hasPullOrders;

  private transient List<Object> actions;

  private transient Map<LineItem, OrderIndex> orderLines;

  private transient SessionCacheHandler sessionCacheHandler;

  private transient DocumentFile selectedDocumentFile;

  // THIS IS THE ASN WHO IS CONCERNED IN THE UPLOAD ACTION //
  private transient AdvanceShipmentNotice asnInCurrentAction;

  private boolean printLabel;

  private UserType userType;

  private Boolean carrierRequired;

  private Boolean dateDeliveryRequired;

  private Boolean dateShippingRequired;

  private Boolean allowBackOrderImport = Boolean.FALSE;

  private String webService;

  private transient Carrier carrierSelected = new Carrier();

  private transient CarrierConverter converter = new CarrierConverter(new ArrayList<>());

  // IMPORT DOCUMENT //
  private boolean importDocucmentEnabled = false;

  private int maxDocumentSize;

  private transient Optional<Document> desadvDraftDoc;

  private transient boolean onUpdatingDesadvFromAsnPortlet = false;

  private transient File labelZipFile;

  private transient boolean editingLine;

  private transient Map<Integer, BigDecimal> tmpBackOrderByItemDetail;

  private transient boolean fromOrderLines;

  private transient boolean curretnAsnInPartial;

  // - VIEW -
  private transient boolean exportActionEnabled = true;

  private boolean countEnabled;

  private transient String importErrorMsgId;

  private transient Object[] importErrorParams;

  private String nextPortletPage;

  public CarrefourAdvanceShipmentNoticeTask() {
    PropertyUtils.addBeanIntrospector(new BeanIssueXcblUtils());
  }

  private String viewerBaseName(boolean datePostfix, Document document) {
    Stream<String> stream = Stream.of(
        getLabel(LABEL_FAMILY, "document_name", USER_FRIENDLY_MESSAGE_NAME, getLocale()).toLowerCase(),
        of("asnHeader.asnNumber")
            .map(this.descriptor::get)
            .filter(p -> document.isIndexed())
            .map(p -> p.getDisplayValue(document.getIndexValue(),
                null, null, PATTERN_DATE,
                document.getReference()))
            .orElse(null),
        datePostfix ? new SimpleDateFormat(PATTERN_DATE).format(new Date()) : EMPTY);
    return (stream.filter(StringUtils::isNotEmpty)
        .collect(joining("-")));
  }

  /**
   * @see com.byzaneo.generix.xcbl.portal.task.AbstractXcblDocumentTask#getEDocument()
   */
  @Override
  public EDocument getEDocument() {
    return EDocument.AdvanceShipmentNotice;
  }

  /**
   * The method is used for make displayable an attached file of an advance shipment notice.
   *
   * @param indexable
   */
  public void onSelectionDocumentFile(Instance instance, Indexable indexable) {

    // A DOCUMENT FILE HAS BEEN SELECTED
    notNull(selectedDocumentFile);

    // RETRIEVES TYPE FILE
    FileType typeFile = selectedDocumentFile.getType();
    boolean displayable = FileType.PDF.equals(typeFile) || FileType.TXT.equals(typeFile);

    // FILE IS DISPLAYABLE - LET'S SHOW IT
    if (displayable) {
      document = this.documentService.getDocument(indexable);
      viewer = new DocumentViewer(document, doc -> decodeSelectedDocumentFileName(selectedDocumentFile));
      viewer.add(selectedDocumentFile);
      selectedInBirtPanel = indexable;
      updateStatus();
      setMultiExport(false);
      exportActionEnabled = false;
    }
    // FILE CAN NOT DISPLAYABLE - SEND AN INFO MESSAGE TO USER
    else {
      error(this, null, COMMON_LABEL_FAMILY + ".error_load_rptdesign");
    }
  }

  public void onSendDrafASN(final Instance instance, final AdvanceShipmentNotice advanceShipementNotice) {
    notNull(advanceShipementNotice, "No advance shipment notice selected.");

    indexable = advanceShipementNotice;
    desadvDraftDoc = Optional.of(documentService.getDocument(indexable));
    if (desadvDraftDoc.isPresent()) {
      document = desadvDraftDoc.get()
          .getParent();
    }

    onUpdatingDesadvFromAsnPortlet = true;
    updateASNDocument(false);

    onUpdatingDesadvFromAsnPortlet = false;
    indexable = null;
  }

  public void onEditDraftAsn(final Instance instance,
      final AdvanceShipmentNotice advanceShipementNotice) {

    onUpdatingDesadvFromAsnPortlet = true;
    notNull(advanceShipementNotice, "No advance shipment notice selected.");
    indexable = advanceShipementNotice;
    initCarrier(advanceShipementNotice);
    desadvDraftDoc = Optional.of(documentService.getDocument(indexable));
    document = desadvDraftDoc.map(doc -> doc.getParent())
        .orElse(null);
    tmpBackOrderByItemDetail = saveBackOrderByItemDetailLine(indexable,
        desadvDraftDoc);
    addMessage(null, FacesMessage.SEVERITY_INFO, "gnxxcblasnlbls.asnDraft");
  }

  @Override
  public String getPreviousTaskUrl(Portal portal) {
    com.byzaneo.portal.bean.Page returnPage = findPage(portal, getPortalSessionHandler().getReturnPage());

    // on updating desadv from asn portlet, the return page is asn portlet
    if (returnPage == null && onUpdatingDesadvFromAsnPortlet) {
      returnPage = getPortalSessionHandler().getPage();
    }
    else if (returnPage == null) {
      return null;
    }
    return PortalISHandler.PORTAL_URL + "?page=" + returnPage.getId();
  }

  public void onDownloadCSVs(Instance instance) {
    onDownloads(instance, BirtOutoutFileType.Excel, getIndexableType());
  }

  public void onDownloadPDFs(Instance instance) {
    onDownloads(instance, BirtOutoutFileType.PDF, getIndexableType());
  }

  @Override
  protected Function<Document, String> viewerBaseName(String baseFileNmae) {
    return (document) -> {
      return viewerBaseName(true, document);
    };
  }

  @Override
  public void onSelectDocument(Instance instance) {
    this.onView(instance, (AdvanceShipmentNotice) getSelected());
    this.selected = null;
    this.allIndexesSelected = false;
  }

  @Override
  public void onSelect(Instance instance, AdvanceShipmentNotice indexable, PropertyDescriptor property) {
    onView(instance, indexable);
    this.selected = null;
    this.allIndexesSelected = false;
  }

  private void onView(Instance instance, AdvanceShipmentNotice advanceShipmentNotice) {
    try {
      exportActionEnabled = true;
      this.viewer = view(instance, advanceShipmentNotice, BirtOutoutFileType.PDF).orElse(null);
      if (viewer != null) {
        document = viewer.getDocument();
        updateStatus();
      }
    }
    catch (TemplateMissingException tme) {
      error(tme, MessageHelper.getMessage(LABEL_FAMILY + ".error_missing_template",
          "There is no Birt template configured, please contact your administrator.", this.selectedLanguage));
    }
    catch (Exception e) {
      error(e, "Error viewing document: %s", getRootCauseMessage(e));
    }
    finally {
      document = null;
    }
  }

  public String goToNextPortlet(Portal portal) {
    if (nextPortletPage == null) {
      Long actionID = getDefinition(this).getParent()
          .getId();
      com.byzaneo.portal.bean.Page page = findFirstPageByPortletContentId(portal, valueOf(actionID));
      if (page == null) {
        MessageHelper.warn("No Page defined for action : %s", taskService.getTaskDefinition(actionID)
            .getTitle()); // I18N
        return null;
      }
      nextPortletPage = page.getId();
    }
    return nextPortletPage;
  }

  private Optional<DocumentViewer> view(Instance instance, AdvanceShipmentNotice advanceShipmentNotice, BirtOutoutFileType type) {
    final Optional<DocumentViewer> view = this.taskVisualisationService.view(
        advanceShipmentNotice, instance, this.partner,
        this.selectedLanguage,
        (document) -> {
          return viewerBaseName(false, document);
        }, false, false, null, type);
    // document not found
    if (!view.isPresent()) {
      error("Document not found");
    }
    // performs straight validation to gets user's feed-backs...
    // to avoid unnecessary transformations, we could
    // change the document visualization workflow
    if (!view.map(DocumentViewer::isValid)
        .get()) {
      error(this, null, LABEL_FAMILY + ".error_missing_template");
      return empty();
    }
    return view;
  }

  public List<String> getWebServices() {
    return new ArrayList<>(xcblService.getLabelServices()
        .keySet());
  }

  /**
   * Indicate if printing labels can be done around owner in parameter
   *
   * @return
   */
  public boolean printableLabels(final Instance instance, final Indexable indexable) {
    boolean isAquittedASN = indexable instanceof AdvanceShipmentNotice && isAcquitedOrSendOrDraft((AdvanceShipmentNotice) indexable);
    return isAquittedASN && transformService.getTemplate(instance.getGroup(), DocumentType.DESADVLABS.toString(), FileType.PDF) != null;
  }

  public boolean isAcquitedOrSendOrDraft(AdvanceShipmentNotice asn) {
    return DocumentStatus.ACQUITTED.equals(asn.getStatusAsEnumValue()) || DocumentStatus.SENT.equals(asn.getStatusAsEnumValue()) ||
        DocumentStatus.PENDING.equals(asn.getStatusAsEnumValue());
  }

  public void onDownloadPDF(Instance instance, AdvanceShipmentNotice advanceShipmentNotice) {
    onDownload(instance, advanceShipmentNotice, BirtOutoutFileType.PDF);
  }

  public void onDownloadCSV(Instance instance, AdvanceShipmentNotice advanceShipmentNotice) {
    onDownload(instance, advanceShipmentNotice, BirtOutoutFileType.Excel);
  }

  public void onDownloadCSV(Instance instance) {
    onDownload(instance, this.documentService.getIndexable(this.viewer.getDocument()), BirtOutoutFileType.Excel);
  }

  public void onDownload() {
    sendFile(download.getFile(), download.getName(), true);
  }

  private void onDownload(Instance instance, AdvanceShipmentNotice advanceShipmentNotice, BirtOutoutFileType type) {
    try {
      this.download = view(instance, advanceShipmentNotice, type)
          .flatMap(DocumentViewer::download)
          .orElse(null);
      getCurrentInstance().addCallbackParam("download", this.download != null);
    }
    catch (Exception e) {
      error(e, "Error downloading document: %s", getRootCauseMessage(e));
    }
  }

  /**
   * Just printing labels if possible.
   * 
   * @param instance
   * @param advanceShipementNotice
   */
  public void onPrintLabel(final Instance instance, final AdvanceShipmentNotice advanceShipementNotice) {
    if (printableLabels(instance, advanceShipementNotice)) {
      // required to obtain the name
      super.selectedInBirtPanel = advanceShipementNotice;

      File file = eDocumentService.getLabelsFile(advanceShipementNotice, getLocale(), instance);
      this.download = new DocumentFileViewer(
          getUserFriendlyBirtFileName(FileType.PDF.getExtension()).orElse(file != null ? file.getName() : null), FileType.PDF,
          file);
      // to save indexable in context on print view

      getCurrentInstance()
          .addCallbackParam("download", this.download != null && this.download.getFile() != null);
    }
  }

  public void onAcquitASN(final AdvanceShipmentNotice asn) {
    asnService.acquitASN(asn);
    addMessage(FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".info_confirm_change_status", asn.getASNHeader()
        .getASNNumber());
  }

  public boolean isAuthorizedToAcquitASN(final AdvanceShipmentNotice asn) {
    return !isPartnerUser() && asnService.isStatusSent(asn);
  }

  /**
   * It is used for download labels PDF file.
   *
   * @param advanceShipementNotice
   */
  public void onPrintLabel(final AdvanceShipmentNotice advanceShipementNotice) {
    final LabelService labelService = SpringContextHelper.getBean(LabelService.class, this.webService);
    if (labelService == null) {
      warn(this, "gnxxcblasnlbls.warn_no_label");
    }
    else {
      try {
        labelZipFile = labelService.getLabelZip(advanceShipementNotice);
        getCurrentInstance().addCallbackParam("download", this.labelZipFile != null);
      }
      catch (BuyerOrderNumberNotFoundException e) {
        error(this, e, "gnxxcblasnlbls.buyerpartnumber_empty");
      }
      catch (final RuntimeException re) {
        log.error(getRootCauseMessage(re), re);
        addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".error_label", getRootCauseMessage(re));

      }
    }
  }

  public void attemptAsnSaving() {

    setEmptyMarked(NOT_APPLICABLE, null);

    final AdvanceShipmentNotice cloneASN = cloneASN(indexable);
    final Map<String, Optional<Order>> orders = asnService.getOrdersByBuyerOrderNumber(cloneASN);

    final boolean confirmationNeeded = hasPullOrders() ? asnService.isShippedMoreThanOrdered(cloneASN, orders)
        : asnService.isPartial(cloneASN);

    if (confirmationNeeded) {
      final RequestContext context = RequestContext.getCurrentInstance();
      context.addCallbackParam("validationFailed", true);
      if (desadvDraftDoc.isPresent()) {
        context.execute("jQuery('.psCfmConfirmAsnDraft').click()");
      }
      else {
        context.execute("jQuery('.psCfmConfirmAsn').click()");
      }
      return;
    }

    // to confirm an existant asn draft document
    if (desadvDraftDoc.isPresent()) {
      updateASNDocument(false);
      removeGeneratedFilesAfterAsnEdition(desadvDraftDoc);
    }
    else {
      createASNDocument(false);
    }

  }

  /**
   * This method is used for : <br>
   * <strong> create a draft desadv </strong> or <strong>update an existant draft desadv without sending</strong> <br>
   * <br>
   *
   * <AUTHOR>
   */
  public void attemptAsnDraft() {
    // for updating of existant asn draft document
    if (desadvDraftDoc.isPresent()) {
      updateASNDocument(true);
      removeGeneratedFilesAfterAsnEdition(desadvDraftDoc);
    }
    else {
      createASNDocument(true);
    }
  }

  /**
   * After edit an asn, remove the birt generated files to force birt to regenerated new files according with modifications
   */
  public void removeGeneratedFilesAfterAsnEdition(final Optional<Document> desadvDraftDoc) {

    Document document = documentService.getDocument(desadvDraftDoc.get()
        .getId());
    asnService.removeFromDocumentGeneratedFiles(document, Arrays.asList(FileType.HTML, FileType.PDF, FileType.CSV));
  }

  public void setEmptyMarked(String value, String valueToReplace) {

    ReferenceType shipmentRef = getShipmentIdentifier(indexable);
    if (StringUtils.equals(shipmentRef.getRefNum(), valueToReplace)) {
      shipmentRef.setRefNum(value);
    }
  }

  public void createASNDocument(boolean saveAsDraft) {
    log.info("Start create Advance shipment notice");
    try {
      if (checkRevisionDocument()) {
        populateChildrenIndex(documentService, document);
        asnService.updateAsnStatus(indexable);
        updateASNTransportRouting();
        createAndSaveASN(saveAsDraft);
        createASNLinks();

        ASNStatusCodeType asnStatus = getASNStatus(indexable);
        addMessage(FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".info_confirm_action",
            getMessage(LABEL_FAMILY + "." + asnStatus, asnStatus.name(), selectedLanguage), TASK_MESSAGES);
      }
      else {
        addMessage(null, FacesMessage.SEVERITY_ERROR, COMMON_LABEL_FAMILY + ".error_saving_concurrent_access", TASK_MESSAGES);
      }
    }
    catch (final Exception e) {
      setEmptyMarked(null, NOT_APPLICABLE);
      log.error("Failed to create advance shipment notice", e);
      addMessage(e, FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".error_in_ASN_generation", TASK_MESSAGES);
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
    }
    log.info("End");
  }

  /**
   * This method is used for : <strong>update an existant draft desadv with possibility to sending</strong> according to <strong>
   * {@code saveAsDraft} </strong> <br>
   *
   * @param saveAsDraft <strong>true</strong> for update and save as draft ,<strong>false</strong> for sending draft
   * <AUTHOR>
   */
  public void updateASNDocument(boolean saveAsDraft) {
    log.info("Start update Advance shipment notice");
    try {
      // During sending of asn from asn portlet, dont need to check document version
      // because it's updated before calling the current method
      if (onUpdatingDesadvFromAsnPortlet || checkRevisionDocument()) {

        document = documentService.getDocument(document.getId());
        populateChildrenIndex(documentService, document);
        asnService.updateAsnStatus(indexable);
        if (!onUpdatingDesadvFromAsnPortlet || saveAsDraft) {
          // the carrier shouldn't be updated when sending from asn portlet (the draft is already saved)
          updateASNTransportRouting();
        }
        updateAndSaveASN(saveAsDraft);

        ASNStatusCodeType asnStatus = getASNStatus(indexable);
        addMessage(FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".info_confirm_action",
            getMessage(LABEL_FAMILY + "." + asnStatus, asnStatus.toString(), selectedLanguage), TASK_MESSAGES);
      }
      else {
        addMessage(null, FacesMessage.SEVERITY_ERROR, COMMON_LABEL_FAMILY + ".error_saving_concurrent_access", TASK_MESSAGES);
      }
    }
    catch (final Exception e) {
      setEmptyMarked(null, NOT_APPLICABLE);
      addMessage(e, FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".error_in_ASN_generation", TASK_MESSAGES);
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
    }
    log.info("End");
  }

  private void createAndSaveASN(boolean saveAsDraft) throws Exception {
    if (document != null) {
      // for draft desadv, dont need to update its parent's (order) status
      Consumer<Document> consumer = saveAsDraft ? newDocument -> updateDocumentRootStatus(null, null)
          : newDocument -> updateDocumentRootStatus(newDocument, newDocument.getStatus());
      asnService.createDocumentProcessAndSave(indexable, document, saveAsDraft, consumer);
    }
    else {
      asnService.processAndSave(AsnXcblHelper.createASNDocument(indexable, this.companyCode));
    }
  }

  /**
   * Update asn document according to {@code saveAsDraft} value
   *
   * @param saveAsDraft
   * @throws Exception
   */
  private void updateAndSaveASN(boolean saveAsDraft) throws Exception {
    if (document != null && desadvDraftDoc.get() != null) {
      Document desadvDocument = documentService.getDocument(desadvDraftDoc.get()
          .getId());

      // for draft desadv, dont update its parent's (order) status
      if (saveAsDraft) {
        asnService.updateDocumentStatusProcessAndSave(indexable, desadvDocument, document, saveAsDraft,
            newDocument -> updateDocumentRootStatus(null, null));
      }
      else {
        asnService.updateDocumentStatusProcessAndSave(indexable, desadvDocument, document, saveAsDraft,
            newDocument -> updateDocumentRootStatus(desadvDocument, desadvDocument.getStatus()));
      }
    }
    else {
      asnService.processAndSave(AsnXcblHelper.createASNDocument(indexable, this.companyCode));
    }
  }

  /**
   * Links the ASN document with the document(s) that initialized it.
   */
  private void createASNLinks() {
    if (document != null) {
      eDocumentService.createDocumentLinks(asList(indexable.getEntityRef(), document.getIndexReference()));
    }
    else if (isNotEmpty(orderLines)) {
      orderLines.values()
          .stream()
          .<String> map(OrderIndex::getEntityRef)
          .distinct()
          .map(ref -> asList(indexable.getEntityRef(), ref))
          .forEach(eDocumentService::createDocumentLinks);
    }
  }

  @Override
  public boolean checkRevisionDocument() {
    return document == null || super.checkRevisionDocument();
  }

  public void saveASNDetail(RowEditEvent event) {
    saveASNDetail();
  }

  public void saveASNDetail() {
    // XXX ASNItemDetailType item = (ASNItemDetailType) event.getObject();
    XcblHelper.computeTransportTotalQuatity(indexable);
    computeAndSetBackOrderForNewOrPartialAsn(indexable, tmpBackOrderByItemDetail);
    editingLine = false;
  }

  public void updateASNTransportRouting() {
    getTransportRoutingType(indexable).ifPresent(routingType -> AsnXcblHelper.updateASNTransportRouting(routingType, carrierSelected));
  }

  public List<Object> getActions(final Instance instance) {
    if (actions == null) {
      actions = taskService.getTypes()
          .stream()
          .filter(taskType -> InvoiceTask.class.equals(taskType.getType()))
          .flatMap(taskType -> taskService.getTasks(instance, taskType)
              .stream())
          .collect(toList());
    }
    return actions;
  }

  public AdvanceShipmentNotice getAdvanceShipmentNotice() {
    try {
      if (indexable == null) {
        Long documentId = Long.valueOf(getPortalSessionHandler().getDocumentId());

        if (getPortalSessionHandler().getDocumentId() != null && documentId >= 0) {
          document = documentService.getDocument(Document.class, documentId, true);

          if (document != null) {
            curretnAsnInPartial = false;
            desadvDraftDoc = document.getChildren()
                .stream()
                .filter(doc -> isType(doc, DESADV) && isEqualsStatusCode(doc.getStatus(), DocumentStatus.PENDING))
                .findFirst();

            indexable = desadvDraftDoc.map(doc -> initASNDraft(doc))
                .orElseGet(() -> initASN(document)/* use orElseGet to invoke initASN() only if the draft is not present */);
          }
        }
        else if (getSessionCacheHandler().getLineItems()
            .containsKey(String.valueOf(getTaskDefinitionId()))) {
              resolveOrganizationCodes();
              desadvDraftDoc = Optional.empty();
              fromOrderLines = true;
              initASNWithOrderLines();
            }

        tmpBackOrderByItemDetail = saveBackOrderByItemDetailLine(indexable, desadvDraftDoc);
      }
    }
    catch (final Exception e) {
      error(this, e, "gnxxcblorderlbls.error_in_OrderToASN_conversion");
      log.error("Failed to get advance shipment notice", e);
    }
    return indexable;
  }

  /**
   * Initialise a draft ASN based on an existing draft document
   * 
   * @param doc
   * @return
   */
  private AdvanceShipmentNotice initASNDraft(Document doc) {
    addMessage(null, FacesMessage.SEVERITY_INFO, "gnxxcblasnlbls.asnDraft");

    AdvanceShipmentNotice advanceShipmentNotice = documentService.<AdvanceShipmentNotice> getIndexable(doc);
    initCarrier(advanceShipmentNotice);
    return advanceShipmentNotice;
  }

  /**
   * Initialise the carrier list if necessary and sets the selected carrier based on the ASN Transport routing carrier
   * 
   * @param advanceShipmentNotice
   */
  private void initCarrier(AdvanceShipmentNotice advanceShipmentNotice) {
    String carrierIdent = getASNCarrierIdent(advanceShipmentNotice);
    if (carrierIdent != null) {
      // if there is a transportRoutingType on the AdvanceShipmentNotice
      // set it as the selected carrier-> obtain the list of available carriers and select it by its id
      Carrier carrier = this.repositoryService.searchCarrierByCarrierId(carrierIdent);
      Carrier carrierAsn = getASNCarrier(advanceShipmentNotice);
      if (carrier != null && sameCarrier(carrier, carrierAsn))
        setCarrierSelected(carrier);
      else
        setCarrierSelected(getASNCarrier(advanceShipmentNotice));
    }
    else {
      setCarrierSelected(this.repositoryService.getDefaultCarrier());
    }
  }

  /**
   * Returns true if they have the same information for the carrier, does a shallow compare
   * 
   * @param carrier1
   * @param carrier2
   */
  private boolean sameCarrier(Carrier carrier1, Carrier carrier2) {
    if (carrier1 == null || carrier2 == null)
      return false;
    if (!carrier1.getCarrierId()
        .equals(carrier2.getCarrierId()))
      return false;
    if (!carrier1.getName()
        .equals(carrier2.getName()))
      return false;
    if (!carrier1.getCarrierMode()
        .equals(carrier2.getCarrierMode()))
      return false;
    if (!carrier1.getCarrierType()
        .equals(carrier2.getCarrierType()))
      return false;
    return true;
  }

  /**
   * Returns an ASN based on the passed {@code document}.
   *
   * @param document the document used to generate the ASN.
   * @return an partially filled ASN.
   * @throws ServiceException if the type of document doesn't allows to init an ASN.
   */
  public AdvanceShipmentNotice initASN(Document document)
      throws ServiceException {

    curretnAsnInPartial = false;

    String documentType = document.getType();
    if (isEqualsStatusCode(DocumentStatus.SENT_PARTIALLY, document.getStatus())) {
      final AdvanceShipmentNotice partialAsn = getLastASN(documentService, document.getId());
      if (partialAsn != null) {
        curretnAsnInPartial = true;
        boolean isPullOrder = isType(document, DocumentType.ORDERS) ? OrderHelper.isPullOrder(document.getIndexValue()) : false;
        return createNextPartialASN(partialAsn, createASNNumberByDocument(document) + "-" + document.getChildCount(), isPullOrder);
      }
    }

    AdvanceShipmentNotice asn;
    switch (DocumentType.valueOf(documentType)) {
    case ORDERS:
      Order orderXcbl = orderService.convertToOrderType((OrderIndex) document.getIndexValue(), false);
      asn = initASNWithOrder(orderXcbl);
      break;
    case ORDRSP:
      final Query query = QueryBuilder.createBuilder()
          .and(Clauses.equal(ENTITY_ID, document.getParent()
              .getId()))
          .query();
      this.order = Optional.ofNullable(documentService.searchIndexable(OrderIndex.class, query))
          .orElseThrow(ServiceException::new);
      asn = initASNWithOrderResponse(document.getIndexValue(), order.getIssueDate());
      break;
    default:
      throw new ServiceException("Don't know how to build ASN from %s", documentType);
    }
    asn.setOwners(document.getOwners());

    if (isSellerAddressNotPresent(asn)) {
      resolveOrganizationCodes();
      asn = updateSellerParty(asn, partner);
    }

    initShipmentReference(asn);
    initCarrier(asn);
    return asn;
  }

  private void initASNWithOrderLines() {
    curretnAsnInPartial = false;
    String taskDefinition = String.valueOf(getTaskDefinitionId());
    List<OrderIndex> ordersIndex = Optional.of(getSessionCacheHandler())
        .map(SessionCacheHandler::getLineItems)
        .map(items -> items.get(taskDefinition))
        .orElse(Collections.emptyList())
        .stream()
        .map(this::initOrderLine)
        .collect(toList());
    List<Order> orders = orderService.convertToOrderType(ordersIndex, false);
    indexable = initASNWithOrderDetails(orders, this.companyCode);
    getSessionCacheHandler().getLineItems()
        .remove(taskDefinition);
  }

  public SessionCacheHandler getSessionCacheHandler() {
    if (sessionCacheHandler == null) {
      sessionCacheHandler = getManagedBean(SessionCacheHandler.class, SessionCacheHandler.MANAGED_BEAN_NAME);
    }
    return sessionCacheHandler;
  }

  private OrderIndex getOrderByItem(final ASNItemDetailType item) {
    LineItem lineItem = new LineItem(getBuyerOrderNumber(item), getBuyerLineItemNum(item));
    return orderLines.get(lineItem);
  }

  public OrderIndex initOrderLine(final LineItem lineItem) {
    OrderIndex order = eDocumentService.searchOrderLine(lineItem.getDocumentId(), lineItem.getLineItemNum());
    if (orderLines == null) {
      orderLines = new HashMap<>();
    }
    orderLines.put(lineItem, order);
    return order;
  }

  public String getASNReference(List<ASNOrderNumberType> asnOrderNumbers) {
    return concatBuyerOrderNumber(asnOrderNumbers, " ");
  }

  public Boolean isPullOrder(final ASNItemDetailType item) {
    if (pullOrder == null) {
      if (isType(document, DocumentType.ORDRSP)) {
        return false;
      }
      if (order == null && MapUtils.isEmpty(orderLines)) {
        return false;
      }
      List<OrderIndex> orders = Collections.singletonList(order != null ? order : getOrderByItem(item));
      pullOrder = containsPullOrders(document, orders);
    }
    return pullOrder;
  }

  public CarrierMode[] getModeCarrier() {
    return Carrier.CarrierMode.values();
  }

  public CarrierType[] getTypeCarrier() {
    return Carrier.CarrierType.values();
  }

  /**
   * Get status values
   * 
   * @param label
   * @return map which contains values status
   */
  @Override
  public Map<Object, String> getValues(String label) {
    return getStatusValues(AdvanceShipmentNotice.class, label);
  }

  @Override
  public List<String> getValues(PropertyDescriptor property) {
    return isOriginalStatusProperty(property) ? getAvailableStatus(AdvanceShipmentNotice.class) : null;
  }

  @Override
  public BeanDescriptor getSearch() {
    if (this.search == null) {
      this.search = super.getSearch();
      this.search.getProperties()
          .stream()
          .filter(pd -> pd.getType() == XMLGregorianCalendar.class || pd.getType() == Date.class)
          .forEach(pd -> pd.setValue(new DatePeriod()));
    }
    return this.search;
  }

  @Override
  public Logs importDocuments(List<InputStream> list) {
    return eDocumentService.importAdvanceShipmentNotices(company, partner, list, getAllowBackOrderImport(), "xcblCarrefourAsnHeader");
  }

  @Override
  public void setMultiSelected(Indexable[] selected) {
  }

  public void uploadImportFile() {
    FileUploadDialogConfiguration configuration = new FileUploadDialogConfiguration()
        .setTitle(COMMON_LABEL_FAMILY + ".choose_import_file")
        .setFilterTypes(".csv, .zip")
        .setAllowTypes("/.(csv|zip)$/")
        .setSizeLimit(FILE_MAX_SIZE)
        .setSingle(false);
    openFileUploadDialog(configuration);
  }

  /**
   * After attached file have been validate from upload file dialog, this event catch them for save.
   *
   * @param event
   */
  public void onUploadLinkedDocumentDialogReturn(FileUploadEvent event) {
    try {
      UploadedFile uploadedFile = event.getFile();
      if (uploadedFile != null) {
        User connectedUser = getAuthenticatedUser();
        eDocumentService.importASNLinkedFiles(connectedUser, getAsnInCurrentAction(), uploadedFile);
      }
    }
    catch (FileAlreadyExistsException e) {
      importErrorMsgId = LABEL_FAMILY + ".error_import_attachment_alreadyExist";
      importErrorParams = new String[] { event.getFile()
          .getFileName() };
    }
    catch (ServiceException | CannotSaveFileException e) {
      importErrorMsgId = LABEL_FAMILY + ".error_import_attachment_general";
      log.error("The document importation can not be done", e);
    }
    finally {
      setAsnInCurrentAction(null);
    }
  }

  /**
   * It is used when importDocucmentEnabled radio button was changed.<br/>
   * This method will update the maxDocumentSize with 0 also when the radio is false.
   */
  public void updateMaxDocumentSize() {
    if (!isImportDocucmentEnabled())
      setMaxDocumentSize(0);
  }

  @SuppressWarnings("unchecked")
  @Override
  protected Class<DocumentFile> getRowExpansionType() {
    return DocumentFile.class;
  }

  @SuppressWarnings("unchecked")
  @Override
  public List<DocumentFile> resolveRowExpansionData(DocumentService documentService, Indexable rowExpansionSelected) {
    return getAttachedFiles(documentService, rowExpansionSelected, ATTACHED_FILES_ACTION_NAME);
  }

  @Override
  public String i18n(final String key) {
    return super.i18n(LABEL_FAMILY + "." + key);
  }

  public void onDownloadLabelZipFile() {
    sendFile(labelZipFile, labelZipFile.getName(), true);
  }

  public void onDownloadLabelFile() {
    onDownload();
  }

  public boolean hasLabelService() {
    return StringUtils.isNotBlank(this.webService);
  }

  public boolean isPrintLabel() {
    return printLabel;
  }

  public void setPrintLabel(final boolean printLabel) {
    this.printLabel = printLabel;
  }

  public boolean isCarrierRequired() {
    return carrierRequired == null ? Boolean.TRUE : carrierRequired;
  }

  public void setCarrierRequired(final boolean carrierRequired) {
    this.carrierRequired = carrierRequired;
  }

  public boolean isDateDeliveryRequired() {
    return dateDeliveryRequired == null ? Boolean.TRUE : dateDeliveryRequired;
  }

  public void setDateDeliveryRequired(final boolean dateDeliveryRequired) {
    this.dateDeliveryRequired = dateDeliveryRequired;
  }

  public boolean isDateShippingRequired() {
    return dateShippingRequired == null ? Boolean.TRUE : dateShippingRequired;
  }

  public void setDateShippingRequired(final boolean dateShippingRequired) {
    this.dateShippingRequired = dateShippingRequired;
  }

  public Carrier getCarrierSelected() {
    return carrierSelected;
  }

  public void setCarrierSelected(Carrier carrier) {
    // carrierselected is not a reference so that modifications can be made on it without changing the original object
    carrierSelected = new Carrier();
    if (carrier != null) {
      carrierSelected.setCarrierId(carrier.getCarrierId());
      carrierSelected.setCarrierMode(carrier.getCarrierMode());
      carrierSelected.setCarrierType(carrier.getCarrierType());
      carrierSelected.setName(carrier.getName());
    }
  }

  public List<Carrier> onCompleteCarrier(String query) {
    List<Carrier> list = getCarrierList(query, 10);
    converter.setCarrierList(list);
    return list;
  }

  /**
   * Return the maxSize carriers found based on the given query. The search is performed on the Carrier.carrierId and Carrier.name
   * attributes
   * 
   * @param query
   * @return
   */
  private List<Carrier> getCarrierList(String query, int maxSize) {

    resolveOrganizationCodes();
    return this.repositoryService.search(Carrier.class,
        createBuilder()
            .and(equal(Carrier.FIELD_OWNERS, companyCode))
            .or(equal(Carrier.FIELD_TO, partnerCode), equal(Carrier.FIELD_TO, companyCode))
            .or(ilike(Carrier.FIELD_REFERENCE, query.concat("*")),
                ilike(Carrier.FIELD_NAME, query),
                ilike(Carrier.FIELD_IDENTIFICATION, query))
            .query(),
        of(0, maxSize, ASC, Carrier.FIELD_NAME));
  }

  public Converter getCarrierConverter() {
    return converter;
  }

  public Boolean hasPullOrders() {
    if (hasPullOrders == null) {
      Collection<OrderIndex> orders = order != null ? Collections.singletonList(order)
          : (orderLines != null ? orderLines.values() : Collections.emptyList());
      hasPullOrders = containsPullOrders(document, orders);
    }
    return hasPullOrders;
  }

  public String getWebService() {
    return webService;
  }

  public AdvanceShipmentNotice getAsnInCurrentAction() {
    return asnInCurrentAction;
  }

  public void setAsnInCurrentAction(AdvanceShipmentNotice asnInCurrentAction) {
    this.asnInCurrentAction = asnInCurrentAction;
  }

  /** Return list of */
  public List<SelectItem> getUserTypes() {
    return getUserTypeLabels("labels.", selectedLanguage);
  }

  /** Check if user connected can print labels */
  public boolean userCanPrintLabels() {
    return isAllUsersCanPrint() || isClientCanPrint() || isPartnersCanPrint();
  }

  /** Check if any user can print labels or if the property 'userType' not defined */
  private boolean isAllUsersCanPrint() {
    return userType == null || userType == UserType.ALL;
  }

  /** Check if user connected is a client and can print labels */
  private boolean isClientCanPrint() {
    return userType == UserType.CLIENT && !isPartnerUserConnected();
  }

  /** Check if user connected is a partner and can print labels */
  private boolean isPartnersCanPrint() {
    return userType == UserType.PARTNERS && isPartnerUserConnected();
  }

  /**
   * It is Used for start from scratch the uploading files process around an Advance chipment notice document.<br/>
   * Specificly, this method is doing reinitializes asnInCurrentAction to null for a next usage.<br/>
   */
  public void initAsnInCurrentAction() {
    setAsnInCurrentAction(null);
    if (importErrorMsgId != null) {
      error(this, null, importErrorMsgId, importErrorParams);
    }
    importErrorMsgId = null;
    importErrorParams = null;
  }

  public void setWebService(String webService) {
    this.webService = webService;
  }

  @Override
  public boolean isMultiExport() {
    return multiExport;
  }

  @Override
  public void setMultiExport(boolean multiExport) {
    this.multiExport = multiExport;
  }

  public boolean isImportDocucmentEnabled() {
    return importDocucmentEnabled;
  }

  public DocumentFile getSelectedDocumentFile() {
    return selectedDocumentFile;
  }

  public String decodeSelectedDocumentFileName(DocumentFile selectedDocumentFile) {
    return decodeFileName(selectedDocumentFile.getFile()
        .getName(), ENCODE_FILE_NAME_PREFIX);
  }

  public void setSelectedDocumentFile(DocumentFile selectedDocumentFile) {
    this.selectedDocumentFile = selectedDocumentFile;
  }

  public void setImportDocucmentEnabled(boolean importDocucmentEnabled) {
    this.importDocucmentEnabled = importDocucmentEnabled;
  }

  public int getMaxDocumentSize() {
    return maxDocumentSize;
  }

  public void setMaxDocumentSize(int maxDocumentSize) {
    this.maxDocumentSize = maxDocumentSize;
  }

  public int getMaxDocumentSizeInByte() {
    return getMaxDocumentSizeInBytes(getMaxDocumentSize(), FILE_MAX_SIZE);
  }

  public AdvanceShipmentNotice getDraftAsnInDatabase() {
    if (desadvDraftDoc.isPresent()) {
      return documentService.getIndexable(desadvDraftDoc.get());
    }
    return new AdvanceShipmentNotice();
  }

  public boolean isOnUpdatingDesadvFromAsnPortlet() {
    return onUpdatingDesadvFromAsnPortlet;
  }

  public void setOnUpdatingDesadvFromAsnPortlet(boolean onUpdatingDesadvFromAsnPortlet) {
    this.onUpdatingDesadvFromAsnPortlet = onUpdatingDesadvFromAsnPortlet;
  }

  public UserType getUserType() {
    return userType;
  }

  public void setUserType(UserType userType) {
    this.userType = userType;
  }

  public boolean isEditingLine() {
    return editingLine;
  }

  public void setEditingLine(boolean editingLine) {
    this.editingLine = editingLine;
  }

  public void onRowCancel(RowEditEvent event) {
    editingLine = false;
  }

  public void onRowInit(RowEditEvent event) {
    editingLine = true;
    // to avoid show 2 times the message
    addMessage(null, FacesMessage.SEVERITY_WARN, COMMON_LABEL_FAMILY + ".validation_msg");
  }

  public Boolean getValidationEnabled() {
    return editingLine;
  }

  public boolean isFromOrderLines() {
    return fromOrderLines;
  }

  public void setFromOrderLines(boolean fromOrderLines) {
    this.fromOrderLines = fromOrderLines;
  }

  @Override
  public String getPathDescriptor() {
    if (pathDescriptor == null)
      pathDescriptor = "descriptors/carrefour-advanceshipmentnotice-columns.xml";
    return pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  public Map<Integer, BigDecimal> getTmpBackOrderByItemDetail() {
    return tmpBackOrderByItemDetail;
  }

  public void setTmpBackOrderByItemDetail(Map<Integer, BigDecimal> tmpBackOrderByItemDetail) {
    this.tmpBackOrderByItemDetail = tmpBackOrderByItemDetail;
  }

  public BigDecimal getASNDetailBackOrder(ASNItemDetailType itemDetailType) {
    return tmpBackOrderByItemDetail.getOrDefault(getBuyerLineItemNum(itemDetailType), BigDecimal.ZERO);
  }

  public boolean isCurretnAsnInPartial() {
    return curretnAsnInPartial;
  }

  public void setCurretnAsnInPartial(boolean curretnAsnInPartial) {
    this.curretnAsnInPartial = curretnAsnInPartial;
  }

  public boolean isExportActionEnabled() {
    return exportActionEnabled;
  }

  public void setExportActionEnabled(boolean exportActionEnabled) {
    this.exportActionEnabled = exportActionEnabled;
  }

  public Boolean getAllowBackOrderImport() {
    return allowBackOrderImport;
  }

  public void setAllowBackOrderImport(Boolean allowBackOrderImport) {
    this.allowBackOrderImport = allowBackOrderImport;
  }

  @Override
  public Optional<String> getUserFriendlyXcblMessageNumber() {
    return Optional.ofNullable(selectedInBirtPanel)
        .map(asn -> AsnHelper.getASNNumber((AdvanceShipmentNotice) asn));
  }

  @Override
  public String getUserFriendlyMessageType() {
    return USER_FRIENDLY_MESSAGE_NAME;
  }

  public String getPackageNumber() {
    return indexable != null ? getShipmentReference(indexable, PACKAGE_NUMBER) : null;
  }

  /**
   * asnHeader.shippingReference.otherShippingReferences.referenceCoded[0].primaryReference.refNum
   * 
   * @param packageNumber
   */
  public void setPackageNumber(String packageNumber) {
    if (indexable != null) {
      setShippingReference(indexable, PACKAGE_NUMBER, PACKAGE_NUMBER_ORDER, packageNumber);
    }
  }

  public String getTrackingNumber() {
    return indexable != null ? getShipmentReference(indexable, TRACKING_NUMBER) : null;
  }

  /**
   * asnHeader.shippingReference.otherShippingReferences.referenceCoded[1].primaryReference.refNum
   * 
   * @param trackingNumber
   */
  public void setTrackingNumber(String trackingNumber) {
    if (indexable != null) {
      setShippingReference(indexable, TRACKING_NUMBER, TRACKING_NUMBER_ORDER, trackingNumber);
    }
  }

  @Override
  public Optional<String> getUserFriendlyXcblMessageNumber(Indexable indexable) {
    return Optional.ofNullable(indexable)
        .map(asn -> ((AdvanceShipmentNotice) asn)
            .getASNHeader()
            .getASNNumber());
  }

  @Override
  public boolean isCountEnabled() {
    return this.countEnabled;
  }

  public void setCountEnabled(boolean countEnabled) {
    this.countEnabled = countEnabled;
  }

}
