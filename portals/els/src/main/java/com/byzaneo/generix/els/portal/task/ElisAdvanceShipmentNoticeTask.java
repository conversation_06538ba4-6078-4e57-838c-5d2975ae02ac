package com.byzaneo.generix.els.portal.task;

import static com.byzaneo.commons.ui.util.JSFHelper.sendFile;
import static com.byzaneo.generix.edocument.ui.DocumentViewerHandler.toURI;
import static com.byzaneo.generix.edocument.util.AsnXcblHelper.deleteDraftDocumentFileBeforeSending;
import static com.byzaneo.generix.edocument.util.XcblHelper.createOrUpdateXcblFile;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.createASNDocument;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.findCorrespondingBaseItemDetail;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.getASNItemDetailTypeFromASN;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.getASNNumberFromASN;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.getBuyerOrderResponseNumber;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.getOrderResponseNumberFromASNDetail;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.getPlaceOfDeliveryFromASN;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.getShippingReferencesFromASN;
import static com.byzaneo.generix.els.portal.task.util.ElisASNXcblHelper.initAdvanceShipmentNotice;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.DEFAULT_PLACE;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.findLatestOrderResponseForSelectedOrderNumber;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.findSameItemWithALowerDeliveryDate;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getNotShippedDetails;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getOrderReferenceFromOrdrsp;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getOrderResponseByOrderReferenceNumber;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getPlaceOfDeliveryFromOrdrsp;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getSplitQuantityList;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getUniqueDeliveryDatesFromSelectedProductLines;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.getValidOrderResponseItems;
import static com.byzaneo.generix.els.portal.task.util.ElisAdvanceShipmentNoticeTaskHelper.initDeliveryNote;
import static com.byzaneo.generix.els.portal.task.util.ElisOrderResponseTaskHelper.generateOrderResponseItemKey;
import static com.byzaneo.generix.util.OrganizationHelper.resolveUserOrganizationsQuery;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.in;
import static com.byzaneo.query.builder.Clauses.notEqual;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.util.QueryHelper.toBql;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.xtrade.xcbl.util.AsnHelper.getASNNumber;
import static java.util.Collections.emptyList;
import static java.util.Optional.ofNullable;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.*;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIData;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTimeComparator;
import org.primefaces.event.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.util.AsnXcblHelper;
import com.byzaneo.generix.els.portal.task.bean.ElisOrderResponseConfirmationConfiguration;
import com.byzaneo.generix.els.portal.task.service.ElisOrderResponseConfirmationService;
import com.byzaneo.generix.els.portal.task.util.*;
import com.byzaneo.generix.service.LockMechanismService;
import com.byzaneo.generix.service.TransformService.BirtOutoutFileType;
import com.byzaneo.generix.xcbl.portal.task.AbstractXcblDocumentTask;
import com.byzaneo.query.*;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.query.clause.Clause;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;

@Task(name = "ElisAdvanceShipmentNotice", library = "portal/specifics", version = "1.0.0")
@TaskViewModel(labelFamily = ElisAdvanceShipmentNoticeTask.LABEL_FAMILY)
public class ElisAdvanceShipmentNoticeTask extends AbstractXcblDocumentTask<AdvanceShipmentNotice> {

  private static final long serialVersionUID = 4371594706936694655L;

  private static final String MULTIPLE = "Multiple";

  public static final String LABEL_FAMILY = "elsasntsklbls";

  public static final String USER_FRIENDLY_MESSAGE_NAME = "elisasn";

  private static final String ORDER_RESPONSE_FROM = "_from";

  public static final String ORDER_RESPONSE_STATE = "_state";

  private static final String ORDER_RESPONSE_CREATION_DATE = "creationDate";

  private static final String ORDER_RESPONSE_REFNUM = "orderResponseHeader.orderReference.refNum";

  private transient boolean modeSelectASN = false;

  private transient AdvanceShipmentNotice selectedASN;

  private transient List<ASNItemDetailType> asnItemDetail;

  private transient List<ASNItemDetailType> filteredASN;

  private transient boolean modeSearchASN = false;

  private transient boolean modeCreateASN = false;

  private transient Set<String> availablePlaces;

  private transient Set<String> availableOrderNumbers;

  private transient List<String> availableDeliveryDates;

  private transient String selectedPlace;

  private transient String selectedOrderNumber;

  private transient String[] selectedDeliveryDates;

  private transient Date deliveryDate;

  private transient List<OrderResponse> orderResponses;

  private transient List<OriginalItemDetailWrapper> productLines;

  private transient OriginalItemDetailWrapper[] selectedOrdrspItems;

  private transient ElisOrderResponseConfirmationConfiguration orderResponseConfirmationConfiguration;

  private transient String deliveryNoteNumber = null;

  private transient List<OrderResponse> latestOrderResponses;

  private transient boolean onCreateASNBack = false;

  private transient boolean showASNSendSuccess = false;

  private transient String asnSendSuccessMessage;

  private transient boolean showSaveAsDraftSuccess = false;

  private transient String asnSaveAsDraftSuccessMessage;

  private transient AdvanceShipmentNotice asnDraftToDelete;

  private transient String draftConfirmationNumber;

  private transient boolean modeDraft = false;

  private transient AdvanceShipmentNotice selectedDraft;

  private transient boolean showDeleteDraftMessage = false;

  private transient String asnDeleteDraftMessage;

  private transient boolean validateASNFail = false;

  private transient String validateASNFailMessage;

  private transient boolean modeValidateASN = false;

  private transient boolean showDraftSendMessage = false;

  private transient String draftSendMessage;

  private transient boolean showDraftUpdatedMessage = false;

  private transient String draftUpdatedMessage;

  private transient boolean showDraftShippedMessage = false;

  private transient String draftShippedMessage;

  private transient boolean dispatchByOrderNumber;

  private transient DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

  private transient boolean modeDeliveryNotes;

  private transient boolean deliveryNoteInputChanged;

  /* Used for Step3 where deadlines are grouped by their delivery date */
  Map<String, List<OriginalItemDetailWrapper>> groupedDeadlines = new HashMap<String, List<OriginalItemDetailWrapper>>();

  /* Used for validating the list of selected delivery dates */
  private transient Map<ItemKey, List<String>> itemKeyDates = new HashMap<ItemKey, List<String>>();

  /* Used for validation on step2 */
  private transient Set<Date> sortedDates = new TreeSet<Date>();

  private transient boolean foundDateLowerThanToday;

  @Autowired
  @Qualifier(DocumentViewerService.SERVICE_NAME)
  private transient DocumentViewerService documentViewerService;

  @Autowired
  @Qualifier(ElisOrderResponseConfirmationService.SERVICE_NAME)
  private transient ElisOrderResponseConfirmationService orderResponseConfirmationService;

  @Autowired
  @Qualifier(LockMechanismService.SERVICE_NAME)
  private transient LockMechanismService lockMechanismService;

  @Autowired
  private transient AsnService asnService;

  @PostConstruct
  public void init() {
    if (!isBackOfficeUser()) {
      // resolves organization codes
      this.resolveOrganizationCodes();
      this.orderResponseConfirmationConfiguration = this.orderResponseConfirmationService.getConfiguration(this.partner);
    }
  }

  @Override
  public Logs importDocuments(List<InputStream> list) {
    return eDocumentService.importOrderResponses(company, partner, list);
  }

  @Override
  public String getPathDescriptor() {
    if (pathDescriptor == null)
      this.pathDescriptor = "descriptors/elis-advanceshipmentnotice-columns.xml";
    return pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  public void onDownload() {
    sendFile(download.getFile(), download.getName(), true);
  }

  @Override
  public String getUserFriendlyMessageType() {
    return USER_FRIENDLY_MESSAGE_NAME;
  }

  @Override
  public void onSelect(Instance instance) {
    this.onSelect(instance, (AdvanceShipmentNotice) getSelected(), null);
  }

  @Override
  public void onSelect(Instance instance, AdvanceShipmentNotice indexable, PropertyDescriptor property) {
    onViewDetails(indexable);
  }

  @Override
  public QueryModel getSearchModel() {
    return super.getSearchModelAdvancedSearch();
  }

  @Override
  public Map<Object, String> getValues(String label) {
    return getStatusValues(AdvanceShipmentNotice.class, label);
  }

  @Override
  public Query resolveSearchQuery() {
    final QueryBuilder qb = createBuilder(this.resolveBaseQuery());

    // search clauses
    if (advSearch) {
      final List<Clause> clauses = computeAdvSearchClauses();
      qb.and(clauses);
    }

    // Since Mongo can not create an index name which has more than 127 byte, so i had to add those 2 fields manually in order for the
    // quick search to also search by those 2.
    if (this.userQuery != null && this.userQuery.toString()
        .contains(Constants.TEXT_FIELD)) {
      String value = this.userQuery.toString()
          .replace("_text_ ~ ", "");
      String replacement = "(" + this.userQuery.toString() + " OR " +
          "asnDetail.listOfASNItemDetail.asnItemDetail.asnBaseItemDetail.asnLineItemReferences.purchaseOrderReference.buyerOrderNumber ~ " +
          value +
          " OR asnDetail.listOfASNItemDetail.asnItemDetail.asnBaseItemDetail.itemIdentifiers.partNumbers.buyerPartNumber.partID ~ " +
          value + ")";
      String query = this.userQuery.toString()
          .replace(this.userQuery.toString(), replacement);
      qb.append(toQuery(query));
    }
    else {
      // search query
      qb.append(userQuery);
    }

    qb.append(this.userQueryQuickSearch);

    String q = toBql(qb.query());

    // resolve property name for search
    q = StringUtils.replaceEach(q, new String[] { "[\'", "\']", "[", "]" }, new String[] { ".", "", ".", "" });

    // search query
    return toQuery(q);
  }

  public void onViewDetails(AdvanceShipmentNotice asn) {
    resetMesages();
    this.modeSelectASN = true;
    this.selectedASN = asn;
    this.filteredASN = null;
  }

  public void onBack() {
    this.lockMechanismService.unlock(getCurrentUser().getLogin());
    this.availableOrderNumbers = null;
    reset();
  }

  /* Return to the selection of order number and the list of delivery dates */
  public void onBackStep1() {
    this.modeSearchASN = true;
    this.dispatchByOrderNumber = true;
    this.modeCreateASN = false;
  }

  /* Return to the list of selectable details */
  public void onBackStep2() {
    keepDeliveryNoteInputValue();
    this.modeDeliveryNotes = false;
    this.modeCreateASN = true;
  }

  @Override
  public void reset() {
    this.lockMechanismService.unlock(getCurrentUser().getLogin());
    this.modeSelectASN = false;
    this.filteredASN = null;
    this.modeSearchASN = false;
    this.modeCreateASN = false;
    this.productLines = null;
    this.onCreateASNBack = false;
    this.selectedPlace = null;
    this.deliveryDate = null;
    this.modeDraft = false;
    this.draftConfirmationNumber = null;
    this.deliveryNoteNumber = null;
    this.selectedDraft = null;
    this.modeValidateASN = false;
    this.dispatchByOrderNumber = false;
    this.selectedOrderNumber = null;
    this.selectedDeliveryDates = null;
  }

  public void resetMesages() {
    this.showASNSendSuccess = false;
    this.showSaveAsDraftSuccess = false;
    this.showDeleteDraftMessage = false;
    this.validateASNFail = false;
    this.validateASNFailMessage = null;
    this.showDeleteDraftMessage = false;
    this.showDraftSendMessage = false;
    this.showDraftUpdatedMessage = false;
    this.showDraftShippedMessage = false;
  }

  public void resetProductLines() {
    this.productLines = null;
    this.selectedOrdrspItems = null;
  }

  public void resetSelectedItems() {
    this.selectedOrdrspItems = null;
    this.deliveryNoteNumber = null;
    reset();
  }

  public void onDispatch(String dispatchMode) {
    resetMesages();
    this.modeSearchASN = true;
    // dispatch by order number
    if (dispatchMode.equals("orderNumber")) {
      dispatchByOrderNumber = true;
      this.availableOrderNumbers = null;
      this.orderResponses = null;
    }
    // dispatch by delivery date and place
    else {
      dispatchByOrderNumber = false;
    }
  }

  @Override
  public String getViewerURI(AdvanceShipmentNotice indexable) {
    return toURI(indexable,
        this.getInstance(),
        this.getCurrentUser()
            .getLocale(),
        this.viewerBaseName(indexable),
        false, false, null, BirtOutoutFileType.PDF);
  }

  public boolean getModeSelectASN() {
    return this.modeSelectASN;
  }

  public void setModeSelectASN(boolean modeSelectASN) {
    this.modeSelectASN = modeSelectASN;
  }

  public AdvanceShipmentNotice getSelectedASN() {
    return this.selectedASN;
  }

  public List<ASNItemDetailType> getFilteredASN() {
    return filteredASN;
  }

  public void setFilteredASN(List<ASNItemDetailType> filteredASN) {
    this.filteredASN = filteredASN;
  }

  public List<ASNItemDetailType> getAsnItemDetail() {
    if (this.selectedASN == null) {
      return null;
    }
    return this.selectedASN.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail();
  }

  public void setAsnItemDetail(List<ASNItemDetailType> asnItemDetail) {
    this.asnItemDetail = asnItemDetail;
  }

  public boolean getModeSearchASN() {
    return modeSearchASN;
  }

  public void setModeSearchASN(boolean modeSearchASN) {
    this.modeSearchASN = modeSearchASN;
  }

  public boolean getModeCreateASN() {
    return modeCreateASN;
  }

  public void setModeCreateASN(boolean modeCreateASN) {
    this.modeCreateASN = modeCreateASN;
  }

  public ElisOrderResponseConfirmationConfiguration getOrderResponseConfirmationConfiguration() {
    return orderResponseConfirmationConfiguration;
  }

  public void setOrderResponseConfirmationConfiguration(ElisOrderResponseConfirmationConfiguration orderResponseConfirmationConfiguration) {
    this.orderResponseConfirmationConfiguration = orderResponseConfirmationConfiguration;
  }

  public String getDeliveryNoteNumber() {
    return deliveryNoteNumber;
  }

  public void setDeliveryNoteNumber(String deliveryNoteNumber) {
    this.deliveryNoteNumber = deliveryNoteNumber;
  }

  public List<OrderResponse> getLatestOrderResponses() {
    return latestOrderResponses;
  }

  public void setLatestOrderResponses(List<OrderResponse> latestOrderResponses) {
    this.latestOrderResponses = latestOrderResponses;
  }

  public boolean isShowASNSendSuccess() {
    return showASNSendSuccess;
  }

  public void setShowASNSendSuccess(boolean showASNSendSuccess) {
    this.showASNSendSuccess = showASNSendSuccess;
  }

  public String getAsnSendSuccessMessage() {
    return asnSendSuccessMessage;
  }

  public void setAsnSendSuccessMessage(String asnSendSuccessMessage) {
    this.asnSendSuccessMessage = asnSendSuccessMessage;
  }

  public boolean getShowSaveAsDraftSuccess() {
    return showSaveAsDraftSuccess;
  }

  public void setShowSaveAsDraftSuccess(boolean showSaveAsDraftSuccess) {
    this.showSaveAsDraftSuccess = showSaveAsDraftSuccess;
  }

  public String getAsnSaveAsDraftSuccessMessage() {
    return asnSaveAsDraftSuccessMessage;
  }

  public void setAsnSaveAsDraftSuccessMessage(String asnSaveAsDraftSuccessMessage) {
    this.asnSaveAsDraftSuccessMessage = asnSaveAsDraftSuccessMessage;
  }

  public AdvanceShipmentNotice getAsnDraftToDelete() {
    return asnDraftToDelete;
  }

  public void setAsnDraftToDelete(AdvanceShipmentNotice asnDraftToDelete) {
    this.asnDraftToDelete = asnDraftToDelete;
  }

  public String getDraftConfirmationNumber() {
    return draftConfirmationNumber;
  }

  public void setDraftConfirmationNumber(String draftConfirmationNumber) {
    this.draftConfirmationNumber = draftConfirmationNumber;
  }

  public boolean getModeDraft() {
    return modeDraft;
  }

  public void setModeDraft(boolean modeDraft) {
    this.modeDraft = modeDraft;
  }

  public AdvanceShipmentNotice getSelectedDraft() {
    return selectedDraft;
  }

  public void setSelectedDraft(AdvanceShipmentNotice selectedDraft) {
    this.selectedDraft = selectedDraft;
  }

  public boolean isShowDeleteDraftMessage() {
    return showDeleteDraftMessage;
  }

  public void setShowDeleteDraftMessage(boolean showDeleteDraftMessage) {
    this.showDeleteDraftMessage = showDeleteDraftMessage;
  }

  public String getAsnDeleteDraftMessage() {
    return asnDeleteDraftMessage;
  }

  public void setAsnDeleteDraftMessage(String asnDeleteDraftMessage) {
    this.asnDeleteDraftMessage = asnDeleteDraftMessage;
  }

  public boolean getValidateASNFail() {
    return validateASNFail;
  }

  public void setValidateASNFail(boolean validateASNFail) {
    this.validateASNFail = validateASNFail;
  }

  public String getValidateASNFailMessage() {
    return validateASNFailMessage;
  }

  public void setValidateASNFailMessage(String validateASNFailMessage) {
    this.validateASNFailMessage = validateASNFailMessage;
  }

  public boolean getModeValidateASN() {
    return modeValidateASN;
  }

  public void setModeValidateASN(boolean modeValidateASN) {
    this.modeValidateASN = modeValidateASN;
  }

  public boolean isModeDeliveryNotes() {
    return modeDeliveryNotes;
  }

  public void setModeDeliveryNotes(boolean modeDeliveryNotes) {
    this.modeDeliveryNotes = modeDeliveryNotes;
  }

  public boolean isDeliveryNoteInputChanged() {
    return deliveryNoteInputChanged;
  }

  public void setDeliveryNoteInputChanged(boolean deliveryNoteInputChanged) {
    this.deliveryNoteInputChanged = deliveryNoteInputChanged;
  }

  public boolean getShowDraftSendMessage() {
    return showDraftSendMessage;
  }

  public void setShowDraftSendMessage(boolean showDraftSendMessage) {
    this.showDraftSendMessage = showDraftSendMessage;
  }

  public String getDraftSendMessage() {
    return draftSendMessage;
  }

  public void setDraftSendMessage(String draftSendMessage) {
    this.draftSendMessage = draftSendMessage;
  }

  public boolean getShowDraftUpdatedMessage() {
    return showDraftUpdatedMessage;
  }

  public void setShowDraftUpdatedMessage(boolean showDraftUpdatedMessage) {
    this.showDraftUpdatedMessage = showDraftUpdatedMessage;
  }

  public boolean isFoundDateLowerThanToday() {
    return foundDateLowerThanToday;
  }

  public void setFoundDateLowerThanToday(boolean foundDateLowerThanToday) {
    this.foundDateLowerThanToday = foundDateLowerThanToday;
  }

  public String getDraftUpdatedMessage() {
    return draftUpdatedMessage;
  }

  public void setDraftUpdatedMessage(String draftUpdatedMessage) {
    this.draftUpdatedMessage = draftUpdatedMessage;
  }

  public boolean getShowDraftShippedMessage() {
    return showDraftShippedMessage;
  }

  public void setShowDraftShippedMessage(boolean showDraftShippedMessage) {
    this.showDraftShippedMessage = showDraftShippedMessage;
  }

  public String getDraftShippedMessage() {
    return draftShippedMessage;
  }

  public void setDraftShippedMessage(String draftShippedMessage) {
    this.draftShippedMessage = draftShippedMessage;
  }

  public boolean isDispatchByOrderNumber() {
    return dispatchByOrderNumber;
  }

  public void setDispatchByOrderNumber(boolean dispatchByOrderNumber) {
    this.dispatchByOrderNumber = dispatchByOrderNumber;
  }

  public List<OrderResponse> getValidOrderResponses() {
    return getValidOrderResponses(this.currentSession()
        .getPartner()
        .getCode(), createBuilder(resolveSearchQueryForSentStatus(false)).query());
  }

  public Query resolveSearchQueryForSentStatus(boolean isSent) {
    // creates query from the edition query (if any)
    final QueryBuilder qb = resolveUserOrganizationsQuery(companyCode, perimeter, getQuery(), getQueryFiedOwners(),
        getQueryFiedFrom(), getQueryFiedTo(), getQueryFieldThirdParty());
    if (isSent)
      qb.and(equal(ORDER_RESPONSE_STATE, "SENT"));
    else
      qb.and(notEqual(ORDER_RESPONSE_STATE, "SENT"));

    // sorting
    if (getSortBy() != null) {
      if (isSortAsc()) {
        qb.asc(getSortBy());
      }
      else {
        qb.desc(getSortBy());
      }
    }

    return qb.query();
  }

  // Returns the order responses that do not have SHIPPED status that correspond to the current user
  // had to create this method with parameters so as to be able to do integration test on it
  public List<OrderResponse> getValidOrderResponses(String from, Query query) {
    SortOperation sortOperation = Aggregation.sort(Sort.by(Direction.DESC, ORDER_RESPONSE_CREATION_DATE));
    GroupOperation groupOperation = Aggregation.group(ORDER_RESPONSE_REFNUM)
        .first("$$ROOT")
        .as("doc");
    ReplaceRootOperation replaceRoot = Aggregation.replaceRoot()
        .withValueOf("$doc");
    List<OrderResponse> list = this.eDocumentService.aggregate(
        OrderResponse.class,
        OrderResponse.class,
        query,
        sortOperation, groupOperation, replaceRoot);

    // had to do another query to obtain all order responses which are sent for the order responses from the "list" so as to remove order
    // responses from list based on ORDERRESPONSEREFNUM
    // could not do this in a single query because the result of an aggregation operation is sent to the next one an so on
    return verifyOrderResponsesNotToBeSent(list, from, resolveSearchQueryForSentStatus(true));
  }

  public List<OrderResponse> verifyOrderResponsesNotToBeSent(List<OrderResponse> list, String from, Query query) {
    // this method verifies if there is an order response which is sent based on ORDERRESPONSEREFNUM
    if (list.size() > 0) {
      Map<String, Date> ordrspMap = new HashMap<String, Date>();
      list.forEach(ordrsp -> {
        String orderNumber = getOrderReferenceFromOrdrsp(ordrsp);
        if (StringUtils.isNotEmpty(orderNumber)) {
          ordrspMap.put(orderNumber, ordrsp.getCreationDate());
        }
      });
      Criteria[] criterias = ordrspMap.entrySet()
          .stream()
          .map(entry -> Criteria.where(ORDER_RESPONSE_REFNUM)
              .is(entry.getKey())
              .and(ORDER_RESPONSE_CREATION_DATE)
              .gt(entry.getValue()))
          .toArray(Criteria[]::new);

      List<String> sentOrdrspsByOrderNumber = this.eDocumentService.aggregate(OrderResponse.class, OrderResponse.class,
          query)
          .stream()
          .map(ordrsp -> getOrderReferenceFromOrdrsp(ordrsp))
          .collect(Collectors.toList());
      return list.stream()
          .filter(ordrsp -> !sentOrdrspsByOrderNumber.contains(getOrderReferenceFromOrdrsp(ordrsp)))
          .collect(Collectors.toList());
    }
    return list;
  }

  // This function searches in the response orders returned by getValidOrderResponses and computes the list of unique places of delivery
  public Set<String> getAvailablePlaces() {
    if (this.availablePlaces != null) {
      if (this.availablePlaces.size() == 1) {
        this.selectedPlace = this.availablePlaces.iterator()
            .next();
      }
      return this.availablePlaces;
    }
    this.availablePlaces = new TreeSet<String>();
    if (this.orderResponses == null) {
      this.orderResponses = getValidOrderResponses();
    }
    this.availablePlaces = this.orderResponses.stream()
        .map(ordrsp -> getPlaceOfDeliveryFromOrdrsp(ordrsp))
        .collect(Collectors.toSet());
    // if there is only one place available than the place of delivery must be automatically completed
    if (this.availablePlaces.size() == 1) {
      this.selectedPlace = this.availablePlaces.iterator()
          .next();
    }
    return this.availablePlaces;
  }

  public Set<String> getAvailableOrderNumbers() {
    if (this.availableOrderNumbers != null) {
      return this.availableOrderNumbers;
    }

    // sort ASC
    this.availableOrderNumbers = new TreeSet<String>();

    if (this.orderResponses == null) {
      this.orderResponses = getValidOrderResponses();
    }

    // remove the order responses which for example have 3 product lines already shipped and the 4th line is not yet confirmed
    this.orderResponses = filterOrderResponses();

    this.availableOrderNumbers = this.orderResponses.stream()
        .map(ordrsp -> getOrderReferenceFromOrdrsp(ordrsp))
        .collect(Collectors.toSet());
    return this.availableOrderNumbers;
  }

  public List<String> getAvailableDeliveryDates() {
    if (this.selectedOrderNumber == null)
      return null;
    if (this.orderResponses == null)
      this.orderResponses = getValidOrderResponses();

    // init available delivery dates (sorted in ASC order)
    Set<Date> sortedDates = getSortedDates();
    this.availableDeliveryDates = sortedDates.stream()
        .map(d -> dateFormat.format(d))
        .collect(Collectors.toList());

    // if only one delivery date is available, it is selected by default
    if (this.availableDeliveryDates.size() == 1) {
      this.selectedDeliveryDates = new String[] { this.availableDeliveryDates.get(0) };
    }

    // check if there is any delivery date < today
    Date today = new Date();
    if (sortedDates.stream()
        .filter(date -> DateTimeComparator.getDateOnlyInstance()
            .compare(date, today) < 0)
        .findAny()
        .isPresent()) {
      this.foundDateLowerThanToday = true;
    }
    else {
      this.foundDateLowerThanToday = false;
    }

    return this.availableDeliveryDates;
  }

  /* Get the available delivery dates is ASC order and in String format */
  private Set<Date> getSortedDates() {
    Set<Date> availableDates = new TreeSet<Date>();
    OrderResponse orderResponseForOrderNumber = getOrderResponseByOrderReferenceNumber(this.orderResponses, this.selectedOrderNumber);
    List<OrderResponseItemDetailType> details = getNotShippedDetails(orderResponseForOrderNumber);
    for (OrderResponseItemDetailType orderResponseItemDetailType : details) {
      List<String> dates = new ArrayList<String>();
      for (SplitQuantityType splitQuantity : getSplitQuantityList(orderResponseItemDetailType)) {
        if (ShipmentStatusEventCodeType.NOT_YET_SHIPPED.equals(splitQuantity.getShipmentStatusEventCoded())) {
          // used truncate to discard Time(hh:mm:ss) from date
          availableDates.add(DateUtils.truncate(splitQuantity.getEstimatedDeliveryDate(), Calendar.DATE));
          dates.add(dateFormat.format(splitQuantity.getEstimatedDeliveryDate()));
        }
      }
      // store a map of itemkey and its sorted dates
      this.itemKeyDates
          .put(ElisOrderResponseTaskHelper.generateOrderResponseItemKey(orderResponseItemDetailType.getOriginalItemDetailWithChanges()
              .getBaseItemDetail()), dates);
    }
    return availableDates;
  }

  public List<OriginalItemDetailWrapper> onSearchOrdersToShip() {
    // productLines are already available via DISPATCH BY ORDER NUMBER
    if (this.productLines != null) {
      return this.productLines;
    }
    this.productLines = new ArrayList<>();
    List<OrderResponse> searchList = this.latestOrderResponses == null ? this.orderResponses : this.latestOrderResponses;

    // there is only one order response on Create ASN from Order mode
    if (this.modeDeliveryNotes) {
      searchList = new ArrayList<OrderResponse>();
      OrderResponse ordrsp = getOrderResponseByOrderReferenceNumber(
          this.latestOrderResponses == null ? this.orderResponses : this.latestOrderResponses, this.selectedOrderNumber);
      if (ordrsp != null)
        searchList.add(ordrsp);
      else searchList = Collections.emptyList();
    }

    // Draft mode is treated differently
    if (!this.modeDraft && searchList.stream()
        .filter(ordrsp -> isNotLockedByCurrentUser(ordrsp, getCurrentUser()
            .getLogin()))
        .findAny()
        .isPresent()) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".order_locked");
      this.reset();
      return null;
    }

    searchList
        .forEach(ordrsp ->
        {
          if (selectedPlace.equals(getPlaceOfDeliveryFromOrdrsp(ordrsp))) {
            for (OrderResponseItemDetailType detail : ordrsp.getOrderResponseDetail()
                .getListOfOrderResponseItemDetail()
                .getOrderResponseItemDetail()) {
              for (SplitQuantityType quantityType : getSplitQuantityList(detail)) {
                // we will display only the first splitQuantity that is not shipped and it's date corresponds to the specified date
                if (!ShipmentStatusEventCodeType.SHIPMENT_COMPLETE
                    .equals(quantityType.getShipmentStatusEventCoded())) {
                  Date estimatedDeliveryDate = quantityType.getEstimatedDeliveryDate();
                  // verify if the selected date of delivery and the order response delivery date match
                  if (DateUtils.isSameDay(deliveryDate, estimatedDeliveryDate)) {
                    this.productLines.add(new OriginalItemDetailWrapper(ordrsp.getOrderResponseHeader(),
                        detail.getOriginalItemDetailWithChanges()));
                    // lock this order response by this user
                    if (!this.modeDraft) {
                      this.lockMechanismService.lock(ordrsp.getId(), getCurrentUser()
                          .getLogin());
                    }
                  }
                  break;
                }
              }
            }
          }
        });
    // error message is shown in case the list is empty which means that there is no order response that has that selected delivery place
    // and delivery date
    if (this.productLines.size() == 0 && this.selectedDraft == null && !this.modeValidateASN) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, "elsasntsklbls.createASN_deliveryPointOrDeliveryDateErrorMessage");
      this.deliveryDate = null;
      this.productLines = null;
    }
    else {
      onCreateASN();
    }
    return this.productLines;
  }

  public List<OriginalItemDetailWrapper> getDeliveryNoteNumbers() {
    if (this.productLines == null)
      return Collections.emptyList();

    Set<String> deliveryDateList = new TreeSet<String>();
    this.productLines.stream()
        .filter(item -> item.isSelected())
        .map(OriginalItemDetailWrapper::getDeliveryNoteNumber)
        .map(DeliveryNoteNumber::getDeliveryDate)
        .forEach(d -> deliveryDateList.add(dateFormat.format(d)));

    int confirmationNumber = this.orderResponseConfirmationConfiguration.getConfirmationNumber();
    List<OriginalItemDetailWrapper> result = new ArrayList<OriginalItemDetailWrapper>();
    for (String date : deliveryDateList) {
      List<OriginalItemDetailWrapper> items = groupedDeadlines.get(date);
      if (!items.isEmpty()) {
        // init the confirmation number for item wrapper
        items.get(0)
            .setConfirmationNumber(StringUtils.leftPad(Integer.toString(confirmationNumber), 8, "0"));
        result.add(items.get(0));
        confirmationNumber++;
      }
    }
    return result;
  }

  /** Initialise DeliveryNoteNumber objects to display on step 3 */
  private void generateDeliveryNoteNumbers() {
    for (OriginalItemDetailWrapper productLine : productLines) {
      if (productLine.getDeliveryNoteNumber() == null)
        initDeliveryNote(productLine);
    }
  }

  /** Can be triggered on Step 2 after the user selects a list of details from which to generate delivery note numbers */
  public void onDisplayDeliveryNoteNumbers() {
    if (!this.productLines.stream()
        .filter(item -> item.isSelected())
        .findAny()
        .isPresent()) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".select_asn_error");
      return;
    }
    if (!validateSelectedItems()) {
      return;
    }
    keepDeliveryNoteInputValue();
    this.modeCreateASN = false;
    this.dispatchByOrderNumber = true;
    this.modeDeliveryNotes = true;
  }

  /** Can be triggered on Step 1 after the user has to fill an order number and delivery dates */
  public void onGenerateProductLines() {
    if (this.selectedDeliveryDates == null || this.selectedDeliveryDates.length == 0) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".step1_error_fill");
      return;
    }

    OrderResponse latest = findLatestOrderResponseForSelectedOrderNumber(getValidOrderResponses(),
        this.orderResponses,
        this.selectedOrderNumber);
    if (latest == null) {
      this.validateASNFail = true;
      this.validateASNFailMessage = MessageHelper.getMessage(LABEL_FAMILY + ".asn_validate_fail",
          "Fail to generate advance shipment notice", this.currentSession()
              .getLocale());
      this.reset();
      this.orderResponses = null;
      return;
    }

    if (isNotLockedByCurrentUser(
        getOrderResponseByOrderReferenceNumber(this.orderResponses, this.selectedOrderNumber), getCurrentUser().getLogin())) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".order_locked");
      this.reset();
      return;
    }
    else {
      lockMechanismService.lock(getOrderResponseByOrderReferenceNumber(this.orderResponses, this.selectedOrderNumber).getId(),
          getCurrentUser().getLogin());
    }
    List<ItemKey> validItemKeys = validateDeliveryDates();
    if (CollectionUtils.isEmpty(validItemKeys)) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".step1_selected_deliveryDates_wrong");
      return;
    }
    if (productLines == null || productLines.isEmpty()) {
      OrderResponse orderResponse = getOrderResponseByOrderReferenceNumber(this.orderResponses, this.selectedOrderNumber);
      List<OrderResponseItemDetailType> details = getNotShippedDetails(orderResponse);
      this.productLines = new ArrayList<OriginalItemDetailWrapper>();
      this.groupedDeadlines = new HashMap<String, List<OriginalItemDetailWrapper>>();
      List<String> selectedDeliveryDatesList = this.selectedDeliveryDates != null ? Arrays.asList(this.selectedDeliveryDates)
          : Collections.emptyList();
      List<OrderResponseItemDetailType> validDetails = getValidOrderResponseItems(validItemKeys, details);
      generateProductLines(orderResponse, selectedDeliveryDatesList, validDetails);
    }
    onCreateASN();
    generateDeliveryNoteNumbers();
    this.modeSearchASN = false;
  }

  /**
   * Verifies that for a product which can have multiple deadlines, those deadlines are selected by delivery date ASC
   * 
   * @return
   */
  private boolean validateSelectedItems() {
    List<OriginalItemDetailWrapper> selectedProductLines = productLines.stream()
        .filter(item -> item.isSelected())
        .collect(Collectors.toList());
    boolean selectionIsValid = true;
    for (OriginalItemDetailWrapper item : selectedProductLines) {
      OriginalItemDetailWrapper wrapper = searchItemWithPreviousDate(generateOrderResponseItemKey(item.getOrderResponseItemDetail()
          .getBaseItemDetail()), item.getDeliveryNoteNumber()
              .getDeliveryDate(),
          this.productLines);
      // that wrapper (productLine) must be selected
      if (wrapper != null && !wrapper.isSelected()) {
        MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".deadline_must_be_selected",
            wrapper.getOrderResponseItemDetail()
                .getBaseItemDetail()
                .getItemIdentifiers()
                .getItemDescription(),
            wrapper.getDeadlineNumber(),
            dateFormat.format(wrapper.getDeliveryNoteNumber()
                .getDeliveryDate()),
            item.getDeadlineNumber(), dateFormat.format(item.getDeliveryNoteNumber()
                .getDeliveryDate()));
        selectionIsValid = false;
      }
    }
    return selectionIsValid;
  }

  /**
   * For a selected item identified by its ItemKey generated, searches if there is a deadline (SplitQuantity) with a previous date than the
   * one selected and if that is also selected
   * 
   * @param itemKey
   * @param itemDate
   * @param productLines
   * @return
   */
  private OriginalItemDetailWrapper searchItemWithPreviousDate(ItemKey itemKey, Date itemDate,
      List<OriginalItemDetailWrapper> selectedItems) {
    return productLines.stream()
        .filter(wrapper -> findSameItemWithALowerDeliveryDate(wrapper, itemKey, itemDate))
        .collect(Collectors.toList())
        .stream()
        .max((firstItem, secondItem) -> DateTimeComparator.getDateOnlyInstance()
            .compare(firstItem.getDeliveryNoteNumber()
                .getDeliveryDate(),
                secondItem.getDeliveryNoteNumber()
                    .getDeliveryDate()))
        .orElse(null);
  }

  /**
   * Returns the ItemKey for valid deadlines. A valid deadline is checked by {@code checkItem} method
   * 
   * @return
   */
  private List<ItemKey> validateDeliveryDates() {
    boolean foundInvalid = false;
    boolean foundDateBeforeToday = false;
    List<ItemKey> validItemKeys = new ArrayList<ItemKey>();
    for (Map.Entry<ItemKey, List<String>> itemKey : itemKeyDates.entrySet()) {
      // do not check items which do not contain any date present in the selected delivery dates
      if (Collections.disjoint(itemKey.getValue(), Arrays.asList(this.selectedDeliveryDates))) {
        continue;
      }
      // some selected delivery date is lower than today's date
      if (findDateLowerThanToday(itemKey.getValue(), dateFormat.format(new Date()))) {
        foundDateBeforeToday = true;
      }
      // validate
      if (checkItem(this.selectedDeliveryDates, itemKey.getValue())) {
        validItemKeys.add(itemKey.getKey());
      }
      else {
        foundInvalid = true;
      }
    }
    if (foundDateBeforeToday) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_WARN, LABEL_FAMILY + ".found_date_before_today");
    }
    if (!validItemKeys.isEmpty() && foundInvalid) {
      // Print a warning message if only some of the selected delivery dates are good and it can continue to the next step
      // Empty validItemKeys will ignore this and generate a different error message
      MessageHelper.addMessage(FacesMessage.SEVERITY_WARN, LABEL_FAMILY + ".selected_delivery_dates_error");
    }
    return validItemKeys;
  }

  /**
   * Verifies the user selected delivery dates against the available delivery dates for each order response item. In this case, the delivery
   * dates of an order response item are represented by a map of ItemKey (to identify the order response item) and a list of its available
   * delivery dates
   * 
   * @param selectedDeliveryDates
   * @param itemKey
   * @return
   */
  private boolean checkItem(String[] selectedDeliveryDates, List<String> itemDates) {
    if (selectedDeliveryDates == null || itemDates == null)
      return false;
    boolean shouldNotFindNext = false;
    boolean isValid = true;
    // no delivery dates in common
    if (Collections.disjoint(Arrays.asList(selectedDeliveryDates), itemDates))
      return false;
    if (findDateLowerThanToday(itemDates, dateFormat.format(new Date()))) {
      return false;
    }
    for (String dateString : itemDates) {
      if (Arrays.asList(selectedDeliveryDates)
          .contains(dateString)) {
        if (shouldNotFindNext == true) {
          return false;
        }
      }
      else {
        // once a date from the deadlines is not in the selected delivery dates, it should not find another date because the dates must be
        // selected in sequence: for example for selected delivery dates = [1,3] and itemDates=[1,2,3,4], this code is reached when item
        // date is 2
        shouldNotFindNext = true;
      }
    }
    return isValid;
  }

  private boolean findDateLowerThanToday(List<String> dates, String todayAsString) {
    return dates
        .stream()
        .filter(strDate ->
        {
          try {
            return (dateFormat.parse(strDate))
                .compareTo(dateFormat.parse(todayAsString)) < 0;
          }
          catch (ParseException ex) {
            throw new RuntimeException(ex);
          }
        })
        .findAny()
        .isPresent();
  }

  public void onCreateASN() {
    resetMesages();
    this.modeCreateASN = true;
  }

  public Date getCorrectEstimatedDeliveryDate(OriginalItemDetailWrapper wrapper) {
    if (this.dispatchByOrderNumber) {
      Date estimatedDeliveryDate = Optional.ofNullable(wrapper)
          .map(OriginalItemDetailWrapper::getSplitQuantity)
          .map(SplitQuantityType::getEstimatedDeliveryDate)
          .orElse(null);
      if (estimatedDeliveryDate != null && this.availableDeliveryDates != null &&
          this.availableDeliveryDates.contains(dateFormat.format(estimatedDeliveryDate))) {
        return estimatedDeliveryDate;
      }
    }
    else {
      return getSplitQuantityList(wrapper).stream()
          .map(SplitQuantityType::getEstimatedDeliveryDate)
          .filter(estimatedDeliveryDate -> DateUtils.isSameDay(deliveryDate, estimatedDeliveryDate))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  public String getCorrectQuantity(OriginalItemDetailWrapper wrapper) {
    if (this.dispatchByOrderNumber) {
      return Optional.ofNullable(wrapper)
          .map(OriginalItemDetailWrapper::getSplitQuantity)
          .map(SplitQuantityType::getSplitQuantity)
          .map(QuantityType::getQuantityValue)
          .map(QuantityValueType::getValue)
          .map(BigDecimal::intValue)
          .map(intVal -> Integer.toString(intVal))
          .map(str -> str.concat(" ")
              .concat(Optional.ofNullable(wrapper)
                  .map(OriginalItemDetailWrapper::getSplitQuantity)
                  .map(SplitQuantityType::getSplitQuantity)
                  .map(QuantityType::getUnitOfMeasurement)
                  .map(UnitOfMeasurementType::getUOMCodedOther)
                  .orElse(StringUtils.EMPTY)))
          .orElse(StringUtils.EMPTY);
    }
    else {
      BigDecimal quantity = null;
      String uom = "";
      List<SplitQuantityType> quantities = getSplitQuantityList(wrapper);
      for (int i = 0; i <= quantities.size(); i++) {
        if (DateUtils.isSameDay(deliveryDate, quantities.get(i)
            .getEstimatedDeliveryDate())) {
          quantity = quantities.get(i)
              .getSplitQuantity()
              .getQuantityValue()
              .getValue();
          uom = quantities.get(i)
              .getSplitQuantity()
              .getUnitOfMeasurement()
              .getUOMCodedOther();
          break;
        }
      }
      return quantity != null ? Integer.toString(quantity.intValue()) + " " + uom : StringUtils.EMPTY;
    }
  }

  public void incrementASNNumber() {
    // increment current order confirmation number
    this.orderResponseConfirmationConfiguration.setConfirmationNumber(
        this.orderResponseConfirmationConfiguration.getConfirmationNumber() + 1);
    this.orderResponseConfirmationService.saveConfiguration(this.partner, this.orderResponseConfirmationConfiguration);
  }

  public void onSendASN() {
    try {
      createASN(false, null);
      if (!this.validateASNFail) {
        updateOrderResponsesStatuses();
        // in case some order responses have been shipped, they will no longer be kept in memory
        this.orderResponses = this.orderResponses.stream()
            .filter(orderResponse -> !orderResponse.getStatusAsEnumValue()
                .equals(DocumentStatus.SENT))
            .collect(Collectors.toList());
      }
      else {
        this.orderResponses = getValidOrderResponses();
      }
      // when for a place of delivery there was shipped the last quantity the corresponding place of delivery should not appear anymore
      this.availablePlaces = null;
      this.availablePlaces = getAvailablePlaces();
      reset();
    }
    catch (Exception e) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, "elsasntsklbls.asn_send_error_msg");
      log.error("Error sending advance shipment notice: ", e);
    }
    finally {
      this.lockMechanismService.unlock(getCurrentUser().getLogin());
    }
  }

  public void onSendMultipleASN() throws Exception {
    try {
      keepDeliveryNoteInputValue();
      if (!validateRequiredDeliveryNoteInput()) {
        MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".required_delivery_note");
        return;
      }
      Set<Date> dates = getUniqueDeliveryDatesFromSelectedProductLines(this.productLines);
      for (Date date : dates) {
        List<OriginalItemDetailWrapper> items = groupedDeadlines.get(dateFormat.format(date))
            .stream()
            .filter(wrapper -> wrapper.isSelected())
            .collect(Collectors.toList());
        if (items.isEmpty())
          continue;
        this.selectedOrdrspItems = items.toArray(new OriginalItemDetailWrapper[0]);
        this.deliveryNoteNumber = items.get(0)
            .getDeliveryNoteNumber()
            .getDeliveryNote();
        this.deliveryDate = items.get(0)
            .getDeliveryNoteNumber()
            .getDeliveryDate();
        this.selectedPlace = getPlaceOfDeliveryFromOrdrsp(
            getOrderResponseByOrderReferenceNumber(this.orderResponses, this.selectedOrderNumber));
        createASN(false, items.get(0)
            .getConfirmationNumber());
        // update order response status
        if (!this.validateASNFail) {
          updateOrderResponsesStatuses();
          // in case some order responses have been shipped, they will no longer be kept in memory
          this.orderResponses = this.orderResponses.stream()
              .filter(ordrsp -> !ordrsp.getStatusAsEnumValue()
                  .equals(DocumentStatus.SENT))
              .collect(Collectors.toList());
        }
      }
      boolean isPartiallyShipped = false;
      if (this.orderResponses != null && !this.orderResponses.isEmpty()) {
        OrderResponse currentOrderResponse = getOrderResponseByOrderReferenceNumber(this.orderResponses, this.selectedOrderNumber);
        if (currentOrderResponse != null)
          isPartiallyShipped = DocumentStatus.PARTIALLY_SHIPPED.equals(currentOrderResponse.getStatusAsEnumValue());
      }

      if (StringUtils.isEmpty(this.validateASNFailMessage)) {
        this.showASNSendSuccess = true;
        this.asnSendSuccessMessage = MessageHelper.getMessage(
            isPartiallyShipped ? LABEL_FAMILY + ".order_number_sent_partially_success" : LABEL_FAMILY + ".order_number_sent_success",
            "", this.currentSession()
                .getLocale(),
            this.selectedOrderNumber);
      }

      this.reset();
      this.modeDeliveryNotes = false;
    }
    finally {
      this.lockMechanismService.unlock(getCurrentUser().getLogin());
    }
  }

  public void updateOrderResponsesStatuses() {
    List<OrderResponse> updatedOrderResponses = new ArrayList<OrderResponse>();
    Set<String> selectedOrderResponses = new HashSet<String>();
    for (int i = 0; i < this.selectedOrdrspItems.length; i++) {
      String orderResponseNumber = getBuyerOrderResponseNumber(this.selectedOrdrspItems[i].getOrderResponseHeader());
      if (orderResponseNumber != null) {
        selectedOrderResponses.add(orderResponseNumber);
      }
    }
    List<OrderResponse> filteredOrderResponses = this.orderResponses.stream()
        .filter(ordrsp -> selectedOrderResponses.contains(getBuyerOrderResponseNumber(ordrsp)))
        .collect(Collectors.toList());
    for (OrderResponse ordrsp : filteredOrderResponses) {
      int shippedLines = 0;
      for (OrderResponseItemDetailType detail : ordrsp.getOrderResponseDetail()
          .getListOfOrderResponseItemDetail()
          .getOrderResponseItemDetail()) {
        // in case the line is already shipped than there is no point in verifying again if it is shipped
        if (!DocumentStatus.SHIPPED.toString()
            .equals(detail.getItemDetailResponseCodedOther())) {
          // for 1 line the total number of shipped quantities
          List<SplitQuantityType> quantities = getSplitQuantityList(detail);
          int numberOfShippedSplitQuantity = (int) quantities
              .stream()
              .filter(quantity -> quantity.getShipmentStatusEventCoded()
                  .equals(ShipmentStatusEventCodeType.SHIPMENT_COMPLETE))
              .count();
          if (quantities.size() == numberOfShippedSplitQuantity) {
            detail.setItemDetailResponseCodedOther(DocumentStatus.SHIPPED.toString());
            detail.setItemDetailResponseCoded(DetailResponseCodeType.OTHER);
            shippedLines++;
          }
          else if (numberOfShippedSplitQuantity > 0) {
            detail.setItemDetailResponseCoded(DetailResponseCodeType.OTHER);
            detail.setItemDetailResponseCodedOther(DocumentStatus.PARTIALLY_SHIPPED.toString());
          }
          if (!ordrsp.getStatusAsEnumValue()
              .equals(DocumentStatus.PARTIALLY_SHIPPED)) {
            ordrsp.setStatusWithEnumValue(DocumentStatus.PARTIALLY_SHIPPED);
          }
        }
        else {
          shippedLines++;
        }
      }
      // if all the lines have status SHIPPED than the order response will have the status SENT
      if (shippedLines == ordrsp.getOrderResponseDetail()
          .getListOfOrderResponseItemDetail()
          .getOrderResponseItemDetail()
          .size()) {
        ordrsp.setStatusWithEnumValue(DocumentStatus.SENT);
      }
      // Order responses that have to be updated.
      updatedOrderResponses.add(ordrsp);
    }
    // since there is the possibility that an order can change the status from PARTIALLY_SHIPPED to ACCEPTED_WITH_AMENDMENT or
    // PARTIALLY_ACCEPTED_WITH_AMENDMENTS by editing the order
    // response after some quantity of that order response was shipped i need basically to update all orders corresponding to the selected
    // order responses
    updateOrdersStatus(updatedOrderResponses);
    Query q = resolveBaseQuery();
    QueryBuilder qb = QueryBuilder.createBuilder(q);
    List<String> buyerOrderNumber = new ArrayList<String>();
    buyerOrderNumber = updatedOrderResponses.stream()
        .map(ordrsp -> getBuyerOrderResponseNumber(ordrsp))
        .collect(Collectors.toList());
    qb.or(in("orderResponseHeader.orderResponseNumber.buyerOrderResponseNumber", buyerOrderNumber));
    List<Document> docs = documentService.searchIndexedDocuments(Document.class, OrderResponse.class, qb.query(), null);
    docs.forEach(doc -> {
      OrderResponse oldOrdrsp = (OrderResponse) doc.getIndex()
          .getValue();
      OrderResponse updatedOrdrsp = updatedOrderResponses.stream()
          .filter(orderResponse -> getBuyerOrderResponseNumber(orderResponse)
              .equals(getBuyerOrderResponseNumber(oldOrdrsp)))
          .findFirst()
          .get();

      doc.getIndex()
          .setValue(updatedOrdrsp);

      doc.setStatus(updatedOrdrsp.getStatus());
    });
    documentService.saveDocuments(docs);
  }

  public void updateOrdersStatus(Collection<OrderResponse> ordrsps) {
    Query q = resolveBaseQuery();
    QueryBuilder qb = QueryBuilder.createBuilder(q);
    List<String> buyerOrderNumber = new ArrayList<String>();
    buyerOrderNumber = ordrsps.stream()
        .map(ordrsp -> ordrsp.getOrderResponseHeader()
            .getOrderReference()
            .getRefNum())
        .collect(Collectors.toList());
    qb.or(
        in("buyerOrderNumber", buyerOrderNumber));
    List<Document> docs = documentService.searchIndexedDocuments(Document.class, OrderIndex.class, qb.query(), null);
    Set<Document> updatedOrders = new HashSet<Document>();

    docs.forEach(doc -> {
      OrderIndex order = (OrderIndex) doc.getIndex()
          .getValue();
      OrderResponse ordrsp = ordrsps.stream()
          .filter(orderResponse -> order.getBuyerOrderNumber()
              .equals(orderResponse.getOrderResponseHeader()
                  .getOrderReference()
                  .getRefNum()))
          .findFirst()
          .get();

      // verify if the actual status of the order is different than the new value, in case they are the same the order will not be updated
      if (!order.getStatusAsEnumValue()
          .equals(DocumentStatus.PARTIALLY_SHIPPED) ||
          (DocumentStatus.PARTIALLY_SHIPPED
              .equals(order.getStatusAsEnumValue()) &&
              DocumentStatus.SENT
                  .equals(ordrsp.getStatusAsEnumValue()))) {
        // treat the case of a partially accepted order response which can have status SENT but not all lines from the order are contained
        // in the order response so it should have status PARTIALLY_SHIPPED
        if (order.getDetails()
            .size() != ordrsp.getOrderResponseDetail()
                .getListOfOrderResponseItemDetail()
                .getOrderResponseItemDetail()
                .size() &&
            !ordrsp.getStatusAsEnumValue()
                .equals(DocumentStatus.PARTIALLY_SHIPPED)) {
          ordrsp.setStatusWithEnumValue(DocumentStatus.PARTIALLY_SHIPPED);
        }
        // update children status
        order.setStatus(ordrsp.getStatus());
        doc.getChildren()
            .stream()
            .forEach(childDoc -> childDoc.setStatus(order.getStatus()));
        doc.setStatus(order.getStatus());
        updatedOrders.add(doc);
      }
    });
    updateAllOrderResponsesToSend(updatedOrders);
    documentService.saveDocuments(updatedOrders);
  }

  // according to AIO-13928 when you create an asn and you ship all lines from the order then all order responses(not just the last one)
  // related to this order must have status SENT
  public void updateAllOrderResponsesToSend(Set<Document> updatedOrders) {
    List<String> shippedOrderNumbers = updatedOrders.stream()
        .filter(ord -> DocumentStatus.SENT.equals(ord.getStatusAsEnumValue()))
        .map(ord -> ((OrderIndex) ord.getIndexValue()).getBuyerOrderNumber())
        .collect(Collectors.toList());
    if (!shippedOrderNumbers.isEmpty()) {
      Query q = resolveBaseQuery();
      QueryBuilder qb = QueryBuilder.createBuilder(q);
      qb.or(
          in("orderResponseHeader.orderReference.refNum", shippedOrderNumbers));
      List<Document> docs = documentService.searchIndexedDocuments(Document.class, OrderResponse.class, qb.query(), null);
      docs.forEach(doc -> {
        ((OrderResponse) doc.getIndexValue()).setStatusWithEnumValue(DocumentStatus.SENT);
        doc.setStatusWithEnumValue(DocumentStatus.SENT);
      });
      documentService.saveDocuments(docs);
    }
  }

  public void onSaveASNAsDraft() {
    try {
      createASN(true, null);
      reset();
    }
    catch (Exception e) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, "elsasntsklbls.asn_save_draft_error_msg");
      log.error(e.getMessage());
    }
  }

  public void onValidateFields() {
    if (!isSendButtonClickable()) {
      addCallbackParam("validationFailed", true);
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, "elsasntsklbls.createASN_deliveryNoteNumberOrCheckboxesErrorMessage");
    }
  }

  public boolean isNotLockedByCurrentUser(OrderResponse ordrsp, String userLogin) {
    return this.lockMechanismService.isLocked(ordrsp.getId()) &&
        !this.lockMechanismService.getValue(ordrsp.getId())
            .equals(userLogin);
  }

  public void createASN(boolean draft, String confirmationNr) throws Exception {
    OrderResponse ordrsp = this.orderResponses.stream()
        .filter(orderResponse -> getBuyerOrderResponseNumber(orderResponse)
            .equals(getBuyerOrderResponseNumber(this.selectedOrdrspItems[0].getOrderResponseHeader())))
        .findFirst()
        .orElse(null);
    if (ordrsp == null)
      return;
    if (isNotLockedByCurrentUser(ordrsp, getCurrentUser().getLogin())) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".order_locked");
      this.reset();
      return;
    }
    String confirmationNumber = confirmationNr == null ? this.orderResponseConfirmationConfiguration.getFormattedConfirmationNumber()
        : confirmationNr;
    AdvanceShipmentNotice desadv = initAdvanceShipmentNotice(ordrsp, this.currentSession()
        .getCompany(),
        this.currentSession()
            .getPartner(),
        this.deliveryDate,
        this.modeDraft ? this.draftConfirmationNumber : confirmationNumber,
        this.selectedOrdrspItems, this.deliveryNoteNumber, draft);

    changeBuyerOrderNumber(desadv);

    boolean asnNumberChanged = updateASNNumber(desadv);
    confirmationNumber = this.orderResponseConfirmationConfiguration.getFormattedConfirmationNumber();
    boolean validatedASN = true;
    if (!draft) {
      validatedASN = validateASN(desadv);
    }
    if (this.modeDraft) {
      if (validatedASN) {
        this.selectedDraft.setASNHeader(desadv.getASNHeader());
        this.selectedDraft.setASNDetail(desadv.getASNDetail());
        this.selectedDraft.setStatus(desadv.getStatus());
        Document doc = documentService.getDocument(this.selectedDraft);
        doc.setStatus(desadv.getStatus());
        documentService.saveDocument(doc);
        if (!draft) {
          deleteDraftDocumentFileBeforeSending(doc);
        }
        createOrUpdateXcblFile(doc, desadv, draft ? AsnXcblHelper.DESADVDIR_DRAFT : AsnXcblHelper.DESADVDIR,
            getASNNumber(desadv));
        asnService.removeFromDocumentGeneratedFiles(doc, Arrays.asList(FileType.HTML, FileType.PDF, FileType.CSV));
      }
      else {
        showMessageWhenValidateFailled();
      }
    }
    else {
      if (validatedASN) {
        documentService.saveDocument(createASNDocument(desadv, this.company.getCode(), draft));
        incrementASNNumber();
      }
      else {
        showMessageWhenValidateFailled();
      }
    }
    if (draft) {
      if (this.modeDraft) {
        this.showDraftUpdatedMessage = true;
        this.draftUpdatedMessage = MessageHelper.getMessage(
            LABEL_FAMILY + ".asn_draft_updated_msg",
            "Advance shipment notice was updated", this.currentSession()
                .getLocale(),
            this.draftConfirmationNumber);
      }
      else {
        this.showSaveAsDraftSuccess = true;
        this.asnSaveAsDraftSuccessMessage = MessageHelper.getMessage(
            asnNumberChanged ? LABEL_FAMILY + ".asn_save_draft_msg_changed" : LABEL_FAMILY + ".asn_save_draft_msg",
            "Advance shipment notice was saved as draft", this.currentSession()
                .getLocale(),
            confirmationNumber);
      }
    }
    else {
      if (validatedASN) {
        if (this.modeDraft) {
          this.showDraftShippedMessage = true;
          this.draftShippedMessage = MessageHelper.getMessage(
              LABEL_FAMILY + ".asn_draft_shipped_msg",
              "Draft was successfully shipped", this.currentSession()
                  .getLocale(),
              this.draftConfirmationNumber);
        }
        else {
          this.showASNSendSuccess = true;
          this.asnSendSuccessMessage = MessageHelper.getMessage(
              asnNumberChanged ? LABEL_FAMILY + ".asn_send_msg_changed" : LABEL_FAMILY + ".asn_send_msg",
              "Advance shipment notice was successfully generated", this.currentSession()
                  .getLocale(),
              confirmationNumber);
        }
      }
    }
  }

  /**
   * Set the /AdvanceShipmentNotice /ASNHeader /ASNOrderNumber /core tag: BuyerOrderNumber to "Multiple" if the shipping advice consists of
   * lines from multiple commands (multiple numbers).
   * 
   * @param asn holding the reference
   */
  public static void changeBuyerOrderNumber(final AdvanceShipmentNotice asn) {

    boolean isMultiple = ofNullable(asn).map(AdvanceShipmentNoticeType::getASNDetail)
        .map(ASNDetailType::getListOfASNItemDetail)
        .map(ListOfASNItemDetailType::getASNItemDetail)
        .orElse(emptyList())
        .stream()
        .map(ASNItemDetailType::getASNBaseItemDetail)
        .map(ASNBaseItemDetailType::getASNLineItemReferences)
        .map(ASNLineItemReferencesType::getPurchaseOrderReference)
        .map(PurchaseOrderReferenceType::getBuyerOrderNumber)
        .distinct()
        .count() != 1;
    if (isMultiple) {
      Optional.of(asn)
          .map(AdvanceShipmentNoticeType::getASNHeader)
          .map(ASNHeaderType::getASNOrderNumber)
          .orElse(emptyList())
          .stream()
          .findFirst()
          .ifPresent(oderNumberType -> oderNumberType.setBuyerOrderNumber(MULTIPLE));
    }
  }

  // takes all the latest order responses(maybe another user shipped at least 1 line that the current user wants to ship or at least 1 line
  // was modified) and verifies if all the lines contained in the asn are not shipped or modified.
  private boolean validateASN(AdvanceShipmentNotice asn) {
    this.modeValidateASN = true;
    List<ASNItemDetailType> details = getASNItemDetailTypeFromASN(asn);
    this.latestOrderResponses = getValidOrderResponses();
    this.productLines = null;
    this.productLines = onSearchOrdersToShip();
    if (this.productLines.size() == 0) {
      return false;
    }
    boolean updatedLines = wereLinesUpdated(details);
    List<OrderResponse> oldOrderResponses = null;
    if (updatedLines) {
      oldOrderResponses = getOldOrderResponses(asn);
    }
    for (ASNItemDetailType detail : details) {
      OriginalItemDetailWrapper line = findCorrespondingBaseItemDetail(updatedLines ? oldOrderResponses : this.latestOrderResponses,
          detail.getASNBaseItemDetail(),
          this.productLines,
          this.deliveryDate);
      if (line == null) {
        this.latestOrderResponses = null;
        return false;
      }
    }
    this.latestOrderResponses = null;
    return true;
  }

  // verifies if all the order response number taken from the details of the asn are contained in the list of order responses corresponding
  // to the place of delivery and date selected(thus no modification was made on lines)
  private boolean wereLinesUpdated(List<ASNItemDetailType> details) {
    Set<String> asnOrdrspNumbers = details.stream()
        .map(detail -> getOrderResponseNumberFromASNDetail(detail.getASNBaseItemDetail()))
        .collect(Collectors.toSet());
    Set<String> ordrspNumbers = this.productLines.stream()
        .map(line -> getBuyerOrderResponseNumber(line.getOrderResponseHeader()))
        .collect(Collectors.toSet());
    for (String number : asnOrdrspNumbers) {
      if (!ordrspNumbers.contains(number)) {
        return true;
      }
    }
    return false;
  }

  private boolean updateASNNumber(AdvanceShipmentNotice asn) {
    if (this.modeDraft) {
      return false;
    }
    String orderResponseNumber = asn.getASNHeader()
        .getASNNumber();

    // get next available confirmation number
    this.orderResponseConfirmationConfiguration = this.orderResponseConfirmationService.getConfiguration(this.partner);
    String availableOrderResponseNumber = this.orderResponseConfirmationConfiguration.getFormattedConfirmationNumber();

    asn.getASNHeader()
        .setASNNumber(availableOrderResponseNumber);

    return !orderResponseNumber.equals(availableOrderResponseNumber);
  }

  public void onDeleteDraft() {
    resetMesages();
    Document doc = documentService.getDocument(this.asnDraftToDelete);
    documentService.removeDocument(doc);
    this.asnDraftToDelete = null;
    deleteDraftDocumentFileBeforeSending(doc);
  }

  public void onUseDraft(AdvanceShipmentNotice asnDraft) throws Exception {
    this.selectedDraft = asnDraft;
    this.modeCreateASN = true;
    this.modeDraft = true;
    this.selectedPlace = getPlaceOfDeliveryFromASN(asnDraft, DEFAULT_PLACE);
    this.deliveryDate = asnDraft.getASNHeader()
        .getASNDates()
        .getShipDate();
    this.deliveryNoteNumber = getShippingReferencesFromASN(asnDraft);
    this.draftConfirmationNumber = getASNNumberFromASN(asnDraft);
    List<ASNItemDetailType> details = getASNItemDetailTypeFromASN(asnDraft);
    this.orderResponses = getValidOrderResponses();
    this.productLines = onSearchOrdersToShip();
    int position = 0;
    int deletedLines = 0;
    boolean needToUpdateDraft = false;
    List<ASNItemDetailType> deletedDetails = new ArrayList<ASNItemDetailType>();
    List<OrderResponse> oldOrderResponses = getOldOrderResponses(asnDraft);
    List<OriginalItemDetailWrapper> items = new ArrayList<OriginalItemDetailWrapper>();
    if (this.productLines == null) {
      showMessageWhenDocumentRemoved(asnDraft);
    }
    else {
      for (ASNItemDetailType detail : details) {
        OriginalItemDetailWrapper line = findCorrespondingBaseItemDetail(oldOrderResponses, detail.getASNBaseItemDetail(),
            this.productLines,
            this.deliveryDate);
        if (line != null) {
          items.add(line);
          // in case there was found a line corresponding to the current detail and their corresponding order response number do not match
          // this means that the order response was modified(thus a new order response was created) so i need to update the draft with the
          // correct order response number
          String orderResponseNumber = getBuyerOrderResponseNumber(line.getOrderResponseHeader());
          String orderResponseNumberFromASN = getOrderResponseNumberFromASNDetail(detail.getASNBaseItemDetail());
          if (!orderResponseNumber.equals(orderResponseNumberFromASN)) {
            detail.getASNBaseItemDetail()
                .setDetailResponseCodedOther(orderResponseNumber);
            detail.getASNBaseItemDetail()
                .getASNLineItemReferences()
                .getPurchaseOrderReference()
                .getAccountCode()
                .setRefNum(orderResponseNumber);
            needToUpdateDraft = true;
          }
        }
        else {
          deletedDetails.add(detail);
          deletedLines++;
        }
      }
      if (details.size() == deletedLines) {
        showMessageWhenDocumentRemoved(asnDraft);
      }
      else if (deletedDetails.size() > 0) {
        deletedDetails.forEach(detail -> details.remove(detail));
      }
      /*
       * If in an order response we edit only the lines which are not already in the asn draft, needToUpdateDraft is true and a new order
       * response is generated. When 'needToUpdateDraft' is true, we need to set in the asn draft the newly created order response number.
       * If in an order response, we edit a line which is in asn draft also, then deletedDetails will contain that line. In that case, the
       * asn draft must also be updated by removing that line from the asn draft.
       */
      if (needToUpdateDraft || deletedDetails.size() > 0) {
        updateDocument(asnDraft);
      }
      this.selectedOrdrspItems = new OriginalItemDetailWrapper[items.size()];
      for (OriginalItemDetailWrapper item : items) {
        this.selectedOrdrspItems[position] = item;
        position++;
      }
    }
  }

  public void updateDocument(AdvanceShipmentNotice asn) throws Exception {
    Document doc = documentService.getDocument(asn);
    if (doc == null)
      return;
    documentService.saveDocument(doc);
    createOrUpdateXcblFile(doc, asn, AsnXcblHelper.DESADVDIR_DRAFT,
        getASNNumber(asn));
    asnService.removeFromDocumentGeneratedFiles(doc, Arrays.asList(FileType.HTML, FileType.PDF, FileType.CSV));
  }

  public void showMessageWhenDocumentRemoved(AdvanceShipmentNotice asn) {
    Document doc = documentService.getDocument(asn);
    if (!doc.getStatusAsEnumValue()
        .equals(DocumentStatus.SENT)) {
      documentService.removeDocument(doc);
      deleteDraftDocumentFileBeforeSending(doc);
      this.showDeleteDraftMessage = true;
      this.asnDeleteDraftMessage = MessageHelper.getMessage(LABEL_FAMILY + ".asn_delete_draft_msg",
          "Advance shipment notice was deleted", this.currentSession()
              .getLocale());
    }
    else {
      showMessageWhenDraftShipped(asn.getASNHeader()
          .getASNNumber());
    }
    reset();
  }

  public void showMessageWhenDraftShipped(String asnNumber) {
    this.showDraftSendMessage = true;
    this.draftSendMessage = MessageHelper.getMessage(LABEL_FAMILY + ".asn_draft_send_msg",
        "Advance shipment notice was deleted", this.currentSession()
            .getLocale(),
        asnNumber);
  }

  public void showMessageWhenValidateFailled() {
    this.validateASNFail = true;
    this.validateASNFailMessage = MessageHelper.getMessage(LABEL_FAMILY + ".asn_validate_fail",
        "Fail to generate advance shipment notice", this.currentSession()
            .getLocale());
  }

  // This function will returns all the order responses that are contained in the draft(maybe some of them were modified so i need them in
  // order to find out its last modification from the orderResponses which contains only the latest modified order responses)
  public List<OrderResponse> getOldOrderResponses(AdvanceShipmentNotice asn) {
    Query q = resolveBaseQuery();
    QueryBuilder qb = QueryBuilder.createBuilder(q);
    Set<String> ordrspNumbers = getASNItemDetailTypeFromASN(asn).stream()
        .map(detail -> getOrderResponseNumberFromASNDetail(detail.getASNBaseItemDetail()))
        .collect(Collectors.toSet());
    qb.or(in("orderResponseHeader.orderResponseNumber.buyerOrderResponseNumber", ordrspNumbers));
    return documentService.searchIndexables(OrderResponse.class, qb.query(), null)
        .getContent();
  }

  public boolean isSearchButtonClickable() {
    return this.selectedPlace == null || this.deliveryDate == null;
  }

  public boolean isSendButtonClickable() {
    return this.deliveryNoteNumber != null && !StringUtils.isBlank(this.deliveryNoteNumber) && this.selectedOrdrspItems != null
        ? this.selectedOrdrspItems.length > 0
        : false;
  }

  public void onCancelSendASN() {
    if (!this.onCreateASNBack) {
      reset();
    }
  }

  public boolean getOnCreateASNBack() {
    return onCreateASNBack;
  }

  public void setOnCreateASNBack(boolean onCreateASNBack) {
    this.onCreateASNBack = onCreateASNBack;
  }

  public void setAvailablePlaces(Set<String> availablePlaces) {
    this.availablePlaces = availablePlaces;
  }

  public String getSelectedPlace() {
    return selectedPlace;
  }

  public void setSelectedPlace(String selectedPlace) {
    this.selectedPlace = selectedPlace;
  }

  public String getSelectedOrderNumber() {
    return selectedOrderNumber;
  }

  public void setSelectedOrderNumber(String selectedOrderNumber) {
    this.selectedOrderNumber = selectedOrderNumber;
  }

  public String[] getSelectedDeliveryDates() {
    return selectedDeliveryDates;
  }

  public void setSelectedDeliveryDates(String[] selectedDeliveryDates) {
    this.selectedDeliveryDates = selectedDeliveryDates;
  }

  public Date getMinDate() {
    return new Date();
  }

  public OriginalItemDetailWrapper[] getSelectedOrdrspItems() {
    return selectedOrdrspItems;
  }

  public void setSelectedOrdrspItems(OriginalItemDetailWrapper[] selectedOrdrspItems) {
    this.selectedOrdrspItems = selectedOrdrspItems;
  }

  public Date getDeliveryDate() {
    return deliveryDate;
  }

  public void setDeliveryDate(Date deliveryDate) {
    this.deliveryDate = deliveryDate;
  }

  public boolean isCrossDocker() {
    Optional<LocationType> locationOpt = Optional.ofNullable(this.selectedASN)
        .map(AdvanceShipmentNotice::getASNHeader)
        .map(ASNHeaderType::getASNTermsOfDelivery)
        .map(TermsOfDeliveryType::getLocation);
    if (locationOpt.isPresent()) {
      LocationType location = locationOpt.get();
      if ("Other".equals(location.getLocationQualifierCoded()) && DEFAULT_PLACE.equals(location.getLocationQualifierCodedOther())) {
        return true;
      }
    }
    return false;
  }

  private List<OrderResponse> filterOrderResponses() {
    List<OrderResponse> filteredOrderResponses = new ArrayList<OrderResponse>();
    for (OrderResponse orderResponse : this.orderResponses) {
      List<OrderResponseItemDetailType> ordrspItems = orderResponse.getOrderResponseDetail()
          .getListOfOrderResponseItemDetail()
          .getOrderResponseItemDetail();
      if (ordrspItems.stream()
          .filter(item -> !DocumentStatus.SHIPPED.name()
              .equals(item.getItemDetailResponseCodedOther()))
          .findAny()
          .isPresent()) {
        filteredOrderResponses.add(orderResponse);
      }
    }
    return filteredOrderResponses;
  }

  @Override
  public void onSelectCheckbox(SelectEvent event) {
    OriginalItemDetailWrapper item = (OriginalItemDetailWrapper) event.getObject();
    item.setSelected(true);
  }

  @Override
  public void onUnselectCheckbox(UnselectEvent event) {
    OriginalItemDetailWrapper item = (OriginalItemDetailWrapper) event.getObject();
    item.setSelected(false);
    // in the case of dispatch by order generateDeliveryNoteNumbers() method is called, while for dispatch by date and place it is not.
    if (item.getDeliveryNoteNumber() != null) {
      List<OriginalItemDetailWrapper> items = groupedDeadlines.get(dateFormat.format(item.getDeliveryNoteNumber()
          .getDeliveryDate()));
      items.stream()
          .filter(wrapper -> !wrapper.equals(item) && !wrapper.isSelected() && wrapper.getDeliveryNoteNumber() != null)
          .forEach(detail -> detail.getDeliveryNoteNumber()
              .setDeliveryNote(null));
      // reset the delivery note input set previously by the user
      item.getDeliveryNoteNumber()
          .setDeliveryNote(null);
    }
  }

  @SuppressWarnings("unchecked")
  @Override
  public void onToggleSelect(ToggleSelectEvent event) {
    UIData table = (UIData) event.getComponent();
    List<OriginalItemDetailWrapper> tableData = (List<OriginalItemDetailWrapper>) table.getValue();

    for (OriginalItemDetailWrapper item : tableData) {
      item.setSelected(event.isSelected());
      // in the case of dispatch by order generateDeliveryNoteNumbers() method is called, while for dispatch by date and place it is not.
      if (event.isSelected() == false && item.getDeliveryNoteNumber() != null) {
        item.getDeliveryNoteNumber()
            .setDeliveryNote(null);
      }
    }
  }

  /**
   * Because items are grouped by delivery date in Step 3, in a group, the items must have the same delivery note. Used for instance in a
   * scenario where there are 2 items dated 25/07 and 25/07. First item is selected and provided a delivery note in step3, after that first
   * item is unselected and the second one is selected. This function ensures that in this case the delivery not is kept because there is
   * still an item with date 25/07 selected.
   */
  private void keepDeliveryNoteInputValue() {
    this.productLines.stream()
        .filter(wrapper -> wrapper.isSelected())
        .forEach(item -> item.getDeliveryNoteNumber()
            .setInError(false));
    Set<Entry<String, List<OriginalItemDetailWrapper>>> entryList = groupedDeadlines.entrySet();
    for (Entry<String, List<OriginalItemDetailWrapper>> entry : entryList) {
      String note = entry.getValue()
          .stream()
          .filter(item -> item.getDeliveryNoteNumber() != null && StringUtils.isNotEmpty(item.getDeliveryNoteNumber()
              .getDeliveryNote()))
          .map(OriginalItemDetailWrapper::getDeliveryNoteNumber)
          .map(DeliveryNoteNumber::getDeliveryNote)
          .findFirst()
          .orElse(null);
      // all items with the same date will have the same note
      if (note != null) {
        entry.getValue()
            .forEach(item -> item.getDeliveryNoteNumber()
                .setDeliveryNote(note));
      }
    }
  }

  private boolean validateRequiredDeliveryNoteInput() {
    boolean isValid = true;
    List<OriginalItemDetailWrapper> selectedItems = this.productLines.stream()
        .filter(item -> item.isSelected())
        .collect(Collectors.toList());
    for (OriginalItemDetailWrapper originalItemDetailWrapper : selectedItems) {
      originalItemDetailWrapper.getDeliveryNoteNumber()
          .setInError(false);
      if (StringUtils.isBlank(originalItemDetailWrapper.getDeliveryNoteNumber()
          .getDeliveryNote())) {
        isValid = false;
        originalItemDetailWrapper.getDeliveryNoteNumber()
            .setInError(true);
      }
    }
    return isValid;
  }

  /** Construct non shipped product lines. Construct product lines grouped by delivery date */
  private void generateProductLines(OrderResponse orderResponse, List<String> selectedDeliveryDatesList,
      List<OrderResponseItemDetailType> validDetails) {
    for (OrderResponseItemDetailType orderResponseItemDetailType : validDetails) {
      int deadlineNumber = 1;
      for (SplitQuantityType splitQuantity : getSplitQuantityList(orderResponseItemDetailType)) {
        if (splitQuantity.getShipmentStatusEventCoded()
            .equals(ShipmentStatusEventCodeType.NOT_YET_SHIPPED)) {
          if (selectedDeliveryDatesList.contains(dateFormat.format(splitQuantity.getEstimatedDeliveryDate()))) {
            OriginalItemDetailWrapper itemWrapper = new OriginalItemDetailWrapper(orderResponse.getOrderResponseHeader(),
                orderResponseItemDetailType.getOriginalItemDetailWithChanges(), splitQuantity);
            itemWrapper.setDeadlineNumber(deadlineNumber);
            deadlineNumber++;
            this.productLines.add(itemWrapper);
            sortedDates.add(splitQuantity.getEstimatedDeliveryDate());
            List<OriginalItemDetailWrapper> listWrapper = groupedDeadlines
                .get(dateFormat.format(splitQuantity.getEstimatedDeliveryDate())) == null
                    ? new ArrayList<OriginalItemDetailWrapper>()
                    : groupedDeadlines.get(dateFormat.format(splitQuantity.getEstimatedDeliveryDate()));
            listWrapper.add(itemWrapper);
            groupedDeadlines.put(dateFormat.format(splitQuantity.getEstimatedDeliveryDate()), listWrapper);
          }
        }
      }
    }
  }
}
