package com.byzaneo.generix.els.portal.task.util;

import static com.byzaneo.generix.els.portal.task.util.ElisOrderResponseTaskHelper.generateOrderResponseItemKey;
import static com.byzaneo.generix.els.portal.task.util.ElisOrderResponseXcblHelper.CROSS_DOCKING_ORDER;
import static java.util.Optional.ofNullable;
import static java.util.stream.Stream.empty;

import java.text.*;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeComparator;

import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;

public class ElisAdvanceShipmentNoticeTaskHelper {

  public static final String DEFAULT_PLACE = "ZB -Bretigny-sur-Orge";

  private static transient DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

  public static List<OrderResponseItemDetailType> getValidOrderResponseItems(List<ItemKey> validItemKeys,
      List<OrderResponseItemDetailType> details) {
    if (CollectionUtils.isEmpty(validItemKeys) || CollectionUtils.isEmpty(details))
      return Collections.emptyList();
    return details.stream()
        .filter(detail -> validItemKeys
            .contains(ElisOrderResponseTaskHelper.generateOrderResponseItemKey(detail.getOriginalItemDetailWithChanges()
                .getBaseItemDetail())))
        .collect(Collectors.toList());
  }

  public static List<OrderResponseItemDetailType> getNotShippedDetails(OrderResponse orderResponse) {
    if (orderResponse == null)
      return Collections.emptyList();
    List<OrderResponseItemDetailType> details = orderResponse.getOrderResponseDetail()
        .getListOfOrderResponseItemDetail()
        .getOrderResponseItemDetail()
        .stream()
        .filter(item -> !DocumentStatus.SHIPPED.name()
            .equals(item.getItemDetailResponseCodedOther()))
        .collect(Collectors.toList());
    return details;
  }

  public static List<SplitQuantityType> getSplitQuantityList(OrderResponseItemDetailType orderResponseItemDetailType) {
    return ofNullable(orderResponseItemDetailType)
        .map(OrderResponseItemDetailType::getOriginalItemDetailWithChanges)
        .map(OriginalItemDetailWithChangesType::getDeliveryDetail)
        .map(ResponseDeliveryDetailType::getListOfSplitQuantity)
        .map(ListOfSplitQuantityType::getSplitQuantity)
        .orElse(Collections.emptyList());
  }

  public static List<SplitQuantityType> getSplitQuantityList(OriginalItemDetailWrapper wrapper) {
    return ofNullable(wrapper)
        .map(OriginalItemDetailWrapper::getOrderResponseItemDetail)
        .map(OriginalItemDetailWithChangesType::getDeliveryDetail)
        .map(ResponseDeliveryDetailType::getListOfSplitQuantity)
        .map(ListOfSplitQuantityType::getSplitQuantity)
        .orElse(Collections.emptyList());
  }

  /**
   * Create DeliveryNoteNumber object for each product line
   * 
   * @param line OriginalItemDetailWrapper
   */
  public static void initDeliveryNote(OriginalItemDetailWrapper line) {
    if (line == null)
      return;
    Date deliveryDate = Optional.ofNullable(line)
        .map(OriginalItemDetailWrapper::getSplitQuantity)
        .map(SplitQuantityType::getEstimatedDeliveryDate)
        .orElse(null);
    String deliveryPoint = Optional.ofNullable(line)
        .map(OriginalItemDetailWrapper::getOrderResponseHeader)
        .map(OrderResponseHeaderType::getOriginalOrderHeaderWithChanges)
        .map(OrderHeaderType::getOrderTermsOfDelivery)
        .map(List::stream)
        .orElse(empty())
        .findFirst()
        .map(TermsOfDeliveryType::getLocation)
        .map(LocationType::getLocationQualifierCodedOther)
        .orElse(null);
    String deliveryPlace = Optional.ofNullable(line)
        .map(OriginalItemDetailWrapper::getOrderResponseHeader)
        .map(OrderResponseHeaderType::getOriginalOrderHeaderWithChanges)
        .map(OrderHeaderType::getOrderParty)
        .map(OrderPartyType::getShipToParty)
        .map(PartyType::getNameAddress)
        .map(NameAddressType::getName1)
        .orElse(null);
    line.setDeliveryNoteNumber(new DeliveryNoteNumber(deliveryDate, deliveryPoint, deliveryPlace, null));
  }

  public static String getOrderReferenceFromOrdrsp(OrderResponse ordrsp) {
    return ofNullable(ordrsp)
        .map(OrderResponse::getOrderResponseHeader)
        .map(OrderResponseHeaderType::getOrderReference)
        .map(ReferenceType::getRefNum)
        .orElse(StringUtils.EMPTY);
  }

  public static String getPlaceOfDeliveryFromOrdrsp(OrderResponse ordrsp) {
    if (ordrsp == null)
      return StringUtils.EMPTY;
    String place = "";
    if (ordrsp.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderParty()
        .getShipToParty() != null) {
      // in case the order type coded is equal to "CrossDockingOrder" than the place of delivery must be "ZB -Bretigny-sur-Orge"
      if (CROSS_DOCKING_ORDER.equals(ordrsp.getOrderResponseHeader()
          .getOriginalOrderHeaderWithChanges()
          .getOrderType()
          .getOrderTypeCoded())) {
        place = DEFAULT_PLACE;
      }
      // else the place of delivery is taken from the corresponding shipToParty
      else {
        String address = Optional.ofNullable(ordrsp)
            .map(OrderResponse::getOrderResponseHeader)
            .map(OrderResponseHeaderType::getOriginalOrderHeaderWithChanges)
            .map(OrderHeaderType::getOrderParty)
            .map(OrderPartyType::getShipToParty)
            .map(PartyType::getNameAddress)
            .map(NameAddressType::getName1)
            .orElse(null);
        if (address != null) {
          place = address;
        }
      }
    }
    return place;
  }

  public static boolean findSameItemWithALowerDeliveryDate(OriginalItemDetailWrapper item, ItemKey itemKey, Date itemDate) {
    if (item == null || itemKey == null || itemDate == null)
      return false;
    ItemKey currentItemKey = generateOrderResponseItemKey(item.getOrderResponseItemDetail()
        .getBaseItemDetail());
    Date currentItemDate = item.getDeliveryNoteNumber()
        .getDeliveryDate();
    if (currentItemKey.equals(itemKey) && DateTimeComparator.getDateOnlyInstance()
        .compare(currentItemDate, itemDate) < 0) {
      return true;
    }
    return false;
  }

  public static Set<Date> getUniqueDeliveryDatesFromSelectedProductLines(List<OriginalItemDetailWrapper> productLines) {
    if (CollectionUtils.isEmpty(productLines))
      return Collections.emptySet();
    Set<Date> dates = productLines.stream()
        .filter(item -> item.isSelected())
        .map(OriginalItemDetailWrapper::getDeliveryNoteNumber)
        .map(DeliveryNoteNumber::getDeliveryDate)
        .map(deliveryDate ->
    {
          try {
            return dateFormat.parse(dateFormat.format(deliveryDate));
          }
          catch (ParseException ex) {
            throw new RuntimeException(ex);
          }
        })
        .collect(Collectors.toSet());
    return new TreeSet<Date>(dates);
  }

  public static OrderResponse getOrderResponseByOrderReferenceNumber(List<OrderResponse> orderResponses, String orderNumber) {
    if (CollectionUtils.isEmpty(orderResponses))
      return null;
    return orderResponses.stream()
        .filter(ordrsp -> ordrsp.getOrderResponseHeader()
            .getOrderReference()
            .getRefNum()
            .equals(orderNumber))
        .findFirst()
        .orElse(null);
  }

  /**
   * Search for the current used order response in the new list of order responses
   * 
   * @param newOrderResponses list of order responses in which to perform the search
   * @return found OrderResponse otherwise null
   */
  public static OrderResponse findLatestOrderResponseForSelectedOrderNumber(List<OrderResponse> newOrderResponses,
      List<OrderResponse> currentOrderResponses, String selectedOrderNumber) {
    OrderResponse currentOrderResponseUsed = getOrderResponseByOrderReferenceNumber(currentOrderResponses, selectedOrderNumber);
    return newOrderResponses.stream()
        .filter(ordrsp -> ordrsp.getOrderResponseHeader()
            .getOrderResponseNumber()
            .getBuyerOrderResponseNumber()
            .equals(currentOrderResponseUsed.getOrderResponseHeader()
                .getOrderResponseNumber()
                .getBuyerOrderResponseNumber()))
        .findAny()
        .orElse(null);
  }
}
