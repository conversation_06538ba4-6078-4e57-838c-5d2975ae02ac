<OrderResponse xmlns:ns5="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" xmlns:ns2="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:ns3="http://www.w3.org/2000/09/xmldsig#" xmlns:ns4="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd">
    <OrderResponseHeader>
        <OrderResponseNumber>
            <BuyerOrderResponseNumber>1990</BuyerOrderResponseNumber>
            <SellerOrderResponseNumber>1990</SellerOrderResponseNumber>
        </OrderResponseNumber>
        <OrderResponseIssueDate>2019-10-16T10:11:04.978+03:00</OrderResponseIssueDate>
        <OrderResponseDocTypeCoded>OrderResponse</OrderResponseDocTypeCoded>
        <OrderReference>
            <ns2:RefNum>**********</ns2:RefNum>
            <ns2:RefDate>2003-01-01T00:00:01+02:00</ns2:RefDate>
        </OrderReference>
        <SellerParty>
            <ns2:PartyID>
                <ns2:Agency>
                    <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                </ns2:Agency>
                <ns2:Ident>800199</ns2:Ident>
            </ns2:PartyID>
            <ns2:NameAddress>
                <ns2:Name1>partner street name</ns2:Name1>
                <ns2:Department>Order Department</ns2:Department>
                <ns2:PostalCode>95006</ns2:PostalCode>
                <ns2:City>Orange</ns2:City>
                <ns2:Region>
                    <ns2:RegionCoded>USCA</ns2:RegionCoded>
                </ns2:Region>
            </ns2:NameAddress>
            <ns2:PrimaryContact>
                <ns2:ContactName>partner fullname</ns2:ContactName>
                <ns2:ListOfContactNumber>
                    <ns2:ContactNumber>
                        <ns2:ContactNumberValue></ns2:ContactNumberValue>
                        <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                    </ns2:ContactNumber>
                    <ns2:ContactNumber>
                        <ns2:ContactNumberValue></ns2:ContactNumberValue>
                        <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                    </ns2:ContactNumber>
                </ns2:ListOfContactNumber>
            </ns2:PrimaryContact>
            <ns2:OtherContacts>
                <ns2:Contact>
                    <ns2:ContactName>Ms Black</ns2:ContactName>
                    <ns2:ContactFunction>
                        <ns2:ContactFunctionCoded>AccountsReceivableContact</ns2:ContactFunctionCoded>
                    </ns2:ContactFunction>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:Contact>
                <ns2:Contact>
                    <ns2:ContactName>George Walsh</ns2:ContactName>
                    <ns2:ContactFunction>
                        <ns2:ContactFunctionCoded>DeliveryContact</ns2:ContactFunctionCoded>
                    </ns2:ContactFunction>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:Contact>
            </ns2:OtherContacts>
        </SellerParty>
        <BuyerParty>
            <ns2:PartyID>
                <ns2:Agency>
                    <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                </ns2:Agency>
                <ns2:Ident>CRFID</ns2:Ident>
            </ns2:PartyID>
            <ns2:NameAddress>
                <ns2:Name1>company street name</ns2:Name1>
                <ns2:POBox POBoxPostalCode="249"></ns2:POBox>
                <ns2:PostalCode>20012</ns2:PostalCode>
                <ns2:City>Alpine</ns2:City>
                <ns2:Region>
                    <ns2:RegionCoded>USNY</ns2:RegionCoded>
                </ns2:Region>
            </ns2:NameAddress>
            <ns2:PrimaryContact>
                <ns2:ContactName>company fullname</ns2:ContactName>
                <ns2:ContactFunction>
                    <ns2:ContactFunctionCoded>DepartmentOrPersonResponsibleForProcessingPurchaseOrder</ns2:ContactFunctionCoded>
                </ns2:ContactFunction>
                <ns2:ListOfContactNumber>
                    <ns2:ContactNumber>
                        <ns2:ContactNumberValue></ns2:ContactNumberValue>
                        <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                    </ns2:ContactNumber>
                    <ns2:ContactNumber>
                        <ns2:ContactNumberValue></ns2:ContactNumberValue>
                        <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                    </ns2:ContactNumber>
                </ns2:ListOfContactNumber>
            </ns2:PrimaryContact>
        </BuyerParty>
        <ResponseType>
            <ns2:ResponseTypeCoded>Accepted</ns2:ResponseTypeCoded>
        </ResponseType>
        <OriginalOrderHeaderWithChanges>
            <OrderNumber>
                <BuyerOrderNumber>**********</BuyerOrderNumber>
            </OrderNumber>
            <OrderIssueDate>2003-01-01T00:00:01+02:00</OrderIssueDate>
            <OrderReferences>
                <AccountCode>
                    <ns2:RefNum>3001</ns2:RefNum>
                </AccountCode>
                <ContractReferences>
                    <ns2:Contract>
                        <ns2:ContractID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>AssignedByPartyOriginatingTheMessage</ns2:AgencyCoded>
                            </ns2:Agency>
                            <ns2:Ident>CONTRACT:002-91</ns2:Ident>
                        </ns2:ContractID>
                        <ns2:TypeOfContract>
                            <ns2:ContractTypeCoded>FixedPriceIncentive</ns2:ContractTypeCoded>
                        </ns2:TypeOfContract>
                        <ns2:SystemID>Order/OrderHeader/OrderReferences/ContractReferences/core:Contract/core:SystemID</ns2:SystemID>
                        <ns2:ContractItemNumber>Order/OrderHeader/OrderReferences/ContractReferences/core:Contract/core:ContractItemNumber</ns2:ContractItemNumber>
                    </ns2:Contract>
                </ContractReferences>
                <OtherOrderReferences>
                    <ns2:ReferenceCoded>
                        <ns2:ReferenceTypeCoded>RequestNumber</ns2:ReferenceTypeCoded>
                        <ns2:ReferenceDescription>Buyers OrderRequest Number</ns2:ReferenceDescription>
                    </ns2:ReferenceCoded>
                </OtherOrderReferences>
            </OrderReferences>
            <Purpose>
                <ns2:PurposeCoded>Original</ns2:PurposeCoded>
            </Purpose>
            <RequestedResponse>
                <ns2:RequestedResponseCoded>ResponseExpected</ns2:RequestedResponseCoded>
            </RequestedResponse>
            <OrderType>
                <ns2:OrderTypeCoded>Order</ns2:OrderTypeCoded>
            </OrderType>
            <OrderCurrency>
                <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
            </OrderCurrency>
            <OrderLanguage>
                <ns2:LanguageCoded>fr</ns2:LanguageCoded>
            </OrderLanguage>
            <OrderDates>
                <RequestedShipByDate>2001-10-20T12:00:00+03:00</RequestedShipByDate>
                <RequestedDeliverByDate>2001-10-20T00:00:13+03:00</RequestedDeliverByDate>
            </OrderDates>
            <OrderParty>
                <BuyerParty>
                    <ns2:PartyID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                        </ns2:Agency>
                        <ns2:Ident>CRFID</ns2:Ident>
                    </ns2:PartyID>
                    <ns2:NameAddress>
                        <ns2:Name1>ABC Enterprises</ns2:Name1>
                        <ns2:POBox POBoxPostalCode="249"></ns2:POBox>
                        <ns2:PostalCode>20012</ns2:PostalCode>
                        <ns2:City>Alpine</ns2:City>
                        <ns2:Region>
                            <ns2:RegionCoded>USNY</ns2:RegionCoded>
                        </ns2:Region>
                    </ns2:NameAddress>
                    <ns2:PrimaryContact>
                        <ns2:ContactName>Dietl,B.</ns2:ContactName>
                        <ns2:ContactFunction>
                            <ns2:ContactFunctionCoded>DepartmentOrPersonResponsibleForProcessingPurchaseOrder</ns2:ContactFunctionCoded>
                        </ns2:ContactFunction>
                        <ns2:ListOfContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                        </ns2:ListOfContactNumber>
                    </ns2:PrimaryContact>
                </BuyerParty>
                <SellerParty>
                    <ns2:PartyID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                        </ns2:Agency>
                        <ns2:Ident>800199</ns2:Ident>
                    </ns2:PartyID>
                    <ns2:NameAddress>
                        <ns2:Name1>Dunn Manufacturing</ns2:Name1>
                        <ns2:Department>Order Department</ns2:Department>
                        <ns2:PostalCode>95006</ns2:PostalCode>
                        <ns2:City>Orange</ns2:City>
                        <ns2:Region>
                            <ns2:RegionCoded>USCA</ns2:RegionCoded>
                        </ns2:Region>
                    </ns2:NameAddress>
                    <ns2:OtherContacts>
                        <ns2:Contact>
                            <ns2:ContactName>Ms Black</ns2:ContactName>
                            <ns2:ContactFunction>
                                <ns2:ContactFunctionCoded>AccountsReceivableContact</ns2:ContactFunctionCoded>
                            </ns2:ContactFunction>
                            <ns2:ListOfContactNumber>
                                <ns2:ContactNumber>
                                    <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                                    <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                                </ns2:ContactNumber>
                                <ns2:ContactNumber>
                                    <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                                    <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                                </ns2:ContactNumber>
                            </ns2:ListOfContactNumber>
                        </ns2:Contact>
                        <ns2:Contact>
                            <ns2:ContactName>George Walsh</ns2:ContactName>
                            <ns2:ContactFunction>
                                <ns2:ContactFunctionCoded>DeliveryContact</ns2:ContactFunctionCoded>
                            </ns2:ContactFunction>
                            <ns2:ListOfContactNumber>
                                <ns2:ContactNumber>
                                    <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                                    <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                                </ns2:ContactNumber>
                                <ns2:ContactNumber>
                                    <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                                    <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                                </ns2:ContactNumber>
                            </ns2:ListOfContactNumber>
                        </ns2:Contact>
                    </ns2:OtherContacts>
                </SellerParty>
                <ShipToParty>
                    <ns2:PartyID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Le roy merlin city ctr</ns2:AgencyCoded>
                        </ns2:Agency>
                        <ns2:Ident>3000</ns2:Ident>
                    </ns2:PartyID>
                    <ns2:NameAddress>
                        <ns2:Name1>ABC Enterprises</ns2:Name1>
                        <ns2:StreetSupplement1>255 Marble Court</ns2:StreetSupplement1>
                        <ns2:StreetSupplement2>Marble Industrial Complex</ns2:StreetSupplement2>
                        <ns2:Building>Building</ns2:Building>
                        <ns2:PostalCode>20001</ns2:PostalCode>
                        <ns2:City>New York</ns2:City>
                        <ns2:Region>
                            <ns2:RegionCoded>USNY</ns2:RegionCoded>
                        </ns2:Region>
                        <ns2:Country>
                            <ns2:CountryCoded>US</ns2:CountryCoded>
                        </ns2:Country>
                    </ns2:NameAddress>
                    <ns2:PrimaryContact>
                        <ns2:ContactName>Ms. Audra Murphy</ns2:ContactName>
                        <ns2:ListOfContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                        </ns2:ListOfContactNumber>
                    </ns2:PrimaryContact>
                </ShipToParty>
                <SoldToParty>
                    <ns2:PartyID>
                        <ns2:Ident>CRFID</ns2:Ident>
                    </ns2:PartyID>
                    <ns2:NameAddress>
                        <ns2:Name1>company street name</ns2:Name1>
                    </ns2:NameAddress>
                </SoldToParty>
            </OrderParty>
            <OrderTermsOfDelivery>
                <ns2:Location>
                    <ns2:LocationQualifierCoded>Other</ns2:LocationQualifierCoded>
                    <ns2:LocationQualifierCodedOther>3000</ns2:LocationQualifierCodedOther>
                </ns2:Location>
            </OrderTermsOfDelivery>
            <OrderHeaderNote>Header Text manually entered</OrderHeaderNote>
            <ListOfStructuredNote>
                <ns2:StructuredNote>
                    <ns2:GeneralNote>Terms Of Delivery Header manually entered</ns2:GeneralNote>
                    <ns2:NoteID>Terms of delivery</ns2:NoteID>
                    <ns2:Agency>
                        <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                        <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                    </ns2:Agency>
                </ns2:StructuredNote>
            </ListOfStructuredNote>
        </OriginalOrderHeaderWithChanges>
        <OrderResponseHeaderNote>Header Text manually entered</OrderResponseHeaderNote>
    </OrderResponseHeader>
    <OrderResponseDetail>
        <ListOfOrderResponseItemDetail>
            <OrderResponseItemDetail>
                <ItemDetailResponseCoded>ItemAccepted</ItemDetailResponseCoded>
                <ShipmentStatusEvent>
                    <ns2:ShipDate>2003-01-01T00:00:01+02:00</ns2:ShipDate>
                </ShipmentStatusEvent>
                <OriginalItemDetailWithChanges>
                    <BaseItemDetail>
                        <LineItemNum>
                            <ns2:BuyerLineItemNum>10</ns2:BuyerLineItemNum>
                        </LineItemNum>
                        <ItemIdentifiers>
                            <ns2:PartNumbers>
                                <ns2:SellerPartNumber>
                                    <ns2:PartID>LRM</ns2:PartID>
                                </ns2:SellerPartNumber>
                                <ns2:BuyerPartNumber>
                                    <ns2:PartID>MAF_Id</ns2:PartID>
                                </ns2:BuyerPartNumber>
                                <ns2:StandardPartNumber>
                                    <ns2:ProductIdentifierQualifierCoded>EAN2-5-5-1</ns2:ProductIdentifierQualifierCoded>
                                    <ns2:ProductIdentifierQualifierCodedOther></ns2:ProductIdentifierQualifierCodedOther>
                                    <ns2:ProductIdentifier>2000011163701</ns2:ProductIdentifier>
                                    <ns2:ProductIdentifierExt></ns2:ProductIdentifierExt>
                                </ns2:StandardPartNumber>
                            </ns2:PartNumbers>
                            <ns2:ItemDescription>Description of product</ns2:ItemDescription>
                        </ItemIdentifiers>
                        <ResponseQuantity>
                            <ns2:QuantityValue>111</ns2:QuantityValue>
                            <ns2:UnitOfMeasurement>
                                <ns2:UOMCoded>Other</ns2:UOMCoded>
                                <ns2:UOMCodedOther>PCE</ns2:UOMCodedOther>
                            </ns2:UnitOfMeasurement>
                        </ResponseQuantity>
                        <OrderedQuantity>
                            <ns2:QuantityValue>111</ns2:QuantityValue>
                            <ns2:UnitOfMeasurement>
                                <ns2:UOMCoded>Other</ns2:UOMCoded>
                                <ns2:UOMCodedOther>PCE</ns2:UOMCodedOther>
                            </ns2:UnitOfMeasurement>
                        </OrderedQuantity>
                    </BaseItemDetail>
                    <PricingDetail>
                        <ns2:ListOfPrice>
                            <ns2:Price>
                                <ns2:PricingType>
                                    <ns2:PriceTypeCoded>PerEach</ns2:PriceTypeCoded>
                                </ns2:PricingType>
                                <ns2:UnitPrice>
                                    <ns2:UnitPriceValue>10.00</ns2:UnitPriceValue>
                                    <ns2:Currency>
                                        <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                                    </ns2:Currency>
                                </ns2:UnitPrice>
                                <ns2:CalculatedPriceBasisQuantity>
                                    <ns2:QuantityValue>1</ns2:QuantityValue>
                                    <ns2:UnitOfMeasurement>
                                        <ns2:UOMCoded>EA</ns2:UOMCoded>
                                    </ns2:UnitOfMeasurement>
                                </ns2:CalculatedPriceBasisQuantity>
                            </ns2:Price>
                        </ns2:ListOfPrice>
                        <ns2:LineItemTotal>
                            <ns2:MonetaryAmount>1110.00</ns2:MonetaryAmount>
                        </ns2:LineItemTotal>
                    </PricingDetail>
                    <DeliveryDetail>
                        <ListOfSplitQuantity>
                            <SplitQuantity>
                                <ShipmentStatusEventCoded>NotYetShipped</ShipmentStatusEventCoded>
                                <SplitQuantity>
                                    <ns2:QuantityValue>111</ns2:QuantityValue>
                                    <ns2:UnitOfMeasurement>
                                        <ns2:UOMCoded>Other</ns2:UOMCoded>
                                        <ns2:UOMCodedOther>PCE</ns2:UOMCodedOther>
                                    </ns2:UnitOfMeasurement>
                                </SplitQuantity>
                                <EstimatedDeliveryDate>2003-01-01T00:00:01+02:00</EstimatedDeliveryDate>
                                <SplitQuantityNote></SplitQuantityNote>
                            </SplitQuantity>
                        </ListOfSplitQuantity>
                    </DeliveryDetail>
                    <LineItemNote>Item text manually entered</LineItemNote>
                    <ListOfStructuredNote>
                        <ns2:StructuredNote>
                            <ns2:GeneralNote>Material PO text manually entered</ns2:GeneralNote>
                            <ns2:NoteID>Material PO text</ns2:NoteID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                                <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                            </ns2:Agency>
                        </ns2:StructuredNote>
                        <ns2:StructuredNote>
                            <ns2:GeneralNote>Info Record PO Text manually entered</ns2:GeneralNote>
                            <ns2:NoteID>Info record PO text</ns2:NoteID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                                <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                            </ns2:Agency>
                        </ns2:StructuredNote>
                        <ns2:StructuredNote>
                            <ns2:GeneralNote>Delivery text manually entered</ns2:GeneralNote>
                            <ns2:NoteID>Delivery text</ns2:NoteID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                                <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                            </ns2:Agency>
                        </ns2:StructuredNote>
                    </ListOfStructuredNote>
                </OriginalItemDetailWithChanges>
            </OrderResponseItemDetail>
            <OrderResponseItemDetail>
                <ItemDetailResponseCoded>ItemAccepted</ItemDetailResponseCoded>
                <ShipmentStatusEvent>
                    <ns2:ShipDate>2003-01-01T00:00:01+02:00</ns2:ShipDate>
                </ShipmentStatusEvent>
                <OriginalItemDetailWithChanges>
                    <BaseItemDetail>
                        <LineItemNum>
                            <ns2:BuyerLineItemNum>11</ns2:BuyerLineItemNum>
                        </LineItemNum>
                        <ItemIdentifiers>
                            <ns2:PartNumbers>
                                <ns2:SellerPartNumber>
                                    <ns2:PartID>R-3456</ns2:PartID>
                                </ns2:SellerPartNumber>
                                <ns2:BuyerPartNumber>
                                    <ns2:PartID>R-3456</ns2:PartID>
                                </ns2:BuyerPartNumber>
                            </ns2:PartNumbers>
                        </ItemIdentifiers>
                        <ResponseQuantity>
                            <ns2:QuantityValue>1</ns2:QuantityValue>
                            <ns2:UnitOfMeasurement>
                                <ns2:UOMCoded>Other</ns2:UOMCoded>
                                <ns2:UOMCodedOther>PCE</ns2:UOMCodedOther>
                            </ns2:UnitOfMeasurement>
                        </ResponseQuantity>
                        <OrderedQuantity>
                            <ns2:QuantityValue>1</ns2:QuantityValue>
                            <ns2:UnitOfMeasurement>
                                <ns2:UOMCoded>Other</ns2:UOMCoded>
                                <ns2:UOMCodedOther>PCE</ns2:UOMCodedOther>
                            </ns2:UnitOfMeasurement>
                        </OrderedQuantity>
                    </BaseItemDetail>
                    <PricingDetail>
                        <ns2:ListOfPrice>
                            <ns2:Price>
                                <ns2:PricingType>
                                    <ns2:PriceTypeCoded>PerEach</ns2:PriceTypeCoded>
                                </ns2:PricingType>
                                <ns2:UnitPrice>
                                    <ns2:UnitPriceValue>1000.00</ns2:UnitPriceValue>
                                    <ns2:Currency>
                                        <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                                    </ns2:Currency>
                                </ns2:UnitPrice>
                                <ns2:CalculatedPriceBasisQuantity>
                                    <ns2:QuantityValue>1</ns2:QuantityValue>
                                    <ns2:UnitOfMeasurement>
                                        <ns2:UOMCoded>EA</ns2:UOMCoded>
                                    </ns2:UnitOfMeasurement>
                                </ns2:CalculatedPriceBasisQuantity>
                            </ns2:Price>
                        </ns2:ListOfPrice>
                        <ns2:LineItemTotal>
                            <ns2:MonetaryAmount>1000.00</ns2:MonetaryAmount>
                        </ns2:LineItemTotal>
                    </PricingDetail>
                    <DeliveryDetail>
                        <ListOfSplitQuantity>
                            <SplitQuantity>
                                <ShipmentStatusEventCoded>NotYetShipped</ShipmentStatusEventCoded>
                                <SplitQuantity>
                                    <ns2:QuantityValue>1</ns2:QuantityValue>
                                    <ns2:UnitOfMeasurement>
                                        <ns2:UOMCoded>Other</ns2:UOMCoded>
                                        <ns2:UOMCodedOther>PCE</ns2:UOMCodedOther>
                                    </ns2:UnitOfMeasurement>
                                </SplitQuantity>
                                <EstimatedDeliveryDate>2003-01-01T00:00:01+02:00</EstimatedDeliveryDate>
                                <SplitQuantityNote></SplitQuantityNote>
                            </SplitQuantity>
                        </ListOfSplitQuantity>
                    </DeliveryDetail>
                    <LineItemNote>Item text manually entered</LineItemNote>
                    <ListOfStructuredNote>
                        <ns2:StructuredNote>
                            <ns2:GeneralNote>Material PO text manually entered</ns2:GeneralNote>
                            <ns2:NoteID>Material PO text</ns2:NoteID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                                <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                            </ns2:Agency>
                        </ns2:StructuredNote>
                        <ns2:StructuredNote>
                            <ns2:GeneralNote>Info Record PO Text manually entered</ns2:GeneralNote>
                            <ns2:NoteID>Info record PO text</ns2:NoteID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                                <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                            </ns2:Agency>
                        </ns2:StructuredNote>
                        <ns2:StructuredNote>
                            <ns2:GeneralNote>Delivery text manually entered</ns2:GeneralNote>
                            <ns2:NoteID>Delivery text</ns2:NoteID>
                            <ns2:Agency>
                                <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                                <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                            </ns2:Agency>
                        </ns2:StructuredNote>
                    </ListOfStructuredNote>
                </OriginalItemDetailWithChanges>
            </OrderResponseItemDetail>
        </ListOfOrderResponseItemDetail>
    </OrderResponseDetail>
    <OrderResponseSummary>
        <OriginalOrderSummary>
            <OrderTotal>
                <ns2:MonetaryAmount>2110.00</ns2:MonetaryAmount>
                <ns2:Currency>
                    <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                </ns2:Currency>
            </OrderTotal>
        </OriginalOrderSummary>
        <RevisedOrderSummary>
            <OrderSubTotal>
                <ns2:MonetaryAmount>2110.00</ns2:MonetaryAmount>
            </OrderSubTotal>
            <OrderTotal>
                <ns2:MonetaryAmount>2110.00</ns2:MonetaryAmount>
                <ns2:Currency>
                    <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                </ns2:Currency>
            </OrderTotal>
        </RevisedOrderSummary>
    </OrderResponseSummary>
</OrderResponse>
