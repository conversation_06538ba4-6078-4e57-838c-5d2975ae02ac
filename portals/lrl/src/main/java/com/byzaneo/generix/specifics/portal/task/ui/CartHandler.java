package com.byzaneo.generix.specifics.portal.task.ui;

import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.ui.util.MessageHelper.warn;
import static com.byzaneo.generix.specifics.portal.task.LorealOrderEditionTask.CART_VIEW;
import static com.byzaneo.generix.specifics.util.LorealHelper.toDate;
import static com.byzaneo.generix.specifics.util.LorealHelper.toLocalDate;
import static com.byzaneo.portal.util.PortalHelper.findFirstPageByPortletContentId;
import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static java.math.BigDecimal.ROUND_HALF_EVEN;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.slf4j.LoggerFactory.getLogger;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import com.byzaneo.generix.customer.bean.Furniture;
import com.byzaneo.generix.customer.service.FurnitureService;
import com.byzaneo.generix.service.TaskService;
import com.byzaneo.generix.specifics.bean.Cart;
import com.byzaneo.generix.specifics.portal.task.LorealOrderEditionTask;
import com.byzaneo.generix.specifics.service.CartService;
import com.byzaneo.generix.ui.SessionHandler;
import com.byzaneo.generix.xcbl.ui.edition.CartSessionParameter;
import com.byzaneo.portal.bean.*;
import com.byzaneo.task.bean.TaskDefinition;

import javax.annotation.PostConstruct;
import javax.faces.bean.*;
import javax.faces.context.FacesContext;

import org.slf4j.Logger;

@ManagedBean(name = CartHandler.MANAGED_BEAN_NAME)
@SessionScoped
public class CartHandler {

  public static final Logger log = getLogger(CartHandler.class);

  public static final String MANAGED_BEAN_NAME = "gnxCartHandler";

  private CartService cartService;

  private FurnitureService furnitureService;

  private TaskService taskService;

  private SessionHandler sessionHandler;

  private BigDecimal totalCartsAmount;

  private final int PRECISION = 2;

  private String nextPortletPage;

  @PostConstruct
  public void init() {
    this.cartService = getSpringBean(CartService.class, CartService.SERVICE_NAME);
    this.furnitureService = getSpringBean(FurnitureService.class, FurnitureService.SERVICE_NAME);
    this.taskService = getSpringBean(TaskService.class, TaskService.SERVICE_NAME);
    this.sessionHandler = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
  }

  public int getNumberOfArticles() {
    Cart cart = getUserCart();
    return cart == null ? 0 : cart.getItems()
        .size();
  }

  public BigDecimal getTotalAmount() {
    Cart cart = getUserCart();
    return cart == null ? BigDecimal.ZERO : cart.getItems()
        .stream()
        .map(i -> i.getPrice()
            .multiply(i.getQuantity()))
        .reduce(BigDecimal.ZERO,
            BigDecimal::add)
        .setScale(PRECISION, ROUND_HALF_EVEN);
  }

  public int getNumberOfActiveCarts() {
    return (int) cartService.findAll()
            .stream()
            .filter(this::isCartActive).count();
  }

  public BigDecimal getTotalCartsAmount() {
    totalCartsAmount = BigDecimal.ZERO;
    cartService.findAll()
        .stream()
        .filter(this::isCartActive)
        .collect(Collectors.toList())
        .forEach(cart ->
    {
          BigDecimal temp = cart.getItems()
              .stream()
              .map(i -> i.getPrice()
                  .multiply(i.getQuantity()))
              .reduce(BigDecimal.ZERO,
                  BigDecimal::add)
              .setScale(PRECISION, ROUND_HALF_EVEN);
          totalCartsAmount = totalCartsAmount.add(temp);
        });
    return totalCartsAmount;
  }

  public void goToStep2() {
    Map<String, Object> facesSessionMap = FacesContext.getCurrentInstance()
        .getExternalContext()
        .getSessionMap();
    facesSessionMap.put(CartSessionParameter.FROM_CART.getName(), Boolean.TRUE);
    facesSessionMap.put(CartSessionParameter.STEP2.getName(), Boolean.TRUE);
    facesSessionMap.put(CART_VIEW, Boolean.TRUE);
  }

  public void goToStep3() {
    Map<String, Object> facesSessionMap = FacesContext.getCurrentInstance()
        .getExternalContext()
        .getSessionMap();
    facesSessionMap.put(CartSessionParameter.FROM_CART.getName(), Boolean.TRUE);
    facesSessionMap.put(CartSessionParameter.STEP3.getName(), Boolean.TRUE);
  }

  public String goToNextPortlet(Portal portal) {
    if (nextPortletPage == null) {
      Object action = resolveLorealOrderEditionTask();

      Page page = ofNullable(getDefinition(action)).map(TaskDefinition::getId)
          .map(String::valueOf)
          .map(id -> findFirstPageByPortletContentId(portal, id))
          .orElse(null);

      if (page == null) {
        warn("No Page defined for action : %s", getDefinition(action).getTitle()); // I18N
        return null;
      }
      nextPortletPage = page.getId();
    }
    return nextPortletPage;
  }

  public Object resolveLorealOrderEditionTask() {
    return this.taskService.getTypes()
            .stream()
            .filter(taskType -> LorealOrderEditionTask.class.equals(taskType.getType()))
            .flatMap(taskType -> this.taskService.getTasks(this.sessionHandler.getInstance(), taskType)
                    .stream())
            .collect(toList())
            .stream()
            .findFirst()
            .orElse(null);
  }

  public Date getValidityDate() {
    Cart cart = getUserCart();
    if (cart != null) {
      return toDate(toLocalDate(new Date(cart.getDateCreation()
          .getTime())).plusDays(2));
    }
    return null;
  }

  public boolean isCartActive() {
    Cart cart = getUserCart();
    if (cart == null)
      return false;
    Date date = java.util.Date.from(
        LocalDate.now()
            .minusDays(2)
            .atStartOfDay()
            .atZone(ZoneId.systemDefault())
            .toInstant());
    boolean cartIsActive = cart.getDateCreation()
        .after(date);
    if (!cartIsActive) {
      cartService.removeByUser(this.sessionHandler.getUser());
    }
    return cartIsActive;
  }

  public boolean isCartActive(Cart cart) {
    if (cart == null)
      return false;
    if (furnitureService.findFurnitureById(cart.getFurnitureTypeCode()) == null ||
        furnitureService.findFurnitureById(cart.getFurnitureBrandCode()) == null) {
      return false;
    }
    else {
      Date date = java.util.Date.from(
          LocalDate.now()
              .minusDays(2)
              .atStartOfDay()
              .atZone(ZoneId.systemDefault())
              .toInstant());
      return cart.getDateCreation()
          .after(date);
    }
  }

  private Cart getUserCart() {
    Cart cart;
    cart = cartService.findByUser(this.sessionHandler.getUser());
    if (cart != null) {
      FurnitureService fs = getSpringBean(FurnitureService.class, FurnitureService.SERVICE_NAME);
      Furniture furniture = fs.findFurnitureById(cart.getFurnitureTypeCode());
      if (furniture == null) {
        cartService.removeByUser(this.sessionHandler.getUser());
        return null;
      }
    }
    return cart;
  }

}
