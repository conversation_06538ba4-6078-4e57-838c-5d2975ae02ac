/**
 * 
 */
package com.byzaneo.generix.customers.batch.components.article;

import static com.byzaneo.generix.customers.task.util.LorealIntegrationHelper.POINT_EXPEDITION_CSV;

import java.util.*;

import javax.annotation.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.namedparam.*;

import com.byzaneo.generix.customer.bean.Article;
import com.byzaneo.generix.customers.batch.components.LorealItemProcessor;
import com.byzaneo.generix.customers.batch.mapper.wrapper.*;
import com.byzaneo.generix.customers.task.util.LorealIntegrationHelper;

/**
 * <AUTHOR>
 * @company Generix Group
 * @date 18 nov. 2018
 */
/**
 * <AUTHOR>
 * @company Generix Group
 * @date 4 déc. 2018
 */
public class LorealArticleItemProcessor extends LorealItemProcessor<Article, Article> {

  private static final String BRAND_LABEL = "BRAND_LABEL";

  private static final String SUB_BRAND = "SUB_BRAND";

  private static final String NUANCE = "NUANCE";

  private static final String SUB_AXE = "SUB_AXE";

  private static final String CLASSE = "CLASSE";

  private static final String FUNCTION = "FUNCTION";

  private static final String QUERY_SELECT_LIB_HIEPDT = "SELECT LIB_HIEPDT FROM CUSTOMERMIG_PRODUCT_HIERARCHY " +
      "WHERE COD_HIEPDT = :COD_HIEPDT ";

  @Autowired
  private NamedParameterJdbcTemplate jdbcTemplate;

  private Map<String, Map<String, String>> cachingValue = new HashMap<>();

  @PostConstruct
  public void init() {
    cachingValue.put(FUNCTION, new HashMap<>());
    cachingValue.put(CLASSE, new HashMap<>());
    cachingValue.put(SUB_AXE, new HashMap<>());
    cachingValue.put(NUANCE, new HashMap<>());
    cachingValue.put(SUB_BRAND, new HashMap<>());
    cachingValue.put(BRAND_LABEL, new HashMap<>());
  }

  @Override
  public Article process(Article item) throws Exception {
    item.setExpPointLabel(getAdditionalInfo(POINT_EXPEDITION_CSV, item.getExpPointCode()));

    Optional.ofNullable(getAdditionalInfo(LorealIntegrationHelper.RUPTURE_PRODUIT_CSV, item.getId(), ProductRuptureWrapper.class))
        .ifPresent(ruptureWrapper ->
        {
          item.setDisponibilityDate(ruptureWrapper.getDisponibilityDate());
          item.setRuptureCode(ruptureWrapper.getRuptureType());
          item.setComment(ruptureWrapper.getRuptureComment());
          item.setRuptureLabel(ruptureWrapper.getRuptureLabel());
        });

    Optional.ofNullable(getAdditionalInfo(LorealIntegrationHelper.HIT_PARADE_CSV, item.getGencode(), HitParadeWrapper.class))
        .ifPresent(paradeWrapper ->
        {
          item.setNewArticle(paradeWrapper.getNewArticle());
          item.setTopArticle(paradeWrapper.getTopArticle());
        });
    item.setBrandLabel(getValueFromCodeAndType(item.getBrandLabel(), BRAND_LABEL));
    item.setSubBrand(getValueFromCodeAndType(item.getSubBrand(), SUB_BRAND));
    item.setNuance(getValueFromCodeAndType(item.getNuance(), NUANCE));
    item.setSubAxe(getValueFromCodeAndType(item.getSubAxe(), SUB_AXE));
    item.setClasse(getValueFromCodeAndType(item.getClasse(), CLASSE));
    item.setFunction(getValueFromCodeAndType(item.getFunction(), FUNCTION));
    return item;
  }

  /**
   * Get Label of the article by code and type in the cache. In case the label are not present we return code[0]
   * 
   * @param code -> template of the code : "COD_HIEPDT"
   * @param type
   * @return
   */
  String getValueFromCodeAndType(String code, String type) {
    return cachingValue.get(type)
        .computeIfAbsent(code, k ->
        {
          String query = QUERY_SELECT_LIB_HIEPDT;
          if (StringUtils.isBlank(k)) {
            return null;
          }
          try {
            MapSqlParameterSource namedParameters = new MapSqlParameterSource().addValue("COD_HIEPDT", k);
            return jdbcTemplate.queryForObject(
                query, namedParameters,
                (rs, rowNum) -> rs.getString("LIB_HIEPDT"));
          }
          catch (EmptyResultDataAccessException e) {
            // TODO Do something to handle the error.
            return k;
          }
        });
  }

  @PreDestroy
  public void destroy() {
    cachingValue.clear();
  }
}
