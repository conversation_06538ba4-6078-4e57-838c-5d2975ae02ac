--
INSERT INTO SEC_USER (USER_ID,USER_<PERSON>OGI<PERSON>,USER_<PERSON>U<PERSON><PERSON><PERSON>,USER_EMAIL,USER_TYPE,USER_DISABLED,USER_LOGIN_COUNT,USER_LOGIN_DATE,USER_CREATION,USER_MODIFICATION,USER_FIRSTNAME,USER_LASTNAME) VALUES ('userID','userID','Administrator','<EMAIL>','USER', false, 0, NOW(), NOW(), NOW(), 'Super', 'Administrator')
--
INSERT INTO CUSTOMER_AFFAIR (COD_AFFAIR, LIB_AFFAIR, NUM_ORD_AFFAIR, LIBCOU_AFFAIR) VALUES ('1', 'Affair', '1', 'Affair');
INSERT INTO CUSTOMER_ORGANIZATION (COD_ORGCOM, COD_AFFAIR) VALUES ('ORG_1', '1');
--
INSERT INTO CUSTOMER_CLIENT (NUM_CLIENT, COD_CA<PERSON>L, ADR_NUMVOI, ARR_CLT, BIN_B2B_SAISIE_CDE, BIN_B2B_SUIVI_CDE, BIN_BLOCAGE, COD_SURFACE, COD_TYPAPPR, COD_TYPINT, COD_TYPMAG, COD_POSTAL, NOM_CONCATENE_CLIENT, COD_NIVHIECLI, LIB_FORMEJURIDIQUE, LIB_SURFACE, NOM_CLIENT, NOM_CLIENS, NOM_HIERARCHIE_CLIENT, COD_AFFAIR, COD_ORGCOM, NUM_CLIENTSUP, HIERARCHY_KEY) VALUES('11', '02', 'Adresse 1', 'Groupe d\unité de quantité', b'1', b'1', b'1', 'XXX', 'DIRECT', 'code_inter', 'code_mag', '321432', 'Client 11 name', '1', 'Forme Juridique', 'surface label', 'Client 11 name', 'name cliens', 'Hierarchy Name Client 11', '1', 'ORG_1', null, null),('1', '02', 'Adresse 1', 'Groupe d\unité de quantité', b'1', b'1', b'1', 'XXX', 'DIRECT', 'code_inter', 'code_mag', '321432', 'Client 1 name', '1', 'Forme Juridique', 'surface label', 'Client 1 name full', 'name cliens', 'Hierarchy Name Client 1', '1', 'ORG_1', '11', '1>11'),('50', '02', 'Adresse 1', 'Groupe d\unité de quantité', b'1', b'1', b'1', 'XXX', 'DIRECT', 'code_inter', 'code_mag', '321432', 'Client 50 name', '1', 'Forme Juridique', 'surface label', 'Client 50 name full', 'name cliens', 'Hierarchy Name Client 50', '1', 'ORG_1', null, null);
--
INSERT INTO CUSTOMER_FILIERE_CLIENT (`TYPE_LIEU_FONCTION`, `ADRESSE_LIEU_FONCTION`, `TYP_BLI`, `COD_DEV`, `FIL_DEFAUT`, `BIN_LIVVEN`, `BIN_LIVLUN`, `BIN_LIVSAM`, `BIN_LIVDIM`, `BIN_LIVJEU`, `BIN_LIVMAR`, `BIN_LIVMER`, `NAME_LIEU_FONCTION`, `TOWN_LIEU_FONCTION`, `NUM_LIEU_FONCTION`, `NUM_CLIENT`, `ID_CLIENT`) VALUES ('DeliveryParty', '224 BLD DE LA PLAGE', 'BLISTER', 'EUR', 'X', b'1', b'1', b'0', b'0', b'1', b'1', b'1', 'LECL ARCACHON LECLERC SN DISARC', 'ARCACHON', 1, 1, '1_504_02_04');
--
INSERT INTO CUSTOMER_PRODUCT_HIERARCHY (COD_HIEPDT, LIB_HIEPDT, COD_NIVHIEPDT, NUM_ORD, COD_AFFAIR, COD_HIEPDTSUP) VALUES ('PS', 'Addidas', 2, 625, '1', NULL), ('PP', 'Nike', 1, 2000, '1', NULL), ('PP02', 'Nike France', 2, 625, '1', 'PP'), ('PP0202', 'Just Do It', 3, 650, '1', 'PP02'), ('PP02025K', 'Nike Futsal', 4, 554, '1', 'PP0202'), ('PP02025K01', 'Nike Futsal Synthetique', 5, 1, '1', 'PP02025K'), ('PP02025K0199', 'Crampons', 6, 625, '1', 'PP02025K01'), ('PP02025K019901', 'Mercurial', 7, 200, '1', 'PP02025K0199'), ('PP02025K01990102', 'Mercurial 2018', 8, 245, '1', 'PP02025K019901'), ('PP02025K0199010211', 'article 11', 10, 1, '1', 'PP02025K01990102'), ('PP02025K0199010212', 'article 12', 10, 2, '1', 'PP02025K01990102'), ('PP02025K0199010213', 'article 13', 10, 1, '1', 'PP02025K01990102');
--
INSERT INTO CUSTOMER_ARTICLE(COD_ARTICLE, COD_MARQUE, COD_GPEART1, NBR_MESNET, DAT_FERVEN, LIB_COMMENT1, DAT_LIMPRO, DAT_MADCLI, COD_EXPE, LIB_EXPE, COD_GENCOD, HTR_PRODUI, LIB_PRODUI, PRF_PRODUI, COD_UNIMESGEN, COD_GPEMAR, FLAG_NVT, COD_CLASSE, TYP_OBJET, DAT_OUVVEN, PDS_BRUT, TYP_PDT, COD_STATUT, LRG_PRODUI, COD_AFFAIR, COD_HIEPDT) VALUES('11', 'Obey', '3213', '2', '2018-04-19 ', 'comment', '2018-04-19', '2018-04-19', 'CODE_EXPE', 'label expe', 'CODE_GEN', '1000', 'article 11', '100', 'CODE_MES', 'DOCE_GPE', b'1', '12', 'objType', '2018-04-19 ', '321', 'prodType', 'CODE_STATUS', '200', '1', 'PP02025K0199010211'), ('12','Obey', '3213','2', '2018-04-19 ', 'comment', '2018-04-19 ', '2018-04-19 ', 'CODE_EXPE', 'label expe', 'CODE_GEN', '1000', 'article 12', '100', 'CODE_MES', 'DOCE_GPE', b'1', '12', 'objType', '2018-04-19 ', '321', 'prodType', 'CODE_STATUS', '200', '1', 'PP02025K0199010212'), ('13','Obey', '3213', '2', '2018-04-19 ', 'comment', '2018-04-19 ', '2018-04-19 ', 'CODE_EXPE', 'label expe', 'CODE_GEN', '1000', 'article 13', '100', 'CODE_MES', 'DOCE_GPE', b'1', '12', 'objType', '2018-04-19 ', '321', 'prodType', 'CODE_STATUS', '200', '1', 'PP02025K0199010213'), ('50','Obey', '3213', '2', '2018-04-19 ', 'comment', '2018-04-19 ', '2018-04-19 ', 'CODE_EXPE', 'label expe', 'CODE_GEN', '1000', 'article 50', '100', 'CODE_MES', 'DOCE_GPE', b'1', '12', 'objType', '2018-04-19 ', '321', 'prodType', 'CODE_STATUS', '200', '1', 'PP02025K0199010214');
--
INSERT INTO CUSTOMER_PRICE (DAT_DEBVAL, DAT_FINVAL, PRIX_CAT, COD_ARTICLE, COD_HIEPDT, ID) VALUES ('1999-12-31 00:00:00', '2100-01-03 00:00:00', 7.93, '11', 'PP02025K0199010211', '1__20070103_99991231'), ('1999-12-31 00:00:00', '2100-01-03 00:00:00', 8.23, '12', 'PP02025K0199010212', '12__20070103_99991231'), ('1999-12-31 00:00:00', '2100-01-03 00:00:00', 10.23, '13', 'PP02025K0199010213', '13__20070103_99991231');
--
INSERT INTO CUSTOMER_COMPOSITION (NBR_COMPOS, COD_COMPOS, COD_ARTICLE) VALUES (2, '11', '13'), (2, '11', '12'), (2, '12', '13');
--
INSERT INTO CUSTOMER_FURNITURE (`COD_FURNITURE`, `BIN_B2B`, `COD_MARQUE`, `DAT_CRE`, `DAT_FINAPP`, `LIB_FURNITURE`, `LIB_MARQUE`, `ID_CLIENT`, `NUM_ORDRE`, `DAT_DEBAPP`, `TYP_FURNITURE`, `LRG_FURNITURE`) VALUES ('2528', b'1', 'P502', NULL, '2100-01-03 00:00:00', 'Nike-Furniture', 'Nike', NULL, 945, '1999-12-31 00:00:00', 'BLISTER', 440),('2529', b'1', 'P502', NULL, '2100-01-03 00:00:00', 'NikeSport-Furniture', 'Nike', NULL, 945, '1999-12-31 00:00:00', 'BLISTER', 440), ('2530', b'1', 'P503', NULL, '2100-01-03 00:00:00', 'Oakley-Furniture', 'Oakley', NULL, 940, '1999-12-31 00:00:00', 'NU', 400), ('2531', b'1', 'P504', NULL, '2100-01-03 00:00:00', 'MNY CW MIH V3 360CM 07-11', NULL, NULL, 935, '1999-12-31 00:00:00', 'BOTH', 360), ('fid', b'1', 'Obey', NULL, '2100-01-03 00:00:00', NULL, NULL, NULL, 10, '1999-12-31 00:00:00', 'BOTH', 360);
INSERT INTO CUSTOMER_FURNITURE_DETAIL (`COD_FURNITURE`, `COD_ARTICLE`, `COD_GENCOD`, `QTE_DEF`) VALUES ('fid', '11', '11', '6'), ('fid', '12', '12', '3'), ('fid', '13', '13', '1');
--
INSERT INTO CUSTOMER_TARGET_FURNITURE (`COD_CIBLE`, `COD_FURNITURE`) VALUES ('20', '2529'), ('11', '2528'), ('11', '2529'), ('11', '2530');
--
INSERT INTO CUSTOMER_TARGET_CLIENT (`COD_CIBLE`, `NUM_CLIENT`) VALUES ('11', 1), ('20', 1), ('15', 1), ('11', 11);
--
INSERT INTO CUSTOMER_ASSORTIMENT (`COD_ARTICLE`, `NUM_CLIENT`, `COD_AFFAIR`,  `COD_ORGCOM`, `COD_CANAL`, `DAT_DEBAPP`, `DAT_FINAPP`) VALUES ('11', '1', '1', 'ORG_1', '02', '1999-12-31 00:00:00', '2100-01-03 00:00:00'), ('12', '1', '1', 'ORG_1', '02', '1999-12-31 00:00:00', '2100-01-03 00:00:00'), ('13', '1', '1', 'ORG_1', '02', '1999-12-31 00:00:00', '2100-01-03 00:00:00'), ('50', '50', '1', 'ORG_1', '02', '1999-12-31 00:00:00', '2100-01-03 00:00:00');
--
INSERT INTO CUSTOMER_CODE_INTERNE (`COD_NIVHIECLI`, `COD_PDTINT`, `COD_ARTICLE`, `NUM_CLIENT`) VALUES ('05', 'code', '13', '1')
