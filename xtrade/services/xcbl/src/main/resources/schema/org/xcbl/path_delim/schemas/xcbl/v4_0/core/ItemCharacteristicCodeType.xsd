<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="ItemCharacteristicCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the characteristic of an item. This code list is derived from EDIFACT 7081 (Item characteristic code) and X12 750 (Product/process characteristic code)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalSectorialCharacteristics">
                <xsd:annotation>
                    <xsd:documentation> A code issued on a sectorial basis which identifies any additional characteristics of a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Age">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholBeverageClass">
                <xsd:annotation>
                    <xsd:documentation> Class characteristics for different compositions of alcoholic beverages. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholBeverageType">
                <xsd:annotation>
                    <xsd:documentation> A descriptive term that further defines the class of an alcoholic beverage. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Alloy">
                <xsd:annotation>
                    <xsd:documentation> A homogeneous mixture or solid solution usually of two or more metals. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnatomicalOriginOfSample">
                <xsd:annotation>
                    <xsd:documentation> Anatomical origin of sample. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Appearance">
                <xsd:annotation>
                    <xsd:documentation> The outward aspect or semblance. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Artist">
                <xsd:annotation>
                    <xsd:documentation> The performing artist(es) of a recorded song or piece of music. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssemblyRequired">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Author">
                <xsd:annotation>
                    <xsd:documentation> The author of a written work. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BalanceOutArticle">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BasisWeightSize">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Behind-the-CounterDrugs">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeverageAge">
                <xsd:annotation>
                    <xsd:documentation> The period during which, after distillation and before bottling, distilled spirits have been stored in containers. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeverageBrand">
                <xsd:annotation>
                    <xsd:documentation> A grouping of beverage products similar in name only, but of different size, age, proof, quality and flavour. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeverageCategory">
                <xsd:annotation>
                    <xsd:documentation> A description to designate the beverage category. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeverageContainerCharacteristics">
                <xsd:annotation>
                    <xsd:documentation> A description of various beverage container characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeverageFlavour">
                <xsd:annotation>
                    <xsd:documentation> Distinctions from the base product that results in a different taste. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeverageSegment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Binding">
                <xsd:annotation>
                    <xsd:documentation> A description of the type of binding used for a written work. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BottomholeLocationMethod">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the method for locating the lowest part or surface of a works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BottomholePressureMethod">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the method for measuring pressures at the lowest part or surface of a works. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrandGroup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrandGroup-AGroupingOfSimilarBrands">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BureauofAlcoholTobaccoAndFirearmsClassCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BureauOfAlcoholTobaccoAndFirearmsTypeCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersColorDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersItemSizeDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Casing-Liner-TubingType">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CasingOrLinerType">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the protective or covering part of a natural or manufactured object. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfConformity">
                <xsd:annotation>
                    <xsd:documentation> Product in conformity with specifications. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Chassis">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the discrete identification of the chassis characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChemicalFamilyName">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's chemical family name. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Chemistry">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's chemical characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CID-CommercialItemDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Coating">
                <xsd:annotation>
                    <xsd:documentation> Description of any special coating required/available on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoatingOrPaintSystemCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoatingOrPaintSystemName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Collateral">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's accompanying or coinciding characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectionMethodCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Color">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Color-LowerBody">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Color-UpperBody">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialStatus">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommonChemicalName">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's common chemical name. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyFieldCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComplianceMethod">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's compliance method characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Composer">
                <xsd:annotation>
                    <xsd:documentation> The composer of a recorded song or piece of music. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConditionedCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstructionMethod">
                <xsd:annotation>
                    <xsd:documentation> Description of the method of construction. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumableResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify resources that are consumed. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumerInstructions">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlItem">
                <xsd:annotation>
                    <xsd:documentation> Security relevant product with special quality control and control documentation prescriptions. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledSubstance">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the controlled substance characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledSubstance-Class2">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledSubstance-Class3Narcotic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledSubstance-Class3NNon-narcotic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledSubstance-Class4">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledSubstance-Class5">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoordinateDescriptionCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Coordinates">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's coordinates in non-numeric terms. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorrosionResistance">
                <xsd:annotation>
                    <xsd:documentation> The characteristics describing the resistance to chemical deterioration. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Coupling">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentArticle">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentArticleSpares">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsSpecifications">
                <xsd:annotation>
                    <xsd:documentation> Item characteristic is described following Customs specifications. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamageCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamageFault">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Denier">
                <xsd:annotation>
                    <xsd:documentation> The unit of fineness for yarns. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Density">
                <xsd:annotation>
                    <xsd:documentation> A code indicating the relation of weight to volume using non-discrete values. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dimensional">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's dimensional characteristics in non-numeric terms. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DirectionalIndicator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dispersion">
                <xsd:annotation>
                    <xsd:documentation> The separation of visible light into its colour components by refraction or diffraction. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DosageForm">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the physical form of the dosage. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrugEfficacy">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the drug's capacity or ability to produce the desired effects. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrugEfficacyStudyImplementation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DyeLot">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's dye lot characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DyeLotDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EdgeTreatment">
                <xsd:annotation>
                    <xsd:documentation> Description of any special edge treatment required/performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Edition">
                <xsd:annotation>
                    <xsd:documentation> Description of the edition of a written work. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Editor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Electrical">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's electrical characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElectronicallyCleaned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElectronicField">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's electronic field characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Ends-Slitting-Splitting-Cutting">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndTreatment">
                <xsd:annotation>
                    <xsd:documentation> Description of any special treatment required/performed on the ends the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndUseApplication">
                <xsd:annotation>
                    <xsd:documentation> Description of what the end use application of the product will be. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engine">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the discrete identification of the engine characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EnginewithTransmission">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Equipment">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the category of equipment. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EscrowCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EuropeanCommunityRiskClass">
                <xsd:annotation>
                    <xsd:documentation> European community classification "CE" indicating the safety risk of an article. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fabric">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's fabric characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FabricDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FailureAnalysisProcess">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FEDSPEC-FederalSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FED-STD-FederalStandard">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fiber">
                <xsd:annotation>
                    <xsd:documentation> A threadlike or filament forming part of a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FibreTow">
                <xsd:annotation>
                    <xsd:documentation> The fibre of flax, hemp or jute prepared for low-grade spinning. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FieldCode-EIAOrDOD">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FieldTest">
                <xsd:annotation>
                    <xsd:documentation> First series of a new item to be tested by end users. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Filament">
                <xsd:annotation>
                    <xsd:documentation> A fine wire heated electrically to incandescence. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Filter">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's filter characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Finish">
                <xsd:annotation>
                    <xsd:documentation> Description of the finish required/available on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinishOrSurfaceRoughness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Flavor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Flow">
                <xsd:annotation>
                    <xsd:documentation> The movement or run in the manner of a liquid. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fluid">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's fluid characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FoldConfiguration">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's fold configuration characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Font">
                <xsd:annotation>
                    <xsd:documentation> This value identifies a font by name. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForecastDeviation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Forming">
                <xsd:annotation>
                    <xsd:documentation> Description of any forming required/performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Formula">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the formula characteristics in non- numeric terms. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fragrance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fuel">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the fuel characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FunctionalPerformance">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's functional performance characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FurtherIdentifyingCharacteristic">
                <xsd:annotation>
                    <xsd:documentation> Description of further identifying characteristic of a product which enables the product to be distinguished from any similar products. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralMerchandise">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralProductForm">
                <xsd:annotation>
                    <xsd:documentation> Description of general product form. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GenericDrug">
                <xsd:annotation>
                    <xsd:documentation> A drug name specially given in order that it may be freely used without legal restriction. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Goods">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Grade">
                <xsd:annotation>
                    <xsd:documentation> Specification of the grade required/available for the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrainDirection">
                <xsd:annotation>
                    <xsd:documentation> Specifies the direction of the grain of the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrapeVariety">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousMaterial">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardRatingSystem">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HealthAndBeautyAids">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HeatTreatOrAnneal">
                <xsd:annotation>
                    <xsd:documentation> Description of any heat treatment or annealing required/performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Hydraulics">
                <xsd:annotation>
                    <xsd:documentation> The characteristics of a liquid conveyed under pressure through pipes or channels. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Ingredient">
                <xsd:annotation>
                    <xsd:documentation> A component part of a mixture. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialSample">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Injectables">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Key-Word">
                <xsd:annotation>
                    <xsd:documentation> A word which may be used as a search key. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Kit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Labour">
                <xsd:annotation>
                    <xsd:documentation> Characteristic being described is labour. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LabourDoubleTime">
                <xsd:annotation>
                    <xsd:documentation> Characteristic being described is labour double time. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LabourOvertime">
                <xsd:annotation>
                    <xsd:documentation> Characteristic being described is overtime labour. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeadOrCopperSampleType">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeasedResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify the characteristics of a leased resource. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LimitingOperation">
                <xsd:annotation>
                    <xsd:documentation> Code indicating that the item has a limiting operation. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Luster">
                <xsd:annotation>
                    <xsd:documentation> A reflected light or sheen. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MachineRun">
                <xsd:annotation>
                    <xsd:documentation> Description of the machine run characteristics for a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Magnetic">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's magnetic characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MajorGrade">
                <xsd:annotation>
                    <xsd:documentation> Specification of the major grade of the item. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturingMethod">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's manufacturing method characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturingProcess">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarketSegment">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the market segment associated with a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Marking">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Material">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialClassification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialDescription">
                <xsd:annotation>
                    <xsd:documentation> Description of material used to manufacture a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify the characteristics of a material resource. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialStatus-OutsideProcessor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mechanical">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's mechanical characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalSupplies">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Metallographic">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's metallographic characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MILSPEC-MilitarySpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MMSBLM-IndianLandPropertyOrUnitNumber">
                <xsd:annotation>
                    <xsd:documentation>MineralsManagementService/BureauOfLandManagement(IndianLand)PropertyOrUnitNumber</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Moisture">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's moisture characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Multi-Media">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MusicStyle">
                <xsd:annotation>
                    <xsd:documentation> The style of music. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MutuallyDefined">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewArticle">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-HazardousMaterial">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-PrescriptionDrug">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the non-prescription drug's characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NonRecurringResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify a resource that is non recurring. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Nutrition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ObsoleteArticle">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Odor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Odorized">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Options">
                <xsd:annotation>
                    <xsd:documentation> An item available in addition to standard features of a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Orientation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherDirectResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify other direct resources that are charged to a task. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherPhysicalDescription">
                <xsd:annotation>
                    <xsd:documentation> Any other relevant physical description. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Over-the-CounterDrug">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackageIntegrity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Parameter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pattern">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's pattern characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PercentageOfAlcohol">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerforationContinuityIndicator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerforationInterval">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerforationType">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Physical">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's physical characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the physical form of a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Aerosol">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-AsDiluted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Concentrate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Emulsion">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Gas">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Liquid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Powder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Semisolid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalForm-Solid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PipeCoupling">
                <xsd:annotation>
                    <xsd:documentation> A collar with internal threads used to join a section of threaded pipe. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PipelineStream">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's pipeline stream characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrecautionaryInstructions">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrescriptionDrug">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the prescription drug's characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentationEffect">
                <xsd:annotation>
                    <xsd:documentation> To indicate a presentation effect. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Preservative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryGrape">
                <xsd:annotation>
                    <xsd:documentation> The type of grape that comprises the largest percentage of grape in the wine product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrintOrientation">
                <xsd:annotation>
                    <xsd:documentation> The orientation of the back printing on a form to the front printing on the same form. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivateLabelName">
                <xsd:annotation>
                    <xsd:documentation> Describes the private label name of a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessActionTaken">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessOrProductionUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Product">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductApplication">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductDataBaseManagementDescription">
                <xsd:annotation>
                    <xsd:documentation> A description indicating how a product should be managed in a data base.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductLifeCycle">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the period of time between product creation and obsolescence. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Program">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectSubject">
                <xsd:annotation>
                    <xsd:documentation> To identify the subject of a given project. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionalEvent">
                <xsd:annotation>
                    <xsd:documentation> Describes the promotional event associated with a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionalOffer">
                <xsd:annotation>
                    <xsd:documentation> Describes the additions to the basic product for a promotional event. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Proof">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Proprietary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicInformation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Publisher">
                <xsd:annotation>
                    <xsd:documentation> The publisher of a written piece of work as part of the item description. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PureForm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Put-upDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Quality">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QualityAssuranceStatus">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Quality-QualityLevel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecordingMedium">
                <xsd:annotation>
                    <xsd:documentation> The medium on which a musical recording is made. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecurringResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify a recurring resource. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Recycle">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RefurbishedArticle">
                <xsd:annotation>
                    <xsd:documentation> Description to be provided. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegulatoryOrStateDistrict">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Regulatory-StateOrEntityCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinstatedArticle">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RejectionReason">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReservoirCode-Company">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResultsMethodCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RevisedArticle">
                <xsd:annotation>
                    <xsd:documentation> Item design revised. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RevisedDesign">
                <xsd:annotation>
                    <xsd:documentation>  </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RouteOfAdministration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SampleType">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondaryGrape">
                <xsd:annotation>
                    <xsd:documentation> The grape that comprises the second largest percentage of the ingredients used in wine product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecretOrConfidentialInformation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SectionProfile">
                <xsd:annotation>
                    <xsd:documentation> Description of the section and profile of the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SeriesTitle">
                <xsd:annotation>
                    <xsd:documentation> Title of a series of works. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceFeature">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Services">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shade">
                <xsd:annotation>
                    <xsd:documentation> The degree to which a colour is mixed with black or is decreasingly illuminated. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shelf-LifeCode">
                <xsd:annotation>
                    <xsd:documentation> A code indicating the shelf-life of a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShelfTag">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingUnitComponent">
                <xsd:annotation>
                    <xsd:documentation> Any designed component of a fixture or container, typically detachable from the base unit for empty return or for cleaning, which provides rigidity, stability, or security when loaded and are an integral part of the container or shipping device and are essential to its functionality. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipToLine">
                <xsd:annotation>
                    <xsd:documentation> Product without quality control at customer's, and packed according production needs. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipToStock">
                <xsd:annotation>
                    <xsd:documentation> Product without quality control when received. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Silhouette">
                <xsd:annotation>
                    <xsd:documentation> Describes the outline of the item. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Size">
                <xsd:annotation>
                    <xsd:documentation> Description of size in non-numeric terms. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SizeSystem">
                <xsd:annotation>
                    <xsd:documentation> A code identifying a size system, comprising a set of sizes. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SocietyGovernmentAndOrCustomerSpecifications">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Solubility">
                <xsd:annotation>
                    <xsd:documentation> A code indicating the amount of a substance that can be dissolved using a non-discrete value. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Source">
                <xsd:annotation>
                    <xsd:documentation> The derivation of a material thing. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialProcessing">
                <xsd:annotation>
                    <xsd:documentation> Description of any special processing requirements performed or required. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Specification">
                <xsd:annotation>
                    <xsd:documentation> Description of the specification of the item. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateControlled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StorageAndDisplay">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's storage or display characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubcontractResource">
                <xsd:annotation>
                    <xsd:documentation> A code to identify resources that are part of a subcontract. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sub-Product">
                <xsd:annotation>
                    <xsd:documentation> Description of a sub-product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceCondition">
                <xsd:annotation>
                    <xsd:documentation> Description of the surface condition (e.g. roughness) of the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceLocationMethod">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's surface location method characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceProtection">
                <xsd:annotation>
                    <xsd:documentation> Description of the surface protection required/available for the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceTreatment-Chemical">
                <xsd:annotation>
                    <xsd:documentation> Description of any chemical surface treatment required/performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceTreatment-Mechanical">
                <xsd:annotation>
                    <xsd:documentation> Description of any mechanical surface treatment required/performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Synonym">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Temper">
                <xsd:annotation>
                    <xsd:documentation> To bring to a required degree of hardness and elasticity by heating and then cooling. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestingAndInspectionAgencies">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestPanelType">
                <xsd:annotation>
                    <xsd:documentation> Specifies the type of test panel used for the item. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestSampleDirection">
                <xsd:annotation>
                    <xsd:documentation> Description of test sample direction. Used when ordering special testing requirements on a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestSampleFrequency">
                <xsd:annotation>
                    <xsd:documentation> Indication of test sample frequency. Used when ordering special testing requirements on a product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestSampleLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TheoreticalWeightFormula">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TherapeuticClass">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's therapeutic class characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TherapeuticEquivalency">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's therapeutic equivalency characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Threshold">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's threshold characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tint">
                <xsd:annotation>
                    <xsd:documentation> A gradation of colour made by adding white to lessen the saturation. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tire">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Title">
                <xsd:annotation>
                    <xsd:documentation> The title of a work. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TitleInsuranceForm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tow">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Translator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transmission">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the discrete identification of the transmission characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Trim">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the product's trim characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Twist">
                <xsd:annotation>
                    <xsd:documentation> Description of any special twisting requirements for the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TypeAndOrProcess">
                <xsd:annotation>
                    <xsd:documentation> Description of the type and/or process involved in making the product. E.g. in steel, description of the steelmaking process. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Typeface">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TypeOfSample">
                <xsd:annotation>
                    <xsd:documentation> Type of sample such as blood or urine. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TypeOfTest-Inspection">
                <xsd:annotation>
                    <xsd:documentation> Description of type of test or inspection. Used to order special tests to be performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TypeSpinning">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Vehicle">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VendorColorDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VendorSizeDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Vintage">
                <xsd:annotation>
                    <xsd:documentation> The harvest year of the grapes that are part of the composition of a particular wine. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Visual">
                <xsd:annotation>
                    <xsd:documentation> Capable of being seen by the eye or with the aid of optics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VolatileOrganicCompoundControl">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VolumeTitle">
                <xsd:annotation>
                    <xsd:documentation> The title of a volume of work. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarrantyDescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarrantyTypeDescription">
                <xsd:annotation>
                    <xsd:documentation> The warranty type description of the item. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Waste">
                <xsd:annotation>
                    <xsd:documentation> Code indicating unusable material left over from a process of manufacturing. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaxCode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeldsOrSplices">
                <xsd:annotation>
                    <xsd:documentation> Description of any special welds and or splices required/performed on the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellClassification">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the well classification characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellDirection">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the well drilling direction. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellPerforationContinuity">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the well perforation continuity characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellPerforationInterval">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the well perforation interval characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellTestType">
                <xsd:annotation>
                    <xsd:documentation> Code indicating the well test type characteristics. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WindingInstructions">
                <xsd:annotation>
                    <xsd:documentation> Description of any special winding instructions for the product. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WineFruit">
                <xsd:annotation>
                    <xsd:documentation> The fruit that is used as a base to produce a wine. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WineGrowingRegion">
                <xsd:annotation>
                    <xsd:documentation> The area where the grape used to produce a wine was harvested. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YarnCount">
                <xsd:annotation>
                    <xsd:documentation> Describes the fineness of the yarn in the cloth. </xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
