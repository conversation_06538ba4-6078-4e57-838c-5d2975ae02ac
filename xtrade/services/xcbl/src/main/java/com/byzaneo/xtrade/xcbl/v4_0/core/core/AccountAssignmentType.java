//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * provides detailed account assignment information referring to items of internal documents.
 * <p>
 * Java class for AccountAssignmentType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AccountAssignmentType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AccountAssignmentID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AccountAssignmentName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AccountAssignmentValue" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AccountAssignmentType", propOrder = {
    "accountAssignmentID",
    "accountAssignmentName",
    "accountAssignmentValue"
})
public class AccountAssignmentType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "AccountAssignmentID")
  protected String accountAssignmentID;
  @XmlElement(name = "AccountAssignmentName")
  protected String accountAssignmentName;
  @XmlElement(name = "AccountAssignmentValue")
  protected String accountAssignmentValue;

  /**
   * Gets the value of the accountAssignmentID property.
   * 
   * @return possible object is {@link String }
   */
  public String getAccountAssignmentID() {
    return accountAssignmentID;
  }

  /**
   * Sets the value of the accountAssignmentID property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setAccountAssignmentID(String value) {
    this.accountAssignmentID = value;
  }

  /**
   * Gets the value of the accountAssignmentName property.
   * 
   * @return possible object is {@link String }
   */
  public String getAccountAssignmentName() {
    return accountAssignmentName;
  }

  /**
   * Sets the value of the accountAssignmentName property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setAccountAssignmentName(String value) {
    this.accountAssignmentName = value;
  }

  /**
   * Gets the value of the accountAssignmentValue property.
   * 
   * @return possible object is {@link String }
   */
  public String getAccountAssignmentValue() {
    return accountAssignmentValue;
  }

  /**
   * Sets the value of the accountAssignmentValue property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setAccountAssignmentValue(String value) {
    this.accountAssignmentValue = value;
  }

}
