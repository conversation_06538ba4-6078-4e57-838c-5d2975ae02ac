//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * is used to specify either a lower or upper bound, beyond which an allowance or charge will apply.
 * <p>
 * Java class for MonetaryLimitType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MonetaryLimitType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="MonetaryLimitValue" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}Decimal21_6Type"/>
 *         &lt;element name="Currency" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}CurrencyType" minOccurs="0"/>
 *       &lt;/sequence>
 *       &lt;attribute name="SignificanceCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}SignificanceCodeType" />
 *       &lt;attribute name="SignificanceCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonetaryLimitType", propOrder = {
    "monetaryLimitValue",
    "monetaryLimitLineValue",
    "currency"
})
public class MonetaryLimitType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "MonetaryLimitValue", required = true)
  protected BigDecimal monetaryLimitValue;
  @XmlElement(name = "MonetaryLimitLineValue", required = true)
  protected BigDecimal monetaryLimitLineValue;
  @XmlElement(name = "Currency")
  protected CurrencyType currency;
  @XmlAttribute(name = "SignificanceCoded")
  protected SignificanceCodeType significanceCoded;
  @XmlAttribute(name = "SignificanceCodedOther")
  protected String significanceCodedOther;

  /**
   * Gets the value of the monetaryLimitValue property.
   * 
   * @return possible object is {@link BigDecimal }
   */
  public BigDecimal getMonetaryLimitValue() {
    return monetaryLimitValue;
  }

  /**
   * Sets the value of the monetaryLimitValue property.
   * 
   * @param value allowed object is {@link BigDecimal }
   */
  public void setMonetaryLimitValue(BigDecimal value) {
    this.monetaryLimitValue = value;
  }

  public BigDecimal getMonetaryLimitLineValue() {
    return monetaryLimitLineValue;
  }

  public void setMonetaryLimitLineValue(BigDecimal monetaryLimitLineValue) {
    this.monetaryLimitLineValue = monetaryLimitLineValue;
  }

  /**
   * Gets the value of the currency property.
   * 
   * @return possible object is {@link CurrencyType }
   */
  public CurrencyType getCurrency() {
    return currency;
  }

  /**
   * Sets the value of the currency property.
   * 
   * @param value allowed object is {@link CurrencyType }
   */
  public void setCurrency(CurrencyType value) {
    this.currency = value;
  }

  /**
   * Gets the value of the significanceCoded property.
   * 
   * @return possible object is {@link SignificanceCodeType }
   */
  public SignificanceCodeType getSignificanceCoded() {
    return significanceCoded;
  }

  /**
   * Sets the value of the significanceCoded property.
   * 
   * @param value allowed object is {@link SignificanceCodeType }
   */
  public void setSignificanceCoded(SignificanceCodeType value) {
    this.significanceCoded = value;
  }

  /**
   * Gets the value of the significanceCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getSignificanceCodedOther() {
    return significanceCodedOther;
  }

  /**
   * Sets the value of the significanceCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setSignificanceCodedOther(String value) {
    this.significanceCodedOther = value;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((currency == null) ? 0 : currency.hashCode());
    result = prime * result + ((monetaryLimitValue == null) ? 0 : monetaryLimitValue
        .hashCode());
    result = prime * result + ((significanceCoded == null) ? 0 : significanceCoded
        .hashCode());
    result = prime * result + ((significanceCodedOther == null) ? 0
        : significanceCodedOther.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    MonetaryLimitType other = (MonetaryLimitType) obj;
    if (currency == null) {
      if (other.currency != null)
        return false;
    }
    else if (!currency.equals(other.currency))
      return false;
    if (monetaryLimitValue == null) {
      if (other.monetaryLimitValue != null)
        return false;
    }
    else if (!monetaryLimitValue.equals(other.monetaryLimitValue))
      return false;
    if (significanceCoded != other.significanceCoded)
      return false;
    if (significanceCodedOther == null) {
      if (other.significanceCodedOther != null)
        return false;
    }
    else if (!significanceCodedOther.equals(other.significanceCodedOther))
      return false;
    return true;
  }

}
