package com.byzaneo.xtrade.xcbl.bean;

import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.commons.util.BeanDescriptorHelper.fromClasspath;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getDateFromInvoiceDateCoded;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getIdentValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getIdentValueFromContractID;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getName1Value;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getPrimaryReferenceRefNum;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.setHashCode;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_GROSS;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_NET;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.math.BigDecimal.ZERO;
import static java.util.Collections.emptyList;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.*;
import java.util.*;
import java.util.concurrent.atomic.*;
import java.util.function.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.expression.*;
import org.springframework.stereotype.Component;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.exception.*;
import com.byzaneo.commons.util.BeanDescriptorHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.SecurityService;
import com.byzaneo.xtrade.IndexingException;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.service.ExchangeRateService;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;


/**
 * XCBL Invoice Indexer.
 *
 * <AUTHOR> Boutour
 * @date 23/07/2015
 */
@Component(InvoiceIndexer.INDEXER_NAME)
public class InvoiceIndexer extends XcblIndexer<Invoice> {

  private static final Logger log = getLogger(InvoiceIndexer.class);

  public static final String INDEXER_NAME = "xcblInvoiceIndexer";
  public static final String INDEXER_SCHEMA = "schema/org/xcbl/path_delim/schemas/xcbl/v4_0/financial/v1_0/financial.xsd";
  private static final String INVOICE_DETAILS_DESCRIPTOR = "descriptors/invoice-details-columns.xml";
  private static final String VALUE_ADDED_TAX_DESCRIPTOR = "descriptors/invoice-valueaddedtax-columns.xml";
  private static final String ALLOWANCE_AND_CHARGE_DESCRIPTOR = "descriptors/invoice-allowanceandcharge-columns.xml";

  public final static String CODED_OTHER = "Other";
  public static final String FREE_GOODS_QUANTITY_QUANTITY_CODED = "FreeQuantityNotIncluded";
  public static final String NUMBER_OF_UNITS_QUANTITY_CODED = "NumberOfUnits";
  public final static String VALUE_ADDED_TAX = "ValueAddedTax";
  public final static String TAX_REGULATORY_TAX = "Tax-RegulatoryTax";
  private final static String RECEIVED_DATE = "DocumentReceivedDateTime";
  private final static String PREPARATION_DATETIME_DOCUMENT = "PreparationDateTimeOfDocument";
  private final static String CUSTOMER_REFERENCE_NUMBER = "CustomerReferenceNumber";
  private final static String BUYER_PARTY = "buyerParty";
  private final static String SELLER_PARTY = "sellerParty";
  private final static String BILL_TO_PARTY = "billToParty";
  private final static String SHIP_TO_PARTY = "shipToParty";
  private final static String OTHER_PARTY = "otherParty";
  private final static String FR_INSEE = "FR-INSEE";
  private final static String RCS_RCM = "RCS-RCM";
  private final static String CREDIT_INVOICE = "CreditInvoice";
  private final static String RECEIVING_NUMBER = "ReceivingNumber";
  private final static String EARLY_PAYMENT = "earlyPayment";
  public final static String PAYMENT_STATUS = "paymentStatus";
  public final static String DAYS_PAYED_EARLY = "daysPayedEarly";
  public final static String EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE = "earlyPaymentAnnualDiscountRate";
  public final static String EARLY_PAYMENT_DISCOUNT_RATE = "earlyPaymentDiscountRate";
  public final static String EARLY_PAYMENT_AMOUNT_DISCOUNT = "earlyPaymentAmountDiscount";

  public final static String EARLY_PAYMENT_CANNOT_PAY_BEFORE = "earlyPaymentCannotPayBefore";

  public final static String TOTAL = "Total";
  private final static String PAYMENT_EFFECTIVE = "PaymentEffective";

  private BeanDescriptor invoiceDetailDescriptor;
  private BeanDescriptor valueAddedDescriptor;
  private BeanDescriptor allowanceAndChargeDescriptor;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  SecurityService securityService;

  @Autowired(required = false)
  ExchangeRateService exchangeRateService;

  @Override
  public String getName() {
    return INDEXER_NAME;
  }

  @Override
  public final String getSchemas() {
    return INDEXER_SCHEMA;
  }

  @SuppressWarnings("unchecked")
  @Override
  public Class<InvoiceIndex> getIndexType() {
    return InvoiceIndex.class;
  }

  @Override
  public String getDescriptorPath(IndexType type) {
    switch (type) {
    case INDEX:
      return "descriptors/invoice-columns.xml";
    case ARCHIVE:
      return "descriptors/archive-invoice-columns.xml";
    default:
      throw new UnsupportedOperationException("Not supported yet " + type);
    }
  }

  protected BeanDescriptor getInvoiceDetailDescriptor() {
    return getDescriptor(invoiceDetailDescriptor, INVOICE_DETAILS_DESCRIPTOR);
  }

  protected BeanDescriptor getValueAddedTaxDescriptor() {
    return getDescriptor(valueAddedDescriptor, VALUE_ADDED_TAX_DESCRIPTOR);
  }

  protected BeanDescriptor getAllowanceAndChargeDescriptor() {
    return getDescriptor(allowanceAndChargeDescriptor, ALLOWANCE_AND_CHARGE_DESCRIPTOR);
  }

  private BeanDescriptor getDescriptor(BeanDescriptor beanDescriptor, String descriptorFile) {
    if (beanDescriptor == null) {
      try {
        beanDescriptor = fromClasspath(descriptorFile);
      }
      catch (IOException e) {
        log.error("Unable to read file descriptor ", e);
      }
    }
    return beanDescriptor;
  }

  /**
   * @see com.byzaneo.xtrade.xcbl.bean#createIndexable(X Bean)
   */
  @Override
  public XcblDocument createIndexable(Invoice invoice) throws IndexingException {
    BeanDescriptor descriptor = getDescriptor();

    if (descriptor != null) {
      try {
        InvoiceIndex invoiceIndex = BeanDescriptorHelper.createIndexable(invoice, descriptor, InvoiceIndex.class);
        parseFreeTexts(invoiceIndex, invoice);
        parseValueAddedTax(invoiceIndex, invoice);
        parseAllowanceAndCharge(invoiceIndex, invoice);
        parseInvoiceDetail(invoiceIndex, invoice);
        parseContractID(invoiceIndex, invoice);
        parseOtherID(invoiceIndex, invoice);
        parseOtherParty(invoiceIndex, invoice);
        parseProcessDateTime(invoiceIndex, invoice);
        parsePreparationDateTime(invoiceIndex, invoice);
        parseCustomerReference(invoiceIndex, invoice);
        updateIndexId(invoiceIndex, invoice);
        parseReceptionNumber(invoiceIndex, invoice);
        parseEarlyPayment(invoiceIndex, invoice);
        parseInvoiceDateOfFinalPayment(invoiceIndex, invoice);
        parsePartyAddressEmail(invoiceIndex, invoice, BUYER_PARTY);
        parsePartyAddressEmail(invoiceIndex, invoice, SELLER_PARTY);
        parsePartyAddressEmail(invoiceIndex, invoice, BILL_TO_PARTY);
        parsePartyAddressEmail(invoiceIndex, invoice, SHIP_TO_PARTY);
        parseInvoiceHeaderAllowOrCharge(invoiceIndex, invoice);
        copyWorkflowInfo(invoiceIndex, invoice);
        parseIDForOtherParties(invoiceIndex, invoice);
        setHashCode(invoiceIndex);
        return invoiceIndex;
      }
      catch (InvalidPathException e) {
        log.error("File descriptor is invalid", e);
        throw new IndexingException(e, "Error setting XCBL index for document type: %s", getXcblType());
      }
      catch (EvaluationException | ParseException | InstantiationException | IllegalAccessException
          | InvocationTargetException | NoSuchMethodException e) {
        throw new IndexingException(e, "Error setting XCBL index for type: %s", getXcblType());
      }
      catch (UndefinedPathException e) {
        log.debug("Path attribute is not defined in bean descriptor of ", getXcblType());
      }
    }
    else {
      throw new IndexingException("No bean descriptor found for document type: %s", getXcblType());
    }
    return invoice;
  }

  private void parseIDForOtherParties(InvoiceIndex invoiceIndex, Invoice invoice) {
    Optional<InvoicePartyType> parties = of(invoice)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceParty);
    invoiceIndex.setRemitToPartyID(parties.map(InvoicePartyType::getRemitToParty)
        .map(InvoiceOCRPartyType::getPartyID)
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .orElse(null));
    List<InvoicePartyCodedType> otherParties = parties.map(InvoicePartyType::getListOfPartyCoded)
        .map(ListOfInvoicePartyCodedType::getPartyCoded)
        .orElse(new ArrayList<>());
    for (InvoicePartyCodedType party : otherParties) {
      String id = ofNullable(party).map(InvoicePartyCodedType::getPartyID)
          .map(InvoiceIdentifierType::getIdent)
          .map(ComplexStringType::getValue)
          .orElse(null);
      if (id == null)
        continue;
      if ("Factor".equals(party.getPartyRoleCoded()))
        invoiceIndex.setFactorPartyID(id);
      if ("BuyersAgentOrRepresentative".equals(party.getPartyRoleCoded()))
        invoiceIndex.setBuyerAgentPartyID(id);
      if ("SellersAgentOrRepresentative".equals(party.getPartyRoleCoded()))
        invoiceIndex.setSellerAgentPartyID(id);
      if ("Payer".equals(party.getPartyRoleCoded()))
        invoiceIndex.setPayerPartyID(id);
    }
  }

  private void copyWorkflowInfo(InvoiceIndex destinationInvoiceIndex, Invoice sourceInvoice) {
    String wkfName = sourceInvoice.getWkfName();
    if (wkfName == null) {
      return;
    }
    destinationInvoiceIndex.setWkfName(sourceInvoice.getWkfName());
    destinationInvoiceIndex.setWkfVersion(sourceInvoice.getWkfVersion());
    destinationInvoiceIndex.setWkfStep(sourceInvoice.getWkfStep());
    destinationInvoiceIndex.setWkfNumberOfSteps(sourceInvoice.getWkfNumberOfSteps());
    destinationInvoiceIndex.setWkfCompletionPercentage(sourceInvoice.getWkfCompletionPercentage());
    destinationInvoiceIndex.setWkfLastModificationDate(sourceInvoice.getWkfLastModificationDate());
    destinationInvoiceIndex.setWkfLockedBy(sourceInvoice.getWkfLockedBy());
    destinationInvoiceIndex.setWkfLockedById(sourceInvoice.getWkfLockedById());
    destinationInvoiceIndex.setWkfStepActions(sourceInvoice.getWkfStepActions());
    destinationInvoiceIndex.setWkfStepActors(sourceInvoice.getWkfStepActors());
    destinationInvoiceIndex.setWkfStepActorsList(sourceInvoice.getWkfStepActorsList());
  }

  protected void parseFreeTexts(InvoiceIndex invoiceIndex, Invoice invoice) {
    List<NameValueSetType> nameValueSetTypes = ofNullable(invoice)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getListOfNameValueSet)
        .map(ListOfNameValueSetType::getNameValueSet)
        .filter(CollectionUtils::isNotEmpty)
        .orElse(emptyList());
    List<NameValuePairType> freeTexts = new ArrayList<NameValuePairType>();
    nameValueSetTypes.forEach(x -> {
      freeTexts.addAll(ofNullable(x)
          .map(NameValueSetType::getListOfNameValuePair)
          .map(ListOfNameValuePairType::getNameValuePair)
          .orElse(emptyList()));
    });

    if (CollectionUtils.isNotEmpty(freeTexts)) {
      invoiceIndex.setFreeText01(getFreeTextByQualifier("FreeText01", freeTexts));
      invoiceIndex.setFreeText02(getFreeTextByQualifier("FreeText02", freeTexts));
      invoiceIndex.setFreeText03(getFreeTextByQualifier("FreeText03", freeTexts));
      invoiceIndex.setFreeText04(getFreeTextByQualifier("FreeText04", freeTexts));
      invoiceIndex.setFreeText05(getFreeTextByQualifier("FreeText05", freeTexts));
      invoiceIndex.setFreeText06(getFreeTextByQualifier("FreeText06", freeTexts));
      invoiceIndex.setFreeText07(getFreeTextByQualifier("FreeText07", freeTexts));
      invoiceIndex.setFreeText08(getFreeTextByQualifier("FreeText08", freeTexts));
      invoiceIndex.setFreeText09(getFreeTextByQualifier("FreeText09", freeTexts));
      invoiceIndex.setFreeText10(getFreeTextByQualifier("FreeText10", freeTexts));
      invoiceIndex.setFreeText11(getFreeTextByQualifier("FreeText11", freeTexts));
      invoiceIndex.setFreeText12(getFreeTextByQualifier("FreeText12", freeTexts));
      invoiceIndex.setFreeText13(getFreeTextByQualifier("FreeText13", freeTexts));
      invoiceIndex.setFreeText14(getFreeTextByQualifier("FreeText14", freeTexts));
      invoiceIndex.setFreeText15(getFreeTextByQualifier("FreeText15", freeTexts));
      invoiceIndex.setFreeText16(getFreeTextByQualifier("FreeText16", freeTexts));
      invoiceIndex.setFreeText17(getFreeTextByQualifier("FreeText17", freeTexts));
      invoiceIndex.setFreeText18(getFreeTextByQualifier("FreeText18", freeTexts));
      invoiceIndex.setFreeText19(getFreeTextByQualifier("FreeText19", freeTexts));
      invoiceIndex.setFreeText20(getFreeTextByQualifier("FreeText20", freeTexts));
      invoiceIndex.setFreeText21(getFreeTextByQualifier("FreeText21", freeTexts));
      invoiceIndex.setFreeText22(getFreeTextByQualifier("FreeText22", freeTexts));
      invoiceIndex.setFreeText23(getFreeTextByQualifier("FreeText23", freeTexts));
      invoiceIndex.setFreeText24(getFreeTextByQualifier("FreeText24", freeTexts));
      invoiceIndex.setFreeText25(getFreeTextByQualifier("FreeText25", freeTexts));
      invoiceIndex.setFreeText26(getFreeTextByQualifier("FreeText26", freeTexts));
      invoiceIndex.setFreeText27(getFreeTextByQualifier("FreeText27", freeTexts));
      invoiceIndex.setFreeText28(getFreeTextByQualifier("FreeText28", freeTexts));
      invoiceIndex.setFreeText29(getFreeTextByQualifier("FreeText29", freeTexts));
      invoiceIndex.setFreeText30(getFreeTextByQualifier("FreeText30", freeTexts));
      invoiceIndex.setFreeText31(getFreeTextByQualifier("FreeText31", freeTexts));
      invoiceIndex.setFreeText32(getFreeTextByQualifier("FreeText32", freeTexts));
      invoiceIndex.setFreeText33(getFreeTextByQualifier("FreeText33", freeTexts));
      invoiceIndex.setFreeText34(getFreeTextByQualifier("FreeText34", freeTexts));
      invoiceIndex.setFreeText35(getFreeTextByQualifier("FreeText35", freeTexts));
    }
  }

  private String getFreeTextByQualifier(String qualifier, List<NameValuePairType> freeTexts) {
    return freeTexts.stream()
        .filter(pair -> qualifier.equalsIgnoreCase(pair.getName()))
        .findFirst()
        .map(NameValuePairType::getValue)
        .orElse(null);
  }

  protected void parseValueAddedTax(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (valueAddedTaxIsNotNull(invoice)) {
      List<InvoiceTaxSummaryType> taxSummaryList = invoice.getInvoiceSummary()
          .getListOfTaxSummary()
          .getTaxSummary();
      taxSummaryList.stream()
          .filter(tax -> Objects.nonNull(tax) && (TOTAL.equals(tax.getTaxTypeCoded()) || VALUE_ADDED_TAX.equals(tax.getTaxTypeCoded())))
          .forEach(taxSummary ->
          {
            try {
              ValueAddedTax summaryBreakdownValueAddedTax = BeanDescriptorHelper.createIndexable(taxSummary, getValueAddedTaxDescriptor(),
                  ValueAddedTax.class);
              invoiceIndex.getSummaryBreakdownValueAddedTax()
                  .add(summaryBreakdownValueAddedTax);
            }
            catch (Exception e) {
              log.debug("PathIndexable is not defined in the bean descriptor : ", e.getMessage());
            }
          });
    }
  }

  private boolean valueAddedTaxIsNotNull(Invoice invoice) {
    return invoice.getInvoiceSummary() != null && invoice.getInvoiceSummary()
        .getListOfTaxSummary() != null && isNotEmpty(invoice.getInvoiceSummary()
            .getListOfTaxSummary()
            .getTaxSummary());
  }

  private void parseAllowanceAndCharge(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (allowanceAndChargeIsNotNull(invoice)) {
      List<TotalAllowOrChargeType> totalAllowOrChargeList = invoice.getInvoiceSummary()
          .getAllowOrChargeSummary()
          .getTotalAllowOrCharge();
      totalAllowOrChargeList.stream()
          .filter(
              allowOrCharge -> Objects.nonNull(allowOrCharge) && TAX_REGULATORY_TAX.equals(allowOrCharge.getAllowanceOrChargeDescription()
                  .getServiceCoded()))
          .forEach(allowOrCharge ->
          {
            try {
              RegulatoryTax regulatoryTax = BeanDescriptorHelper.createIndexable(allowOrCharge, getAllowanceAndChargeDescriptor(),
                  RegulatoryTax.class);
              invoiceIndex.getRegulatoryTaxes()
                  .add(regulatoryTax);
            }
            catch (Exception e) {
              log.debug("PathIndexable is not defined in the bean descriptor : ", e.getMessage());
            }
          });
    }
  }

  private boolean allowanceAndChargeIsNotNull(Invoice invoice) {
    return invoice.getInvoiceSummary() != null && invoice.getInvoiceSummary()
        .getAllowOrChargeSummary() != null && invoice.getInvoiceSummary()
            .getAllowOrChargeSummary()
            .getTotalAllowOrCharge() != null;
  }

  private void parseInvoiceDetail(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (invoiceDetailIsNotNull(invoice)) {
      List<InvoiceItemDetailType> invoiceDetailList = invoice.getInvoiceDetail()
          .getListOfInvoiceItemDetail()
          .getInvoiceItemDetail();
      invoiceDetailList.forEach(detail -> {
        try {
          InvoiceDetailIndex detailIndex = BeanDescriptorHelper.createIndexable(detail, getInvoiceDetailDescriptor(),
              InvoiceDetailIndex.class);
          parseNetAndGrossPrice(detail.getInvoicePricingDetail()
              .getListOfPrice()
              .getPrice(), detailIndex);
          processInvoiceDetail(detail, detailIndex);
          invoiceIndex.getDetails()
              .add(detailIndex);
        }
        catch (Exception e) {
          log.debug("PathIndexable is not defined in the bean descriptor : ", e.getMessage());
        }
      });
    }
  }

  public void parseInvoiceHeaderAllowOrCharge(InvoiceIndex invoiceIndex, Invoice sourceInvoice) {
    List<InvoiceAllowOrChargeType> allowOrChargeList = Optional.ofNullable(sourceInvoice)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceAllowancesOrCharges)
        .map(ListOfInvoiceAllowOrChargeType::getAllowOrCharge)
        .orElse(emptyList());
    allowOrChargeList=allowOrChargeList.stream()
        .filter(allOrCh -> MethodOfHandlingCodeType.ALLOWANCE_TO_BE_ISSUED_BY_VENDOR.equals(allOrCh.getMethodOfHandlingCoded()) ||
            MethodOfHandlingCodeType.CALCULATE_AND_ADDTO_INVOICE.equals(allOrCh.getMethodOfHandlingCoded()) ||
            MethodOfHandlingCodeType.CHARGE_TO_BE_PAID_BY_CUSTOMER.equals(allOrCh.getMethodOfHandlingCoded()))
        .collect(Collectors.toList());
    allowOrChargeList.forEach(all -> {
      String indicatorCoded = Optional.of(all)
          .map(InvoiceAllowOrChargeType::getIndicatorCoded)
          .map(IndicatorCodeType::value)
          .orElse(null);
      String refId = Optional.of(all)
          .map(InvoiceAllowOrChargeType::getAllowanceOrChargeDescription)
          .map(InvoiceAllowOrChgDescType::getRefID)
          .orElse(null);
      String listOfDescription = Optional.of(all)
          .map(InvoiceAllowOrChargeType::getAllowanceOrChargeDescription)
          .map(InvoiceAllowOrChgDescType::getListOfDescription)
          .map(ComplexStringType::getValue)
          .orElse(null);
      String serviceCodedAux = Optional.of(all)
          .map(InvoiceAllowOrChargeType::getAllowanceOrChargeDescription)
          .map(InvoiceAllowOrChgDescType::getServiceCoded)
          .orElse(null);
      String serviceCoded = "Other".equals(serviceCodedAux) ? all.getAllowanceOrChargeDescription()
          .getServiceCodedOther() : serviceCodedAux;

      InvoiceHeaderAllowOrChargeIndex invoiceHeaderAllowOrChargeIndex = new InvoiceHeaderAllowOrChargeIndex();
      invoiceHeaderAllowOrChargeIndex.setIndicatorCoded(indicatorCoded);
      invoiceHeaderAllowOrChargeIndex.setRefID(refId);
      invoiceHeaderAllowOrChargeIndex.setListOfDescription(listOfDescription);
      invoiceHeaderAllowOrChargeIndex.setServiceCoded(serviceCoded);
      BigDecimal monetaryAmount = ZERO;
      if (all.getBasisCoded() == null || BasisCodeType.PERCENT.equals(all.getBasisCoded()))
        monetaryAmount = getPercentageMonetaryAmount(all);
      if (BasisCodeType.MONETARY_AMOUNT.equals(all.getBasisCoded()) || monetaryAmount == null)
        monetaryAmount = getMonetaryAmount(all);
      invoiceHeaderAllowOrChargeIndex.setMonetaryAmount(monetaryAmount);
      //common properties are added here
      invoiceIndex.getInvoiceHeaderAllowOrChargeIndexList()
          .add(invoiceHeaderAllowOrChargeIndex);
    });
    populateMonetaryAmountTAC(sourceInvoice, invoiceIndex);
  }

  private void setCommonsTaxProperties(InvoiceIndex invoiceIndex, String currencyReference, BigDecimal rateOfExchangeInvTAC,
      String rateOfExchangeRefNumInvTAC, BigDecimal invoiceTotalTAC, BigDecimal taxableValueTAC, BigDecimal totalTaxAmountTAC,
      BigDecimal totalAmountPayable) {
    invoiceIndex.setToAccountingCurrency(currencyReference);
    invoiceIndex.setRateOfExchangeInvTAC(rateOfExchangeInvTAC);
    invoiceIndex.setDateOfRateOfExchangeInvTAC(new Date());
    invoiceIndex.setRateOfExchangeRefNumInvTAC(rateOfExchangeRefNumInvTAC);
    invoiceIndex.setInvoiceTotalTAC(invoiceTotalTAC);
    invoiceIndex.setTaxableValueTAC(taxableValueTAC);
    invoiceIndex.setTotalTaxAmountTAC(totalTaxAmountTAC);
    invoiceIndex.setTotalAmountPayableTAC(totalAmountPayable);
  }

  public void populateMonetaryAmountTAC(Invoice sourceInvoice, InvoiceIndex invoiceIndex) {
    //Invoice reference currency
    String currencyFromInvoice = getInvoiceCurrency(sourceInvoice);
    //group (company or partner) currency
    String groupCurrency = getRecipientCurrency(sourceInvoice);
    AtomicInteger nbOfDecimals = new AtomicInteger(2);

    ListOfRateOfExchangeDetailType listOfRateOfExchangeDetailType = of(sourceInvoice).map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getListOfRateOfExchangeDetail)
        .orElse(null);

    AtomicBoolean referenceCurrencySet = new AtomicBoolean(FALSE);
    //in this case the currencies are the same
    if (groupCurrency.equals(currencyFromInvoice)) {
      referenceCurrencySet.set(TRUE);
      setInfoEqualsCurrenciesOrRateNotFound(sourceInvoice, invoiceIndex, groupCurrency, "Not applicable");
    }
    else if (!groupCurrency.equals(currencyFromInvoice) && listOfRateOfExchangeDetailType != null) {
      List<RateOfExchangeDetailType> lRateOfExchangeDetail = listOfRateOfExchangeDetailType.getRateOfExchangeDetail();
      if (lRateOfExchangeDetail != null && !lRateOfExchangeDetail.isEmpty()) {
        lRateOfExchangeDetail.forEach(rateOfExchange -> {
          final String refCurrency = of(rateOfExchange).map(RateOfExchangeDetailType::getReferenceCurrency)
              .map(CurrencyType::getCurrencyCoded)
              .map(CurrencyCodeType::value)
              .orElse("");
          final String targetCurrency = of(rateOfExchange).map(RateOfExchangeDetailType::getTargetCurrency)
              .map(CurrencyType::getCurrencyCoded)
              .map(CurrencyCodeType::value)
              .orElse("");
          final BigDecimal currentRateOfExchange = of(rateOfExchange).map(RateOfExchangeDetailType::getRateOfExchange)
              .orElse(null);
          final BigDecimal currentInverseRateOfExchange = of(rateOfExchange).map(RateOfExchangeDetailType::getInverseRateOfExchange)
              .orElse(null);
          final Date dateOfRateOfExchangeInvTAC = of(rateOfExchange).map(RateOfExchangeDetailType::getDateOfRateOfExchange)
              .orElse(new Date());
          final List<ReferenceType> referenceTypeList = of(rateOfExchange).map(RateOfExchangeDetailType::getListOfRateOfExchangeReference)
              .map(ListOfReferenceType::getReference)
              .orElse(emptyList());
          final String refnum = referenceTypeList.stream()
              .findFirst()
              .map(ReferenceType::getRefNum)
              .orElse(invoiceIndex.getInvoiceNumber());

          if (refCurrency.equals(currencyFromInvoice) && targetCurrency.equals(groupCurrency)) {
            setReferenceCurrencyValues(invoiceIndex, groupCurrency, currentRateOfExchange, dateOfRateOfExchangeInvTAC, refnum);
            referenceCurrencySet.set(TRUE);
            nbOfDecimals.set(2);
            calculateTotalsTaxesBasedOnRateExchangeandNbOfDecimals(sourceInvoice, invoiceIndex, nbOfDecimals.get(),
                invoiceIndex.getRateOfExchangeInvTAC());
          }
          else if (refCurrency.equals(groupCurrency) && targetCurrency.equals(currencyFromInvoice)) {
            setReferenceCurrencyValues(invoiceIndex, groupCurrency, currentInverseRateOfExchange, dateOfRateOfExchangeInvTAC, refnum);
            referenceCurrencySet.set(TRUE);
            nbOfDecimals.set(2);
            calculateTotalsTaxesBasedOnRateExchangeandNbOfDecimals(sourceInvoice, invoiceIndex, nbOfDecimals.get(),
                invoiceIndex.getRateOfExchangeInvTAC());
          }
        });
      }

    }
    //currency from invoice and group currency are not equals
    //and the pair was not found in </ListOfRateOfExchangeDetail> loop
    //so, in this case exchange_rate table will be consulted
    if (FALSE.equals(referenceCurrencySet.get()) && exchangeRateService != null) {
      //we will obtain a result if the coresponding partner for "to" code has records in exchange_rate table
      Optional<ExchangeRate> exchangeRate = exchangeRateService.findByOwnerPartnerIdCurrenciesAndDate(sourceInvoice.getOwners(),
          sourceInvoice.getTo(), currencyFromInvoice, groupCurrency, sourceInvoice.getInvoiceHeader()
              .getInvoiceIssueDate()
              .getValue());
      if (exchangeRate.isEmpty())
        //we will obtain a result if the coresponding partner for "to" code has not records in exchange_rate table
        exchangeRate = exchangeRateService.findByOwnerPartnerIdCurrenciesAndDate(sourceInvoice.getOwners(),
            null, currencyFromInvoice, groupCurrency, sourceInvoice.getInvoiceHeader()
                .getInvoiceIssueDate()
                .getValue());

      if (!exchangeRate.isEmpty()) {
        setReferenceCurrencyValues(invoiceIndex, groupCurrency, BigDecimal.valueOf(exchangeRate.get()
                .getExchangeRate()), exchangeRate.get()
                .getRateDate(),
            exchangeRate.get()
                .getReference());
        nbOfDecimals.set(2);
        calculateTotalsTaxesBasedOnRateExchangeandNbOfDecimals(sourceInvoice, invoiceIndex, nbOfDecimals.get(),
            invoiceIndex.getRateOfExchangeInvTAC());
      }
      else {
        setReferenceCurrencyValues(invoiceIndex, groupCurrency, new BigDecimal(1), new Date(), "Not found");
        setInfoEqualsCurrenciesOrRateNotFound(sourceInvoice, invoiceIndex, groupCurrency, "Not found");
      }
    }
  }

  private void setInfoEqualsCurrenciesOrRateNotFound(Invoice sourceInvoice, InvoiceIndex invoiceIndex, String groupCurrency,
      String rateOfExchangeRefNumbInvTAC) {
    InvoiceTotalsType invoiceTotalsType = getInvoiceTotalsType(sourceInvoice);
    BigDecimal invoiceTotalTac = getInvoiceTotal(invoiceTotalsType);
    BigDecimal taxableValueTAC = getTaxableValue(invoiceTotalsType);
    BigDecimal totalTaxAmountTAC = getTotalTaxAmount(invoiceTotalsType);
    BigDecimal totalAmountPayable = getTotalAmountPayable(invoiceTotalsType);
    setCommonsTaxProperties(invoiceIndex, groupCurrency, new BigDecimal(1), rateOfExchangeRefNumbInvTAC, invoiceTotalTac, taxableValueTAC,
        totalTaxAmountTAC,
        totalAmountPayable);
    invoiceIndex.getDetails()
        .forEach(d -> d.setItemTotalTAC(d.getItemTotal()));
    invoiceIndex.getSummaryBreakdownValueAddedTax()
        .forEach(sumVal -> sumVal.setTaxableAmountTAC(sumVal.getTaxableAmount()));
    invoiceIndex.getSummaryBreakdownValueAddedTax()
        .forEach(sumVal -> sumVal.setTaxAmountTAC(sumVal.getTaxAmount()));
    invoiceIndex.getInvoiceHeaderAllowOrChargeIndexList()
        .forEach(hAllowOrCharge -> hAllowOrCharge.setMonetaryAmountTAC(hAllowOrCharge.getMonetaryAmount()));
  }

  public void setReferenceCurrencyValues(InvoiceIndex invoiceIndex, String toAccountingCurrency, BigDecimal rateOfExchangeInvTAC,
      Date dateOfRateOfExchangeInvTAC,
      String rateOfExchangeRefNumInvTAC) {
    invoiceIndex.setToAccountingCurrency(toAccountingCurrency);
    invoiceIndex.setRateOfExchangeInvTAC(rateOfExchangeInvTAC);
    invoiceIndex.setDateOfRateOfExchangeInvTAC(dateOfRateOfExchangeInvTAC);
    invoiceIndex.setRateOfExchangeRefNumInvTAC(rateOfExchangeRefNumInvTAC);
  }

  private String getRecipientCurrency(Invoice sourceInvoice) {
    Group group = getGroupByOwnerAndToCodes(sourceInvoice);
    String recipientCurrency = CurrencyCodeEnum.EUR.name();
    if (group != null && ("PARTNER".equals(group.getDescription()) || "COMPANY".equals(group.getDescription())))
      recipientCurrency = ofNullable(group).map(Group::getReferenceCurrency)
          .map(CurrencyCodeEnum::name)
          .orElse(null);
    return recipientCurrency != null ? recipientCurrency : CurrencyCodeEnum.EUR.name();
  }

  private Group getGroupByOwnerAndToCodes(Invoice sourceInvoice) {
    //company code is represented by sourceInvoice.getOwners()
    //group code is represented by souceInvoice.getTo()
    String companyCode = sourceInvoice.getOwners();
    Company company = securityService.getCompanyByCode(companyCode);
    List<Group> groups=new ArrayList<>();
    if (company != null)
      groups = securityService.getGroupsByCodes(company.getId(), Arrays.asList(sourceInvoice.getTo()));
    Group group;
    if (groups != null && !groups.isEmpty())
      group = groups.get(0);
    else group = null;
    return group;
  }

  private void calculateTotalsTaxesBasedOnRateExchangeandNbOfDecimals(Invoice sourceInvoice, InvoiceIndex invoiceIndex, int nbOfDecimals,
      BigDecimal rateOfExchangeInvTAC) {
    InvoiceTotalsType invoiceTotalsType = getInvoiceTotalsType(sourceInvoice);
    BigDecimal invoiceTotalTAC = getInvoiceTotal(invoiceTotalsType);
    BigDecimal taxableValueTAC = getTaxableValue(invoiceTotalsType);
    BigDecimal totalTaxAmountTAC = getTotalTaxAmount(invoiceTotalsType);
    BigDecimal totalAmountPayableTAC = getTotalAmountPayable(invoiceTotalsType);
    if (invoiceTotalTAC != null)
      invoiceIndex.setInvoiceTotalTAC(invoiceTotalTAC.multiply(rateOfExchangeInvTAC)
          .setScale(nbOfDecimals, RoundingMode.HALF_UP));
    if (taxableValueTAC != null)
      invoiceIndex.setTaxableValueTAC(taxableValueTAC.multiply(rateOfExchangeInvTAC)
          .setScale(nbOfDecimals, RoundingMode.HALF_UP));
    if (totalTaxAmountTAC != null)
      invoiceIndex.setTotalTaxAmountTAC(totalTaxAmountTAC.multiply(rateOfExchangeInvTAC)
          .setScale(nbOfDecimals, RoundingMode.HALF_UP));
    if (totalAmountPayableTAC != null)
      invoiceIndex.setTotalAmountPayableTAC(totalAmountPayableTAC.multiply(rateOfExchangeInvTAC)
          .setScale(nbOfDecimals, RoundingMode.HALF_UP));

    invoiceIndex.getDetails()
        .forEach(d -> {
          if (d.getItemTotal() != null)
            d.setItemTotalTAC(d.getItemTotal()
                .multiply(rateOfExchangeInvTAC)
                .setScale(nbOfDecimals, RoundingMode.HALF_UP));

        });
    invoiceIndex.getSummaryBreakdownValueAddedTax()
        .forEach(sumVal ->
            {
              if (sumVal.getTaxableAmount() != null)
                sumVal.setTaxableAmountTAC(sumVal.getTaxableAmount()
                    .multiply(rateOfExchangeInvTAC)
                    .setScale(nbOfDecimals, RoundingMode.HALF_UP));
            }
        );
    invoiceIndex.getSummaryBreakdownValueAddedTax()
        .forEach(sumVal -> {
              if (sumVal.getTaxAmount() != null)
                sumVal.setTaxAmountTAC(sumVal.getTaxAmount()
                    .multiply(rateOfExchangeInvTAC)
                    .setScale(nbOfDecimals, RoundingMode.HALF_UP));
            }
        );
    invoiceIndex.getInvoiceHeaderAllowOrChargeIndexList()
        .forEach(hAllowOrCharge -> {
          if (hAllowOrCharge.getMonetaryAmount() != null)
            hAllowOrCharge.setMonetaryAmountTAC(hAllowOrCharge.getMonetaryAmount()
                .multiply(rateOfExchangeInvTAC)
                .setScale(nbOfDecimals, RoundingMode.HALF_UP));
        });
  }

  public String getInvoiceCurrency(Invoice sourceInvoice) {
    return Optional.ofNullable(sourceInvoice)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceCurrency)
        .map(CurrencyType::getCurrencyCoded)
        .map(CurrencyCodeType::value)
        .orElse(null);
  }
  private BigDecimal getPercentageMonetaryAmount(InvoiceAllowOrChargeType invoiceAllowOrChargeType) {
    return Optional.ofNullable(invoiceAllowOrChargeType).
        map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getPercentageAllowanceOrCharge)
        .map(PercentageAllowanceOrChargeType::getPercentageMonetaryValue)
        .map(MonetaryValueType::getMonetaryAmount)
        .orElse(null);
  }

  private BigDecimal getMonetaryAmount(InvoiceAllowOrChargeType invoiceAllowOrChargeType) {
    return Optional.ofNullable(invoiceAllowOrChargeType)
        .map(InvoiceAllowOrChargeType::getTypeOfAllowanceOrCharge)
        .map(InvoiceTypeOfAllowanceOrChargeType::getMonetaryValue)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue)
        .orElse(null);
  }
  private InvoiceTotalsType getInvoiceTotalsType(Invoice sourceInvoice) {
    return Optional.ofNullable(sourceInvoice)
        .map(Invoice::getInvoiceSummary)
        .map(InvoiceSummaryType::getInvoiceTotals)
        .orElse(null);
  }

  private BigDecimal getInvoiceTotal(InvoiceTotalsType invoiceTotalsType) {
    return Optional.ofNullable(invoiceTotalsType)
        .map(InvoiceTotalsType::getInvoiceTotal)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue)
        .orElse(null);
  }

  private BigDecimal getTaxableValue(InvoiceTotalsType invoiceTotalsType) {
    return Optional.ofNullable(invoiceTotalsType)
        .map(InvoiceTotalsType::getTaxableValue)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue)
        .orElse(null);
  }

  private BigDecimal getTotalTaxAmount(InvoiceTotalsType invoiceTotalsType) {
    return Optional.ofNullable(invoiceTotalsType)
        .map(InvoiceTotalsType::getTotalTaxAmount)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue)
        .orElse(null);
  }

  private BigDecimal getTotalAmountPayable(InvoiceTotalsType invoiceTotalsType) {
    return Optional.ofNullable(invoiceTotalsType)
        .map(InvoiceTotalsType::getTotalAmountPayable)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue)
        .orElse(null);
  }

  private void parseNetAndGrossPrice(List<InvoicePriceType> detail, InvoiceDetailIndex detailIndex) {
    InvoicePriceType grossUnitPrice = resolveOptNull(detail
        .stream()
        .filter(price -> CALCULATION_GROSS.equals(resolveNull(() -> price.getPricingType()
            .getPriceTypeCoded()).orElse(null)))
        .findFirst());
    InvoicePriceType netUnitPrice = resolveOptNull(detail.stream()
        .filter(price -> CALCULATION_NET.equals(resolveNull(() -> price.getPricingType()
            .getPriceTypeCoded()).orElse(null)))
        .findFirst());

    detailIndex.setItemGrossUnitPrice(resolveNull(() -> grossUnitPrice.getUnitPrice()
        .getUnitPriceValue()
        .getValue()).orElse(null));
    detailIndex.setItemNetUnitPrice(resolveNull(() -> netUnitPrice.getUnitPrice()
        .getUnitPriceValue()
        .getValue()).orElse(null));

  }

  protected void processInvoiceDetail(InvoiceItemDetailType detail, InvoiceDetailIndex detailIndex) {
    // invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCoded== Other
    Optional<InvoiceUnitOfMeasurementType> uom = ofNullable(detail).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getInvoicedQuantity)
        .map(InvoiceQuantityType::getUnitOfMeasurement);
    if (isInvoiceUomCoded(uom, CODED_OTHER)) {
      detailIndex.setItemQuantityUnitOfMeasurement(uom.map(InvoiceUnitOfMeasurementType::getUOMCodedOther)
          .map(ComplexStringType::getValue)
          .orElse(null));
    }
    else {
      detailIndex.setItemQuantityUnitOfMeasurement(uom.map(InvoiceUnitOfMeasurementType::getUOMCoded)
          .orElse(null));
    }

    detailIndex.setItemNumberOfUnitsValue(null);
    detailIndex.setItemNumberOfUnitsMeasurement(null);
    detailIndex.setItemFreeQuantityValue(null);
    detailIndex.setItemFreeQuantityUnitOfMeasurement(null);

    ofNullable(detail).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getListOfQuantityCoded)
        .map(ListOfQuantityCodedType::getQuantityCoded)
        .orElse(new ArrayList<>())
        .stream()
        .forEach(quantityCoded -> processQuantityCoded(detailIndex, of(quantityCoded)));
    // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.UnitOfMeasurement.UOMCoded = "Other"

  }

  protected void processQuantityCoded(InvoiceDetailIndex detailIndex, Optional<QuantityCodedType> quantityCoded) {
    if (isTypeCoded(quantityCoded, NUMBER_OF_UNITS_QUANTITY_CODED)) {

      // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.UnitOfMeasurement.QuantityQualifierCoded == "NumberOfUnits"
      // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.QuantityValue est mappé dans itemNumberOfUnitsValue
      // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.UnitOfMeasurement.UOMCodedOther est mappé dans
      // itemNumberOfUnitsMeasurement
      detailIndex.setItemNumberOfUnitsValue(quantityCoded.map(QuantityCodedType::getQuantityValue)
          .map(QuantityValueType::getValue)
          .orElse(null));
      if (isUomCoded(quantityCoded.map(QuantityCodedType::getUnitOfMeasurement), CODED_OTHER)) {
        detailIndex.setItemNumberOfUnitsMeasurement(quantityCoded.map(QuantityCodedType::getUnitOfMeasurement)
            .map(UnitOfMeasurementType::getUOMCodedOther)
            .orElse(null));
      }
      else {
        detailIndex.setItemNumberOfUnitsMeasurement(quantityCoded.map(QuantityCodedType::getUnitOfMeasurement)
            .map(UnitOfMeasurementType::getUOMCoded)
            .orElse(null));
      }
    }
    if (isTypeCoded(quantityCoded, FREE_GOODS_QUANTITY_QUANTITY_CODED)) {
      // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.QuantityQualifierCoded :="FreeQuantityNotIncluded"
      // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.QuantityValue est mappé dans itemFreeQuantityValue
      // detail.invoiceBaseItemDetail.ListOfQuantityCoded.QuantityCoded.UnitOfMeasurement.UOMCodedOther est mappé dans
      // itemFreeQuantityUnitOfMeasurement
      detailIndex.setItemFreeQuantityValue(quantityCoded.map(QuantityCodedType::getQuantityValue)
          .map(QuantityValueType::getValue)
          .orElse(null));
      if (isUomCoded(quantityCoded.map(QuantityCodedType::getUnitOfMeasurement), CODED_OTHER)) {
        detailIndex.setItemFreeQuantityUnitOfMeasurement(quantityCoded.map(QuantityCodedType::getUnitOfMeasurement)
            .map(UnitOfMeasurementType::getUOMCodedOther)
            .orElse(null));
      }
      else {
        detailIndex.setItemFreeQuantityUnitOfMeasurement(quantityCoded.map(QuantityCodedType::getUnitOfMeasurement)
            .map(UnitOfMeasurementType::getUOMCoded)
            .orElse(null));
      }
    }
  }

  private boolean isTypeCoded(Optional<QuantityCodedType> quantityCoded, String qualifier) {
    return quantityCoded.map(QuantityCodedType::getQuantityQualifierCoded)
        .filter(qty -> qty.equals(qualifier))
        .isPresent();
  }

  private boolean isUomCoded(Optional<UnitOfMeasurementType> uom, String uomCoded) {
    return uom.map(UnitOfMeasurementType::getUOMCoded)
        .filter(init -> init.equals(uomCoded))
        .isPresent();
  }

  private boolean isInvoiceUomCoded(Optional<InvoiceUnitOfMeasurementType> uom, String uomCoded) {
    return uom.map(InvoiceUnitOfMeasurementType::getUOMCoded)
        .filter(init -> init.equals(uomCoded))
        .isPresent();
  }

  private boolean invoiceDetailIsNotNull(Invoice invoice) {
    return invoice.getInvoiceDetail() != null && invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail() != null;
  }

  private void parseContractID(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (invoiceReferenceIsNotNull(invoice)) {
      List<InvoiceReferencesType> invoiceReferences = invoice.getInvoiceHeader()
          .getInvoiceReferences();
      Optional<InvoiceReferencesType> invRefWithContractID = invoiceReferences.stream()
          .filter(invRef -> invRef.getContractReference() != null && invRef.getContractReference()
              .getContractID() != null)
          .findFirst();
      if (invRefWithContractID.isPresent()) {
        invoiceIndex.setContractID(getIdentValueFromContractID(invRefWithContractID.get()));
      }
    }
  }

  private boolean invoiceReferenceIsNotNull(Invoice invoice) {
    return invoice.getInvoiceHeader() != null && isNotEmpty(invoice.getInvoiceHeader()
        .getInvoiceReferences());
  }

  private void parseOtherID(InvoiceIndex invoiceIndex, Invoice invoice) {
    parseBuyerPartyID(invoiceIndex, invoice);
    parseSellerPartyID(invoiceIndex, invoice);
    parseBillToPartyID(invoiceIndex, invoice);
  }

  private void parseOtherParty(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (otherIdentifierIsNotNull(invoice)) {
      List<InvoicePartyCodedType> partyCodedList = invoice.getInvoiceHeader()
          .getInvoiceParty()
          .getListOfPartyCoded()
          .getPartyCoded();
      Optional<InvoicePartyCodedType> otherPartyCoded = partyCodedList.stream()
          .filter(partyCoded -> CODED_OTHER.equals(partyCoded.getPartyRoleCoded()))
          .findFirst();

      if (otherPartyCoded.isPresent()) {
        invoiceIndex.setOtherPartyRole(otherPartyCoded.get()
            .getPartyRoleCodedOther());

        invoiceIndex.setOtherPartyID(getIdentValue(otherPartyCoded.get()
            .getPartyID()));

        invoiceIndex.setOtherPartyName(getName1Value(otherPartyCoded.get()
            .getNameAddress()));
        invoiceIndex.setOtherPartyCountryCoded(otherPartyCoded.get()
            .getNameAddress()
            .getCountry()
            .getCountryCoded());
        invoiceIndex.setOtherPartyTaxIdentifier(getIdentValue(otherPartyCoded.get()
            .getPartyTaxInformation()
            .getTaxIdentifier()));

        List<InvoiceIdentifierType> identifierList = otherPartyCoded.get()
            .getListOfIdentifier()
            .getIdentifier();
        parseSirenAndRcsRcm(invoiceIndex, identifierList, OTHER_PARTY);
      }
    }
  }

  private void parseBuyerPartyID(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (buyerIdentifierIsNotNull(invoice)) {
      List<InvoiceIdentifierType> identifierList = invoice.getInvoiceHeader()
          .getInvoiceParty()
          .getBuyerParty()
          .getListOfIdentifier()
          .getIdentifier();
      parseSirenAndRcsRcm(invoiceIndex, identifierList, BUYER_PARTY);
    }
  }

  private void parseSellerPartyID(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (sellerIdentifierIsNotNull(invoice)) {
      List<InvoiceIdentifierType> identifierList = invoice.getInvoiceHeader()
          .getInvoiceParty()
          .getSellerParty()
          .getListOfIdentifier()
          .getIdentifier();
      parseSirenAndRcsRcm(invoiceIndex, identifierList, SELLER_PARTY);
    }
  }

  private void parseBillToPartyID(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (billToIdentifierIsNotNull(invoice)) {
      List<InvoiceIdentifierType> identifierList = invoice.getInvoiceHeader()
          .getInvoiceParty()
          .getBillToParty()
          .getListOfIdentifier()
          .getIdentifier();
      parseSirenAndRcsRcm(invoiceIndex, identifierList, BILL_TO_PARTY);
    }
  }

  private void parseSirenAndRcsRcm(InvoiceIndex invoiceIndex, List<InvoiceIdentifierType> identifierList, String partyType) {
    Optional<InvoiceIdentifierType> siren = identifierList.stream()
        .filter(identifier -> FR_INSEE.equals(identifier.getAgency()
            .getAgencyCoded()))
        .findFirst();
    Optional<InvoiceIdentifierType> rcsRcm = identifierList.stream()
        .filter(identifier -> RCS_RCM.equals(identifier.getAgency()
            .getAgencyCodedOther()))
        .findFirst();

    if (siren.isPresent()) {
      switch (partyType) {
      case BUYER_PARTY:
        invoiceIndex.setBuyerPartyOtherID(getIdentValue(siren.get()));
        break;
      case SELLER_PARTY:
        invoiceIndex.setSellerPartyOtherID(getIdentValue(siren.get()));
        break;
      case BILL_TO_PARTY:
        invoiceIndex.setBillToPartyOtherID(getIdentValue(siren.get()));
        break;
      case OTHER_PARTY:
        invoiceIndex.setOtherPartyOtherID(getIdentValue(siren.get()));
      }
    }
    else if (rcsRcm.isPresent()) {
      switch (partyType) {
      case BUYER_PARTY:
        invoiceIndex.setBuyerPartyOtherID(getIdentValue(rcsRcm.get()));
        break;
      case SELLER_PARTY:
        invoiceIndex.setSellerPartyOtherID(getIdentValue(rcsRcm.get()));
        break;
      case BILL_TO_PARTY:
        invoiceIndex.setBillToPartyOtherID(getIdentValue(rcsRcm.get()));
        break;
      case OTHER_PARTY:
        invoiceIndex.setOtherPartyOtherID(getIdentValue(rcsRcm.get()));
      }
    }
  }

  private boolean buyerIdentifierIsNotNull(Invoice invoice) {
    return invoicePartyIsNotNull(invoice) && invoice.getInvoiceHeader()
        .getInvoiceParty()
        .getBuyerParty() != null && invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getBuyerParty()
            .getListOfIdentifier() != null &&
        isNotEmpty(invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getBuyerParty()
            .getListOfIdentifier()
            .getIdentifier());
  }

  private boolean sellerIdentifierIsNotNull(Invoice invoice) {
    return invoicePartyIsNotNull(invoice) && invoice.getInvoiceHeader()
        .getInvoiceParty()
        .getSellerParty() != null && invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getSellerParty()
            .getListOfIdentifier() != null &&
        isNotEmpty(invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getSellerParty()
            .getListOfIdentifier()
            .getIdentifier());
  }

  private boolean billToIdentifierIsNotNull(Invoice invoice) {
    return invoicePartyIsNotNull(invoice) && invoice.getInvoiceHeader()
        .getInvoiceParty()
        .getBillToParty() != null && invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getBillToParty()
            .getListOfIdentifier() != null &&
        isNotEmpty(invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getBillToParty()
            .getListOfIdentifier()
            .getIdentifier());
  }

  private boolean otherIdentifierIsNotNull(Invoice invoice) {
    return invoicePartyIsNotNull(invoice) && invoice.getInvoiceHeader()
        .getInvoiceParty()
        .getListOfPartyCoded() != null && isNotEmpty(invoice.getInvoiceHeader()
            .getInvoiceParty()
            .getListOfPartyCoded()
            .getPartyCoded());
  }

  private boolean invoicePartyIsNotNull(Invoice invoice) {
    return invoice.getInvoiceHeader() != null && invoice.getInvoiceHeader()
        .getInvoiceParty() != null;
  }

  private void parseProcessDateTime(InvoiceIndex invoiceIndex, Invoice invoice) {
    parseDateCoded(invoiceIndex, invoice, PREPARATION_DATETIME_DOCUMENT);
  }

  private void parsePreparationDateTime(InvoiceIndex invoiceIndex, Invoice invoice) {
    parseDateCoded(invoiceIndex, invoice, RECEIVED_DATE);
  }

  private void parseInvoiceDateOfFinalPayment(InvoiceIndex invoiceIndex, Invoice invoice) {
    parseDateCoded(invoiceIndex, invoice, PAYMENT_EFFECTIVE);
  }

  private void parseDateCoded(InvoiceIndex invoiceIndex, Invoice invoice, String dateQualifierCodedSearched) {
    if (dateCodedListIsNotNull(invoice)) {
      List<InvoiceDateCodedType> dateCodedList = invoice.getInvoiceHeader()
          .getInvoiceDates()
          .getListOfOtherInvoiceDates()
          .getDateCoded();
      Optional<InvoiceDateCodedType> dateQualifierCoded = dateCodedList.stream()
          .filter(dateCoded -> dateCoded.getDateQualifier() != null)
          .filter(dateCoded -> dateQualifierCodedSearched.equals(dateCoded.getDateQualifier()
              .getDateQualifierCoded()))
          .findFirst();
      if (dateQualifierCoded.isPresent()) {
        if (PREPARATION_DATETIME_DOCUMENT.equals(dateQualifierCodedSearched)) {
          invoiceIndex.setInvoicePreparationDateTime(getDateFromInvoiceDateCoded(dateQualifierCoded.get()));
        }
        else if (RECEIVED_DATE.equals(dateQualifierCodedSearched)) {
          invoiceIndex.setInvoiceProcessDateTime(getDateFromInvoiceDateCoded(dateQualifierCoded.get()));
        }
        else if (PAYMENT_EFFECTIVE.equals(dateQualifierCodedSearched)) {
          invoiceIndex.setInvoiceDateOfFinalPayment(getDateFromInvoiceDateCoded(dateQualifierCoded.get()));
        }
      }
    }
  }

  private boolean isACreditInvoice(Invoice invoice) {

    if (invoice.getInvoiceHeader()
        .getInvoiceType() != null &&
        invoice.getInvoiceHeader()
            .getInvoiceType()
            .getInvoiceTypeCoded() != null) {
      return CREDIT_INVOICE.equals(invoice.getInvoiceHeader()
          .getInvoiceType()
          .getInvoiceTypeCoded()
          .value());
    }

    return false;

  }

  private boolean dateCodedListIsNotNull(Invoice invoice) {
    return invoice.getInvoiceHeader() != null && invoice.getInvoiceHeader()
        .getInvoiceDates() != null && invoice.getInvoiceHeader()
            .getInvoiceDates()
            .getListOfOtherInvoiceDates() != null &&
        isNotEmpty(invoice.getInvoiceHeader()
            .getInvoiceDates()
            .getListOfOtherInvoiceDates()
            .getDateCoded());
  }

  private void parseCustomerReference(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (invoiceReferenceIsNotNull(invoice)) {
      List<InvoiceReferencesType> invoiceReferences = invoice.getInvoiceHeader()
          .getInvoiceReferences();
      List<InvoiceReferenceCodedType> references = invoiceReferences.stream()
          .filter(invRef -> invRef.getOtherInvoiceReferences() != null && isNotEmpty(invRef.getOtherInvoiceReferences()
              .getReferenceCoded()))
          .map(invRef -> invRef.getOtherInvoiceReferences())
          .map(ListOfInvoiceReferenceCodedType::getReferenceCoded)
          .findFirst()
          .orElse(new ArrayList<>());

      Optional<InvoiceReferenceCodedType> referenceTypeCoded = references.stream()
          .filter(refCoded -> CUSTOMER_REFERENCE_NUMBER.equals(refCoded.getReferenceTypeCoded()))
          .findFirst();

      if (referenceTypeCoded.isPresent()) {
        invoiceIndex.setCustomerReferenceNumber(getPrimaryReferenceRefNum(referenceTypeCoded.get()));
      }
    }
  }

  private void parseReceptionNumber(InvoiceIndex invoiceIndex, Invoice invoice) {
    if (invoiceReferenceIsNotNull(invoice)) {
      List<InvoiceReferencesType> invoiceReferences = invoice.getInvoiceHeader()
          .getInvoiceReferences();
      List<InvoiceReferenceCodedType> references = invoiceReferences.stream()
          .filter(invRef -> invRef.getOtherInvoiceReferences() != null && isNotEmpty(invRef.getOtherInvoiceReferences()
              .getReferenceCoded()))
          .map(invRef -> invRef.getOtherInvoiceReferences())
          .map(ListOfInvoiceReferenceCodedType::getReferenceCoded)
          .findFirst()
          .orElse(new ArrayList<>());

      Optional<InvoiceReferenceCodedType> referenceTypeCoded = references.stream()
          .filter(refCoded -> RECEIVING_NUMBER.equals(refCoded.getReferenceTypeCoded()))
          .findFirst();

      if (referenceTypeCoded.isPresent()) {
        invoiceIndex.setReceptionNumber(getPrimaryReferenceRefNum(referenceTypeCoded.get()));
      }
    }
  }

  protected void parseEarlyPayment(InvoiceIndex invoiceIndex, Invoice invoice) {
    ListOfNameValuePairType nameValuePairs = getEarlyPaymentNameValuePair(invoice);
    if (nameValuePairs != null)
      for (NameValuePairType pair : nameValuePairs.getNameValuePair()) {
        if (StringUtils.isNoneEmpty(pair.getValue()))
          switch (pair.getName()) {
        case PAYMENT_STATUS:
          invoiceIndex.setPaymentStatus(PaymentStatus.valueOf(pair.getValue()));
          break;
        case DAYS_PAYED_EARLY:
          invoiceIndex.setDaysPayedEarly(Integer.valueOf(pair.getValue()));
          break;
        case EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE:
          invoiceIndex.setEarlyPaymentAnnualDiscountRate(new BigDecimal(pair.getValue()));
          break;
        case EARLY_PAYMENT_DISCOUNT_RATE:
          invoiceIndex.setEarlyPaymentDiscountRate(new BigDecimal(pair.getValue()));
          break;
        case EARLY_PAYMENT_AMOUNT_DISCOUNT:
          invoiceIndex.setEarlyPaymentAmountDiscount(new BigDecimal(pair.getValue()));
          break;
        case EARLY_PAYMENT_CANNOT_PAY_BEFORE:
          invoiceIndex.setEarlyPaymentCannotPayBefore(Integer.valueOf(pair.getValue()));
          break;
        default:
          break;
        }
      }
  }

  public static ListOfNameValuePairType getEarlyPaymentNameValuePair(Invoice invoice) {
    List<NameValueSetType> nameValueSets = Optional.ofNullable(invoice)
        .map(InvoiceType::getInvoiceHeader)
        .map(InvoiceHeaderType::getListOfNameValueSet)
        .map(ListOfNameValueSetType::getNameValueSet)
        .orElse(null);
    if (nameValueSets == null)
      return null;
    return nameValueSets.stream()
        .filter(nameValueSet -> EARLY_PAYMENT.equals(nameValueSet.getSetName()))
        .findFirst()
        .map(nameValueSet -> nameValueSet.getListOfNameValuePair())
        .orElse(null);
  }

  public void parsePartyAddressEmail(InvoiceIndex invoiceIndex, Invoice invoice, String partyType) {
    if (invoicePartyIsNotNull(invoice)) {
      InvoicePartyType invoiceParty = invoice.getInvoiceHeader()
          .getInvoiceParty();
      switch (partyType) {
      case BUYER_PARTY:
        invoiceIndex.setBuyerAddressEmail(getContactNumberType(invoiceParty.getBuyerParty()));
        break;
      case SELLER_PARTY:
        invoiceIndex.setSellerAddressEmail(getContactNumberType(invoiceParty.getSellerParty()));
        break;
      case BILL_TO_PARTY:
        invoiceIndex.setBillToAddressEmail(getContactNumberType(invoiceParty.getBillToParty()));
        break;
      case SHIP_TO_PARTY:
        invoiceIndex.setShipToAddressEmail(getContactNumberType(invoiceParty.getShipToParty()));
        break;
      }
    }
  }

  private String getContactNumberType(InvoiceOCRPartyType invoicePartyType) {
    String cpontactNbValue = ofNullable(invoicePartyType)
        .map(InvoiceOCRPartyType::getPrimaryContact)
        .map(InvoiceContactType::getListOfContactNumber)
        .map(ListOfInvoiceContactNumberType::getContactNumber)
        .orElse(new ArrayList<>())
        .stream()
        .filter(cNb -> ContactNumberTypeCodeType.EMAIL_ADDRESS
            .equals(cNb.getContactNumberTypeCoded()))
        .map(InvoiceContactNumberType::getContactNumberValue)
        .filter(Objects::nonNull)
        .map(ComplexStringType::getValue)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse("");
    return cpontactNbValue.isEmpty() ? null : cpontactNbValue;
  }

  /**
   * @since AIO-7413
   * @see com.byzaneo.xtrade.xcbl.bean.XcblIndexer#migrates(com.byzaneo.xtrade.bean.Document, com.byzaneo.xtrade.xcbl.api.XcblDocument)
   */
  @Override
  public void migrates(final Document document, final Invoice xcbl) {
    if (document.isIndexed()) {
      if (document.getIndexValue() instanceof InvoiceIndex) {
        log.warn("Invoice was already migrated {}", document);
      }
      else {
        document.getIndex()
            .setValue(createIndexable(xcbl));
      }
      document.getIndex()
          .getValue()
          .setId(xcbl.getId());
      document.getIndex()
          .getValue()
          .setEntityRef(document.getIndexReference());
      document.getIndex()
          .setType(getIndexType());
      if (StringUtils.isEmpty(document.getIndex()
          .getId())) {
        document.getIndex()
            .setId(xcbl.getId());
      }
    }
  }

  @Override
  public boolean manages(final Class<? extends Indexable> indexableType) {
    return indexableType != null && (getIndexType().isAssignableFrom(indexableType) || getXcblType().isAssignableFrom(indexableType));
  }

  @Override
  protected Predicate<DocumentFile> hasXcblDocumentFile() {
    return dof -> dof != null && XCBL.equals(dof.getType()) && OFFICIAL_INDEX.equals(dof.getDescription());
  }

  private <T> T resolveOptNull(Optional<T> optional) {
    try {
      return optional.get();
    }
    catch (NullPointerException | NoSuchElementException e) {
      return null;
    }
  }

  private <T> Optional<T> resolveNull(Supplier<T> resolver) {
    try {
      T result = resolver.get();
      return Optional.ofNullable(result);
    }
    catch (NullPointerException e) {
      return Optional.empty();
    }
  }
}
