//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.applicationintegration.v1_0.applicationintegration;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.util.Date;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfAttachmentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfNameValueSetType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PurposeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.SpecialHandlingType;

/**
 * contains a request for new purchase orders.
 * <p>
 * Java class for GetOrderType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="GetOrderType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="GetOrderID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="GetOrderIssueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="SenderParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType"/>
 *         &lt;element name="ReceiverParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType"/>
 *         &lt;element name="Purpose" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PurposeType" minOccurs="0"/>
 *         &lt;element name="ListOfPOReferences" type="{rrn:org.xcbl:schemas/xcbl/v4_0/applicationintegration/v1_0/applicationintegration.xsd}ListOfPOReferencesType"/>
 *         &lt;element name="GetOrderLanguage" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}LanguageType"/>
 *         &lt;element name="SpecialHandling" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}SpecialHandlingType" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *         &lt;element name="ListOfNameValueSet" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfNameValueSetType" minOccurs="0"/>
 *         &lt;element name="GetOrderAttachments" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfAttachmentType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetOrderType", propOrder = {
    "getOrderID",
    "getOrderIssueDate",
    "senderParty",
    "receiverParty",
    "purpose",
    "listOfPOReferences",
    "getOrderLanguage",
    "specialHandling",
    "listOfStructuredNote",
    "listOfNameValueSet",
    "getOrderAttachments"
})
public class GetOrderType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "GetOrderID", required = true)
  protected String getOrderID;
  @XmlElement(name = "GetOrderIssueDate", required = true)
  @XmlSchemaType(name = "dateTime")
  protected Date getOrderIssueDate;
  @XmlElement(name = "SenderParty", required = true)
  protected PartyType senderParty;
  @XmlElement(name = "ReceiverParty", required = true)
  protected PartyType receiverParty;
  @XmlElement(name = "Purpose")
  protected PurposeType purpose;
  @XmlElement(name = "ListOfPOReferences", required = true)
  protected ListOfPOReferencesType listOfPOReferences;
  @XmlElement(name = "GetOrderLanguage", required = true)
  protected LanguageType getOrderLanguage;
  @XmlElement(name = "SpecialHandling")
  protected SpecialHandlingType specialHandling;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;
  @XmlElement(name = "ListOfNameValueSet")
  protected ListOfNameValueSetType listOfNameValueSet;
  @XmlElement(name = "GetOrderAttachments")
  protected ListOfAttachmentType getOrderAttachments;

  /**
   * Gets the value of the getOrderID property.
   * 
   * @return possible object is {@link String }
   */
  public String getGetOrderID() {
    return getOrderID;
  }

  /**
   * Sets the value of the getOrderID property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setGetOrderID(String value) {
    this.getOrderID = value;
  }

  /**
   * Gets the value of the getOrderIssueDate property.
   * 
   * @return possible object is {@link Date }
   */
  public Date getGetOrderIssueDate() {
    return getOrderIssueDate;
  }

  /**
   * Sets the value of the getOrderIssueDate property.
   * 
   * @param value allowed object is {@link Date }
   */
  public void setGetOrderIssueDate(Date value) {
    this.getOrderIssueDate = value;
  }

  /**
   * Gets the value of the senderParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getSenderParty() {
    return senderParty;
  }

  /**
   * Sets the value of the senderParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setSenderParty(PartyType value) {
    this.senderParty = value;
  }

  /**
   * Gets the value of the receiverParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getReceiverParty() {
    return receiverParty;
  }

  /**
   * Sets the value of the receiverParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setReceiverParty(PartyType value) {
    this.receiverParty = value;
  }

  /**
   * Gets the value of the purpose property.
   * 
   * @return possible object is {@link PurposeType }
   */
  public PurposeType getPurpose() {
    return purpose;
  }

  /**
   * Sets the value of the purpose property.
   * 
   * @param value allowed object is {@link PurposeType }
   */
  public void setPurpose(PurposeType value) {
    this.purpose = value;
  }

  /**
   * Gets the value of the listOfPOReferences property.
   * 
   * @return possible object is {@link ListOfPOReferencesType }
   */
  public ListOfPOReferencesType getListOfPOReferences() {
    return listOfPOReferences;
  }

  /**
   * Sets the value of the listOfPOReferences property.
   * 
   * @param value allowed object is {@link ListOfPOReferencesType }
   */
  public void setListOfPOReferences(ListOfPOReferencesType value) {
    this.listOfPOReferences = value;
  }

  /**
   * Gets the value of the getOrderLanguage property.
   * 
   * @return possible object is {@link LanguageType }
   */
  public LanguageType getGetOrderLanguage() {
    return getOrderLanguage;
  }

  /**
   * Sets the value of the getOrderLanguage property.
   * 
   * @param value allowed object is {@link LanguageType }
   */
  public void setGetOrderLanguage(LanguageType value) {
    this.getOrderLanguage = value;
  }

  /**
   * Gets the value of the specialHandling property.
   * 
   * @return possible object is {@link SpecialHandlingType }
   */
  public SpecialHandlingType getSpecialHandling() {
    return specialHandling;
  }

  /**
   * Sets the value of the specialHandling property.
   * 
   * @param value allowed object is {@link SpecialHandlingType }
   */
  public void setSpecialHandling(SpecialHandlingType value) {
    this.specialHandling = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

  /**
   * Gets the value of the listOfNameValueSet property.
   * 
   * @return possible object is {@link ListOfNameValueSetType }
   */
  public ListOfNameValueSetType getListOfNameValueSet() {
    return listOfNameValueSet;
  }

  /**
   * Sets the value of the listOfNameValueSet property.
   * 
   * @param value allowed object is {@link ListOfNameValueSetType }
   */
  public void setListOfNameValueSet(ListOfNameValueSetType value) {
    this.listOfNameValueSet = value;
  }

  /**
   * Gets the value of the getOrderAttachments property.
   * 
   * @return possible object is {@link ListOfAttachmentType }
   */
  public ListOfAttachmentType getGetOrderAttachments() {
    return getOrderAttachments;
  }

  /**
   * Sets the value of the getOrderAttachments property.
   * 
   * @param value allowed object is {@link ListOfAttachmentType }
   */
  public void setGetOrderAttachments(ListOfAttachmentType value) {
    this.getOrderAttachments = value;
  }

}
