//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.PurposeCodeType;

/**
 * <p>
 * Java class for ASNPurposeCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="ASNPurposeCodeType">
 *   &lt;restriction base="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PurposeCodeType">
 *     &lt;enumeration value="Other"/>
 *     &lt;enumeration value="Original"/>
 *     &lt;enumeration value="Confirmation"/>
 *     &lt;enumeration value="Duplicate"/>
 *     &lt;enumeration value="Replace"/>
 *     &lt;enumeration value="Cancellation"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "ASNPurposeCodeType")
@XmlEnum(PurposeCodeType.class)
public enum ASNPurposeCodeType {

  /**
   * Custom Code
   */
  @XmlEnumValue("Other")
  OTHER(PurposeCodeType.OTHER),
  @XmlEnumValue("Original")
  ORIGINAL(PurposeCodeType.ORIGINAL),
  @XmlEnumValue("Confirmation")
  CONFIRMATION(PurposeCodeType.CONFIRMATION),
  @XmlEnumValue("Duplicate")
  DUPLICATE(PurposeCodeType.DUPLICATE),
  @XmlEnumValue("Replace")
  REPLACE(PurposeCodeType.REPLACE),
  @XmlEnumValue("Cancellation")
  CANCELLATION(PurposeCodeType.CANCELLATION);
  private final PurposeCodeType value;

  ASNPurposeCodeType(PurposeCodeType v) {
    value = v;
  }

  public PurposeCodeType value() {
    return value;
  }

  public static ASNPurposeCodeType fromValue(PurposeCodeType v) {
    for (ASNPurposeCodeType c : ASNPurposeCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v.toString());
  }

}
