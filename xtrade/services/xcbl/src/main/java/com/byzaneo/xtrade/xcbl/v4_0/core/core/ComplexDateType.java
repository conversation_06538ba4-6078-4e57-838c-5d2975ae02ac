//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2020.12.17 at 10:43:17 AM EET
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.*;

/**
 * *an element which has a date value and an optional confidence rate.*
 * <p>
 * Java class for ComplexDateType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ComplexDateType">
 *   &lt;simpleContent>
 *     &lt;extension base="&lt;http://www.w3.org/2001/XMLSchema>dateTime">
 *       &lt;attribute name="ocrRate" type="{http://www.w3.org/2001/XMLSchema}decimal" />
 *     &lt;/extension>
 *   &lt;/simpleContent>
 * &lt;/complexType>
 * </pre>
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ComplexdDateType", propOrder = {
    "value"
})
public class ComplexDateType
    implements Serializable {

  private final static long serialVersionUID = 1L;
  @XmlValue
  @XmlSchemaType(name = "dateTime")
  protected Date value;
  @XmlAttribute(name = "ocrRate")
  protected BigDecimal ocrRate;

  /**
   * Gets the value of the value property.
   *
   * @return possible object is {@link String }
   */
  public Date getValue() {
    return value;
  }

  /**
   * Sets the value of the value property.
   *
   * @param value allowed object is {@link String }
   */
  public void setValue(Date value) {
    this.value = value;
  }

  /**
   * Gets the value of the ocrRate property.
   *
   * @return possible object is {@link BigDecimal }
   */
  public BigDecimal getOcrRate() {
    return ocrRate;
  }

  /**
   * Sets the value of the ocrRate property.
   *
   * @param value allowed object is {@link BigDecimal }
   */
  public void setOcrRate(BigDecimal value) {
    this.ocrRate = value;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((value == null) ? 0 : value.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    ComplexDateType other = (ComplexDateType) obj;
    if (value == null) {
      if (other.value != null) return false;
    }
    else if (!value.equals(other.value)) return false;
    return true;
  }

}
