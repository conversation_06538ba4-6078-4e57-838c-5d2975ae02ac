//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.CollapsedStringAdapter;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;

import org.springframework.data.mongodb.core.mapping.Field;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfAttachmentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfTransportRoutingType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PurposeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.RequestedResponseType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TermsOfDeliveryType;

/**
 * contains the header information of the ShippingSchedule.
 * <p>
 * Java class for ShippingScheduleHeaderType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ShippingScheduleHeaderType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ScheduleID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ScheduleIssuedDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="ScheduleReferences" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}ScheduleReferencesType" minOccurs="0"/>
 *         &lt;element name="ReleaseNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Purpose" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PurposeType" minOccurs="0"/>
 *         &lt;element name="RequestedResponse" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}RequestedResponseType" minOccurs="0"/>
 *         &lt;element name="ScheduleTypeCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}ScheduleTypeCodeType"/>
 *         &lt;element name="ScheduleTypeCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="QuantityQualifierCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}QuantityQualifierCodeType" minOccurs="0"/>
 *         &lt;element name="QuantityQualifierCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShippingDates" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}ShippingDatesType" minOccurs="0"/>
 *         &lt;element name="ScheduleParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}SchedulePartyType"/>
 *         &lt;element name="ListOfTransportRouting" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfTransportRoutingType" minOccurs="0"/>
 *         &lt;element name="TermsOfDelivery" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}TermsOfDeliveryType" minOccurs="0"/>
 *         &lt;element name="Language" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}LanguageType"/>
 *         &lt;element name="ShippingScheduleHeaderNote" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *         &lt;element name="ListOfAttachment" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfAttachmentType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ShippingScheduleHeaderType", propOrder = {
    "scheduleID",
    "scheduleIssuedDate",
    "scheduleReferences",
    "releaseNumber",
    "purpose",
    "requestedResponse",
    "scheduleTypeCoded",
    "scheduleTypeCodedOther",
    "quantityQualifierCoded",
    "quantityQualifierCodedOther",
    "shippingDates",
    "scheduleParty",
    "listOfTransportRouting",
    "termsOfDelivery",
    "language",
    "shippingScheduleHeaderNote",
    "listOfStructuredNote",
    "listOfAttachment"
})
public class ShippingScheduleHeaderType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "ScheduleID", required = true)
  protected String scheduleID;
  @XmlElement(name = "ScheduleIssuedDate", required = true)
  @XmlSchemaType(name = "dateTime")
  protected Date scheduleIssuedDate;
  @XmlElement(name = "ScheduleReferences")
  protected ScheduleReferencesType scheduleReferences;
  @XmlElement(name = "ReleaseNumber")
  protected String releaseNumber;
  @XmlElement(name = "Purpose")
  protected PurposeType purpose;
  @XmlElement(name = "RequestedResponse")
  protected RequestedResponseType requestedResponse;
  @XmlElement(name = "ScheduleTypeCoded", required = true)
  protected ScheduleTypeCodeType scheduleTypeCoded;
  @XmlElement(name = "ScheduleTypeCodedOther")
  protected String scheduleTypeCodedOther;
  @XmlElement(name = "QuantityQualifierCoded")
  @XmlJavaTypeAdapter(CollapsedStringAdapter.class)
  protected String quantityQualifierCoded;
  @XmlElement(name = "QuantityQualifierCodedOther")
  protected String quantityQualifierCodedOther;
  @XmlElement(name = "ShippingDates")
  protected ShippingDatesType shippingDates;
  @XmlElement(name = "ScheduleParty", required = true)
  protected SchedulePartyType scheduleParty;
  @XmlElement(name = "ListOfTransportRouting")
  protected ListOfTransportRoutingType listOfTransportRouting;
  @XmlElement(name = "TermsOfDelivery")
  protected TermsOfDeliveryType termsOfDelivery;
  @XmlElement(name = "Language", required = true)
  @Field("lang") // XXX language is a reserved index key
  protected LanguageType language;
  @XmlElement(name = "ShippingScheduleHeaderNote")
  protected String shippingScheduleHeaderNote;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;
  @XmlElement(name = "ListOfAttachment")
  protected ListOfAttachmentType listOfAttachment;

  /**
   * Gets the value of the scheduleID property.
   * 
   * @return possible object is {@link String }
   */
  public String getScheduleID() {
    return scheduleID;
  }

  /**
   * Sets the value of the scheduleID property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setScheduleID(String value) {
    this.scheduleID = value;
  }

  /**
   * Gets the value of the scheduleIssuedDate property.
   * 
   * @return possible object is {@link Date }
   */
  public Date getScheduleIssuedDate() {
    return scheduleIssuedDate;
  }

  /**
   * Sets the value of the scheduleIssuedDate property.
   * 
   * @param value allowed object is {@link Date }
   */
  public void setScheduleIssuedDate(Date value) {
    this.scheduleIssuedDate = value;
  }

  /**
   * Gets the value of the scheduleReferences property.
   * 
   * @return possible object is {@link ScheduleReferencesType }
   */
  public ScheduleReferencesType getScheduleReferences() {
    return scheduleReferences;
  }

  /**
   * Sets the value of the scheduleReferences property.
   * 
   * @param value allowed object is {@link ScheduleReferencesType }
   */
  public void setScheduleReferences(ScheduleReferencesType value) {
    this.scheduleReferences = value;
  }

  /**
   * Gets the value of the releaseNumber property.
   * 
   * @return possible object is {@link String }
   */
  public String getReleaseNumber() {
    return releaseNumber;
  }

  /**
   * Sets the value of the releaseNumber property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setReleaseNumber(String value) {
    this.releaseNumber = value;
  }

  /**
   * Gets the value of the purpose property.
   * 
   * @return possible object is {@link PurposeType }
   */
  public PurposeType getPurpose() {
    return purpose;
  }

  /**
   * Sets the value of the purpose property.
   * 
   * @param value allowed object is {@link PurposeType }
   */
  public void setPurpose(PurposeType value) {
    this.purpose = value;
  }

  /**
   * Gets the value of the requestedResponse property.
   * 
   * @return possible object is {@link RequestedResponseType }
   */
  public RequestedResponseType getRequestedResponse() {
    return requestedResponse;
  }

  /**
   * Sets the value of the requestedResponse property.
   * 
   * @param value allowed object is {@link RequestedResponseType }
   */
  public void setRequestedResponse(RequestedResponseType value) {
    this.requestedResponse = value;
  }

  /**
   * Gets the value of the scheduleTypeCoded property.
   * 
   * @return possible object is {@link ScheduleTypeCodeType }
   */
  public ScheduleTypeCodeType getScheduleTypeCoded() {
    return scheduleTypeCoded;
  }

  /**
   * Sets the value of the scheduleTypeCoded property.
   * 
   * @param value allowed object is {@link ScheduleTypeCodeType }
   */
  public void setScheduleTypeCoded(ScheduleTypeCodeType value) {
    this.scheduleTypeCoded = value;
  }

  /**
   * Gets the value of the scheduleTypeCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getScheduleTypeCodedOther() {
    return scheduleTypeCodedOther;
  }

  /**
   * Sets the value of the scheduleTypeCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setScheduleTypeCodedOther(String value) {
    this.scheduleTypeCodedOther = value;
  }

  /**
   * Gets the value of the quantityQualifierCoded property.
   * 
   * @return possible object is {@link String }
   */
  public String getQuantityQualifierCoded() {
    return quantityQualifierCoded;
  }

  /**
   * Sets the value of the quantityQualifierCoded property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setQuantityQualifierCoded(String value) {
    this.quantityQualifierCoded = value;
  }

  /**
   * Gets the value of the quantityQualifierCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getQuantityQualifierCodedOther() {
    return quantityQualifierCodedOther;
  }

  /**
   * Sets the value of the quantityQualifierCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setQuantityQualifierCodedOther(String value) {
    this.quantityQualifierCodedOther = value;
  }

  /**
   * Gets the value of the shippingDates property.
   * 
   * @return possible object is {@link ShippingDatesType }
   */
  public ShippingDatesType getShippingDates() {
    return shippingDates;
  }

  /**
   * Sets the value of the shippingDates property.
   * 
   * @param value allowed object is {@link ShippingDatesType }
   */
  public void setShippingDates(ShippingDatesType value) {
    this.shippingDates = value;
  }

  /**
   * Gets the value of the scheduleParty property.
   * 
   * @return possible object is {@link SchedulePartyType }
   */
  public SchedulePartyType getScheduleParty() {
    return scheduleParty;
  }

  /**
   * Sets the value of the scheduleParty property.
   * 
   * @param value allowed object is {@link SchedulePartyType }
   */
  public void setScheduleParty(SchedulePartyType value) {
    this.scheduleParty = value;
  }

  /**
   * Gets the value of the listOfTransportRouting property.
   * 
   * @return possible object is {@link ListOfTransportRoutingType }
   */
  public ListOfTransportRoutingType getListOfTransportRouting() {
    return listOfTransportRouting;
  }

  /**
   * Sets the value of the listOfTransportRouting property.
   * 
   * @param value allowed object is {@link ListOfTransportRoutingType }
   */
  public void setListOfTransportRouting(ListOfTransportRoutingType value) {
    this.listOfTransportRouting = value;
  }

  /**
   * Gets the value of the termsOfDelivery property.
   * 
   * @return possible object is {@link TermsOfDeliveryType }
   */
  public TermsOfDeliveryType getTermsOfDelivery() {
    return termsOfDelivery;
  }

  /**
   * Sets the value of the termsOfDelivery property.
   * 
   * @param value allowed object is {@link TermsOfDeliveryType }
   */
  public void setTermsOfDelivery(TermsOfDeliveryType value) {
    this.termsOfDelivery = value;
  }

  /**
   * Gets the value of the language property.
   * 
   * @return possible object is {@link LanguageType }
   */
  public LanguageType getLanguage() {
    return language;
  }

  /**
   * Sets the value of the language property.
   * 
   * @param value allowed object is {@link LanguageType }
   */
  public void setLanguage(LanguageType value) {
    this.language = value;
  }

  /**
   * Gets the value of the shippingScheduleHeaderNote property.
   * 
   * @return possible object is {@link String }
   */
  public String getShippingScheduleHeaderNote() {
    return shippingScheduleHeaderNote;
  }

  /**
   * Sets the value of the shippingScheduleHeaderNote property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setShippingScheduleHeaderNote(String value) {
    this.shippingScheduleHeaderNote = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

  /**
   * Gets the value of the listOfAttachment property.
   * 
   * @return possible object is {@link ListOfAttachmentType }
   */
  public ListOfAttachmentType getListOfAttachment() {
    return listOfAttachment;
  }

  /**
   * Sets the value of the listOfAttachment property.
   * 
   * @param value allowed object is {@link ListOfAttachmentType }
   */
  public void setListOfAttachment(ListOfAttachmentType value) {
    this.listOfAttachment = value;
  }

}
