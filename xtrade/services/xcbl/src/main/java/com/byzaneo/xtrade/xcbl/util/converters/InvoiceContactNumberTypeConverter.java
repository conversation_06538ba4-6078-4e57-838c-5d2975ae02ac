package com.byzaneo.xtrade.xcbl.util.converters;

import org.apache.commons.beanutils.converters.AbstractConverter;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceContactNumberType;

public class InvoiceContactNumberTypeConverter extends AbstractConverter {

  @Override
  protected <T> T convertToType(Class<T> type, Object value) throws Throwable {
    return OCRTypeConverter.convertToInvoiceContactNumberType(type, value);
  }

  @Override
  protected Class<?> getDefaultType() {
    return InvoiceContactNumberType.class;
  }

}
