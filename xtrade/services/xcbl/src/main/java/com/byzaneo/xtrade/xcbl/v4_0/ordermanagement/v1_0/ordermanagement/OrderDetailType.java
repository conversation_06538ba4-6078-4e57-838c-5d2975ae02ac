//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfPackageDetailType;

/**
 * contains the line item and package details of the Order.
 * <p>
 * Java class for OrderDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OrderDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ListOfItemDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd}ListOfItemDetailType"/>
 *         &lt;element name="ListOfPackageDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfPackageDetailType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderDetailType", propOrder = {
    "listOfItemDetail",
    "listOfPackageDetail"
})
public class OrderDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "ListOfItemDetail", required = true)
  protected ListOfItemDetailType listOfItemDetail;
  @XmlElement(name = "ListOfPackageDetail")
  protected ListOfPackageDetailType listOfPackageDetail;

  /**
   * Gets the value of the listOfItemDetail property.
   * 
   * @return possible object is {@link ListOfItemDetailType }
   */
  public ListOfItemDetailType getListOfItemDetail() {
    return listOfItemDetail;
  }

  /**
   * Sets the value of the listOfItemDetail property.
   * 
   * @param value allowed object is {@link ListOfItemDetailType }
   */
  public void setListOfItemDetail(ListOfItemDetailType value) {
    this.listOfItemDetail = value;
  }

  /**
   * Gets the value of the listOfPackageDetail property.
   * 
   * @return possible object is {@link ListOfPackageDetailType }
   */
  public ListOfPackageDetailType getListOfPackageDetail() {
    return listOfPackageDetail;
  }

  /**
   * Sets the value of the listOfPackageDetail property.
   * 
   * @param value allowed object is {@link ListOfPackageDetailType }
   */
  public void setListOfPackageDetail(ListOfPackageDetailType value) {
    this.listOfPackageDetail = value;
  }

}
