//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.preordermanagement.v1_0.preordermanagement;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.PurposeCodeType;

/**
 * <p>
 * Java class for RequestQuotePurposeCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="RequestQuotePurposeCodeType">
 *   &lt;restriction base="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PurposeCodeType">
 *     &lt;enumeration value="Original"/>
 *     &lt;enumeration value="Cancellation"/>
 *     &lt;enumeration value="Replace"/>
 *     &lt;enumeration value="Confirmation"/>
 *     &lt;enumeration value="Duplicate"/>
 *     &lt;enumeration value="Other"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "RequestQuotePurposeCodeType")
@XmlEnum(PurposeCodeType.class)
public enum RequestQuotePurposeCodeType {

  /**
   * Refers to the initial transmission of the message.
   */
  @XmlEnumValue("Original")
  ORIGINAL(PurposeCodeType.ORIGINAL),

  /**
   * Indicates that a previous transmission of the message is to be cancelled.
   */
  @XmlEnumValue("Cancellation")
  CANCELLATION(PurposeCodeType.CANCELLATION),

  /**
   * Indicates that this transmission is to replace a previous message.
   */
  @XmlEnumValue("Replace")
  REPLACE(PurposeCodeType.REPLACE),

  /**
   * Indicates that this transmission is to confirm details of a previous message.
   */
  @XmlEnumValue("Confirmation")
  CONFIRMATION(PurposeCodeType.CONFIRMATION),

  /**
   * Indicates that this transmission is a duplicate of a previously generated message.
   */
  @XmlEnumValue("Duplicate")
  DUPLICATE(PurposeCodeType.DUPLICATE),

  /**
   * Custom code
   */
  @XmlEnumValue("Other")
  OTHER(PurposeCodeType.OTHER);
  private final PurposeCodeType value;

  RequestQuotePurposeCodeType(PurposeCodeType v) {
    value = v;
  }

  public PurposeCodeType value() {
    return value;
  }

  public static RequestQuotePurposeCodeType fromValue(PurposeCodeType v) {
    for (RequestQuotePurposeCodeType c : RequestQuotePurposeCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v.toString());
  }

}
