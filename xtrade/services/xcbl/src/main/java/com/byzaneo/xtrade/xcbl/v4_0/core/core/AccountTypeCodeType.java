//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for AccountTypeCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="AccountTypeCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *     &lt;enumeration value="Other"/>
 *     &lt;enumeration value="NewBusinessAccount"/>
 *     &lt;enumeration value="GroupNumber"/>
 *     &lt;enumeration value="PolicyNumber"/>
 *     &lt;enumeration value="MoneyMarketFund"/>
 *     &lt;enumeration value="RetirementAccountVestedInterestatMarketValue"/>
 *     &lt;enumeration value="RetirementAccountVestedInterestNetCashValue"/>
 *     &lt;enumeration value="IndividualRetirementAccount-IRA-VestedInterestatMarketValue"/>
 *     &lt;enumeration value="IndividualRetirementAccount-IRA-VestedInterestNetCashValue"/>
 *     &lt;enumeration value="CorporateControlledRetirementAccountVestedInterest"/>
 *     &lt;enumeration value="TimeDeposit"/>
 *     &lt;enumeration value="CheckingAccount"/>
 *     &lt;enumeration value="Stock"/>
 *     &lt;enumeration value="Bond"/>
 *     &lt;enumeration value="LifeInsuranceFaceValue"/>
 *     &lt;enumeration value="RetirementAccount-VestedInterest"/>
 *     &lt;enumeration value="BusinessAccount"/>
 *     &lt;enumeration value="TrustFundAccount"/>
 *     &lt;enumeration value="StocksandBondsAccount"/>
 *     &lt;enumeration value="LifeInsuranceAccount-NetCashValue"/>
 *     &lt;enumeration value="EmployeeRetirementAccount-401K"/>
 *     &lt;enumeration value="Agency"/>
 *     &lt;enumeration value="Application"/>
 *     &lt;enumeration value="BrokerageAccount"/>
 *     &lt;enumeration value="ChargeBackAccountforReturns"/>
 *     &lt;enumeration value="CreditCard"/>
 *     &lt;enumeration value="CashManagement"/>
 *     &lt;enumeration value="DemandDeposit"/>
 *     &lt;enumeration value="DebitCard"/>
 *     &lt;enumeration value="ExistingCarrier"/>
 *     &lt;enumeration value="ReturnItemsonDemandDepositAccount"/>
 *     &lt;enumeration value="ReturnItemsonSavingsAccount"/>
 *     &lt;enumeration value="Savings"/>
 *     &lt;enumeration value="AgencyLocationCode"/>
 *     &lt;enumeration value="Annuity"/>
 *     &lt;enumeration value="ControlledDisbursementMasterAccount"/>
 *     &lt;enumeration value="ControlledDisbursementSubAccount"/>
 *     &lt;enumeration value="CreditUnion"/>
 *     &lt;enumeration value="EnrichedPlanContractNumber"/>
 *     &lt;enumeration value="Long-termDisabilityPolicyNumber"/>
 *     &lt;enumeration value="MutualFund"/>
 *     &lt;enumeration value="PrimaryBank"/>
 *     &lt;enumeration value="RelationshipSummaryAccount"/>
 *     &lt;enumeration value="Safekeeping-CustodyAccount"/>
 *     &lt;enumeration value="ZeroBalanceMasterAccount"/>
 *     &lt;enumeration value="ZeroBalanceAffiliateAccount"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "AccountTypeCodeType")
@XmlEnum
public enum AccountTypeCodeType {

  @XmlEnumValue("Other")
  OTHER("Other"),
  @XmlEnumValue("NewBusinessAccount")
  NEW_BUSINESS_ACCOUNT("NewBusinessAccount"),
  @XmlEnumValue("GroupNumber")
  GROUP_NUMBER("GroupNumber"),
  @XmlEnumValue("PolicyNumber")
  POLICY_NUMBER("PolicyNumber"),
  @XmlEnumValue("MoneyMarketFund")
  MONEY_MARKET_FUND("MoneyMarketFund"),
  @XmlEnumValue("RetirementAccountVestedInterestatMarketValue")
  RETIREMENT_ACCOUNT_VESTED_INTERESTAT_MARKET_VALUE("RetirementAccountVestedInterestatMarketValue"),
  @XmlEnumValue("RetirementAccountVestedInterestNetCashValue")
  RETIREMENT_ACCOUNT_VESTED_INTEREST_NET_CASH_VALUE("RetirementAccountVestedInterestNetCashValue"),
  @XmlEnumValue("IndividualRetirementAccount-IRA-VestedInterestatMarketValue")
  INDIVIDUAL_RETIREMENT_ACCOUNT_IRA_VESTED_INTERESTAT_MARKET_VALUE("IndividualRetirementAccount-IRA-VestedInterestatMarketValue"),
  @XmlEnumValue("IndividualRetirementAccount-IRA-VestedInterestNetCashValue")
  INDIVIDUAL_RETIREMENT_ACCOUNT_IRA_VESTED_INTEREST_NET_CASH_VALUE("IndividualRetirementAccount-IRA-VestedInterestNetCashValue"),
  @XmlEnumValue("CorporateControlledRetirementAccountVestedInterest")
  CORPORATE_CONTROLLED_RETIREMENT_ACCOUNT_VESTED_INTEREST("CorporateControlledRetirementAccountVestedInterest"),
  @XmlEnumValue("TimeDeposit")
  TIME_DEPOSIT("TimeDeposit"),
  @XmlEnumValue("CheckingAccount")
  CHECKING_ACCOUNT("CheckingAccount"),
  @XmlEnumValue("Stock")
  STOCK("Stock"),
  @XmlEnumValue("Bond")
  BOND("Bond"),
  @XmlEnumValue("LifeInsuranceFaceValue")
  LIFE_INSURANCE_FACE_VALUE("LifeInsuranceFaceValue"),
  @XmlEnumValue("RetirementAccount-VestedInterest")
  RETIREMENT_ACCOUNT_VESTED_INTEREST("RetirementAccount-VestedInterest"),
  @XmlEnumValue("BusinessAccount")
  BUSINESS_ACCOUNT("BusinessAccount"),
  @XmlEnumValue("TrustFundAccount")
  TRUST_FUND_ACCOUNT("TrustFundAccount"),
  @XmlEnumValue("StocksandBondsAccount")
  STOCKSAND_BONDS_ACCOUNT("StocksandBondsAccount"),
  @XmlEnumValue("LifeInsuranceAccount-NetCashValue")
  LIFE_INSURANCE_ACCOUNT_NET_CASH_VALUE("LifeInsuranceAccount-NetCashValue"),
  @XmlEnumValue("EmployeeRetirementAccount-401K")
  EMPLOYEE_RETIREMENT_ACCOUNT_401_K("EmployeeRetirementAccount-401K"),
  @XmlEnumValue("Agency")
  AGENCY("Agency"),
  @XmlEnumValue("Application")
  APPLICATION("Application"),
  @XmlEnumValue("BrokerageAccount")
  BROKERAGE_ACCOUNT("BrokerageAccount"),
  @XmlEnumValue("ChargeBackAccountforReturns")
  CHARGE_BACK_ACCOUNTFOR_RETURNS("ChargeBackAccountforReturns"),
  @XmlEnumValue("CreditCard")
  CREDIT_CARD("CreditCard"),
  @XmlEnumValue("CashManagement")
  CASH_MANAGEMENT("CashManagement"),
  @XmlEnumValue("DemandDeposit")
  DEMAND_DEPOSIT("DemandDeposit"),
  @XmlEnumValue("DebitCard")
  DEBIT_CARD("DebitCard"),
  @XmlEnumValue("ExistingCarrier")
  EXISTING_CARRIER("ExistingCarrier"),
  @XmlEnumValue("ReturnItemsonDemandDepositAccount")
  RETURN_ITEMSON_DEMAND_DEPOSIT_ACCOUNT("ReturnItemsonDemandDepositAccount"),
  @XmlEnumValue("ReturnItemsonSavingsAccount")
  RETURN_ITEMSON_SAVINGS_ACCOUNT("ReturnItemsonSavingsAccount"),
  @XmlEnumValue("Savings")
  SAVINGS("Savings"),
  @XmlEnumValue("AgencyLocationCode")
  AGENCY_LOCATION_CODE("AgencyLocationCode"),
  @XmlEnumValue("Annuity")
  ANNUITY("Annuity"),
  @XmlEnumValue("ControlledDisbursementMasterAccount")
  CONTROLLED_DISBURSEMENT_MASTER_ACCOUNT("ControlledDisbursementMasterAccount"),
  @XmlEnumValue("ControlledDisbursementSubAccount")
  CONTROLLED_DISBURSEMENT_SUB_ACCOUNT("ControlledDisbursementSubAccount"),
  @XmlEnumValue("CreditUnion")
  CREDIT_UNION("CreditUnion"),
  @XmlEnumValue("EnrichedPlanContractNumber")
  ENRICHED_PLAN_CONTRACT_NUMBER("EnrichedPlanContractNumber"),
  @XmlEnumValue("Long-termDisabilityPolicyNumber")
  LONG_TERM_DISABILITY_POLICY_NUMBER("Long-termDisabilityPolicyNumber"),
  @XmlEnumValue("MutualFund")
  MUTUAL_FUND("MutualFund"),
  @XmlEnumValue("PrimaryBank")
  PRIMARY_BANK("PrimaryBank"),
  @XmlEnumValue("RelationshipSummaryAccount")
  RELATIONSHIP_SUMMARY_ACCOUNT("RelationshipSummaryAccount"),
  @XmlEnumValue("Safekeeping-CustodyAccount")
  SAFEKEEPING_CUSTODY_ACCOUNT("Safekeeping-CustodyAccount"),
  @XmlEnumValue("ZeroBalanceMasterAccount")
  ZERO_BALANCE_MASTER_ACCOUNT("ZeroBalanceMasterAccount"),
  @XmlEnumValue("ZeroBalanceAffiliateAccount")
  ZERO_BALANCE_AFFILIATE_ACCOUNT("ZeroBalanceAffiliateAccount");
  private final String value;

  AccountTypeCodeType(String v) {
    value = v;
  }

  public String value() {
    return value;
  }

  public static AccountTypeCodeType fromValue(String v) {
    for (AccountTypeCodeType c : AccountTypeCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v);
  }

}
