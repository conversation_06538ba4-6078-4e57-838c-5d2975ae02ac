//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for DetailResponseCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="DetailResponseCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *     &lt;enumeration value="Other"/>
 *     &lt;enumeration value="AcceptedWithAmendment"/>
 *     &lt;enumeration value="AcceptedWithAmendmentNoConfirmationRequired"/>
 *     &lt;enumeration value="AcknowledgeWithDetailAndChange"/>
 *     &lt;enumeration value="Agreed"/>
 *     &lt;enumeration value="AlreadyDelivered"/>
 *     &lt;enumeration value="ApprovedAsAmended"/>
 *     &lt;enumeration value="ApprovedAsSubmitted"/>
 *     &lt;enumeration value="Cancelled"/>
 *     &lt;enumeration value="ChangeOfDates"/>
 *     &lt;enumeration value="ChangeOfDateTerms"/>
 *     &lt;enumeration value="ChangesToItemLevelAllowanceAndCharges"/>
 *     &lt;enumeration value="ChangesToLineItems"/>
 *     &lt;enumeration value="ChangeToConfirmationOfRevisedAnnouncement"/>
 *     &lt;enumeration value="ChangeToOriginalConfirmationOfOriginaAnnouncement"/>
 *     &lt;enumeration value="ItemAccepted"/>
 *     &lt;enumeration value="ItemAcceptedAndReleasedForShipment"/>
 *     &lt;enumeration value="ItemAcceptedAndShipped"/>
 *     &lt;enumeration value="ItemAcceptedChangesMade"/>
 *     &lt;enumeration value="ItemAcceptedDateRescheduled"/>
 *     &lt;enumeration value="ItemAccepted-OrderForwardedToAlternateSupplierLocation"/>
 *     &lt;enumeration value="ItemAcceptedPartialShipmentBalanceBackordered"/>
 *     &lt;enumeration value="ItemAcceptedPriceChanged"/>
 *     &lt;enumeration value="ItemAcceptedPricePending"/>
 *     &lt;enumeration value="ItemAcceptedQuantityChanged"/>
 *     &lt;enumeration value="ItemAcceptedScheduleDatePending"/>
 *     &lt;enumeration value="ItemAcceptedSubstitutionMade"/>
 *     &lt;enumeration value="ItemBackordered"/>
 *     &lt;enumeration value="ItemChangeRejected"/>
 *     &lt;enumeration value="ItemDeleted"/>
 *     &lt;enumeration value="ItemNotAcceptedByTheSeller"/>
 *     &lt;enumeration value="ItemOnHold"/>
 *     &lt;enumeration value="ItemOnHoldIncompleteDescription"/>
 *     &lt;enumeration value="ItemOnHoldWaiverRequired"/>
 *     &lt;enumeration value="ItemRejected"/>
 *     &lt;enumeration value="ItemRejectedContractItemNotAvailable"/>
 *     &lt;enumeration value="ItemRejectedInvalidItemProductNumber"/>
 *     &lt;enumeration value="ItemRejectedInvalidUnitOfIssue"/>
 *     &lt;enumeration value="ItemRejectedNotAContractItem"/>
 *     &lt;enumeration value="ItemRejectedReorderItemAsADropShipment"/>
 *     &lt;enumeration value="ItemRejectedReorderItemAsAJustInTimeOrder"/>
 *     &lt;enumeration value="ItemRejectedReorderItemAsASurgeOrder"/>
 *     &lt;enumeration value="ItemRjectedReorderItemAsAnExtendedDeliveryOrder"/>
 *     &lt;enumeration value="LineItemNumberChanged"/>
 *     &lt;enumeration value="New"/>
 *     &lt;enumeration value="NotChecked"/>
 *     &lt;enumeration value="NotProcessed"/>
 *     &lt;enumeration value="Pending"/>
 *     &lt;enumeration value="PendingIncomplete"/>
 *     &lt;enumeration value="PendingWaitingAdditionalMaterial"/>
 *     &lt;enumeration value="PendingWaitingReview"/>
 *     &lt;enumeration value="PriceChange"/>
 *     &lt;enumeration value="QuantityDecrease"/>
 *     &lt;enumeration value="QuantityIncrease"/>
 *     &lt;enumeration value="ReferredItemAccepted"/>
 *     &lt;enumeration value="ReferredItemRejected"/>
 *     &lt;enumeration value="RejectedDuplicate"/>
 *     &lt;enumeration value="RejectedNoDetail"/>
 *     &lt;enumeration value="RejectedNotAsAgreed"/>
 *     &lt;enumeration value="RejectedSubmittedWithCorrections"/>
 *     &lt;enumeration value="RejectedViolatesIndustryPractices"/>
 *     &lt;enumeration value="RejectedWithCounterOffer"/>
 *     &lt;enumeration value="RejectWithDetail"/>
 *     &lt;enumeration value="RejectWithExceptionDetailOnly"/>
 *     &lt;enumeration value="ReplaceAllDates"/>
 *     &lt;enumeration value="ReplaceAllValues"/>
 *     &lt;enumeration value="Replaced"/>
 *     &lt;enumeration value="ReplacementItem"/>
 *     &lt;enumeration value="ReplacementItemWithModifications"/>
 *     &lt;enumeration value="ReplaceModeOfShipment"/>
 *     &lt;enumeration value="Repriced"/>
 *     &lt;enumeration value="Reschedule"/>
 *     &lt;enumeration value="Reschedule-QuantityChange"/>
 *     &lt;enumeration value="SellerRejectsDispute"/>
 *     &lt;enumeration value="TermsChangedForNewTerms"/>
 *     &lt;enumeration value="UnitPrice-QuantityChange"/>
 *     &lt;enumeration value="UnitPrice-RescheduleChange"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "DetailResponseCodeType")
@XmlEnum
public enum DetailResponseCodeType {

  /**
   * Custom Code
   */
  @XmlEnumValue("Other")
  OTHER("Other"),
  @XmlEnumValue("AcceptedWithAmendment")
  ACCEPTED_WITH_AMENDMENT("AcceptedWithAmendment"),
  @XmlEnumValue("AcceptedWithAmendmentNoConfirmationRequired")
  ACCEPTED_WITH_AMENDMENT_NO_CONFIRMATION_REQUIRED("AcceptedWithAmendmentNoConfirmationRequired"),
  @XmlEnumValue("AcknowledgeWithDetailAndChange")
  ACKNOWLEDGE_WITH_DETAIL_AND_CHANGE("AcknowledgeWithDetailAndChange"),
  @XmlEnumValue("Agreed")
  AGREED("Agreed"),
  @XmlEnumValue("AlreadyDelivered")
  ALREADY_DELIVERED("AlreadyDelivered"),
  @XmlEnumValue("ApprovedAsAmended")
  APPROVED_AS_AMENDED("ApprovedAsAmended"),
  @XmlEnumValue("ApprovedAsSubmitted")
  APPROVED_AS_SUBMITTED("ApprovedAsSubmitted"),
  @XmlEnumValue("Cancelled")
  CANCELLED("Cancelled"),
  @XmlEnumValue("ChangeOfDates")
  CHANGE_OF_DATES("ChangeOfDates"),
  @XmlEnumValue("ChangeOfDateTerms")
  CHANGE_OF_DATE_TERMS("ChangeOfDateTerms"),
  @XmlEnumValue("ChangesToItemLevelAllowanceAndCharges")
  CHANGES_TO_ITEM_LEVEL_ALLOWANCE_AND_CHARGES("ChangesToItemLevelAllowanceAndCharges"),
  @XmlEnumValue("ChangesToLineItems")
  CHANGES_TO_LINE_ITEMS("ChangesToLineItems"),
  @XmlEnumValue("ChangeToConfirmationOfRevisedAnnouncement")
  CHANGE_TO_CONFIRMATION_OF_REVISED_ANNOUNCEMENT("ChangeToConfirmationOfRevisedAnnouncement"),
  @XmlEnumValue("ChangeToOriginalConfirmationOfOriginaAnnouncement")
  CHANGE_TO_ORIGINAL_CONFIRMATION_OF_ORIGINA_ANNOUNCEMENT("ChangeToOriginalConfirmationOfOriginaAnnouncement"),
  @XmlEnumValue("ItemAccepted")
  ITEM_ACCEPTED("ItemAccepted"),
  @XmlEnumValue("ItemAcceptedAndReleasedForShipment")
  ITEM_ACCEPTED_AND_RELEASED_FOR_SHIPMENT("ItemAcceptedAndReleasedForShipment"),
  @XmlEnumValue("ItemAcceptedAndShipped")
  ITEM_ACCEPTED_AND_SHIPPED("ItemAcceptedAndShipped"),
  @XmlEnumValue("ItemAcceptedChangesMade")
  ITEM_ACCEPTED_CHANGES_MADE("ItemAcceptedChangesMade"),
  @XmlEnumValue("ItemAcceptedDateRescheduled")
  ITEM_ACCEPTED_DATE_RESCHEDULED("ItemAcceptedDateRescheduled"),
  @XmlEnumValue("ItemAccepted-OrderForwardedToAlternateSupplierLocation")
  ITEM_ACCEPTED_ORDER_FORWARDED_TO_ALTERNATE_SUPPLIER_LOCATION("ItemAccepted-OrderForwardedToAlternateSupplierLocation"),
  @XmlEnumValue("ItemAcceptedPartialShipmentBalanceBackordered")
  ITEM_ACCEPTED_PARTIAL_SHIPMENT_BALANCE_BACKORDERED("ItemAcceptedPartialShipmentBalanceBackordered"),
  @XmlEnumValue("ItemAcceptedPriceChanged")
  ITEM_ACCEPTED_PRICE_CHANGED("ItemAcceptedPriceChanged"),
  @XmlEnumValue("ItemAcceptedPricePending")
  ITEM_ACCEPTED_PRICE_PENDING("ItemAcceptedPricePending"),
  @XmlEnumValue("ItemAcceptedQuantityChanged")
  ITEM_ACCEPTED_QUANTITY_CHANGED("ItemAcceptedQuantityChanged"),
  @XmlEnumValue("ItemAcceptedScheduleDatePending")
  ITEM_ACCEPTED_SCHEDULE_DATE_PENDING("ItemAcceptedScheduleDatePending"),
  @XmlEnumValue("ItemAcceptedSubstitutionMade")
  ITEM_ACCEPTED_SUBSTITUTION_MADE("ItemAcceptedSubstitutionMade"),
  @XmlEnumValue("ItemBackordered")
  ITEM_BACKORDERED("ItemBackordered"),
  @XmlEnumValue("ItemChangeRejected")
  ITEM_CHANGE_REJECTED("ItemChangeRejected"),
  @XmlEnumValue("ItemDeleted")
  ITEM_DELETED("ItemDeleted"),
  @XmlEnumValue("ItemNotAcceptedByTheSeller")
  ITEM_NOT_ACCEPTED_BY_THE_SELLER("ItemNotAcceptedByTheSeller"),
  @XmlEnumValue("ItemOnHold")
  ITEM_ON_HOLD("ItemOnHold"),
  @XmlEnumValue("ItemOnHoldIncompleteDescription")
  ITEM_ON_HOLD_INCOMPLETE_DESCRIPTION("ItemOnHoldIncompleteDescription"),
  @XmlEnumValue("ItemOnHoldWaiverRequired")
  ITEM_ON_HOLD_WAIVER_REQUIRED("ItemOnHoldWaiverRequired"),
  @XmlEnumValue("ItemRejected")
  ITEM_REJECTED("ItemRejected"),
  @XmlEnumValue("ItemRejectedContractItemNotAvailable")
  ITEM_REJECTED_CONTRACT_ITEM_NOT_AVAILABLE("ItemRejectedContractItemNotAvailable"),
  @XmlEnumValue("ItemRejectedInvalidItemProductNumber")
  ITEM_REJECTED_INVALID_ITEM_PRODUCT_NUMBER("ItemRejectedInvalidItemProductNumber"),
  @XmlEnumValue("ItemRejectedInvalidUnitOfIssue")
  ITEM_REJECTED_INVALID_UNIT_OF_ISSUE("ItemRejectedInvalidUnitOfIssue"),
  @XmlEnumValue("ItemRejectedNotAContractItem")
  ITEM_REJECTED_NOT_A_CONTRACT_ITEM("ItemRejectedNotAContractItem"),
  @XmlEnumValue("ItemRejectedReorderItemAsADropShipment")
  ITEM_REJECTED_REORDER_ITEM_AS_A_DROP_SHIPMENT("ItemRejectedReorderItemAsADropShipment"),
  @XmlEnumValue("ItemRejectedReorderItemAsAJustInTimeOrder")
  ITEM_REJECTED_REORDER_ITEM_AS_A_JUST_IN_TIME_ORDER("ItemRejectedReorderItemAsAJustInTimeOrder"),
  @XmlEnumValue("ItemRejectedReorderItemAsASurgeOrder")
  ITEM_REJECTED_REORDER_ITEM_AS_A_SURGE_ORDER("ItemRejectedReorderItemAsASurgeOrder"),
  @XmlEnumValue("ItemRjectedReorderItemAsAnExtendedDeliveryOrder")
  ITEM_RJECTED_REORDER_ITEM_AS_AN_EXTENDED_DELIVERY_ORDER("ItemRjectedReorderItemAsAnExtendedDeliveryOrder"),
  @XmlEnumValue("LineItemNumberChanged")
  LINE_ITEM_NUMBER_CHANGED("LineItemNumberChanged"),
  @XmlEnumValue("New")
  NEW("New"),
  @XmlEnumValue("NotChecked")
  NOT_CHECKED("NotChecked"),
  @XmlEnumValue("NotProcessed")
  NOT_PROCESSED("NotProcessed"),
  @XmlEnumValue("Pending")
  PENDING("Pending"),
  @XmlEnumValue("PendingIncomplete")
  PENDING_INCOMPLETE("PendingIncomplete"),
  @XmlEnumValue("PendingWaitingAdditionalMaterial")
  PENDING_WAITING_ADDITIONAL_MATERIAL("PendingWaitingAdditionalMaterial"),
  @XmlEnumValue("PendingWaitingReview")
  PENDING_WAITING_REVIEW("PendingWaitingReview"),
  @XmlEnumValue("PriceChange")
  PRICE_CHANGE("PriceChange"),
  @XmlEnumValue("QuantityDecrease")
  QUANTITY_DECREASE("QuantityDecrease"),
  @XmlEnumValue("QuantityIncrease")
  QUANTITY_INCREASE("QuantityIncrease"),
  @XmlEnumValue("ReferredItemAccepted")
  REFERRED_ITEM_ACCEPTED("ReferredItemAccepted"),
  @XmlEnumValue("ReferredItemRejected")
  REFERRED_ITEM_REJECTED("ReferredItemRejected"),
  @XmlEnumValue("RejectedDuplicate")
  REJECTED_DUPLICATE("RejectedDuplicate"),
  @XmlEnumValue("RejectedNoDetail")
  REJECTED_NO_DETAIL("RejectedNoDetail"),
  @XmlEnumValue("RejectedNotAsAgreed")
  REJECTED_NOT_AS_AGREED("RejectedNotAsAgreed"),
  @XmlEnumValue("RejectedSubmittedWithCorrections")
  REJECTED_SUBMITTED_WITH_CORRECTIONS("RejectedSubmittedWithCorrections"),
  @XmlEnumValue("RejectedViolatesIndustryPractices")
  REJECTED_VIOLATES_INDUSTRY_PRACTICES("RejectedViolatesIndustryPractices"),
  @XmlEnumValue("RejectedWithCounterOffer")
  REJECTED_WITH_COUNTER_OFFER("RejectedWithCounterOffer"),
  @XmlEnumValue("RejectWithDetail")
  REJECT_WITH_DETAIL("RejectWithDetail"),
  @XmlEnumValue("RejectWithExceptionDetailOnly")
  REJECT_WITH_EXCEPTION_DETAIL_ONLY("RejectWithExceptionDetailOnly"),
  @XmlEnumValue("ReplaceAllDates")
  REPLACE_ALL_DATES("ReplaceAllDates"),
  @XmlEnumValue("ReplaceAllValues")
  REPLACE_ALL_VALUES("ReplaceAllValues"),
  @XmlEnumValue("Replaced")
  REPLACED("Replaced"),
  @XmlEnumValue("ReplacementItem")
  REPLACEMENT_ITEM("ReplacementItem"),
  @XmlEnumValue("ReplacementItemWithModifications")
  REPLACEMENT_ITEM_WITH_MODIFICATIONS("ReplacementItemWithModifications"),
  @XmlEnumValue("ReplaceModeOfShipment")
  REPLACE_MODE_OF_SHIPMENT("ReplaceModeOfShipment"),
  @XmlEnumValue("Repriced")
  REPRICED("Repriced"),
  @XmlEnumValue("Reschedule")
  RESCHEDULE("Reschedule"),
  @XmlEnumValue("Reschedule-QuantityChange")
  RESCHEDULE_QUANTITY_CHANGE("Reschedule-QuantityChange"),
  @XmlEnumValue("SellerRejectsDispute")
  SELLER_REJECTS_DISPUTE("SellerRejectsDispute"),
  @XmlEnumValue("TermsChangedForNewTerms")
  TERMS_CHANGED_FOR_NEW_TERMS("TermsChangedForNewTerms"),
  @XmlEnumValue("UnitPrice-QuantityChange")
  UNIT_PRICE_QUANTITY_CHANGE("UnitPrice-QuantityChange"),
  @XmlEnumValue("UnitPrice-RescheduleChange")
  UNIT_PRICE_RESCHEDULE_CHANGE("UnitPrice-RescheduleChange");
  private final String value;

  DetailResponseCodeType(String v) {
    value = v;
  }

  public String value() {
    return value;
  }

  public static DetailResponseCodeType fromValue(String v) {
    for (DetailResponseCodeType c : DetailResponseCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v);
  }

}
