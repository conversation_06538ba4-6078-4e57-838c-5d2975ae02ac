//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.DetailResponseCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ErrorInfoType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfErrorInfoType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfReferenceCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ShipmentStatusEventType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.StatusType;

/**
 * holds response information for a line item, any references for that line item, and the relevant response to that line item. Applications
 * will be expected to return any line items with any changes and generally all of the accepted line items as well. Any line item not
 * present in this section that was found in the original order will be assumed to be accepted as is.
 * <p>
 * Java class for OrderResponseItemDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OrderResponseItemDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ItemDetailResponseCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}DetailResponseCodeType"/>
 *         &lt;element name="ItemDetailResponseCodedOther" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShipmentStatusEvent" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ShipmentStatusEventType" minOccurs="0"/>
 *         &lt;element name="PaymentStatusEvent" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}StatusType" minOccurs="0"/>
 *         &lt;element name="PriceErrorInfo" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ErrorInfoType" minOccurs="0"/>
 *         &lt;element name="AvailabilityErrorInfo" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ErrorInfoType" minOccurs="0"/>
 *         &lt;element name="ListOfErrorInfo" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfErrorInfoType" minOccurs="0"/>
 *         &lt;element name="TrackingURL" type="{http://www.w3.org/2001/XMLSchema}anyURI" minOccurs="0"/>
 *         &lt;element name="ListOfReferenceCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfReferenceCodedType" minOccurs="0"/>
 *         &lt;element name="OriginalItemDetailWithChanges" type="{rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd}OriginalItemDetailWithChangesType"/>
 *         &lt;element name="LineItemNote" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderResponseItemDetailType", propOrder = {
    "itemDetailResponseCoded",
    "itemDetailResponseCodedOther",
    "shipmentStatusEvent",
    "paymentStatusEvent",
    "priceErrorInfo",
    "availabilityErrorInfo",
    "listOfErrorInfo",
    "trackingURL",
    "listOfReferenceCoded",
    "originalItemDetailWithChanges",
    "lineItemNote",
    "listOfStructuredNote"
})
public class OrderResponseItemDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "ItemDetailResponseCoded", required = true)
  protected DetailResponseCodeType itemDetailResponseCoded;
  @XmlElement(name = "ItemDetailResponseCodedOther")
  protected String itemDetailResponseCodedOther;
  @XmlElement(name = "ShipmentStatusEvent")
  protected ShipmentStatusEventType shipmentStatusEvent;
  @XmlElement(name = "PaymentStatusEvent")
  protected StatusType paymentStatusEvent;
  @XmlElement(name = "PriceErrorInfo")
  protected ErrorInfoType priceErrorInfo;
  @XmlElement(name = "AvailabilityErrorInfo")
  protected ErrorInfoType availabilityErrorInfo;
  @XmlElement(name = "ListOfErrorInfo")
  protected ListOfErrorInfoType listOfErrorInfo;
  @XmlElement(name = "TrackingURL")
  @XmlSchemaType(name = "anyURI")
  protected String trackingURL;
  @XmlElement(name = "ListOfReferenceCoded")
  protected ListOfReferenceCodedType listOfReferenceCoded;
  @XmlElement(name = "OriginalItemDetailWithChanges", required = true)
  protected OriginalItemDetailWithChangesType originalItemDetailWithChanges;
  @XmlElement(name = "LineItemNote")
  protected String lineItemNote;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;

  /**
   * Gets the value of the itemDetailResponseCoded property.
   * 
   * @return possible object is {@link DetailResponseCodeType }
   */
  public DetailResponseCodeType getItemDetailResponseCoded() {
    return itemDetailResponseCoded;
  }

  /**
   * Sets the value of the itemDetailResponseCoded property.
   * 
   * @param value allowed object is {@link DetailResponseCodeType }
   */
  public void setItemDetailResponseCoded(DetailResponseCodeType value) {
    this.itemDetailResponseCoded = value;
  }

  /**
   * Gets the value of the itemDetailResponseCodedOther property.
   * 
   * @return possible object is {@link String }
   */
  public String getItemDetailResponseCodedOther() {
    return itemDetailResponseCodedOther;
  }

  /**
   * Sets the value of the itemDetailResponseCodedOther property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setItemDetailResponseCodedOther(String value) {
    this.itemDetailResponseCodedOther = value;
  }

  /**
   * Gets the value of the shipmentStatusEvent property.
   * 
   * @return possible object is {@link ShipmentStatusEventType }
   */
  public ShipmentStatusEventType getShipmentStatusEvent() {
    return shipmentStatusEvent;
  }

  /**
   * Sets the value of the shipmentStatusEvent property.
   * 
   * @param value allowed object is {@link ShipmentStatusEventType }
   */
  public void setShipmentStatusEvent(ShipmentStatusEventType value) {
    this.shipmentStatusEvent = value;
  }

  /**
   * Gets the value of the paymentStatusEvent property.
   * 
   * @return possible object is {@link StatusType }
   */
  public StatusType getPaymentStatusEvent() {
    return paymentStatusEvent;
  }

  /**
   * Sets the value of the paymentStatusEvent property.
   * 
   * @param value allowed object is {@link StatusType }
   */
  public void setPaymentStatusEvent(StatusType value) {
    this.paymentStatusEvent = value;
  }

  /**
   * Gets the value of the priceErrorInfo property.
   * 
   * @return possible object is {@link ErrorInfoType }
   */
  public ErrorInfoType getPriceErrorInfo() {
    return priceErrorInfo;
  }

  /**
   * Sets the value of the priceErrorInfo property.
   * 
   * @param value allowed object is {@link ErrorInfoType }
   */
  public void setPriceErrorInfo(ErrorInfoType value) {
    this.priceErrorInfo = value;
  }

  /**
   * Gets the value of the availabilityErrorInfo property.
   * 
   * @return possible object is {@link ErrorInfoType }
   */
  public ErrorInfoType getAvailabilityErrorInfo() {
    return availabilityErrorInfo;
  }

  /**
   * Sets the value of the availabilityErrorInfo property.
   * 
   * @param value allowed object is {@link ErrorInfoType }
   */
  public void setAvailabilityErrorInfo(ErrorInfoType value) {
    this.availabilityErrorInfo = value;
  }

  /**
   * Gets the value of the listOfErrorInfo property.
   * 
   * @return possible object is {@link ListOfErrorInfoType }
   */
  public ListOfErrorInfoType getListOfErrorInfo() {
    return listOfErrorInfo;
  }

  /**
   * Sets the value of the listOfErrorInfo property.
   * 
   * @param value allowed object is {@link ListOfErrorInfoType }
   */
  public void setListOfErrorInfo(ListOfErrorInfoType value) {
    this.listOfErrorInfo = value;
  }

  /**
   * Gets the value of the trackingURL property.
   * 
   * @return possible object is {@link String }
   */
  public String getTrackingURL() {
    return trackingURL;
  }

  /**
   * Sets the value of the trackingURL property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setTrackingURL(String value) {
    this.trackingURL = value;
  }

  /**
   * Gets the value of the listOfReferenceCoded property.
   * 
   * @return possible object is {@link ListOfReferenceCodedType }
   */
  public ListOfReferenceCodedType getListOfReferenceCoded() {
    return listOfReferenceCoded;
  }

  /**
   * Sets the value of the listOfReferenceCoded property.
   * 
   * @param value allowed object is {@link ListOfReferenceCodedType }
   */
  public void setListOfReferenceCoded(ListOfReferenceCodedType value) {
    this.listOfReferenceCoded = value;
  }

  /**
   * Gets the value of the originalItemDetailWithChanges property.
   * 
   * @return possible object is {@link OriginalItemDetailWithChangesType }
   */
  public OriginalItemDetailWithChangesType getOriginalItemDetailWithChanges() {
    return originalItemDetailWithChanges;
  }

  /**
   * Sets the value of the originalItemDetailWithChanges property.
   * 
   * @param value allowed object is {@link OriginalItemDetailWithChangesType }
   */
  public void setOriginalItemDetailWithChanges(OriginalItemDetailWithChangesType value) {
    this.originalItemDetailWithChanges = value;
  }

  /**
   * Gets the value of the lineItemNote property.
   * 
   * @return possible object is {@link String }
   */
  public String getLineItemNote() {
    return lineItemNote;
  }

  /**
   * Sets the value of the lineItemNote property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setLineItemNote(String value) {
    this.lineItemNote = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

}
