//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.statisticsandforecasting.v1_0.statisticsandforecasting;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * contains the basic elements used to describe a characteristic product group with respect to the source and target systems.
 * <p>
 * Java class for CharacteristicProductGroupType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CharacteristicProductGroupType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SourceProductGroup" type="{rrn:org.xcbl:schemas/xcbl/v4_0/statisticsandforecasting/v1_0/statisticsandforecasting.xsd}BaseCharacteristicProductType" minOccurs="0"/>
 *         &lt;element name="TargetProductGroup" type="{rrn:org.xcbl:schemas/xcbl/v4_0/statisticsandforecasting/v1_0/statisticsandforecasting.xsd}BaseCharacteristicProductType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CharacteristicProductGroupType", propOrder = {
    "sourceProductGroup",
    "targetProductGroup"
})
public class CharacteristicProductGroupType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "SourceProductGroup")
  protected BaseCharacteristicProductType sourceProductGroup;
  @XmlElement(name = "TargetProductGroup")
  protected BaseCharacteristicProductType targetProductGroup;

  /**
   * Gets the value of the sourceProductGroup property.
   * 
   * @return possible object is {@link BaseCharacteristicProductType }
   */
  public BaseCharacteristicProductType getSourceProductGroup() {
    return sourceProductGroup;
  }

  /**
   * Sets the value of the sourceProductGroup property.
   * 
   * @param value allowed object is {@link BaseCharacteristicProductType }
   */
  public void setSourceProductGroup(BaseCharacteristicProductType value) {
    this.sourceProductGroup = value;
  }

  /**
   * Gets the value of the targetProductGroup property.
   * 
   * @return possible object is {@link BaseCharacteristicProductType }
   */
  public BaseCharacteristicProductType getTargetProductGroup() {
    return targetProductGroup;
  }

  /**
   * Sets the value of the targetProductGroup property.
   * 
   * @param value allowed object is {@link BaseCharacteristicProductType }
   */
  public void setTargetProductGroup(BaseCharacteristicProductType value) {
    this.targetProductGroup = value;
  }

}
