//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial;

import javax.xml.bind.annotation.*;

/**
 * <p>
 * Java class for InvoiceTypeCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="InvoiceTypeCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *     &lt;enumeration value="Other"/>
 *     &lt;enumeration value="MeteredServicesInvoice"/>
 *     &lt;enumeration value="CreditNoteFinancialAdjustment"/>
 *     &lt;enumeration value="DebitNoteFinancialAdjustment"/>
 *     &lt;enumeration value="InvoicingDataSheet"/>
 *     &lt;enumeration value="ProformaInvoice"/>
 *     &lt;enumeration value="CommercialInvoice"/>
 *     &lt;enumeration value="CreditNoteGoodsAndServices"/>
 *     &lt;enumeration value="DebitNoteGoodsAndServices"/>
 *     &lt;enumeration value="CommissionNote"/>
 *     &lt;enumeration value="CorrectedInvoice"/>
 *     &lt;enumeration value="ConsolidatedInvoice"/>
 *     &lt;enumeration value="PrepaymentInvoice"/>
 *     &lt;enumeration value="SelfBilledInvoice"/>
 *     &lt;enumeration value="DelcredereInvoice"/>
 *     &lt;enumeration value="FactoredInvoice"/>
 *     &lt;enumeration value="CreditInvoice"/>
 *     &lt;enumeration value="CreditMemo"/>
 *     &lt;enumeration value="DetourBilling"/>
 *     &lt;enumeration value="ThirdPartyConsolidatedInvoice"/>
 *     &lt;enumeration value="DebitInvoice"/>
 *     &lt;enumeration value="DebitMemo"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "InvoiceTypeCodeType")
@XmlEnum
public enum InvoiceTypeCodeType {

  /**
   * Custom Code
   */
  @XmlEnumValue("Other")
  OTHER("Other"),

  /**
   * Metered Services Invoice
   */
  @XmlEnumValue("MeteredServicesInvoice")
  METERED_SERVICES_INVOICE("MeteredServicesInvoice"),

  /**
   * Credit Note Related To Financial Adjustments
   */
  @XmlEnumValue("CreditNoteFinancialAdjustment")
  CREDIT_NOTE_FINANCIAL_ADJUSTMENT("CreditNoteFinancialAdjustment"),

  /**
   * Debit Note Related To Financial Adjustment
   */
  @XmlEnumValue("DebitNoteFinancialAdjustment")
  DEBIT_NOTE_FINANCIAL_ADJUSTMENT("DebitNoteFinancialAdjustment"),

  /**
   * Invoicing Data Sheet
   */
  @XmlEnumValue("InvoicingDataSheet")
  INVOICING_DATA_SHEET("InvoicingDataSheet"),

  /**
   * Proforma Invoice
   */
  @XmlEnumValue("ProformaInvoice")
  PROFORMA_INVOICE("ProformaInvoice"),

  /**
   * Commercial Invoice
   */
  @XmlEnumValue("CommercialInvoice")
  COMMERCIAL_INVOICE("CommercialInvoice"),

  /**
   * Credit Note - Goods and Services
   */
  @XmlEnumValue("CreditNoteGoodsAndServices")
  CREDIT_NOTE_GOODS_AND_SERVICES("CreditNoteGoodsAndServices"),

  /**
   * Debit Note - Goods and Services
   */
  @XmlEnumValue("DebitNoteGoodsAndServices")
  DEBIT_NOTE_GOODS_AND_SERVICES("DebitNoteGoodsAndServices"),

  /**
   * Commission Note
   */
  @XmlEnumValue("CommissionNote")
  COMMISSION_NOTE("CommissionNote"),

  /**
   * Corrected Invoice
   */
  @XmlEnumValue("CorrectedInvoice")
  CORRECTED_INVOICE("CorrectedInvoice"),

  /**
   * Consolidated Invoice
   */
  @XmlEnumValue("ConsolidatedInvoice")
  CONSOLIDATED_INVOICE("ConsolidatedInvoice"),

  /**
   * Prepayment Invoice
   */
  @XmlEnumValue("PrepaymentInvoice")
  PREPAYMENT_INVOICE("PrepaymentInvoice"),

  /**
   * Self-Billed Invoice
   */
  @XmlEnumValue("SelfBilledInvoice")
  SELF_BILLED_INVOICE("SelfBilledInvoice"),

  /**
   * Delcredere Invoice
   */
  @XmlEnumValue("DelcredereInvoice")
  DELCREDERE_INVOICE("DelcredereInvoice"),

  /**
   * Factored Invoice
   */
  @XmlEnumValue("FactoredInvoice")
  FACTORED_INVOICE("FactoredInvoice"),

  /**
   * Credit Invoice
   */
  @XmlEnumValue("CreditInvoice")
  CREDIT_INVOICE("CreditInvoice"),

  /**
   * Credit Memo
   */
  @XmlEnumValue("CreditMemo")
  CREDIT_MEMO("CreditMemo"),

  /**
   * Detour Billing
   */
  @XmlEnumValue("DetourBilling")
  DETOUR_BILLING("DetourBilling"),

  /**
   * Third Party Consolidated Invoice
   */
  @XmlEnumValue("ThirdPartyConsolidatedInvoice")
  THIRD_PARTY_CONSOLIDATED_INVOICE("ThirdPartyConsolidatedInvoice"),

  /**
   * Debit Invoice
   */
  @XmlEnumValue("DebitInvoice")
  DEBIT_INVOICE("DebitInvoice"),

  /**
   * Debit Memo
   */
  @XmlEnumValue("DebitMemo")
  DEBIT_MEMO("DebitMemo"),

  @XmlEnumValue("CorrectedFactoredInvoice")
  CORRECTED_FACTORED_INVOICE("CorrectedFactoredInvoice"),

  @XmlEnumValue("FactoredCreditNote")
  FACTORED_CREDIT_NOTE("FactoredCreditNote"),

  @XmlEnumValue("PrepaymentInvoiceCreditNote ")
  PREPAYMENT_INVOICE_CREDIT_NOTE("PrepaymentInvoiceCreditNote "),

  @XmlEnumValue("SelfBilledFactoredInvoice")
  SELF_BILLED_FACTORED_INVOICE("SelfBilledFactoredInvoice"),

  @XmlEnumValue("SelfBilledPrepaymentInvoice")
  SELF_BILLED_PREPAYMENT_INVOICE("SelfBilledPrepaymentInvoice"),

  @XmlEnumValue("SelfBilledCorrectedInvoice")
  SELF_BILLED_CORRECTED_INVOICE("SelfBilledCorrectedInvoice"),

  @XmlEnumValue("SelfBilledCorrectedFactoredInvoice")
  SELF_BILLED_CORRECTED_FACTORED_INVOICE("SelfBilledCorrectedFactoredInvoice"),

  @XmlEnumValue("SelfBilledCreditNote")
  SELF_BILLED_CREDIT_NOTE("SelfBilledCreditNote"),

  @XmlEnumValue("SelfBilledFactoredCreditNote")
  SELF_BILLED_FACTORED_CREDIT_NOTE("SelfBilledFactoredCreditNote");
  private final String value;

  InvoiceTypeCodeType(String v) {
    value = v;
  }

  public String value() {
    return value;
  }

  public static InvoiceTypeCodeType fromValue(String v) {
    for (InvoiceTypeCodeType c : InvoiceTypeCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v);
  }

}
