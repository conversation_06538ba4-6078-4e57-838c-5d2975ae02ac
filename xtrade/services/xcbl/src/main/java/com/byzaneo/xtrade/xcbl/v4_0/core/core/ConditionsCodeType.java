//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for ConditionsCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="ConditionsCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *     &lt;enumeration value="Other"/>
 *     &lt;enumeration value="WhereAirEquals1"/>
 *     &lt;enumeration value="WhereButylAcetateEquals1"/>
 *     &lt;enumeration value="WhereH2OEquals1OrWaterEquals1"/>
 *     &lt;enumeration value="CorrectedTo60DegreesFahrenheit"/>
 *     &lt;enumeration value="WhereTolueneEquals1"/>
 *     &lt;enumeration value="VaporInAir"/>
 *     &lt;enumeration value="VaporInOtherThanAir"/>
 *     &lt;enumeration value="StandardTemperatureAndPressure"/>
 *     &lt;enumeration value="ConditionsOtherThanStandardTemperatureAndPressure"/>
 *     &lt;enumeration value="InEthylAlcohol"/>
 *     &lt;enumeration value="InEthylEther"/>
 *     &lt;enumeration value="InWater"/>
 *     &lt;enumeration value="At1AtmospherePressure"/>
 *     &lt;enumeration value="WhereEtherEquals1"/>
 *     &lt;enumeration value="Actual"/>
 *     &lt;enumeration value="Predicted"/>
 *     &lt;enumeration value="Air-DriedBasis"/>
 *     &lt;enumeration value="As-ReceivedBasis"/>
 *     &lt;enumeration value="DryBasis"/>
 *     &lt;enumeration value="EquilibriumBasis"/>
 *     &lt;enumeration value="MoistureAndAsh-FreeBasis"/>
 *     &lt;enumeration value="OxidizingAtmosphere"/>
 *     &lt;enumeration value="ReducingAtmosphere"/>
 *     &lt;enumeration value="Calculated"/>
 *     &lt;enumeration value="ScaledWeight"/>
 *     &lt;enumeration value="Ratchet"/>
 *     &lt;enumeration value="SaturatedVapor"/>
 *     &lt;enumeration value="Unconditional"/>
 *     &lt;enumeration value="Short-Term"/>
 *     &lt;enumeration value="Time-Weighted"/>
 *     &lt;enumeration value="Corrected"/>
 *     &lt;enumeration value="Uncorrected"/>
 *     &lt;enumeration value="OffPeak"/>
 *     &lt;enumeration value="OnPeak"/>
 *     &lt;enumeration value="Intermediate"/>
 *     &lt;enumeration value="Average"/>
 *     &lt;enumeration value="PerGallon"/>
 *     &lt;enumeration value="Estimated"/>
 *     &lt;enumeration value="Minimum"/>
 *     &lt;enumeration value="Mist"/>
 *     &lt;enumeration value="Predominant"/>
 *     &lt;enumeration value="Total"/>
 *     &lt;enumeration value="Cost"/>
 *     &lt;enumeration value="Tenant"/>
 *     &lt;enumeration value="Owner"/>
 *     &lt;enumeration value="ForSale"/>
 *     &lt;enumeration value="RealEstateOwnedOrCorporateOwned"/>
 *     &lt;enumeration value="BoardedOrBlockedUp"/>
 *     &lt;enumeration value="Planned"/>
 *     &lt;enumeration value="Completed"/>
 *     &lt;enumeration value="Sold"/>
 *     &lt;enumeration value="Rented"/>
 *     &lt;enumeration value="Current"/>
 *     &lt;enumeration value="CurrentList"/>
 *     &lt;enumeration value="Effective"/>
 *     &lt;enumeration value="ListWhenSold"/>
 *     &lt;enumeration value="Sales"/>
 *     &lt;enumeration value="FinalList"/>
 *     &lt;enumeration value="AsIs"/>
 *     &lt;enumeration value="AsRepairedOrImproved"/>
 *     &lt;enumeration value="Instantaneous"/>
 *     &lt;enumeration value="Low"/>
 *     &lt;enumeration value="LowToGood"/>
 *     &lt;enumeration value="LowToHigh"/>
 *     &lt;enumeration value="LowToMedium"/>
 *     &lt;enumeration value="LowToModerate"/>
 *     &lt;enumeration value="Medium"/>
 *     &lt;enumeration value="MediumToGood"/>
 *     &lt;enumeration value="MediumToHigh"/>
 *     &lt;enumeration value="Moderate"/>
 *     &lt;enumeration value="ModerateToGood"/>
 *     &lt;enumeration value="ModerateToHigh"/>
 *     &lt;enumeration value="ModerateToMedium"/>
 *     &lt;enumeration value="Good"/>
 *     &lt;enumeration value="GoodToHigh"/>
 *     &lt;enumeration value="High"/>
 *     &lt;enumeration value="Budgeted"/>
 *     &lt;enumeration value="Forecast"/>
 *     &lt;enumeration value="Adjusted"/>
 *     &lt;enumeration value="Allocated"/>
 *     &lt;enumeration value="Increasing"/>
 *     &lt;enumeration value="Stable"/>
 *     &lt;enumeration value="Declining"/>
 *     &lt;enumeration value="Previous"/>
 *     &lt;enumeration value="Potential"/>
 *     &lt;enumeration value="Modeled"/>
 *     &lt;enumeration value="Measured"/>
 *     &lt;enumeration value="Maximum"/>
 *     &lt;enumeration value="SummerOn-Peak"/>
 *     &lt;enumeration value="SummerMid-Peak"/>
 *     &lt;enumeration value="SummerOff-Peak"/>
 *     &lt;enumeration value="SummerSuperOn-Peak"/>
 *     &lt;enumeration value="SummerSuperOff-Peak"/>
 *     &lt;enumeration value="WinterOn-Peak"/>
 *     &lt;enumeration value="WinterMid-Peak"/>
 *     &lt;enumeration value="WinterOff-Peak"/>
 *     &lt;enumeration value="WinterSuperOn-Peak"/>
 *     &lt;enumeration value="WinterSuperOff-Peak"/>
 *     &lt;enumeration value="SummerDay"/>
 *     &lt;enumeration value="SummerNight"/>
 *     &lt;enumeration value="WinterDay"/>
 *     &lt;enumeration value="WinterNight"/>
 *     &lt;enumeration value="Summer"/>
 *     &lt;enumeration value="Winter"/>
 *     &lt;enumeration value="Day"/>
 *     &lt;enumeration value="Night"/>
 *     &lt;enumeration value="Peak-2"/>
 *     &lt;enumeration value="Peak-3"/>
 *     &lt;enumeration value="Peak-4"/>
 *     &lt;enumeration value="Shoulder"/>
 *     &lt;enumeration value="NonTimeRelatedDemand"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "ConditionsCodeType")
@XmlEnum
public enum ConditionsCodeType {

  /**
   * Custom Code
   */
  @XmlEnumValue("Other")
  OTHER("Other"),
  @XmlEnumValue("WhereAirEquals1")
  WHERE_AIR_EQUALS_1("WhereAirEquals1"),
  @XmlEnumValue("WhereButylAcetateEquals1")
  WHERE_BUTYL_ACETATE_EQUALS_1("WhereButylAcetateEquals1"),
  @XmlEnumValue("WhereH2OEquals1OrWaterEquals1")
  WHERE_H_2_O_EQUALS_1_OR_WATER_EQUALS_1("WhereH2OEquals1OrWaterEquals1"),
  @XmlEnumValue("CorrectedTo60DegreesFahrenheit")
  CORRECTED_TO_60_DEGREES_FAHRENHEIT("CorrectedTo60DegreesFahrenheit"),
  @XmlEnumValue("WhereTolueneEquals1")
  WHERE_TOLUENE_EQUALS_1("WhereTolueneEquals1"),
  @XmlEnumValue("VaporInAir")
  VAPOR_IN_AIR("VaporInAir"),
  @XmlEnumValue("VaporInOtherThanAir")
  VAPOR_IN_OTHER_THAN_AIR("VaporInOtherThanAir"),
  @XmlEnumValue("StandardTemperatureAndPressure")
  STANDARD_TEMPERATURE_AND_PRESSURE("StandardTemperatureAndPressure"),
  @XmlEnumValue("ConditionsOtherThanStandardTemperatureAndPressure")
  CONDITIONS_OTHER_THAN_STANDARD_TEMPERATURE_AND_PRESSURE("ConditionsOtherThanStandardTemperatureAndPressure"),
  @XmlEnumValue("InEthylAlcohol")
  IN_ETHYL_ALCOHOL("InEthylAlcohol"),
  @XmlEnumValue("InEthylEther")
  IN_ETHYL_ETHER("InEthylEther"),
  @XmlEnumValue("InWater")
  IN_WATER("InWater"),
  @XmlEnumValue("At1AtmospherePressure")
  AT_1_ATMOSPHERE_PRESSURE("At1AtmospherePressure"),
  @XmlEnumValue("WhereEtherEquals1")
  WHERE_ETHER_EQUALS_1("WhereEtherEquals1"),
  @XmlEnumValue("Actual")
  ACTUAL("Actual"),
  @XmlEnumValue("Predicted")
  PREDICTED("Predicted"),
  @XmlEnumValue("Air-DriedBasis")
  AIR_DRIED_BASIS("Air-DriedBasis"),
  @XmlEnumValue("As-ReceivedBasis")
  AS_RECEIVED_BASIS("As-ReceivedBasis"),
  @XmlEnumValue("DryBasis")
  DRY_BASIS("DryBasis"),
  @XmlEnumValue("EquilibriumBasis")
  EQUILIBRIUM_BASIS("EquilibriumBasis"),
  @XmlEnumValue("MoistureAndAsh-FreeBasis")
  MOISTURE_AND_ASH_FREE_BASIS("MoistureAndAsh-FreeBasis"),
  @XmlEnumValue("OxidizingAtmosphere")
  OXIDIZING_ATMOSPHERE("OxidizingAtmosphere"),
  @XmlEnumValue("ReducingAtmosphere")
  REDUCING_ATMOSPHERE("ReducingAtmosphere"),
  @XmlEnumValue("Calculated")
  CALCULATED("Calculated"),
  @XmlEnumValue("ScaledWeight")
  SCALED_WEIGHT("ScaledWeight"),
  @XmlEnumValue("Ratchet")
  RATCHET("Ratchet"),
  @XmlEnumValue("SaturatedVapor")
  SATURATED_VAPOR("SaturatedVapor"),
  @XmlEnumValue("Unconditional")
  UNCONDITIONAL("Unconditional"),
  @XmlEnumValue("Short-Term")
  SHORT_TERM("Short-Term"),
  @XmlEnumValue("Time-Weighted")
  TIME_WEIGHTED("Time-Weighted"),
  @XmlEnumValue("Corrected")
  CORRECTED("Corrected"),
  @XmlEnumValue("Uncorrected")
  UNCORRECTED("Uncorrected"),
  @XmlEnumValue("OffPeak")
  OFF_PEAK("OffPeak"),
  @XmlEnumValue("OnPeak")
  ON_PEAK("OnPeak"),
  @XmlEnumValue("Intermediate")
  INTERMEDIATE("Intermediate"),
  @XmlEnumValue("Average")
  AVERAGE("Average"),
  @XmlEnumValue("PerGallon")
  PER_GALLON("PerGallon"),
  @XmlEnumValue("Estimated")
  ESTIMATED("Estimated"),
  @XmlEnumValue("Minimum")
  MINIMUM("Minimum"),
  @XmlEnumValue("Mist")
  MIST("Mist"),
  @XmlEnumValue("Predominant")
  PREDOMINANT("Predominant"),
  @XmlEnumValue("Total")
  TOTAL("Total"),
  @XmlEnumValue("Cost")
  COST("Cost"),
  @XmlEnumValue("Tenant")
  TENANT("Tenant"),
  @XmlEnumValue("Owner")
  OWNER("Owner"),
  @XmlEnumValue("ForSale")
  FOR_SALE("ForSale"),
  @XmlEnumValue("RealEstateOwnedOrCorporateOwned")
  REAL_ESTATE_OWNED_OR_CORPORATE_OWNED("RealEstateOwnedOrCorporateOwned"),
  @XmlEnumValue("BoardedOrBlockedUp")
  BOARDED_OR_BLOCKED_UP("BoardedOrBlockedUp"),
  @XmlEnumValue("Planned")
  PLANNED("Planned"),
  @XmlEnumValue("Completed")
  COMPLETED("Completed"),
  @XmlEnumValue("Sold")
  SOLD("Sold"),
  @XmlEnumValue("Rented")
  RENTED("Rented"),
  @XmlEnumValue("Current")
  CURRENT("Current"),
  @XmlEnumValue("CurrentList")
  CURRENT_LIST("CurrentList"),
  @XmlEnumValue("Effective")
  EFFECTIVE("Effective"),
  @XmlEnumValue("ListWhenSold")
  LIST_WHEN_SOLD("ListWhenSold"),
  @XmlEnumValue("Sales")
  SALES("Sales"),
  @XmlEnumValue("FinalList")
  FINAL_LIST("FinalList"),
  @XmlEnumValue("AsIs")
  AS_IS("AsIs"),
  @XmlEnumValue("AsRepairedOrImproved")
  AS_REPAIRED_OR_IMPROVED("AsRepairedOrImproved"),
  @XmlEnumValue("Instantaneous")
  INSTANTANEOUS("Instantaneous"),
  @XmlEnumValue("Low")
  LOW("Low"),
  @XmlEnumValue("LowToGood")
  LOW_TO_GOOD("LowToGood"),
  @XmlEnumValue("LowToHigh")
  LOW_TO_HIGH("LowToHigh"),
  @XmlEnumValue("LowToMedium")
  LOW_TO_MEDIUM("LowToMedium"),
  @XmlEnumValue("LowToModerate")
  LOW_TO_MODERATE("LowToModerate"),
  @XmlEnumValue("Medium")
  MEDIUM("Medium"),
  @XmlEnumValue("MediumToGood")
  MEDIUM_TO_GOOD("MediumToGood"),
  @XmlEnumValue("MediumToHigh")
  MEDIUM_TO_HIGH("MediumToHigh"),
  @XmlEnumValue("Moderate")
  MODERATE("Moderate"),
  @XmlEnumValue("ModerateToGood")
  MODERATE_TO_GOOD("ModerateToGood"),
  @XmlEnumValue("ModerateToHigh")
  MODERATE_TO_HIGH("ModerateToHigh"),
  @XmlEnumValue("ModerateToMedium")
  MODERATE_TO_MEDIUM("ModerateToMedium"),
  @XmlEnumValue("Good")
  GOOD("Good"),
  @XmlEnumValue("GoodToHigh")
  GOOD_TO_HIGH("GoodToHigh"),
  @XmlEnumValue("High")
  HIGH("High"),
  @XmlEnumValue("Budgeted")
  BUDGETED("Budgeted"),
  @XmlEnumValue("Forecast")
  FORECAST("Forecast"),
  @XmlEnumValue("Adjusted")
  ADJUSTED("Adjusted"),
  @XmlEnumValue("Allocated")
  ALLOCATED("Allocated"),
  @XmlEnumValue("Increasing")
  INCREASING("Increasing"),
  @XmlEnumValue("Stable")
  STABLE("Stable"),
  @XmlEnumValue("Declining")
  DECLINING("Declining"),
  @XmlEnumValue("Previous")
  PREVIOUS("Previous"),
  @XmlEnumValue("Potential")
  POTENTIAL("Potential"),
  @XmlEnumValue("Modeled")
  MODELED("Modeled"),
  @XmlEnumValue("Measured")
  MEASURED("Measured"),
  @XmlEnumValue("Maximum")
  MAXIMUM("Maximum"),
  @XmlEnumValue("SummerOn-Peak")
  SUMMER_ON_PEAK("SummerOn-Peak"),
  @XmlEnumValue("SummerMid-Peak")
  SUMMER_MID_PEAK("SummerMid-Peak"),
  @XmlEnumValue("SummerOff-Peak")
  SUMMER_OFF_PEAK("SummerOff-Peak"),
  @XmlEnumValue("SummerSuperOn-Peak")
  SUMMER_SUPER_ON_PEAK("SummerSuperOn-Peak"),
  @XmlEnumValue("SummerSuperOff-Peak")
  SUMMER_SUPER_OFF_PEAK("SummerSuperOff-Peak"),
  @XmlEnumValue("WinterOn-Peak")
  WINTER_ON_PEAK("WinterOn-Peak"),
  @XmlEnumValue("WinterMid-Peak")
  WINTER_MID_PEAK("WinterMid-Peak"),
  @XmlEnumValue("WinterOff-Peak")
  WINTER_OFF_PEAK("WinterOff-Peak"),
  @XmlEnumValue("WinterSuperOn-Peak")
  WINTER_SUPER_ON_PEAK("WinterSuperOn-Peak"),
  @XmlEnumValue("WinterSuperOff-Peak")
  WINTER_SUPER_OFF_PEAK("WinterSuperOff-Peak"),
  @XmlEnumValue("SummerDay")
  SUMMER_DAY("SummerDay"),
  @XmlEnumValue("SummerNight")
  SUMMER_NIGHT("SummerNight"),
  @XmlEnumValue("WinterDay")
  WINTER_DAY("WinterDay"),
  @XmlEnumValue("WinterNight")
  WINTER_NIGHT("WinterNight"),
  @XmlEnumValue("Summer")
  SUMMER("Summer"),
  @XmlEnumValue("Winter")
  WINTER("Winter"),
  @XmlEnumValue("Day")
  DAY("Day"),
  @XmlEnumValue("Night")
  NIGHT("Night"),
  @XmlEnumValue("Peak-2")
  PEAK_2("Peak-2"),
  @XmlEnumValue("Peak-3")
  PEAK_3("Peak-3"),
  @XmlEnumValue("Peak-4")
  PEAK_4("Peak-4"),
  @XmlEnumValue("Shoulder")
  SHOULDER("Shoulder"),
  @XmlEnumValue("NonTimeRelatedDemand")
  NON_TIME_RELATED_DEMAND("NonTimeRelatedDemand");
  private final String value;

  ConditionsCodeType(String v) {
    value = v;
  }

  public String value() {
    return value;
  }

  public static ConditionsCodeType fromValue(String v) {
    for (ConditionsCodeType c : ConditionsCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v);
  }

}
