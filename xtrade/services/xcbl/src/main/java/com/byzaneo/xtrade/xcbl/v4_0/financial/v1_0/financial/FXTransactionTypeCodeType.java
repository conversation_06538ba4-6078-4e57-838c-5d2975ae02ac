//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for FXTransactionTypeCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="FXTransactionTypeCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *     &lt;enumeration value="Spot"/>
 *     &lt;enumeration value="Forward"/>
 *     &lt;enumeration value="Future"/>
 *     &lt;enumeration value="Option"/>
 *     &lt;enumeration value="Other"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "FXTransactionTypeCodeType")
@XmlEnum
public enum FXTransactionTypeCodeType {

  @XmlEnumValue("Spot")
  SPOT("Spot"),
  @XmlEnumValue("Forward")
  FORWARD("Forward"),
  @XmlEnumValue("Future")
  FUTURE("Future"),
  @XmlEnumValue("Option")
  OPTION("Option"),
  @XmlEnumValue("Other")
  OTHER("Other");
  private final String value;

  FXTransactionTypeCodeType(String v) {
    value = v;
  }

  public String value() {
    return value;
  }

  public static FXTransactionTypeCodeType fromValue(String v) {
    for (FXTransactionTypeCodeType c : FXTransactionTypeCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v);
  }

}
