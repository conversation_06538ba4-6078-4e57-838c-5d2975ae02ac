//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.messagemanagement.v1_0.messagemanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.LineItemNumberReferenceType;

/**
 * contains information to identify the line item and the schedule line information that contains an error. If an error does not occur in
 * the line item it is not necessary to complete this
 * <p>
 * Java class for ItemReferenceType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ItemReferenceType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="LineItemNumberReference" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}LineItemNumberReferenceType"/>
 *         &lt;element name="ScheduleLineID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ItemReferenceType", propOrder = {
    "lineItemNumberReference",
    "scheduleLineID"
})
public class ItemReferenceType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "LineItemNumberReference", required = true)
  protected LineItemNumberReferenceType lineItemNumberReference;
  @XmlElement(name = "ScheduleLineID")
  protected Integer scheduleLineID;

  /**
   * Gets the value of the lineItemNumberReference property.
   * 
   * @return possible object is {@link LineItemNumberReferenceType }
   */
  public LineItemNumberReferenceType getLineItemNumberReference() {
    return lineItemNumberReference;
  }

  /**
   * Sets the value of the lineItemNumberReference property.
   * 
   * @param value allowed object is {@link LineItemNumberReferenceType }
   */
  public void setLineItemNumberReference(LineItemNumberReferenceType value) {
    this.lineItemNumberReference = value;
  }

  /**
   * Gets the value of the scheduleLineID property.
   * 
   * @return possible object is {@link Integer }
   */
  public Integer getScheduleLineID() {
    return scheduleLineID;
  }

  /**
   * Sets the value of the scheduleLineID property.
   * 
   * @param value allowed object is {@link Integer }
   */
  public void setScheduleLineID(Integer value) {
    this.scheduleLineID = value;
  }

}
