//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.preordermanagement.v1_0.preordermanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.util.Date;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.BaseItemReferencesType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ItemIdentifiersType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LineItemNumType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LineItemNumberReferenceType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LineItemTypeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfAttachmentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.QuantityType;

/**
 * contains the item detail for the AvailabilityCheckRequest.
 * <p>
 * Java class for AvailabilityCheckRequestItemDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AvailabilityCheckRequestItemDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="LineItemNum" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}LineItemNumType"/>
 *         &lt;element name="LineItemType" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}LineItemTypeType" minOccurs="0"/>
 *         &lt;element name="ParentItemNumber" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}LineItemNumberReferenceType" minOccurs="0"/>
 *         &lt;element name="ItemIdentifiers" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ItemIdentifiersType"/>
 *         &lt;element name="RequestedQuantity" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}QuantityType"/>
 *         &lt;element name="RequestedDeliveryDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="MaxBackOrderQuantity" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}QuantityType" minOccurs="0"/>
 *         &lt;element name="BaseItemReferences" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}BaseItemReferencesType" minOccurs="0"/>
 *         &lt;element name="GeneralLineItemNote" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *         &lt;element name="ListOfAttachment" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfAttachmentType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AvailabilityCheckRequestItemDetailType", propOrder = {
    "lineItemNum",
    "lineItemType",
    "parentItemNumber",
    "itemIdentifiers",
    "requestedQuantity",
    "requestedDeliveryDate",
    "maxBackOrderQuantity",
    "baseItemReferences",
    "generalLineItemNote",
    "listOfStructuredNote",
    "listOfAttachment"
})
public class AvailabilityCheckRequestItemDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "LineItemNum", required = true)
  protected LineItemNumType lineItemNum;
  @XmlElement(name = "LineItemType")
  protected LineItemTypeType lineItemType;
  @XmlElement(name = "ParentItemNumber")
  protected LineItemNumberReferenceType parentItemNumber;
  @XmlElement(name = "ItemIdentifiers", required = true)
  protected ItemIdentifiersType itemIdentifiers;
  @XmlElement(name = "RequestedQuantity", required = true)
  protected QuantityType requestedQuantity;
  @XmlElement(name = "RequestedDeliveryDate")
  @XmlSchemaType(name = "dateTime")
  protected Date requestedDeliveryDate;
  @XmlElement(name = "MaxBackOrderQuantity")
  protected QuantityType maxBackOrderQuantity;
  @XmlElement(name = "BaseItemReferences")
  protected BaseItemReferencesType baseItemReferences;
  @XmlElement(name = "GeneralLineItemNote")
  protected String generalLineItemNote;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;
  @XmlElement(name = "ListOfAttachment")
  protected ListOfAttachmentType listOfAttachment;

  /**
   * Gets the value of the lineItemNum property.
   * 
   * @return possible object is {@link LineItemNumType }
   */
  public LineItemNumType getLineItemNum() {
    return lineItemNum;
  }

  /**
   * Sets the value of the lineItemNum property.
   * 
   * @param value allowed object is {@link LineItemNumType }
   */
  public void setLineItemNum(LineItemNumType value) {
    this.lineItemNum = value;
  }

  /**
   * Gets the value of the lineItemType property.
   * 
   * @return possible object is {@link LineItemTypeType }
   */
  public LineItemTypeType getLineItemType() {
    return lineItemType;
  }

  /**
   * Sets the value of the lineItemType property.
   * 
   * @param value allowed object is {@link LineItemTypeType }
   */
  public void setLineItemType(LineItemTypeType value) {
    this.lineItemType = value;
  }

  /**
   * Gets the value of the parentItemNumber property.
   * 
   * @return possible object is {@link LineItemNumberReferenceType }
   */
  public LineItemNumberReferenceType getParentItemNumber() {
    return parentItemNumber;
  }

  /**
   * Sets the value of the parentItemNumber property.
   * 
   * @param value allowed object is {@link LineItemNumberReferenceType }
   */
  public void setParentItemNumber(LineItemNumberReferenceType value) {
    this.parentItemNumber = value;
  }

  /**
   * Gets the value of the itemIdentifiers property.
   * 
   * @return possible object is {@link ItemIdentifiersType }
   */
  public ItemIdentifiersType getItemIdentifiers() {
    return itemIdentifiers;
  }

  /**
   * Sets the value of the itemIdentifiers property.
   * 
   * @param value allowed object is {@link ItemIdentifiersType }
   */
  public void setItemIdentifiers(ItemIdentifiersType value) {
    this.itemIdentifiers = value;
  }

  /**
   * Gets the value of the requestedQuantity property.
   * 
   * @return possible object is {@link QuantityType }
   */
  public QuantityType getRequestedQuantity() {
    return requestedQuantity;
  }

  /**
   * Sets the value of the requestedQuantity property.
   * 
   * @param value allowed object is {@link QuantityType }
   */
  public void setRequestedQuantity(QuantityType value) {
    this.requestedQuantity = value;
  }

  /**
   * Gets the value of the requestedDeliveryDate property.
   * 
   * @return possible object is {@link Date }
   */
  public Date getRequestedDeliveryDate() {
    return requestedDeliveryDate;
  }

  /**
   * Sets the value of the requestedDeliveryDate property.
   * 
   * @param value allowed object is {@link Date }
   */
  public void setRequestedDeliveryDate(Date value) {
    this.requestedDeliveryDate = value;
  }

  /**
   * Gets the value of the maxBackOrderQuantity property.
   * 
   * @return possible object is {@link QuantityType }
   */
  public QuantityType getMaxBackOrderQuantity() {
    return maxBackOrderQuantity;
  }

  /**
   * Sets the value of the maxBackOrderQuantity property.
   * 
   * @param value allowed object is {@link QuantityType }
   */
  public void setMaxBackOrderQuantity(QuantityType value) {
    this.maxBackOrderQuantity = value;
  }

  /**
   * Gets the value of the baseItemReferences property.
   * 
   * @return possible object is {@link BaseItemReferencesType }
   */
  public BaseItemReferencesType getBaseItemReferences() {
    return baseItemReferences;
  }

  /**
   * Sets the value of the baseItemReferences property.
   * 
   * @param value allowed object is {@link BaseItemReferencesType }
   */
  public void setBaseItemReferences(BaseItemReferencesType value) {
    this.baseItemReferences = value;
  }

  /**
   * Gets the value of the generalLineItemNote property.
   * 
   * @return possible object is {@link String }
   */
  public String getGeneralLineItemNote() {
    return generalLineItemNote;
  }

  /**
   * Sets the value of the generalLineItemNote property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setGeneralLineItemNote(String value) {
    this.generalLineItemNote = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

  /**
   * Gets the value of the listOfAttachment property.
   * 
   * @return possible object is {@link ListOfAttachmentType }
   */
  public ListOfAttachmentType getListOfAttachment() {
    return listOfAttachment;
  }

  /**
   * Sets the value of the listOfAttachment property.
   * 
   * @param value allowed object is {@link ListOfAttachmentType }
   */
  public void setListOfAttachment(ListOfAttachmentType value) {
    this.listOfAttachment = value;
  }

}
