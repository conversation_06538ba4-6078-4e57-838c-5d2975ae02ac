//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2021.01.12 at 11:08:14 AM EET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.io.Serializable;

import javax.xml.bind.annotation.*;

/**
 * is a container for a date that has its function indicated with a qualifier.
 * <p>
 * Java class for InvoiceDateCodedType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="InvoiceDateCodedType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Date" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ComplexDateType"/>
 *         &lt;element name="DateQualifier" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}DateQualifierType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InvoiceDateCodedType", propOrder = {
    "date",
    "dateQualifier"
})
public class InvoiceDateCodedType
    implements Serializable {

  private final static long serialVersionUID = 1L;
  @XmlElement(name = "Date", required = true)
  protected ComplexDateType date;
  @XmlElement(name = "DateQualifier", required = true)
  protected DateQualifierType dateQualifier;

  /**
   * Gets the value of the date property.
   * 
   * @return possible object is {@link ComplexDateType }
   */
  public ComplexDateType getDate() {
    if (date == null)
      date = new ComplexDateType();
    return date;
  }

  /**
   * Sets the value of the date property.
   * 
   * @param value allowed object is {@link ComplexDateType }
   */
  public void setDate(ComplexDateType value) {
    this.date = value;
  }

  /**
   * Gets the value of the dateQualifier property.
   * 
   * @return possible object is {@link DateQualifierType }
   */
  public DateQualifierType getDateQualifier() {
    return dateQualifier;
  }

  /**
   * Sets the value of the dateQualifier property.
   * 
   * @param value allowed object is {@link DateQualifierType }
   */
  public void setDateQualifier(DateQualifierType value) {
    this.dateQualifier = value;
  }

}
