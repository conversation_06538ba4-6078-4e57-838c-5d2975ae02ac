<AvailabilityToPromise xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd">
    <AvailabilityToPromiseHeader>
        <AvailabilityID>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityID</AvailabilityID>
        <AvailabilityIssueDate>2003-01-01T00:00:01</AvailabilityIssueDate>
        <ListOfReferenceCoded>
            <core:ReferenceCoded>
                <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                <core:ReferenceTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfReferenceCoded/core:ReferenceCoded/core:ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                <core:PrimaryReference>
                    <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfReferenceCoded/core:ReferenceCoded/core:PrimaryReference/core:RefNum</core:RefNum>
                    <core:RefDate>2003-01-01T00:00:02</core:RefDate>
                </core:PrimaryReference>
                <core:SupportingReference>
                    <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfReferenceCoded/core:ReferenceCoded/core:SupportingReference/core:RefNum</core:RefNum>
                    <core:RefDate>2003-01-01T00:00:03</core:RefDate>
                </core:SupportingReference>
                <core:SupportingSubReference>
                    <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfReferenceCoded/core:ReferenceCoded/core:SupportingSubReference/core:RefNum</core:RefNum>
                    <core:RefDate>2003-01-01T00:00:04</core:RefDate>
                </core:SupportingSubReference>
                <core:ReferenceDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfReferenceCoded/core:ReferenceCoded/core:ReferenceDescription</core:ReferenceDescription>
            </core:ReferenceCoded>
        </ListOfReferenceCoded>
        <AvailabilityToPromisePurpose>
            <AvailabilityToPromisePurposeCoded>Closed</AvailabilityToPromisePurposeCoded>
            <AvailabilityToPromisePurposeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromisePurpose/AvailabilityToPromisePurposeCodedOther</AvailabilityToPromisePurposeCodedOther>
        </AvailabilityToPromisePurpose>
        <AvailabilityDeliveryOption>
            <AvailabilityDeliveryOptionCoded>Complete</AvailabilityDeliveryOptionCoded>
            <AvailabilityDeliveryOptionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityDeliveryOption/AvailabilityDeliveryOptionCodedOther</AvailabilityDeliveryOptionCodedOther>
        </AvailabilityDeliveryOption>
        <ATPCheckType>
            <ATPCheckTypeCoded>NotSpecified</ATPCheckTypeCoded>
            <ATPCheckTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/ATPCheckType/ATPCheckTypeCodedOther</ATPCheckTypeCodedOther>
        </ATPCheckType>
        <ATPResponse>
            <AvailabilityToPromiseResponseCoded>NotAccepted</AvailabilityToPromiseResponseCoded>
            <AvailabilityToPromiseResponseCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/ATPResponse/AvailabilityToPromiseResponseCodedOther</AvailabilityToPromiseResponseCodedOther>
        </ATPResponse>
        <InitiatingParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
            <core:ListOfIdentifier>
                <core:Identifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                </core:Identifier>
            </core:ListOfIdentifier>
            <core:MDFBusiness>false</core:MDFBusiness>
            <core:NameAddress>
                <core:ExternalAddressID>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                <core:Name1>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Name1</core:Name1>
                <core:Name2>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Name2</core:Name2>
                <core:Name3>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Name3</core:Name3>
                <core:Identifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                </core:Identifier>
                <core:POBox POBoxPostalCode="AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:POBox/@POBoxPostalCode">AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:POBox</core:POBox>
                <core:Street>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Street</core:Street>
                <core:HouseNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                <core:StreetSupplement1>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                <core:StreetSupplement2>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                <core:Building>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Building</core:Building>
                <core:Floor>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Floor</core:Floor>
                <core:RoomNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                <core:InhouseMail>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                <core:Department>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Department</core:Department>
                <core:PostalCode>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:PostalCode</core:PostalCode>
                <core:City>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:City</core:City>
                <core:County>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:County</core:County>
                <core:Region>
                    <core:RegionCoded>ARA</core:RegionCoded>
                    <core:RegionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                </core:Region>
                <core:District>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:District</core:District>
                <core:Country>
                    <core:CountryCoded>AE</core:CountryCoded>
                    <core:CountryCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                </core:Country>
                <core:Timezone>
                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                    <core:TimezoneCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                </core:Timezone>
            </core:NameAddress>
            <core:PrimaryContact>
                <core:ContactID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                </core:ContactID>
                <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactName</core:ContactName>
                <core:ContactFunction>
                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                    <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                </core:ContactFunction>
                <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
                <core:CorrespondenceLanguage>
                    <core:LanguageCoded>aa</core:LanguageCoded>
                    <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                    <core:LocaleCoded>ae</core:LocaleCoded>
                    <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                </core:CorrespondenceLanguage>
            </core:PrimaryContact>
            <core:OtherContacts>
                <core:Contact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:Contact>
            </core:OtherContacts>
            <core:PartyTaxInformation>
                <core:TaxIdentifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                </core:TaxIdentifier>
                <core:RegisteredName>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                <core:RegisteredOffice>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                <core:TaxLocation>
                    <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                    <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                    <core:LocationIdentifier>
                        <core:LocID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                        </core:LocID>
                        <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                    </core:LocationIdentifier>
                    <core:GPSCoordinates>
                        <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                        <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                        <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                    </core:GPSCoordinates>
                </core:TaxLocation>
                <core:CompanyRegistrationNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/InitiatingParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
            </core:PartyTaxInformation>
        </InitiatingParty>
        <RespondingParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
            <core:ListOfIdentifier>
                <core:Identifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                </core:Identifier>
            </core:ListOfIdentifier>
            <core:MDFBusiness>true</core:MDFBusiness>
            <core:NameAddress>
                <core:ExternalAddressID>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                <core:Name1>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Name1</core:Name1>
                <core:Name2>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Name2</core:Name2>
                <core:Name3>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Name3</core:Name3>
                <core:Identifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                </core:Identifier>
                <core:POBox POBoxPostalCode="AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:POBox/@POBoxPostalCode">AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:POBox</core:POBox>
                <core:Street>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Street</core:Street>
                <core:HouseNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                <core:StreetSupplement1>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                <core:StreetSupplement2>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                <core:Building>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Building</core:Building>
                <core:Floor>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Floor</core:Floor>
                <core:RoomNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                <core:InhouseMail>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                <core:Department>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Department</core:Department>
                <core:PostalCode>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:PostalCode</core:PostalCode>
                <core:City>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:City</core:City>
                <core:County>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:County</core:County>
                <core:Region>
                    <core:RegionCoded>ARA</core:RegionCoded>
                    <core:RegionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                </core:Region>
                <core:District>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:District</core:District>
                <core:Country>
                    <core:CountryCoded>AE</core:CountryCoded>
                    <core:CountryCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                </core:Country>
                <core:Timezone>
                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                    <core:TimezoneCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                </core:Timezone>
            </core:NameAddress>
            <core:PrimaryContact>
                <core:ContactID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                </core:ContactID>
                <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactName</core:ContactName>
                <core:ContactFunction>
                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                    <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                </core:ContactFunction>
                <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
                <core:CorrespondenceLanguage>
                    <core:LanguageCoded>aa</core:LanguageCoded>
                    <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                    <core:LocaleCoded>ae</core:LocaleCoded>
                    <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                </core:CorrespondenceLanguage>
            </core:PrimaryContact>
            <core:OtherContacts>
                <core:Contact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:Contact>
            </core:OtherContacts>
            <core:PartyTaxInformation>
                <core:TaxIdentifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                </core:TaxIdentifier>
                <core:RegisteredName>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                <core:RegisteredOffice>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                <core:TaxLocation>
                    <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                    <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                    <core:LocationIdentifier>
                        <core:LocID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                        </core:LocID>
                        <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                    </core:LocationIdentifier>
                    <core:GPSCoordinates>
                        <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                        <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                        <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                    </core:GPSCoordinates>
                </core:TaxLocation>
                <core:CompanyRegistrationNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/RespondingParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
            </core:PartyTaxInformation>
        </RespondingParty>
        <AvailabilityShipToParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
            <core:ListOfIdentifier>
                <core:Identifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                </core:Identifier>
            </core:ListOfIdentifier>
            <core:MDFBusiness>false</core:MDFBusiness>
            <core:NameAddress>
                <core:ExternalAddressID>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                <core:Name1>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Name1</core:Name1>
                <core:Name2>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Name2</core:Name2>
                <core:Name3>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Name3</core:Name3>
                <core:Identifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                </core:Identifier>
                <core:POBox POBoxPostalCode="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:POBox/@POBoxPostalCode">AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:POBox</core:POBox>
                <core:Street>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Street</core:Street>
                <core:HouseNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                <core:StreetSupplement1>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                <core:StreetSupplement2>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                <core:Building>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Building</core:Building>
                <core:Floor>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Floor</core:Floor>
                <core:RoomNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                <core:InhouseMail>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                <core:Department>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Department</core:Department>
                <core:PostalCode>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:PostalCode</core:PostalCode>
                <core:City>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:City</core:City>
                <core:County>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:County</core:County>
                <core:Region>
                    <core:RegionCoded>ARA</core:RegionCoded>
                    <core:RegionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                </core:Region>
                <core:District>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:District</core:District>
                <core:Country>
                    <core:CountryCoded>AE</core:CountryCoded>
                    <core:CountryCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                </core:Country>
                <core:Timezone>
                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                    <core:TimezoneCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                </core:Timezone>
            </core:NameAddress>
            <core:PrimaryContact>
                <core:ContactID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                </core:ContactID>
                <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactName</core:ContactName>
                <core:ContactFunction>
                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                    <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                </core:ContactFunction>
                <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
                <core:CorrespondenceLanguage>
                    <core:LanguageCoded>aa</core:LanguageCoded>
                    <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                    <core:LocaleCoded>ae</core:LocaleCoded>
                    <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                </core:CorrespondenceLanguage>
            </core:PrimaryContact>
            <core:OtherContacts>
                <core:Contact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:Contact>
            </core:OtherContacts>
            <core:PartyTaxInformation>
                <core:TaxIdentifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                </core:TaxIdentifier>
                <core:RegisteredName>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                <core:RegisteredOffice>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                <core:TaxLocation>
                    <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                    <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                    <core:LocationIdentifier>
                        <core:LocID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                        </core:LocID>
                        <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                    </core:LocationIdentifier>
                    <core:GPSCoordinates>
                        <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                        <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                        <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                    </core:GPSCoordinates>
                </core:TaxLocation>
                <core:CompanyRegistrationNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityShipToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
            </core:PartyTaxInformation>
        </AvailabilityShipToParty>
        <AvailabilityToPromiseHeaderTransport>
            <core:TransportRouteID>1001</core:TransportRouteID>
            <core:TransportMode>
                <core:TransportModeCoded>Air</core:TransportModeCoded>
                <core:TransportModeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMode/core:TransportModeCodedOther</core:TransportModeCodedOther>
            </core:TransportMode>
            <core:TransportMeans>
                <core:TransportMeansCoded>20FtILContainer-ClosedTop</core:TransportMeansCoded>
                <core:TransportMeansCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeans/core:TransportMeansCodedOther</core:TransportMeansCodedOther>
            </core:TransportMeans>
            <core:TransportMeansIdentifier>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansIdentifier/core:Ident</core:Ident>
            </core:TransportMeansIdentifier>
            <core:TransportMeansReference>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansReference/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansReference/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansReference/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportMeansReference/core:Ident</core:Ident>
            </core:TransportMeansReference>
            <core:TransportRequirementCoded>SpecifiedFlagRequired</core:TransportRequirementCoded>
            <core:TransportRequirementCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportRequirementCodedOther</core:TransportRequirementCodedOther>
            <core:CarrierName>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:CarrierName</core:CarrierName>
            <core:CarrierID>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:CarrierID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:CarrierID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:CarrierID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:CarrierID/core:Ident</core:Ident>
            </core:CarrierID>
            <core:TransportQuantities>
                <core:LadingQuantity>
                    <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1002</core:QuantityValue>
                    <core:UnitOfMeasurement>
                        <core:UOMCoded>1</core:UOMCoded>
                        <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportQuantities/core:LadingQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                    </core:UnitOfMeasurement>
                </core:LadingQuantity>
                <core:ListOfQuantityCoded>
                    <core:QuantityCoded>
                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1003</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>1</core:UOMCoded>
                            <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                        <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                        <core:QuantityQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                    </core:QuantityCoded>
                </core:ListOfQuantityCoded>
            </core:TransportQuantities>
            <core:CustShippingContractNum>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:CustShippingContractNum</core:CustShippingContractNum>
            <core:ServiceLevel>
                <core:ServiceLevelCoded>BulkCommodityTrain</core:ServiceLevelCoded>
                <core:ServiceLevelCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ServiceLevel/core:ServiceLevelCodedOther</core:ServiceLevelCodedOther>
                <core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder</core:ServiceLevelReasonCoded>
                <core:ServiceLevelReasonCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ServiceLevel/core:ServiceLevelReasonCodedOther</core:ServiceLevelReasonCodedOther>
                <core:ServiceLevelResponsibilityCoded>CustomerPlant</core:ServiceLevelResponsibilityCoded>
                <core:ServiceLevelResponsibilityCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ServiceLevel/core:ServiceLevelResponsibilityCodedOther</core:ServiceLevelResponsibilityCodedOther>
            </core:ServiceLevel>
            <core:ShippingInstructions>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ShippingInstructions</core:ShippingInstructions>
            <core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
            <core:TransportLegCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLegCodedOther</core:TransportLegCodedOther>
            <core:ListOfTransportEquipment>
                <core:TransportEquipment>
                    <core:EquipmentProviderCoded>ShipperSupplied</core:EquipmentProviderCoded>
                    <core:EquipmentProviderCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentProviderCodedOther</core:EquipmentProviderCodedOther>
                    <core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased</core:EquipmentOwnerCoded>
                    <core:EquipmentOwnerCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentOwnerCodedOther</core:EquipmentOwnerCodedOther>
                    <core:EquipmentID>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentID</core:EquipmentID>
                    <core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop</core:EquipmentSizeTypeCoded>
                    <core:EquipementSizeTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipementSizeTypeCodedOther</core:EquipementSizeTypeCodedOther>
                    <core:EquipmentStatusCoded>Continental</core:EquipmentStatusCoded>
                    <core:EquipmentStatusCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentStatusCodedOther</core:EquipmentStatusCodedOther>
                    <core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
                    <core:FullIndicatorCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:FullIndicatorCodedOther</core:FullIndicatorCodedOther>
                    <core:Conditions>
                        <core:RefrigerationOn>true</core:RefrigerationOn>
                        <core:Residue>false</core:Residue>
                        <core:ListOfConditions>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1004</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </core:ListOfConditions>
                    </core:Conditions>
                    <core:EquipmentNote>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentNote</core:EquipmentNote>
                    <core:ListOfSealInfo>
                        <core:SealInfo>
                            <core:SealNumber>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealNumber</core:SealNumber>
                            <core:SealIssuer>
                                <core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
                                <core:SealIssuerCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealIssuer/core:SealIssuerCodedOther</core:SealIssuerCodedOther>
                            </core:SealIssuer>
                            <core:SealStatusDescription>
                                <core:SealStatusCoded>Broken</core:SealStatusCoded>
                                <core:SealStatusCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusCodedOther</core:SealStatusCodedOther>
                                <core:SealStatusInfo>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusInfo</core:SealStatusInfo>
                            </core:SealStatusDescription>
                        </core:SealInfo>
                    </core:ListOfSealInfo>
                    <core:ListOfEquipmentMeasurements>
                        <core:Dimension>
                            <core:Measurement>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1005</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Measurement>
                            <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                            <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                        </core:Dimension>
                    </core:ListOfEquipmentMeasurements>
                </core:TransportEquipment>
            </core:ListOfTransportEquipment>
            <core:TransitDirection>
                <core:TransitDirectionCoded>BuyerToSeller</core:TransitDirectionCoded>
                <core:TransitDirectionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransitDirection/core:TransitDirectionCodedOther</core:TransitDirectionCodedOther>
                <core:TransitTimeQualifierCoded>AirHours</core:TransitTimeQualifierCoded>
                <core:TransitTimeQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransitDirection/core:TransitTimeQualifierCodedOther</core:TransitTimeQualifierCodedOther>
                <core:TransitTime>1006</core:TransitTime>
            </core:TransitDirection>
            <core:TransportLocationList>
                <core:StartTransportLocation>
                    <core:Location>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:Location>
                    <core:LocationID>1007</core:LocationID>
                    <core:Sequence>1008</core:Sequence>
                    <core:EstimatedArrivalDate>2003-01-01T00:00:05</core:EstimatedArrivalDate>
                    <core:ActualArrivalDate>2003-01-01T00:00:06</core:ActualArrivalDate>
                    <core:EstimatedDepartureDate>2003-01-01T00:00:07</core:EstimatedDepartureDate>
                    <core:ActualDepartureDate>2003-01-01T00:00:08</core:ActualDepartureDate>
                </core:StartTransportLocation>
                <core:InterimTransportLocation>
                    <core:Location>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:Location>
                    <core:LocationID>1009</core:LocationID>
                    <core:Sequence>1010</core:Sequence>
                    <core:EstimatedArrivalDate>2003-01-01T00:00:09</core:EstimatedArrivalDate>
                    <core:ActualArrivalDate>2003-01-01T00:00:10</core:ActualArrivalDate>
                    <core:EstimatedDepartureDate>2003-01-01T00:00:11</core:EstimatedDepartureDate>
                    <core:ActualDepartureDate>2003-01-01T00:00:12</core:ActualDepartureDate>
                </core:InterimTransportLocation>
                <core:EndTransportLocation>
                    <core:Location>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderTransport/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:Location>
                    <core:LocationID>1011</core:LocationID>
                    <core:Sequence>1012</core:Sequence>
                    <core:EstimatedArrivalDate>2003-01-01T00:00:13</core:EstimatedArrivalDate>
                    <core:ActualArrivalDate>2003-01-01T00:00:14</core:ActualArrivalDate>
                    <core:EstimatedDepartureDate>2003-01-01T00:00:15</core:EstimatedDepartureDate>
                    <core:ActualDepartureDate>2003-01-01T00:00:16</core:ActualDepartureDate>
                </core:EndTransportLocation>
            </core:TransportLocationList>
        </AvailabilityToPromiseHeaderTransport>
        <AvailabilityToPromiseHeaderNote>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseHeaderNote</AvailabilityToPromiseHeaderNote>
        <ListOfStructuredNote>
            <core:StructuredNote>
                <core:GeneralNote>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:GeneralNote</core:GeneralNote>
                <core:NoteID>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:NoteID</core:NoteID>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:Agency/core:AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:NoteURL>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:NoteURL</core:NoteURL>
                <core:TextTypeCoded>Deadlines</core:TextTypeCoded>
                <core:TextTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/ListOfStructuredNote/core:StructuredNote/core:TextTypeCodedOther</core:TextTypeCodedOther>
            </core:StructuredNote>
        </ListOfStructuredNote>
        <AvailabilityToPromiseListOfAttachment>
            <core:Attachment>
                <core:AttachmentActionCoded>Add</core:AttachmentActionCoded>
                <core:AttachmentActionCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:AttachmentActionCodedOther</core:AttachmentActionCodedOther>
                <core:AttachmentPurpose>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:AttachmentPurpose</core:AttachmentPurpose>
                <core:FileName>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:FileName</core:FileName>
                <core:AttachmentTitle>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:AttachmentTitle</core:AttachmentTitle>
                <core:AttachmentDescription>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:AttachmentDescription</core:AttachmentDescription>
                <core:Language>
                    <core:LanguageCoded>aa</core:LanguageCoded>
                    <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:Language/core:LanguageCodedOther</core:LanguageCodedOther>
                    <core:LocaleCoded>ae</core:LocaleCoded>
                    <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:Language/core:LocaleCodedOther</core:LocaleCodedOther>
                </core:Language>
                <core:MIMEType>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:MIMEType</core:MIMEType>
                <core:AttachmentLocation>AvailabilityToPromise/AvailabilityToPromiseHeader/AvailabilityToPromiseListOfAttachment/core:Attachment/core:AttachmentLocation</core:AttachmentLocation>
            </core:Attachment>
        </AvailabilityToPromiseListOfAttachment>
    </AvailabilityToPromiseHeader>
    <AvailabilityToPromiseDetail>
        <ListOfAvailabilityToPromiseItemDetail>
            <AvailabilityToPromiseItemDetail>
                <AvailabilityToPromiseBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1013</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1014</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>ComponentGroup</core:LineItemTypeCoded>
                        <core:LineItemTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/LineItemType/core:LineItemTypeCodedOther</core:LineItemTypeCodedOther>
                    </LineItemType>
                    <ParentItemNumber LineItemNumTypeCoded="Seller">1015</ParentItemNumber>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:PartID</core:PartID>
                                <core:PartIDExt>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:PartIDExt</core:PartIDExt>
                                <core:RevisionNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:RevisionNumber</core:RevisionNumber>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:PartID</core:PartID>
                                <core:PartIDExt>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:PartIDExt</core:PartIDExt>
                                <core:RevisionNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:RevisionNumber</core:RevisionNumber>
                            </core:BuyerPartNumber>
                            <core:ManufacturerPartNumber>
                                <core:PartID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:PartID</core:PartID>
                                <core:PartIDExt>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:PartIDExt</core:PartIDExt>
                                <core:RevisionNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:RevisionNumber</core:RevisionNumber>
                                <core:ManufacturerID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Ident</core:Ident>
                                </core:ManufacturerID>
                                <core:ManufacturerName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerName</core:ManufacturerName>
                            </core:ManufacturerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifierQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifierQualifierCodedOther</core:ProductIdentifierQualifierCodedOther>
                                <core:ProductIdentifier>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifier</core:ProductIdentifier>
                                <core:ProductIdentifierExt>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifierExt</core:ProductIdentifierExt>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifierQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SubstitutePartNumbers/core:ProductIdentifierCoded/core:ProductIdentifierQualifierCodedOther</core:ProductIdentifierQualifierCodedOther>
                                    <core:ProductIdentifier>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SubstitutePartNumbers/core:ProductIdentifierCoded/core:ProductIdentifier</core:ProductIdentifier>
                                    <core:ProductIdentifierExt>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SubstitutePartNumbers/core:ProductIdentifierCoded/core:ProductIdentifierExt</core:ProductIdentifierExt>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                            <core:OtherItemIdentifiers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifierQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:OtherItemIdentifiers/core:ProductIdentifierCoded/core:ProductIdentifierQualifierCodedOther</core:ProductIdentifierQualifierCodedOther>
                                    <core:ProductIdentifier>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:OtherItemIdentifiers/core:ProductIdentifierCoded/core:ProductIdentifier</core:ProductIdentifier>
                                    <core:ProductIdentifierExt>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:OtherItemIdentifiers/core:ProductIdentifierCoded/core:ProductIdentifierExt</core:ProductIdentifierExt>
                                </core:ProductIdentifierCoded>
                            </core:OtherItemIdentifiers>
                        </core:PartNumbers>
                        <core:Service>true</core:Service>
                        <core:ItemDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ItemDescription</core:ItemDescription>
                        <core:ListOfItemCharacteristic>
                            <core:ItemCharacteristic>
                                <core:ItemCharacteristicCoded>AdditionalSectorialCharacteristics</core:ItemCharacteristicCoded>
                                <core:ItemCharacteristicCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ItemCharacteristicCodedOther</core:ItemCharacteristicCodedOther>
                                <core:SurfaceLayerPositionCoded>All</core:SurfaceLayerPositionCoded>
                                <core:SurfaceLayerPositionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:SurfaceLayerPositionCodedOther</core:SurfaceLayerPositionCodedOther>
                                <core:ItemCharacteristicValue>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ItemCharacteristicValue</core:ItemCharacteristicValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                                <core:ListOfDimension>
                                    <core:Dimension>
                                        <core:Measurement>
                                            <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1016</core:MeasurementValue>
                                            <core:UnitOfMeasurement>
                                                <core:UOMCoded>1</core:UOMCoded>
                                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                            </core:UnitOfMeasurement>
                                        </core:Measurement>
                                        <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                        <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                    </core:Dimension>
                                </core:ListOfDimension>
                            </core:ItemCharacteristic>
                        </core:ListOfItemCharacteristic>
                        <core:CommodityCode>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Ident</core:Ident>
                        </core:CommodityCode>
                        <core:Category>
                            <core:CategoryID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:Category/core:CategoryID</core:CategoryID>
                            <core:StandardCategoryID>
                                <core:StandardCategoryType>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:Category/core:StandardCategoryID/core:StandardCategoryType</core:StandardCategoryType>
                                <core:ClassificationID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:Category/core:StandardCategoryID/core:ClassificationID</core:ClassificationID>
                                <core:TechnicalID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ItemIdentifiers/core:Category/core:StandardCategoryID/core:TechnicalID</core:TechnicalID>
                            </core:StandardCategoryID>
                        </core:Category>
                    </ItemIdentifiers>
                    <ListOfDimension>
                        <core:Dimension>
                            <core:Measurement>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1017</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Measurement>
                            <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                            <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                        </core:Dimension>
                    </ListOfDimension>
                    <ATPQuantities>
                        <TotalQuantity>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/TotalQuantity/core:QuantityValue/@SignificanceCodedOther">1018</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/TotalQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                        </TotalQuantity>
                        <MinimumQuantity>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/MinimumQuantity/core:QuantityValue/@SignificanceCodedOther">1019</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/MinimumQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                        </MinimumQuantity>
                        <MaximumQuantity>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/MaximumQuantity/core:QuantityValue/@SignificanceCodedOther">1020</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/MaximumQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                        </MaximumQuantity>
                        <ListOfQuantityCoded>
                            <core:QuantityCoded>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1021</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                                <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                                <core:QuantityQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ATPQuantities/ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                            </core:QuantityCoded>
                        </ListOfQuantityCoded>
                    </ATPQuantities>
                    <BaseItemReferences>
                        <core:OffCatalogFlag>false</core:OffCatalogFlag>
                        <core:CatalogReference>
                            <core:CatalogURL>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:CatalogReference/core:CatalogURL</core:CatalogURL>
                            <core:CatalogID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:CatalogReference/core:CatalogID</core:CatalogID>
                            <core:CatalogItemID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:CatalogReference/core:CatalogItemID</core:CatalogItemID>
                        </core:CatalogReference>
                        <core:ItemContractReferences>
                            <core:Contract>
                                <core:ContractID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:ContractID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:ContractID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:ContractID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:ContractID/core:Ident</core:Ident>
                                </core:ContractID>
                                <core:TypeOfContract>
                                    <core:ContractTypeCoded>DiagnosisRelatedGroup</core:ContractTypeCoded>
                                    <core:ContractTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:TypeOfContract/core:ContractTypeCodedOther</core:ContractTypeCodedOther>
                                </core:TypeOfContract>
                                <core:ValidityDates>
                                    <core:StartDate>2003-01-01T00:00:17</core:StartDate>
                                    <core:EndDate>2003-01-01T00:00:18</core:EndDate>
                                </core:ValidityDates>
                                <core:SystemID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:SystemID</core:SystemID>
                                <core:ContractItemNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ItemContractReferences/core:Contract/core:ContractItemNumber</core:ContractItemNumber>
                            </core:Contract>
                        </core:ItemContractReferences>
                        <core:LineItemRequisitionReference>
                            <core:RequisitionReference>
                                <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:LineItemRequisitionReference/core:RequisitionReference/core:RefNum</core:RefNum>
                                <core:RefDate>2003-01-01T00:00:19</core:RefDate>
                            </core:RequisitionReference>
                            <core:RequisitionLineItemNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:LineItemRequisitionReference/core:RequisitionLineItemNumber</core:RequisitionLineItemNumber>
                        </core:LineItemRequisitionReference>
                        <core:BlanketOrderReference>
                            <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:BlanketOrderReference/core:RefNum</core:RefNum>
                            <core:RefDate>2003-01-01T00:00:20</core:RefDate>
                        </core:BlanketOrderReference>
                        <core:IsItemImmutable>true</core:IsItemImmutable>
                        <core:ListOfCostCenter>
                            <core:CostCenter>
                                <core:CostCenterNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:CostCenterNumber</core:CostCenterNumber>
                                <core:ListOfAccountAssignment>
                                    <core:AccountAssignment>
                                        <core:AccountAssignmentID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:ListOfAccountAssignment/core:AccountAssignment/core:AccountAssignmentID</core:AccountAssignmentID>
                                        <core:AccountAssignmentName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:ListOfAccountAssignment/core:AccountAssignment/core:AccountAssignmentName</core:AccountAssignmentName>
                                        <core:AccountAssignmentValue>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:ListOfAccountAssignment/core:AccountAssignment/core:AccountAssignmentValue</core:AccountAssignmentValue>
                                    </core:AccountAssignment>
                                </core:ListOfAccountAssignment>
                                <core:MonetaryValue>
                                    <core:MonetaryAmount>1022</core:MonetaryAmount>
                                    <core:Currency>
                                        <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                        <core:CurrencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:MonetaryValue/core:Currency/core:CurrencyCodedOther</core:CurrencyCodedOther>
                                    </core:Currency>
                                    <core:RateOfExchangeDetail>
                                        <core:ReferenceCurrency>
                                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                            <core:CurrencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:MonetaryValue/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther</core:CurrencyCodedOther>
                                        </core:ReferenceCurrency>
                                        <core:TargetCurrency>
                                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                            <core:CurrencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:MonetaryValue/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther</core:CurrencyCodedOther>
                                        </core:TargetCurrency>
                                        <core:RateOfExchange>1023</core:RateOfExchange>
                                        <core:InverseRateOfExchange>1024</core:InverseRateOfExchange>
                                        <core:IndicativeIndicator>false</core:IndicativeIndicator>
                                        <core:DateOfRateOfExchange>2003-01-01T00:00:21</core:DateOfRateOfExchange>
                                        <core:ListOfRateOfExchangeReference>
                                            <core:Reference>
                                                <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:MonetaryValue/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum</core:RefNum>
                                                <core:RefDate>2003-01-01T00:00:22</core:RefDate>
                                            </core:Reference>
                                        </core:ListOfRateOfExchangeReference>
                                    </core:RateOfExchangeDetail>
                                </core:MonetaryValue>
                                <core:Quantity>
                                    <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1025</core:QuantityValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:Quantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Quantity>
                                <core:Percent UnitOfMeasurement="P1">1026</core:Percent>
                            </core:CostCenter>
                        </core:ListOfCostCenter>
                        <core:ListOfItemReferences>
                            <core:ReferenceCoded>
                                <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                                <core:ReferenceTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfItemReferences/core:ReferenceCoded/core:ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                                <core:PrimaryReference>
                                    <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfItemReferences/core:ReferenceCoded/core:PrimaryReference/core:RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:23</core:RefDate>
                                </core:PrimaryReference>
                                <core:SupportingReference>
                                    <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfItemReferences/core:ReferenceCoded/core:SupportingReference/core:RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:24</core:RefDate>
                                </core:SupportingReference>
                                <core:SupportingSubReference>
                                    <core:RefNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfItemReferences/core:ReferenceCoded/core:SupportingSubReference/core:RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:25</core:RefDate>
                                </core:SupportingSubReference>
                                <core:ReferenceDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/BaseItemReferences/core:ListOfItemReferences/core:ReferenceCoded/core:ReferenceDescription</core:ReferenceDescription>
                            </core:ReferenceCoded>
                        </core:ListOfItemReferences>
                    </BaseItemReferences>
                    <FinalRecipient>
                        <core:PartyID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyID/core:Ident</core:Ident>
                        </core:PartyID>
                        <core:ListOfIdentifier>
                            <core:Identifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                            </core:Identifier>
                        </core:ListOfIdentifier>
                        <core:MDFBusiness>true</core:MDFBusiness>
                        <core:NameAddress>
                            <core:ExternalAddressID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                            <core:Name1>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Name1</core:Name1>
                            <core:Name2>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Name2</core:Name2>
                            <core:Name3>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Name3</core:Name3>
                            <core:Identifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                            </core:Identifier>
                            <core:POBox POBoxPostalCode="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:POBox/@POBoxPostalCode">AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:POBox</core:POBox>
                            <core:Street>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Street</core:Street>
                            <core:HouseNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:HouseNumber</core:HouseNumber>
                            <core:StreetSupplement1>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                            <core:StreetSupplement2>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                            <core:Building>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Building</core:Building>
                            <core:Floor>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Floor</core:Floor>
                            <core:RoomNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:RoomNumber</core:RoomNumber>
                            <core:InhouseMail>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:InhouseMail</core:InhouseMail>
                            <core:Department>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Department</core:Department>
                            <core:PostalCode>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:PostalCode</core:PostalCode>
                            <core:City>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:City</core:City>
                            <core:County>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:County</core:County>
                            <core:Region>
                                <core:RegionCoded>ARA</core:RegionCoded>
                                <core:RegionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                            </core:Region>
                            <core:District>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:District</core:District>
                            <core:Country>
                                <core:CountryCoded>AE</core:CountryCoded>
                                <core:CountryCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                            </core:Country>
                            <core:Timezone>
                                <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                <core:TimezoneCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                            </core:Timezone>
                        </core:NameAddress>
                        <core:PrimaryContact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:PrimaryContact>
                        <core:OtherContacts>
                            <core:Contact>
                                <core:ContactID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                                </core:ContactID>
                                <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                                <core:ContactFunction>
                                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                    <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                </core:ContactFunction>
                                <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                                <core:ListOfContactNumber>
                                    <core:ContactNumber>
                                        <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                        <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                    </core:ContactNumber>
                                </core:ListOfContactNumber>
                                <core:CorrespondenceLanguage>
                                    <core:LanguageCoded>aa</core:LanguageCoded>
                                    <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                    <core:LocaleCoded>ae</core:LocaleCoded>
                                    <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                                </core:CorrespondenceLanguage>
                            </core:Contact>
                        </core:OtherContacts>
                        <core:PartyTaxInformation>
                            <core:TaxIdentifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                            </core:TaxIdentifier>
                            <core:RegisteredName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                            <core:RegisteredOffice>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                            <core:TaxLocation>
                                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                <core:LocationIdentifier>
                                    <core:LocID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                    </core:LocID>
                                    <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                </core:LocationIdentifier>
                                <core:GPSCoordinates>
                                    <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                    <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                    <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                                </core:GPSCoordinates>
                            </core:TaxLocation>
                            <core:CompanyRegistrationNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/FinalRecipient/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                        </core:PartyTaxInformation>
                    </FinalRecipient>
                    <ListOfPartyCoded>
                        <core:PartyCoded>
                            <core:PartyID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Ident</core:Ident>
                            </core:PartyID>
                            <core:ListOfIdentifier>
                                <core:Identifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                                </core:Identifier>
                            </core:ListOfIdentifier>
                            <core:MDFBusiness>false</core:MDFBusiness>
                            <core:NameAddress>
                                <core:ExternalAddressID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                                <core:Name1>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name1</core:Name1>
                                <core:Name2>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name2</core:Name2>
                                <core:Name3>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name3</core:Name3>
                                <core:Identifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                                </core:Identifier>
                                <core:POBox POBoxPostalCode="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox</core:POBox>
                                <core:Street>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Street</core:Street>
                                <core:HouseNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:HouseNumber</core:HouseNumber>
                                <core:StreetSupplement1>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                                <core:StreetSupplement2>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                                <core:Building>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Building</core:Building>
                                <core:Floor>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Floor</core:Floor>
                                <core:RoomNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:RoomNumber</core:RoomNumber>
                                <core:InhouseMail>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:InhouseMail</core:InhouseMail>
                                <core:Department>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Department</core:Department>
                                <core:PostalCode>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:PostalCode</core:PostalCode>
                                <core:City>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:City</core:City>
                                <core:County>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:County</core:County>
                                <core:Region>
                                    <core:RegionCoded>ARA</core:RegionCoded>
                                    <core:RegionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                                </core:Region>
                                <core:District>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:District</core:District>
                                <core:Country>
                                    <core:CountryCoded>AE</core:CountryCoded>
                                    <core:CountryCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                                </core:Country>
                                <core:Timezone>
                                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                    <core:TimezoneCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                                </core:Timezone>
                            </core:NameAddress>
                            <core:PrimaryContact>
                                <core:ContactID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                                </core:ContactID>
                                <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactName</core:ContactName>
                                <core:ContactFunction>
                                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                    <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                </core:ContactFunction>
                                <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                                <core:ListOfContactNumber>
                                    <core:ContactNumber>
                                        <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                        <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                    </core:ContactNumber>
                                </core:ListOfContactNumber>
                                <core:CorrespondenceLanguage>
                                    <core:LanguageCoded>aa</core:LanguageCoded>
                                    <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                    <core:LocaleCoded>ae</core:LocaleCoded>
                                    <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                                </core:CorrespondenceLanguage>
                            </core:PrimaryContact>
                            <core:OtherContacts>
                                <core:Contact>
                                    <core:ContactID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                                    </core:ContactID>
                                    <core:ContactName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                                    <core:ContactFunction>
                                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                        <core:ContactFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                    </core:ContactFunction>
                                    <core:ContactDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                                    <core:ListOfContactNumber>
                                        <core:ContactNumber>
                                            <core:ContactNumberValue>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                            <core:ContactNumberTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                        </core:ContactNumber>
                                    </core:ListOfContactNumber>
                                    <core:CorrespondenceLanguage>
                                        <core:LanguageCoded>aa</core:LanguageCoded>
                                        <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                        <core:LocaleCoded>ae</core:LocaleCoded>
                                        <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                                    </core:CorrespondenceLanguage>
                                </core:Contact>
                            </core:OtherContacts>
                            <core:PartyTaxInformation>
                                <core:TaxIdentifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                                </core:TaxIdentifier>
                                <core:RegisteredName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                                <core:RegisteredOffice>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                                <core:TaxLocation>
                                    <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                    <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                    <core:LocationIdentifier>
                                        <core:LocID>
                                            <core:Agency>
                                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                            </core:Agency>
                                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                        </core:LocID>
                                        <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                    </core:LocationIdentifier>
                                    <core:GPSCoordinates>
                                        <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                        <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                        <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                                    </core:GPSCoordinates>
                                </core:TaxLocation>
                                <core:CompanyRegistrationNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                            </core:PartyTaxInformation>
                            <core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
                            <core:PartyRoleCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyRoleCodedOther</core:PartyRoleCodedOther>
                        </core:PartyCoded>
                    </ListOfPartyCoded>
                </AvailabilityToPromiseBaseItemDetail>
                <AvailabilityToPromiseDeliveryDetail>
                    <core:ShipToLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipToLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:ShipToLocation>
                    <core:ShipFromLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ShipFromLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:ShipFromLocation>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:ScheduleLineID>1027</core:ScheduleLineID>
                            <core:Quantity>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1028</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:Quantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2003-01-01T00:00:26</core:RequestedDeliveryDate>
                            <core:ScheduleLineNote>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ScheduleLineNote</core:ScheduleLineNote>
                            <core:TransportRouting>
                                <core:TransportRouteID>1029</core:TransportRouteID>
                                <core:TransportMode>
                                    <core:TransportModeCoded>Air</core:TransportModeCoded>
                                    <core:TransportModeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMode/core:TransportModeCodedOther</core:TransportModeCodedOther>
                                </core:TransportMode>
                                <core:TransportMeans>
                                    <core:TransportMeansCoded>20FtILContainer-ClosedTop</core:TransportMeansCoded>
                                    <core:TransportMeansCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeans/core:TransportMeansCodedOther</core:TransportMeansCodedOther>
                                </core:TransportMeans>
                                <core:TransportMeansIdentifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Ident</core:Ident>
                                </core:TransportMeansIdentifier>
                                <core:TransportMeansReference>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Ident</core:Ident>
                                </core:TransportMeansReference>
                                <core:TransportRequirementCoded>SpecifiedFlagRequired</core:TransportRequirementCoded>
                                <core:TransportRequirementCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportRequirementCodedOther</core:TransportRequirementCodedOther>
                                <core:CarrierName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierName</core:CarrierName>
                                <core:CarrierID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Ident</core:Ident>
                                </core:CarrierID>
                                <core:TransportQuantities>
                                    <core:LadingQuantity>
                                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1030</core:QuantityValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:LadingQuantity>
                                    <core:ListOfQuantityCoded>
                                        <core:QuantityCoded>
                                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1031</core:QuantityValue>
                                            <core:UnitOfMeasurement>
                                                <core:UOMCoded>1</core:UOMCoded>
                                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                            </core:UnitOfMeasurement>
                                            <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                                            <core:QuantityQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                                        </core:QuantityCoded>
                                    </core:ListOfQuantityCoded>
                                </core:TransportQuantities>
                                <core:CustShippingContractNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CustShippingContractNum</core:CustShippingContractNum>
                                <core:ServiceLevel>
                                    <core:ServiceLevelCoded>BulkCommodityTrain</core:ServiceLevelCoded>
                                    <core:ServiceLevelCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ServiceLevel/core:ServiceLevelCodedOther</core:ServiceLevelCodedOther>
                                    <core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder</core:ServiceLevelReasonCoded>
                                    <core:ServiceLevelReasonCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ServiceLevel/core:ServiceLevelReasonCodedOther</core:ServiceLevelReasonCodedOther>
                                    <core:ServiceLevelResponsibilityCoded>CustomerPlant</core:ServiceLevelResponsibilityCoded>
                                    <core:ServiceLevelResponsibilityCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ServiceLevel/core:ServiceLevelResponsibilityCodedOther</core:ServiceLevelResponsibilityCodedOther>
                                </core:ServiceLevel>
                                <core:ShippingInstructions>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ShippingInstructions</core:ShippingInstructions>
                                <core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
                                <core:TransportLegCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLegCodedOther</core:TransportLegCodedOther>
                                <core:ListOfTransportEquipment>
                                    <core:TransportEquipment>
                                        <core:EquipmentProviderCoded>ShipperSupplied</core:EquipmentProviderCoded>
                                        <core:EquipmentProviderCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentProviderCodedOther</core:EquipmentProviderCodedOther>
                                        <core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased</core:EquipmentOwnerCoded>
                                        <core:EquipmentOwnerCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentOwnerCodedOther</core:EquipmentOwnerCodedOther>
                                        <core:EquipmentID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentID</core:EquipmentID>
                                        <core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop</core:EquipmentSizeTypeCoded>
                                        <core:EquipementSizeTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipementSizeTypeCodedOther</core:EquipementSizeTypeCodedOther>
                                        <core:EquipmentStatusCoded>Continental</core:EquipmentStatusCoded>
                                        <core:EquipmentStatusCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentStatusCodedOther</core:EquipmentStatusCodedOther>
                                        <core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
                                        <core:FullIndicatorCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:FullIndicatorCodedOther</core:FullIndicatorCodedOther>
                                        <core:Conditions>
                                            <core:RefrigerationOn>true</core:RefrigerationOn>
                                            <core:Residue>false</core:Residue>
                                            <core:ListOfConditions>
                                                <core:Dimension>
                                                    <core:Measurement>
                                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1032</core:MeasurementValue>
                                                        <core:UnitOfMeasurement>
                                                            <core:UOMCoded>1</core:UOMCoded>
                                                            <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                                        </core:UnitOfMeasurement>
                                                    </core:Measurement>
                                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                                    <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                                </core:Dimension>
                                            </core:ListOfConditions>
                                        </core:Conditions>
                                        <core:EquipmentNote>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentNote</core:EquipmentNote>
                                        <core:ListOfSealInfo>
                                            <core:SealInfo>
                                                <core:SealNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealNumber</core:SealNumber>
                                                <core:SealIssuer>
                                                    <core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
                                                    <core:SealIssuerCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealIssuer/core:SealIssuerCodedOther</core:SealIssuerCodedOther>
                                                </core:SealIssuer>
                                                <core:SealStatusDescription>
                                                    <core:SealStatusCoded>Broken</core:SealStatusCoded>
                                                    <core:SealStatusCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusCodedOther</core:SealStatusCodedOther>
                                                    <core:SealStatusInfo>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusInfo</core:SealStatusInfo>
                                                </core:SealStatusDescription>
                                            </core:SealInfo>
                                        </core:ListOfSealInfo>
                                        <core:ListOfEquipmentMeasurements>
                                            <core:Dimension>
                                                <core:Measurement>
                                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1033</core:MeasurementValue>
                                                    <core:UnitOfMeasurement>
                                                        <core:UOMCoded>1</core:UOMCoded>
                                                        <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                                    </core:UnitOfMeasurement>
                                                </core:Measurement>
                                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                                <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                            </core:Dimension>
                                        </core:ListOfEquipmentMeasurements>
                                    </core:TransportEquipment>
                                </core:ListOfTransportEquipment>
                                <core:TransitDirection>
                                    <core:TransitDirectionCoded>BuyerToSeller</core:TransitDirectionCoded>
                                    <core:TransitDirectionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransitDirection/core:TransitDirectionCodedOther</core:TransitDirectionCodedOther>
                                    <core:TransitTimeQualifierCoded>AirHours</core:TransitTimeQualifierCoded>
                                    <core:TransitTimeQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransitDirection/core:TransitTimeQualifierCodedOther</core:TransitTimeQualifierCodedOther>
                                    <core:TransitTime>1034</core:TransitTime>
                                </core:TransitDirection>
                                <core:TransportLocationList>
                                    <core:StartTransportLocation>
                                        <core:Location>
                                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                            <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                            <core:LocationIdentifier>
                                                <core:LocID>
                                                    <core:Agency>
                                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                    </core:Agency>
                                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                                </core:LocID>
                                                <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                            </core:LocationIdentifier>
                                            <core:GPSCoordinates>
                                                <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                                <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                                <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                                            </core:GPSCoordinates>
                                        </core:Location>
                                        <core:LocationID>1035</core:LocationID>
                                        <core:Sequence>1036</core:Sequence>
                                        <core:EstimatedArrivalDate>2003-01-01T00:00:27</core:EstimatedArrivalDate>
                                        <core:ActualArrivalDate>2003-01-01T00:00:28</core:ActualArrivalDate>
                                        <core:EstimatedDepartureDate>2003-01-01T00:00:29</core:EstimatedDepartureDate>
                                        <core:ActualDepartureDate>2003-01-01T00:00:30</core:ActualDepartureDate>
                                    </core:StartTransportLocation>
                                    <core:InterimTransportLocation>
                                        <core:Location>
                                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                            <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                            <core:LocationIdentifier>
                                                <core:LocID>
                                                    <core:Agency>
                                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                    </core:Agency>
                                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                                </core:LocID>
                                                <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                            </core:LocationIdentifier>
                                            <core:GPSCoordinates>
                                                <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                                <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                                <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                                            </core:GPSCoordinates>
                                        </core:Location>
                                        <core:LocationID>1037</core:LocationID>
                                        <core:Sequence>1038</core:Sequence>
                                        <core:EstimatedArrivalDate>2003-01-01T00:00:31</core:EstimatedArrivalDate>
                                        <core:ActualArrivalDate>2003-01-01T00:00:32</core:ActualArrivalDate>
                                        <core:EstimatedDepartureDate>2003-01-01T00:00:33</core:EstimatedDepartureDate>
                                        <core:ActualDepartureDate>2003-01-01T00:00:34</core:ActualDepartureDate>
                                    </core:InterimTransportLocation>
                                    <core:EndTransportLocation>
                                        <core:Location>
                                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                            <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                            <core:LocationIdentifier>
                                                <core:LocID>
                                                    <core:Agency>
                                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                    </core:Agency>
                                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                                </core:LocID>
                                                <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                            </core:LocationIdentifier>
                                            <core:GPSCoordinates>
                                                <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                                <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                                <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                                            </core:GPSCoordinates>
                                        </core:Location>
                                        <core:LocationID>1039</core:LocationID>
                                        <core:Sequence>1040</core:Sequence>
                                        <core:EstimatedArrivalDate>2003-01-01T00:00:35</core:EstimatedArrivalDate>
                                        <core:ActualArrivalDate>2003-01-01T00:00:36</core:ActualArrivalDate>
                                        <core:EstimatedDepartureDate>2003-01-01T00:00:37</core:EstimatedDepartureDate>
                                        <core:ActualDepartureDate>2003-01-01T00:00:38</core:ActualDepartureDate>
                                    </core:EndTransportLocation>
                                </core:TransportLocationList>
                            </core:TransportRouting>
                            <core:ListOfShipToSubInformation>
                                <core:ShipToSubInformation>
                                    <core:ShipToSubLocation>
                                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                        <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                        <core:LocationIdentifier>
                                            <core:LocID>
                                                <core:Agency>
                                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                    <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                                    <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                    <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                </core:Agency>
                                                <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                            </core:LocID>
                                            <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                        </core:LocationIdentifier>
                                        <core:GPSCoordinates>
                                            <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                            <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                            <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                                        </core:GPSCoordinates>
                                    </core:ShipToSubLocation>
                                    <core:ShipToSubQuantity>
                                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubQuantity/core:QuantityValue/@SignificanceCodedOther">1041</core:QuantityValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:ShipToSubQuantity>
                                </core:ShipToSubInformation>
                            </core:ListOfShipToSubInformation>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                    <core:SimplePackageNote>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:SimplePackageNote</core:SimplePackageNote>
                    <core:TermsOfDelivery>
                        <core:TermsOfDeliveryFunctionCoded>PriceCondition</core:TermsOfDeliveryFunctionCoded>
                        <core:TermsOfDeliveryFunctionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:TermsOfDeliveryFunctionCodedOther</core:TermsOfDeliveryFunctionCodedOther>
                        <core:TransportTermsCoded>Ex-Works</core:TransportTermsCoded>
                        <core:TransportTermsCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:TransportTermsCodedOther</core:TransportTermsCodedOther>
                        <core:ShipmentMethodOfPaymentCoded>Account</core:ShipmentMethodOfPaymentCoded>
                        <core:ShipmentMethodOfPaymentCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:ShipmentMethodOfPaymentCodedOther</core:ShipmentMethodOfPaymentCodedOther>
                        <core:Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:Location>
                        <core:TermsOfDeliveryDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:TermsOfDeliveryDescription</core:TermsOfDeliveryDescription>
                        <core:TransportDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:TransportDescription</core:TransportDescription>
                        <core:RiskOfLossCoded>RiskOfLossInTransitToDestinationIsBuyers</core:RiskOfLossCoded>
                        <core:RiskOfLossCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:RiskOfLossCodedOther</core:RiskOfLossCodedOther>
                        <core:RiskOfLossDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:TermsOfDelivery/core:RiskOfLossDescription</core:RiskOfLossDescription>
                    </core:TermsOfDelivery>
                    <core:CargoClassification>
                        <core:NatureOfGoods>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Ident</core:Ident>
                        </core:NatureOfGoods>
                        <core:OperationalTypeCoded>Documents</core:OperationalTypeCoded>
                        <core:OperationalTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:CargoClassification/core:OperationalTypeCodedOther</core:OperationalTypeCodedOther>
                        <core:TypeOfCargo>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseDeliveryDetail/core:CargoClassification/core:TypeOfCargo</core:TypeOfCargo>
                    </core:CargoClassification>
                </AvailabilityToPromiseDeliveryDetail>
                <AvailabilityToPromiseTransportDetail>
                    <core:TransportRouteID>1042</core:TransportRouteID>
                    <core:TransportMode>
                        <core:TransportModeCoded>Air</core:TransportModeCoded>
                        <core:TransportModeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMode/core:TransportModeCodedOther</core:TransportModeCodedOther>
                    </core:TransportMode>
                    <core:TransportMeans>
                        <core:TransportMeansCoded>20FtILContainer-ClosedTop</core:TransportMeansCoded>
                        <core:TransportMeansCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeans/core:TransportMeansCodedOther</core:TransportMeansCodedOther>
                    </core:TransportMeans>
                    <core:TransportMeansIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansIdentifier/core:Ident</core:Ident>
                    </core:TransportMeansIdentifier>
                    <core:TransportMeansReference>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansReference/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansReference/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansReference/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportMeansReference/core:Ident</core:Ident>
                    </core:TransportMeansReference>
                    <core:TransportRequirementCoded>SpecifiedFlagRequired</core:TransportRequirementCoded>
                    <core:TransportRequirementCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportRequirementCodedOther</core:TransportRequirementCodedOther>
                    <core:CarrierName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:CarrierName</core:CarrierName>
                    <core:CarrierID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:CarrierID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:CarrierID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:CarrierID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:CarrierID/core:Ident</core:Ident>
                    </core:CarrierID>
                    <core:TransportQuantities>
                        <core:LadingQuantity>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1043</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportQuantities/core:LadingQuantity/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                        </core:LadingQuantity>
                        <core:ListOfQuantityCoded>
                            <core:QuantityCoded>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1044</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                                <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                                <core:QuantityQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                            </core:QuantityCoded>
                        </core:ListOfQuantityCoded>
                    </core:TransportQuantities>
                    <core:CustShippingContractNum>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:CustShippingContractNum</core:CustShippingContractNum>
                    <core:ServiceLevel>
                        <core:ServiceLevelCoded>BulkCommodityTrain</core:ServiceLevelCoded>
                        <core:ServiceLevelCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ServiceLevel/core:ServiceLevelCodedOther</core:ServiceLevelCodedOther>
                        <core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder</core:ServiceLevelReasonCoded>
                        <core:ServiceLevelReasonCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ServiceLevel/core:ServiceLevelReasonCodedOther</core:ServiceLevelReasonCodedOther>
                        <core:ServiceLevelResponsibilityCoded>CustomerPlant</core:ServiceLevelResponsibilityCoded>
                        <core:ServiceLevelResponsibilityCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ServiceLevel/core:ServiceLevelResponsibilityCodedOther</core:ServiceLevelResponsibilityCodedOther>
                    </core:ServiceLevel>
                    <core:ShippingInstructions>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ShippingInstructions</core:ShippingInstructions>
                    <core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
                    <core:TransportLegCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLegCodedOther</core:TransportLegCodedOther>
                    <core:ListOfTransportEquipment>
                        <core:TransportEquipment>
                            <core:EquipmentProviderCoded>ShipperSupplied</core:EquipmentProviderCoded>
                            <core:EquipmentProviderCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentProviderCodedOther</core:EquipmentProviderCodedOther>
                            <core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased</core:EquipmentOwnerCoded>
                            <core:EquipmentOwnerCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentOwnerCodedOther</core:EquipmentOwnerCodedOther>
                            <core:EquipmentID>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentID</core:EquipmentID>
                            <core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop</core:EquipmentSizeTypeCoded>
                            <core:EquipementSizeTypeCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipementSizeTypeCodedOther</core:EquipementSizeTypeCodedOther>
                            <core:EquipmentStatusCoded>Continental</core:EquipmentStatusCoded>
                            <core:EquipmentStatusCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentStatusCodedOther</core:EquipmentStatusCodedOther>
                            <core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
                            <core:FullIndicatorCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:FullIndicatorCodedOther</core:FullIndicatorCodedOther>
                            <core:Conditions>
                                <core:RefrigerationOn>true</core:RefrigerationOn>
                                <core:Residue>false</core:Residue>
                                <core:ListOfConditions>
                                    <core:Dimension>
                                        <core:Measurement>
                                            <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1045</core:MeasurementValue>
                                            <core:UnitOfMeasurement>
                                                <core:UOMCoded>1</core:UOMCoded>
                                                <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                            </core:UnitOfMeasurement>
                                        </core:Measurement>
                                        <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                        <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                    </core:Dimension>
                                </core:ListOfConditions>
                            </core:Conditions>
                            <core:EquipmentNote>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentNote</core:EquipmentNote>
                            <core:ListOfSealInfo>
                                <core:SealInfo>
                                    <core:SealNumber>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealNumber</core:SealNumber>
                                    <core:SealIssuer>
                                        <core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
                                        <core:SealIssuerCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealIssuer/core:SealIssuerCodedOther</core:SealIssuerCodedOther>
                                    </core:SealIssuer>
                                    <core:SealStatusDescription>
                                        <core:SealStatusCoded>Broken</core:SealStatusCoded>
                                        <core:SealStatusCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusCodedOther</core:SealStatusCodedOther>
                                        <core:SealStatusInfo>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusInfo</core:SealStatusInfo>
                                    </core:SealStatusDescription>
                                </core:SealInfo>
                            </core:ListOfSealInfo>
                            <core:ListOfEquipmentMeasurements>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1046</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </core:ListOfEquipmentMeasurements>
                        </core:TransportEquipment>
                    </core:ListOfTransportEquipment>
                    <core:TransitDirection>
                        <core:TransitDirectionCoded>BuyerToSeller</core:TransitDirectionCoded>
                        <core:TransitDirectionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransitDirection/core:TransitDirectionCodedOther</core:TransitDirectionCodedOther>
                        <core:TransitTimeQualifierCoded>AirHours</core:TransitTimeQualifierCoded>
                        <core:TransitTimeQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransitDirection/core:TransitTimeQualifierCodedOther</core:TransitTimeQualifierCodedOther>
                        <core:TransitTime>1047</core:TransitTime>
                    </core:TransitDirection>
                    <core:TransportLocationList>
                        <core:StartTransportLocation>
                            <core:Location>
                                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                <core:LocationIdentifier>
                                    <core:LocID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                    </core:LocID>
                                    <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                </core:LocationIdentifier>
                                <core:GPSCoordinates>
                                    <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                    <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                    <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                                </core:GPSCoordinates>
                            </core:Location>
                            <core:LocationID>1048</core:LocationID>
                            <core:Sequence>1049</core:Sequence>
                            <core:EstimatedArrivalDate>2003-01-01T00:00:39</core:EstimatedArrivalDate>
                            <core:ActualArrivalDate>2003-01-01T00:00:40</core:ActualArrivalDate>
                            <core:EstimatedDepartureDate>2003-01-01T00:00:41</core:EstimatedDepartureDate>
                            <core:ActualDepartureDate>2003-01-01T00:00:42</core:ActualDepartureDate>
                        </core:StartTransportLocation>
                        <core:InterimTransportLocation>
                            <core:Location>
                                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                <core:LocationIdentifier>
                                    <core:LocID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                    </core:LocID>
                                    <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                </core:LocationIdentifier>
                                <core:GPSCoordinates>
                                    <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                    <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                    <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                                </core:GPSCoordinates>
                            </core:Location>
                            <core:LocationID>1050</core:LocationID>
                            <core:Sequence>1051</core:Sequence>
                            <core:EstimatedArrivalDate>2003-01-01T00:00:43</core:EstimatedArrivalDate>
                            <core:ActualArrivalDate>2003-01-01T00:00:44</core:ActualArrivalDate>
                            <core:EstimatedDepartureDate>2003-01-01T00:00:45</core:EstimatedDepartureDate>
                            <core:ActualDepartureDate>2003-01-01T00:00:46</core:ActualDepartureDate>
                        </core:InterimTransportLocation>
                        <core:EndTransportLocation>
                            <core:Location>
                                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                <core:LocationQualifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                <core:LocationIdentifier>
                                    <core:LocID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                    </core:LocID>
                                    <core:LocationDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                </core:LocationIdentifier>
                                <core:GPSCoordinates>
                                    <core:GPSSystem>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                    <core:Latitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                    <core:Longitude>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseTransportDetail/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                                </core:GPSCoordinates>
                            </core:Location>
                            <core:LocationID>1052</core:LocationID>
                            <core:Sequence>1053</core:Sequence>
                            <core:EstimatedArrivalDate>2003-01-01T00:00:47</core:EstimatedArrivalDate>
                            <core:ActualArrivalDate>2003-01-01T00:00:48</core:ActualArrivalDate>
                            <core:EstimatedDepartureDate>2003-01-01T00:00:49</core:EstimatedDepartureDate>
                            <core:ActualDepartureDate>2003-01-01T00:00:50</core:ActualDepartureDate>
                        </core:EndTransportLocation>
                    </core:TransportLocationList>
                </AvailabilityToPromiseTransportDetail>
                <AvailabilityToPromiseItemListOfAttachment>
                    <core:Attachment>
                        <core:AttachmentActionCoded>Add</core:AttachmentActionCoded>
                        <core:AttachmentActionCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:AttachmentActionCodedOther</core:AttachmentActionCodedOther>
                        <core:AttachmentPurpose>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:AttachmentPurpose</core:AttachmentPurpose>
                        <core:FileName>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:FileName</core:FileName>
                        <core:AttachmentTitle>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:AttachmentTitle</core:AttachmentTitle>
                        <core:AttachmentDescription>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:AttachmentDescription</core:AttachmentDescription>
                        <core:Language>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:Language/core:LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:Language/core:LocaleCodedOther</core:LocaleCodedOther>
                        </core:Language>
                        <core:MIMEType>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:MIMEType</core:MIMEType>
                        <core:AttachmentLocation>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/AvailabilityToPromiseItemListOfAttachment/core:Attachment/core:AttachmentLocation</core:AttachmentLocation>
                    </core:Attachment>
                </AvailabilityToPromiseItemListOfAttachment>
                <LineItemNote>AvailabilityToPromise/AvailabilityToPromiseDetail/ListOfAvailabilityToPromiseItemDetail/AvailabilityToPromiseItemDetail/LineItemNote</LineItemNote>
            </AvailabilityToPromiseItemDetail>
        </ListOfAvailabilityToPromiseItemDetail>
    </AvailabilityToPromiseDetail>
</AvailabilityToPromise>