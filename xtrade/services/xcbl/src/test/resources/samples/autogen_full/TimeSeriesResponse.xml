<TimeSeriesResponse xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/statisticsandforecasting/v1_0/statisticsandforecasting.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/statisticsandforecasting/v1_0/statisticsandforecasting.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/statisticsandforecasting/v1_0/statisticsandforecasting.xsd">
    <TimeSeriesResponseHeader>
        <TimeSeriesResponseIssueDate>2003-01-01T00:00:01</TimeSeriesResponseIssueDate>
        <TimeSeriesResponseID>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseID</TimeSeriesResponseID>
        <TimeSeriesReference>
            <core:RefNum>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesReference/core:RefNum</core:RefNum>
            <core:RefDate>2003-01-01T00:00:02</core:RefDate>
        </TimeSeriesReference>
        <TimeSeriesPlanningData>
            <SourceID>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesPlanningData/SourceID</SourceID>
            <TargetID>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesPlanningData/TargetID</TargetID>
        </TimeSeriesPlanningData>
        <TimeSeriesResponseParty>
            <SourceParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyID/core:Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>false</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Name1</core:Name1>
                    <core:Name2>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Name2</core:Name2>
                    <core:Name3>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:POBox/@POBoxPostalCode">TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Street</core:Street>
                    <core:HouseNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                    <core:Building>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Building</core:Building>
                    <core:Floor>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Floor</core:Floor>
                    <core:RoomNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                    <core:InhouseMail>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                    <core:Department>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Department</core:Department>
                    <core:PostalCode>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:PostalCode</core:PostalCode>
                    <core:City>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:City</core:City>
                    <core:County>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/SourceParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </SourceParty>
            <ReceiverParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyID/core:Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>true</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Name1</core:Name1>
                    <core:Name2>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Name2</core:Name2>
                    <core:Name3>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:POBox/@POBoxPostalCode">TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Street</core:Street>
                    <core:HouseNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                    <core:Building>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Building</core:Building>
                    <core:Floor>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Floor</core:Floor>
                    <core:RoomNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                    <core:InhouseMail>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                    <core:Department>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Department</core:Department>
                    <core:PostalCode>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:PostalCode</core:PostalCode>
                    <core:City>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:City</core:City>
                    <core:County>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                            <core:Latitude>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                            <core:Longitude>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ReceiverParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </ReceiverParty>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Ident</core:Ident>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:MDFBusiness>false</core:MDFBusiness>
                    <core:NameAddress>
                        <core:ExternalAddressID>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                        <core:Name1>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name1</core:Name1>
                        <core:Name2>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name2</core:Name2>
                        <core:Name3>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name3</core:Name3>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                        </core:Identifier>
                        <core:POBox POBoxPostalCode="TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox</core:POBox>
                        <core:Street>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Street</core:Street>
                        <core:HouseNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:HouseNumber</core:HouseNumber>
                        <core:StreetSupplement1>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                        <core:StreetSupplement2>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                        <core:Building>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Building</core:Building>
                        <core:Floor>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Floor</core:Floor>
                        <core:RoomNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:RoomNumber</core:RoomNumber>
                        <core:InhouseMail>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:InhouseMail</core:InhouseMail>
                        <core:Department>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Department</core:Department>
                        <core:PostalCode>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:PostalCode</core:PostalCode>
                        <core:City>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:City</core:City>
                        <core:County>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:County</core:County>
                        <core:Region>
                            <core:RegionCoded>ARA</core:RegionCoded>
                            <core:RegionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                        </core:Region>
                        <core:District>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:District</core:District>
                        <core:Country>
                            <core:CountryCoded>AE</core:CountryCoded>
                            <core:CountryCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                        </core:Country>
                        <core:Timezone>
                            <core:TimezoneCoded>12.00</core:TimezoneCoded>
                            <core:TimezoneCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                        </core:Timezone>
                    </core:NameAddress>
                    <core:PrimaryContact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:PrimaryContact>
                    <core:OtherContacts>
                        <core:Contact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:Contact>
                    </core:OtherContacts>
                    <core:PartyTaxInformation>
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                        </core:TaxIdentifier>
                        <core:RegisteredName>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                        <core:RegisteredOffice>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                        <core:TaxLocation>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                <core:Latitude>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                <core:Longitude>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:TaxLocation>
                        <core:CompanyRegistrationNumber>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                    </core:PartyTaxInformation>
                    <core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
                    <core:PartyRoleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseParty/ListOfPartyCoded/core:PartyCoded/core:PartyRoleCodedOther</core:PartyRoleCodedOther>
                </core:PartyCoded>
            </ListOfPartyCoded>
        </TimeSeriesResponseParty>
        <TimeSeriesHeaderResponseCoded>Reject</TimeSeriesHeaderResponseCoded>
        <TimeSeriesHeaderResponseCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesHeaderResponseCodedOther</TimeSeriesHeaderResponseCodedOther>
        <ChangedTimeSeriesHeader>
            <TimeSeriesIssueDate>2003-01-01T00:00:03</TimeSeriesIssueDate>
            <TimeSeriesID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesID</TimeSeriesID>
            <TimeSeriesPurposeCoded>Delete</TimeSeriesPurposeCoded>
            <TimeSeriesPurposeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesPurposeCodedOther</TimeSeriesPurposeCodedOther>
            <TimeSeriesTimePeriod>
                <core:StartDate>2003-01-01T00:00:04</core:StartDate>
                <core:EndDate>2003-01-01T00:00:05</core:EndDate>
            </TimeSeriesTimePeriod>
            <TimeSeriesSystemType>
                <SourceID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesSystemType/SourceID</SourceID>
                <TargetID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesSystemType/TargetID</TargetID>
            </TimeSeriesSystemType>
            <TimeSeriesPlanningData>
                <SourceID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesPlanningData/SourceID</SourceID>
                <TargetID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesPlanningData/TargetID</TargetID>
            </TimeSeriesPlanningData>
            <TimeSeriesSelection>
                <SourceID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesSelection/SourceID</SourceID>
                <TargetID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesSelection/TargetID</TargetID>
            </TimeSeriesSelection>
            <TimeSeriesPlanningStep>
                <SourceID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesPlanningStep/SourceID</SourceID>
                <TargetID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesPlanningStep/TargetID</TargetID>
            </TimeSeriesPlanningStep>
            <TimeSeriesParty>
                <SourceParty>
                    <core:PartyID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyID/core:Ident</core:Ident>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:MDFBusiness>true</core:MDFBusiness>
                    <core:NameAddress>
                        <core:ExternalAddressID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                        <core:Name1>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Name1</core:Name1>
                        <core:Name2>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Name2</core:Name2>
                        <core:Name3>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Name3</core:Name3>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                        </core:Identifier>
                        <core:POBox POBoxPostalCode="TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:POBox/@POBoxPostalCode">TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:POBox</core:POBox>
                        <core:Street>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Street</core:Street>
                        <core:HouseNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                        <core:StreetSupplement1>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                        <core:StreetSupplement2>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                        <core:Building>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Building</core:Building>
                        <core:Floor>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Floor</core:Floor>
                        <core:RoomNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                        <core:InhouseMail>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                        <core:Department>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Department</core:Department>
                        <core:PostalCode>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:PostalCode</core:PostalCode>
                        <core:City>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:City</core:City>
                        <core:County>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:County</core:County>
                        <core:Region>
                            <core:RegionCoded>ARA</core:RegionCoded>
                            <core:RegionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                        </core:Region>
                        <core:District>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:District</core:District>
                        <core:Country>
                            <core:CountryCoded>AE</core:CountryCoded>
                            <core:CountryCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                        </core:Country>
                        <core:Timezone>
                            <core:TimezoneCoded>12.00</core:TimezoneCoded>
                            <core:TimezoneCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                        </core:Timezone>
                    </core:NameAddress>
                    <core:PrimaryContact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:PrimaryContact>
                    <core:OtherContacts>
                        <core:Contact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:Contact>
                    </core:OtherContacts>
                    <core:PartyTaxInformation>
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                        </core:TaxIdentifier>
                        <core:RegisteredName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                        <core:RegisteredOffice>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                        <core:TaxLocation>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                <core:Latitude>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                <core:Longitude>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:TaxLocation>
                        <core:CompanyRegistrationNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/SourceParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                    </core:PartyTaxInformation>
                </SourceParty>
                <ReceiverParty>
                    <core:PartyID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyID/core:Ident</core:Ident>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:MDFBusiness>false</core:MDFBusiness>
                    <core:NameAddress>
                        <core:ExternalAddressID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                        <core:Name1>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Name1</core:Name1>
                        <core:Name2>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Name2</core:Name2>
                        <core:Name3>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Name3</core:Name3>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                        </core:Identifier>
                        <core:POBox POBoxPostalCode="TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:POBox/@POBoxPostalCode">TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:POBox</core:POBox>
                        <core:Street>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Street</core:Street>
                        <core:HouseNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:HouseNumber</core:HouseNumber>
                        <core:StreetSupplement1>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                        <core:StreetSupplement2>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                        <core:Building>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Building</core:Building>
                        <core:Floor>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Floor</core:Floor>
                        <core:RoomNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:RoomNumber</core:RoomNumber>
                        <core:InhouseMail>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:InhouseMail</core:InhouseMail>
                        <core:Department>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Department</core:Department>
                        <core:PostalCode>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:PostalCode</core:PostalCode>
                        <core:City>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:City</core:City>
                        <core:County>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:County</core:County>
                        <core:Region>
                            <core:RegionCoded>ARA</core:RegionCoded>
                            <core:RegionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                        </core:Region>
                        <core:District>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:District</core:District>
                        <core:Country>
                            <core:CountryCoded>AE</core:CountryCoded>
                            <core:CountryCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                        </core:Country>
                        <core:Timezone>
                            <core:TimezoneCoded>12.00</core:TimezoneCoded>
                            <core:TimezoneCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                        </core:Timezone>
                    </core:NameAddress>
                    <core:PrimaryContact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:PrimaryContact>
                    <core:OtherContacts>
                        <core:Contact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:Contact>
                    </core:OtherContacts>
                    <core:PartyTaxInformation>
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                        </core:TaxIdentifier>
                        <core:RegisteredName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                        <core:RegisteredOffice>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                        <core:TaxLocation>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                <core:Latitude>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                <core:Longitude>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:TaxLocation>
                        <core:CompanyRegistrationNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ReceiverParty/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                    </core:PartyTaxInformation>
                </ReceiverParty>
                <ListOfPartyCoded>
                    <core:PartyCoded>
                        <core:PartyID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Ident</core:Ident>
                        </core:PartyID>
                        <core:ListOfIdentifier>
                            <core:Identifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Ident</core:Ident>
                            </core:Identifier>
                        </core:ListOfIdentifier>
                        <core:MDFBusiness>true</core:MDFBusiness>
                        <core:NameAddress>
                            <core:ExternalAddressID>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:ExternalAddressID</core:ExternalAddressID>
                            <core:Name1>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name1</core:Name1>
                            <core:Name2>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name2</core:Name2>
                            <core:Name3>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name3</core:Name3>
                            <core:Identifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Ident</core:Ident>
                            </core:Identifier>
                            <core:POBox POBoxPostalCode="TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox</core:POBox>
                            <core:Street>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Street</core:Street>
                            <core:HouseNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:HouseNumber</core:HouseNumber>
                            <core:StreetSupplement1>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement1</core:StreetSupplement1>
                            <core:StreetSupplement2>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement2</core:StreetSupplement2>
                            <core:Building>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Building</core:Building>
                            <core:Floor>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Floor</core:Floor>
                            <core:RoomNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:RoomNumber</core:RoomNumber>
                            <core:InhouseMail>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:InhouseMail</core:InhouseMail>
                            <core:Department>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Department</core:Department>
                            <core:PostalCode>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:PostalCode</core:PostalCode>
                            <core:City>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:City</core:City>
                            <core:County>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:County</core:County>
                            <core:Region>
                                <core:RegionCoded>ARA</core:RegionCoded>
                                <core:RegionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Region/core:RegionCodedOther</core:RegionCodedOther>
                            </core:Region>
                            <core:District>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:District</core:District>
                            <core:Country>
                                <core:CountryCoded>AE</core:CountryCoded>
                                <core:CountryCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Country/core:CountryCodedOther</core:CountryCodedOther>
                            </core:Country>
                            <core:Timezone>
                                <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                <core:TimezoneCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Timezone/core:TimezoneCodedOther</core:TimezoneCodedOther>
                            </core:Timezone>
                        </core:NameAddress>
                        <core:PrimaryContact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:PrimaryContact>
                        <core:OtherContacts>
                            <core:Contact>
                                <core:ContactID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Ident</core:Ident>
                                </core:ContactID>
                                <core:ContactName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactName</core:ContactName>
                                <core:ContactFunction>
                                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                    <core:ContactFunctionCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                </core:ContactFunction>
                                <core:ContactDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactDescription</core:ContactDescription>
                                <core:ListOfContactNumber>
                                    <core:ContactNumber>
                                        <core:ContactNumberValue>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue</core:ContactNumberValue>
                                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                        <core:ContactNumberTypeCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                    </core:ContactNumber>
                                </core:ListOfContactNumber>
                                <core:CorrespondenceLanguage>
                                    <core:LanguageCoded>aa</core:LanguageCoded>
                                    <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther</core:LanguageCodedOther>
                                    <core:LocaleCoded>ae</core:LocaleCoded>
                                    <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther</core:LocaleCodedOther>
                                </core:CorrespondenceLanguage>
                            </core:Contact>
                        </core:OtherContacts>
                        <core:PartyTaxInformation>
                            <core:TaxIdentifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Ident</core:Ident>
                            </core:TaxIdentifier>
                            <core:RegisteredName>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredName</core:RegisteredName>
                            <core:RegisteredOffice>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredOffice</core:RegisteredOffice>
                            <core:TaxLocation>
                                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                <core:LocationIdentifier>
                                    <core:LocID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                    </core:LocID>
                                    <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                                </core:LocationIdentifier>
                                <core:GPSCoordinates>
                                    <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                    <core:Latitude>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude</core:Latitude>
                                    <core:Longitude>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude</core:Longitude>
                                </core:GPSCoordinates>
                            </core:TaxLocation>
                            <core:CompanyRegistrationNumber>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                        </core:PartyTaxInformation>
                        <core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
                        <core:PartyRoleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesParty/ListOfPartyCoded/core:PartyCoded/core:PartyRoleCodedOther</core:PartyRoleCodedOther>
                    </core:PartyCoded>
                </ListOfPartyCoded>
            </TimeSeriesParty>
            <Language>
                <core:LanguageCoded>aa</core:LanguageCoded>
                <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/Language/core:LanguageCodedOther</core:LanguageCodedOther>
                <core:LocaleCoded>ae</core:LocaleCoded>
                <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/Language/core:LocaleCodedOther</core:LocaleCodedOther>
            </Language>
            <TimeSeriesHeaderNote>TimeSeriesResponse/TimeSeriesResponseHeader/ChangedTimeSeriesHeader/TimeSeriesHeaderNote</TimeSeriesHeaderNote>
        </ChangedTimeSeriesHeader>
        <Language>
            <core:LanguageCoded>aa</core:LanguageCoded>
            <core:LanguageCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/Language/core:LanguageCodedOther</core:LanguageCodedOther>
            <core:LocaleCoded>ae</core:LocaleCoded>
            <core:LocaleCodedOther>TimeSeriesResponse/TimeSeriesResponseHeader/Language/core:LocaleCodedOther</core:LocaleCodedOther>
        </Language>
        <TimeSeriesResponseNote>TimeSeriesResponse/TimeSeriesResponseHeader/TimeSeriesResponseNote</TimeSeriesResponseNote>
    </TimeSeriesResponseHeader>
    <TimeSeriesResponseDetail>
        <TimeSeriesDetailResponseCoded>Change</TimeSeriesDetailResponseCoded>
        <TimeSeriesDetailResponseCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/TimeSeriesDetailResponseCodedOther</TimeSeriesDetailResponseCodedOther>
        <ListOfChangedCharacteristicCombinations>
            <CharacteristicCombination>
                <CharacteristicCombinationPurposeCoded>Delete</CharacteristicCombinationPurposeCoded>
                <CharacteristicCombinationPurposeCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicCombinationPurposeCodedOther</CharacteristicCombinationPurposeCodedOther>
                <CharacteristicCombinationResponseCoded>Accepted</CharacteristicCombinationResponseCoded>
                <CharacteristicCombinationResponseCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicCombinationResponseCodedOther</CharacteristicCombinationResponseCodedOther>
                <CharacteristicCombinationID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicCombinationID</CharacteristicCombinationID>
                <CharacteristicProduct>
                    <SourceProduct>
                        <ProductAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductAttributeID</ProductAttributeID>
                        <ProductAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductAttributeName/core:Ident</core:Ident>
                        </ProductAttributeName>
                        <ProductAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductAttributeDescription</ProductAttributeDescription>
                        <ListOfDimension>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1001</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </ListOfDimension>
                        <ProductNotes>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/ProductNotes</ProductNotes>
                        <OtherProductAttribute>
                            <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicName</CharacteristicName>
                            <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeID</CharacteristicAttributeID>
                            <CharacteristicAttributeName>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeName/core:Ident</core:Ident>
                            </CharacteristicAttributeName>
                            <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                            <ListOfDimension>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1002</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </ListOfDimension>
                            <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/SourceProduct/OtherProductAttribute/CharacteristicAttributeNote</CharacteristicAttributeNote>
                        </OtherProductAttribute>
                    </SourceProduct>
                    <TargetProduct>
                        <ProductAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductAttributeID</ProductAttributeID>
                        <ProductAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductAttributeName/core:Ident</core:Ident>
                        </ProductAttributeName>
                        <ProductAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductAttributeDescription</ProductAttributeDescription>
                        <ListOfDimension>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1003</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </ListOfDimension>
                        <ProductNotes>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/ProductNotes</ProductNotes>
                        <OtherProductAttribute>
                            <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicName</CharacteristicName>
                            <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeID</CharacteristicAttributeID>
                            <CharacteristicAttributeName>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeName/core:Ident</core:Ident>
                            </CharacteristicAttributeName>
                            <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                            <ListOfDimension>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1004</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </ListOfDimension>
                            <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProduct/TargetProduct/OtherProductAttribute/CharacteristicAttributeNote</CharacteristicAttributeNote>
                        </OtherProductAttribute>
                    </TargetProduct>
                </CharacteristicProduct>
                <CharacteristicLocation>
                    <SourceLocation>
                        <LocationAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationAttributeID</LocationAttributeID>
                        <LocationAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationAttributeName/core:Ident</core:Ident>
                        </LocationAttributeName>
                        <LocationAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationAttributeDescription</LocationAttributeDescription>
                        <Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                <core:Latitude>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                <core:Longitude>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </Location>
                        <LocationNotes>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/LocationNotes</LocationNotes>
                        <OtherLocationAttribute>
                            <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicName</CharacteristicName>
                            <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeID</CharacteristicAttributeID>
                            <CharacteristicAttributeName>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Ident</core:Ident>
                            </CharacteristicAttributeName>
                            <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                            <ListOfDimension>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1005</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </ListOfDimension>
                            <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/SourceLocation/OtherLocationAttribute/CharacteristicAttributeNote</CharacteristicAttributeNote>
                        </OtherLocationAttribute>
                    </SourceLocation>
                    <TargetLocation>
                        <LocationAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationAttributeID</LocationAttributeID>
                        <LocationAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationAttributeName/core:Ident</core:Ident>
                        </LocationAttributeName>
                        <LocationAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationAttributeDescription</LocationAttributeDescription>
                        <Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:LocationIdentifier/core:LocID/core:Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:LocationIdentifier/core:LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:GPSCoordinates/core:GPSSystem</core:GPSSystem>
                                <core:Latitude>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:GPSCoordinates/core:Latitude</core:Latitude>
                                <core:Longitude>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/Location/core:GPSCoordinates/core:Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </Location>
                        <LocationNotes>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/LocationNotes</LocationNotes>
                        <OtherLocationAttribute>
                            <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicName</CharacteristicName>
                            <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeID</CharacteristicAttributeID>
                            <CharacteristicAttributeName>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeName/core:Ident</core:Ident>
                            </CharacteristicAttributeName>
                            <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                            <ListOfDimension>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1006</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </ListOfDimension>
                            <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicLocation/TargetLocation/OtherLocationAttribute/CharacteristicAttributeNote</CharacteristicAttributeNote>
                        </OtherLocationAttribute>
                    </TargetLocation>
                </CharacteristicLocation>
                <CharacteristicProductGroup>
                    <SourceProductGroup>
                        <ProductAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductAttributeID</ProductAttributeID>
                        <ProductAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductAttributeName/core:Ident</core:Ident>
                        </ProductAttributeName>
                        <ProductAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductAttributeDescription</ProductAttributeDescription>
                        <ListOfDimension>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1007</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </ListOfDimension>
                        <ProductNotes>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/ProductNotes</ProductNotes>
                        <OtherProductAttribute>
                            <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicName</CharacteristicName>
                            <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeID</CharacteristicAttributeID>
                            <CharacteristicAttributeName>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Ident</core:Ident>
                            </CharacteristicAttributeName>
                            <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                            <ListOfDimension>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1008</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </ListOfDimension>
                            <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/SourceProductGroup/OtherProductAttribute/CharacteristicAttributeNote</CharacteristicAttributeNote>
                        </OtherProductAttribute>
                    </SourceProductGroup>
                    <TargetProductGroup>
                        <ProductAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductAttributeID</ProductAttributeID>
                        <ProductAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductAttributeName/core:Ident</core:Ident>
                        </ProductAttributeName>
                        <ProductAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductAttributeDescription</ProductAttributeDescription>
                        <ListOfDimension>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1009</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </ListOfDimension>
                        <ProductNotes>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/ProductNotes</ProductNotes>
                        <OtherProductAttribute>
                            <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicName</CharacteristicName>
                            <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeID</CharacteristicAttributeID>
                            <CharacteristicAttributeName>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeName/core:Ident</core:Ident>
                            </CharacteristicAttributeName>
                            <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                            <ListOfDimension>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1010</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </ListOfDimension>
                            <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicProductGroup/TargetProductGroup/OtherProductAttribute/CharacteristicAttributeNote</CharacteristicAttributeNote>
                        </OtherProductAttribute>
                    </TargetProductGroup>
                </CharacteristicProductGroup>
                <CharacteristicOther>
                    <SourceCharacteristicsOther>
                        <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicName</CharacteristicName>
                        <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeID</CharacteristicAttributeID>
                        <CharacteristicAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeName/core:Ident</core:Ident>
                        </CharacteristicAttributeName>
                        <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                        <ListOfDimension>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1011</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </ListOfDimension>
                        <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/SourceCharacteristicsOther/CharacteristicAttributeNote</CharacteristicAttributeNote>
                    </SourceCharacteristicsOther>
                    <TargetCharacteristicsOther>
                        <CharacteristicName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicName</CharacteristicName>
                        <CharacteristicAttributeID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeID</CharacteristicAttributeID>
                        <CharacteristicAttributeName>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeName/core:Agency/core:AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeName/core:Agency/core:AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeName/core:Agency/core:CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeName/core:Ident</core:Ident>
                        </CharacteristicAttributeName>
                        <CharacteristicAttributeDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeDescription</CharacteristicAttributeDescription>
                        <ListOfDimension>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1012</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/ListOfDimension/core:Dimension/core:DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </ListOfDimension>
                        <CharacteristicAttributeNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedCharacteristicCombinations/CharacteristicCombination/CharacteristicOther/TargetCharacteristicsOther/CharacteristicAttributeNote</CharacteristicAttributeNote>
                    </TargetCharacteristicsOther>
                </CharacteristicOther>
            </CharacteristicCombination>
        </ListOfChangedCharacteristicCombinations>
        <ListOfChangedTimeSeriesKeyFigureData>
            <TimeSeriesKeyFigureData>
                <TimeSeriesKeyFigurePurposeCoded>Delete</TimeSeriesKeyFigurePurposeCoded>
                <TimeSeriesKeyFigurePurposeCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/TimeSeriesKeyFigurePurposeCodedOther</TimeSeriesKeyFigurePurposeCodedOther>
                <TimeSeriesKeyFigureResponseCoded>Accepted</TimeSeriesKeyFigureResponseCoded>
                <TimeSeriesKeyFigureResponseCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/TimeSeriesKeyFigureResponseCodedOther</TimeSeriesKeyFigureResponseCodedOther>
                <CharacteristicCombinationID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/CharacteristicCombinationID</CharacteristicCombinationID>
                <KeyFigureInformation>
                    <SourceKeyFigure>
                        <KeyFigureID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/KeyFigureInformation/SourceKeyFigure/KeyFigureID</KeyFigureID>
                        <KeyFigureName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/KeyFigureInformation/SourceKeyFigure/KeyFigureName</KeyFigureName>
                        <KeyFigureDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/KeyFigureInformation/SourceKeyFigure/KeyFigureDescription</KeyFigureDescription>
                    </SourceKeyFigure>
                    <TargetKeyFigure>
                        <KeyFigureID>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/KeyFigureInformation/TargetKeyFigure/KeyFigureID</KeyFigureID>
                        <KeyFigureName>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/KeyFigureInformation/TargetKeyFigure/KeyFigureName</KeyFigureName>
                        <KeyFigureDescription>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/KeyFigureInformation/TargetKeyFigure/KeyFigureDescription</KeyFigureDescription>
                    </TargetKeyFigure>
                </KeyFigureInformation>
                <UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/UnitOfMeasurement/core:UOMCodedOther</core:UOMCodedOther>
                </UnitOfMeasurement>
                <TimeSeriesData>
                    <TimeSeriesBucket>
                        <core:StartDate>2003-01-01T00:00:06</core:StartDate>
                        <core:EndDate>2003-01-01T00:00:07</core:EndDate>
                    </TimeSeriesBucket>
                    <TimeSeriesValue>1013.00000</TimeSeriesValue>
                    <TimeSeriesDataNote>TimeSeriesResponse/TimeSeriesResponseDetail/ListOfChangedTimeSeriesKeyFigureData/TimeSeriesKeyFigureData/TimeSeriesData/TimeSeriesDataNote</TimeSeriesDataNote>
                </TimeSeriesData>
            </TimeSeriesKeyFigureData>
        </ListOfChangedTimeSeriesKeyFigureData>
    </TimeSeriesResponseDetail>
    <TimeSeriesResponseSummary>
        <TotalTimeSeriesData>1014</TotalTimeSeriesData>
        <TotalCharacteristicCombinations>1015</TotalCharacteristicCombinations>
        <TotalKeyFigures>1016</TotalKeyFigures>
    </TimeSeriesResponseSummary>
</TimeSeriesResponse>