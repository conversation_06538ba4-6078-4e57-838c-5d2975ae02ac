<Order xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd">
    <OrderHeader>
        <OrderNumber>
            <BuyerOrderNumber>BuyerOrderNumber</BuyerOrderNumber>
            <SellerOrderNumber>SellerOrderNumber</SellerOrderNumber>
        </OrderNumber>
        <OrderIssueDate>2003-01-01T00:00:01</OrderIssueDate>
        <OrderReferences>
            <AccountCode>
                <core:RefNum>RefNum</core:RefNum>
                <core:RefDate>2003-01-01T00:00:02</core:RefDate>
            </AccountCode>
            <ContractReferences>
                <core:Contract>
                    <core:ContractID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContractID>
                    <core:TypeOfContract>
                        <core:ContractTypeCoded>DiagnosisRelatedGroup</core:ContractTypeCoded>
                        <core:ContractTypeCodedOther>ContractTypeCodedOther</core:ContractTypeCodedOther>
                    </core:TypeOfContract>
                    <core:ValidityDates>
                        <core:StartDate>2003-01-01T00:00:03</core:StartDate>
                        <core:EndDate>2003-01-01T00:00:04</core:EndDate>
                    </core:ValidityDates>
                    <core:SystemID>SystemID</core:SystemID>
                    <core:ContractItemNumber>ContractItemNumber</core:ContractItemNumber>
                </core:Contract>
            </ContractReferences>
            <QuoteReference>
                <core:RefNum>RefNum</core:RefNum>
                <core:RefDate>2003-01-01T00:00:05</core:RefDate>
            </QuoteReference>
            <RequisitionReference>
                <core:RefNum>RefNum</core:RefNum>
                <core:RefDate>2003-01-01T00:00:06</core:RefDate>
            </RequisitionReference>
            <ListOfCostCenter>
                <core:CostCenter>
                    <core:CostCenterNumber>CostCenterNumber</core:CostCenterNumber>
                    <core:ListOfAccountAssignment>
                        <core:AccountAssignment>
                            <core:AccountAssignmentID>AccountAssignmentID</core:AccountAssignmentID>
                            <core:AccountAssignmentName>AccountAssignmentName</core:AccountAssignmentName>
                            <core:AccountAssignmentValue>AccountAssignmentValue</core:AccountAssignmentValue>
                        </core:AccountAssignment>
                    </core:ListOfAccountAssignment>
                    <core:MonetaryValue>
                        <core:MonetaryAmount>1001</core:MonetaryAmount>
                        <core:Currency>
                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                        </core:Currency>
                        <core:RateOfExchangeDetail>
                            <core:ReferenceCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:ReferenceCurrency>
                            <core:TargetCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:TargetCurrency>
                            <core:RateOfExchange>1002</core:RateOfExchange>
                            <core:InverseRateOfExchange>1003</core:InverseRateOfExchange>
                            <core:IndicativeIndicator>false</core:IndicativeIndicator>
                            <core:DateOfRateOfExchange>2003-01-01T00:00:07</core:DateOfRateOfExchange>
                            <core:ListOfRateOfExchangeReference>
                                <core:Reference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:08</core:RefDate>
                                </core:Reference>
                            </core:ListOfRateOfExchangeReference>
                        </core:RateOfExchangeDetail>
                    </core:MonetaryValue>
                    <core:Quantity>
                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderReferences/ListOfCostCenter/core:CostCenter/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1004</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>1</core:UOMCoded>
                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </core:Quantity>
                    <core:Percent UnitOfMeasurement="P1">1005</core:Percent>
                </core:CostCenter>
            </ListOfCostCenter>
            <OtherOrderReferences>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                    <core:ReferenceTypeCodedOther>ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                    <core:PrimaryReference>
                        <core:RefNum>RefNum</core:RefNum>
                        <core:RefDate>2003-01-01T00:00:09</core:RefDate>
                    </core:PrimaryReference>
                    <core:SupportingReference>
                        <core:RefNum>RefNum</core:RefNum>
                        <core:RefDate>2003-01-01T00:00:10</core:RefDate>
                    </core:SupportingReference>
                    <core:SupportingSubReference>
                        <core:RefNum>RefNum</core:RefNum>
                        <core:RefDate>2003-01-01T00:00:11</core:RefDate>
                    </core:SupportingSubReference>
                    <core:ReferenceDescription>ReferenceDescription</core:ReferenceDescription>
                </core:ReferenceCoded>
            </OtherOrderReferences>
        </OrderReferences>
        <ReleaseNumber>ReleaseNumber</ReleaseNumber>
        <Purpose>
            <core:PurposeCoded>Cancellation</core:PurposeCoded>
            <core:PurposeCodedOther>PurposeCodedOther</core:PurposeCodedOther>
        </Purpose>
        <RequestedResponse>
            <core:RequestedResponseCoded>DebitAdvice</core:RequestedResponseCoded>
            <core:RequestedResponseCodedOther>RequestedResponseCodedOther</core:RequestedResponseCodedOther>
        </RequestedResponse>
        <OrderType>
            <core:OrderTypeCoded>AcknowledgementMessage</core:OrderTypeCoded>
            <core:OrderTypeCodedOther>OrderTypeCodedOther</core:OrderTypeCodedOther>
        </OrderType>
        <OrderCurrency>
            <core:CurrencyCoded>AFA</core:CurrencyCoded>
            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
        </OrderCurrency>
        <TaxAccountingCurrency>
            <core:CurrencyCoded>AFA</core:CurrencyCoded>
            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
        </TaxAccountingCurrency>
        <OrderLanguage>
            <core:LanguageCoded>aa</core:LanguageCoded>
            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
            <core:LocaleCoded>ae</core:LocaleCoded>
            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
        </OrderLanguage>
        <OrderTaxReference>
            <core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
            <core:TaxTypeCodedOther>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:Ident>Ident</core:Ident>
            </core:TaxTypeCodedOther>
            <core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem</core:TaxFunctionQualifierCoded>
            <core:TaxFunctionQualifierCodedOther>TaxFunctionQualifierCodedOther</core:TaxFunctionQualifierCodedOther>
            <core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
            <core:TaxCategoryCodedOther>TaxCategoryCodedOther</core:TaxCategoryCodedOther>
            <core:ReasonTaxExemptCoded>Exempt-ForExport</core:ReasonTaxExemptCoded>
            <core:ReasonTaxExemptCodedOther>ReasonTaxExemptCodedOther</core:ReasonTaxExemptCodedOther>
            <core:TaxPercent UnitOfMeasurement="P1">1006</core:TaxPercent>
            <core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
            <core:TaxPaymentMethodCodedOther>TaxPaymentMethodCodedOther</core:TaxPaymentMethodCodedOther>
            <core:TaxTreatmentCoded>NoTaxApplies</core:TaxTreatmentCoded>
            <core:TaxTreatmentCodedOther>TaxTreatmentCodedOther</core:TaxTreatmentCodedOther>
            <core:TaxAmount>1007</core:TaxAmount>
            <core:TaxAmountInTaxAccountingCurrency>1008</core:TaxAmountInTaxAccountingCurrency>
            <core:TaxLocation>
                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                <core:LocationIdentifier>
                    <core:LocID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:LocID>
                    <core:LocationDescription>LocationDescription</core:LocationDescription>
                </core:LocationIdentifier>
                <core:GPSCoordinates>
                    <core:GPSSystem>GPSSystem</core:GPSSystem>
                    <core:Latitude>Latitude</core:Latitude>
                    <core:Longitude>Longitude</core:Longitude>
                </core:GPSCoordinates>
            </core:TaxLocation>
        </OrderTaxReference>
        <OrderInvoiceMediumTypeCoded>Circuitswitching</OrderInvoiceMediumTypeCoded>
        <OrderInvoiceMediumTypeCodedOther>OrderInvoiceMediumTypeCodedOther</OrderInvoiceMediumTypeCodedOther>
        <OrderDates>
            <RequestedShipByDate>2003-01-01T00:00:12</RequestedShipByDate>
            <RequestedDeliverByDate>2003-01-01T00:00:13</RequestedDeliverByDate>
            <PromiseDate>2003-01-01T00:00:14</PromiseDate>
            <ValidityDates>
                <core:StartDate>2003-01-01T00:00:15</core:StartDate>
                <core:EndDate>2003-01-01T00:00:16</core:EndDate>
            </ValidityDates>
            <CancelByDate>2003-01-01T00:00:17</CancelByDate>
            <ListOfDateCoded>
                <core:DateCoded>
                    <core:Date>2003-01-01T00:00:18</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>36030</core:DateQualifierCoded>
                        <core:DateQualifierCodedOther>DateQualifierCodedOther</core:DateQualifierCodedOther>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfDateCoded>
        </OrderDates>
        <PartialShipmentAllowed>true</PartialShipmentAllowed>
        <OrderParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>false</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/BuyerParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/BuyerParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>true</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/SellerParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/SellerParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>false</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/ShipToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/ShipToParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </ShipToParty>
            <BillToParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>true</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/BillToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/BillToParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </BillToParty>
            <RemitToParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>false</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/RemitToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/RemitToParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </RemitToParty>
            <ShipFromParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>true</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/ShipFromParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/ShipFromParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </ShipFromParty>
            <WarehouseParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>false</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/WarehouseParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/WarehouseParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </WarehouseParty>
            <SoldToParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>true</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/SoldToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/SoldToParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </SoldToParty>
            <ManufacturingParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>false</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/ManufacturingParty/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/ManufacturingParty/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </ManufacturingParty>
            <MaterialIssuer>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:MDFBusiness>true</core:MDFBusiness>
                <core:NameAddress>
                    <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                    <core:Name1>Name1</core:Name1>
                    <core:Name2>Name2</core:Name2>
                    <core:Name3>Name3</core:Name3>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:Identifier>
                    <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/MaterialIssuer/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/MaterialIssuer/core:NameAddress/core:POBox</core:POBox>
                    <core:Street>Street</core:Street>
                    <core:HouseNumber>HouseNumber</core:HouseNumber>
                    <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                    <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                    <core:Building>Building</core:Building>
                    <core:Floor>Floor</core:Floor>
                    <core:RoomNumber>RoomNumber</core:RoomNumber>
                    <core:InhouseMail>InhouseMail</core:InhouseMail>
                    <core:Department>Department</core:Department>
                    <core:PostalCode>PostalCode</core:PostalCode>
                    <core:City>City</core:City>
                    <core:County>County</core:County>
                    <core:Region>
                        <core:RegionCoded>ARA</core:RegionCoded>
                        <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                    </core:Region>
                    <core:District>District</core:District>
                    <core:Country>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </core:Country>
                    <core:Timezone>
                        <core:TimezoneCoded>12.00</core:TimezoneCoded>
                        <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                    </core:Timezone>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:ContactID>
                    <core:ContactName>ContactName</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                    </core:ContactFunction>
                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                    <core:CorrespondenceLanguage>
                        <core:LanguageCoded>aa</core:LanguageCoded>
                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                        <core:LocaleCoded>ae</core:LocaleCoded>
                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                    </core:CorrespondenceLanguage>
                </core:PrimaryContact>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:Contact>
                </core:OtherContacts>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>RegisteredName</core:RegisteredName>
                    <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                    <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                </core:PartyTaxInformation>
            </MaterialIssuer>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:MDFBusiness>false</core:MDFBusiness>
                    <core:NameAddress>
                        <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                        <core:Name1>Name1</core:Name1>
                        <core:Name2>Name2</core:Name2>
                        <core:Name3>Name3</core:Name3>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:Identifier>
                        <core:POBox POBoxPostalCode="Order/OrderHeader/OrderParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox</core:POBox>
                        <core:Street>Street</core:Street>
                        <core:HouseNumber>HouseNumber</core:HouseNumber>
                        <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                        <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                        <core:Building>Building</core:Building>
                        <core:Floor>Floor</core:Floor>
                        <core:RoomNumber>RoomNumber</core:RoomNumber>
                        <core:InhouseMail>InhouseMail</core:InhouseMail>
                        <core:Department>Department</core:Department>
                        <core:PostalCode>PostalCode</core:PostalCode>
                        <core:City>City</core:City>
                        <core:County>County</core:County>
                        <core:Region>
                            <core:RegionCoded>ARA</core:RegionCoded>
                            <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                        </core:Region>
                        <core:District>District</core:District>
                        <core:Country>
                            <core:CountryCoded>AE</core:CountryCoded>
                            <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                        </core:Country>
                        <core:Timezone>
                            <core:TimezoneCoded>12.00</core:TimezoneCoded>
                            <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                        </core:Timezone>
                    </core:NameAddress>
                    <core:PrimaryContact>
                        <core:ContactID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:ContactID>
                        <core:ContactName>ContactName</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                            <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                        </core:ContactFunction>
                        <core:ContactDescription>ContactDescription</core:ContactDescription>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                        <core:CorrespondenceLanguage>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:CorrespondenceLanguage>
                    </core:PrimaryContact>
                    <core:OtherContacts>
                        <core:Contact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:Contact>
                    </core:OtherContacts>
                    <core:PartyTaxInformation>
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:TaxIdentifier>
                        <core:RegisteredName>RegisteredName</core:RegisteredName>
                        <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                        <core:TaxLocation>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                <core:Latitude>Latitude</core:Latitude>
                                <core:Longitude>Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:TaxLocation>
                        <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                    </core:PartyTaxInformation>
                    <core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
                    <core:PartyRoleCodedOther>PartyRoleCodedOther</core:PartyRoleCodedOther>
                </core:PartyCoded>
            </ListOfPartyCoded>
        </OrderParty>
        <PartLocation>PartLocation</PartLocation>
        <ListOfTransportRouting>
            <core:TransportRouting>
                <core:TransportRouteID>1009</core:TransportRouteID>
                <core:TransportMode>
                    <core:TransportModeCoded>Air</core:TransportModeCoded>
                    <core:TransportModeCodedOther>TransportModeCodedOther</core:TransportModeCodedOther>
                </core:TransportMode>
                <core:TransportMeans>
                    <core:TransportMeansCoded>20FtILContainer-ClosedTop</core:TransportMeansCoded>
                    <core:TransportMeansCodedOther>TransportMeansCodedOther</core:TransportMeansCodedOther>
                </core:TransportMeans>
                <core:TransportMeansIdentifier>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:TransportMeansIdentifier>
                <core:TransportMeansReference>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:TransportMeansReference>
                <core:TransportRequirementCoded>SpecifiedFlagRequired</core:TransportRequirementCoded>
                <core:TransportRequirementCodedOther>TransportRequirementCodedOther</core:TransportRequirementCodedOther>
                <core:CarrierName>CarrierName</core:CarrierName>
                <core:CarrierID>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:CarrierID>
                <core:TransportQuantities>
                    <core:LadingQuantity>
                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1010</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>1</core:UOMCoded>
                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </core:LadingQuantity>
                    <core:ListOfQuantityCoded>
                        <core:QuantityCoded>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1011</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                            <core:QuantityQualifierCodedOther>QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                        </core:QuantityCoded>
                    </core:ListOfQuantityCoded>
                </core:TransportQuantities>
                <core:CustShippingContractNum>CustShippingContractNum</core:CustShippingContractNum>
                <core:ServiceLevel>
                    <core:ServiceLevelCoded>BulkCommodityTrain</core:ServiceLevelCoded>
                    <core:ServiceLevelCodedOther>ServiceLevelCodedOther</core:ServiceLevelCodedOther>
                    <core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder</core:ServiceLevelReasonCoded>
                    <core:ServiceLevelReasonCodedOther>ServiceLevelReasonCodedOther</core:ServiceLevelReasonCodedOther>
                    <core:ServiceLevelResponsibilityCoded>CustomerPlant</core:ServiceLevelResponsibilityCoded>
                    <core:ServiceLevelResponsibilityCodedOther>ServiceLevelResponsibilityCodedOther</core:ServiceLevelResponsibilityCodedOther>
                </core:ServiceLevel>
                <core:ShippingInstructions>ShippingInstructions</core:ShippingInstructions>
                <core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
                <core:TransportLegCodedOther>TransportLegCodedOther</core:TransportLegCodedOther>
                <core:ListOfTransportEquipment>
                    <core:TransportEquipment>
                        <core:EquipmentProviderCoded>ShipperSupplied</core:EquipmentProviderCoded>
                        <core:EquipmentProviderCodedOther>EquipmentProviderCodedOther</core:EquipmentProviderCodedOther>
                        <core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased</core:EquipmentOwnerCoded>
                        <core:EquipmentOwnerCodedOther>EquipmentOwnerCodedOther</core:EquipmentOwnerCodedOther>
                        <core:EquipmentID>EquipmentID</core:EquipmentID>
                        <core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop</core:EquipmentSizeTypeCoded>
                        <core:EquipementSizeTypeCodedOther>EquipementSizeTypeCodedOther</core:EquipementSizeTypeCodedOther>
                        <core:EquipmentStatusCoded>Continental</core:EquipmentStatusCoded>
                        <core:EquipmentStatusCodedOther>EquipmentStatusCodedOther</core:EquipmentStatusCodedOther>
                        <core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
                        <core:FullIndicatorCodedOther>FullIndicatorCodedOther</core:FullIndicatorCodedOther>
                        <core:Conditions>
                            <core:RefrigerationOn>true</core:RefrigerationOn>
                            <core:Residue>false</core:Residue>
                            <core:ListOfConditions>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1012</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </core:ListOfConditions>
                        </core:Conditions>
                        <core:EquipmentNote>EquipmentNote</core:EquipmentNote>
                        <core:ListOfSealInfo>
                            <core:SealInfo>
                                <core:SealNumber>SealNumber</core:SealNumber>
                                <core:SealIssuer>
                                    <core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
                                    <core:SealIssuerCodedOther>SealIssuerCodedOther</core:SealIssuerCodedOther>
                                </core:SealIssuer>
                                <core:SealStatusDescription>
                                    <core:SealStatusCoded>Broken</core:SealStatusCoded>
                                    <core:SealStatusCodedOther>SealStatusCodedOther</core:SealStatusCodedOther>
                                    <core:SealStatusInfo>SealStatusInfo</core:SealStatusInfo>
                                </core:SealStatusDescription>
                            </core:SealInfo>
                        </core:ListOfSealInfo>
                        <core:ListOfEquipmentMeasurements>
                            <core:Dimension>
                                <core:Measurement>
                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1013</core:MeasurementValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Measurement>
                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                            </core:Dimension>
                        </core:ListOfEquipmentMeasurements>
                    </core:TransportEquipment>
                </core:ListOfTransportEquipment>
                <core:TransitDirection>
                    <core:TransitDirectionCoded>BuyerToSeller</core:TransitDirectionCoded>
                    <core:TransitDirectionCodedOther>TransitDirectionCodedOther</core:TransitDirectionCodedOther>
                    <core:TransitTimeQualifierCoded>AirHours</core:TransitTimeQualifierCoded>
                    <core:TransitTimeQualifierCodedOther>TransitTimeQualifierCodedOther</core:TransitTimeQualifierCodedOther>
                    <core:TransitTime>1014</core:TransitTime>
                </core:TransitDirection>
                <core:TransportLocationList>
                    <core:StartTransportLocation>
                        <core:Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                <core:Latitude>Latitude</core:Latitude>
                                <core:Longitude>Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:Location>
                        <core:LocationID>1015</core:LocationID>
                        <core:Sequence>1016</core:Sequence>
                        <core:EstimatedArrivalDate>2003-01-01T00:00:19</core:EstimatedArrivalDate>
                        <core:ActualArrivalDate>2003-01-01T00:00:20</core:ActualArrivalDate>
                        <core:EstimatedDepartureDate>2003-01-01T00:00:21</core:EstimatedDepartureDate>
                        <core:ActualDepartureDate>2003-01-01T00:00:22</core:ActualDepartureDate>
                    </core:StartTransportLocation>
                    <core:InterimTransportLocation>
                        <core:Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                <core:Latitude>Latitude</core:Latitude>
                                <core:Longitude>Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:Location>
                        <core:LocationID>1017</core:LocationID>
                        <core:Sequence>1018</core:Sequence>
                        <core:EstimatedArrivalDate>2003-01-01T00:00:23</core:EstimatedArrivalDate>
                        <core:ActualArrivalDate>2003-01-01T00:00:24</core:ActualArrivalDate>
                        <core:EstimatedDepartureDate>2003-01-01T00:00:25</core:EstimatedDepartureDate>
                        <core:ActualDepartureDate>2003-01-01T00:00:26</core:ActualDepartureDate>
                    </core:InterimTransportLocation>
                    <core:EndTransportLocation>
                        <core:Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                <core:Latitude>Latitude</core:Latitude>
                                <core:Longitude>Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:Location>
                        <core:LocationID>1019</core:LocationID>
                        <core:Sequence>1020</core:Sequence>
                        <core:EstimatedArrivalDate>2003-01-01T00:00:27</core:EstimatedArrivalDate>
                        <core:ActualArrivalDate>2003-01-01T00:00:28</core:ActualArrivalDate>
                        <core:EstimatedDepartureDate>2003-01-01T00:00:29</core:EstimatedDepartureDate>
                        <core:ActualDepartureDate>2003-01-01T00:00:30</core:ActualDepartureDate>
                    </core:EndTransportLocation>
                </core:TransportLocationList>
            </core:TransportRouting>
        </ListOfTransportRouting>
        <OrderTermsOfDelivery>
            <core:TermsOfDeliveryFunctionCoded>PriceCondition</core:TermsOfDeliveryFunctionCoded>
            <core:TermsOfDeliveryFunctionCodedOther>TermsOfDeliveryFunctionCodedOther</core:TermsOfDeliveryFunctionCodedOther>
            <core:TransportTermsCoded>Ex-Works</core:TransportTermsCoded>
            <core:TransportTermsCodedOther>TransportTermsCodedOther</core:TransportTermsCodedOther>
            <core:ShipmentMethodOfPaymentCoded>Account</core:ShipmentMethodOfPaymentCoded>
            <core:ShipmentMethodOfPaymentCodedOther>ShipmentMethodOfPaymentCodedOther</core:ShipmentMethodOfPaymentCodedOther>
            <core:Location>
                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                <core:LocationIdentifier>
                    <core:LocID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:LocID>
                    <core:LocationDescription>LocationDescription</core:LocationDescription>
                </core:LocationIdentifier>
                <core:GPSCoordinates>
                    <core:GPSSystem>GPSSystem</core:GPSSystem>
                    <core:Latitude>Latitude</core:Latitude>
                    <core:Longitude>Longitude</core:Longitude>
                </core:GPSCoordinates>
            </core:Location>
            <core:TermsOfDeliveryDescription>TermsOfDeliveryDescription</core:TermsOfDeliveryDescription>
            <core:TransportDescription>TransportDescription</core:TransportDescription>
            <core:RiskOfLossCoded>RiskOfLossInTransitToDestinationIsBuyers</core:RiskOfLossCoded>
            <core:RiskOfLossCodedOther>RiskOfLossCodedOther</core:RiskOfLossCodedOther>
            <core:RiskOfLossDescription>RiskOfLossDescription</core:RiskOfLossDescription>
        </OrderTermsOfDelivery>
        <OrderHeaderPrice>
            <core:Price>
                <core:PricingType>
                    <core:PriceTypeCoded>CancellationPrice</core:PriceTypeCoded>
                    <core:PriceTypeCodedOther>PriceTypeCodedOther</core:PriceTypeCodedOther>
                </core:PricingType>
                <core:UnitPrice>
                    <core:UnitPriceValue>1021</core:UnitPriceValue>
                    <core:Currency>
                        <core:CurrencyCoded>AFA</core:CurrencyCoded>
                        <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                    </core:Currency>
                </core:UnitPrice>
                <core:UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                </core:UnitOfMeasurement>
                <core:CalculatedPriceBasisQuantity>
                    <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderHeaderPrice/core:Price/core:CalculatedPriceBasisQuantity/core:QuantityValue/@SignificanceCodedOther">1022</core:QuantityValue>
                    <core:UnitOfMeasurement>
                        <core:UOMCoded>1</core:UOMCoded>
                        <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                    </core:UnitOfMeasurement>
                </core:CalculatedPriceBasisQuantity>
                <core:ValidityDates>
                    <core:StartDate>2003-01-01T00:00:31</core:StartDate>
                    <core:EndDate>2003-01-01T00:00:32</core:EndDate>
                </core:ValidityDates>
                <core:PriceQuantityRange>
                    <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderHeaderPrice/core:Price/core:PriceQuantityRange/core:QuantityValue/@SignificanceCodedOther">1023</core:QuantityValue>
                    <core:UnitOfMeasurement>
                        <core:UOMCoded>1</core:UOMCoded>
                        <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                    </core:UnitOfMeasurement>
                </core:PriceQuantityRange>
                <core:PriceMultiplier>
                    <core:PriceMultiplierCoded>CostMarkupMultiplier-OriginalCost</core:PriceMultiplierCoded>
                    <core:PriceMultiplierCodedOther>PriceMultiplierCodedOther</core:PriceMultiplierCodedOther>
                    <core:Multiplier>1024</core:Multiplier>
                </core:PriceMultiplier>
            </core:Price>
        </OrderHeaderPrice>
        <OrderPaymentInstructions>
            <core:PaymentTerms>
                <core:NetDueDate>2003-01-01T00:00:33</core:NetDueDate>
                <core:NetTimeDue>
                    <core:TimeDue>
                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderPaymentInstructions/core:PaymentTerms/core:NetTimeDue/core:TimeDue/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderHeader/OrderPaymentInstructions/core:PaymentTerms/core:NetTimeDue/core:TimeDue/core:MeasurementValue/@ConditionsCodedOther">1025</core:MeasurementValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>1</core:UOMCoded>
                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </core:TimeDue>
                    <core:DateTimeRefCoded>AnticipatedDeliveryDate</core:DateTimeRefCoded>
                    <core:DateTimeRefCodedOther>DateTimeRefCodedOther</core:DateTimeRefCodedOther>
                </core:NetTimeDue>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>10DaysAfterEndOfMonth</core:PaymentTermCoded>
                    <core:PaymentTermCodedOther>PaymentTermCodedOther</core:PaymentTermCodedOther>
                    <core:PaymentTermValue>1026</core:PaymentTermValue>
                    <core:PaymentTermDescription>PaymentTermDescription</core:PaymentTermDescription>
                    <core:DiscountInformation>
                        <core:DiscountPercent UnitOfMeasurement="P1">1027</core:DiscountPercent>
                        <core:DiscountAmount>
                            <core:MonetaryAmount>1028</core:MonetaryAmount>
                            <core:Currency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:Currency>
                            <core:RateOfExchangeDetail>
                                <core:ReferenceCurrency>
                                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                </core:ReferenceCurrency>
                                <core:TargetCurrency>
                                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                </core:TargetCurrency>
                                <core:RateOfExchange>1029</core:RateOfExchange>
                                <core:InverseRateOfExchange>1030</core:InverseRateOfExchange>
                                <core:IndicativeIndicator>true</core:IndicativeIndicator>
                                <core:DateOfRateOfExchange>2003-01-01T00:00:34</core:DateOfRateOfExchange>
                                <core:ListOfRateOfExchangeReference>
                                    <core:Reference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:35</core:RefDate>
                                    </core:Reference>
                                </core:ListOfRateOfExchangeReference>
                            </core:RateOfExchangeDetail>
                        </core:DiscountAmount>
                        <core:DiscountDueDate>2003-01-01T00:00:36</core:DiscountDueDate>
                        <core:DiscountDayOfMonth>1031</core:DiscountDayOfMonth>
                        <core:DiscountTimeDue>
                            <core:TimeDue>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderPaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountTimeDue/core:TimeDue/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderHeader/OrderPaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountTimeDue/core:TimeDue/core:MeasurementValue/@ConditionsCodedOther">1032</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:TimeDue>
                            <core:DateTimeRefCoded>AnticipatedDeliveryDate</core:DateTimeRefCoded>
                            <core:DateTimeRefCodedOther>DateTimeRefCodedOther</core:DateTimeRefCodedOther>
                        </core:DiscountTimeDue>
                    </core:DiscountInformation>
                </core:PaymentTerm>
                <core:PaymentTermsNote>PaymentTermsNote</core:PaymentTermsNote>
            </core:PaymentTerms>
            <core:PaymentMethod>
                <core:PaymentMeanCoded>1035Exchange</core:PaymentMeanCoded>
                <core:PaymentMeanCodedOther>PaymentMeanCodedOther</core:PaymentMeanCodedOther>
                <core:PaymentMeanReference>
                    <core:RefNum>RefNum</core:RefNum>
                    <core:RefDate>2003-01-01T00:00:37</core:RefDate>
                </core:PaymentMeanReference>
                <core:PaymentSystemCoded>OrdinaryPost</core:PaymentSystemCoded>
                <core:PaymentSystemCodedOther>PaymentSystemCodedOther</core:PaymentSystemCodedOther>
                <core:FITransfer>
                    <core:FromFITransfer>
                        <core:AccountDetail>
                            <core:AccountID>AccountID</core:AccountID>
                            <core:SecondaryAccountID>SecondaryAccountID</core:SecondaryAccountID>
                            <core:IBAN>AA000000001033</core:IBAN>
                            <core:AccountControlKey>AccountControlKey</core:AccountControlKey>
                            <core:AccountTypeCoded>NewBusinessAccount</core:AccountTypeCoded>
                            <core:AccountTypeCodedOther>AccountTypeCodedOther</core:AccountTypeCodedOther>
                            <core:AccountName1>AccountName1</core:AccountName1>
                            <core:AccountName2>AccountName2</core:AccountName2>
                            <core:Currency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:Currency>
                            <core:AccountReferences>
                                <core:ReferenceCoded>
                                    <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                                    <core:ReferenceTypeCodedOther>ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                                    <core:PrimaryReference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:38</core:RefDate>
                                    </core:PrimaryReference>
                                    <core:SupportingReference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:39</core:RefDate>
                                    </core:SupportingReference>
                                    <core:SupportingSubReference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:40</core:RefDate>
                                    </core:SupportingSubReference>
                                    <core:ReferenceDescription>ReferenceDescription</core:ReferenceDescription>
                                </core:ReferenceCoded>
                            </core:AccountReferences>
                        </core:AccountDetail>
                        <core:FinancialInstitution>
                            <core:FinancialInstitutionID>FinancialInstitutionID</core:FinancialInstitutionID>
                            <core:FinancialInstitutionName>FinancialInstitutionName</core:FinancialInstitutionName>
                            <core:FIBranchID>FIBranchID</core:FIBranchID>
                            <core:FIBranchAddress>
                                <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                                <core:Name1>Name1</core:Name1>
                                <core:Name2>Name2</core:Name2>
                                <core:Name3>Name3</core:Name3>
                                <core:Identifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:Identifier>
                                <core:POBox POBoxPostalCode="Order/OrderHeader/OrderPaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderPaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox</core:POBox>
                                <core:Street>Street</core:Street>
                                <core:HouseNumber>HouseNumber</core:HouseNumber>
                                <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                                <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                                <core:Building>Building</core:Building>
                                <core:Floor>Floor</core:Floor>
                                <core:RoomNumber>RoomNumber</core:RoomNumber>
                                <core:InhouseMail>InhouseMail</core:InhouseMail>
                                <core:Department>Department</core:Department>
                                <core:PostalCode>PostalCode</core:PostalCode>
                                <core:City>City</core:City>
                                <core:County>County</core:County>
                                <core:Region>
                                    <core:RegionCoded>ARA</core:RegionCoded>
                                    <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                                </core:Region>
                                <core:District>District</core:District>
                                <core:Country>
                                    <core:CountryCoded>AE</core:CountryCoded>
                                    <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                                </core:Country>
                                <core:Timezone>
                                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                    <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                                </core:Timezone>
                            </core:FIBranchAddress>
                        </core:FinancialInstitution>
                    </core:FromFITransfer>
                    <core:ToFITransfer>
                        <core:AccountDetail>
                            <core:AccountID>AccountID</core:AccountID>
                            <core:SecondaryAccountID>SecondaryAccountID</core:SecondaryAccountID>
                            <core:IBAN>AA000000001034</core:IBAN>
                            <core:AccountControlKey>AccountControlKey</core:AccountControlKey>
                            <core:AccountTypeCoded>NewBusinessAccount</core:AccountTypeCoded>
                            <core:AccountTypeCodedOther>AccountTypeCodedOther</core:AccountTypeCodedOther>
                            <core:AccountName1>AccountName1</core:AccountName1>
                            <core:AccountName2>AccountName2</core:AccountName2>
                            <core:Currency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:Currency>
                            <core:AccountReferences>
                                <core:ReferenceCoded>
                                    <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                                    <core:ReferenceTypeCodedOther>ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                                    <core:PrimaryReference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:41</core:RefDate>
                                    </core:PrimaryReference>
                                    <core:SupportingReference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:42</core:RefDate>
                                    </core:SupportingReference>
                                    <core:SupportingSubReference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:43</core:RefDate>
                                    </core:SupportingSubReference>
                                    <core:ReferenceDescription>ReferenceDescription</core:ReferenceDescription>
                                </core:ReferenceCoded>
                            </core:AccountReferences>
                        </core:AccountDetail>
                        <core:FinancialInstitution>
                            <core:FinancialInstitutionID>FinancialInstitutionID</core:FinancialInstitutionID>
                            <core:FinancialInstitutionName>FinancialInstitutionName</core:FinancialInstitutionName>
                            <core:FIBranchID>FIBranchID</core:FIBranchID>
                            <core:FIBranchAddress>
                                <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                                <core:Name1>Name1</core:Name1>
                                <core:Name2>Name2</core:Name2>
                                <core:Name3>Name3</core:Name3>
                                <core:Identifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:Identifier>
                                <core:POBox POBoxPostalCode="Order/OrderHeader/OrderPaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox/@POBoxPostalCode">Order/OrderHeader/OrderPaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox</core:POBox>
                                <core:Street>Street</core:Street>
                                <core:HouseNumber>HouseNumber</core:HouseNumber>
                                <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                                <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                                <core:Building>Building</core:Building>
                                <core:Floor>Floor</core:Floor>
                                <core:RoomNumber>RoomNumber</core:RoomNumber>
                                <core:InhouseMail>InhouseMail</core:InhouseMail>
                                <core:Department>Department</core:Department>
                                <core:PostalCode>PostalCode</core:PostalCode>
                                <core:City>City</core:City>
                                <core:County>County</core:County>
                                <core:Region>
                                    <core:RegionCoded>ARA</core:RegionCoded>
                                    <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                                </core:Region>
                                <core:District>District</core:District>
                                <core:Country>
                                    <core:CountryCoded>AE</core:CountryCoded>
                                    <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                                </core:Country>
                                <core:Timezone>
                                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                    <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                                </core:Timezone>
                            </core:FIBranchAddress>
                        </core:FinancialInstitution>
                    </core:ToFITransfer>
                    <core:PaymentRecordOther>PaymentRecordOther</core:PaymentRecordOther>
                </core:FITransfer>
            </core:PaymentMethod>
        </OrderPaymentInstructions>
        <OrderAllowancesOrCharges>
            <core:AllowOrCharge>
                <core:IndicatorCoded>About</core:IndicatorCoded>
                <core:IndicatorCodedOther>IndicatorCodedOther</core:IndicatorCodedOther>
                <core:BasisCoded>None</core:BasisCoded>
                <core:BasisCodedOther>BasisCodedOther</core:BasisCodedOther>
                <core:MethodOfHandlingCoded>BillBack</core:MethodOfHandlingCoded>
                <core:MethodOfHandlingCodedOther>MethodOfHandlingCodedOther</core:MethodOfHandlingCodedOther>
                <core:AllowOrChargeTreatment>
                    <core:AllowOrChargeTreatmentCoded>UnitPriceNet</core:AllowOrChargeTreatmentCoded>
                    <core:AllowOrChargeTreatmentCodedOther>AllowOrChargeTreatmentCodedOther</core:AllowOrChargeTreatmentCodedOther>
                </core:AllowOrChargeTreatment>
                <core:AllowanceOrChargeDescription>
                    <core:RefID>RefID</core:RefID>
                    <core:ListOfDescription>ListOfDescription</core:ListOfDescription>
                    <core:ServiceCoded>About</core:ServiceCoded>
                    <core:ServiceCodedOther>ServiceCodedOther</core:ServiceCodedOther>
                </core:AllowanceOrChargeDescription>
                <core:ValidityDates>
                    <core:StartDate>2003-01-01T00:00:44</core:StartDate>
                    <core:EndDate>2003-01-01T00:00:45</core:EndDate>
                </core:ValidityDates>
                <core:BasisQuantityRange>
                    <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderAllowancesOrCharges/core:AllowOrCharge/core:BasisQuantityRange/core:QuantityValue/@SignificanceCodedOther">1035</core:QuantityValue>
                    <core:UnitOfMeasurement>
                        <core:UOMCoded>1</core:UOMCoded>
                        <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                    </core:UnitOfMeasurement>
                </core:BasisQuantityRange>
                <core:TypeOfAllowanceOrCharge>
                    <core:QuantityAllowanceOrCharge>
                        <core:Quantity>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderHeader/OrderAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1036</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                        </core:Quantity>
                        <core:Rate>
                            <core:RatePerUnit>
                                <core:UnitPriceValue>1037</core:UnitPriceValue>
                                <core:Currency>
                                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                </core:Currency>
                            </core:RatePerUnit>
                            <core:UnitPriceBasis>1038</core:UnitPriceBasis>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                        </core:Rate>
                        <core:QuantityMonetaryValue>
                            <core:MonetaryAmount>1039</core:MonetaryAmount>
                            <core:Currency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:Currency>
                            <core:RateOfExchangeDetail>
                                <core:ReferenceCurrency>
                                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                </core:ReferenceCurrency>
                                <core:TargetCurrency>
                                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                </core:TargetCurrency>
                                <core:RateOfExchange>1040</core:RateOfExchange>
                                <core:InverseRateOfExchange>1041</core:InverseRateOfExchange>
                                <core:IndicativeIndicator>false</core:IndicativeIndicator>
                                <core:DateOfRateOfExchange>2003-01-01T00:00:46</core:DateOfRateOfExchange>
                                <core:ListOfRateOfExchangeReference>
                                    <core:Reference>
                                        <core:RefNum>RefNum</core:RefNum>
                                        <core:RefDate>2003-01-01T00:00:47</core:RefDate>
                                    </core:Reference>
                                </core:ListOfRateOfExchangeReference>
                            </core:RateOfExchangeDetail>
                        </core:QuantityMonetaryValue>
                    </core:QuantityAllowanceOrCharge>
                </core:TypeOfAllowanceOrCharge>
                <core:Tax>
                    <core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
                    <core:TaxTypeCodedOther>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>Ident</core:Ident>
                    </core:TaxTypeCodedOther>
                    <core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem</core:TaxFunctionQualifierCoded>
                    <core:TaxFunctionQualifierCodedOther>TaxFunctionQualifierCodedOther</core:TaxFunctionQualifierCodedOther>
                    <core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
                    <core:TaxCategoryCodedOther>TaxCategoryCodedOther</core:TaxCategoryCodedOther>
                    <core:ReasonTaxExemptCoded>Exempt-ForExport</core:ReasonTaxExemptCoded>
                    <core:ReasonTaxExemptCodedOther>ReasonTaxExemptCodedOther</core:ReasonTaxExemptCodedOther>
                    <core:TaxPercent UnitOfMeasurement="P1">1042</core:TaxPercent>
                    <core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
                    <core:TaxPaymentMethodCodedOther>TaxPaymentMethodCodedOther</core:TaxPaymentMethodCodedOther>
                    <core:TaxableAmount>1043</core:TaxableAmount>
                    <core:TaxableAmountInTaxAccountingCurrency>1044</core:TaxableAmountInTaxAccountingCurrency>
                    <core:TaxAmount>1045</core:TaxAmount>
                    <core:TaxAmountInTaxAccountingCurrency>1046</core:TaxAmountInTaxAccountingCurrency>
                    <core:TaxLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:TaxLocation>
                </core:Tax>
            </core:AllowOrCharge>
        </OrderAllowancesOrCharges>
        <RoundTripInformation>
            <core:Immutable>true</core:Immutable>
            <core:ReLink>ReLink</core:ReLink>
            <core:ReLinkID>ReLinkID</core:ReLinkID>
            <core:SellerShoppingCartID>SellerShoppingCartID</core:SellerShoppingCartID>
        </RoundTripInformation>
        <OrderHeaderNote>OrderHeaderNote</OrderHeaderNote>
        <SpecialHandling>
            <core:SpecialHandlingCoded>SpeedRestrictedTo25MilesPerHour</core:SpecialHandlingCoded>
            <core:SpecialHandlingCodedOther>SpecialHandlingCodedOther</core:SpecialHandlingCodedOther>
            <core:SpecialHandlingNote>SpecialHandlingNote</core:SpecialHandlingNote>
        </SpecialHandling>
        <ListOfStructuredNote>
            <core:StructuredNote>
                <core:GeneralNote>GeneralNote</core:GeneralNote>
                <core:NoteID>NoteID</core:NoteID>
                <core:Agency>
                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                </core:Agency>
                <core:NoteURL>NoteURL</core:NoteURL>
                <core:TextTypeCoded>Deadlines</core:TextTypeCoded>
                <core:TextTypeCodedOther>TextTypeCodedOther</core:TextTypeCodedOther>
            </core:StructuredNote>
        </ListOfStructuredNote>
        <ListOfNameValueSet>
            <core:NameValueSet>
                <core:SetName>SetName</core:SetName>
                <core:SetID>SetID</core:SetID>
                <core:ListOfNameValuePair>
                    <core:NameValuePair>
                        <core:Name>Name</core:Name>
                        <core:Value>Value</core:Value>
                        <core:Datatype>Datatype</core:Datatype>
                    </core:NameValuePair>
                </core:ListOfNameValuePair>
            </core:NameValueSet>
        </ListOfNameValueSet>
        <OrderHeaderAttachments>
            <core:Attachment>
                <core:AttachmentActionCoded>Add</core:AttachmentActionCoded>
                <core:AttachmentActionCodedOther>AttachmentActionCodedOther</core:AttachmentActionCodedOther>
                <core:AttachmentPurpose>AttachmentPurpose</core:AttachmentPurpose>
                <core:FileName>FileName</core:FileName>
                <core:AttachmentTitle>AttachmentTitle</core:AttachmentTitle>
                <core:AttachmentDescription>AttachmentDescription</core:AttachmentDescription>
                <core:Language>
                    <core:LanguageCoded>aa</core:LanguageCoded>
                    <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                    <core:LocaleCoded>ae</core:LocaleCoded>
                    <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                </core:Language>
                <core:MIMEType>MIMEType</core:MIMEType>
                <core:AttachmentLocation>AttachmentLocation</core:AttachmentLocation>
            </core:Attachment>
        </OrderHeaderAttachments>
    </OrderHeader>
    <OrderDetail>
        <ListOfItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1047</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1048</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>ComponentGroup</core:LineItemTypeCoded>
                        <core:LineItemTypeCodedOther>LineItemTypeCodedOther</core:LineItemTypeCodedOther>
                    </LineItemType>
                    <ParentItemNumber LineItemNumTypeCoded="Seller">1049</ParentItemNumber>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>PartID</core:PartID>
                                <core:PartIDExt>PartIDExt</core:PartIDExt>
                                <core:RevisionNumber>RevisionNumber</core:RevisionNumber>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>PartID</core:PartID>
                                <core:PartIDExt>PartIDExt</core:PartIDExt>
                                <core:RevisionNumber>RevisionNumber</core:RevisionNumber>
                            </core:BuyerPartNumber>
                            <core:ManufacturerPartNumber>
                                <core:PartID>PartID</core:PartID>
                                <core:PartIDExt>PartIDExt</core:PartIDExt>
                                <core:RevisionNumber>RevisionNumber</core:RevisionNumber>
                                <core:ManufacturerID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:ManufacturerID>
                                <core:ManufacturerName>ManufacturerName</core:ManufacturerName>
                            </core:ManufacturerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifierQualifierCodedOther>ProductIdentifierQualifierCodedOther</core:ProductIdentifierQualifierCodedOther>
                                <core:ProductIdentifier>ProductIdentifier</core:ProductIdentifier>
                                <core:ProductIdentifierExt>ProductIdentifierExt</core:ProductIdentifierExt>
                            </core:StandardPartNumber>
                            <core:SubstitutePartNumbers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifierQualifierCodedOther>ProductIdentifierQualifierCodedOther</core:ProductIdentifierQualifierCodedOther>
                                    <core:ProductIdentifier>ProductIdentifier</core:ProductIdentifier>
                                    <core:ProductIdentifierExt>ProductIdentifierExt</core:ProductIdentifierExt>
                                </core:ProductIdentifierCoded>
                            </core:SubstitutePartNumbers>
                            <core:OtherItemIdentifiers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifierQualifierCodedOther>ProductIdentifierQualifierCodedOther</core:ProductIdentifierQualifierCodedOther>
                                    <core:ProductIdentifier>ProductIdentifier</core:ProductIdentifier>
                                    <core:ProductIdentifierExt>ProductIdentifierExt</core:ProductIdentifierExt>
                                </core:ProductIdentifierCoded>
                            </core:OtherItemIdentifiers>
                        </core:PartNumbers>
                        <core:Service>false</core:Service>
                        <core:ItemDescription>ItemDescription</core:ItemDescription>
                        <core:ListOfItemCharacteristic>
                            <core:ItemCharacteristic>
                                <core:ItemCharacteristicCoded>AdditionalSectorialCharacteristics</core:ItemCharacteristicCoded>
                                <core:ItemCharacteristicCodedOther>ItemCharacteristicCodedOther</core:ItemCharacteristicCodedOther>
                                <core:SurfaceLayerPositionCoded>All</core:SurfaceLayerPositionCoded>
                                <core:SurfaceLayerPositionCodedOther>SurfaceLayerPositionCodedOther</core:SurfaceLayerPositionCodedOther>
                                <core:ItemCharacteristicValue>ItemCharacteristicValue</core:ItemCharacteristicValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                                <core:ListOfDimension>
                                    <core:Dimension>
                                        <core:Measurement>
                                            <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1050</core:MeasurementValue>
                                            <core:UnitOfMeasurement>
                                                <core:UOMCoded>1</core:UOMCoded>
                                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                            </core:UnitOfMeasurement>
                                        </core:Measurement>
                                        <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                        <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                                    </core:Dimension>
                                </core:ListOfDimension>
                            </core:ItemCharacteristic>
                        </core:ListOfItemCharacteristic>
                        <core:CommodityCode>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:CommodityCode>
                        <core:Category>
                            <core:CategoryID>CategoryID</core:CategoryID>
                            <core:StandardCategoryID>
                                <core:StandardCategoryType>StandardCategoryType</core:StandardCategoryType>
                                <core:ClassificationID>ClassificationID</core:ClassificationID>
                                <core:TechnicalID>TechnicalID</core:TechnicalID>
                            </core:StandardCategoryID>
                        </core:Category>
                    </ItemIdentifiers>
                    <ListOfDimension>
                        <core:Dimension>
                            <core:Measurement>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1051</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Measurement>
                            <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                            <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                        </core:Dimension>
                    </ListOfDimension>
                    <TotalQuantity>
                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/TotalQuantity/core:QuantityValue/@SignificanceCodedOther">1052</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>1</core:UOMCoded>
                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </TotalQuantity>
                    <MaxBackOrderQuantity>
                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/MaxBackOrderQuantity/core:QuantityValue/@SignificanceCodedOther">1053</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>1</core:UOMCoded>
                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </MaxBackOrderQuantity>
                    <ListOfQuantityCoded>
                        <core:QuantityCoded>
                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1054</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                            <core:QuantityQualifierCodedOther>QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                        </core:QuantityCoded>
                    </ListOfQuantityCoded>
                    <BaseItemReferences>
                        <core:OffCatalogFlag>true</core:OffCatalogFlag>
                        <core:CatalogReference>
                            <core:CatalogURL>CatalogURL</core:CatalogURL>
                            <core:CatalogID>CatalogID</core:CatalogID>
                            <core:CatalogItemID>CatalogItemID</core:CatalogItemID>
                        </core:CatalogReference>
                        <core:ItemContractReferences>
                            <core:Contract>
                                <core:ContractID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:ContractID>
                                <core:TypeOfContract>
                                    <core:ContractTypeCoded>DiagnosisRelatedGroup</core:ContractTypeCoded>
                                    <core:ContractTypeCodedOther>ContractTypeCodedOther</core:ContractTypeCodedOther>
                                </core:TypeOfContract>
                                <core:ValidityDates>
                                    <core:StartDate>2003-01-01T00:00:48</core:StartDate>
                                    <core:EndDate>2003-01-01T00:00:49</core:EndDate>
                                </core:ValidityDates>
                                <core:SystemID>SystemID</core:SystemID>
                                <core:ContractItemNumber>ContractItemNumber</core:ContractItemNumber>
                            </core:Contract>
                        </core:ItemContractReferences>
                        <core:LineItemRequisitionReference>
                            <core:RequisitionReference>
                                <core:RefNum>RefNum</core:RefNum>
                                <core:RefDate>2003-01-01T00:00:50</core:RefDate>
                            </core:RequisitionReference>
                            <core:RequisitionLineItemNumber>RequisitionLineItemNumber</core:RequisitionLineItemNumber>
                        </core:LineItemRequisitionReference>
                        <core:BlanketOrderReference>
                            <core:RefNum>RefNum</core:RefNum>
                            <core:RefDate>2003-01-01T00:00:51</core:RefDate>
                        </core:BlanketOrderReference>
                        <core:IsItemImmutable>false</core:IsItemImmutable>
                        <core:ListOfCostCenter>
                            <core:CostCenter>
                                <core:CostCenterNumber>CostCenterNumber</core:CostCenterNumber>
                                <core:ListOfAccountAssignment>
                                    <core:AccountAssignment>
                                        <core:AccountAssignmentID>AccountAssignmentID</core:AccountAssignmentID>
                                        <core:AccountAssignmentName>AccountAssignmentName</core:AccountAssignmentName>
                                        <core:AccountAssignmentValue>AccountAssignmentValue</core:AccountAssignmentValue>
                                    </core:AccountAssignment>
                                </core:ListOfAccountAssignment>
                                <core:MonetaryValue>
                                    <core:MonetaryAmount>1055</core:MonetaryAmount>
                                    <core:Currency>
                                        <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                        <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                    </core:Currency>
                                    <core:RateOfExchangeDetail>
                                        <core:ReferenceCurrency>
                                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                        </core:ReferenceCurrency>
                                        <core:TargetCurrency>
                                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                        </core:TargetCurrency>
                                        <core:RateOfExchange>1056</core:RateOfExchange>
                                        <core:InverseRateOfExchange>1057</core:InverseRateOfExchange>
                                        <core:IndicativeIndicator>true</core:IndicativeIndicator>
                                        <core:DateOfRateOfExchange>2003-01-01T00:00:52</core:DateOfRateOfExchange>
                                        <core:ListOfRateOfExchangeReference>
                                            <core:Reference>
                                                <core:RefNum>RefNum</core:RefNum>
                                                <core:RefDate>2003-01-01T00:00:53</core:RefDate>
                                            </core:Reference>
                                        </core:ListOfRateOfExchangeReference>
                                    </core:RateOfExchangeDetail>
                                </core:MonetaryValue>
                                <core:Quantity>
                                    <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/BaseItemReferences/core:ListOfCostCenter/core:CostCenter/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1058</core:QuantityValue>
                                    <core:UnitOfMeasurement>
                                        <core:UOMCoded>1</core:UOMCoded>
                                        <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                    </core:UnitOfMeasurement>
                                </core:Quantity>
                                <core:Percent UnitOfMeasurement="P1">1059</core:Percent>
                            </core:CostCenter>
                        </core:ListOfCostCenter>
                        <core:ListOfItemReferences>
                            <core:ReferenceCoded>
                                <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                                <core:ReferenceTypeCodedOther>ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                                <core:PrimaryReference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:54</core:RefDate>
                                </core:PrimaryReference>
                                <core:SupportingReference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:55</core:RefDate>
                                </core:SupportingReference>
                                <core:SupportingSubReference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:56</core:RefDate>
                                </core:SupportingSubReference>
                                <core:ReferenceDescription>ReferenceDescription</core:ReferenceDescription>
                            </core:ReferenceCoded>
                        </core:ListOfItemReferences>
                    </BaseItemReferences>
                    <CountryOfOrigin>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </CountryOfOrigin>
                    <CountryOfDestination>
                        <core:CountryCoded>AE</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </CountryOfDestination>
                    <FinalRecipient>
                        <core:PartyID>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:PartyID>
                        <core:ListOfIdentifier>
                            <core:Identifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:Identifier>
                        </core:ListOfIdentifier>
                        <core:MDFBusiness>false</core:MDFBusiness>
                        <core:NameAddress>
                            <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                            <core:Name1>Name1</core:Name1>
                            <core:Name2>Name2</core:Name2>
                            <core:Name3>Name3</core:Name3>
                            <core:Identifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:Identifier>
                            <core:POBox POBoxPostalCode="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/FinalRecipient/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/FinalRecipient/core:NameAddress/core:POBox</core:POBox>
                            <core:Street>Street</core:Street>
                            <core:HouseNumber>HouseNumber</core:HouseNumber>
                            <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                            <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                            <core:Building>Building</core:Building>
                            <core:Floor>Floor</core:Floor>
                            <core:RoomNumber>RoomNumber</core:RoomNumber>
                            <core:InhouseMail>InhouseMail</core:InhouseMail>
                            <core:Department>Department</core:Department>
                            <core:PostalCode>PostalCode</core:PostalCode>
                            <core:City>City</core:City>
                            <core:County>County</core:County>
                            <core:Region>
                                <core:RegionCoded>ARA</core:RegionCoded>
                                <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                            </core:Region>
                            <core:District>District</core:District>
                            <core:Country>
                                <core:CountryCoded>AE</core:CountryCoded>
                                <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                            </core:Country>
                            <core:Timezone>
                                <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                            </core:Timezone>
                        </core:NameAddress>
                        <core:PrimaryContact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:PrimaryContact>
                        <core:OtherContacts>
                            <core:Contact>
                                <core:ContactID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:ContactID>
                                <core:ContactName>ContactName</core:ContactName>
                                <core:ContactFunction>
                                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                    <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                </core:ContactFunction>
                                <core:ContactDescription>ContactDescription</core:ContactDescription>
                                <core:ListOfContactNumber>
                                    <core:ContactNumber>
                                        <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                        <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                    </core:ContactNumber>
                                </core:ListOfContactNumber>
                                <core:CorrespondenceLanguage>
                                    <core:LanguageCoded>aa</core:LanguageCoded>
                                    <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                                    <core:LocaleCoded>ae</core:LocaleCoded>
                                    <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                                </core:CorrespondenceLanguage>
                            </core:Contact>
                        </core:OtherContacts>
                        <core:PartyTaxInformation>
                            <core:TaxIdentifier>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:TaxIdentifier>
                            <core:RegisteredName>RegisteredName</core:RegisteredName>
                            <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                            <core:TaxLocation>
                                <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                <core:LocationIdentifier>
                                    <core:LocID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>Ident</core:Ident>
                                    </core:LocID>
                                    <core:LocationDescription>LocationDescription</core:LocationDescription>
                                </core:LocationIdentifier>
                                <core:GPSCoordinates>
                                    <core:GPSSystem>GPSSystem</core:GPSSystem>
                                    <core:Latitude>Latitude</core:Latitude>
                                    <core:Longitude>Longitude</core:Longitude>
                                </core:GPSCoordinates>
                            </core:TaxLocation>
                            <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                        </core:PartyTaxInformation>
                    </FinalRecipient>
                    <ListOfPartyCoded>
                        <core:PartyCoded>
                            <core:PartyID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:PartyID>
                            <core:ListOfIdentifier>
                                <core:Identifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:Identifier>
                            </core:ListOfIdentifier>
                            <core:MDFBusiness>true</core:MDFBusiness>
                            <core:NameAddress>
                                <core:ExternalAddressID>ExternalAddressID</core:ExternalAddressID>
                                <core:Name1>Name1</core:Name1>
                                <core:Name2>Name2</core:Name2>
                                <core:Name3>Name3</core:Name3>
                                <core:Identifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:Identifier>
                                <core:POBox POBoxPostalCode="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox</core:POBox>
                                <core:Street>Street</core:Street>
                                <core:HouseNumber>HouseNumber</core:HouseNumber>
                                <core:StreetSupplement1>StreetSupplement1</core:StreetSupplement1>
                                <core:StreetSupplement2>StreetSupplement2</core:StreetSupplement2>
                                <core:Building>Building</core:Building>
                                <core:Floor>Floor</core:Floor>
                                <core:RoomNumber>RoomNumber</core:RoomNumber>
                                <core:InhouseMail>InhouseMail</core:InhouseMail>
                                <core:Department>Department</core:Department>
                                <core:PostalCode>PostalCode</core:PostalCode>
                                <core:City>City</core:City>
                                <core:County>County</core:County>
                                <core:Region>
                                    <core:RegionCoded>ARA</core:RegionCoded>
                                    <core:RegionCodedOther>RegionCodedOther</core:RegionCodedOther>
                                </core:Region>
                                <core:District>District</core:District>
                                <core:Country>
                                    <core:CountryCoded>AE</core:CountryCoded>
                                    <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                                </core:Country>
                                <core:Timezone>
                                    <core:TimezoneCoded>12.00</core:TimezoneCoded>
                                    <core:TimezoneCodedOther>TimezoneCodedOther</core:TimezoneCodedOther>
                                </core:Timezone>
                            </core:NameAddress>
                            <core:PrimaryContact>
                                <core:ContactID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:ContactID>
                                <core:ContactName>ContactName</core:ContactName>
                                <core:ContactFunction>
                                    <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                    <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                </core:ContactFunction>
                                <core:ContactDescription>ContactDescription</core:ContactDescription>
                                <core:ListOfContactNumber>
                                    <core:ContactNumber>
                                        <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                        <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                    </core:ContactNumber>
                                </core:ListOfContactNumber>
                                <core:CorrespondenceLanguage>
                                    <core:LanguageCoded>aa</core:LanguageCoded>
                                    <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                                    <core:LocaleCoded>ae</core:LocaleCoded>
                                    <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                                </core:CorrespondenceLanguage>
                            </core:PrimaryContact>
                            <core:OtherContacts>
                                <core:Contact>
                                    <core:ContactID>
                                        <core:Agency>
                                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                        </core:Agency>
                                        <core:Ident>Ident</core:Ident>
                                    </core:ContactID>
                                    <core:ContactName>ContactName</core:ContactName>
                                    <core:ContactFunction>
                                        <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                        <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                                    </core:ContactFunction>
                                    <core:ContactDescription>ContactDescription</core:ContactDescription>
                                    <core:ListOfContactNumber>
                                        <core:ContactNumber>
                                            <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                            <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                        </core:ContactNumber>
                                    </core:ListOfContactNumber>
                                    <core:CorrespondenceLanguage>
                                        <core:LanguageCoded>aa</core:LanguageCoded>
                                        <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                                        <core:LocaleCoded>ae</core:LocaleCoded>
                                        <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                                    </core:CorrespondenceLanguage>
                                </core:Contact>
                            </core:OtherContacts>
                            <core:PartyTaxInformation>
                                <core:TaxIdentifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:TaxIdentifier>
                                <core:RegisteredName>RegisteredName</core:RegisteredName>
                                <core:RegisteredOffice>RegisteredOffice</core:RegisteredOffice>
                                <core:TaxLocation>
                                    <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                    <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                    <core:LocationIdentifier>
                                        <core:LocID>
                                            <core:Agency>
                                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                            </core:Agency>
                                            <core:Ident>Ident</core:Ident>
                                        </core:LocID>
                                        <core:LocationDescription>LocationDescription</core:LocationDescription>
                                    </core:LocationIdentifier>
                                    <core:GPSCoordinates>
                                        <core:GPSSystem>GPSSystem</core:GPSSystem>
                                        <core:Latitude>Latitude</core:Latitude>
                                        <core:Longitude>Longitude</core:Longitude>
                                    </core:GPSCoordinates>
                                </core:TaxLocation>
                                <core:CompanyRegistrationNumber>CompanyRegistrationNumber</core:CompanyRegistrationNumber>
                            </core:PartyTaxInformation>
                            <core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
                            <core:PartyRoleCodedOther>PartyRoleCodedOther</core:PartyRoleCodedOther>
                        </core:PartyCoded>
                    </ListOfPartyCoded>
                    <ConditionsOfSale>
                        <core:SalesRequirement>
                            <core:SalesRequirementCoded>AddQuantityToMakeMinimumWeightRequirement</core:SalesRequirementCoded>
                            <core:SalesRequirementCodedOther>SalesRequirementCodedOther</core:SalesRequirementCodedOther>
                        </core:SalesRequirement>
                        <core:SalesActionCoded>CancelBalanceThatExceedsSalesActionValues</core:SalesActionCoded>
                        <core:SalesActionCodedOther>SalesActionCodedOther</core:SalesActionCodedOther>
                        <core:SalesActionValue>SalesActionValue</core:SalesActionValue>
                    </ConditionsOfSale>
                    <HazardousMaterials>
                        <core:ListOfHazardousIdentifiers>
                            <core:HazardousIdentifiers>
                                <core:HazardousRegulationsCoded>AAMVA</core:HazardousRegulationsCoded>
                                <core:HazardousRegulationsCodedOther>HazardousRegulationsCodedOther</core:HazardousRegulationsCodedOther>
                                <core:HazardCode>HazardCode</core:HazardCode>
                                <core:CodeExtension>CodeExtension</core:CodeExtension>
                                <core:CodeVersion>CodeVersion</core:CodeVersion>
                                <core:HazardOfficialText>HazardOfficialText</core:HazardOfficialText>
                                <core:TremCardNum>TremCardNum</core:TremCardNum>
                            </core:HazardousIdentifiers>
                        </core:ListOfHazardousIdentifiers>
                        <core:HazardClassCoded>Primary</core:HazardClassCoded>
                        <core:HazardClassCodedOther>HazardClassCodedOther</core:HazardClassCodedOther>
                        <core:HazardousPlacardInformation>
                            <core:HazardousPlacardIdentification>HazardousPlacardIdentification</core:HazardousPlacardIdentification>
                            <core:HazardousPlacardText>HazardousPlacardText</core:HazardousPlacardText>
                        </core:HazardousPlacardInformation>
                        <core:HazardousReferences>
                            <core:ReferenceCoded>
                                <core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber</core:ReferenceTypeCoded>
                                <core:ReferenceTypeCodedOther>ReferenceTypeCodedOther</core:ReferenceTypeCodedOther>
                                <core:PrimaryReference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:57</core:RefDate>
                                </core:PrimaryReference>
                                <core:SupportingReference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:58</core:RefDate>
                                </core:SupportingReference>
                                <core:SupportingSubReference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:00:59</core:RefDate>
                                </core:SupportingSubReference>
                                <core:ReferenceDescription>ReferenceDescription</core:ReferenceDescription>
                            </core:ReferenceCoded>
                        </core:HazardousReferences>
                        <core:HazardousContact>
                            <core:ContactID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:ContactID>
                            <core:ContactName>ContactName</core:ContactName>
                            <core:ContactFunction>
                                <core:ContactFunctionCoded>AcceptingContact</core:ContactFunctionCoded>
                                <core:ContactFunctionCodedOther>ContactFunctionCodedOther</core:ContactFunctionCodedOther>
                            </core:ContactFunction>
                            <core:ContactDescription>ContactDescription</core:ContactDescription>
                            <core:ListOfContactNumber>
                                <core:ContactNumber>
                                    <core:ContactNumberValue>ContactNumberValue</core:ContactNumberValue>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberTypeCodedOther>ContactNumberTypeCodedOther</core:ContactNumberTypeCodedOther>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                            <core:CorrespondenceLanguage>
                                <core:LanguageCoded>aa</core:LanguageCoded>
                                <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                                <core:LocaleCoded>ae</core:LocaleCoded>
                                <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                            </core:CorrespondenceLanguage>
                        </core:HazardousContact>
                        <core:HazardNote>HazardNote</core:HazardNote>
                        <core:UNDGNum>1060</core:UNDGNum>
                        <core:HazardousTemperatures>
                            <core:Flashpoint>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:Flashpoint/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:Flashpoint/core:MeasurementValue/@ConditionsCodedOther">1061</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Flashpoint>
                            <core:Emergency>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:Emergency/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:Emergency/core:MeasurementValue/@ConditionsCodedOther">1062</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Emergency>
                            <core:Control>
                                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:Control/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:Control/core:MeasurementValue/@ConditionsCodedOther">1063</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Control>
                            <core:ListOfTemperatureCoded>
                                <core:Dimension>
                                    <core:Measurement>
                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:ListOfTemperatureCoded/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/BaseItemDetail/HazardousMaterials/core:HazardousTemperatures/core:ListOfTemperatureCoded/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1064</core:MeasurementValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Measurement>
                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                    <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                                </core:Dimension>
                            </core:ListOfTemperatureCoded>
                        </core:HazardousTemperatures>
                        <core:HazardousShipmentInformation>
                            <core:HazardPackingCoded>NotSpecified</core:HazardPackingCoded>
                            <core:HazardPackingCodedOther>HazardPackingCodedOther</core:HazardPackingCodedOther>
                            <core:HazardousShipmentCoded>AdditionalDescriptiveInformationNotRequiredByRegulationButDesiredToAccompanyTheMovementByTheShipper</core:HazardousShipmentCoded>
                            <core:HazardousShipmentCodedOther>HazardousShipmentCodedOther</core:HazardousShipmentCodedOther>
                            <core:HazardousShipmentNote>HazardousShipmentNote</core:HazardousShipmentNote>
                            <core:HazardousZoneCoded>HazardZoneA</core:HazardousZoneCoded>
                            <core:HazardousZoneCodedOther>HazardousZoneCodedOther</core:HazardousZoneCodedOther>
                        </core:HazardousShipmentInformation>
                        <core:EMSNum>EMSNum</core:EMSNum>
                        <core:Mfag>Mfag</core:Mfag>
                    </HazardousMaterials>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CancellationPrice</core:PriceTypeCoded>
                                <core:PriceTypeCodedOther>PriceTypeCodedOther</core:PriceTypeCodedOther>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1065</core:UnitPriceValue>
                                <core:Currency>
                                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                </core:Currency>
                            </core:UnitPrice>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>1</core:UOMCoded>
                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:CalculatedPriceBasisQuantity>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/PricingDetail/core:ListOfPrice/core:Price/core:CalculatedPriceBasisQuantity/core:QuantityValue/@SignificanceCodedOther">1066</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:CalculatedPriceBasisQuantity>
                            <core:ValidityDates>
                                <core:StartDate>2003-01-01T00:01:00</core:StartDate>
                                <core:EndDate>2003-01-01T00:01:01</core:EndDate>
                            </core:ValidityDates>
                            <core:PriceQuantityRange>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/PricingDetail/core:ListOfPrice/core:Price/core:PriceQuantityRange/core:QuantityValue/@SignificanceCodedOther">1067</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:PriceQuantityRange>
                            <core:PriceMultiplier>
                                <core:PriceMultiplierCoded>CostMarkupMultiplier-OriginalCost</core:PriceMultiplierCoded>
                                <core:PriceMultiplierCodedOther>PriceMultiplierCodedOther</core:PriceMultiplierCodedOther>
                                <core:Multiplier>1068</core:Multiplier>
                            </core:PriceMultiplier>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
                        <core:TaxTypeCodedOther>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:TaxTypeCodedOther>
                        <core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem</core:TaxFunctionQualifierCoded>
                        <core:TaxFunctionQualifierCodedOther>TaxFunctionQualifierCodedOther</core:TaxFunctionQualifierCodedOther>
                        <core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
                        <core:TaxCategoryCodedOther>TaxCategoryCodedOther</core:TaxCategoryCodedOther>
                        <core:ReasonTaxExemptCoded>Exempt-ForExport</core:ReasonTaxExemptCoded>
                        <core:ReasonTaxExemptCodedOther>ReasonTaxExemptCodedOther</core:ReasonTaxExemptCodedOther>
                        <core:TaxPercent UnitOfMeasurement="P1">1069</core:TaxPercent>
                        <core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
                        <core:TaxPaymentMethodCodedOther>TaxPaymentMethodCodedOther</core:TaxPaymentMethodCodedOther>
                        <core:TaxableAmount>1070</core:TaxableAmount>
                        <core:TaxableAmountInTaxAccountingCurrency>1071</core:TaxableAmountInTaxAccountingCurrency>
                        <core:TaxAmount>1072</core:TaxAmount>
                        <core:TaxAmountInTaxAccountingCurrency>1073</core:TaxAmountInTaxAccountingCurrency>
                        <core:TaxLocation>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                <core:Latitude>Latitude</core:Latitude>
                                <core:Longitude>Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:TaxLocation>
                    </core:Tax>
                    <core:TotalTaxAmount>
                        <core:MonetaryAmount>1074</core:MonetaryAmount>
                        <core:Currency>
                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                        </core:Currency>
                        <core:RateOfExchangeDetail>
                            <core:ReferenceCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:ReferenceCurrency>
                            <core:TargetCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:TargetCurrency>
                            <core:RateOfExchange>1075</core:RateOfExchange>
                            <core:InverseRateOfExchange>1076</core:InverseRateOfExchange>
                            <core:IndicativeIndicator>false</core:IndicativeIndicator>
                            <core:DateOfRateOfExchange>2003-01-01T00:01:02</core:DateOfRateOfExchange>
                            <core:ListOfRateOfExchangeReference>
                                <core:Reference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:01:03</core:RefDate>
                                </core:Reference>
                            </core:ListOfRateOfExchangeReference>
                        </core:RateOfExchangeDetail>
                    </core:TotalTaxAmount>
                    <core:ItemAllowancesOrCharges>
                        <core:AllowOrCharge>
                            <core:IndicatorCoded>About</core:IndicatorCoded>
                            <core:IndicatorCodedOther>IndicatorCodedOther</core:IndicatorCodedOther>
                            <core:BasisCoded>None</core:BasisCoded>
                            <core:BasisCodedOther>BasisCodedOther</core:BasisCodedOther>
                            <core:MethodOfHandlingCoded>BillBack</core:MethodOfHandlingCoded>
                            <core:MethodOfHandlingCodedOther>MethodOfHandlingCodedOther</core:MethodOfHandlingCodedOther>
                            <core:AllowOrChargeTreatment>
                                <core:AllowOrChargeTreatmentCoded>UnitPriceNet</core:AllowOrChargeTreatmentCoded>
                                <core:AllowOrChargeTreatmentCodedOther>AllowOrChargeTreatmentCodedOther</core:AllowOrChargeTreatmentCodedOther>
                            </core:AllowOrChargeTreatment>
                            <core:AllowanceOrChargeDescription>
                                <core:RefID>RefID</core:RefID>
                                <core:ListOfDescription>ListOfDescription</core:ListOfDescription>
                                <core:ServiceCoded>About</core:ServiceCoded>
                                <core:ServiceCodedOther>ServiceCodedOther</core:ServiceCodedOther>
                            </core:AllowanceOrChargeDescription>
                            <core:ValidityDates>
                                <core:StartDate>2003-01-01T00:01:04</core:StartDate>
                                <core:EndDate>2003-01-01T00:01:05</core:EndDate>
                            </core:ValidityDates>
                            <core:BasisQuantityRange>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/PricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:BasisQuantityRange/core:QuantityValue/@SignificanceCodedOther">1077</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:BasisQuantityRange>
                            <core:TypeOfAllowanceOrCharge>
                                <core:QuantityAllowanceOrCharge>
                                    <core:Quantity>
                                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/PricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1078</core:QuantityValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Quantity>
                                    <core:Rate>
                                        <core:RatePerUnit>
                                            <core:UnitPriceValue>1079</core:UnitPriceValue>
                                            <core:Currency>
                                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                            </core:Currency>
                                        </core:RatePerUnit>
                                        <core:UnitPriceBasis>1080</core:UnitPriceBasis>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:Rate>
                                    <core:QuantityMonetaryValue>
                                        <core:MonetaryAmount>1081</core:MonetaryAmount>
                                        <core:Currency>
                                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                        </core:Currency>
                                        <core:RateOfExchangeDetail>
                                            <core:ReferenceCurrency>
                                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                            </core:ReferenceCurrency>
                                            <core:TargetCurrency>
                                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                                            </core:TargetCurrency>
                                            <core:RateOfExchange>1082</core:RateOfExchange>
                                            <core:InverseRateOfExchange>1083</core:InverseRateOfExchange>
                                            <core:IndicativeIndicator>true</core:IndicativeIndicator>
                                            <core:DateOfRateOfExchange>2003-01-01T00:01:06</core:DateOfRateOfExchange>
                                            <core:ListOfRateOfExchangeReference>
                                                <core:Reference>
                                                    <core:RefNum>RefNum</core:RefNum>
                                                    <core:RefDate>2003-01-01T00:01:07</core:RefDate>
                                                </core:Reference>
                                            </core:ListOfRateOfExchangeReference>
                                        </core:RateOfExchangeDetail>
                                    </core:QuantityMonetaryValue>
                                </core:QuantityAllowanceOrCharge>
                            </core:TypeOfAllowanceOrCharge>
                            <core:Tax>
                                <core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
                                <core:TaxTypeCodedOther>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:TaxTypeCodedOther>
                                <core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem</core:TaxFunctionQualifierCoded>
                                <core:TaxFunctionQualifierCodedOther>TaxFunctionQualifierCodedOther</core:TaxFunctionQualifierCodedOther>
                                <core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
                                <core:TaxCategoryCodedOther>TaxCategoryCodedOther</core:TaxCategoryCodedOther>
                                <core:ReasonTaxExemptCoded>Exempt-ForExport</core:ReasonTaxExemptCoded>
                                <core:ReasonTaxExemptCodedOther>ReasonTaxExemptCodedOther</core:ReasonTaxExemptCodedOther>
                                <core:TaxPercent UnitOfMeasurement="P1">1084</core:TaxPercent>
                                <core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
                                <core:TaxPaymentMethodCodedOther>TaxPaymentMethodCodedOther</core:TaxPaymentMethodCodedOther>
                                <core:TaxableAmount>1085</core:TaxableAmount>
                                <core:TaxableAmountInTaxAccountingCurrency>1086</core:TaxableAmountInTaxAccountingCurrency>
                                <core:TaxAmount>1087</core:TaxAmount>
                                <core:TaxAmountInTaxAccountingCurrency>1088</core:TaxAmountInTaxAccountingCurrency>
                                <core:TaxLocation>
                                    <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                    <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                    <core:LocationIdentifier>
                                        <core:LocID>
                                            <core:Agency>
                                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                            </core:Agency>
                                            <core:Ident>Ident</core:Ident>
                                        </core:LocID>
                                        <core:LocationDescription>LocationDescription</core:LocationDescription>
                                    </core:LocationIdentifier>
                                    <core:GPSCoordinates>
                                        <core:GPSSystem>GPSSystem</core:GPSSystem>
                                        <core:Latitude>Latitude</core:Latitude>
                                        <core:Longitude>Longitude</core:Longitude>
                                    </core:GPSCoordinates>
                                </core:TaxLocation>
                            </core:Tax>
                        </core:AllowOrCharge>
                    </core:ItemAllowancesOrCharges>
                    <core:LineItemSubTotal>
                        <core:MonetaryAmount>1089</core:MonetaryAmount>
                        <core:Currency>
                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                        </core:Currency>
                        <core:RateOfExchangeDetail>
                            <core:ReferenceCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:ReferenceCurrency>
                            <core:TargetCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:TargetCurrency>
                            <core:RateOfExchange>1090</core:RateOfExchange>
                            <core:InverseRateOfExchange>1091</core:InverseRateOfExchange>
                            <core:IndicativeIndicator>false</core:IndicativeIndicator>
                            <core:DateOfRateOfExchange>2003-01-01T00:01:08</core:DateOfRateOfExchange>
                            <core:ListOfRateOfExchangeReference>
                                <core:Reference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:01:09</core:RefDate>
                                </core:Reference>
                            </core:ListOfRateOfExchangeReference>
                        </core:RateOfExchangeDetail>
                    </core:LineItemSubTotal>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1092</core:MonetaryAmount>
                        <core:Currency>
                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                        </core:Currency>
                        <core:RateOfExchangeDetail>
                            <core:ReferenceCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:ReferenceCurrency>
                            <core:TargetCurrency>
                                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                            </core:TargetCurrency>
                            <core:RateOfExchange>1093</core:RateOfExchange>
                            <core:InverseRateOfExchange>1094</core:InverseRateOfExchange>
                            <core:IndicativeIndicator>true</core:IndicativeIndicator>
                            <core:DateOfRateOfExchange>2003-01-01T00:01:10</core:DateOfRateOfExchange>
                            <core:ListOfRateOfExchangeReference>
                                <core:Reference>
                                    <core:RefNum>RefNum</core:RefNum>
                                    <core:RefDate>2003-01-01T00:01:11</core:RefDate>
                                </core:Reference>
                            </core:ListOfRateOfExchangeReference>
                        </core:RateOfExchangeDetail>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ShipToLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:ShipToLocation>
                    <core:ShipFromLocation>
                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Agency>
                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                </core:Agency>
                                <core:Ident>Ident</core:Ident>
                            </core:LocID>
                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                        </core:LocationIdentifier>
                        <core:GPSCoordinates>
                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                            <core:Latitude>Latitude</core:Latitude>
                            <core:Longitude>Longitude</core:Longitude>
                        </core:GPSCoordinates>
                    </core:ShipFromLocation>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:ScheduleLineID>1095</core:ScheduleLineID>
                            <core:Quantity>
                                <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1096</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>1</core:UOMCoded>
                                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2003-01-01T00:01:12</core:RequestedDeliveryDate>
                            <core:ScheduleLineNote>ScheduleLineNote</core:ScheduleLineNote>
                            <core:TransportRouting>
                                <core:TransportRouteID>1097</core:TransportRouteID>
                                <core:TransportMode>
                                    <core:TransportModeCoded>Air</core:TransportModeCoded>
                                    <core:TransportModeCodedOther>TransportModeCodedOther</core:TransportModeCodedOther>
                                </core:TransportMode>
                                <core:TransportMeans>
                                    <core:TransportMeansCoded>20FtILContainer-ClosedTop</core:TransportMeansCoded>
                                    <core:TransportMeansCodedOther>TransportMeansCodedOther</core:TransportMeansCodedOther>
                                </core:TransportMeans>
                                <core:TransportMeansIdentifier>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:TransportMeansIdentifier>
                                <core:TransportMeansReference>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:TransportMeansReference>
                                <core:TransportRequirementCoded>SpecifiedFlagRequired</core:TransportRequirementCoded>
                                <core:TransportRequirementCodedOther>TransportRequirementCodedOther</core:TransportRequirementCodedOther>
                                <core:CarrierName>CarrierName</core:CarrierName>
                                <core:CarrierID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:CarrierID>
                                <core:TransportQuantities>
                                    <core:LadingQuantity>
                                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1098</core:QuantityValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:LadingQuantity>
                                    <core:ListOfQuantityCoded>
                                        <core:QuantityCoded>
                                            <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1099</core:QuantityValue>
                                            <core:UnitOfMeasurement>
                                                <core:UOMCoded>1</core:UOMCoded>
                                                <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                            </core:UnitOfMeasurement>
                                            <core:QuantityQualifierCoded>AcceptableUnserviceableQuantity</core:QuantityQualifierCoded>
                                            <core:QuantityQualifierCodedOther>QuantityQualifierCodedOther</core:QuantityQualifierCodedOther>
                                        </core:QuantityCoded>
                                    </core:ListOfQuantityCoded>
                                </core:TransportQuantities>
                                <core:CustShippingContractNum>CustShippingContractNum</core:CustShippingContractNum>
                                <core:ServiceLevel>
                                    <core:ServiceLevelCoded>BulkCommodityTrain</core:ServiceLevelCoded>
                                    <core:ServiceLevelCodedOther>ServiceLevelCodedOther</core:ServiceLevelCodedOther>
                                    <core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder</core:ServiceLevelReasonCoded>
                                    <core:ServiceLevelReasonCodedOther>ServiceLevelReasonCodedOther</core:ServiceLevelReasonCodedOther>
                                    <core:ServiceLevelResponsibilityCoded>CustomerPlant</core:ServiceLevelResponsibilityCoded>
                                    <core:ServiceLevelResponsibilityCodedOther>ServiceLevelResponsibilityCodedOther</core:ServiceLevelResponsibilityCodedOther>
                                </core:ServiceLevel>
                                <core:ShippingInstructions>ShippingInstructions</core:ShippingInstructions>
                                <core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
                                <core:TransportLegCodedOther>TransportLegCodedOther</core:TransportLegCodedOther>
                                <core:ListOfTransportEquipment>
                                    <core:TransportEquipment>
                                        <core:EquipmentProviderCoded>ShipperSupplied</core:EquipmentProviderCoded>
                                        <core:EquipmentProviderCodedOther>EquipmentProviderCodedOther</core:EquipmentProviderCodedOther>
                                        <core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased</core:EquipmentOwnerCoded>
                                        <core:EquipmentOwnerCodedOther>EquipmentOwnerCodedOther</core:EquipmentOwnerCodedOther>
                                        <core:EquipmentID>EquipmentID</core:EquipmentID>
                                        <core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop</core:EquipmentSizeTypeCoded>
                                        <core:EquipementSizeTypeCodedOther>EquipementSizeTypeCodedOther</core:EquipementSizeTypeCodedOther>
                                        <core:EquipmentStatusCoded>Continental</core:EquipmentStatusCoded>
                                        <core:EquipmentStatusCodedOther>EquipmentStatusCodedOther</core:EquipmentStatusCodedOther>
                                        <core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
                                        <core:FullIndicatorCodedOther>FullIndicatorCodedOther</core:FullIndicatorCodedOther>
                                        <core:Conditions>
                                            <core:RefrigerationOn>false</core:RefrigerationOn>
                                            <core:Residue>true</core:Residue>
                                            <core:ListOfConditions>
                                                <core:Dimension>
                                                    <core:Measurement>
                                                        <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1100</core:MeasurementValue>
                                                        <core:UnitOfMeasurement>
                                                            <core:UOMCoded>1</core:UOMCoded>
                                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                                        </core:UnitOfMeasurement>
                                                    </core:Measurement>
                                                    <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                                    <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                                                </core:Dimension>
                                            </core:ListOfConditions>
                                        </core:Conditions>
                                        <core:EquipmentNote>EquipmentNote</core:EquipmentNote>
                                        <core:ListOfSealInfo>
                                            <core:SealInfo>
                                                <core:SealNumber>SealNumber</core:SealNumber>
                                                <core:SealIssuer>
                                                    <core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
                                                    <core:SealIssuerCodedOther>SealIssuerCodedOther</core:SealIssuerCodedOther>
                                                </core:SealIssuer>
                                                <core:SealStatusDescription>
                                                    <core:SealStatusCoded>Broken</core:SealStatusCoded>
                                                    <core:SealStatusCodedOther>SealStatusCodedOther</core:SealStatusCodedOther>
                                                    <core:SealStatusInfo>SealStatusInfo</core:SealStatusInfo>
                                                </core:SealStatusDescription>
                                            </core:SealInfo>
                                        </core:ListOfSealInfo>
                                        <core:ListOfEquipmentMeasurements>
                                            <core:Dimension>
                                                <core:Measurement>
                                                    <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1101</core:MeasurementValue>
                                                    <core:UnitOfMeasurement>
                                                        <core:UOMCoded>1</core:UOMCoded>
                                                        <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                                    </core:UnitOfMeasurement>
                                                </core:Measurement>
                                                <core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
                                                <core:DimensionCodedOther>DimensionCodedOther</core:DimensionCodedOther>
                                            </core:Dimension>
                                        </core:ListOfEquipmentMeasurements>
                                    </core:TransportEquipment>
                                </core:ListOfTransportEquipment>
                                <core:TransitDirection>
                                    <core:TransitDirectionCoded>BuyerToSeller</core:TransitDirectionCoded>
                                    <core:TransitDirectionCodedOther>TransitDirectionCodedOther</core:TransitDirectionCodedOther>
                                    <core:TransitTimeQualifierCoded>AirHours</core:TransitTimeQualifierCoded>
                                    <core:TransitTimeQualifierCodedOther>TransitTimeQualifierCodedOther</core:TransitTimeQualifierCodedOther>
                                    <core:TransitTime>1102</core:TransitTime>
                                </core:TransitDirection>
                                <core:TransportLocationList>
                                    <core:StartTransportLocation>
                                        <core:Location>
                                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                            <core:LocationIdentifier>
                                                <core:LocID>
                                                    <core:Agency>
                                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                    </core:Agency>
                                                    <core:Ident>Ident</core:Ident>
                                                </core:LocID>
                                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                                            </core:LocationIdentifier>
                                            <core:GPSCoordinates>
                                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                                <core:Latitude>Latitude</core:Latitude>
                                                <core:Longitude>Longitude</core:Longitude>
                                            </core:GPSCoordinates>
                                        </core:Location>
                                        <core:LocationID>1103</core:LocationID>
                                        <core:Sequence>1104</core:Sequence>
                                        <core:EstimatedArrivalDate>2003-01-01T00:01:13</core:EstimatedArrivalDate>
                                        <core:ActualArrivalDate>2003-01-01T00:01:14</core:ActualArrivalDate>
                                        <core:EstimatedDepartureDate>2003-01-01T00:01:15</core:EstimatedDepartureDate>
                                        <core:ActualDepartureDate>2003-01-01T00:01:16</core:ActualDepartureDate>
                                    </core:StartTransportLocation>
                                    <core:InterimTransportLocation>
                                        <core:Location>
                                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                            <core:LocationIdentifier>
                                                <core:LocID>
                                                    <core:Agency>
                                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                    </core:Agency>
                                                    <core:Ident>Ident</core:Ident>
                                                </core:LocID>
                                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                                            </core:LocationIdentifier>
                                            <core:GPSCoordinates>
                                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                                <core:Latitude>Latitude</core:Latitude>
                                                <core:Longitude>Longitude</core:Longitude>
                                            </core:GPSCoordinates>
                                        </core:Location>
                                        <core:LocationID>1105</core:LocationID>
                                        <core:Sequence>1106</core:Sequence>
                                        <core:EstimatedArrivalDate>2003-01-01T00:01:17</core:EstimatedArrivalDate>
                                        <core:ActualArrivalDate>2003-01-01T00:01:18</core:ActualArrivalDate>
                                        <core:EstimatedDepartureDate>2003-01-01T00:01:19</core:EstimatedDepartureDate>
                                        <core:ActualDepartureDate>2003-01-01T00:01:20</core:ActualDepartureDate>
                                    </core:InterimTransportLocation>
                                    <core:EndTransportLocation>
                                        <core:Location>
                                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                            <core:LocationIdentifier>
                                                <core:LocID>
                                                    <core:Agency>
                                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                    </core:Agency>
                                                    <core:Ident>Ident</core:Ident>
                                                </core:LocID>
                                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                                            </core:LocationIdentifier>
                                            <core:GPSCoordinates>
                                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                                <core:Latitude>Latitude</core:Latitude>
                                                <core:Longitude>Longitude</core:Longitude>
                                            </core:GPSCoordinates>
                                        </core:Location>
                                        <core:LocationID>1107</core:LocationID>
                                        <core:Sequence>1108</core:Sequence>
                                        <core:EstimatedArrivalDate>2003-01-01T00:01:21</core:EstimatedArrivalDate>
                                        <core:ActualArrivalDate>2003-01-01T00:01:22</core:ActualArrivalDate>
                                        <core:EstimatedDepartureDate>2003-01-01T00:01:23</core:EstimatedDepartureDate>
                                        <core:ActualDepartureDate>2003-01-01T00:01:24</core:ActualDepartureDate>
                                    </core:EndTransportLocation>
                                </core:TransportLocationList>
                            </core:TransportRouting>
                            <core:ListOfShipToSubInformation>
                                <core:ShipToSubInformation>
                                    <core:ShipToSubLocation>
                                        <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                                        <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                                        <core:LocationIdentifier>
                                            <core:LocID>
                                                <core:Agency>
                                                    <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                                    <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                                    <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                                    <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                                    <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                                </core:Agency>
                                                <core:Ident>Ident</core:Ident>
                                            </core:LocID>
                                            <core:LocationDescription>LocationDescription</core:LocationDescription>
                                        </core:LocationIdentifier>
                                        <core:GPSCoordinates>
                                            <core:GPSSystem>GPSSystem</core:GPSSystem>
                                            <core:Latitude>Latitude</core:Latitude>
                                            <core:Longitude>Longitude</core:Longitude>
                                        </core:GPSCoordinates>
                                    </core:ShipToSubLocation>
                                    <core:ShipToSubQuantity>
                                        <core:QuantityValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderDetail/ListOfItemDetail/ItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubQuantity/core:QuantityValue/@SignificanceCodedOther">1109</core:QuantityValue>
                                        <core:UnitOfMeasurement>
                                            <core:UOMCoded>1</core:UOMCoded>
                                            <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                                        </core:UnitOfMeasurement>
                                    </core:ShipToSubQuantity>
                                </core:ShipToSubInformation>
                            </core:ListOfShipToSubInformation>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                    <core:SimplePackageNote>SimplePackageNote</core:SimplePackageNote>
                    <core:TermsOfDelivery>
                        <core:TermsOfDeliveryFunctionCoded>PriceCondition</core:TermsOfDeliveryFunctionCoded>
                        <core:TermsOfDeliveryFunctionCodedOther>TermsOfDeliveryFunctionCodedOther</core:TermsOfDeliveryFunctionCodedOther>
                        <core:TransportTermsCoded>Ex-Works</core:TransportTermsCoded>
                        <core:TransportTermsCodedOther>TransportTermsCodedOther</core:TransportTermsCodedOther>
                        <core:ShipmentMethodOfPaymentCoded>Account</core:ShipmentMethodOfPaymentCoded>
                        <core:ShipmentMethodOfPaymentCodedOther>ShipmentMethodOfPaymentCodedOther</core:ShipmentMethodOfPaymentCodedOther>
                        <core:Location>
                            <core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling</core:LocationQualifierCoded>
                            <core:LocationQualifierCodedOther>LocationQualifierCodedOther</core:LocationQualifierCodedOther>
                            <core:LocationIdentifier>
                                <core:LocID>
                                    <core:Agency>
                                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                                    </core:Agency>
                                    <core:Ident>Ident</core:Ident>
                                </core:LocID>
                                <core:LocationDescription>LocationDescription</core:LocationDescription>
                            </core:LocationIdentifier>
                            <core:GPSCoordinates>
                                <core:GPSSystem>GPSSystem</core:GPSSystem>
                                <core:Latitude>Latitude</core:Latitude>
                                <core:Longitude>Longitude</core:Longitude>
                            </core:GPSCoordinates>
                        </core:Location>
                        <core:TermsOfDeliveryDescription>TermsOfDeliveryDescription</core:TermsOfDeliveryDescription>
                        <core:TransportDescription>TransportDescription</core:TransportDescription>
                        <core:RiskOfLossCoded>RiskOfLossInTransitToDestinationIsBuyers</core:RiskOfLossCoded>
                        <core:RiskOfLossCodedOther>RiskOfLossCodedOther</core:RiskOfLossCodedOther>
                        <core:RiskOfLossDescription>RiskOfLossDescription</core:RiskOfLossDescription>
                    </core:TermsOfDelivery>
                    <core:CargoClassification>
                        <core:NatureOfGoods>
                            <core:Agency>
                                <core:AgencyCoded>AAMVA</core:AgencyCoded>
                                <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                                <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                                <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>Ident</core:Ident>
                        </core:NatureOfGoods>
                        <core:OperationalTypeCoded>Documents</core:OperationalTypeCoded>
                        <core:OperationalTypeCodedOther>OperationalTypeCodedOther</core:OperationalTypeCodedOther>
                        <core:TypeOfCargo>TypeOfCargo</core:TypeOfCargo>
                    </core:CargoClassification>
                </DeliveryDetail>
                <RoundTripInformation>
                    <core:Immutable>false</core:Immutable>
                    <core:ReLink>ReLink</core:ReLink>
                    <core:ReLinkID>ReLinkID</core:ReLinkID>
                    <core:SellerShoppingCartID>SellerShoppingCartID</core:SellerShoppingCartID>
                </RoundTripInformation>
                <LineItemNote>LineItemNote</LineItemNote>
                <SpecialHandling>
                    <core:SpecialHandlingCoded>SpeedRestrictedTo25MilesPerHour</core:SpecialHandlingCoded>
                    <core:SpecialHandlingCodedOther>SpecialHandlingCodedOther</core:SpecialHandlingCodedOther>
                    <core:SpecialHandlingNote>SpecialHandlingNote</core:SpecialHandlingNote>
                </SpecialHandling>
                <ListOfStructuredNote>
                    <core:StructuredNote>
                        <core:GeneralNote>GeneralNote</core:GeneralNote>
                        <core:NoteID>NoteID</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>AAMVA</core:AgencyCoded>
                            <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                            <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                            <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:NoteURL>NoteURL</core:NoteURL>
                        <core:TextTypeCoded>Deadlines</core:TextTypeCoded>
                        <core:TextTypeCodedOther>TextTypeCodedOther</core:TextTypeCodedOther>
                    </core:StructuredNote>
                </ListOfStructuredNote>
                <ListOfNameValueSet>
                    <core:NameValueSet>
                        <core:SetName>SetName</core:SetName>
                        <core:SetID>SetID</core:SetID>
                        <core:ListOfNameValuePair>
                            <core:NameValuePair>
                                <core:Name>Name</core:Name>
                                <core:Value>Value</core:Value>
                                <core:Datatype>Datatype</core:Datatype>
                            </core:NameValuePair>
                        </core:ListOfNameValuePair>
                    </core:NameValueSet>
                </ListOfNameValueSet>
                <LineItemAttachments>
                    <core:Attachment>
                        <core:AttachmentActionCoded>Add</core:AttachmentActionCoded>
                        <core:AttachmentActionCodedOther>AttachmentActionCodedOther</core:AttachmentActionCodedOther>
                        <core:AttachmentPurpose>AttachmentPurpose</core:AttachmentPurpose>
                        <core:FileName>FileName</core:FileName>
                        <core:AttachmentTitle>AttachmentTitle</core:AttachmentTitle>
                        <core:AttachmentDescription>AttachmentDescription</core:AttachmentDescription>
                        <core:Language>
                            <core:LanguageCoded>aa</core:LanguageCoded>
                            <core:LanguageCodedOther>LanguageCodedOther</core:LanguageCodedOther>
                            <core:LocaleCoded>ae</core:LocaleCoded>
                            <core:LocaleCodedOther>LocaleCodedOther</core:LocaleCodedOther>
                        </core:Language>
                        <core:MIMEType>MIMEType</core:MIMEType>
                        <core:AttachmentLocation>AttachmentLocation</core:AttachmentLocation>
                    </core:Attachment>
                </LineItemAttachments>
            </ItemDetail>
        </ListOfItemDetail>
    </OrderDetail>
    <OrderSummary>
        <NumberOfLines>1110</NumberOfLines>
        <ListOfTaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
                <core:TaxTypeCodedOther>
                    <core:Agency>
                        <core:AgencyCoded>AAMVA</core:AgencyCoded>
                        <core:AgencyCodedOther>AgencyCodedOther</core:AgencyCodedOther>
                        <core:AgencyDescription>AgencyDescription</core:AgencyDescription>
                        <core:CodeListIdentifierCoded>AcceptanceSiteCode</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>CodeListIdentifierCodedOther</core:CodeListIdentifierCodedOther>
                    </core:Agency>
                    <core:Ident>Ident</core:Ident>
                </core:TaxTypeCodedOther>
                <core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem</core:TaxFunctionQualifierCoded>
                <core:TaxFunctionQualifierCodedOther>TaxFunctionQualifierCodedOther</core:TaxFunctionQualifierCodedOther>
                <core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>TaxCategoryCodedOther</core:TaxCategoryCodedOther>
                <core:TaxableAmount>1111</core:TaxableAmount>
                <core:TaxableAmountInTaxAccountingCurrency>1112</core:TaxableAmountInTaxAccountingCurrency>
                <core:TaxAmount>1113</core:TaxAmount>
                <core:TaxAmountInTaxAccountingCurrency>1114</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
        </ListOfTaxSummary>
        <AllowOrChargeSummary>
            <core:TotalAllowOrCharge>
                <core:AllowOrChargeIndicatorCoded>Allowance</core:AllowOrChargeIndicatorCoded>
                <core:AllowOrChargeIndicatorCodedOther>AllowOrChargeIndicatorCodedOther</core:AllowOrChargeIndicatorCodedOther>
                <core:AllowanceOrChargeDescription>
                    <core:RefID>RefID</core:RefID>
                    <core:ListOfDescription>ListOfDescription</core:ListOfDescription>
                    <core:ServiceCoded>About</core:ServiceCoded>
                    <core:ServiceCodedOther>ServiceCodedOther</core:ServiceCodedOther>
                </core:AllowanceOrChargeDescription>
                <core:SummaryAllowOrCharge>
                    <core:MonetaryAmount>1115</core:MonetaryAmount>
                    <core:Currency>
                        <core:CurrencyCoded>AFA</core:CurrencyCoded>
                        <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                    </core:Currency>
                    <core:RateOfExchangeDetail>
                        <core:ReferenceCurrency>
                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                        </core:ReferenceCurrency>
                        <core:TargetCurrency>
                            <core:CurrencyCoded>AFA</core:CurrencyCoded>
                            <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                        </core:TargetCurrency>
                        <core:RateOfExchange>1116</core:RateOfExchange>
                        <core:InverseRateOfExchange>1117</core:InverseRateOfExchange>
                        <core:IndicativeIndicator>true</core:IndicativeIndicator>
                        <core:DateOfRateOfExchange>2003-01-01T00:01:25</core:DateOfRateOfExchange>
                        <core:ListOfRateOfExchangeReference>
                            <core:Reference>
                                <core:RefNum>RefNum</core:RefNum>
                                <core:RefDate>2003-01-01T00:01:26</core:RefDate>
                            </core:Reference>
                        </core:ListOfRateOfExchangeReference>
                    </core:RateOfExchangeDetail>
                </core:SummaryAllowOrCharge>
            </core:TotalAllowOrCharge>
        </AllowOrChargeSummary>
        <OrderSubTotal>
            <core:MonetaryAmount>1118</core:MonetaryAmount>
            <core:Currency>
                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
            </core:Currency>
            <core:RateOfExchangeDetail>
                <core:ReferenceCurrency>
                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                </core:ReferenceCurrency>
                <core:TargetCurrency>
                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                </core:TargetCurrency>
                <core:RateOfExchange>1119</core:RateOfExchange>
                <core:InverseRateOfExchange>1120</core:InverseRateOfExchange>
                <core:IndicativeIndicator>false</core:IndicativeIndicator>
                <core:DateOfRateOfExchange>2003-01-01T00:01:27</core:DateOfRateOfExchange>
                <core:ListOfRateOfExchangeReference>
                    <core:Reference>
                        <core:RefNum>RefNum</core:RefNum>
                        <core:RefDate>2003-01-01T00:01:28</core:RefDate>
                    </core:Reference>
                </core:ListOfRateOfExchangeReference>
            </core:RateOfExchangeDetail>
        </OrderSubTotal>
        <OrderTotal>
            <core:MonetaryAmount>1121</core:MonetaryAmount>
            <core:Currency>
                <core:CurrencyCoded>AFA</core:CurrencyCoded>
                <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
            </core:Currency>
            <core:RateOfExchangeDetail>
                <core:ReferenceCurrency>
                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                </core:ReferenceCurrency>
                <core:TargetCurrency>
                    <core:CurrencyCoded>AFA</core:CurrencyCoded>
                    <core:CurrencyCodedOther>CurrencyCodedOther</core:CurrencyCodedOther>
                </core:TargetCurrency>
                <core:RateOfExchange>1122</core:RateOfExchange>
                <core:InverseRateOfExchange>1123</core:InverseRateOfExchange>
                <core:IndicativeIndicator>true</core:IndicativeIndicator>
                <core:DateOfRateOfExchange>2003-01-01T00:01:29</core:DateOfRateOfExchange>
                <core:ListOfRateOfExchangeReference>
                    <core:Reference>
                        <core:RefNum>RefNum</core:RefNum>
                        <core:RefDate>2003-01-01T00:01:30</core:RefDate>
                    </core:Reference>
                </core:ListOfRateOfExchangeReference>
            </core:RateOfExchangeDetail>
        </OrderTotal>
        <TransportPackagingTotals>
            <core:TotalPackages>1124</core:TotalPackages>
            <core:TotalPackageDepth>1125</core:TotalPackageDepth>
            <core:TotalTransport>1126</core:TotalTransport>
            <core:TotalGrossWeight>
                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalGrossWeight/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalGrossWeight/core:MeasurementValue/@ConditionsCodedOther">1127</core:MeasurementValue>
                <core:UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                </core:UnitOfMeasurement>
            </core:TotalGrossWeight>
            <core:TotalNetWeight>
                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalNetWeight/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalNetWeight/core:MeasurementValue/@ConditionsCodedOther">1128</core:MeasurementValue>
                <core:UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                </core:UnitOfMeasurement>
            </core:TotalNetWeight>
            <core:TotalNetNetWeight>
                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalNetNetWeight/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalNetNetWeight/core:MeasurementValue/@ConditionsCodedOther">1129</core:MeasurementValue>
                <core:UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                </core:UnitOfMeasurement>
            </core:TotalNetNetWeight>
            <core:TotalTareWeight>
                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalTareWeight/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderSummary/TransportPackagingTotals/core:TotalTareWeight/core:MeasurementValue/@ConditionsCodedOther">1130</core:MeasurementValue>
                <core:UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                </core:UnitOfMeasurement>
            </core:TotalTareWeight>
            <core:GrossVolume>
                <core:MeasurementValue SignificanceCoded="Approximately" SignificanceCodedOther="Order/OrderSummary/TransportPackagingTotals/core:GrossVolume/core:MeasurementValue/@SignificanceCodedOther" ConditionsCoded="WhereAirEquals1" ConditionsCodedOther="Order/OrderSummary/TransportPackagingTotals/core:GrossVolume/core:MeasurementValue/@ConditionsCodedOther">1131</core:MeasurementValue>
                <core:UnitOfMeasurement>
                    <core:UOMCoded>1</core:UOMCoded>
                    <core:UOMCodedOther>UOMCodedOther</core:UOMCodedOther>
                </core:UnitOfMeasurement>
            </core:GrossVolume>
        </TransportPackagingTotals>
        <SummaryNote>SummaryNote</SummaryNote>
    </OrderSummary>
</Order>