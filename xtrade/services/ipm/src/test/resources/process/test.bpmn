<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="f28250ed-974e-4ecd-bca6-1f41df09011f" name="First" isExecutable="true">
    <sequenceFlow id="link31" sourceRef="Parallel_Gateway3" targetRef="Log2"></sequenceFlow>
    <serviceTask id="Log2" name="Bonjour" activiti:class="com.byzaneo.xtrade.process.task.LogTask">
      <extensionElements>
        <activiti:field name="message">
          <activiti:string>toto</activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <startEvent id="Start-2" name="Start"></startEvent>
    <sequenceFlow id="link-62" sourceRef="Parallel_Gateway-6" targetRef="End2"></sequenceFlow>
    <endEvent id="End2" name="End"></endEvent>
    <sequenceFlow id="link-23" sourceRef="Start-2" targetRef="Parallel_Gateway3"></sequenceFlow>
    <parallelGateway id="Parallel_Gateway3" name="Parallel_Gateway"></parallelGateway>
    <sequenceFlow id="link1-6" sourceRef="Log2" targetRef="Parallel_Gateway-6"></sequenceFlow>
    <sequenceFlow id="link-4-6" sourceRef="Log3" targetRef="Parallel_Gateway-6"></sequenceFlow>
    <parallelGateway id="Parallel_Gateway-6" name="Parallel_Gateway"></parallelGateway>
    <sequenceFlow id="link3-4" sourceRef="Parallel_Gateway3" targetRef="Log3"></sequenceFlow>
    <serviceTask id="Log3" name="Hello" activiti:class="com.byzaneo.xtrade.process.task.LogTask">
      <extensionElements>
        <activiti:field name="message">
          <activiti:string>coucou</activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_f28250ed-974e-4ecd-bca6-1f41df09011f">
    <bpmndi:BPMNPlane bpmnElement="f28250ed-974e-4ecd-bca6-1f41df09011f" id="BPMNPlane_f28250ed-974e-4ecd-bca6-1f41df09011f">
      <bpmndi:BPMNShape bpmnElement="End2" id="BPMNShape_End2">
        <omgdc:Bounds height="30.0" width="30.0" x="410.0" y="95.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Log2" id="BPMNShape_Log2">
        <omgdc:Bounds height="60.0" width="100.0" x="170.0" y="0.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Log3" id="BPMNShape_Log3">
        <omgdc:Bounds height="60.0" width="100.0" x="170.0" y="160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Parallel_Gateway-6" id="BPMNShape_Parallel_Gateway-6">
        <omgdc:Bounds height="40.0" width="40.0" x="320.0" y="90.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Start-2" id="BPMNShape_Start-2">
        <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="95.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Parallel_Gateway3" id="BPMNShape_Parallel_Gateway3">
        <omgdc:Bounds height="40.0" width="40.0" x="80.0" y="90.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="link31" id="BPMNEdge_link31">
        <omgdi:waypoint x="120.0" y="102.5"></omgdi:waypoint>
        <omgdi:waypoint x="132.0" y="102.5"></omgdi:waypoint>
        <omgdi:waypoint x="132.0" y="30.000000000000007"></omgdi:waypoint>
        <omgdi:waypoint x="170.0" y="30.000000000000007"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="link3-4" id="BPMNEdge_link3-4">
        <omgdi:waypoint x="120.0" y="117.5"></omgdi:waypoint>
        <omgdi:waypoint x="132.0" y="117.5"></omgdi:waypoint>
        <omgdi:waypoint x="132.0" y="190.0"></omgdi:waypoint>
        <omgdi:waypoint x="170.0" y="190.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="link-4-6" id="BPMNEdge_link-4-6">
        <omgdi:waypoint x="270.0" y="190.0"></omgdi:waypoint>
        <omgdi:waypoint x="282.0" y="190.0"></omgdi:waypoint>
        <omgdi:waypoint x="282.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="320.0" y="110.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="link-62" id="BPMNEdge_link-62">
        <omgdi:waypoint x="360.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="410.0" y="110.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="link-23" id="BPMNEdge_link-23">
        <omgdi:waypoint x="30.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="80.0" y="110.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="link1-6" id="BPMNEdge_link1-6">
        <omgdi:waypoint x="270.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="282.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="282.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="320.0" y="110.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>