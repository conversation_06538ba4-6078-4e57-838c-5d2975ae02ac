package com.byzaneo.xtrade.service;

import com.byzaneo.xtrade.bean.*;

import java.util.*;

public interface DocumentErrorTranslationService {

  String SERVICE_NAME = "gnxDocumentErrorTranslationService";

  void saveErrorTranslation(DocumentErrorTranslation documentErrorTranslation);

  void removeSpecificErrorTranslation(DocumentErrorTranslationId id);

  DocumentErrorTranslation getErrorTranslationById(DocumentErrorTranslationId id);
  List<DocumentErrorTranslation> getErrorTranslationByLang(String Lang);

  Map<Locale, String> getLabelsForCode(String code);
}
