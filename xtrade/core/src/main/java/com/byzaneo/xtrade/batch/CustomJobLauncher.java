/**
 * 
 */
package com.byzaneo.xtrade.batch;

import static java.util.concurrent.Executors.newSingleThreadExecutor;

import java.util.concurrent.*;

import org.apache.logging.log4j.ThreadContext;
import org.slf4j.*;
import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.transaction.annotation.*;
import org.springframework.util.Assert;

import com.byzaneo.commons.service.Log4jService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 * <AUTHOR>
 * @company Generix Group
 * @date 25 oct. 2018
 * @serial TODO Manage executor of Jobs
 */
public class CustomJobLauncher implements JobLauncher, InitializingBean {

  private static final Logger log = LoggerFactory.getLogger(CustomJobLauncher.class);

  public static final String COMPONENT_NAME = "jobLauncher";

  private JobRepository jobRepository;

  /**
   * Run the provided job with the given {@link JobParameters}. The {@link JobParameters} will be used to determine if this is an execution
   * of an existing job instance, or if a new one should be created.
   *
   * @param job the job to be run.
   * @param jobParameters the {@link JobParameters} for this particular execution.
   * @return JobExecutionAlreadyRunningException if the JobInstance already exists and has an execution already running.
   * @throws JobRestartException if the execution would be a re-start, but a re-start is either not allowed or not needed.
   * @throws JobInstanceAlreadyCompleteException if this instance has already completed successfully
   * @throws JobParametersInvalidException
   */
  @Override
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public JobExecution run(final Job job, final JobParameters jobParameters)
      throws JobExecutionAlreadyRunningException, JobRestartException, JobInstanceAlreadyCompleteException,
      JobParametersInvalidException {

    Assert.notNull(job, "The Job must not be null.");
    Assert.notNull(jobParameters, "The JobParameters must not be null.");

    final JobExecution jobExecution;
    JobExecution lastExecution = jobRepository.getLastJobExecution(job.getName(), jobParameters);
    if (lastExecution != null) {
      if (!job.isRestartable()) {
        throw new JobRestartException("JobInstance already exists and is not restartable");
      }
      /*
       * validate here if it has stepExecutions that are UNKNOWN retrieve the previous execution and check
       */
      for (StepExecution execution : lastExecution.getStepExecutions()) {
        if (execution.getStatus() == BatchStatus.UNKNOWN) {
          throw new JobRestartException("Step [" + execution.getStepName() + "] is of status UNKNOWN");
        } // end if
      } // end for
    }

    // Check the validity of the parameters before doing creating anything
    // in the repository...
    job.getJobParametersValidator()
        .validate(jobParameters);

    /*
     * There is a very small probability that a non-restartable job can be restarted, but only if another process or thread manages to
     * launch <i>and</i> fail a job execution for this instance between the last assertion and the next method returning successfully.
     */
    jobExecution = jobRepository.createJobExecution(job.getName(), jobParameters);

    try {

      Runnable runnable = runnable(job, jobParameters, jobExecution);
      newSingleThreadExecutor(
          new ThreadFactoryBuilder().setNameFormat(Thread.currentThread()
              .getName() + "-%d")
              .build())
                  .submit(runnable)
                  .get(jobParameters.getLong("timeout", Long.valueOf(10L)), TimeUnit.MINUTES);
    }
    catch (TaskRejectedException | InterruptedException | ExecutionException | TimeoutException e) {
      jobExecution.upgradeStatus(BatchStatus.FAILED);
      if (jobExecution.getExitStatus()
          .equals(ExitStatus.UNKNOWN)) {
        jobExecution.setExitStatus(ExitStatus.FAILED.addExitDescription(e));
      }
      jobRepository.update(jobExecution);
    }

    return jobExecution;
  }

  private Runnable runnable(final Job job, final JobParameters jobParameters, final JobExecution jobExecution) {
    Runnable runnable = new Runnable() {
      @Override
      public void run() {
        try {
          ThreadContext.put(Log4jService.THREAD_CONTEXT_APPID, jobParameters.getString("APP_ID"));
          log.debug("Job: [" + job + "] launched with the following parameters: [" + jobParameters + "]");
          job.execute(jobExecution);
          log.debug("Job: [" + job + "] completed with the following parameters: [" + jobParameters + "] and the following status: [" +
              jobExecution.getStatus() + "]");
        }
        catch (Throwable t) {
          log.error("Job: [" + job + "] failed unexpectedly and fatally with the following parameters: [" + jobParameters + "]", t);
          rethrow(t);
        }
      }

      private void rethrow(Throwable t) {
        if (t instanceof RuntimeException) {
          throw (RuntimeException) t;
        }
        else if (t instanceof Error) {
          throw (Error) t;
        }
        throw new IllegalStateException(t);
      }
    };
    return runnable;
  }

  /**
   * Set the JobRepsitory.
   *
   * @param jobRepository
   */
  public void setJobRepository(JobRepository jobRepository) {
    this.jobRepository = jobRepository;
  }

  /**
   * Ensure the required dependencies of a {@link JobRepository} have been set.
   */
  @Override
  public void afterPropertiesSet() throws Exception {
    Assert.state(jobRepository != null, "A JobRepository has not been set.");
  }
}
