package com.byzaneo.xtrade.process;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.job.Trigger;
import com.byzaneo.commons.service.DocumentLockingService;
import com.byzaneo.commons.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.IndexableDocument;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.process.el.*;
import com.byzaneo.xtrade.process.task.AbstractTask;
import com.byzaneo.xtrade.service.*;
import com.ibm.icu.text.SimpleDateFormat;
import org.activiti.engine.delegate.*;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.impl.delegate.ActivityBehavior;
import org.activiti.engine.impl.el.ParsingElContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.util.ProcessDefinitionUtil;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.lang3.*;
import org.apache.logging.log4j.Level;
import org.apache.tools.ant.types.FileSet;
import org.slf4j.*;
import org.springframework.beans.SimpleTypeConverter;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.util.ResourceUtils;
import org.w3c.dom.Node;

import javax.el.*;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.net.*;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.byzaneo.commons.util.DomHelper.evaluateString;
import static com.byzaneo.commons.util.DomHelper.parseFile;
import static com.byzaneo.commons.util.ExpressionHelper.populate;
import static com.byzaneo.commons.util.ExpressionHelper.setProperty;
import static com.byzaneo.commons.util.FileHelper.fileSet;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.GsonHelper.toList;
import static com.byzaneo.commons.util.GsonHelper.toMap;
import static com.byzaneo.commons.util.JAXBHelper.unmarshal;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.mapper.BeanPropertyMapperHelper.setValues;
import static com.byzaneo.xtrade.api.DocumentStatus.ERROR;
import static com.byzaneo.xtrade.bean.Document.ProcessingWay.RECEIVING;
import static com.byzaneo.xtrade.bean.Document.ProcessingWay.SENDING;
import static com.byzaneo.xtrade.process.Constants.RESOURCE_PREFIX;
import static com.byzaneo.xtrade.process.Constants.XPATH_PREFIX;
import static com.byzaneo.xtrade.process.Variable.CHECK_INTEGRITY;
import static com.byzaneo.xtrade.process.Variable.DEADS;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.INDEXABLES;
import static com.byzaneo.xtrade.process.Variable.JOB_TRIGGER;
import static com.byzaneo.xtrade.process.Variable.LOG_LEVEL;
import static com.byzaneo.xtrade.process.Variable.PERSIST_DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.PROCESS_RELAUNCH_TOPIC;
import static com.byzaneo.xtrade.process.Variable.PROCESS_RESOURCES;
import static com.byzaneo.xtrade.process.Variable.PROCESS_WORK_DIR;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static com.google.common.base.Predicates.notNull;
import static com.google.common.collect.Collections2.filter;
import static java.lang.Class.forName;
import static java.lang.String.format;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.CollectionUtils.emptyCollection;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.MapUtils.isEmpty;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.io.FileUtils.listFiles;
import static org.apache.commons.io.filefilter.FalseFileFilter.FALSE;
import static org.apache.commons.io.filefilter.TrueFileFilter.TRUE;
import static org.apache.commons.lang3.BooleanUtils.toBoolean;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.trimToNull;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT;
import static org.apache.logging.log4j.Level.INFO;
import static org.springframework.beans.BeanUtils.copyProperties;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @since 31 dec. 2009
 */
public class VariableHelper {
  private static final Logger log = LoggerFactory.getLogger(VariableHelper.class);

  // -- DIRECTORY NAMES --
  public static final String INPUT_DIR = "input";

  public static final String DEAD_DIR = "dead";

  public static final String INPUT_BACKUP_DIR = "input_backup";

  public static final String ATTACH_BACKUP_DIR = "attach_backup";

  public static final String MISSING_DOCS = "missingSqlDocs";

  public static final String TOPIC_PERIOD = "_period";

  public static final String NON_PERSISTENT_PROPERTY_PREFIX = "DOC.CUSTOM.";

  // -- EXPRESSION MANAGEMENT --
  /** Default Expression Language implementation (does nothing) */
  private static final ELContext EL_CONTEXT = new ParsingElContext();

  /** JUEL based expression factory implementation */
  private static final ExpressionFactory EXPRESSION_FACTORY = new com.sun.el.ExpressionFactoryImpl();

  // -- BEAN MANAGEMENT --
  /** Spring {@link SimpleTypeConverter} */
  private static final SimpleTypeConverter CONVERTER = new SimpleTypeConverter();
  public static final String TAX_ORIGINAL_LABEL = "taxOriginal";

  // -- SERVICE --
  private static DocumentService documentService;

  private static IndexValidationService indexValidationService;

  private static DocumentLockingService documentLockingService;

  /*
   * -- CONSTRUCTOR --
   */

  /**
   * private constructor to hide the implicit public one
   */
  private VariableHelper() {
    // should not be instantiated
  }

  /*
   * -- SERVICES --
   */

  public static final DocumentService getDocumentService() {
    if (documentService == null)
      documentService = getBean(DocumentService.class, DocumentService.SERVICE_NAME);
    return documentService;
  }

  public static final IndexValidationService getIndexValidationService() {
    if (indexValidationService == null)
      indexValidationService = getBean(IndexValidationService.class, IndexValidationService.SERVICE_NAME);
    return indexValidationService;
  }

  public static final DocumentLockingService getDocumentLockingService() {
    if (documentLockingService == null) {
      documentLockingService = getBean(DocumentLockingService.class);
    }
    return documentLockingService;
  }
  /*
   * -- VARIABLES --
   */

  /**
   * @param execution
   * @param var
   * @param defaultValue
   * @since 7.0
   * @return the context execution variable value. if null, the given defaultValue is returned
   */
  public static final <T> T getVariable(final DelegateExecution execution, String var, T defaultValue) {
    @SuppressWarnings("unchecked")
    T value = (T) execution.getVariable(var);
    return value == null ? defaultValue : value;
  }

  /**
   * @param execution
   * @param var
   * @param defaultValue
   * @since 5.2 XTD-337
   * @return the context execution variable value. if null, the given defaultValue is returned
   */
  public static final <T> T getVariable(final DelegateExecution execution, Variable var, T defaultValue) {
    return getVariable(execution, var.toString(), defaultValue);
  }

  /**
   * @see #getVariable(DelegateExecution, Variable, Object)
   * @param execution
   * @param var
   * @since 5.2 XTD-337
   * @return the context execution variable value
   */
  public static final <T> T getVariable(final DelegateExecution execution, Variable var) {
    return getVariable(execution, var.toString(), null);
  }

  /**
   * Sets the variable with the provided name to the provided value.
   * <p>
   * A variable is set according to the following algorithm:
   * <p>
   * <li>If this scope already contains a variable by the provided name as a <strong>local</strong> variable, its value is overwritten to
   * the provided value.</li>
   * <li>If this scope does <strong>not</strong> contain a variable by the provided name as a local variable, the variable is set to this
   * scope's parent scope, if there is one. If there is no parent scope (meaning this scope is the root scope of the hierarchy it belongs
   * to), this scope is used. This applies recursively up the parent scope chain until, if no scope contains a local variable by the
   * provided name, ultimately the root scope is reached and the variable value is set on that scope.</li>
   * <p>
   * In practice for most cases, this algorithm will set variables to the scope of the execution at the process instance’s root level, if
   * there is no execution-local variable by the provided name.
   *
   * @param execution
   * @param var the name of the variable to be set
   * @param value the value of the variable to be set
   * @since 8.0
   * @see DelegateExecution#setVariable(String, Object)
   */
  public static final void setVariable(final DelegateExecution execution, String var, Object value) {
    if (getDocumentsVar(execution).equals(var)) {
      lockDocuments(value);
    }
    execution.setVariable(var, value);
  }

  public static final void setVariable(String repo, final DelegateExecution execution, String var, List<Document> value,
      List<Document> documentsToAdd, boolean unlockLockedDocumentsMap) {
    if (getDocumentsVar(execution).equals(var)) {
      lockDocuments(repo, execution, documentsToAdd, unlockLockedDocumentsMap);
    }
    execution.setVariable(var, value);
  }

  /**
   * Adds locks to {@link Document Documents} so they can not be accessed by other {@link AbstractTask Tasks}
   * 
   * @param value - {@link List} of {@link Document}
   */
  @SuppressWarnings("unchecked")
  private static void lockDocuments(Object documents) {
    getDocumentLockingService().addDocumentLocks(ofNullable(documents).filter(Collection.class::isInstance)
        .map(Collection.class::cast)
        .orElse(emptyCollection()));
  }

  @SuppressWarnings("unchecked")
  public static void lockDocuments(String repo, DelegateExecution execution, List<Document> documentsToAdd,
      boolean unlockLockedDocumentsMap) {
    getDocumentLockingService().addDocumentLocks(repo, execution, unlockLockedDocumentsMap, true, documentsToAdd);
  }

  /**
   * @param execution
   * @return the execution log {@link Level}. If not specified in context, {@link Level#INFO} wil be returned/
   */
  public static final Level getLogLevel(final DelegateExecution execution) {
    return getVariable(execution, LOG_LEVEL, INFO);
  }

  /*
   * -- DOCUMENTS--
   */

  /**
   * @param execution
   * @param documentClassName
   * @return {@link Document} instance based on the given expression (Document.class is used if expression is not set)
   * @throws Exception
   */
  public static final Document newDocumentInstance(final DelegateExecution execution, final Expression documentClassName) {
    try {
      return (Document) forName(getString(execution, documentClassName, Document.class.getName())).newInstance();
    }
    catch (Exception e) {
      log.error("Error instanciating document.", e);
      return null;
    }
  }

  /**
   * @param execution context
   * @param documentClassName document type to instantiate
   * @param documentProperties properties as JSon map expression
   * @param file source of properties
   * @return {@link Document} instance based on the given expression (Document.class is used if expression is not set) populated with
   *         documentProperties
   * @throws IllegalAccessException if the caller does not have access to the property accessor method
   * @throws InvocationTargetException if the property accessor method throws an exception
   * @see {@link #populateDocument(DelegateExecution, Document, Expression, File)}
   */
  public static final Document newDocumentInstancePopulated(
      final DelegateExecution execution,
      final Expression documentClassName,
      final Expression documentProperties,
      final File file) throws IllegalAccessException, InvocationTargetException {
    return populateDocument(execution, newDocumentInstance(execution, documentClassName), documentProperties, file);
  }

  /**
   * @param execution context
   * @param doc to populate
   * @param documentProperties properties as JSon map expression
   * @param file source of properties
   * @return populated document with the properties expression map.
   * @throws IllegalAccessException if the caller does not have access to the property accessor method
   * @throws InvocationTargetException if the property accessor method throws an exception
   */
  public static final Document populateDocument(DelegateExecution execution, Document doc, Expression documentProperties, File file)
      throws IllegalAccessException, InvocationTargetException {
    return populateObject(execution, doc, documentProperties, file);
  }

  private static <T> T populateObject(DelegateExecution execution, T doc, Expression documentProperties, File file) {
    if (doc == null || documentProperties == null)
      return doc;

    final Map<String, Object> props = getMap(execution, documentProperties);
    if (isEmpty(props))
      return doc;

    // try field by field resolution
    try {
      setValues(file, doc, props);
    }
    catch (Exception e) {
      log.warn("Error while populating document '{}' with properties: {}, fall-back to map populating method ({})",
          doc, props, getRootCauseMessage(e));
      // fall-back to expression helper populate method
      populate(doc, getMap(execution, documentProperties));
    }

    return doc;
  }

  /**
   * @param execution
   * @param doc
   * @param metadata representing a JSon serialized {@link PropertyGroup}
   * @param file (optional) used to evaluate XPath value
   * @return the doc populated with the metadata expression
   * @since 5.5 XTD-356
   */
  public static final Document populateDocumentMetadata(
      final DelegateExecution execution, final Document doc, final Expression metadata, final File file) {
    if (doc == null || metadata == null)
      return doc;
    PropertyGroup meta = getGson().fromJson(getString(execution, metadata), PropertyGroup.class);
    if (meta == null || meta.getProperties()
        .isEmpty())
      return doc;

    doc.setMetaGroup(setValues(file, meta, meta.getPropertyList()));

    return doc;
  }

  public static final PropertyGroup populateDocumentMetadata(final DelegateExecution execution, final PropertyGroup group,
      final Expression metadata, final File file) {
    if (group == null || metadata == null)
      return group;
    PropertyGroup meta = getGson().fromJson(getString(execution, metadata), PropertyGroup.class);
    if (meta == null || meta.getProperties()
        .isEmpty())
      return group;

    return setValues(file, group, meta.getPropertyList());

  }

  /**
   * @param execution
   * @param document
   * @param indexClassName
   * @param indexFile specifies if the given file has to be unmarshalled as index
   * @param indexProperties
   * @param file
   * @return the index created and populated from the given expressions. The index will be associated to the document (if not
   *         <code>null</code>).
   * @since 7.0
   */
  @SuppressWarnings("unchecked")
  public static final <T extends Indexable> T createIndex(final DelegateExecution execution, final Document document,
      final Expression indexClassName, Expression indexFile, final Expression indexProperties, final File file) {
    if (indexClassName == null)
      return null;

    // creates index instance
    final T index;
    try {
      Class<T> indexClass = (Class<T>) forName(getString(execution, indexClassName));
      index = getBoolean(execution, indexFile, Boolean.FALSE) ? unmarshal(indexClass, file) : indexClass.newInstance();
    }
    catch (Exception e) {
      log.error("Error instanciating index '{}' ({})",
          indexClassName.getExpressionText(), getRootCauseMessage(e));
      return null;
    }

    // Converts type of document(ex: INVOIC) to the specific class (Invoice).
    String documentClass = getIndexValidationService().getClassFromType(document.getType());

    if (documentClass != null && !index.getClass()
        .getSimpleName()
        .equals(documentClass)) {
      log.error("Cannot create index of type {} from {} for {}", index.getClass()
          .getSimpleName(), documentClass, file.getName());
      return null;
    }

    // populates index with the document properties
    if (document != null && index instanceof IndexableDocument) {
      try {
        copyProperties(document, index);
      }
      catch (Exception e) {
        log.error("Error populating index ({}) form document properties: {} ({})",
            index, document, getRootCauseMessage(e));
        return null;
      }
    }

    // populates index with the properties
    final PropertyGroup iprops = getGson().fromJson(getString(execution, indexProperties), PropertyGroup.class);
    if (iprops != null && !iprops.getProperties()
        .isEmpty()) {
      Node node = null;
      boolean xpath;
      // log.debug("Populate metadata: {}", doc);
      for (Property p : iprops.getPropertyList()) {
        if (isBlank(p.getValue()))
          continue;
        xpath = p.getValue()
            .startsWith(XPATH_PREFIX.toString());
        // xpath: value needs to parse the file
        if (xpath && file != null && node == null)
          node = parseFile(file, false);
        // set the converted (if necessary) meta value
        p.setValue(xpath ? trimToNull(evaluateString(node, p.getValue()
            .replace(XPATH_PREFIX.toString(), ""))) : getValue(execution, p.getValue(), String.class, null));
        // log.debug("\t- {}={}", p.getKey(), p.getObject());
        if (p.getValue() != null) {
          // set index property
          try {
            if (p.getObject() != null) {
              // TODO set nested index properties (creating intermediary beans as necessary). See PropertyUtils
              setProperty(index, p.getKey(), p.getObject());
            }
            else {
              log.error("Could not parse {}", p);
              return null;
            }
          }
          catch (Exception e) {
            log.error("Error setting index ({}) property {} with value: {} ({})",
                index.getClass(), p.getKey(), p.getValue(), getRootCauseMessage(e));
          }
        }
      }
    }

    // associates index to the document
    if (document != null) {
      removeIndexIfExist(document);
      document.setIndexValue(index);
    }

    return index;
  }

  public static void removeIndexIfExist(Document document) {
    if (document.isIndexed()) {
      getDocumentService().removeIndex(document.getIndex());
    }
  }

  // -- DOCUMENTS --

  /**
   * @param execution
   * @return the {@link Document} list in the context (if none, return an empty list)
   * @see #getDocuments(DelegateExecution, Class, Expression)
   */
  public static final List<Document> getDocuments(DelegateExecution execution) {
    return getDocuments(execution, Document.class, null, null);
  }

  /**
   * @param execution
   * @param type
   * @return the document {@link List} in the context variable with key {@value Variable#DOCUMENTS}.
   * @see #getDocuments(DelegateExecution, Class, Expression)
   */
  public static final <D extends Document> List<D> getDocuments(DelegateExecution execution, Class<D> type) {
    return getDocuments(execution, type, null, null);
  }

  /**
   * @param execution
   * @param bqlExpression
   * @return
   * @see #getDocuments(DelegateExecution, Class, Expression)
   */
  public static final List<Document> getDocuments(DelegateExecution execution, Expression bqlExpression) {
    return getDocuments(execution, Document.class, bqlExpression, getNonPersistentPropertiesWithoutPrefix(execution));
  }

  /**
   * @param execution context
   * @param type expected document type
   * @param bqlExpression filtering BQL query as expression
   * @return the document {@link List} in the context variable with key {@value Variable#DOCUMENTS} and optionally filtered by the given BQL
   *         query expression. NB : the key may be overridden, @see {@link VariableHelper#getDocumentsVar(DelegateExecution)}
   * @see QueryHelper#search(String, Class, Collection)
   */
  public static final <D extends Document> List<D> getDocuments(DelegateExecution execution, Class<D> type, Expression bqlExpression, Map<String, Map<String, Object>> nonPersistentProperties) {
    return new ArrayList<D>(QueryHelper.search(
        getString(execution, bqlExpression),
        type,
        getVariable(execution, getDocumentsVar(execution), emptyList()), nonPersistentProperties));
  }

  /**
   * @param execution
   * @param docs to put in the given execution context
   */
  public static final void setDocuments(final DelegateExecution execution, List<? extends Document> docs) {
    setVariable(execution, getDocumentsVar(execution), docs);
  }

  public static final void setDocuments(String repo, final DelegateExecution execution, List<Document> docs, List<Document> documentsToAdd,
      boolean unlockLockedDocumentsMap) {
    setVariable(repo, execution, getDocumentsVar(execution), docs, documentsToAdd, unlockLockedDocumentsMap);
  }
  // -- INDEXABLES --

  /**
   * @param execution
   * @return the {@link Indexable} list in the context (if none, return an empty list)
   */
  public static final <I extends com.byzaneo.xtrade.bean.Indexable> List<I> getIndexables(DelegateExecution execution) {
    return new ArrayList<I>(getVariable(execution, getIndexablesVar(execution), emptyList()));
  }

  /**
   * @param execution
   * @param indexables to put in the given execution context
   */
  public static final void setIndexables(final DelegateExecution execution, List<? extends com.byzaneo.xtrade.bean.Indexable> indexables) {
    setVariable(execution, getIndexablesVar(execution), indexables);
  }

  /**
   * Adds the given documents to the context. Replace the ones already in the list.
   * 
   * @param execution
   * @param documentsToAdd
   * @return the new list of documents.
   * @see #addDocuments(DelegateExecution, Collection)
   */
  public static final List<Document> addDocuments(final DelegateExecution execution, Document... documentsToAdd) {
    return addDocuments(execution, documentsToAdd == null ? null : asList(documentsToAdd));
  }

  /**
   * Adds the given documents to the context. Replaces the duplicates.
   * <p/>
   * NOTE: if the documents are not persisted (identifier are <code>null</code>) the references is used as identifier for the unicity check
   * (see {@link Document#equals(Object)}
   * 
   * @param execution
   * @param documentsToAdd
   * @return the new list of documents.
   */
  public final static List<Document> addDocuments(final DelegateExecution execution, Collection<Document> documentsToAdd) {
    List<Document> docs = getDocuments(execution);
    if (isEmpty(documentsToAdd))
      return docs;

    // removes duplicate
    final Set<Document> docset = new LinkedHashSet<>(docs);
    docset.addAll(documentsToAdd);
    docs = new ArrayList<>(docset);
    setDocuments(execution, docs);

    return docs;
  }

  public final static List<Document> addDocuments(String repository, final DelegateExecution execution, Collection<Document> documentsToAdd,
      boolean unlockLockedDocumentsMap) {
    List<Document> docs = getDocuments(execution);
    if (isEmpty(documentsToAdd))
      return docs;

    // removes duplicate
    final Set<Document> docset = new LinkedHashSet<>(docs);
    docset.addAll(documentsToAdd);
    docs = new ArrayList<>(docset);
    setDocuments(repository, execution, docs, new ArrayList<>(documentsToAdd), unlockLockedDocumentsMap);

    return docs;
  }

  public final static <T extends com.byzaneo.xtrade.bean.Indexable> List<T> addIndexables(final DelegateExecution execution,
      List<T> indexablesToAdd) {
    List<T> indexables = getIndexables(execution);
    if (isEmpty(indexablesToAdd))
      return indexables;

    indexables.addAll(indexablesToAdd);
    setIndexables(execution, indexables);

    return indexables;
  }

  public final static List<WorkflowDocumentStatus> addWkfDocumentStatuses(final DelegateExecution execution,
      Collection<WorkflowDocumentStatus> wkfStatusesToAdd) {

    List<WorkflowDocumentStatus> existingWkfDocSts = getWkfDocumentStatuses(execution);
    if (isEmpty(wkfStatusesToAdd))
      return existingWkfDocSts;

    // removes duplicate
    final Set<WorkflowDocumentStatus> docset = new LinkedHashSet<>(existingWkfDocSts);
    docset.addAll(wkfStatusesToAdd);
    existingWkfDocSts = new ArrayList<>(docset);
    setVariable(execution, Variable.WKF_DOC_STS.toString(), existingWkfDocSts);
    return existingWkfDocSts;

  }

  public final static List<WorkflowDocumentStatus> getWkfDocumentStatuses(final DelegateExecution execution) {
    return getVariable(execution, Variable.WKF_DOC_STS, emptyList());

  }

  public final static List<DocumentTimeline> addDocumentsTimeline(final DelegateExecution execution,
      Collection<DocumentTimeline> timelinesToAdd) {

    List<DocumentTimeline> existingTimelines = getDocumentTimelines(execution);
    if (isEmpty(timelinesToAdd))
      return existingTimelines;

    // removes duplicate
    final Set<DocumentTimeline> docset = new LinkedHashSet<>(existingTimelines);
    docset.addAll(timelinesToAdd);
    existingTimelines = new ArrayList<>(docset);
    setVariable(execution, Variable.DOCUMENT_TIMELINE.toString(), existingTimelines);
    return existingTimelines;

  }

  public final static List<DocumentTimeline> getDocumentTimelines(final DelegateExecution execution) {
    return getVariable(execution, Variable.DOCUMENT_TIMELINE, emptyList());

  }

  public final static List<ReconciliationRelation> addReconciliationRelations(final DelegateExecution execution,
      Collection<ReconciliationRelation> reconcilationRelationsToAdd) {

    List<ReconciliationRelation> existingReconcilationRelations = getReconciliationRelations(execution);
    if (isEmpty(reconcilationRelationsToAdd))
      return existingReconcilationRelations;

    // removes duplicate
    final Set<ReconciliationRelation> docset = new LinkedHashSet<>(existingReconcilationRelations);
    docset.addAll(reconcilationRelationsToAdd);
    existingReconcilationRelations = new ArrayList<>(docset);
    setVariable(execution, Variable.RECONCILIATION_RELATIONS.toString(), existingReconcilationRelations);
    return existingReconcilationRelations;

  }

  public final static List<ReconciliationRelation> getReconciliationRelations(final DelegateExecution execution) {
    return getVariable(execution, Variable.RECONCILIATION_RELATIONS, emptyList());

  }

  // -- DEADS --

  /**
   * @return <code>true</code>, if the given document is part of the dead queue
   */
  public static final boolean isDead(DelegateExecution execution, Document document) {
    return getDeads(execution).contains(document);
  }

  /**
   * @param execution
   * @param type
   * @return the dead document {@link List} in the context variable with key {@value Variable#DEADS}.
   */
  public static final <D extends Document> List<D> getDeads(DelegateExecution execution) {
    return getVariable(execution, DEADS, new ArrayList<D>());
  }

  /**
   * @param execution
   * @param deads document to put in the given execution context
   */
  public static final void setDeads(final DelegateExecution execution, List<? extends Document> deads) {
    setVariable(execution, DEADS.toString(), deads);
  }

  /**
   * Adds the given documents to the dead queue. Removes them from the {@link #getDocuments(DelegateExecution)} in the context and sets the
   * documents' status to ERROR. The documents' error message is also set if it was blank. The document's children are <strong>not</strong>
   * added to the dead queue.
   * 
   * @param execution
   * @param deadsToAdd
   * @return the new list of documents (deads).
   */
  public static final List<Document> addDeads(final DelegateExecution execution, Document... deadsToAdd) {
    return addDeads(execution, false, deadsToAdd);
  }

  /**
   * Adds the given documents to the dead queue. Removes them from the {@link #getDocuments(DelegateExecution)} in the context and sets the
   * documents' status to ERROR. The documents' error message is also set if it was blank.
   * 
   * @param execution
   * @param recursive when {@code true}, children of the given documents are also moved to the dead queue.
   * @param deadsToAdd
   * @return the new list of documents (deads).
   */
  public static final List<Document> addDeads(final DelegateExecution execution, boolean recursive, Document... deadsToAdd) {
    return addDeads(execution, recursive, asList(deadsToAdd));
  }

  /**
   * Adds the given documents to the dead queue. Removes them from the {@link #getDocuments(DelegateExecution)} in the context and sets the
   * documents' status to ERROR. The documents' error message is also set if it was blank. The document's children are <strong>not</strong>
   * added to the dead queue.
   * 
   * @param execution
   * @param deadsToAdd
   * @return the new list of documents (deads).
   */
  public static final List<Document> addDeads(final DelegateExecution execution, Collection<Document> deadsToAdd) {
    return addDeads(execution, false, deadsToAdd);
  }

  /**
   * Adds the given documents to the dead queue. Removes them from the {@link #getDocuments(DelegateExecution)} in the context and sets the
   * documents' status to ERROR. The documents' error message is also set if it was blank.
   * 
   * @param execution
   * @param recursive when {@code true}, children of the given documents are also moved to the dead queue.
   * @param deadsToAdd
   * @return the new list of documents (deads).
   */
  public static final List<Document> addDeads(final DelegateExecution execution, boolean recursive, Collection<Document> deadsToAdd) {
    List<Document> deads = getDeads(execution);

    final String date = ISO_8601_EXTENDED_DATETIME_FORMAT.format(new Date());

    // removes duplicates
    final Set<Document> deadset = new LinkedHashSet<>(deads);
    addDeadsRecursive(recursive, execution, deadsToAdd, deadset, date);
    deads = new ArrayList<>(deadset);

    // sets deads
    setDeads(execution, deads);

    return deads;
  }

  private static void addDeadsRecursive(boolean recursive, DelegateExecution execution, Collection<Document> deadsToAdd,
      Collection<Document> deads, String date) {
    Collection<Document> toAdd = filter(deadsToAdd, notNull());
    if (isEmpty(toAdd)) {
      return;
    }

    for (Document dead : toAdd) {
      dead.setStatusWithEnumValue(ERROR);
      if (isBlank(dead.getError())) {
        dead.setError(format("Put in dead queue channel by process %s:%s at %s",
            execution.getProcessDefinitionId(), execution.getProcessInstanceId(), date));
      }

      if (recursive && dead.getChildCount() > 0) {
        addDeadsRecursive(true, execution, dead.getChildren(), deads, date);
      }
    }
    // removes dead documents from contextual documents
    getVariable(execution, DOCUMENTS, new ArrayList<Document>()).removeAll(toAdd);
    if (!DOCUMENTS.toString()
        .equals(getDocumentsVar(execution))) {
      getVariable(execution, getDocumentsVar(execution), new ArrayList<Document>()).removeAll(toAdd);
    }
    deads.addAll(toAdd);

    // detach dead from parent in contextual documents
    for (Document dead : toAdd) {
      Document deadParent = dead.getParent();
      if (deadParent != null) {
        int index = getVariable(execution, DOCUMENTS, new ArrayList<Document>()).indexOf(deadParent);
        if (index > -1) {
          Document deadParentInContext = getVariable(execution, DOCUMENTS, new ArrayList<Document>()).get(index);
          if (deadParentInContext.getChildCount() > 0) {
            deadParentInContext.getChildren()
                .remove(dead);
          }
        }
      }
    }
  }

  /**
   * @param execution
   * @return the absolute dead target directory for the given execution
   */
  public final static File getDeadDirectory(final DelegateExecution execution) {
    return getDirectoryFromProcessWorkDir(execution, DEAD_DIR);
  }

  /**
   * @param execution
   * @return the absolute input backup target directory for the given execution
   */
  public final static File getInputBackupDirectory(final DelegateExecution execution) {
    return getDirectoryFromProcessWorkDir(execution, INPUT_BACKUP_DIR);
  }

  public final static File getAttachBackupDirectory(final DelegateExecution execution) {
    return getDirectoryFromProcessWorkDir(execution, ATTACH_BACKUP_DIR);
  }

  /**
   * @param execution
   * @param targetDirectory target directory in PROCESS_WORK_DIR
   * @return the absolute target directory
   */
  private final static File getDirectoryFromProcessWorkDir(final DelegateExecution execution, final String targetDirectory) {
    File r;
    try {
      r = new File(getVariable(execution, PROCESS_WORK_DIR, getTempDirectory()), targetDirectory);
    }
    catch (Exception e) {
      r = new File(getTempDirectory(), targetDirectory);
    }
    r.mkdirs();

    return r;
  }

  /*
   * -- EXPRESSION --
   */

  /**
   * Generic method to get the value from a {@link Expression} in the given {@link DelegateExecution} context.
   *
   * @param <T>
   * @param execution
   * @param expression
   * @param type
   * @param defaultValue
   * @return value from the given {@link Expression}.
   */
  public static final <T> T getExpressionValue(final DelegateExecution execution, final Expression expression, final Class<T> type,
      final T defaultValue) {
    if (expression == null)
      return defaultValue;

    try {
      final Object value = "null".equals(expression.getValue(execution)) ? null : expression.getValue(execution);
      return value != null ? convert(value, type) : defaultValue;

    }
    catch (Exception e) {
      log.warn("Error converting '{}' expression value to '{}' ({})", expression, type, e.getMessage());
      return defaultValue;
    }
  }

  public static final <T> T getValue(final DelegateExecution execution, final String expression, final Class<T> type,
      final T defaultValue) {
    return getExpressionValue(execution, createExpression(expression), type, defaultValue);
  }

  public static final Long getLong(final DelegateExecution exe, final Expression exp) {
    return getLong(exe, exp, null);
  }

  public static final Long getLong(final DelegateExecution execution, final Expression expression, final Long defaultValue) {
    return getExpressionValue(execution, expression, Long.class, defaultValue);
  }

  public static final Integer getInteger(final DelegateExecution exe, final Expression exp) {
    return getInteger(exe, exp, null);
  }

  public static final Integer getInteger(final DelegateExecution execution, final Expression expression, final Integer defaultValue) {
    return getExpressionValue(execution, expression, Integer.class, defaultValue);
  }

  public static final Boolean getBoolean(final DelegateExecution exe, final Expression exp) {
    return getBoolean(exe, exp, null);
  }

  public synchronized static final Boolean getBoolean(final DelegateExecution execution, final Expression expression,
      final Boolean defaultValue) {
    return getExpressionValue(execution, expression, Boolean.class, defaultValue);
  }

  public static final String getString(final DelegateExecution exe, final Expression exp) {
    return getString(exe, exp, null);
  }

  public static final String getString(final DelegateExecution execution, final Expression expression, final String defaultValue) {
    return getExpressionValue(execution, expression, String.class, defaultValue);
  }

  public static String getPropertyFromDocument(Expression expression, DocumentFile document, DelegateExecution execution) {
    return getPropertyFromDocument(expression.getExpressionText(), document, execution);
  }

  public static String getPropertyFromDocument(String name, DocumentFile documentFile, DelegateExecution execution) {
    if (!name.contains("${")) {
      return name;
    }
    String before = name.substring(0, name.indexOf("$"));
    String methodName = name.substring(name.indexOf("{") + 1, name.indexOf("}"));
    try {
      if (methodName.startsWith(NON_PERSISTENT_PROPERTY_PREFIX.toLowerCase())) {
        String propertyValue = getNonPersistentPropertyValue(execution, documentFile, methodName);
        return before + (propertyValue != null ? propertyValue : "");
      }
      var result = ExpressionHelper.getProperty(documentFile, methodName);
      return before + (result == null ? "" : result.toString());
    }
    catch (SpelEvaluationException e) {
      var result = ExpressionHelper.getProperty(documentFile.getDocument(), methodName);
      return before + (result == null ? "" : result.toString());
    }
  }

  public static void verifyIfOtherDocumentHasTaxOriginalTag(Document document, DocumentFile documentFile) {
    if (TAX_ORIGINAL_LABEL.equals(documentFile.getActionName())) {
      changeActionNameFromTaxOriginal(document.getFiles());
      if (document.getParent() != null) {
        changeActionNameFromTaxOriginal(document.getParent()
            .getFiles());
      }
    }
  }

  private static void changeActionNameFromTaxOriginal(List<DocumentFile> documentFiles) {
    for (var document : documentFiles) {
      if (TAX_ORIGINAL_LABEL.equals(document.getActionName())) {
        document.setActionName(null);
      }
    }
  }


  public static final File getFile(final DelegateExecution exe, final Expression exp) {
    return getFile(exe, exp, null);
  }

  public static final File getFile(final DelegateExecution execution, final Expression expression, final File defaultValue) {
    String fileValue = getString(execution, expression);
    File file = defaultValue;
    if (isBlank(fileValue))
      return file;

    // process resource
    if (fileValue.startsWith(RESOURCE_PREFIX.toString())) {
      Map<String, Object> rmap = getVariable(execution, PROCESS_RESOURCES);
      String rname = fileValue.substring(RESOURCE_PREFIX.toString()
          .length());
      String path;
      if (rmap != null && (path = (String) rmap.get(rname)) != null)
        file = new File(path);
      else if (file == null || !file.exists()) {
        log.error("Resource file not found: {}", rname);
        return null;
      }
    }
    // Spring resource file (no scheme, classpath: or file:)
    else {
      try {
        file = ResourceUtils.getFile(fileValue);
      }
      catch (Exception e) {
        log.error("Resource file not found: {}", fileValue);
      }
    }

    return file;
  }

  public static final InputStream getFileStream(final DelegateExecution execution, final Expression expression) {
    String resourceValue = getString(execution, expression);
    if (isBlank(resourceValue))
      return null;

    // process resource
    URL url;
    if (resourceValue.startsWith(RESOURCE_PREFIX.toString())) {
      Map<String, Object> rmap = getVariable(execution, PROCESS_RESOURCES);
      String rname = resourceValue.substring(RESOURCE_PREFIX.toString()
          .length());
      String path;
      if (rmap != null && (path = (String) rmap.get(rname)) != null)
        try {
          url = new File(path).toURI()
              .toURL();
        }
        catch (MalformedURLException e) {
          url = null;
        }
      else {
        url = null;
      }
    }
    // Spring resource file (no scheme, classpath: or file:)
    else {
      try {
        url = ResourceUtils.getURL(resourceValue);
      }
      catch (FileNotFoundException e) {
        url = null;
      }
    }

    if (url == null) {
      log.error("Resource file not found: {}", resourceValue);
      return null;
    }

    try {
      return url.openStream();
    }
    catch (Exception e) {
      log.error("Resource file not found: {}", resourceValue);
      return null;
    }
  }

  public static final String getFilePath(final DelegateExecution exe, final Expression exp) {
    File f;
    return (f = getFile(exe, exp, null)) == null ? null : f.getAbsolutePath();
  }

  @SuppressWarnings({ "unchecked", "rawtypes" })
  public static final <T extends Enum> T getEnum(final DelegateExecution execution, final Expression expression, final Class<T> type,
      final T defaultValue) {
    try {
      return (T) Enum.valueOf(type, getString(execution, expression, defaultValue != null ? defaultValue.toString() : null));
    }
    catch (Exception e) {
      return defaultValue;
    }
  }

  /**
   * @see GsonHelper#toMap(String)
   * @param execution
   * @param expression a Json String map representation
   * @return the resulting map of the Gson deserialization.
   */
  public static final <V> Map<String, V> getMap(final DelegateExecution execution, final Expression expression) {
    final Map<String, String> map = toMap(getString(execution, expression, ""));
    if (map == null)
      return null;
    final Map<String, V> r = new LinkedHashMap<>(map.size());
    for (Entry<String, String> e : map.entrySet())
      r.put(e.getKey(), VariableHelper.<V> resolveStringExpression(execution, e.getValue()));
    return r;
  }

  /**
   * @param execution
   * @param expression
   * @param defaultClass
   * @return
   */
  public static <T> T getInstance(DelegateExecution execution, Expression expression, Class<T> defaultClass) {
    Class<T> type = getClass(execution, expression, defaultClass);
    if (type == null)
      return null;
    try {
      return type.newInstance();
    }
    catch (Exception e) {
      log.warn("Error instanciating type: {} ({})", type, e.getMessage());
      return null;
    }
  }

  /**
   * @param execution
   * @param expression
   * @param defaultClass
   * @return
   */
  @SuppressWarnings("unchecked")
  public static <T> Class<T> getClass(DelegateExecution execution, Expression expression, Class<T> defaultClass) {
    String className = getString(execution, expression, defaultClass != null ? defaultClass.getName() : null);
    if (isBlank(className))
      return null;

    try {
      return (Class<T>) forName(className);
    }
    catch (Exception e) {
      log.warn("Class unknown: {} ({})", className, e.getMessage());
      return defaultClass;
    }
  }

  /*
   * -- LIST & ARRAY--
   */

  /** @see #getList(DelegateExecution, Expression, List) */
  public static final <T> List<T> getList(final DelegateExecution execution, final Expression expression) {
    return VariableHelper.<T> getList(execution, expression, null);
  }

  /**
   * @see GsonHelper#toList(String)
   * @param execution
   * @param expression
   * @return {@link List} of {@link Object} values
   */
  public static final <T> List<T> getList(final DelegateExecution execution, final Expression expression, final List<T> defaultValue) {
    if (expression == null || StringUtils.isBlank(expression.getExpressionText())) {
      return defaultValue;
    }
    String json = trimToNull(expression.getExpressionText());

    // backward compatibility (comma separated list)
    if (!json.startsWith("[")) json = "[" + json;
    if (!json.endsWith("]")) json += "]";

    final List<String> list = toList(json);
    List<T> rslt = new ArrayList<T>();
    for (String val : list) {
      rslt.add(VariableHelper.<T> resolveStringExpression(execution, val));
    }
    return isNotEmpty(rslt) ? rslt : defaultValue;
  }

  /** @see #getArray(DelegateExecution, Expression, String[]) */
  public static final String[] getArray(final DelegateExecution execution, final Expression expression) {
    return getArray(execution, expression, null);
  }

  /**
   * @see #getList(DelegateExecution, Expression)
   * @param execution
   * @param expression
   * @return {@link Object[]} from the given {@link Expression}
   */
  public static final String[] getArray(final DelegateExecution execution, final Expression expression, final String[] defaultValue) {
    final List<String> list = getList(execution, expression, null);
    return isNotEmpty(list) ? list.toArray(new String[list.size()]) : defaultValue;
  }

  /*
   * FILE SET
   */

  public static final FileSet createFileSet(final DelegateExecution execution, final Expression expression, final File dir, int latency) {
    final Map<String, String> fsprops = getMap(execution, expression);

    return isEmpty(fsprops) ? FileHelper.createFileSet(dir, "*", null, false, null, null, latency)
        : FileHelper.createFileSet(dir,
            fsprops.get("includes"),
            fsprops.get("excludes"),
            toBoolean(fsprops.get("caseSensitive")),
            fsprops.get("containRegExp"),
            fsprops.get("contain"), latency);
  }

  public static final FileSet createFileSet(final DelegateExecution execution, final Expression expression, final File dir) {
    return createFileSet(execution, expression, dir, 0);
  }

  public static final Collection<File> getFileSet(final DelegateExecution execution, final Expression fileSet, final File input) {
    if (input == null)
      return emptyList();

    // Single file
    if (input.isFile())
      return asList(input);

    // Ant FileSet specified
    if (fileSet != null)
      return fileSet(input, createFileSet(execution, fileSet, input, 0));

    // all files in the directory
    return listFiles(input, TRUE, FALSE);
  }

  /*
   * EXPRESSION
   */

  /**
   * @param execution
   * @param expression string expression
   * @return the resolved the string expression against the given execution
   */
  public static final <V> V resolveStringExpression(final DelegateExecution execution, final String expression) {
    return resolveExpression(execution, createExpression(expression));
  }

  /**
   * @param execution
   * @param expression
   * @return the resolved the expression against the given execution
   */
  @SuppressWarnings("unchecked")
  public static final <V> V resolveExpression(final DelegateExecution execution, final Expression expression) {
    try {
      return expression == null ? null : (V) expression.getValue(execution);
    }
    catch (Exception e) {
      log.warn(e.getMessage());
      return null;
    }
  }

  /**
   * @param expression
   * @return {@link Expression#getExpressionText()}
   */
  public static final String getExpressionAsString(Expression expression) {
    if (expression == null)
      return null;
    return expression.getExpressionText();
  }

  /**
   * <ul>
   * <li>{@link JuelExpression}: If {@link String} contains "${" and not starts with "{" or "[" (Json)</li>
   * <li>{@link FixedValue}: otherwise</li>
   * </ul>
   * 
   * @param expression
   * @return the {@link Expression} created from the given {@link String} expression.
   */
  @SuppressWarnings("unchecked")
  public static final <E extends Expression> E createExpression(String expression) {
    String trimedValue = trimToNull(expression);
    if (trimedValue == null)
      return null;
    // JUEL expression
    if (trimedValue.contains("${") && !trimedValue.startsWith("[") && !trimedValue.startsWith("{"))
      return (E) new JuelExpression(
          EXPRESSION_FACTORY.createValueExpression(EL_CONTEXT, expression.trim(), Object.class),
          expression);
    // Fixed value
    return (E) new FixedValue(trimedValue);
  }

  /**
   * @param expression
   * @return the {@link Expression#getExpressionText()}. If <code>null</code>, returns an empty string.
   */
  public static final String getExpressionText(final Expression expression) {
    return expression == null ? "" : expression.getExpressionText();
  }

  /**
   * @param execution
   * @return the [process definition ID]:[execution id]
   * @see #resolveExecutionId(String, String)
   */
  public static final String resolveExecutionId(final DelegateExecution execution) {

    ProcessDefinition processDefinition = getProcessDefinition(execution);
    // since AIO-10310 we change the IdGenerator and so the processId will be a 36 characters string
    // Activiti creates the process ID of form processDefinition.getKey() + ":" + processDefinition.getVersion() + ":" + nextId;
    // but because of some stupid modification on BpmnDeployer.getIdForNewProcessDefinition if the concatenated ID has more than 64
    // characters it will only be 'nextId'
    //
    // SO we need to take that into consideration and make sure the processDefinitionId is of form processDefinition.getKey() + ":" +
    // processDefinition.getVersion() + ":" + nextId;
    if (processDefinition.getId() != null && !processDefinition.getId()
        .contains(":")) {
      return resolveExecutionId(processDefinition.getKey() + ":" + processDefinition.getVersion() + ":" + processDefinition.getId(),
          execution.getId());
    }
    return resolveExecutionId(execution.getProcessDefinitionId(), execution.getId());

  }

  /**
   * @param definitionId
   * @param instanceId
   * @return the [process definition ID]:[execution id]
   */
  private static final String resolveExecutionId(final String definitionId, final String instanceId) {
    return format("%s:%s", definitionId, instanceId);
  }

  /**
   * @param execution
   * @return the process definition associated to the given execution
   * @since 7.0
   */
  @SuppressWarnings("unchecked")
  public static final <D extends ProcessDefinition> D getProcessDefinition(final DelegateExecution execution) {
    return (D) ProcessDefinitionUtil.getProcessDefinition(execution.getProcessDefinitionId());
  }

  /**
   * @param execution
   * @return the process instance associated to the given execution
   * @since 7.0
   */
  @SuppressWarnings("unchecked")
  public static final <I extends ProcessInstance> I getProcessInstance(final DelegateExecution execution) {
    return (I) ((ExecutionEntity) execution).getProcessInstance();
  }

  /**
   * @param execution
   * @return the id defined in the current {@link ActivityBehavior}
   */
  public static final String getActivityId(final DelegateExecution execution) {
    return ((ExecutionEntity) execution).getActivityId();
  }

  /*
   * -- POLICY --
   */

  /**
   * @param policy to apply
   * @param execution context
   * @param document source of the duplication check
   * @param duplicates source duplicates found in repository
   * @return <code>true</code> if the given policy applied
   */
  public static final boolean applyDocumentPolicy(final DocumentPolicy policy, final DelegateExecution execution, final Document document,
      final List<Document> duplicates, final List<DocumentType> childrenTypeToIgnore) {
    if (document == null)
      return false;
    if (duplicates.isEmpty()) {
      log.debug("No duplicate for '{}'", document.getReference());
      return false;
    }
    switch (policy) {
    case NONE:
      log.warn("Document '{}' processed (no policy)", document.getReference());
      return true;
    case IGNORE:
      log.warn("Document '{}' ignored (removed from the context)", document.getReference());
      getVariable(execution, DOCUMENTS, new ArrayList<Document>()).remove(document);
      getVariable(execution, getDocumentsVar(execution), new ArrayList<Document>()).remove(document);
      return true;
    case DEAD:
      log.warn("Document '{}' moved to dead queue channel", document.getReference());
      addDeads(execution, document);
      return true;
    case REPLACE:
      log.warn("Document '{}' replace its {} previous document(s)", document.getReference(), duplicates.size());
      getDocumentService().removeDocuments(duplicates);
      return true;
    case LINK:
      log.warn("Document '{}' linked with {} previous document(s)", document.getReference(), duplicates.size());

      for (Document duplicate : duplicates) {
        if (duplicate.getParent() == null) {
          document.setTo(duplicate.getTo());
          duplicate.addChild(document);
          duplicate.setStatus(document.getStatus());
        }
      }
      return true;
    case CANCEL:
      for (Document duplicate : duplicates) {
        if (isEqualsStatusCode(duplicate.getStatus(), DocumentStatus.CANCEL)) {
          continue;
        }
        long nbChildren = childrenTypeToIgnore == null || childrenTypeToIgnore.isEmpty() ? duplicate.getChildCount()
            : duplicate.getChildren()
                .stream()
                .filter(d -> !childrenTypeToIgnore.contains(DocumentType.valueOf(d.getType())))
                .count();
        if (nbChildren > 0) {
          log.warn("Document '{}' moved to dead queue channel", document.getReference());
          addDeads(execution, document);
          return true;
        }
        else {
          duplicate.setStatusWithEnumValue(DocumentStatus.CANCEL);
          getDocumentService().saveDocument(duplicate);
          return false;
        }
      }
      return false;
    default:
      throw new IllegalArgumentException("Unsupported policy: " + policy);
    }
  }

  /*
   * -- QUERY --
   */

  /**
   * @param execution current execution (optional if contextual documents parameter is set)
   * @param repo query targeted repository (required)
   * @param query BQL request
   * @param documentClass document type (default {@link Document} type)
   * @param indexClass index type (required on {@link Repository#INDEX} query)
   * @param documents contextual documents (optional if execution is set)
   * @return search in the query targeted repository the requested documents
   */
  @SuppressWarnings("unchecked")
  public static final <D extends Document> List<Document> search(final DelegateExecution execution, final Repository repo,
      final Query query, final Class<D> documentClass, final Class<Indexable> indexClass, Collection<Document> documents) {
    // TODO adds sanity checks
    switch (repo) {
    case CONTEXT:
      return QueryHelper.search(query, Document.class, documents == null && execution != null ? getDocuments(execution) : documents,
          getNonPersistentPropertiesWithoutPrefix(execution));
    case DATABASE:
      return (List<Document>) getDocumentService().search(documentClass, query, null)
          .getContent();
    case INDEX:
      return (List<Document>) getDocumentService().searchIndexedDocuments(documentClass, indexClass, query, null);
    default:
      throw new IllegalArgumentException("Unsupported repository: " + repo);
    }
  }

  @SuppressWarnings("unchecked")
  public static final <D extends Document> List<Document> searchDocumentsWithIndexable(final DelegateExecution execution,
      final Repository repo,
      final Query query, final Class<D> documentClass, final Class<Indexable> indexClass, Collection<Document> documents) {
    switch (repo) {
    case CONTEXT:
      return QueryHelper.search(query, Document.class, documents == null && execution != null ? getDocuments(execution) : documents,
          getNonPersistentPropertiesWithoutPrefix(execution));
    case DATABASE:
      return (List<Document>) getDocumentService().searchDocumentsWithIndexable(documentClass, query, null);
    case INDEX:
      return (List<Document>) getDocumentService().searchIndexedDocuments(documentClass, indexClass, query, null);
    default:
      throw new UnsupportedOperationException("Unsupported repository: " + repo);
    }
  }

  /**
   * @param execution current execution (optional if contextual documents parameter is set)
   * @param repo query targeted repository (required)
   * @param query BQL request
   * @param documentClass document type (default {@link Document} type)
   * @param indexClass index type (required on {@link Repository#INDEX} query)
   * @param documents contextual documents (optional if execution is set)
   * @return search in the query targeted repository the requested documents
   */
  public static final long count(final DelegateExecution execution, final Repository repo,
      final Query query, final Class<Document> documentClass, final Class<Indexable> indexClass, Collection<Document> documents) {
    switch (repo) {
    case CONTEXT:
      return QueryHelper.search(query, Document.class, documents == null && execution != null ? getDocuments(execution) : documents)
          .size();
    case DATABASE:
      return getDocumentService().count(documentClass == null ? Document.class : documentClass, query);
    case INDEX:
      return getDocumentService().countIndexable(indexClass, query);
    default:
      throw new IllegalArgumentException("Unsupported repository: " + repo);
    }
  }

  public static String getDocumentsVar(final DelegateExecution execution) {
    String documentsVar = getVariable(execution, Variable.DOCUMENTS_VAR, DOCUMENTS.toString());
    return documentsVar;
  }

  public static String getIndexablesVar(final DelegateExecution execution) {
    return getVariable(execution, Variable.INDEXABLES_VAR, INDEXABLES.toString());
  }

  @SuppressWarnings("unchecked")
  public static <T> T convert(Object value, Class<T> type) throws ParseException {
    // TODO Find a best way to handle date converter
    if (Date.class.isAssignableFrom(type)) {
      return (T) new SimpleDateFormat("yyyy-MM-DD'T'hh:mm:ss").parse((String) value);
    }
    return CONVERTER.convertIfNecessary(value, type);
  }

  /**
   * Adds the given documents to the context. Replaces the duplicates.
   * <p/>
   * NOTE: if the documents are not persisted (identifier are <code>null</code>) the references is used as identifier for the unicity check
   * (see {@link Document#equals(Object)}
   * 
   * @param execution
   * @param documentsToAdd
   * @return the new list of documents.
   */
  public final static Set<String> addPartitionCollectionName(final DelegateExecution execution, String... nameToAdd) {
    Set<String> names = getVariable(execution, Variable.PARTITION_DOC_COLLECTIONS, new LinkedHashSet<String>());
    if (ArrayUtils.isEmpty(nameToAdd)) {
      return names;
    }
    names.addAll(Arrays.asList(nameToAdd));
    setVariable(execution, Variable.PARTITION_DOC_COLLECTIONS.toString(), names);
    return names;
  }

  public static List<Document> backupDocuments(DelegateExecution execution, List<Document> docs) {
    if (isEmpty(docs)) {
      return getBackupDocuments(execution);
    }

    final Set<Document> docsToAdd = docs.stream()
        .map(doc ->
        {
          Document clone = SerializationUtils.<Document> clone(doc);
          if (doc.isIndexed()) {
            clone.setIndexValue(SerializationUtils.clone(doc.getIndexValue()));
          }
          return clone;
        })
        .collect(Collectors.toSet());

    List<Document> backups = Optional.of(docsToAdd)
        .map(d ->
        {
          d.addAll(new LinkedHashSet<>(getBackupDocuments(execution)));
          return d;
        })
        .map(ArrayList::new)
        .orElseGet(ArrayList::new);
    execution.setVariable(Variable.DOCUMENTS_BACKUP.toString(), backups);
    return backups;
  }

  public static List<Document> getBackupDocuments(DelegateExecution execution) {
    return getVariable(execution, Variable.DOCUMENTS_BACKUP.toString(), new ArrayList<>());
  }

  public static boolean isCheckIntegrity(DelegateExecution execution) {
    return ofNullable(execution.getVariable(CHECK_INTEGRITY.toString()))
        .filter(Boolean.class::isInstance)
        .map(Boolean.class::cast)
        .orElse(Boolean.FALSE);
  }

  public static boolean isPersistDocumentsChecked(DelegateExecution execution) {
    return ofNullable(execution.getVariable(PERSIST_DOCUMENTS.toString()))
        .filter(Boolean.class::isInstance)
        .map(Boolean.class::cast)
        .orElse(Boolean.FALSE);
  }

  public static String getProcessRelaunchTopic(DelegateExecution execution) {
    return getVariable(execution, PROCESS_RELAUNCH_TOPIC);
  }

  public static void setProcessRelaunchPeriodTopicIfNecessary(DelegateExecution execution) {
    String processRelaunchTopic = getProcessRelaunchTopic(execution);
    if (processRelaunchTopic != null) {
      setVariable(execution, Variable.PROCESS_RELAUNCH_TOPIC.toString(), processRelaunchTopic + TOPIC_PERIOD);
    }
  }

  public static <T extends Trigger> T getJobTrigger(DelegateExecution execution) {
    return getVariable(execution, JOB_TRIGGER);
  }

  public static List<Indexable> searchIndexables(Class<Indexable> idxType, Query query) {
    return getDocumentService().searchIndexables(idxType, query, null)
        .getContent();
  }

  public final static void addDocumentError(final DelegateExecution execution, DocumentError documentError) {

    List<DocumentError> existingErrors = getDocumentErrors(execution);

    existingErrors.add(documentError);
    setVariable(execution, Variable.DOCUMENT_ERRORS.toString(), existingErrors);
  }

  public final static void addDocumentErrors(final DelegateExecution execution, List<DocumentError> documentErrors) {
    List<DocumentError> existingErrors = getDocumentErrors(execution);

    existingErrors.addAll(documentErrors);
    setVariable(execution, Variable.DOCUMENT_ERRORS.toString(), existingErrors);
  }

  public static List<DocumentError> getDocumentErrors(DelegateExecution execution) {
    return getVariable(execution, Variable.DOCUMENT_ERRORS, new ArrayList<>());
  }

  public static void addNonPersistentPropertiesToExecution(DelegateExecution execution, Map<String, Object> properties,
      String docUuid) {
    Map<String, Map<String, Object>> nonPersistentProperties = getNonPersistentProperties(execution);
    nonPersistentProperties.put(docUuid, properties);
    setVariable(execution, String.valueOf(Variable.NON_PERSISTENT_DOC_PROPERTIES), nonPersistentProperties);
  }

  public static Map<String, Map<String, Object>> getNonPersistentProperties(DelegateExecution execution) {
    return getVariable(execution, Variable.NON_PERSISTENT_DOC_PROPERTIES,
        new LinkedHashMap<>());
  }

  public static Map<String, Map<String, Object>> getNonPersistentPropertiesWithoutPrefix(DelegateExecution execution) {
    return getNonPersistentProperties(execution).entrySet()
        .stream()
        .map(entry -> {
          String key = entry.getKey();
          Map<String, Object> values = entry.getValue()
              .entrySet()
              .stream()
              .filter(e -> e.getKey()
                  .startsWith(NON_PERSISTENT_PROPERTY_PREFIX))
              .collect(Collectors.toMap(
                  e -> e.getKey()
                      .substring(NON_PERSISTENT_PROPERTY_PREFIX.length()).toLowerCase(),
                  Map.Entry::getValue
              ));
          return Map.entry(key, values);
        })
        .filter(e -> !e.getValue()
            .isEmpty())
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            Map.Entry::getValue
        ));
  }

  private static String getNonPersistentPropertyValue(DelegateExecution execution, DocumentFile documentFile, String propertyName){
    Map<String, Map<String, Object>> nonPersistentProperties = getNonPersistentProperties(execution);
    return ofNullable(nonPersistentProperties.get(ofNullable(documentFile)
        .map(DocumentFile::getDocument)
        .map(Document::getUuid)
        .orElse(null)))
        .map(properties -> properties.get(propertyName.toUpperCase()))
        .map(Object::toString)
        .orElse(null);
  }

  public static boolean verifyProcessingWay(DelegateExecution execution, String processingWay) {
    if (!SENDING.toString()
        .equals(processingWay) && !RECEIVING.toString()
        .equals(processingWay)) {
      addDeads(execution, getDocuments(execution));
      return false;
    }
    return true;
  }
}
