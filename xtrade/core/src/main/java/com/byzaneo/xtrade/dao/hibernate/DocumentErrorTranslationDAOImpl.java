package com.byzaneo.xtrade.dao.hibernate;

import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.DocumentErrorTranslationDAO;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.TypedQuery;
import java.util.List;

@Repository(DocumentErrorTranslationDAO.DAO_NAME)
public class DocumentErrorTranslationDAOImpl extends GenericJpaDAO<DocumentErrorTranslation, DocumentErrorTranslationId> implements DocumentErrorTranslationDAO {

  @Override
  @Transactional
  public void removeSpecificErrorTranslation(DocumentErrorTranslationId id) {
    this.getEntityManager().createQuery("DELETE FROM " + getEntityName() + " det WHERE det.id = :id AND det.standardError IS FALSE")
        .setParameter("id", id)
        .executeUpdate();
  }

  @Override
  @Transactional(readOnly = true)
  public List<DocumentErrorTranslation> getErrorTranslationByLang(String lang) {
    String jpql = "SELECT det FROM " + getEntityName() + " det";
    TypedQuery<DocumentErrorTranslation> query;

    if (lang != null && !lang.isBlank()) {
      jpql += " WHERE det.id.languageCode = :lang";
      query = getEntityManager().createQuery(jpql, DocumentErrorTranslation.class)
          .setParameter("lang", lang);
    } else {
      query = getEntityManager().createQuery(jpql, DocumentErrorTranslation.class);
    }

    return query.getResultList();
  }



}
