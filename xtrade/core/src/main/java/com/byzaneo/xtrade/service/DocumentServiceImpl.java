package com.byzaneo.xtrade.service;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.exception.ProcessInterruptedException;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.clause.OrClause;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.ReportedFutureTask;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.Report;
import com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction;
import com.byzaneo.xtrade.dao.*;
import com.byzaneo.xtrade.dao.DocumentDAO.DocumentField;
import com.byzaneo.xtrade.dao.mongo.MongoIndexableDocument;
import com.byzaneo.xtrade.process.*;
import com.byzaneo.xtrade.util.ReportedCallable;
import com.byzaneo.xtrade.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Iterators;
import com.mongodb.DBObject;
import org.activiti.engine.delegate.DelegateExecution;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonBinaryWriter;
import org.bson.codecs.*;
import org.bson.io.BasicOutputBuffer;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.*;
import javax.faces.model.SelectItem;
import javax.faces.model.*;
import javax.persistence.EntityNotFoundException;
import javax.persistence.metamodel.SingularAttribute;
import java.io.*;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.*;

import static com.byzaneo.commons.bean.FileType.ARCHIVE;
import static com.byzaneo.commons.bean.FileType.XML;
import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.BACKUP_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.WORK_DIR;
import static com.byzaneo.commons.ui.util.JSFHelper.SELECT_ITEM_COMPARATOR;
import static com.byzaneo.commons.ui.util.JSFHelper.toSelectItems;
import static com.byzaneo.commons.util.FileHelper.deleteFile;
import static com.byzaneo.commons.util.FileHelper.incrementFileName;
import static com.byzaneo.commons.util.FileHelper.isZip;
import static com.byzaneo.commons.util.PersistentHelper.isPersisted;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.isNull;
import static com.byzaneo.query.builder.Clauses.notEqual;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.xtrade.api.ArchiveStatus.ARCHIVED;
import static com.byzaneo.xtrade.api.DocumentStatus.NONE;
import static com.byzaneo.xtrade.api.DocumentStatus.PENDING;
import static com.byzaneo.xtrade.api.DocumentType.FOLDER;
import static com.byzaneo.xtrade.api.IndexableDocument.FIELD_OWNERS;
import static com.byzaneo.xtrade.process.DocumentPolicy.DEAD;
import static com.byzaneo.xtrade.process.DocumentPurgeProcess.info;
import static com.byzaneo.xtrade.util.DocumentBuilder.createDocument;
import static com.byzaneo.xtrade.util.DocumentHelper.findByFile;
import static com.byzaneo.xtrade.util.IndexHelper.getIndexByEntityRefs;
import static com.byzaneo.xtrade.util.IndexHelper.getRecursiveIndexes;
import static com.byzaneo.xtrade.util.IndexHelper.preSave;
import static com.byzaneo.xtrade.util.IndexHelper.setIndexByEntityRefs;
import static com.byzaneo.xtrade.util.ReportHelper.toReportDocument;
import static com.byzaneo.xtrade.util.ReportHelper.toXml;
import static java.io.File.createTempFile;
import static java.lang.String.format;
import static java.lang.System.currentTimeMillis;
import static java.nio.channels.Channels.newChannel;
import static java.nio.file.Files.copy;
import static java.nio.file.Files.deleteIfExists;
import static java.nio.file.Paths.get;
import static java.nio.file.StandardCopyOption.REPLACE_EXISTING;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singleton;
import static java.util.Collections.singletonList;
import static java.util.Collections.sort;
import static java.util.Collections.unmodifiableMap;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.reducing;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.MapUtils.isNotEmpty;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.io.FilenameUtils.getExtension;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;

/**
 * Service dedicated to the management of the {@link Document}
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Nov 12, 2011
 * @since 1.0
 */
@Service(DocumentService.SERVICE_NAME)
public class DocumentServiceImpl implements DocumentService, CancelableProcess {
  private static final Logger log = getLogger(DocumentServiceImpl.class);

  public static final int BATCH_SIZE = 1000;

  // SERVICEs
  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService config;

  // sanity jobs
  @Autowired
  @Qualifier(ExecutorService.SERVICE_NAME)
  private ExecutorService executorService;

  @Autowired
  @Qualifier(DocumentStatusService.SERVICE_NAME)
  private transient DocumentStatusService documentStatusService;

  // DAOs
  @Autowired
  @Qualifier(DocumentDAO.DAO_NAME)
  private DocumentDAO documentAO;

  @Autowired
  @Qualifier(DocumentFileDAO.DAO_NAME)
  private DocumentFileDAO documentFileAO;

  @Autowired
  @Qualifier(DeadDocumentDAO.DAO_NAME)
  private DeadDocumentDAO deadDocumentAO;

  @Autowired
  @Qualifier(CachingStatusDAO.DAO_NAME)
  private CachingStatusDAO cachingStatusAO;

  @Autowired
  @Qualifier(WorkflowDocumentStatusDAO.DAO_NAME)
  private WorkflowDocumentStatusDAO workflowDocumentStatusDAO;

  @Autowired
  @Qualifier(ReconciliationRelationDAO.DAO_NAME)
  private ReconciliationRelationDAO reconciliationRelationDAO;

  @Autowired
  @Qualifier(DocumentTimelineDAO.DAO_NAME)
  private DocumentTimelineDAO documentTimelineDAO;
  @Autowired
  @Qualifier(DocumentErrorDAO.DAO_NAME)
  private DocumentErrorDAO documentErrorDAO;

  // INDEXING
  @Autowired(required = false)
  @Qualifier(DocumentService.INDEX_OPERATIONS_NAME)
  private IndexOperations indexOperations;

  // PROCESS
  @Autowired(required = false)
  @Qualifier(DocumentPurgeProcess.SERVICE_NAME)
  private DocumentPurgeProcess documentPurgeProcess;

  @Autowired
  @Qualifier(DocumentTimelineService.SERVICE_NAME)
  private transient DocumentTimelineService documentTimelineService;

  @Autowired
  @Qualifier(DocumentErrorService.SERVICE_NAME)
  private transient DocumentErrorService documentErrorService;
  @Autowired(required = false)
  private Map<String, Indexer<?>> indexers;

  /** Root documentation directory */
  private File documentationDirectory;

  private File reportsDirectory;

  /** List of Collections Names */
  private List<String> documentCollectionNames;

  private ReentrantLock lockOnSharedMap;

  /*
   * -- LIFE CYCLE --
   */

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public void init() throws Exception {
    final long start = currentTimeMillis();
    log.info("STARTING DOCUMENT SERVICE...");

    final File data = this.config.getFile(DATA_DIR, getTempDirectory());
    this.documentationDirectory = new File(data, "docs");
    this.documentationDirectory.mkdirs();
    log.info("\t- Documentation Directory: {}", this.documentationDirectory);

    if (isIndexing()) {
      log.info("\t- Index operations on: {}", this.indexOperations.getName());
    }
    else {
      log.info("\t- Indexing is DISABLED");
    }

    this.reportsDirectory = new File(data, "reports");
    this.reportsDirectory.mkdirs();
    log.info("\t- Reports directory: {}", this.reportsDirectory);

    if (this.indexers == null) {
      this.indexers = emptyMap();
    }
    log.info("\t- Indexer count: {}", this.indexers.size());

    // - MIGRATION -
    // executeIssueScript(config, documentAO,
    // "XTD-426" // document's revision
    // );

    log.info("DOCUMENT SERVICE STARTED in {}ms.", currentTimeMillis() - start);
  }

  /** @see com.byzaneo.commons.service.Disposable#destroy() */
  @Override
  @PreDestroy
  public void destroy() {
    final long start = currentTimeMillis();
    log.info("STOPPING DOCUMENT SERVICE...");
    if (isIndexing()) {
      log.info("\t- Closing {} index operations", this.indexOperations.getName());
      this.indexOperations.close();
    }
    log.info("DOCUMENT SERVICE STOPPED in {}ms.", currentTimeMillis() - start);
  }

  /*
   * -- ACCESSORS --
   */

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentationDirectory() */
  @Override
  public Path getDocumentationDirectory() {
    return this.documentationDirectory.toPath();
  }

  @Override
  public Path getDefaultReportDirectory() {
    return this.reportsDirectory.toPath();
  }

  /*
   * -- SEARCH --
   */

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#getDocuments(java.lang.Class, org.springframework.data.domain.Pageable, java.util.List,
   *      java.util.List, java.lang.String, java.lang.String, java.lang.String, boolean, java.lang.String, java.util.Date, java.util.Date,
   *      com.byzaneo.xtrade.api.DocumentStatus[])
   */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> Page<D> getDocuments(Class<D> type, Pageable pageable, List<String> owners, List<FileType> filetypes,
      String doctype,
      String from, String to, boolean recipientsNotNull, String referenceLike, Date fromDate, Date toDate, DocumentStatusEntity... status)
      throws ServiceException {
    try {
      final List<String> froms = StringHelper.isBlank(from) ? null : singletonList(from);
      final List<String> tos = StringHelper.isBlank(to) ? null : singletonList(to);
      return this.documentAO.findBy(type, pageable,
          owners, filetypes, doctype, froms, tos, froms == null || tos == null, recipientsNotNull,
          referenceLike, fromDate, toDate, status);
    }
    catch (Exception e) {
      throw new ServiceException(e);
    }
  }

  @Override
  @Transactional(readOnly = true)
  public List<Document> getDocuments(List<Long> ids) {
    return this.documentAO.findByIds(ids);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#search(java.lang.String, org.springframework.data.domain.Pageable) */
  @Override
  @Transactional(readOnly = true)
  public Page<Document> search(String query, Pageable pageable) {
    return this.documentAO.search(query, pageable);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#search(com.byzaneo.query.Query, org.springframework.data.domain.Pageable) */
  @Override
  @Transactional(readOnly = true)
  public Page<Document> search(Query query, Pageable pageable) {
    return this.documentAO.search(query, pageable);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#search(com.byzaneo.query.Query) */
  @Override
  public List<Document> search(Query query) {
    return this.documentAO.search(query);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#search(java.lang.Class, java.lang.String, org.springframework.data.domain.Pageable) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> Page<D> search(Class<D> type, String query, Pageable pageable) {
    return this.documentAO.search(type, query, pageable);
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#search(java.lang.Class, com.byzaneo.query.Query,
   *      org.springframework.data.domain.Pageable)
   */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> Page<D> search(Class<D> type, Query query, Pageable pageable) {
    return this.documentAO.search(type, query, pageable);
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#searchDocumentsWithIndexable(java.lang.Class, com.byzaneo.query.Query,
   *      org.springframework.data.domain.Pageable)
   */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> List<D> searchDocumentsWithIndexable(Class<D> type, Query query, Pageable pageable) {
    try (Stream<D> rslts = documentAO.stream(type, query, null)) {
      List<D> documents = new ArrayList<>();
      rslts.forEach(doc -> {
        Indexable indexable = getIndexable(doc);
        if (indexable != null) {
          doc.setIndexValue(indexable);
        }
        else {
          // If we do not refer to an index in SQL do not raise any Warning and continues to process normally.
          // If an index is referenced in SQL but not present in Mongo, raise a Warning
          if (doc.getIndex()
              .isValid()) {
            log.warn("Failed to get indexable of document {}", doc);
          }
        }
        documents.add(doc);
      });
      return documents;
    }
    catch (Exception e) {
      log.error("Failed to get documents with indexable : ", e.getMessage());
      return Collections.emptyList();
    }
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#search(java.lang.Class, com.byzaneo.query.Query,
   *      org.springframework.data.domain.Pageable, java.lang.String[])
   */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> Page<D> search(Class<D> type, Query query, Pageable pageable, String... fetches) {
    return this.documentAO.search(type, query, pageable, fetches);
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#search(java.lang.Class, org.springframework.data.jpa.domain.Specification,
   *      org.springframework.data.domain.Pageable)
   */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> Page<D> search(Class<D> type, Specification<D> specification, Pageable pageable) {
    return this.documentAO.search(type, specification, pageable);
  }

  /*
   * -- STATISTICS --
   */

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#getReportCount(java.util.List, java.util.List,
   *      com.byzaneo.xtrade.dao.DocumentDAO.DocumentField, java.lang.String, List, java.util.List, java.util.List, boolean, java.util.List)
   */
  @Override
  @Transactional(readOnly = true)
  public Map<String, Map<String, Number>> getReportCount(List<DocumentField> groupFields, List<DatePeriod> periods,
      DocumentField countField,
      String owner, List<String> docType, List<String> froms, List<String> tos, boolean recipientsNotNull, List<DocumentStatus> status)
      throws ServiceException {
    return this.documentAO.reportCount(groupFields, periods, countField, owner, docType, froms, tos, recipientsNotNull, status);
  }

  /*
   * -- CRUD --
   */

  /** @see com.byzaneo.xtrade.service.DocumentService#saveDocuments(java.util.Collection) */
  @Override
  @Transactional
  public <D extends Document> List<D> saveDocuments(Collection<D> docs) {
    return saveDocuments(docs, true);
  }

  @Override
  @Transactional
  public <D extends Document> List<D> saveDocuments(Collection<D> docs, boolean updateIndex) {
    // keeps document's indexables as they could be detached from the
    // owning document by the persistent system (declared as transient
    // field)
    final Map<String, Index> indexByEntityRefs = isIndexing()
        ? getIndexByEntityRefs(docs)
        : null;

    // takes all the children documents
    List<Document> allDocuments = DocumentHelper.getFlattenDocuments((Collection<Document>) docs);
    setStatusWithObject(allDocuments);
    // stores the documents
    final List<D> documents = this.documentAO.storeAll(docs);

    populateDocumentCacheStatus(allDocuments);

    if (updateIndex && isIndexing() && isNotEmpty(indexByEntityRefs)) {
      // restores the indexables to their freshly stored documents
      setIndexByEntityRefs(documents, indexByEntityRefs);

      // stores the indexables
      this.saveDocumentIndexes(documents);
    }

    return documents;
  }

  protected <D extends Document> void setStatusWithObject(Collection<D> allDocuments) {
    for (Document doc : allDocuments) {
      if (documentStatusService.getDocumentStatusByCode(doc.getStatusAsString()) == null) {
        log.error("The status with code " + doc.getStatusAsString() + " doesn\'t exists");
      }
      doc.setStatus(documentStatusService.getDocumentStatusByCode(doc.getStatusAsString()));
    }
  }

  protected <D extends Document> void setStatusWithObject(D document) {
    setStatusWithObject(Arrays.asList(document));
  }

  protected <D extends Document> void populateDocumentCacheStatus(final List<D> allDocuments) {
    List<CachingStatus> csList = this.cachingStatusAO.findAll();
    if (csList == null)
      csList = new LinkedList<>();
    for (Document d : allDocuments) {
      if (StringUtils.isEmpty(d.getOwners()) || d.getIndexType() == null || d.getStatus() == null)
        continue;
      CachingStatus cs = new CachingStatus(d.getOwners(), (DocumentStatusEntity) d.getStatus(), d.getIndexType());
      if (!csList.contains(cs)) {
        csList.add(cs);
        this.cachingStatusAO.store(cs);
      }
    }
  }

  @Override
  @Transactional(readOnly = true)
  public <D extends Document> List<D> detachDocuments(Collection<D> docs) {
    return this.documentAO.detach(docs);
  }

  @Override
  @Transactional
  public <D extends Document> D save(D doc) {
    setStatusWithObject(doc);
    final List<D> docs = this.documentAO.storeAll(singletonList(doc));
    return isEmpty(docs) ? doc : docs.get(0);
  }

  @Override
  @Transactional
  public <D extends Document> List<D> saveAll(Collection<D> docs) {
    setStatusWithObject(docs);
    return this.documentAO.storeAll(docs);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#saveDocument(com.byzaneo.xtrade.bean.Document) */
  @Override
  @Transactional(readOnly = false)
  public <D extends Document> D saveDocument(D doc) {
    final List<D> docs = this.saveDocuments(singletonList(doc));
    return isEmpty(docs) ? doc : docs.get(0);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#count(com.byzaneo.query.Query) */
  @Override
  @Transactional(readOnly = true)
  public long count(Query query) {
    return this.count(Document.class, query);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#count(com.byzaneo.query.Query) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> long count(Class<D> type, Query query) {
    return this.documentAO.count(type, query);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#countIndexable(Class, Query) */
  @Override
  public <I extends Indexable> long countIndexable(Class<I> type, Query query) {

    return isIndexing() ? this.indexOperations.count(type, query) : 0L;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#countIndexable(Class, Query) */
  @Override
  public <I extends Indexable> long countIndexable(Class<I> type, final String jsonQuery) {
    return isIndexing() ? this.indexOperations.count(type, jsonQuery) : 0L;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#distinctIndexable(java.lang.Class, java.lang.String, com.byzaneo.query.Query) */
  @Override
  public <I extends Indexable, T> List<T> distinctIndexable(Class<I> type, String field, Query query, Class<T> result) {
    return isIndexing() ? this.indexOperations.distinct(type, field, query, result) : emptyList();
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocument(java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public Document getDocument(Long id) {
    return this.getDocument(Document.class, id, false);
  }

  @Override
  @Transactional(readOnly = true)
  public Document getDocumentLazy(Long id) {
    return documentAO.getDocumentLazy(id);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocument(java.lang.Class, java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> D getDocument(Class<D> type, Long id) {
    return this.getDocument(type, id, false);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocument(java.lang.Class, java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> D getDocument(Class<D> type, Long id, boolean loadIndex) {
    return loadIndex ? this.populateIndex(documentAO.findById(type, id)) : documentAO.findById(type, id);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocument(java.lang.Class, com.byzaneo.xtrade.api.Indexable) */
  @Override
  @Transactional(readOnly = true)
  public Document getDocument(Indexable indexable) {
    return this.getDocument(Document.class, indexable);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocument(java.lang.Class, com.byzaneo.xtrade.api.Indexable) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> D getDocument(Class<D> type, Indexable indexable) {
    if (!isPersisted(indexable) || !isIndexing()) {
      return null;
    }
    final D document;
    try {
      document = this.indexOperations.findEntity(indexable, Document_.index.getName(), Index_.reference.getName());
    }
    catch (Exception e) {
      log.error("Error getting document from indexable: {} ({})", indexable, e.getMessage());
      return null;
    }
    if (document != null) {
      // restores the original index if the document
      // has been fetched from a generic IndexData index.
      if (indexable instanceof IndexData)
        this.populateIndex(document);
      else document.setIndexValue(indexable);
    }
    return document;
  }

  @Override
  @Transactional(readOnly = true)
  public Document getDocumentByUUID(String uuid) {
    return documentAO.findByUUID(uuid);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentByReference(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public Document getDocumentByReference(String ref) {
    return documentAO.findByReference(ref);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#findByIndexReferences(java.util.List) */
  @Override
  @Transactional(readOnly = true)
  public List<Document> findByIndexReferences(Set<String> entityRefs) {
    return documentAO.findByIndexReferences(entityRefs);
  }

  @Override
  @Transactional(readOnly = true)
  public Document findByIndexReferenceAndLoadFiles(String entityRef) {
    Document doc = documentAO.findByIndexReferences(new HashSet<>(Arrays.asList(entityRef)))
        .stream()
        .findFirst()
        .orElse(null);
    if (doc != null)
      Hibernate.initialize(doc.getFiles());
    return doc;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocuments(java.util.Collection) */
  @Override
  @Transactional
  public void removeDocuments(Collection<Document> docs) {
    this.removeDocuments(docs, false);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocument(com.byzaneo.xtrade.bean.Document) */
  @Override
  @Transactional
  public void removeDocument(Document doc) {
    this.removeDocument(doc, false);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocuments(java.util.Collection, boolean deleteFile) */
  @Override
  @Transactional
  public void removeDocuments(Collection<Document> docs, boolean deleteFile) {
    if (docs == null) {
      return;
    }

    docs.forEach(doc -> this.removeDocument(doc, deleteFile));
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocument(com.byzaneo.xtrade.bean.Document, boolean deleteFile) */
  @Override
  @Transactional
  public void removeDocument(Document doc, boolean deleteFile) {
    if (isPersisted(doc)) {
      // loads the document to remove
      final Document document = documentAO.findById(doc.getId());
      if (document == null)
        return;
      // remove records in Timeline table
      this.removeTimeLineRow(document);
      // remove records in workflowDocumentStatus table
      this.removeWkfDocumentStatus(document);
      // remove records in reconciliationRelation table
      this.removeReconciliationRelation(document);
      // remove documents error
      this.removeDocumentErrors(document);
      // removes the document and its children (cascading)
      this.documentAO.remove(document);

      // removes document's index and the ones of its children
      this.removeDocumentIndex(document);

      // removes the document and its children files
      if (deleteFile) {
        document.flattened()
            .map(Document::getFiles)
            .flatMap(List::stream)
            .map(DocumentFile::getFile)
            .forEach(FileHelper::deleteFile);
      }
    }
  }

  @Transactional
  public void removeDocumentErrors(Document doc) {
    if (isPersisted(doc)) {
      final List<DocumentError> documentErrors = documentErrorService.findErrors(doc);
      for (DocumentError documentError : documentErrors) {
        documentErrorDAO.remove(documentError);
      }
    }
  }

  /**
   * remove the timeline row from the DocumentTimeline table by document
   *
   * @param doc
   */
  @Transactional
  public void removeTimeLineRow(Document doc) {
    if (isPersisted(doc)) {
      // loads the document to remove
      final List<DocumentTimeline> documentTimelines = documentTimelineDAO.getAllTimelinesForDocument(doc.getId());
      for (DocumentTimeline documentTimeline : documentTimelines) {
        documentTimelineDAO.remove(documentTimeline);
      }

    }
  }

  @Transactional
  public void removeWkfDocumentStatus(Document doc) {
    if (isPersisted(doc)) {
      WorkflowDocumentStatus wkfDocStatus = workflowDocumentStatusDAO.getCurrentWorkflowDocumentStatusDocId(doc.getId());
      if (wkfDocStatus != null) {
        workflowDocumentStatusDAO.remove(wkfDocStatus);
      }
    }
  }

  public int removeReconciliationRelation(Document doc) {
    if (isPersisted(doc)) {
      reconciliationRelationDAO.deleteReceptionByDocId(doc.getId());
      reconciliationRelationDAO.deletePreviousInvoiceByDocId(doc.getId());
      reconciliationRelationDAO.deletePreviousInvoiceByRelation(doc.getId());
      return reconciliationRelationDAO.deleteRelationByDocId(doc.getId());
    }
    return 0;
  }

  @Override
  @Transactional
  public int deleteAllReconciliationRelations(Collection<Long> docsToDelete) {
    Collection<Long> relationIds = reconciliationRelationDAO.getRelationsByDocsId(docsToDelete);
    Collection<Long> receptionIds = reconciliationRelationDAO.getReceptionsIdsByDocsId(docsToDelete);
    Collection<Long> recPreviousInvoiceIds = reconciliationRelationDAO.getPreviousInvoiceByDocsId(docsToDelete);
    // both @see ReconciliationReception and ReconciliationPreviousInvoice have ReconciliationRelation as joinColumn so that is why we need
    // to add what was obtained in relationIds to the other 2
    receptionIds.addAll(relationIds);
    recPreviousInvoiceIds.addAll(relationIds);
    return documentAO.removeReconciliationOfDocuments(receptionIds, recPreviousInvoiceIds, relationIds);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocument(com.byzaneo.xtrade.bean.Document, boolean deleteFile) */
  @Override
  @Transactional
  public boolean removeDocuments(Indexable... indexables) {
    if (indexables == null)
      return false;
    final AtomicBoolean removed = new AtomicBoolean(false);
    stream(indexables)
        .filter(Objects::nonNull)
        .forEach(i -> {
          final Document document = this.getDocument(i);
          if (document == null)
            this.removeIndexable(i);
          else this.removeDocument(document, true);
          removed.compareAndSet(false, true);
        });
    return removed.get();
  }

  // FIELDS VALUES

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentTypes() */
  @Override
  @Transactional(readOnly = true)
  public List<String> getDocumentTypes() {
    return this.documentAO.findDocumentFieldValues(Document_.type.getName())
        .stream()
        .sorted()
        .collect(toList());
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentFroms() */
  @Override
  @Transactional(readOnly = true)
  public List<String> getDocumentFroms() {
    return this.documentAO.findDocumentFieldValues(Document_.from.getName())
        .stream()
        .sorted()
        .collect(toList());
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentTos() */
  @Override
  @Transactional(readOnly = true)
  public List<String> getDocumentTos() {
    return this.documentAO.findDocumentFieldValues(Document_.to.getName())
        .stream()
        .sorted()
        .collect(toList());
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#searchAttribute(javax.persistence.metamodel.SingularAttribute, com.byzaneo.query.Query,
   *      int, int)
   */
  @Override
  @Transactional(readOnly = true)
  public <T> List<T> searchAttribute(SingularAttribute<Document, T> attribute, Query query, int firstResult, int maxResults) {
    return this.documentAO.searchAttribute(attribute, query, 0, maxResults);
  }

  // DOCUMENT FILE

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentFile(java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public DocumentFile getDocumentFile(Long id) {
    return documentFileAO.findById(id);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getDocumentFiles(java.util.Collection) */
  @Override
  @Transactional(readOnly = true)
  public List<DocumentFile> getDocumentFiles(Collection<Long> ids) {
    if (isEmpty(ids)) {
      return emptyList();
    }

    return ids.stream()
        .map(documentFileAO::findById)
        .filter(dof -> dof != null)
        .collect(toList());
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getContent(com.byzaneo.xtrade.bean.DocumentFile) */
  @Override
  public InputStream getContent(final DocumentFile dof) {
    if (dof == null || dof.getUri() == null) {
      return null;
    }

    String uri = dof.getUri();
    try {
      if (uri.startsWith("file:")) {
        return new FileInputStream(dof.getFile());
      }
    }
    catch (FileNotFoundException e) {
      throw new IllegalArgumentException(getRootCauseMessage(e), e);
    }

    return null;
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#getUri(java.lang.String, java.io.File, java.lang.String, java.lang.String,
   *      java.lang.String)
   */
  @Override
  public String getUri(final String name, final File file, final String scheme, final String encoding, final String mimetype) {
    if (!"file".equals(scheme)) {
      throw new UnsupportedOperationException("Only 'file' scheme is yet supported");
    }
    if (file == null || !file.exists()) {
      throw new ServiceException("File not found: %s", file);
    }
    return file.toURI()
        .toString();
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#getUri(java.lang.String, java.io.InputStream, java.lang.String, String, String, File)
   */
  @Override
  public String getUri(final String name, final InputStream content, final String scheme, final String encoding, final String mimetype,
      final File directory) {
    if (content == null) {
      return null;
    }

    // TODO manages URI schemes

    // resolves the parent directory
    final File parent = directory == null ? new File(this.config.getFile(DATA_DIR, getTempDirectory()), "files") : directory;

    // resolves the file
    final File file;
    try {
      parent.mkdirs();
      file = isNotBlank(name) ? incrementFileName(new File(parent, name)) : createTempFile("file-", ".xtd", parent);
    }
    catch (Exception e) {
      throw new ServiceException(e, "Error creating file in: %s", parent);
    }

    // writes the file
    try (final FileOutputStream output = new FileOutputStream(file);
        final ReadableByteChannel channel = newChannel(content);) {
      output.getChannel()
          .transferFrom(channel, 0, content.available());
    }
    catch (Exception e) {
      throw new ServiceException(e, "Error writing file: %s", e);
    }

    return file.toURI()
        .toString();
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#search(com.byzaneo.query.Query) */
  @Override
  public List<DocumentFile> searchFiles(Query query) {
    return this.documentFileAO.search(query);
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#addFileToDocument(com.byzaneo.xtrade.api.Indexable, java.io.File, java.lang.String,
   *      java.lang.String)
   */
  @Override
  public void addFileToDocument(Indexable target, File file, String action, String comment) {
    if (target == null || file == null)
      throw new IllegalArgumentException("target and file must not be null");
    // gets the document from its indexable
    final Document doc = getDocument(target);
    if (doc == null)
      throw new EntityNotFoundException(String.format("no document found for indexable [%s]", target.toString()));

    // creates and adds the document file
    doc.addFile(new DocumentFile(file, getType(file), action, null, doc, comment));

    // persists the document
    saveDocument(doc);
  }

  /*
   * -- DOCUMENTATION MANAGEMENT --
   */

  /** @see com.byzaneo.xtrade.service.DocumentServiceImpl#getRootFolder(java.lang.String) */
  @Override
  @Transactional
  public Document getRootFolder(String owner) {
    return this.getRootFolder(owner, DOCUMENT_ROOT_REFERENCE, true);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getRootFolder(java.lang.String, java.lang.String) */
  @Override
  @Transactional
  public Document getRootFolder(String owner, String name) {
    return this.getRootFolder(owner, name, true);
  }

  /** @see DocumentService#getRootFolder(String, String, boolean) */
  @Override
  @Transactional
  public Document getRootFolder(String owner, String name, boolean createIfMissing) {
    return ofNullable(this.documentAO.findFolderRootByOwnerAndReference(owner, name))
        .orElseGet(() -> createIfMissing
            ? this.documentAO.store(createDocument()
                .reference(name)
                .owners(owner)
                .type(FOLDER.toString())
                .status(NONE.name())
                .build())
            : null);
  }

  /** @see com.byzaneo.xtrade.service.DocumentServiceImpl#getFolder(com.byzaneo.xtrade.bean.Document, java.lang.String) */
  @Override
  @Transactional
  public Document getFolder(Document parentFolder, String folderName) {
    return ofNullable(this.documentAO.findFolderByParentAndName(parentFolder, folderName))
        .orElseGet(() -> parentFolder == null || isBlank(folderName)
            ? null
            : this.documentAO.store(createDocument()
                .parent(parentFolder)
                .reference(folderName)
                .owners(parentFolder.getOwners())
                .type(FOLDER.toString())
                .status(NONE.name())
                .build()));
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#addFolderFile(com.byzaneo.xtrade.bean.Document, java.io.File) */
  @SuppressWarnings("squid:S1226")
  @Override
  @Transactional
  public DocumentFile addFolderFile(Document folder, File file) throws FileAlreadyExistsException {
    final DocumentFile dof = createFolderFile(folder, file.getName(), null, null, null);

    // Copies the file to the document folder
    // Note: the given file has to be outside
    // the documentation folder
    try {
      copy(get(file.getAbsolutePath()),
          get(dof.getFile()
              .getAbsolutePath()),
          REPLACE_EXISTING);
    }
    catch (IOException e) {
      throw new ServiceException("Impossible to move file from %s to %s",
          file.getAbsolutePath(), dof.getFile()
              .getAbsolutePath());
    }

    // adds and store the dof
    folder.addFile(dof);
    folder = this.saveDocument(folder);
    return findByFile(singleton(folder), dof.getFile());
  }

  /** @see DocumentService#addFolderFile(Document, String, String, String, Integer, byte[]) */
  @SuppressWarnings("squid:S1226")
  @Override
  @Transactional
  public DocumentFile addFolderFile(Document folder, String filename, String fileAuthor, String fileDescription, Integer daysBeforeDeletion,
      byte[] fileContents) {
    folder = this.documentAO.findById(folder.getId());
    final DocumentFile dof = createFolderFile(folder, filename, fileAuthor, fileDescription, daysBeforeDeletion);

    // Writes the file
    try (FileOutputStream fos = new FileOutputStream(dof.getFile())) {
      fos.write(fileContents);
      dof.setType(getType(dof.getFile()));
      folder.addFile(dof);
      return this.documentFileAO.store(dof);
    }
    catch (Exception e) {
      throw new ServiceException(e, "Error adding file %s", filename);
    }
  }

  @Override
  @Transactional
  public Document updateFolderFile(Document folder, DocumentFile file, byte[] fileContents) throws FileNotFoundException {
    if (folder == null || file == null) {
      return null;
    }
    // Writes the file
    try (FileOutputStream fos = new FileOutputStream(file.getFile())) {
      fos.write(fileContents);
      file.setType(getType(file.getFile()));
      folder.addFile(file);
      this.documentFileAO.store(file);
      log.info("Change content of file :" + file.getUri());
      return this.documentAO.findById(folder.getId());
    }
    catch (Exception e) {
      throw new ServiceException(e, "Error in update file %s", file.getFile()
          .getName()); // I18N
    }
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeFile(com.byzaneo.xtrade.bean.Document, com.byzaneo.xtrade.bean.DocumentFile) */
  @Override
  @Transactional
  public Document removeFile(Document folder, DocumentFile file) throws ServiceException {
    if (folder == null || file == null) {
      return folder;
    }
    this.documentFileAO.remove(this.documentFileAO.findById(file.getId()));
    deleteFile(file.getFile());
    return this.documentAO.findById(folder.getId());
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeFolder(com.byzaneo.xtrade.bean.Document, boolean) */
  @Override
  @Transactional
  public void removeFolder(Document folder, boolean deleteFiles) {
    if (folder == null || folder.getId() == null) {
      return;
    }
    final Document toRemove = this.getDocument(folder.getId());
    if (toRemove == null) {
      return;
    }
    // removes children
    if (toRemove.getChildCount() > 0) {
      toRemove.getChildren()
          .forEach(child -> this.removeFolder(child, deleteFiles));
    }
    // removes dof's files
    if (deleteFiles && isNotEmpty(toRemove.getFiles())) {
      toRemove.getFiles()
          .forEach(dof -> deleteFile(dof.getFile()));
    }
    // removes document folder
    this.documentAO.remove(toRemove);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#fileNameHasChanged(com.byzaneo.xtrade.bean.Document, long, java.lang.String) */
  @Override
  @Transactional
  public boolean fileNameHasChanged(Document folder, long fileId, String newName) {
    return !isNotEmpty(DocumentHelper.searchFiles(singleton(folder), format("id = \"%s\" and comment = \"%s\"", fileId, newName)));
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#renameFile(com.byzaneo.xtrade.bean.Document, com.byzaneo.xtrade.bean.DocumentFile,
   *      java.lang.String)
   */
  @Override
  @Transactional
  public Document renameFile(Document folder, DocumentFile file, String newName) throws ServiceException, FileAlreadyExistsException {
    if (isNotEmpty(DocumentHelper.searchFiles(singleton(folder), format("comment = \"%s\"", newName)))) {
      throw new FileAlreadyExistsException(file.getComment());
    }
    file.setComment(newName);
    this.documentFileAO.store(file);
    return this.documentAO.findById(folder.getId());
  }

  @Override
  @Transactional
  public DocumentFile setDocumentFile(DocumentFile file) {
    return this.documentFileAO.store(file);
  }

  /*
   * -- STATUS --
   */

  @Override
  public void markDocumentConsultStatusAsRead(Indexable indexable, boolean canUpdateConsultStatus, String username) {
    if (!canUpdateConsultStatus)
      return;
    Document document = this.getDocument(indexable);
    if (document != null && DocumentConsultStatus.NONE == document.getConsultStatus()) {
      document.setConsultStatus(DocumentConsultStatus.READ);
      this.saveDocument(document);
      DocumentTimeline docTimeline = new DocumentTimeline(document, new Date(), TimelineAction.CONSULT,
          username != null ? username : DocumentTimeline.USER_SYSTEM);
      documentTimelineService.save(docTimeline);

    }
  }

  /*
   * -- INDEXING --
   */

  /** @see com.byzaneo.xtrade.service.DocumentService#isIndexing() */
  @Override
  public boolean isIndexing() {
    return this.indexOperations != null;
  }

  // - INDEXABLE: SAVE -

  /** @see com.byzaneo.xtrade.service.DocumentService#saveDocumentIndex(com.byzaneo.xtrade.bean.Document) */
  @Override
  @SuppressWarnings({ "unchecked", "squid:S1226" })
  public <D extends Document> D saveDocumentIndex(D document) {
    if (document != null && document.isIndexed() && isIndexing()) {
      // prepares the document's index
      final Indexable indexable = preSave(document);

      // saves index
      boolean newIndex = indexable.getEntityId() == null;
      this.indexOperations.save(indexable);

      // updates reference: entity -> index
      // should we use the reference only? (no need to persist)
      if (newIndex) {
        document.setIndexValue(indexable);
        document = (D) this.documentAO.store(document);
      }
    }
    return document;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#saveDocumentIndexes(Collection) */
  @Override
  public <D extends Document> List<D> saveDocumentIndexes(Collection<D> documents) {
    if (isNotEmpty(documents) && isIndexing()) {
      // batch save new indexables
      this.indexOperations.saveAll(getRecursiveIndexes(documents, indexers));
    }
    return documents == null ? emptyList() : new ArrayList<>(documents);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#saveIndexable(com.byzaneo.xtrade.api.Indexable) */
  @Override
  public <I extends Indexable> I saveIndexable(I indexable) {
    if (indexable != null && isIndexing()) {
      this.indexOperations.save(indexable);
    }
    return indexable;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#saveIndexables(java.util.Collection) */
  @SuppressWarnings("squid:S1226")
  @Override
  public <I extends Indexable> Collection<I> saveIndexables(Collection<I> indexables) {
    if (isNotEmpty(indexables) && isIndexing()) {
      indexables = this.indexOperations.saveAll(indexables);
    }
    return indexables;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#saveIndexables(Collection) */
  @Override
  public <I extends com.byzaneo.xtrade.bean.Indexable> List<I> saveContextIndexables(List<I> indexables) {
    if (isNotEmpty(indexables) && isIndexing()) {
      return new ArrayList<I>(this.indexOperations.saveAll(indexables));
    }
    return indexables;
  }

  // - INDEXABLE: GET -

  /** @see com.byzaneo.xtrade.service.DocumentService#populateIndex(com.byzaneo.xtrade.bean.Document) */
  @Override
  public <D extends Document> D populateIndex(D document) {
    this.getIndexable(document);
    return document;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getIndexable(com.byzaneo.xtrade.bean.Document) */
  @Override
  public <I extends Indexable> I getIndexable(Document document) {
    return document == null
        ? null
        : this.<I> getIndexable(document.getIndex())
            .orElse(null);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getIndexable(com.byzaneo.xtrade.bean.Index) */
  @Override
  public <I extends Indexable> Optional<I> getIndexable(Index index) {
    return isIndexing() && this.indexOperations.populateIndex(index)
        ? ofNullable(index.getValue())
        : empty();
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#getIndexable(java.lang.Class, java.lang.String) */
  @Override
  public <I extends Indexable> I getIndexable(Class<I> type, String identifier) {
    return type == null || identifier == null || !isIndexing() ? null : this.indexOperations.findById(type, identifier);
  }

  // - INDEXABLE: REMOVE -

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocumentIndex(com.byzaneo.xtrade.bean.Document) */
  @Override
  public void removeDocumentIndex(Document document) {
    if (document == null) {
      return;
    }
    this.removeIndex(document.getIndex());
    document.setIndexValue(null);
    try {
      Set<Document> children = document.getChildren();
      if (children != null) {
        children.forEach(this::removeDocumentIndex);
      }
    }
    catch (Exception e) {
      log.warn("Failed to get children for document {} : {}", document.getReference(), e.getMessage());
    }
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeDocumentIndexes(java.util.Collection) */
  @Override
  public void removeDocumentIndexes(Collection<Document> documents) {
    if (isNotEmpty(documents) && isIndexing()) {
      // removes
      this.indexOperations.remove(getRecursiveIndexes(documents));
      // clears the documents indexes
      documents.stream()
          .filter(document -> document != null)
          .forEach(document -> document.setIndex(null));
    }
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeIndexables(java.util.Collection) */
  @Override
  public void removeIndexables(Collection<? extends Indexable> indexables) {
    if (isIndexing()) {
      this.indexOperations.remove(indexables);
    }
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeIndex(com.byzaneo.xtrade.bean.Index) */
  @Override
  public void removeIndex(Index index) {
    if (isIndexing() && index.getId() != null) {
      this.indexOperations.remove(index);
    }
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#removeIndexable(com.byzaneo.xtrade.api.Indexable) */
  @Override
  public void removeIndexable(Indexable indexable) {
    if (indexable != null && isIndexing()) {
      this.indexOperations.remove(indexable);
    }
  }

  // - INDEXABLE: SEARCH -

  /** @see com.byzaneo.xtrade.service.DocumentService#searchIndexable(java.lang.Class, java.lang.String) */
  @Override
  public <I extends Indexable> I searchIndexable(Class<I> type, String jsonQuery) {
    return isIndexing() ? this.indexOperations.findOneByJson(type, jsonQuery) : null;
  }

  @Override
  public <I extends Indexable> I searchIndexable(Class<I> type, Query query) {
    return isIndexing() ? this.indexOperations.findOneByQuery(type, query) : null;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#searchIndexables(java.lang.Class, java.lang.String, Pageable) */
  @Override
  public <I extends Indexable> Page<I> searchIndexables(Class<I> type, String jsonQuery, int limit, Pageable pageable) {
    return isIndexing() ? this.indexOperations.findByJson(type, jsonQuery, limit, pageable) : new PageImpl<>(emptyList());
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#searchIndexables(java.lang.Class, com.byzaneo.query.Query, Pageable) */
  @Override
  public <I extends Indexable> Page<I> searchIndexables(Class<I> type, Query query, Pageable pageable) {
    return isIndexing() ? this.indexOperations.findByQuery(type, query, pageable) : new PageImpl<>(emptyList());
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#searchIndexData(java.lang.String, com.byzaneo.query.Query,
   *      org.springframework.data.domain.Pageable)
   */
  @Override
  public Page<MongoIndexableDocument> searchIndexData(String collection, Query query, Pageable pageable) {
    return isIndexing() ? this.indexOperations.findIndexData(collection, query, pageable) : new PageImpl<>(emptyList());
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#searchIndexData(String, Query, Pageable, Function) */
  @Override
  public <I extends IndexData> Page<I> searchIndexData(String collection, Query query, Pageable pageable,
      Function<? super Map<String, Object>, I> mapper) {
    return isIndexing()
        ? this.indexOperations.findIndexData(collection, query, pageable, mapper)
        : new PageImpl<>(emptyList());
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#searchIndexedDocuments(java.lang.Class, java.lang.Class, com.byzaneo.query.Query,
   *      org.springframework.data.domain.Pageable)
   */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document, I extends Indexable> List<D> searchIndexedDocuments(Class<D> documentType, Class<I> indexableType,
      Query indexableQuery, Pageable pageable) {
    if (!isIndexing() || indexableQuery == null) {
      return emptyList();
    }

    final List<I> indexables = this.searchIndexables(indexableType, indexableQuery, pageable)
        .getContent();
    if (isEmpty(indexables)) {
      return emptyList();
    }

    return getDocuments(documentType, indexables);
  }

  // - INDEXABLE: OPERATIONS -

  @Override
  public boolean indexField(String collection, String field, String indexName, boolean ascending) {
    return !isIndexing()
        ? false
        : this.indexOperations.indexField(collection, field, indexName, ascending);
  }

  /* -- AGGREGATION -- */

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#aggregate(java.lang.Class, java.lang.Class, com.byzaneo.query.Query,
   *      org.springframework.data.mongodb.core.aggregation.AggregationOperation[])
   */
  @Override
  public <O, I extends Indexable> List<O> aggregate(Class<I> inputType, Class<O> outputType, Query match,
      AggregationOperation... operations) {
    if (!isIndexing() || inputType == null) {
      return emptyList();
    }

    // operations as list
    List<AggregationOperation> ops = operations != null && operations.length > 0
        ? new ArrayList<>(asList(operations))
        : new ArrayList<>(1);

    // converts query to match operation
    if (match != null) {
      ops.add(0, context -> new org.bson.Document("$match",
          context.getMappedObject(
              org.bson.Document.parse(this.indexOperations.toJson(inputType, null, match)))));
    }

    return this.aggregate(newAggregation(inputType, ops), outputType);
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#aggregate(org.springframework.data.mongodb.core.aggregation.TypedAggregation,
   *      java.lang.Class)
   */
  @Override
  public <O, I extends Indexable> List<O> aggregate(TypedAggregation<I> aggregation, Class<O> outputType) {
    if (!isIndexing() || aggregation == null || outputType == null) {
      return emptyList();
    }

    if (!this.indexOperations.isAggregationSupported()) {
      throw new ServiceException("Aggregation not supported for index operations: %s (supported only with MongoDB indexing)",
          this.indexOperations.getName());
    }

    final AggregationResults<O> result = ((MongoTemplate) this.indexOperations).aggregate(aggregation, outputType);
    return result == null || result.getMappedResults() == null ? Collections.<O> emptyList() : result.getMappedResults();
  }

  /* -- INDEX INTEGRITY -- */

  /** @see com.byzaneo.xtrade.service.DocumentService#getIndexers() */
  @Override
  public Map<String, Indexer<?>> getIndexers() {
    return unmodifiableMap(new TreeMap<>(indexers));
  }

  /*
   * -- REPORTING --
   */

  /** @see com.byzaneo.xtrade.service.DocumentService#createReportDocument(com.byzaneo.xtrade.api.Report, boolean, boolean, boolean) */
  @Override
  @Transactional
  public Document createReportDocument(com.byzaneo.xtrade.api.Report report, boolean generateXmlReport, boolean metadata,
      boolean throwOnError) {
    String xmlReportName = isNotBlank(report.getReference()) ? report.getReference()
        .replaceAll("[^\\w\\s\\-_]", "") : "REPORT-" + currentTimeMillis();
    File xmlReport = null;
    try {
      xmlReport = toXml(report, new File(reportsDirectory, xmlReportName + XML.getExtension()));
      report.setXmlFilePath(xmlReport.getAbsolutePath());
    }
    catch (Exception e) {
      log.error("Error creating xml report", e);
    }
    return createReportDocument(report, xmlReport, metadata, throwOnError);
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#createReportDocument(com.byzaneo.xtrade.api.Report, java.io.File, boolean, boolean) */
  @Override
  @Transactional
  public Document createReportDocument(com.byzaneo.xtrade.api.Report report, File xmlReport, boolean metadata, boolean throwOnError) {
    try {
      return saveDocument(toReportDocument(report, xmlReport, metadata));

    }
    catch (Exception e) {

      log.error("Error persisting indexing report document " + report.getLogsFilePath(), e);

      if (throwOnError) {
        throw new ServiceException(e, "Error persisting report document: %s", xmlReport.getPath());
      }

      return null;

    }
  }

  /*
   * -- ARCHIVING --
   */

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#archiving(Class, Query, String, String, String, boolean, boolean,
   *      DocumentChildrenPolicy, boolean)
   */
  @Override
  public ReportedFutureTask<Document> archiving(final Class<Document> type, final Query query,
      final String archiveTo, final String archiveFrom, final String archiveOwner,
      final boolean backup, final boolean remove,
      final DocumentChildrenPolicy childrenPolicy,
      final boolean dryRun) {

    // For now, we request a query to avoid full archiving
    // or mistaken method invocation
    notNull(query, "Archiving document's query is required");

    // - creates archive -
    // archive document
    final Document archive = new Document("ARC-" + currentTimeMillis(),
        isNotBlank(archiveOwner) ? archiveOwner : DocumentService.SERVICE_NAME,
        ARCHIVE.toString(),
        archiveFrom, archiveTo,
        PENDING);

    // archive file (only if backup is active and not a dry run)
    if (backup && !dryRun) {
      final File backupDir = new File(this.config.getFile(BACKUP_DIR, getTempDirectory()), "documents");
      backupDir.mkdirs();
      archive.addFile(new DocumentFile(
          new File(backupDir, archive.getReference() + ARCHIVE.getExtension()), FileType.ARCHIVE,
          "archiving", archiveTo, archive, null));
    }

    // - archiving -
    final ReportedFutureTask<Document> future = new com.byzaneo.xtrade.util.ReportedFutureTask<>(
        new ReportedCallable<Document>() {
          @Override
          public Document execute() throws Exception {
            // something to do?
            if (!backup && !remove) {
              archive.setStatusWithEnumValue(NONE);
              return archive;
            }
            // archiving...
            try {
              return documentAO.archiving(
                  type, query, archive,
                  remove, childrenPolicy, this,
                  indexOperations,
                  dryRun);
            }
            catch (Exception e) {
              report.getLogs()
                  .fatal(e, "Unexpected error occurred");
              report.setInProgress(false);
              return archive;
            }
          }
        });
    // executes future archiving
    this.executorService.getExecutor()
        .execute(future);

    return future;
  }

  /** @see com.byzaneo.xtrade.service.DocumentService#restoring(java.io.InputStream) */
  @Override
  public ReportedFutureTask<Document> restoring(InputStream stream) {
    notNull(stream, "Restoring stream is required");
    isTrue(isZip(stream), "The restoring stream has to be a Zip archive");

    // - extracts stream -
    final String archiveRef = "RST-" + currentTimeMillis();
    // archive document
    final Document archive = new Document(archiveRef,
        SERVICE_NAME,
        ARCHIVE.toString(),
        null, null,
        PENDING);
    final File restoreDir = new File(this.config.getFile(WORK_DIR, getTempDirectory()), "documents/" + archiveRef);
    final File restoreFile = new File(restoreDir, archiveRef + ".zip");
    restoreDir.mkdirs();
    archive.addFile(new DocumentFile(
        restoreFile, FileType.ARCHIVE,
        "restoring", SERVICE_NAME, archive, null));
    // extracts...
    try (InputStream input = new BufferedInputStream(stream)) {
      copy(input, restoreFile.toPath());
      // copyInputStreamToFile(stream, restoreFile);
    }
    catch (IOException e) {
      throw new UncheckedIOException(e);
    }

    // - restoring -
    final ReportedFutureTask<Document> future = new com.byzaneo.xtrade.util.ReportedFutureTask<>(
        new ReportedCallable<Document>() {
          @Override
          public Document execute() throws Exception {
            try {
              return documentAO.restore(archive, DEAD, indexOperations, this);
            }
            catch (Exception e) {
              report.getLogs()
                  .fatal(e, "Unexpected error occurred");
              report.setInProgress(false);
              return archive;
            }
          }
        });

    // executes future restoration
    this.executorService.getExecutor()
        .execute(future);

    return future;
  }

  /*
   * -- PRIVATE --
   */

  /**
   * @param folder folder
   * @param filename filename
   * @param author
   * @param fileDescription {@link DocumentFile#description File Description}
   * @return the new {@link DocumentFile} created (deletes existing one)
   */
  private synchronized DocumentFile createFolderFile(final Document folder, final String filename, final String author,
      String fileDescription, Integer daysBeforeDeletion) {
    // Parent directory
    final File parentDir = new File(this.documentationDirectory, folder.getId()
        .toString());
    parentDir.mkdirs();

    // DoF
    final DocumentFile dof = new DocumentFile();
    dof.setFile(new File(parentDir, format("H%s-%s.%s", filename.hashCode(), currentTimeMillis(), getExtension(filename))));
    dof.setComment(filename);
    dof.setAuthor(author);
    dof.setDescription(fileDescription);
    dof.setDaysBeforeDeletion(daysBeforeDeletion);

    // deletes existing physical file (if any)
    if (deleteFile(dof.getFile())) {
      log.warn("Existing physical folder's file deleted: {}", dof.getFile());
    }

    return dof;
  }

  /**
   * @param documentType documentType
   * @param indexables indexables
   * @return List
   */
  private <D extends Document, I extends Indexable> List<D> getDocuments(Class<D> documentType, List<I> indexables) {
    // optimization?
    return indexables.stream()
        .map(indexable -> this.getDocument(documentType, indexable))
        .filter(Objects::nonNull)
        .distinct()
        .collect(toList());
  }

  /** @see DocumentService#getCollectionNames() */
  @Override
  public List<String> getCollectionNames() {
    return this.isIndexing()
        ? this.indexOperations.getCollectionNames()
            .stream()
            .sorted()
            .collect(toList())
        : emptyList();
  }

  /** @see DocumentService#getDocumentCollectionNames() */
  @Override
  public List<String> getDocumentCollectionNames() {
    if (documentCollectionNames == null)
      documentCollectionNames = ClassPathScanningHelper.findAnnotatedClasses(
          org.springframework.data.mongodb.core.mapping.Document.class,
          "com/byzaneo/**/bean/**")
          .stream()
          .map(clazz -> clazz.getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class))
          .map(org.springframework.data.mongodb.core.mapping.Document::collection)
          .filter(StringUtils::isNotBlank)
          .distinct()
          .sorted()
          .collect(toList());
    return documentCollectionNames;
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentService#searchDeadDocuments(com.byzaneo.query.Query, org.springframework.data.domain.Pageable)
   */
  @Override
  @Transactional(readOnly = true)
  public Page<DeadDocument> searchDeadDocuments(Query query, Pageable pageable) {
    return deadDocumentAO.search(query, pageable);
  }

  @Override
  @Transactional
  public List<DeadDocument> saveDeadDocuments(List<Document> deads, String process) {
    if (CollectionUtils.isEmpty(deads)) {
      return Collections.emptyList();
    }
    List<DeadDocument> deadDocs = new ArrayList<>();
    deads.forEach(doc -> {
      DeadDocument dead = new DeadDocument();
      dead.setOwners(doc.getOwners());
      dead.setFrom(doc.getFrom());
      dead.setTo(doc.getTo());
      dead.setType(doc.getType());
      dead.setProcess(process);
      dead.setErrors(doc.getError());
      if (CollectionUtils.isNotEmpty(doc.getFiles())) {
        dead.setFiles(doc.getFiles()
            .stream()
            .map(DocumentFile::getFile)
            .map(File::getAbsolutePath)
            .collect(Collectors.toSet()));
      }
      deadDocs.add(dead);
    });
    return deadDocumentAO.storeAll(deadDocs);
  }

  @Override
  public <R extends Report> List<SelectItem> getReportFieldItems(Class<R> type, String owner, String field) {
    List<SelectItem> ownerItems = toSelectItems(distinctIndexable(type, FIELD_OWNERS, createQueryBuilder(owner).query(), String.class))
        .stream()
        .filter(si -> si.getLabel() != null && si.getValue() != null)
        .collect(Collectors.toList());
    List<SelectItem> processItems = new ArrayList<>(ownerItems.size());
    for (SelectItem item : ownerItems) {
      List<String> processes = distinctIndexable(
          type,
          field,
          createQueryBuilder((String) item.getValue()).query(), String.class);
      if (!processes.isEmpty()) {
        processItems.add(new SelectItemGroup(item.getLabel(), item.getLabel(), false,
            toSelectItems(processes)
                .stream()
                .sorted(SELECT_ITEM_COMPARATOR)
                .toArray(SelectItem[]::new)));
      }
    }
    sort(processItems, SELECT_ITEM_COMPARATOR);

    return processItems;
  }

  private QueryBuilder createQueryBuilder(String owner) {
    // creates reports query
    final QueryBuilder qb = createBuilder()
        .desc("creationDate");

    if (isNotBlank(owner)) {
      qb.and(equal(FIELD_OWNERS, owner));
    }
    return qb;
  }

  @Override
  public ReportedFutureTask<Document> purgeFutureTask(final Query query, final boolean readOnly) {
    final ReportedFutureTask<Document> future = new com.byzaneo.xtrade.util.ReportedFutureTask<>(
        new ReportedCallable<Document>() {
          @Override
          public Document execute() throws Exception {
            purge(query, null, readOnly, false, of(this));
            return null;/* no archive document */
          }
        });
    // executes future archiving
    this.executorService.getExecutor()
        .execute(future);

    return future;
  }

  @Override
  public int purge(Query purgeQuery, DelegateExecution execution, boolean readonly, boolean isReprocess,
      Optional<ReportedCallable<Document>> reporting) {

    // For now, we request a query to avoid full purge
    // or mistaken method invocation
    notNull(purgeQuery, "Purge document's query is required");

    try {

      // select all documents for the given query with an UNDEFINED or NULL stage or with ARCHIVED status
      // and that are not a FOLDER type document
      QueryBuilder queryBuilder = QueryBuilder.createBuilder(purgeQuery);
      final String applicationType = getConfigurationService().getProperties()
          .getProperty("app.type")
          .toLowerCase();
      Query query;
      if ("development".equals(applicationType) || "acceptance".equals(applicationType) || "preproduction".equals(applicationType)) {
        query = queryBuilder.and(notEqual("type", DocumentType.FOLDER))
            .query();
      }
      else {
        query = QueryBuilder.createBuilder(purgeQuery)
            .and(new OrClause(isNull("stage"),
                equal("stage", DocumentStage.UNDEFINED),
                equal("archiveStatus", ARCHIVED)),
                notEqual("type", DocumentType.FOLDER))
            .query();
      }

      // The Document purge Process deals with the SQL database purge
      final PurgeResult result = documentPurgeProcess.purge(query, execution, readonly, isReprocess, reporting);
      if (result == null) {
        info("No documents found", reporting);
        return 0;
      }
      // removes indexes
      if (log.isDebugEnabled()) {
        log.debug("Deleting " + result.getIndexes()
            .size() + " indexes");
      }

      info("Remove {} indexes", reporting, result.getIndexes()
          .size());

      final AtomicInteger countDelete = new AtomicInteger();
      Iterators.partition(result.getIndexes()
          .iterator(), DocumentPurgeProcess.BATCH_SIZE)
          .forEachRemaining(indexes -> {
            if (Thread.currentThread()
                .isInterrupted()) {
              throw new ProcessInterruptedException("User canceled execution. " + countDelete + " indexes already removed ");
            }
            Map<Class<?>, Set<String>> indexRefByCollection = indexes.stream()
                .filter(index -> Objects.nonNull(index.getType()))
                .collect(groupingBy(Index::getType, Collectors.mapping(Index::getReference, Collectors.toSet())));
            for (Entry<Class<?>, Set<String>> entry : indexRefByCollection.entrySet()) {

              Query deleteQuery = QueryBuilder.createBuilder()
                  .and(Clauses.in(Indexable.ENTITY_REF, entry.getValue()))
                  .query();
              Class<?> clazzType = entry.getKey();
              if (log.isDebugEnabled()) {
                log.debug("Deleting {} indexes: {} ", clazzType, entry.getValue());
              }
              int deleted = (!readonly ? (int) indexOperations.removeIndexByQuery(deleteQuery, clazzType) : 0);
              log.info("Deleting {} indexes..", clazzType.getSimpleName()/* type */);

              info(DocumentPurgeProcess.IDENT + "{}/{} indexes successfully removed", reporting,
                  countDelete.addAndGet(deleted), /* nb of documents */
                  result.getIndexes()
                      .size());
            }
          });

      // removes files
      int deleted = result.getFiles()
          .stream()
          .map(File::getAbsolutePath)
          .map(Paths::get)
          .collect(reducing(0, path -> deleteFileQuietly(path, readonly), Integer::sum));

      info("{}/{} files successfully removed", reporting, deleted, result.getFiles()
          .size());
      info("Document purged count: {}", reporting, result.getCount());
      reporting.ifPresent(r -> r.getReport()
          .setDocumentCount(result.getCount()));
      return result.getCount();
    }
    catch (ProcessInterruptedException e) {
      log.error("Purge process intterupted by user");
      throw e;
    }
    catch (Exception e) {
      log.error(String.format("Error exuecuting purge for query %s", purgeQuery.toString()), e);
      reporting.ifPresent(r -> r.getReport()
          .getLogs()
          .error(e, "Error exuecuting purge for query %s", purgeQuery.toString()));
    }
    finally {
      reporting.ifPresent(r -> r.getReport()
          .setInProgress(false));
    }
    return 0;
  }

  private int deleteFileQuietly(Path path, boolean readonly) {
    try {
      if (log.isDebugEnabled()) {
        log.debug("Deleting file " + path);
      }
      if (Files.isDirectory(path)) {
        if (!readonly) {
          FileHelper.deleteDirectory(path.toFile());
          return 1;
        }
        return 0;
      }
      else {
        return readonly ? 0 : deleteIfExists(path) ? 1 : 0;
      }
    }
    catch (IOException e) {
      log.warn("Error deleting document's file: {} ({})", path, getRootCauseMessage(e));
    }
    return 0;
  }

  @Override
  public long countDeadDocuments(Query query) {
    // TODO Auto-generated method stub
    return this.deadDocumentAO.count(DeadDocument.class, query);
  }

  @Override
  public List<String> searchByFieldsText(Query query, String field, String value, int limit) {
    return this.deadDocumentAO.searchPartialValueField(query, field, value, limit);
  }

  @Override
  public boolean removeDeadDocuments(DeadDocument... deads) {
    if (deads == null)
      return false;
    final AtomicBoolean removed = new AtomicBoolean(false);
    stream(deads).filter(Objects::nonNull)
        .forEach(d -> {
          this.deadDocumentAO.remove(this.deadDocumentAO.merge(d));
          removed.compareAndSet(false, true);
        });
    return removed.get();
  }

  @Override
  public List<DeadDocument> searchDeadDocumentsByFiles(Collection<String> files) {
    return deadDocumentAO.searchDeadDocumentsByFiles(files);
  }

  @Override
  public <I extends Indexable> Map<Long, Integer> getAttachFileCount(List<I> indexes, String... actions) {
    return this.documentFileAO.getAttachFileCountForDocuments(indexes.stream()
        .map(i -> (Long) i.getEntityId())
        .collect(Collectors.toList()), actions);
  }

  @Override
  public <I extends Indexable> List<String> getOwnersFromIdexable(Class<I> inputType) {
    return aggregate(inputType, DBObject.class, null, Aggregation.group("owners"))
        .stream()
        .map(d -> d.get("_id"))
        .map(Object::toString)
        .collect(Collectors.toList());
  }





  @Override
  public Collection<Document> searchAllChildren(Document doc, Query query) {

    Set<Document> result = new HashSet<Document>();
    result.add(doc);
    List<Document> firstLevel = documentAO.searchChildren(Arrays.asList(doc), query);
    if (firstLevel.isEmpty()) {
      return result;
    }
    result.addAll(firstLevel);
    List<Document> secondLevel = documentAO.searchChildren(firstLevel, query);
    if (secondLevel.isEmpty()) {
      return result;
    }
    result.addAll(secondLevel);
    List<Document> thirdLevel = documentAO.searchChildren(secondLevel, query);
    if (thirdLevel.isEmpty()) {
      return result;
    }
    result.addAll(thirdLevel);
    result.addAll(documentAO.searchChildren(thirdLevel, query));

    return result;
  }

  @Override
  @Transactional
  public boolean attachFileToADocFromRTE(String uuid, String fullPath, String type, String action, String subtype, String comment,
      List<Document> contextDocuments) throws ServiceException {
    Document document = null;

    if (uuid.isBlank()) {
      log.info(String.format("No document found for UUID: %s", uuid));
    }

    for (Document doc : contextDocuments) {
      if (uuid.equals(doc.getUuid()))
        // document from context
        document = doc;
    }
    if (document == null)
      document = getDocumentByUUID(uuid);
    if (document == null)
      throw new ServiceException("We can't find a document with the given uuid.");
    List<DocumentFile> documentFiles = document.getFiles();
    if (documentFiles != null) {
      for (DocumentFile documentFile : documentFiles) {
        if ((documentFile.getFile()
            .getPath()
            .replace("/", "")
            .replace("\\", ""))
            .equals(fullPath.replace("\\", "")
                .replace("/", "")))
          return false;
      }
    }
    File file = new File(fullPath);
    DocumentFile documentFile = new DocumentFile(file, FileType.valueOf(type), action, null,
        document, comment, null, subtype);
    document.addFile(documentFile);
    return true;
  }

  /*
  update lock column for a document already used when a process is starting
  */
  @Override
  public void updateDocumentLock(String lockProcessInstanceId, List<Long> docIds) {
    List<List<Long>> partitions = ListUtils.partition(docIds.stream()
        .collect(toList()), BATCH_SIZE);
    for (List<Long> partition : partitions) {
      documentAO.updateDocumentLock(lockProcessInstanceId, partition);
    }
  }

  @Override
  public ReentrantLock getMapLockOnMultithreading() {
    if (lockOnSharedMap == null)
      lockOnSharedMap = new ReentrantLock();
    return lockOnSharedMap;
  }

  public boolean isLockedByOtherThread() {
    return getMapLockOnMultithreading().isLocked() && !getMapLockOnMultithreading().isHeldByCurrentThread();
  }

  @Override
  public void unlockLockedDocumentsMap() {
    if (!getMapLockOnMultithreading().isLocked()) {
      return;
    }
    log.debug(" Unlocking map by Thread {} at {} ", Thread.currentThread(), LocalDateTime.now());
    getMapLockOnMultithreading().unlock();
  }

  @Override
  public void updateSqlDocumentStatus(List<String> invoices) {
    this.documentAO.updateSqlDocumentStatus(invoices);
  }

  @Override
  public boolean tryLockDocumentsMap() {
    if (getMapLockOnMultithreading().isHeldByCurrentThread()) {
      return true;
    }
    log.debug(" Try Locking map by Thread {} at {} ", Thread.currentThread(), LocalDateTime.now());
    return getMapLockOnMultithreading().tryLock();
  }

  public Map<String, Long> getDocumentsByGroup(String[] fieldNames, Query query) {
    return this.documentAO.countDocumentsGroupedByField(fieldNames, query);
  }

  @Override
  public Map<Document, String> getDocumentsOverLimit(List<Document> documents) {
    if (documents == null) return Collections.emptyMap();

    final long maxSize = 16 * 1024 * 1024; // 16MB
    return documents.stream()
        .filter(Objects::nonNull)
        .map(doc -> {
          long docSize = getObjectSize(doc.getIndex());
          return docSize > maxSize ? Map.entry(doc, String.format("%.2f", docSize / (1024.0 * 1024.0))) : null;
        })
        .filter(Objects::nonNull)
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  private long getObjectSize(Object object) {
    if (object == null) return -1;

    try (BasicOutputBuffer buffer = new BasicOutputBuffer();
        BsonBinaryWriter writer = new BsonBinaryWriter(buffer)) {

      Map<String, Object> map = new ObjectMapper().convertValue(object, new TypeReference<>() {
      });
      new DocumentCodec().encode(writer, new org.bson.Document(map), EncoderContext.builder()
          .build());
      return buffer.getSize();
    }
    catch (Exception e) {
      return -1;
    }
  }

  @Override

  public List<Object[]> getDocumentChangedStatus(String action, Date start, Date end, Document.ProcessingWay processingWay,
      List<String> selfBillingList, List<String> partnersList, int limit, int offset) {
    return this.documentAO.getDocumentChangedStatus(action, start, end, processingWay, selfBillingList, partnersList, limit, offset);

  }

}
