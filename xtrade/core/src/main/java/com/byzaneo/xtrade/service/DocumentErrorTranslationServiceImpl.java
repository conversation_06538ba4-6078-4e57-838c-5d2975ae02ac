package com.byzaneo.xtrade.service;

import com.byzaneo.query.builder.*;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.DocumentErrorTranslationDAO;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service(DocumentErrorTranslationService.SERVICE_NAME)
public class DocumentErrorTranslationServiceImpl implements DocumentErrorTranslationService {

  @Autowired
  @Qualifier(DocumentErrorTranslationDAO.DAO_NAME)
  private DocumentErrorTranslationDAO documentErrorTranslationDao;

  @Override
  @Transactional
  public void saveErrorTranslation(DocumentErrorTranslation documentErrorTranslation) {
    documentErrorTranslationDao
        .store(documentErrorTranslationDao.merge(documentErrorTranslation));
  }

  @Override
  @Transactional
  public void removeSpecificErrorTranslation(DocumentErrorTranslationId id) {
    documentErrorTranslationDao.removeSpecificErrorTranslation(id);
  }

  @Override
  @Transactional(readOnly = true)
  public DocumentErrorTranslation getErrorTranslationById(DocumentErrorTranslationId id) {
    return documentErrorTranslationDao.findById(id);
  }

  @Override
  public List<DocumentErrorTranslation> getErrorTranslationByLang(String Lang) {
    return documentErrorTranslationDao.getErrorTranslationByLang(Lang);
  }


  @Override
  @Transactional(readOnly = true)
  public Map<Locale, String> getLabelsForCode(String code) {
    return documentErrorTranslationDao.search(new QueryBuilder().and(Clauses.equal("id.errorCode", code))
            .query())
        .stream()
        .collect(
            Collectors.toMap(k -> new Locale(k.getId()
                .getLanguageCode()), v -> v.getErrorLabel()));
  }

}
