package com.byzaneo.xtrade.dao;

import com.byzaneo.commons.dao.GenericDAO;
import com.byzaneo.xtrade.bean.*;

import java.util.List;

public interface DocumentErrorTranslationDAO extends GenericDAO<DocumentErrorTranslation, DocumentErrorTranslationId> {
  public static final String DAO_NAME = "xtdDocumentErrorTranslationDAO";
  
  void removeSpecificErrorTranslation(DocumentErrorTranslationId id);
  List<DocumentErrorTranslation> getErrorTranslationByLang(String Lang) ;

}
