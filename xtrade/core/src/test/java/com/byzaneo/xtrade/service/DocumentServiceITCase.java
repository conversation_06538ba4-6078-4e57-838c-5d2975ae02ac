package com.byzaneo.xtrade.service;

import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.xtrade.api.ArchiveStatus.ARCHIVED;
import static com.byzaneo.xtrade.api.ArchiveStatus.UNDEFINED;
import static com.byzaneo.xtrade.service.DocumentService.DocumentChildrenPolicy.Cascade;
import static com.byzaneo.xtrade.service.DocumentService.DocumentChildrenPolicy.Detach;
import static com.byzaneo.xtrade.service.DocumentService.DocumentChildrenPolicy.Ignore;
import static java.util.Optional.empty;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.byzaneo.commons.bean.PropertyType;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.xtrade.api.ArchiveStatus;
import com.byzaneo.xtrade.api.DocumentStage;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.test.bean.IndexableTest;
import com.byzaneo.xtrade.util.DocumentHelper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer.MethodName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date 26 janv. 2014
 * @since 1.0
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/ci/ci.beans.xml"}, loader = SystemPropertyContextLoader.class)
@TestMethodOrder(MethodName.class)
public class DocumentServiceITCase {

  @Autowired
  private DocumentService service;

  @Autowired
  @Qualifier(DocumentStatusService.SERVICE_NAME)
  private DocumentStatusService statusService;

  private static List<Document> docs;
  private static List<Document> folders;
  private static List<Document> docsWithIndexes;

  @BeforeAll
  public static void beforeClass(@Autowired DocumentService service,
      @Autowired DocumentStatusService statusService) throws Exception {
    DocumentHelper.populateDocumentStatusEntityTable(statusService);
    if (docs != null) {
      return;
    }
    docs = new ArrayList<>(10);
    for (int i = 1; i <= 10; i++) {
      Document parent = createDocument(i, String.valueOf(i));
      for (int j = 1; j < 4; j++) {
        parent.addChild(createDocument("child", j, i + "-" + j));
      }
      docs.add(parent);
    }
    docs = service.saveDocuments(docs);

    docsWithIndexes = new ArrayList<>(10);
    for (int i = 1; i <= 10; i++) {
      docsWithIndexes.add(createDocument(i, String.valueOf(i)));
    }
    if (docsWithIndexes.get(0)
        .getIndexValue() == null) {
      IndexableTest index = new IndexableTest("indexDoc1");
      index.setEntityRef("ref1");
      index.setOwners("test1");
      docsWithIndexes.get(0)
          .setIndexValue(index);
    }
    if (docsWithIndexes.get(1)
        .getIndexValue() == null) {
      IndexableTest index = new IndexableTest("indexDoc2");
      index.setId("id");
      index.setOwners("test2");
      index.setEntityRef("ref2");
      docsWithIndexes.get(1)
          .setIndexValue(index);
    }
    docsWithIndexes = service.saveDocuments(docsWithIndexes);

    List<Document> docsToBePurged = new ArrayList<>(1000);
    for (int i = 11; i <= 50; i++) {
      Document parent = createDocument("ref_to_delete", i, String.valueOf(i));
      if (i % 5 == 0) {
        parent.setStage(DocumentStage.CORRECT);
      }
      if (i % 6 == 0) {
        parent.setArchiveStatus(ArchiveStatus.ARCHIVED);
      }
      for (int j = 1; j < 4; j++) {
        parent.addChild(createDocument("ref_to_delete_child", j, i + "-" + j));
      }

      docsToBePurged.add(parent);
    }

    docsToBePurged.add(createDocEdiFactWithChild(0, ARCHIVED));
    docsToBePurged.add(createDocEdiFactWithChild(1, UNDEFINED));

    service.saveDocuments(docsToBePurged);

    folders = new ArrayList<>(3);
    for (int i = 1; i <= 3; i++) {
      Document folder = createDocument("folder", i, String.valueOf(i));
      folder.setType(DocumentType.FOLDER);
      folder.setStage(DocumentStage.CORRECT);
      folder.setArchiveStatus(ArchiveStatus.ARCHIVED);
      folders.add(folder);
    }
    service.saveDocuments(folders);
  }

  private static Document createDocEdiFactWithChild(int n, ArchiveStatus archiveStatus)
      throws IOException {
    Document docWithChildrenNotArchived = createDocument("ref_to_edi_with_child", n,
        String.valueOf(n));
    docWithChildrenNotArchived.setType(DocumentType.INTERCHANGE_EDIFACT);
    docWithChildrenNotArchived.setStage(DocumentStage.UNDEFINED);
    int iMaxChild = 5;

    for (int i = n * iMaxChild + 2; i < (n + 1) * iMaxChild; i++) {
      Document child = createDocument("ref_to_edi_with_child", i, String.valueOf(i));
      child.setStage(DocumentStage.UNDEFINED);
      child.setArchiveStatus(archiveStatus);
      docWithChildrenNotArchived.addChild(child);
    }
    return docWithChildrenNotArchived;
  }

  @AfterAll
  public static void afterClass(@Autowired DocumentService service,
      @Autowired DocumentStatusService statusService) throws Exception {
    docs.forEach(doc -> service.removeDocuments(doc.getChildren()));
    service.removeDocuments(docs);
    folders.forEach(doc -> service.removeDocument(doc));
    Page<Document> reports = service.getDocuments(Document.class, null,
        Collections.singletonList("xtdDocumentService"), null,
        "REPORT", null, null, false, null, null, null,
        statusService.getDocumentStatusByCode(docs.get(0).getStatusAsString()));
    if (reports.getTotalElements() > 0) {
      reports.getContent()
          .forEach(doc ->
          {
            System.out.println("Doc ->" + doc);
            service.removeDocument(doc);
          });
    }

    docs = null;
  }

  @Test
  void parentQuery() {
    Page<Document> result = this.service.search(
        QueryBuilder.createBuilder()
            .and(
                Clauses.ilike("parent.reference", "ref"),
                Clauses.join("parent.model.properties",
                    Clauses.equal("key", "key1"),
                    Clauses.equal("value", "value12")),
                Clauses.join("parent.model.properties",
                    Clauses.equal("key", "key2"),
                    Clauses.equal("value", "value22")))
            .query(),
        null);
    // System.out.println("========================================================");
    // System.out.println(result.getContent());
    // System.out.println("========================================================");
    assertEquals(3, result.getTotalElements());
  }

  @Test
  void parentSpecification() {
    Page<Document> result = this.service.<Document>search(
        Document.class,
        new Specification<Document>() {
          @Override
          public Predicate toPredicate(Root<Document> root, CriteriaQuery<?> query,
              CriteriaBuilder cb) {
            // Join<?, ?> jm1 = root.join("parent").join("model").join("properties");
            // jm1.on(cb.equal(jm1.get("key"), "key1"));
            // Join<?, ?> jm2 = root.join("parent").join("model").join("properties");
            // jm2.on(cb.equal(jm2.get("key"), "key2"));
            // Path<String> p2 = jm2.get("value");
            // Predicate r = cb.and(
            // cb.like(jm1.<String>get("value"), "value1%"),
            // cb.like(p2, "value2%")
            // );
            // - OR -
            Join<?, ?> jm1 = root.join("parent")
                .join("model")
                .join("properties");
            Join<?, ?> jm2 = root.join("parent")
                .join("model")
                .join("properties");
            Path<String> p2 = jm2.get("value");
            Predicate r = cb.and(
                cb.and(
                    cb.equal(jm1.get("key"), "key1"),
                    cb.like(jm1.<String>get("value"), "value1%")),
                cb.and(
                    cb.equal(jm2.get("key"), "key2"),
                    cb.like(p2, "value2%")));
            query.orderBy(cb.desc(p2));
            return r;
          }
        }, Pageable.unpaged());
    // System.out.println("========================================================");
    // for (Document document : result.getContent()) {
    // System.out.println(document);
    // }
    // System.out.println("========================================================");
    assertEquals(156, result.getTotalElements());
  }

  /* -- REVISION -- */

  @Test
  void documentUpdateParent() throws Exception {
    Document doc = docs.get(3);
    long rev = doc.getRevision();
    doc.setReference(doc.getReference() + "_updated");
    docs = this.service.saveDocuments(docs);
    assertEquals(rev + 1l, doc.getRevision()
        .longValue());
  }

  @Test
  void documentUpdateChild() throws Exception {
    Document doc = docs.get(1)
        .getChildren()
        .iterator()
        .next();
    long rev = doc.getRevision();
    doc.setReference(doc.getReference() + "_updated");
    this.service.saveDocument(doc);
    assertEquals(rev + 1l, doc.getRevision()
        .longValue());
  }

  @Test
  void documentUpdateParentChild() throws Exception {
    Document parent = docs.get(4);
    long prev = parent.getRevision();
    Document child = parent.getChildren()
        .iterator()
        .next();
    long crev = child.getRevision();
    child.setReference(child.getReference() + "_updated");
    parent = this.service.saveDocument(parent);
    // TODO to check with functionnal team
    assertEquals(prev + 1l, parent.getRevision()
        .longValue());
    assertEquals(crev + 1l, child.getRevision()
        .longValue());
  }

  /* -- ARCHIVING -- */

  // TODO Firdaws
  @Test
  @Disabled
  void ztestArchiving() throws Exception {
    assertEquals(0, countArchiveEntries(this.service
        .archiving(
            Document.class,
            parse("reference ~ 'ref*'"),
            null, null, null,
            true, false,
            Ignore, false)
        .get()));

    assertEquals(0, countArchiveEntries(this.service
        .archiving(
            Document.class,
            parse("reference ~ 'ref*'"),
            null, null, null,
            true, true,
            Ignore, false)
        .get()));

    assertEquals(1, countArchiveEntries(this.service
        .archiving(
            Document.class,
            parse("reference = 'child1-1'"),
            null, null, null,
            true, true,
            Ignore, false)
        .get()));

    assertEquals(1, countArchiveEntries(this.service
        .archiving(
            Document.class,
            parse("reference = 'ref2'"),
            null, null, null,
            true, true,
            Detach, false)
        .get()));

    assertEquals(35, countArchiveEntries(this.service
        .archiving(
            Document.class,
            parse("reference ~ 'ref*'"),
            null, null, null,
            true, true,
            Cascade, false)
        .get()));
  }

  @Test
  void purge() {

    Query query = QueryHelper.toQuery("reference ~ 'ref_to_delete*'");

    getConfigurationService().getProperties()
        .setProperty("app.type", "Production");
    assertEquals(160, service.count(query));
    service.purge(query, null, false, false, empty());
    assertEquals(8, service.count(query));
    getConfigurationService().getProperties()
        .setProperty("app.type", "Development");
    service.purge(query, null, false, false, empty());
    assertEquals(0, service.count(query));

    query = QueryHelper.toQuery("reference ~ 'folder*'");
    getConfigurationService().getProperties()
        .setProperty("app.type", "Production");
    assertEquals(3, service.count(query));
    service.purge(query, null, false, false, empty());
    assertEquals(3, service.count(query));
    getConfigurationService().getProperties()
        .setProperty("app.type", "Development");
    assertEquals(3, service.count(query));
    service.purge(query, null, false, false, empty());
    assertEquals(3, service.count(query));
  }

  @Test
  void purgeEdiFactWithChildrenNotArchived() {
    Query query = QueryHelper.toQuery("reference ~ 'ref_to_edi_with_child*'");

    getConfigurationService().getProperties().setProperty("app.type", "Production");
    // At the beginning, we have two parents interChangeEdiFact with three children for each one
    assertEquals(8, service.count(query));
    service.purge(query, null, false, false, empty());
    /* We have the interChangeEdiFact (with ARCHIVED status) with three children that are purged and the other children of 
     * the other interChangeEdiFact (with UNDEFINED status). But not the parent of interChangeEdiFact (with UNDEFINED status) 
     because his children are not archived */
    assertEquals(1, service.count(query));
    getConfigurationService().getProperties().setProperty("app.type", "Development");
    assertEquals(1, service.count(query));
    service.purge(query, null, false, false, empty());
    // The final interChangeEdiFact remaining is purged because he has no child now
    assertEquals(0, service.count(query));
  }

  @Test
  void removeIndex() {
    Query query1 = QueryBuilder.createBuilder()
        .and(Clauses.equal("_entity_ref", "ref1"))
        .query();
    service.removeIndex(docsWithIndexes.get(0)
        .getIndex());
    assertNotNull(
        service.searchIndexable(com.byzaneo.xtrade.test.bean.IndexableTest.class, query1));

    Query query2 = QueryBuilder.createBuilder()
        .and(Clauses.equal("_entity_ref", "ref2"))
        .query();
    service.removeIndex(docsWithIndexes.get(1)
        .getIndex());
    assertNull(service.searchIndexable(com.byzaneo.xtrade.test.bean.IndexableTest.class, query2));
  }

  /* -- PRIVATES -- */

  @Test
  void getOwnersFromIdexable() {
    List<String> owners = service.getOwnersFromIdexable(IndexableTest.class);
    assertEquals(2, owners.size());
    assertEquals("test1", owners.get(0));
    assertEquals("test2", owners.get(1));
  }

  private int countArchiveEntries(Document document) throws ZipException, IOException {
    try (ZipFile zipFile = new ZipFile(document.getFirstFile())) {
      return zipFile.size();
    }
  }

  private static Document createDocument(int i, String index) throws IOException {
    return createDocument("ref", i, index);
  }

  private static Document createDocument(String refPrefix, int i, String index) throws IOException {
    Document r = new Document(
        refPrefix + index,
        "test" + index,
        (DocumentType.values()[i % 10] == DocumentType.FOLDER
            ? DocumentType.WKGRRE
            : DocumentType.values()[i % 10]).toString(),
        "form" + i % 2,
        "to" + i % 3,
        DocumentStatus.values()[(i % 2) + 2]);
    r.addModel("key1", PropertyType.String, null, "value1" + index);
    r.addModel("key2", PropertyType.String, null, "value2" + index);
    return r;
  }
}
