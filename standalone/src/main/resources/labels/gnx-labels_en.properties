# locale: en
about=About TradeXpress Evolution
about_short=About
agree=Agree
agreement=Service Agreement
agreement_lang_required=Service Agreement language is required
agreement_version_required=Service Agreement version is required
agreement_file_not_found={0} agreement file not found for {1} language
agreement_instance_required=Service Agreement instance is required
agreement_default_required=Service Agreement terms must be associated to the default language {0} (Service Agreement tab)
alpha=Alpha
alphanumeric=Alphanumeric
alphanumeric_underscore=Only alphanumeric and underscore are supported
accept=Accept
account=My Account
account_confirmation=Account confirmation creation
account_creation=Account creation
account_creation_confirm=Your account has been successfully created. You are going to receive a confirmation email to the address:
accounting=Customer Contact
action=Action
actions=Actions
action_global=Global action
action_line=Action on line
add=Add
add_above=Add above
add_below=Add below
add_certificate=Add certificate
add_child_page=Add child page
add_left=Add left
add_right=Add right
add_description=Add a description
administration=Administration
advSearch=Advanced search
analyze=Analyze
all=All
archive=Archive
archiving=Archiving
authentication=Authentication
authentication_mail_template=New contact notification email
author=Author
back=Back
backoffice=Back Office
backup=Backup
bad_account_or_password=Bad account or password.
banking=Banking
banking_partner=Banking partner
begin=Begin
boolean_false=False
boolean_select=--Select--
boolean_true=True
bql=BQL
button_edit=Edit
cancel=Cancel
control=Check
control_ean=Check length of EAN code field
correct=Correct
chat=Chat
choose=Choose
classificationPlan=Classification plan
clear=Clear
clear_page_confirm=Are you sure to clear this page content?
clear_portlet_confirm=Are you sure to clear this portlet content?
client=Client
clientPartners=My ${clients}
clientUsers=My users
close=Close
code=Code
company_code_validator=Code should respect the three capitalized letters format
instance_code_validator=Code should respect the three digits or capitalized letters format
partner_code_validator=Partner code should respect his customer three capitalized letters code format followed by his identification
collapse_all=Collapse All
comment=Comment
comment_upload_file=Please add a comment link to your attachment
communication=Communication
companies=Customers
company=Customer
company_edit= Edit customer {1} ({0})
company_add=Add customer
company_view=Customer {1} ({0})
company_number=${client} n\u00B0
company_society=Customer
complementary_info=Complementary Information
completion=Completion
completion_bounded=Completion Bounded
configuration=Configuration
configuration_page=Page configuration
configuration_portal=Portal configuration
configuration_portlet=Portlet configuration
confirm=Confirm
confirm_task_result_reset=The task\\'s results will be deleted. Do you want to continue?
connect=Connect
contact=Contact
contacts=Contacts
contact_admin=Please contact your administrator.
contact_subject=Subject of the contact email
contact_recipients=Recipients of the contact email
contact_us=CONTACT
contact_validator=Contact email address is not valid. Multiple addresses are allowed, separated by ';'.
content=Content
Contract=RoquetteContract
copy=Copier
count=Count
country=Country
create=Create
create_mail=Create mail
create_notifications=Create notification
creation=Creation Date
cut=Couper
dashboard=Dashboard
date=Date
date_last_authentication=Last connection
date_last_authentication_no_date= Last connection date : Information unavailable
date_to=at
date_read=Read Date
day=Day
day_of_month=Day of month
day_of_week=Day of week
decimal_separator=Decimal Separator
default=default
delete=Delete
delete_select=Would you like to permanently delete the selected items ?
delete_select_single=Would you like to permanently delete the selected item ?
deny=Deny
deny_message=Please, describe your denial
deploy_process=Deploy process
deploy=Deploy
deployment=Deployment
description=Description
description_short=Short Description
details=Details
disable=Disable
disabled=Disabled
disagree=Disagree
display=Display
display_extension=Display extension
displayed_extension_name=Displayed extension name
document_children_policy=Children document policy
document_type=Document's type
document=Document
documents=Documents
doc_edit=edit a document
domain=Domain
download=Download
duns=Reference
duplicate=Duplicate
duration=Duration
edit=Edit
edit_record=Edit
edit_mail=Edit mail
edit_notifications=Edit notification
editor=Editor
edocument=E-Document
eDocument=eDocument
domain_edit=Domain Edition
domain_modif=Domain modify
domain_create=Domain create
email=E-mail
emitter=Emitter
empty_page=Empty Page
enable=Enable
enabled=Enabled
enabled_linked_document_import=Enable import of document linked
encoding=Encoding
end=Fin
end_date=End date
error=Error
error_input_content_not_valid=The entered content is not valid : {0}
events=Events
execute=Execute
expand_all=Expand All
export=Export
export_extension_file=Export file
export_extension_all_files=Export files
export_list=Export list
export_portal=Exporter portal
extensions=Extensions
extension_label=Recording n\u00B0
extension_name=Extension Name
family=Family
field=field
field_missing=The field \"{0}\" is missing.
file=File
file_name=File name:
filter=Filter
freetext=Freetext
freetext_details=FreeText/Details
folder=Folder
folder_in=Input Folder
folder_out=Output Folder
format=Format
from_address=From address
from_address_validator=From address email is not valid. Only one address can be filled.
fullname=Fullname
gcn_identification_group=Generix Collaborative Network
gcn_subscriber_active=Generix client
gcnIdentification=GCN Id (gid)
gcnIdentification_error=GCN Id must contains at most 64 characters alphanumeric
gcnSubscriberIdentification=Generix Billing Id
gcnSubscriberIdentification_error=Generix Billing Id must contains at most 64 characters alphanumeric
general=General
general_edi_service=Generix EDI Services
general_invoices_service=Generix Invoices Services
general_supplier_service=Generix Suppliers Services
general_customers_service=Generix Customers Services
generate_gcn_subscriber=Generate ID
generate_new_password=Generate a password renewal link
generate_new_password_confirm=Are you sure you want to generate a password renewal link ?
generate_password=Generate password renewal link
identical_new_passoword=The new password is identical to the old password
generate_reset_link_expired=Password renewal link has expired.
generate_reset_link_invalid=Invalid password renewal link. Please contact your administrator.
good=good
help=Help
history=History
History=RoquetteHistory
home=Home
home_message=Accelerate your EDI/B2B roll-outs while reducing your costs with GCI Community Management
host=Host
hosts=Hosts
hour=Hour
hours=Hours
identification=Identification
identification_validator=Identification should contains only alphanumeric or '_' characters
iframe_error_message=Refused to display document because display forbidden by X-Frame-Options
ignored=Ignored
information=Information
information_system=Information System
import=Import
import_cert_jks_pkcs12=JKS/PKCS#12...
import_cert_pkcs11=PKCS#11...
import_cert_pem_cer_crt=PEM/CER/CRT
import_cert_pkcs7=PKCS#7
import_portal=Import portal
importInstance=Import (*.json)
indexClassName=Index Class
indexing=Indexing
infinite=Infinite
integration=Process:Activity
integrationnotification=Receive reporting for process integrations
dictionary=Business dictionaries
instance=Environment
instances=Environments
io=IO
insert=Insert
installation_detail=Installed Software
invalid_file_type=Invalid file type
invalid_file_csv_type=The extension file should be CSV
invalid_file_size=Invalid file size
invalid_file_special_char=The file can not contain special characters
invalid_file_accent_char=The file can not contain accented characters
invalid_generix_billing_id=Invalid Generix Billing ID
invalid_password=Invalid password. Please enter a new password.
is_locked=is locked
is_expired=is expired
keystores=Keystores
label_search=Enter your search here
label=Label
labels=Labels
lang_english=English
lang_french=French
language=Language
languages=Languages
last_modification=Last modification
locale_not_defined=Your language is not defined in your partners profile, please fill it
localisation=Portal Localisation
layout=Layout
left=Left
length=Length
length_max=Maximum Length
length_min=Minimum Length
library=Library
linked_certificates=Linked certificates
linked_document_import=Import attachment
linked_document_import_selection=Select one or more files
login=Login
login_validator=Login should contains at least 3 alphanumeric, '_', '-', '@' and '.' characters
logout=Sign Out
managed_by=Managed by
managed_by_order_issuer=Managed by OI
mark_as_read=Mark as read
max_size_of_linked_document=Max size of linked files
menurights = Menu Rights
companyinformation= My company informations
message=Message
messages=Messages
messaging=Messaging
milestone=Planned completion date
minutes=Minutes
modification=Modification date
modif_contact_partner= Contact partner modification
modify=Modify
monitoring=Activity
month=Month
move_bottom=Move bottom
move_down=Move down
move_left=Move left
move_rigth=Move right
move_top=Move top
move_up=Move up
my_bank_accounts=My bank accounts
bank_accounts=Bank accounts
name=Name
name_executable=Name of executable
new_message=New message
number_connexion=Number of connections
number_reviewed_messages=Number of messages reviewed
numeric=Numeric
no=No
no_data=No data
no_processed=Not processed
no_processes_were_found=The RTE is not called by any process
none=None
notification=Notification
notifications=Notifications
notification_new_contact=Email notifications new contact
notify=Notify
occurence=Occurrences
ok=OK
order_issuer=Order Issuer
other=Other
page=Page
pages=Pages
page_new=New Page
page_noselection=No Selected Page
parameters=Parameters
parameter=Parameter
partner=Partner
partner_add= Partner add
partner_edit= Partner edit {1} ({0})
partner_field_configuration=Partner's Fields Configuration
partner_field=Partner's Field
partner_view= Partner {1} ({0})
paste=Coller
Penalty=FranprixPenalty
period=Period
period_to=to
permission_missing=You have not enough permission to access this functionnality
permission_edit=Edit permissions for
permission_required=Selection is required
permissions=Permissions
permissions_dialog_campaign_title=Add partners in the campaign
permissions_dialog_page_title=Define access rights to the page
phone=Phone
portal=Portal
portlet=Portlet
portal_general=General
portlet_collection= Collection
portlet_order=Order
portlet_referentiel_produits=Referential/Products
portlet_taxes_allowances = Allowances and para-fiscal Taxes
portlet_referentiel_taxes=Referential/Taxes
portlet_referentiel_global_allowances_charges=Referential/Global allowances & charges
portlet_referentiel_produits_remise_charge=Referential/Products/Allowances & charges
portlet_referentiel_produits_other=Referential/Produits/Others
portlet_referentiel_produits_general_description=Referential/Products/General/Description
portlet_referentiel_produits_general_net_price=Referential/Products/General/Net Price
portlet_asn_ship=ASN : Ship
portlet_asn_import=ASN : Import
portlet_contact_us=Contact us
portlet_documentation=DOCUMENTATION
portlet_deadpool=Deadpool
portlet_faq=FAQ
portlet_freetext= FreeText
portlet_files=FILES
portlet_carousel=Carousel
portlet_indexdata_export=INDEXDATA : Export
portlet_order_confirm=ORDER: Confirm
portlet_order_confirm_with_modification=ORDER: Confirm with modification
portlet_order_mark_as_unread=ORDER: Mark as unread
portlet_order_actions_1=ORDER : Action 1
portlet_order_actions_2=ORDER : Action 2
portlet_order_actions_3=ORDER : Action 3
portlet_order_actions_4=ORDER : Action 4
portlet_order_actions_5=ORDER : Action 5
portlet_order_actions_6=ORDER : Action 6
portlet_order_actions_7=ORDER : Action 7
portlet_order_actions_8=ORDER : Action 8
portlet_order_actions_9=ORDER : Action 9
portlet_order_actions_10=ORDER : Action 10
portlet_invoice_join=INVOICE : Join
portlet_invoice_open=INVOICE : Open
portlet_invoice_print=INVOICE : Print
portlet_invoice_diagnostic=INVOICE : Diagnostic
portlet_invoice_refused_manually=INVOICE : Refuse manually
portlet_invoice_forced=INVOICE : Force
portlet_invoice_add=INVOICE : Add
portlet_invoice_import=INVOICE : Import
portlet_invoice_export=INVOICE : Export
portlet_invoice_export_list=INVOICE : Export list
portlet_invoice_view_attachment=INVOICE : View Attachment
portlet_invoice_view_history=INVOICE : Audit
portlet_invoice_remove=INVOICE : Remove
portlet_invoice_actions_1=INVOICE : Action 1
portlet_invoice_actions_2=INVOICE : Action 2
portlet_invoice_actions_3=INVOICE : Action 3
portlet_invoice_actions_4=INVOICE : Action 4
portlet_invoice_actions_5=INVOICE : Action 5
portlet_invoice_actions_6=INVOICE : Action 6
portlet_invoice_actions_7=INVOICE : Action 7
portlet_invoice_actions_8=INVOICE : Action 8
portlet_invoice_actions_9=INVOICE : Action 9
portlet_invoice_actions_10=INVOICE : Action 10
portlet_pack=Pack
portlet_uploadpdfclient=UploadPdfClient
portlet_referentiel_carrier=Referential/Carrier
portlet_switch_user=Switch user
preferences=Preferences
preview=Preview
preview_not_available=Preview not available
print=Print
profile=Profile
profile_title=My account
progress=In progress..
purge=Purge
prompt=Enter the password
quantityValue_validator=The quantity value is not valid
query=Filter
clear_query=Clear
quick_search=Quick Search
quick_search_loreal_order=Quick search on Brand, article, sign code, EAN
quicksight=QuickSight
readonly=Read-only
recent_business=Recent business
recent_mapping=Recent mapping
recipients=Recipients
refresh=Refresh
refuse=Refuse
regenerate_all=Regenerate all
regenerate_all_password=Regenerate all password
related_process=Search related processes
remindpassword=Remind Password
remove=Remove
removed=Removed
rename=Rename
rendered=Rendered
repository=Repository
reset=Reset
result=Results
required=Required
return=Return
return_to=Return to
rigth=Rigth
rte=RTE
rte_collection=Collection
rte_file_or_folder_invalid_name=Name should be an alpahnumerical string without blank. '.', '_' et '-' are accepted.
roadmap=Roadmap
role=Role
roles=Roles
role_add= add
role_edit=Edit role
role_min=role
root=Root
save=Save
schedule=Schedule
schema=Schema
scope=Scope
search=Search
seconds=Seconds
security=Security
select=Select
select_instance=Select an environment
selection=Selection
selection_or_enter=Choose or enter a value
select_one=Select One
send=Send
severity=Severity
show_triggers=Show triggers
site_optimized_for_ie9=Site optimized for Internet Explorer 9.
siret=Registration
size=Size
sort_by=Sort by
sort_order=Sort order
sort_order_ascending=Ascending
sort_order_descending=Descending
sources=Sources
state=State
statistics=Reporting
status=Status
start=Start
started=Started
start_date=Start date
stop=Stop
strong=strong
style=Style
styleClass=Style Classes
subject=Subject
switch_off=Connect as
switch_user=Switch user
task=Task
task_adv_search=Advanced search
task_adv_search_simple=Simple
task_adv_search_avancee=Advanced
task_create=Create task
task_edit=Edit task
task_last_completed=Last task completed
task_to_complete=Task to complete
task_types_revision=Visualization task definition
task_types=Task Types
tasks=Tasks
tasks_campaign=Tasks of workflow
tasks_noselection=No selected task
template=Template
jsf_template=Template Jsf
angular_template=Template Angular
angular_url=Angular url
template_edit=Editing the template
template_import=Import template
template_invalid_name=Name must contain between 3 and 18 lowercase characters and/or digits
template_invalid_pathname=Archive contains invalid characters in pathname
template_new=Create template
template_revision=Import email template
template_saved=Template saved
templates=Templates
test_send_mail=Mail auto-sending
to_edit=Edit
processing=Status
processed=Processed
process_msg=Process
theme=Theme
title=Title
tout=All
trf_required_document_type=Document type or collection is required in a document transformation configuration
trf_required_template_uri=Document transformation template URI is required
trf_generic=generic format
tva=VAT
type=Type
subtype=Subtype
undefine=Not dematerialized
unread_message=unread message(s)
unprocesses_message=unprocessed message(s)
upload_file=Upload File
upload_files=Upload Files
upload_file_limit=File limit reached
upload_invalid_file=Invalid file format
upload_invalid_size=Invalid file size
upload_with_conflict_conflict_message=Following files will be overwritten :
upload_with_conflict_title=Import archive
usage=Usage
user=User
users=Users
users_email=Users / email
user_company=Customer users
user_blocked=Your user account has been blocked. Please contact your administrator.
user_edit=Edit user
user_new=Create user
user_search=Refine your results by typing the name of the user that made the changes
validate=Validate
validator_email=Invalid email.
validator_password=Invalid password (at least 3 characters).
version=Version
version_technical=Technical reference
value=Value
value_default=Default value
variables=Variables
view=View
view_all=View All
weak=weak
week=Week
workflow=Workflow
writer=Writer
active_partners=MOST ACTIVE PARTNERS
year=Year
yes=Yes
allow_user_managing_tab=Allow users to manage this tab

# self-register
check_site_conditions=I agree to the Terms and Conditions
client_number=${client} number
connection_code=Unique connection number
partner_with_code_missing=${client} number and/or registration number don't exist
save_register=I agree to register
self_register=Self-registration
self_register_bottom_msg=Once the registration done, you will receive an email with your temporary password. This password should be modified when you first access the account.
self_register_login=Register
self_register_ko=A problem was encountered during the registration, thank you for trying later
self_register_ok=Registration successfully done!<br>A password renewal link has been sent to you by email.
self_register_placeholder_1=Self-registration Placeholder 1
self_register_placeholder_1_placeholder=${client} number field from the self-registration page
self_register_placeholder_2=Self-registration Placeholder 2
self_register_placeholder_2_placeholder=Unique connection number field from the self-registration page
site_conditions_link=Terms and Conditions
user_firstname=Firstname
user_firstname_placeholder=Your firstname
user_lastname=Lastname
user_lastname_placeholder=Your lastname
user_email=Email address
user_email_placeholder=Email address that will be used to access the account
users_number_exceeded=Number of users for a ${client} was exceeded. Please remove inactive users or contact support

# ADMIN INSTANCE SIDE MENU
imenu_general=General
imenu_general_instance=Environment
imenu_messaging=Messaging
imenu_security=Partners
imenu_repository=Repository
imenu_repository_documents=Documents
imenu_repository_dictionary=Business dictionary
imenu_repository_templates=Templates
imenu_portal=Collaborative
imenu_portal_p2p=Purchase to pay
imenu_portal_o2c=Order to cash
imenu_portal_einvoice=e-Invoice
imenu.portal=Collaborative
imenu_integration=Process
imenu_integration_recent_business=Recent process
imenu_integration_recent_mapping=Recent mapping
imenu_campaigns=Onboarding
imenu_report=Activity

# LOGISTIC TAB
logistic=Logistic
logistic_gs1_company_prefix=GS1 company code
logistic_gs1_error_format_message=Make sure you have between 7 to 10 digits for GS1 company code
logistic_gs1_company_not_provided=Please fill in the GS1 company prefix in the Logistic Tab of your company
logistic_gs1_company_required=GS1 is mandatory because at least one partner use the automatic generation
logistic_extension=Extension
logistic_serial_reference=Serial Reference
logistic_serial_reference_error_format_message=Make sure you have between 6 to 9 digits for Serial reference
logistic_sscc=SSCC
logistic_sscc_fieldset=SSCC number
logistic_missing_sscc_message=Please fill in the required datas
logistic_sscc_auto=SSCC generation
logistic_extension_required=Extension character is mandatory because SSCC generation has been activated
logistic_serial_reference_required=Serial number is mandatory because SSCC generation has been activated.
logistic_serial_reference_and_cnuf_error_format_message=GS1 code and serial number lenght must be 16 characters.
logistic_missing_mandatories_message=Please fill all the required fields  because SSCC generation has been activated.


# ACCOUNT CREATION
confirmation_mail_subject=Account creation confirmation.
confirmation_mail_message_html=Hello {0},<br/><br/>Your account has been successfully created. Please confirm your email by clicking on the following link : <a href=\"{1}\">Email confirmation</a>.<br/></br>Alternatively, you can cut and paste the following address in your favorite browser: {1}.<br/><br/>See you soon!
confirmation_mail_message_text=Hello {0},\n\nYour account has been successfully created. Please cut and paste the following address in your favorite browser to confirm your account creation: {1}.\n\nSee you soon!=======

# MENU
menu=Menu rights
menu_company_information=My company information
menu_company_information_client=My ${client} information
menu_security_user=My security
menu_user_information=My account
menu_user_parameters=My parameters
menu_admin=Administration
menu_audit=Audit
menu_campaigns=Onboarding
menu_config=Configuration
menu_dashboard=Dashboard
menu_domain=Domain
menu_instances=Environments
menu_message=Mail
menu_messages=Mails
menu_notification=Notification
menu_notifications=Notifications
menu_monitoring=Activity
menu_partners=Partners
menu_process=Process
menu_process_deployment=Process:Deployments
menu_process_execution=Process:Executions
menu_process_manage=Process Management
menu_process_reports=Process Reports
menu_process_trigger=Process:Triggers
menu_rights_clients=Clients
menu_rights_users=Users
menu_rights_roles=Roles
menu_search=Search
menu_security=Security
menu_security_companies=Customers
menu_security_groups=Groups
menu_security_order_issuers=Order Issuers
menu_security_partners=Partners
menu_security_users=Back Office Users
menu_statistics=Reporting
menu_system=System
menu_basket=Basket

# CAMPAIGN
CAMPAIGN=ONBOARDING
campaign=Campaign
campaigns=Onboarding
campaigns_new=New Campaign
campaign_name=Name
campaign_name_short=Short name
campaign_description=Description
campaign_date_creation=Creation Date
campaign_date_start=Started Date
campaign_date_end=Stopped Date
campaign_general=General
campaign_documents=Documents

# PARTNER
partners=Partners
partner_new=New partner
partner_name=Partner name
partner_identification=Identification
partner_contacts=Contacts
partner_parent=Parent
partner_comment=Comment
partner_address=Address
partner_imported=Partner imported : {0}.
partner_import_file=Partners file import (*.xls, *.xlsx)
partner_import_error=Error importing partners : {0}
partner_saved=Partner saved : {0}.
partner_save_error=Error saving partner : {0}.
partner_send_mail=Send account information
partner_deleted=Partner deleted: {0}.
partner_delete_error=Error deleting partner : {0}.
partner_delete_error_children_exist=Could not delete partner with sub-partners: {0}.
partner_user_add=Add contact
partner_import_already_existing_users= user(s) already exist(s)
partner_import_already_existing_partners= partner(s) already exist(s)
partner_import_no_user=Partner {0} has no user
contextual_validation_user_scope_partner=Partner code {0} does not belong to {1}
contextual_validation_user_scope_user=User {0} does not exist
contextual_validation_user_scope_user_cpy=User {0} does not belong to {1}
contextual_validation_user_scope_role=User {0} does not have the role {1}
contextual_validation_user_scope_partner_id=Partner code {0} and perimeter ID {1} should be different for the user {2}
contextual_validation_partner_role=Role has to exist

# FAQ/SURVEY
entry_question=Question
entry_answer=Answer
entry_new_answer=Option
entry_answers=Answers
entry_type=Type
entry_remove=Remove
entry_new=New entry
entry_add=Add
entry_add_question=Add a question
entry_add_title=Add Title
entry_delete_question=Delete a question
entry_edit_question=Edit a question
entry_import_faq=Import FAQ
entry_select_excel=Select an Excel file (*.xls, *.xlsx)
entry_text=Text
entry_radio=Radio
entry_checkbox=Checkbox
entry_title=Titre

# MAIL
contact_user=Contact user
other_variable=Other variable
reminder=Reminder
reminder_number=Reminder number
reminder_delay=Reminder delay
reminder_subject=Reminder subject
reminder_content=Reminder content
mail_content=Content
user_logged=Already logged
mail_sent=Sent
keyword_$login=User's login
keyword_$url=Application url
keyword_$password=Users's password
keyword_$contact=Contact
keyword_$company=Customer
keyword_$currentDate=Current date
keyword_$campaigncontactname=Campaign contact name
keyword_$campaigncontactphone=Campaign contact phone
keyword_$campaigncontactemail=Campaign contact email
keyword_$campaignvariable=Campaign free variable

# DOCUMENTS
docs_list=Documents list
doc_name=Document's name
doc_select=Select
doc_update=Update
doc_update_information=Update my information
doc_upload=Upload
doc_cancel=Cancel
doc_new_name=New name
doc_uploaded=Document uploaded: {0}
doc_uploaded_success=Document uploaded: {0}/{1}
doc_uploaded_update=updated document
doc_upload_duplicate=Duplicate: {0}
doc_upload_error=Error uploading document: {0} ({1})
doc_upload_disabled=You can't perform this action because the task is locked (a campaign may be in progress)
doc_rename_error=Document existing
doc_remove_error=Error removing document
doc_deleted=Document deleted
doc_downloads=Document downloads
docs_available=Documents available
docs_selected=Documents selected

#SSL Files
ks_alias_not_found=Alias not found : {0}. Found aliases, with ; separator : {1}
ks_cannot_determine_alias=Cannot determine alias to use. Found aliases, with ; separator : {0}
ks_cannot_load_keystore=Keystore not loadable. Check parameters. Contact an administrator.
ks_certificationpath=Chain of trust
ks_comment=Description
ks_config_require_name=The KeyStore friendly name is required
ks_config_require_usage=The KeyStore usage is required
ks_config_require_instance=The KeyStore instance is required
ks_entrydetailstab=Certificate
ks_entry_subject=Delivered to
ks_entry_notbefore=Valid from
ks_entry_notafter=Valid until
ks_entry_alias=Alias
ks_entry_alias_optional=Optional unless alias cannot be automatically determined
ks_entry_version=Version
ks_entry_issuer=Issuer
ks_entry_serialnumber=Serial number
ks_entry_signaturealgoname=Signature algorithm name
ks_entry_fingerprintmd5=Fingerprint (MD5)
ks_entry_fingerprintsha1=Fingerprint (SHA-1)
ks_error_could_not_find_nor_create_parent_for_friendly_name=Could not find nor create parent for friendly name {0}
ks_error_could_not_write_certificate_file=Could not write the document file for this certificate
ks_error_could_not_extract_certificate=Could not extract the certificate(s) contained in the file
ks_error_no_certificate_found=No certificate(s) found in the file
ks_error_multiple_parent_for_friendly_name=Inconsistent state : multiple parents found for friendly name {0}
ks_error_no_friendly_name_found=Friendly name not found in parameters and cannot be determined
ks_error_no_keystore_folder=Error - the keystore folder could not be retrieved
ks_filename=File name
ks_friendlyname=Friendly name
ks_hasprivatekey=With private key
ks_hasRSApublickey=RSA
ks_keystoredetailstab=Keystore details
ks_library=Library
ks_modulus_length=Modulus length (bits)
ks_morethanoneentry=Your keystore contains more than one entry. We can't load this sort of keystore.
ks_no_file_selected=A keystore file must be selected
ks_error_during_file_reading= An error occurred while reading the file
ks_no_key_found=No key found in keystore
ks_no_slot_index_selected=A slot index must be selected
ks_password=Password
ks_password_pkcs11=Password (PIN code)
ks_provide_friendly_name=Friendly name must be set
ks_provide_library=Library must be set
ks_publickeytab=Public key certificate
ks_remove_error=Error removing file : {0}
ks_remove_linked_partner_integrity=Operation blocked : certificate still linked to these groups : {0}
ks_remove_timestamp_server_integrity=Operation blocked : certificate still referenced in timestamp server
ks_slotIndex=Slot index
ks_label=Label
ks_type=Type
ks_unexpected_error_determing_alias=Unexpected error determining the alias
ks_unexpected_multiple_keys=Unexpected multiple keys in keystore
ks_unrecognized=Uploaded data can not be loaded as PKCS11, PKCS12 or JKS keystore.\n - Check the password.\n - Check your file. \n - Try to change the \"Unlimited Strength Jurisdiction Policy Files\" of your JRE.
ks_upload=Upload a keystore
ks_uploaddate=Creation Date
ks_uploaded=Keystore uploaded : {0}
ks_upload_select=Keystore (*.jks, *.p12, *.pfx)
ks_upload_error=Error uploading keystore : {0}
ks_usage_mismatch=Usage {0} must match parent usage {1}
ks_use_end_date=End of use
ks_use_period=Use period
ks_use_start_date=Start of use

#TEST
test_upload=Upload a test file

#ABOUT US
about_contact=Contact us
about_contact_email=Contact's email

#TEXT
text=Text

# ENUMERATES
# CompletionDefinition
AllUsers=For all the partner's users
AtLeastOneUser=At least one partner's user

# ContactMode
contact_mode=Media
fax=Fax

#REPORTS
report_partners_completion=PARTNERS COMPLETION
report_partners_completion_campaigns=PARTNERS COMPLETION
report_partners_status_by_month=PARTNERS STATUS EVOLUTION
report_partners_status=PARTNERS STATUS
report_no_data=NO DATA
report_not_completed=Not completed
report_completed=Completed
report_informations=Informations

#ORDERS
client_order=Client order
invoice=Invoice number
later_shipment_date=At the latest shipment date
order=Order
shipment_date=Shipment date
sooner_shipment_date=As soon as possible date

# USER ACCOUNT MAILS
creation_mail_subject=Your account has been created for the site {0}
creation_mail_message_html=Hello {0},<br/><br/>Please find below your connection informations to the {1} website ({2}): <br/><br/> <li>Login: {3} </li><li>Password renewal link: {4}</li><br/><br/>See you soon!
creation_mail_message_text=Hello {0},\n\nPlease find below your connection informations to the {1} website ({2}): \n\n- Login: {3}\n- Password renewal link: {4}\n\nSee you soon !

#CHANNELS
channels=Channels
channel=Channel
channel_add=Add a channel
channel_modify=Modify a channel
channel_add_type=Adding channel of type
channel_name=Channel name
channel_type_mandatory=You have to choose a channel type before clicking on add button.
channel_type_select=Channel type...
channel_type=Type
channel_desc=Description
channel_create_error=Error creating new channel
channel_save_error_null=Error while saving channel (channel to be saved == null).
channel_add_error_duplicatename=A channel already exists with this name. This channel cannot be created.
channel_save_ok=Channel saved.
channel_not_selected=No channel selected
channel_deleted=Channel deleted
channel_duplicate_error=Error while copying channel
channel_duplicate_ok=Channel duplicated
channel_delete_linked_chanel=Error while deleting channel. Existing alias to this channel:<br/>{0}
channel_toggle_linked_chanel=Error while modifying channel. Existing alias to this channel:<br/>{0}

#CHANNEL/ALIAS
channel_AliasEndpointConfiguration=Alias
channel_alias_target=Target

#CHANNEL/ARKHINEO
channel_ArkhineoEndpointConfiguration=Arkhineo
channel_arkhineo_digitalVaultId_req=Digital vault id
channel_arkhineo_clientId_req=Client id
channel_arkhineo_sectionId_req=Section id
channel_arkhineo_depositCert_req=Deposit certificate
channel_arkhineo_consultCert_req=Consult certificate

#CHANNEL/FTP
channel_FtpEndpointConfiguration=FTP

channel_ftp_port=Port
channel_ftp_ftp_variant=FTP mode
channel_ftp_hostname=Server hostname
channel_ftp_username=User name
channel_ftp_account=Account
channel_ftp_binary=Binary
channel_ftp_disconnect=Disconnect
channel_ftp_localWorkDirectory=Local work directory
channel_ftp_passiveMode=Passive mode
channel_ftp_securityProtocol=Security protocol
channel_ftp_disableSecureDataChannelDefaults=disable secure data channel defaults
channel_ftp_download=Download
channel_ftp_streamDownload=Stream download
channel_ftp_execProt=execProt
channel_ftp_execPbsz=execPbsz
channel_ftp_isImplicit=isImplicit
channel_ftp_knownHostsFile=known_hosts file
channel_ftp_knownHostsUri=known_hosts file URI
channel_ftp_keyPair=Java KeyPair
channel_ftp_privateKeyFile=Private key file
channel_ftp_privateKeyUri=Private key file URI
channel_ftp_privateKeyPassphrase=Private key file passphrase
channel_ftp_preferredAuthentications=Preferred authentications
channel_ftp_ciphers=Ciphers
channel_ftp_fastExistsCheck=Fast existence check
channel_ftp_strictHostKeyChecking=Strict host key checking
channel_ftp_maximumReconnectAttempts=Maximum reconnect attempts
channel_ftp_reconnectDelay=Reconnection delay (ms)
channel_ftp_connectTimeout=Connect timeout (ms)
channel_ftp_soTimeout=SocketOptions.SO_TIMEOUT (ms)
channel_ftp_timeout=Data timeout (ms)
channel_ftp_siteCommand=Site commands
channel_ftp_stepWise=Stepwise traversing directories
channel_ftp_separator=Path separator
channel_ftp_chmod=Set chmod
channel_ftp_compression=Compression level
channel_ftp_receiveBufferSize=Downloading buffer size
channel_ftp_serverAliveInterval=SFTP serverAliveInterval
channel_ftp_serverAliveCountMax=SFTP serverAliveCountMax
channel_ftp_ftpClientTrustStoreId=Client truststore
channel_ftp_ftpClientKeyStoreId=Client keystore
channel_ftp_useList=Use FTP LIST
channel_ftp_ignoreFileNotFoundOrPermissionError=Ignore FileNotFound or Permission Error
channel_ftp_sendNoop=Send noop
channel_ftp_jschLoggingLevel=JSCH log level
channel_ftp_flatten=Flatten file path
channel_ftp_charset=File encoding

#CHANNEL/EMC2 VNX
channel_Emc2VnxEndpointConfiguration=EMC\u00B2 VNX
# TODO
#channel_emc2_vnx_...

#CHANNEL AS2
channel_AS2EndpointConfiguration=AS2
channel_as2_only_one_endpoint_per_organization=You can have only one AS2 channel
channel_MDN=MDN
channel_others=Others
channel_retry=Retry
channel_other=Other
channel_relation=Relationship
channel_url=URL
channel_test=Test
channel_authentification_http=HTTP authentification
channel_ssl=SSL
channel_signed_message=Signed message
channel_coded_message=Encrypted message
channel_compressed_message=Compressed message
channel_hashing_algorithm=Hashing algorithm
channel_encryption_algorithm=Encryption algorithm
channel_mime=Mime
channel_encoding=Encoding
channel_mdn_type=Type
channel_synchronous=Synchronous
channel_asynchronous=Asynchronous
channel_port=Port
channel_signed_mdn=Signed MDN
channel_coded_mdn=Encrypted MDN
channel_debug=Debug
channel_proxy_name=Proxy name
channel_proxy_url=Proxy URL
channel_authentification_proxy=Proxy authentification
channel_timeout=Timeout
channel_generate_file=Automatic Generation File
channel_number_of_retry=Number of retry
channel_delay=delay between attempts
channel_used_keystore=Used keystore
channel_alernative_relation=Alternative relationship
channel_as2to=AS2 to
channel_server=Server name
channel_filename=FileName-<NUM>-<DATE>.edi
channel_filename_required=File name mask for automatic file
channel_proxy_used=Used a proxy server definition

#CHANNEL FAX
channel_FaxEndpointConfiguration=Fax
channel_fax=Fax number
channel_fax_only_one_endpoint_per_organization=You can have only one fax channel

#CHANNEL HTTP/S
channel_HttpEndpointConfiguration=HTTP/S
url=Url
port=Port
timeout=Timeout
secure=Secure (HTTP/S)
keystore=Keystore

#CHANNEL SMTP
channel_SmtpEndpointConfiguration=SMTP
smtp_enableStartTLS=enableStartTLS
smtp_mailHost=Host
smtp_mailPort=Port
smtp_ssl=SSL

#CHANNEL X400
channel_X400EndpointConfiguration=X400
channel_surname=Surname
channel_given_name=Given name
channel_common_name=Common name
channel_organization=Organization
channel_private_domain=Private management domain
channel_admin_domain=Administration management domain
channel_options=Options
channel_delivery_report=Delivery report
channel_report_timer=Delivery report timer
channel_receipt_report=Receipt report
channel_encoding_message=Encoding message
channel_message_body=Message body
channel_priority=Priority
channel_subject=Subject
channel_content_ident=Content identification
channel_indicator=Test indicator
channel_unit1=Organisation unit1
channel_unit2=Organisation unit2
channel_unit3=Organisation unit3
channel_unit4=Organisation unit4
channel_generation_qualifier=Generation qualifier
channel_initial=Initial
channel_directory_name=Directory name
channel_adressX121=X121 address
channel_uaid=Uaid
channel_terminal_id=Terminal id
channel_dda1=DDA1
channel_dda2=DDA2
channel_dda3=DDA3
channel_dda4=DDA4

# CHANNEL WS/Stock
channel_WSStockEndpointConfiguration=WS/Supply
channel_WSStockEndpoint_stock_retrieval=Endpoint supply data retrieval
channel_WSStockEndpoint_stock_login=Login
channel_WSStockEndpoint_stock_password=Password

#CHANNEL GCN
channel_GcnEndpointConfiguration=GCN
channel_gcn_only_one_endpoint_per_organization=You can have only one GCN channel

#CHANNEL AUTHENTICATION
channel_AuthenticationEndpointConfiguration=Authentication
channel_auth_userDnPattern=User DN pattern
channel_auth_userSearchBase=User search base
channel_auth_userSearchFilter=User search filter
channel_auth_groupSearchBase=Group search base
channel_auth_groupSearchFilter=Group search filter

#PROPERTY
property_not_integer=should be an integer.
property_required=is required.

#MESSAGES
#EXCEPTIONS
exception_message=An error occurred. \nPlease try again or contact your administrator\n ({0})
exception_backoffice_user={0} is a back-office user
exception_code_duplication=Duplicate code: {0}
exception_constraint_violation={0} : {1}
exception_duplicate_email=This email address already exists
exception_duplicate_login=Duplicate login
exception_duplicate_role=Duplicate role code: {0}
exception_failing_ACE_instanciation=Failed to instanciate ACE from type: {0}
exception_file_upload_unknown_request=Unknown request parameter: {0}
exception_id_duplication=Duplicate identification: {0}
exception_gcn_id_duplication=Duplicate GCN identification: {0}
exception_gcn_subscriber_id_duplication=Duplicate Generix Billing Id: {0}
exception_import_error_during_cloning=Error in clone Portal
exception_import_export_null=Export is null
exception_import_instance_code_null=Environment code is null
exception_import_instance_null=Environment in export file is null
exception_export_null=No Export find in file
exception_import_page_null= Page in export file is null
exception_import_portal_not_empty=Portal is not empty for environment {0}
exception_instance_associated={0} environment(s) associated
exception_instance_not_found=User {0} has no environment configured for domain {2}
exception_invalid_gson=Invalid file Gson
exception_json_export_error=Error during JSon export: {0}
exception_more_than_one_instance=User {0} associated to the customer {1} has more than one environment configured for domains {2} ({3})
exception_multiple_principal_groups_user=User {0} has more than one principal group ({1})
exception_no_backoffice_access={0} is not part of a partner or a customer and is not granted to access the back-office
exception_partner_associated={0} partner(s) associated
exception_exchange_associated={0} exchange(s) allowed associated
exception_partner_subgroups=Partner {0} has {1} sub-group(s)
exception_portlet_cloning=Problem during portlet cloning {0}
exception_removed_instance=Environment {0} has been removed
exception_role_has_users=Role {0} has {1} user(s) associated
exception_role_has_pages=Role {0} has {1} page(s) associated
exception_role_has_sso=Role {0} has sso role mapping(s) associated
exception_sending_mail_partner=Error sending mail to partner(s): {0}
exception_task_change_parent=Error of changeParent algorithm
exception_task_import=Problem of import tasks for name {0}
exception_task_import_parents=Problem of import tasksParent for name {0}
exception_task_type_not_found=Impossible to find task type {0}
exception_task_properties_not_found=Impossible to import task {0}, a rise of version is necessary
exception_unavailable_instance= {0} site is momentarily unavailable
exception_unknown_layout_type=Layout type unknown
exception_user_associated={0} user(s) associated
exception_user_no_associated_company=User {0} partner group {1} is not associated with any customer
exception_user_not_partner=User {0} principal group {1} is not a Partner or Customer group
exception_user_not_specified=User is not specified
exception_user_more_companies=User {0} partner group {1} has more than one customer associated ({2})
exception_access_denied=Access denied
exception_admin_url_portal=Please type a different username or contact your administrator to get your access portal URL

#ERRORS
error_duplicate_template=Template {0} already exists
error_changing_layout=Error changing page layout ({0})
error_creating_template_archive=Error creating template archive ({0})
error_editing_portlet=Error editing portlet ({0})
error_editing_default_host=Default host is not editable
error_exporting_instance=Export failed : {0}
error_exporting_page=Export failed
error_exporting_partner=Error exporting partners ({0})
error_exporting_client=Error exporting clients ({0})
error_file_not_found=File not found {0}
error_getting_portlet_content=Portlet {0}, Content {1}: {2}
error_importing_instance=Error while import: {0}
error_importing_page= Error while import
error_importing_partner=Error importing partners ({0})
error_no_company_specified=No customer specified for partner edition: {0}
error_no_user_found=No user found with identifier: {0}
error_password_generation=An error occurred while generating the password
error_password_link_generation=An error occurred while generating the password renewal link
error_profile_name_mandatory=Profile name is mandatory
error_removing_company=Error removing customer ({0})
error_removing_file=Error removing file ({0})
error_removing_host=Error removing domain ({0})
error_removing_page=Error removing page ({0})
error_removing_partner=Error removing partner ({0})
error_removing_client=Error removing client ({0})
error_removing_portlet=Error removing portlet content ({0})
error_removing_portlet_content=Error deleting the contents of the portlet ({0})
error_removing_role=Error removing role ({0})
error_removing_row=Error removing row ({0})
error_removing_template=Error removing template ({0})
error_removing_user=Error removing user ({0})
error_saving_company=Error saving company.
error_saving_file=Error saving file ({0})
error_saving_host=Error saving domain ({0})
error_saving_host_not_unique=A domain already exists for this name
error_saving_instance=Error saving environment ({0})
error_saving_organization=Error, the item already exists
error_saving_partner=Error saving partner ({0})
error_saving_client=Error saving client ({0})
error_saving_permission=Error saving permission ({0})
error_saving_portal=Error saving portal ({0})
error_saving_portal_portlet_missing=Missing portlet configuration
error_saving_role=Error saving role ({0})
error_saving_user=Error saving user ({0})
error_select_correct_user=Please, select a correct user
error_sending_new_password=An error occurred while sending password renewal link to {0} ({1})
error_switching_user=Error switching user ({0})
error_uploding_logo=Error loading logo {0}
error_uploding_template=Error loading template ({0})
error_user_without_primary_group=No primary group found for user duplication
error_save_user_no_env=Environment selection is required to notify the user
error_save_user_no_partner=Partner selection is required
error_saving=Error during saving ({0})
error_duplicate_configuration=Configuration already exists
error_export_empty_portlet=You cannot export an empty portlet;
error_export_empty_page=You cannot export an empty page;
error_export_portlet_rte_collection= You cannot export the portlet Collection.
error_export_not_empty_portlet = You cannot export : the portlet is not empty
error_importing_Languages= An error occurred while importing portlets localisation file
error_invalid_row_length=[{0},{1}] Invalid row length expected ({2}) found ({3})
error_localizations_import_bad_structure=The file structure is not compliant regarding the expected format
error_invalid_number={0} : The entered content is not a number : {1}
error_invalid_date={0} : the entered content is not a date : {1}

#WARNS
warn_app_template_deleting={0} template cannot be deleted.
warn_existing_resource=This resource already exists
warn_creation_succes=The creation succeeded
warn_creation_fail=The creation failed
warn_delete_fail=The suppression failed
warn_host_already_used=This domain is already used
warn_host_not_removed={0} has NOT been removed ({1} associated environment(s):CODE= {2})
warn_instance_code_already_used=This code is already used
warn_language_mandatory=Language configuration is mandatory
warn_locked_file=Locked file
warn_locked_folder=Locked folder
warn_no_type_selected=Please, select a type of task
warn_only_parent_empty=Only parent page should be defined as empty
warm_add_empty_when_other_exits=You can not add an empty disposition if there already exists a disposition
warm_change_into_empty_when_other_exists=You can not change this layout into empty one until in this page exists other layouts
warn_partner_profile_deleted=Partner profile should not be deleted
warn_portlet_content_not_found=Portlet's content with id {0} not found
warn_profile_already_exists=Profile already exists
warn_select_partner_notification=Please, select the partners to notify
warn_select_role=Please, select a role
warn_template_with_associated_host=Some domains are associated with the template
warn_user_missing=User is missing (see uid parameter)
warn_partner_missing=User is missing (see pcode parameter)
warn_deleting_own_user=It is not possible to delete the user {0}. This is your own user.
warn_import_pt_collection=If the portal use \"collection\" portlets, a configuration of those portlets will be mandatory
warn_portlet_localization_lang_not_supported={0} language not supported by the environment
warn_localizations_import_portlet_not_found=Portlet {0} not found in the portal definition

#INFOS
info_company_removed=Customer {0} removed
info_company_saved=Customer {0} saved
info_company_created=Customer {0} created
info_file_removed=File removed
info_file_saved=File saved
info_host_removed=Domain {0} removed
info_host_saved=Domain {0} saved
info_instance_saved={0} environment saved
info_instance_toggled={0} environment toggled
info_import_portal=The portal has been imported
info_import_processes=The processes have been imported
info_logo_uploaded=Logo loaded
info_no_portlet_content_defined=Portlet has been set to this page without content
info_no_portlet_defined=Portlet has not been set for this page
info_partner_file_import={0} imported
info_partner_removed=Partner {0} removed
info_client_removed=Client {0} removed
info_partner_saved=Partner {0} saved
info_partner_saved_detail=Partner {0} saved : {1}
info_client_saved=Client {0} saved
info_portal_saved=Portal saved
info_portlet_removed=Portlet content removed
info_portlet_saved=Portlet content saved
info_role_removed=Role {0} removed
info_role_saved=Role {0} saved
info_user_new_password=A password renewal link has been sent to {0}
info_user_removed=User {0} removed
info_user_saved=User {0} saved
info_parameters_saved=User parameters saved
info_user_role_saved=User role saved
info_user_role_duplicated=The role {0} was duplicated
info_partner_role_saved=Partner role saved
info_portlet_localization_import=The portal localisation finished successfully. {0} message(s) added and  {1} modified
import_role_header= Roles file import (*.xls, *.xlsx)

#CONFIRM
confirm_instance_disable=All the environment\\'s services will be disabled (process, onboarding...).\\n\\nAre you sure you want to disable this environment?
confirm_instance_enable=All the environment\\'s services will be enabled (process, onboarding...).\\n\\nAre you sure you want to enable this environment?
confirm_file_delete=Are you sure you want to remove {0}
confirmScopeSelection=You will lose your selection of partner scopes

# PrimeFaces
no_records_found=No records found
no_records_found_loreal_order=No articles match your search. <br/> We invite you to modify this one.
no_extensions_found = No extensions found for this database
ui_converter=Value conversion error
ui_file_limit=Maximum number of files exceeded
ui_invalid_file=Invalid file type
ui_invalid_size=Invalid file size
ui_required=Value is required
ui_validator=Value is not valid

# Report
OK_icon=fa fa-check-circle-o
OK_color=#87b87f
OK_label=OK
APPROVED_icon=fa fa-check-circle-o
APPROVED_color=#87b87f
APPROVED_label=OK
WARN_icon=fa fa-exclamation-circle
WARN_color=#ffb752
WARN_label=WARNING
WARNING_icon=fa fa-exclamation-circle
WARNING_color=#ffb752
WARNING_label=WARNING
ERROR_icon=fa fa-times-circle-o
ERROR_color=#d15b47
ERROR_label=ERROR
FATAL_icon=fa fa-bomb
FATAL_color=#cc1e00
FATAL_label=FATAL
DISABLED_icon=fa fa-minus-circle
DISABLED_color=#d4d4d4
DISABLED_label=DISABLED
NONE_icon=fa fa-minus-circle
NONE_color=#d4d4d4
NONE_label=NONE
PENDING_icon=fa fa-cog fa-spin
PENDING_color=#bebfbb
PENDING_label=REPORT IN PROGRESS...
COMPLETED_icon=fa fa-check-circle-o
COMPLETED_color=#87b87f
COMPLETED_label=COMPLETED
STARTING_icon=fa fa-times-circle-o
STARTING_color=#bebfbb
STARTING_label=STARTING
STARTED_icon=fa fa-times-circle-o
STARTED_color=#bebfbb
STARTED_label=STARTED
STOPPING_icon=fa fa-exclamation-circle
STOPPING_color=#cc1e00
STOPPING_label=STOPPING
STOPPED_icon=fa fa-exclamation-circle
STOPPED_color=#cc1e00
STOPPED_label=STOPPED
FAILED_icon=fa fa-exclamation-circle
FAILED_color=#cc1e00
FAILED_label=FAILED
ABANDONED_icon=fa fa-exclamation-circle
ABANDONED_color=#cc1e00
ABANDONED_label=ABANDONED
UNKNOWN_icon=fa fa-exclamation-circle
UNKNOWN_color=#d4d4d4
UNKNOWN_label=UNKNOWN
edition=Edition
new=New
new_file=New file
new_folder=New folder
exit=Exit
themes=Themes
find=Find
replace=Replace
line_comment=Line comment

#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=Birt
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Counter
DocumentLineChart=LineChart
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
archiveimv3=IMV3 archive
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated

# Organization
organization_code=Identifier
organization_collaborativeId=GCN Id (gid)
organization_subscriberId=Generix Billing Id
organization_vat=VAT Code
organization_registration=Government reference
organization_duns=Reference
organization_phone=_phone
organization_fax=_fax
organization_email=_email
organization_web=_web
organization_orderContact=Contact
organization_orderPhone=Phone
organization_orderFax=Fax
organization_orderEmail=Email
organization_client=_client
organization_profile=Profile
organization_gcnSubscriber=Generix client
organization_shareCapital=Legal capital
organization_registerName=Register name
organization_legalStructure=Legal structure

organization_id=_id
organization_name=Code
organization_fullname=Name
organization_creation=_creation
organization_modification=_modification
organization_comment=Description
organization_description=_description
organization_userGroupAssociations=_userGroupAssociations
organization_location=_location
organization_parent=_parent
organization_children=_children
organization_logoSmall=_logoSmall
organization_logoMedium=_logoMedium
organization_logoLarge=_logoLarge
organization_freeText01=_freeText01
organization_freeText02=_freeText02
organization_freeText03=_freeText03
organization_freeText04=_freeText04
organization_freeText05=_freeText05
organization_freeText06=_freeText06
organization_freeText07=_freeText07
organization_freeText08=_freeText08
organization_freeText09=_freeText09
organization_freeLongText01=_freeLongText01
organization_freeLongText02=_freeLongText02
organization_freeBoolean01=_freeBoolean01
organization_freeBoolean02=_freeBoolean02
organization_freeDouble01=_freeDouble01
organization_freeDouble02=_freeDouble02
organization_freeDate01=_freeDate01
organization_freeDate02=_freeDate02
organization_freeViewConfiguration=Free fields
organization_freeViewProfile=Profile

organization_address_address=_address.address
organization_address_addressComplement=Addition to address
organization_address_country_country= ISO-2 country code
organization_address_country_displayCountry = Country
organization_address_country_iSO3Country = ISO-3 country code
organization_address_addressLine=Address
organization_address_streetName=Address
organization_address_postalCode=Postal Code
organization_address_city=City
organization_address_country=Country
organization_start=Start date
organization_end=End date
organization_role=Role

organization_autoGenerationOfSSCC=SSCC generation
organization_extension=Extension
organization_gs1=GS1 company code
organization_serialReference=Serial Reference

organization_bankAccount_currency=Currency
organization_bankAccount_iban=IBAN
organization_bankAccount_bic=BIC
organization_bankAccount_user=Created by
organization_bankAccount_partner=Partner
organization_bankAccount_date=Creation date


xPath=xPath

#Statuts
NONE=Unread
NONE_ICON=fa fa-envelope-o
NONE_STYLE=status_yellow
PENDING=Draft
PENDING_ICON=fa fa-eur
PENDING_STYLE=status_yellow
APPROVED=Approved
APPROVED_STYLE=info
ACCEPTED=Confirmed
ACCEPTED_ICON=fa fa-check
ACCEPTED_STYLE=info
ACCEPTED_WITH_AMENDMENT=Confirmed with amendment
ACCEPTED_WITH_AMENDMENT_ICON=fa fa-check-circle-o
ACCEPTED_WITH_AMENDMENT_STYLE=info
REFUSED=Refused
REFUSED_ICON=fa fa-exclamation-triangle
REFUSED_STYLE=status_red
SENT=Shipped
SENT_ICON=fa fa-paper-plane-o
SENT_STYLE=info
ACQUITTED=Acknowledged
ACQUITTED_STYLE=status_green
ARCHIVED=Archived
UNARCHIVABLE=Unarchivable
IN_SUBMISSION=In submission
SUBMITTED=Submitted
REMOVED=Removed
REMOVED_STYLE=default
ERROR=Error
ERROR_ICON=fa fa-remove
ERROR_STYLE=status_red
WARNING=Warning
WARNING_STYLE=status_yellow
DUPLICATE=Duplicated
DUPLICATE_STYLE=status_yellow
TO_VALIDATE=To Validate
TO_VALIDATE_ICON=fa fa fa-pause fa-fw
TO_VALIDATE_STYLE=status_yellow
UPDATED=Updated
UPDATED_STYLE=info
READ=Read
READ_ICON=fa fa-eye
READ_STYLE=info
CANCEL=Canceled
CANCEL_ICON=fa fa-times
CANCEL_STYLE=default
FATAL=System Error
FATAL_STYLE=status_red
SENT_PARTIALLY=Partially shipped
SENT_PARTIALLY_ICON=fa fa-paper-plane
SENT_PARTIALLY_STYLE=status_yellow
INVOICED=Invoiced
INVOICED_STYLE=status_green
ANSWERED=Answered
ANSWERED_ICON=fa fa-undo fa-fw fa-rotate-90
ANSWERED_STYLE=info
PARTIALLY_ACCEPTED=Partially confirmed
PARTIALLY_ACCEPTED_STYLE=status_partially_accepted
PARTIALLY_ACCEPTED_WITH_AMENDMENTS=Partially confirmed with modifications
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE=status_deep_blue
PARTIALLY_SHIPPED=Partially shipped
PARTIALLY_SHIPPED_ICON=fa fa-paper-plane
PARTIALLY_SHIPPED_STYLE=status_yellow
TIMEOUT=Timeout
TIMEOUT_ICON=fa fa-remove fa-fw
TIMEOUT_STYLE=status_red
TO_REMOVE=To Remove
TO_REMOVE_ICON=fa fa-archive
TO_REMOVE_STYLE=status_yellow
CLOSED=Closed
CLOSED_STYLE=default
DELIVERED=Delivered
DELIVERED_STYLE=status_green
SYNTAX_ERR=Syntax Err.
SYNTAX_ERR_STYLE=status_red
DEMAT_ERR=Demat Err.
DEMAT_ERR_STYLE=status_red
TO_CORRECT=To correct
TO_CORRECT_STYLE=status_yellow
OK=Ok
OK_STYLE=status_green
RESOLVED=Resolved
RESOLVED_STYLE=status_green
SHIPPED=Sent
SHIPPED_STYLE=status_green
ERR_XLEG=Ex. Leg. Err.
ERR_XLEG_STYLE=status_red
INTEGRATED=Integrated
INTEGRATED_STYLE=status_green
IN_DISPUTE=In dispute
IN_DISPUTE_STYLE=status_yellow
BLOCKED=Blocked
BLOCKED_STYLE=status_orange
TO_PAY=To pay
TO_PAY_STYLE=status_green
PAID=Paid
PAID_STYLE=status_green
NO_ROUTE=No Route
NO_ROUTE_STYLE=status_yellow
IMPORT_CORRECTION=Import correction
IMPORT_CORRECTION_STYLE=info
UNKNOWN=Unknown
UNKNOWN_STYLE=status_yellow
UNKNOWN_ICON=fa fa-remove

IN_PREPARATION=To prepare
IN_PREPARATION_STYLE=status_orderlist_yellow

IN_DELIVERY=In delivery
IN_DELIVERY_STYLE=status_orderlist_yellow

VALIDATED=Validated
VALIDATED_STYLE=status_orderlist_green

IN_VALIDATION=In validation
IN_VALIDATION_STYLE=status_orderlist_blue

#DB Schenker
REFERENTIAL_OK=Referential ok
REFERENTIAL_OK_STYLE=status_green
REFERENTIAL_KO=Referential ko
REFERENTIAL_KO_STYLE=status_red
CONTROL_OK=Controls ok
CONTROL_OK_STYLE=status_green
METADATA_KO=Metadata ko
METADATA_KO_STYLE=status_red
SIGNATURE_OK=Signature ok
SIGNATURE_OK_STYLE=status_green
SIGNATURE_KO=Signature ko
SIGNATURE_KO_STYLE=status_red
BEING_SENT_CLIENT=Being sent to client
BEING_SENT_CLIENT_STYLE=status_orange
AWAITING_VALIDATION=Awaiting validation
AWAITING_VALIDATION_STYLE=status_orange
SMTP_ERROR=Smtp error
SMTP_ERROR_STYLE=status_red
CONTROL_TOTAL_KO=Amount controls ko
CONTROL_TOTAL_KO_STYLE=status_red
END_PROCESS=End process
END_PROCESS_STYLE=status_green
RESENT=Resent
RESENT_STYLE=status_green

#Stage
UNDEFINED=Not dematerialized
CORRECT=Dematerialized
UNDEFINED_STYLE_STAGE=default
CORRECT_STYLE_STAGE=success
REFUSED_STYLE_STAGE=warning
ERROR_STYLE_STAGE=danger
UNKNOWN_STYLE_STAGE=warning

#Archive
UNDEFINED_STYLE=default
ARCHIVED_STYLE=status_green
SUBMITTED_STYLE=status_green
IN_SUBMISSION_STYLE=status_yellow
UNARCHIVABLE_STYLE = status_red

autoGenerationOfSSCC = Generated SSCC

# column types
com_byzaneo_xtrade_api_DocumentStatus=Status
java_lang_Boolean=Boolean
java_lang_Class=Class
java_lang_Integer=Integer
com_byzaneo_xtrade_api_DocumentStage=State
java_lang_String=Text
java_math_BigDecimal=Decimal
java_util_Date=Date
javax_xml_datatype_XMLGregorianCalendar=XML Date

# Archiving policy
Cascade=Purge child documents
Detach=Detach child documents
Ignore=Ignore parent documents
doc_purge_children_policy=Linked documents

S=Sender
R=Receiver
SENDER=Sender
RECIPIENT=Receiver

# Demat partner Dialog
demat_partner_dialog_title=Update history
REFUSED_MANUALLY=Refused manually
REFUSED_MANUALLY_STYLE=default
FORCED=Forced
FORCED_STYLE=default

# OrderResponse Rights Portlet
portlet_orderresponse_export= ORDER RESPONSE: Export
portlet_orderresponse_print= ORDER RESPONSE: Print
portlet_orderresponse_import= ORDER RESPONSE: Import

# Portlet Categories
EDOCUMENT=INVOICING SERVICES
RTE=EDI SERVICES
GCN=COLLABORATIVE NETWORK
SMARTPDF=SUPPLIER PORTALS
CUSTOMERS=CUSTOMER PORTALS
DOC=KPI

# Application type
Development=Development
Acceptance=Acceptance
Preproduction=Pre-production
Production=Production
Unknown=UNKNOWN !

SYSTEMATIC= Always create the file
REPLACE_IF_EXISTS= Replace older files
NO_GENERATION_IF_EXISTS= No file if it exists already

#Factor
factor=Factor

# Application name
Noname=NO NAME !

#Bank Accounts
bank_accounts_dlg_header=Add bank account
bank_account_currency=Currency
bank_account_iban=IBAN
bank_account_bic=BIC

bank_account_name_required=The name is mandatory
bank_account_iban_error_exist=Sorry but there is already an IBAN associated to the selected currency. Please remove the IBAN before adding a new one
bank_account_iban_error_notValid=The IBAN is not valid
bank_account_iban_error_required=The IBAN is mandatory
bank_account_bic_error_required=The BIC code is mandatory
bank_account_bic_error_notValid=The BIC code must contain 8 to 11 characters
bank_account_delete_confirm=Are you sure you want to delete the IBAN for the currency
bank_account_deleting_error=Error deleting bank account {0}

#Environment Constraints
environment_error_max_characters_allowed = Maximum 64 characters are allowed

#Order Tracking 
to_validate = To validate
in_validation = In validation
validated = Validated
to_prepare = In preparation
in_delivery = In delivery
order_history_title = HISTORY OF MY ORDER N\u00B0 :
estimated_delivery_date = Estimated delivery date :
requested_delivery_date = Requested delivery date :
order_number = Order n\u00B0

#customer
customer_clientPartners=My ${clients}
customer_clientUsers=My users
customer_partner_number=${client} n\u00B0
customer_partner_name=Name
customer_partner_address_city=City
customer_partner_connectionCode=Unique connection N\u00B0
customer_partner_userNumber=Number of users
customer_partner_create=Add ${client}
customer_partner_create_button=Add ${client}
customer_partner_edit=${client}
customer_partner_edit_button=Update ${client}
customer_partner_delete=Delete ${client}
customer_partner_message_delete=The ${client} will be permanently deleted.
customer_partner_delete_yes=Yes I delete it
customer_partner_delete_no=No, I'm thinking
customer_partner_import=Import ${client} file
customer_partner_import_header=Import ${client} file (*.xls, *.xlsx)
customer_partner_import_button=Import the file
customer_partner_export=Export ${client} file
customer_partner_add_user=Add user
customer_partner_add_user_dialog_header=Add user to ${client}
customer_partner_name_for_user=${client} name
customer_partner_search_placeholder=Search ${client} by n\u00B0, name or city
partner_user_add_role=Give a role
partner_company=Customer
partner_client_name=${client} name
partner_address_1=Address 1
partner_address_2=Address 2
partner_postal_code=Postal Code
partner_city=City
partner_country=Country
partner_user=User
partner_user_create=Add user
partner_user_delete=Delete user
partner_user_send_new_pass=Reset password
partner_user_send_new_pass_header=Reset password for user
partner_user_message_send_new_pass_msg=An email containing a link will be sent to the user, which will allow him to change his password. The link has a lifetime of 1h.
partner_user_send_pass_yes=Yes I'm sending it
partner_user_send_pass_no=No, I'm thinking
partner_user_roles=Role for user
partner_user_roles_save=Modify user's role
partner_user_search_placeholder=Search user by n\u00B0, ${client} name, user, firstname or lastname
user_login=User
user_login_validator=User's id must be an email address.
user_email_address=Email
user_phone=Phone
user_mobile=Mobile
user_last_authentication=Last connection
user_number_connexion=Connection(s)
user_creation_date=Creation date
user_password_dialog_header=Add password
user_password_confirm=Confirm password
user_message_delete=The user will be permanently deleted.
user_empty_client=Choose a ${client}...
add_user_button=Add user
edit_user_button=Update user
add_user_mail_button=Add user + Email notification
customer_partner_show_users=Users associated to client
users_search_placeholder=Search user by user, firstname or lastname

#Library Portlet
category = Category
Category.name = Category
category_field_empty = the category field have to be filled.
fileName = File name
introduction_placeholder = Introduction text Ex : \n Available documentation is categorized. Feel free to click on a category to discover the documents that we put at your disposal. If a document is missing you can report it via the contact page.
library_created_success=The documentation was created
library_edited_succes= The documentation was edited
library_deleted_success=The documentation was deleted
library_document_introduction = The available documentation is categorized. Feel free to click on a category to discover the documents that we put at your disposal.
portlet_library = Library
save_success=The whole library was saved.
info_category=To order the categories, it is necessary to make a drag / drop by going up the category from bottom to top
uploaded_since = Uploaded on
one_hour_past = less than an hour
add_library=Add a library
edit_library=Edit a library
a_day_past = less than a day
by_date = {0}
info_addressSearch=It is not possible to search by country field

# Shopping Cart
cart_view=VIEW CART
cart_checkout=CHECKOUT
cart_numberOfArticles=article(s) in the cart
cart_totalAmount=Total :
active_carts=cart(s) active(s)
total_carts=Total baskets :
validity_date=Validity date:
not_valid=Not valid

#RTE Status
rte_status_deployed = The RTE script is properly deployed
rte_status_deployed_but_changed = The RTE script has been modified since its deployment
rte_status_not_deployed = The RTE script is not deployed
rte_status_unknown = The file status could not be determined

# RTE Test Results
run_rte=Run
default_test_rte=Default test
individual_tests_rte=One test
rte_no_init_test_file=No init.tst default file found
rte_base_not_supported=RTE collections are outside the testing scope (at the moment)
rte_forbidden_value=init.tst: improper value for {0} property
rte_test_success_message=Rte test executed successfully
rte_test_fail_message=Rte test executed with failure
rte_test_properties_placeholder=Properties:
rte_test_results_header=Rte Test Results
rte_input_file_must_be_unique=Not a unique input file
rte_property_unknown=init.tst: unknown {0} property

#Save filters in local storage 
confirmationTitle=Confirmation
confirmOverrideFilter=This filter already exists. Do you want to replace it?
canNotSaveFilter =You can not save that filter because local storage is full
filterSuccessfullySaved=The filter has been saved

# Instance Types
instance_type=Portal type
instance_type_CUSTOMER=Customer
instance_type_EDI=EDI
instance_type_INVOICE=Invoice
instance_type_SPECIFIC=Specific
instance_type_SUPPLIER=Supplier

client_type_SPECIFIC=partner
client_type_EDI=partner
client_type_SUPPLIER=supplier
client_type_CUSTOMER=customer
client_type_INVOICE=partner
clients_type_SPECIFIC=partners
clients_type_EDI=partners
clients_type_SUPPLIER=suppliers
clients_type_CUSTOMER=customers
clients_type_INVOICE=partners

#edit role dialog
bql_filter_title=Filters
bql_filter_details=BQL query Invoice portlet filtering

update_user_parameters=Update my parameters

triggername_ACTION_1=Action 1
triggername_ACTION_2=Action 2
triggername_ACTION_3=Action 3
triggername_ACTION_4=Action 4
triggername_ACTION_5=Action 5
triggername_ACTION_6=Action 6
triggername_ACTION_7=Action 7
triggername_ACTION_8=Action 8
triggername_ACTION_9=Action 9
triggername_ACTION_10=Action 10

#perimeter
perimeter=Perimeter
perimeters=Perimeters
error_saving_perimeter=Error saving perimeter ({0})
info_perimeter_saved=Perimeter {0} saved
info_perimeter_removed=Perimeter {0} removed
error_removing_perimeter=Error removing parameter ({0})
exception_duplicate_perimeter=Duplicate perimeter name: {0}
info_perimeter_duplicated=The perimeter {0} was duplicated
perimeter_edit=Edit perimeter for
exception_perimeter_has_partners=Perimieter {0} has {1} partner(s) associated
exception_perimeter_has_users=Perimieter {0} has {1} user(s) associated
scope_partner=Extended scope
scope_customer=Limited scope

#announcement
no_message_display=Do not show this message again

#cookie
cookie=Cookie

#SSO Authentication
regex=Regex
duplicate_mapping=This mapping already exists.
sso_role_mapping=Role Mapping
add_sso_role_mapping=Add a role mapping

#document status
document_status=Document Status
add_new_status=Add a new document status
edit_status=Edit a document status
status_import_file= Document status import(*.xlsx)
document_status_added=The document status {0} was added
document_status_updated=The document status {0} was updated
document_status_exists=The document status {0} already exists
document_status_removed=The document status {0} was removed
document_statuses_updated={0} status(es) was\\were updated
document_statuses_added={0} new status(es) was\\were added
doc_status_code=Status code
doc_status_style=Status style

#instance KPI
database_kpi=KPI database
aws_batch=AWS Batch
arn=IAM role ARN
host_name=Host name
tcp_port=TCP port
database_name=Database name
cron_expression=Cron expression
placeholder_host_name=The host name or IP address of the system where the instance is running
placeholder_database_name=The name of the instance database
placeholder_tcp=The TCP port on which the instance listens
url_tracking=Enable URL tracking for every logged used
warn_kpi_invalid=The KPI service cannot be started. Please try again later or contact your administrator
DocChart=DocChart
