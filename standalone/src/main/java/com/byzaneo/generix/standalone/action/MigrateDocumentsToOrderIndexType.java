package com.byzaneo.generix.standalone.action;

import static org.slf4j.LoggerFactory.getLogger;

import org.slf4j.Logger;

import com.byzaneo.xtrade.xcbl.bean.*;

public class MigrateDocumentsToOrderIndexType extends AbstractMigrateDocument {

  protected static final Logger log = getLogger(MigrateDocumentsToOrderIndexType.class);

  @Override
  protected String getDocumentType() {
    return "Orders";
  }

  @Override
  protected String getOriginalType() {
    return Order.class.getName();
  }

  @Override
  protected String getTargetType() {
    return OrderIndex.class.getName();
  }

}
