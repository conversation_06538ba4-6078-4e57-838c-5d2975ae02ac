package com.byzaneo.generix.standalone.action;

import static org.slf4j.LoggerFactory.getLogger;

import java.io.*;
import java.sql.Types;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang.SerializationUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.dao.InstanceDAO;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.*;

import static java.util.Optional.ofNullable;

public abstract class AbstractMigrateStatuses {
  protected static final Logger logger = getLogger(AbstractMigrateStatuses.class);

  @Autowired
  @Qualifier(InstanceDAO.DAO_NAME)
  protected InstanceDAO instanceDAO;

  @Autowired
  @Qualifier(DocumentStatusEntityDAO.DAO_NAME)
  protected transient DocumentStatusEntityDAO documentStatusesDao;

  @Autowired
  @Qualifier(DocumentDAO.DAO_NAME)
  protected DocumentDAO documentDao;

  @Autowired
  protected JdbcTemplate jdbcTemplate;

  @Autowired
  protected ConfigurationService configurationService;

  @Value("${migration.status.batch.size:100000}")
  protected int batchSize;

  protected static final int[] SELECT_QUERY_ARGS_TYPE = new int[] { Types.BIGINT, Types.BIGINT };

  private Map<Locale, Properties> properties = new HashMap<Locale, Properties>();

  private static final List<Locale> DEFINED_LOCALES = Arrays.asList(new Locale("de"), new Locale("en"), new Locale("es"), new Locale("fi"),
      new Locale("fr"), new Locale("it"), new Locale("nl"), new Locale("no"), new Locale("pl"),
      new Locale("pt"), new Locale("sv"));

  private static final Map<Integer, DocumentStatus> ORDINAL_TO_ENUM;

  static {
    ORDINAL_TO_ENUM = Arrays.stream(DocumentStatus.values())
        .collect(Collectors.toMap(Enum::ordinal, Function.identity()));
  }

  abstract public void doAction(String[] args);

  // populate DOCUMENT_STATUS_ENTYTY table with standard statuses
  @Transactional
  protected void populateStatusEntityTable() {
    logger.info("*** Start populate DOCUMENT_STATUS table with standard statuses ***");
    List<Instance> instances = instanceDAO.findAll();
    List<DocumentStatusEntity> statuses = new ArrayList<DocumentStatusEntity>();
    for (DocumentStatus docSts : DocumentStatus.values()) {
      DocumentStatusEntity docStsEntity = new DocumentStatusEntity();
      docStsEntity.setStatusCode(docSts.toString());
      DocumentStatusValues docStsValues = new DocumentStatusValues();
      docStsValues.setStyle(properties.get(Locale.ENGLISH)
          .getProperty(docSts.name() + "_STYLE"));
      for (Locale locale : DEFINED_LOCALES) {
        docStsValues.getLabels()
            .put(locale, properties.get(locale)
                .getProperty(docSts.name()));
      }

      instances.stream()
          .forEach(i ->
      {
            DocumentStatusValues newVal = (DocumentStatusValues) SerializationUtils.clone(docStsValues);
            newVal.setEnvCode(i.getCode());
            docStsEntity.getValues()
                .put(i.getCode(), newVal);
          });
      statuses.add(docStsEntity);
    }
    for (DocumentStatusEntity documentStatusEntity : statuses) {
      if (documentStatusesDao.findById(documentStatusEntity.getStatusCode()) == null) {
        documentStatusesDao.merge(documentStatusEntity);
      }
    }
    instances.stream()
        .map(i -> i.getCode())
        .forEach(c -> logger.info("{} standard statuses was added to the environment {}", statuses.size(), c));
    logger.info("*** End  populate wiht standard statuses *** ");
  }

  // INIT PROPERTIES

  public void initPropertiesMap() {
    for (Locale locale : DEFINED_LOCALES) {
      Properties prop = new Properties();
      String propFileName = "labels/gnx-labels_" + locale.getLanguage() + ".properties";
      InputStream inputStream = getClass().getClassLoader()
          .getResourceAsStream(propFileName);
      if (inputStream != null) {
        try {
          prop.load(inputStream);
          properties.put(locale, prop);
        }
        catch (IOException e) {
          logger.error(e.getMessage());
        }
      }
    }
  }

  // UTIL

  public String getStatusEntityId(List<DocumentStatusEntity> statuses, String statusCode) {
    return statuses.stream()
        .filter(s -> s.getStatusCode()
            .equals(statusCode))
        .map(s -> s.getStatusCode())
        .findFirst()
        .orElse(null);
  }

  public String getStatusByOrdinal(int intStatus) {
    return ofNullable(ORDINAL_TO_ENUM.get(intStatus)).map(DocumentStatus::name)
        .orElse(null);
  }

  protected long countDocuments(String query) {
    return jdbcTemplate.queryForObject(query, Long.class);
  }

  protected DataBaseType getDataBaseType() {
    switch (configurationService.getString(ConfigurationKey.DATABASE_TYPE)) {
    case "oracle":
      return DataBaseType.ORACLE;
    case "postgres":
      return DataBaseType.POSTGRES;
    case "mysql":
      return DataBaseType.MYSQL;
    default:
      return DataBaseType.MYSQL;
    }
  }

  public enum DataBaseType {
    ORACLE,
    MYSQL,
    POSTGRES;
  }

}
