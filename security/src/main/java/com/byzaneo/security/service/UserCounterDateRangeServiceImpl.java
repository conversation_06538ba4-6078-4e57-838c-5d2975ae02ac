package com.byzaneo.security.service;

import java.util.List;

import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.dao.UserCounterDateRangeDAO;

@Service(UserCounterDateRangeService.SERVICE_NAME)
public class UserCounterDateRangeServiceImpl extends GenericJpaDAO<UserCounterDateRange, UserCounterDateRangeId>
    implements UserCounterDateRangeService {

  @Autowired
  @Qualifier(UserCounterDateRangeDAO.DAO_NAME)
  private UserCounterDateRangeDAO userCounterDateRangeDAO;

  @Override
  @Transactional(readOnly = true)
  public List<UserCounterDateRange> findAllByUser(String userId) {
    return userCounterDateRangeDAO.findAllByUser(userId);
  }

  @Override
  @Transactional
  public void saveUserCounterDateRange(UserCounterDateRange userCounterDateRange) {
    userCounterDateRangeDAO.store(userCounterDateRangeDAO.merge(userCounterDateRange));
  }

  @Override
  @Transactional
  public int removeByPortletId(Long portletId) {
    return userCounterDateRangeDAO.removeByPortletId(portletId);
  }

  @Override
  @Transactional
  public int removeByPortletIdAndUserId(Long portletId, String userId) {
    return userCounterDateRangeDAO.removeByPortletIdAndUserId(portletId, userId);
  }

}
