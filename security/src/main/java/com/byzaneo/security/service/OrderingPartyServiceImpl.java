package com.byzaneo.security.service;

import java.util.List;

import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.bean.OrderingParty;
import com.byzaneo.security.dao.OrderingPartyDAO;

@Service(OrderingPartyService.SERVICE_NAME)
public class OrderingPartyServiceImpl implements OrderingPartyService {

  @Autowired
  @Qualifier(OrderingPartyDAO.DAO_NAME)
  protected OrderingPartyDAO orderingPartyDAO;

  @Override
  @Transactional
  public OrderingParty save(OrderingParty orderingParty, boolean updateCount) {
    if (updateCount)
    orderingParty.setPartnersCount(orderingParty.getPartners()
        .size());
    return orderingPartyDAO.store(orderingPartyDAO.merge(orderingParty));
  }

  @Override
  @Transactional
  public Page<OrderingParty> search(Query query, Pageable pageable) {
    return orderingPartyDAO.search(query, pageable);
  }

  @Override
  @Transactional
  public void remove(OrderingParty orderingParty) {
    OrderingParty op = orderingPartyDAO.findById(orderingParty.getIdentifier());
    if (op != null) orderingPartyDAO.remove(op);

  }

  @Override
  public Page<OrderingParty> search(Class<OrderingParty> type, Specification<OrderingParty> query, Pageable pageable) {
    return orderingPartyDAO.search(type, query, pageable);
  }

  /**
   * used to load parters because this field is lazy
   */
  @Override
  @Transactional
  public OrderingParty loadPartners(OrderingParty orderingParty) {
    OrderingParty op = orderingPartyDAO.findById(orderingParty.getId());
    op.getPartners()
        .size();
    return op;
  }

  @Override
  @Transactional
  public OrderingParty getByIdentifier(String identifier) {
    Query q = new Query(new AndClause(Clauses.equal("identifier", identifier)));
    return orderingPartyDAO.search(q)
        .stream()
        .findAny()
        .orElse(null);
  }

  @Override
  @Transactional
  public List<OrderingParty> findAll() {
    return orderingPartyDAO.findAll();
  }

}
