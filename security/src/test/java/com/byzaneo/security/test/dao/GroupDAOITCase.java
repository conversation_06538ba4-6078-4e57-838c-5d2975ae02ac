package com.byzaneo.security.test.dao;

import com.byzaneo.commons.test.OpenEntityMangerInTest;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.dao.GroupDAO;
import com.byzaneo.security.dao.UserDAO;
import com.byzaneo.security.service.SecurityService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static java.util.Collections.sort;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {"classpath:security.beans.xml"}, loader = SystemPropertyContextLoader.class)
public class GroupDAOITCase extends OpenEntityMangerInTest {

  @Autowired
  private UserDAO userDao;
  @Autowired
  private GroupDAO groupDao;

  @Autowired
  private SecurityService securityService;


  @Test
  void persistBatchPartner() {
    Company company = createCompanyForTest("AAAA");
    Group partner1 = createPartnerForTest(company, "8");
    this.groupDao.store(company);
    this.securityService.saveGroup(partner1);
    Group partner2 = createPartnerForTest(company, "7");
    this.securityService.saveGroup(partner2);
    Group partner3 = createPartnerForTest(company, "6");
    this.securityService.saveGroup(partner3);
    Group partner4 = createPartnerForTest(company, "5");
    this.securityService.saveGroup(partner4);
    Group partner5 = createPartnerForTest(company, "4");
    this.securityService.saveGroup(partner5);

    List<Group> partnersIte = this.groupDao.findByNames("8", "7", "6", "5", "4");
    sort(partnersIte, new Comparator<Group>() {
      public int compare(Group g1, Group g2) {
        return g1.getCode()
            .compareTo(g2.getCode());
      }
    });
    assertNotNull(partnersIte);
    assertEquals(5, partnersIte.size());

    this.groupDao.remove(partner1);
    this.groupDao.remove(partner2);
    this.groupDao.remove(partner3);
    this.groupDao.remove(partner4);
    this.groupDao.remove(partner5);
    assertTrue(this.groupDao.findByNames("8", "7", "6", "5", "4")
        .isEmpty());

    List<Group> partnersBatchInsert = new ArrayList<>();
    partnersBatchInsert.add(createPartnerForTest(company, "8"));
    partnersBatchInsert.add(createPartnerForTest(company, "7"));
    partnersBatchInsert.add(createPartnerForTest(company, "6"));
    partnersBatchInsert.add(createPartnerForTest(company, "5"));
    partnersBatchInsert.add(createPartnerForTest(company, "4"));
    this.securityService.saveGroups(partnersBatchInsert);
    List<Group> partnersBatch = this.groupDao.findByNames("8", "7", "6", "5", "4");
    sort(partnersBatch, new Comparator<Group>() {
      public int compare(Group g1, Group g2) {
        return g1.getCode()
            .compareTo(g2.getCode());
      }
    });
    assertNotNull(partnersBatch);
    assertEquals(5, partnersBatch.size());
    for (int i = 0; i < 5; i++) {
      assertEquals(partnersIte.get(i)
          .getParent(), partnersBatch.get(i)
          .getParent());
      assertEquals(partnersIte.get(i)
          .getCode(), partnersBatch.get(i)
          .getCode());
      assertEquals(partnersIte.get(i)
          .getName(), partnersBatch.get(i)
          .getName());
    }

  }

  @Test
  void persistBatchUser() {
    Company company = createCompanyForTest("BBBB");
    Partner partner = createPartnerForTest(company, "9");
    User user = createUserForTest(partner, "userTest1", "USER_TEST_TYPE");
    this.groupDao.store(company);
    this.groupDao.store(partner);
    this.userDao.store(user);
    user = createUserForTest(partner, "userTest2", "USER_TEST_TYPE");
    this.userDao.store(user);
    user = createUserForTest(partner, "userTest3", "USER_TEST_TYPE");
    this.userDao.store(user);
    user = createUserForTest(partner, "userTest4", "USER_TEST_TYPE");
    this.userDao.store(user);
    user = createUserForTest(partner, "userTest5", "USER_TEST_TYPE");
    this.userDao.store(user);
    // All users have the same type
    List<User> usersIte = this.userDao.findByType("USER_TEST_TYPE");
    assertNotNull(usersIte);
    assertEquals(5, usersIte.size());

    List<User> usersBatchInsert = new ArrayList<>();
    for (int i = 0; i < 200; i++)
      usersBatchInsert.add(createUserForTest(partner, "userTestBatch" + i, "USER_TEST_TYPE_BATCH"));
    this.userDao.persistBatch(usersBatchInsert);
    List<User> usersBatch = this.userDao.findByType("USER_TEST_TYPE_BATCH");
    assertNotNull(usersBatch);
    assertEquals(200, usersBatch.size());
    usersBatch.forEach(userBatch ->
        // Test for Partner
        assertEquals(partner, userBatch
            .getPrimaryGroup()));

  }

  private Company createCompanyForTest(String name) {
    Company company = new Company(name);
    company.setFullname(name);
    return company;
  }

  private Partner createPartnerForTest(Company company, String name) {
    Partner partner = new Partner(name);
    partner.setFullname(name);
    partner.setParent(company);
    partner.setCode(name);
    return partner;
  }

  private User createUserForTest(Partner partner, String login, String type) {
    User user = new User(login);
    user.setType(type);
    user.setEmail("<EMAIL>");
    user.setPrimaryGroup(partner);
    return user;
  }
}
