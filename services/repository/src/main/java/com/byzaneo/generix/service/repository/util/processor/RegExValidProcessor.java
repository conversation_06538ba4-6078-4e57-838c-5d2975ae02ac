package com.byzaneo.generix.service.repository.util.processor;

import java.text.MessageFormat;

import org.apache.commons.lang3.StringUtils;
import org.supercsv.cellprocessor.constraint.StrRegEx;
import org.supercsv.exception.SuperCsvCellProcessorException;
import org.supercsv.util.CsvContext;

public class RegExValidProcessor extends StrRegEx {

  private String customErorMessage;

  private static final String DEFAULT_ERROR_MESSAGE = "Invalid value {0}";

  public RegExValidProcessor(String regex) {
    super(regex);
  }

  public RegExValidProcessor(String regex, String customErorMessage) {
    super(regex);
    this.customErorMessage = customErorMessage;
  }

  @Override
  public Object execute(Object value, CsvContext context) {
    try {
      return super.execute(value, context);
    }
    catch (SuperCsvCellProcessorException e) {
      throw new CustomSuperCsvException(MessageFormat.format(
          StringUtils.isNotEmpty(customErorMessage) ? customErorMessage : DEFAULT_ERROR_MESSAGE,
          value), context);
    }
  }
}
