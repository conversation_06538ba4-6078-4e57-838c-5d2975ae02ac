package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceActualDeliveryDate;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceActualShipDate;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceIssueDate;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceNumber;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.doReturn;

import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoiceHeaderWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.CsvHeader;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;
import java.util.Date;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class InvoiceHeaderWrapperITCase {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  private MockedStatic<CsvImportHelper> mockedCsvImportHelper;

  @Spy
  private InvoiceHeaderWrapper instance;

  @BeforeEach
  public void before() {
    setUpStaticMocks();
  }

  void setUpStaticMocks() {
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
    mockedCsvImportHelper = Mockito.mockStatic(CsvImportHelper.class);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedCsvImportHelper.closeOnDemand();
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateEmptyDates() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER");
    beanHeader.setDocumentType("380");
    beanHeader.setDueDate(new Date());
    //
    try {
      instance.validate(beanHeader, null);
      fail("expected validation exception");
    } catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("date");
      assertTrue(asList(
          "Un champ date doit \u00EAtre rempli",
          "A date field should be filled")
          .contains(serviceException.getMessage()));
    }
  }

  @Test
  public void testValidateTwoDates() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER");
    beanHeader.setDocumentType("380");
    beanHeader.setShippingDate(new Date());
    beanHeader.setDeliveryDate(new Date());
    beanHeader.setDueDate(new Date());
    try {
      instance.validate(beanHeader, null);
      fail("expected validation exception");
    } catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("date");
      assertTrue(asList(
          "Les champs dates de l''en-tête ne doivent pas être présents en même temps",
          "Header dates fields cannot be present altogether")
          .contains(serviceException.getMessage()));
    }
  }

  @Test
  public void testValidateAlreadyExistingHeader() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER");
    beanHeader.setDocumentType("480");
    beanHeader.setDocumentDate(new Date());
    beanHeader.setDeliveryDate(new Date());

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(new Invoice());

    try {
      instance.validate(beanHeader, null);
      fail("expected validation exception");
    } catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("multiple");
      Assertions.assertEquals("Plus d''une facture dans le fichier", serviceException.getMessage());
    }
  }

  @Test
  public void testValidateUnknownType() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER");
    beanHeader.setDocumentType("480");
    beanHeader.setDocumentDate(new Date());
    beanHeader.setDeliveryDate(new Date());

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);

    try {
      instance.validate(beanHeader, null);
      fail("expected validation exception");
    } catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("type");
      assertTrue(asList(
          "Type de facture inconnu : 480",
          "Unknown invoice type : 480")
          .contains(serviceException.getMessage()));
    }
  }

  @Test
  public void testValidateCommercialInvoice() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER");
    beanHeader.setDocumentType("380");

    beanHeader.setCurrency(CurrencyCodeType.ROL.value());

    Invoice invoice = instance.initNewInvoice();
    doReturn(invoice).when(instance).initNewInvoice();
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);
    try {
      beanHeader.setDocumentDate(DateUtils.parseDate("25/11/2015", "dd/MM/yyyy"));
      beanHeader.setDeliveryDate(DateUtils.parseDate("01/12/2015", "dd/MM/yyyy"));
      beanHeader.setDueDate(new Date());

      instance.validate(beanHeader, null);

      // Verification
      assertEquals(InvoiceTypeCodeType.COMMERCIAL_INVOICE, invoice.getInvoiceHeader()
          .getInvoiceType()
          .getInvoiceTypeCoded());
      assertEquals("INV_NUMBER", getInvoiceNumber(invoice));
      assertEquals(CurrencyCodeType.ROL, invoice.getInvoiceHeader()
          .getInvoiceCurrency()
          .getCurrencyCoded());
      assertEquals(CurrencyCodeType.ROL, invoice.getInvoiceHeader()
          .getTaxAccountingCurrency()
          .getCurrencyCoded());
      assertEquals(DateUtils.parseDate("25/11/2015", "dd/MM/yyyy"), getInvoiceIssueDate(invoice));
      assertEquals(DateUtils.parseDate("01/12/2015", "dd/MM/yyyy"),
          getInvoiceActualDeliveryDate(invoice));

    } catch (PropertiesException e) {
      fail("no validation exception expected");
    } catch (Exception e) {
      fail("exception " + e.getMessage());
    }

  }

  @Test
  public void testValidateProformaInvoice() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER_2");
    beanHeader.setDocumentType("325");

    beanHeader.setCurrency(CurrencyCodeType.FRF.value());

    Invoice invoice = instance.initNewInvoice();

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);
    try {
      doReturn(invoice).when(instance).initNewInvoice();

      beanHeader.setDocumentDate(DateUtils.parseDate("15/10/2015", "dd/MM/yyyy"));
      beanHeader.setDeliveryDate(DateUtils.parseDate("16/10/2015", "dd/MM/yyyy"));
      beanHeader.setDueDate(new Date());

      instance.validate(beanHeader, null);

      // Verification
      assertEquals(InvoiceTypeCodeType.PROFORMA_INVOICE, invoice.getInvoiceHeader()
          .getInvoiceType()
          .getInvoiceTypeCoded());
      assertEquals("INV_NUMBER_2", getInvoiceNumber(invoice));
      assertEquals(CurrencyCodeType.FRF, invoice.getInvoiceHeader()
          .getInvoiceCurrency()
          .getCurrencyCoded());
      assertEquals(CurrencyCodeType.FRF, invoice.getInvoiceHeader()
          .getTaxAccountingCurrency()
          .getCurrencyCoded());
      assertEquals(DateUtils.parseDate("15/10/2015", "dd/MM/yyyy"), getInvoiceIssueDate(invoice));
      assertEquals(DateUtils.parseDate("16/10/2015", "dd/MM/yyyy"),
          getInvoiceActualDeliveryDate(invoice));

    } catch (PropertiesException e) {
      fail("no validation exception expected");
    } catch (Exception e) {
      fail("exception " + e.getMessage());
    }

  }

  @Test
  public void testValidateCreditNote() {
    CsvHeader beanHeader = new CsvHeader();
    beanHeader.setDocumentNumber("INV_NUMBER_3");
    beanHeader.setDocumentType("381");

    beanHeader.setCurrency(CurrencyCodeType.HKD.value());

    Invoice invoice = instance.initNewInvoice();
    doReturn(invoice).when(instance).initNewInvoice();

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);

    try {
      beanHeader.setDocumentDate(DateUtils.parseDate("15/11/2015", "dd/MM/yyyy"));
      beanHeader.setShippingDate(DateUtils.parseDate("21/11/2015", "dd/MM/yyyy"));
      beanHeader.setDueDate(new Date());

      instance.validate(beanHeader, null);

      // Verification
      assertEquals(InvoiceTypeCodeType.CREDIT_NOTE_GOODS_AND_SERVICES, invoice.getInvoiceHeader()
          .getInvoiceType()
          .getInvoiceTypeCoded());
      assertEquals("INV_NUMBER_3", getInvoiceNumber(invoice));
      assertEquals(CurrencyCodeType.HKD, invoice.getInvoiceHeader()
          .getInvoiceCurrency()
          .getCurrencyCoded());
      assertEquals(CurrencyCodeType.HKD, invoice.getInvoiceHeader()
          .getTaxAccountingCurrency()
          .getCurrencyCoded());
      assertEquals(DateUtils.parseDate("15/11/2015", "dd/MM/yyyy"), getInvoiceIssueDate(invoice));
      assertEquals(DateUtils.parseDate("21/11/2015", "dd/MM/yyyy"),
          getInvoiceActualShipDate(invoice));

    } catch (PropertiesException e) {
      fail("no validation exception expected");
    } catch (Exception e) {
      fail("exception " + e.getMessage());
    }

  }
}
