package com.byzaneo.generix.edocument.service;

import static com.byzaneo.commons.bean.FileType.EDIUNIT;
import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.commons.util.FileHelper.deleteFile;
import static com.byzaneo.commons.util.JAXBHelper.unmarshal;
import static com.byzaneo.generix.edocument.service.EDocumentService.EDocument.getDocumentTypes;
import static com.byzaneo.generix.edocument.util.XcblHelper.AGENCY_CODED_OTHER;
import static com.byzaneo.generix.edocument.util.XcblHelper.FR_INSEE;
import static com.byzaneo.generix.edocument.util.XcblHelper.SIREN;
import static com.byzaneo.generix.util.DocumentCompoundTypeHelper.createDocumentCompoundType;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.like;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.xtrade.api.DocumentStatus.PENDING;
import static com.byzaneo.xtrade.api.DocumentStatus.REMOVED;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static org.apache.commons.lang3.time.DateUtils.addDays;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.byzaneo.commons.test.OpenEntityMangerInTest;
import com.byzaneo.commons.test.SpringJUnitClassListener;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.commons.util.JAXBHelper;
import com.byzaneo.commons.util.SpringContextHelper;
import com.byzaneo.generix.edocument.ExchangeNotAllowedException;
import com.byzaneo.generix.edocument.ExchangeNotAllowedException.Code;
import com.byzaneo.generix.edocument.bean.Archive;
import com.byzaneo.generix.edocument.bean.ArchivedIndexable;
import com.byzaneo.generix.edocument.bean.ArchivedInvoice;
import com.byzaneo.generix.edocument.bean.CarrefourArchivedFaxes;
import com.byzaneo.generix.edocument.service.documentcheck.DocumentFileCheckPolicy;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.location.bean.Address;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Exchange;
import com.byzaneo.security.bean.Exchange.Direction;
import com.byzaneo.security.bean.ExchangeId;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.Document_;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.util.DocumentHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndexer;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.AgencyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ComplexStringType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceIdentifierType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceNameAddressType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceOCRPartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoicePartyTaxInformationType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfInvoiceIdentifierType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoicePartyType;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Dec 20, 2014
 * @since 1.0
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/gnx-edoc-test.beans.xml"}, loader = SystemPropertyContextLoader.class)
public class EDocumentServiceITCase extends OpenEntityMangerInTest  {

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private SecurityService securityService;

  @Autowired
  @Qualifier(EDocumentService.SERVICE_NAME)
  private EDocumentService eDocumentService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  @Qualifier(DocumentStatusService.SERVICE_NAME)
  private DocumentStatusService statusService;

  // - ALLOWED EXHCANGES -
  private static Company cpy;
  private static Partner pt1;
  private static Partner pt2;

  private Company companyForExchanges;
  private Partner from;
  private Partner to;
  private Partner other;
  private Document document;
  private final String errorMessageSendingMode = "is not allowed to send this type of document to";
  private final String errorMessageReceivingMode = "is not allowed to receive this type of document from";

  /* -- LIFE CYCLE -- */

  @BeforeAll
  public static void beforeClass(@Autowired SecurityService securityService,
      @Autowired EDocumentService eDocumentService, @Autowired DocumentStatusService statusService)
      throws Exception {
    // - creates company & partners -
    cpy = new Company("TST", "My Company Fullname TST ", securityService.getApplication(),
        new HashSet<>());
    cpy.setCode("09876");
    securityService.saveCompany(cpy);
    pt1 = new Partner("TST-12345");
    pt1.setCode("12345");
    cpy.addChild(pt1);
    pt2 = new Partner("TST-67890");
    pt2.setCode("67890");
    cpy.addChild(pt2);

    securityService.saveGroup(pt1);
    securityService.saveGroup(pt2);

    Date now = new Date();
    String kind = getDocumentTypes().get(0);
    Exchange exchange = new Exchange(pt1, pt2, kind, Direction.S, addDays(now, -1), null);
    pt1.addExchangeToLegalEntity(exchange);
    securityService.saveGroup(pt1);
    Exchange exchange2 = new Exchange(cpy, pt1, kind, Direction.R, addDays(now, -1),
        addDays(now, 5));
    cpy.addExchangeToLegalEntity(exchange2);
    securityService.saveGroup(cpy);

    // Reset archive db
    eDocumentService.removeArchives(
        eDocumentService.searchArchives(ArchivedInvoice.class, createBuilder().query()));

    eDocumentService.saveArchives(createSampleArchiveIndexableList());
    DocumentHelper.populateDocumentStatusEntityTable(statusService);
  }


  /* -- ALLOWED EXCHANGE -- */

  @Test
  public void testCheckDocumentExchangeAllowed() {
    // creates document
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType(getDocumentTypes().get(0), "96A"),
        pt1.getCode(), pt2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));

    // checks exchange
    this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, false, false,
        pt2.getCode(),
        pt1.getCode(), "buyerToPartyID");

    // creates document
    doc = new Document("docTest2", cpy.getCode(),
        createDocumentCompoundType(getDocumentTypes().get(0), "96A"),
        pt1.getCode(), cpy.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, PDF, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));

    // checks exchange
    this.eDocumentService.checkDocumentExchange(cpy, Direction.R, doc,
        DocumentFileCheckPolicy.XCBL_AND_PDF, false, false, cpy.getCode(),
        pt1.getCode(), "buyerToPartyID");
  }

  @Test
  public void testCheckDocumentExchangeNotAllowed_document_kind_unknown() {
    // creates document
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("POUET", "96A"), // Wrong e-doc type
        pt1.getCode(), pt2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));

    // checks exchange
    try {
      this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
          DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, false, false,
          pt2.getCode(),
          pt1.getCode(), "buyerPartyTaxIdentifier");
      fail();
    } catch (ExchangeNotAllowedException enae) {
      assertEquals(Code.document_kind_unknown, enae.getCode());
    }
  }

  @Test
  public void testCheckDocumentExchangeNotAllowed_wrong_source() {
    // creates document
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType(getDocumentTypes().get(0), "96A"),
        pt1.getCode(), pt2.getCode(),
        PENDING);
    // no PDF or EDIUNIT file
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));

    // checks exchange
    try {
      this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
          DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, false, false,
          pt2.getCode(),
          pt1.getCode(), "billToPartyID");
      fail();
    } catch (ExchangeNotAllowedException enae) {
      assertEquals(Code.wrong_source, enae.getCode());
    }

    // creates document
    doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType(getDocumentTypes().get(0), "96A"),
        pt1.getCode(), pt2.getCode(),
        PENDING);
    // no XCBL
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));

    // checks exchange
    try {
      this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
          DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, false, false,
          pt2.getCode(),
          pt1.getCode(), "billToPartyID");
      fail();
    } catch (ExchangeNotAllowedException enae) {
      assertEquals(Code.wrong_source, enae.getCode());
    }
  }

  @Test
  public void testCheckDocumentExchangeNotAllowed_wrong_to() {
    // creates document
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType(getDocumentTypes().get(0), "96A"),
        pt1.getCode(), "XXXXX", // Wrong TO
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));

    // checks exchange
    try {
      this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
          DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, false, false, "XXXXX",
          pt1.getCode(), "to");
      fail();
    } catch (ExchangeNotAllowedException enae) {
      assertEquals(Code.wrong_to, enae.getCode());
    }
  }

  @Test
  @Transactional
  public void testCheckDocumentExchangeAllowed_createExchange() {
    // creates document
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("INVOIC", "96A"),
        pt1.getCode(), pt2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));

    this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true, false, pt2.getCode(),
        pt1.getCode(), "to");

    QueryBuilder qb = QueryBuilder.createBuilder();
    qb.and(equal("id.direction", "S"),
        equal("id.kind", "INVOIC"),
        equal("end", null),
        equal("id.legalEntity.id", pt1.getId()),
        equal("id.dematPartner.id", pt2.getId()));
    Page<Exchange> ex = this.securityService.searchExchange(qb.query(), null);
    assertNotNull(ex);
    assertEquals(1, ex.getTotalElements());

    Exchange expectedExchange = createExchange(null, "INVOIC", Direction.S, pt1, pt2);
    assertEquals(expectedExchange.getEnd(), null);
    assertEquals(expectedExchange.getId(), ex.getContent()
        .get(0)
        .getId());
  }

  @Test
  @Transactional
  public void testCreatPartner_missingInformation() {
    Partner p1 = createPartner("P1", "01", "STR1", "123", null, Locale.FRANCE, "SRT1", "VAT1"),
        p2 = createPartner("P2", "02", "STR2", "456", "CT2", Locale.UK, "SRT2", "VAT2");
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("INVOIC", "96A"),
        p1.getCode(), p2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));
    addIndex(doc, p1, p2);

    try {
      this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
          DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true, true, p2.getCode(),
          p1.getCode(), "billToPartyID");
      fail();
    } catch (ExchangeNotAllowedException enae) {
      assertEquals(Code.partner_information_missing, enae.getCode());
    }
  }

  @Test
  @Transactional
  public void testCreatPartner_newPartners() {
    Partner p1 = createPartner("P1", "01", "STR1", "123", "CT1", Locale.FRANCE, "SRT1", "VAT1"),
        p2 = createPartner("P2", "02", "STR2", "456", "CT2", Locale.UK, "SRT2", "VAT2");
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("INVOIC", "96A"),
        p1.getCode(), p2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));
    addIndex(doc, p1, p2);

    this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true, true, p2.getCode(),
        p1.getCode(), "billToPartyID");

    assertTrue(samePartner(p1, securityService.getPartner(p1.getCode(), cpy)));
    assertTrue(samePartner(p2, securityService.getPartner(p2.getCode(), cpy)));
  }

  @Test
  @Transactional
  public void testCreatPartner_newSeller() {
    Partner p3 = createPartner("P3", "03", "STR3", "123", "CT3", Locale.FRANCE, "SRT3", "VAT3"),
        p2 = createPartner("P2", pt2.getCode(), "STR2", "456", "CT2", Locale.UK, "SRT2", "VAT2");
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("INVOIC", "96A"),
        p3.getCode(), p2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));
    addIndex(doc, p3, p2);

    this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true, true, p2.getCode(),
        p3.getCode(), "billToPartyID");

    assertTrue(samePartner(p3, securityService.getPartner(p3.getCode(), cpy)));
  }

  @Test
  @Transactional
  public void testCreatPartner_newBillTo() {
    Partner p1 = createPartner("P1", pt1.getCode(), "STR1", "123", "CT1", Locale.FRANCE, "SRT1",
        "VAT1"),
        p4 = createPartner("P4", "04", "STR4", "456", "CT4", Locale.UK, "SRT4", "VAT4");
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("INVOIC", "96A"),
        p1.getCode(), p4.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));
    addIndex(doc, p1, p4);

    this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true, true, p4.getCode(),
        p1.getCode(), "billToPartyID");

    assertTrue(samePartner(p4, securityService.getPartner(p4.getCode(), cpy)));
  }

  @Test
  @Transactional
  public void testCreatPartner_noNewPartner() {
    int partnerCount = securityService.getPartners(cpy)
        .size();
    Partner p1 = createPartner("P1", pt1.getCode(), "STR1", "123", "CT1", Locale.FRANCE, "SRT1",
        "VAT1"),
        p2 = createPartner("P2", pt2.getCode(), "STR2", "456", "CT2", Locale.UK, "SRT2", "VAT2");
    Document doc = new Document("docTest1", cpy.getCode(),
        createDocumentCompoundType("INVOIC", "96A"),
        p1.getCode(), p2.getCode(),
        PENDING);
    doc.addFile(new DocumentFile(null, EDIUNIT, null, null, doc, null));
    doc.addFile(new DocumentFile(null, XCBL, null, null, doc, null));
    addIndex(doc, p1, p2);

    this.eDocumentService.checkDocumentExchange(cpy, Direction.S, doc,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true, true, p2.getCode(),
        p1.getCode(), "billToPartyID");

    assertTrue(securityService.getPartners(cpy)
        .size() == partnerCount);
  }

  private Exchange createExchange(Date end, String documentKind, Direction direction, Partner from,
      Partner to) {
    Exchange exchange = new Exchange();
    exchange.setEnd(end);
    ExchangeId exchangeId = new ExchangeId();
    exchangeId.setKind(documentKind);
    exchangeId.setDirection(direction);
    if (Direction.S.equals(direction)) {
      exchangeId.setLegalEntity(from);
      exchangeId.setDematPartner(to);
    } else {
      exchangeId.setDematPartner(from);
      exchangeId.setLegalEntity(to);
    }
    exchange.setId(exchangeId);
    return exchange;
  }

  private Partner createPartner(String name, String ident, String street, String postalCode,
      String city, Locale locale, String siren,
      String vat) {
    Partner partner = new Partner();
    partner.setFullname(name);
    partner.setCode(ident);
    Address address = partner.getLocation()
        .getAddress();
    address.setStreetName(street);
    address.setPostalCode(postalCode);
    address.setCity(city);
    address.setCountry(locale);
    partner.setRegistration(siren);
    partner.setVat(vat);
    return partner;
  }

  private boolean samePartner(Partner p1, Partner p2) {
    return p1.getFullname()
        .equals(p2.getFullname()) && p1.getCode()
        .equals(p2.getCode()) &&
        p1.getLocation()
            .equals(p2.getLocation()) &&
        p1.getRegistration()
            .equals(p2.getRegistration()) &&
        p1.getVat()
            .equals(p2.getVat());
  }

  private void addIndex(Document document, Partner seller, Partner billTo) {
    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
        .setInvoiceParty(new InvoicePartyType());
    invoice.getInvoiceHeader()
        .getInvoiceParty()
        .setSellerParty(populatePartie(seller));
    invoice.getInvoiceHeader()
        .getInvoiceParty()
        .setBillToParty(populatePartie(billTo));
    document.setIndexValue(invoice);
  }

  private InvoiceOCRPartyType populatePartie(Partner partner) {
    Address address = partner.getLocation()
        .getAddress();
    InvoiceOCRPartyType partie = new InvoiceOCRPartyType();
    partie.setNameAddress(new InvoiceNameAddressType());
    partie.getNameAddress()
        .setName1(new ComplexStringType());
    partie.getNameAddress()
        .getName1()
        .setValue(partner.getFullname());
    partie.setPartyID(new InvoiceIdentifierType());
    partie.getPartyID()
        .setIdent(new ComplexStringType());
    partie.getPartyID()
        .getIdent()
        .setValue(partner.getCode());
    partie.getNameAddress()
        .setStreet(new ComplexStringType());
    partie.getNameAddress()
        .getStreet()
        .setValue(address.getStreetName());
    partie.getNameAddress()
        .setPostalCode(new ComplexStringType());
    partie.getNameAddress()
        .getPostalCode()
        .setValue(address.getPostalCode());
    partie.getNameAddress()
        .setCity(new ComplexStringType());
    partie.getNameAddress()
        .getCity()
        .setValue(address.getCity());
    partie.getNameAddress()
        .setCountry(new CountryType());
    partie.getNameAddress()
        .getCountry()
        .setCountryCoded(CountryCodeType.fromValue(address.getCountry()
            .getCountry()));
    partie.setListOfIdentifier(new ListOfInvoiceIdentifierType());
    partie.getListOfIdentifier()
        .getIdentifier()
        .add(new InvoiceIdentifierType());
    InvoiceIdentifierType identifier = partie.getListOfIdentifier()
        .getIdentifier()
        .get(0);
    identifier.setAgency(new AgencyType());
    AgencyType agencySiren = identifier.getAgency();
    agencySiren.setAgencyCoded(FR_INSEE);
    agencySiren.setCodeListIdentifierCoded(AGENCY_CODED_OTHER);
    agencySiren.setCodeListIdentifierCodedOther(SIREN);
    identifier.setIdent(new ComplexStringType());
    identifier.getIdent().setValue(partner.getRegistration());
    partie.setPartyTaxInformation(new InvoicePartyTaxInformationType());
    partie.getPartyTaxInformation()
        .setTaxIdentifier(new InvoiceIdentifierType());
    InvoiceIdentifierType taxIdentifier = partie.getPartyTaxInformation()
        .getTaxIdentifier();
    taxIdentifier.setAgency(new AgencyType());
    AgencyType agencyTva = taxIdentifier.getAgency();
    agencyTva.setAgencyCoded("CEC");
    agencyTva.setCodeListIdentifierCoded("ValueAddedTaxIdentification");
    taxIdentifier.setIdent(new ComplexStringType());
    taxIdentifier.getIdent().setValue(partner.getVat());
    return partie;
  }

  /* -- RECAP LIST -- */

  @Test
  public void testExportRecapList() throws Exception {
    try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
      this.eDocumentService.exportRecapListItems(
          createBuilder().and(like("legalEntityCodeCode", "legalEntityCodeCode1*"))
              .query(), out, Locale.FRANCE, null);
      System.out.println(new String(out.toByteArray()));
    } catch (IOException e) {
      fail(e.getMessage());
    }

  }

  @Test
  public void updateDocumentTest() throws IOException {
    File file = new File("./src/test/resources/xcbl/InvoiceSample-ABC_xCBL40.xml");
    File file0 = new File("./src/test/resources/xcbl/invoice-sample_0.xml");
    File file1 = new File("./src/test/resources/xcbl/invoice-sample_1.xml");

    FileHelper.copyFile(file, file0);
    FileHelper.copyFile(file, file1);

    List<Document> documents = new ArrayList<>();
    Document document = new Document();
    document.addFile(new DocumentFile(file0, XCBL, null, null, document, null));
    documents.add(document);

    document = new Document();
    document.addFile(new DocumentFile(file1, XCBL, null, null, document, null));
    documents.add(document);

    eDocumentService.updateDocument(documents);
    Invoice invoice = documents.get(0)
        .getIndexValue();

    assertTrue("FR00123456789".contains(invoice.getInvoiceHeader()
        .getListOfNameValueSet()
        .getNameValueSet()
        .get(0)
        .getListOfNameValuePair()
        .getNameValuePair()
        .get(0)
        .getValue()));

    assertTrue("FR00123456789".contains(invoice.getInvoiceHeader()
        .getListOfNameValueSet()
        .getNameValueSet()
        .get(0)
        .getListOfNameValuePair()
        .getNameValuePair()
        .get(1)
        .getValue()));

    eDocumentService.updateDocument(documents, "FRXXXXXXXXXXXXXX");
    invoice = documents.get(1)
        .getIndexValue();

    assertTrue("FRXXXXXXXXXXXXXX".contains(invoice.getInvoiceHeader()
        .getListOfNameValueSet()
        .getNameValueSet()
        .get(0)
        .getListOfNameValuePair()
        .getNameValuePair()
        .get(0)
        .getValue()));

    assertTrue("FRXXXXXXXXXXXXXX".contains(invoice.getInvoiceHeader()
        .getListOfNameValueSet()
        .getNameValueSet()
        .get(0)
        .getListOfNameValuePair()
        .getNameValuePair()
        .get(1)
        .getValue()));

    deleteFile(file0);
    deleteFile(file1);
  }

  /* -- ARCHIVE SERVICE -- */

  @Test
  public void crudArchivesTest() throws Exception {
    assertEquals(0, eDocumentService.saveArchives(null)
        .size());

    List<ArchivedInvoice> archives = new ArrayList<>();
    Invoice invoice = unmarshal(Invoice.class,
        new File("./src/test/resources/xcbl/InvoiceSample-ABC_xCBL40.xml"));
    InvoiceIndexer indexer = SpringContextHelper.getBean(InvoiceIndexer.class,
        InvoiceIndexer.INDEXER_NAME);
    for (int i = 0; i < 10; i++) {
      ArchivedInvoice archive = new ArchivedInvoice();
      archive.setArchiveUid(UUID.randomUUID()
          .toString());
      archive.setSafeBox("CDC ARKINEO");
      archive.setCreationDate(new Date());
      archive.setOwners("ARCHIVE_OWNER");
      archive.setFrom("xxxxxxxxxxxxxxxxxxxxxxx");
      archive.setTo("xxxxxxxxxxxxxxxxxxxxxxx");
      archive.setInvoice((InvoiceIndex) indexer.createIndexable(invoice));
      if (i < 5) {
        archive.setStatusWithEnumValue(PENDING);
      } else {
        archive.setStatusWithEnumValue(REMOVED);
      }
      archives.add(archive);
    }
    assertEquals(10, eDocumentService.saveArchives(archives)
        .size()); // SAVING ARCHIVES

    QueryBuilder qb = createBuilder().and(Clauses.equal("status", "REMOVED"));
    eDocumentService.removeArchives(
        eDocumentService.searchArchives(ArchivedInvoice.class, qb.query())); // REMOVE ARCHIVES

    QueryBuilder qbs = createBuilder().and(Clauses.equal("status", "PENDING"));
    assertEquals(5, eDocumentService.searchArchives(ArchivedInvoice.class, qbs.query())
        .size()); // SEARCH
  }

  @Test
  public void testImportInvoice() {
    Partner partner = createMockedPartner();
    Company company = createMockedCompany();

    try {
      // list of files
      List<InputStream> list = new ArrayList<>();
      list.add(
          new FileInputStream(new File("src/test/resources/xcbl/invoice/invoice-compute2.csv")));
      list.add(
          new FileInputStream(new File("src/test/resources/xcbl/invoice/invoice-footer2.csv")));
      eDocumentService.importInvoices(company, partner, list, 5, 2);

      QueryBuilder qb = QueryBuilder.createBuilder();
      qb.and(equal(Document_.owners.getName(), company.getCode()),
          equal(Document_.from.getName(), partner.getCode()),
          equal("invoiceTypeCoded", "CREDIT_NOTE_GOODS_AND_SERVICES"));

      List<InvoiceIndex> documents = this.documentService.searchIndexables(InvoiceIndex.class,
              qb.query(), null)
          .getContent();
      assertEquals(1, documents.size());

      qb = QueryBuilder.createBuilder();
      qb.and(equal(Document_.owners.getName(), company.getCode()),
          equal(Document_.from.getName(), partner.getCode()),
          equal("invoiceTypeCoded", "COMMERCIAL_INVOICE"));
      documents = this.documentService.searchIndexables(InvoiceIndex.class, qb.query(), null)
          .getContent();
      assertEquals(1, documents.size());
    } catch (Exception e) {
      e.printStackTrace();
      fail();
    }
  }

  @Test
  public void testImportInvoice_referenceOrderDate() {
    Partner partner = createMockedPartner();
    Company company = createMockedCompany();

    try {

      // save order
      Order order = JAXBHelper.unmarshal(Order.class,
          new File("./src/test/resources/xcbl/OrderSample-ABC_xCBL40.xml"));
      order.setOwners(company.getCode());
      order.setFrom(partner.getCode());
      order.setStatusWithEnumValue(DocumentStatus.NONE);
      Document doc = new Document();
      doc.setOwners(company.getCode());
      doc.setFrom(partner.getCode());
      doc.setIndexValue(order);
      doc.setStatusWithEnumValue(DocumentStatus.NONE);
      DocumentFile docFile = new DocumentFile(
          new File("./src/test/resources/xcbl/OrderSample-ABC_xCBL40.xml"), XCBL, null,
          null, doc, null, "official_index", null);
      doc.getFiles()
          .add(docFile);
      this.documentService.saveDocument(doc);

      // list of files
      List<InputStream> list = new ArrayList<>();
      list.add(
          new FileInputStream(new File("src/test/resources/xcbl/invoice/invoice-compute3.csv")));
      eDocumentService.importInvoices(company, partner, list, 5, 2);

      QueryBuilder qb = QueryBuilder.createBuilder();
      qb.and(equal(Document_.owners.getName(), company.getCode()),
          equal(Document_.from.getName(), partner.getCode()),
          equal("invoiceTypeCoded", "CREDIT_NOTE_GOODS_AND_SERVICES"));
      List<InvoiceIndex> documents = this.documentService.searchIndexables(InvoiceIndex.class,
              qb.query(), null)
          .getContent();
      assertEquals(1, documents.size());

      assertEquals(new GregorianCalendar(2003, 0, 1, 0, 0, 1).getTime(), documents.get(0)
          .getPurchaseOrderDate());
    } catch (Exception e) {
      e.printStackTrace();
      fail();
    }
  }

  private Company createMockedCompany() {
    Company company = mock(Company.class, RETURNS_DEEP_STUBS);
    when(company.getFullname()).thenReturn("Generix group");
    when(company.getCode()).thenReturn("codeg");
    when(company.getLocation()
        .getAddress()
        .getStreetName()).thenReturn("15 rue Beaubourg");
    when(company.getLocation()
        .getAddress()
        .getPostalCode()).thenReturn("75003");
    when(company.getLocation()
        .getAddress()
        .getCity()).thenReturn("Paris");
    when(company.getLocation()
        .getAddress()
        .getCountry()).thenReturn(Locale.FRANCE);
    when(company.getDescription()).thenReturn(Company.DESCRIPTION);
    return company;
  }

  private Partner createMockedPartner() {
    Partner partner = mock(Partner.class, RETURNS_DEEP_STUBS);
    when(partner.getFullname()).thenReturn("Partner");
    when(partner.getCode()).thenReturn("codep");
    when(partner.getLocation()
        .getAddress()
        .getStreetName()).thenReturn("15 rue des Mimosas");
    when(partner.getLocation()
        .getAddress()
        .getPostalCode()).thenReturn("13001");
    when(partner.getLocation()
        .getAddress()
        .getCity()).thenReturn("Marseille");
    when(partner.getLocation()
        .getAddress()
        .getCountry()).thenReturn(Locale.FRANCE);
    when(partner.getDescription()).thenReturn(Partner.DESCRIPTION);
    return partner;
  }

  @Test
  @Transactional
  public void testSearchExchangeFromDbSql() {
    initExchange();
    Company company = securityService.getCompanyByCode("XXX");
    Partner partner = securityService.getPartner("Partnercode-YYY", company);
    com.byzaneo.security.bean.Exchange exchange = eDocumentService.searchExchange("ORDERS",
        (Group) company, (Group) partner, Direction.S,
        new Date(), false);

    Assertions.assertNotNull(exchange);
  }

  @Test
  public void testExchangeAll() {
    initPartnersDocAndCompanyInvoic();
    testNoExchangeCreatedForPartners(Direction.S, errorMessageSendingMode);
    testNoExchangeCreatedForPartners(Direction.R, errorMessageReceivingMode);

    testExchangeCreatedWithStartDateAfterToday(Direction.S, errorMessageSendingMode);
    testExchangeCreatedWithStartDateAfterToday(Direction.R, errorMessageReceivingMode);

    testExchangeCreatedWithWrongDocumentType(Direction.S, errorMessageSendingMode);
    testExchangeCreatedWithWrongDocumentType(Direction.R, errorMessageReceivingMode);

    testExchangeCreatedWithWrongPartner(Direction.S, errorMessageSendingMode);
    testExchangeCreatedWithWrongPartner(Direction.R, errorMessageReceivingMode);

    testGetExchangeInDatabase();
  }

  @Test
  public void testQueryShouldReturn_OrdersOnly() {
    Query ordersQuery = toQuery("type = ORDERS");
    List<ArchivedIndexable> shouldBeOrdersOnly = eDocumentService.searchArchives(
        ArchivedIndexable.class, ordersQuery);
    Assertions.assertTrue(shouldBeOrdersOnly.stream()
        .allMatch(archive -> archive.getType()
            .equals(DocumentType.ORDERS.toString())));
  }

  @Test
  public void testQueryShouldReturn_DesadvOnly() {
    Query desadvQuery = toQuery("type = DESADV");
    List<ArchivedIndexable> shouldBeDesadvOnly = eDocumentService.searchArchives(
        ArchivedIndexable.class, desadvQuery);
    Assertions.assertTrue(shouldBeDesadvOnly.stream()
        .allMatch(archive -> archive.getType()
            .equals(DocumentType.DESADV.toString())));
  }

  @Test
  public void testQueryShouldReturn_InventoryReportOnly() {
    Query invrptQuery = toQuery("type = INVRPT");
    List<ArchivedIndexable> shouldBeInvrptOnly = eDocumentService.searchArchives(
        ArchivedIndexable.class, invrptQuery);
    Assertions.assertTrue(shouldBeInvrptOnly.stream()
        .allMatch(archive -> archive.getType()
            .equals(DocumentType.INVRPT.toString())));
  }

  @Test
  public void testQueryShouldReturn_ArchiveUuidOnly() {
    Query invrptQuery = toQuery("type = INVOIC");
    List<String> uuidList = eDocumentService.searchArchivesUid(ArchivedInvoice.class, invrptQuery);

    assertEquals(2, uuidList.size());

    Assertions.assertTrue(uuidList.stream()
        .anyMatch(archiveUUID -> archiveUUID.equals("ArchivedInvoice_UUID_1")));

    Assertions.assertTrue(uuidList.stream()
        .anyMatch(archiveUUID -> archiveUUID.equals("ArchivedInvoice_UUID_2")));
  }

  public void testExchangeCreatedWithStartDateAfterToday(Direction direction, String message) {
    Exchange exchangeAfterToday = new Exchange(from, to, "INVOIC", direction,
        addDays(new Date(), 1), null);
    to.addExchangeToLegalEntity(exchangeAfterToday);
    executeTestExchange(direction, message);
  }

  public void testNoExchangeCreatedForPartners(Direction direction, String message) {
    executeTestExchange(direction, message);
  }

  public void testExchangeCreatedWithWrongDocumentType(Direction direction, String message) {
    Exchange exchangeWithWrongDocumentType = new Exchange(from, to, "DESADV", direction,
        addDays(new Date(), -1), null);
    to.addExchangeToLegalEntity(exchangeWithWrongDocumentType);
    executeTestExchange(direction, message);
  }

  public void testExchangeCreatedWithWrongPartner(Direction direction, String message) {
    Exchange exchangeWithWrongPartner = new Exchange(from, other, "INVOIC", direction,
        addDays(new Date(), -1), null);
    to.addExchangeToLegalEntity(exchangeWithWrongPartner);
    executeTestExchange(direction, message);
  }

  public void testGetExchangeInDatabase() {
    from.addExchangeToLegalEntity(
        new Exchange(from, to, "INVOIC", Direction.S, addDays(new Date(), -1), null));
    securityService.saveGroup(from);
    Exchange result = eDocumentService.searchExchange("INVOIC", from, to, Direction.S, new Date(),
        false);
    Assertions.assertTrue(result != null && result.getId()
        .getKind()
        .equals("INVOIC") &&
        result.getId()
            .getDirection() == Direction.S &&
        result.getId()
            .getLegalEntity()
            .getId()
            .equals(from.getId()) &&
        result.getId()
            .getDematPartner()
            .getId()
            .equals(to.getId()));
  }

  private void initPartnersDocAndCompanyInvoic() {
    companyForExchanges = new Company("EXC", "TO-Generix", securityService.getApplication(),
        new HashSet<>());
    companyForExchanges.setCode("EXC");
    securityService.saveCompany(companyForExchanges);
    from = new Partner("FROM");
    from.setCode("CODE_FROM");
    to = new Partner("TO");
    to.setCode("CODE_TO");
    other = new Partner("OTHER");
    other.setCode("CODE_OTHER");

    companyForExchanges.addChild(from);
    companyForExchanges.addChild(to);
    companyForExchanges.addChild(other);

    from = securityService.saveGroup(from);
    to = securityService.saveGroup(to);
    other = securityService.saveGroup(other);

    document = new Document("doc_reference", "GNX", "{\"kind\":\"INVOIC\",\"norm\":\"D96A\"}",
        "CODE_FROM", "CODE_TO",
        DocumentStatus.PENDING);
    document.addFile(new DocumentFile(null, EDIUNIT, null, null, document, null));
    document.addFile(new DocumentFile(null, XCBL, null, null, document, null));

  }

  private static List<Archive> createSampleArchiveIndexableList() {
    List<Archive> docList = new ArrayList<>();

    ArchivedIndexable order1 = new ArchivedIndexable();
    order1.setType(DocumentType.ORDERS.toString());
    order1.setCreationDate(new Date());
    order1.setReference("Order_1");
    docList.add(order1);

    ArchivedIndexable order2 = new ArchivedIndexable();
    order2.setType(DocumentType.ORDERS.toString());
    order2.setCreationDate(new Date());
    order2.setReference("Order_2");
    docList.add(order2);

    ArchivedIndexable desadv1 = new ArchivedIndexable();
    desadv1.setType(DocumentType.DESADV.toString());
    desadv1.setCreationDate(new Date());
    desadv1.setReference("Desadv_1");
    docList.add(desadv1);

    ArchivedIndexable desadv2 = new ArchivedIndexable();
    desadv2.setType(DocumentType.DESADV.toString());
    desadv2.setCreationDate(new Date());
    desadv2.setReference("Desadv_2");
    docList.add(desadv2);

    ArchivedIndexable InventoryReport1 = new ArchivedIndexable();
    InventoryReport1.setType(DocumentType.INVRPT.toString());
    InventoryReport1.setCreationDate(new Date());
    InventoryReport1.setReference("Inv_report_1");
    docList.add(InventoryReport1);

    ArchivedIndexable InventoryReport2 = new ArchivedIndexable();
    InventoryReport2.setType(DocumentType.INVRPT.toString());
    InventoryReport2.setCreationDate(new Date());
    InventoryReport2.setReference("Inv_report_2");
    docList.add(InventoryReport2);

    CarrefourArchivedFaxes carrefourFax1 = new CarrefourArchivedFaxes();
    carrefourFax1.setType("FAX");
    carrefourFax1.setCreationDate(new Date());
    carrefourFax1.setReference("Carrefour_fax_1");
    docList.add(carrefourFax1);

    ArchivedInvoice archivedInvoice1 = new ArchivedInvoice();
    archivedInvoice1.setType(DocumentType.INVOIC.toString());
    archivedInvoice1.setCreationDate(new Date());
    archivedInvoice1.setReference("ArchivedInvoice_ref_1");
    archivedInvoice1.setArchiveUid("ArchivedInvoice_UUID_1");
    docList.add(archivedInvoice1);

    ArchivedInvoice archivedInvoice2 = new ArchivedInvoice();
    archivedInvoice2.setType(DocumentType.INVOIC.toString());
    archivedInvoice2.setCreationDate(new Date());
    archivedInvoice2.setReference("ArchivedInvoice_ref_2");
    archivedInvoice2.setArchiveUid("ArchivedInvoice_UUID_2");
    docList.add(archivedInvoice2);

    return docList;
  }

  private void executeTestExchange(Direction direction, String message) {
    eDocumentService.tryCheckDocumentExchange(companyForExchanges, direction, document,
        DocumentFileCheckPolicy.XCBL_AND_EDIUNIT, true,
        false, false, false, document.getTo(), document.getFrom(), "to");
    Assertions.assertTrue(isEqualsStatusCode(document.getStatus(), DocumentStatus.REFUSED));
    Assertions.assertTrue(document.getError()
        .contains(message));
    document.getFiles()
        .forEach(file -> Assertions.assertTrue(isEqualsStatusCode(file.getDocument()
            .getStatus(), DocumentStatus.REFUSED)));
  }

  private void initGroups(String legalEntity, String dematPartner) {
    Company cpy = new Company("CompanyName-" + legalEntity, "My Company Fullname : " + legalEntity,
        securityService.getApplication(),
        new LinkedHashSet<>());
    cpy.setCode(legalEntity);
    Partner partner = new Partner("PartnerName-" + dematPartner);
    partner.setCode("Partnercode-" + dematPartner);
    cpy.addChild(partner);

    securityService.saveGroup(cpy);
    securityService.saveGroup(partner);
  }

  private void initExchange() {
    initGroups("XXX", "YYY");
    Group legalEntity = this.securityService.getGroupByName("CompanyName-XXX");
    Group dematPartner = this.securityService.getGroupByName("PartnerName-YYY");
    com.byzaneo.security.bean.Exchange exchange = new com.byzaneo.security.bean.Exchange(
        legalEntity, dematPartner, "ORDERS", Direction.S,
        DateUtils.addDays(new Date(), -2), DateUtils.addDays(new Date(), 2));
    legalEntity.addExchangeToLegalEntity(exchange);
    securityService.saveGroup(legalEntity);
  }

}
