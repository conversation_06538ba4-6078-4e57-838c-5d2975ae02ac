package com.byzaneo.generix.edocument.util;

import com.byzaneo.commons.util.MemoryAppender;
import com.byzaneo.security.bean.Location;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.ItemDetailType;
import com.rits.cloning.Cloner;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import javax.xml.transform.stream.StreamSource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.byzaneo.generix.edocument.util.AsnXcblHelper.initASNWithOrder;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.*;
import static com.byzaneo.xtrade.api.DocumentStatus.INVOICED;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.*;
import static com.byzaneo.xtrade.xcbl.util.converters.OCRTypeConverter.convertToInvoicePaymentInstructionsType;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.*;

//TODO ugly test... Need to be refactored
public class InvoiceXcblHelperTest {

  @TempDir
  public File folder;
  private Order order;

  @Mock
  Logger log;

  private static final MemoryAppender memoryAppender = new MemoryAppender("MemoryAppender", null, null);

  @BeforeAll
  public static void beforeAll(){
    LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
    Configuration config = ctx.getConfiguration();
    LoggerConfig loggerConfig = config.getLoggerConfig(LogManager.ROOT_LOGGER_NAME);
    loggerConfig.setLevel(Level.ALL);
    loggerConfig.addAppender(memoryAppender, Level.ALL, null);
    ctx.updateLoggers();

  }

  @AfterEach
  public void afterAll(){
    memoryAppender.stop();
  }

  @BeforeEach
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
    final JAXBContext jc = JAXBContext.newInstance(Order.class);
    final Unmarshaller unmarshaller = jc.createUnmarshaller();
    final InputStream orderStream = InvoiceXcblHelperTest.class.getResourceAsStream("/xcbl/OrderSample-ABC_xCBL40.xml");
    order = unmarshaller.unmarshal(new StreamSource(orderStream), Order.class)
        .getValue();
    memoryAppender.start();
  }

  @Test
  public void testPaymentChoice() {
    assertThrows(IllegalArgumentException.class, () ->
      PaymentChoiceEnum.fromValue("Payment"));
  }

  @Test
  public void testExemptTVAItem() {
    assertThrows(IllegalArgumentException.class, () ->
      ExemptVatEnum.fromValue("Article 100 ter-i of the CGI"));
  }

  @Test
  public void testInitPaymentMethod() {
    List<PaymentMethodType> paymentMethodList = new ArrayList<>();
    initPaymentMethod(paymentMethodList, PaymentChoiceEnum.fromValue("Cheque"));
    initPaymentMethod(paymentMethodList, PaymentChoiceEnum.fromValue("PaymentToBankAccount"));
    initPaymentMethod(paymentMethodList, PaymentChoiceEnum.fromValue("PromissoryNote"));
    initPaymentMethod(paymentMethodList, PaymentChoiceEnum.fromValue("BillDrawnByTheCreditorOnTheDebtor"));
    assertEquals(4, paymentMethodList.size());
  }

  @Test
  public void initInvoiceWithASNNull() {
    try {
      InvoiceXcblHelper.initInvoiceWithASN(null, null, null);
      fail("Should throw Illegal argument exception");
    }
    catch (final IllegalArgumentException e) {
      assertTrue(e.getMessage()
          .contains("Advance shipment notice null : Illegal arguments"));
    }
  }

  @Test
  public void testInitInvoiceWithASN() {
    final AdvanceShipmentNotice desadv = initASNWithOrder(order);
    // used to test OCRTypeConverter#convertToListOfInvoicePartyCodedType
    desadv.getASNHeader()
        .getASNParty()
        .setListOfPartyCoded(new ListOfPartyCodedType());
    desadv.getASNHeader()
        .getASNParty()
        .getListOfPartyCoded()
        .getPartyCoded()
        .addAll(createListOfPartyCodedType(desadv));
    final Invoice invoice = InvoiceXcblHelper.initInvoiceWithASN(desadv, order, new OrderResponse());

    assertEquals(desadv.getASNHeader()
            .getASNNumber(),
        getRefNumFromAsnNumber(invoice.getInvoiceHeader()
            .getInvoiceReferences()
            .stream()
            .filter(a -> a.getASNNumber() != null)
            .collect(Collectors.toList())
            .get(0)));
  }

  @Test
  public void testAttachInvoiceDocument() {
    Document document = new Document();
    Document invoice = new Document();
    InvoiceXcblHelper.attachInvoiceDocument(document, invoice);
    assertEquals(1, document.getChildCount());
    assertEquals(invoice, document.getChildren()
        .iterator()
        .next());
  }

  @Test
  public void testCreateInvoiceDocumentFromDocument() throws IOException, JAXBException {
    Invoice invoice = getInvoice();
    Document document = new Document();
    document.setFrom("A");
    document.setTo("B");
    document.setOwners("Owner");
    Document invoiceDoc = InvoiceXcblHelper.createInvoiceDocumentFromParent(document, invoice, folder
        .getPath(), null, null, null, false);

    assertTrue(invoiceDoc.getOwners()
        .equals("Owner") &&
        invoiceDoc.getFrom()
            .equals("A") &&
        invoiceDoc.getTo()
            .equals("B"));
    assertEquals(document, invoiceDoc.getParent());
    assertEquals(INVOICED, invoiceDoc.getStatusAsEnumValue());
    assertEquals("{\"kind\":\"INVOIC\",\"norm\":\"USER_RECORD\"}", invoiceDoc.getType());
  }

  @Test
  public void testCreateInvoiceDocumentFromInvoice() throws IOException, JAXBException {
    Invoice invoice = getInvoice();
    Document invoiceDoc = InvoiceXcblHelper.createInvoiceDocument(invoice, "Owner", "Invoice Document from an invoice", folder
        .getPath(), null, null, false);

    assertTrue(invoiceDoc.getOwners()
        .equals("Owner") &&
        invoiceDoc.getFrom()
            .equals("LRM") &&
        invoiceDoc.getTo()
            .equals("MAF_Id"));
    assertEquals(INVOICED, invoiceDoc.getStatusAsEnumValue());
    assertEquals("{\"kind\":\"INVOIC\",\"norm\":\"USER_RECORD\"}", invoiceDoc.getType());
  }

  @Test
  public void testInitNewInvoice() {
    Invoice invoice = new Invoice();
    Partner partner = new Partner();
    partner.setLocation(new Location("DEFAULT", new com.byzaneo.location.bean.Address(), true));
    partner.getLocation()
        .getAddress()
        .setCountry(Locale.FRANCE);

    InvoiceXcblHelper.initNewInvoice(invoice, partner);
    assertNotNull(invoice.getInvoiceHeader());
    assertNotNull(invoice.getInvoiceDetail());
    assertNotNull(invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail());
    assertNotNull(invoice.getInvoiceSummary());
  }

  @Test
  public void testGetListOfPartyCodedAndAddPartnerInformation() {
    Invoice invoice = getInvoice();
    String[] entities = { "Sender", "Buyer", "Seller", "RemitTo" };
    List<InvoicePartyCodedType> listOfPartyCoded = InvoiceXcblHelper.getListOfPartyCoded(invoice, entities);
    Partner partner = new Partner();
    partner.setLocation(new Location("DEFAULT", new com.byzaneo.location.bean.Address(), true));
    partner.getLocation()
        .getAddress()
        .setCountry(Locale.FRANCE);
    partner.setRegisteredName("Gnx");
    partner.setLegalStructure("SAS");
    partner.setShareCapital("10");

    assertNotNull(listOfPartyCoded);
    assertEquals(entities.length, listOfPartyCoded.size());
    InvoicePartyCodedType sellerParty = listOfPartyCoded.get(2);
    InvoiceXcblHelper.addPartnerInformation(sellerParty, partner);
    assertEquals("Gnx", sellerParty.getPartyTaxInformation()
        .getRegisteredName()
        .getValue());
    assertEquals(4, sellerParty.getListOfIdentifier()
        .getIdentifier()
        .size());
    assertTrue(memoryAppender.searchLevel(Level.ERROR.name()).isPresent());
  }

  @Test
  public void testPopulateInvoiceParty() {
    Invoice invoice = getInvoice();
    String[] entities = { "Sender", "Buyer", "Seller", "RemitTo", "BillTo", "ShipTo", "ShipFrom", };
    List<InvoicePartyCodedType> listOfPartyCoded = InvoiceXcblHelper.getListOfPartyCoded(invoice, entities);
    Partner partner = new Partner();
    partner.setLocation(new Location("DEFAULT", new com.byzaneo.location.bean.Address(), true));
    partner.getLocation()
        .getAddress()
        .setCountry(Locale.FRANCE);
    InvoiceXcblHelper.populateInvoiceParty(invoice, listOfPartyCoded, partner);
    InvoicePartyType invoiceParty = invoice.getInvoiceHeader()
        .getInvoiceParty();
    assertNotNull(invoiceParty.getBuyerParty());
    assertNotNull(invoiceParty.getSellerParty());
    assertNotNull(invoiceParty.getRemitToParty());
    assertNotNull(invoiceParty.getBillToParty());
    assertNotNull(invoiceParty.getShipFromParty());
    assertNotNull(invoiceParty.getShipToParty());
    assertNotEquals(0, invoiceParty.getListOfPartyCoded()
        .getPartyCoded()
        .size());
  }

  @Test
  public void testInvoiceDates() {
    Date date = new Date(1500000000000L);
    Invoice invoice = getInvoice();
    DateQualifierType dateQualifierType = new DateQualifierType();
    dateQualifierType.setDateQualifierCoded("Submission_date");
    InvoiceDateCodedType dateCodedType = new InvoiceDateCodedType();
    dateCodedType.setDateQualifier(dateQualifierType);
    dateCodedType.setDate(toComplexDateType(dateCodedType.getDate(), new Date()));
    ListOfInvoiceDateCodedType listOfDateCodedType = new ListOfInvoiceDateCodedType();
    listOfDateCodedType.getDateCoded()
        .add(dateCodedType);
    invoice.getInvoiceHeader()
        .getInvoiceDates()
        .setListOfOtherInvoiceDates(null);

    InvoiceXcblHelper.populateInvoiceDates(invoice, listOfDateCodedType.getDateCoded());
    InvoiceDateCodedType dateCoded = InvoiceXcblHelper.addOtherInvoiceDate(invoice, "DateCoded", date);
    InvoiceXcblHelper.removeOtherInvoiceDate(invoice, "Submission_date");
    assertNull(invoice.getInvoiceHeader()
        .getInvoiceDates()
        .getInvoicingPeriod()
        .getStartDate()
        .getValue());
    assertNull(invoice.getInvoiceHeader()
        .getInvoiceDates()
        .getInvoicingPeriod()
        .getEndDate()
        .getValue());
    ListOfInvoiceDateCodedType listOfOtherInvoiceDates = invoice.getInvoiceHeader()
        .getInvoiceDates()
        .getListOfOtherInvoiceDates();
    assertNotNull(listOfOtherInvoiceDates);
    assertNotEquals(Optional.empty(), InvoiceXcblHelper.getOtherInvoiceDate(invoice, "PreparationDateTimeOfDocument"));
    assertEquals(date, getDateFromInvoiceDateCoded(dateCoded));
    assertTrue(listOfOtherInvoiceDates.getDateCoded()
        .contains(dateCoded));
    listOfOtherInvoiceDates.getDateCoded()
        .forEach(d -> assertNotEquals("Submission_date", d.getDateQualifier()
            .getDateQualifierCoded()));
  }

  @Test
  public void testListOfNameValue() {
    InvoiceHeaderType invoiceHeaderType = new InvoiceHeaderType();
    Invoice invoice = new Invoice();
    InvoiceXcblHelper.initListOfNameValueSet(invoiceHeaderType);
    invoice.setInvoiceHeader(invoiceHeaderType);
    assertNotNull(invoice.getInvoiceHeader()
        .getListOfNameValueSet());
    List<NameValueSetType> nameValueSet = invoice.getInvoiceHeader()
        .getListOfNameValueSet()
        .getNameValueSet();
    assertEquals(1, nameValueSet.size());
    assertEquals(4, nameValueSet.get(0)
        .getListOfNameValuePair()
        .getNameValuePair()
        .size());
    assertNotEquals(Optional.empty(), InvoiceXcblHelper.getNameValuePair(invoice, "Format In"));
    assertNotEquals(Optional.empty(), InvoiceXcblHelper.getNameValuePair(invoice, "Format Out"));
    assertNotEquals(Optional.empty(), InvoiceXcblHelper.getNameValuePair(invoice, "Nature"));
    assertNotEquals(Optional.empty(), InvoiceXcblHelper.getNameValuePair(invoice, "Sens"));
    assertEquals(Optional.empty(), InvoiceXcblHelper.getNameValuePair(invoice, "Pair"));
  }

  @Test
  public void testAddIdentifierType() {
    Invoice invoice = getInvoice();
    String[] entities = { "Sender", "Buyer", "Seller", "RemitTo" };
    List<InvoicePartyCodedType> listOfPartyCoded = InvoiceXcblHelper.getListOfPartyCoded(invoice, entities);
    assertNull(InvoiceXcblHelper.addIdentifierType(listOfPartyCoded, "", "Buyer", "Other"));
    assertEquals(2, listOfPartyCoded.get(1)
        .getListOfIdentifier()
        .getIdentifier()
        .size());
    assertNull(InvoiceXcblHelper.addIdentifierType(listOfPartyCoded, "Buyers", "Buyers", "Other"));
    assertEquals(2, listOfPartyCoded.get(1)
        .getListOfIdentifier()
        .getIdentifier()
        .size());
    assertNotNull(InvoiceXcblHelper.addIdentifierType(listOfPartyCoded, "Buyer", "Buyer", "New"));
    assertEquals(3, listOfPartyCoded.get(1)
        .getListOfIdentifier()
        .getIdentifier()
        .size());
  }

  @Test
  public void testCheckStartDateBeforeEndDate() {
    Date startDate = new Date(1500000000000L);
    Date endDate = new Date(1600000000000L);
    assertTrue(InvoiceXcblHelper.checkStartDateBeforeEndDate(null, endDate));
    assertTrue(InvoiceXcblHelper.checkStartDateBeforeEndDate(startDate, null));
    assertTrue(InvoiceXcblHelper.checkStartDateBeforeEndDate(startDate, endDate));
    assertTrue(InvoiceXcblHelper.checkStartDateBeforeEndDate(startDate, startDate));
    assertFalse(InvoiceXcblHelper.checkStartDateBeforeEndDate(endDate, startDate));
  }

  @Test
  public void testStructuredNote() {
    Invoice invoice = getInvoice();
    InvoiceXcblHelper.addStructuredNote(invoice.getInvoiceHeader(), "GoodsRelatedInvoice", TextTypeCodeType.OTHER, "GeneralInformation");
    InvoiceXcblHelper.addStructuredNote(invoice.getInvoiceHeader(), "APPLICATION DU TARIF J", TextTypeCodeType.OTHER, "SupplierRemarks");
    ListOfStructuredNoteType listOfStructuredNoteType = invoice.getInvoiceHeader()
        .getListOfStructuredNote();
    assertEquals(2, listOfStructuredNoteType.getStructuredNote()
        .size());
    InvoiceXcblHelper.addStructuredNote(invoice.getInvoiceHeader(), "APPLICATION DU TARIF K", TextTypeCodeType.OTHER, "SupplierRemarks");
    assertEquals(2, listOfStructuredNoteType.getStructuredNote()
        .size());
    listOfStructuredNoteType.getStructuredNote()
        .forEach(n -> assertNotEquals("APPLICATION DU TARIF J", n.getGeneralNote()));
    InvoiceXcblHelper.removeStructuredNote(invoice.getInvoiceHeader(), "SupplierRemarks");
    assertEquals(1, listOfStructuredNoteType.getStructuredNote()
        .size());
    invoice.getInvoiceHeader()
        .getListOfStructuredNote()
        .getStructuredNote()
        .forEach(n -> assertNotEquals("APPLICATION DU TARIF K", n.getGeneralNote()));
  }

  @Test
  public void testCreateInvoiceMedium() {
    Invoice invoice = getInvoice();
    InvoiceXcblHelper.createInvoiceMedium(invoice.getInvoiceHeader(), InvoiceMediumCodeType.OTHER, "Portal");
    assertNotNull(invoice.getInvoiceHeader()
        .getInvoiceMedium());
    assertEquals(InvoiceMediumCodeType.OTHER, invoice.getInvoiceHeader()
        .getInvoiceMedium()
        .getInvoiceMediumCoded());
    assertEquals("Portal", invoice.getInvoiceHeader()
        .getInvoiceMedium()
        .getInvoiceMediumCodedOther());
  }

  @Test
  public void testInitOrderReference() {
    List<InvoiceReferencesType> invRef = new ArrayList<>();
    InvoiceXcblHelper.initOrderReference(invRef);
    assertEquals(1, invRef.size());
    assertNotNull(invRef.get(0)
        .getPurchaseOrderReference());
    assertNotNull(invRef.get(0)
        .getASNNumber());
  }

  @Test
  public void testInitRelatedInvoiceRef() {
    List<InvoiceReferencesType> invRef = new ArrayList<>();
    InvoiceXcblHelper.initRelatedInvoiceRef(invRef);
    assertEquals(1, invRef.size());
    ListOfRelatedInvoiceRefType listOfRelatedInvoiceRef = invRef.get(0)
        .getListOfRelatedInvoiceRef();
    assertNotNull(listOfRelatedInvoiceRef);
    assertEquals(InvoiceTypeCodeType.COMMERCIAL_INVOICE, listOfRelatedInvoiceRef.getRelatedInvoiceRef()
        .get(0)
        .getRelatedInvoiceType()
        .getInvoiceTypeCoded());
  }

  @Test
  public void testPayment() {
    Invoice invoice = getInvoice();
    InvoiceXcblHelper.createPaymentConditions(invoice.getInvoiceHeader());
    List<InvoicePaymentTermType> paymentTerms = invoice.getInvoiceHeader()
        .getInvoicePaymentInstructions()
        .getPaymentTerms()
        .getPaymentTerm();
    assertEquals(3, paymentTerms.size());
    assertEquals(3, InvoiceXcblHelper.getPaymentTerm(invoice)
        .count());
    paymentTerms.forEach(p -> assertTrue(p.getPaymentTermCoded()
        .equals(DISCOUNT_PAYMENT) ||
        p.getPaymentTermCoded()
            .equals(LATE_PAYMENT) ||
        p.getPaymentTermCoded()
            .equals(PENALTY_TERMS)));

    InvoiceXcblHelper.addActualPayment(invoice, BigDecimal.valueOf(1000));
    InvoiceXcblHelper.addActualPayment(invoice, BigDecimal.valueOf(2000));
    ListOfInvoiceActualPaymentType listOfActualPayment = invoice.getInvoiceSummary()
        .getListOfActualPayment();
    assertNotNull(listOfActualPayment);
    assertEquals(2, listOfActualPayment.getActualPayment()
        .size());
    Optional<InvoiceActualPaymentType> firstActualPayment = InvoiceXcblHelper.getFirstActualPayment(invoice);
    assertNotEquals(Optional.empty(), firstActualPayment);
    assertEquals(BigDecimal.valueOf(1000), getMonetaryAmountFromInvoiceActualPaymentType(firstActualPayment.get()));

    InvoiceXcblHelper.setActualPayment(invoice, BigDecimal.valueOf(3000));
    listOfActualPayment = invoice.getInvoiceSummary()
        .getListOfActualPayment();
    assertNotNull(listOfActualPayment);
    assertEquals(1, listOfActualPayment.getActualPayment()
        .size());
    firstActualPayment = InvoiceXcblHelper.getFirstActualPayment(invoice);
    assertNotEquals(Optional.empty(), firstActualPayment);
    assertEquals(BigDecimal.valueOf(3000), getMonetaryAmountFromInvoiceActualPaymentType(firstActualPayment.get()));
  }

  @Test
  public void testGetAllowOrChargeHeader() {
    Invoice invoice = getInvoice();
    Stream<InvoiceAllowOrChargeType> headers = InvoiceXcblHelper.getAllowOrChargeHeader(invoice);
    List<InvoiceAllowOrChargeType> allowOrCharges = InvoiceXcblHelper.getAllowOrCharges(invoice);
    assertEquals(headers.count(), allowOrCharges.size());
  }

  @Test
  public void testPrepareInvoiceForSaving() {
    Invoice invoice = getInvoice();
    Partner partner = new Partner();
    partner.setLocation(new Location("DEFAULT", new com.byzaneo.location.bean.Address(), true));
    partner.getLocation()
        .getAddress()
        .setCountry(Locale.FRANCE);
    String[] entities = { "Sender", "Buyer", "Seller", "RemitTo" };
    List<InvoicePartyCodedType> listOfPartyCoded = InvoiceXcblHelper.getListOfPartyCoded(invoice, entities);
    InvoiceItemDetailType item = new InvoiceItemDetailType();
    item.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    item.getInvoiceBaseItemDetail()
        .setLineItemNum(new LineItemNumType());
    item.getInvoiceBaseItemDetail()
        .getLineItemNum()
        .setBuyerLineItemNum(1);

    List<InvoiceItemDetailType> details = asList(item);
    invoice = InvoiceXcblHelper.prepareInvoiceForSaving(invoice, details, listOfPartyCoded, null, null, true, partner, null);
    InvoicePartyType invoiceParty = invoice.getInvoiceHeader()
        .getInvoiceParty();

    assertEquals(details.size(), invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .size());
    assertNull(invoice.getInvoiceSummary()
        .getInvoiceTotals()
        .getTaxableValue());
    assertNull(invoice.getInvoiceSummary()
        .getInvoiceTotals()
        .getTotalTaxAmount());
    assertNotNull(invoiceParty.getBuyerParty());
    assertNotNull(invoiceParty.getSellerParty());
    assertNotNull(invoiceParty.getRemitToParty());
    assertNotNull(invoiceParty.getBillToParty());
    assertNotEquals(0, invoiceParty.getListOfPartyCoded()
        .getPartyCoded()
        .size());
  }

  @Test
  public void testGetDetails() {
    Invoice invoice1 = getInvoice();
    Invoice invoice2 = new Invoice();
    invoice2.setInvoiceDetail(new InvoiceDetailType());
    List<InvoiceItemDetailType> details = InvoiceXcblHelper.getDetails(invoice1);
    assertEquals(2, details.size());
    assertEquals(0, InvoiceXcblHelper.getDetails(invoice2)
        .size());
    InvoiceXcblHelper.setDetails(invoice2, details);
    assertEquals(2, InvoiceXcblHelper.getDetails(invoice2)
        .size());
  }

  @Test
  public void testIban() {
    String iban = "*******************";
    Invoice invoice = getInvoice();
    InvoiceXcblHelper.addIbanAndBic(iban, null, invoice);
    List<PaymentMethodType> paymentMethods = invoice.getInvoiceHeader()
        .getInvoicePaymentInstructions()
        .getPaymentMethod();
    assertEquals(1, paymentMethods.size());
    assertNotNull(paymentMethods.get(0)
        .getFITransfer()
        .getToFITransfer()
        .getAccountDetail());
    assertEquals("RelatedDetailAccount", paymentMethods.get(0)
        .getPaymentMeanCoded());
  }

  @Test
  public void testBic() {
    String bic = "ABCDEFGH";
    Invoice invoice = getInvoice();
    InvoiceXcblHelper.addIbanAndBic(null, bic, invoice);
    List<PaymentMethodType> paymentMethods = invoice.getInvoiceHeader()
        .getInvoicePaymentInstructions()
        .getPaymentMethod();
    FinancialInstitutionType financialInstitution = paymentMethods.get(0)
        .getFITransfer()
        .getToFITransfer()
        .getFinancialInstitution();

    assertEquals(1, paymentMethods.size());
    assertNotNull(financialInstitution);
    assertEquals(bic, financialInstitution.getFinancialInstitutionName());

    String newBic = "HGFEDCBA";
    InvoiceXcblHelper.addIbanAndBic(null, newBic, invoice);
    financialInstitution = paymentMethods.get(0)
        .getFITransfer()
        .getToFITransfer()
        .getFinancialInstitution();

    assertEquals(1, paymentMethods.size());
    assertNotNull(financialInstitution);
    assertEquals(newBic, financialInstitution.getFinancialInstitutionName());
  }

  @Test
  public void testGetOrderBuyerPartID() {
    ItemDetailType detail = new ItemDetailType();
    assertEquals("MAF_Id", getOrderBuyerPartID(order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()
        .get(0)));
    assertEquals("", getOrderBuyerPartID(detail));
  }

  @Test
  public void testGetASNBuyerPartID() {
    final AdvanceShipmentNotice desadv = initASNWithOrder(order);
    ASNItemDetailType detail = new ASNItemDetailType();
    assertEquals("MAF_Id", getASNBuyerPartID(desadv.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)));
    assertEquals("", getASNBuyerPartID(detail));
  }

  @Test
  public void testGetOrderLineItemNumber() {
    ItemDetailType detail = new ItemDetailType();
    assertEquals(10, getOrderLineItemNumber(order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()
        .get(0)));
    assertEquals(-1, getOrderLineItemNumber(detail));
  }

  @Test
  public void testGetASNLineItemNumber() {
    final AdvanceShipmentNotice desadv = initASNWithOrder(order);
    ASNItemDetailType detail = new ASNItemDetailType();
    assertEquals(10, getASNLineItemNumber(desadv.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)));
    assertEquals(-1, getASNLineItemNumber(detail));
  }

  @Test
  public void testPaymentInstructionsTypeConverterToInvoicePaymentInstructionsType() {
    Invoice invoice = getInvoiceWithPaymentInstructions();
    assertEquals("Test term description", getPaymentTermDescriptionFromInvoice(invoice));
    assertEquals(new Date(1500000000000L), invoice.getInvoiceHeader()
        .getInvoicePaymentInstructions()
        .getPaymentTerms()
        .getNetDueDate()
        .getValue());
    assertEquals("Payment note term", invoice.getInvoiceHeader()
        .getInvoicePaymentInstructions()
        .getPaymentTerms()
        .getPaymentTermsNote()
        .getValue());
  }

  @Test
  public void testFreeText_shouldCreateFreeTextIfListOfNameValueSetIsNULL() {
    // actual
    InvoiceHeaderType invoiceHeader = new InvoiceHeaderType();

    // operations
    addFreeText(invoiceHeader, "FreeText01");

    // expected
    ListOfNameValueSetType expectedListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType nameValueSetFreeText = createNameValueSetForTest("FreeText");
    NameValuePairType nameValuePairFreeText = createNameValuePairForTest("FreeText01", null);
    nameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(nameValuePairFreeText);
    expectedListOfNameValueSet.getNameValueSet()
        .add(nameValueSetFreeText);

    // assert
    assertEquals(expectedListOfNameValueSet, invoiceHeader.getListOfNameValueSet());
  }

  @Test
  public void testFreeText_shouldCreateFreeTextNameValueSetIfNoneIsPresent() {
    // actual
    InvoiceHeaderType invoiceHeader = new InvoiceHeaderType();
    ListOfNameValueSetType actualListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType actualNameValueSetFreeText = createNameValueSetForTest("RandomSet");
    NameValuePairType actualNameValuePairFreeText = createNameValuePairForTest("RandomPair", null);
    actualNameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(actualNameValuePairFreeText);
    actualListOfNameValueSet.getNameValueSet()
        .add(actualNameValueSetFreeText);
    invoiceHeader.setListOfNameValueSet(actualListOfNameValueSet);

    // operations
    addFreeText(invoiceHeader, "FreeText01");

    // expected
    ListOfNameValueSetType expectedListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType nameValueSetFreeText = createNameValueSetForTest("FreeText");
    NameValuePairType nameValuePairFreeText = createNameValuePairForTest("FreeText01", null);
    nameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(nameValuePairFreeText);
    // expected contains both FreeText and RandomSet which already existed.
    expectedListOfNameValueSet.getNameValueSet()
        .add(actualNameValueSetFreeText);
    expectedListOfNameValueSet.getNameValueSet()
        .add(nameValueSetFreeText);

    // assert
    assertEquals(expectedListOfNameValueSet, invoiceHeader.getListOfNameValueSet());
  }

  @Test
  public void testFreeText_shouldNotOverrideExistingFreeTextNameValuePairs() {
    // actual
    InvoiceHeaderType invoiceHeader = new InvoiceHeaderType();
    ListOfNameValueSetType actualListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType actualNameValueSetFreeText = createNameValueSetForTest("FreeText");
    NameValuePairType actualNameValuePairFreeText = createNameValuePairForTest("FreeText01", "somevalue");
    actualNameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(actualNameValuePairFreeText);
    actualListOfNameValueSet.getNameValueSet()
        .add(actualNameValueSetFreeText);
    invoiceHeader.setListOfNameValueSet(actualListOfNameValueSet);

    // operations
    addFreeText(invoiceHeader, "FreeText01");

    // expected
    ListOfNameValueSetType expectedListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType nameValueSetFreeText = createNameValueSetForTest("FreeText");
    NameValuePairType nameValuePairFreeText = createNameValuePairForTest("FreeText01", "somevalue");
    nameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(nameValuePairFreeText);
    expectedListOfNameValueSet.getNameValueSet()
        .add(nameValueSetFreeText);

    // assert
    assertEquals(expectedListOfNameValueSet, invoiceHeader.getListOfNameValueSet());
  }

  @Test
  public void testFreeText_shouldAddFreeTextAndNotOverrideExistingFreeTextNameValuePair() {
    // actual
    InvoiceHeaderType invoiceHeader = new InvoiceHeaderType();
    ListOfNameValueSetType actualListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType actualNameValueSetFreeText = createNameValueSetForTest("FreeText");
    NameValuePairType actualNameValuePairFreeText = createNameValuePairForTest("FreeText01", "somevalue");
    actualNameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(actualNameValuePairFreeText);
    actualListOfNameValueSet.getNameValueSet()
        .add(actualNameValueSetFreeText);
    invoiceHeader.setListOfNameValueSet(actualListOfNameValueSet);

    // operations
    addFreeText(invoiceHeader, "FreeText02");

    // expected
    ListOfNameValueSetType expectedListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType nameValueSetFreeText = createNameValueSetForTest("FreeText");
    NameValuePairType nameValuePairFreeText1 = createNameValuePairForTest("FreeText01", "somevalue");
    NameValuePairType nameValuePairFreeText2 = createNameValuePairForTest("FreeText02", null);
    nameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(nameValuePairFreeText1);
    nameValueSetFreeText.getListOfNameValuePair()
        .getNameValuePair()
        .add(nameValuePairFreeText2);
    expectedListOfNameValueSet.getNameValueSet()
        .add(nameValueSetFreeText);

    // assert
    assertEquals(expectedListOfNameValueSet, invoiceHeader.getListOfNameValueSet());
  }

  @Test
  public void testFreeText_shouldNotContainValueInCreatedNameValuePair() {
    NameValuePairType actual = addFreeText(new InvoiceHeaderType(), "FreeText01");
    NameValuePairType expected = new NameValuePairType();
    expected.setName("FreeText01");
    expected.setValue(null);
    assertEquals(expected, actual);
  }

  @Test
  public void testFreeText_shouldReturnCreatedFreeTextNameValuePairIfListOfNameValueSetIsPresent() {
    InvoiceHeaderType invoiceHeader = new InvoiceHeaderType();
    invoiceHeader.setListOfNameValueSet(new ListOfNameValueSetType());
    NameValuePairType actual = addFreeText(new InvoiceHeaderType(), "FreeText01");
    assertNotNull(actual);
  }

  @Test
  public void testFreeText_shouldReturnPopulatedFreeTextIfFreeTextNameValueSetIsAlreadyPresent() {
    // init
    InvoiceHeaderType invoiceHeader = new InvoiceHeaderType();
    ListOfNameValueSetType actualListOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType actualNameValueSetFreeText = createNameValueSetForTest("FreeText");
    actualListOfNameValueSet.getNameValueSet()
        .add(actualNameValueSetFreeText);
    invoiceHeader.setListOfNameValueSet(actualListOfNameValueSet);

    // actual
    NameValuePairType actual = addFreeText(invoiceHeader, "FreeText01");

    // expected
    NameValuePairType expected = new NameValuePairType();
    expected.setName("FreeText01");
    expected.setValue(null);

    assertEquals(expected, actual);
  }

  private NameValuePairType createNameValuePairForTest(String name, String value) {
    NameValuePairType nameValuePair = new NameValuePairType();
    nameValuePair.setName(name);
    nameValuePair.setValue(value);
    return nameValuePair;
  }

  private NameValueSetType createNameValueSetForTest(String name) {
    NameValueSetType nameValueSetType = new NameValueSetType();
    nameValueSetType.setSetName(name);
    nameValueSetType.setListOfNameValuePair(new ListOfNameValuePairType());
    return nameValueSetType;
  }

  private Invoice getInvoice() {
    AdvanceShipmentNotice desadv = initASNWithOrder(order);
    return InvoiceXcblHelper.initInvoiceWithASN(desadv, order, new OrderResponse());
  }

  private Invoice getInvoiceWithPaymentInstructions() {
    AdvanceShipmentNotice desadv = initASNWithOrder(order);
    PaymentInstructionsType instr = new PaymentInstructionsType();
    PaymentTermsType terms = new PaymentTermsType();
    terms.setNetDueDate(new Date(1500000000000L));
    terms.setPaymentTermsNote("Payment note term");
    PaymentTermType term = new PaymentTermType();
    term.setPaymentTermDescription("Test term description");
    term.setPaymentTermCoded("Code");
    terms.getPaymentTerm()
        .add(term);
    instr.setPaymentTerms(terms);
    desadv.getASNHeader()
        .setASNPaymentInstructions(instr);
    Invoice invoice = InvoiceXcblHelper.initInvoiceWithASN(desadv, order, new OrderResponse());
    Cloner cloner = new Cloner();
    // in the method "createInvoiceHeaderFromAsn" which is called from "initInvoiceWithASN" it does the below clone, but after that it will
    // set another invoicePaymentInstruction inside "initInvoiceWithASN" method
    invoice.getInvoiceHeader()
        .setInvoicePaymentInstructions(convertToInvoicePaymentInstructionsType(cloner.deepClone(desadv.getASNHeader()
            .getASNPaymentInstructions())));
    return invoice;
  }

  public static <T, V> Function<V, T> create(
      Supplier<? extends T> constructor, BiConsumer<? super T, ? super V> setter) {
    return v -> {
      T t = constructor.get();
      setter.accept(t, v);
      return t;
    };
  }

  private List<PartyCodedType> createListOfPartyCodedType(AdvanceShipmentNotice desadv) {
    List<PartyCodedType> listOfPartyCodedType = new ArrayList<>();

    PartyCodedType partyCodedType1 = new PartyCodedType();
    IdentifierType identifier1 = new IdentifierType();
    identifier1.setIdent("ident1");
    partyCodedType1.setPartyID(identifier1);
    listOfPartyCodedType.add(partyCodedType1);

    PartyCodedType partyCodedType2 = new PartyCodedType();
    IdentifierType identifier2 = new IdentifierType();
    identifier2.setIdent("ident2");
    partyCodedType1.setPartyID(identifier2);
    listOfPartyCodedType.add(partyCodedType2);

    return listOfPartyCodedType;
  }

}
