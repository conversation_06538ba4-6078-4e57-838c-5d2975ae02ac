<Invoice xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd"
	xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/financial/v1_0/financial.xsd">
	<InvoiceHeader>
		<InvoiceNumber>Invoice/InvoiceHeader/InvoiceNumber</InvoiceNumber>
		<InvoiceIssueDate>2003-01-01T00:00:01</InvoiceIssueDate>
		<TaxPointDate>2003-01-01T00:00:02</TaxPointDate>
		<InvoiceNumber> duplicated</InvoiceNumber>
		<InvoiceReferences>
			<PurchaseOrderReference>
				<core:BuyerOrderNumber>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:BuyerOrderNumber
				</core:BuyerOrderNumber>
				<core:SellerOrderNumber>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:SellerOrderNumber
				</core:SellerOrderNumber>
				<core:AccountCode>
					<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:AccountCode/core:RefNum
					</core:RefNum>
					<core:RefDate>2003-01-01T00:00:03</core:RefDate>
				</core:AccountCode>
				<core:PurchaseOrderDate>2003-01-01T00:00:04</core:PurchaseOrderDate>
				<core:PurchaseOrderLineItemNumber>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:PurchaseOrderLineItemNumber
				</core:PurchaseOrderLineItemNumber>
				<core:PartialOrderCoded>CompleteOrder</core:PartialOrderCoded>
				<core:PartialOrderCodedOther>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:PartialOrderCodedOther
				</core:PartialOrderCodedOther>
				<core:OrderType>
					<core:OrderTypeCoded>AcknowledgementMessage</core:OrderTypeCoded>
					<core:OrderTypeCodedOther>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:OrderType/core:OrderTypeCodedOther
					</core:OrderTypeCodedOther>
				</core:OrderType>
				<core:ReleaseNumber>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:ReleaseNumber
				</core:ReleaseNumber>
				<core:ChangeOrderSequenceNumber>Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/core:ChangeOrderSequenceNumber
				</core:ChangeOrderSequenceNumber>
			</PurchaseOrderReference>
			<ContractReference>
				<core:ContractID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:ContractID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:ContractID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:ContractID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:ContractID/core:Ident
					</core:Ident>
				</core:ContractID>
				<core:TypeOfContract>
					<core:ContractTypeCoded>DiagnosisRelatedGroup
					</core:ContractTypeCoded>
					<core:ContractTypeCodedOther>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:TypeOfContract/core:ContractTypeCodedOther
					</core:ContractTypeCodedOther>
				</core:TypeOfContract>
				<core:ValidityDates>
					<core:StartDate>2003-01-01T00:00:05</core:StartDate>
					<core:EndDate>2003-01-01T00:00:06</core:EndDate>
				</core:ValidityDates>
				<core:SystemID>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:SystemID
				</core:SystemID>
				<core:ContractItemNumber>Invoice/InvoiceHeader/InvoiceReferences/ContractReference/core:ContractItemNumber
				</core:ContractItemNumber>
			</ContractReference>
			<AccountNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/AccountNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:07</core:RefDate>
			</AccountNumber>
			<ProformaInvoiceNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/ProformaInvoiceNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:08</core:RefDate>
			</ProformaInvoiceNumber>
			<ASNNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/ASNNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:09</core:RefDate>
			</ASNNumber>
			<PriceListNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/PriceListNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:10</core:RefDate>
			</PriceListNumber>
			<PriceListVersionNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/PriceListVersionNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:11</core:RefDate>
			</PriceListVersionNumber>
			<BuyersCatalogNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/BuyersCatalogNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:12</core:RefDate>
			</BuyersCatalogNumber>
			<BillOfLadingNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/BillOfLadingNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:13</core:RefDate>
			</BillOfLadingNumber>
			<AirWayBillNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/AirWayBillNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:14</core:RefDate>
			</AirWayBillNumber>
			<LetterOfCreditNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/LetterOfCreditNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:15</core:RefDate>
			</LetterOfCreditNumber>
			<AuthorizationReference>
				<AuthorizationNumber>
					<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationNumber/core:RefNum
					</core:RefNum>
					<core:RefDate>2003-01-01T00:00:16</core:RefDate>
				</AuthorizationNumber>
				<AuthorizationAssignedBy>
					<core:PartyID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Ident
						</core:Ident>
					</core:PartyID>
					<core:ListOfIdentifier>
						<core:Identifier>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Ident
							</core:Ident>
						</core:Identifier>
					</core:ListOfIdentifier>
					<core:MDFBusiness>false</core:MDFBusiness>
					<core:NameAddress>
						<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:ExternalAddressID
						</core:ExternalAddressID>
						<core:Name1>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Name1
						</core:Name1>
						<core:Name2>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Name2
						</core:Name2>
						<core:Name3>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Name3
						</core:Name3>
						<core:Identifier>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Ident
							</core:Ident>
						</core:Identifier>
						<core:POBox
							POBoxPostalCode="Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:POBox
						</core:POBox>
						<core:Street>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Street
						</core:Street>
						<core:HouseNumber>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:HouseNumber
						</core:HouseNumber>
						<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:StreetSupplement1
						</core:StreetSupplement1>
						<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:StreetSupplement2
						</core:StreetSupplement2>
						<core:Building>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Building
						</core:Building>
						<core:Floor>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Floor
						</core:Floor>
						<core:RoomNumber>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:RoomNumber
						</core:RoomNumber>
						<core:InhouseMail>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:InhouseMail
						</core:InhouseMail>
						<core:Department>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Department
						</core:Department>
						<core:PostalCode>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:PostalCode
						</core:PostalCode>
						<core:City>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:City
						</core:City>
						<core:County>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:County
						</core:County>
						<core:Region>
							<core:RegionCoded>ARA</core:RegionCoded>
							<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Region/core:RegionCodedOther
							</core:RegionCodedOther>
						</core:Region>
						<core:District>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:District
						</core:District>
						<core:Country>
							<core:CountryCoded>AE</core:CountryCoded>
							<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Country/core:CountryCodedOther
							</core:CountryCodedOther>
						</core:Country>
						<core:Timezone>
							<core:TimezoneCoded>12.00</core:TimezoneCoded>
							<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Timezone/core:TimezoneCodedOther
							</core:TimezoneCodedOther>
						</core:Timezone>
					</core:NameAddress>
					<core:PrimaryContact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:PrimaryContact>
					<core:OtherContacts>
						<core:Contact>
							<core:ContactID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Ident
								</core:Ident>
							</core:ContactID>
							<core:ContactName>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactName
							</core:ContactName>
							<core:ContactFunction>
								<core:ContactFunctionCoded>AcceptingContact
								</core:ContactFunctionCoded>
								<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
								</core:ContactFunctionCodedOther>
							</core:ContactFunction>
							<core:ContactDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactDescription
							</core:ContactDescription>
							<core:ListOfContactNumber>
								<core:ContactNumber>
									<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
									</core:ContactNumberValue>
									<core:ContactNumberTypeCoded>TelephoneNumber
									</core:ContactNumberTypeCoded>
									<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
									</core:ContactNumberTypeCodedOther>
								</core:ContactNumber>
							</core:ListOfContactNumber>
							<core:CorrespondenceLanguage>
								<core:LanguageCoded>aa</core:LanguageCoded>
								<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
								</core:LanguageCodedOther>
								<core:LocaleCoded>ae</core:LocaleCoded>
								<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
								</core:LocaleCodedOther>
							</core:CorrespondenceLanguage>
						</core:Contact>
					</core:OtherContacts>
					<core:PartyTaxInformation>
						<core:TaxIdentifier>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
							</core:Ident>
						</core:TaxIdentifier>
						<core:RegisteredName>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:RegisteredName
						</core:RegisteredName>
						<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:RegisteredOffice
						</core:RegisteredOffice>
						<core:TaxLocation>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:TaxLocation>
						<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:CompanyRegistrationNumber
						</core:CompanyRegistrationNumber>
					</core:PartyTaxInformation>
				</AuthorizationAssignedBy>
			</AuthorizationReference>
			<DeliveryNoteNumber>
				<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/DeliveryNoteNumber/core:RefNum
				</core:RefNum>
				<core:RefDate>2003-01-01T00:00:17</core:RefDate>
			</DeliveryNoteNumber>
			<CostAllocation>
				<CostAllocationNumber>
					<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/CostAllocation/CostAllocationNumber/core:RefNum
					</core:RefNum>
					<core:RefDate>2003-01-01T00:00:18</core:RefDate>
				</CostAllocationNumber>
				<WorkBreakdownStructure>Invoice/InvoiceHeader/InvoiceReferences/CostAllocation/WorkBreakdownStructure
				</WorkBreakdownStructure>
				<FixedAsset>Invoice/InvoiceHeader/InvoiceReferences/CostAllocation/FixedAsset
				</FixedAsset>
			</CostAllocation>
			<ListOfRelatedInvoiceRef>
				<RelatedInvoiceRef>
					<RelatedInvoiceType>
						<InvoiceTypeCoded>MeteredServicesInvoice</InvoiceTypeCoded>
						<InvoiceTypeCodedOther>Invoice/InvoiceHeader/InvoiceReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/RelatedInvoiceType/InvoiceTypeCodedOther
						</InvoiceTypeCodedOther>
					</RelatedInvoiceType>
					<InvoiceNumber>
						<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/InvoiceNumber/core:RefNum
						</core:RefNum>
						<core:RefDate>2003-01-01T00:00:19</core:RefDate>
					</InvoiceNumber>
					<InvoiceLineItemNumber>Invoice/InvoiceHeader/InvoiceReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/InvoiceLineItemNumber
					</InvoiceLineItemNumber>
				</RelatedInvoiceRef>
			</ListOfRelatedInvoiceRef>
			<OtherInvoiceReferences>
				<core:ReferenceCoded>
					<core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber
					</core:ReferenceTypeCoded>
					<core:ReferenceTypeCodedOther>Invoice/InvoiceHeader/InvoiceReferences/OtherInvoiceReferences/core:ReferenceCoded/core:ReferenceTypeCodedOther
					</core:ReferenceTypeCodedOther>
					<core:PrimaryReference>
						<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/OtherInvoiceReferences/core:ReferenceCoded/core:PrimaryReference/core:RefNum
						</core:RefNum>
						<core:RefDate>2003-01-01T00:00:20</core:RefDate>
					</core:PrimaryReference>
					<core:SupportingReference>
						<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/OtherInvoiceReferences/core:ReferenceCoded/core:SupportingReference/core:RefNum
						</core:RefNum>
						<core:RefDate>2003-01-01T00:00:21</core:RefDate>
					</core:SupportingReference>
					<core:SupportingSubReference>
						<core:RefNum>Invoice/InvoiceHeader/InvoiceReferences/OtherInvoiceReferences/core:ReferenceCoded/core:SupportingSubReference/core:RefNum
						</core:RefNum>
						<core:RefDate>2003-01-01T00:00:22</core:RefDate>
					</core:SupportingSubReference>
					<core:ReferenceDescription>Invoice/InvoiceHeader/InvoiceReferences/OtherInvoiceReferences/core:ReferenceCoded/core:ReferenceDescription
					</core:ReferenceDescription>
				</core:ReferenceCoded>
			</OtherInvoiceReferences>
		</InvoiceReferences>
		<InvoicePurpose>
			<InvoicePurposeCoded>Original</InvoicePurposeCoded>
			<InvoicePurposeCodedOther>Invoice/InvoiceHeader/InvoicePurpose/InvoicePurposeCodedOther
			</InvoicePurposeCodedOther>
		</InvoicePurpose>
		<InvoiceType>
			<InvoiceTypeCoded>MeteredServicesInvoice</InvoiceTypeCoded>
			<InvoiceTypeCodedOther>Invoice/InvoiceHeader/InvoiceType/InvoiceTypeCodedOther
			</InvoiceTypeCodedOther>
		</InvoiceType>
		<InvoiceCurrency>
			<core:CurrencyCoded>AFA</core:CurrencyCoded>
			<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoiceCurrency/core:CurrencyCodedOther
			</core:CurrencyCodedOther>
		</InvoiceCurrency>
		<PaymentCurrency>
			<core:CurrencyCoded>AFA</core:CurrencyCoded>
			<core:CurrencyCodedOther>Invoice/InvoiceHeader/PaymentCurrency/core:CurrencyCodedOther
			</core:CurrencyCodedOther>
		</PaymentCurrency>
		<ListOfRateOfExchangeDetail>
			<core:RateOfExchangeDetail>
				<core:ReferenceCurrency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceHeader/ListOfRateOfExchangeDetail/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:ReferenceCurrency>
				<core:TargetCurrency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceHeader/ListOfRateOfExchangeDetail/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:TargetCurrency>
				<core:RateOfExchange>1001</core:RateOfExchange>
				<core:InverseRateOfExchange>1002</core:InverseRateOfExchange>
				<core:IndicativeIndicator>true</core:IndicativeIndicator>
				<core:DateOfRateOfExchange>2003-01-01T00:00:23
				</core:DateOfRateOfExchange>
				<core:ListOfRateOfExchangeReference>
					<core:Reference>
						<core:RefNum>Invoice/InvoiceHeader/ListOfRateOfExchangeDetail/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
						</core:RefNum>
						<core:RefDate>2003-01-01T00:00:24</core:RefDate>
					</core:Reference>
				</core:ListOfRateOfExchangeReference>
			</core:RateOfExchangeDetail>
		</ListOfRateOfExchangeDetail>
		<TaxAccountingCurrency>
			<core:CurrencyCoded>AFA</core:CurrencyCoded>
			<core:CurrencyCodedOther>Invoice/InvoiceHeader/TaxAccountingCurrency/core:CurrencyCodedOther
			</core:CurrencyCodedOther>
		</TaxAccountingCurrency>
		<InvoiceLanguage>
			<core:LanguageCoded>aa</core:LanguageCoded>
			<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceLanguage/core:LanguageCodedOther
			</core:LanguageCodedOther>
			<core:LocaleCoded>ae</core:LocaleCoded>
			<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceLanguage/core:LocaleCodedOther
			</core:LocaleCodedOther>
		</InvoiceLanguage>
		<TaxReference>
			<core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
			<core:TaxTypeCodedOther>
				<core:Agency>
					<core:AgencyCoded>AAMVA</core:AgencyCoded>
					<core:AgencyCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
					</core:AgencyCodedOther>
					<core:AgencyDescription>Invoice/InvoiceHeader/TaxReference/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
					</core:AgencyDescription>
					<core:CodeListIdentifierCoded>AcceptanceSiteCode
					</core:CodeListIdentifierCoded>
					<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
					</core:CodeListIdentifierCodedOther>
				</core:Agency>
				<core:Ident>Invoice/InvoiceHeader/TaxReference/core:TaxTypeCodedOther/core:Ident
				</core:Ident>
			</core:TaxTypeCodedOther>
			<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
			</core:TaxFunctionQualifierCoded>
			<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxFunctionQualifierCodedOther
			</core:TaxFunctionQualifierCodedOther>
			<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
			<core:TaxCategoryCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxCategoryCodedOther
			</core:TaxCategoryCodedOther>
			<core:ReasonTaxExemptCoded>Exempt-ForExport
			</core:ReasonTaxExemptCoded>
			<core:ReasonTaxExemptCodedOther>Invoice/InvoiceHeader/TaxReference/core:ReasonTaxExemptCodedOther
			</core:ReasonTaxExemptCodedOther>
			<core:TaxPercent UnitOfMeasurement="P1">1003
			</core:TaxPercent>
			<core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
			<core:TaxPaymentMethodCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxPaymentMethodCodedOther
			</core:TaxPaymentMethodCodedOther>
			<core:TaxTreatmentCoded>NoTaxApplies</core:TaxTreatmentCoded>
			<core:TaxTreatmentCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxTreatmentCodedOther
			</core:TaxTreatmentCodedOther>
			<core:TaxAmount>1004</core:TaxAmount>
			<core:TaxAmountInTaxAccountingCurrency>1005
			</core:TaxAmountInTaxAccountingCurrency>
			<core:TaxLocation>
				<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
				</core:LocationQualifierCoded>
				<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:LocationQualifierCodedOther
				</core:LocationQualifierCodedOther>
				<core:LocationIdentifier>
					<core:LocID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
						</core:Ident>
					</core:LocID>
					<core:LocationDescription>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
					</core:LocationDescription>
				</core:LocationIdentifier>
				<core:GPSCoordinates>
					<core:GPSSystem>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
					</core:GPSSystem>
					<core:Latitude>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:GPSCoordinates/core:Latitude
					</core:Latitude>
					<core:Longitude>Invoice/InvoiceHeader/TaxReference/core:TaxLocation/core:GPSCoordinates/core:Longitude
					</core:Longitude>
				</core:GPSCoordinates>
			</core:TaxLocation>
		</TaxReference>
		<InvoiceMedium>
			<InvoiceMediumCoded>ByMail</InvoiceMediumCoded>
			<InvoiceMediumCodedOther>Invoice/InvoiceHeader/InvoiceMedium/InvoiceMediumCodedOther
			</InvoiceMediumCodedOther>
		</InvoiceMedium>
		<InvoiceDates>
			<InvoiceDueDate>2003-01-01T00:00:25</InvoiceDueDate>
			<ExpectedShipDate>2003-01-01T00:00:26</ExpectedShipDate>
			<ActualShipDate>2003-01-01T00:00:27</ActualShipDate>
			<ExpectedDeliveryDate>2003-01-01T00:00:28</ExpectedDeliveryDate>
			<ActualDeliveryDate>2003-01-01T00:00:29</ActualDeliveryDate>
			<ReceiptDate>2003-01-01T00:00:30</ReceiptDate>
			<TaxPeriod>
				<core:StartDate>2003-01-01T00:00:31</core:StartDate>
				<core:EndDate>2003-01-01T00:00:32</core:EndDate>
			</TaxPeriod>
			<InvoicingPeriod>
				<core:StartDate>2003-01-01T00:00:33</core:StartDate>
				<core:EndDate>2003-01-01T00:00:34</core:EndDate>
			</InvoicingPeriod>
			<ListOfOtherInvoiceDates>
				<core:DateCoded>
					<core:Date>2003-01-01T00:00:35</core:Date>
					<core:DateQualifier>
						<core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
						<core:DateQualifierCodedOther>Invoice/InvoiceHeader/InvoiceDates/ListOfOtherInvoiceDates/core:DateCoded/core:DateQualifier/core:DateQualifierCodedOther
						</core:DateQualifierCodedOther>
					</core:DateQualifier>
				</core:DateCoded>
			</ListOfOtherInvoiceDates>
		</InvoiceDates>
		<!-- //InvoiceHeader/InvoiceDates/ListOfOtherInvoiceDates/DateCoded[DateQualifierCoded 
			='PreparationDateTimeOfDocument']/Date -->
		<InvoiceParty>
			<BuyerParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>false</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/BuyerParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</BuyerParty>
			<BuyerTax>
				<core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
				<core:TaxTypeCodedOther>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxTypeCodedOther/core:Ident
					</core:Ident>
				</core:TaxTypeCodedOther>
				<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
				</core:TaxFunctionQualifierCoded>
				<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxFunctionQualifierCodedOther
				</core:TaxFunctionQualifierCodedOther>
				<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
				<core:TaxCategoryCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxCategoryCodedOther
				</core:TaxCategoryCodedOther>
				<core:ReasonTaxExemptCoded>Exempt-ForExport
				</core:ReasonTaxExemptCoded>
				<core:ReasonTaxExemptCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:ReasonTaxExemptCodedOther
				</core:ReasonTaxExemptCodedOther>
				<core:TaxPercent UnitOfMeasurement="P1">1006
				</core:TaxPercent>
				<core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
				<core:TaxPaymentMethodCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxPaymentMethodCodedOther
				</core:TaxPaymentMethodCodedOther>
				<core:TaxableAmount>1007</core:TaxableAmount>
				<core:TaxableAmountInTaxAccountingCurrency>1008
				</core:TaxableAmountInTaxAccountingCurrency>
				<core:TaxAmount>1009</core:TaxAmount>
				<core:TaxAmountInTaxAccountingCurrency>1010
				</core:TaxAmountInTaxAccountingCurrency>
				<core:TaxLocation>
					<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
					</core:LocationQualifierCoded>
					<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:LocationQualifierCodedOther
					</core:LocationQualifierCodedOther>
					<core:LocationIdentifier>
						<core:LocID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
							</core:Ident>
						</core:LocID>
						<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
						</core:LocationDescription>
					</core:LocationIdentifier>
					<core:GPSCoordinates>
						<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
						</core:GPSSystem>
						<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:GPSCoordinates/core:Latitude
						</core:Latitude>
						<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/BuyerTax/core:TaxLocation/core:GPSCoordinates/core:Longitude
						</core:Longitude>
					</core:GPSCoordinates>
				</core:TaxLocation>
			</BuyerTax>
			<SellerParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>true</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/SellerParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</SellerParty>
			<ShipToParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>false</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/ShipToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</ShipToParty>
			<BillToParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>FR-INSEE</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>true</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>FR</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/BillToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</BillToParty>
			<RemitToParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>false</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/RemitToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</RemitToParty>
			<RemitToTax>
				<core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
				<core:TaxTypeCodedOther>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxTypeCodedOther/core:Ident
					</core:Ident>
				</core:TaxTypeCodedOther>
				<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
				</core:TaxFunctionQualifierCoded>
				<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxFunctionQualifierCodedOther
				</core:TaxFunctionQualifierCodedOther>
				<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
				<core:TaxCategoryCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxCategoryCodedOther
				</core:TaxCategoryCodedOther>
				<core:ReasonTaxExemptCoded>Exempt-ForExport
				</core:ReasonTaxExemptCoded>
				<core:ReasonTaxExemptCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:ReasonTaxExemptCodedOther
				</core:ReasonTaxExemptCodedOther>
				<core:TaxPercent UnitOfMeasurement="P1">1011
				</core:TaxPercent>
				<core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
				<core:TaxPaymentMethodCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxPaymentMethodCodedOther
				</core:TaxPaymentMethodCodedOther>
				<core:TaxableAmount>1012</core:TaxableAmount>
				<core:TaxableAmountInTaxAccountingCurrency>1013
				</core:TaxableAmountInTaxAccountingCurrency>
				<core:TaxAmount>1014</core:TaxAmount>
				<core:TaxAmountInTaxAccountingCurrency>1015
				</core:TaxAmountInTaxAccountingCurrency>
				<core:TaxLocation>
					<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
					</core:LocationQualifierCoded>
					<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:LocationQualifierCodedOther
					</core:LocationQualifierCodedOther>
					<core:LocationIdentifier>
						<core:LocID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
							</core:Ident>
						</core:LocID>
						<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
						</core:LocationDescription>
					</core:LocationIdentifier>
					<core:GPSCoordinates>
						<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
						</core:GPSSystem>
						<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:GPSCoordinates/core:Latitude
						</core:Latitude>
						<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/RemitToTax/core:TaxLocation/core:GPSCoordinates/core:Longitude
						</core:Longitude>
					</core:GPSCoordinates>
				</core:TaxLocation>
			</RemitToTax>
			<ShipFromParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>true</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/ShipFromParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</ShipFromParty>
			<SoldToParty>
				<core:PartyID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyID/core:Ident
					</core:Ident>
				</core:PartyID>
				<core:ListOfIdentifier>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:ListOfIdentifier/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
				</core:ListOfIdentifier>
				<core:MDFBusiness>false</core:MDFBusiness>
				<core:NameAddress>
					<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:ExternalAddressID
					</core:ExternalAddressID>
					<core:Name1>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Name1
					</core:Name1>
					<core:Name2>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Name2
					</core:Name2>
					<core:Name3>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Name3
					</core:Name3>
					<core:Identifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Identifier/core:Ident
						</core:Ident>
					</core:Identifier>
					<core:POBox
						POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:POBox
					</core:POBox>
					<core:Street>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Street
					</core:Street>
					<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:HouseNumber
					</core:HouseNumber>
					<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:StreetSupplement1
					</core:StreetSupplement1>
					<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:StreetSupplement2
					</core:StreetSupplement2>
					<core:Building>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Building
					</core:Building>
					<core:Floor>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Floor
					</core:Floor>
					<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:RoomNumber
					</core:RoomNumber>
					<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:InhouseMail
					</core:InhouseMail>
					<core:Department>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Department
					</core:Department>
					<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:PostalCode
					</core:PostalCode>
					<core:City>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:City
					</core:City>
					<core:County>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:County
					</core:County>
					<core:Region>
						<core:RegionCoded>ARA</core:RegionCoded>
						<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Region/core:RegionCodedOther
						</core:RegionCodedOther>
					</core:Region>
					<core:District>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:District
					</core:District>
					<core:Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</core:Country>
					<core:Timezone>
						<core:TimezoneCoded>12.00</core:TimezoneCoded>
						<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
						</core:TimezoneCodedOther>
					</core:Timezone>
				</core:NameAddress>
				<core:PrimaryContact>
					<core:ContactID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactID/core:Ident
						</core:Ident>
					</core:ContactID>
					<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactName
					</core:ContactName>
					<core:ContactFunction>
						<core:ContactFunctionCoded>AcceptingContact
						</core:ContactFunctionCoded>
						<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
						</core:ContactFunctionCodedOther>
					</core:ContactFunction>
					<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ContactDescription
					</core:ContactDescription>
					<core:ListOfContactNumber>
						<core:ContactNumber>
							<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
							</core:ContactNumberValue>
							<core:ContactNumberTypeCoded>TelephoneNumber
							</core:ContactNumberTypeCoded>
							<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
							</core:ContactNumberTypeCodedOther>
						</core:ContactNumber>
					</core:ListOfContactNumber>
					<core:CorrespondenceLanguage>
						<core:LanguageCoded>aa</core:LanguageCoded>
						<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
						</core:LanguageCodedOther>
						<core:LocaleCoded>ae</core:LocaleCoded>
						<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
						</core:LocaleCodedOther>
					</core:CorrespondenceLanguage>
				</core:PrimaryContact>
				<core:OtherContacts>
					<core:Contact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:Contact>
				</core:OtherContacts>
				<core:PartyTaxInformation>
					<core:TaxIdentifier>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
						</core:Ident>
					</core:TaxIdentifier>
					<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:RegisteredName
					</core:RegisteredName>
					<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:RegisteredOffice
					</core:RegisteredOffice>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
					<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/SoldToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
					</core:CompanyRegistrationNumber>
				</core:PartyTaxInformation>
			</SoldToParty>
			<ListOfPartyCoded>
				<core:PartyCoded>
					<core:PartyID>
						<core:Agency>
							<core:AgencyCoded>EAN</core:AgencyCoded>
							<core:CodeListIdentifierCoded>LocationCode
							</core:CodeListIdentifierCoded>
						</core:Agency>
						<core:Ident>3014531200102</core:Ident>
					</core:PartyID>
					<core:PartyRoleCoded>HeadOffice</core:PartyRoleCoded>
					<core:NameAddress>
						<core:Name1>DISTRIBUTOUT</core:Name1>
						<core:PostalCode>75009</core:PostalCode>
						<core:City>PARIS</core:City>
						<core:Country>
							<core:CountryCoded>FR</core:CountryCoded>
						</core:Country>
					</core:NameAddress>
				</core:PartyCoded>
				<core:PartyCoded>
					<core:PartyID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Ident
						</core:Ident>
					</core:PartyID>
					<core:ListOfIdentifier>
						<core:Identifier>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Ident
							</core:Ident>
						</core:Identifier>
					</core:ListOfIdentifier>
					<core:MDFBusiness>true</core:MDFBusiness>
					<core:NameAddress>
						<core:ExternalAddressID>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:ExternalAddressID
						</core:ExternalAddressID>
						<core:Name1>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name1
						</core:Name1>
						<core:Name2>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name2
						</core:Name2>
						<core:Name3>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name3
						</core:Name3>
						<core:Identifier>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Ident
							</core:Ident>
						</core:Identifier>
						<core:POBox
							POBoxPostalCode="Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox
						</core:POBox>
						<core:Street>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Street
						</core:Street>
						<core:HouseNumber>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:HouseNumber
						</core:HouseNumber>
						<core:StreetSupplement1>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement1
						</core:StreetSupplement1>
						<core:StreetSupplement2>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement2
						</core:StreetSupplement2>
						<core:Building>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Building
						</core:Building>
						<core:Floor>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Floor
						</core:Floor>
						<core:RoomNumber>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:RoomNumber
						</core:RoomNumber>
						<core:InhouseMail>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:InhouseMail
						</core:InhouseMail>
						<core:Department>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Department
						</core:Department>
						<core:PostalCode>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:PostalCode
						</core:PostalCode>
						<core:City>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:City
						</core:City>
						<core:County>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:County
						</core:County>
						<core:Region>
							<core:RegionCoded>ARA</core:RegionCoded>
							<core:RegionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Region/core:RegionCodedOther
							</core:RegionCodedOther>
						</core:Region>
						<core:District>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:District
						</core:District>
						<core:Country>
							<core:CountryCoded>AE</core:CountryCoded>
							<core:CountryCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Country/core:CountryCodedOther
							</core:CountryCodedOther>
						</core:Country>
						<core:Timezone>
							<core:TimezoneCoded>12.00</core:TimezoneCoded>
							<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Timezone/core:TimezoneCodedOther
							</core:TimezoneCodedOther>
						</core:Timezone>
					</core:NameAddress>
					<core:PrimaryContact>
						<core:ContactID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Ident
							</core:Ident>
						</core:ContactID>
						<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactName
						</core:ContactName>
						<core:ContactFunction>
							<core:ContactFunctionCoded>AcceptingContact
							</core:ContactFunctionCoded>
							<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
							</core:ContactFunctionCodedOther>
						</core:ContactFunction>
						<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactDescription
						</core:ContactDescription>
						<core:ListOfContactNumber>
							<core:ContactNumber>
								<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
								</core:ContactNumberValue>
								<core:ContactNumberTypeCoded>TelephoneNumber
								</core:ContactNumberTypeCoded>
								<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
								</core:ContactNumberTypeCodedOther>
							</core:ContactNumber>
						</core:ListOfContactNumber>
						<core:CorrespondenceLanguage>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:CorrespondenceLanguage>
					</core:PrimaryContact>
					<core:OtherContacts>
						<core:Contact>
							<core:ContactID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Ident
								</core:Ident>
							</core:ContactID>
							<core:ContactName>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactName
							</core:ContactName>
							<core:ContactFunction>
								<core:ContactFunctionCoded>AcceptingContact
								</core:ContactFunctionCoded>
								<core:ContactFunctionCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
								</core:ContactFunctionCodedOther>
							</core:ContactFunction>
							<core:ContactDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactDescription
							</core:ContactDescription>
							<core:ListOfContactNumber>
								<core:ContactNumber>
									<core:ContactNumberValue>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
									</core:ContactNumberValue>
									<core:ContactNumberTypeCoded>TelephoneNumber
									</core:ContactNumberTypeCoded>
									<core:ContactNumberTypeCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
									</core:ContactNumberTypeCodedOther>
								</core:ContactNumber>
							</core:ListOfContactNumber>
							<core:CorrespondenceLanguage>
								<core:LanguageCoded>aa</core:LanguageCoded>
								<core:LanguageCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
								</core:LanguageCodedOther>
								<core:LocaleCoded>ae</core:LocaleCoded>
								<core:LocaleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
								</core:LocaleCodedOther>
							</core:CorrespondenceLanguage>
						</core:Contact>
					</core:OtherContacts>
					<core:PartyTaxInformation>
						<core:TaxIdentifier>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
							</core:Ident>
						</core:TaxIdentifier>
						<core:RegisteredName>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredName
						</core:RegisteredName>
						<core:RegisteredOffice>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredOffice
						</core:RegisteredOffice>
						<core:TaxLocation>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:TaxLocation>
						<core:CompanyRegistrationNumber>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:CompanyRegistrationNumber
						</core:CompanyRegistrationNumber>
					</core:PartyTaxInformation>
					<core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
					<core:PartyRoleCodedOther>Invoice/InvoiceHeader/InvoiceParty/ListOfPartyCoded/core:PartyCoded/core:PartyRoleCodedOther
					</core:PartyRoleCodedOther>
				</core:PartyCoded>
			</ListOfPartyCoded>
		</InvoiceParty>
		<ListOfTransportRouting>
			<core:TransportRouting>
				<core:TransportRouteID>1016</core:TransportRouteID>
				<core:TransportMode>
					<core:TransportModeCoded>Air</core:TransportModeCoded>
					<core:TransportModeCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMode/core:TransportModeCodedOther
					</core:TransportModeCodedOther>
				</core:TransportMode>
				<core:TransportMeans>
					<core:TransportMeansCoded>20FtILContainer-ClosedTop
					</core:TransportMeansCoded>
					<core:TransportMeansCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeans/core:TransportMeansCodedOther
					</core:TransportMeansCodedOther>
				</core:TransportMeans>
				<core:TransportMeansIdentifier>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansIdentifier/core:Ident
					</core:Ident>
				</core:TransportMeansIdentifier>
				<core:TransportMeansReference>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansReference/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansReference/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansReference/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportMeansReference/core:Ident
					</core:Ident>
				</core:TransportMeansReference>
				<core:TransportRequirementCoded>SpecifiedFlagRequired
				</core:TransportRequirementCoded>
				<core:TransportRequirementCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportRequirementCodedOther
				</core:TransportRequirementCodedOther>
				<core:CarrierName>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:CarrierName
				</core:CarrierName>
				<core:CarrierID>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:CarrierID/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:CarrierID/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:CarrierID/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:CarrierID/core:Ident
					</core:Ident>
				</core:CarrierID>
				<core:TransportQuantities>
					<core:LadingQuantity>
						<core:QuantityValue SignificanceCoded="Approximately"
							SignificanceCodedOther="Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1017</core:QuantityValue>
						<core:UnitOfMeasurement>
							<core:UOMCoded>1</core:UOMCoded>
							<core:UOMCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:UnitOfMeasurement/core:UOMCodedOther
							</core:UOMCodedOther>
						</core:UnitOfMeasurement>
					</core:LadingQuantity>
					<core:ListOfQuantityCoded>
						<core:QuantityCoded>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1018</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
							<core:QuantityQualifierCoded>AcceptableUnserviceableQuantity
							</core:QuantityQualifierCoded>
							<core:QuantityQualifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther
							</core:QuantityQualifierCodedOther>
						</core:QuantityCoded>
					</core:ListOfQuantityCoded>
				</core:TransportQuantities>
				<core:CustShippingContractNum>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:CustShippingContractNum
				</core:CustShippingContractNum>
				<core:ServiceLevel>
					<core:ServiceLevelCoded>BulkCommodityTrain</core:ServiceLevelCoded>
					<core:ServiceLevelCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ServiceLevel/core:ServiceLevelCodedOther
					</core:ServiceLevelCodedOther>
					<core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder
					</core:ServiceLevelReasonCoded>
					<core:ServiceLevelReasonCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ServiceLevel/core:ServiceLevelReasonCodedOther
					</core:ServiceLevelReasonCodedOther>
					<core:ServiceLevelResponsibilityCoded>CustomerPlant
					</core:ServiceLevelResponsibilityCoded>
					<core:ServiceLevelResponsibilityCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ServiceLevel/core:ServiceLevelResponsibilityCodedOther
					</core:ServiceLevelResponsibilityCodedOther>
				</core:ServiceLevel>
				<core:ShippingInstructions>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ShippingInstructions
				</core:ShippingInstructions>
				<core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
				<core:TransportLegCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLegCodedOther
				</core:TransportLegCodedOther>
				<core:ListOfTransportEquipment>
					<core:TransportEquipment>
						<core:EquipmentProviderCoded>ShipperSupplied
						</core:EquipmentProviderCoded>
						<core:EquipmentProviderCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentProviderCodedOther
						</core:EquipmentProviderCodedOther>
						<core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased
						</core:EquipmentOwnerCoded>
						<core:EquipmentOwnerCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentOwnerCodedOther
						</core:EquipmentOwnerCodedOther>
						<core:EquipmentID>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentID
						</core:EquipmentID>
						<core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop
						</core:EquipmentSizeTypeCoded>
						<core:EquipementSizeTypeCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipementSizeTypeCodedOther
						</core:EquipementSizeTypeCodedOther>
						<core:EquipmentStatusCoded>Continental</core:EquipmentStatusCoded>
						<core:EquipmentStatusCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentStatusCodedOther
						</core:EquipmentStatusCodedOther>
						<core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
						<core:FullIndicatorCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:FullIndicatorCodedOther
						</core:FullIndicatorCodedOther>
						<core:Conditions>
							<core:RefrigerationOn>false</core:RefrigerationOn>
							<core:Residue>true</core:Residue>
							<core:ListOfConditions>
								<core:Dimension>
									<core:Measurement>
										<core:MeasurementValue
											SignificanceCoded="Approximately"
											SignificanceCodedOther="Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther"
											ConditionsCoded="WhereAirEquals1"
											ConditionsCodedOther="Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1019</core:MeasurementValue>
										<core:UnitOfMeasurement>
											<core:UOMCoded>1</core:UOMCoded>
											<core:UOMCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther
											</core:UOMCodedOther>
										</core:UnitOfMeasurement>
									</core:Measurement>
									<core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
									<core:DimensionCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:DimensionCodedOther
									</core:DimensionCodedOther>
								</core:Dimension>
							</core:ListOfConditions>
						</core:Conditions>
						<core:EquipmentNote>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentNote
						</core:EquipmentNote>
						<core:ListOfSealInfo>
							<core:SealInfo>
								<core:SealNumber>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealNumber
								</core:SealNumber>
								<core:SealIssuer>
									<core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
									<core:SealIssuerCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealIssuer/core:SealIssuerCodedOther
									</core:SealIssuerCodedOther>
								</core:SealIssuer>
								<core:SealStatusDescription>
									<core:SealStatusCoded>Broken</core:SealStatusCoded>
									<core:SealStatusCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusCodedOther
									</core:SealStatusCodedOther>
									<core:SealStatusInfo>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusInfo
									</core:SealStatusInfo>
								</core:SealStatusDescription>
							</core:SealInfo>
						</core:ListOfSealInfo>
						<core:ListOfEquipmentMeasurements>
							<core:Dimension>
								<core:Measurement>
									<core:MeasurementValue SignificanceCoded="Approximately"
										SignificanceCodedOther="Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther"
										ConditionsCoded="WhereAirEquals1"
										ConditionsCodedOther="Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1020</core:MeasurementValue>
									<core:UnitOfMeasurement>
										<core:UOMCoded>1</core:UOMCoded>
										<core:UOMCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther
										</core:UOMCodedOther>
									</core:UnitOfMeasurement>
								</core:Measurement>
								<core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
								<core:DimensionCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:DimensionCodedOther
								</core:DimensionCodedOther>
							</core:Dimension>
						</core:ListOfEquipmentMeasurements>
					</core:TransportEquipment>
				</core:ListOfTransportEquipment>
				<core:TransitDirection>
					<core:TransitDirectionCoded>BuyerToSeller
					</core:TransitDirectionCoded>
					<core:TransitDirectionCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransitDirection/core:TransitDirectionCodedOther
					</core:TransitDirectionCodedOther>
					<core:TransitTimeQualifierCoded>AirHours
					</core:TransitTimeQualifierCoded>
					<core:TransitTimeQualifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransitDirection/core:TransitTimeQualifierCodedOther
					</core:TransitTimeQualifierCodedOther>
					<core:TransitTime>1021</core:TransitTime>
				</core:TransitDirection>
				<core:TransportLocationList>
					<core:StartTransportLocation>
						<core:Location>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:Location>
						<core:LocationID>1022</core:LocationID>
						<core:Sequence>1023</core:Sequence>
						<core:EstimatedArrivalDate>2003-01-01T00:00:36
						</core:EstimatedArrivalDate>
						<core:ActualArrivalDate>2003-01-01T00:00:37
						</core:ActualArrivalDate>
						<core:EstimatedDepartureDate>2003-01-01T00:00:38
						</core:EstimatedDepartureDate>
						<core:ActualDepartureDate>2003-01-01T00:00:39
						</core:ActualDepartureDate>
					</core:StartTransportLocation>
					<core:InterimTransportLocation>
						<core:Location>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:Location>
						<core:LocationID>1024</core:LocationID>
						<core:Sequence>1025</core:Sequence>
						<core:EstimatedArrivalDate>2003-01-01T00:00:40
						</core:EstimatedArrivalDate>
						<core:ActualArrivalDate>2003-01-01T00:00:41
						</core:ActualArrivalDate>
						<core:EstimatedDepartureDate>2003-01-01T00:00:42
						</core:EstimatedDepartureDate>
						<core:ActualDepartureDate>2003-01-01T00:00:43
						</core:ActualDepartureDate>
					</core:InterimTransportLocation>
					<core:EndTransportLocation>
						<core:Location>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceHeader/ListOfTransportRouting/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:Location>
						<core:LocationID>1026</core:LocationID>
						<core:Sequence>1027</core:Sequence>
						<core:EstimatedArrivalDate>2003-01-01T00:00:44
						</core:EstimatedArrivalDate>
						<core:ActualArrivalDate>2003-01-01T00:00:45
						</core:ActualArrivalDate>
						<core:EstimatedDepartureDate>2003-01-01T00:00:46
						</core:EstimatedDepartureDate>
						<core:ActualDepartureDate>2003-01-01T00:00:47
						</core:ActualDepartureDate>
					</core:EndTransportLocation>
				</core:TransportLocationList>
			</core:TransportRouting>
		</ListOfTransportRouting>
		<InvoiceTermsOfDelivery>
			<core:TermsOfDeliveryFunctionCoded>PriceCondition
			</core:TermsOfDeliveryFunctionCoded>
			<core:TermsOfDeliveryFunctionCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:TermsOfDeliveryFunctionCodedOther
			</core:TermsOfDeliveryFunctionCodedOther>
			<core:TransportTermsCoded>Ex-Works</core:TransportTermsCoded>
			<core:TransportTermsCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:TransportTermsCodedOther
			</core:TransportTermsCodedOther>
			<core:ShipmentMethodOfPaymentCoded>Account
			</core:ShipmentMethodOfPaymentCoded>
			<core:ShipmentMethodOfPaymentCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:ShipmentMethodOfPaymentCodedOther
			</core:ShipmentMethodOfPaymentCodedOther>
			<core:Location>
				<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
				</core:LocationQualifierCoded>
				<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:LocationQualifierCodedOther
				</core:LocationQualifierCodedOther>
				<core:LocationIdentifier>
					<core:LocID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Ident
						</core:Ident>
					</core:LocID>
					<core:LocationDescription>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:LocationIdentifier/core:LocationDescription
					</core:LocationDescription>
				</core:LocationIdentifier>
				<core:GPSCoordinates>
					<core:GPSSystem>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:GPSCoordinates/core:GPSSystem
					</core:GPSSystem>
					<core:Latitude>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:GPSCoordinates/core:Latitude
					</core:Latitude>
					<core:Longitude>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:Location/core:GPSCoordinates/core:Longitude
					</core:Longitude>
				</core:GPSCoordinates>
			</core:Location>
			<core:TermsOfDeliveryDescription>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:TermsOfDeliveryDescription
			</core:TermsOfDeliveryDescription>
			<core:TransportDescription>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:TransportDescription
			</core:TransportDescription>
			<core:RiskOfLossCoded>RiskOfLossInTransitToDestinationIsBuyers
			</core:RiskOfLossCoded>
			<core:RiskOfLossCodedOther>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:RiskOfLossCodedOther
			</core:RiskOfLossCodedOther>
			<core:RiskOfLossDescription>Invoice/InvoiceHeader/InvoiceTermsOfDelivery/core:RiskOfLossDescription
			</core:RiskOfLossDescription>
		</InvoiceTermsOfDelivery>
		<InvoicePaymentInstructions>
			<core:PaymentTerms>
				<core:NetDueDate>2003-01-01T00:00:48</core:NetDueDate>
				<core:NetTimeDue>
					<core:TimeDue>
						<core:MeasurementValue SignificanceCoded="Approximately"
							SignificanceCodedOther="Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:NetTimeDue/core:TimeDue/core:MeasurementValue/@SignificanceCodedOther"
							ConditionsCoded="WhereAirEquals1"
							ConditionsCodedOther="Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:NetTimeDue/core:TimeDue/core:MeasurementValue/@ConditionsCodedOther">1028</core:MeasurementValue>
						<core:UnitOfMeasurement>
							<core:UOMCoded>1</core:UOMCoded>
							<core:UOMCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:NetTimeDue/core:TimeDue/core:UnitOfMeasurement/core:UOMCodedOther
							</core:UOMCodedOther>
						</core:UnitOfMeasurement>
					</core:TimeDue>
					<core:DateTimeRefCoded>AnticipatedDeliveryDate
					</core:DateTimeRefCoded>
					<core:DateTimeRefCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:NetTimeDue/core:DateTimeRefCodedOther
					</core:DateTimeRefCodedOther>
				</core:NetTimeDue>
				<core:PaymentTerm>
					<core:PaymentTermCoded>10DaysAfterEndOfMonth
					</core:PaymentTermCoded>
					<core:PaymentTermCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:PaymentTermCodedOther
					</core:PaymentTermCodedOther>
					<core:PaymentTermValue>1029</core:PaymentTermValue>
					<core:PaymentTermDescription>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:PaymentTermDescription
					</core:PaymentTermDescription>
					<core:DiscountInformation>
						<core:DiscountPercent UnitOfMeasurement="P1">1030
						</core:DiscountPercent>
						<core:DiscountAmount>
							<core:MonetaryAmount>1031</core:MonetaryAmount>
							<core:Currency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountAmount/core:Currency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:Currency>
							<core:RateOfExchangeDetail>
								<core:ReferenceCurrency>
									<core:CurrencyCoded>AFA</core:CurrencyCoded>
									<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountAmount/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
									</core:CurrencyCodedOther>
								</core:ReferenceCurrency>
								<core:TargetCurrency>
									<core:CurrencyCoded>AFA</core:CurrencyCoded>
									<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountAmount/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
									</core:CurrencyCodedOther>
								</core:TargetCurrency>
								<core:RateOfExchange>1032</core:RateOfExchange>
								<core:InverseRateOfExchange>1033</core:InverseRateOfExchange>
								<core:IndicativeIndicator>false</core:IndicativeIndicator>
								<core:DateOfRateOfExchange>2003-01-01T00:00:49
								</core:DateOfRateOfExchange>
								<core:ListOfRateOfExchangeReference>
									<core:Reference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountAmount/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:50</core:RefDate>
									</core:Reference>
								</core:ListOfRateOfExchangeReference>
							</core:RateOfExchangeDetail>
						</core:DiscountAmount>
						<core:DiscountDueDate>2003-01-01T00:00:51</core:DiscountDueDate>
						<core:DiscountDayOfMonth>1034</core:DiscountDayOfMonth>
						<core:DiscountTimeDue>
							<core:TimeDue>
								<core:MeasurementValue SignificanceCoded="Approximately"
									SignificanceCodedOther="Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountTimeDue/core:TimeDue/core:MeasurementValue/@SignificanceCodedOther"
									ConditionsCoded="WhereAirEquals1"
									ConditionsCodedOther="Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountTimeDue/core:TimeDue/core:MeasurementValue/@ConditionsCodedOther">1035</core:MeasurementValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountTimeDue/core:TimeDue/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
							</core:TimeDue>
							<core:DateTimeRefCoded>AnticipatedDeliveryDate
							</core:DateTimeRefCoded>
							<core:DateTimeRefCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:DiscountInformation/core:DiscountTimeDue/core:DateTimeRefCodedOther
							</core:DateTimeRefCodedOther>
						</core:DiscountTimeDue>
					</core:DiscountInformation>
				</core:PaymentTerm>
				<core:PaymentTermsNote>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentTerms/core:PaymentTermsNote
				</core:PaymentTermsNote>
			</core:PaymentTerms>
			<core:PaymentMethod>
				<core:PaymentMeanCoded>1035Exchange</core:PaymentMeanCoded>
				<core:PaymentMeanCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:PaymentMeanCodedOther
				</core:PaymentMeanCodedOther>
				<core:PaymentMeanReference>
					<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:PaymentMeanReference/core:RefNum
					</core:RefNum>
					<core:RefDate>2003-01-01T00:00:52</core:RefDate>
				</core:PaymentMeanReference>
				<core:PaymentSystemCoded>OrdinaryPost</core:PaymentSystemCoded>
				<core:PaymentSystemCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:PaymentSystemCodedOther
				</core:PaymentSystemCodedOther>
				<core:FITransfer>
					<core:FromFITransfer>
						<core:AccountDetail>
							<core:AccountID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountID
							</core:AccountID>
							<core:SecondaryAccountID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:SecondaryAccountID
							</core:SecondaryAccountID>
							<core:IBAN>AA000000001036</core:IBAN>
							<core:AccountControlKey>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountControlKey
							</core:AccountControlKey>
							<core:AccountTypeCoded>NewBusinessAccount</core:AccountTypeCoded>
							<core:AccountTypeCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountTypeCodedOther
							</core:AccountTypeCodedOther>
							<core:AccountName1>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountName1
							</core:AccountName1>
							<core:AccountName2>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountName2
							</core:AccountName2>
							<core:Currency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:Currency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:Currency>
							<core:AccountReferences>
								<core:ReferenceCoded>
									<core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber
									</core:ReferenceTypeCoded>
									<core:ReferenceTypeCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:ReferenceTypeCodedOther
									</core:ReferenceTypeCodedOther>
									<core:PrimaryReference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:PrimaryReference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:53</core:RefDate>
									</core:PrimaryReference>
									<core:SupportingReference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:SupportingReference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:54</core:RefDate>
									</core:SupportingReference>
									<core:SupportingSubReference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:SupportingSubReference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:55</core:RefDate>
									</core:SupportingSubReference>
									<core:ReferenceDescription>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:ReferenceDescription
									</core:ReferenceDescription>
								</core:ReferenceCoded>
							</core:AccountReferences>
						</core:AccountDetail>
						<core:FinancialInstitution>
							<core:FinancialInstitutionID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FinancialInstitutionID
							</core:FinancialInstitutionID>
							<core:FinancialInstitutionName>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FinancialInstitutionName
							</core:FinancialInstitutionName>
							<core:FIBranchID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchID
							</core:FIBranchID>
							<core:FIBranchAddress>
								<core:ExternalAddressID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:ExternalAddressID
								</core:ExternalAddressID>
								<core:Name1>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Name1
								</core:Name1>
								<core:Name2>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Name2
								</core:Name2>
								<core:Name3>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Name3
								</core:Name3>
								<core:Identifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Ident
									</core:Ident>
								</core:Identifier>
								<core:POBox
									POBoxPostalCode="Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox
								</core:POBox>
								<core:Street>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Street
								</core:Street>
								<core:HouseNumber>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:HouseNumber
								</core:HouseNumber>
								<core:StreetSupplement1>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:StreetSupplement1
								</core:StreetSupplement1>
								<core:StreetSupplement2>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:StreetSupplement2
								</core:StreetSupplement2>
								<core:Building>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Building
								</core:Building>
								<core:Floor>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Floor
								</core:Floor>
								<core:RoomNumber>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:RoomNumber
								</core:RoomNumber>
								<core:InhouseMail>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:InhouseMail
								</core:InhouseMail>
								<core:Department>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Department
								</core:Department>
								<core:PostalCode>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:PostalCode
								</core:PostalCode>
								<core:City>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:City
								</core:City>
								<core:County>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:County
								</core:County>
								<core:Region>
									<core:RegionCoded>ARA</core:RegionCoded>
									<core:RegionCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Region/core:RegionCodedOther
									</core:RegionCodedOther>
								</core:Region>
								<core:District>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:District
								</core:District>
								<core:Country>
									<core:CountryCoded>AE</core:CountryCoded>
									<core:CountryCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Country/core:CountryCodedOther
									</core:CountryCodedOther>
								</core:Country>
								<core:Timezone>
									<core:TimezoneCoded>12.00</core:TimezoneCoded>
									<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:FromFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Timezone/core:TimezoneCodedOther
									</core:TimezoneCodedOther>
								</core:Timezone>
							</core:FIBranchAddress>
						</core:FinancialInstitution>
					</core:FromFITransfer>
					<core:ToFITransfer>
						<core:AccountDetail>
							<core:AccountID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountID
							</core:AccountID>
							<core:SecondaryAccountID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:SecondaryAccountID
							</core:SecondaryAccountID>
							<core:IBAN>AA000000001037</core:IBAN>
							<core:AccountControlKey>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountControlKey
							</core:AccountControlKey>
							<core:AccountTypeCoded>NewBusinessAccount</core:AccountTypeCoded>
							<core:AccountTypeCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountTypeCodedOther
							</core:AccountTypeCodedOther>
							<core:AccountName1>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountName1
							</core:AccountName1>
							<core:AccountName2>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountName2
							</core:AccountName2>
							<core:Currency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:Currency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:Currency>
							<core:AccountReferences>
								<core:ReferenceCoded>
									<core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber
									</core:ReferenceTypeCoded>
									<core:ReferenceTypeCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:ReferenceTypeCodedOther
									</core:ReferenceTypeCodedOther>
									<core:PrimaryReference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:PrimaryReference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:56</core:RefDate>
									</core:PrimaryReference>
									<core:SupportingReference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:SupportingReference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:57</core:RefDate>
									</core:SupportingReference>
									<core:SupportingSubReference>
										<core:RefNum>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:SupportingSubReference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:00:58</core:RefDate>
									</core:SupportingSubReference>
									<core:ReferenceDescription>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:AccountDetail/core:AccountReferences/core:ReferenceCoded/core:ReferenceDescription
									</core:ReferenceDescription>
								</core:ReferenceCoded>
							</core:AccountReferences>
						</core:AccountDetail>
						<core:FinancialInstitution>
							<core:FinancialInstitutionID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FinancialInstitutionID
							</core:FinancialInstitutionID>
							<core:FinancialInstitutionName>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FinancialInstitutionName
							</core:FinancialInstitutionName>
							<core:FIBranchID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchID
							</core:FIBranchID>
							<core:FIBranchAddress>
								<core:ExternalAddressID>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:ExternalAddressID
								</core:ExternalAddressID>
								<core:Name1>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Name1
								</core:Name1>
								<core:Name2>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Name2
								</core:Name2>
								<core:Name3>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Name3
								</core:Name3>
								<core:Identifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Identifier/core:Ident
									</core:Ident>
								</core:Identifier>
								<core:POBox
									POBoxPostalCode="Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:POBox
								</core:POBox>
								<core:Street>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Street
								</core:Street>
								<core:HouseNumber>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:HouseNumber
								</core:HouseNumber>
								<core:StreetSupplement1>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:StreetSupplement1
								</core:StreetSupplement1>
								<core:StreetSupplement2>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:StreetSupplement2
								</core:StreetSupplement2>
								<core:Building>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Building
								</core:Building>
								<core:Floor>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Floor
								</core:Floor>
								<core:RoomNumber>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:RoomNumber
								</core:RoomNumber>
								<core:InhouseMail>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:InhouseMail
								</core:InhouseMail>
								<core:Department>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Department
								</core:Department>
								<core:PostalCode>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:PostalCode
								</core:PostalCode>
								<core:City>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:City
								</core:City>
								<core:County>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:County
								</core:County>
								<core:Region>
									<core:RegionCoded>ARA</core:RegionCoded>
									<core:RegionCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Region/core:RegionCodedOther
									</core:RegionCodedOther>
								</core:Region>
								<core:District>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:District
								</core:District>
								<core:Country>
									<core:CountryCoded>AE</core:CountryCoded>
									<core:CountryCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Country/core:CountryCodedOther
									</core:CountryCodedOther>
								</core:Country>
								<core:Timezone>
									<core:TimezoneCoded>12.00</core:TimezoneCoded>
									<core:TimezoneCodedOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:ToFITransfer/core:FinancialInstitution/core:FIBranchAddress/core:Timezone/core:TimezoneCodedOther
									</core:TimezoneCodedOther>
								</core:Timezone>
							</core:FIBranchAddress>
						</core:FinancialInstitution>
					</core:ToFITransfer>
					<core:PaymentRecordOther>Invoice/InvoiceHeader/InvoicePaymentInstructions/core:PaymentMethod/core:FITransfer/core:PaymentRecordOther
					</core:PaymentRecordOther>
				</core:FITransfer>
			</core:PaymentMethod>
		</InvoicePaymentInstructions>
		<InvoiceAllowancesOrCharges>
			<core:AllowOrCharge>
				<core:IndicatorCoded>About</core:IndicatorCoded>
				<core:IndicatorCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:IndicatorCodedOther
				</core:IndicatorCodedOther>
				<core:BasisCoded>None</core:BasisCoded>
				<core:BasisCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:BasisCodedOther
				</core:BasisCodedOther>
				<core:MethodOfHandlingCoded>BillBack</core:MethodOfHandlingCoded>
				<core:MethodOfHandlingCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:MethodOfHandlingCodedOther
				</core:MethodOfHandlingCodedOther>
				<core:AllowOrChargeTreatment>
					<core:AllowOrChargeTreatmentCoded>UnitPriceNet
					</core:AllowOrChargeTreatmentCoded>
					<core:AllowOrChargeTreatmentCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:AllowOrChargeTreatment/core:AllowOrChargeTreatmentCodedOther
					</core:AllowOrChargeTreatmentCodedOther>
				</core:AllowOrChargeTreatment>
				<core:AllowanceOrChargeDescription>
					<core:RefID>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:AllowanceOrChargeDescription/core:RefID
					</core:RefID>
					<core:ListOfDescription>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:AllowanceOrChargeDescription/core:ListOfDescription
					</core:ListOfDescription>
					<core:ServiceCoded>About</core:ServiceCoded>
					<core:ServiceCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:AllowanceOrChargeDescription/core:ServiceCodedOther
					</core:ServiceCodedOther>
				</core:AllowanceOrChargeDescription>
				<core:ValidityDates>
					<core:StartDate>2003-01-01T00:00:59</core:StartDate>
					<core:EndDate>2003-01-01T00:01:00</core:EndDate>
				</core:ValidityDates>
				<core:BasisQuantityRange>
					<core:QuantityValue SignificanceCoded="Approximately"
						SignificanceCodedOther="Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:BasisQuantityRange/core:QuantityValue/@SignificanceCodedOther">1038</core:QuantityValue>
					<core:UnitOfMeasurement>
						<core:UOMCoded>1</core:UOMCoded>
						<core:UOMCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:BasisQuantityRange/core:UnitOfMeasurement/core:UOMCodedOther
						</core:UOMCodedOther>
					</core:UnitOfMeasurement>
				</core:BasisQuantityRange>
				<core:TypeOfAllowanceOrCharge>
					<core:QuantityAllowanceOrCharge>
						<core:Quantity>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1039</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Quantity/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</core:Quantity>
						<core:Rate>
							<core:RatePerUnit>
								<core:UnitPriceValue>1040</core:UnitPriceValue>
								<core:Currency>
									<core:CurrencyCoded>AFA</core:CurrencyCoded>
									<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Rate/core:RatePerUnit/core:Currency/core:CurrencyCodedOther
									</core:CurrencyCodedOther>
								</core:Currency>
							</core:RatePerUnit>
							<core:UnitPriceBasis>1041</core:UnitPriceBasis>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Rate/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</core:Rate>
						<core:QuantityMonetaryValue>
							<core:MonetaryAmount>1042</core:MonetaryAmount>
							<core:Currency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:Currency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:Currency>
							<core:RateOfExchangeDetail>
								<core:ReferenceCurrency>
									<core:CurrencyCoded>AFA</core:CurrencyCoded>
									<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
									</core:CurrencyCodedOther>
								</core:ReferenceCurrency>
								<core:TargetCurrency>
									<core:CurrencyCoded>AFA</core:CurrencyCoded>
									<core:CurrencyCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
									</core:CurrencyCodedOther>
								</core:TargetCurrency>
								<core:RateOfExchange>1043</core:RateOfExchange>
								<core:InverseRateOfExchange>1044</core:InverseRateOfExchange>
								<core:IndicativeIndicator>true</core:IndicativeIndicator>
								<core:DateOfRateOfExchange>2003-01-01T00:01:01
								</core:DateOfRateOfExchange>
								<core:ListOfRateOfExchangeReference>
									<core:Reference>
										<core:RefNum>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
										</core:RefNum>
										<core:RefDate>2003-01-01T00:01:02</core:RefDate>
									</core:Reference>
								</core:ListOfRateOfExchangeReference>
							</core:RateOfExchangeDetail>
						</core:QuantityMonetaryValue>
					</core:QuantityAllowanceOrCharge>
				</core:TypeOfAllowanceOrCharge>
				<core:Tax>
					<core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
					<core:TaxTypeCodedOther>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:Ident>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Ident
						</core:Ident>
					</core:TaxTypeCodedOther>
					<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
					</core:TaxFunctionQualifierCoded>
					<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxFunctionQualifierCodedOther
					</core:TaxFunctionQualifierCodedOther>
					<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
					<core:TaxCategoryCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxCategoryCodedOther
					</core:TaxCategoryCodedOther>
					<core:ReasonTaxExemptCoded>Exempt-ForExport
					</core:ReasonTaxExemptCoded>
					<core:ReasonTaxExemptCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:ReasonTaxExemptCodedOther
					</core:ReasonTaxExemptCodedOther>
					<core:TaxPercent UnitOfMeasurement="P1">1045
					</core:TaxPercent>
					<core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
					<core:TaxPaymentMethodCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxPaymentMethodCodedOther
					</core:TaxPaymentMethodCodedOther>
					<core:TaxableAmount>1046</core:TaxableAmount>
					<core:TaxableAmountInTaxAccountingCurrency>1047
					</core:TaxableAmountInTaxAccountingCurrency>
					<core:TaxAmount>1048</core:TaxAmount>
					<core:TaxAmountInTaxAccountingCurrency>1049
					</core:TaxAmountInTaxAccountingCurrency>
					<core:TaxLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceHeader/InvoiceAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:TaxLocation>
				</core:Tax>
			</core:AllowOrCharge>
		</InvoiceAllowancesOrCharges>
		<CountrySpecificRequirements>
			<NotaFiscalType>Invoice/InvoiceHeader/CountrySpecificRequirements/NotaFiscalType
			</NotaFiscalType>
			<PaymentReferenceNumber>Invoice/InvoiceHeader/CountrySpecificRequirements/PaymentReferenceNumber
			</PaymentReferenceNumber>
			<OtherCountryRequirements>
				<CountryRequirement>
					<Country>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement/Country/core:CountryCodedOther
						</core:CountryCodedOther>
					</Country>
					<ListOfSpecificRequirement>
						<SpecificRequirement>
							<RequirementTypeCoded>AbbreviatedNewDrugApplicationNumber
							</RequirementTypeCoded>
							<RequirementTypeCodedOther>Invoice/InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement/ListOfSpecificRequirement/SpecificRequirement/RequirementTypeCodedOther
							</RequirementTypeCodedOther>
							<RequirementReference>
								<core:RefNum>Invoice/InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement/ListOfSpecificRequirement/SpecificRequirement/RequirementReference/core:RefNum
								</core:RefNum>
								<core:RefDate>2003-01-01T00:01:03</core:RefDate>
							</RequirementReference>
							<RequirementDetails>Invoice/InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement/ListOfSpecificRequirement/SpecificRequirement/RequirementDetails
							</RequirementDetails>
						</SpecificRequirement>
					</ListOfSpecificRequirement>
				</CountryRequirement>
			</OtherCountryRequirements>
		</CountrySpecificRequirements>
		<InvoiceHeaderNote>Invoice/InvoiceHeader/InvoiceHeaderNote
		</InvoiceHeaderNote>
		<ListOfStructuredNote>
			<core:StructuredNote>
				<core:GeneralNote>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:GeneralNote
				</core:GeneralNote>
				<core:NoteID>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:NoteID
				</core:NoteID>
				<core:Agency>
					<core:AgencyCoded>AAMVA</core:AgencyCoded>
					<core:AgencyCodedOther>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:Agency/core:AgencyCodedOther
					</core:AgencyCodedOther>
					<core:AgencyDescription>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:Agency/core:AgencyDescription
					</core:AgencyDescription>
					<core:CodeListIdentifierCoded>AcceptanceSiteCode
					</core:CodeListIdentifierCoded>
					<core:CodeListIdentifierCodedOther>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:Agency/core:CodeListIdentifierCodedOther
					</core:CodeListIdentifierCodedOther>
				</core:Agency>
				<core:NoteURL>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:NoteURL
				</core:NoteURL>
				<core:TextTypeCoded>Deadlines</core:TextTypeCoded>
				<core:TextTypeCodedOther>Invoice/InvoiceHeader/ListOfStructuredNote/core:StructuredNote/core:TextTypeCodedOther
				</core:TextTypeCodedOther>
			</core:StructuredNote>
		</ListOfStructuredNote>
		<ListOfNameValueSet>
			<core:NameValueSet>
				<core:SetName>Invoice/InvoiceHeader/ListOfNameValueSet/core:NameValueSet/core:SetName
				</core:SetName>
				<core:SetID>Invoice/InvoiceHeader/ListOfNameValueSet/core:NameValueSet/core:SetID
				</core:SetID>
				<core:ListOfNameValuePair>
					<core:NameValuePair>
						<core:Name>Invoice/InvoiceHeader/ListOfNameValueSet/core:NameValueSet/core:ListOfNameValuePair/core:NameValuePair/core:Name
						</core:Name>
						<core:Value>Invoice/InvoiceHeader/ListOfNameValueSet/core:NameValueSet/core:ListOfNameValuePair/core:NameValuePair/core:Value
						</core:Value>
						<core:Datatype>Invoice/InvoiceHeader/ListOfNameValueSet/core:NameValueSet/core:ListOfNameValuePair/core:NameValuePair/core:Datatype
						</core:Datatype>
					</core:NameValuePair>
				</core:ListOfNameValuePair>
			</core:NameValueSet>
		</ListOfNameValueSet>
		<ListOfAttachment>
			<core:Attachment>
				<core:AttachmentActionCoded>Add</core:AttachmentActionCoded>
				<core:AttachmentActionCodedOther>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:AttachmentActionCodedOther
				</core:AttachmentActionCodedOther>
				<core:AttachmentPurpose>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:AttachmentPurpose
				</core:AttachmentPurpose>
				<core:FileName>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:FileName
				</core:FileName>
				<core:AttachmentTitle>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:AttachmentTitle
				</core:AttachmentTitle>
				<core:AttachmentDescription>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:AttachmentDescription
				</core:AttachmentDescription>
				<core:Language>
					<core:LanguageCoded>aa</core:LanguageCoded>
					<core:LanguageCodedOther>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:Language/core:LanguageCodedOther
					</core:LanguageCodedOther>
					<core:LocaleCoded>ae</core:LocaleCoded>
					<core:LocaleCodedOther>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:Language/core:LocaleCodedOther
					</core:LocaleCodedOther>
				</core:Language>
				<core:MIMEType>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:MIMEType
				</core:MIMEType>
				<core:AttachmentLocation>Invoice/InvoiceHeader/ListOfAttachment/core:Attachment/core:AttachmentLocation
				</core:AttachmentLocation>
			</core:Attachment>
		</ListOfAttachment>
	</InvoiceHeader>
	<InvoiceDetail>
		<ListOfInvoiceItemDetail>
			<InvoiceItemDetail>
				<InvoiceBaseItemDetail>
					<LineItemNum>
						<core:BuyerLineItemNum>1050</core:BuyerLineItemNum>
						<core:SellerLineItemNum>1051</core:SellerLineItemNum>
					</LineItemNum>
					<LineItemType>
						<core:LineItemTypeCoded>ComponentGroup</core:LineItemTypeCoded>
						<core:LineItemTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemType/core:LineItemTypeCodedOther
						</core:LineItemTypeCodedOther>
					</LineItemType>
					<ParentItemNumber LineItemNumTypeCoded="Seller">1052
					</ParentItemNumber>
					<ItemIdentifiers>
						<core:PartNumbers>
							<core:SellerPartNumber>
								<core:PartID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:PartID
								</core:PartID>
								<core:PartIDExt>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:PartIDExt
								</core:PartIDExt>
								<core:RevisionNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:RevisionNumber
								</core:RevisionNumber>
							</core:SellerPartNumber>
							<core:BuyerPartNumber>
								<core:PartID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:PartID
								</core:PartID>
								<core:PartIDExt>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:PartIDExt
								</core:PartIDExt>
								<core:RevisionNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:RevisionNumber
								</core:RevisionNumber>
							</core:BuyerPartNumber>
							<core:ManufacturerPartNumber>
								<core:PartID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:PartID
								</core:PartID>
								<core:PartIDExt>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:PartIDExt
								</core:PartIDExt>
								<core:RevisionNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:RevisionNumber
								</core:RevisionNumber>
								<core:ManufacturerID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerID/core:Ident
									</core:Ident>
								</core:ManufacturerID>
								<core:ManufacturerName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:ManufacturerPartNumber/core:ManufacturerName
								</core:ManufacturerName>
							</core:ManufacturerPartNumber>
							<core:StandardPartNumber>
								<core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer
								</core:ProductIdentifierQualifierCoded>
								<core:ProductIdentifierQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifierQualifierCodedOther
								</core:ProductIdentifierQualifierCodedOther>
								<core:ProductIdentifier>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifier
								</core:ProductIdentifier>
								<core:ProductIdentifierExt>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifierExt
								</core:ProductIdentifierExt>
							</core:StandardPartNumber>
							<core:SubstitutePartNumbers>
								<core:ProductIdentifierCoded>
									<core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer
									</core:ProductIdentifierQualifierCoded>
									<core:ProductIdentifierQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SubstitutePartNumbers/core:ProductIdentifierCoded/core:ProductIdentifierQualifierCodedOther
									</core:ProductIdentifierQualifierCodedOther>
									<core:ProductIdentifier>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SubstitutePartNumbers/core:ProductIdentifierCoded/core:ProductIdentifier
									</core:ProductIdentifier>
									<core:ProductIdentifierExt>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SubstitutePartNumbers/core:ProductIdentifierCoded/core:ProductIdentifierExt
									</core:ProductIdentifierExt>
								</core:ProductIdentifierCoded>
							</core:SubstitutePartNumbers>
							<core:OtherItemIdentifiers>
								<core:ProductIdentifierCoded>
									<core:ProductIdentifierQualifierCoded>AdditionalProductIdentificationAssignedByTheManufacturer
									</core:ProductIdentifierQualifierCoded>
									<core:ProductIdentifierQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:OtherItemIdentifiers/core:ProductIdentifierCoded/core:ProductIdentifierQualifierCodedOther
									</core:ProductIdentifierQualifierCodedOther>
									<core:ProductIdentifier>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:OtherItemIdentifiers/core:ProductIdentifierCoded/core:ProductIdentifier
									</core:ProductIdentifier>
									<core:ProductIdentifierExt>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:PartNumbers/core:OtherItemIdentifiers/core:ProductIdentifierCoded/core:ProductIdentifierExt
									</core:ProductIdentifierExt>
								</core:ProductIdentifierCoded>
							</core:OtherItemIdentifiers>
						</core:PartNumbers>
						<core:Service>false</core:Service>
						<core:ItemDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ItemDescription
						</core:ItemDescription>
						<core:ListOfItemCharacteristic>
							<core:ItemCharacteristic>
								<core:ItemCharacteristicCoded>AdditionalSectorialCharacteristics
								</core:ItemCharacteristicCoded>
								<core:ItemCharacteristicCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ItemCharacteristicCodedOther
								</core:ItemCharacteristicCodedOther>
								<core:SurfaceLayerPositionCoded>All
								</core:SurfaceLayerPositionCoded>
								<core:SurfaceLayerPositionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:SurfaceLayerPositionCodedOther
								</core:SurfaceLayerPositionCodedOther>
								<core:ItemCharacteristicValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ItemCharacteristicValue
								</core:ItemCharacteristicValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
								<core:ListOfDimension>
									<core:Dimension>
										<core:Measurement>
											<core:MeasurementValue
												SignificanceCoded="Approximately"
												SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther"
												ConditionsCoded="WhereAirEquals1"
												ConditionsCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1053</core:MeasurementValue>
											<core:UnitOfMeasurement>
												<core:UOMCoded>1</core:UOMCoded>
												<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther
												</core:UOMCodedOther>
											</core:UnitOfMeasurement>
										</core:Measurement>
										<core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
										<core:DimensionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:ListOfItemCharacteristic/core:ItemCharacteristic/core:ListOfDimension/core:Dimension/core:DimensionCodedOther
										</core:DimensionCodedOther>
									</core:Dimension>
								</core:ListOfDimension>
							</core:ItemCharacteristic>
						</core:ListOfItemCharacteristic>
						<core:CommodityCode>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:CommodityCode/core:Ident
							</core:Ident>
						</core:CommodityCode>
						<core:Category>
							<core:CategoryID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:Category/core:CategoryID
							</core:CategoryID>
							<core:StandardCategoryID>
								<core:StandardCategoryType>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:Category/core:StandardCategoryID/core:StandardCategoryType
								</core:StandardCategoryType>
								<core:ClassificationID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:Category/core:StandardCategoryID/core:ClassificationID
								</core:ClassificationID>
								<core:TechnicalID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/core:Category/core:StandardCategoryID/core:TechnicalID
								</core:TechnicalID>
							</core:StandardCategoryID>
						</core:Category>
					</ItemIdentifiers>
					<ListOfDimension>
						<core:Dimension>
							<core:Measurement>
								<core:MeasurementValue SignificanceCoded="Approximately"
									SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther"
									ConditionsCoded="WhereAirEquals1"
									ConditionsCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1054</core:MeasurementValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfDimension/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
							</core:Measurement>
							<core:DimensionCoded>ConsolidatedWeight</core:DimensionCoded>
							<core:DimensionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfDimension/core:Dimension/core:DimensionCodedOther
							</core:DimensionCodedOther>
						</core:Dimension>
					</ListOfDimension>
					<InvoicedQuantity>
						<core:QuantityValue SignificanceCoded="Approximately"
							SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/InvoicedQuantity/core:QuantityValue/@SignificanceCodedOther">1055</core:QuantityValue>
						<core:UnitOfMeasurement>
							<core:UOMCoded>1</core:UOMCoded>
							<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/InvoicedQuantity/core:UnitOfMeasurement/core:UOMCodedOther
							</core:UOMCodedOther>
						</core:UnitOfMeasurement>
					</InvoicedQuantity>
					<ExceptionQuantities>
						<OrderedQuantity>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/OrderedQuantity/core:QuantityValue/@SignificanceCodedOther">1056</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/OrderedQuantity/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</OrderedQuantity>
						<DamagedQuantity>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/DamagedQuantity/core:QuantityValue/@SignificanceCodedOther">1057</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/DamagedQuantity/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</DamagedQuantity>
						<UnusableQuantity>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/UnusableQuantity/core:QuantityValue/@SignificanceCodedOther">1058</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/UnusableQuantity/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</UnusableQuantity>
						<ReturnedQuantity>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/ReturnedQuantity/core:QuantityValue/@SignificanceCodedOther">1059</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/ReturnedQuantity/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</ReturnedQuantity>
						<ShippedToDateQuantity>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/ShippedToDateQuantity/core:QuantityValue/@SignificanceCodedOther">1060</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ExceptionQuantities/ShippedToDateQuantity/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
						</ShippedToDateQuantity>
					</ExceptionQuantities>
					<ListOfQuantityCoded>
						<core:QuantityCoded>
							<core:QuantityValue SignificanceCoded="Approximately"
								SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1061</core:QuantityValue>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
							<core:QuantityQualifierCoded>AcceptableUnserviceableQuantity
							</core:QuantityQualifierCoded>
							<core:QuantityQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther
							</core:QuantityQualifierCodedOther>
						</core:QuantityCoded>
					</ListOfQuantityCoded>
					<LineItemReferences>
						<PurchaseOrderReference>
							<core:BuyerOrderNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:BuyerOrderNumber
							</core:BuyerOrderNumber>
							<core:SellerOrderNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:SellerOrderNumber
							</core:SellerOrderNumber>
							<core:AccountCode>
								<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:AccountCode/core:RefNum
								</core:RefNum>
								<core:RefDate>2003-01-01T00:01:04</core:RefDate>
							</core:AccountCode>
							<core:PurchaseOrderDate>2003-01-01T00:01:05
							</core:PurchaseOrderDate>
							<core:PurchaseOrderLineItemNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:PurchaseOrderLineItemNumber
							</core:PurchaseOrderLineItemNumber>
							<core:PartialOrderCoded>CompleteOrder</core:PartialOrderCoded>
							<core:PartialOrderCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:PartialOrderCodedOther
							</core:PartialOrderCodedOther>
							<core:OrderType>
								<core:OrderTypeCoded>AcknowledgementMessage
								</core:OrderTypeCoded>
								<core:OrderTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:OrderType/core:OrderTypeCodedOther
								</core:OrderTypeCodedOther>
							</core:OrderType>
							<core:ReleaseNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:ReleaseNumber
							</core:ReleaseNumber>
							<core:ChangeOrderSequenceNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PurchaseOrderReference/core:ChangeOrderSequenceNumber
							</core:ChangeOrderSequenceNumber>
						</PurchaseOrderReference>
						<ContractReference>
							<core:ContractID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:ContractID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:ContractID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:ContractID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:ContractID/core:Ident
								</core:Ident>
							</core:ContractID>
							<core:TypeOfContract>
								<core:ContractTypeCoded>DiagnosisRelatedGroup
								</core:ContractTypeCoded>
								<core:ContractTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:TypeOfContract/core:ContractTypeCodedOther
								</core:ContractTypeCodedOther>
							</core:TypeOfContract>
							<core:ValidityDates>
								<core:StartDate>2003-01-01T00:01:06</core:StartDate>
								<core:EndDate>2003-01-01T00:01:07</core:EndDate>
							</core:ValidityDates>
							<core:SystemID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:SystemID
							</core:SystemID>
							<core:ContractItemNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ContractReference/core:ContractItemNumber
							</core:ContractItemNumber>
						</ContractReference>
						<AccountNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AccountNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:08</core:RefDate>
						</AccountNumber>
						<ProformaInvoiceNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ProformaInvoiceNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:09</core:RefDate>
						</ProformaInvoiceNumber>
						<ASNNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ASNNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:10</core:RefDate>
						</ASNNumber>
						<PriceListNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PriceListNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:11</core:RefDate>
						</PriceListNumber>
						<PriceListVersionNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/PriceListVersionNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:12</core:RefDate>
						</PriceListVersionNumber>
						<BuyersCatalogNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/BuyersCatalogNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:13</core:RefDate>
						</BuyersCatalogNumber>
						<BillOfLadingNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/BillOfLadingNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:14</core:RefDate>
						</BillOfLadingNumber>
						<AirWayBillNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AirWayBillNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:15</core:RefDate>
						</AirWayBillNumber>
						<LetterOfCreditNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/LetterOfCreditNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:16</core:RefDate>
						</LetterOfCreditNumber>
						<AuthorizationReference>
							<AuthorizationNumber>
								<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationNumber/core:RefNum
								</core:RefNum>
								<core:RefDate>2003-01-01T00:01:17</core:RefDate>
							</AuthorizationNumber>
							<AuthorizationAssignedBy>
								<core:PartyID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyID/core:Ident
									</core:Ident>
								</core:PartyID>
								<core:ListOfIdentifier>
									<core:Identifier>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:ListOfIdentifier/core:Identifier/core:Ident
										</core:Ident>
									</core:Identifier>
								</core:ListOfIdentifier>
								<core:MDFBusiness>true</core:MDFBusiness>
								<core:NameAddress>
									<core:ExternalAddressID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:ExternalAddressID
									</core:ExternalAddressID>
									<core:Name1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Name1
									</core:Name1>
									<core:Name2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Name2
									</core:Name2>
									<core:Name3>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Name3
									</core:Name3>
									<core:Identifier>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Identifier/core:Ident
										</core:Ident>
									</core:Identifier>
									<core:POBox
										POBoxPostalCode="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:POBox
									</core:POBox>
									<core:Street>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Street
									</core:Street>
									<core:HouseNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:HouseNumber
									</core:HouseNumber>
									<core:StreetSupplement1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:StreetSupplement1
									</core:StreetSupplement1>
									<core:StreetSupplement2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:StreetSupplement2
									</core:StreetSupplement2>
									<core:Building>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Building
									</core:Building>
									<core:Floor>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Floor
									</core:Floor>
									<core:RoomNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:RoomNumber
									</core:RoomNumber>
									<core:InhouseMail>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:InhouseMail
									</core:InhouseMail>
									<core:Department>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Department
									</core:Department>
									<core:PostalCode>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:PostalCode
									</core:PostalCode>
									<core:City>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:City
									</core:City>
									<core:County>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:County
									</core:County>
									<core:Region>
										<core:RegionCoded>ARA</core:RegionCoded>
										<core:RegionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Region/core:RegionCodedOther
										</core:RegionCodedOther>
									</core:Region>
									<core:District>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:District
									</core:District>
									<core:Country>
										<core:CountryCoded>AE</core:CountryCoded>
										<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Country/core:CountryCodedOther
										</core:CountryCodedOther>
									</core:Country>
									<core:Timezone>
										<core:TimezoneCoded>12.00</core:TimezoneCoded>
										<core:TimezoneCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:NameAddress/core:Timezone/core:TimezoneCodedOther
										</core:TimezoneCodedOther>
									</core:Timezone>
								</core:NameAddress>
								<core:PrimaryContact>
									<core:ContactID>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactID/core:Ident
										</core:Ident>
									</core:ContactID>
									<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactName
									</core:ContactName>
									<core:ContactFunction>
										<core:ContactFunctionCoded>AcceptingContact
										</core:ContactFunctionCoded>
										<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
										</core:ContactFunctionCodedOther>
									</core:ContactFunction>
									<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ContactDescription
									</core:ContactDescription>
									<core:ListOfContactNumber>
										<core:ContactNumber>
											<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
											</core:ContactNumberValue>
											<core:ContactNumberTypeCoded>TelephoneNumber
											</core:ContactNumberTypeCoded>
											<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
											</core:ContactNumberTypeCodedOther>
										</core:ContactNumber>
									</core:ListOfContactNumber>
									<core:CorrespondenceLanguage>
										<core:LanguageCoded>aa</core:LanguageCoded>
										<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
										</core:LanguageCodedOther>
										<core:LocaleCoded>ae</core:LocaleCoded>
										<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
										</core:LocaleCodedOther>
									</core:CorrespondenceLanguage>
								</core:PrimaryContact>
								<core:OtherContacts>
									<core:Contact>
										<core:ContactID>
											<core:Agency>
												<core:AgencyCoded>AAMVA</core:AgencyCoded>
												<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
												</core:AgencyCodedOther>
												<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
												</core:AgencyDescription>
												<core:CodeListIdentifierCoded>AcceptanceSiteCode
												</core:CodeListIdentifierCoded>
												<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
												</core:CodeListIdentifierCodedOther>
											</core:Agency>
											<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactID/core:Ident
											</core:Ident>
										</core:ContactID>
										<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactName
										</core:ContactName>
										<core:ContactFunction>
											<core:ContactFunctionCoded>AcceptingContact
											</core:ContactFunctionCoded>
											<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
											</core:ContactFunctionCodedOther>
										</core:ContactFunction>
										<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ContactDescription
										</core:ContactDescription>
										<core:ListOfContactNumber>
											<core:ContactNumber>
												<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
												</core:ContactNumberValue>
												<core:ContactNumberTypeCoded>TelephoneNumber
												</core:ContactNumberTypeCoded>
												<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
												</core:ContactNumberTypeCodedOther>
											</core:ContactNumber>
										</core:ListOfContactNumber>
										<core:CorrespondenceLanguage>
											<core:LanguageCoded>aa</core:LanguageCoded>
											<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
											</core:LanguageCodedOther>
											<core:LocaleCoded>ae</core:LocaleCoded>
											<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
											</core:LocaleCodedOther>
										</core:CorrespondenceLanguage>
									</core:Contact>
								</core:OtherContacts>
								<core:PartyTaxInformation>
									<core:TaxIdentifier>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
										</core:Ident>
									</core:TaxIdentifier>
									<core:RegisteredName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:RegisteredName
									</core:RegisteredName>
									<core:RegisteredOffice>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:RegisteredOffice
									</core:RegisteredOffice>
									<core:TaxLocation>
										<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
										</core:LocationQualifierCoded>
										<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
										</core:LocationQualifierCodedOther>
										<core:LocationIdentifier>
											<core:LocID>
												<core:Agency>
													<core:AgencyCoded>AAMVA</core:AgencyCoded>
													<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
													</core:AgencyCodedOther>
													<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
													</core:AgencyDescription>
													<core:CodeListIdentifierCoded>AcceptanceSiteCode
													</core:CodeListIdentifierCoded>
													<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
													</core:CodeListIdentifierCodedOther>
												</core:Agency>
												<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
												</core:Ident>
											</core:LocID>
											<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
											</core:LocationDescription>
										</core:LocationIdentifier>
										<core:GPSCoordinates>
											<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
											</core:GPSSystem>
											<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
											</core:Latitude>
											<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
											</core:Longitude>
										</core:GPSCoordinates>
									</core:TaxLocation>
									<core:CompanyRegistrationNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/AuthorizationReference/AuthorizationAssignedBy/core:PartyTaxInformation/core:CompanyRegistrationNumber
									</core:CompanyRegistrationNumber>
								</core:PartyTaxInformation>
							</AuthorizationAssignedBy>
						</AuthorizationReference>
						<DeliveryNoteNumber>
							<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/DeliveryNoteNumber/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:01:18</core:RefDate>
						</DeliveryNoteNumber>
						<CostAllocation>
							<CostAllocationNumber>
								<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/CostAllocation/CostAllocationNumber/core:RefNum
								</core:RefNum>
								<core:RefDate>2003-01-01T00:01:19</core:RefDate>
							</CostAllocationNumber>
							<WorkBreakdownStructure>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/CostAllocation/WorkBreakdownStructure
							</WorkBreakdownStructure>
							<FixedAsset>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/CostAllocation/FixedAsset
							</FixedAsset>
						</CostAllocation>
						<ListOfRelatedInvoiceRef>
							<RelatedInvoiceRef>
								<RelatedInvoiceType>
									<InvoiceTypeCoded>MeteredServicesInvoice</InvoiceTypeCoded>
									<InvoiceTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/RelatedInvoiceType/InvoiceTypeCodedOther
									</InvoiceTypeCodedOther>
								</RelatedInvoiceType>
								<InvoiceNumber>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/InvoiceNumber/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:20</core:RefDate>
								</InvoiceNumber>
								<InvoiceLineItemNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/InvoiceLineItemNumber
								</InvoiceLineItemNumber>
							</RelatedInvoiceRef>
						</ListOfRelatedInvoiceRef>
						<OtherInvoiceReferences>
							<core:ReferenceCoded>
								<core:ReferenceTypeCoded>AbbreviatedNewDrugApplicationNumber
								</core:ReferenceTypeCoded>
								<core:ReferenceTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/OtherInvoiceReferences/core:ReferenceCoded/core:ReferenceTypeCodedOther
								</core:ReferenceTypeCodedOther>
								<core:PrimaryReference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/OtherInvoiceReferences/core:ReferenceCoded/core:PrimaryReference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:21</core:RefDate>
								</core:PrimaryReference>
								<core:SupportingReference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/OtherInvoiceReferences/core:ReferenceCoded/core:SupportingReference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:22</core:RefDate>
								</core:SupportingReference>
								<core:SupportingSubReference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/OtherInvoiceReferences/core:ReferenceCoded/core:SupportingSubReference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:23</core:RefDate>
								</core:SupportingSubReference>
								<core:ReferenceDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/OtherInvoiceReferences/core:ReferenceCoded/core:ReferenceDescription
								</core:ReferenceDescription>
							</core:ReferenceCoded>
						</OtherInvoiceReferences>
					</LineItemReferences>
					<CountryOfOrigin>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/CountryOfOrigin/core:CountryCodedOther
						</core:CountryCodedOther>
					</CountryOfOrigin>
					<CountryOfDestination>
						<core:CountryCoded>AE</core:CountryCoded>
						<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/CountryOfDestination/core:CountryCodedOther
						</core:CountryCodedOther>
					</CountryOfDestination>
					<ListOfPartyCoded>
						<core:PartyCoded>
							<core:PartyID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Ident
								</core:Ident>
							</core:PartyID>
							<core:ListOfIdentifier>
								<core:Identifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Ident
									</core:Ident>
								</core:Identifier>
							</core:ListOfIdentifier>
							<core:MDFBusiness>false</core:MDFBusiness>
							<core:NameAddress>
								<core:ExternalAddressID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:ExternalAddressID
								</core:ExternalAddressID>
								<core:Name1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name1
								</core:Name1>
								<core:Name2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name2
								</core:Name2>
								<core:Name3>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name3
								</core:Name3>
								<core:Identifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Ident
									</core:Ident>
								</core:Identifier>
								<core:POBox
									POBoxPostalCode="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox
								</core:POBox>
								<core:Street>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Street
								</core:Street>
								<core:HouseNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:HouseNumber
								</core:HouseNumber>
								<core:StreetSupplement1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement1
								</core:StreetSupplement1>
								<core:StreetSupplement2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement2
								</core:StreetSupplement2>
								<core:Building>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Building
								</core:Building>
								<core:Floor>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Floor
								</core:Floor>
								<core:RoomNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:RoomNumber
								</core:RoomNumber>
								<core:InhouseMail>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:InhouseMail
								</core:InhouseMail>
								<core:Department>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Department
								</core:Department>
								<core:PostalCode>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:PostalCode
								</core:PostalCode>
								<core:City>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:City
								</core:City>
								<core:County>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:County
								</core:County>
								<core:Region>
									<core:RegionCoded>ARA</core:RegionCoded>
									<core:RegionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Region/core:RegionCodedOther
									</core:RegionCodedOther>
								</core:Region>
								<core:District>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:District
								</core:District>
								<core:Country>
									<core:CountryCoded>AE</core:CountryCoded>
									<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Country/core:CountryCodedOther
									</core:CountryCodedOther>
								</core:Country>
								<core:Timezone>
									<core:TimezoneCoded>12.00</core:TimezoneCoded>
									<core:TimezoneCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Timezone/core:TimezoneCodedOther
									</core:TimezoneCodedOther>
								</core:Timezone>
							</core:NameAddress>
							<core:PrimaryContact>
								<core:ContactID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Ident
									</core:Ident>
								</core:ContactID>
								<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactName
								</core:ContactName>
								<core:ContactFunction>
									<core:ContactFunctionCoded>AcceptingContact
									</core:ContactFunctionCoded>
									<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
									</core:ContactFunctionCodedOther>
								</core:ContactFunction>
								<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactDescription
								</core:ContactDescription>
								<core:ListOfContactNumber>
									<core:ContactNumber>
										<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
										</core:ContactNumberValue>
										<core:ContactNumberTypeCoded>TelephoneNumber
										</core:ContactNumberTypeCoded>
										<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
										</core:ContactNumberTypeCodedOther>
									</core:ContactNumber>
								</core:ListOfContactNumber>
								<core:CorrespondenceLanguage>
									<core:LanguageCoded>aa</core:LanguageCoded>
									<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
									</core:LanguageCodedOther>
									<core:LocaleCoded>ae</core:LocaleCoded>
									<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
									</core:LocaleCodedOther>
								</core:CorrespondenceLanguage>
							</core:PrimaryContact>
							<core:OtherContacts>
								<core:Contact>
									<core:ContactID>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Ident
										</core:Ident>
									</core:ContactID>
									<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactName
									</core:ContactName>
									<core:ContactFunction>
										<core:ContactFunctionCoded>AcceptingContact
										</core:ContactFunctionCoded>
										<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
										</core:ContactFunctionCodedOther>
									</core:ContactFunction>
									<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactDescription
									</core:ContactDescription>
									<core:ListOfContactNumber>
										<core:ContactNumber>
											<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
											</core:ContactNumberValue>
											<core:ContactNumberTypeCoded>TelephoneNumber
											</core:ContactNumberTypeCoded>
											<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
											</core:ContactNumberTypeCodedOther>
										</core:ContactNumber>
									</core:ListOfContactNumber>
									<core:CorrespondenceLanguage>
										<core:LanguageCoded>aa</core:LanguageCoded>
										<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
										</core:LanguageCodedOther>
										<core:LocaleCoded>ae</core:LocaleCoded>
										<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
										</core:LocaleCodedOther>
									</core:CorrespondenceLanguage>
								</core:Contact>
							</core:OtherContacts>
							<core:PartyTaxInformation>
								<core:TaxIdentifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
									</core:Ident>
								</core:TaxIdentifier>
								<core:RegisteredName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredName
								</core:RegisteredName>
								<core:RegisteredOffice>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredOffice
								</core:RegisteredOffice>
								<core:TaxLocation>
									<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
									</core:LocationQualifierCoded>
									<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
									</core:LocationQualifierCodedOther>
									<core:LocationIdentifier>
										<core:LocID>
											<core:Agency>
												<core:AgencyCoded>AAMVA</core:AgencyCoded>
												<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
												</core:AgencyCodedOther>
												<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
												</core:AgencyDescription>
												<core:CodeListIdentifierCoded>AcceptanceSiteCode
												</core:CodeListIdentifierCoded>
												<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
												</core:CodeListIdentifierCodedOther>
											</core:Agency>
											<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
											</core:Ident>
										</core:LocID>
										<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
										</core:LocationDescription>
									</core:LocationIdentifier>
									<core:GPSCoordinates>
										<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
										</core:GPSSystem>
										<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
										</core:Latitude>
										<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
										</core:Longitude>
									</core:GPSCoordinates>
								</core:TaxLocation>
								<core:CompanyRegistrationNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:CompanyRegistrationNumber
								</core:CompanyRegistrationNumber>
							</core:PartyTaxInformation>
							<core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
							<core:PartyRoleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ListOfPartyCoded/core:PartyCoded/core:PartyRoleCodedOther
							</core:PartyRoleCodedOther>
						</core:PartyCoded>
					</ListOfPartyCoded>
				</InvoiceBaseItemDetail>
				<InvoicePricingDetail>
					<core:ListOfPrice>
						<core:Price>
							<core:PricingType>
								<core:PriceTypeCoded>CancellationPrice</core:PriceTypeCoded>
								<core:PriceTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:PricingType/core:PriceTypeCodedOther
								</core:PriceTypeCodedOther>
							</core:PricingType>
							<core:UnitPrice>
								<core:UnitPriceValue>1062</core:UnitPriceValue>
								<core:Currency>
									<core:CurrencyCoded>AFA</core:CurrencyCoded>
									<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:UnitPrice/core:Currency/core:CurrencyCodedOther
									</core:CurrencyCodedOther>
								</core:Currency>
							</core:UnitPrice>
							<core:UnitOfMeasurement>
								<core:UOMCoded>1</core:UOMCoded>
								<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:UnitOfMeasurement/core:UOMCodedOther
								</core:UOMCodedOther>
							</core:UnitOfMeasurement>
							<core:CalculatedPriceBasisQuantity>
								<core:QuantityValue SignificanceCoded="Approximately"
									SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:CalculatedPriceBasisQuantity/core:QuantityValue/@SignificanceCodedOther">1063</core:QuantityValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:CalculatedPriceBasisQuantity/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
							</core:CalculatedPriceBasisQuantity>
							<core:ValidityDates>
								<core:StartDate>2003-01-01T00:01:24</core:StartDate>
								<core:EndDate>2003-01-01T00:01:25</core:EndDate>
							</core:ValidityDates>
							<core:PriceQuantityRange>
								<core:QuantityValue SignificanceCoded="Approximately"
									SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:PriceQuantityRange/core:QuantityValue/@SignificanceCodedOther">1064</core:QuantityValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:PriceQuantityRange/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
							</core:PriceQuantityRange>
							<core:PriceMultiplier>
								<core:PriceMultiplierCoded>CostMarkupMultiplier-OriginalCost
								</core:PriceMultiplierCoded>
								<core:PriceMultiplierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ListOfPrice/core:Price/core:PriceMultiplier/core:PriceMultiplierCodedOther
								</core:PriceMultiplierCodedOther>
								<core:Multiplier>1065</core:Multiplier>
							</core:PriceMultiplier>
						</core:Price>
					</core:ListOfPrice>
					<core:Tax>
						<core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
						<core:TaxTypeCodedOther>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxTypeCodedOther/core:Ident
							</core:Ident>
						</core:TaxTypeCodedOther>
						<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
						</core:TaxFunctionQualifierCoded>
						<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxFunctionQualifierCodedOther
						</core:TaxFunctionQualifierCodedOther>
						<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
						<core:TaxCategoryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxCategoryCodedOther
						</core:TaxCategoryCodedOther>
						<core:ReasonTaxExemptCoded>Exempt-ForExport
						</core:ReasonTaxExemptCoded>
						<core:ReasonTaxExemptCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:ReasonTaxExemptCodedOther
						</core:ReasonTaxExemptCodedOther>
						<core:TaxPercent UnitOfMeasurement="P1">1066
						</core:TaxPercent>
						<core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
						<core:TaxPaymentMethodCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxPaymentMethodCodedOther
						</core:TaxPaymentMethodCodedOther>
						<core:TaxableAmount>1067</core:TaxableAmount>
						<core:TaxableAmountInTaxAccountingCurrency>1068
						</core:TaxableAmountInTaxAccountingCurrency>
						<core:TaxAmount>1069</core:TaxAmount>
						<core:TaxAmountInTaxAccountingCurrency>1070
						</core:TaxAmountInTaxAccountingCurrency>
						<core:TaxLocation>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:Tax/core:TaxLocation/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:TaxLocation>
					</core:Tax>
					<core:TotalTaxAmount>
						<core:MonetaryAmount>1071</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:TotalTaxAmount/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:TotalTaxAmount/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:TotalTaxAmount/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1072</core:RateOfExchange>
							<core:InverseRateOfExchange>1073</core:InverseRateOfExchange>
							<core:IndicativeIndicator>true</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:01:26
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:TotalTaxAmount/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:27</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</core:TotalTaxAmount>
					<core:ItemAllowancesOrCharges>
						<core:AllowOrCharge>
							<core:IndicatorCoded>About</core:IndicatorCoded>
							<core:IndicatorCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:IndicatorCodedOther
							</core:IndicatorCodedOther>
							<core:BasisCoded>None</core:BasisCoded>
							<core:BasisCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:BasisCodedOther
							</core:BasisCodedOther>
							<core:MethodOfHandlingCoded>BillBack</core:MethodOfHandlingCoded>
							<core:MethodOfHandlingCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:MethodOfHandlingCodedOther
							</core:MethodOfHandlingCodedOther>
							<core:AllowOrChargeTreatment>
								<core:AllowOrChargeTreatmentCoded>UnitPriceNet
								</core:AllowOrChargeTreatmentCoded>
								<core:AllowOrChargeTreatmentCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:AllowOrChargeTreatment/core:AllowOrChargeTreatmentCodedOther
								</core:AllowOrChargeTreatmentCodedOther>
							</core:AllowOrChargeTreatment>
							<core:AllowanceOrChargeDescription>
								<core:RefID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:AllowanceOrChargeDescription/core:RefID
								</core:RefID>
								<core:ListOfDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:AllowanceOrChargeDescription/core:ListOfDescription
								</core:ListOfDescription>
								<core:ServiceCoded>About</core:ServiceCoded>
								<core:ServiceCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:AllowanceOrChargeDescription/core:ServiceCodedOther
								</core:ServiceCodedOther>
							</core:AllowanceOrChargeDescription>
							<core:ValidityDates>
								<core:StartDate>2003-01-01T00:01:28</core:StartDate>
								<core:EndDate>2003-01-01T00:01:29</core:EndDate>
							</core:ValidityDates>
							<core:BasisQuantityRange>
								<core:QuantityValue SignificanceCoded="Approximately"
									SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:BasisQuantityRange/core:QuantityValue/@SignificanceCodedOther">1074</core:QuantityValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:BasisQuantityRange/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
							</core:BasisQuantityRange>
							<core:TypeOfAllowanceOrCharge>
								<core:QuantityAllowanceOrCharge>
									<core:Quantity>
										<core:QuantityValue SignificanceCoded="Approximately"
											SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1075</core:QuantityValue>
										<core:UnitOfMeasurement>
											<core:UOMCoded>1</core:UOMCoded>
											<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Quantity/core:UnitOfMeasurement/core:UOMCodedOther
											</core:UOMCodedOther>
										</core:UnitOfMeasurement>
									</core:Quantity>
									<core:Rate>
										<core:RatePerUnit>
											<core:UnitPriceValue>1076</core:UnitPriceValue>
											<core:Currency>
												<core:CurrencyCoded>AFA</core:CurrencyCoded>
												<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Rate/core:RatePerUnit/core:Currency/core:CurrencyCodedOther
												</core:CurrencyCodedOther>
											</core:Currency>
										</core:RatePerUnit>
										<core:UnitPriceBasis>1077</core:UnitPriceBasis>
										<core:UnitOfMeasurement>
											<core:UOMCoded>1</core:UOMCoded>
											<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:Rate/core:UnitOfMeasurement/core:UOMCodedOther
											</core:UOMCodedOther>
										</core:UnitOfMeasurement>
									</core:Rate>
									<core:QuantityMonetaryValue>
										<core:MonetaryAmount>1078</core:MonetaryAmount>
										<core:Currency>
											<core:CurrencyCoded>AFA</core:CurrencyCoded>
											<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:Currency/core:CurrencyCodedOther
											</core:CurrencyCodedOther>
										</core:Currency>
										<core:RateOfExchangeDetail>
											<core:ReferenceCurrency>
												<core:CurrencyCoded>AFA</core:CurrencyCoded>
												<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
												</core:CurrencyCodedOther>
											</core:ReferenceCurrency>
											<core:TargetCurrency>
												<core:CurrencyCoded>AFA</core:CurrencyCoded>
												<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
												</core:CurrencyCodedOther>
											</core:TargetCurrency>
											<core:RateOfExchange>1079</core:RateOfExchange>
											<core:InverseRateOfExchange>1080</core:InverseRateOfExchange>
											<core:IndicativeIndicator>false</core:IndicativeIndicator>
											<core:DateOfRateOfExchange>2003-01-01T00:01:30
											</core:DateOfRateOfExchange>
											<core:ListOfRateOfExchangeReference>
												<core:Reference>
													<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:TypeOfAllowanceOrCharge/core:QuantityAllowanceOrCharge/core:QuantityMonetaryValue/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
													</core:RefNum>
													<core:RefDate>2003-01-01T00:01:31</core:RefDate>
												</core:Reference>
											</core:ListOfRateOfExchangeReference>
										</core:RateOfExchangeDetail>
									</core:QuantityMonetaryValue>
								</core:QuantityAllowanceOrCharge>
							</core:TypeOfAllowanceOrCharge>
							<core:Tax>
								<core:TaxTypeCoded>911CityTax</core:TaxTypeCoded>
								<core:TaxTypeCodedOther>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxTypeCodedOther/core:Ident
									</core:Ident>
								</core:TaxTypeCodedOther>
								<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
								</core:TaxFunctionQualifierCoded>
								<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxFunctionQualifierCodedOther
								</core:TaxFunctionQualifierCodedOther>
								<core:TaxCategoryCoded>DutyPaidBySupplier
								</core:TaxCategoryCoded>
								<core:TaxCategoryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxCategoryCodedOther
								</core:TaxCategoryCodedOther>
								<core:ReasonTaxExemptCoded>Exempt-ForExport
								</core:ReasonTaxExemptCoded>
								<core:ReasonTaxExemptCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:ReasonTaxExemptCodedOther
								</core:ReasonTaxExemptCodedOther>
								<core:TaxPercent UnitOfMeasurement="P1">1081
								</core:TaxPercent>
								<core:TaxPaymentMethodCoded>Normal</core:TaxPaymentMethodCoded>
								<core:TaxPaymentMethodCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxPaymentMethodCodedOther
								</core:TaxPaymentMethodCodedOther>
								<core:TaxableAmount>1082</core:TaxableAmount>
								<core:TaxableAmountInTaxAccountingCurrency>1083
								</core:TaxableAmountInTaxAccountingCurrency>
								<core:TaxAmount>1084</core:TaxAmount>
								<core:TaxAmountInTaxAccountingCurrency>1085
								</core:TaxAmountInTaxAccountingCurrency>
								<core:TaxLocation>
									<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
									</core:LocationQualifierCoded>
									<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationQualifierCodedOther
									</core:LocationQualifierCodedOther>
									<core:LocationIdentifier>
										<core:LocID>
											<core:Agency>
												<core:AgencyCoded>AAMVA</core:AgencyCoded>
												<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
												</core:AgencyCodedOther>
												<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
												</core:AgencyDescription>
												<core:CodeListIdentifierCoded>AcceptanceSiteCode
												</core:CodeListIdentifierCoded>
												<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
												</core:CodeListIdentifierCodedOther>
											</core:Agency>
											<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
											</core:Ident>
										</core:LocID>
										<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
										</core:LocationDescription>
									</core:LocationIdentifier>
									<core:GPSCoordinates>
										<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
										</core:GPSSystem>
										<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:GPSCoordinates/core:Latitude
										</core:Latitude>
										<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:Tax/core:TaxLocation/core:GPSCoordinates/core:Longitude
										</core:Longitude>
									</core:GPSCoordinates>
								</core:TaxLocation>
							</core:Tax>
						</core:AllowOrCharge>
					</core:ItemAllowancesOrCharges>
					<core:LineItemSubTotal>
						<core:MonetaryAmount>1086</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemSubTotal/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemSubTotal/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemSubTotal/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1087</core:RateOfExchange>
							<core:InverseRateOfExchange>1088</core:InverseRateOfExchange>
							<core:IndicativeIndicator>true</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:01:32
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemSubTotal/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:33</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</core:LineItemSubTotal>
					<core:LineItemTotal>
						<core:MonetaryAmount>1089</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemTotal/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemTotal/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemTotal/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1090</core:RateOfExchange>
							<core:InverseRateOfExchange>1091</core:InverseRateOfExchange>
							<core:IndicativeIndicator>false</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:01:34
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/core:LineItemTotal/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:35</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</core:LineItemTotal>
					<PaymentCurrencyTotalValue>
						<core:MonetaryAmount>1092</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/PaymentCurrencyTotalValue/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/PaymentCurrencyTotalValue/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/PaymentCurrencyTotalValue/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1093</core:RateOfExchange>
							<core:InverseRateOfExchange>1094</core:InverseRateOfExchange>
							<core:IndicativeIndicator>true</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:01:36
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/PaymentCurrencyTotalValue/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:37</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</PaymentCurrencyTotalValue>
					<TaxAccountingSubTotalValue>
						<core:MonetaryAmount>1095</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/TaxAccountingSubTotalValue/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/TaxAccountingSubTotalValue/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/TaxAccountingSubTotalValue/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1096</core:RateOfExchange>
							<core:InverseRateOfExchange>1097</core:InverseRateOfExchange>
							<core:IndicativeIndicator>false</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:01:38
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/TaxAccountingSubTotalValue/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:01:39</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</TaxAccountingSubTotalValue>
					<ActualPaymentStatus>
						<core:ActualPaymentStatusCoded>PaidInFull
						</core:ActualPaymentStatusCoded>
						<core:ActualPaymentStatusCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/ActualPaymentStatus/core:ActualPaymentStatusCodedOther
						</core:ActualPaymentStatusCodedOther>
					</ActualPaymentStatus>
				</InvoicePricingDetail>
				<LineItemDates>
					<InvoiceDueDate>2003-01-01T00:01:40</InvoiceDueDate>
					<ExpectedShipDate>2003-01-01T00:01:41</ExpectedShipDate>
					<ActualShipDate>2003-01-01T00:01:42</ActualShipDate>
					<ExpectedDeliveryDate>2003-01-01T00:01:43</ExpectedDeliveryDate>
					<ActualDeliveryDate>2003-01-01T00:01:44</ActualDeliveryDate>
					<ReceiptDate>2003-01-01T00:01:45</ReceiptDate>
					<TaxPeriod>
						<core:StartDate>2003-01-01T00:01:46</core:StartDate>
						<core:EndDate>2003-01-01T00:01:47</core:EndDate>
					</TaxPeriod>
					<InvoicingPeriod>
						<core:StartDate>2003-01-01T00:01:48</core:StartDate>
						<core:EndDate>2003-01-01T00:01:49</core:EndDate>
					</InvoicingPeriod>
					<ListOfOtherInvoiceDates>
						<core:DateCoded>
							<core:Date>2003-01-01T00:01:50</core:Date>
							<core:DateQualifier>
								<core:DateQualifierCoded>36030</core:DateQualifierCoded>
								<core:DateQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemDates/ListOfOtherInvoiceDates/core:DateCoded/core:DateQualifier/core:DateQualifierCodedOther
								</core:DateQualifierCodedOther>
							</core:DateQualifier>
						</core:DateCoded>
					</ListOfOtherInvoiceDates>
				</LineItemDates>
				<OtherInvoiceParties>
					<BillToParty>
						<core:PartyID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyID/core:Ident
							</core:Ident>
						</core:PartyID>
						<core:ListOfIdentifier>
							<core:Identifier>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:ListOfIdentifier/core:Identifier/core:Ident
								</core:Ident>
							</core:Identifier>
						</core:ListOfIdentifier>
						<core:MDFBusiness>true</core:MDFBusiness>
						<core:NameAddress>
							<core:ExternalAddressID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:ExternalAddressID
							</core:ExternalAddressID>
							<core:Name1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Name1
							</core:Name1>
							<core:Name2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Name2
							</core:Name2>
							<core:Name3>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Name3
							</core:Name3>
							<core:Identifier>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Identifier/core:Ident
								</core:Ident>
							</core:Identifier>
							<core:POBox
								POBoxPostalCode="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:POBox
							</core:POBox>
							<core:Street>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Street
							</core:Street>
							<core:HouseNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:HouseNumber
							</core:HouseNumber>
							<core:StreetSupplement1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:StreetSupplement1
							</core:StreetSupplement1>
							<core:StreetSupplement2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:StreetSupplement2
							</core:StreetSupplement2>
							<core:Building>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Building
							</core:Building>
							<core:Floor>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Floor
							</core:Floor>
							<core:RoomNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:RoomNumber
							</core:RoomNumber>
							<core:InhouseMail>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:InhouseMail
							</core:InhouseMail>
							<core:Department>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Department
							</core:Department>
							<core:PostalCode>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:PostalCode
							</core:PostalCode>
							<core:City>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:City
							</core:City>
							<core:County>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:County
							</core:County>
							<core:Region>
								<core:RegionCoded>ARA</core:RegionCoded>
								<core:RegionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Region/core:RegionCodedOther
								</core:RegionCodedOther>
							</core:Region>
							<core:District>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:District
							</core:District>
							<core:Country>
								<core:CountryCoded>AE</core:CountryCoded>
								<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Country/core:CountryCodedOther
								</core:CountryCodedOther>
							</core:Country>
							<core:Timezone>
								<core:TimezoneCoded>12.00</core:TimezoneCoded>
								<core:TimezoneCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
								</core:TimezoneCodedOther>
							</core:Timezone>
						</core:NameAddress>
						<core:PrimaryContact>
							<core:ContactID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactID/core:Ident
								</core:Ident>
							</core:ContactID>
							<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactName
							</core:ContactName>
							<core:ContactFunction>
								<core:ContactFunctionCoded>AcceptingContact
								</core:ContactFunctionCoded>
								<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
								</core:ContactFunctionCodedOther>
							</core:ContactFunction>
							<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ContactDescription
							</core:ContactDescription>
							<core:ListOfContactNumber>
								<core:ContactNumber>
									<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
									</core:ContactNumberValue>
									<core:ContactNumberTypeCoded>TelephoneNumber
									</core:ContactNumberTypeCoded>
									<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
									</core:ContactNumberTypeCodedOther>
								</core:ContactNumber>
							</core:ListOfContactNumber>
							<core:CorrespondenceLanguage>
								<core:LanguageCoded>aa</core:LanguageCoded>
								<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
								</core:LanguageCodedOther>
								<core:LocaleCoded>ae</core:LocaleCoded>
								<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
								</core:LocaleCodedOther>
							</core:CorrespondenceLanguage>
						</core:PrimaryContact>
						<core:OtherContacts>
							<core:Contact>
								<core:ContactID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
									</core:Ident>
								</core:ContactID>
								<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactName
								</core:ContactName>
								<core:ContactFunction>
									<core:ContactFunctionCoded>AcceptingContact
									</core:ContactFunctionCoded>
									<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
									</core:ContactFunctionCodedOther>
								</core:ContactFunction>
								<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ContactDescription
								</core:ContactDescription>
								<core:ListOfContactNumber>
									<core:ContactNumber>
										<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
										</core:ContactNumberValue>
										<core:ContactNumberTypeCoded>TelephoneNumber
										</core:ContactNumberTypeCoded>
										<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
										</core:ContactNumberTypeCodedOther>
									</core:ContactNumber>
								</core:ListOfContactNumber>
								<core:CorrespondenceLanguage>
									<core:LanguageCoded>aa</core:LanguageCoded>
									<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
									</core:LanguageCodedOther>
									<core:LocaleCoded>ae</core:LocaleCoded>
									<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
									</core:LocaleCodedOther>
								</core:CorrespondenceLanguage>
							</core:Contact>
						</core:OtherContacts>
						<core:PartyTaxInformation>
							<core:TaxIdentifier>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
								</core:Ident>
							</core:TaxIdentifier>
							<core:RegisteredName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:RegisteredName
							</core:RegisteredName>
							<core:RegisteredOffice>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:RegisteredOffice
							</core:RegisteredOffice>
							<core:TaxLocation>
								<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
								</core:LocationQualifierCoded>
								<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
								</core:LocationQualifierCodedOther>
								<core:LocationIdentifier>
									<core:LocID>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
										</core:Ident>
									</core:LocID>
									<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
									</core:LocationDescription>
								</core:LocationIdentifier>
								<core:GPSCoordinates>
									<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
									</core:GPSSystem>
									<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
									</core:Latitude>
									<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
									</core:Longitude>
								</core:GPSCoordinates>
							</core:TaxLocation>
							<core:CompanyRegistrationNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/BillToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
							</core:CompanyRegistrationNumber>
						</core:PartyTaxInformation>
					</BillToParty>
					<RemitToParty>
						<core:PartyID>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyID/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyID/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyID/core:Ident
							</core:Ident>
						</core:PartyID>
						<core:ListOfIdentifier>
							<core:Identifier>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:ListOfIdentifier/core:Identifier/core:Ident
								</core:Ident>
							</core:Identifier>
						</core:ListOfIdentifier>
						<core:MDFBusiness>false</core:MDFBusiness>
						<core:NameAddress>
							<core:ExternalAddressID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:ExternalAddressID
							</core:ExternalAddressID>
							<core:Name1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Name1
							</core:Name1>
							<core:Name2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Name2
							</core:Name2>
							<core:Name3>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Name3
							</core:Name3>
							<core:Identifier>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Identifier/core:Ident
								</core:Ident>
							</core:Identifier>
							<core:POBox
								POBoxPostalCode="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:POBox
							</core:POBox>
							<core:Street>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Street
							</core:Street>
							<core:HouseNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:HouseNumber
							</core:HouseNumber>
							<core:StreetSupplement1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:StreetSupplement1
							</core:StreetSupplement1>
							<core:StreetSupplement2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:StreetSupplement2
							</core:StreetSupplement2>
							<core:Building>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Building
							</core:Building>
							<core:Floor>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Floor
							</core:Floor>
							<core:RoomNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:RoomNumber
							</core:RoomNumber>
							<core:InhouseMail>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:InhouseMail
							</core:InhouseMail>
							<core:Department>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Department
							</core:Department>
							<core:PostalCode>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:PostalCode
							</core:PostalCode>
							<core:City>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:City
							</core:City>
							<core:County>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:County
							</core:County>
							<core:Region>
								<core:RegionCoded>ARA</core:RegionCoded>
								<core:RegionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Region/core:RegionCodedOther
								</core:RegionCodedOther>
							</core:Region>
							<core:District>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:District
							</core:District>
							<core:Country>
								<core:CountryCoded>AE</core:CountryCoded>
								<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Country/core:CountryCodedOther
								</core:CountryCodedOther>
							</core:Country>
							<core:Timezone>
								<core:TimezoneCoded>12.00</core:TimezoneCoded>
								<core:TimezoneCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:NameAddress/core:Timezone/core:TimezoneCodedOther
								</core:TimezoneCodedOther>
							</core:Timezone>
						</core:NameAddress>
						<core:PrimaryContact>
							<core:ContactID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactID/core:Ident
								</core:Ident>
							</core:ContactID>
							<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactName
							</core:ContactName>
							<core:ContactFunction>
								<core:ContactFunctionCoded>AcceptingContact
								</core:ContactFunctionCoded>
								<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
								</core:ContactFunctionCodedOther>
							</core:ContactFunction>
							<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ContactDescription
							</core:ContactDescription>
							<core:ListOfContactNumber>
								<core:ContactNumber>
									<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
									</core:ContactNumberValue>
									<core:ContactNumberTypeCoded>TelephoneNumber
									</core:ContactNumberTypeCoded>
									<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
									</core:ContactNumberTypeCodedOther>
								</core:ContactNumber>
							</core:ListOfContactNumber>
							<core:CorrespondenceLanguage>
								<core:LanguageCoded>aa</core:LanguageCoded>
								<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
								</core:LanguageCodedOther>
								<core:LocaleCoded>ae</core:LocaleCoded>
								<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
								</core:LocaleCodedOther>
							</core:CorrespondenceLanguage>
						</core:PrimaryContact>
						<core:OtherContacts>
							<core:Contact>
								<core:ContactID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactID/core:Ident
									</core:Ident>
								</core:ContactID>
								<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactName
								</core:ContactName>
								<core:ContactFunction>
									<core:ContactFunctionCoded>AcceptingContact
									</core:ContactFunctionCoded>
									<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
									</core:ContactFunctionCodedOther>
								</core:ContactFunction>
								<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ContactDescription
								</core:ContactDescription>
								<core:ListOfContactNumber>
									<core:ContactNumber>
										<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
										</core:ContactNumberValue>
										<core:ContactNumberTypeCoded>TelephoneNumber
										</core:ContactNumberTypeCoded>
										<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
										</core:ContactNumberTypeCodedOther>
									</core:ContactNumber>
								</core:ListOfContactNumber>
								<core:CorrespondenceLanguage>
									<core:LanguageCoded>aa</core:LanguageCoded>
									<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
									</core:LanguageCodedOther>
									<core:LocaleCoded>ae</core:LocaleCoded>
									<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
									</core:LocaleCodedOther>
								</core:CorrespondenceLanguage>
							</core:Contact>
						</core:OtherContacts>
						<core:PartyTaxInformation>
							<core:TaxIdentifier>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
								</core:Ident>
							</core:TaxIdentifier>
							<core:RegisteredName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:RegisteredName
							</core:RegisteredName>
							<core:RegisteredOffice>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:RegisteredOffice
							</core:RegisteredOffice>
							<core:TaxLocation>
								<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
								</core:LocationQualifierCoded>
								<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
								</core:LocationQualifierCodedOther>
								<core:LocationIdentifier>
									<core:LocID>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
										</core:Ident>
									</core:LocID>
									<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
									</core:LocationDescription>
								</core:LocationIdentifier>
								<core:GPSCoordinates>
									<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
									</core:GPSSystem>
									<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
									</core:Latitude>
									<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
									</core:Longitude>
								</core:GPSCoordinates>
							</core:TaxLocation>
							<core:CompanyRegistrationNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/RemitToParty/core:PartyTaxInformation/core:CompanyRegistrationNumber
							</core:CompanyRegistrationNumber>
						</core:PartyTaxInformation>
					</RemitToParty>
					<ListOfPartyCoded>
						<core:PartyCoded>
							<core:PartyID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyID/core:Ident
								</core:Ident>
							</core:PartyID>
							<core:ListOfIdentifier>
								<core:Identifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:ListOfIdentifier/core:Identifier/core:Ident
									</core:Ident>
								</core:Identifier>
							</core:ListOfIdentifier>
							<core:MDFBusiness>true</core:MDFBusiness>
							<core:NameAddress>
								<core:ExternalAddressID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:ExternalAddressID
								</core:ExternalAddressID>
								<core:Name1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name1
								</core:Name1>
								<core:Name2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name2
								</core:Name2>
								<core:Name3>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Name3
								</core:Name3>
								<core:Identifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Identifier/core:Ident
									</core:Ident>
								</core:Identifier>
								<core:POBox
									POBoxPostalCode="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox/@POBoxPostalCode">Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:POBox
								</core:POBox>
								<core:Street>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Street
								</core:Street>
								<core:HouseNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:HouseNumber
								</core:HouseNumber>
								<core:StreetSupplement1>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement1
								</core:StreetSupplement1>
								<core:StreetSupplement2>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:StreetSupplement2
								</core:StreetSupplement2>
								<core:Building>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Building
								</core:Building>
								<core:Floor>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Floor
								</core:Floor>
								<core:RoomNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:RoomNumber
								</core:RoomNumber>
								<core:InhouseMail>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:InhouseMail
								</core:InhouseMail>
								<core:Department>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Department
								</core:Department>
								<core:PostalCode>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:PostalCode
								</core:PostalCode>
								<core:City>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:City
								</core:City>
								<core:County>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:County
								</core:County>
								<core:Region>
									<core:RegionCoded>ARA</core:RegionCoded>
									<core:RegionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Region/core:RegionCodedOther
									</core:RegionCodedOther>
								</core:Region>
								<core:District>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:District
								</core:District>
								<core:Country>
									<core:CountryCoded>AE</core:CountryCoded>
									<core:CountryCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Country/core:CountryCodedOther
									</core:CountryCodedOther>
								</core:Country>
								<core:Timezone>
									<core:TimezoneCoded>12.00</core:TimezoneCoded>
									<core:TimezoneCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:NameAddress/core:Timezone/core:TimezoneCodedOther
									</core:TimezoneCodedOther>
								</core:Timezone>
							</core:NameAddress>
							<core:PrimaryContact>
								<core:ContactID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactID/core:Ident
									</core:Ident>
								</core:ContactID>
								<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactName
								</core:ContactName>
								<core:ContactFunction>
									<core:ContactFunctionCoded>AcceptingContact
									</core:ContactFunctionCoded>
									<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactFunction/core:ContactFunctionCodedOther
									</core:ContactFunctionCodedOther>
								</core:ContactFunction>
								<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ContactDescription
								</core:ContactDescription>
								<core:ListOfContactNumber>
									<core:ContactNumber>
										<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
										</core:ContactNumberValue>
										<core:ContactNumberTypeCoded>TelephoneNumber
										</core:ContactNumberTypeCoded>
										<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
										</core:ContactNumberTypeCodedOther>
									</core:ContactNumber>
								</core:ListOfContactNumber>
								<core:CorrespondenceLanguage>
									<core:LanguageCoded>aa</core:LanguageCoded>
									<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LanguageCodedOther
									</core:LanguageCodedOther>
									<core:LocaleCoded>ae</core:LocaleCoded>
									<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PrimaryContact/core:CorrespondenceLanguage/core:LocaleCodedOther
									</core:LocaleCodedOther>
								</core:CorrespondenceLanguage>
							</core:PrimaryContact>
							<core:OtherContacts>
								<core:Contact>
									<core:ContactID>
										<core:Agency>
											<core:AgencyCoded>AAMVA</core:AgencyCoded>
											<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyCodedOther
											</core:AgencyCodedOther>
											<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:AgencyDescription
											</core:AgencyDescription>
											<core:CodeListIdentifierCoded>AcceptanceSiteCode
											</core:CodeListIdentifierCoded>
											<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Agency/core:CodeListIdentifierCodedOther
											</core:CodeListIdentifierCodedOther>
										</core:Agency>
										<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactID/core:Ident
										</core:Ident>
									</core:ContactID>
									<core:ContactName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactName
									</core:ContactName>
									<core:ContactFunction>
										<core:ContactFunctionCoded>AcceptingContact
										</core:ContactFunctionCoded>
										<core:ContactFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactFunction/core:ContactFunctionCodedOther
										</core:ContactFunctionCodedOther>
									</core:ContactFunction>
									<core:ContactDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ContactDescription
									</core:ContactDescription>
									<core:ListOfContactNumber>
										<core:ContactNumber>
											<core:ContactNumberValue>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberValue
											</core:ContactNumberValue>
											<core:ContactNumberTypeCoded>TelephoneNumber
											</core:ContactNumberTypeCoded>
											<core:ContactNumberTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:ListOfContactNumber/core:ContactNumber/core:ContactNumberTypeCodedOther
											</core:ContactNumberTypeCodedOther>
										</core:ContactNumber>
									</core:ListOfContactNumber>
									<core:CorrespondenceLanguage>
										<core:LanguageCoded>aa</core:LanguageCoded>
										<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LanguageCodedOther
										</core:LanguageCodedOther>
										<core:LocaleCoded>ae</core:LocaleCoded>
										<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:OtherContacts/core:Contact/core:CorrespondenceLanguage/core:LocaleCodedOther
										</core:LocaleCodedOther>
									</core:CorrespondenceLanguage>
								</core:Contact>
							</core:OtherContacts>
							<core:PartyTaxInformation>
								<core:TaxIdentifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxIdentifier/core:Ident
									</core:Ident>
								</core:TaxIdentifier>
								<core:RegisteredName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredName
								</core:RegisteredName>
								<core:RegisteredOffice>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:RegisteredOffice
								</core:RegisteredOffice>
								<core:TaxLocation>
									<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
									</core:LocationQualifierCoded>
									<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationQualifierCodedOther
									</core:LocationQualifierCodedOther>
									<core:LocationIdentifier>
										<core:LocID>
											<core:Agency>
												<core:AgencyCoded>AAMVA</core:AgencyCoded>
												<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
												</core:AgencyCodedOther>
												<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
												</core:AgencyDescription>
												<core:CodeListIdentifierCoded>AcceptanceSiteCode
												</core:CodeListIdentifierCoded>
												<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
												</core:CodeListIdentifierCodedOther>
											</core:Agency>
											<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocID/core:Ident
											</core:Ident>
										</core:LocID>
										<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:LocationIdentifier/core:LocationDescription
										</core:LocationDescription>
									</core:LocationIdentifier>
									<core:GPSCoordinates>
										<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:GPSSystem
										</core:GPSSystem>
										<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Latitude
										</core:Latitude>
										<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:TaxLocation/core:GPSCoordinates/core:Longitude
										</core:Longitude>
									</core:GPSCoordinates>
								</core:TaxLocation>
								<core:CompanyRegistrationNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyTaxInformation/core:CompanyRegistrationNumber
								</core:CompanyRegistrationNumber>
							</core:PartyTaxInformation>
							<core:PartyRoleCoded>AcceptanceLocation</core:PartyRoleCoded>
							<core:PartyRoleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/OtherInvoiceParties/ListOfPartyCoded/core:PartyCoded/core:PartyRoleCodedOther
							</core:PartyRoleCodedOther>
						</core:PartyCoded>
					</ListOfPartyCoded>
				</OtherInvoiceParties>
				<DeliveryDetail>
					<core:ShipToLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipToLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:ShipToLocation>
					<core:ShipFromLocation>
						<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
						</core:LocationQualifierCoded>
						<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:LocationQualifierCodedOther
						</core:LocationQualifierCodedOther>
						<core:LocationIdentifier>
							<core:LocID>
								<core:Agency>
									<core:AgencyCoded>AAMVA</core:AgencyCoded>
									<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
									</core:AgencyCodedOther>
									<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
									</core:AgencyDescription>
									<core:CodeListIdentifierCoded>AcceptanceSiteCode
									</core:CodeListIdentifierCoded>
									<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
									</core:CodeListIdentifierCodedOther>
								</core:Agency>
								<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocID/core:Ident
								</core:Ident>
							</core:LocID>
							<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:LocationIdentifier/core:LocationDescription
							</core:LocationDescription>
						</core:LocationIdentifier>
						<core:GPSCoordinates>
							<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:GPSCoordinates/core:GPSSystem
							</core:GPSSystem>
							<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:GPSCoordinates/core:Latitude
							</core:Latitude>
							<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ShipFromLocation/core:GPSCoordinates/core:Longitude
							</core:Longitude>
						</core:GPSCoordinates>
					</core:ShipFromLocation>
					<core:ListOfScheduleLine>
						<core:ScheduleLine>
							<core:ScheduleLineID>1098</core:ScheduleLineID>
							<core:Quantity>
								<core:QuantityValue SignificanceCoded="Approximately"
									SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:Quantity/core:QuantityValue/@SignificanceCodedOther">1099</core:QuantityValue>
								<core:UnitOfMeasurement>
									<core:UOMCoded>1</core:UOMCoded>
									<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:Quantity/core:UnitOfMeasurement/core:UOMCodedOther
									</core:UOMCodedOther>
								</core:UnitOfMeasurement>
							</core:Quantity>
							<core:RequestedDeliveryDate>2003-01-01T00:01:51
							</core:RequestedDeliveryDate>
							<core:ScheduleLineNote>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ScheduleLineNote
							</core:ScheduleLineNote>
							<core:TransportRouting>
								<core:TransportRouteID>1100</core:TransportRouteID>
								<core:TransportMode>
									<core:TransportModeCoded>Air</core:TransportModeCoded>
									<core:TransportModeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMode/core:TransportModeCodedOther
									</core:TransportModeCodedOther>
								</core:TransportMode>
								<core:TransportMeans>
									<core:TransportMeansCoded>20FtILContainer-ClosedTop
									</core:TransportMeansCoded>
									<core:TransportMeansCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeans/core:TransportMeansCodedOther
									</core:TransportMeansCodedOther>
								</core:TransportMeans>
								<core:TransportMeansIdentifier>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansIdentifier/core:Ident
									</core:Ident>
								</core:TransportMeansIdentifier>
								<core:TransportMeansReference>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportMeansReference/core:Ident
									</core:Ident>
								</core:TransportMeansReference>
								<core:TransportRequirementCoded>SpecifiedFlagRequired
								</core:TransportRequirementCoded>
								<core:TransportRequirementCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportRequirementCodedOther
								</core:TransportRequirementCodedOther>
								<core:CarrierName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierName
								</core:CarrierName>
								<core:CarrierID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CarrierID/core:Ident
									</core:Ident>
								</core:CarrierID>
								<core:TransportQuantities>
									<core:LadingQuantity>
										<core:QuantityValue SignificanceCoded="Approximately"
											SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:QuantityValue/@SignificanceCodedOther">1101</core:QuantityValue>
										<core:UnitOfMeasurement>
											<core:UOMCoded>1</core:UOMCoded>
											<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:LadingQuantity/core:UnitOfMeasurement/core:UOMCodedOther
											</core:UOMCodedOther>
										</core:UnitOfMeasurement>
									</core:LadingQuantity>
									<core:ListOfQuantityCoded>
										<core:QuantityCoded>
											<core:QuantityValue SignificanceCoded="Approximately"
												SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityValue/@SignificanceCodedOther">1102</core:QuantityValue>
											<core:UnitOfMeasurement>
												<core:UOMCoded>1</core:UOMCoded>
												<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:UnitOfMeasurement/core:UOMCodedOther
												</core:UOMCodedOther>
											</core:UnitOfMeasurement>
											<core:QuantityQualifierCoded>AcceptableUnserviceableQuantity
											</core:QuantityQualifierCoded>
											<core:QuantityQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportQuantities/core:ListOfQuantityCoded/core:QuantityCoded/core:QuantityQualifierCodedOther
											</core:QuantityQualifierCodedOther>
										</core:QuantityCoded>
									</core:ListOfQuantityCoded>
								</core:TransportQuantities>
								<core:CustShippingContractNum>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:CustShippingContractNum
								</core:CustShippingContractNum>
								<core:ServiceLevel>
									<core:ServiceLevelCoded>BulkCommodityTrain
									</core:ServiceLevelCoded>
									<core:ServiceLevelCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ServiceLevel/core:ServiceLevelCodedOther
									</core:ServiceLevelCodedOther>
									<core:ServiceLevelReasonCoded>ScheduleIncreaseForecastChangeOrSpecialCarOrder
									</core:ServiceLevelReasonCoded>
									<core:ServiceLevelReasonCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ServiceLevel/core:ServiceLevelReasonCodedOther
									</core:ServiceLevelReasonCodedOther>
									<core:ServiceLevelResponsibilityCoded>CustomerPlant
									</core:ServiceLevelResponsibilityCoded>
									<core:ServiceLevelResponsibilityCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ServiceLevel/core:ServiceLevelResponsibilityCodedOther
									</core:ServiceLevelResponsibilityCodedOther>
								</core:ServiceLevel>
								<core:ShippingInstructions>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ShippingInstructions
								</core:ShippingInstructions>
								<core:TransportLegCoded>InlandTransport</core:TransportLegCoded>
								<core:TransportLegCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLegCodedOther
								</core:TransportLegCodedOther>
								<core:ListOfTransportEquipment>
									<core:TransportEquipment>
										<core:EquipmentProviderCoded>ShipperSupplied
										</core:EquipmentProviderCoded>
										<core:EquipmentProviderCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentProviderCodedOther
										</core:EquipmentProviderCodedOther>
										<core:EquipmentOwnerCoded>NotCustomerOwnedOrLeased
										</core:EquipmentOwnerCoded>
										<core:EquipmentOwnerCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentOwnerCodedOther
										</core:EquipmentOwnerCodedOther>
										<core:EquipmentID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentID
										</core:EquipmentID>
										<core:EquipmentSizeTypeCoded>20FtIlContainer-ClosedTop
										</core:EquipmentSizeTypeCoded>
										<core:EquipementSizeTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipementSizeTypeCodedOther
										</core:EquipementSizeTypeCodedOther>
										<core:EquipmentStatusCoded>Continental
										</core:EquipmentStatusCoded>
										<core:EquipmentStatusCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentStatusCodedOther
										</core:EquipmentStatusCodedOther>
										<core:FullIndicatorCoded>Full</core:FullIndicatorCoded>
										<core:FullIndicatorCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:FullIndicatorCodedOther
										</core:FullIndicatorCodedOther>
										<core:Conditions>
											<core:RefrigerationOn>false</core:RefrigerationOn>
											<core:Residue>true</core:Residue>
											<core:ListOfConditions>
												<core:Dimension>
													<core:Measurement>
														<core:MeasurementValue
															SignificanceCoded="Approximately"
															SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther"
															ConditionsCoded="WhereAirEquals1"
															ConditionsCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1103</core:MeasurementValue>
														<core:UnitOfMeasurement>
															<core:UOMCoded>1</core:UOMCoded>
															<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther
															</core:UOMCodedOther>
														</core:UnitOfMeasurement>
													</core:Measurement>
													<core:DimensionCoded>ConsolidatedWeight
													</core:DimensionCoded>
													<core:DimensionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:Conditions/core:ListOfConditions/core:Dimension/core:DimensionCodedOther
													</core:DimensionCodedOther>
												</core:Dimension>
											</core:ListOfConditions>
										</core:Conditions>
										<core:EquipmentNote>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:EquipmentNote
										</core:EquipmentNote>
										<core:ListOfSealInfo>
											<core:SealInfo>
												<core:SealNumber>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealNumber
												</core:SealNumber>
												<core:SealIssuer>
													<core:SealIssuerCoded>Consolidator</core:SealIssuerCoded>
													<core:SealIssuerCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealIssuer/core:SealIssuerCodedOther
													</core:SealIssuerCodedOther>
												</core:SealIssuer>
												<core:SealStatusDescription>
													<core:SealStatusCoded>Broken</core:SealStatusCoded>
													<core:SealStatusCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusCodedOther
													</core:SealStatusCodedOther>
													<core:SealStatusInfo>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfSealInfo/core:SealInfo/core:SealStatusDescription/core:SealStatusInfo
													</core:SealStatusInfo>
												</core:SealStatusDescription>
											</core:SealInfo>
										</core:ListOfSealInfo>
										<core:ListOfEquipmentMeasurements>
											<core:Dimension>
												<core:Measurement>
													<core:MeasurementValue
														SignificanceCoded="Approximately"
														SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@SignificanceCodedOther"
														ConditionsCoded="WhereAirEquals1"
														ConditionsCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:MeasurementValue/@ConditionsCodedOther">1104</core:MeasurementValue>
													<core:UnitOfMeasurement>
														<core:UOMCoded>1</core:UOMCoded>
														<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:Measurement/core:UnitOfMeasurement/core:UOMCodedOther
														</core:UOMCodedOther>
													</core:UnitOfMeasurement>
												</core:Measurement>
												<core:DimensionCoded>ConsolidatedWeight
												</core:DimensionCoded>
												<core:DimensionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:ListOfTransportEquipment/core:TransportEquipment/core:ListOfEquipmentMeasurements/core:Dimension/core:DimensionCodedOther
												</core:DimensionCodedOther>
											</core:Dimension>
										</core:ListOfEquipmentMeasurements>
									</core:TransportEquipment>
								</core:ListOfTransportEquipment>
								<core:TransitDirection>
									<core:TransitDirectionCoded>BuyerToSeller
									</core:TransitDirectionCoded>
									<core:TransitDirectionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransitDirection/core:TransitDirectionCodedOther
									</core:TransitDirectionCodedOther>
									<core:TransitTimeQualifierCoded>AirHours
									</core:TransitTimeQualifierCoded>
									<core:TransitTimeQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransitDirection/core:TransitTimeQualifierCodedOther
									</core:TransitTimeQualifierCodedOther>
									<core:TransitTime>1105</core:TransitTime>
								</core:TransitDirection>
								<core:TransportLocationList>
									<core:StartTransportLocation>
										<core:Location>
											<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
											</core:LocationQualifierCoded>
											<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationQualifierCodedOther
											</core:LocationQualifierCodedOther>
											<core:LocationIdentifier>
												<core:LocID>
													<core:Agency>
														<core:AgencyCoded>AAMVA</core:AgencyCoded>
														<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
														</core:AgencyCodedOther>
														<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
														</core:AgencyDescription>
														<core:CodeListIdentifierCoded>AcceptanceSiteCode
														</core:CodeListIdentifierCoded>
														<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
														</core:CodeListIdentifierCodedOther>
													</core:Agency>
													<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident
													</core:Ident>
												</core:LocID>
												<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription
												</core:LocationDescription>
											</core:LocationIdentifier>
											<core:GPSCoordinates>
												<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem
												</core:GPSSystem>
												<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Latitude
												</core:Latitude>
												<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:StartTransportLocation/core:Location/core:GPSCoordinates/core:Longitude
												</core:Longitude>
											</core:GPSCoordinates>
										</core:Location>
										<core:LocationID>1106</core:LocationID>
										<core:Sequence>1107</core:Sequence>
										<core:EstimatedArrivalDate>2003-01-01T00:01:52
										</core:EstimatedArrivalDate>
										<core:ActualArrivalDate>2003-01-01T00:01:53
										</core:ActualArrivalDate>
										<core:EstimatedDepartureDate>2003-01-01T00:01:54
										</core:EstimatedDepartureDate>
										<core:ActualDepartureDate>2003-01-01T00:01:55
										</core:ActualDepartureDate>
									</core:StartTransportLocation>
									<core:InterimTransportLocation>
										<core:Location>
											<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
											</core:LocationQualifierCoded>
											<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationQualifierCodedOther
											</core:LocationQualifierCodedOther>
											<core:LocationIdentifier>
												<core:LocID>
													<core:Agency>
														<core:AgencyCoded>AAMVA</core:AgencyCoded>
														<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
														</core:AgencyCodedOther>
														<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
														</core:AgencyDescription>
														<core:CodeListIdentifierCoded>AcceptanceSiteCode
														</core:CodeListIdentifierCoded>
														<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
														</core:CodeListIdentifierCodedOther>
													</core:Agency>
													<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident
													</core:Ident>
												</core:LocID>
												<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription
												</core:LocationDescription>
											</core:LocationIdentifier>
											<core:GPSCoordinates>
												<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem
												</core:GPSSystem>
												<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Latitude
												</core:Latitude>
												<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:InterimTransportLocation/core:Location/core:GPSCoordinates/core:Longitude
												</core:Longitude>
											</core:GPSCoordinates>
										</core:Location>
										<core:LocationID>1108</core:LocationID>
										<core:Sequence>1109</core:Sequence>
										<core:EstimatedArrivalDate>2003-01-01T00:01:56
										</core:EstimatedArrivalDate>
										<core:ActualArrivalDate>2003-01-01T00:01:57
										</core:ActualArrivalDate>
										<core:EstimatedDepartureDate>2003-01-01T00:01:58
										</core:EstimatedDepartureDate>
										<core:ActualDepartureDate>2003-01-01T00:01:59
										</core:ActualDepartureDate>
									</core:InterimTransportLocation>
									<core:EndTransportLocation>
										<core:Location>
											<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
											</core:LocationQualifierCoded>
											<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationQualifierCodedOther
											</core:LocationQualifierCodedOther>
											<core:LocationIdentifier>
												<core:LocID>
													<core:Agency>
														<core:AgencyCoded>AAMVA</core:AgencyCoded>
														<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
														</core:AgencyCodedOther>
														<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
														</core:AgencyDescription>
														<core:CodeListIdentifierCoded>AcceptanceSiteCode
														</core:CodeListIdentifierCoded>
														<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
														</core:CodeListIdentifierCodedOther>
													</core:Agency>
													<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocID/core:Ident
													</core:Ident>
												</core:LocID>
												<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:LocationIdentifier/core:LocationDescription
												</core:LocationDescription>
											</core:LocationIdentifier>
											<core:GPSCoordinates>
												<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:GPSSystem
												</core:GPSSystem>
												<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Latitude
												</core:Latitude>
												<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:TransportRouting/core:TransportLocationList/core:EndTransportLocation/core:Location/core:GPSCoordinates/core:Longitude
												</core:Longitude>
											</core:GPSCoordinates>
										</core:Location>
										<core:LocationID>1110</core:LocationID>
										<core:Sequence>1111</core:Sequence>
										<core:EstimatedArrivalDate>2003-01-01T00:02:00
										</core:EstimatedArrivalDate>
										<core:ActualArrivalDate>2003-01-01T00:02:01
										</core:ActualArrivalDate>
										<core:EstimatedDepartureDate>2003-01-01T00:02:02
										</core:EstimatedDepartureDate>
										<core:ActualDepartureDate>2003-01-01T00:02:03
										</core:ActualDepartureDate>
									</core:EndTransportLocation>
								</core:TransportLocationList>
							</core:TransportRouting>
							<core:ListOfShipToSubInformation>
								<core:ShipToSubInformation>
									<core:ShipToSubLocation>
										<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
										</core:LocationQualifierCoded>
										<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationQualifierCodedOther
										</core:LocationQualifierCodedOther>
										<core:LocationIdentifier>
											<core:LocID>
												<core:Agency>
													<core:AgencyCoded>AAMVA</core:AgencyCoded>
													<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
													</core:AgencyCodedOther>
													<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
													</core:AgencyDescription>
													<core:CodeListIdentifierCoded>AcceptanceSiteCode
													</core:CodeListIdentifierCoded>
													<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
													</core:CodeListIdentifierCodedOther>
												</core:Agency>
												<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocID/core:Ident
												</core:Ident>
											</core:LocID>
											<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:LocationIdentifier/core:LocationDescription
											</core:LocationDescription>
										</core:LocationIdentifier>
										<core:GPSCoordinates>
											<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:GPSCoordinates/core:GPSSystem
											</core:GPSSystem>
											<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:GPSCoordinates/core:Latitude
											</core:Latitude>
											<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubLocation/core:GPSCoordinates/core:Longitude
											</core:Longitude>
										</core:GPSCoordinates>
									</core:ShipToSubLocation>
									<core:ShipToSubQuantity>
										<core:QuantityValue SignificanceCoded="Approximately"
											SignificanceCodedOther="Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubQuantity/core:QuantityValue/@SignificanceCodedOther">1112</core:QuantityValue>
										<core:UnitOfMeasurement>
											<core:UOMCoded>1</core:UOMCoded>
											<core:UOMCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:ListOfScheduleLine/core:ScheduleLine/core:ListOfShipToSubInformation/core:ShipToSubInformation/core:ShipToSubQuantity/core:UnitOfMeasurement/core:UOMCodedOther
											</core:UOMCodedOther>
										</core:UnitOfMeasurement>
									</core:ShipToSubQuantity>
								</core:ShipToSubInformation>
							</core:ListOfShipToSubInformation>
						</core:ScheduleLine>
					</core:ListOfScheduleLine>
					<core:SimplePackageNote>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:SimplePackageNote
					</core:SimplePackageNote>
					<core:TermsOfDelivery>
						<core:TermsOfDeliveryFunctionCoded>PriceCondition
						</core:TermsOfDeliveryFunctionCoded>
						<core:TermsOfDeliveryFunctionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:TermsOfDeliveryFunctionCodedOther
						</core:TermsOfDeliveryFunctionCodedOther>
						<core:TransportTermsCoded>Ex-Works</core:TransportTermsCoded>
						<core:TransportTermsCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:TransportTermsCodedOther
						</core:TransportTermsCodedOther>
						<core:ShipmentMethodOfPaymentCoded>Account
						</core:ShipmentMethodOfPaymentCoded>
						<core:ShipmentMethodOfPaymentCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:ShipmentMethodOfPaymentCodedOther
						</core:ShipmentMethodOfPaymentCodedOther>
						<core:Location>
							<core:LocationQualifierCoded>JurisdictionToReceiveCreditForUniformCommercialCodeFiling
							</core:LocationQualifierCoded>
							<core:LocationQualifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationQualifierCodedOther
							</core:LocationQualifierCodedOther>
							<core:LocationIdentifier>
								<core:LocID>
									<core:Agency>
										<core:AgencyCoded>AAMVA</core:AgencyCoded>
										<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyCodedOther
										</core:AgencyCodedOther>
										<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:AgencyDescription
										</core:AgencyDescription>
										<core:CodeListIdentifierCoded>AcceptanceSiteCode
										</core:CodeListIdentifierCoded>
										<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Agency/core:CodeListIdentifierCodedOther
										</core:CodeListIdentifierCodedOther>
									</core:Agency>
									<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocID/core:Ident
									</core:Ident>
								</core:LocID>
								<core:LocationDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:LocationIdentifier/core:LocationDescription
								</core:LocationDescription>
							</core:LocationIdentifier>
							<core:GPSCoordinates>
								<core:GPSSystem>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:GPSCoordinates/core:GPSSystem
								</core:GPSSystem>
								<core:Latitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:GPSCoordinates/core:Latitude
								</core:Latitude>
								<core:Longitude>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:Location/core:GPSCoordinates/core:Longitude
								</core:Longitude>
							</core:GPSCoordinates>
						</core:Location>
						<core:TermsOfDeliveryDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:TermsOfDeliveryDescription
						</core:TermsOfDeliveryDescription>
						<core:TransportDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:TransportDescription
						</core:TransportDescription>
						<core:RiskOfLossCoded>RiskOfLossInTransitToDestinationIsBuyers
						</core:RiskOfLossCoded>
						<core:RiskOfLossCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:RiskOfLossCodedOther
						</core:RiskOfLossCodedOther>
						<core:RiskOfLossDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:TermsOfDelivery/core:RiskOfLossDescription
						</core:RiskOfLossDescription>
					</core:TermsOfDelivery>
					<core:CargoClassification>
						<core:NatureOfGoods>
							<core:Agency>
								<core:AgencyCoded>AAMVA</core:AgencyCoded>
								<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Agency/core:AgencyCodedOther
								</core:AgencyCodedOther>
								<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Agency/core:AgencyDescription
								</core:AgencyDescription>
								<core:CodeListIdentifierCoded>AcceptanceSiteCode
								</core:CodeListIdentifierCoded>
								<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Agency/core:CodeListIdentifierCodedOther
								</core:CodeListIdentifierCodedOther>
							</core:Agency>
							<core:Ident>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:CargoClassification/core:NatureOfGoods/core:Ident
							</core:Ident>
						</core:NatureOfGoods>
						<core:OperationalTypeCoded>Documents</core:OperationalTypeCoded>
						<core:OperationalTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:CargoClassification/core:OperationalTypeCodedOther
						</core:OperationalTypeCodedOther>
						<core:TypeOfCargo>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/DeliveryDetail/core:CargoClassification/core:TypeOfCargo
						</core:TypeOfCargo>
					</core:CargoClassification>
				</DeliveryDetail>
				<LineItemNote>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemNote
				</LineItemNote>
				<ListOfStructuredNote>
					<core:StructuredNote>
						<core:GeneralNote>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:GeneralNote
						</core:GeneralNote>
						<core:NoteID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:NoteID
						</core:NoteID>
						<core:Agency>
							<core:AgencyCoded>AAMVA</core:AgencyCoded>
							<core:AgencyCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:Agency/core:AgencyCodedOther
							</core:AgencyCodedOther>
							<core:AgencyDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:Agency/core:AgencyDescription
							</core:AgencyDescription>
							<core:CodeListIdentifierCoded>AcceptanceSiteCode
							</core:CodeListIdentifierCoded>
							<core:CodeListIdentifierCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:Agency/core:CodeListIdentifierCodedOther
							</core:CodeListIdentifierCodedOther>
						</core:Agency>
						<core:NoteURL>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:NoteURL
						</core:NoteURL>
						<core:TextTypeCoded>Deadlines</core:TextTypeCoded>
						<core:TextTypeCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfStructuredNote/core:StructuredNote/core:TextTypeCodedOther
						</core:TextTypeCodedOther>
					</core:StructuredNote>
				</ListOfStructuredNote>
				<ListOfNameValueSet>
					<core:NameValueSet>
						<core:SetName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfNameValueSet/core:NameValueSet/core:SetName
						</core:SetName>
						<core:SetID>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfNameValueSet/core:NameValueSet/core:SetID
						</core:SetID>
						<core:ListOfNameValuePair>
							<core:NameValuePair>
								<core:Name>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfNameValueSet/core:NameValueSet/core:ListOfNameValuePair/core:NameValuePair/core:Name
								</core:Name>
								<core:Value>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfNameValueSet/core:NameValueSet/core:ListOfNameValuePair/core:NameValuePair/core:Value
								</core:Value>
								<core:Datatype>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/ListOfNameValueSet/core:NameValueSet/core:ListOfNameValuePair/core:NameValuePair/core:Datatype
								</core:Datatype>
							</core:NameValuePair>
						</core:ListOfNameValuePair>
					</core:NameValueSet>
				</ListOfNameValueSet>
				<LineItemAttachments>
					<core:Attachment>
						<core:AttachmentActionCoded>Add</core:AttachmentActionCoded>
						<core:AttachmentActionCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:AttachmentActionCodedOther
						</core:AttachmentActionCodedOther>
						<core:AttachmentPurpose>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:AttachmentPurpose
						</core:AttachmentPurpose>
						<core:FileName>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:FileName
						</core:FileName>
						<core:AttachmentTitle>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:AttachmentTitle
						</core:AttachmentTitle>
						<core:AttachmentDescription>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:AttachmentDescription
						</core:AttachmentDescription>
						<core:Language>
							<core:LanguageCoded>aa</core:LanguageCoded>
							<core:LanguageCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:Language/core:LanguageCodedOther
							</core:LanguageCodedOther>
							<core:LocaleCoded>ae</core:LocaleCoded>
							<core:LocaleCodedOther>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:Language/core:LocaleCodedOther
							</core:LocaleCodedOther>
						</core:Language>
						<core:MIMEType>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:MIMEType
						</core:MIMEType>
						<core:AttachmentLocation>Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemAttachments/core:Attachment/core:AttachmentLocation
						</core:AttachmentLocation>
					</core:Attachment>
				</LineItemAttachments>
			</InvoiceItemDetail>
		</ListOfInvoiceItemDetail>
	</InvoiceDetail>
	<InvoiceSummary>
		<NumberOfLines>1113</NumberOfLines>
		<NumberOfOrders>1114</NumberOfOrders>
		<InvoiceTotals>
			<InvoiceTotal>
				<core:MonetaryAmount>1115</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/InvoiceTotal/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/InvoiceTotal/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/InvoiceTotal/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1116</core:RateOfExchange>
					<core:InverseRateOfExchange>1117</core:InverseRateOfExchange>
					<core:IndicativeIndicator>false</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:04
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/InvoiceTotal/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:05</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</InvoiceTotal>
			<InvoiceSubTotal>
				<core:MonetaryAmount>1118</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/InvoiceSubTotal/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/InvoiceSubTotal/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/InvoiceSubTotal/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1119</core:RateOfExchange>
					<core:InverseRateOfExchange>1120</core:InverseRateOfExchange>
					<core:IndicativeIndicator>true</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:06
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/InvoiceSubTotal/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:07</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</InvoiceSubTotal>
			<TaxableValue>
				<core:MonetaryAmount>1121</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TaxableValue/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TaxableValue/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TaxableValue/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1122</core:RateOfExchange>
					<core:InverseRateOfExchange>1123</core:InverseRateOfExchange>
					<core:IndicativeIndicator>false</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:08
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/TaxableValue/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:09</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</TaxableValue>
			<TaxableValueInTaxAccountingCurrency>
				<core:MonetaryAmount>1124</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1125</core:RateOfExchange>
					<core:InverseRateOfExchange>1126</core:InverseRateOfExchange>
					<core:IndicativeIndicator>true</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:10
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:11</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</TaxableValueInTaxAccountingCurrency>
			<ChargeTotal>
				<core:MonetaryAmount>1127</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/ChargeTotal/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/ChargeTotal/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/ChargeTotal/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1128</core:RateOfExchange>
					<core:InverseRateOfExchange>1129</core:InverseRateOfExchange>
					<core:IndicativeIndicator>false</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:12
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/ChargeTotal/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:13</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</ChargeTotal>
			<AllowanceTotal>
				<core:MonetaryAmount>1130</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/AllowanceTotal/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/AllowanceTotal/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/AllowanceTotal/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1131</core:RateOfExchange>
					<core:InverseRateOfExchange>1132</core:InverseRateOfExchange>
					<core:IndicativeIndicator>true</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:14
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/AllowanceTotal/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:15</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</AllowanceTotal>
			<TotalAmountPayable>
				<core:MonetaryAmount>1133</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountPayable/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountPayable/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountPayable/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1134</core:RateOfExchange>
					<core:InverseRateOfExchange>1135</core:InverseRateOfExchange>
					<core:IndicativeIndicator>false</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:16
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountPayable/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:17</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</TotalAmountPayable>
			<PrepaidAmount>
				<core:MonetaryAmount>1136</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/PrepaidAmount/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/PrepaidAmount/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/PrepaidAmount/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1137</core:RateOfExchange>
					<core:InverseRateOfExchange>1138</core:InverseRateOfExchange>
					<core:IndicativeIndicator>true</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:18
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/PrepaidAmount/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:19</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</PrepaidAmount>
			<TotalDiscount>
				<core:MonetaryAmount>1139</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalDiscount/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalDiscount/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalDiscount/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1140</core:RateOfExchange>
					<core:InverseRateOfExchange>1141</core:InverseRateOfExchange>
					<core:IndicativeIndicator>false</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:20
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/TotalDiscount/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:21</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</TotalDiscount>
			<TotalTaxAmount>
				<core:MonetaryAmount>1142</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalTaxAmount/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalTaxAmount/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalTaxAmount/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1143</core:RateOfExchange>
					<core:InverseRateOfExchange>1144</core:InverseRateOfExchange>
					<core:IndicativeIndicator>true</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:22
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/TotalTaxAmount/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:23</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</TotalTaxAmount>
			<TotalAmountMinusDiscount>
				<core:MonetaryAmount>1145</core:MonetaryAmount>
				<core:Currency>
					<core:CurrencyCoded>AFA</core:CurrencyCoded>
					<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountMinusDiscount/core:Currency/core:CurrencyCodedOther
					</core:CurrencyCodedOther>
				</core:Currency>
				<core:RateOfExchangeDetail>
					<core:ReferenceCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountMinusDiscount/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:ReferenceCurrency>
					<core:TargetCurrency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountMinusDiscount/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:TargetCurrency>
					<core:RateOfExchange>1146</core:RateOfExchange>
					<core:InverseRateOfExchange>1147</core:InverseRateOfExchange>
					<core:IndicativeIndicator>false</core:IndicativeIndicator>
					<core:DateOfRateOfExchange>2003-01-01T00:02:24
					</core:DateOfRateOfExchange>
					<core:ListOfRateOfExchangeReference>
						<core:Reference>
							<core:RefNum>Invoice/InvoiceSummary/InvoiceTotals/TotalAmountMinusDiscount/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
							</core:RefNum>
							<core:RefDate>2003-01-01T00:02:25</core:RefDate>
						</core:Reference>
					</core:ListOfRateOfExchangeReference>
				</core:RateOfExchangeDetail>
			</TotalAmountMinusDiscount>
		</InvoiceTotals>
		<ListOfTaxSummary>
			<core:TaxSummary>
				<core:TaxTypeCoded>85214701</core:TaxTypeCoded>
				<core:TaxTypeCodedOther>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Ident
					</core:Ident>
				</core:TaxTypeCodedOther>
				<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
				</core:TaxFunctionQualifierCoded>
				<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxFunctionQualifierCodedOther
				</core:TaxFunctionQualifierCodedOther>
				<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
				<core:TaxCategoryCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxCategoryCodedOther
				</core:TaxCategoryCodedOther>
				<core:TaxableAmount>1148</core:TaxableAmount>
				<core:TaxableAmountInTaxAccountingCurrency>1149
				</core:TaxableAmountInTaxAccountingCurrency>
				<core:TaxAmount>1150</core:TaxAmount>
				<core:TaxAmountInTaxAccountingCurrency>1151
				</core:TaxAmountInTaxAccountingCurrency>
			</core:TaxSummary>
				<core:TaxSummary>
				<core:TaxTypeCoded>*********</core:TaxTypeCoded>
				<core:TaxTypeCodedOther>
					<core:Agency>
						<core:AgencyCoded>AAMVA</core:AgencyCoded>
						<core:AgencyCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Agency/core:AgencyCodedOther
						</core:AgencyCodedOther>
						<core:AgencyDescription>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Agency/core:AgencyDescription
						</core:AgencyDescription>
						<core:CodeListIdentifierCoded>AcceptanceSiteCode
						</core:CodeListIdentifierCoded>
						<core:CodeListIdentifierCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Agency/core:CodeListIdentifierCodedOther
						</core:CodeListIdentifierCodedOther>
					</core:Agency>
					<core:Ident>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxTypeCodedOther/core:Ident
					</core:Ident>
				</core:TaxTypeCodedOther>
				<core:TaxFunctionQualifierCoded>IndividualDutyTaxOrFeeCustomsItem
				</core:TaxFunctionQualifierCoded>
				<core:TaxFunctionQualifierCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxFunctionQualifierCodedOther
				</core:TaxFunctionQualifierCodedOther>
				<core:TaxCategoryCoded>DutyPaidBySupplier</core:TaxCategoryCoded>
				<core:TaxCategoryCodedOther>Invoice/InvoiceSummary/ListOfTaxSummary/core:TaxSummary/core:TaxCategoryCodedOther
				</core:TaxCategoryCodedOther>
				<core:TaxableAmount>1148</core:TaxableAmount>
				<core:TaxableAmountInTaxAccountingCurrency>1149
				</core:TaxableAmountInTaxAccountingCurrency>
				<core:TaxAmount>1150</core:TaxAmount>
				<core:TaxAmountInTaxAccountingCurrency>1151
				</core:TaxAmountInTaxAccountingCurrency>
			</core:TaxSummary>
		</ListOfTaxSummary>
		<AllowOrChargeSummary>
			<core:TotalAllowOrCharge>
				<core:AllowOrChargeIndicatorCoded>Allowance
				</core:AllowOrChargeIndicatorCoded>
				<core:AllowOrChargeIndicatorCodedOther>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:AllowOrChargeIndicatorCodedOther
				</core:AllowOrChargeIndicatorCodedOther>
				<core:AllowanceOrChargeDescription>
					<core:RefID>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:AllowanceOrChargeDescription/core:RefID
					</core:RefID>
					<core:ListOfDescription>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:AllowanceOrChargeDescription/core:ListOfDescription
					</core:ListOfDescription>
					<core:ServiceCoded>About</core:ServiceCoded>
					<core:ServiceCodedOther>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:AllowanceOrChargeDescription/core:ServiceCodedOther
					</core:ServiceCodedOther>
				</core:AllowanceOrChargeDescription>
				<core:SummaryAllowOrCharge>
					<core:MonetaryAmount>1152</core:MonetaryAmount>
					<core:Currency>
						<core:CurrencyCoded>AFA</core:CurrencyCoded>
						<core:CurrencyCodedOther>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:SummaryAllowOrCharge/core:Currency/core:CurrencyCodedOther
						</core:CurrencyCodedOther>
					</core:Currency>
					<core:RateOfExchangeDetail>
						<core:ReferenceCurrency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:SummaryAllowOrCharge/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:ReferenceCurrency>
						<core:TargetCurrency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:SummaryAllowOrCharge/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:TargetCurrency>
						<core:RateOfExchange>1153</core:RateOfExchange>
						<core:InverseRateOfExchange>1154</core:InverseRateOfExchange>
						<core:IndicativeIndicator>true</core:IndicativeIndicator>
						<core:DateOfRateOfExchange>2003-01-01T00:02:26
						</core:DateOfRateOfExchange>
						<core:ListOfRateOfExchangeReference>
							<core:Reference>
								<core:RefNum>Invoice/InvoiceSummary/AllowOrChargeSummary/core:TotalAllowOrCharge/core:SummaryAllowOrCharge/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
								</core:RefNum>
								<core:RefDate>2003-01-01T00:02:27</core:RefDate>
							</core:Reference>
						</core:ListOfRateOfExchangeReference>
					</core:RateOfExchangeDetail>
				</core:SummaryAllowOrCharge>
			</core:TotalAllowOrCharge>
		</AllowOrChargeSummary>
		<ListOfActualPayment>
			<core:ActualPayment>
				<core:PaymentAmount>
					<core:InvoiceCurrencyAmt>
						<core:MonetaryAmount>1155</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:InvoiceCurrencyAmt/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:InvoiceCurrencyAmt/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:InvoiceCurrencyAmt/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1156</core:RateOfExchange>
							<core:InverseRateOfExchange>1157</core:InverseRateOfExchange>
							<core:IndicativeIndicator>false</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:02:28
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:InvoiceCurrencyAmt/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:02:29</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</core:InvoiceCurrencyAmt>
					<core:ForeignCurrencyPayment>
						<core:MonetaryAmount>1158</core:MonetaryAmount>
						<core:Currency>
							<core:CurrencyCoded>AFA</core:CurrencyCoded>
							<core:CurrencyCodedOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:ForeignCurrencyPayment/core:Currency/core:CurrencyCodedOther
							</core:CurrencyCodedOther>
						</core:Currency>
						<core:RateOfExchangeDetail>
							<core:ReferenceCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:ForeignCurrencyPayment/core:RateOfExchangeDetail/core:ReferenceCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:ReferenceCurrency>
							<core:TargetCurrency>
								<core:CurrencyCoded>AFA</core:CurrencyCoded>
								<core:CurrencyCodedOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:ForeignCurrencyPayment/core:RateOfExchangeDetail/core:TargetCurrency/core:CurrencyCodedOther
								</core:CurrencyCodedOther>
							</core:TargetCurrency>
							<core:RateOfExchange>1159</core:RateOfExchange>
							<core:InverseRateOfExchange>1160</core:InverseRateOfExchange>
							<core:IndicativeIndicator>true</core:IndicativeIndicator>
							<core:DateOfRateOfExchange>2003-01-01T00:02:30
							</core:DateOfRateOfExchange>
							<core:ListOfRateOfExchangeReference>
								<core:Reference>
									<core:RefNum>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentAmount/core:ForeignCurrencyPayment/core:RateOfExchangeDetail/core:ListOfRateOfExchangeReference/core:Reference/core:RefNum
									</core:RefNum>
									<core:RefDate>2003-01-01T00:02:31</core:RefDate>
								</core:Reference>
							</core:ListOfRateOfExchangeReference>
						</core:RateOfExchangeDetail>
					</core:ForeignCurrencyPayment>
				</core:PaymentAmount>
				<core:PaymentDate>2003-01-01T00:02:32</core:PaymentDate>
				<core:PaymentMean>
					<core:PaymentMeanCoded>1035Exchange</core:PaymentMeanCoded>
					<core:PaymentCodedMeanOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentMean/core:PaymentCodedMeanOther
					</core:PaymentCodedMeanOther>
				</core:PaymentMean>
				<core:PaymentRecord>
					<core:PaymentRef>
						<core:RefNum>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentRecord/core:PaymentRef/core:RefNum
						</core:RefNum>
						<core:RefDate>2003-01-01T00:02:33</core:RefDate>
					</core:PaymentRef>
					<core:CardInfo>
						<core:CardNum>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentRecord/core:CardInfo/core:CardNum
						</core:CardNum>
						<core:CardAuthCode>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentRecord/core:CardInfo/core:CardAuthCode
						</core:CardAuthCode>
						<core:CardRefNum>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentRecord/core:CardInfo/core:CardRefNum
						</core:CardRefNum>
						<core:CardExpirationDate>2003-01-01T00:02:34
						</core:CardExpirationDate>
						<core:CardType>AMEX</core:CardType>
						<core:CardTypeOther>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentRecord/core:CardInfo/core:CardTypeOther
						</core:CardTypeOther>
						<core:CardHolderName>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:PaymentRecord/core:CardInfo/core:CardHolderName
						</core:CardHolderName>
					</core:CardInfo>
				</core:PaymentRecord>
				<core:OtherPaymentInfo>Invoice/InvoiceSummary/ListOfActualPayment/core:ActualPayment/core:OtherPaymentInfo
				</core:OtherPaymentInfo>
			</core:ActualPayment>
		</ListOfActualPayment>
		<SummaryNote>Invoice/InvoiceSummary/SummaryNote</SummaryNote>
	</InvoiceSummary>
</Invoice>