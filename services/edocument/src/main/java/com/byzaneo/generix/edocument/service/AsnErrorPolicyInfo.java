package com.byzaneo.generix.edocument.service;

import org.slf4j.Logger;

import com.byzaneo.generix.edocument.exception.asn.AsnProcessException;

public class AsnErrorPolicyInfo {
  public final AsnProcessException asnProcessException;

  public final Logger logger;

  public final AsnErrorPolicy.Level level;

  public AsnErrorPolicyInfo(AsnProcessException asnProcessException, Logger logger, AsnErrorPolicy.Level level) {
    this.asnProcessException = asnProcessException;
    this.logger = logger;
    this.level = level;
  }
}
