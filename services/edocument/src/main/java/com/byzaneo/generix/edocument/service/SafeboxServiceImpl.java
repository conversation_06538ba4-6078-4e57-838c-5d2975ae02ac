package com.byzaneo.generix.edocument.service;

import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.s3.transfer.model.UploadResult;
import com.byzaneo.amazon.service.*;
import com.byzaneo.amazon.utils.AmazonArchivingHelper;
import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.EndpointConfiguration;
import com.byzaneo.generix.edocument.SafeboxException;
import com.byzaneo.generix.edocument.archive.metadata.*;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.exception.*;
import com.byzaneo.generix.edocument.util.SipHelper;
import com.byzaneo.generix.service.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.*;
import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;
import java.io.*;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;

import static com.byzaneo.amazon.utils.AmazonArchivingHelper.getPathFromKey;
import static com.byzaneo.generix.ui.MessageDialogHandler.getCurrentDate;
import static org.apache.commons.lang3.StringUtils.isEmpty;

@Service(SafeboxService.SERVICE_NAME)
public class SafeboxServiceImpl implements SafeboxService {

  private static final Logger log = LoggerFactory.getLogger(SafeboxServiceImpl.class);

  // SERVICES
  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  @Qualifier(RoutingService.SERVICE_NAME)
  private transient RoutingService routingService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private transient DocumentService documentService;

  @Autowired
  private transient ConfigurationService configService;

  @Autowired(required = false)
  @Qualifier(AmazonS3ServiceImpl.SERVICE_NAME)
  protected transient S3Service s3Service;

  @Autowired(required = true)
  @Qualifier(S3ResolverService.SERVICE_NAME)
  private transient S3ResolverService s3ResolverService;

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public void init() {
    final long start = System.currentTimeMillis();
    log.info("STARTING SAFEBOX SERVICE...");
    log.info("SAFEBOX SERVICE STARTED in {}ms.", System.currentTimeMillis() - start);
  }

  @Override
  @PreDestroy
  public void destroy() {
    final long start = System.currentTimeMillis();
    log.info("STOPPING SAFEBOX SERVICE...");
    log.info("SAFEBOX SERVICE STOPPED in {}ms.", System.currentTimeMillis() - start);
  }

  /*
   * -- PRIVATE --
   */

  private EndpointConfiguration resolveChannel(Document doc) {
    String channelName = doc.getNotNullModel()
        .getPropertyValue("channelId");
    if (isEmpty(channelName))
      throw new SafeboxException("channel id can not be empty");

    Organization org = this.securityService.getOrganizationByCode(doc.getOwners(), doc.getOwners());
    if (org == null)
      throw new SafeboxException("channel owner not found for document's owner: %s", doc.getOwners());

    final EndpointConfiguration channel = this.routingService.getEndpointConfiguration(org, channelName, null);
    if (channel == null)
      throw new SafeboxException("Unable to resolve channel with name=%s and group=%s", channelName, org);
    return channel;
  }

  @Override
  public void populateArchiveWithIndexMetadata(Document archive, File indexMetadata)
      throws ArchiveFileNotFoundException {
    try {
      IoAIP archiveIndexfile = JAXBHelper.unmarshal(IoAIP.class, indexMetadata);
      List<FileGroup> fileGroups = archiveIndexfile.getFileGroup();
      for (FileGroup group : fileGroups) {
        if (String.valueOf(archive.getId())
            .equals(group.getID())) {
          long dofId = 0l;
          for (com.byzaneo.generix.edocument.archive.metadata.FileType type : group.getFile()) {
            archive.addFile(createDocumentFileFromFileType(indexMetadata.getParentFile(), type, dofId++));
          }
        }
      }
      archive.addFile(new DocumentFile(indexMetadata, FileType.XML, "", "", archive, "Archive index"));
    }
    catch (IOException | JAXBException | XMLStreamException e) {
      throw new SafeboxException("Failed to populate archive document", e);
    }
  }

  protected void updateArchiveDocWithIndexMetadata(Document archiveDoc, File indexFile) {
    try {
      populateArchiveWithIndexMetadata(archiveDoc, indexFile);
    }
    catch (ArchiveFileNotFoundException e) {
      throw new SafeboxException("Failed to update archive document", e);
    }
  }

  private List<DocumentLink> getArchiveLinkFromFolder(File archiveFolder, Document archive) {
    if (archiveFolder != null && archiveFolder.isDirectory()) {
      List<DocumentLink> documentLinks = new ArrayList<>();
      File linkFile = new File(archiveFolder,
          "ARC_" + archive.getNotNullModel()
              .getPropertyValue("archive_id") + File.separator +
              "LINKS_" + archive.getNotNullModel()
                  .getPropertyValue("archive_id")
                  .replaceFirst("(.)*_", "") +
              ".xml");
      if (linkFile.exists() && linkFile.isFile()) {
        documentLinks = getDocumentLinkFromFile(linkFile);
      }
      return documentLinks;
    }
    return Collections.emptyList();
  }

  private List<DocumentLink> getDocumentLinkFromFile(File links) {
    try {
      List<Link> linkList = JAXBHelper.unmarshal(Links.class, links)
          .getDocumentLinks();
      List<DocumentLink> documentLinks = new ArrayList<>();
      linkList.forEach(link -> {
        DocumentLink docLink = new DocumentLink(link.getSource(), link.getTarget());
        docLink.setId(link.getSource() + "_" + link.getTarget());
        documentLinks.add(docLink);
      });
      return documentLinks;
    }
    catch (IOException | JAXBException | XMLStreamException e) {
      throw new SafeboxException("Failed to get document link from file", e);
    }
  }

  @Override
  public Document getArchiveFromS3(Archive archive, Group group) throws IOException, ArchiveDownloadFailedException {
    this.s3Service = this.s3ResolverService.initS3Service(group);
    if (s3Service == null || !s3Service.isS3ArchiveServiceAvailable()) {
      log.error("Unavailable Archive Service");
      throw new SafeboxException("edctsklbls.unavailable_archive_service");
    }

    String retrieveFolder = getSafeboxRetrievePath();
    if (StringUtils.isBlank(retrieveFolder)) {
      log.error("Archiving destination file not configured");
      throw new SafeboxException("edctsklbls.archiving_destination_file_not_configured");
    }
    if (!Paths.get(retrieveFolder)
        .toFile()
        .exists()) {
      Paths.get(retrieveFolder)
          .toFile()
          .mkdirs();
    }
    return getArchive(archive, group, retrieveFolder);
  }

  @Override
  public String getSafeboxRetrievePath() {
    File dataDir = configService.getFile(ConfigurationKey.DATA_DIR, FileHelper.getTempDirectory());
    return Paths.get(dataDir.getAbsolutePath(), SAFEBOX_RETRIEVE_DIRECTORY)
        .toString();
  }

  protected String getArchiveId(String archiveFolder) {
    Matcher matcher = ARCHIVE_FOLDER_PATTERN.matcher(archiveFolder);
    if (matcher.matches()) {
      return matcher.group(3);
    }
    return archiveFolder;
  }

  @Override
  public File getDailyRecapFromS3(DailyRecapListItem dailyRecapListItem, Group group) throws IOException, ArchiveDownloadFailedException {
    this.s3Service = this.s3ResolverService.initS3Service(group);
    if (s3Service == null || !s3Service.isS3ArchiveServiceAvailable()) {
      log.error("S3 storage not configured, ask to the administrator");
      throw new AmazonS3Exception("S3 storage not configured, ask to the administrator");
    }

    String retrieveFolder = getRecapsRetrievePath();
    if (StringUtils.isBlank(retrieveFolder)) {
      log.error("Destination file not configured");
      throw new AmazonS3Exception("S3 storage not configured, ask to the administrator");
    }
    if (!Paths.get(retrieveFolder)
        .toFile()
        .exists()) {
      Paths.get(retrieveFolder)
          .toFile()
          .mkdirs();
    }
    return getDailyRecap(dailyRecapListItem, group, retrieveFolder);
  }

  @Override
  public File getDailyDematFromS3(DailyExchangeListItem dailyExchangeListItem, Group group) throws IOException, ArchiveDownloadFailedException {
    this.s3Service = this.s3ResolverService.initS3Service(group);
    if (s3Service == null || !s3Service.isS3ArchiveServiceAvailable()) {
      log.error("S3 storage not configured, ask to the administrator");
      throw new AmazonS3Exception("S3 storage not configured, ask to the administrator");
    }

    String retrieveFolder = getDematsRetrievePath();
    if (StringUtils.isBlank(retrieveFolder)) {
      log.error("Destination file not configured");
      throw new AmazonS3Exception("S3 storage not configured, ask to the administrator");
    }
    if (!Paths.get(retrieveFolder)
            .toFile()
            .exists()) {
      Paths.get(retrieveFolder)
              .toFile()
              .mkdirs();
    }
    return getDailyDemat(dailyExchangeListItem, group, retrieveFolder);
  }

  public String getRecapsRetrievePath() {
    File dataDir = configService.getFile(ConfigurationKey.DATA_DIR, FileHelper.getTempDirectory());
    return Paths.get(dataDir.getAbsolutePath(), RECAP_RETRIEVE_DIRECTORY)
        .toString();
  }

  public String getDematsRetrievePath() {
    File dataDir = configService.getFile(ConfigurationKey.DATA_DIR, FileHelper.getTempDirectory());
    return Paths.get(dataDir.getAbsolutePath(), DEMAT_RETRIEVE_DIRECTORY)
            .toString();
  }

  protected Path downloadAndUnzipArchive(String retrieveFolder, String archiveUid, Group group)
      throws IOException, ArchiveDownloadFailedException {
    Path archiveZip = downloadArchiveOnS3(retrieveFolder, archiveUid, group);
    return AmazonArchivingHelper.unzipArchiveZip(archiveZip);
  }

  protected Path downloadArchiveOnS3(String retrieveFolder, String archiveUid, Group group)
      throws IOException, ArchiveDownloadFailedException {
    this.s3Service = this.s3ResolverService.initS3Service(group);
    Path archiveZipPath = Paths.get(retrieveFolder, getPathFromKey(archiveUid));
    s3Service.downloadArchive(archiveUid, archiveZipPath.toString());
    if (archiveZipPath.toFile()
        .exists() &&
        archiveZipPath.toFile()
            .isFile()) {
      return archiveZipPath;
    }
    throw new ArchiveDownloadFailedException("Failed to download archive %s", archiveUid);
  }

  protected DocumentFile createDocumentFileFromFileType(File rootFolder, com.byzaneo.generix.edocument.archive.metadata.FileType fileType,
      long dofId) throws ArchiveFileNotFoundException {
    DocumentFile dof = new DocumentFile();
    File file = new File(rootFolder, fileType.getURI());
    if (!file.exists()) {
      throw new ArchiveFileNotFoundException("Failed to find file %s", fileType.getURI());
    }
    dof.setId(dofId);
    dof.setFile(file);
    if (CollectionUtils.isNotEmpty(fileType.getExtraInformation())) {
      for (ExtraInformationType info : fileType.getExtraInformation()) {
        if ("Action name".equals(info.getXMLSchema())) {
          dof.setActionName(info.getExtraInformationDescription());
        }
        if ("Description".equals(info.getXMLSchema())) {
          dof.setDescription(info.getExtraInformationDescription());
        }
        if ("File type".equals(info.getXMLSchema())) {
          dof.setType(FileType.valueOf(info.getExtraInformationDescription()));
        }
        if ("Comment".equals(info.getXMLSchema())) {
          dof.setComment(info.getExtraInformationDescription());
        }
      }

    }
    return dof;
  }

  @Override
  public Map<Archive, Document> getArchivesFromS3(List<ArchivedInvoice> archives, Group group)
      throws IOException, ArchiveDownloadFailedException {
    this.s3Service = this.s3ResolverService.initS3Service(group);
    if (s3Service == null || !s3Service.isS3ArchiveServiceAvailable()) {
      log.error("Unavailable Archive Service");
      throw new SafeboxException("edctsklbls.unavailable_archive_service");
    }

    String retrieveFolder = getSafeboxRetrievePath();
    if (StringUtils.isBlank(retrieveFolder)) {
      log.error("Archiving destination file not configured");
      throw new SafeboxException("edctsklbls.archiving_destination_file_not_configured");
    }
    if (!Paths.get(retrieveFolder)
        .toFile()
        .exists()) {
      Paths.get(retrieveFolder)
          .toFile()
          .mkdirs();
    }

    Map<Archive, Document> archiveDocs = new HashMap<Archive, Document>();
    for (Archive archive : archives) {
      // in case that the archive cannot be found we continue to search for the others
      try {
        archiveDocs.put(archive, getArchive(archive, group, retrieveFolder));
      }
      catch (Exception e) {
        log.error(e.getMessage());
      }
    }
    return archiveDocs;
  }

  private Document getArchive(Archive archive, Group group, String retrieveFolder) throws IOException, ArchiveDownloadFailedException {
    Document archiveDoc = new Document();
    BeanUtils.copyProperties(archive, archiveDoc);
    archiveDoc.setId(Long.valueOf(archive.getEntityId()
        .toString()));
    archiveDoc.setIndexValue(archive);

    Path archiveFolder = Paths.get(retrieveFolder, FilenameUtils.removeExtension(getPathFromKey(archive.getArchiveUid())));
    String archiveId;
    File indexFile = null;
    try {
      if (!archiveFolder.toFile()
          .exists()) {
        archiveFolder = downloadAndUnzipArchive(retrieveFolder, archive.getArchiveUid(), group);
      }
      archiveId = getArchiveId(archiveFolder.toFile()
          .getName());
      indexFile = new File(archiveFolder.toFile(), SipHelper.createIndexFileName(archiveId));
      if (!indexFile.exists()) {
        // index file name got changed from 'archiveId' to Sip_index_'archiveId'
        // we look for the old file name for backward compability
        indexFile = new File(archiveFolder.toFile(), archiveId + ".xml");
        if (!indexFile.exists()) {
          throw new ArchiveFileNotFoundException("Failed to get archive index file", archiveId);
        }
      }
      populateArchiveWithIndexMetadata(archiveDoc, indexFile);
    }
    catch (ArchiveFileNotFoundException e) {
      log.error("Failed to construct archive document", e);
      FileUtils.deleteQuietly(archiveFolder.toFile());
      archiveDoc.getFiles()
          .clear();

      // Retry
      log.debug("Trying to retreive archive on S3...");
      archiveFolder = downloadAndUnzipArchive(retrieveFolder, archive.getArchiveUid(), group);
      updateArchiveDocWithIndexMetadata(archiveDoc, indexFile);
    }
    return archiveDoc;
  }

  private File getDailyRecap(DailyRecapListItem dailyRecapListItem, Group group, String retrieveFolder)
      throws ArchiveDownloadFailedException, IOException {
    Path recapFolder = Paths.get(retrieveFolder, FilenameUtils.removeExtension(dailyRecapListItem.getS3Path()));
    if (!recapFolder.toFile()
        .exists()) {
      recapFolder = downloadArchiveOnS3(retrieveFolder, dailyRecapListItem.getS3Path(), group);
    }
    return recapFolder.toFile();
  }

  private File getDailyDemat(DailyExchangeListItem dailyExchangeListItem, Group group, String retrieveFolder)
          throws ArchiveDownloadFailedException, IOException {
    Path dematFolder = Paths.get(retrieveFolder, FilenameUtils.removeExtension(dailyExchangeListItem.getS3Path()));
    if (!dematFolder.toFile()
            .exists()) {
      dematFolder = downloadArchiveOnS3(retrieveFolder, dailyExchangeListItem.getS3Path(), group);
    }
    return dematFolder.toFile();
  }

  public String getTags(String legalEntity, String processingway, String date) {
    return legalEntity + File.separator + processingway + File.separator + date;
  }

  public void storageToS3(String entity, String directionAsString, File fileToStore, String date, Organization org, SimpleDateFormat retainUntilDateFormatter, String fileType) {
    this.s3Service = s3ResolverService.initS3Service(org);
    try {
      if (s3Service == null || !s3Service.isS3ArchiveServiceAvailable()) {
        throw new AmazonS3Exception("S3 storage not configured, ask to the administrator");
      }
      Map<String, String> arguments = new HashMap<>();
      arguments.put(AbstractS3Service.RETAIN_UNTIL_DATE, retainUntilDateFormatter.format(DateUtils.addYears(getCurrentDate(), 10)));
      List<Tag> tags = initTags(entity, directionAsString, date.substring(0, 4), date.substring(4, 6), date.substring(6, 8), fileType);
      String zipFileKey = uploadFile(entity, fileType, arguments, fileToStore, tags);
      if (zipFileKey == null) {
        throw new AmazonS3Exception("Failed to load the document");
      }
    }
    catch (AmazonS3Exception e) {
      throw e;
    }
  }

  public String uploadFile(String documentFromOrTOValue, String documentType, Map<String, String> arguments, File fileToUpload,
      List<Tag> tags) {
    if (fileToUpload == null || !fileToUpload.exists()) {
      log.error("The file was not found {}", fileToUpload);
      return null;
    }
    String fileName = FilenameUtils.getName(fileToUpload
        .getPath());

    org.springframework.util.Assert.hasText(documentFromOrTOValue, "Entity is required");
    Assert.hasText(documentType, "Document type is required");
    String fileKey = String.join("/", documentFromOrTOValue, documentType, fileName);

    UploadResult fileReportResult = s3Service.uploadArchive(fileKey, fileToUpload, arguments, tags);
    if (fileReportResult == null) {
      log.error("Failed to store the file {}", fileToUpload);
      return null;
    }
    return fileKey;
  }

  public List<Tag> initTags(String entity, String processingWay, String year, String month, String day, String documentType) {
    List<Tag> tags = new ArrayList<>();
    tags.add(new Tag("gnx:entity", entity));
    tags.add(new Tag("gnx:processing way", processingWay));
    tags.add(new Tag("gnx:year", year));
    tags.add(new Tag("gnx:month", month));
    tags.add(new Tag("gnx:day", day));
    tags.add(new Tag("gnx:document type", documentType));
    return tags;
  }
}
