package com.byzaneo.generix.edocument.validation.bean;

public enum ConstraintType {
  FR,
  EU,
  FR_B2B,
  FR_B2G,
  FR_B2G_OrderReference,
  FR_B2G_BuyerReference,
  FR_B2G_BuyerAndOrderReference,
  FR_B2G_BuyerOrOrderReference,
  PROP;

  public static ConstraintType getConstraintType(String name, ConstraintType defaultValue) {
    for (ConstraintType constraintType : values()) {
      if (constraintType.toString()
          .equals(name)) {
        return constraintType;
      }
    }
    return defaultValue;
  }

}