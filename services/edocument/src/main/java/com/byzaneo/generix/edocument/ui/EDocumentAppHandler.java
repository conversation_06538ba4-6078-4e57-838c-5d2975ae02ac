package com.byzaneo.generix.edocument.ui;

import com.byzaneo.generix.edocument.listener.FieldEventEntry;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.Indexer;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.XcblIndexer;

import javax.annotation.PostConstruct;
import javax.faces.bean.ApplicationScoped;
import javax.faces.bean.ManagedBean;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;

import org.apache.cassandra.thrift.IndexType;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.generix.edocument.service.EDocumentService.EDocument.getCollections;
import static com.byzaneo.generix.edocument.service.EDocumentService.EDocument.valueOfIgnoringCase;
import static java.util.Arrays.asList;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Dec 18, 2014
 * @since 3.0 GNX-1740
 */
@ManagedBean(name = EDocumentAppHandler.MANAGED_BEAN_NAME)
@ApplicationScoped
public class EDocumentAppHandler implements Serializable {
  private static final long serialVersionUID = -225299731444123086L;

  public static final String MANAGED_BEAN_NAME = "gnxEDocumentAppHandler";

  // SERVICES
  private transient EDocumentService eDocumentService;

  private transient DocumentService documentService;

  private List<SelectItem> indexers;

  private List<SelectItem> archiveIndexable;

  @PostConstruct
  public void init() {
    eDocumentService = getSpringBean(EDocumentService.class, EDocumentService.SERVICE_NAME);
    documentService = getSpringBean(DocumentService.class, DocumentService.SERVICE_NAME);
  }

  /*
   * -- RESTITUTION --
   */

  public List<String> onCompleteDocumentTypes(String query) {
    return this.eDocumentService.searchDocumentTypes(query);
  }

  public String getDocumentTypeGroup(String type) {
    return valueOfIgnoringCase(type) != null
        ? getLabel("labels", "edocument", "E-Document", null)
        : getLabel("labels", "other", "Others", null);
  }

  public List<String> onCompleteDocumentSubTypes(String query) {
    return this.eDocumentService.searchDocumentSubTypes(query);
  }

  public List<String> onCompleteCollections(String query) {
    return this.eDocumentService.searchCollectionNames(query);
  }

  public String getCollectionGroup(String collection) {
    return getCollections().contains(collection)
        ? getLabel("labels", "edocument", "E-Document", null)
        : getLabel("labels", "other", "Others", null);
  }

  /*
   * -- INDEXER --
   */

  public List<SelectItem> getIndexers() {
    if (indexers == null) {
      final Collection<Indexer<?>> listOfIndexers = this.documentService.getIndexers()
          .values();
      final SelectItemGroup xcbl = new SelectItemGroup("XCBL");

      xcbl.setSelectItems(listOfIndexers.stream()
          .filter(indexer -> indexer instanceof XcblIndexer<?>)
          .map(indexer -> new SelectItem(indexer.getName(), indexer.getType()
              .getSimpleName()))
          .toArray(size -> new SelectItem[size]));

      final SelectItemGroup others = new SelectItemGroup("OTHER");
      others.setSelectItems(listOfIndexers.stream()
          .filter(indexer -> !(indexer instanceof XcblIndexer<?>))
          .map(indexer -> new SelectItem(indexer.getName(), indexer.getType()
              .getSimpleName()))
          .toArray(size -> new SelectItem[size]));
      indexers = asList(xcbl, others);
    }
    return indexers;
  }

  /*
   * -- ARCHIVE INDEXABLES --
   */

  public List<SelectItem> getArchiveIndexables() {
    if (archiveIndexable == null) {
      final Collection<Indexer<?>> listOfIndexers = this.documentService.getIndexers()
          .values();
      final SelectItemGroup indexables = new SelectItemGroup("INDEXABLES");
      indexables.setSelectItems(listOfIndexers.stream()
          .filter(indexer -> !"Invoice".equals(indexer.getType()
              .getSimpleName()))
          .filter(indexer -> indexableHasArchiveBeanDescriptor(indexer))
          .map(indexer -> new SelectItem(indexer.getName(), indexer.getType()
              .getSimpleName()))
          .toArray(size -> new SelectItem[size]));
      archiveIndexable = asList(indexables);
    }
    return archiveIndexable;
  }

  private boolean indexableHasArchiveBeanDescriptor(Indexer<?> indexer) {
    try {
      String archiveBeanDescriptor = indexer.getDescriptorPath(Indexer.IndexType.ARCHIVE);
      return StringUtils.hasText(archiveBeanDescriptor);
    }
    catch (UnsupportedOperationException e) {
      return false;
    }
  }

  /*
   * -- LISTENERS --
   */

  public List<FieldEventEntry> getStatusHistory(Indexable indexeable) {
    return new ArrayList<>(this.eDocumentService.searchStatusEvents(indexeable));
  }
}
