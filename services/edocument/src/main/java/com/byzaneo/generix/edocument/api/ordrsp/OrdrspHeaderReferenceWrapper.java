package com.byzaneo.generix.edocument.api.ordrsp;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.byzaneo.commons.api.BeanDescriptorWrapper;
import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.generix.edocument.bean.xcbl.CsvHeaderReference;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfReferenceCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceType;

@Component("xcblOrdrspHeaderReference")
public class OrdrspHeaderReferenceWrapper extends BeanDescriptorWrapper<CsvHeaderReference> {

  @Autowired
  protected BeanService beanService;

  /**
   * {@link ReferenceCodedType#referenceTypeCoded RFF+AIJ reference}
   */
  private static final String TRANSACTION_REFERENCE_NUMBER = "TransactionReferenceNumber";

  @Override
  protected BeanDescriptor createBeanDescriptor() {
    try {
      return beanService.fromClasspath("com/byzaneo/generix/edocument/bean/ordrsp/ordrspHeaderReferenceDescriptor.xml");
    }
    catch (IOException ex) {
      throw new RuntimeException(ex);
    }
  }

  @Override
  public Iterable<CsvHeaderReference> getBeans(Object owner) {
    return null;
  }

  @Override
  public CsvHeaderReference getBean() {
    return new CsvHeaderReference();
  }

  @Override
  public void validate(CsvHeaderReference bean, BeanImportContext context) throws PropertiesException {
    Map<String, Exception> exceptions = new HashMap<>();
    OrderResponse ordrsp = (OrderResponse) CsvImportHelper.getImportedIndexable();

    if (ordrsp == null) {
      exceptions.put("header", new ServiceException("labels.import-xcbl.unknown_header.", "No header ", null));
      bean = null;
      throw new PropertiesException(exceptions);
    }

    switch (bean.getReferenceType()) {
    case "ON":
      ordrsp.getOrderResponseHeader()
          .setOrderReference(new ReferenceType());
      ordrsp.getOrderResponseHeader()
          .getOrderReference()
          .setRefNum(bean.getReferenceNumber());
      ordrsp.getOrderResponseHeader()
          .getOrderReference()
          .setRefDate(bean.getDateReference());
      break;
    case "AIJ":
      ReferenceCodedType referenceCodedType = new ReferenceCodedType();
      referenceCodedType.setPrimaryReference(new ReferenceType());
      referenceCodedType.getPrimaryReference()
          .setRefNum(bean.getReferenceNumber());
      referenceCodedType.getPrimaryReference()
          .setRefDate(bean.getDateReference());
      referenceCodedType.setReferenceTypeCoded(TRANSACTION_REFERENCE_NUMBER);
      if (ordrsp.getOrderResponseHeader()
          .getListOfReferenceCoded() == null) {
        ListOfReferenceCodedType listOfReferenceCodedType = new ListOfReferenceCodedType();
        ordrsp.getOrderResponseHeader()
            .setListOfReferenceCoded(listOfReferenceCodedType);
      }
      ordrsp.getOrderResponseHeader()
          .getListOfReferenceCoded()
          .getReferenceCoded()
          .add(referenceCodedType);
      break;
    default:
      exceptions.put("referenceType",
          new ServiceException("labels.import-xcbl.unknown_reference_type.", "Unknown reference type %s", null, bean.getReferenceType()));
    }

    if (!exceptions.isEmpty()) {
      ordrsp.setReference("-1");
      throw new PropertiesException(exceptions);
    }
  }

  @Override
  public boolean persist(CsvHeaderReference bean) {
    return false;
  }

}
