package com.byzaneo.generix.edocument.util;

import com.byzaneo.generix.service.repository.bean.*;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import org.apache.commons.collections4.CollectionUtils;

import java.math.*;
import java.util.*;
import java.util.function.Function;

import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.applyInvoiceAllowanceOrChargeOnDetail;
import static com.byzaneo.generix.edocument.util.InvoicePriceXcblHelper.buildInvoicePriceType;
import static com.byzaneo.generix.edocument.util.InvoicePriceXcblHelper.createInvoicePriceType;
import static com.byzaneo.generix.edocument.util.InvoicePriceXcblHelper.getPricetypeCoded;
import static com.byzaneo.generix.edocument.util.InvoicePriceXcblHelper.getUnitPriceValue;
import static com.byzaneo.generix.edocument.util.InvoiceTaxSummaryXcblHelper.populateInvoiceTaxSummary;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getDetails;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.generix.edocument.util.XcblHelper.BigDecimal_CENT;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_GROSS;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_NET;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.NET_ITEM_PRICE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.UNIT_COST_PRICE;
import static java.math.RoundingMode.HALF_EVEN;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Stream.empty;

public class InvoiceItemDetailXcblHelper {

  /*
   * -- FUNCTION --
   */

  private static final Function<InvoiceItemDetailType, InvoiceBaseItemDetailType> INVOICE_BASE_ITEM_DETAIL_TYPE = FunctionXcblHelper.orSet(
      InvoiceItemDetailType::getInvoiceBaseItemDetail,
      InvoiceItemDetailType::setInvoiceBaseItemDetail,
      InvoiceBaseItemDetailType::new);

  private static final Function<InvoiceBaseItemDetailType, LineItemNumType> INVOICE_LINE_ITEM_NUM_TYPE = FunctionXcblHelper.orSet(
      InvoiceBaseItemDetailType::getLineItemNum,
      InvoiceBaseItemDetailType::setLineItemNum,
      LineItemNumType::new);

  private static final Function<InvoiceBaseItemDetailType, InvoiceItemIdentifiersType> INVOICE_ITEM_IDENTIFIERS_TYPE = FunctionXcblHelper
      .orSet(
          InvoiceBaseItemDetailType::getItemIdentifiers,
          InvoiceBaseItemDetailType::setItemIdentifiers,
          InvoiceItemIdentifiersType::new);

  public static final Function<InvoiceItemIdentifiersType, ComplexStringType> ITEM_DESCRIPTION = FunctionXcblHelper
      .orSet(
          InvoiceItemIdentifiersType::getItemDescription,
          InvoiceItemIdentifiersType::setItemDescription,
          ComplexStringType::new);

  public static final Function<InvoiceItemIdentifiersType, InvoicePartNumbersType> PART_NUMBERS_TYPE = FunctionXcblHelper.orSet(
      InvoiceItemIdentifiersType::getPartNumbers,
      InvoiceItemIdentifiersType::setPartNumbers,
      InvoicePartNumbersType::new);

  public static final Function<InvoicePartNumbersType, InvoicePartNumType> BUYER_PART_NUM_TYPE = FunctionXcblHelper.orSet(
      InvoicePartNumbersType::getBuyerPartNumber,
      InvoicePartNumbersType::setBuyerPartNumber,
      InvoicePartNumType::new);

  public static final Function<InvoiceProductIdentifierCodedType, ComplexStringType> STANDARD_PART_NUMBER = FunctionXcblHelper.orSet(
      InvoiceProductIdentifierCodedType::getProductIdentifier,
      InvoiceProductIdentifierCodedType::setProductIdentifier,
      ComplexStringType::new);

  public static final Function<InvoicePartNumType, ComplexStringType> PARTID = FunctionXcblHelper.orSet(
      InvoicePartNumType::getPartID,
      InvoicePartNumType::setPartID,
      ComplexStringType::new);

  public static final Function<InvoicePartNumbersType, InvoicePartNumType> SELLER_PART_NUM_TYPE = FunctionXcblHelper.orSet(
      InvoicePartNumbersType::getSellerPartNumber,
      InvoicePartNumbersType::setSellerPartNumber,
      InvoicePartNumType::new);

  public static final Function<InvoicePartNumbersType, InvoiceProductIdentifierCodedType> PRODUCT_ID_CODED_TYPE = FunctionXcblHelper.orSet(
      InvoicePartNumbersType::getStandardPartNumber,
      InvoicePartNumbersType::setStandardPartNumber,
      InvoiceProductIdentifierCodedType::new);

  private static final Function<InvoiceBaseItemDetailType, InvoiceQuantityType> INVOICE_QUANTITY_TYPE = FunctionXcblHelper.orSet(
      InvoiceBaseItemDetailType::getInvoicedQuantity,
      InvoiceBaseItemDetailType::setInvoicedQuantity,
      InvoiceQuantityType::new);

  private static final Function<InvoiceQuantityType, QuantityValueType> QUANTITY_VALUE_TYPE = FunctionXcblHelper.orSet(
      InvoiceQuantityType::getQuantityValue,
      InvoiceQuantityType::setQuantityValue,
      QuantityValueType::new);

  private static final Function<InvoiceQuantityType, InvoiceUnitOfMeasurementType> UNIT_OF_MEASUREMENT_TYPE = FunctionXcblHelper.orSet(
      InvoiceQuantityType::getUnitOfMeasurement,
      InvoiceQuantityType::setUnitOfMeasurement,
      InvoiceUnitOfMeasurementType::new);

  private static final Function<InvoiceItemDetailType, InvoicePricingDetailType> INVOICE_PRICING_DETAIL_TYPE = FunctionXcblHelper.orSet(
      InvoiceItemDetailType::getInvoicePricingDetail,
      InvoiceItemDetailType::setInvoicePricingDetail,
      InvoicePricingDetailType::new);

  public static final Function<InvoicePricingDetailType, InvoiceMonetaryValueType> INVOICE_LINE_ITEM_SUB_TOTAL = FunctionXcblHelper.orSet(
      InvoicePricingDetailType::getLineItemSubTotal,
      InvoicePricingDetailType::setLineItemSubTotal,
      InvoiceMonetaryValueType::new);

  private static final Function<InvoicePricingDetailType, InvoiceMonetaryValueType> INVOICE_LINE_ITEM_TOTAL = FunctionXcblHelper.orSet(
      InvoicePricingDetailType::getLineItemTotal,
      InvoicePricingDetailType::setLineItemTotal,
      InvoiceMonetaryValueType::new);

  private static final Function<InvoicePricingDetailType, ListOfInvoicePriceType> INVOICE_LIST_OF_PRICE = FunctionXcblHelper.orSet(
      InvoicePricingDetailType::getListOfPrice,
      InvoicePricingDetailType::setListOfPrice,
      ListOfInvoicePriceType::new);

  private static final Function<InvoicePricingDetailType, ListOfInvoiceAllowOrChargeType> INVOICE_LIST_ALLOW_CHARGE_TYPE = FunctionXcblHelper
      .orSet(
          InvoicePricingDetailType::getItemAllowancesOrCharges,
          InvoicePricingDetailType::setItemAllowancesOrCharges,
          ListOfInvoiceAllowOrChargeType::new);

  /*
   * -- BUILDER --
   */

  public static InvoiceItemDetailType buildInvoiceItemDetail() {
    InvoiceItemDetailType item = new InvoiceItemDetailType();
    of(item).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
        .map(INVOICE_LINE_ITEM_NUM_TYPE);
    of(item).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
        .map(INVOICE_ITEM_IDENTIFIERS_TYPE)
        .ifPresent(ident -> {
          ITEM_DESCRIPTION.apply(ident);
        });
    of(item).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
        .map(INVOICE_ITEM_IDENTIFIERS_TYPE)
        .map(PART_NUMBERS_TYPE)
        .ifPresent(n -> {
          PARTID.apply(BUYER_PART_NUM_TYPE.apply(n));
          PARTID.apply(SELLER_PART_NUM_TYPE.apply(n));
          InvoiceProductIdentifierCodedType id = PRODUCT_ID_CODED_TYPE.apply(n);
          id.setProductIdentifierQualifierCoded("Other");
          id.setProductIdentifierQualifierCodedOther("Reference");
        });
    of(item).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
        .map(INVOICE_QUANTITY_TYPE)
        .ifPresent(qt -> {
          QUANTITY_VALUE_TYPE.apply(qt)
              .setValue(BigDecimal.ONE);
          UNIT_OF_MEASUREMENT_TYPE.apply(qt)
              .setUOMCoded(UnitOfMeasurement.UOM_EA.getCode());
        });
    of(item).map(INVOICE_PRICING_DETAIL_TYPE)
        .ifPresent(price -> {
          INVOICE_LINE_ITEM_SUB_TOTAL.apply(price);
          INVOICE_LINE_ITEM_TOTAL.apply(price);
          INVOICE_LIST_ALLOW_CHARGE_TYPE.apply(price);
        });
    setAllPriceType(item, BigDecimal.ZERO, BigDecimal.ZERO);

    return item;
  }

  /**
   * use {@link #buildInvoiceItemDetail()} instead
   */
  // @Deprecated
  public static InvoiceItemDetailType createInvoiceItemDetail() {
    InvoiceItemDetailType item = new InvoiceItemDetailType();
    item.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    item.getInvoiceBaseItemDetail()
        .setLineItemNum(new LineItemNumType());
    // InvoiceItemIdentifiersType
    InvoiceItemIdentifiersType invoiceItemIdentifiersType = new InvoiceItemIdentifiersType();
    invoiceItemIdentifiersType.setItemDescription(new ComplexStringType());
    item.getInvoiceBaseItemDetail()
        .setItemIdentifiers(invoiceItemIdentifiersType);
    item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .setPartNumbers(new InvoicePartNumbersType());
    // BuyerPartNumber
    InvoicePartNumType buyerPartNum = new InvoicePartNumType();
    buyerPartNum.setPartID(new ComplexStringType());
    item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .setBuyerPartNumber(buyerPartNum);
    // SellerPartNumber
    InvoicePartNumType sellerPartNum = new InvoicePartNumType();
    sellerPartNum.setPartID(new ComplexStringType());
    item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .setSellerPartNumber(sellerPartNum);
    // InvoiceProductIdentifierCodedType
    InvoiceProductIdentifierCodedType productIdentifierCoded = new InvoiceProductIdentifierCodedType();
    productIdentifierCoded.setProductIdentifier(new ComplexStringType());
    item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .setStandardPartNumber(productIdentifierCoded);
    item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getStandardPartNumber()
        .setProductIdentifierQualifierCoded("Other");
    item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getStandardPartNumber()
        .setProductIdentifierQualifierCodedOther("Reference");
    item.getInvoiceBaseItemDetail()
        .setInvoicedQuantity(new InvoiceQuantityType());
    item.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .setQuantityValue(new QuantityValueType());
    item.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getQuantityValue()
        .setValue(BigDecimal.ONE);
    item.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .setUnitOfMeasurement(new InvoiceUnitOfMeasurementType());
    item.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getUnitOfMeasurement()
        .setUOMCoded(UnitOfMeasurement.UOM_EA.getCode());

    item.setInvoicePricingDetail(new InvoicePricingDetailType());
    InvoiceMonetaryValueType monetaryValueTypeForSubtotal = new InvoiceMonetaryValueType();
    monetaryValueTypeForSubtotal.setMonetaryAmount(new ComplexBigDecimalType());
    item.getInvoicePricingDetail()
        .setLineItemSubTotal(monetaryValueTypeForSubtotal);
    InvoiceMonetaryValueType monetaryValueTypeForTotal = new InvoiceMonetaryValueType();
    monetaryValueTypeForTotal.setMonetaryAmount(new ComplexBigDecimalType());
    item.getInvoicePricingDetail()
        .setLineItemTotal(monetaryValueTypeForTotal);
    item.getInvoicePricingDetail()
        .setListOfPrice(new ListOfInvoicePriceType());
    item.getInvoicePricingDetail()
        .getListOfPrice()
        .getPrice()
        .add(createInvoicePriceType(CALCULATION_GROSS, BigDecimal.ZERO));
    item.getInvoicePricingDetail()
        .getListOfPrice()
        .getPrice()
        .add(createInvoicePriceType(CALCULATION_NET, BigDecimal.ZERO));
    item.getInvoicePricingDetail()
        .getListOfPrice()
        .getPrice()
        .add(createInvoicePriceType(UNIT_COST_PRICE, BigDecimal.ZERO));
    item.getInvoicePricingDetail()
        .getListOfPrice()
        .getPrice()
        .add(createInvoicePriceType(NET_ITEM_PRICE, BigDecimal.ZERO));
    item.getInvoicePricingDetail()
        .setItemAllowancesOrCharges(new ListOfInvoiceAllowOrChargeType());

    // init free quantity
    item.getInvoiceBaseItemDetail()
        .setListOfQuantityCoded(new ListOfQuantityCodedType());
    List<QuantityCodedType> quantityCodeds = item.getInvoiceBaseItemDetail()
        .getListOfQuantityCoded()
        .getQuantityCoded();
    Optional<QuantityCodedType> findAny = quantityCodeds.stream()
        .filter(quantiTyCoded -> InvoiceXcblHelper.FREE_QUANTITY_NOT_INCLUDED.equals(quantiTyCoded.getQuantityQualifierCoded()))
        .findAny();
    if (!findAny.isPresent()) {
      // initialize the node
      QuantityCodedType quantityCoded = new QuantityCodedType();
      quantityCoded.setUnitOfMeasurement(of(item).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
          .map(InvoiceBaseItemDetailType::getInvoicedQuantity)
          .map(InvoiceQuantityType::getUnitOfMeasurement)
          .map(invoiceUnitOfMeasurement -> InvoiceHelper.convertToUnitOfMeassurement(invoiceUnitOfMeasurement))
          .orElse(null));
      quantityCoded.setQuantityQualifierCoded(InvoiceXcblHelper.FREE_QUANTITY_NOT_INCLUDED);
      quantityCodeds.add(quantityCoded);
      QuantityValueType quantityValue = quantityCoded.getQuantityValue();
      if (quantityValue == null) {
        quantityValue = new QuantityValueType();
        quantityValue.setValue(BigDecimal.ZERO);
        quantityCoded.setQuantityValue(quantityValue);
      }
    }
    // else
    // nothing, the node is already initialized
    return item;
  }

  /*
   * -- GETTER & SETTER --
   */

  public static Optional<InvoicePriceType> getPriceType(InvoiceItemDetailType detail, PriceTypeCodeType priceTypeCode) {
    return ofNullable(detail)
        .map(INVOICE_PRICING_DETAIL_TYPE)
        .map(INVOICE_LIST_OF_PRICE)
        .map(ListOfInvoicePriceType::getPrice)
        .map(List::stream)
        .orElse(empty())
        .filter(price -> getPricetypeCoded(price).isPresent() && priceTypeCode.equals(getPricetypeCoded(price).get()))
        .findAny();
  }

  public static void copyPriceValueFromTo(List<InvoiceItemDetailType> details, PriceTypeCodeType from, PriceTypeCodeType to) {
    for (InvoiceItemDetailType detail : details) {
      int fromIndex = InvoiceItemDetailXcblHelper.getPriceIndex(detail, from);
      int toIndex = InvoiceItemDetailXcblHelper.getPriceIndex(detail, to);
      Optional<BigDecimal> fromPrice = InvoiceItemDetailXcblHelper.getPrice(detail, from);
      if (fromIndex >= 0 && toIndex >= 0 && fromPrice.isPresent()) {
        detail.getInvoicePricingDetail()
            .getListOfPrice()
            .getPrice()
            .get(toIndex)
            .getUnitPrice()
            .getUnitPriceValue()
            .setValue(fromPrice.get());
      }
    }

  }

  public static Optional<BigDecimal> getPrice(InvoiceItemDetailType detail, PriceTypeCodeType priceTypeCode) {
    if (priceTypeCode == null)
      return Optional.empty();

    Optional<InvoicePriceType> price = getPriceType(detail, priceTypeCode);
    if (price.isPresent()) {
      if (getPricetypeCoded(price.orElse(null)).isPresent() && priceTypeCode.equals(getPricetypeCoded(price.orElse(null)).get()) &&
          getUnitPriceValue(price.orElse(null)).isPresent()) {
        return getUnitPriceValue(price.orElse(null));
      }
    }
    return Optional.empty();
  }

  private static void setPrice(InvoiceItemDetailType detail, BigDecimal unitValue, final PriceTypeCodeType priceTypeCode, int precision,
      RoundingMode mode) {
    if (detail == null || priceTypeCode == null)
      return;

    Optional<InvoicePriceType> price = getPriceType(detail, priceTypeCode);
    if (!price.isPresent()) {
      of(detail).map(INVOICE_PRICING_DETAIL_TYPE)
          .map(INVOICE_LIST_OF_PRICE)
          .map(ListOfInvoicePriceType::getPrice)
          .ifPresent(list -> list.add(InvoicePriceXcblHelper.buildInvoicePriceType(priceTypeCode, unitValue, precision, mode)));
    }
    if (price.isPresent()) {
      if (getPricetypeCoded(price.orElse(null)).isPresent() && priceTypeCode.equals(getPricetypeCoded(price.orElse(null)).get())) {
        InvoicePriceXcblHelper.setUnitPriceValue(price.orElse(null), scale(unitValue, precision, mode));
      }
    }
  }

  public static Optional<BigDecimal> getCalculationGross(InvoiceItemDetailType detail) {
    return getPrice(detail, UNIT_COST_PRICE).isPresent()
        ? getPrice(detail, UNIT_COST_PRICE)
        : getPrice(detail, CALCULATION_GROSS);
  }

  public static void setCalculationGross(InvoiceItemDetailType detail, BigDecimal unitValue, int precision, RoundingMode mode) {
    if (getPrice(detail, UNIT_COST_PRICE).isPresent())
      setPrice(detail, unitValue, UNIT_COST_PRICE, precision, mode);
    setPrice(detail, unitValue, CALCULATION_GROSS, precision, mode);
  }

  public static Optional<BigDecimal> getCalculationNet(InvoiceItemDetailType detail) {
    return getPrice(detail, NET_ITEM_PRICE).isPresent()
        ? getPrice(detail, NET_ITEM_PRICE)
        : getPrice(detail, CALCULATION_NET);
  }

  public static void setCalculationNet(InvoiceItemDetailType detail, BigDecimal unitValue, int precision, RoundingMode mode) {
    if (getPrice(detail, NET_ITEM_PRICE).isPresent())
      setPrice(detail, unitValue, NET_ITEM_PRICE, precision, mode);
    setPrice(detail, unitValue, CALCULATION_NET, precision, mode);
  }

  private static void setAllPriceType(InvoiceItemDetailType item, BigDecimal unitGrossValue, BigDecimal unitNetValue) {
    of(item).map(INVOICE_PRICING_DETAIL_TYPE)
        .map(INVOICE_LIST_OF_PRICE)
        .map(ListOfInvoicePriceType::getPrice)
        .ifPresent(list -> {
          list.add(buildInvoicePriceType(CALCULATION_GROSS, unitGrossValue));
          list.add(buildInvoicePriceType(CALCULATION_NET, unitNetValue));
          // list.add(PriceXcblHelper.buildPriceType(UNIT_COST_PRICE, unitGrossValue));
          // list.add(PriceXcblHelper.buildPriceType(NET_ITEM_PRICE, unitNetValue));
        });
  }

  /**
   * * use {@link #getPrice(InvoiceItemDetailType, PriceTypeCodeType)} instead Get first occurency of gross or net price
   */
  // @Deprecated
  public static BigDecimal getGrossOrNetPrice(InvoiceItemDetailType detail, BigDecimal unitValue, PriceTypeCodeType priceTypeCode) {
    if (priceTypeCode == null)
      return null;

    Optional<InvoicePriceType> price = getPriceType(detail, priceTypeCode);
    if (price.isPresent()) {
      if (priceTypeCode.equals(price.get()
          .getPricingType()
          .getPriceTypeCoded())) {
        if (price.get()
            .getUnitPrice() == null) {
          price.get()
              .setUnitPrice(new InvoiceUnitPriceType());
        }
        if (!price.map(InvoicePriceType::getUnitPrice)
            .map(InvoiceUnitPriceType::getUnitPriceValue)
            .map(ComplexBigDecimalType::getValue)
            .isPresent()) {
          price.get()
              .getUnitPrice()
              .setUnitPriceValue(InvoiceHelper.toComplexBigDecimalType(price.get()
                  .getUnitPrice()
                  .getUnitPriceValue(), unitValue));
        }
        return price.map(InvoicePriceType::getUnitPrice)
            .map(InvoiceUnitPriceType::getUnitPriceValue)
            .map(ComplexBigDecimalType::getValue)
            .orElse(null);
      }
    }
    return null;
  }

  /**
   * use {@link #getPrice(InvoiceItemDetailType, PriceTypeCodeType)} instead
   */
  // @Deprecated
  public static int getPriceIndex(InvoiceItemDetailType detail, PriceTypeCodeType type) {
    int index = 0;
    try {
      for (InvoicePriceType price : detail.getInvoicePricingDetail()
          .getListOfPrice()
          .getPrice()) {
        if (type.equals(price.getPricingType()
            .getPriceTypeCoded())) {
          return index;
        }
        index++;
      }
      detail.getInvoicePricingDetail()
          .getListOfPrice()
          .getPrice()
          .add(createInvoicePriceType(type, null));
      return index;
    }
    catch (Exception e) {
      InvoiceXcblHelper.log.error("Error " + e.getMessage());
    }
    return -1;
  }

  public static BigDecimal populateItemDetailWithUnitPrice(InvoiceItemDetailType detail, Product product, int detailPrecision) {
    BigDecimal unitNetValue = null;
    BigDecimal unitGrossValue = null;
    if (ofNullable(product).map(Product::getCalculationNet)
        .isPresent())
      unitNetValue = scale(new BigDecimal(product.getCalculationNet()
          .toString()), detailPrecision, HALF_EVEN);

    if (ofNullable(product).map(Product::getCalculationGross)
        .isPresent())
      unitGrossValue = scale(new BigDecimal(product.getCalculationGross()
          .toString()), detailPrecision, HALF_EVEN);

    if (ofNullable(detail).map(INVOICE_PRICING_DETAIL_TYPE)
        .map(INVOICE_LIST_OF_PRICE)
        .map(ListOfInvoicePriceType::getPrice)
        .get()
        .isEmpty()) {
      setAllPriceType(detail, unitGrossValue, unitNetValue);
    }
    else {
      setCalculationGross(detail, unitGrossValue, detailPrecision, null);
      setCalculationNet(detail, unitNetValue, detailPrecision, null);
    }
    return unitNetValue;
  }

  public static void populateItemDetailWithGrossPrice(InvoiceItemDetailType detail, Product product, int detailPrecision,
      RoundingMode mode) {
    if (ofNullable(product).map(Product::getCalculationGross)
        .isPresent()) {
      setCalculationGross(detail, new BigDecimal(product.getCalculationGross()), detailPrecision, mode);
    }
  }

  public static void populateItemWithAllowanceOrCharge(InvoiceItemDetailType detail, Product product) {
    if (product == null)
      return;

    if (detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges() == null)
      detail.getInvoicePricingDetail()
          .setItemAllowancesOrCharges(new ListOfInvoiceAllowOrChargeType());

    if (product.getObjectAttribute() != null) {
      for (ObjectAttribute object : product.getObjectAttribute()) {
        InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
        InvoiceAllowOrChargeXcblHelper.populateAllowanceOrCharge(allowOrChargeType, object, true);
        if (!allowOrChargeType.equals(InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge()))
          detail.getInvoicePricingDetail()
              .getItemAllowancesOrCharges()
              .getAllowOrCharge()
              .add(allowOrChargeType);
      }
    }
  }

  /**
   * Add VAT in the item detail
   */
  public static BigDecimal populateItemWithTax(InvoiceItemDetailType detail, Optional<BigDecimal> netPrice, BigDecimal taxValue) {
    return populateItemWithTax(detail, netPrice, taxValue, TaxCategoryCodeType.STANDARD_RATE);
  }

  public static BigDecimal populateItemWithTax(InvoiceItemDetailType detail, Optional<BigDecimal> netPrice, BigDecimal taxValue,
      TaxCategoryCodeType taxCategory) {
    InvoiceTaxType tax = createInvoiceTaxtype(netPrice, ofNullable(taxValue), taxCategory);

    if (detail.getInvoicePricingDetail()
        .getTax()
        .isEmpty())
      detail.getInvoicePricingDetail()
          .getTax()
          .add(tax);

    else
      detail.getInvoicePricingDetail()
          .getTax()
          .set(0, tax);

    // TODO : Refacto
    List<InvoiceAllowOrChargeType> allowOrCharges = Optional.of(detail)
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getItemAllowancesOrCharges)
        .map(ListOfInvoiceAllowOrChargeType::getAllowOrCharge)
        .orElse(Collections.emptyList());

    // apply allowance taxes and charges
    for (InvoiceAllowOrChargeType allOrChrg : allowOrCharges) {
      InvoiceAllowOrChargeXcblHelper.computeTaxOnInvoiceAllowanceOrCharge(allOrChrg, detail, netPrice.orElse(BigDecimal.ZERO));
    }

    return tax.getTaxAmount()
        .getValue();
  }

  public static void applyTaxOnInvoiceItemDetail(InvoiceItemDetailType detail, Map<String, InvoiceTaxSummaryType> taxSummaries,
      int detailPrecision) {
    Optional<InvoiceTaxType> tax = ofNullable(detail).map(INVOICE_PRICING_DETAIL_TYPE)
        .map(InvoicePricingDetailType::getTax)
        .map(List::stream)
        .orElse(empty())
        .findFirst();

    if (!tax.isPresent())
      return;

    Optional<BigDecimal> amountHT = getAmountHT(detail);
    if (tax.map(InvoiceTaxType::getTaxPercent)
        .isPresent() && amountHT.isPresent()) {
      BigDecimal taxAmount = amountHT.get()
          .multiply(tax.get()
              .getTaxPercent()
              .getValue())
          .divide(BigDecimal_CENT, HALF_EVEN);
      computeAmountTTC(detail, detailPrecision, HALF_EVEN);

      tax.get()
          .setTaxAmount(toComplexBigDecimalType(tax.get()
              .getTaxAmount(), taxAmount.setScale(TAX_PRECISION, HALF_EVEN)));
      tax.get()
          .setTaxableAmount(amountHT.get()
              .setScale(TAX_PRECISION, HALF_EVEN));

      // Compute tax summary
      populateInvoiceTaxSummary(taxSummaries, tax.get(), detailPrecision);
    }
  }

  public static void populateInvoiceItemDetail(InvoiceItemDetailType detail, Product product, int detailPrecision, int amountPrecision,
      TaxCategoryCodeType taxCategory) {
    populateInvoiceBaseItemDetail(detail, product);
    populateInvoicePricingDetail(detail, product, detailPrecision, amountPrecision, taxCategory);
  }

  public static void populateInvoiceBaseItemDetail(InvoiceItemDetailType detail, Product product) {
    if (product == null)
      return;

    ofNullable(detail).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
        .map(INVOICE_ITEM_IDENTIFIERS_TYPE)
        .map(PART_NUMBERS_TYPE)
        .ifPresent(part -> {
          if (part.getStandardPartNumber() != null)
            part.getStandardPartNumber()
                .setProductIdentifier(InvoiceHelper.toComplexStringType(part.getStandardPartNumber()
                    .getProductIdentifier(), product.getReference()));
          if (part.getBuyerPartNumber() != null)
            part.getBuyerPartNumber()
                .setPartID(InvoiceHelper.toComplexStringType(part.getBuyerPartNumber()
                    .getPartID(), product.getBaseProductNumber()));
          if (part.getSellerPartNumber() != null)
            part.getSellerPartNumber()
                .setPartID(toComplexStringType(product.getManuPartNumber()));
        });

    ofNullable(detail).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
        .map(INVOICE_ITEM_IDENTIFIERS_TYPE)
        .ifPresent(item -> item.setItemDescription(toComplexStringType(product.getDescription())));

    boolean uomValid = Arrays.stream(UnitOfMeasurement.values())
        .anyMatch(e -> e.getCode()
            .equalsIgnoreCase(product.getUnitOfMeasurementType()));

    if (uomValid) {
      ofNullable(detail).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
          .map(INVOICE_QUANTITY_TYPE)
          .map(UNIT_OF_MEASUREMENT_TYPE)
          .ifPresent(unit -> unit.setUOMCoded(product.getUnitOfMeasurementType()));
    }
    else {
      ofNullable(detail).map(INVOICE_BASE_ITEM_DETAIL_TYPE)
          .map(INVOICE_QUANTITY_TYPE)
          .map(UNIT_OF_MEASUREMENT_TYPE)
          .ifPresent(unit -> {
            unit.setUOMCoded(UnitOfMeasurement.UOM_Other.getCode());
            unit.setUOMCodedOther(
                InvoiceHelper.toComplexStringType(unit.getUOMCodedOther(), product.getUnitOfMeasurementType()));
          });
    }
  }

  public static void populateInvoicePricingDetail(InvoiceItemDetailType detail, Product product, int detailPrecision, int amountPrecision,
      TaxCategoryCodeType taxCategory) {
    populateItemDetailWithGrossPrice(detail, product, detailPrecision, HALF_EVEN);

    populateItemWithAllowanceOrCharge(detail, product);

    applyInvoiceAllowanceOrChargeOnDetail(detail, detailPrecision);

    computeAmountHT(detail, amountPrecision, HALF_EVEN);

    BigDecimal taxValue = ofNullable(product).map(Product::getVat)
        .map(BigDecimal::valueOf)
        .orElse(null);
    if (taxCategory == null) {
      populateItemWithTax(detail, getCalculationNet(detail), taxValue != null ? taxValue.setScale(TAX_PRECISION, HALF_EVEN) : null);
    }
    else {
      populateItemWithTax(detail, getCalculationNet(detail), taxValue != null ? taxValue.setScale(TAX_PRECISION, HALF_EVEN) : null,
          taxCategory);
    }

    computeAmountTTC(detail, amountPrecision, HALF_EVEN);
  }
  
  public static ComplexStringType getBuyerPartID(InvoiceItemDetailType product) {
    if (getBuyerPartIDIntermediate(product) == null) {
      setBuyerPartID(null, product);
    }
    return getBuyerPartIDIntermediate(product);
  }

  private static ComplexStringType getBuyerPartIDIntermediate(InvoiceItemDetailType product) {
    return ofNullable(product)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getPartNumbers)
        .map(InvoicePartNumbersType::getBuyerPartNumber)
        .map(InvoicePartNumType::getPartID)
        .orElse(null);
  }

  public static void setPartNumbersHelper(InvoiceItemDetailType product) {
    if (product.getInvoiceBaseItemDetail() == null) {
      product.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    }

    if (product.getInvoiceBaseItemDetail()
        .getItemIdentifiers() == null) {
      product.getInvoiceBaseItemDetail()
          .setItemIdentifiers(new InvoiceItemIdentifiersType());
    }
    if (product.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers() == null)
      product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .setPartNumbers(new InvoicePartNumbersType());
  }

  public static void setBuyerPartID(String buyerPartID, InvoiceItemDetailType product) {
    if (product != null) {
      setPartNumbersHelper(product);
      if (product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getBuyerPartNumber() == null) {
        product.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .setBuyerPartNumber(new InvoicePartNumType());
      }
      product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getBuyerPartNumber()
          .setPartID(toComplexStringType(product.getInvoiceBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getBuyerPartNumber()
              .getPartID(), buyerPartID));
    }
  }

  public static ComplexStringType getSellerPartID(InvoiceItemDetailType product) {
    if (getSellerPartIDIntermediate(product) == null) {
      setSellerPartID(null, product);
    }
    return getSellerPartIDIntermediate(product);
  }

  private static ComplexStringType getSellerPartIDIntermediate(InvoiceItemDetailType product) {
    return ofNullable(product).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getPartNumbers)
        .map(InvoicePartNumbersType::getSellerPartNumber)
        .map(InvoicePartNumType::getPartID)
        .orElse(null);
  }

  public static void setSellerPartID(String sellerPartID, InvoiceItemDetailType product) {
    if (product != null) {
      setPartNumbersHelper(product);
      if (product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getSellerPartNumber() == null) {
        product.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .setSellerPartNumber(new InvoicePartNumType());
      }
      product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getSellerPartNumber()
          .setPartID(toComplexStringType(product.getInvoiceBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getSellerPartNumber()
              .getPartID(), sellerPartID));
    }
  }
  
  public static ComplexStringType getBuyerOrderNumber(InvoiceItemDetailType product) {
    if(getBuyerOrderNumberIntermediate(product) == null) {
       setBuyerOrderNumber(null, product);
    }
    return getBuyerOrderNumberIntermediate(product);
  }


  private static ComplexStringType getBuyerOrderNumberIntermediate(InvoiceItemDetailType product) {
    return ofNullable(product)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getLineItemReferences)
        .map(InvoiceReferencesType::getPurchaseOrderReference)
        .map(InvoicePurchaseOrderReferenceType::getBuyerOrderNumber)
        .orElse(null);
  }
  
  public static void setBuyerOrderNumber(String buyerOrderNumber, InvoiceItemDetailType product) {
    if (product != null) {
      setLineItemReferencesHelper(product);
      if (product.getInvoiceBaseItemDetail()
          .getLineItemReferences()
          .getPurchaseOrderReference() == null) {
        product.getInvoiceBaseItemDetail()
            .getLineItemReferences()
            .setPurchaseOrderReference(new InvoicePurchaseOrderReferenceType());

      }
      product.getInvoiceBaseItemDetail()
          .getLineItemReferences()
          .getPurchaseOrderReference()
          .setBuyerOrderNumber(toComplexStringType(product.getInvoiceBaseItemDetail()
              .getLineItemReferences()
              .getPurchaseOrderReference()
              .getBuyerOrderNumber(), buyerOrderNumber));
    }
  }
  
  public static ComplexStringType getAsnNumber(InvoiceItemDetailType product) {
    if(getAsnNumberIntermediate(product) == null) {
      setAsnNumber(null, product);
    }
    return getAsnNumberIntermediate(product);
  }
  
  private static ComplexStringType getAsnNumberIntermediate(InvoiceItemDetailType product) {
    return ofNullable(product)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getLineItemReferences)
        .map(InvoiceReferencesType::getASNNumber)
        .map(InvoiceOCRReferenceType::getRefNum)
        .orElse(null);
  }
  
  public static void setAsnNumber(String asnNumber, InvoiceItemDetailType product) {
    if (product != null) {
      setLineItemReferencesHelper(product);
      if (product.getInvoiceBaseItemDetail()
          .getLineItemReferences()
          .getASNNumber() == null) {
        product.getInvoiceBaseItemDetail()
            .getLineItemReferences()
            .setASNNumber(new InvoiceOCRReferenceType());

      }
      product.getInvoiceBaseItemDetail()
          .getLineItemReferences()
          .getASNNumber()
          .setRefNum(toComplexStringType(product.getInvoiceBaseItemDetail()
              .getLineItemReferences()
              .getASNNumber()
              .getRefNum(), asnNumber));

    }
  }
  
  private static void setLineItemReferencesHelper(InvoiceItemDetailType product) {
    if (product.getInvoiceBaseItemDetail() == null) {
      product.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    }
    if (product.getInvoiceBaseItemDetail()
        .getLineItemReferences() == null) {
      product.getInvoiceBaseItemDetail()
          .setLineItemReferences(new InvoiceReferencesType());
    }
  }

  public static ComplexStringType getStandardProductIdentifier(InvoiceItemDetailType product) {
    if (getStandardProductIdentifierIntermediate(product) == null) {
      setStandardProductIdentifier(null, product);
    }
    return getStandardProductIdentifierIntermediate(product);
  }

  private static ComplexStringType getStandardProductIdentifierIntermediate(InvoiceItemDetailType product) {
    return ofNullable(product).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getPartNumbers)
        .map(InvoicePartNumbersType::getStandardPartNumber)
        .map(InvoiceProductIdentifierCodedType::getProductIdentifier)
        .orElse(null);
  }

  public static void setStandardProductIdentifier(String standardProductIdentifier, InvoiceItemDetailType product) {
    if (product != null) {
      setPartNumbersHelper(product);
      if (product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getStandardPartNumber() == null) {
        product.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .setStandardPartNumber(new InvoiceProductIdentifierCodedType());
      }

      product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getStandardPartNumber()
          .setProductIdentifier(toComplexStringType(product.getInvoiceBaseItemDetail()
              .getItemIdentifiers()
              .getPartNumbers()
              .getStandardPartNumber()
              .getProductIdentifier(), standardProductIdentifier));
    }
  }

  public static ComplexStringType getItemDesription(InvoiceItemDetailType product) {
    if (getItemDesriptionIntermediate(product) == null) {
      if (product.getInvoiceBaseItemDetail() == null) {
        product.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
      }

      if (product.getInvoiceBaseItemDetail()
          .getItemIdentifiers() == null) {
        product.getInvoiceBaseItemDetail()
            .setItemIdentifiers(new InvoiceItemIdentifiersType());
      }
      if (product.getInvoiceBaseItemDetail()
          .getItemIdentifiers()
          .getItemDescription() == null) {
        product.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .setItemDescription(new ComplexStringType());
      }
    }
    return getItemDesriptionIntermediate(product);
  }

  public static ComplexStringType getItemDesriptionIntermediate(InvoiceItemDetailType product) {
    return ofNullable(product).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getItemDescription)
        .orElse(null);
  }

  public static Optional<BigDecimal> getQuantity(InvoiceItemDetailType detail) {
    return ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getInvoicedQuantity)
        .map(InvoiceQuantityType::getQuantityValue)
        .map(QuantityValueType::getValue);
  }

  public static void setQuantity(InvoiceItemDetailType detail, BigDecimal qty) {
    ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getInvoicedQuantity)
        .map(InvoiceQuantityType::getQuantityValue)
        .ifPresent(v -> v.setValue(qty));
  }

  public static Optional<InvoiceUnitOfMeasurementType> getUnitOfMeasurement(InvoiceItemDetailType detail) {
    return ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getInvoicedQuantity)
        .map(InvoiceQuantityType::getUnitOfMeasurement);
  }

  public static Optional<BigDecimal> getAmountHT(InvoiceItemDetailType detail) {
    Optional<BigDecimal> amountHT = ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getLineItemSubTotal)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue);
    if (!amountHT.isPresent())
      amountHT = computeAmountHT(detail, 0, HALF_EVEN);
    return amountHT;
  }

  public static Optional<String> getProductIdentifier(InvoiceItemDetailType detail) {
    return getStandardPartNumber(detail).map(InvoiceProductIdentifierCodedType::getProductIdentifier)
        .map(ComplexStringType::getValue);
  }

  public static Optional<InvoiceProductIdentifierCodedType> getStandardPartNumber(InvoiceItemDetailType detail) {
    return ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getPartNumbers)
        .map(InvoicePartNumbersType::getStandardPartNumber);
  }

  public static Optional<BigDecimal> getAmountVAT(InvoiceItemDetailType detail) {
    Optional<BigDecimal> amountTTC = ofNullable(detail.getInvoicePricingDetail()
        .getTax()
        .stream()
        .map(InvoiceTaxType::getTaxAmount)
        .map(ComplexBigDecimalType::getValue)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add));

    if (!amountTTC.isPresent())
      amountTTC = computeAmountTTC(detail, 0, HALF_EVEN);
    return amountTTC;
  }

  public static Optional<BigDecimal> getFirstTaxValue(InvoiceItemDetailType detail) {
    return ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getTax)
        .filter(CollectionUtils::isNotEmpty)
        .map(List::iterator)
        .map(Iterator::next)
        .map(InvoiceTaxType::getTaxPercent)
        .map(PercentType::getValue);
  }

  public static Optional<InvoiceTaxType> getFirstTax(InvoiceItemDetailType detail) {
    return ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getTax)
        .map(List::stream)
        .orElse(empty())
        .findFirst();
  }

  // -- Invoice Calcul --

  private static BigDecimal scale(BigDecimal value, int precision, RoundingMode mode) {
    if (value == null)
      return BigDecimal.ZERO.setScale(precision, mode != null ? mode : HALF_EVEN);
    return value.setScale(precision, mode != null ? mode : HALF_EVEN);
  }

  public static Optional<BigDecimal> computeAmountHT(InvoiceItemDetailType detail, int precision, RoundingMode mode) {
    Optional<BigDecimal> quantity = getQuantity(detail);
    InvoiceMonetaryValueType lineItemSubTotal = of(detail).map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(INVOICE_LINE_ITEM_SUB_TOTAL)
        .orElse(new InvoiceMonetaryValueType());
    getCalculationNet(detail)
        .map(g -> g.multiply(quantity.orElse(BigDecimal.ONE)))
        .map(g -> scale(g, precision, mode))
        .ifPresent(
            g -> lineItemSubTotal.setMonetaryAmount(toComplexBigDecimalType(lineItemSubTotal.getMonetaryAmount(), g)));
    return ofNullable(lineItemSubTotal.getMonetaryAmount()).map(ComplexBigDecimalType::getValue);
  }

  public static Optional<BigDecimal> computeAmountTTC(InvoiceItemDetailType detail, int precision, RoundingMode mode) {
    Optional<BigDecimal> tax = getFirstTaxValue(detail);
    InvoiceMonetaryValueType lineItemTotal = of(detail).map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(INVOICE_LINE_ITEM_TOTAL)
        .orElse(new InvoiceMonetaryValueType());
    // TODO : not sure
    if (!tax.isPresent())
      lineItemTotal
          .setMonetaryAmount(toComplexBigDecimalType(getAmountHT(detail).orElse(BigDecimal.ZERO)));
    getAmountHT(detail)
        .map(ht -> ht.add(ht.multiply(tax.orElse(BigDecimal.ZERO))
            .divide(BigDecimal_CENT, HALF_EVEN)))
        .map(ht -> scale(ht, precision, mode))
        .ifPresent(
            g -> lineItemTotal
                .setMonetaryAmount(toComplexBigDecimalType(lineItemTotal.getMonetaryAmount(), scale(g, precision, mode))));
    return of(lineItemTotal.getMonetaryAmount()).map(ComplexBigDecimalType::getValue);
  }

  public static Optional<BigDecimal> computeAmountTTCWithoutTax(InvoiceItemDetailType detail, int precision, RoundingMode mode) {
    getAmountHT(detail)
        .map(ht -> scale(ht, precision, mode))
        .ifPresent(g -> detail.getInvoicePricingDetail()
            .getLineItemTotal()
            .setMonetaryAmount(toComplexBigDecimalType(detail.getInvoicePricingDetail()
                .getLineItemTotal()
                .getMonetaryAmount(), g)));
    return of(detail).map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getLineItemSubTotal)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue);
  }

  public static String obtainProductIdentifier(InvoiceItemDetailType item) {
    return Optional.of(item.getInvoiceBaseItemDetail())
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getPartNumbers)
        .map(InvoicePartNumbersType::getStandardPartNumber)
        .map(InvoiceProductIdentifierCodedType::getProductIdentifier)
        .map(ComplexStringType::getValue)
        .orElse("");
  }

  public static String obtainPartId(InvoiceItemDetailType item) {
    return obtainPartComplexId(item)
        .map(ComplexStringType::getValue)
        .orElse("");
  }

  public static Optional<ComplexStringType> obtainPartComplexId(InvoiceItemDetailType item) {
    return Optional.of(item.getInvoiceBaseItemDetail())
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getPartNumbers)
        .map(InvoicePartNumbersType::getBuyerPartNumber)
        .map(InvoicePartNumType::getPartID);
  }

  public static String getItemDescription(InvoiceItemDetailType item) {
    return of(item).map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getItemIdentifiers)
        .map(InvoiceItemIdentifiersType::getItemDescription)
        .map(ComplexStringType::getValue)
        .orElse("");
  }

  public static void replaceValueOfLineItemTotal(Invoice invoice, int amountPrecision) {
    // Since AIO-6735
    // LineItemSubTotal become LineItemTotal
    getDetails(invoice).stream()
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .forEach(price -> {
          Optional<ComplexBigDecimalType> optional = of(price).map(InvoicePricingDetailType::getLineItemSubTotal)
              .map(InvoiceMonetaryValueType::getMonetaryAmount);

          if (optional.isPresent() && optional.map(ComplexBigDecimalType::getValue)
              .isPresent()) {
            optional.get()
                .setValue(optional.get()
                    .getValue()
                    .setScale(amountPrecision, HALF_EVEN));
          }
          price.setLineItemSubTotal(null);
        });
  }

  public static void initPriceType(InvoicePriceType invoicePriceType) {
    if (invoicePriceType.getUnitPrice() == null) {
      invoicePriceType.setUnitPrice(new InvoiceUnitPriceType());
    }
    if (invoicePriceType.getUnitPrice()
        .getUnitPriceValue() == null) {
      invoicePriceType.getUnitPrice()
          .setUnitPriceValue(new ComplexBigDecimalType());
    }
  }
}
