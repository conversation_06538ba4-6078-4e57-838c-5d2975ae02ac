package com.byzaneo.generix.edocument.api.asn;

import static com.byzaneo.generix.edocument.util.CsvImportHelper.checkField;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.byzaneo.commons.api.BeanDescriptorWrapper;
import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.generix.edocument.bean.xcbl.CsvParty;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.IdentifierType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfPartyCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.NameAddressType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;

@Component("xcblAsnParty")
public class AsnPartyWrapper extends BeanDescriptorWrapper<CsvParty> {

  @Autowired
  @Qualifier(BeanService.SERVICE_NAME)
  protected BeanService beanService;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  protected transient SecurityService securityService;

  @Override
  protected BeanDescriptor createBeanDescriptor() {
    try {
      return beanService.fromClasspath("com/byzaneo/generix/edocument/bean/asn/asnPartyDescriptor.xml");
    }
    catch (IOException ex) {
      throw new RuntimeException(ex);
    }
  }

  @Override
  public Iterable<CsvParty> getBeans(Object owner) {
    return null;
  }

  @Override
  public CsvParty getBean() {
    return new CsvParty();
  }

  @SuppressWarnings("squid:S1226")
  @Override
  public void validate(CsvParty bean, BeanImportContext context) throws PropertiesException {
    Map<String, Exception> exceptions = new HashMap<>();
    AdvanceShipmentNotice desadv = (AdvanceShipmentNotice) CsvImportHelper.getImportedIndexable();

    if (desadv == null) {
      // No header found
      bean = null;
      return;
    }

    // Check bean
    checkField(bean.getType(), "type", 3, exceptions);
    checkField(bean.getEanCode(), "eanCode", 35, exceptions);

    PartyType party = new PartyType();
    party.setPartyID(new IdentifierType());
    party.getPartyID()
        .setIdent(bean.getEanCode());
    setNameAdress(bean, party);

    switch (bean.getType()) {
    case "DP":
      desadv.getASNHeader()
          .getASNParty()
          .setShipToParty(party);
      break;
    case "SF":
      desadv.getASNHeader()
          .getASNParty()
          .setShipFromParty(party);
      break;
    case "SH":
      desadv.getASNHeader()
          .getASNParty()
          .setWarehouseParty(party);
      break;
    case "UD":
      ListOfPartyCodedType list = new ListOfPartyCodedType();
      PartyCodedType partyCoded = new PartyCodedType();
      partyCoded.setPartyID(party.getPartyID());
      partyCoded.setNameAddress(party.getNameAddress());
      partyCoded.setPartyRoleCoded("UltimateCustomer");
      list.getPartyCoded()
          .add(partyCoded);
      desadv.getASNHeader()
          .getASNParty()
          .setListOfPartyCoded(list);
      break;
    case "SU":
      desadv.getASNHeader()
          .getASNParty()
          .setManufacturingParty(party);
      break;
    case "BY":
      desadv.getASNHeader()
          .getASNParty()
          .setBuyerParty(party);
      break;
    case "SE":
      desadv.getASNHeader()
          .getASNParty()
          .setSellerParty(party);
      break;

    default:
      exceptions.put("partyType", new ServiceException("Unknown type"));
    }

    if (!exceptions.isEmpty()) {
      desadv.setReference("-1");
      throw new PropertiesException(exceptions);
    }
  }

  private void setNameAdress(CsvParty bean, PartyType party) {
    NameAddressType address = new NameAddressType();
    Partner partner = null;

    if (bean.getName() != null &&
        (bean.getAddress() == null ||
            bean.getZipCode() == null ||
            bean.getCity() == null ||
            bean.getCountry() == null)) {
      partner = this.securityService.getOrganizationByCode(Partner.class, bean.getEanCode());
    }

    if (partner != null) {
      address.setName1(partner.getFullnameOrName());
      com.byzaneo.location.bean.Address addressPartner = partner.getLocation()
          .getAddress();
      address.setStreet(addressPartner.getStreetName());
      address.setPostalCode(addressPartner.getPostalCode());
      address.setCity(addressPartner.getCity());
      CountryType country = new CountryType();
      country.setCountryCoded(CountryCodeType.valueOf(addressPartner.getCountry()
          .getCountry()));
      address.setCountry(country);
    }
    else {
      address.setName1(bean.getName());
      address.setStreet(bean.getAddress());
      address.setPostalCode(bean.getZipCode());
      address.setCity(bean.getCity());
      if (bean.getCountry() != null) {
        CountryType country = new CountryType();
        country.setCountryCoded(CountryCodeType.fromValue(bean.getCountry()));
        address.setCountry(country);
      }
    }
    party.setNameAddress(address);
  }

  @Override
  public boolean persist(CsvParty bean) {
    return false;
  }

}
