package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.applyAllowOrChrgOnTaxableValue;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.applyTaxOnHeaderAllowanceCharge;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.computeInvoiceTotalTaxable;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getAllowOrCharges;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getInvoiceItemDetails;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initializeInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType.fromValue;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.security.api.Organization;
import com.byzaneo.security.bean.Group;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;

// XXX : Refacto this class
public class CsvImportHelper {
  public static final String DETAIL_PRECISION = "DETAIL_PRECISION";

  public static final String FOOTER_PRECISION = "FOOTER_PRECISION";

  private static final ThreadLocal<Integer> rootContainerIndex = new ThreadLocal<Integer>() {
  };

  private static final ThreadLocal<Boolean> documentVersion = new ThreadLocal<Boolean>() {
  };

  private static final ThreadLocal<XcblDocument> importedIndexable = new ThreadLocal<XcblDocument>() {
  };

  private static final ThreadLocal<List<String>> products = new ThreadLocal<List<String>>() {
    /* initialValue() is called */
    @Override
    protected List<String> initialValue() {
      return new ArrayList<>();
    }
  };

  private static final ThreadLocal<Map<Integer, PackageDetailType>> packageDetails = new ThreadLocal<Map<Integer, PackageDetailType>>() {
    /* initialValue() is called */
    @Override
    protected Map<Integer, PackageDetailType> initialValue() {
      return new HashMap<>();
    }
  };

  private static final ThreadLocal<Map<Integer, PackageType>> containers = new ThreadLocal<Map<Integer, PackageType>>() {
    /* initialValue() is called */
    @Override
    protected Map<Integer, PackageType> initialValue() {
      return new HashMap<>();
    }
  };

  public final static String VAT = "ValueAddedTax";

  public final static String OTHER = "Other";

  public final static String SIREN = "SIREN";

  public final static String CONTAINER_TRUCK = "Truck";

  public final static String CONTAINER_PALLET = "Pallet";

  public static final int getProductLineNumber(String id) {
    if (!products.get()
        .contains(id)) {
      products.get()
          .add(id);
    }
    return products.get()
        .indexOf(id);
  }

  public static final XcblDocument getImportedIndexable() {
    return importedIndexable.get();
  }

  public static final void setImportedIndexable(XcblDocument xcblDocument) {
    importedIndexable.set(xcblDocument);
  }

  public static final Map<Integer, PackageType> getContainersMap() {
    return containers.get();
  }

  public static final Map<Integer, PackageDetailType> getContainersDetailMap() {
    return packageDetails.get();
  }

  public static final Integer getRootContainerIndex() {
    return rootContainerIndex.get();
  }

  public static final void setRootContainerIndex(Integer integer) {
    rootContainerIndex.set(integer);
  }

  public static final Boolean isDocumentLongVersion() {
    return documentVersion.get();
  }

  public static final void setDocumentLongVersion(Boolean value) {
    documentVersion.set(value);
  }

  public static final void reinitMaps() {
    containers.get()
        .clear();
    packageDetails.get()
        .clear();
    importedIndexable.set(null);
    products.get()
        .clear();
    rootContainerIndex.remove();
    documentVersion.set(false);
  }

  public static PartyType fillGroupInformations(Group group) {
    PartyType party = fillOrganizationInformations((Organization) group);

    NameAddressType address = new NameAddressType();
    address.setName1(group.getFullname());
    address.setStreet(group.getLocation()
        .getAddress()
        .getStreetName());
    address.setPostalCode(group.getLocation()
        .getAddress()
        .getPostalCode());
    address.setCity(group.getLocation()
        .getAddress()
        .getCity());
    address.setCountry(new CountryType());
    CountryType country = new CountryType();

    try {
      country.setCountryCoded(fromValue(group.getLocation()
          .getAddress()
          .getCountry()
          .getCountry()));
    }
    catch (IllegalArgumentException e) {
      country.setCountryCoded(CountryCodeType.OTHER);
      country.setCountryCodedOther(group.getLocation()
          .getAddress()
          .getCountry()
          .getCountry());
    }

    address.setCountry(country);
    party.setNameAddress(address);
    return party;
  }

  public static PartNumType getPartNumberType(String identValue) {
    PartNumType partNum = new PartNumType();
    partNum.setPartID(identValue);
    return partNum;
  }

  public static InvoicePartNumType getInvoicePartNumberType(String identValue) {
    InvoicePartNumType partNum = new InvoicePartNumType();
    partNum.setPartID(toComplexStringType(identValue));
    return partNum;
  }

  public static UnitOfMeasurementType getUnitOfMeasurement(String field) {
    if (StringUtils.isBlank(field))
      return null;
    UnitOfMeasurementType unit = new UnitOfMeasurementType();

    if (isValidUnitOfMeasurement(field)) {
      if ("PCE".equals(field)) {
        unit.setUOMCoded("EA");
      }
      else {
        unit.setUOMCoded(field);
      }
    }
    else {
      unit.setUOMCoded("Other");
      unit.setUOMCodedOther(field);
    }
    return unit;
  }

  public static InvoiceUnitOfMeasurementType getInvoiceUnitOfMeasurement(String field) {
    if (StringUtils.isBlank(field))
      return null;
    InvoiceUnitOfMeasurementType unit = new InvoiceUnitOfMeasurementType();

    if (isValidUnitOfMeasurement(field)) {
      if ("PCE".equals(field)) {
        unit.setUOMCoded("EA");
      }
      else {
        unit.setUOMCoded(field);
      }
    }
    else {
      unit.setUOMCoded("Other");
      unit.setUOMCodedOther(toComplexStringType(field));
    }
    return unit;
  }

  private static boolean isValidUnitOfMeasurement(String unit) {
    return UnitOfMeasurement.isValidUMOCoded(unit);
  }

  public static DateCodedType getDateCoded(Date date, String qualifier) {
    DateCodedType dateCoded = new DateCodedType();
    dateCoded.setDateQualifier(new DateQualifierType());
    dateCoded.getDateQualifier()
        .setDateQualifierCoded(qualifier);
    dateCoded.setDate(date);

    return dateCoded;
  }

  public static QuantityType getQuantityType(BigDecimal value, UnitOfMeasurementType unit) {
    QuantityType quantity = new QuantityType();
    quantity.setQuantityValue(new QuantityValueType());
    quantity.getQuantityValue()
        .setValue(value);
    quantity.setUnitOfMeasurement(unit);

    return quantity;
  }

  public static String getProductId(Integer localId, BeanDescriptor bd) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    String ID_SEPARATOR = "_";
    StringBuilder sb = new StringBuilder(localId.toString());
    sb.append(ID_SEPARATOR)
        .append(bd.get("gtinCode")
            .<String> getValue());

    if (StringUtils.isNotBlank(bd.get("buyerCode")
        .getValue())) {
      sb.append(ID_SEPARATOR)
          .append(bd.get("buyerCode")
              .<String> getValue());
    }
    if (StringUtils.isNotBlank(bd.get("sellerCode")
        .getValue())) {
      sb.append(ID_SEPARATOR)
          .append(bd.get("sellerCode")
              .<String> getValue());
    }
    if (bd.get("expirationDate")
        .getValue() != null) {
      sb.append(ID_SEPARATOR)
          .append(sdf.format(bd.get("expirationDate")
              .<Date> getValue()));
    }
    if (bd.get("dluo")
        .getValue() != null) {
      sb.append(ID_SEPARATOR)
          .append(sdf.format(bd.get("dluo")
              .<Date> getValue()));
    }
    sb.append(ID_SEPARATOR)
        .append(bd.get("orderNumber")
            .<String> getValue());
    sb.append(ID_SEPARATOR)
        .append(bd.get("orderLineNumber")
            .<Integer> getValue());

    return sb.toString();
  }

  /**
   * Compute invoice data
   * 
   * @param invoice
   * @return invoice with computed data
   */
  public static Invoice computeInvoiceTotal(Invoice invoice, int detailPrecision, int precision) {

    Map<String, InvoiceTaxSummaryType> summaries = new HashMap<>();
    InvoiceSummaryType invoiceSummary = initializeInvoiceSummary(invoice);
    invoiceSummary.setListOfTaxSummary(new ListOfInvoiceTaxSummaryType());

    List<InvoiceItemDetailType> detailList = getInvoiceItemDetails(invoice);
    List<InvoiceAllowOrChargeType> allowOrChargeList = getAllowOrCharges(invoice).stream()
        .filter(allow -> !MethodOfHandlingCodeType.INFORMATION_ONLY.equals(allow.getMethodOfHandlingCoded()))
        .collect(Collectors.toList());

    BigDecimal totalTaxableAmount = detailList.isEmpty()
        ? BigDecimal.ZERO
        : computeInvoiceTotalTaxable(invoice, detailList, precision, false);

    applyAllowOrChrgOnTaxableValue(invoice, allowOrChargeList, totalTaxableAmount, false, precision);
    applyTaxOnHeaderAllowanceCharge(allowOrChargeList, summaries, invoice, detailList, false, detailPrecision, precision);

    invoice.getInvoiceSummary()
        .getListOfTaxSummary()
        .getTaxSummary()
        .addAll(summaries.values());

    // calculateAllowOrChargeSummary(invoice, precision);

    return invoice;
  }

  /* -- UTILs -- */
  public static void checkField(String field, String fieldName, int maxSize, Map<String, Exception> exceptions) {
    if (isBlank(field)) {
      exceptions.put(fieldName,
          new ServiceException("labels.import-xcbl.asn_missing_field", "Missing field " + fieldName, null, fieldName));
    }
    else if (field.length() > maxSize) {
      exceptions.put(fieldName,
          new ServiceException("labels.import-xcbl.asn_field_size_exceeded", "Size exceeded for field " + fieldName, null, fieldName));
    }
  }

  public static void checkField(Number field, String fieldName, int maxSize, Map<String, Exception> exceptions) {
    if (field == null) {
      exceptions.put(fieldName,
          new ServiceException("labels.import-xcbl.asn_missing_field", "Missing field " + fieldName, null, fieldName));
    }
    else if (field.toString()
        .length() > maxSize) {
      exceptions.put(fieldName,
          new ServiceException("labels.import-xcbl.asn_field_size_exceeded", "Size exceeded for field " + fieldName, null, fieldName));
    }
  }

  /* -- PRIVATE -- */
  private static PartyType fillOrganizationInformations(Organization organization) {
    PartyType party = new PartyType();

    AgencyType agency;
    if (isNotBlank(organization.getCode())) {
      party.setPartyID(getIdentifierType(organization.getCode(), null));
    }

    if (isNotBlank(organization.getDuns())) {
      agency = new AgencyType();
      agency.setAgencyCoded("FR-INSEE");
      agency.setCodeListIdentifierCoded(OTHER);
      agency.setCodeListIdentifierCodedOther(SIREN);
      party.setListOfIdentifier(new ListOfIdentifierType());
      party.getListOfIdentifier()
          .getIdentifier()
          .add(getIdentifierType(organization.getDuns(), agency));
    }
    if (isNotBlank(organization.getVat())) {
      agency = new AgencyType();
      agency.setAgencyCoded("CEC");
      agency.setCodeListIdentifierCoded("ValueAddedTaxIdentification");
      party.setListOfIdentifier(new ListOfIdentifierType());
      party.getListOfIdentifier()
          .getIdentifier()
          .add(getIdentifierType(organization.getVat(), agency));
    }
    if (isNotBlank(organization.getRegistration())) {
      agency = new AgencyType();
      agency.setAgencyCoded(OTHER);
      agency.setAgencyCodedOther("RCS-RCM");
      agency.setAgencyDescription("French Trade and Companies Register");
      party.setListOfIdentifier(new ListOfIdentifierType());
      party.getListOfIdentifier()
          .getIdentifier()
          .add(getIdentifierType(organization.getRegistration(), agency));
    }

    return party;
  }

  public static IdentifierType getIdentifierType(String identValue, AgencyType agency) {
    IdentifierType ident = new IdentifierType();
    ident.setIdent(identValue);
    if (agency != null) {
      ident.setAgency(agency);
    }
    return ident;
  }

  public static InvoiceIdentifierType getInvoiceIdentifierType(String identValue, AgencyType agency) {
    InvoiceIdentifierType ident = new InvoiceIdentifierType();
    ident.setIdent(toComplexStringType(ident.getIdent(), identValue));
    if (agency != null) {
      ident.setAgency(agency);
    }
    return ident;
  }

  public static OrderResponseItemDetailType createOrderResponseItemDetail(Integer lineNb) {
    OrderResponseItemDetailType ordrspDetail = new OrderResponseItemDetailType();
    ordrspDetail.setOriginalItemDetailWithChanges(new OriginalItemDetailWithChangesType());
    ordrspDetail.getOriginalItemDetailWithChanges()
        .setBaseItemDetail(new ResponseBaseItemDetailType());
    ordrspDetail.getOriginalItemDetailWithChanges()
        .getBaseItemDetail()
        .setLineItemNum(new LineItemNumType());
    ordrspDetail.getOriginalItemDetailWithChanges()
        .getBaseItemDetail()
        .getLineItemNum()
        .setBuyerLineItemNum(lineNb);

    return ordrspDetail;
  }
  //
  // private static void calculateAllowOrChargeSummary(Invoice invoice, int precision) {
  // Map<String, BigDecimal> taxes = new HashMap<>();
  //
  // // Header allowances and charges
  // if (invoice.getInvoiceHeader().getInvoiceAllowancesOrCharges() != null) {
  // for (AllowOrChargeType allow : invoice.getInvoiceHeader().getInvoiceAllowancesOrCharges().getAllowOrCharge()) {
  // if (MethodOfHandlingCodeType.INFORMATION_ONLY.equals(allow.getMethodOfHandlingCoded())) {
  // continue;
  // }
  //
  // String taxKey = allow.getTax().get(0).getTaxCategoryCodedOther();
  // BigDecimal taxValue = getAllowanceOrChargeTaxes(allow);
  // taxes.put(taxKey, taxes.getOrDefault(taxKey, ZERO).add(taxValue));
  // }
  //
  //
  // }
  //
  // if (invoice.getInvoiceDetail() != null) {
  // for (InvoiceItemDetailType detail : invoice.getInvoiceDetail().getListOfInvoiceItemDetail().getInvoiceItemDetail()) {
  // if (detail.getInvoicePricingDetail().getLineItemSubTotal() == null) {
  // continue;
  // }
  // BigDecimal total = detail.getInvoicePricingDetail().getLineItemSubTotal().getMonetaryAmount();
  // String taxKey = detail.getInvoicePricingDetail().getTax().get(0).getTaxCategoryCodedOther();
  //
  // taxes.put(taxKey, taxes.getOrDefault(taxKey, ZERO).add(total));
  // }
  // }
  //
  // Map<String, TotalAllowOrChargeType> products = new HashMap<>();
  // Map<String, BigDecimal> productsTotal = new HashMap<>();
  // computeTotal(products, productsTotal, taxes, invoice.getInvoiceHeader().getInvoiceAllowancesOrCharges() == null ?
  // Collections.emptyList() : invoice.getInvoiceHeader().getInvoiceAllowancesOrCharges().getAllowOrCharge(), precision);
  // if (invoice.getInvoiceDetail() != null) {
  // for (InvoiceItemDetailType detail : invoice.getInvoiceDetail().getListOfInvoiceItemDetail().getInvoiceItemDetail()) {
  // if (detail.getInvoicePricingDetail().getItemAllowancesOrCharges() == null) {
  // continue;
  // }
  // computeTotal(products, productsTotal, taxes, detail.getInvoicePricingDetail().getItemAllowancesOrCharges().getAllowOrCharge(),
  // precision);
  // }
  // }
  // for (String key : products.keySet()) {
  // TotalAllowOrChargeType total = products.get(key);
  // // Calcul
  // BigDecimal taxAmount = productsTotal.get(key).divide(new BigDecimal("100")).setScale(precision, ROUND_HALF_EVEN);
  // if (total.getSummaryAllowOrCharge() == null) {
  // total.setSummaryAllowOrCharge(new MonetaryValueType());
  // }
  // total.getSummaryAllowOrCharge().setMonetaryAmount(taxAmount);
  // if (invoice.getInvoiceSummary().getAllowOrChargeSummary() == null) {
  // invoice.getInvoiceSummary().setAllowOrChargeSummary(new AllowOrChargeSummaryType());
  // }
  // invoice.getInvoiceSummary().getAllowOrChargeSummary().getTotalAllowOrCharge().add(total);
  // }
  // }
  //
  // /**
  // * Compute totals
  // * @param products
  // * @param productsTotal
  // * @param taxes
  // * @param charges
  // */
  // private static void computeTotal(
  // Map<String, TotalAllowOrChargeType> products,
  // Map<String, BigDecimal> productsTotal, Map<String, BigDecimal> taxes, List<AllowOrChargeType> charges, int precision) {
  //
  // for (AllowOrChargeType allow : charges) {
  // if (isNotBlank(allow.getAllowanceOrChargeDescription().getRefID())) {
  // String productKey = allow.getAllowanceOrChargeDescription().getRefID();
  // TotalAllowOrChargeType total = products.get(productKey);
  //
  // if (total == null) {
  // total = new TotalAllowOrChargeType();
  // total.setAllowOrChargeIndicatorCoded(AllowOrChargeIndicatorCodeType.CHARGE);
  // total.setAllowanceOrChargeDescription(new AllowOrChgDescType());
  // total.getAllowanceOrChargeDescription().setRefID(allow.getAllowanceOrChargeDescription().getRefID());
  // total.getAllowanceOrChargeDescription().setListOfDescription(allow.getAllowanceOrChargeDescription().getListOfDescription());
  // total.getAllowanceOrChargeDescription().setServiceCoded("Tax–RegulatoryTax");
  // products.put(allow.getAllowanceOrChargeDescription().getRefID(), total);
  // }
  //
  // if (!taxes.isEmpty()) {
  // BigDecimal tax = taxes.get(allow.getTax().get(0).getTaxCategoryCodedOther());
  // BigDecimal totalProduct = productsTotal.get(productKey) != null ? productsTotal.get(productKey) : BigDecimal.ZERO;
  // if (tax != null) {
  // TypeOfAllowanceOrChargeType allowanceType = allow.getTypeOfAllowanceOrCharge();
  // if (allowanceType.getPercentageAllowanceOrCharge() != null &&
  // allowanceType.getPercentageAllowanceOrCharge().getPercentageMonetaryValue() != null) {
  // totalProduct =
  // totalProduct.add(allowanceType.getPercentageAllowanceOrCharge().getPercentageMonetaryValue().getMonetaryAmount().multiply(tax));
  // } else if (allowanceType.getMonetaryValue().getMonetaryAmount() != null) {
  // totalProduct = totalProduct.add(allowanceType.getMonetaryValue().getMonetaryAmount().multiply(tax));
  // }
  // }
  // productsTotal.put(productKey, totalProduct.setScale(precision, ROUND_HALF_EVEN));
  // }
  // }
  // }
  // }
  //
  // /**
  // * Fill the taxes map with allowances and charges data
  // * @param allowance
  // * @param taxes map containing the tax and the amount related to the tax
  // * @return map
  // */
  // private static BigDecimal getAllowanceOrChargeTaxes(AllowOrChargeType allow) {
  // BigDecimal total;
  // if (allow.getTypeOfAllowanceOrCharge().getMonetaryValue() != null) {
  // total = allow.getTypeOfAllowanceOrCharge().getMonetaryValue().getMonetaryAmount();
  // } else {
  // total = allow.getTypeOfAllowanceOrCharge().getPercentageAllowanceOrCharge().getPercentageMonetaryValue().getMonetaryAmount();
  // }
  //
  // if (allow.getIndicatorCoded() == IndicatorCodeType.ALLOWANCE) {
  // total = total.negate();
  // }
  // return total;
  // }
}
