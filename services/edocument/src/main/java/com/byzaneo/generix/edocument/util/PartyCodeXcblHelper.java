package com.byzaneo.generix.edocument.util;

import com.byzaneo.generix.service.repository.bean.Factor;
import com.byzaneo.generix.service.repository.bean.address.Address;
import com.byzaneo.generix.service.repository.bean.address.AddressContact;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoicePartyType;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.*;
import static com.byzaneo.generix.edocument.util.XcblHelper.AGENCY_CODED_OTHER;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getIdentValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static java.util.Collections.emptyList;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Stream.empty;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Jul 11, 2017
 * @since AOI-6417
 */
public class PartyCodeXcblHelper {

  // -- CREATE -

  public static InvoicePartyCodedType createEmptyParty(String partyRoleCoded) {
    InvoicePartyCodedType partyCoded = createInvoicePartyCoded();
    partyCoded.setPartyRoleCoded(partyRoleCoded);
    return partyCoded;
  }

  public static InvoicePartyCodedType createInvoicePartyCoded() {
    InvoicePartyCodedType invoicePartyCodedType = new InvoicePartyCodedType();
    invoicePartyCodedType.setPartyID(new InvoiceIdentifierType());
    InvoiceNameAddressType nameAddressType = new InvoiceNameAddressType();
    nameAddressType.setName1(toComplexStringType(nameAddressType.getName1(), EMPTY));
    nameAddressType.setCity(toComplexStringType(nameAddressType.getCity(), EMPTY));
    nameAddressType.setPostalCode(toComplexStringType(nameAddressType.getPostalCode(), EMPTY));
    nameAddressType.setStreet(toComplexStringType(nameAddressType.getStreet(), EMPTY));
    nameAddressType.setCountry(new CountryType());
    invoicePartyCodedType.setNameAddress(nameAddressType);
    InvoiceIdentifierType invoiceInvoiceIdentifierType = new InvoiceIdentifierType();
    AgencyType agencyType = new AgencyType();
    agencyType.setAgencyCoded("EAN");
    agencyType.setCodeListIdentifierCoded("LocationCode");
    invoiceInvoiceIdentifierType.setAgency(agencyType);
    invoiceInvoiceIdentifierType.setIdent(new ComplexStringType());
    invoicePartyCodedType.setPartyID(invoiceInvoiceIdentifierType);
    InvoicePartyTaxInformationType informationType = new InvoicePartyTaxInformationType();
    invoiceInvoiceIdentifierType = InvoiceXcblHelper.initInvoicePartyTaxInformationType();
    informationType.setTaxIdentifier(invoiceInvoiceIdentifierType);
    invoicePartyCodedType.setPartyTaxInformation(informationType);

    ListOfInvoiceIdentifierType listOfInvoiceIdentifierType = InvoiceXcblHelper.initListOfIdentifierType();
    invoicePartyCodedType.setListOfIdentifier(listOfInvoiceIdentifierType);
    invoicePartyCodedType.setPrimaryContact(initPrimaryContact());
    return invoicePartyCodedType;
  }

  // -- FACTOR RESOLUTION --

  public static final InvoicePartyCodedType getAddressFromFactor(Factor factor, InvoicePartyCodedType party) {
    if (party == null)
      return null;

    InvoiceNameAddressType address = new InvoiceNameAddressType();
    address.setCountry(new CountryType());
    if (factor != null) {
      address.setName1(toComplexStringType(address.getName1(), factor.getName()));
      address.setStreet(toComplexStringType(address.getStreet(), factor.getAddress()));
      address.setPostalCode(toComplexStringType(address.getPostalCode(), factor.getPostalCode()));
      address.setCity(toComplexStringType(address.getCity(), factor.getCity()));
      address.setCountry(factor.getCountry());
    }
    party.setNameAddress(address);

    return party;
  }

  // -- ADDRESS RESOLUTION --
  public static InvoiceContactType initPrimaryContact() {
    InvoiceContactType contactType = new InvoiceContactType();
    contactType.setContactName(toComplexStringType(contactType.getContactName(), null));
    return createEmptyContactMail(ContactNumberTypeCodeType.EMAIL_ADDRESS.name(), contactType);
  }

  public static InvoiceContactType initContactMail(InvoiceContactType contactType) {
    String contactNumberName = ContactNumberTypeCodeType.EMAIL_ADDRESS.name();
    return createEmptyContactMail(contactNumberName, contactType);
  }

  public static InvoiceContactType createEmptyContactMail(String contactNumberName, InvoiceContactType contactType) {
    if (contactType.getListOfContactNumber() == null)
      contactType.setListOfContactNumber(new ListOfInvoiceContactNumberType());
    List<InvoiceContactNumberType> contactNumberList = contactType.getListOfContactNumber()
        .getContactNumber();
    if (contactNumberList.isEmpty() ||
        contactNumberList.stream()
            .noneMatch(cn -> contactNumberName.equals(cn.getContactNumberTypeCoded()
                .name()))) {
      InvoiceContactNumberType contactNumber = new InvoiceContactNumberType();
      contactNumber.setContactNumberValue(new ComplexStringType());
      contactNumber.setContactNumberTypeCoded(ContactNumberTypeCodeType.valueOf(contactNumberName));
      contactType.getListOfContactNumber()
          .getContactNumber()
          .add(contactNumber);
    }
    return contactType;
  }

  public static final InvoicePartyCodedType convert(Address address, ListOfInvoiceIdentifierType identifiers) {
    if (address == null)
      return null;

    InvoicePartyCodedType party = new InvoicePartyCodedType();
    party.setPartyID(new InvoiceIdentifierType());
    party.getPartyID()
        .setIdent(toComplexStringType(party.getPartyID()
            .getIdent(), address.getIdentification()));
    party.setNameAddress(getInvoiceNameAddressFromAddress(address));
    party.setPartyTaxInformation(getInvoiceTaxInformationFromAddress(address.getVatCode()));
    List<AddressContact> contacts = address.getContacts();
    if (!contacts.isEmpty()) {
      party.setPrimaryContact(initContactMail(convertToInvoiceContactType(contacts.get(0))));
      party.setOtherContacts(new ListOfContactType());
      for (AddressContact contact : contacts) {
        party.getOtherContacts()
            .getContact()
            .add(convertToContactType(contact));
      }
    }
    else {
      party.setPrimaryContact(initPrimaryContact());
    }
    switch (address.getTypeAddress()) {
    case BILL_TO:
      party.setPartyRoleCoded(BILL_TO);
      break;
    case BUYER:
      party.setPartyRoleCoded(BUYER);
      break;
    case SELLER:
      party.setPartyRoleCoded(SELLER);
      break;
    case SHIP_TO:
      party.setPartyRoleCoded(SHIP_TO);
      break;
    case FACTOR:
      party.setPartyRoleCoded(FACTOR);
      break;
    case FORWARDER:
      party.setPartyRoleCoded(FORWARDER);
      break;
    case REMIT_TO:
      party.setPartyRoleCoded(REMIT_TO);
      break;
    case BUYER_AGENT:
      party.setPartyRoleCoded(BUYER_AGENT);
      break;
    case SELLER_AGENT:
      party.setPartyRoleCoded(SELLER_AGENT);
      break;
    case PAYER:
      party.setPartyRoleCoded(PAYER);
      break;
    case SELLER_DECLARANTS:
      party.setPartyRoleCoded(DECLARANTS_AGENT);
    default:
      break;
    }
    if (identifiers == null)
      party.setListOfIdentifier(InvoiceXcblHelper.initListOfIdentifierType());
    else
      party.setListOfIdentifier(identifiers);

    Optional<InvoiceIdentifierType> siren = party.getListOfIdentifier()
        .getIdentifier()
        .stream()
        .filter(identifier -> XcblHelper.FR_INSEE.equals(identifier.getAgency()
            .getAgencyCoded()))
        .findFirst();

    if (siren.isPresent() && siren.map(InvoiceIdentifierType::getIdent)
        .isPresent()) {
      siren.get()
          .getIdent()
          .setValue(address.getSiret());
    }

    Optional<InvoiceIdentifierType> rcsRcm = party.getListOfIdentifier()
        .getIdentifier()
        .stream()
        .filter(identifier -> XcblHelper.RCS_RCM.equals(identifier.getAgency()
            .getAgencyCodedOther()))
        .findFirst();

    if (rcsRcm.isPresent() && rcsRcm.map(InvoiceIdentifierType::getIdent)
        .isPresent()) {
      rcsRcm.get()
          .getIdent()
          .setValue(address.getRcs());
    }

    return party;
  }

  private static final InvoiceNameAddressType getInvoiceNameAddressFromAddress(Address address) {
    InvoiceNameAddressType nameAddress = new InvoiceNameAddressType();
    nameAddress.setName1(toComplexStringType(nameAddress.getName1(), address.getName()));
    nameAddress.setCity(toComplexStringType(nameAddress.getCity(), address.getCity()));
    nameAddress.setStreet(toComplexStringType(nameAddress.getStreet(), address.getStreet()));
    nameAddress.setStreetSupplement1(address.getStreetSupplement1());
    nameAddress.setCountry(address.getCountry());
    nameAddress.setPostalCode(toComplexStringType(nameAddress.getPostalCode(), address.getPostalCode()));
    return nameAddress;
  }

  public static final InvoicePartyTaxInformationType getInvoiceTaxInformationFromAddress(String vatCode) {
    InvoicePartyTaxInformationType partyTaxInformation = new InvoicePartyTaxInformationType();
    partyTaxInformation.setTaxIdentifier(InvoiceXcblHelper.initInvoicePartyTaxInformationType());
    partyTaxInformation.getTaxIdentifier()
        .setIdent(toComplexStringType(partyTaxInformation.getTaxIdentifier()
            .getIdent(), vatCode));
    return partyTaxInformation;
  }

  private static final InvoiceContactType convertToInvoiceContactType(AddressContact contact) {
    InvoiceContactType contactType = new InvoiceContactType();
    contactType.setContactName(toComplexStringType(contactType.getContactName(), contact.getName()));

    contactType.setListOfContactNumber(new ListOfInvoiceContactNumberType());

    InvoiceContactNumberType email = new InvoiceContactNumberType();
    email.setContactNumberTypeCoded(ContactNumberTypeCodeType.EMAIL_ADDRESS);
    email.setContactNumberValue(toComplexStringType(email.getContactNumberValue(), contact.getEmail()));
    contactType.getListOfContactNumber()
        .getContactNumber()
        .add(email);

    InvoiceContactNumberType tel = new InvoiceContactNumberType();
    tel.setContactNumberTypeCoded(ContactNumberTypeCodeType.TELEPHONE_NUMBER);
    tel.setContactNumberValue(toComplexStringType(tel.getContactNumberValue(), contact.getTel()));
    contactType.getListOfContactNumber()
        .getContactNumber()
        .add(tel);

    return contactType;
  }

  private static final ContactType convertToContactType(AddressContact contact) {
    ContactType contactType = new ContactType();
    contactType.setContactName(contact.getName());

    contactType.setListOfContactNumber(new ListOfContactNumberType());

    ContactNumberType email = new ContactNumberType();
    email.setContactNumberTypeCoded(ContactNumberTypeCodeType.EMAIL_ADDRESS);
    email.setContactNumberValue(contact.getEmail());
    contactType.getListOfContactNumber()
        .getContactNumber()
        .add(email);

    ContactNumberType tel = new ContactNumberType();
    tel.setContactNumberTypeCoded(ContactNumberTypeCodeType.TELEPHONE_NUMBER);
    tel.setContactNumberValue(contact.getTel());
    contactType.getListOfContactNumber()
        .getContactNumber()
        .add(tel);

    return contactType;
  }

  // -- GETTER --

  public static InvoicePartyCodedType getInvoicePartyCodedFromRole(final List<InvoicePartyCodedType> listOfPartyCoded, final String role) {
    return listOfPartyCoded.stream()
        .filter(p -> role.equals(p.getPartyRoleCoded()))
        .findAny()
        .orElse(null);
  }

  public static String getInvoicePartyIdent(Optional<InvoiceOCRPartyType> partyType) {
    return partyType.map(InvoiceOCRPartyType::getPartyID)
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .orElse(null);
  }

  public static String getBillToPartyIdent(final Invoice invoice) {
    return of(invoice).map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceParty)
        .map(InvoicePartyType::getBillToParty)
        .map(InvoiceOCRPartyType::getPartyID)
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .orElse(null);
  }

  public static String getSellerPartyIdent(final Invoice invoice) {
    return of(invoice).map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceParty)
        .map(InvoicePartyType::getSellerParty)
        .map(InvoiceOCRPartyType::getPartyID)
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .orElse(null);
  }

  public static String getFactorPartyIdent(final Invoice invoice) {
    return ofNullable(invoice)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceParty)
        .map(InvoicePartyType::getListOfPartyCoded)
        .map(ListOfInvoicePartyCodedType::getPartyCoded)
        .map(List::stream)
        .orElse(empty())
        .filter(Objects::nonNull)
        .filter(party -> InvoiceXcblHelper.FACTOR.equals(party.getPartyRoleCoded()))
        .findAny()
        .map(InvoicePartyCodedType::getPartyID)
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .orElse(null);
  }

  public static String getSiren(InvoiceOCRPartyType party) {
    return of(party).map(InvoiceOCRPartyType::getListOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .orElseGet(() -> new ArrayList<>())
        .stream()
        .filter(identifier -> XcblHelper.FR_INSEE.equals(identifier.getAgency()
            .getAgencyCoded()))
        .findFirst()
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .orElse("");
  }

  // -- SETTER --

  public static void updateListOfPartyCodedType(final Invoice invoice, List<InvoicePartyCodedType> listOfOtherPartyCoded) {
    if (!listOfOtherPartyCoded.isEmpty()) {
      of(invoice).map(Invoice::getInvoiceHeader)
          .map(InvoiceHeaderType::getInvoiceParty)
          .ifPresent(party ->
          {
            if (party.getListOfPartyCoded() == null)
              party.setListOfPartyCoded(new ListOfInvoicePartyCodedType());

            List<InvoicePartyCodedType> partyCoded = party.getListOfPartyCoded()
                .getPartyCoded();
            listOfOtherPartyCoded.forEach(partyOther -> {
              partyCoded.removeIf(x -> partyOther.getPartyRoleCoded()
                  .equals(x.getPartyRoleCoded()));
            });
            partyCoded.addAll(listOfOtherPartyCoded);
          });
    }
  }

  public static void updatePartyAgencyCodedType(Invoice invoice, InvoicePartyCodedType party) {
    of(invoice).map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceParty)
        .map(InvoicePartyType::getSellerParty)
        .map(InvoiceOCRPartyType::getPartyID)
        .ifPresent(ident ->
        {
          ident.setIdent(toComplexStringType(ident.getIdent(), getIdentValue(party.getPartyID())));
          AgencyType agency = Optional.ofNullable(party)
              .map(InvoicePartyCodedType::getPartyID)
              .map(InvoiceIdentifierType::getAgency)
              .orElse(null);
          if (agency == null)
            return;
          else if (ident.getAgency() == null)
            ident.setAgency(new AgencyType());
          ident.getAgency()
              .setAgencyCoded("Other");
          ident.getAgency()
              .setAgencyCodedOther(agency
                  .getAgencyCodedOther());
        });
  }

  // -- CLEANER --

  public static void removeEmptyPartyTaxInformation(final InvoicePartyCodedType party) {
    if (ofNullable(party.getPartyTaxInformation())
        .map(InvoicePartyTaxInformationType::getTaxIdentifier)
        .map(InvoiceIdentifierType::getIdent)
        .map(ComplexStringType::getValue)
        .isEmpty() && ofNullable(party.getPartyTaxInformation()).map(InvoicePartyTaxInformationType::getTaxIdentifier)
        .map(InvoiceIdentifierType::getAgency)
        .isEmpty())
      party.setPartyTaxInformation(null);
    ofNullable(party.getListOfIdentifier())
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .filter(List::isEmpty)
        .ifPresent(unused -> party.setPartyTaxInformation(null));
  }

  public static void removeUnusedIdentifier(final InvoiceOCRPartyType party) {
    ofNullable(party).map(InvoiceOCRPartyType::getListOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .ifPresent(identifierValues -> identifierValues.removeIf(value -> isBlank(InvoiceHelper.getComplexStringValue(value.getIdent()))));
  }

  // -- POPULATE --

  public static void populateInvoiceParty(Invoice invoice, List<InvoicePartyCodedType> listOfPartyCoded, Partner partner) {
    List<InvoicePartyCodedType> listOfOtherPartyCoded = new ArrayList<>();
    for (InvoicePartyCodedType party : listOfPartyCoded) {
      if (!SHIP_TO.equals(party.getPartyRoleCoded()) && !SHIP_FROM.equals(party.getPartyRoleCoded())) {
        removeEmptyPartyTaxInformation(party);
        removeUnusedIdentifier(party);
      }
      switch (party.getPartyRoleCoded()) {
      case BILL_TO:
        invoice.getInvoiceHeader()
            .getInvoiceParty()
            .setBillToParty(party);
        break;
      case BUYER:
        invoice.getInvoiceHeader()
            .getInvoiceParty()
            .setBuyerParty(party);
        break;
      case SELLER:
        invoice.getInvoiceHeader()
            .getInvoiceParty()
            .setSellerParty(party);
        // Case of PT country : Update code of the agency
        if (CountryCodeType.PT.toString()
            .equals(
                partner.getLocation()
                    .getAddress()
                    .getCountry()
                    .getCountry())) {
          of(invoice).map(Invoice::getInvoiceHeader)
              .map(InvoiceHeaderType::getInvoiceParty)
              .map(InvoicePartyType::getSellerParty)
              .map(InvoiceOCRPartyType::getPartyID)
              .ifPresent(p ->
              {
                p.setIdent(toComplexStringType(p.getIdent(), getIdentValue(party.getPartyID())));
                p.getAgency()
                    .setAgencyCoded("Other");
                p.getAgency()
                    .setAgencyCodedOther(party.getPartyID()
                        .getAgency()
                        .getAgencyCodedOther());
              });
        }
        break;
      case SHIP_TO:
        invoice.getInvoiceHeader()
            .getInvoiceParty()
            .setShipToParty(party);
        break;
      case SHIP_FROM:
        invoice.getInvoiceHeader()
            .getInvoiceParty()
            .setShipFromParty(party);
        break;
      case REMIT_TO:
        invoice.getInvoiceHeader()
            .getInvoiceParty()
            .setRemitToParty(party);
        break;
      default:
        listOfOtherPartyCoded.add(party);
        break;
      }
    }
    updateListOfPartyCodedType(invoice, listOfOtherPartyCoded);
  }

  public static Optional<InvoicePartyCodedType> getBillTo(List<InvoicePartyCodedType> listOfPartyCoded, boolean isChorusPro) {
    return Optional.ofNullable(listOfPartyCoded)
        .orElse(emptyList())
        .stream()
        .filter(party -> (isChorusPro ? InvoiceXcblHelper.BUYER : InvoiceXcblHelper.BILL_TO).equals(party.getPartyRoleCoded()))
        .findFirst();
  }

  public static String getBillToName(List<InvoicePartyCodedType> listOfPartyCoded, boolean isChorusPro) {
    if (PartyCodeXcblHelper.getBillTo(listOfPartyCoded, isChorusPro)
        .isPresent())
      return Optional.ofNullable(PartyCodeXcblHelper.getBillTo(listOfPartyCoded, isChorusPro)
              .get()
              .getNameAddress())
          .map(InvoiceNameAddressType::getName1)
          .map(ComplexStringType::getValue)
          .orElse("");
    return "";
  }

  public static String getBillToTva(List<InvoicePartyCodedType> listOfPartyCoded, boolean isChorusPro) {
    if (PartyCodeXcblHelper.getBillTo(listOfPartyCoded, isChorusPro)
        .isPresent())
      return Optional.ofNullable(PartyCodeXcblHelper.getBillTo(listOfPartyCoded, isChorusPro)
              .get()
              .getPartyTaxInformation())
          .map(InvoicePartyTaxInformationType::getTaxIdentifier)
          .map(InvoiceIdentifierType::getIdent)
          .map(ComplexStringType::getValue)
          .orElse("");
    return "";
  }

  public static String getBillToSiren(List<InvoicePartyCodedType> listOfPartyCoded, boolean isChorusPro) {
    if (PartyCodeXcblHelper.getBillTo(listOfPartyCoded, isChorusPro)
        .isPresent())
      return PartyCodeXcblHelper.getSiren(PartyCodeXcblHelper.getBillTo(listOfPartyCoded, isChorusPro)
          .get());

    return "";

  }

  public static <T extends InvoiceOCRPartyType> void copyPartyProperties(T partyFrom, T partyTo) {
    InvoiceOCRPartyType clone = XcblHelper.cloner.deepClone(partyFrom);

    // ID
    partyTo.getPartyID()
        .setIdent(toComplexStringType(partyTo.getPartyID()
            .getIdent(), getIdentValue(clone.getPartyID())));
    // Address
    partyTo.setNameAddress(clone.getNameAddress());
    // ListOfIdentifier
    partyTo.setListOfIdentifier(clone.getListOfIdentifier());
    // PartyTaxInformation
    partyTo.setPartyTaxInformation(clone.getPartyTaxInformation());
  }

  public static Optional<InvoiceIdentifierType> getFormJuridique(ListOfInvoiceIdentifierType listOfIdentifier) {
    return Optional.ofNullable(listOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .map(List::stream)
        .orElse(empty())
        .filter(identifier -> isFormJuridique(identifier))
        .findFirst();
  }

  public static boolean isFormJuridique(InvoiceIdentifierType identifier) {
    Optional<AgencyType> agency = Optional.ofNullable(identifier)
        .map(InvoiceIdentifierType::getAgency);
    if (agency.isPresent()) {
      if (InvoiceXcblHelper.ASSIGNED_BY_NATIONAL_TRADE_AGENCY.equals(
          agency.map(AgencyType::getAgencyCoded)
              .orElse(null)) &&
          "BusinessLegalStructureType".equals(agency.map(AgencyType::getCodeListIdentifierCoded)
              .orElse(null)))
        return true;
    }
    return false;
  }

  public static Optional<InvoiceIdentifierType> getShareCapital(ListOfInvoiceIdentifierType listOfIdentifier) {
    return Optional.ofNullable(listOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .map(List::stream)
        .orElse(empty())
        .filter(identifier -> isShareCapital(identifier))
        .findFirst();
  }

  public static boolean isShareCapital(InvoiceIdentifierType identifier) {
    Optional<AgencyType> agency = Optional.ofNullable(identifier)
        .map(InvoiceIdentifierType::getAgency);
    if (agency.isPresent()) {
      if (InvoiceXcblHelper.CEC.equals(
          agency.map(AgencyType::getAgencyCoded)
              .orElse(null)) &&
          AGENCY_CODED_OTHER.equals(agency.map(AgencyType::getCodeListIdentifierCoded)
              .orElse(null)) &&
          InvoiceXcblHelper.LEGAL_CAPITAL.equals(agency.map(AgencyType::getCodeListIdentifierCodedOther)
              .orElse(null)))
        return true;
    }
    return false;
  }

  public static Optional<InvoiceIdentifierType> getSiren(ListOfInvoiceIdentifierType listOfIdentifier) {
    return Optional.ofNullable(listOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .map(List::stream)
        .orElse(empty())
        .filter(identifier -> isSiren(identifier))
        .findFirst();
  }

  public static boolean isSiren(InvoiceIdentifierType identifier) {
    Optional<AgencyType> agency = Optional.ofNullable(identifier)
        .map(InvoiceIdentifierType::getAgency);
    if (agency.isPresent()) {
      if (InvoiceXcblHelper.FR_INSEE.equals(
          agency.map(AgencyType::getAgencyCoded)
              .orElse(null)) &&
          AGENCY_CODED_OTHER.equals(agency.map(AgencyType::getCodeListIdentifierCoded)
              .orElse(null)) &&
          InvoiceXcblHelper.SIREN.equals(agency.map(AgencyType::getCodeListIdentifierCodedOther)
              .orElse(null)))
        return true;
    }
    return false;
  }

  public static Optional<InvoiceIdentifierType> getRcsRcm(ListOfInvoiceIdentifierType listOfIdentifier) {
    return Optional.ofNullable(listOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .map(List::stream)
        .orElse(empty())
        .filter(identifier -> isRcsRcm(identifier))
        .findFirst();
  }

  public static boolean isRcsRcm(InvoiceIdentifierType identifier) {
    Optional<AgencyType> agency = Optional.ofNullable(identifier)
        .map(InvoiceIdentifierType::getAgency);
    if (agency.isPresent()) {
      if (AGENCY_CODED_OTHER.equals(
          agency.map(AgencyType::getAgencyCoded)
              .orElse(null)) &&
          InvoiceXcblHelper.RCS_RCM.equals(agency.map(AgencyType::getAgencyCodedOther)
              .orElse(null)) &&
          InvoiceXcblHelper.FRENCH_TRADE_AND_COMPANIES_REGISTER.equals(agency.map(AgencyType::getAgencyDescription)
              .orElse(null)))
        return true;
    }
    return false;
  }

  // Portugal Party InvoiceIdentifierTypes(GOVERNMENT-HASHCODE & NIF)

  public static Optional<InvoiceIdentifierType> getGovernmentHashcode(ListOfInvoiceIdentifierType listOfIdentifier) {
    return Optional.ofNullable(listOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .map(List::stream)
        .orElse(empty())
        .filter(identifier -> isGovernmentHashcode(identifier))
        .findFirst();
  }

  public static boolean isGovernmentHashcode(InvoiceIdentifierType identifier) {
    Optional<AgencyType> agency = Optional.ofNullable(identifier)
        .map(InvoiceIdentifierType::getAgency);
    if (agency.isPresent()) {
      if (AGENCY_CODED_OTHER.equals(
          agency.map(AgencyType::getAgencyCoded)
              .orElse(null)) &&
          InvoiceXcblHelper.GOVERNMENT_HASHCODE.equals(agency.map(AgencyType::getAgencyCodedOther)
              .orElse(null)))
        return true;
    }
    return false;
  }

  public static Optional<InvoiceIdentifierType> getNIF(ListOfInvoiceIdentifierType listOfIdentifier) {
    return Optional.ofNullable(listOfIdentifier)
        .map(ListOfInvoiceIdentifierType::getIdentifier)
        .map(List::stream)
        .orElse(empty())
        .filter(identifier -> isNIF(identifier))
        .findFirst();
  }

  public static boolean isNIF(InvoiceIdentifierType identifier) {
    Optional<AgencyType> agency = Optional.ofNullable(identifier)
        .map(InvoiceIdentifierType::getAgency);
    if (agency.isPresent()) {
      if (AGENCY_CODED_OTHER.equals(
          agency.map(AgencyType::getAgencyCoded)
              .orElse(null)) &&
          InvoiceXcblHelper.NIF.equals(agency.map(AgencyType::getAgencyCodedOther)
              .orElse(null)))
        return true;
    }
    return false;
  }

  // Form Juridique (Legal Capital)
  public static boolean hasFormJuridique(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null || CollectionUtils.isEmpty(listOfIdentifier.getIdentifier()))
      return false;
    return PartyCodeXcblHelper.getFormJuridique(listOfIdentifier)
        .isPresent();
  }

  public static int getFormJuridiqueIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    List<InvoiceIdentifierType> identifiers = listOfIdentifier.getIdentifier();
    for (int i = 0; i < identifiers.size(); i++) {
      if (PartyCodeXcblHelper.isFormJuridique(identifiers.get(i)))
        return i;
    }
    return -1;
  }

  // Share Capital
  public static boolean hasShareCapital(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null || CollectionUtils.isEmpty(listOfIdentifier.getIdentifier()))
      return false;
    return PartyCodeXcblHelper.getFormJuridique(listOfIdentifier)
        .isPresent();
  }

  public static int getShareCapitalIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    List<InvoiceIdentifierType> identifiers = listOfIdentifier.getIdentifier();
    for (int i = 0; i < identifiers.size(); i++) {
      if (PartyCodeXcblHelper.isShareCapital(identifiers.get(i)))
        return i;
    }
    return -1;
  }

  // Siren
  public static boolean hasSiren(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null || CollectionUtils.isEmpty(listOfIdentifier.getIdentifier()))
      return false;
    return PartyCodeXcblHelper.getSiren(listOfIdentifier)
        .isPresent();
  }

  public static int getSirenIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    List<InvoiceIdentifierType> identifiers = listOfIdentifier.getIdentifier();
    for (int i = 0; i < identifiers.size(); i++) {
      if (PartyCodeXcblHelper.isSiren(identifiers.get(i)))
        return i;
    }
    return -1;
  }

  // Rcs-Rcm
  public static boolean hasRcsRcm(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null || CollectionUtils.isEmpty(listOfIdentifier.getIdentifier()))
      return false;
    return PartyCodeXcblHelper.getRcsRcm(listOfIdentifier)
        .isPresent();
  }

  public static int getRcsRcmIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    List<InvoiceIdentifierType> identifiers = listOfIdentifier.getIdentifier();
    for (int i = 0; i < identifiers.size(); i++) {
      if (PartyCodeXcblHelper.isRcsRcm(identifiers.get(i)))
        return i;
    }
    return -1;
  }

  // PT: Government-Hashcode
  public static boolean hasGovernmentHashcode(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null || CollectionUtils.isEmpty(listOfIdentifier.getIdentifier()))
      return false;
    return PartyCodeXcblHelper.getGovernmentHashcode(listOfIdentifier)
        .isPresent();
  }

  public static int getGovernmentHashcodeIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    List<InvoiceIdentifierType> identifiers = listOfIdentifier.getIdentifier();
    for (int i = 0; i < identifiers.size(); i++) {
      if (PartyCodeXcblHelper.isGovernmentHashcode(identifiers.get(i)))
        return i;
    }
    return -1;
  }

  // PT: NIF
  public static boolean hasNIF(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null || CollectionUtils.isEmpty(listOfIdentifier.getIdentifier()))
      return false;
    return PartyCodeXcblHelper.getNIF(listOfIdentifier)
        .isPresent();
  }

  public static int getNIFIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    List<InvoiceIdentifierType> identifiers = listOfIdentifier.getIdentifier();
    for (int i = 0; i < identifiers.size(); i++) {
      if (PartyCodeXcblHelper.isNIF(identifiers.get(i)))
        return i;
    }
    return -1;
  }

  public static int getEmailAddressIndex(ListOfInvoiceContactNumberType listOfContactNumberType) {
    List<InvoiceContactNumberType> contactNumberList = ofNullable(listOfContactNumberType)
        .map(ListOfInvoiceContactNumberType::getContactNumber)
        .orElse(emptyList());
    for (int i = 0; i < contactNumberList.size(); i++) {
      if (PartyCodeXcblHelper.isEmailAddress(contactNumberList.get(i)))
        return i;
    }
    return -1;
  }

  public static String getEmailAddress(List<InvoiceContactNumberType> invoiceContactNumberTypes) {
      return invoiceContactNumberTypes.stream()
              .filter(PartyCodeXcblHelper::isEmailAddress)
              .findFirst()
              .map(InvoiceContactNumberType::getContactNumberValue)
              .map(ComplexStringType::getValue)
              .orElse(null);
  }

  public static boolean isEmailAddress(InvoiceContactNumberType contactNumber) {
    Optional<ContactNumberTypeCodeType> contactNumberTypeCodeType = ofNullable(contactNumber)
        .map(InvoiceContactNumberType::getContactNumberTypeCoded);
    if (contactNumberTypeCodeType.isPresent() && ContactNumberTypeCodeType.EMAIL_ADDRESS.value()
        .equals(contactNumberTypeCodeType.get()
            .value())) {
      return true;
    }
    return false;
  }
}
