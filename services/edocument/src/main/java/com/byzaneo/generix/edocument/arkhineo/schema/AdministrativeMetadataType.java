//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.09.04 at 11:26:18 AM CEST 
//

package com.byzaneo.generix.edocument.arkhineo.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * Administrative metadata element contain all administrative data of archive : - Spatio-temporal coordinates (customerId, cfeId, sectionId,
 * archiveId, depositDate, initial end of life, status - Digests of data and metadata sent by client - protocol information gathered during
 * deposit (may be absent if no data-object provided) - Depositor information - Link to previous version of archive - Optional Note : The
 * global status of an archive is obtained by examining each archive process status : - if at least one process has status Waiting, global
 * status is Waiting. - otherwise, if at least one process has status Error, global status is Error. - otherwise, if at least one process
 * has status Warning, global status is warning. - otherwise, global status is Done.
 * <p>
 * Java class for administrative-metadata-type complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="administrative-metadata-type"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;all&gt;
 *         &lt;element name="previous-version" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="archive-id" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformArchiveID" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="depositor-info" type="{http://www.arkhineo.fr/CFE/metadata/1.1}depositor-info-type"/&gt;
 *         &lt;element name="digests" type="{http://www.arkhineo.fr/CFE/metadata/1.1}digests-type"/&gt;
 *         &lt;element name="protocol-info" type="{http://www.arkhineo.fr/CFE/metadata/1.1}protocol-info-type" minOccurs="0"/&gt;
 *       &lt;/all&gt;
 *       &lt;attribute name="deposit-date" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformDate" /&gt;
 *       &lt;attribute name="status" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformProcessStatus" /&gt;
 *       &lt;attribute name="end-of-life-date" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformDate" /&gt;
 *       &lt;attribute name="client-id" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformGUID" /&gt;
 *       &lt;attribute name="archive-id" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformArchiveID" /&gt;
 *       &lt;attribute name="section-id" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformGUID" /&gt;
 *       &lt;attribute name="cfe-id" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformGUID" /&gt;
 *       &lt;attribute name="compartment-id" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformGUID" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "administrative-metadata-type", propOrder = {

})
public class AdministrativeMetadataType {

  @XmlElement(name = "previous-version")
  protected AdministrativeMetadataType.PreviousVersion previousVersion;

  @XmlElement(name = "depositor-info", required = true)
  protected DepositorInfoType depositorInfo;

  @XmlElement(required = true)
  protected DigestsType digests;

  @XmlElement(name = "protocol-info")
  protected ProtocolInfoType protocolInfo;

  @XmlAttribute(name = "deposit-date", required = true)
  protected String depositDate;

  @XmlAttribute(name = "status", required = true)
  protected CfePlatformProcessStatus status;

  @XmlAttribute(name = "end-of-life-date", required = true)
  protected String endOfLifeDate;

  @XmlAttribute(name = "client-id", required = true)
  protected String clientId;

  @XmlAttribute(name = "archive-id", required = true)
  protected String archiveId;

  @XmlAttribute(name = "section-id", required = true)
  protected String sectionId;

  @XmlAttribute(name = "cfe-id", required = true)
  protected String cfeId;

  @XmlAttribute(name = "compartment-id")
  protected String compartmentId;

  /**
   * Gets the value of the previousVersion property.
   * 
   * @return possible object is {@link AdministrativeMetadataType.PreviousVersion }
   */
  public AdministrativeMetadataType.PreviousVersion getPreviousVersion() {
    return previousVersion;
  }

  /**
   * Sets the value of the previousVersion property.
   * 
   * @param value allowed object is {@link AdministrativeMetadataType.PreviousVersion }
   */
  public void setPreviousVersion(AdministrativeMetadataType.PreviousVersion value) {
    this.previousVersion = value;
  }

  /**
   * Gets the value of the depositorInfo property.
   * 
   * @return possible object is {@link DepositorInfoType }
   */
  public DepositorInfoType getDepositorInfo() {
    return depositorInfo;
  }

  /**
   * Sets the value of the depositorInfo property.
   * 
   * @param value allowed object is {@link DepositorInfoType }
   */
  public void setDepositorInfo(DepositorInfoType value) {
    this.depositorInfo = value;
  }

  /**
   * Gets the value of the digests property.
   * 
   * @return possible object is {@link DigestsType }
   */
  public DigestsType getDigests() {
    return digests;
  }

  /**
   * Sets the value of the digests property.
   * 
   * @param value allowed object is {@link DigestsType }
   */
  public void setDigests(DigestsType value) {
    this.digests = value;
  }

  /**
   * Gets the value of the protocolInfo property.
   * 
   * @return possible object is {@link ProtocolInfoType }
   */
  public ProtocolInfoType getProtocolInfo() {
    return protocolInfo;
  }

  /**
   * Sets the value of the protocolInfo property.
   * 
   * @param value allowed object is {@link ProtocolInfoType }
   */
  public void setProtocolInfo(ProtocolInfoType value) {
    this.protocolInfo = value;
  }

  /**
   * Gets the value of the depositDate property.
   * 
   * @return possible object is {@link String }
   */
  public String getDepositDate() {
    return depositDate;
  }

  /**
   * Sets the value of the depositDate property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setDepositDate(String value) {
    this.depositDate = value;
  }

  /**
   * Gets the value of the status property.
   * 
   * @return possible object is {@link CfePlatformProcessStatus }
   */
  public CfePlatformProcessStatus getStatus() {
    return status;
  }

  /**
   * Sets the value of the status property.
   * 
   * @param value allowed object is {@link CfePlatformProcessStatus }
   */
  public void setStatus(CfePlatformProcessStatus value) {
    this.status = value;
  }

  /**
   * Gets the value of the endOfLifeDate property.
   * 
   * @return possible object is {@link String }
   */
  public String getEndOfLifeDate() {
    return endOfLifeDate;
  }

  /**
   * Sets the value of the endOfLifeDate property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setEndOfLifeDate(String value) {
    this.endOfLifeDate = value;
  }

  /**
   * Gets the value of the clientId property.
   * 
   * @return possible object is {@link String }
   */
  public String getClientId() {
    return clientId;
  }

  /**
   * Sets the value of the clientId property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setClientId(String value) {
    this.clientId = value;
  }

  /**
   * Gets the value of the archiveId property.
   * 
   * @return possible object is {@link String }
   */
  public String getArchiveId() {
    return archiveId;
  }

  /**
   * Sets the value of the archiveId property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setArchiveId(String value) {
    this.archiveId = value;
  }

  /**
   * Gets the value of the sectionId property.
   * 
   * @return possible object is {@link String }
   */
  public String getSectionId() {
    return sectionId;
  }

  /**
   * Sets the value of the sectionId property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setSectionId(String value) {
    this.sectionId = value;
  }

  /**
   * Gets the value of the cfeId property.
   * 
   * @return possible object is {@link String }
   */
  public String getCfeId() {
    return cfeId;
  }

  /**
   * Sets the value of the cfeId property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setCfeId(String value) {
    this.cfeId = value;
  }

  /**
   * Gets the value of the compartmentId property.
   * 
   * @return possible object is {@link String }
   */
  public String getCompartmentId() {
    return compartmentId;
  }

  /**
   * Sets the value of the compartmentId property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setCompartmentId(String value) {
    this.compartmentId = value;
  }

  /**
   * <p>
   * Java class for anonymous complex type.
   * <p>
   * The following schema fragment specifies the expected content contained within this class.
   * 
   * <pre>
   * &lt;complexType&gt;
   *   &lt;complexContent&gt;
   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
   *       &lt;attribute name="archive-id" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformArchiveID" /&gt;
   *     &lt;/restriction&gt;
   *   &lt;/complexContent&gt;
   * &lt;/complexType&gt;
   * </pre>
   */
  @XmlAccessorType(XmlAccessType.FIELD)
  @XmlType(name = "")
  public static class PreviousVersion {

    @XmlAttribute(name = "archive-id", required = true)
    protected String archiveId;

    /**
     * Gets the value of the archiveId property.
     * 
     * @return possible object is {@link String }
     */
    public String getArchiveId() {
      return archiveId;
    }

    /**
     * Sets the value of the archiveId property.
     * 
     * @param value allowed object is {@link String }
     */
    public void setArchiveId(String value) {
      this.archiveId = value;
    }

  }

}
