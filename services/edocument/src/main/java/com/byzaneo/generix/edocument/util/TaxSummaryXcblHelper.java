package com.byzaneo.generix.edocument.util;

import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;

import java.math.BigDecimal;
import java.util.*;

import static com.byzaneo.generix.edocument.util.TaxXcblHelper.VALUE_ADDED_TAX;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxableAmountFromInvoiceTaxSummaryType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.ALLOWANCE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxCategoryCodeType.OTHER;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxFunctionQualifierCodeType.TAX;
import static java.math.BigDecimal.ROUND_HALF_EVEN;
import static java.math.BigDecimal.ZERO;
import static java.util.Collections.emptyList;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> Lemesle <<EMAIL>>
 * @company Byzaneo
 * @date 05 April, 2018
 */
public class TaxSummaryXcblHelper {

  // -- CREATE -

  public static void createTaxSummary(Invoice invoice) {
    invoice.getInvoiceSummary()
        .setListOfTaxSummary(new ListOfInvoiceTaxSummaryType());
  }

  // -- GETTER --

  public static List<InvoiceTaxSummaryType> getTaxSummary(Invoice invoice) {
    return of(invoice)
        .map(Invoice::getInvoiceSummary)
        .map(InvoiceSummaryType::getListOfTaxSummary)
        .map(ListOfInvoiceTaxSummaryType::getTaxSummary)
        .orElse(emptyList());
  }

  // -- REMOVE --

  public static void removeTaxSummary(Invoice invoice) {
    invoice.getInvoiceSummary()
        .setListOfTaxSummary(null);
  }

  // -- POPULATE --

  public static void populateInvoiceTaxSummary(Map<String, InvoiceTaxSummaryType> taxSummaries, TaxType tax, int precision) {
    populateInvoiceTaxSummary(taxSummaries, tax, precision, null);
  }
  
  public static void populateInvoiceParafiscalTaxSummary(Map<String, TotalAllowOrChargeType> parafiscalTaxes,
      List<InvoiceAllowOrChargeType> allowOrCharges, int precision, BigDecimal invoiceQty) {
    if (parafiscalTaxes == null)
      return;
    BigDecimal typeOfAllowOrCharge = null;
    for (InvoiceAllowOrChargeType allowOrCharge : allowOrCharges) {
      String description = allowOrCharge.getAllowanceOrChargeDescription()
          .getListOfDescription()
          .getValue();
      typeOfAllowOrCharge = BasisCodeType.MONETARY_AMOUNT.equals(allowOrCharge.getBasisCoded())
          ? allowOrCharge.getTypeOfAllowanceOrCharge()
              .getMonetaryValue()
              .getMonetaryAmount()
              .getValue()
          : allowOrCharge.getTypeOfAllowanceOrCharge()
              .getPercentageAllowanceOrCharge()
              .getPercentageMonetaryValue()
              .getMonetaryAmount();
      if (parafiscalTaxes.containsKey(description)) {
        // Tax Amount
        BigDecimal totalTaxableAmount = parafiscalTaxes.get(description)
            .getSummaryAllowOrCharge()
            .getMonetaryAmount()
            .setScale(precision, ROUND_HALF_EVEN);
        parafiscalTaxes.get(description)
            .getSummaryAllowOrCharge()
            .setMonetaryAmount(totalTaxableAmount.add(typeOfAllowOrCharge.multiply(invoiceQty)));
      }
      else {
        TotalAllowOrChargeType totalAllowOrCharge = new TotalAllowOrChargeType();
        totalAllowOrCharge.setAllowOrChargeIndicatorCoded(AllowOrChargeIndicatorCodeType.CHARGE);
        totalAllowOrCharge.setAllowanceOrChargeDescription(new AllowOrChgDescType());
        totalAllowOrCharge.getAllowanceOrChargeDescription()
            .setServiceCoded("Tax-RegulatoryTax");
        totalAllowOrCharge.getAllowanceOrChargeDescription()
            .setListOfDescription(allowOrCharge
                .getAllowanceOrChargeDescription()
                .getListOfDescription()
                .getValue());
        totalAllowOrCharge.getAllowanceOrChargeDescription()
            .setRefID(allowOrCharge
                .getAllowanceOrChargeDescription()
                .getRefID());
        totalAllowOrCharge.setSummaryAllowOrCharge(new MonetaryValueType());
        totalAllowOrCharge.getSummaryAllowOrCharge()
            .setMonetaryAmount(typeOfAllowOrCharge.multiply(invoiceQty));
        parafiscalTaxes.put(description, totalAllowOrCharge);
      }
    }
  }

  /**
   * Get the first tax of each element of the list and add it to taxSummaries
   *
   * @param taxSummaries
   * @param list
   * @param precision
   */
  public static void populateInvoiceTaxSummary(Map<String, InvoiceTaxSummaryType> taxSummaries, List<InvoiceItemDetailType> list,
      int precision) {
    list.forEach(item -> ofNullable(item)
        .map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getTax)
        .filter(l -> !l.isEmpty())
        .map(l -> l.get(0))
        .ifPresent(tax -> InvoiceTaxSummaryXcblHelper.populateInvoiceTaxSummary(taxSummaries, tax, precision)));
  }

  /**
   * Adds or subtracts the tax remise/charge from the tax summary
   *
   * @param taxSummaries tax summaries grouped by the tax percent
   * @param tax the tax to be computed
   * @param precision
   * @param indicatorCode
   */
  public static void populateInvoiceTaxSummary(Map<String, InvoiceTaxSummaryType> taxSummaries, TaxType tax, int precision,
      IndicatorCodeType indicatorCode) {
    // Sanity Check
    if (taxSummaries == null || tax == null || tax.getTaxCategoryCodedOther() == null || tax.getTaxableAmount() == null ||
        tax.getTaxAmount() == null)
      return;

    // Key : tax.getTaxCategoryCodedOther()
    // based on the indicatorCode add/subtract the taxes and set the specified scale (round)
    taxSummaries.compute(tax.getTaxCategoryCodedOther(), (key, value) -> {
      // If value is not present, create the object
      if (value == null) {
        value = new InvoiceTaxSummaryType();
        // Coded
        value.setTaxTypeCoded(VALUE_ADDED_TAX);
        value.setTaxFunctionQualifierCoded(TAX);
        value.setTaxCategoryCoded(OTHER);
        value.setTaxCategoryCodedOther(toComplexStringType(value.getTaxCategoryCodedOther(), key));
        // Taxable amount
        value.setTaxableAmount(toComplexBigDecimalType(value.getTaxableAmount(),
            computeValue(ZERO, tax.getTaxableAmount(), indicatorCode).setScale(precision, ROUND_HALF_EVEN)));
        // Tax amount
        value.setTaxAmount(computeValue(ZERO, tax.getTaxAmount(), indicatorCode).setScale(precision, ROUND_HALF_EVEN));
        value.setTaxAmountInTaxAccountingCurrency(
            toComplexBigDecimalType(value.getTaxAmountInTaxAccountingCurrency(), value.getTaxAmount()));
      }
      else {
        // Taxable amount
        value.setTaxableAmount(toComplexBigDecimalType(value.getTaxableAmount(),
            computeValue(getTaxableAmountFromInvoiceTaxSummaryType(value), tax.getTaxableAmount(), indicatorCode).setScale(precision,
                ROUND_HALF_EVEN)));
        // Tax amount
        value.setTaxAmount(computeValue(value.getTaxAmount(), tax.getTaxAmount(), indicatorCode).setScale(precision, ROUND_HALF_EVEN));
        value.setTaxAmountInTaxAccountingCurrency(
            toComplexBigDecimalType(value.getTaxAmountInTaxAccountingCurrency(), value.getTaxAmount()));
      }

      return value;
    });
  }

  /**
   * Return a signed and safe value with precision
   *
   * @param indicatorCode (sign)
   * @param left
   * @param right
   * @param precision
   * @return
   */
  public static BigDecimal computeValue(BigDecimal left, BigDecimal right, IndicatorCodeType indicatorCode) {
    return left.add(ALLOWANCE.equals(indicatorCode) ? right.negate() : right);
  }
}
