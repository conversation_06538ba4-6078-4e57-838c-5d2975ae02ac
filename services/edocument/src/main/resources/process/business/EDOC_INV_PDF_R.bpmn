<?xml version='1.0' encoding='UTF-8'?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="IDc5f327b7-34ca-40be-9a57-59b1ee659468" name="EDOC_INV_PDF_R" isExecutable="true">
    <extensionElements>
      <activiti:executionListener event="start" class="com.byzaneo.xtrade.process.listener.ReportListener">
        <activiti:field name="enabled">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="loggerLevel">
          <activiti:string><![CDATA[INFO]]></activiti:string>
        </activiti:field>
        <activiti:field name="addVariables">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDocuments">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDeads">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addLogs">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persist">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persistReport">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeEmptyReport">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeAsync">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeTimeoutInMinutes">
          <activiti:string><![CDATA[120]]></activiti:string>
        </activiti:field>
        <activiti:field name="notifyOnlyIfProblem">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledged">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledgementDirectory">
          <activiti:expression>${process_work_dir_string}/ack</activiti:expression>
        </activiti:field>
        <activiti:field name="acknowledgementProducer">
          <activiti:string><![CDATA[com.byzaneo.xtrade.process.listener.ReportListener$DefaultAckProducer]]></activiti:string>
        </activiti:field>
      </activiti:executionListener>
      <activiti:executionListener event="end" class="com.byzaneo.xtrade.process.listener.ReportListener">
        <activiti:field name="enabled">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="loggerLevel">
          <activiti:string><![CDATA[INFO]]></activiti:string>
        </activiti:field>
        <activiti:field name="addVariables">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDocuments">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDeads">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addLogs">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persist">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persistReport">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeEmptyReport">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeAsync">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeTimeoutInMinutes">
          <activiti:string><![CDATA[120]]></activiti:string>
        </activiti:field>
        <activiti:field name="notifyOnlyIfProblem">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledged">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledgementDirectory">
          <activiti:expression>${process_work_dir_string}/ack</activiti:expression>
        </activiti:field>
        <activiti:field name="acknowledgementProducer">
          <activiti:string><![CDATA[com.byzaneo.xtrade.process.listener.ReportListener$DefaultAckProducer]]></activiti:string>
        </activiti:field>
      </activiti:executionListener>
      <activiti:executionListener event="start" class="com.byzaneo.xtrade.process.listener.VariablesListener">
        <activiti:field name="variables">
          <activiti:string><![CDATA[{"instance_id":"{\"type\":\"java.lang.String\",\"value\":\"6b4b75b4-1536-458c-9858-8083934015c5\"}","instance_state":"{\"type\":\"java.lang.String\",\"value\":\"Enabled\"}","instance_code":"{\"type\":\"java.lang.String\",\"value\":\"MAF\"}","instance_name":"{\"type\":\"java.lang.String\",\"value\":\"MAF_ENV\"}","instance_host":"{\"type\":\"com.byzaneo.generix.bean.Host\",\"value\":{\"name\":\"www.maf.dev.com\",\"template\":\"majidalfuttaim\",\"theme\":\"bluesky\",\"description\":\"\",\"creationDate\":\"Jul 31, 2014 5:32:02 PM\",\"modificationDate\":\"Feb 26, 2015 4:18:33 PM\",\"id\":2}}","instance_host_name":"{\"type\":\"java.lang.String\",\"value\":\"www.maf.dev.com\"}","instance_host_template":"{\"type\":\"java.lang.String\",\"value\":\"majidalfuttaim\"}","instance_language":"{\"type\":\"java.util.Locale\",\"value\":\"fr\"}","instance_languages":"{\"type\":\"java.util.ArrayList\",\"value\":\"[\\\"{\\\\\\\"type\\\\\\\":\\\\\\\"java.util.Locale\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"fr\\\\\\\"}\\\",\\\"{\\\\\\\"type\\\\\\\":\\\\\\\"java.util.Locale\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"en\\\\\\\"}\\\",\\\"{\\\\\\\"type\\\\\\\":\\\\\\\"java.util.Locale\\\\\\\",\\\\\\\"value\\\\\\\":\\\\\\\"it\\\\\\\"}\\\"]\"}","instance_company_code":"{\"type\":\"java.lang.String\",\"value\":\"MAF\"}","instance_company_name":"{\"type\":\"java.lang.String\",\"value\":\"MajidAlFutaim\"}","instance_company_identifier":"{\"type\":\"java.lang.String\",\"value\":\"MAFID\"}","process_owner":"{\"type\":\"java.lang.String\",\"value\":\"MAF\"}"}]]></activiti:string>
        </activiti:field>
        <activiti:field name="replace">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
      </activiti:executionListener>
    </extensionElements>
    <sequenceFlow id="Link-1543769502" sourceRef="Start1628791431" targetRef="Copy342"/>
    <startEvent id="Start1628791431" name="Start"/>
    <endEvent id="End1446682623" name="End"/>
    <sequenceFlow id="Link1509874637" sourceRef="Copy342" targetRef="DocumentSet343"/>
    <serviceTask id="Copy342" name="Copy PDF in process workspace" activiti:class="com.byzaneo.generix.commons.task.business.InputTask">
      <extensionElements>
        <activiti:field name="from">
          <activiti:expression>${input_dir}/edocuments/pdf</activiti:expression>
        </activiti:field>
        <activiti:field name="fileSet">
          <activiti:string><![CDATA[{"includes":"*.pdf","excludes":null,"containRegExp":null,"contain":null,"caseSensitive":true}]]></activiti:string>
        </activiti:field>
        <activiti:field name="move">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="overwrite">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="flatten">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="polled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="limit">
          <activiti:string><![CDATA[-1]]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="Link-1053285578" sourceRef="DocumentSet343" targetRef="ConverterInvoiceXcb40344"/>
    <serviceTask id="DocumentSet343" name="DocumentSet PDF" activiti:class="com.byzaneo.xtrade.process.task.DocumentSetTask">
      <extensionElements>
        <activiti:field name="fileSet">
          <activiti:string><![CDATA[{"includes":"*.pdf","excludes":null,"containRegExp":null,"contain":null,"caseSensitive":true}]]></activiti:string>
        </activiti:field>
        <activiti:field name="documentClassName">
          <activiti:string><![CDATA[com.byzaneo.xtrade.bean.Document]]></activiti:string>
        </activiti:field>
        <activiti:field name="documentProperties">
          <activiti:string><![CDATA[{"status":"PENDING", "owners":"${instance_company_identifier}", "type":"{\"kind\":\"INVOIC\",\"norm\":\"NAMED_PDF\"}"}]]></activiti:string>
        </activiti:field>
        <activiti:field name="fileType">
          <activiti:string><![CDATA[PDF]]></activiti:string>
        </activiti:field>
        <activiti:field name="indexFile">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="Link336157821" sourceRef="ConverterInvoiceXcb40344" targetRef="DocumentConsolidation347"/>
    <serviceTask id="ConverterInvoiceXcb40344" name="Convert to XCBL 4.0" activiti:class="com.byzaneo.generix.edocument.task.business.ConverterInvoiceXcbl40Task">
      <extensionElements>
        <activiti:field name="timeout">
          <activiti:string><![CDATA[3]]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="Link994641657" sourceRef="SignatureVerificationDocument345" targetRef="End1446682623"/>
    <serviceTask id="SignatureVerificationDocument345" name="Verify signature document" activiti:class="com.byzaneo.generix.signature.task.SignatureVerificationTask">
      <extensionElements>
        <activiti:field name="contextDocumentFilterPolicyExpr">
          <activiti:string><![CDATA[XCBL_AND_PDF]]></activiti:string>
        </activiti:field>
        <activiti:field name="documentFileFilterExpr">
          <activiti:string><![CDATA[type = 'PDF']]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="Link234497882" sourceRef="ExchangeAllowed346" targetRef="SignatureVerificationDocument345"/>
    <serviceTask id="ExchangeAllowed346" name="Verify allowed exchange" activiti:class="com.byzaneo.generix.edocument.task.business.ExchangeAllowedTask">
      <extensionElements>
        <activiti:field name="processingWay">
          <activiti:string><![CDATA[RECEIVING]]></activiti:string>
        </activiti:field>
        <activiti:field name="documentFileCheckPolicyExpr">
          <activiti:string><![CDATA[XCBL_AND_PDF]]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="Link336096371" sourceRef="DocumentConsolidation347" targetRef="ExchangeAllowed346"/>
    <serviceTask id="DocumentConsolidation347" name="Consolidation" activiti:class="com.byzaneo.xtrade.process.task.DocumentConsolidationTask">
      <extensionElements>
        <activiti:field name="organizationName">
          <activiti:expression>${instance_company_code}</activiti:expression>
        </activiti:field>
        <activiti:field name="fromGetter">
          <activiti:string><![CDATA[from]]></activiti:string>
        </activiti:field>
        <activiti:field name="fromNameSetter">
          <activiti:string><![CDATA[index.value.invoiceHeader.invoiceParty.sellerParty.nameAddress.name1]]></activiti:string>
        </activiti:field>
        <activiti:field name="toGetter">
          <activiti:string><![CDATA[to]]></activiti:string>
        </activiti:field>
        <activiti:field name="toNameSetter">
          <activiti:string><![CDATA[index.value.invoiceHeader.invoiceParty.billToParty.nameAddress.name1]]></activiti:string>
        </activiti:field>
        <activiti:field name="ownersGetter">
          <activiti:string><![CDATA[to]]></activiti:string>
        </activiti:field>
        <activiti:field name="ownersNameSetter">
          <activiti:string><![CDATA[index.value.invoiceHeader.invoiceParty.buyerParty.nameAddress.name1]]></activiti:string>
        </activiti:field>
        <activiti:field name="contextualDocumentsQuery">
          <activiti:string><![CDATA[type = "{\"kind\":\"INVOIC\",\"norm\":\"NAMED_PDF\"}"]]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_IDc5f327b7-34ca-40be-9a57-59b1ee659468">
    <bpmndi:BPMNPlane bpmnElement="IDc5f327b7-34ca-40be-9a57-59b1ee659468" id="BPMNPlane_IDc5f327b7-34ca-40be-9a57-59b1ee659468">
      <bpmndi:BPMNShape bpmnElement="Start1628791431" id="BPMNShape_Start1628791431">
        <omgdc:Bounds height="33.0" width="33.0" x="-116.0" y="-227.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="End1446682623" id="BPMNShape_End1446682623">
        <omgdc:Bounds height="33.0" width="33.0" x="354.0" y="-12.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Copy342" id="BPMNShape_Copy342">
        <omgdc:Bounds height="55.0" width="105.0" x="182.0" y="-227.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="DocumentSet343" id="BPMNShape_DocumentSet343">
        <omgdc:Bounds height="55.0" width="105.0" x="352.0" y="-227.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ConverterInvoiceXcb40344" id="BPMNShape_ConverterInvoiceXcb40344">
        <omgdc:Bounds height="55.0" width="105.0" x="530.0" y="-227.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="SignatureVerificationDocument345" id="BPMNShape_SignatureVerificationDocument345">
        <omgdc:Bounds height="55.0" width="105.0" x="703.0" y="-12.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExchangeAllowed346" id="BPMNShape_ExchangeAllowed346">
        <omgdc:Bounds height="55.0" width="105.0" x="703.0" y="-120.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="DocumentConsolidation347" id="BPMNShape_DocumentConsolidation347">
        <omgdc:Bounds height="55.0" width="105.0" x="703.0" y="-227.0"/>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>