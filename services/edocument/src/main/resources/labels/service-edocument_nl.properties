exchange_requiered_recipient = De bestemmeling is verplicht voor de toegestane uitwisseling
exchange_requiered_direction = Direction is required in a allowed exchange ('S' or 'R')
exchange_requiered_start = Start date is required in a allowed exchange
unauthorized_exchange_receiving= {0} mag dit documenttype niet ontvangen van {1}
unauthorized_exchange_sending= {0} mag dit documenttype niet verzenden aan {1}
signature_error = De ondertekening van het document is mislukt.
exchange_ts_truststore_help = Zoekt authenticatie bij onze server
bean_import_contextual_validation_exchange_owner = Geen eigenaarscode {0} voor de klantencode {1} voor de uitwisseling {2}
invrpt = Inventaris
recaplist_wrong_format_message_date = Opvullen van de samenvattende lijst: De datum van het document kan niet gelezen worden.
generic_unparsable_xcbl_file = Proces van elektronische factuur: Het XCBL bestand van het bericht kan niet gelezen worden.
signature_verif_refused = De verificatie van de handtekening is mislukt.
exchange_requiered_owner = <PERSON>ige<PERSON> (owner) is verplicht voor de toegestane uitwisseling
order = Bestelling
date_period = Periode
document_subtype = Documentsubtype
generic_null_message = Proces van elektronische factuur: Het bericht is null.
contract = Contract
exchange_sending = Uitzending
exchanges = Uitwisselingen
to_end = aan
receiving_from = Ontvangen van
DESADVLABS = Verzendingsbericht (Etiketten)
generic_error_processing_way = Proces van elektronische factuur: Fout bij het ophalen van de variabele ProcessingWay.
signature_xcbl_publickey_error = De publieke sleutel kan niet in het XCBL bestand geschreven worden.
bean_import_contextual_validation_exchange = De klantencode {0} komt niet overeen met {1} voor de uitwisseling {2}
contextual_validation_acUser = Klantcode {0} komt niet overeen met {1} voor acUser {2}
INVOIC = Factuur (INVOIC)
exchange_requiered_from = De verzender (sender) is verplicht voor de toegestane uitwisseling
document_kind = Documenttype
exchange_ts_server = Timeserver
exchange_ts_server_url = URL
exchange_wrong_period = Verkeerde periode voor de toegestane uitwisseling
invoic = Factuur
receiving = Ontvangst
ORDCHG = Wijziging van bestelling (ORDCHG)
recaplist_wrong_format_ati_amount = Opvullen van de samenvattende lijst: Het totaal bedrag alle taksen inbegrepen kan niet gelezen worden.
exchange_ts_truststore_name = Truststore
sending = Uitzending
signature_referenced_company_not_found = De klantenreferentie bestaat niet.
INVRPT = Stockinventaris (INVRPT)
exchange_same_recipients = De eigenaar en de ontvanger van de toegestane uitwisseling moeten verschillend zijn
recaplist_error_copy_doc_props = Opvullen van de samenvattende lijst: fout bij het kopi\u00EBren van de eigenschappen van het document.
exchange_ts_keystore_name = Keystore
pnlty = Boete
exchange_receiving = Ontvangst
PENALTY = Boete (PENALTY)
signature_cert_verif_no_cert = Er werd geen enkel geldig certificaat gevonden.
exchange_requiered_to = De ontvanger (recipient) is verplicht voor de toegestane uitwisseling
generic_not_exists_xcbl_file = Proces van elektronische factuur: Het XCBL bestand van het bericht bestaat niet.
asn = Verzendingsbericht
signature_handler_error = Fout bij het ophalen van de handler van de handtekening.
INTERCHANGE_EDIFACT = Afwikkeling EDIFACT
recaplist_null_message_type = Opvullen van de samenvattende lijst: Het documenttype werd niet verstrekt.
signature_referenced_company_not_instance_company = De klantenreferentie bestaat niet in deze omgeving.
bean_import_contextual_validation_exchange_recipient = Geen ontvangerscode {0} voor de klantencode {1} voor de uitwisseling {2}
CNTCND = Contractvoorwaarden (CNTCND)
ORDERS = Bestelling (ORDERS)
sending_to = Uitgezonden naar
exchange_ts_keystore_help = Wij zoeken authenticatie bij de server
DESADV = Verzendingsbericht (DESADV)
signature_get_publickey_error = Fout bij het ophalen van de publieke sleutel.
recaplist_wrong_format_message_preparation_date = Opvullen van de samenvattende lijst: De datum en de tijd van de voorbereiding van het document kunnen niet gelezen worden.
exchange_requiered_document_kind = Het documenttype is verplicht voor de toegestane uitwisseling
recaplist_null_message_number = Opvullen van de samenvattende lijst: Het documentnummer werd niet verstrekt.
recaplist_null_software_version = Opvullen van de samenvattende lijst: De softwareversie werd niet gevonden.
signature_cert_verif_error = De verificatie van het certificaat is mislukt.
document_collection = Documentverzameling
exchange_new = Nieuwe uitwisseling
generic_error_xcbl_file = Proces van elektronische factuur: Het document mag slechts een gelinkt bestand van het type XCBL hebben.
exchange = Uitwisseling
ORDRSP = Antwoord op bestelling (ORDRSP)
edocument = e-Document
generic_null_message_file_uri = Proces van elektronische factuur: Het bestand met het bericht bestaat niet.
exchange_ts_policyoid = OID Beleid
recaplist_wrong_format_taxable_amount = Opvullen van de samenvattende lijst: Het belastbaar bedrag kan niet gelezen worden.
unk = Onbekend
generic_null_recaplist = Proces van elektronische factuur: Onmogelijk de samenvattende lijst op te halen of aan te maken.
recipient = Ontvanger
APPRSP = Applicatierespons
DELINS = Leveringsinstructies (DELINS)
ordchg = Wijziging Bestelling
ordrsp = Antwoord op Bestelling
recaplist_null_dematPartner_code = Opvullen van de samenvattende lijst: De code van de dematerialisatiepartner werd niet verstrekt.
exchange_already_exist = De toegestane uitwisseling bestaat al
recaplist_uncomplete_legaleEntity_city = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De stad ontbreekt.
recaplist_unknown_dematPartner = Opvullen van de samenvattende lijst: De dematerialisatiepartner bestaat niet in deze omgeving.
recaplist_unknown_legalEntity = Opvullen van de samenvattende lijst: De rechtspersoon bestaat niet in deze omgeving.
recaplist_uncomplete_dematPartner_city = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De stad ontbreekt.
recaplist_uncomplete_dematPartner_name = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De naam ontbreekt.
recaplist_uncomplete_legaleEntity_name = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De naam ontbreekt.
recaplist_uncomplete_dematPartner_country = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. Het land ontbreekt.
recaplist_uncomplete_dematPartner_legalcode = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De RCS, BTW en SIREN codes ontbreken.
recaplist_uncomplete_legaleEntity_postalcode = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De postcode ontbreekt.
recaplist_null_legalEntity_code = Opvullen van de samenvattende lijst: De code van de rechtspersoon werd niet verstrekt.
recaplist_uncomplete_legaleEntity_country = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. Het land ontbreekt.
recaplist_uncomplete_dematPartner_postalcode = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De postcode ontbreekt.
recaplist_uncomplete_dematPartner_street = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De naam van de straat ontbreekt.
recaplist_uncomplete_legaleEntity_code = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De code ontbreekt.
recaplist_uncomplete_legaleEntity_street = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De naam van de straat ontbreekt.
recaplist_uncomplete_dematPartner_code = Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De code ontbreekt.
recaplist_uncomplete_legaleEntity_legalcode = Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De RCS, BTW en SIREN codes ontbreken.
bean_import_contextual_validation_exchange_kind = Het documenttype {0} is ongeldig voor de uitwisseling {1}

gs1_build_xmlpdfgs1file = Kan geen XML/PDF GS1 Frankrijk 2009 aanmaken.
signature_parse_gs1file_error = Fout bij het parsen van XML PDF GS1-bestand {}.
exchange_receiver = Ontvanger
gs1_too_much_xcbl_file = Meer dan \u00E9\u00E9n XCBL-bestand gekoppeld aan document.
gs1_toomuch_xml_file = Meer dan \u00E9\u00E9n XML-bestand gekoppeld aan document.
processing_way_missing = De verwerkingsmethode is niet gedefinieerd in het document
archive_option = Archief optie:
partner_information_missing = Sommige partnerinformatie ontbreekt
gs1_retrieve_xcbl_infos = Kan geen btw-intracode ophalen uit XCBL voor verkoper of koper.
add_exchange = Een beurs toevoegen
gs1_no_xcbl_file = Kan XCBL-bestand dat aan document is gekoppeld niet vinden.
signature_read_gs1file_error = Fout bij het lezen van XML PDF GS1-bestand {}.
gs1_no_xml_file = XML-bestand gekoppeld aan document niet gevonden.
created_exchange_receiving = Geautoriseerde uitwisseling aangemaakt: {0} mag documenten van het type {1} ontvangen van {2}
recaplist_wrong_format_tax_amount = RecapList invullen: Belastingbedrag is onleesbaar.
signature_retrieve_trusted_certs = Fout bij het lezen van ingesloten cert-bestand.
signature_publickey_error = Openbare sleutel ontbreekt in bestand {0}.
gs1_missing_partner = De afzender van het document maakt geen deel uit van de instantie.
gs1_missing_fromcode = Afzender (fromCode) kan niet worden ge\u00EFdentificeerd voor document.
gs1_toomuch_pdf_file = Meer dan \u00E9\u00E9n PDF-bestand gekoppeld aan document.
created_exchange_sending = Geautoriseerde uitwisseling aangemaakt: {0} heeft toestemming om documenten van het type {1} naar {2} te sturen.
exchange_sender = Afzender
signature_keyinfo_error = Ontbrekend KeyInfo element in bestand {0}.
gs1_no_pdf_file = Kan PDF-bestand dat aan document is gekoppeld niet vinden.
generic_error_xcbl_norm = Onbekend berichtversienummer en/of releasenummer. Gedwongen tot D96A.