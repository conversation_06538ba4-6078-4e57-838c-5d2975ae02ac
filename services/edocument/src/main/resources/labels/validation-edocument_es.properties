legislation_eu = EUROPEA
legislation_fr = FRANCESA
invoiceheader_headoffice_vat_ident_empty = Debe introducirse el c\u00F3digo de IVA intracomunitario del domicilio social
invoiceheader_billtoparty_gov_reference_notunique = El SIREN del facturado debe ser \u00FAnico
invoiceheader_headoffice_address_city_notunique = La ciudad del domicilio social debe ser \u00FAnica
invoicesummary_actualpayment_payment_number_empty = Debe indicarse el n\u00FAmero de cada anticipo
invoiceheader_sellerparty_address_name_notunique = El vendedor no puede tener m\u00E1s de una raz\u00F3n social
invoiceheader_headoffice_address_postalcode_empty = Debe indicarse el c\u00F3digo postal del domicilio social
invoicesummary_taxsummary_tax_amount_notunique = Debe introducirse un \u00FAnico importe de impuesto para cada tipo de IVA desglosado
invoicedetail_quantity_value_notunique = En cada l\u00EDnea solo puede aparecer una cantidad
invoiceheader_tax_accounting_currency_notequal = La moneda de pago del IVA debe ser el euro
invoiceheader_sellerparty_vat_ident_empty = Debe introducirse el c\u00F3digo de IVA intracomunitario del vendedor
invoiceheader_tax_accounting__notunique = La moneda de pago del IVA debe ser \u00FAnica
invoiceheader_billtoparty_address_city_empty = Debe introducirse la ciudad del facturado
invoicesummary_actualpayment_prepaid_amount_illegalvalue = El importe prepagado es incorrecto
invoiceheader_sellerparty_missing = El facturado do debe de ser informado
invoice_tax_line_tax_unmatched = El impuesto de l\u00EDnea no aparece en el desglose de impuestos al pie del mensaje.
invoiceheader_sellerparty_gov_reference_empty = Debe introducirse el n\u00FAmero SIREN del vendedor
invoiceheader_alloworcharge_description_empty = Debe rellenarse la descripci\u00F3n de un descuento o cargo en el encabezado
invoicedetail_invoicepricingdetail_gross_unit_price_notunique = El precio unitario bruto sin IVA debe ser \u00FAnico para cada l\u00EDnea
invoiceheader_date_preparation_date_time_document_illegalvalue = La fecha/hora de preparaci\u00F3n del documento no es correcta
invoiceheader_headoffice_registration_ident_empty = El numero de registro (RCS-RCM) de la sede social debe de ser informado
invoicedetail_charge_type_notunique = La tasa o importe introducido para un cargo de l\u00EDnea debe ser \u00FAnico
invoicesummary_invoicetotals_total_amount_empty = Debe introducirse el importe total, IVA incluido (BT-112), regla G1.24
invoicesummary_invoicetotals_monetary_amount_illegalvalue = El importe total del impuesto es incorrecto
invoicedetail_allowance_amount_illegalvalue = El importe del descuento de l\u00EDnea no es correcto
invoicesummary_actualpayment_prepaid_amount_empty = Debe indicarse el importe prepagado
invoice_tax_taxsummary_tax_unmatched = Falta el tipo impositivo en el resto del mensaje
invoiceheader_headoffice_gov_reference_notunique = El SIREN del domicilio social debe ser \u00FAnico
invoiceheader_billtoparty_address_postalcode_empty = Debe introducirse el c\u00F3digo postal del facturado
invoicedetail_allowance_type_notunique = El tipo o importe introducido para un descuento de l\u00EDnea debe ser \u00FAnico
invoiceheader_declarantsagentorrepresentative_address_name_notunique = El representante fiscal no puede tener m\u00E1s de una raz\u00F3n social
invoiceheader_paymentterms_latepayment_term_empty = Debe introducirse la multa a tanto alzado
invoicedetail_charge_percent_illegalvalue = La tasa de carga de la l\u00EDnea no es correcta
invoiceheader_declarantsagentorrepresentative_address_city_empty = Debe introducirse la ciudad del representante fiscal
invoiceheader_declarantsagentorrepresentative_address_name_empty = Debe introducirse la raz\u00F3n social del representante fiscal
invoiceheader_billtoparty_address_city_notunique = La ciudad facturado debe ser \u00FAnica
invoicesummary_invoicetotals_taxable_amount_illegalvalue = La base imponible total (BT-109) es incorrecta, regla G6.08
invoicedetail_paratax_type_empty = Debe introducirse el tipo o el importe del impuesto parafiscal de la l\u00EDnea
invoicedetail_paratax_percent_illegalvalue = El tipo de la tasa parafiscal por l\u00EDnea no es correcto
invoicedetail_paratax_type_notunique = El tipo o importe introducido para la tasa parafiscal en la l\u00EDnea debe ser \u00FAnico
invoiceheader_declarantsagentorrepresentative_address_postalcode_empty = Debe introducirse el c\u00F3digo postal del representante fiscal
invoiceheader_invoicingperiod_invoicing_period_endDate_illegalvalue = El final del periodo de referencia es incorrecto.
invoice_tax_taxsummary_tax_notunique = La tasa de impuesto esta transmitida en doblon
invoiceheader_invoice_issue_date_notunique = La fecha del documento debe ser \u00FAnica
invoiceheader_declarantsagentorrepresentative_vat_ident_empty = Debe introducirse el c\u00F3digo de IVA intracomunitario del representante fiscal
invoiceheader_invoicingperiod_invoicing_period_startDate_notunique = El inicio del periodo de referencia debe ser \u00FAnico
invoice_tax_global_tax_unmatched = Falta el tipo impositivo en el resto del mensaje
invoiceheader_sellerparty_address_country_notunique = El pa\u00EDs del vendedor debe ser \u00FAnico
invoiceheader_billtoparty_address_name_empty = Debe indicarse la raz\u00F3n social del facturado
invoiceheader_alloworcharge_vat_rate_empty = Debe introducirse el tipo de IVA para un descuento o cargo de encabezado.
invoiceheader_billtoparty_address_name_notunique = El facturado no puede tener m\u00E1s de una raz\u00F3n social
invoiceheader_headoffice_address_country_empty = Debe indicarse el pa\u00EDs del domicilio social
invoiceheader_invoicing_period_start_date_illegalvalue = El inicio del periodo de referencia es incorrecto.
invoiceheader_billtoparty_gov_reference_empty = Debe introducirse el SIREN del facturado
invoiceheader_headoffice_vat_ident_notunique = El c\u00F3digo de IVA intracomunitario del domicilio social debe ser \u00FAnico
invoiceheader_sellerparty_address_city_empty = Debe introducirse la ciudad del vendedor
invoiceheader_sellerparty_address_name_empty = Debe introducirse la raz\u00F3n social del vendedor
invoiceheader_sellerparty_address_city_notunique = La ciudad del vendedor debe ser \u00FAnica
invoicedetail_paratax_amount_illegalvalue = El importe de la tasa parafiscal de la l\u00EDnea no es correcto
invoiceheader_paymentterms_discount_term_empty = Deben introducirse las condiciones de descuento.
invoiceheader_declarantsagentorrepresentative_address_postalcode_notunique = El c\u00F3digo postal del representante fiscal debe ser \u00FAnico
invoiceheader_sellerparty_registration_ident_empty = Debe indicarse el RCS-RCM del vendedor
invoiceheader_declarantsagentorrepresentative_address_country_notunique = El pa\u00EDs del representante fiscal debe ser \u00FAnico
invoiceheader_invoicingperiod_invoicing_period_startDate_illegalvalue = El inicio del periodo de referencia es incorrecto.
invoiceheader_declarantsagentorrepresentative_address_street_empty = Debe introducirse la direcci\u00F3n del representante fiscal
invoiceheader_invoicing_period_end_date_illegalvalue = El final del periodo de referencia es incorrecto.
invoicedetail_charge_amount_illegalvalue = El importe del cargo a la l\u00EDnea no es correcto
invoiceheader_billtoparty_vat_ident_notunique = El c\u00F3digo de IVA intracomunitario del facturado debe ser \u00FAnico
invoicedetail_charge_amount_percent_illegalvalue = La tasa o el importe de la cuota de l\u00EDnea no son correctos
invoiceheader_paymentterms_payment_term_empty = La indemnizacion arancelaria de penalidad no esta informada
invoiceheader_sellerparty_address_street_empty = Debe introducirse la direcci\u00F3n del vendedor
invoiceheader_paymentterms_penalty_term_empty = Deben introducirse las condiciones de penalizaci\u00F3n.
invoiceheader_headoffice_gov_reference_empty = La referencia gubernamental (SIREN) de la sede social tiene que ser informada
invoiceheader_invoice_issue_date_illegalvalue = La fecha del documento no es correcta
invoiceheader_declarantsagentorrepresentative_address_country_empty = Debe introducirse el pa\u00EDs del representante fiscal
invoiceheader_declarantsagentorrepresentative_vat_ident_notunique = El c\u00F3digo de IVA intracomunitario del representante fiscal debe ser \u00FAnico
invoiceheader_headoffice_address_postalcode_notunique = El c\u00F3digo postal del domicilio social debe ser \u00FAnico
invoiceheader_invoice_reference_date_illegalvalue = La fecha del documento de referencia es incorrecta.
invoiceheader_due_date_empty = Debe introducirse la fecha de vencimiento
invoicedetail_allowance_percent_illegalvalue = El tipo de descuento de la l\u00EDnea no es correcto
invoiceheader_invoice_references_empty = La informaci\u00F3n del documento de registro es incompleta
invoicesummary_actualpayment_payment_date_illegalvalue = La fecha de pago del anticipo es incorrecta
invoicesummary_taxsummary_vat_rate_empty = El desglose del IVA (BT-119) debe indicar un tipo de IVA, regla G6.08
invoiceheader_headoffice_address_name_empty = Debe indicarse la raz\u00F3n social del domicilio social
invoicedetail_quantity_value_illegalvalue = La cantidad de la l\u00EDnea es incorrecta
invoiceheader_sellerparty_registration_ident_notunique = El RCS-RCM del vendedor debe ser \u00FAnico
invoice_tax_line_allowanceorcharge_tax_unmatched = Falta el tipo impositivo en el desglose de impuestos al pie del mensaje
invoiceheader_alloworcharge_type_allowance_charge_empty = Se debe introducir el tipo de descuento o cargo en el encabezado
invoiceheader_invoice_type_notunique = El tipo de documento debe ser \u00FAnico
invoicedetail_invoicepricingdetail_vat_rate_empty = Debe introducirse el tipo de IVA de la l\u00EDnea.
invoiceheader_alloworcharge_type_allowance_charge_percent_illegalvalue = El porcentaje de un descuento o cargo en el encabezado es incorrecto
invoicesummary_invoicetotals_monetary_amount_notunique = Debe indicarse un \u00FAnico importe total del impuesto (BT-110), regla G1.24
invoiceheader_declarantsagentorrepresentative_address_city_notunique = La ciudad del representante fiscal debe ser \u00FAnica
invoiceheader_headoffice_address_street_notunique = La direcci\u00F3n del domicilio social debe ser \u00FAnica
invoicedetail_invoicepricingdetail_gross_unit_price_empty = En cada l\u00EDnea debe indicarse el precio unitario bruto sin IVA.
invoicedetail_paratax_amount_percent_illegalvalue = El tipo o importe de la tasa parafiscal de la l\u00EDnea no es correcto
invoicesummary_taxsummary_tax_amount_empty = Debe introducirse un importe del impuesto para cada tipo de IVA desglosado
invoiceheader_billtoparty_address_country_empty = Debe introducirse el pa\u00EDs del facturado
invoiceheader_declarantsagentorrepresentative_gov_reference_empty = La referencia gubernamental (SIREN) del representante fiscal debe de ser informada
invoicesummary_taxsummary_tax_amount_illegalvalue = El importe del impuesto es incorrecto
invoicedetail_base_item_detail_empty = La descripci\u00F3n de los bienes y servicios debe figurar en cada l\u00EDnea
invoiceheader_invoice_number_empty = Debe introducirse el n\u00FAmero de documento
invoiceheader_sellerparty_address_country_empty = Debe introducirse el pa\u00EDs del vendedor
invoiceheader_headoffice_address_name_notunique = El domicilio social no puede tener m\u00E1s de una raz\u00F3n social
invoiceheader_sellerparty_address_street_notunique = La direcci\u00F3n del vendedor debe ser \u00FAnica
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_illegalvalue = El importe de un descuento o cargo en el encabezado es incorrecto
invoiceheader_billtoparty_address_postalcode_notunique = El c\u00F3digo postal del facturado debe ser \u00FAnico
invoiceheader_paymentterms_missing = Las informaciones de pago no son informadas
invoicedetail_invoicepricingdetail_gross_unit_price_illegalvalue = El precio unitario bruto sin IVA es incorrecto
invoiceheader_invoice_currency_notunique = La moneda de facturaci\u00F3n debe ser \u00FAnica
invoiceheader_alloworcharge_type_allowance_charge_percentagemonetaryvalue_illegalvalue = El porcentaje o importe de un descuento o recargo en el encabezado es incorrecto
invoiceheader_billtoparty_missing = El facturado do debe de ser informado
invoicesummary_invoicetotals_missing = Los totales globales deben de ser informados
invoicesummary_invoicetotals_total_amount_payable_illegalvalue = El importe total IVA incl. a pagar es incorrecto
invoicesummary_taxsummary_taxable_amount_empty = Debe introducirse una base imponible para cada tipo de IVA desglosado
invoiceheader_billtoparty_address_street_empty = Debe facilitarse la direcci\u00F3n del facturado
invoicesummary_invoicetotals_monetary_amount_empty = Debe introducirse el importe total del impuesto (BT-110), regla G6.08
invoicesummary_invoicetotals_taxable_amount_empty = Debe introducirse la base imponible total (BT-109), regla G6.08
invoicesummary_taxsummary_taxable_amount_notunique = Debe introducirse una \u00FAnica base imponible para cada tipo de IVA desglosado
invoicesummary_actualpayment_payment_date_empty = Debe introducirse la fecha de pago de un anticipo
invoicesummary_taxsummary_missing = La ventilacion de IVA debe de ser informada
invoiceheader_billtoparty_registration_ident_notunique = El RCS-RCM del facturado debe ser \u00FAnico
invoiceheader_declarantsagentorrepresentative_address_street_notunique = La direcci\u00F3n del representante fiscal debe ser \u00FAnica
invoicedetail_allowance_amount_percent_illegalvalue = El tipo o importe del descuento de l\u00EDnea no es correcto
invoiceheader_date_preparation_date_time_document_empty = Debe introducirse la fecha/hora en que se prepar\u00F3 el documento
invoiceheader_declarantsagentorrepresentative_gov_reference_notunique = El SIREN del representante fiscal debe ser \u00FAnico
invoicedetail_charge_type_empty = Debe introducirse la tasa o el importe de un cargo de l\u00EDnea.
invoiceheader_invoice_number_notunique = El n\u00FAmero de documento debe ser \u00FAnico
invoiceheader_invoice_type_other_empty = Debe rellenarse el nombre del documento (es decir, el tipo)
invoiceheader_tax_accounting_currency_notunique = La moneda de pago del IVA debe ser \u00FAnica
invoiceheader_headoffice_address_city_empty = Debe indicarse la ciudad del domicilio social
invoiceheader_invoicingperiod_reference_period_empty = Debe introducirse el periodo de referencia
invoiceheader_billtoparty_address_street_notunique = La direcci\u00F3n del facturado debe ser \u00FAnica
invoiceheader_invoice_type_empty = Debe introducirse el tipo de documento
invoiceheader_billtoparty_vat_ident_empty = Debe introducirse el c\u00F3digo de IVA intracomunitario del facturado
invoiceheader_paymentterms_discount_due_date_illegalvalue = La fecha de pago del anticipo es incorrecta.
invoiceheader_billtoparty_address_country_notunique = El pa\u00EDs del facturado debe ser \u00FAnico
invoicedetail_invoicepricingdetail_missing = El precio unitario debe de ser indormado en cada linea
invoiceheader_headoffice_address_street_empty = Debe indicarse la direcci\u00F3n del domicilio social
invoiceheader_despatch_date_illegalvalue = Fecha de env\u00EDo incorrecta
invoiceheader_due_date_illegalvalue = La fecha de vencimiento no es correcta
invoiceheader_sellerparty_gov_reference_notunique = El SIREN del vendedor debe ser \u00FAnico
invoicedetail_allowance_type_empty = Debe introducirse el tipo o el importe de un descuento de l\u00EDnea
invoiceheader_headoffice_registration_ident_notunique = El RCS-RCM del domicilio social debe ser \u00FAnico
invoiceheader_alloworcharge_vat_rate_notunique = Solo puede introducirse un tipo de IVA para el descuento o cargo en el encabezado
invoiceheader_sellerparty_vat_ident_notunique = El c\u00F3digo de IVA intracomunitario del vendedor debe ser \u00FAnico
invoiceheader_declarantsagentorrepresentative_registration_ident_notunique = El RCS-RCM del representante fiscal debe ser \u00FAnico
invoiceheader_sellerparty_address_postalcode_notunique = El c\u00F3digo postal del vendedor debe ser \u00FAnico
invoiceheader_headoffice_address_country_notunique = El pa\u00EDs del domicilio social debe ser \u00FAnico
invoicedetail_invoicepricingdetail_net_unit_price_empty = En cada l\u00EDnea debe indicarse el precio unitario neto sin IVA
invoicesummary_invoicetotals_total_amount_illegalvalue = El importe total, IVA incluido, es incorrecto
invoicedetail_invoicepricingdetail_net_unit_price_notunique = El precio neto unitario sin IVA debe ser \u00FAnico para cada l\u00EDnea
invoiceheader_sellerparty_address_postalcode_empty = Debe introducirse el c\u00F3digo postal del vendedor
invoicedetail_invoicepricingdetail_net_unit_price_illegalvalue = El precio unitario neto sin IVA es incorrecto
invoiceheader_invoice_issue_date_empty = Debe introducirse la fecha del documento

invoiceheader_sellerparty_legal_structure_notunique = La forma jur\u00EDdica del vendedor solo debe indicarse una vez
invoice_tax_taxsummary_tax_notuniquevalue = El tipo impositivo se ha transmite en duplicado en el resumen del IVA
invoicedetail_invoicepricingdetail_exempt_vat_rate_not_zero = =El tipo de IVA de la l\u00EDnea debe ser cero en caso de exenci\u00F3n fiscal.
invoiceheader_sellerparty_registered_name_empty = Debe introducirse la raz\u00F3n social del vendedor
invoicedetail_paratax_desc_empty_or_notunique = La descripci\u00F3n del impuesto parafiscal en la l\u00EDnea debe rellenarse y el impuesto parafiscal en la l\u00EDnea solo puede tener una descripci\u00F3n
invoiceheader_sellerparty_legal_capital_empty = Debe indicarse el capital social del vendedor
invoiceheader_sellerparty_legal_structure_empty = Debe indicarse la forma jur\u00EDdica del vendedor
invoicedetail_charge_desc_empty_or_notunique = La descripci\u00F3n de una carga de l\u00EDnea debe rellenarse, y una carga de l\u00EDnea solo puede tener una descripci\u00F3n
invoicedetail_invoicepricingdetail_vat_empty = Debe introducirse el tipo de IVA de la l\u00EDnea
invoicedetail_invoicepricingdetail_vat_notunique = El tipo de IVA de la l\u00EDnea debe ser \u00FAnico
invoicedetail_paratax_regulatory_vat_empty_or_notunique = Debe introducirse el tipo de IVA aplicado a un impuesto parafiscal en la l\u00EDnea y el tipo de IVA aplicado a un impuesto parafiscal en la l\u00EDnea debe ser \u00FAnico
invoicedetail_allowance_desc_empty_or_notunique = Debe introducirse la descripci\u00F3n de un salto de l\u00EDnea, y un salto de l\u00EDnea solo puede tener una descripci\u00F3n
invoiceheader_invoice_currency_notequal = No se especifica la moneda de pago del IVA y la moneda de facturaci\u00F3n no es el euro.
invoiceheader_sellerparty_registered_name_notunique = La raz\u00F3n social del vendedor solo debe indicarse una vez
invoiceheader_sellerparty_legal_capital_notunique = El capital social del vendedor solo debe indicarse una vez
invoicesummary_listoftaxsummary_tax_exempt_code_empty = En caso de exenci\u00F3n del IVA (BT-118), debe indicarse el c\u00F3digo (BT-121) y la descripci\u00F3n (BT-120) del motivo de la exenci\u00F3n, regla G1.40
invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_notunique = El c\u00F3digo de tipo de factura (EXT-FR-FE-137) de una factura anterior indicado en la l\u00EDnea debe ser \u00FAnico, regla G1.01
invoicesummary_invoicetotals_total_amount_too_large = El importe total, IVA incluido (BT-112) no puede tener m\u00E1s de diecinueve cifras, incluidos dos decimales, regla G1.14
invoiceheader_invoiceparty_address_country_coded_notequal = El c\u00F3digo de pa\u00EDs del representante fiscal (BT-69) debe ser FR, regla G1.49
invoiceheader_invoice_type_bt_121_notequal = El c\u00F3digo del tipo de factura (BT-3) debe ser 261, 381 o 396 porque el c\u00F3digo del motivo de exenci\u00F3n del IVA (BT-121) es VATEX-FR-CNWVAT, regla G6.21
invoiceheader_party_tax_information_ident_not_in_list = El identificador de IVA del beneficiario (EXT-FR-FE-27) debe cumplir la norma ISO-3166-1 alfa-2, regla G6.18.
invoicesummary_invoicetotals_taxable_amount_sum_inconsistency = El importe total sin IVA (BT-109) debe ser igual a la suma de las bases imponibles de cada tipo de IVA (BT-116), regla G1.54
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_not_decimal = El importe global del descuento (BT-92) y el importe global de los gastos (BT-99) no pueden tener m\u00E1s de dos decimales, regla G1.14
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_empty = Debe introducirse el importe del IVA por tipo de IVA (BT-117), regla G1.14
invoicesummary_invoicetotals_taxable_amount_not_decimal = El importe total sin IVA (BT-109) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_sellerparty_siret_0223_too_large = El c\u00F3digo identificador de un vendedor europeo (BT-29b) debe tener un m\u00E1ximo de 18 caracteres, regla G1.08
invoiceheader_buyerparty_address_country_not_in_list = El c\u00F3digo de pa\u00EDs del comprador (BT-55) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_list_of_attachment_file_name_empty = Debe introducirse el nombre del archivo del documento adjunto (BT-125-2), regla G4.16
invoicedetail_buyer_line_number_not_integer = El identificador de l\u00EDnea (BG-25) debe constar solo de d\u00EDgitos, regla G6.01
invoiceheader_invoice_currency_fr_not_in_list = El c\u00F3digo de moneda de una factura (BT-5) debe pertenecer a la lista de valores permitidos, regla G1.10
invoicesummary_invoicetotals_monetary_amount_not_decimal = El importe total del IVA (BT-110) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicesummary_invoicetotals_taxable_amount_notunique = El importe total sin IVA (BT-109) debe ser \u00FAnico, regla G1.14
invoiceheader_buyerparty_vat_ident_not_in_list = El identificador de IVA del comprador (BT-48) debe cumplir la norma ISO-3166-1 alfa-2, regla G1.47.
invoiceheader_sellerparty_additional_party_ident_empty = Debe rellenarse el identificador adicional (BT-29), regla G1.47
invoicedetail_invoicepricingdetail_net_unit_price_negative = El precio neto de un art\u00EDculo (BT-146) debe ser positivo o igual a cero, regla BR-27
invoiceheader_factor_address_country_code_not_in_list = El c\u00F3digo de pa\u00EDs del tercero emisor de la factura (EXT-FR-FE-130) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoicedetail_invoicepricingdetail_net_invoice_line_amount_empty = El importe neto de l\u00EDnea (BT-131) es obligatorio en cada l\u00EDnea, norma BR-24
invoiceheader_invoiceparty_VAT_seller_identifier_not_in_list = El identificador de IVA del agente del vendedor (EXT-FR-FE-73) debe cumplir la norma ISO-3166-1 alfa-2, regla G6.17.
invoicedetail_invoiced_quantity_empty = El c\u00F3digo de unidad de medida de la cantidad facturada de una l\u00EDnea (BT-130) es obligatorio en cada l\u00EDnea, regla G6.09
invoicedetail_invoicepricingdetail_net_invoice_line_amount_too_large = El importe neto de la l\u00EDnea (BT-131) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoicedetail_structured_note_text_type_coded_not_in_list = El c\u00F3digo de asunto de la nota de l\u00EDnea de factura (EXT-FR-FE-183) debe cumplir la norma UNTDID-4451, regla G1.52.
invoiceheader_former_invoice_type_notunique = Solo puede haber un c\u00F3digo de tipo de factura (EXT-FR-FE-02) por factura anterior, regla G1.01
legislation_en16931 = EN16931
invoiceheader_buyer_order_number_b2g_fr_empty_or_notunique = La referencia de la nota de pedido (BT-13) es obligatoria, norma G1.11
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_too_large = La base imponible por tipo de IVA (BT-116) no puede tener m\u00E1s de diecinueve cifras, incluidos dos decimales, regla G1.14
invoicedetail_invoicepricingdetail_allow_or_charge_base_for_invoice_line_discount_too_large = La base de descuento de la l\u00EDnea de factura (BT-137) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoicesummary_invoicetotals_total_amount_payable_notunique = El importe a pagar (BT-115) debe ser \u00FAnico, regla G1.14
invoicesummary_invoicetotals_allowance_total_monetary_amount_notunique = La suma total de los descuentos (BT-107) debe ser \u00FAnica, regla G1.14
invoicedetail_charge_list_of_description_unmatched_pair = El c\u00F3digo de motivo de los cargos o tasas aplicables a una l\u00EDnea de factura (BT-145) debe corresponderse con su descripci\u00F3n (BT-144) de acuerdo con la norma UNTDID-7161, regla BR-CO-8.
invoicesummary_invoicetotals_monetary_amount_too_large = El importe total del IVA (BT-110) no puede tener m\u00E1s de diecinueve cifras, incluidos dos decimales, regla G1.14
invoiceheader_invoiceparty_address_country_coded_notunique = El c\u00F3digo de pa\u00EDs del representante fiscal (BT-69) debe ser \u00FAnico, regla G1.49
invoiceheader_sellerparty_siret_0227_too_large = El c\u00F3digo identificador de un vendedor no europeo (BT-29b) debe tener una longitud m\u00E1xima de 18 caracteres, regla G1.08
invoicesummary_invoicetotals_total_VAT_amount_not_decimal = El importe total del IVA expresado en moneda contable (BT-111) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_too_large = El importe de descuento global (BT-92) y el importe de cargo global (BT-99) no pueden tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14.
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_empty = Debe introducirse la base imponible por tipo de IVA (BT-116), regla G1.14
invoiceheader_invoicing_period_end_date_notunique = La fecha de fin del periodo de facturaci\u00F3n (BT-74) solo debe introducirse una vez, regla G1.36.
invoicedetail_invoicingperiod_end_date_datenotafter = La fecha de inicio del periodo de facturaci\u00F3n de una l\u00EDnea (BT-134) debe ser anterior o igual a la fecha de fin del periodo de facturaci\u00F3n (BT-135) de dicha l\u00EDnea, regla BR-30
invoiceheader_despatch_date_notunique = La fecha efectiva de entrega (BT-72) debe ser \u00FAnica dentro de la factura, regla G1.36
invoiceheader_remit_to_party_siret_0228_too_large = El RIDET del beneficiario (BT-60) debe tener un m\u00E1ximo de 10 caracteres, regla G1.08
invoicedetail_buyer_line_number_too_large = El identificador de l\u00EDnea (BG-25) no puede contener m\u00E1s de 6 d\u00EDgitos, regla G6.01
invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_empty = Debe introducirse el c\u00F3digo de tipo de factura (EXT-FR-FE-137) de una factura anterior indicada en la l\u00EDnea, regla G1.01
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_sum_inconsistency = La suma de los importes netos de las l\u00EDneas de factura (BT-106) debe ser igual a la suma de los importes netos de las l\u00EDneas de factura (BT-131), regla G1.54
invoiceheader_declarantsagentorrepresentative_vat_ident_not_in_list = El identificador de IVA del representante fiscal (BT-63) debe comenzar por un c\u00F3digo de la norma ISO-3166-1 alfa-2, regla G1.47.
invoicesummary_invoicetotals_charge_total_monetary_amount_not_decimal = La suma total de cargos o tasas (BT-108) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_contract_type_fr_notunique = Solo puede haber un tipo de contrato (EXT-FR-FE-01) para una factura, regla G1.03
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_not_decimal = El importe del IVA por tipo de IVA (BT-117) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_fr_requirements_business_process_type_fr_in_list = Si el marco de facturaci\u00F3n (BT-23) es S3, el destinatario de la factura es siempre una entidad p\u00FAblica, regla G1.81
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_not_decimal = La base imponible por tipo de IVA (BT-116) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_invoicepricingdetail_discount_on_item_price_too_large = El descuento sobre el precio del art\u00EDculo (BT-147) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos cuatro decimales, regla G1.15
invoicesummary_invoicetotals_total_amount_notunique = El importe total incluido el IVA (BT-112) debe ser \u00FAnico, regla G1.14
invoicesummary_list_of_tax_summary_VAT_amount_not_decimal = El importe del IVA para cada tipo de IVA (BT-117) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_charge_service_coded_other_empty = El c\u00F3digo de motivo de los gastos o tasas aplicables a la l\u00EDnea de factura (BT-145) debe introducirse si la descripci\u00F3n correspondiente (BT-144) no lo est\u00E1, regla G1.29
invoicedetail_ship_to_country_code_notunique = El c\u00F3digo de pa\u00EDs del lugar de entrega a la l\u00EDnea (EXT-FR-FE-157) debe ser \u00FAnico, regla G6.11
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_too_large = La suma de los importes netos de las l\u00EDneas de factura (BT-106) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoicesummary_listoftaxsummary_tax_exempt_reason_notequal = Cuando el c\u00F3digo del motivo de exenci\u00F3n del IVA (BT-121) es igual a VATEX-FR-FRANCHISE, el motivo de exenci\u00F3n del IVA (BT-120) debe ser: IVA no aplicable de conformidad con el art\u00EDculo 293B del C\u00F3digo General de Impuestos, regla G1.56
invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_not_decimal = La base de los cargos o descuentos (BT-93 y BT-100) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_notunique = Solo puede haber un importe por tipo de descuento global (BT-92) o cargo global (BT-99), regla G1.14
invoicesummary_invoicetotals_taxable_amount_too_large = El importe total sin IVA (BT-109) no puede tener m\u00E1s de diecinueve cifras, incluidos dos decimales, regla G1.14
invoiceheader_remit_to_party_siret_0223_too_large = El c\u00F3digo que identifica a un beneficiario europeo (BT-60) debe tener un m\u00E1ximo de 18 caracteres, regla G1.08
invoiceheader_buyerparty_address_name_empty = Debe introducirse el nombre del comprador (BG-8), regla G6.08
invoicesummary_invoicetotals_payroll_amount_too_large = El importe pagado (BT-113) no puede tener m\u00E1s de diecinueve cifras, incluidos dos decimales, regla G1.14
invoicedetail_charge_service_coded_other_not_in_list = El c\u00F3digo de motivo de los cargos o tasas aplicables a la l\u00EDnea (BT-145) debe cumplir la norma UNTDID-7161, regla G1.29.
invoiceheader_alloworcharge_list_of_description_empty = La descripci\u00F3n del motivo del descuento (BT-97) debe rellenarse si no se ha rellenado el c\u00F3digo correspondiente (BT-98), regla G1.29
invoiceheader_buyerparty_vat_ident_notunique = El identificador de IVA del comprador (BT-48) debe ser \u00FAnico, regla G6.11
invoicesummary_invoicetotals_total_amount_payable_not_decimal = El importe a pagar (BT-115) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_quantity_value_too_large = La cantidad facturada (BT-129) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos cuatro decimales, regla G1.15.
invoiceheader_due_date_not_century_21 = La fecha de vencimiento (BT-9) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36
invoicedetail_invoicepricingdetail_base_quantity_of_item_price_not_decimal = La cantidad base del precio del art\u00EDculo (BT-149) no puede tener m\u00E1s de cuatro decimales, regla G1.15
invoiceheader_buyerparty_gov_reference_empty = Debe introducirse el n\u00FAmero SIREN del comprador (BT-47), regla G6.08
invoiceheader_buyer_reference_b2g_fr_not_french = La referencia del comprador (BT-10) no debe contener caracteres especiales, regla G2.29
invoiceheader_buyerparty_vat_ident_empty = Debe introducirse la identificaci\u00F3n de IVA del comprador (BT-48), regla G6.11
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_notunique = La base imponible por tipo de IVA (BT-116) debe ser \u00FAnica, regla G1.14
invoicedetail_buyer_line_number_empty = Una factura debe contener al menos una l\u00EDnea (BG-25), regla G6.01 (BR-16)
invoicesummary_invoicetotals_total_amount_payable_notequal = El importe a pagar (BT-115) debe ser cero, regla G1.33
invoicesummary_taxsummary_vat_rate_not_in_decimal_list = El tipo de IVA para el desglose del IVA (BT-119) debe pertenecer a la lista de valores autorizados, regla G1.24
invoicedetail_invoicepricingdetail_discount_on_item_price_notunique = El descuento sobre el precio del art\u00EDculo (BT-147) debe ser \u00FAnico para cada l\u00EDnea, regla G1.15
invoiceheader_invoiceparty_VAT_payer_identifier_not_in_list = El identificador de IVA del pagador (EXT-FR-FE-50) debe cumplir la norma ISO-3166-1 alfa-2, regla G6.17.
invoiceheader_list_of_structured_notes_coded_other_not_in_list = El c\u00F3digo de la nota de factura (BT-21) debe cumplir la norma UNTDID-4451, regla G1.52.
invoicedetail_charge_amount_not_decimal = El importe del cargo (BT-141) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicesummary_invoicetotals_rounded_amount_notunique = El importe redondeado (BT-114) debe ser \u00FAnico, regla G1.14
invoiceheader_alloworcharge_charge_service_coded_other_empty = El c\u00F3digo del motivo de los cargos o tasas (BT-105) debe introducirse si no se ha introducido la descripci\u00F3n correspondiente (BT-104), regla G1.29
invoiceheader_fr_requirements_profile_type_empty_or_notunique = El tipo de perfil (BT-24) es obligatorio, norma BR-1
invoiceheader_sellerparty_vat_ident_not_in_list = El identificador de IVA del vendedor (BT-31) debe comenzar por un c\u00F3digo perteneciente a la norma ISO-3166-1 alfa-2, regla G1.47.
invoicedetail_invoicepricingdetail_tax_tax_type_coded_other_empty = El c\u00F3digo del tipo de IVA del art\u00EDculo facturado en una l\u00EDnea (BT-151) es obligatorio en cada l\u00EDnea, regla BR-CO-4
invoiceheader_buyerparty_gov_reference_notunique = El n\u00FAmero SIREN del comprador (BT-47) debe ser \u00FAnico, regla G6.08
invoiceheader_former_invoice_reference_empty = La referencia a una factura anterior (BG-3) debe indicar el n\u00FAmero de la factura anterior (BT-25), regla BR-55.
invoiceheader_invoice_currency_fr_empty_or_notunique = Una factura debe contener un c\u00F3digo de moneda (BT-5) y solo un c\u00F3digo, regla G1.10
invoiceheader_invoice_type_other_empty_or_notunique = Una factura debe tener un c\u00F3digo de tipo de factura (BT-3), reglas G6.08 y BR-4
invoiceheader_invoicing_period_end_date_empty = El periodo de facturaci\u00F3n de una factura requiere una fecha de inicio (BT-73) y una fecha de fin (BT-74), regla G6.11 y regla BR-CO-19
invoicedetail_allowance_list_of_description_unmatched_pair = El motivo de descuento de una l\u00EDnea de factura (BT-140) debe corresponderse con su descripci\u00F3n (BT-139) de acuerdo con UNTDID-5189, regla BR-CO-7.
invoiceheader_buyer_reference_b2g_fr_empty = La entidad p\u00FAblica que recibe esta factura necesita un c\u00F3digo de servicio (BT-10), regla G1.11
invoiceheader_factor_address_country_code_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del tercero emisor de la factura (EXT-FR-FE-130), regla G2.01
invoicesummary_invoicetotals_total_amount_not_decimal = El importe total incluido el IVA (BT-112) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_charge_amount_empty = En el caso de tasas y c\u00E1nones aplicables a la l\u00EDnea, el importe (BT-141) es obligatorio, regla G1.14
invoiceheader_sellerparty_siret_qualifier_notequal = El identificador del esquema SIRET del vendedor (BT-29b-1) debe ser igual a 0009, regla G1.71
invoicedetail_invoicepricingdetail_gross_unit_price_sum_inconsistency = El precio bruto del art\u00EDculo (BT-148) debe corresponder a la suma del precio neto (BT-146) y los descuentos (BT-147), regla G1.55
invoiceheader_tax_point_date_notunique = La fecha de exigibilidad del IVA (BT-7) debe ser \u00FAnica dentro de la factura, regla G1.36
invoicedetail_invoicepricingdetail_tax_tax_type_coded_other_not_in_list = El c\u00F3digo del tipo de IVA del art\u00EDculo facturado (BT-151) debe pertenecer a la lista de valores autorizados, regla G2.31
invoicedetail_invoicepricingdetail_gross_unit_price_too_large = El precio bruto del art\u00EDculo (BT-148) no puede tener m\u00E1s de diecinueve cifras, incluidos cuatro decimales, regla G1.15
invoiceheader_buyer_reference_b2g_fr_notunique = Solo puede haber un c\u00F3digo de servicio (BT-10) por factura, regla G1.11
invoiceheader_invoice_number_empty_or_notunique = Debe introducirse el n\u00FAmero de factura (BT-1), regla BR-2
invoiceheader_invoiceparty_address_country_coded_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del representante fiscal (BT-69), regla G1.49
invoiceheader_invoiceparty_sellerparty_empty = Debe introducirse el vendedor (BG-4), regla G6.08
invoiceheader_invoice_type_empty_or_notunique = Una factura debe tener un c\u00F3digo de tipo de factura (BT-3), reglas G6.08 y BR-4
invoiceheader_sellerparty_only_b2g_illegalvalue = El PPF solo recibe facturas B2G de este tipo de proveedores (BT-29b-1), regla G1.13
invoicedetail_charge_list_of_description_empty = La descripci\u00F3n del motivo de los cargos o tasas aplicables a la l\u00EDnea de factura (BT-144) debe rellenarse si no se ha rellenado el c\u00F3digo correspondiente (BT-145), regla G1.29
invoicesummary_invoicetotals_rounded_amount_too_large = El importe redondeado (BT-114) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoicesummary_invoicetotals_allowance_total_monetary_amount_not_decimal = La suma total de los descuentos (BT-107) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_empty = Debe introducirse el importe de descuento global (BT-92) o el importe de cargo global (BT-99), regla G1.14
invoicedetail_invoicepricingdetail_gross_unit_price_negative = El precio bruto de un art\u00EDculo (BT-148) debe ser positivo o igual a cero, regla BR-28
invoiceheader_buyerparty_address_country_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del comprador (BT-55), regla G6.08
invoicedetail_article_name_empty = El nombre del art\u00EDculo de una l\u00EDnea (BT-153) es obligatorio en cada l\u00EDnea, norma BR-25
invoicesummary_invoicetotals_payroll_amount_notunique = El importe pagado (BT-113) debe ser \u00FAnico, regla G1.04
invoicedetail_invoiced_quantity_not_in_list = El c\u00F3digo de la unidad de medida de la cantidad facturada a la l\u00EDnea (BT-130) debe pertenecer a la lista definida por la norma EN-16931, regla G6.09.
invoicedetail_invoicingperiod_end_date_not_century_21 = La fecha de fin del periodo de facturaci\u00F3n de una l\u00EDnea (BT-135) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36.
invoiceheader_invoicing_period_end_date_not_century_21 = La fecha de fin del periodo de facturaci\u00F3n (BT-74) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36.
invoicedetail_allowance_service_coded_other_not_in_list = El c\u00F3digo de motivo de descuento en la l\u00EDnea (BT-140) debe cumplir la norma UNTDID-5189, regla G1.29.
invoice_tax_invoice_detail_invoice_line_empty = La presencia de al menos una l\u00EDnea de factura (BG-25) es obligatoria, regla G6.09
invoicedetail_article_name_notunique = El nombre del art\u00EDculo (BT-153) debe ser \u00FAnico, regla G6.09
invoiceheader_invoice_number_too_large = El n\u00FAmero de factura (BT-1) es demasiado grande, regla G1.05
invoiceheader_tax_treatment_code_other_not_in_list = El c\u00F3digo de la fecha de exigibilidad del IVA (BT-8) debe ser igual a uno de los valores permitidos, regla S1.13
invoicedetail_invoicepricingdetail_net_unit_price_too_large = El precio neto del art\u00EDculo (BT-146) no puede tener m\u00E1s de diecinueve cifras, incluidos cuatro decimales, regla G1.15
invoicesummary_listoftaxsummary_tax_exempt_reason_notunique = El motivo de la exenci\u00F3n del IVA (BT-120) debe ser \u00FAnico, regla G1.40
invoiceheader_invoice_number_not_ascii = El n\u00FAmero de factura (BT-1) contiene caracteres prohibidos, regla G1.05
invoiceheader_billtoparty_address_country_code_not_in_list = El c\u00F3digo del pa\u00EDs \"destinatario\" (EXT-FR-FE-107) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoicedetail_article_ident_notunique = El identificador del art\u00EDculo (BG-31) debe ser \u00FAnico, regla G6.09
invoiceheader_list_of_attachment_file_name_not_character_accepted = El nombre del archivo del documento adjunto (BT-125-2) no debe contener caracteres especiales, regla G4.09
invoiceheader_invoice_issue_date_not_century_21 = La fecha de facturaci\u00F3n (BT-2) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36
invoiceheader_contract_type_fr_notequal = El tipo de contrato de una factura (EXT-FR-FE-01) debe pertenecer a la lista de c\u00F3digos autorizados, regla G1.03.
invoiceheader_sellerparty_siret_0226_too_large = El c\u00F3digo identificador de un vendedor concreto (BT-29b) debe tener un m\u00E1ximo de 80 caracteres, regla G1.08
invoicesummary_listoftaxsummary_tax_exempt_reason_empty = En caso de exenci\u00F3n del IVA (BT-118), debe indicarse el c\u00F3digo (BT-121) y la descripci\u00F3n (BT-120) del motivo de la exenci\u00F3n, regla G1.40
invoiceheader_list_of_attachment_file_name_not_unique_content = El nombre de archivo del documento adjunto (BT-125-2) debe ser \u00FAnico, regla G4.15
invoiceheader_payer_address_country_code_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del pagador (EXT-FR-FE-61), regla G2.01
invoicedetail_invoicepricingdetail_allow_or_charge_basis_of_fees_applicable_to_the_invoice_line_too_large = La base de los cargos o tasas aplicables a la l\u00EDnea de factura (BT-142) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoiceheader_invoice_issue_date_empty_or_notunique = Debe introducirse la fecha de facturaci\u00F3n (BT-2), reglas G6.08 y BR-3
invoicesummary_invoicetotals_total_amount_payable_empty = Debe indicarse el importe a pagar (BT-115), regla G1.14
invoiceheader_sellers_agent_or_representative_address_country_code_not_in_list = El c\u00F3digo de pa\u00EDs del agente del vendedor (EXT-FR-FE-84) debe ser ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_invoiceparty_VAT_seller_identifier_notunique = El identificador de IVA del agente del vendedor (EXT-FR-FE-73) debe ser \u00FAnico, regla G6.17
invoicedetail_invoicingperiod_start_date_notunique = La fecha de inicio del periodo de facturaci\u00F3n de una l\u00EDnea (BT-134) solo debe introducirse una vez, regla G1.36
invoiceheader_fr_requirements_business_process_type_fr_not_in_list = El marco de facturaci\u00F3n (BT-23) debe pertenecer a la lista de c\u00F3digos autorizados, regla G1.02.
invoiceheader_sellers_agent_or_representative_address_country_code_notunique = El c\u00F3digo de pa\u00EDs del agente del vendedor (EXT-FR-FE-84) debe ser \u00FAnico, regla G2.01
invoiceheader_tax_treatment_code_other_notunique = El c\u00F3digo de la fecha de exigibilidad del IVA (BT-8) debe ser \u00FAnico, regla S1.13
invoiceheader_invoice_type_equal = El c\u00F3digo de tipo de factura (BT-3) no debe ser 386 porque el marco de facturaci\u00F3n (BT-23) es B7 o S7, regla G1.60
invoicesummary_invoicetotals_total_amount_payable_too_large = El importe a pagar (BT-115) no puede tener m\u00E1s de diecinueve cifras, incluidos dos decimales, regla G1.14
invoicedetail_invoicingperiod_end_date_notunique = La fecha de fin del periodo de facturaci\u00F3n de una l\u00EDnea (BT-135) solo debe introducirse una vez, regla G1.36
invoiceheader_billtoparty_address_country_code_empty = Debe introducirse el c\u00F3digo del pa\u00EDs \"destinatario\" (EXT-FR-FE-107), regla G2.01
invoiceheader_party_tax_information_ident_notunique = El identificador de IVA del beneficiario (EXT-FR-FE-27) debe ser \u00FAnico, regla G6.18
invoiceheader_buyerparty_address_name_notunique = El nombre del comprador (BG-8) debe ser \u00FAnico, regla G6.08
invoicedetail_allowance_amount_not_decimal = El importe del descuento (BT-136) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicesummary_listoftaxsummary_taxsummary_header_empty = Una factura debe contener al menos un desglose de IVA (BG-23) con un c\u00F3digo de tipo de IVA (BT-118), reglas BR-CO-18 y BR-47.
invoiceheader_invoicing_period_start_date_empty = El periodo de facturaci\u00F3n de una factura requiere una fecha de inicio (BT-73) y una fecha de fin (BT-74), regla G6.11 y regla BR-CO-19
invoicedetail_allowance_amount_notunique = El importe del descuento (BT-136) debe indicarse una sola vez por descuento, regla G1.14
invoicesummary_invoicetotals_allowance_total_monetary_amount_sum_inconsistency = La suma total de los descuentos (BT-107) debe ser igual a la suma de todos los descuentos del documento (BT-92), regla G1.54
invoiceheader_invoice_type_bt_121_not_in_list = El c\u00F3digo del tipo de factura (BT-3) debe ser 261, 381 o 396 porque el c\u00F3digo del motivo de exenci\u00F3n del IVA (BT-121) es VATEX-FR-CNWVAT, regla G6.21
invoicesummary_list_of_tax_summary_VAT_amount_empty = Debe introducirse el importe del IVA para cada tipo de IVA (BT-117), regla G1.14
invoicesummary_listoftaxsummary_taxsummary_notequal = El c\u00F3digo del tipo de IVA (BT-118) debe ser igual a E seg\u00FAn el c\u00F3digo del motivo de exenci\u00F3n del IVA (BT-121), regla G1.56.
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_notunique = El importe del IVA por tipo de IVA (BT-117) debe ser \u00FAnico, regla G1.14
invoicedetail_invoicepricingdetail_gross_unit_price_not_decimal = El precio bruto del art\u00EDculo (BT-148) no puede tener m\u00E1s de cuatro decimales, regla G1.15
invoiceheader_contract_type_fr_empty = El tipo de contrato (EXT-FR-FE-01) es obligatorio cuando se refiere a un contrato (BT-12), regla G1.03
invoicedetail_ship_to_country_code_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del lugar de entrega a la l\u00EDnea (EXT-FR-FE-157), regla G6.11
invoiceheader_sellerparty_siret_qualifier_not_in_list = Tipo de identificador del vendedor (BT-29b-1) desconocido, regla G1.11
invoiceheader_alloworcharge_service_coded_other_not_in_list = El c\u00F3digo de motivo de descuento (BT-98) debe cumplir la norma UNTDID-5189, regla G1.29.
invoicedetail_allowance_amount_too_large = El importe del descuento (BT-136) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoiceheader_taxfunction_vat_type_empty = Debe introducirse el tipo de IVA (BT-95 y BT-102), regla G6.12
invoiceheader_despatch_date_not_century_21 = La fecha efectiva de entrega (BT-72) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36
invoiceheader_alloworcharge_charge_list_of_description_empty = La descripci\u00F3n del motivo de los cargos o tasas (BT-104) debe introducirse si no se ha introducido el c\u00F3digo correspondiente (BT-105), regla G1.29
invoiceheader_buyerparty_identifier_routing_code_not_french = El identificador del comprador (c\u00F3digo de encaminamiento) (BT-46c) no debe contener caracteres especiales, regla G2.29
invoiceheader_sellerparty_siret_qualifier_empty = Debe rellenarse el identificador del r\u00E9gimen SIRET del vendedor (BT-29b-1), regla G1.11
invoicesummary_invoicetotals_charge_total_monetary_amount_notunique = La suma total de cargos o tasas (BT-108) debe ser \u00FAnica, regla G1.14
invoiceheader_billtoparty_address_country_code_notunique = El c\u00F3digo del pa\u00EDs \"destinatario\" (EXT-FR-FE-107) debe ser \u00FAnico, regla G2.01
invoicedetail_invoicepricingdetail_allow_or_charge_basis_of_fees_applicable_to_the_invoice_line_notunique = La base de los cargos o tasas aplicables a la l\u00EDnea de factura (BT-142) debe ser \u00FAnica, regla G1.14
invoiceheader_remit_to_party_address_country_code_not_in_list = El c\u00F3digo de pa\u00EDs del beneficiario (EXT-FR-FE-38) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_buyer_order_number_b2g_fr_empty = La entidad p\u00FAblica que recibe esta factura necesita una referencia de compromiso (BT-13), regla G1.04
invoicedetail_allowance_service_coded_other_empty = El motivo de descuento aplicable a la l\u00EDnea de factura (BT-140) debe introducirse si la descripci\u00F3n correspondiente (BT-139) no lo est\u00E1, regla G1.29, BR-CO-23, BR-42
invoicedetail_invoicepricingdetail_base_quantity_of_item_price_too_large = La cantidad base del precio del art\u00EDculo (BT-149) debe ser \u00FAnica, regla G1.15
invoiceheader_tax_point_date_not_century_21 = La fecha de exigibilidad del IVA debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36
invoiceheader_sellerparty_agency_coded_other_not_in_list = El identificador del esquema del SIRET del vendedor (BT-29b-1) debe pertenecer a la lista de valores autorizados, regla G1.71
invoicesummary_invoicetotals_total_VAT_amount_too_large = El importe total del IVA expresado en moneda contable (BT-111) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoiceheader_buyer_order_number_b2g_fr_too_large = La referencia de la nota de pedido (BT-13) contiene demasiados caracteres, regla G3.04
invoiceheader_list_of_attachment_MIME_Type_notunique = El c\u00F3digo MIME del documento adjunto (BT-125-1) debe ser \u00FAnico, regla G4.17
invoiceheader_alloworcharge_vat_rate_not_in_decimal_list = El tipo de IVA para un descuento global (BT-96) o un cargo global (BT-103) debe pertenecer a la lista de tipos de IVA posibles, regla G1.24.
invoiceheader_former_invoice_type_not_in_list = El c\u00F3digo del tipo de factura (EXT-FR-FE-02) de una factura anterior debe pertenecer a la lista de c\u00F3digos autorizados, regla G1.01.
invoicesummary_invoicetotals_monetary_amount_notequal = El importe total del IVA (BT-110) debe ser cero, regla G1.56
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_empty = Debe introducirse la suma de los importes netos de las l\u00EDneas de factura (BT-106), regla G1.14
invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_notunique = La base de los cargos o descuentos (BT-93 y BT-100) debe ser \u00FAnica, regla G1.14
invoiceheader_factor_address_country_code_notunique = El c\u00F3digo de pa\u00EDs del tercero emisor de la factura (EXT-FR-FE-130) debe ser \u00FAnico, regla G2.01
invoiceheader_taxfunction_vat_type_notunique = El tipo de IVA (BT-95 y BT-102) debe ser \u00FAnico, regla G2.31
invoiceheader_invoiceparty_agent_or_representative_code_empty = Debe introducirse el n\u00FAmero de SIREN del \"destinatario\" (EXT-FR-FE-94), regla G1.63
invoiceheader_invoiceparty_buyerparty_notunique = El comprador (BG-7) debe ser \u00FAnico, regla G6.08
invoicedetail_invoicepricingdetail_net_invoice_line_amount_not_decimal = El importe neto de la l\u00EDnea (BT-131) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_base_item_detail_country_origin_code_notunique = El c\u00F3digo del pa\u00EDs de origen del art\u00EDculo (BT-159) debe ser \u00FAnico, regla G2.01
invoiceheader_taxfunction_vat_type_not_in_list = El tipo de IVA (BT-95 y BT-102) debe pertenecer a la lista de valores autorizados, regla G6.12
invoicesummary_invoicetotals_charge_total_monetary_amount_too_large = La suma total de los cargos o tasas (BT-108) no puede exceder de diecinueve cifras, incluidos dos decimales, regla G1.14.
invoicedetail_structured_note_text_type_coded_notunique = El c\u00F3digo de asunto de la nota de l\u00EDnea de factura (EXT-FR-FE-183) debe ser \u00FAnico, regla G1.52
invoiceheader_invoice_type_not_in_list = Un c\u00F3digo de tipo de factura (BT-3) debe pertenecer a la lista de c\u00F3digos autorizados, regla G1.01.
invoiceheader_invoiceparty_seller_agent_or_representative_code_empty = Debe introducirse el n\u00FAmero SIREN del agente del vendedor (EXT-FR-FE-71), regla G1.81
invoicedetail_invoicepricingdetail_base_quantity_of_item_price_notunique = La cantidad base del precio del art\u00EDculo (BT-149) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos cuatro decimales, regla G1.15
invoiceheader_list_of_attachment_MIME_Type_not_in_list = El c\u00F3digo MIME del documento adjunto (BT-125-1) debe pertenecer a la lista de valores permitidos, regla G4.17
invoiceheader_remit_to_party_siret_0229_too_large = El c\u00F3digo TAHITI del beneficiario (BT-60) debe tener un m\u00E1ximo de 9 caracteres, regla G1.08
invoicesummary_listoftaxsummary_taxsummary_not_in_list = El c\u00F3digo del tipo de IVA (BT-118) debe pertenecer a la lista de valores autorizados, regla G2.31
invoicesummary_invoicetotals_payroll_amount_not_decimal = El importe pagado (BT-113) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_invoiced_quantity_notunique = La cantidad facturada a la l\u00EDnea (BT-129) y su c\u00F3digo de unidad de medida (BT-130) deben ser \u00FAnicos para cada l\u00EDnea, regla G6.09
invoiceheader_former_invoice_ref_date_not_century_21 = La fecha de emisi\u00F3n de una factura anterior (BT-26) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36
invoicedetail_invoicepricingdetail_allow_or_charge_base_for_invoice_line_discount_not_decimal = La base de descuento de la l\u00EDnea de factura (BT-137) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_invoice_type_notequal = El c\u00F3digo de tipo de factura (BT-3) debe ser 380 porque el marco de facturaci\u00F3n (BT-23) es B4, M4 o S4, regla G1.60
invoiceheader_invoiceparty_seller_agent_or_representative_code_notunique = El n\u00FAmero SIREN del agente del vendedor (EXT-FR-FE-71) debe ser \u00FAnico, regla G1.81
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_notunique = La suma de los importes netos de las l\u00EDneas de factura (BT-106) debe ser \u00FAnica, regla G1.14
invoicedetail_invoicepricingdetail_allow_or_charge_basis_of_fees_applicable_to_the_invoice_line_not_decimal = La base de los gastos o tasas aplicables a la l\u00EDnea de factura (BT-142) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_not_in_list = El c\u00F3digo de tipo de factura (EXT-FR-FE-137) de una factura anterior indicado en la l\u00EDnea debe pertenecer a la lista de c\u00F3digos autorizados, regla G1.01.
invoiceheader_due_date_notunique = La fecha de vencimiento (BT-9) debe ser \u00FAnica dentro de la factura, regla G1.36
invoiceheader_alloworcharge_service_coded_other_empty = El c\u00F3digo del motivo del descuento (BT-98) debe introducirse si la descripci\u00F3n correspondiente (BT-97) no lo est\u00E1, regla G1.29
invoiceheader_remit_to_party_address_country_code_notunique = El c\u00F3digo de pa\u00EDs del beneficiario (EXT-FR-FE-38) debe ser \u00FAnico, regla G2.01
invoicedetail_invoicingperiod_start_date_not_century_21 = La fecha de inicio del periodo de facturaci\u00F3n de una l\u00EDnea (BT-134) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36.
invoicedetail_base_item_detail_country_origin_code_not_in_list = El c\u00F3digo del pa\u00EDs de origen del art\u00EDculo (BT-159) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoicedetail_article_ident_empty = El identificador del art\u00EDculo (BG-31) es obligatorio, regla G6.09
invoiceheader_shiptoparty_address_country_notunique = El c\u00F3digo de pa\u00EDs del lugar de entrega (BT-80) debe ser \u00FAnico, regla G6.08
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_not_decimal = La suma de los importes netos de las l\u00EDneas de factura (BT-106) no puede tener m\u00E1s de dos decimales, regla G1.14
invoicedetail_invoicingperiod_notunique = Solo se permite un periodo de facturaci\u00F3n (BG-26) por l\u00EDnea, regla G6.11
invoiceheader_list_of_attachment_file_name_notunique = El nombre de archivo del documento adjunto (BT-125-2) debe ser \u00FAnico, regla G4.16
invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_too_large = La base de los cargos o descuentos (BT-93 y BT-100) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoiceheader_sellerparty_siret_0228_too_large = El RIDET del vendedor (BT-29b) debe tener un m\u00E1ximo de 10 caracteres, regla G1.08
invoicesummary_invoicetotals_total_amount_not_same_content = El importe total IVA incluido (BT-112) debe ser igual a la base imponible \u00FAnica por tipo de IVA (BT-116) indicada en la factura, regla G1.56.
invoiceheader_sellerparty_address_country_not_in_list = El c\u00F3digo de pa\u00EDs del vendedor debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_former_invoice_number_fr_empty = El n\u00FAmero de la factura anterior (BT-25) debe introducirse si el tipo de factura (BT-3) es una factura rectificativa (384) o una nota de abono (381), regla G1.31.
invoiceheader_invoiceparty_agent_or_representative_code_notunique = El n\u00FAmero SIREN del \"destinatario\" (EXT-FR-FE-94) debe ser \u00FAnico, regla G1.63
invoiceheader_invoicing_period_start_date_not_century_21 = La fecha de inicio del periodo de facturaci\u00F3n (BT-73) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36.
invoicesummary_invoicetotals_notunique = Los totales del pie de p\u00E1gina (BG-22) solo deben mostrarse una vez, regla G6.08
invoicedetail_invoicepricingdetail_discount_on_item_price_not_decimal = El descuento sobre el precio del art\u00EDculo (BT-147) no puede tener m\u00E1s de cuatro decimales, regla G1.15
invoicedetail_invoicepricingdetail_tax_tax_type_coded_other_notunique = El c\u00F3digo del tipo de IVA del art\u00EDculo facturado (BT-151) debe ser \u00FAnico, regla G2.31
invoiceheader_agent_or_representative_address_country_code_not_in_list = El c\u00F3digo de pa\u00EDs del agente (EXT-FR-FE-21) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_payer_address_country_code_notunique = El c\u00F3digo de pa\u00EDs del pagador (EXT-FR-FE-61) debe ser \u00FAnico, regla G2.01
invoicesummary_invoicetotals_total_VAT_amount_notunique = El importe total del IVA expresado en la moneda contable (BT-111) debe ser \u00FAnico, regla G1.14
invoiceheader_former_invoice_number_fr_not_ascii = El n\u00FAmero de factura anterior (BT-25) contiene caracteres prohibidos, regla G1.05
invoiceheader_former_invoice_number_fr_too_large = El n\u00FAmero de la factura anterior (BT-25) es demasiado grande, regla G1.05
invoicedetail_delivery_ref_date_not_century_21 = La fecha de entrega a la l\u00EDnea (EXT-FR-FE-158) debe estar comprendida entre el 1 de enero de 2000 y el 31 de diciembre de 2099, regla G1.36.
invoiceheader_buyer_order_number_b2g_fr_notunique = Solo puede haber una referencia de compromiso (BT-13) por factura, norma G1.11
invoicedetail_quantity_value_empty = La cantidad facturada (BT-129) es obligatoria en cada l\u00EDnea, regla BR-22
invoiceheader_invoice_issue_date_not_past_date = La fecha de emisi\u00F3n de la factura (BT-2) debe ser anterior o igual a la fecha de presentaci\u00F3n, regla G1.07
invoiceheader_buyerparty_address_country_notunique = El c\u00F3digo de pa\u00EDs del comprador (BT-55) debe ser \u00FAnico, regla G6.08
invoiceheader_invoiceparty_VAT_payer_identifier_notunique = El identificador de IVA del pagador (EXT-FR-FE-50) debe ser \u00FAnico, regla G6.17
invoiceheader_shiptoparty_address_country_not_in_list = El c\u00F3digo de pa\u00EDs del lugar de entrega (BT-80) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_invoicing_period_start_date_notunique = La fecha de inicio del periodo de facturaci\u00F3n (BT-73) solo debe introducirse una vez, regla G1.36.
invoiceheader_sellerparty_siret_fr_too_large = El SIRET del vendedor (BT-29b) debe tener un m\u00E1ximo de 14 caracteres, regla G1.08
invoicedetail_invoicepricingdetail_allow_or_charge_base_for_invoice_line_discount_notunique = La base para el descuento por l\u00EDnea de factura (BT-137) debe ser \u00FAnica, regla G1.14
invoiceheader_billtoparty_siret_ident_not_french = El c\u00F3digo SIRET (EXT-FR-FE-92c) no debe contener caracteres especiales, regla G2.29
invoiceheader_remit_to_party_siret_0226_too_large = El c\u00F3digo que identifica a un beneficiario concreto (BT-60) debe tener un m\u00E1ximo de 80 caracteres, regla G1.08
invoicesummary_listoftaxsummary_tax_exempt_code_not_in_list = El c\u00F3digo de motivo de exenci\u00F3n del IVA (BT-121) debe pertenecer a la lista CEF VATEX
invoicedetail_invoicingperiod_start_date_empty = La fecha de inicio del periodo de facturaci\u00F3n de una l\u00EDnea (BT-134) debe introducirse si se introduce la fecha final, regla G6.11.
invoicesummary_invoicetotals_charge_total_monetary_amount_sum_inconsistency = La suma total de cargos o tasas (BT-108) debe ser igual a la suma de cargos o tasas del documento (BT-99), regla G1.54
invoicedetail_invoicepricingdetail_net_unit_price_not_decimal = El precio neto del art\u00EDculo (BT-146) no puede tener m\u00E1s de cuatro decimales, regla G1.15
invoicesummary_invoicetotals_rounded_amount_not_decimal = El importe redondeado (BT-114) no puede tener m\u00E1s de dos decimales, regla G1.14
invoiceheader_payer_address_country_code_not_in_list = El c\u00F3digo de pa\u00EDs del pagador (EXT-FR-FE-61) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_fr_requirements_profile_type_not_in_list = El tipo de perfil (BT-24) debe pertenecer a la lista de c\u00F3digos autorizados, norma S1.06.
invoicedetail_invoicingperiod_end_date_empty = La fecha de fin del periodo de facturaci\u00F3n de una l\u00EDnea (BT-135) debe introducirse si se introduce la fecha de inicio, regla G6.11
invoiceheader_sellerparty_siret_qualifier_not_french = El identificador del vendedor (c\u00F3digo de encaminamiento) (BT-29c) no debe contener caracteres especiales, regla G2.29
invoiceheader_invoice_type_other_not_in_list = El c\u00F3digo de tipo de factura (BT-3) debe corresponder al cr\u00E9dito autofacturado (261) o al cr\u00E9dito factorizado (396), regla G6.21
invoicedetail_delivery_ref_date_notunique = Solo puede haber una fecha de entrega por l\u00EDnea (EXT-FR-FE-158), regla G1.36
invoiceheader_remit_to_party_siret_fr_too_large = El SIRET del beneficiario (BT-60) debe tener un m\u00E1ximo de 14 caracteres, regla G1.08
invoiceheader_sellerparty_siret_0229_too_large = El c\u00F3digo TAHITI del vendedor (BT-29b) debe tener un m\u00E1ximo de 9 caracteres, regla G1.08
invoicesummary_list_of_tax_summary_VAT_amount_notunique = El importe del IVA para cada tipo de IVA (BT-117) debe ser \u00FAnico, regla G1.14
invoicedetail_buyer_line_number_not_unique_content = El identificador de l\u00EDnea de factura (BT-126) debe ser \u00FAnico dentro de la factura, regla G1.62
invoiceheader_remit_to_party_siret_0227_too_large = El c\u00F3digo que identifica a un beneficiario no europeo (BT-60) debe tener un m\u00E1ximo de 18 caracteres, regla G1.08
invoicedetail_charge_amount_too_large = El importe del cargo (BT-141) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14.
invoiceheader_tax_currency_fr_not_in_list = El c\u00F3digo de moneda del IVA (BT-6) debe corresponder a un valor autorizado de la norma ISO-4217 G1.10.
invoiceheader_buyerparty_siret_ident_empty = El identificador de IVA del comprador (BT-48) es obligatorio, regla G1.72
invoiceheader_alloworcharge_charge_service_coded_other_not_in_list = El c\u00F3digo de motivo de los cargos o tasas (BT-105) debe ajustarse a la norma UNTDID-7161, regla G1.29.
invoiceheader_former_invoice_former_invoice_type_not_in_list = El c\u00F3digo del tipo de factura (EXT-FR-FE-02) de una factura anterior debe pertenecer a la lista de c\u00F3digos autorizados, regla G1.01.
invoiceheader_list_of_attachment_MIME_Type_empty = Debe introducirse el c\u00F3digo MIME del documento adjunto (BT-125-1), regla G4.17
invoicesummary_invoicetotals_empty = Los totales del pie de factura (BG-22) son obligatorios, regla G6.08
invoicesummary_listoftaxsummary_tax_exempt_code_notunique = El c\u00F3digo de motivo de exenci\u00F3n del IVA (BT-121) debe ser \u00FAnico, regla G1.40
invoicedetail_invoicepricingdetail_net_invoice_line_amount_notunique = El importe neto de l\u00EDnea (BT-131) debe ser \u00FAnico, regla G1.17
invoiceheader_fr_requirements_business_process_type_fr_empty_or_notunique = El marco de facturaci\u00F3n (BT-23) es obligatorio, regla G6.08
invoicedetail_quantity_value_not_decimal = La cantidad facturada (BT-129) no puede tener m\u00E1s de cuatro decimales, regla G1.15
invoiceheader_former_invoice_type_empty = El c\u00F3digo de tipo de factura (EXT-FR-FE-02) de una factura anterior es obligatorio, regla G1.01
invoiceheader_shiptoparty_address_country_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del lugar de entrega (BT-80), regla G6.08
invoiceheader_contract_reference_too_large = La referencia del contrato (BT-12) debe tener un m\u00E1ximo de 50 caracteres, regla G3.02
invoiceheader_agent_or_representative_address_country_code_notunique = El c\u00F3digo de pa\u00EDs del agente (EXT-FR-FE-21) debe ser \u00FAnico, regla G2.01
invoicesummary_invoicetotals_total_VAT_amount_notequal = El importe total del IVA expresado en moneda contable (BT-111) debe ser cero, regla G1.56
invoicesummary_list_of_tax_summary_VAT_amount_too_large = El importe del IVA para cada tipo de IVA (BT-117) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoicedetail_invoicepricingdetail_vat_rate_notunique = El tipo de IVA del art\u00EDculo facturado (BT-152) debe ser \u00FAnico, regla G1.24
invoiceheader_remit_to_party_address_country_code_empty = Debe introducirse el c\u00F3digo de pa\u00EDs del beneficiario (EXT-FR-FE-38), regla G2.01
invoiceheader_requirement_country_notequal = El tipo de perfil (BT-24) es obligatorio, norma BR-1
invoicedetail_buyer_line_number_notunique = El identificador de l\u00EDnea de factura (BT-126) debe ser \u00FAnico dentro de la factura, regla G1.62
invoicedetail_allowance_amount_empty = En el caso de los descuentos por l\u00EDnea de factura, el importe sin IVA (BT-136) es obligatorio, regla G1.14
invoiceheader_former_invoice_number_fr_empty_or_notunique = Una factura anterior debe tener un n\u00FAmero de factura (BT-25), regla BR-55
invoiceheader_sellerparty_siret_empty = El identificador del vendedor (BT-29b) es obligatorio, regla G1.11
invoiceheader_tax_treatment_code_other_empty = Debe rellenarse el c\u00F3digo de la fecha de exigibilidad del IVA (BT-8), regla G6.11
invoiceheader_requirement_country_empty = El tipo de perfil (BT-24) es obligatorio, norma BR-1
invoicedetail_ship_to_country_code_not_in_list = El c\u00F3digo de pa\u00EDs del lugar de entrega a la l\u00EDnea (EXT-FR-FE-157) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoicedetail_item_atribute_name_value_empty = Un atributo de art\u00EDculo se compone necesariamente de un nombre (BT-160) y un valor (BT-161), regla BR-54
invoiceheader_former_invoice_former_invoice_type_empty = Debe introducirse el c\u00F3digo de tipo de factura (EXT-FR-FE-02) de una factura anterior, regla G1.01
invoicedetail_allowance_list_of_description_empty = La descripci\u00F3n del motivo de descuento aplicable a la l\u00EDnea de factura (BT-139) debe introducirse si el c\u00F3digo correspondiente (BT-140) no lo est\u00E1, regla G1.29, BR-CO-23, BR-42
invoiceheader_list_of_structured_notes_coded_other_empty = Una nota de factura consta necesariamente de un c\u00F3digo de asunto (BT-21) y de la propia nota (BT-22), regla G6.11
invoicesummary_invoicetotals_allowance_total_monetary_amount_too_large = La suma total de los descuentos (BT-107) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14.
invoicesummary_taxsummary_vat_rate_notunique = El tipo de IVA del desglose del IVA (BT-119) debe ser \u00FAnico, regla G1.24
invoiceheader_list_of_attachment_file_name_not_in_list = El nombre de archivo del documento adjunto (BT-125-2) debe pertenecer a la lista de extensiones autorizadas, regla G4.15
invoiceheader_invoiceparty_sellerparty_notunique = El vendedor (BG-4) debe ser \u00FAnico, regla G6.08
invoicedetail_charge_amount_notunique = El importe del cargo (BT-141) debe indicarse una sola vez por cargo, regla G1.14
invoiceheader_invoiceparty_address_country_coded_not_in_list = El c\u00F3digo de pa\u00EDs del representante fiscal (BT-69) debe cumplir la norma ISO-3166-1 alfa-2, regla G2.01.
invoiceheader_invoiceparty_buyerparty_empty = El comprador (BG-7) es obligatorio, norma G6.08
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_too_large = El importe del IVA por tipo de IVA (BT-117) no puede tener m\u00E1s de diecinueve d\u00EDgitos, incluidos dos decimales, regla G1.14
invoiceheader_buyer_reference_b2g_fr_empty_or_notunique = La entidad p\u00FAblica que recibe esta factura necesita un c\u00F3digo de servicio (BT-10) o una referencia de compromiso (BT-13), regla G1.04
invoiceheader_former_invoice_ref_date_notunique = La fecha de emisi\u00F3n de una factura anterior (BT-26) debe ser \u00FAnica, regla G1.36
invoicedetail_invoicepricingdetail_vat_rate_not_in_decimal_list = El tipo de IVA del art\u00EDculo facturado (BT-152) debe pertenecer a la lista de posibles tipos de IVA, regla G1.24
invoiceheader_invoice_currency_empty = La moneda de facturaci\u00F3n (BT-5) es obligatoria, regla G6.08
invoice_header_parties_Buyer_INSEE_SIREN_missing = {0} (BT-47) no coincide con un SIREN de comprador existente en la base de datos del INSEE, regla G1.63
invoice_header_parties_SellersAgentOrRepresentative_INSEE_SIRET_missing = {0} (EXT-FR-FE-69) no coincide con un SIRET de agente vendedor existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_BuyersAgentOrRepresentative_INSEE_SIRET_missing = {0} (EXT-FR-FE-06) no coincide con un SIRET de agente Comprador existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_Factor_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos de SIRET {0} (EXT-FR-FE-115) no coinciden con SIREN {1} (EXT-FR-FE-117) para Factor, regla G1.80
invoice_header_parties_SellersAgentOrRepresentative_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos del SIRET {0} (EXT-FR-FE-69) no coinciden con el SIREN {1} (EXT-FR-FE-71) para el agente vendedor, regla G1.80
invoice_header_parties_RemitTo_INSEE_SIREN_missing = {0} (BT-61) no coincide con un SIREN existente en la base de datos del INSEE, regla G1.75
invoice_header_parties_SellersAgentOrRepresentative_INSEE_SIREN_missing = {0} (EXT-FR-FE-71) no coincide con un SIREN de agente vendedor existente en la base de datos del INSEE, regla G1.75
invoice_header_parties_BuyersAgentOrRepresentative_INSEE_SIREN_missing = {0} (EXT-FR-FE-08) no coincide con un SIREN de agente Comprador existente en la base de datos del INSEE, regla G1.63
invoice_header_parties_BillTo_INSEE_SIREN_missing = {0} (EXT-FR-FE-94) no coincide con un n\u00FAmero SIREN de destinatario de factura existente en la base de datos del INSEE, regla G1.75
invoice_header_parties_BillTo_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos del SIRET {0} (EXT-FR-FE-92b) no coinciden con el SIREN {1} (EXT-FR-FE-94) para el destinatario de la factura, regla G1.80
invoice_header_parties_RemitTo_INSEE_SIRET_missing = {0} (BT-60) no coincide con un Remit to SIRET existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_BillTo_INSEE_SIRET_missing = {0} (EXT-FR-FE-92b) no coincide con un n\u00FAmero SIRET de destinatario de factura existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_Payer_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos del SIRET {0} (EXT-FR-FE-46) no coinciden con el SIREN {1} (EXT-FR-FE-48) para el Pagador, regla G1.80
invoice_header_parties_RemitTo_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos de SIRET {0} (BT-60) no coinciden con SIREN {1} (BT-61) para Remit to, regla G1.80
invoice_header_parties_Seller_INSEE_SIREN_missing = {0} (BT-30) no coincide con un n\u00FAmero SIREN de vendedor existente en la base de datos del INSEE, regla G1.61
invoice_header_parties_Factor_INSEE_SIREN_missing = {0} (EXT-FR-FE-117) no coincide con un SIREN de factor existente en la base de datos del INSEE, regla G1.75
invoice_header_parties_Seller_INSEE_SIRET_missing = {0} (BT-29b) no coincide con un n\u00FAmero SIRET de vendedor existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_BuyersAgentOrRepresentative_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos del SIRET {0} (EXT-FR-FE-06) no coinciden con el SIREN {1} (EXT-FR-FE-08) para el agente comprador, regla G1.80
invoice_header_parties_Factor_INSEE_SIRET_missing = {0} (EXT-FR-FE-115) no coincide con un Factor SIRET existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_Payer_INSEE_SIRET_missing = {0} (EXT-FR-FE-46) no coincide con un SIRET de Pagador existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_Buyer_INSEE_SIRET_missing = {0} (BT-46b) no coincide con un SIRET de comprador existente en la base de datos del INSEE, regla G1.74
invoice_header_parties_Payer_INSEE_SIREN_missing = {0} (EXT-FR-FE-48) no coincide con un SIREN de Pagador existente en la base de datos del INSEE, regla G1.75
invoice_header_parties_Buyer_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos del SIRET {0} (BT-46b) no coinciden con el SIREN {1} (BT-47) del comprador, regla G1.80
invoice_header_parties_Seller_SIRET_SIREN_unmatched = Los primeros 9 d\u00EDgitos del SIRET {0} (BT-29b) no coinciden con el SIREN {1} (BT-30) del vendedor, regla G1.80
invoiceheader_fr_requirements_business_process_type_fr_illegalvalue = Si el c\u00F3digo de tipo de proceso empresarial (BT-23) es S4, el destinatario de la factura debe ser una entidad p\u00FAblica, regla G1.81.