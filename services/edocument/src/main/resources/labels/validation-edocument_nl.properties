invoiceheader_headoffice_vat_ident_empty = Het intracommunautaire BTW nummer van de maatschappelijke zetel moet verstrekt worden
invoiceheader_billtoparty_gov_reference_notunique = De RCS-RCM van de gefactureerde moet uniek zijn
invoiceheader_headoffice_address_city_notunique = De stad van de maatschappelijke zetel moet uniek zijn
invoicesummary_actualpayment_payment_number_empty = Het nummer van elk voorschot moet verstrekt worden
invoiceheader_sellerparty_address_name_notunique = De verkoper kan slechts een bedrijfsnaam hebben
invoiceheader_headoffice_address_postalcode_empty = De postcode van de maatschappelijke zetel moet verstrekt worden
invoicesummary_taxsummary_tax_amount_notunique = Er mag slechts een taksbedrag voor elk BTW tarief dat in het overzicht staat verstrekt worden
invoicedetail_quantity_value_notunique = Er kan slechts een hoeveelheid op elke lijn opgegeven worden
invoiceheader_tax_accounting_currency_notequal = De munteenheid voor de BTW betaling moet de euro zijn
invoiceheader_sellerparty_vat_ident_empty = Het intracommunautaire BTW nummer van de verkoper moet verstrekt worden
invoiceheader_tax_accounting__notunique = De munteenheid voor de BTW betaling moet uniek zijn
invoiceheader_billtoparty_address_city_empty = De stad van de gefactureerde moet verstrekt worden
invoicesummary_actualpayment_prepaid_amount_illegalvalue = Het voorafbetaald bedrag is niet juist
invoiceheader_sellerparty_missing = De verkoper moet verstrekt worden
invoice_tax_line_tax_unmatched = De taks op de lijn verschijnt niet in het overzicht van de taksen onderaan het bericht.
invoiceheader_sellerparty_gov_reference_empty = De RCS-RCM van de verkoper moet verstrekt worden
invoiceheader_alloworcharge_description_empty = De omschrijving van een korting of kost op het briefhoofd moet verstrekt worden
invoicedetail_invoicepricingdetail_gross_unit_price_notunique = De bruto eenheidsprijs zonder taksen moet op elke lijn uniek zijn
invoiceheader_date_preparation_date_time_document_illegalvalue = De datum/de tijd van de voorbereiding van het document is niet juist
invoiceheader_headoffice_registration_ident_empty = De SIREN van de maatschappelijke zetel moet verstrekt worden
invoicedetail_charge_type_notunique = Het tarief of het verstrekt bedrag van de kost op de lijn moet uniek zijn
invoicesummary_invoicetotals_total_amount_empty = Het totale bedrag met alle taksen inbegrepen moet verstrekt worden
invoicesummary_invoicetotals_monetary_amount_illegalvalue = Het totale taksbedrag is niet juist
invoicedetail_allowance_amount_illegalvalue = Het bedrag van de korting op de lijn is niet juist
invoicesummary_actualpayment_prepaid_amount_empty = Het voorafbetaald bedrag moet verstrekt worden
invoice_tax_taxsummary_tax_unmatched = Het tarief van de taks ontbreekt in de rest van het bericht
invoiceheader_headoffice_gov_reference_notunique = De RCS-RCM van de maatschappelijke zetel moet uniek zijn
invoiceheader_billtoparty_address_postalcode_empty = De postcode van de gefactureerde moet verstrekt worden
invoicedetail_allowance_type_notunique = Het tarief of het verstrekt bedrag van de korting op de lijn moet uniek zijn
invoiceheader_declarantsagentorrepresentative_address_name_notunique = De fiscale vertegenwoordiger kan slechts een bedrijfsnaam hebben
invoiceheader_paymentterms_latepayment_term_empty = De vaste boetevergoeding moet verstrekt worden
invoicedetail_charge_percent_illegalvalue = Het tarief van de kost op de lijn is niet juist
invoiceheader_declarantsagentorrepresentative_address_city_empty = De stad van de fiscale vertegenwoordiger moet verstrekt worden
invoiceheader_declarantsagentorrepresentative_address_name_empty = De bedrijfsnaam van de fiscale vertegenwoordiger moet verstrekt worden
invoiceheader_billtoparty_address_city_notunique = De stad van de gefactureerde moet uniek zijn
invoicesummary_invoicetotals_taxable_amount_illegalvalue = Het totale belastbare bedrag is niet juist
invoicedetail_paratax_type_empty = Het tarief of het bedrag van de parafiscale taks op de lijn moet verstrekt worden
invoicedetail_paratax_percent_illegalvalue = Het tarief van de parafiscale taks op de lijn is niet juist
invoicedetail_paratax_type_notunique = Het tarief of het verstrekt bedrag van de parafiscale taks op de lijn moet uniek zijn
invoiceheader_declarantsagentorrepresentative_address_postalcode_empty = De postcode van de fiscale vertegenwoordiger moet verstrekt worden
invoiceheader_invoicingperiod_invoicing_period_endDate_illegalvalue = Het einde van de referentieperiode is niet juist.
invoice_tax_taxsummary_tax_notunique = Het tarief van de taks werd dubbel doorgestuurd
invoiceheader_invoice_issue_date_notunique = De documentdatum moet uniek zijn
invoiceheader_declarantsagentorrepresentative_vat_ident_empty = Het intracommunautaire BTW nummer van de fiscale vertegenwoordiger moet verstrekt worden
invoiceheader_invoicingperiod_invoicing_period_startDate_notunique = Het begin van de referentieperiode moet uniek zijn
invoice_tax_global_tax_unmatched = Het tarief van de taks ontbreekt in de rest van het bericht
invoiceheader_sellerparty_address_country_notunique = Het land van de verkoper moet uniek zijn
invoiceheader_billtoparty_address_name_empty = De bedrijfsnaam van de gefactureerde moet verstrekt worden
invoiceheader_alloworcharge_vat_rate_empty = Het BTW tarief van de korting of kost op het briefhoofd moet verstrekt worden
invoiceheader_billtoparty_address_name_notunique = De gefactureerde kan slechts een bedrijfsnaam hebben
invoiceheader_headoffice_address_country_empty = Het land van de maatschappelijke zetel moet verstrekt worden
invoiceheader_invoicing_period_start_date_illegalvalue = Het begin van de referentieperiode is niet juist.
invoiceheader_billtoparty_gov_reference_empty = De RCS-RCM van de gefactureerde moet verstrekt worden
invoiceheader_headoffice_vat_ident_notunique = Het intracommunautaire BTW nummer van de maatschappelijke zetel moet uniek zijn
invoiceheader_sellerparty_address_city_empty = De stad van de verkoper moet verstrekt worden
invoiceheader_sellerparty_address_name_empty = De bedrijfsnaam van de verkoper moet verstrekt worden
invoiceheader_sellerparty_address_city_notunique = De stad van de verkoper moet uniek zijn
invoicedetail_paratax_amount_illegalvalue = Het bedrag van de parafiscale taks op de lijn is niet juist
legislation_eu = EUROPEES
invoiceheader_paymentterms_discount_term_empty = De disconteringvoorwaarden moeten verstrekt worden.
invoiceheader_declarantsagentorrepresentative_address_postalcode_notunique = De postcode van de fiscale vertegenwoordiger moet uniek zijn
invoiceheader_sellerparty_registration_ident_empty = De SIREN van de verkoper moet verstrekt worden
invoiceheader_declarantsagentorrepresentative_address_country_notunique = Het land van de fiscale vertegenwoordiger moet uniek zijn
invoiceheader_invoicingperiod_invoicing_period_startDate_illegalvalue = Het begin van de referentieperiode is niet juist.
invoiceheader_declarantsagentorrepresentative_address_street_empty = Het adres van de fiscale vertegenwoordiger moet verstrekt worden
invoiceheader_invoicing_period_end_date_illegalvalue = Het einde van de referentieperiode is niet juist.
invoicedetail_charge_amount_illegalvalue = Het bedrag van de kost op de lijn is niet juist
invoiceheader_billtoparty_vat_ident_notunique = Het intracommunautaire BTW nummer van de gefactureerde moet uniek zijn
invoicedetail_charge_amount_percent_illegalvalue = Het tarief of het bedrag van de kost op de lijn is niet juist
invoiceheader_paymentterms_payment_term_empty = De vaste boetevergoeding moet verstrekt worden
invoiceheader_sellerparty_address_street_empty = Het adres van de verkoper moet verstrekt worden
invoiceheader_paymentterms_penalty_term_empty = De boetevoorwaarden moeten verstrekt worden.
invoiceheader_headoffice_gov_reference_empty = De RCS-RCM van de maatschappelijke zetel moet verstrekt worden
invoiceheader_invoice_issue_date_illegalvalue = De documentdatum is niet juist
invoiceheader_declarantsagentorrepresentative_address_country_empty = Het land van de fiscale vertegenwoordiger moet verstrekt worden
invoiceheader_declarantsagentorrepresentative_vat_ident_notunique = Het intracommunautaire BTW nummer van de fiscale vertegenwoordiger moet uniek zijn
invoiceheader_headoffice_address_postalcode_notunique = De postcode van de maatschappelijke zetel moet uniek zijn
invoiceheader_invoice_reference_date_illegalvalue = De datum van het referentiedocument is niet juist.
invoiceheader_due_date_empty = De vervaldatum moet verstrekt worden
invoicedetail_allowance_percent_illegalvalue = Het tarief van de korting op de lijn is niet juist
legislation_fr = FRANS
invoiceheader_invoice_references_empty = De inlichtingen van het referentiedocument zijn onvolledig
invoicesummary_actualpayment_payment_date_illegalvalue = De datum van de betaling van het voorschot is niet juist
invoicesummary_taxsummary_vat_rate_empty = Het BTW overzicht moet een BTW tarief opgeven
invoiceheader_headoffice_address_name_empty = De bedrijfsnaam van de maatschappelijke zetel moet verstrekt worden
invoicedetail_quantity_value_illegalvalue = De hoeveelheid van de lijn is niet juist
invoiceheader_sellerparty_registration_ident_notunique = De SIREN van de verkoper moet uniek zijn
invoice_tax_line_allowanceorcharge_tax_unmatched = Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht
invoiceheader_alloworcharge_type_allowance_charge_empty = Het type korting of kost op het briefhoofd moet verstrekt worden
invoiceheader_invoice_type_notunique = Het documenttype moet uniek zijn
invoicedetail_invoicepricingdetail_vat_rate_empty = Buyer VAT code
invoiceheader_alloworcharge_type_allowance_charge_percent_illegalvalue = Het percentage van de korting of kost op het briefhoofd is niet juist
invoicesummary_invoicetotals_monetary_amount_notunique = Er mag slechts een totaal taksbedrag opgegeven worden
invoiceheader_declarantsagentorrepresentative_address_city_notunique = De stad van de fiscale vertegenwoordiger moet uniek zijn
invoiceheader_headoffice_address_street_notunique = Het adres van de maatschappelijke zetel moet uniek zijn
invoicedetail_invoicepricingdetail_gross_unit_price_empty = De bruto eenheidsprijs zonder taksen moet op elke lijn verstrekt worden
invoicedetail_paratax_amount_percent_illegalvalue = Het tarief of het bedrag van de parafiscale taks op de lijn is niet juist
invoicesummary_taxsummary_tax_amount_empty = Een taksbedrag moet verstrekt worden voor elk BTW tarief dat in het overzicht staat
invoiceheader_billtoparty_address_country_empty = Het land van de gefactureerde moet verstrekt worden
invoiceheader_declarantsagentorrepresentative_gov_reference_empty = De RCS-RCM van de fiscale vertegenwoordiger moet verstrekt worden
invoicesummary_taxsummary_tax_amount_illegalvalue = Het taksbedrag is niet juist
invoicedetail_base_item_detail_empty = De omschrijving van de goederen en diensten moet op elke lijn verstrekt worden
invoiceheader_invoice_number_empty = Het documentnummer moet verstrekt worden
invoiceheader_sellerparty_address_country_empty = Het land van de verkoper moet verstrekt worden
invoiceheader_headoffice_address_name_notunique = De maatschappelijke zetel kan slechts een bedrijfsnaam hebben
invoiceheader_sellerparty_address_street_notunique = Het adres van de verkoper moet uniek zijn
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_illegalvalue = Het bedrag van de korting of kost op het briefhoofd is niet juist
invoiceheader_billtoparty_address_postalcode_notunique = De postcode van de gefactureerde moet uniek zijn
invoiceheader_paymentterms_missing = De betalingsinfo werd niet verstrekt
invoicedetail_invoicepricingdetail_gross_unit_price_illegalvalue = De bruto eenheidsprijs zonder taksen is niet juist
invoiceheader_invoice_currency_notunique = De munteenheid moet uniek zijn
invoiceheader_alloworcharge_type_allowance_charge_percentagemonetaryvalue_illegalvalue = Het percentage of het bedrag van de korting of kost op het briefhoofd is niet juist
invoiceheader_billtoparty_missing = De gefactureerde moet verstrekt worden
invoicesummary_invoicetotals_missing = De globale totalen moeten verstrekt worden
invoicesummary_invoicetotals_total_amount_payable_illegalvalue = Het totale bedrag met alle taksen inbegrepen dat nog betaald moet worden, is niet juist
invoicesummary_taxsummary_taxable_amount_empty = Een belastbaar bedrag moet verstrekt worden voor elk BTW tarief dat in het overzicht staat
invoiceheader_billtoparty_address_street_empty = Het adres van de gefactureerde moet verstrekt worden
invoicesummary_invoicetotals_monetary_amount_empty = Het totale taksbedrag moet verstrekt worden
invoicesummary_invoicetotals_taxable_amount_empty = Het totale belastbare bedrag moet verstrekt worden
invoicesummary_taxsummary_taxable_amount_notunique = Er mag slechts een belastbaar bedrag voor elk BTW tarief dat in het overzicht staat verstrekt worden
invoicesummary_actualpayment_payment_date_empty = De datum van de betaling van het voorschot moet verstrekt worden
invoicesummary_taxsummary_missing = Het overzicht van de BTW moet verstrekt worden
invoiceheader_billtoparty_registration_ident_notunique = De SIREN van de gefactureerde moet uniek zijn
invoiceheader_declarantsagentorrepresentative_address_street_notunique = Het adres van de fiscale vertegenwoordiger moet uniek zijn
invoicedetail_allowance_amount_percent_illegalvalue = Het tarief of het bedrag van de korting op de lijn is niet juist
invoiceheader_date_preparation_date_time_document_empty = De datum/de tijd van de voorbereiding van het document moet verstrekt worden
invoiceheader_declarantsagentorrepresentative_gov_reference_notunique = De RCS-RCM van de fiscale vertegenwoordiger moet uniek zijn
invoicedetail_charge_type_empty = Het tarief of het bedrag van de kost op de lijn moet verstrekt worden
invoiceheader_invoice_number_notunique = Het documentnummer moet uniek zijn
invoiceheader_invoice_type_other_empty = Het detail van het documenttype moet verstrekt worden
invoiceheader_tax_accounting_currency_notunique = De munteenheid voor de BTW betaling moet uniek zijn
invoiceheader_headoffice_address_city_empty = De stad van de maatschappelijke zetel moet verstrekt worden
invoiceheader_invoicingperiod_reference_period_empty = De referentieperiode moet verstrekt worden
invoiceheader_billtoparty_address_street_notunique = Het adres van de gefactureerde moet uniek zijn
invoiceheader_invoice_type_empty = Het documenttype moet verstrekt worden
invoiceheader_billtoparty_vat_ident_empty = Het intracommunautaire BTW nummer van de gefactureerde moet verstrekt worden
invoiceheader_paymentterms_discount_due_date_illegalvalue = De datum van de betaling van het voorschot is niet juist.
invoiceheader_billtoparty_address_country_notunique = Het land van de gefactureerde moet uniek zijn
invoicedetail_invoicepricingdetail_missing = De eenheidsprijs moet op elke lijn verstrekt worden
invoiceheader_headoffice_address_street_empty = Het adres van de maatschappelijke zetel moet verstrekt worden
invoiceheader_despatch_date_illegalvalue = De verzendingsdatum is niet juist
invoiceheader_due_date_illegalvalue = De vervaldatum is niet juist
invoiceheader_sellerparty_gov_reference_notunique = De RCS-RCM van de verkoper moet uniek zijn
invoicedetail_allowance_type_empty = Het tarief of het bedrag van de korting op de lijn moet verstrekt worden
invoiceheader_headoffice_registration_ident_notunique = De SIREN van de maatschappelijke zetel moet uniek zijn
invoiceheader_alloworcharge_vat_rate_notunique = Er kan slechts een enkel BTW tarief van de korting of kost op het briefhoofd verstrekt worden
invoiceheader_sellerparty_vat_ident_notunique = Het intracommunautaire BTW nummer van de verkoper moet uniek zijn
invoiceheader_declarantsagentorrepresentative_registration_ident_notunique = De SIREN van de fiscale vertegenwoordiger moet uniek zijn
invoiceheader_sellerparty_address_postalcode_notunique = De postcode van de verkoper moet uniek zijn
invoiceheader_headoffice_address_country_notunique = Het land van de maatschappelijke zetel moet uniek zijn
invoicedetail_invoicepricingdetail_net_unit_price_empty = De netto eenheidsprijs zonder taksen moet op elke lijn verstrekt worden
invoicesummary_invoicetotals_total_amount_illegalvalue = Het totale bedrag met alle taksen inbegrepen is niet juist
invoicedetail_invoicepricingdetail_net_unit_price_notunique = De netto eenheidsprijs zonder taksen moet op elke lijn uniek zijn
invoiceheader_sellerparty_address_postalcode_empty = De postcode van de verkoper moet verstrekt worden
invoicedetail_invoicepricingdetail_net_unit_price_illegalvalue = De netto eenheidsprijs zonder taksen is niet juist
invoiceheader_invoice_issue_date_empty = De documentdatum moet verstrekt worden
invoiceheader_sellerparty_legal_structure_notunique = De juridische vorm van de verkoper mag slechts een keer opgegeven worden
invoicedetail_invoicepricingdetail_exempt_vat_rate_not_zero = = Het BTW tarief op de lijn moet gelijk zijn aan nul in het kader van een vrijstelling van taks.
invoiceheader_sellerparty_registered_name_empty = De bedrijfsnaam van de verkoper moet verstrekt worden
invoicedetail_paratax_desc_empty_or_notunique = De omschrijving van de parafiscale taks op de lijn moet verstrekt worden en een parafiscale taks op de lijn kan slechts een omschrijving hebben
invoiceheader_sellerparty_legal_capital_empty = Het aandelenkapitaal van de verkoper moet verstrekt worden
invoiceheader_sellerparty_legal_structure_empty = De juridische vorm van de verkoper moet verstrekt worden
invoicedetail_charge_desc_empty_or_notunique = De omschrijving van de kost op de lijn moet verstrekt worden en een kost op de lijn kan slechts een omschrijving hebben
invoicedetail_invoicepricingdetail_vat_empty = Het BTW tarief op de lijn moet verstrekt worden
invoicedetail_invoicepricingdetail_vat_notunique = Het BTW tarief op de lijn moet uniek zijn
invoicedetail_paratax_regulatory_vat_empty_or_notunique = Het BTW tarief dat bij een parafiscale taks op de lijn toegepast wordt, moet verstrekt worden en het BTW tarief dat bij een parafiscale taks op de lijn toegepast wordt, moet uniek zijn
invoicedetail_allowance_desc_empty_or_notunique = De omschrijving van de korting op de lijn moet verstrekt worden en een korting op de lijn kan slechts een omschrijving hebben
invoiceheader_invoice_currency_notequal = De munteenheid voor de betaling van de BTW werd niet verstrekt en de munteenheid is niet de euro.
invoiceheader_sellerparty_registered_name_notunique = De bedrijfsnaam van de verkoper mag slechts een keer opgegeven worden
invoiceheader_sellerparty_legal_capital_notunique = Het aandelenkapitaal van de verkoper mag slechts een keer opgegeven worden

invoice_tax_taxsummary_tax_notuniquevalue = Het tarief van de taks werd dubbel doorgestuurd
invoicesummary_listoftaxsummary_tax_exempt_code_empty = In geval van btw-vrijstelling (BT-118) zijn de reden van btw-vrijstelling (BT-120) en de bijbehorende code (BT-121) verplicht, regel G1.40.
invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_notunique = De factuurtypecode (EXT-FR-FE-137) van een voormalige factuur waarnaar op een regel wordt verwezen, moet uniek zijn, regel G1.01.
invoicesummary_invoicetotals_total_amount_too_large = Het gehele totaalbedrag (BT-112) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoiceheader_invoiceparty_address_country_coded_notequal = Landcode van fiscaal vertegenwoordiger (BT-69) moet gelijk zijn aan FR, regel G1.49
invoiceheader_invoice_type_bt_121_notequal = De factuurcode (BT-3) moet 261, 381 of 396 zijn omdat de btw-vrijstellingscode (BT-121) VATEX-FR-CNWVAT is, regel G6.21.
invoiceheader_party_tax_information_ident_not_in_list = De partijbelasting-identificator (EXT-FR-FE-27) komt niet voor in de ISO-3166 aplha-2 landencodelijst, regel G6.18
invoicesummary_invoicetotals_taxable_amount_sum_inconsistency = De totale maatstaf van heffing (BT-109) moet gelijk zijn aan de som van alle heffingsgrondslagen voor btw-type (BT-116), regel G1.54.
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_not_decimal = De globale vergoeding (BT-92) en de globale heffing (BT-99) moeten getallen zijn met maximaal 2 decimalen, regel G1.14.
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_empty = Belastingbedrag in fiscale boekhoudvaluta (BT-117) is verplicht, regel G1.14
invoicesummary_invoicetotals_taxable_amount_not_decimal = Belastbaar geldbedrag (BT-109) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoiceheader_sellerparty_siret_0223_too_large = Verkopers-ID (BT-29b) is maximaal 18 tekens voor in de EU gevestigde bedrijven, regel G1.08
invoiceheader_buyerparty_address_country_not_in_list = Het land van de koper (BT-55) moet een ISO3166-code zijn, regel G2.01
invoiceheader_list_of_attachment_file_name_empty = Bestandsnaam van bijgevoegd document (BT-125-2) kan niet leeg zijn, regel G4.16
invoicedetail_buyer_line_number_not_integer = Het regelnummer (BG-25) moet een numerieke reeks zijn, regel G6.01
invoiceheader_invoice_currency_fr_not_in_list = Een factuurvalutacode (BT-5) moet voorkomen in de lijst van toegestane codes, regel G1.10.
invoicesummary_invoicetotals_monetary_amount_not_decimal = Het totale btw-bedrag (BT-110) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoicesummary_invoicetotals_taxable_amount_notunique = Belastbaar geldbedrag (BT-109) moet uniek zijn, regel G1.14
invoiceheader_buyerparty_vat_ident_not_in_list = BTW-nummer koper (BT-48) moet behoren tot de lijst CountryCodeISO3166-alpha2, regel G1.47
invoiceheader_sellerparty_additional_party_ident_empty = Extra identificator (BT-29) kan niet leeg zijn, regel G1.47
invoicedetail_invoicepricingdetail_net_unit_price_negative = De nettoprijs van de post (BT-146) kan alleen positief of gelijk aan nul zijn, regel BR-27
invoiceheader_factor_address_country_code_not_in_list = Factor landcode (EXT-FR-FE-130) moet een ISO3166 code zijn, regel G2.01
invoicedetail_invoicepricingdetail_net_invoice_line_amount_empty = Het nettobedrag van de factuurregel (BT-131) is verplicht voor elke regel, regel BR-24
invoiceheader_invoiceparty_VAT_seller_identifier_not_in_list = Het BTW-nummer van de vertegenwoordiger van de verkoper (EXT-FR-FE-73) moet voorkomen in de ISO-3166 aplha-2-landencodelijst, regel G6.17.
invoicedetail_invoiced_quantity_empty = De code van de maateenheid van de gefactureerde hoeveelheid van een regel (BT-130) is verplicht voor elke regel, regel G6.09
invoicedetail_invoicepricingdetail_net_invoice_line_amount_too_large = Het nettobedrag van de factuurregel (BT-131) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicedetail_structured_note_text_type_coded_not_in_list = De onderwerpcode van de factuurnota (EXT-FR-FE-183) hoort thuis in de lijst UNTDID4451, regel G1.52.
invoiceheader_former_invoice_type_notunique = De factuurtypecode (EXT-FR-FE-02) van een voormalige factuur moet uniek zijn, regel G1.01.
legislation_en16931 = EN16931
invoiceheader_buyer_order_number_b2g_fr_empty_or_notunique = Het bestelnummer van de koper (BT-13) moet aanwezig en uniek zijn, regel G1.11.
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_too_large = Belastinggrondslag voor BTW-type (BT-116) kan niet meer dan 19 cijfers hebben, inclusief 2 decimalen, regel G1.14
invoicedetail_invoicepricingdetail_allow_or_charge_base_for_invoice_line_discount_too_large = Basis voor factuurregelkorting (BT-137) kan niet meer dan 19 cijfers hebben, inclusief 2 decimalen, regel G1.14
invoicesummary_invoicetotals_total_amount_payable_notunique = Te betalen bedrag (BT-115) moet uniek zijn, regel G1.14
invoicesummary_invoicetotals_allowance_total_monetary_amount_notunique = Het geldbedrag van de vergoeding (BT-107) moet uniek zijn, regel G1.14.
invoicedetail_charge_list_of_description_unmatched_pair = De reden voor de factuurregel (BT-144) moet overeenkomen met de code (BT-145) volgens de UNTDID-7161-lijst, regel BR-CO-8
invoicesummary_invoicetotals_monetary_amount_too_large = Het totale btw-bedrag (BT-110) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoiceheader_invoiceparty_address_country_coded_notunique = Landcode van fiscaal vertegenwoordiger (BT-69) moet uniek zijn, regel G1.49
invoiceheader_sellerparty_siret_0227_too_large = Verkopers-ID (BT-29b) is maximaal 18 tekens voor niet in de EU gevestigd bedrijf, regel G1.08
invoicesummary_invoicetotals_total_VAT_amount_not_decimal = Het totale btw-bedrag van de factuur uitgedrukt (munteenheid) (BT-111) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_too_large = De globale vergoeding (BT-92) en de globale heffing (BT-99) mogen niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_empty = Belastinggrondslag voor btw-type (BT-116) is verplicht, regel G1.14
invoiceheader_invoicing_period_end_date_notunique = Einddatum factureringsperiode (BT-74) mag maar \u00E9\u00E9n keer voorkomen, regel G1.36
invoicedetail_invoicingperiod_end_date_datenotafter = De einddatum van de factuurregelperiode (BT-135) moet een latere datum zijn dan (of dezelfde datum als) de begindatum van de factuurregelperiode (BT-134), regel BR-30.
invoiceheader_despatch_date_notunique = De verzenddatum (BT-72) mag maar \u00E9\u00E9n keer op de factuur voorkomen, regel G1.36.
invoiceheader_remit_to_party_siret_0228_too_large = Payee RIDET (BT-60) is maximaal 10 tekens, regel G1.08
invoicedetail_buyer_line_number_too_large = Het regelnummer (BG-25) mag niet langer zijn dan 6 tekens (1-999999), regel G6.01
invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_empty = De factuurtypecode (EXT-FR-FE-137) van een voormalige factuur waarnaar op een regel wordt verwezen, is verplicht, regel G1.01
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_sum_inconsistency = Het subtotaalbedrag van de factuur (BT-106) moet gelijk zijn aan de som van alle netto factuurregels (BT-131), regel G1.54.
invoiceheader_declarantsagentorrepresentative_vat_ident_not_in_list = Fiscaal vertegenwoordiger BTW-identificatienummer (BT-63) moet behoren tot de CountryCodeISO3166-alpha2-lijst, regel G1.47
invoicesummary_invoicetotals_charge_total_monetary_amount_not_decimal = Het in rekening gebrachte geldbedrag (BT-108) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoiceheader_contract_type_fr_notunique = De Franse contracttypecode (EXT-FR-FE-01) van een factuur moet uniek zijn, regel G1.03.
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_not_decimal = Belastingbedrag in fiscale boekhoudvaluta (BT-117) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoiceheader_fr_requirements_business_process_type_fr_in_list = De huidige waarde van de typecode bedrijfsproces (BT-23) is verboden, regel G1.60
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_not_decimal = Belastinggrondslag voor BTW-type (BT-116) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoicedetail_invoicepricingdetail_discount_on_item_price_too_large = Korting op artikelprijs (BT-147) kan niet meer dan 19 cijfers bevatten, inclusief 4 decimalen, regel G1.15
invoicesummary_invoicetotals_total_amount_notunique = Het hele totaalbedrag (BT-112) moet uniek zijn, regel G1.14.
invoicesummary_list_of_tax_summary_VAT_amount_not_decimal = BTW-bedrag voor elk type BTW (BT-117) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoicedetail_charge_service_coded_other_empty = Factuurregel reden code (BT-145) moet aanwezig zijn als factuurregel reden (BT-144) ontbreekt, regel G1.29
invoicedetail_ship_to_country_code_notunique = Slechts \u00E9\u00E9n land van verzending (EXT-FR-FE-157) is toegestaan in artikelgegevens, regel G6.11
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_too_large = Het monetaire subtotaalbedrag van de factuur (BT-106) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicesummary_listoftaxsummary_tax_exempt_reason_notequal = Wanneer de btw-vrijstellingscode (BT-121) VATEX-FR-FRANCHISE is, moet de vrijstellingsreden (BT-120) zijn: TVA non applicable en application de l'article 293B du CGI, rule G1.56
invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_not_decimal = De monetaire grenswaarde van een toeslag of een vergoeding (BT-93 en BT-100) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_notunique = De globale vergoeding (BT-92) en de globale heffing (BT-99) moeten uniek zijn, regel G1.14.
invoicesummary_invoicetotals_taxable_amount_too_large = Belastbaar geldbedrag (BT-109) kan niet meer dan 19 cijfers hebben, inclusief 2 decimalen, regel G1.14
invoiceheader_remit_to_party_siret_0223_too_large = ID begunstigde (BT-60) is maximaal 18 tekens voor in de EU gevestigd bedrijf, regel G1.08
invoiceheader_buyerparty_address_name_empty = De naam van de koper (BG-8) is verplicht, regel G6.08
invoicesummary_invoicetotals_payroll_amount_too_large = Het loonbedrag (BT-113) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicedetail_charge_service_coded_other_not_in_list = De ServiceCodedOther (BT-145) moet een waarde hebben uit de UNTDID 7161 lijst, regel G1.29
invoiceheader_alloworcharge_list_of_description_empty = ListOfDescription (BT-97) moet aanwezig zijn als SeriveCodedOther (BT-98) ontbreekt, regel G1.29
invoiceheader_buyerparty_vat_ident_notunique = Slechts \u00E9\u00E9n BTW-identificator (BT-48) is toegestaan in koper, regel G6.11
invoicesummary_invoicetotals_total_amount_payable_not_decimal = Te betalen bedrag (BT-115) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoicedetail_quantity_value_too_large = Hoeveelheidswaarde (BT-129) kan niet meer dan 19 cijfers bevatten, inclusief 4 decimalen, regel G1.15
invoiceheader_due_date_not_century_21 = De vervaldatum van de factuur (BT-9) moet in 21 eeuw zijn, regel G1.36.
invoicedetail_invoicepricingdetail_base_quantity_of_item_price_not_decimal = Basishoeveelheid van artikelprijs (BT-149) moet een getal zijn met maximaal 4 decimalen, regel G1.15
invoiceheader_buyerparty_gov_reference_empty = De administratieve verwijzing naar de koper (BT-47) is verplicht, regel G6.08
invoiceheader_buyer_reference_b2g_fr_not_french = De kopersreferentie (BT-10) mag geen speciale tekens bevatten, regel G2.29
invoiceheader_buyerparty_vat_ident_empty = De BTW-identificator van de afnemer (BT-48) moet aanwezig zijn, regel G6.11.
invoicesummary_list_of_tax_summary_tax_base_for_VAT_type_notunique = Belastinggrondslag voor btw-type (BT-116) moet uniek zijn, regel G1.14
invoicedetail_buyer_line_number_empty = Een factuur moet ten minste \u00E9\u00E9n factuurregel hebben (BG-25), regel G6.01 (BR-16)
invoicesummary_invoicetotals_total_amount_payable_notequal = Te betalen bedrag (BT-115) moet 0 zijn, regel G1.33
invoicesummary_taxsummary_vat_rate_not_in_decimal_list = Gedetailleerd btw-tarief (BT-119) moet een waarde hebben uit de lijst met btw-tariefpercentages, regel G1.24
invoicedetail_invoicepricingdetail_discount_on_item_price_notunique = Korting op artikelprijs (BT-147) moet uniek zijn voor elke artikelregel, regel G1.15
invoiceheader_invoiceparty_VAT_payer_identifier_not_in_list = BTW-nummer betaler (EXT-FR-FE-50) moet in ISO-3166 aplha-2 landencodelijst staan, regel G6.17
invoiceheader_list_of_structured_notes_coded_other_not_in_list = De onderwerpcode van de factuurnota (BT-21) moet behoren tot de lijst UNTDID4451, regel G1.52.
invoicedetail_charge_amount_not_decimal = Lijnkosten (BT-141) moeten een getal zijn met maximaal 2 decimalen, regel G1.14.
invoicesummary_invoicetotals_rounded_amount_notunique = Afgerond bedrag (BT-114) moet uniek zijn, regel G1.14
invoiceheader_alloworcharge_charge_service_coded_other_empty = SeriveCodedOther (BT-105) moet aanwezig zijn als ListOfDescription (BT-104) ontbreekt, regel G1.29
invoiceheader_fr_requirements_profile_type_empty_or_notunique = De specificatie-identificatie (BT-24) van een factuur is verplicht, regel BR-1
invoiceheader_sellerparty_vat_ident_not_in_list = BTW-identificatienummer van de verkoper (BT-31) moet beginnen met een ISO-3166-code, regel G1.47
invoicedetail_invoicepricingdetail_tax_tax_type_coded_other_empty = De factuurpost BTW-categoriecode van een regel (BT-151) is verplicht voor elke regel, regel BR-CO-4
invoiceheader_buyerparty_gov_reference_notunique = De administratieve referentie van de koper (BT-47) moet uniek zijn, regel G6.08
invoiceheader_former_invoice_reference_empty = Voorafgaande factuurreferentie (BG-3) bevat het voorafgaande factuurnummer (BT-25), regel BR-55
invoiceheader_invoice_currency_fr_empty_or_notunique = Een factuur mag maar \u00E9\u00E9n valutacode (BT-5) hebben, regel G1.10.
invoiceheader_invoice_type_other_empty_or_notunique = Een factuur heeft een factuurcode (BT-3), regels G6.08 en BR-4.
invoiceheader_invoicing_period_end_date_empty = Facturatieperiode moet een begindatum (BT-73) en een einddatumwaarde (BT-74) hebben, regel G6.11 en BR-CO-19.
invoicedetail_allowance_list_of_description_unmatched_pair = De reden voor de factuurregelvergoeding (BT-139) moet overeenkomen met de code (BT-140) volgens de UNTDID-5189-lijst, regel BR-CO-7.
invoiceheader_buyer_reference_b2g_fr_empty = De Franse administratieve entiteit van de ontvanger van deze factuur heeft de kopersreferentie (BT-10) nodig, regel G.11
invoiceheader_factor_address_country_code_empty = Factor landcode (EXT-FR-FE-130) mag niet leeg zijn, regel G2.01
invoicesummary_invoicetotals_total_amount_not_decimal = Het gehele totaalbedrag (BT-112) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoicedetail_charge_amount_empty = In geval van een factuurregelheffing is het bedrag (BT-141) verplicht, regel G1.14
invoiceheader_sellerparty_siret_qualifier_notequal = De kwalificator voor verkopers-ID (BT-29b-1) moet gelijk zijn aan code 0009, regel G1.71
invoicedetail_invoicepricingdetail_gross_unit_price_sum_inconsistency = De brutoeenheidsprijs van de post (BT-148) moet gelijk zijn aan de som van de nettoprijzen (BT-146) en de disconteringsheffingen (BT-147), regel G1.55.
invoiceheader_tax_point_date_notunique = De datum van het belastingpunt (BT-7) mag maar \u00E9\u00E9n keer op de factuur voorkomen, regel G1.36.
invoicedetail_invoicepricingdetail_tax_tax_type_coded_other_not_in_list = BTW-typecode van gefactureerd item (BT-151) moet in de gegeven lijst staan, regel G2.31
invoicedetail_invoicepricingdetail_gross_unit_price_too_large = De brutoeenheidsprijs van een artikel (BT-148) mag niet meer dan 19 cijfers bevatten, inclusief 4 decimalen, regel G1.15.
invoiceheader_buyer_reference_b2g_fr_notunique = De kopersreferentie (BT-10) moet uniek zijn, regel G1.11
invoiceheader_invoice_number_empty_or_notunique = Een factuur heeft een factuurnummer (BT-1), regel BR-2
invoiceheader_invoiceparty_address_country_coded_empty = Landcode van fiscaal vertegenwoordiger (BT-69) kan niet leeg zijn, regel G1.49
invoiceheader_invoiceparty_sellerparty_empty = Verkoper (BG-4) is verplicht, regel G6.08
invoiceheader_invoice_type_empty_or_notunique = Een factuur heeft een factuurcode (BT-3), regels G6.08 en BR-4.
invoiceheader_sellerparty_only_b2g_illegalvalue = De PPF ontvangt van dit type verkoper (BT-29b-1) alleen B2G-facturen, regel G1.13
invoicedetail_charge_list_of_description_empty = Factuurregel reden (BT-144) moet aanwezig zijn als factuurregel reden code (BT-145) ontbreekt, regel G1.29
invoicesummary_invoicetotals_rounded_amount_too_large = Afgerond bedrag (BT-114) kan niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14
invoicesummary_invoicetotals_allowance_total_monetary_amount_not_decimal = Het geldbedrag van de vergoeding (BT-107) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_empty = De globale vergoeding (BT-92) en de globale heffing (BT-99) kunnen geen lege velden zijn, regel G1.14.
invoicedetail_invoicepricingdetail_gross_unit_price_negative = De brutoprijs van een artikel (BT-148) kan alleen positief of gelijk aan nul zijn, regel BR-28
invoiceheader_buyerparty_address_country_empty = Het land van de koper (BT-55) is verplicht, regel G6.08
invoicedetail_article_name_empty = De itemnaam (BT-153) is verplicht voor elke regel, regel BR-25
invoicesummary_invoicetotals_payroll_amount_notunique = Salarisbedrag (BT-113) moet uniek zijn, regel G1.04
invoicedetail_invoiced_quantity_not_in_list = De gefactureerde hoeveelheid (BT-130) moet behoren tot de EN16931 codelijst, regel G6.09
invoicedetail_invoicingperiod_end_date_not_century_21 = Einddatum facturering artikelgegevens (BT-135) moet in 21 eeuw zijn, regel G1.36
invoiceheader_invoicing_period_end_date_not_century_21 = Einddatum factureringsperiode (BT-74) moet in 21 eeuw zijn, regel G1.36
invoicedetail_allowance_service_coded_other_not_in_list = De ServiceCodedOther (BT-140) moet een waarde hebben uit de UNTDID 5189-lijst, regel G1.29
invoice_tax_invoice_detail_invoice_line_empty = Factuurregel (BG-25) is verplicht, regel G6.09
invoicedetail_article_name_notunique = Artikelnaam (BT-153) moet uniek zijn, regel G6.09
invoiceheader_invoice_number_too_large = Het factuurnummer (BT-1) is te groot, regel G1.05
invoiceheader_tax_treatment_code_other_not_in_list = De fiscale behandelingscode (BT-8) is niet gelijk aan een van de volgende waarden: 3, 5, 29, 35, 72, of 432, regel S1.13
invoicedetail_invoicepricingdetail_net_unit_price_too_large = Item netto eenheidsprijs (BT-146) kan niet meer dan 19 cijfers hebben, inclusief 4 decimalen, regel G1.15
invoicesummary_listoftaxsummary_tax_exempt_reason_notunique = Slechts \u00E9\u00E9n btw-vrijstellingsreden (BT-120) is toegestaan in factuuroverzicht, regel G1.40
invoiceheader_invoice_number_not_ascii = Het factuurnummer (BT-1) bevat verboden tekens, regel G1.05
invoiceheader_billtoparty_address_country_code_not_in_list = De Bill to Party-landencode (EXT-FR-FE-107) moet een ISO3166-code zijn, regel G2.01.
invoicedetail_article_ident_notunique = Artikelidentificatie (BG-31) moet uniek zijn, regel G6.09
invoiceheader_list_of_attachment_file_name_not_character_accepted = Bestandsnaam van bijgevoegd document (BT-125-2) mag geen speciale tekens bevatten, regel G4.09
invoiceheader_invoice_issue_date_not_century_21 = De afgiftedatum (BT-2) moet in 21 eeuw zijn, regel G1.36
invoiceheader_contract_type_fr_notequal = De Franse contracttypecode (EXT-FR-FE-01) van een factuur moet deel uitmaken van de lijst met toegestane codes, regel G1.03.
invoiceheader_sellerparty_siret_0226_too_large = Verkopers-ID (BT-29b) is maximaal 80 tekens voor Franse burgers, regel G1.08
invoicesummary_listoftaxsummary_tax_exempt_reason_empty = In geval van btw-vrijstelling (BT-118) zijn de btw-vrijstellingsreden (BT-120) en de bijbehorende code (BT-121) verplicht, en regel G1.40
invoiceheader_list_of_attachment_file_name_not_unique_content = Bestandsnaam van bijgevoegd document (BT-125-2) moet unieke waarde hebben, regel G4.15
invoiceheader_payer_address_country_code_empty = Landcode betaler (EXT-FR-FE-61) mag niet leeg zijn, regel G2.01
invoicedetail_invoicepricingdetail_allow_or_charge_basis_of_fees_applicable_to_the_invoice_line_too_large = Basis van vergoedingen van toepassing op de factuurregel (BT-142) kan niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14
invoiceheader_invoice_issue_date_empty_or_notunique = De afgiftedatum (BT-2) moet worden vermeld, regels G6.08 en BR-3.
invoicesummary_invoicetotals_total_amount_payable_empty = Te betalen bedrag (BT-115) kan niet leeg zijn, regel G1.14
invoiceheader_sellers_agent_or_representative_address_country_code_not_in_list = De landcode van verkopersagent of vertegenwoordiger (EXT-FR-FE-84) moet een ISO3166-code zijn, regel G2.01
invoiceheader_invoiceparty_VAT_seller_identifier_notunique = BTW-identificatiecode van de agent van de verkoper (EXT-FR-FE-73) moet uniek zijn, regel G6.17
invoicedetail_invoicingperiod_start_date_notunique = Begindatum factuurpostgegevens (BT-134) mag maar \u00E9\u00E9n keer voorkomen, regel G1.36
invoiceheader_fr_requirements_business_process_type_fr_not_in_list = De bedrijfsprocescode (BT-23) van een factuur moet behoren tot de lijst van toegestane codes, regel G1.02.
invoiceheader_sellers_agent_or_representative_address_country_code_notunique = De landcode van de verkopersagent of vertegenwoordiger (EXT-FR-FE-84) moet uniek zijn, regel G2.01
invoiceheader_tax_treatment_code_other_notunique = De codesectie voor fiscale behandeling (BT-8) is niet uniek, regel S1.13
invoicesummary_invoicetotals_total_amount_payable_too_large = Het te betalen bedrag (BT-115) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicedetail_invoicingperiod_end_date_notunique = Einddatum facturering artikelgegevens (BT-135) mag maar \u00E9\u00E9n keer voorkomen, regel G1.36
invoiceheader_billtoparty_address_country_code_empty = De Bill to party landcode (EXT-FR-FE-107) mag niet leeg zijn, regel G2.01
invoiceheader_party_tax_information_ident_notunique = De partijbelasting-identificatiecode (EXT-FR-FE-27) moet uniek zijn, regel G6.18.
invoiceheader_buyerparty_address_name_notunique = De naam van de koper (BG-8) moet uniek zijn, regel G6.08
invoicedetail_allowance_amount_not_decimal = De lijntoeslag (BT-136) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoicesummary_listoftaxsummary_taxsummary_header_empty = Een factuur moet \u00E9\u00E9n btw-onderverdeling (BG-23) hebben met een btw-categoriecode (BT-118), regel BR-CO-18 en BR-47.
invoiceheader_invoicing_period_start_date_empty = Facturatieperiode moet een begindatum (BT-73) en een einddatumwaarde (BT-74) hebben, regel G6.11 en BR-CO-19.
invoicedetail_allowance_amount_notunique = Lijntoeslag (BT-136) moet uniek zijn, regel G1.14
invoicesummary_invoicetotals_allowance_total_monetary_amount_sum_inconsistency = Het geldbedrag van de vergoeding (BT-107) moet gelijk zijn aan de som van alle kortingsbedragen op documentniveau (BT-92), regel G1.54.
invoiceheader_invoice_type_bt_121_not_in_list = De factuurcode (BT-3) moet 261, 381 of 396 zijn omdat de btw-vrijstellingscode (BT-121) VATEX-FR-CNWVAT is, regel G6.21.
invoicesummary_list_of_tax_summary_VAT_amount_empty = BTW-bedrag voor elk type BTW (BT-117) kan niet leeg zijn, regel G1.14
invoicesummary_listoftaxsummary_taxsummary_notequal = Belastingoverzicht (BT-118) moet gelijk zijn aan E volgens de btw-vrijstellingsredencode (BT-121), regel G1.56
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_notunique = Belastingbedrag in fiscale boekhoudvaluta (BT-117) moet uniek zijn, regel G1.14
invoicedetail_invoicepricingdetail_gross_unit_price_not_decimal = De brutoeenheidsprijs van de post (BT-148) moet een getal zijn met maximaal 4 decimalen, regel G1.15.
invoiceheader_contract_type_fr_empty = De Franse contracttypecode (EXT-FR-FE-01) van een factuur is verplicht bij een contractreferentie (BT-12), regel G1.03
invoicedetail_ship_to_country_code_empty = Code schip naar land (EXT-FR-FE-157) moet aanwezig zijn, regel G6.11
invoiceheader_sellerparty_siret_qualifier_not_in_list = Aanduiding verkoper ID onbekend (BT-29b-1), regel G1.11
invoiceheader_alloworcharge_service_coded_other_not_in_list = De ServiceCodedOther (BT-98) moet een waarde hebben uit de UNTDID 5189 lijst, regel G1.29
invoicedetail_allowance_amount_too_large = Regelvergoeding (BT-136) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoiceheader_taxfunction_vat_type_empty = BTW-type (BT-95 en BT-102) mag niet leeg zijn, regel G6.12
invoiceheader_despatch_date_not_century_21 = De verzenddatum (BT-72) moet in 21 eeuw zijn, regel G1.36.
invoiceheader_alloworcharge_charge_list_of_description_empty = ListOfDescription (BT-104) moet aanwezig zijn als SeriveCodedOther (BT-105) ontbreekt, regel G1.29
invoiceheader_buyerparty_identifier_routing_code_not_french = Koper ID (Routing Code) (BT-46c) mag geen speciale tekens bevatten, regel G2.29
invoiceheader_sellerparty_siret_qualifier_empty = De kwalificator Verkoper ID (BT-29b-1) kan niet leeg zijn, regel G1.11
invoicesummary_invoicetotals_charge_total_monetary_amount_notunique = Het in rekening gebrachte geldbedrag (BT-108) moet uniek zijn, regel G1.14
invoiceheader_billtoparty_address_country_code_notunique = De Bill to Party-landencode (EXT-FR-FE-107) moet uniek zijn, regel G2.01.
invoicedetail_invoicepricingdetail_allow_or_charge_basis_of_fees_applicable_to_the_invoice_line_notunique = Basis van vergoedingen van toepassing op de factuurregel (BT-142) moet uniek zijn, regel G1.14
invoiceheader_remit_to_party_address_country_code_not_in_list = De landcode van de begunstigde (EXT-FR-FE-38) moet een ISO3166-code zijn, regel G2.01.
invoiceheader_buyer_order_number_b2g_fr_empty = De Franse aankopende administratieve entiteit van deze factuur heeft een bestelnummer (BT-13) nodig, regel G1.04.
invoicedetail_allowance_service_coded_other_empty = ServiceCodedOther (BT-140) moet aanwezig zijn als ListOfDescription (BT-139) ontbreekt, regel G1.29, BR-CO-23, BR-42
invoicedetail_invoicepricingdetail_base_quantity_of_item_price_too_large = Basishoeveelheid van artikelprijs (BT-149) kan niet meer dan 19 cijfers hebben, inclusief 4 decimalen, regel G1.15
invoiceheader_tax_point_date_not_century_21 = De datum van het belastingpunt (BT-7) moet in 21 eeuw zijn, regel G1.36
invoiceheader_sellerparty_agency_coded_other_not_in_list = De kwalificeerder ID verkoper (BT-29b-1) moet behoren tot de gegeven lijst, regel G1.71
invoicesummary_invoicetotals_total_VAT_amount_too_large = Het totale btw-bedrag van de factuur uitgedrukt (munteenheid) (BT-111) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14
invoiceheader_buyer_order_number_b2g_fr_too_large = Het bestelnummer van de koper (BT-13) is te groot, regel G3.04
invoiceheader_list_of_attachment_MIME_Type_notunique = MIME-code van het bijgevoegde document (BT-125-1) moet uniek zijn, regel G4.17
invoiceheader_alloworcharge_vat_rate_not_in_decimal_list = Het btw-tarief van een toeslag en een vergoeding in de kop (BT-96 en BT-103) moeten waarden hebben uit de lijst met btw-tariefpercentages, regel G1.24.
invoiceheader_former_invoice_type_not_in_list = De factuurtypecode (EXT-FR-FE-02) van een voormalige factuur moet deel uitmaken van de lijst met toegestane codes, regel G1.01.
invoicesummary_invoicetotals_monetary_amount_notequal = Het totale belastingbedrag (BT-110) moet gelijk zijn aan nul, regel G1.56.
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_empty = Subtotaal monetair bedrag factuur (BT-106) kan niet leeg zijn, regel G1.14
invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_notunique = De monetaire grenswaarde van een toeslag of een vergoeding (BT-93 en BT-100) moet uniek zijn, regel G1.14
invoiceheader_factor_address_country_code_notunique = Factor landcode (EXT-FR-FE-130) moet uniek zijn, regel G2.01
invoiceheader_taxfunction_vat_type_notunique = BTW-type (BT-95 en BT-102) moet uniek zijn, regel G2.31
invoiceheader_invoiceparty_agent_or_representative_code_empty = De SIREN-codes van de koper en zijn agent/vertegenwoordiger (EXT-FR-FE-94) mogen niet leeg zijn, regel G1.63
invoiceheader_invoiceparty_buyerparty_notunique = Koper (BG-7) moet uniek zijn, regel G6.08
invoicedetail_invoicepricingdetail_net_invoice_line_amount_not_decimal = Netto factuurregelbedrag (BT-131) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoicedetail_base_item_detail_country_origin_code_notunique = De landcode van artikelgegevens (BT-159) moet uniek zijn, regel G2.01
invoiceheader_taxfunction_vat_type_not_in_list = BTW-type (BT-95 en BT-102) moet behoren tot de gegeven lijst, regel G6.12
invoicesummary_invoicetotals_charge_total_monetary_amount_too_large = Het geldbedrag van de rekening (BT-108) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicedetail_structured_note_text_type_coded_notunique = Factuurnota onderwerpcode (EXT-FR-FE-183) moet uniek zijn, regel G1.52
invoiceheader_invoice_type_not_in_list = Een factuurtypecode (BT-3) moet voorkomen in de lijst van toegestane codes, regel G1.01.
invoiceheader_invoiceparty_seller_agent_or_representative_code_empty = De SIREN-codes van de verkoper en zijn agent/vertegenwoordiger (EXT-FR-FE-71) kunnen niet leeg zijn, regel G1.81
invoicedetail_invoicepricingdetail_base_quantity_of_item_price_notunique = Basishoeveelheid van artikelprijs (BT-149) moet uniek zijn, regel G1.15
invoiceheader_list_of_attachment_MIME_Type_not_in_list = MIME-code van het bijgevoegde document (BT-125-1) moet behoren tot de gegeven lijst, regel G4.17
invoiceheader_remit_to_party_siret_0229_too_large = TAHITI-code begunstigde (BT-60) is max. 9 tekens, regel G1.08
invoicesummary_listoftaxsummary_taxsummary_not_in_list = Belastingoverzicht (BT-118) moet behoren tot de gegeven lijst, regel G2.31
invoicesummary_invoicetotals_payroll_amount_not_decimal = Het loonbedrag (BT-113) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoicedetail_invoiced_quantity_notunique = Gefactureerde hoeveelheid (BT-130) moet uniek zijn, regel G6.09
invoiceheader_former_invoice_ref_date_not_century_21 = De referentiedatum van de vorige factuur (BT-26) moet in 21 eeuw zijn, regel G1.36.
invoicedetail_invoicepricingdetail_allow_or_charge_base_for_invoice_line_discount_not_decimal = Basis voor factuurregelkorting (BT-137) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoiceheader_invoice_type_notequal = De factuurtypecode (BT-3) mag niet 386 zijn omdat het bedrijfsprocestype (BT-23) B7 of S7 is, regel G1.60.
invoiceheader_invoiceparty_seller_agent_or_representative_code_notunique = De SIREN-codes van de verkoper en zijn agent/vertegenwoordiger (EXT-FR-FE-71) moeten uniek zijn, regel G1.81
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_notunique = Het subtotaalbedrag van de factuur (BT-106) moet uniek zijn, regel G1.14.
invoicedetail_invoicepricingdetail_allow_or_charge_basis_of_fees_applicable_to_the_invoice_line_not_decimal = Basis van vergoedingen van toepassing op de factuurregel (BT-142) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_not_in_list = De factuurtypecode (EXT-FR-FE-137) van een voormalige factuur waarnaar op een regel wordt verwezen, moet voorkomen in de lijst met toegestane codes, regel G1.01.
invoiceheader_due_date_notunique = De vervaldatum van de factuur (BT-9) mag maar \u00E9\u00E9n keer op de factuur voorkomen, regel G1.36.
invoiceheader_alloworcharge_service_coded_other_empty = SeriveCodedOther (BT-98) moet aanwezig zijn als ListOfDescription (BT-97) ontbreekt, regel G1.29
invoiceheader_remit_to_party_address_country_code_notunique = De landcode van de begunstigde (EXT-FR-FE-38) moet uniek zijn, regel G2.01.
invoicedetail_invoicingperiod_start_date_not_century_21 = Begindatum factuurpostgegevens (BT-134) moet in 21 eeuw zijn, regel G1.36
invoicedetail_base_item_detail_country_origin_code_not_in_list = De landcode van de artikelgegevens (BT-159) moet een ISO3166-code zijn, regel G2.01
invoicedetail_article_ident_empty = Artikelidentificatiecode (BG-31) is verplicht, regel G6.09
invoiceheader_shiptoparty_address_country_notunique = De Ship to country code (BT-80) moet uniek zijn, regel G6.08
invoicesummary_invoicetotals_invoice_subtotal_monetary_amount_not_decimal = Het monetaire subtotaalbedrag van de factuur (BT-106) moet een getal zijn met maximaal 2 decimalen, regel G1.14.
invoicedetail_invoicingperiod_notunique = E\u00E9n factureringsperiode (BG-26) is toegestaan voor een regel, regel G6.11
invoiceheader_list_of_attachment_file_name_notunique = Bestandsnaam van bijgevoegd document (BT-125-2) moet uniek zijn, regel G4.16
invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_too_large = De monetaire grenswaarde van een toeslag of vergoeding (BT-93 en BT-100) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoiceheader_sellerparty_siret_0228_too_large = Verkoper RIDET (BT-29b) is maximaal 10 tekens, regel G1.08
invoicesummary_invoicetotals_total_amount_not_same_content = Het gehele totaalbedrag (BT-112) moet dezelfde waarde hebben als BT-116, regel G1.56.
invoiceheader_sellerparty_address_country_not_in_list = De landcode van de verkoper moet een ISO3166-code zijn, regel G2.01
invoiceheader_former_invoice_number_fr_empty = Vorig factuurnummer (BT-25) is verplicht als het factuurtype (BT-3) CorrectedInvoice of CreditNoteGoodsAndServices is, regel G1.31.
invoiceheader_invoiceparty_agent_or_representative_code_notunique = De SIREN-codes van de koper en zijn agent/vertegenwoordiger (EXT-FR-FE-94) moeten uniek zijn, regel G1.63
invoiceheader_invoicing_period_start_date_not_century_21 = Begindatum factureringsperiode (BT-73) moet in 21 eeuw zijn, regel G1.36
invoicesummary_invoicetotals_notunique = Factuurtotalen (BG-22) moeten een uniek veld zijn, regel G6.08
invoicedetail_invoicepricingdetail_discount_on_item_price_not_decimal = Korting op artikelprijs (BT-147) moet een getal zijn met maximaal 4 decimalen, regel G1.15
invoicedetail_invoicepricingdetail_tax_tax_type_coded_other_notunique = BTW-typecode van gefactureerd item (BT-151) moet uniek zijn, regel G2.31
invoiceheader_agent_or_representative_address_country_code_not_in_list = Landcode agent of vertegenwoordiger (EXT-FR-FE-21) moet een ISO3166-code zijn, regel G2.01
invoiceheader_payer_address_country_code_notunique = De landcode van de betaler (EXT-FR-FE-61) moet uniek zijn, regel G2.01.
invoicesummary_invoicetotals_total_VAT_amount_notunique = Het totale btw-bedrag van de factuur uitgedrukt (boekhoudkundige valuta) (BT-111) moet uniek zijn, regel G1.14
invoiceheader_former_invoice_number_fr_not_ascii = Het voorgaande factuurnummer (BT-25) bevat verboden tekens, regel G1.05
invoiceheader_former_invoice_number_fr_too_large = Het voorgaande factuurnummer (BT-25) is te groot, regel G1.05
invoicedetail_delivery_ref_date_not_century_21 = Referentiedatum levering (EXT-FR-FE-158) moet in 21 eeuw zijn, regel G1.36
invoiceheader_buyer_order_number_b2g_fr_notunique = Het bestelnummer van de koper (BT-13) moet uniek zijn, regel G1.11
invoicedetail_quantity_value_empty = De gefactureerde hoeveelheid (BT-129) is verplicht voor elke regel, regel BR-22
invoiceheader_invoice_issue_date_not_past_date = De afgiftedatum (BT-2) moet in het verleden worden ingesteld, regel G1.07.
invoiceheader_buyerparty_address_country_notunique = Het land van de koper (BT-55) moet uniek zijn, regel G6.08
invoiceheader_invoiceparty_VAT_payer_identifier_notunique = BTW-identificator betaler (EXT-FR-FE-50) moet uniek zijn, regel G6.17
invoiceheader_shiptoparty_address_country_not_in_list = De Ship to country code (BT-80) moet een ISO3166-code zijn, regel G2.01
invoiceheader_invoicing_period_start_date_notunique = De begindatum van de factureringsperiode (BT-73) mag maar \u00E9\u00E9n keer voorkomen, regel G1.36.
invoiceheader_sellerparty_siret_fr_too_large = Verkoper SIRET (BT-29b) is maximaal 14 tekens, regel G1.08
invoicedetail_invoicepricingdetail_allow_or_charge_base_for_invoice_line_discount_notunique = Basis voor factuurregelkorting (BT-137) moet uniek zijn, regel G1.14
invoiceheader_billtoparty_siret_ident_not_french = Siret Ident (EXT-FR-FE-92c) mag geen speciale tekens bevatten, regel G2.29
invoiceheader_remit_to_party_siret_0226_too_large = ID begunstigde (BT-60) is maximaal 80 tekens voor Franse burger, regel G1.08
invoicesummary_listoftaxsummary_tax_exempt_code_not_in_list = BTW-vrijstellingscode (BT-121) is beperkt tot de CEF VATEX-lijst
invoicedetail_invoicingperiod_start_date_empty = Begindatum artikelgegevens facturering (BT-134) moet aanwezig zijn, regel G6.11
invoicesummary_invoicetotals_charge_total_monetary_amount_sum_inconsistency = Het geldbedrag van de kosten (BT-108) moet gelijk zijn aan de som van alle kostenbedragen (BT-99), regel G1.54.
invoicedetail_invoicepricingdetail_net_unit_price_not_decimal = Item netto eenheidsprijs (BT-146) moet een getal zijn met maximaal 4 decimalen, regel G1.15
invoicesummary_invoicetotals_rounded_amount_not_decimal = Afgerond bedrag (BT-114) moet een getal zijn met maximaal 2 decimalen, regel G1.14
invoiceheader_payer_address_country_code_not_in_list = De landcode van de betaler (EXT-FR-FE-61) moet een ISO3166-code zijn, regel G2.01.
invoiceheader_fr_requirements_profile_type_not_in_list = De specificatie-identificatie (BT-24) van een factuur moet behoren tot de lijst van standaardopties van EN16931, regel S1.06.
invoicedetail_invoicingperiod_end_date_empty = Einddatum artikelgegevens facturering (BT-135) moet aanwezig zijn, regel G6.11
invoiceheader_sellerparty_siret_qualifier_not_french = De kwalificator voor verkopers-ID (BT-29c) mag geen speciale tekens bevatten, regel G2.29
invoiceheader_invoice_type_other_not_in_list = Het factuurtype (BT-3) is niet gelijk aan SelfBilledCreditNote of FactoredCreditNote, regel G6.21.
invoicedetail_delivery_ref_date_notunique = Referentiedatum levering (EXT-FR-FE-158) mag maar \u00E9\u00E9n keer voorkomen op factuur, regel G1.36
invoiceheader_remit_to_party_siret_fr_too_large = Payee SIRET (BT-60) is maximaal 14 tekens, regel G1.08
invoiceheader_sellerparty_siret_0229_too_large = Verkoper TAHITI code (BT-29b) is maximaal 9 tekens, regel G1.08
invoicesummary_list_of_tax_summary_VAT_amount_notunique = BTW-bedrag voor elk type BTW (BT-117) moet uniek zijn, regel G1.14
invoicedetail_buyer_line_number_not_unique_content = Het factuurkoperregelnummer (BT-126) moet een unieke waarde hebben, regel G1.62.
invoiceheader_remit_to_party_siret_0227_too_large = ID begunstigde (BT-60) is maximaal 18 tekens voor niet in de EU gevestigd bedrijf, regel G1.08
invoicedetail_charge_amount_too_large = Lijnkosten (BT-141) mogen niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoiceheader_tax_currency_fr_not_in_list = De BTW-valutacode van de factuur (BT-6) moet behoren tot de ISO-4217 codelijst, regel G1.10.
invoiceheader_buyerparty_siret_ident_empty = De identificatiecode Buyer SIRET (BT-46b) is verplicht, regel G1.72.
invoiceheader_alloworcharge_charge_service_coded_other_not_in_list = De ServiceCodedOther (BT-105) moet een waarde hebben uit de UNTDID 7161 lijst, regel G1.29
invoiceheader_former_invoice_former_invoice_type_not_in_list = De factuurtypecode (EXT-FR-FE-02) van een voormalige factuur moet deel uitmaken van de lijst met toegestane codes, regel G1.01.
invoiceheader_list_of_attachment_MIME_Type_empty = MIME-code van het bijgevoegde document (BT-125-1) kan niet leeg zijn, regel G4.17
invoicesummary_invoicetotals_empty = Factuurtotalen (BG-22) is verplicht, regel G6.08
invoicesummary_listoftaxsummary_tax_exempt_code_notunique = Slechts \u00E9\u00E9n btw-vrijstellingscode (BT-121) is toegestaan in factuuroverzicht, regel G1.40
invoicedetail_invoicepricingdetail_net_invoice_line_amount_notunique = Netto factuurregelbedrag (BT-131) moet uniek zijn, regel G1.17
invoiceheader_fr_requirements_business_process_type_fr_empty_or_notunique = De bedrijfsprocescode (BT-23) van een factuur is verplicht, regel G6.08
invoicedetail_quantity_value_not_decimal = Hoeveelheidswaarde (BT-129) moet een getal zijn met maximaal 4 decimalen, regel G1.15
invoiceheader_former_invoice_type_empty = De factuurtypecode (EXT-FR-FE-02) van een voormalige factuur is verplicht, regel G1.01.
invoiceheader_shiptoparty_address_country_empty = Het schip naar land (BT-80) is verplicht, regel G6.08
invoiceheader_contract_reference_too_large = De contractreferentie (BT-12) is maximaal 50 tekens, regel G3.02
invoiceheader_agent_or_representative_address_country_code_notunique = Landcode agent of vertegenwoordiger (EXT-FR-FE-21) moet uniek zijn, regel G2.01
invoicesummary_invoicetotals_total_VAT_amount_notequal = Het totale btw-bedrag van de factuur uitgedrukt (boekhoudkundige valuta) (BT-111) moet gelijk zijn aan nul, regel G1.56
invoicesummary_list_of_tax_summary_VAT_amount_too_large = BTW-bedrag voor elk type BTW (BT-117) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14
invoicedetail_invoicepricingdetail_vat_rate_notunique = BTW-tarief voor een artikelregel (BT-152) moet uniek zijn, regel G1.24
invoiceheader_remit_to_party_address_country_code_empty = Landcode begunstigde (EXT-FR-FE-38) mag niet leeg zijn, regel G2.01
invoiceheader_requirement_country_notequal = De specificatie-identificator (BT-24) van een factuur is verplicht, regel BR-1
invoicedetail_buyer_line_number_notunique = Het factuurboekingsnummer (BT-126) moet uniek zijn, regel G1.62.
invoicedetail_allowance_amount_empty = In het geval van een factuurregelvergoeding is het bedrag (BT-136) verplicht, regel G1.14.
invoiceheader_former_invoice_number_fr_empty_or_notunique = Een voorafgaande factuur moet een nummer vermelden (BT-25), regel BR-55
invoiceheader_sellerparty_siret_empty = Verkopers-ID (BT-29b) is verplicht, regel G1.11
invoiceheader_tax_treatment_code_other_empty = De codesectie voor fiscale behandeling (BT-8) moet aanwezig zijn, regel G6.11.
invoiceheader_requirement_country_empty = De specificatie-identificator (BT-24) van een factuur is verplicht, regel BR-1
invoicedetail_ship_to_country_code_not_in_list = Code schip naar land (EXT-FR-FE-157) moet een ISO3166-code zijn, regel G2.01
invoicedetail_item_atribute_name_value_empty = Voor een itemattribuut zijn zowel de naam (BT-160) als de waarde (BT-161) verplicht, regel BR-54
invoiceheader_former_invoice_former_invoice_type_empty = De factuurtypecode (EXT-FR-FE-02) van een voormalige factuur is verplicht, regel G1.01.
invoicedetail_allowance_list_of_description_empty = ListOfDescription (BT-139) moet aanwezig zijn als ServiceCodedOther (BT-140) ontbreekt, regel G1.29, BR-CO-23, BR-42
invoiceheader_list_of_structured_notes_coded_other_empty = Voor een factuurnota zijn zowel de onderwerpcode (BT-21) als de nota zelf (BT-22) verplicht, regel G6.11
invoicesummary_invoicetotals_allowance_total_monetary_amount_too_large = Het geldbedrag van de vergoeding (BT-107) mag niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14.
invoicesummary_taxsummary_vat_rate_notunique = Gedetailleerd btw-tarief (BT-119) moet uniek zijn, regel G1.24
invoiceheader_list_of_attachment_file_name_not_in_list = Bestandsnaam van bijgevoegd document (BT-125-2) moet behoren tot de FileExtensions-lijst, regel G4.15
invoiceheader_invoiceparty_sellerparty_notunique = Verkoper (BG-4) moet uniek zijn, regel G6.08
invoicedetail_charge_amount_notunique = Lijnkosten (BT-141) moeten uniek zijn, regel G1.14
invoiceheader_invoiceparty_address_country_coded_not_in_list = Landcode van fiscaal vertegenwoordiger (BT-69) moet een ISO 3166-code zijn, regel G2.01
invoiceheader_fr_requirements_business_process_type_fr_illegalvalue = Als de bedrijfsprocescode (BT-23) S4 is, moet de ontvanger van de factuur een publieke entiteit zijn, regel G1.81.
invoiceheader_invoiceparty_buyerparty_empty = Koper (BG-7) is verplicht, regel G6.08
invoicesummary_list_of_tax_summary_tax_amount_in_tax_accounting_currency_too_large = Belastingbedrag in fiscale boekhoudvaluta (BT-117) kan niet meer dan 19 cijfers bevatten, inclusief 2 decimalen, regel G1.14
invoiceheader_buyer_reference_b2g_fr_empty_or_notunique = De Franse administratieve entiteit van de ontvanger van deze factuur heeft \u00E9\u00E9n kopersreferentie (BT-10) of \u00E9\u00E9n bestelnummer (BT-13) nodig, regel G1.04.
invoiceheader_former_invoice_ref_date_notunique = De referentiedatum van de vorige factuur (BT-26) mag slechts \u00E9\u00E9n keer voorkomen, regel G1.36
invoicedetail_invoicepricingdetail_vat_rate_not_in_decimal_list = BTW-tarief voor een artikelregel (BT-152) moet waarden hebben uit BTW-tarief-percentagelijst, regel G1.24
invoiceheader_invoice_currency_empty = Factuurvaluta (BT-5) is verplicht, regel G6.08
invoice_header_parties_Buyer_INSEE_SIREN_missing = {0} (BT-47) komt niet overeen met een bestaande Koper SIREN in INSEE database, regel G1.63
invoice_header_parties_SellersAgentOrRepresentative_INSEE_SIRET_missing = {0} (EXT-FR-FE-69) komt niet overeen met een bestaande verkoper agent SIRET in INSEE database, regel G1.74
invoice_header_parties_BuyersAgentOrRepresentative_INSEE_SIRET_missing = {0} (EXT-FR-FE-06) komt niet overeen met een bestaande Koper agent SIRET in INSEE database, regel G1.74
invoice_header_parties_Factor_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (EXT-FR-FE-115) komt niet overeen met SIREN {1} (EXT-FR-FE-117) voor Factor, regel G1.80
invoice_header_parties_SellersAgentOrRepresentative_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (EXT-FR-FE-69) komt niet overeen met SIREN {1} (EXT-FR-FE-71) voor verkopende agent, regel G1.80
invoice_header_parties_RemitTo_INSEE_SIREN_missing = {0} (BT-61) komt niet overeen met een bestaande Remit to SIREN in de INSEE-database, regel G1.75
invoice_header_parties_SellersAgentOrRepresentative_INSEE_SIREN_missing = {0} (EXT-FR-FE-71) komt niet overeen met een bestaande SIREN van een verkoper in de INSEE-database, regel G1.75
invoice_header_parties_BuyersAgentOrRepresentative_INSEE_SIREN_missing = {0} (EXT-FR-FE-08) komt niet overeen met een bestaande SIREN voor Koperagent in de INSEE-database, regel G1.63
invoice_header_parties_BillTo_INSEE_SIREN_missing = {0} (EXT-FR-FE-94) komt niet overeen met een bestaande factuur naar SIREN in de INSEE-database, regel G1.75
invoice_header_parties_BillTo_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (EXT-FR-FE-92b) komt niet overeen met SIREN {1} (EXT-FR-FE-94) voor Rekening aan, regel G1.80
invoice_header_parties_RemitTo_INSEE_SIRET_missing = {0} (BT-60) komt niet overeen met een bestaande Remit naar SIRET in INSEE-database, regel G1.74
invoice_header_parties_BillTo_INSEE_SIRET_missing = {0} (EXT-FR-FE-92b) komt niet overeen met een bestaande rekening naar SIRET in de INSEE-database, regel G1.74
invoice_header_parties_Payer_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (EXT-FR-FE-46) komt niet overeen met SIREN {1} (EXT-FR-FE-48) voor de betaler, regel G1.80
invoice_header_parties_RemitTo_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (BT-60) komt niet overeen met SIREN {1} (BT-61) voor Remit to, regel G1.80
invoice_header_parties_Seller_INSEE_SIREN_missing = {0} (BT-30) komt niet overeen met een bestaande SIREN van de verkoper in de INSEE-database, regel G1.61
invoice_header_parties_Factor_INSEE_SIREN_missing = {0} (EXT-FR-FE-117) komt niet overeen met een bestaande Factor SIREN in de INSEE-database, regel G1.75
invoice_header_parties_Seller_INSEE_SIRET_missing = {0} (BT-29b) komt niet overeen met een bestaande SIRET van de verkoper in de INSEE-database, regel G1.74
invoice_header_parties_BuyersAgentOrRepresentative_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (EXT-FR-FE-06) komt niet overeen met SIREN {1} (EXT-FR-FE-08) voor kopersagent, regel G1.80
invoice_header_parties_Factor_INSEE_SIRET_missing = {0} (EXT-FR-FE-115) komt niet overeen met een bestaande factor SIRET in de INSEE-database, regel G1.74
invoice_header_parties_Payer_INSEE_SIRET_missing = {0} (EXT-FR-FE-46) komt niet overeen met een bestaande SIRET van de betaler in de INSEE-database, regel G1.74
invoice_header_parties_Buyer_INSEE_SIRET_missing = {0} (BT-46b) komt niet overeen met een bestaande Koper SIRET in INSEE database, regel G1.74
invoice_header_parties_Payer_INSEE_SIREN_missing = {0} (EXT-FR-FE-48) komt niet overeen met een bestaande SIREN van de betaler in de INSEE-database, regel G1.75
invoice_header_parties_Buyer_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (BT-46b) komt niet overeen met SIREN {1} (BT-47) voor Koper, regel G1.80
invoice_header_parties_Seller_SIRET_SIREN_unmatched = De eerste 9 cijfers van SIRET {0} (BT-29b) komen niet overeen met SIREN {1} (BT-30) voor Verkoper, regel G1.80