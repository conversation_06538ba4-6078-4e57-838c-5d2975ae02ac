<?xml version="1.0" encoding="UTF-8"?>
<rules xmlns="http://www.example.org/constraints"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.example.org/constraints constraints.xsd"
    context="//InvoiceHeader" label="invoiceheader">

    <field path="InvoiceType/InvoiceTypeCoded" description="Type du document" label="invoice_type">
        <!-- BT-3 -->
        <constraints>
            <constraint
                legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint
                legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CommercialInvoice" /> <!-- CII code: 380 -->
                <arg value="CreditNoteGoodsAndServices" /> <!-- CII code: 381 -->
                <arg value="CorrectedInvoice" /> <!-- CII code: 384 -->
                <arg value="PrepaymentInvoice" /> <!-- CII code: 386 -->
                <arg value="SelfBilledInvoice" /> <!-- CII code: 389 -->
                <arg value="FactoredInvoice" /> <!-- CII code: 393 -->
                <arg value="Other" />
            </constraint>
            <!-- rule G1.60  
                Note that the other part of the rule is implemented within BT-23
            -->
            <constraint class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint"
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                <arg value="CommercialInvoice" /> <!-- CII code: 380 -->
                <!--BT-23-->
                <precondition expression="//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType']/RequirementReference[RefNum='B4' or RefNum='S4' or RefNum='M4']" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceType/InvoiceTypeCoded" description="Type du document" label="invoice_type_bt_121">
        <!-- BT-3 -->
        <constraints>
            <!-- rule G6.21 -->
            <constraint class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint"
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                <arg value="CreditNoteGoodsAndServices" /> <!-- CII code: 381 -->
                <precondition expression="not(InvoiceType[InvoiceTypeCoded='Other'])" />
                <!-- BT-121 -->
                <precondition
                    expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-FR-CNWVAT']" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceType/InvoiceTypeCodedOther" description="Type du document" label="invoice_type_bt_121">
        <!-- BT-3 -->
        <constraints>
            <!-- rule G6.21 -->
            <constraint class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint"
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                <arg value="SelfBilledCreditNote" /> <!-- CII code: 261 -->
                <arg value="FactoredCreditNote" /> <!-- CII code: 396 -->
                <precondition expression="InvoiceType[InvoiceTypeCoded='Other']" />
                <!--BT-121-->
                <precondition
                    expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-FR-CNWVAT']" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceType/InvoiceTypeCodedOther" description="Type du document" label="invoice_type_other">
        <!-- element BT-3 -->
        <constraints>
            <constraint
                legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="InvoiceType[InvoiceTypeCoded='Other']" />
            </constraint>
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint">
                <precondition expression="InvoiceType[InvoiceTypeCoded='Other']" />
            </constraint>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="SelfBilledCreditNote" /> <!-- CII code: 261 -->
                <arg value="FactoredCreditNote" /> <!-- CII code: 396 -->
                <arg value="SelfBilledPrepaymentInvoice" /> <!-- CII code: 500 -->
                <arg value="SelfBilledFactoredInvoice" /> <!-- CII code: 501 -->
                <arg value="SelfBilledFactoredCreditNote" /> <!-- CII code: 502 -->
                <arg value="PrepaymentCreditNote" /> <!-- CII code: 503 -->
                <arg value="SelfBilledCorrectedInvoice" /> <!-- CII code: to be defined (facture rectificative auto-facturée)-->
                <arg value="FactoredCorrectedInvoice" /> <!-- CII code: to be defined (facture rectificative affacturée)-->
                <arg value="SelfBilledFactoredCorrectedInvoice" /> <!-- CII code: to be defined (facture rectificative auto-facturée affacturée)-->
                <precondition expression="InvoiceType[InvoiceTypeCoded='Other']" />
            </constraint>
        </constraints>
    </field>

    <field path="TaxReference/TaxTreatmentCodedOther"
        description="Validation of tax treatment coded other"
        label="tax_treatment_code_other">
        <!--BT-8-->
        <!--rule BR-CO-3-->
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="TaxReference[TaxTreatmentCoded='Other']" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="TaxReference[TaxTreatmentCoded='Other']" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="3" />
                <arg value="5" />
                <arg value="29" />
                <arg value="35" />
                <arg value="72" />
                <arg value="432" />
                <precondition expression="TaxReference[TaxTreatmentCoded='Other']" />
            </constraint>
        </constraints>
    </field>

    <section label="former_invoice"
        step="InvoiceReferences/ListOfRelatedInvoiceRef"
        required="false">

        <field path="RelatedInvoiceRef" description="Référence à une facture antérieure"
            label="reference">
            <!-- Rule EN: BR-55 -->
            <!-- element BG-3 -->
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.AllDescendantsNotEmptyConstraint">
                    <arg value="InvoiceNumber/RefNum" />
                </constraint>
            </constraints>
        </field>

        <field path="RelatedInvoiceRef/InvoiceNumber/RefNum"
            description="Numéro de facture antérieure" label="number_fr">
            <!-- Rule FR: G1.05 -->
            <!-- element BT-25 -->
            <!--Rule BR-55-->
            <constraints>
                <!-- G1.31 -->
                <constraint
                    legislation="EN"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceType/InvoiceTypeCoded='CorrectedInvoice' or //InvoiceHeader/InvoiceType/InvoiceTypeCoded='CreditNoteGoodsAndServices'" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceType/InvoiceTypeCoded='CorrectedInvoice' or //InvoiceHeader/InvoiceType/InvoiceTypeCoded='CreditNoteGoodsAndServices'" />
                    <!--G6.11-->
                    <precondition
                        expression="//InvoiceHeader/InvoiceReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.AsciiConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                    <arg value="20" />
                </constraint>
            </constraints>
        </field>

        <field path="RelatedInvoiceRef/InvoiceNumber/RefDate" description="Date of a previous invoice"
            label="ref_date">
            <!--BT-26-->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceHeader/InvoiceReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/InvoiceNumber" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                    <precondition expression="//InvoiceHeader/InvoiceReferences/ListOfRelatedInvoiceRef/RelatedInvoiceRef/InvoiceNumber" />
                </constraint>
            </constraints>
        </field>
        <field
            path="RelatedInvoiceRef/RelatedInvoiceType/InvoiceTypeCoded"
            description="Type de facture antérieure" label="type">
            <!-- Rule FR: G1.01 -->
            <!-- element EXT-FR-FE-02 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                    <arg value="CommercialInvoice" /> <!-- CII code: 380 -->
                    <arg value="CreditNoteGoodsAndServices" /> <!-- CII code: 381 -->
                    <arg value="CorrectedInvoice" /> <!-- CII code: 384 -->
                    <arg value="PrepaymentInvoice" /> <!-- CII code: 386 -->
                    <arg value="SelfBilledInvoice" /> <!-- CII code: 389 -->
                    <arg value="FactoredInvoice" /> <!-- CII code: 393 -->
                    <arg value="Other" />
                </constraint>
            </constraints>
        </field>

        <field
            path="RelatedInvoiceRef/RelatedInvoiceType/InvoiceTypeCodedOther"
            description="Type de facture antérieure" label="former_invoice_type">
            <!-- Rule FR: G1.01 -->
            <!-- element EXT-FR-FE-02 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="RelatedInvoiceRef/RelatedInvoiceType[InvoiceTypeCoded='Other']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="SelfBilledCreditNote" /> <!-- CII code: 261 -->
                    <arg value="GlobalAllowances" /> <!-- CII code: 262 -->
                    <arg value="FactoredCreditNote" /> <!-- CII code: 396 -->
                    <arg value="SelfBilledPrepaymentInvoice" /> <!-- CII code: 500 -->
                    <arg value="SelfBilledFactoredInvoice" /> <!-- CII code: 501 -->
                    <arg value="SelfBilledFactoredCreditNote" /> <!-- CII code: 502 -->
                    <arg value="PrepaymentCreditNote" /> <!-- CII code: 503 -->
                    <arg value="SelfBilledCorrectedInvoice" /> <!-- CII code: to be defined (facture rectificative auto-facturée)-->
                    <arg value="FactoredCorrectedInvoice" /> <!-- CII code: to be defined (facture rectificative affacturée)-->
                    <arg value="SelfBilledFactoredCorrectedInvoice" /> <!-- CII code: to be defined (facture rectificative auto-facturée affacturée)-->
                </constraint>
            </constraints>
        </field>
    </section>

    <field path="CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement/Country/CountryCoded"
        description="requirements country" label="requirement_country">
        <constraints>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                <arg value="FR" />
            </constraint>
        </constraints>
    </field>

    <section label="fr_requirements"
        step="CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement"
        required="false">
        <field path="RequirementReference/RefNum" description="Cadre de facturation"
            label="business_process_type_fr">
            <!-- element BT-23 -->
            <constraints>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="B1" />
                    <arg value="S1" />
                    <arg value="M1" />
                    <arg value="B2" />
                    <arg value="S2" />
                    <arg value="M2" />
                    <arg value="S3" />
                    <arg value="B4" />
                    <arg value="S4" />
                    <arg value="M4" />
                    <arg value="S5" />
                    <arg value="S6" />
                    <arg value="B7" />
                    <arg value="S7" />
                </constraint>
                <!-- rule G1.60  
                    Note that the other part of the rule is implemented within BT-3
                -->
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotInListConstraint">
                    <!--BT-3-->
                    <precondition expression="//InvoiceHeader/InvoiceType/InvoiceTypeCoded='PrepaymentInvoice'" />
                    <arg value="B4" />
                    <arg value="S4" />
                    <arg value="M4" />
                    <arg value="B7" />
                    <arg value="S7" />
                </constraint>
                <constraint
                    legislation="FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="S3" />
                </constraint>
            </constraints>
        </field>

        <field path="RequirementDetails" description="List of profile type values"
            label="profile_type">
            <!-- element BT-24 -->
            <constraints>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="urn:cen.eu:en16931:2017" />
                    <arg value="urn:cen.eu:en16931:2017#compliant#urn:factur-x.eu:1p0:basic" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:factur-x.eu:1p0:extended" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn.cpro.gouv.fr:1p0:extended-ctc-fr " />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:ubl.eu:1p0:extended-ctc-fr" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:cii.eu:1p0:extended-ctc-fr" />
                    <arg value="urn:factur-x.eu:1p0:basicwlOCR" /> <!-- Until 2027/12/31 only -->
                    <arg value="urn:factur-x.eu:1p0:basicwl" /> <!-- Until 2027/08/31 only -->
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition
                        expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
                    <arg value="urn:cen.eu:en16931:2017" />
                    <arg value="urn:cen.eu:en16931:2017#compliant#urn:factur-x.eu:1p0:basic" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:factur-x.eu:1p0:extended" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn.cpro.gouv.fr:1p0:extended-ctc-fr " />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:ubl.eu:1p0:extended-ctc-fr" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:cii.eu:1p0:extended-ctc-fr" />
                    <arg value="urn:factur-x.eu:1p0:basicwlOCR" /> <!-- Until 2027/12/31 only -->
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition
                        expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20271231]" />
                    <arg value="urn:cen.eu:en16931:2017" />
                    <arg value="urn:cen.eu:en16931:2017#compliant#urn:factur-x.eu:1p0:basic" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:factur-x.eu:1p0:extended" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn.cpro.gouv.fr:1p0:extended-ctc-fr " />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:ubl.eu:1p0:extended-ctc-fr" />
                    <arg value="urn:cen.eu:en16931:2017#conformant#urn:cii.eu:1p0:extended-ctc-fr" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- BT-12 -->
    <field
        path="InvoiceReferences/ContractReference/ContractID/Ident"
        description="Reference de document"
        label="contract_reference">
        <constraints>
            <!-- Rule G3.02 -->
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                <arg value="50" />
            </constraint>
        </constraints>
    </field>

    <field
        path="InvoiceReferences/ContractReference/TypeOfContract/ContractTypeCoded"
        description="Type de contrat"
        label="contract_type_fr">
        <!-- Rule FR: G1.03 -->
        <!-- element EXT-FR-FE-01 -->
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/ContractReference/ContractID/Ident" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/ContractReference/ContractID/Ident" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/ContractReference/ContractID/Ident" />
                <arg value="Other" />
            </constraint>
        </constraints>
    </field>

    <field
        path="InvoiceReferences/ContractReference/TypeOfContract/ContractTypeCodedOther"
        description="Type de contrat"
        label="contract_type_fr">
        <!-- Rule FR: G1.03 -->
        <!-- element EXT-FR-FE-01 -->
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/ContractReference/TypeOfContract[ContractTypeCoded='Other']" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                <arg value="MARCHE" />
                <arg value="CONTRAT" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceReferences" description="Référence du document" label="invoice_references">
        <constraints>
            <constraint legislation="FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition
                    expression="InvoiceType[InvoiceTypeCoded='CreditNoteFinancialAdjustment' or InvoiceTypeCoded='CreditNoteGoodsAndServices' or InvoiceTypeCoded='CreditInvoice' or InvoiceTypeCoded='CreditMemo']" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceReferences/PurchaseOrderReference/BuyerOrderNumber" description="Référence du bon de commande" label="buyer_order_number_b2g_fr">
        <!-- Rule FR: G1.04 -->
        <!-- element BT-13 -->
        <constraints>
            <constraint
                legislation="FR_B2G_BuyerOrOrderReference"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint">
                <precondition
                    expression="not (//InvoiceHeader/InvoiceReferences/AccountNumber/RefNum)" />
            </constraint>
            <!--RULE G3.01-->
            <constraint
                legislation="FR_B2G_BuyerOrOrderReference"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <!--BT-10-->
                <precondition
                    expression="(not (//InvoiceHeader/InvoiceReferences/AccountNumber/RefNum) or (//InvoiceHeader/InvoiceReferences/AccountNumber/RefNum=''))" />
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/PurchaseOrderReference" />
            </constraint>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerAndOrderReference"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/PurchaseOrderReference" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/PurchaseOrderReference" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                <arg value="50" />
                <precondition
                    expression="//InvoiceHeader/InvoiceReferences/PurchaseOrderReference" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceReferences/PurchaseOrderReference/PurchaseOrderDate"
        description="Reference date" label="invoice_reference_date">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
        </constraints>
    </field>

    <field path="InvoiceReferences/ASNNumber/RefDate" description="Reference date"
        label="invoice_reference_date">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
        </constraints>
    </field>

    <field path="InvoiceReferences/ContractReference/ValidityDates/StartDate"
        description="Reference date" label="invoice_reference_date">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
        </constraints>
    </field>

    <field path="InvoiceReferences/ProformaInvoiceNumber/RefDate" description="Reference date"
        label="invoice_reference_date">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
        </constraints>
    </field>

    <field path="InvoiceReferences/OtherInvoiceReferences/ReferenceCoded/PrimaryReference/RefDate"
        description="Reference date" label="invoice_reference_date">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
        </constraints>
    </field>

    <field
        path="InvoiceReferences/AccountNumber/RefNum"
        description="Référence de l'acheteur"
        label="buyer_reference_b2g_fr">
        <!-- Rule FR: G1.04 -->
        <!-- element BT-10 -->
        <constraints>
            <constraint
                legislation="FR_B2G_BuyerOrOrderReference"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint">
                <precondition
                    expression="not (//InvoiceHeader/InvoiceReferences/PurchaseOrderReference/BuyerOrderNumber)" />
            </constraint>
            <constraint
                legislation="FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint
                legislation="FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <!--Rule G2.29-->
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.FrenchCharactersConstraint" />
        </constraints>
    </field>

    <field path="InvoiceNumber" description="Numéro du document" label="invoice_number">
        <!-- element BT-1 -->
        <!-- Rule EN: BR-2 -->
        <!-- Rule FR: G1.05 -->
        <constraints>
            <constraint
                legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint
                legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.AsciiConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                <arg value="20" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceIssueDate" description="Date du document" label="invoice_issue_date">
        <!-- element BT-2 -->
        <constraints>
            <constraint
                legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint
                legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint
                legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.PastConstraint" />
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint" />
        </constraints>
    </field>

    <field path="InvoiceCurrency" description="Code de la devise de facturation"
        label="invoice_currency">
        <!-- element BT-5 -->
        <!-- Rule EN: BR-5 -->
        <constraints>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint
                legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                <precondition expression="not (//InvoiceHeader[TaxAccountingCurrency])" />
                <arg value="EUR" />
                <!-- Note: this rule is not included in the new French rules -->
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceCurrency/CurrencyCoded" description="Code de la devise de facturation"
        label="invoice_currency_fr">
        <!-- Rule FR: G1.10 -->
        <!-- element BT-5 -->
        <!--Rule BR-5-->
        <constraints>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="AED" />
                <arg value="AFN" />
                <arg value="ALL" />
                <arg value="AMD" />
                <arg value="ANG" />
                <arg value="AOA" />
                <arg value="ARP" />
                <arg value="ARS" />
                <arg value="AUD" />
                <arg value="AWG" />
                <arg value="AZN" />
                <arg value="BAM" />
                <arg value="BBD" />
                <arg value="BDT" />
                <arg value="BGN" />
                <arg value="BHD" />
                <arg value="BIF" />
                <arg value="BMD" />
                <arg value="BND" />
                <arg value="BOB" />
                <arg value="BOV" />
                <arg value="BRL" />
                <arg value="BSD" />
                <arg value="BTN" />
                <arg value="BWP" />
                <arg value="BYR" />
                <arg value="BYN" />
                <arg value="BZD" />
                <arg value="CAD" />
                <arg value="CDF" />
                <arg value="CHF" />
                <arg value="CLF" />
                <arg value="CLP" />
                <arg value="CNY" />
                <arg value="COP" />
                <arg value="COU" />
                <arg value="CRC" />
                <arg value="CUP" />
                <arg value="CVE" />
                <arg value="CZK" />
                <arg value="DJF" />
                <arg value="DKK" />
                <arg value="DOP" />
                <arg value="DZD" />
                <arg value="ECS" />
                <arg value="ECV" />
                <arg value="EGP" />
                <arg value="ERN" />
                <arg value="ETB" />
                <arg value="EUR" />
                <arg value="FJD" />
                <arg value="FKP" />
                <arg value="GBP" />
                <arg value="GEL" />
                <arg value="GHS" />
                <arg value="GIP" />
                <arg value="GMD" />
                <arg value="GNF" />
                <arg value="GTQ" />
                <arg value="GWP" />
                <arg value="GYD" />
                <arg value="HKD" />
                <arg value="HNL" />
                <arg value="HTG" />
                <arg value="HUF" />
                <arg value="IDR" />
                <arg value="ILS" />
                <arg value="INR" />
                <arg value="IQD" />
                <arg value="IRR" />
                <arg value="ISK" />
                <arg value="JMD" />
                <arg value="JOD" />
                <arg value="JPY" />
                <arg value="KES" />
                <arg value="KGS" />
                <arg value="KHR" />
                <arg value="KMF" />
                <arg value="KPW" />
                <arg value="KRW" />
                <arg value="KZT" />
                <arg value="KWD" />
                <arg value="KYD" />
                <arg value="LAK" />
                <arg value="LBP" />
                <arg value="LKR" />
                <arg value="LRD" />
                <arg value="LSL" />
                <arg value="LYD" />
                <arg value="MAD" />
                <arg value="MDL" />
                <arg value="MGA" />
                <arg value="MKD" />
                <arg value="MMK" />
                <arg value="MNT" />
                <arg value="MOP" />
                <arg value="MRU" />
                <arg value="MUR" />
                <arg value="MVR" />
                <arg value="MWK" />
                <arg value="MXN" />
                <arg value="MXV" />
                <arg value="MYR" />
                <arg value="MZN" />
                <arg value="NAD" />
                <arg value="NGN" />
                <arg value="NHF" />
                <arg value="NIO" />
                <arg value="NOK" />
                <arg value="NPR" />
                <arg value="NZD" />
                <arg value="OMR" />
                <arg value="PAB" />
                <arg value="PEN" />
                <arg value="PGK" />
                <arg value="PHP" />
                <arg value="PKR" />
                <arg value="PLN" />
                <arg value="PYG" />
                <arg value="QAR" />
                <arg value="RON" />
                <arg value="RSD" />
                <arg value="RUB" />
                <arg value="RWF" />
                <arg value="SAR" />
                <arg value="SBD" />
                <arg value="SCR" />
                <arg value="SDG" />
                <arg value="SEK" />
                <arg value="SGD" />
                <arg value="SHP" />
                <arg value="SLL" />
                <arg value="SOS" />
                <arg value="SRD" />
                <arg value="SSP" />
                <arg value="STD" />
                <arg value="SVC" />
                <arg value="SYP" />
                <arg value="SZL" />
                <arg value="THB" />
                <arg value="TJS" />
                <arg value="TMT" />
                <arg value="TND" />
                <arg value="TOP" />
                <arg value="TRY" />
                <arg value="TTD" />
                <arg value="TWD" />
                <arg value="TZS" />
                <arg value="UAH" />
                <arg value="UGX" />
                <arg value="USD" />
                <arg value="USN" />
                <arg value="UYU" />
                <arg value="UYW" />
                <arg value="UZS" />
                <arg value="VEB" />
                <arg value="VES" />
                <arg value="VND" />
                <arg value="VUV" />
                <arg value="WST" />
                <arg value="XAF" />
                <arg value="XAG" />
                <arg value="XAU" />
                <arg value="XCD" />
                <arg value="XDR" />
                <arg value="XFO" />
                <arg value="XFU" />
                <arg value="XOF" />
                <arg value="XPD" />
                <arg value="XPF" />
                <arg value="XPT" />
                <arg value="XSU" />
                <arg value="XUA" />
                <arg value="YER" />
                <arg value="ZAR" />
                <arg value="ZMW" />
                <arg value="ZWL" />
            </constraint>
        </constraints>
    </field>

    <field path="TaxPointDate" description="Tax point date" label="tax_point_date">
        <!--BT-7 Rule BR-CO-3-->
        <constraints>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="//InvoiceHeader" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                <precondition expression="//InvoiceHeader" />
            </constraint>
        </constraints>
    </field>

    <!-- element BT-6 -->
    <field path="TaxAccountingCurrency" label="tax_accounting_">
        <constraints>
            <constraint
                legislation="EU, FR, EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
        </constraints>
    </field>
    <section label="tax_accounting" step="TaxAccountingCurrency" required="false">
        <field path="CurrencyCoded" description="Code de la devise de paiement de la TVA"
            label="currency">
            <!-- element BT-6 -->
            <constraints>
                <constraint
                    legislation="EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <arg value="EUR" />
                </constraint>
            </constraints>
        </field>
    </section>

    <field path="TaxAccountingCurrency/CurrencyCoded" description="Code de la devise de facturation"
        label="tax_currency_fr">
        <!-- Rule FR: G1.10 -->
        <!-- element BT-6 -->
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="AED" />
                <arg value="AFN" />
                <arg value="ALL" />
                <arg value="AMD" />
                <arg value="ANG" />
                <arg value="AOA" />
                <arg value="ARP" />
                <arg value="ARS" />
                <arg value="AUD" />
                <arg value="AWG" />
                <arg value="AZN" />
                <arg value="BAM" />
                <arg value="BBD" />
                <arg value="BDT" />
                <arg value="BGN" />
                <arg value="BHD" />
                <arg value="BIF" />
                <arg value="BMD" />
                <arg value="BND" />
                <arg value="BOB" />
                <arg value="BOV" />
                <arg value="BRL" />
                <arg value="BSD" />
                <arg value="BTN" />
                <arg value="BWP" />
                <arg value="BYR" />
                <arg value="BYN" />
                <arg value="BZD" />
                <arg value="CAD" />
                <arg value="CDF" />
                <arg value="CHF" />
                <arg value="CLF" />
                <arg value="CLP" />
                <arg value="CNY" />
                <arg value="COP" />
                <arg value="COU" />
                <arg value="CRC" />
                <arg value="CUP" />
                <arg value="CVE" />
                <arg value="CZK" />
                <arg value="DJF" />
                <arg value="DKK" />
                <arg value="DOP" />
                <arg value="DZD" />
                <arg value="ECS" />
                <arg value="ECV" />
                <arg value="EGP" />
                <arg value="ERN" />
                <arg value="ETB" />
                <arg value="EUR" />
                <arg value="FJD" />
                <arg value="FKP" />
                <arg value="GBP" />
                <arg value="GEL" />
                <arg value="GHS" />
                <arg value="GIP" />
                <arg value="GMD" />
                <arg value="GNF" />
                <arg value="GTQ" />
                <arg value="GWP" />
                <arg value="GYD" />
                <arg value="HKD" />
                <arg value="HNL" />
                <arg value="HTG" />
                <arg value="HUF" />
                <arg value="IDR" />
                <arg value="ILS" />
                <arg value="INR" />
                <arg value="IQD" />
                <arg value="IRR" />
                <arg value="ISK" />
                <arg value="JMD" />
                <arg value="JOD" />
                <arg value="JPY" />
                <arg value="KES" />
                <arg value="KGS" />
                <arg value="KHR" />
                <arg value="KMF" />
                <arg value="KPW" />
                <arg value="KRW" />
                <arg value="KZT" />
                <arg value="KWD" />
                <arg value="KYD" />
                <arg value="LAK" />
                <arg value="LBP" />
                <arg value="LKR" />
                <arg value="LRD" />
                <arg value="LSL" />
                <arg value="LYD" />
                <arg value="MAD" />
                <arg value="MDL" />
                <arg value="MGA" />
                <arg value="MKD" />
                <arg value="MMK" />
                <arg value="MNT" />
                <arg value="MOP" />
                <arg value="MRU" />
                <arg value="MUR" />
                <arg value="MVR" />
                <arg value="MWK" />
                <arg value="MXN" />
                <arg value="MXV" />
                <arg value="MYR" />
                <arg value="MZN" />
                <arg value="NAD" />
                <arg value="NGN" />
                <arg value="NHF" />
                <arg value="NIO" />
                <arg value="NOK" />
                <arg value="NPR" />
                <arg value="NZD" />
                <arg value="OMR" />
                <arg value="PAB" />
                <arg value="PEN" />
                <arg value="PGK" />
                <arg value="PHP" />
                <arg value="PKR" />
                <arg value="PLN" />
                <arg value="PYG" />
                <arg value="QAR" />
                <arg value="RON" />
                <arg value="RSD" />
                <arg value="RUB" />
                <arg value="RWF" />
                <arg value="SAR" />
                <arg value="SBD" />
                <arg value="SCR" />
                <arg value="SDG" />
                <arg value="SEK" />
                <arg value="SGD" />
                <arg value="SHP" />
                <arg value="SLL" />
                <arg value="SOS" />
                <arg value="SRD" />
                <arg value="SSP" />
                <arg value="STD" />
                <arg value="SVC" />
                <arg value="SYP" />
                <arg value="SZL" />
                <arg value="THB" />
                <arg value="TJS" />
                <arg value="TMT" />
                <arg value="TND" />
                <arg value="TOP" />
                <arg value="TRY" />
                <arg value="TTD" />
                <arg value="TWD" />
                <arg value="TZS" />
                <arg value="UAH" />
                <arg value="UGX" />
                <arg value="USD" />
                <arg value="USN" />
                <arg value="UYU" />
                <arg value="UYW" />
                <arg value="UZS" />
                <arg value="VEB" />
                <arg value="VES" />
                <arg value="VND" />
                <arg value="VUV" />
                <arg value="WST" />
                <arg value="XAF" />
                <arg value="XAG" />
                <arg value="XAU" />
                <arg value="XCD" />
                <arg value="XDR" />
                <arg value="XFO" />
                <arg value="XFU" />
                <arg value="XOF" />
                <arg value="XPD" />
                <arg value="XPF" />
                <arg value="XPT" />
                <arg value="XSU" />
                <arg value="XUA" />
                <arg value="YER" />
                <arg value="ZAR" />
                <arg value="ZMW" />
                <arg value="ZWL" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceDates/InvoiceDueDate" description="Date d’échéance de règlement"
        label="due_date">
        <!-- element BT-9 -->
        <!--Rule BR-CO-25-->
        <!-- Note: these rules are not included in the new French rules -->
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint legislation="EN,FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            <constraint legislation="EN, FR, EU, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceDates/ActualShipDate" description="Despatch date" label="despatch_date">
        <!-- element BT-72 -->
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint legislation="EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates" />
            </constraint>
        </constraints>
    </field>

    <!-- INVOICING PERIOD start of the block-->
    <field path="InvoiceDates/InvoicingPeriod/StartDate" description="Invoicing period start date"
        label="invoicing_period_start_date">
        <!-- element BT-73 -->
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod" />
            </constraint>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod" />
            </constraint>
            <!--Rule BR-CO-19-->
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod/EndDate" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceDates/InvoicingPeriod/EndDate" description="Invoicing period end date"
        label="invoicing_period_end_date">
        <!-- element BT-74 -->
        <!--Rule BR-29-->
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod" />
            </constraint>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod" />
            </constraint>
            <!--Rule BR-CO-19-->
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceHeader/InvoiceDates/InvoicingPeriod/StartDate" />
            </constraint>
        </constraints>
    </field>

    <field path="InvoiceDates/InvoicingPeriod" description="Période de référence" label="invoicingperiod_reference_period">
        <!-- element BG-14 -->
        <constraints>
            <!-- G1.31 -->
            <constraint legislation="FR"
                class="com.byzaneo.generix.edocument.validation.constraints.AllDescendantsNotEmptyConstraint">
                <arg value="StartDate" />
                <arg value="EndDate" />
            </constraint>
        </constraints>
    </field>

    <section label="invoicingperiod" step="InvoiceDates/InvoicingPeriod" required="false">
        <field path="StartDate" description="Période de référence"
            label="invoicing_period_startDate">
            <!-- element BT-73 -->
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            </constraints>
        </field>

        <field path="EndDate" description="Période de référence" label="invoicing_period_endDate">
            <!-- element BT-74 -->
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            </constraints>
        </field>
    </section>
    <!-- INVOICING PERIOD end of the block-->

    <!-- (1) EU & FR - Date et heure de préparation du document -->
    <section label="date"
        step="InvoiceDates/ListOfOtherInvoiceDates/DateCoded[DateQualifier/DateQualifierCoded = 'PreparationDateTimeOfDocument']"
        required="false">
        <field path="Date" description="Date et heure de préparation du document"
            label="preparation_date_time_document">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            </constraints>
        </field>
    </section>

    <!-- PAYMENT TERMS -->
    <section label="paymentterms" step="InvoicePaymentInstructions/PaymentTerms"
        description="validation of Payment Terms ">
        <field path="PaymentTerm[PaymentTermCoded='PenaltyTerms']/PaymentTermDescription"
            description="Conditions de pénalités" label="penalty_term">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="PaymentTerm[PaymentTermCoded='Discount']/PaymentTermDescription"
            description="Conditions d'escompte" label="discount_term">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="PaymentTerm[PaymentTermCoded='LatePayment']/PaymentTermDescription"
            description="Indemnité forfaitaire de pénalité" label="latepayment_term">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/SellerParty/NameAddress/Country[CountryCoded='FR']" />
                </constraint>
            </constraints>
        </field>
        <field path="PaymentTerm/DiscountInformation/DiscountDueDate" description="Date escompte"
            label="discount_due_date">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            </constraints>
        </field>
    </section>

    <!-- INVOICE PARTY -->
    <section step="InvoiceParty" label="invoiceparty">
        <!-- BG-4 -->
        <field path="SellerParty" label="sellerparty">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="SellerParty/ListOfIdentifier/Identifier/Agency/CodeListIdentifierCodedOther"
            label="sellerparty_id_scheme" description="identifier scheme">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.OneInListConstraint">
                    <!-- <arg value="SIRE." /> -->
                    <!-- In java regex SIRE* will match SIR, SIRE, SIREE, SIREEE but not SIREN or SIRET; that is done by SIRE. (matches SIREN, SIRET, SIREZ ...) -->
                    <!-- Also please note that SIREN and SIRET are often found together, so the rule SIRE. will generate a lot of errors -->
                    <arg value="SIREN" />
                    <arg value="0223" />
                    <arg value="0226" />
                    <arg value="0227" />
                    <arg value="0228" />
                    <arg value="0229" />
                    <!-- If we have more than one argument that is the list. If we have exactly 1 argument this is a file in the "lists" folder containing the list of values-->
                    <!-- <arg value="one_in_list_constraints" />-->
                </constraint>
            </constraints>
        </field>

        <!-- BG-7 -->
        <field path="BuyerParty" label="buyerparty">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <!--EXT-FR-FE-50-->
        <field path="ListOfPartyCoded/PartyCoded[PartyRoleCoded='Payer']/PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'] [CodeListIdentifierCoded='ValueAddedTaxIdentification']]/Ident"
            label="VAT_payer_identifier"
            description="VAT payer identifier">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[AgencyCoded='CEC']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[CodeListIdentifierCoded='ValueAddedTaxIdentification']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='Payer']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[AgencyCoded='CEC']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[CodeListIdentifierCoded='ValueAddedTaxIdentification']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='Payer']" />
                </constraint>
            </constraints>
        </field>
        <!--EXT-FR-FE-73-->
        <field path="ListOfPartyCoded/PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']/PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'] [CodeListIdentifierCoded='ValueAddedTaxIdentification']]/Ident"
            label="VAT_seller_identifier"
            description="VAT identifier of the Seller's Agent">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[AgencyCoded='CEC']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[CodeListIdentifierCoded='ValueAddedTaxIdentification']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[AgencyCoded='CEC']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded/PartyTaxInformation/TaxIdentifier/Agency[CodeListIdentifierCoded='ValueAddedTaxIdentification']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']" />
                </constraint>
            </constraints>
        </field>
        <!-- EXT-FR-FE-94 -->
        <field path="ListOfPartyCoded/PartyCoded[PartyRoleCoded='AgentOrRepresentative']/ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            description="Référence gouvernementale de l'acheteur" label="agent_or_representative_code">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='AgentOrRepresentative']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="PartyCoded[PartyRoleCoded['AgentOrRepresentative']]/ListOfIdentifier/Identifier/Ident" />
                </constraint>
            </constraints>
        </field>
        <!--BT-69-->
        <field path="ListOfPartyCoded/PartyCoded[PartyRoleCoded='DeclarantsAgentOrRepresentative']/NameAddress[Name1]/Country/CountryCoded"
            label="address_country_coded" description="Country code of tax representative">
            <constraints>
                <constraint class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint"
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                    <!--BT-62-->
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='DeclarantsAgentOrRepresentative']/NameAddress/Name1" />
                </constraint>
                <constraint class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint"
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                    <arg value="FR" />
                    <!--BT-62-->
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='DeclarantsAgentOrRepresentative']/NameAddress/Name1" />
                </constraint>
                <constraint class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint"
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                    <arg value="PartyCoded/NameAddress/Country/CountryCoded" />
                    <!--BT-62-->
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='DeclarantsAgentOrRepresentative']/NameAddress/Name1" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                </constraint>
            </constraints>
        </field>
        <!-- EXT-FR-FE-71 -->
        <field path="ListOfPartyCoded/PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']/ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            label="seller_agent_or_representative_code">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement/RequirementReference[RefNum='S3' or RefNum='S6']" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']/NameAddress/Name1" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="PartyCoded[PartyRoleCoded['SellersAgentOrRepresentative']]/ListOfIdentifier/Identifier/Ident" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- BILL TO PARTY -->
    <section label="billtoparty" step="InvoiceParty/BillToParty"
        description="validation of BillToParty">
        <field path="NameAddress/Name1" description="Dénomination sociale du facturé"
            label="address_name">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Country" description="Pays de l’adresse du facturé"
            label="address_country">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <!--EXT-FR-FE-107-->
        <field path="NameAddress/Country/CountryCoded"
            label="address_country_code">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="BillToParty/NameAddress/Country/CountryCoded" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                </constraint>
            </constraints>
        </field>

        <field path="NameAddress/Street" description="Adresse du facturé" label="address_street">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/PostalCode" description="Adresse du facturé"
            label="address_postalcode">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/City" description="Adresse du facturé" label="address_city">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='RCS-RCM']]/Ident"
            description="Numéro d’enregistrement du facturé" label="registration_ident">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            description="Référence gouvernementale du facturé" label="gov_reference">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="NameAddress/Country[CountryCoded='FR']" />
                </constraint>
            </constraints>
        </field>
        <!--EXT-FR-FE-92c-->
        <!--RULE G2.29-->
        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIRET']]/Ident"
            label="siret_ident" description="SIRET Ident">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.FrenchCharactersConstraint" />
            </constraints>
        </field>

        <field
            path="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded = 'CEC'][CodeListIdentifierCoded ='ValueAddedTaxIdentification']]/Ident"
            description="Code TVA intracommunautaire du facturé" label="vat_ident">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EU"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI FR GR HU IE IT LV LT LU MT MC NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI GR HU IE IT LV LT LU MT NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- SELLER PARTY -->
    <section label="sellerparty" step="InvoiceParty/SellerParty"
        description="Dénomination sociale du vendeur">

        <!-- BG-5 -->
        <!--Rule BR-8-->
        <field path="NameAddress"
            label="address_name">
            <constraints>
                <constraint legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Name1" description="Dénomination sociale du vendeur"
            label="address_name">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Country" description="Pays de l’adresse du vendeur"
            label="address_country">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Country/CountryCoded" description="Pays de l’adresse du vendeur"
            label="address_country">
            <!-- Rule FR: G6.08 -->
            <!-- element BT-40 -->
            <!--Rule BR-9-->
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                </constraint>
            </constraints>
        </field>

        <field path="NameAddress/Street" description="Adresse du vendeur" label="address_street">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/PostalCode" description="Adresse du vendeur"
            label="address_postalcode">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/City" description="Adresse du vendeur" label="address_city">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='RCS-RCM']]/Ident"
            description="Numéro d’enregistrement du vendeur" label="registration_ident">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="NameAddress/Country[CountryCoded='FR']" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            description="Référence gouvernementale du vendeur" label="gov_reference">
            <!-- Rule FR: G6.08 -->
            <!-- element BT-30 -->
            <!--Rule BR-CO-26-->
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="NameAddress/Country[CountryCoded='FR']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIRET']][Ident='0223' or Ident='0227' or Ident='0028' or Ident='0229' or Ident='0226']" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]" />
                    <!--rule G1.47-->
                    <!--BT-29 and BT-31 are not present, so BT-30 will pe mandatory-->
                    <precondition
                        expression="(not(PartyID/Ident) and not(PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]/Ident))" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Ident"
            description="SIRET du vendeur ou équivalent" label="siret">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIRET']]/Ident"
            description="Qualifiant du SIRET" label="siret_qualifier">
            <!-- Rule FR: G1.11 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <!--RULE G1.71-->
                <constraint
                    legislation="FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference,  FR_B2G_BuyerOrOrderReference, FR_B2G_BuyerAndOrderReference,FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <arg value="0009" />
                    <!--BT-30 defined-->
                    <precondition expression="//InvoiceHeader/InvoiceParty/SellerParty/ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident" />
                </constraint>
                <constraint
                    legislation="FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference,  FR_B2G_BuyerOrOrderReference, FR_B2G_BuyerAndOrderReference,FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="0223" />
                    <arg value="0226" />
                    <arg value="0227" />
                    <arg value="0228" />
                    <arg value="0229" />
                    <!--BT-30 not defined-->
                    <precondition expression="not(//InvoiceHeader/InvoiceParty/SellerParty/ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident)" />
                </constraint>
                <!--BT-29c-->
                <!--Rule G2.29-->
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.FrenchCharactersConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Agency[AgencyCoded='Other'][CodeListIdentifierCodedOther='SIRET']/AgencyCodedOther"
            description="Qualifiant du code vendeur" label="agency_coded_other">
            <!-- Rule FR: G1.11 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="0223" />
                    <arg value="0226" />
                    <arg value="0227" />
                    <arg value="0228" />
                    <arg value="0229" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIRET']]/Ident"
            description="SIRET du vendeur français" label="siret_fr">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="14" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0227']]/Ident"
            description="SIRET ou équivalent du vendeur étranger" label="siret_0227">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="18" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0223']]/Ident"
            description="SIRET ou équivalent du vendeur européen" label="siret_0223">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="18" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0228']]/Ident"
            description="SIRET ou équivalent du vendeur néocalédonien" label="siret_0228">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="10" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0229']]/Ident"
            description="SIRET ou équivalent du vendeur tahitien" label="siret_0229">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="9" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0226']]/Ident"
            description="SIRET ou équivalent du vendeur citoyen français" label="siret_0226">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-29b -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="80" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Agency[AgencyCoded='Other']/AgencyCodedOther"
            description="Qualifiant du code vendeur" label="only_b2g">
            <!-- Rule FR: G1.13 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="0223" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Agency[AgencyCoded='Other']/AgencyCodedOther"
            description="Qualifiant du code vendeur" label="only_b2g">
            <!-- Rule FR: G1.13 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="0226" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Agency[AgencyCoded='Other']/AgencyCodedOther"
            description="Qualifiant du code vendeur" label="only_b2g">
            <!-- Rule FR: G1.13 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="0227" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Agency[AgencyCoded='Other']/AgencyCodedOther"
            description="Qualifiant du code vendeur" label="only_b2g">
            <!-- Rule FR: G1.13 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="0228" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier/Agency[AgencyCoded='Other']/AgencyCodedOther"
            description="Qualifiant du code vendeur" label="only_b2g">
            <!-- Rule FR: G1.13 -->
            <!-- element BT-29b-1 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2B"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="0229" />
                </constraint>
            </constraints>
        </field>

        <field
            path="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]/Ident"
            description="Code TVA intracommunautaire du vendeur" label="vat_ident">
            <!-- BT-31 -->
            <!--Rule BR-CO-9BR-CO-26-->
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EU"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI FR GR HU IE IT LV LT LU MT MC NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI GR HU IE IT LV LT LU MT NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]" />
                    <!--BT-116>150-->
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary/TaxableAmount > '150'" />
                    <!--BT-3 precondition-->
                    <precondition
                        expression="(//InvoiceHeader/InvoiceType[(InvoiceTypeCoded!='CorrectedInvoice' and InvoiceTypeCoded!='Other') or
                            (InvoiceTypeCoded='Other' and InvoiceTypeCodedOther!='SelfBilledCorrectedInvoice' and InvoiceTypeCodedOther!='FactoredCorrectedInvoice' and InvoiceTypeCodedOther!='SelfBilledFactoredCorrectedInvoice')])" />
                    <!--BT-29 and BT-30 are not present, so BT-31 will pe mandatory-->
                    <precondition
                        expression="(not(PartyID/Ident) and not(ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident)) " />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                    <precondition
                        expression="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]" />
                </constraint>
            </constraints>
        </field>

        <field
            path="PartyTaxInformation/RegisteredName"
            description="Raison sociale du vendeur" label="registered_name">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="NameAddress/Country[CountryCoded='FR']" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='AssignedByNationalTradeAgency'][CodeListIdentifierCoded='BusinessLegalStructureType']]/Ident"
            description="Forme juridique du vendeur" label="legal_structure">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="NameAddress/Country[CountryCoded='FR']" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='LegalCapital']]/Ident"
            description="Capital social du vendeur" label="legal_capital">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="NameAddress/Country[CountryCoded='FR']" />
                </constraint>
            </constraints>
        </field>
        <!--BT-29-->
        <field path="PartyID/Ident" label="additional_party_ident" description="Additional identifier">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <!--rule G1.47-->
                    <!--BT-30 and BT-31 are not present, so BT-29 will pe mandatory-->
                    <precondition
                        expression="(not(ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident) and not(PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]/Ident))" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- BUYER PARTY -->
    <section label="buyerparty" step="InvoiceParty/BuyerParty"
        description="Dénomination sociale de l'acheteur">
        <!-- BG-8 -->
        <!--Rule BR-10-->
        <field path="NameAddress"
            label="address_name">
            <constraints>
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>
        <field path="NameAddress/Country/CountryCoded" description="Pays de l’adresse de l'acheteur"
            label="address_country">
            <!-- Rule FR: G6.08 -->
            <!-- element BT-55 -->
            <!--Rule BR-11-->
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                </constraint>
            </constraints>
        </field>
        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            description="Référence gouvernementale de l'acheteur" label="gov_reference">
            <!-- Rule FR: G6.08 -->
            <!-- element BT-47 -->
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIREN']]" />
                </constraint>
            </constraints>
        </field>
        <!--BT-46b-->
        <field path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='SIRET']]/Ident"
            label="siret_ident" description="SIRET Ident">
            <constraints>
                <constraint legislation="FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>
        <field
            path="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]/Ident"
            description="Code TVA intracommunautaire du acheteur" label="vat_ident">
            <!-- BT-48 -->
            <!--Rule BR-CO-9-->
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]" />
                </constraint>
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                    <precondition expression="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded='CEC'][CodeListIdentifierCoded='ValueAddedTaxIdentification']]" />
                </constraint>
            </constraints>
        </field>
        <!--RULE G2.29-->
        <!--BT-46C-->
        <field path="ListOfIdentifier/Identifier[Agency[AgencyCoded='FR-INSEE'][CodeListIdentifierCoded='Other'][CodeListIdentifierCodedOther='ROUTAGE']]/Ident"
            label="identifier_routing_code" description="Buyer ID (Routing Code)">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.FrenchCharactersConstraint" />
            </constraints>
        </field>
    </section>

    <!-- SHIP TO PARTY -->
    <section label="shiptoparty" step="InvoiceParty/ShipToParty" required="false">
        <field path="NameAddress/Country/CountryCoded"
            label="address_country">
            <!-- Rule FR: G6.08 -->
            <!-- element BT-80 -->
            <!--Rule BR-57-->
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                </constraint>
            </constraints>
        </field>
    </section>

    <section label="remit_to_party" step="InvoiceParty/RemitToParty/ListOfIdentifier"
        description="Identifiant du bénéficiaire" required="false">

        <field
            path="Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0009']]/Ident"
            description="SIRET du bénéficiaire" label="siret_fr">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-60 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="14" />
                </constraint>
            </constraints>
        </field>

        <field
            path="Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0227']]/Ident"
            description="SIRET ou équivalent du vendeur étranger" label="siret_0227">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-60 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="18" />
                </constraint>
            </constraints>
        </field>

        <field
            path="Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0223']]/Ident"
            description="SIRET ou équivalent du vendeur européen" label="siret_0223">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-60 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="18" />
                </constraint>
            </constraints>
        </field>

        <field
            path="Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0228']]/Ident"
            description="SIRET ou équivalent du vendeur néocalédonien" label="siret_0228">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-60 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="10" />
                </constraint>
            </constraints>
        </field>

        <field
            path="Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0229']]/Ident"
            description="SIRET ou équivalent du vendeur tahitien" label="siret_0229">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-60 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="9" />
                </constraint>
            </constraints>
        </field>

        <field
            path="Identifier[Agency[AgencyCoded='Other'][AgencyCodedOther='0226']]/Ident"
            description="SIRET ou équivalent du vendeur citoyen français" label="siret_0226">
            <!-- Rule FR: G1.08 -->
            <!-- element BT-60 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="80" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!--EXT-FR-FE-21-->
    <field path="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='AgentOrRepresentative']/NameAddress/Country/CountryCoded"
        label="agent_or_representative_address_country_code">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="PartyCoded[PartyRoleCoded='AgentOrRepresentative']/NameAddress/Country/CountryCoded" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
            </constraint>
        </constraints>
    </field>

    <!--EXT-FR-FE-61-->
    <field path="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='Payer']/NameAddress/Country/CountryCoded"
        label="payer_address_country_code">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='Payer']" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="PartyCoded[PartyRoleCoded='Payer']/NameAddress/Country/CountryCoded" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
            </constraint>
        </constraints>
    </field>

    <!--EXT-FR-FE-84-->
    <field path="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']/NameAddress/Country/CountryCoded"
        label="sellers_agent_or_representative_address_country_code">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="PartyCoded[PartyRoleCoded='SellersAgentOrRepresentative']/NameAddress/Country/CountryCoded" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
            </constraint>
        </constraints>
    </field>

    <!--EXT-FR-FE-130-->
    <field path="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='Factor']/NameAddress/Country/CountryCoded"
        label="factor_address_country_code">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='Factor']" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="PartyCoded[PartyRoleCoded='Factor']/NameAddress/Country/CountryCoded" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
            </constraint>
        </constraints>
    </field>

    <!--EXT-FR-FE-27-->
    <field path="InvoiceParty/RemitToParty/PartyTaxInformation/TaxIdentifier/Ident"
        label="party_tax_information_ident" description="Party tax identifier">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition
                    expression="InvoiceParty/RemitToParty/PartyTaxInformation/TaxIdentifier/Agency[AgencyCoded='CEC']" />
                <precondition
                    expression="InvoiceParty/RemitToParty/PartyTaxInformation/TaxIdentifier/Agency[CodeListIdentifierCoded='ValueAddedTaxIdentification']" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
                <precondition
                    expression="InvoiceParty/RemitToParty/PartyTaxInformation/TaxIdentifier/Agency[AgencyCoded='CEC']" />
                <precondition
                    expression="InvoiceParty/RemitToParty/PartyTaxInformation/TaxIdentifier/Agency[CodeListIdentifierCoded='ValueAddedTaxIdentification']" />
            </constraint>
        </constraints>
    </field>

    <!--EXT-FR-FE-38-->
    <field path="InvoiceParty/RemitToParty/NameAddress/Country/CountryCoded"
        label="remit_to_party_address_country_code">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="InvoiceParty[RemitToParty]" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="RemitToParty/NameAddress/Country/CountryCoded" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
            </constraint>
        </constraints>
    </field>

    <!-- HEAD OFFICE -->
    <section label="headoffice"
        step="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='HeadOffice']"
        description="validation of HeadOffice" required="false">

        <field path="NameAddress/Name1"
            description="Dénomination sociale du siège social du vendeur" label="address_name">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Country"
            description="Code du pays de l’adresse du siège social du vendeur"
            label="address_country">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Street" description="Adresse du siège social du vendeur"
            label="address_street">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/PostalCode" description="Adresse du siège social du vendeur"
            label="address_postalcode">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/City" description="Adresse du siège social du vendeur"
            label="address_city">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded = 'Other'][AgencyCodedOther ='RCS–RCM']]/Ident"
            description="Numéro d’enregistrement du siège social du vendeur"
            label="registration_ident">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded = 'FR-INSEE'][CodeListIdentifierCoded ='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            description="Référence gouvernementale du siège social du vendeur" label="gov_reference">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded = 'CEC'][CodeListIdentifierCoded = 'ValueAddedTaxIdentification']]/Ident"
            description="Code TVA intracommunautaire du siège social du vendeur" label="vat_ident">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI GR HU IE IT LV LT LU MT NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
                <constraint legislation="EU"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI FR GR HU IE IT LV LT LU MT MC NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- DECLARANTS AGENT OR REPRESENTATIVE -->
    <section label="declarantsagentorrepresentative"
        step="InvoiceParty/ListOfPartyCoded/PartyCoded[PartyRoleCoded='DeclarantsAgentOrRepresentative']"
        description="validation of DeclarantsAgentOrRepresentative" required="false">

        <field path="NameAddress/Name1" description="Dénomination sociale du représentant fiscal"
            label="address_name">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Country"
            description="Code du pays de l’adresse du représentant fiscal"
            label="address_country">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/Street" description="Adresse du représentant fiscal"
            label="address_street">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/PostalCode" description="Adresse du représentant fiscal"
            label="address_postalcode">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="NameAddress/City" description="Adresse du représentant fiscal"
            label="address_city">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded = 'Other'][AgencyCodedOther ='RCS–RCM']]/Ident"
            description="Numéro d’enregistrement du représentant fiscal"
            label="registration_ident">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfIdentifier/Identifier[Agency[AgencyCoded = 'FR-INSEE'][CodeListIdentifierCoded ='Other'][CodeListIdentifierCodedOther='SIREN']]/Ident"
            description="Référence gouvernementale du représentant fiscal"
            label="gov_reference">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>
        <!--BT-63-->
        <field
            path="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded = 'CEC'][CodeListIdentifierCoded = 'ValueAddedTaxIdentification']]/Ident"
            description="Code TVA intracommunautaire du représentant fiscal"
            label="vat_ident">

            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI GR HU IE IT LV LT LU MT NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
                <constraint legislation="EU"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="NameAddress/Country[contains('DE AT BE BG CY HR DK ES EE FI FR GR HU IE IT LV LT LU MT MC NL PL PT CZ RO GB SK SI SE', CountryCoded)]" />
                </constraint>
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CountryCodeISO3166-alpha2" />
                    <precondition expression="PartyTaxInformation/TaxIdentifier[Agency[AgencyCoded = 'CEC'][CodeListIdentifierCoded = 'ValueAddedTaxIdentification']]" />
                </constraint>

            </constraints>
        </field>
    </section>

    <!-- INVOICE ALLOWANCES OR CHARGES -->
    <section label="alloworcharge" step="InvoiceAllowancesOrCharges/AllowOrCharge"
        description="validation of Invoice Allowances or Charges " required="false">

        <field path="AllowanceOrChargeDescription" description="Libellé"
            label="description">
            <constraints>
                <constraint
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint"
                    legislation="FR">
                    <precondition expression="InvoiceAllowancesOrCharges/AllowOrCharge" />
                </constraint>
            </constraints>
        </field>
        <field path="AllowanceOrChargeDescription/ListOfDescription" description="Libellé"
            label="description">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>
        <field path="AllowanceOrChargeDescription/ServiceCoded" description="Libellé"
            label="description">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="AllowanceOrChargeDescription/ListOfDescription" description="ListOfDescription"
            label="list_of_description">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='Allowance'" />
                    <precondition expression="MethodOfHandlingCoded='AllowanceToBeIssuedByVendor'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ServiceCodedOther) or AllowanceOrChargeDescription/ServiceCodedOther=''" />
                </constraint>
            </constraints>
        </field>

        <field path="AllowanceOrChargeDescription/ServiceCodedOther" description="ServiceCodedOther"
            label="service_coded_other">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='Allowance'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ListOfDescription) or AllowanceOrChargeDescription/ListOfDescription=''" />
                </constraint>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="IndicatorCoded='Allowance'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <arg value="UNTDID5189" />
                </constraint>
            </constraints>
        </field>

        <field path="AllowanceOrChargeDescription/ListOfDescription" description="ChargeListOfDescription"
            label="charge_list_of_description">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='Charge'" />
                    <precondition expression="MethodOfHandlingCoded='ChargeToBePaidByCustomer'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ServiceCodedOther) or AllowanceOrChargeDescription/ServiceCodedOther=''" />
                </constraint>
            </constraints>
        </field>

        <field path="AllowanceOrChargeDescription/ServiceCodedOther" description="ChargeServiceCodedOther"
            label="charge_service_coded_other">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='Charge'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ListOfDescription) or AllowanceOrChargeDescription/ListOfDescription=''" />
                </constraint>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="IndicatorCoded='Charge'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <arg value="UNTDID7161" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge" label="type_allowance_charge">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.HasDescendantNotEmptyConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge" />
                    <arg value="MonetaryValue/MonetaryAmount" />
                    <arg value="PercentageAllowanceOrCharge/Percent" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/Percent"
            label="type_allowance_charge_percent" required="false">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>
        <!--BT-92 && BT-99-->
        <!--Rule BR-31-->
        <field path="TypeOfAllowanceOrCharge/MonetaryValue/MonetaryAmount"
            label="type_allowance_charge_monetaryvalue" required="false">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>

        <field
            path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryAmount"
            label="type_allowance_charge_percentagemonetaryvalue" required="false">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>
        <!--BT-93 and BT-100-->
        <field path="BasisMonetaryRange/MonetaryLimit/MonetaryLimitValue"
            label="basis_monetary_range_monetary_limit_value" description="Monetary limit value">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />

                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition
                        expression="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />
                </constraint>
            </constraints>
        </field>
    </section>
    <!--BT-96 and BT-103-->
    <section label="alloworcharge"
        step="InvoiceAllowancesOrCharges/AllowOrCharge/Tax[TaxTypeCoded='ValueAddedTax' and TaxFunctionQualifierCoded='Tax']"
        description="validation of Invoice Allowances or Charges " required="false">
        <field path="TaxPercent" description="Taux de TVA appliqué" label="vat_rate">
            <constraints>
                <constraint legislation="EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <!-- Rule G6.10 -->
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge[IndicatorCoded='Allowance' or IndicatorCoded='Charge']" /> <!-- BG-20 or BG-21 -->
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InDecimalListConstraint">
                    <arg value="VAT rate-percent" />
                    <precondition
                        expression="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/Tax[TaxTypeCoded='ValueAddedTax' and TaxFunctionQualifierCoded='Tax']" />
                </constraint>
            </constraints>
        </field>
    </section>
    <!--BT-95 && BT-102-->
    <!--RULE G6.12-->
    <section label="taxfunction"
        step="InvoiceAllowancesOrCharges/AllowOrCharge/Tax[TaxFunctionQualifierCoded='Tax' and TaxCategoryCoded='Other' and TaxTypeCoded='ValueAddedTax']"
        description="validation of VAT type" required="false">
        <field path="TaxCategoryCodedOther " description="Type of VAT" label="vat_type">
            <constraints>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="S" />
                    <arg value="Z" />
                    <arg value="E" />
                    <arg value="AE" />
                    <arg value="K" />
                    <arg value="G" />
                    <arg value="O" />
                    <arg value="L" />
                    <arg value="M" />
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="InvoiceAllowancesOrCharges/AllowOrCharge/Tax[TaxFunctionQualifierCoded='Tax' and TaxCategoryCoded='Other' and TaxTypeCoded='ValueAddedTax']/TaxCategoryCodedOther" />
                </constraint>

            </constraints>
        </field>

    </section>

    <section step="ListOfStructuredNote" label="list_of_structured_notes" description="validation of invoice note" required="false">
        <!-- BG-1 -->
        <field path="StructuredNote[TextTypeCoded='Other']/TextTypeCodedOther" label="coded_other">
            <!-- BT-21 -->
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceHeader/InvoicePaymentInstructions/PaymentTerms/PaymentTerm[PaymentTermCoded='Discount' or PaymentTermCoded='LatePayment' or PaymentTermCoded='PenaltyTerms']" />
                    <precondition expression="StructuredNote[TextTypeCoded='Other']" />
                    <!--AIO-17664-->
                    <!--BT-22-->
                    <precondition expression="//InvoiceHeader/InvoicePaymentInstructions/PaymentTerms/PaymentTerm/PaymentTermDescription" />
                    <precondition expression="StructuredNote/GeneralNote" />
                </constraint>
                <constraint legislation="FR_B2B2024, FR_B2G2024"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceHeader/InvoicePaymentInstructions/PaymentTerms/PaymentTerm[PaymentTermCoded='Discount' or PaymentTermCoded='LatePayment' or PaymentTermCoded='PenaltyTerms']" />
                    <precondition expression="StructuredNote[TextTypeCoded='Other']" />
                </constraint>
                <!--RULE G1.52-->
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="StructuredNote[TextTypeCoded='Other']" />
                    <arg value="UNTDID4451" />
                </constraint>
            </constraints>
        </field>
        <field path="StructuredNote/GeneralNote" label="coded_other">
            <!-- BT-22 -->
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint legislation="FR_B2B2024, FR_B2G2024"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceHeader/InvoicePaymentInstructions/PaymentTerms/PaymentTerm/PaymentTermDescription" />
                    <precondition expression="//InvoiceHeader/ListOfStructuredNote/StructuredNote" />
                </constraint>
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceHeader/InvoicePaymentInstructions/PaymentTerms/PaymentTerm/PaymentTermDescription" />
                    <precondition expression="//InvoiceHeader/ListOfStructuredNote/StructuredNote" />
                    <!--AIO-17664-->
                    <!--BT-21-->
                    <precondition expression="//InvoiceHeader/InvoicePaymentInstructions/PaymentTerms/PaymentTerm[PaymentTermCoded='Discount' or PaymentTermCoded='LatePayment' or PaymentTermCoded='PenaltyTerms']" />
                    <precondition expression="StructuredNote[TextTypeCoded='Other']/TextTypeCodedOther" />
                </constraint>
            </constraints>
        </field>
    </section>
    <section step="ListOfAttachment/Attachment" label="list_of_attachment" required="false">
        <!--BT-125-2-->
        <!--rule G4.16, rule G4.15-->
        <field path="FileName" label="file_name" description="File name of the attached document">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="Attachment/FileName" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="FileExtensions" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueContentConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.CharactersNotAccepted" />
            </constraints>
        </field>
        <!--BT-125-1-->
        <!--rule G4.17-->
        <field path="MIMEType" label="MIME_Type" description="MIME code of the attached document">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="Attachment/MIMEType" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="MimeTypes" />
                </constraint>
            </constraints>
        </field>
    </section>
</rules>