<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="author">Generix Team</property>
    <property name="comments">Generix Orders for GCI B2B Portal</property>
    <property name="createdBy">Eclipse BIRT Designer Version 4.6.0.v201606072122</property>
    <text-property name="title">Order response</text-property>
    <html-property name="description">Generix Orders for GCI B2B Portal</html-property>
    <simple-property-list name="includeResource">
        <value>ordrsp</value>
    </simple-property-list>
    <list-property name="propertyBindings">
        <structure>
            <property name="name">FILELIST</property>
            <property name="id">314</property>
            <expression name="value" type="javascript">params["uri"].value</expression>
        </structure>
    </list-property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="layoutPreference">auto layout</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="uri" id="359">
            <property name="hidden">true</property>
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">ordrsp.xml</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.datatools.enablement.oda.xml" name="XML Ordrsp" id="314">
            <property name="FILELIST">ordrsp.xml</property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="Ordrsp-x" id="476">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">SellerOrderResponseNumber</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerOrderResponseNumber</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerOrderResponseNumber</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerOrderResponseNumber</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderResponseIssueDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderResponseIssueDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderResponseDocTypeCoded</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderResponseDocTypeCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrdersRefNum</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrdersRefNum</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderRefDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderRefDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerIdent</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerIdent</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerName1</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerName1</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerName2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">SellerName2</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerName3</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">SellerName3</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerStreet</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerStreet</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerStreetSupplement1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">SellerStreetSupplement1</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerStreetSupplement2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">SellerStreetSupplement2</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerPostalCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">SellerPostalCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerCity</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerCity</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerCountryCoded</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerCountryCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerContactName</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerContactName</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerIdent</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerIdent</text-property>
                </structure>
                <structure>
                    <property name="columnName">Buyer:Name1</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">Buyer:Name1</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerName2</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerName2</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerName3</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerName3</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerStreet</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerStreet</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerStreetSupplement1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerStreetSupplement1</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerStreetSupplement2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerStreetSupplement2</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerPostalCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerPostalCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerCity</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerCity</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerCountry</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerCountry</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerContactName</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerContactName</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrdersRequestedDeliverByDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrdersRequestedDeliverByDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderPaymentTermCoded</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderPaymentTermCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderCurrencyCoded</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderCurrencyCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToIdent</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToIdent</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToName1</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToName1</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToName2</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToName2</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToName3</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToName3</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToStreet</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToStreet</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToStreetSupplement1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">ShipToStreetSupplement1</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToStreetSupplement2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">ShipToStreetSupplement2</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToPostalCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">ShipToPostalCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToCity</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToCity</text-property>
                </structure>
                <structure>
                    <property name="columnName">ShipToCountry</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ShipToCountry</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">SellerOrderResponseNumber</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">BuyerOrderResponseNumber</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">OrderResponseIssueDate</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">OrderResponseDocTypeCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">OrdersRefNum</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">OrderRefDate</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">SellerIdent</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">SellerName1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">SellerName2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">SellerName3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">SellerStreet</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">SellerStreetSupplement1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">SellerStreetSupplement2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">SellerPostalCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">SellerCity</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">SellerCountryCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">SellerContactName</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">BuyerIdent</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">Buyer:Name1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">BuyerName2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">BuyerName3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">BuyerStreet</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">BuyerStreetSupplement1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">BuyerStreetSupplement2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">BuyerPostalCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">BuyerCity</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">BuyerCountry</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">BuyerContactName</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">OrdersRequestedDeliverByDate</property>
                        <property name="dataType">date-time</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">OrderPaymentTermCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">OrderCurrencyCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">ShipToIdent</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">ShipToName1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">ShipToName2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">ShipToName3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">ShipToStreet</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">ShipToStreetSupplement1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">ShipToStreetSupplement2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">39</property>
                        <property name="name">ShipToPostalCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">40</property>
                        <property name="name">ShipToCity</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">41</property>
                        <property name="name">ShipToCountry</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">SellerOrderResponseNumber</property>
                    <property name="nativeName">SellerOrderResponseNumber</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">BuyerOrderResponseNumber</property>
                    <property name="nativeName">BuyerOrderResponseNumber</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">OrderResponseIssueDate</property>
                    <property name="nativeName">OrderResponseIssueDate</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">OrderResponseDocTypeCoded</property>
                    <property name="nativeName">OrderResponseDocTypeCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">OrdersRefNum</property>
                    <property name="nativeName">OrdersRefNum</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">OrderRefDate</property>
                    <property name="nativeName">OrderRefDate</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">SellerIdent</property>
                    <property name="nativeName">SellerIdent</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">SellerName1</property>
                    <property name="nativeName">SellerName1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">SellerName2</property>
                    <property name="nativeName">SellerName2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">SellerName3</property>
                    <property name="nativeName">SellerName3</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">SellerStreet</property>
                    <property name="nativeName">SellerStreet</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">SellerStreetSupplement1</property>
                    <property name="nativeName">SellerStreetSupplement1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">SellerStreetSupplement2</property>
                    <property name="nativeName">SellerStreetSupplement2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">SellerPostalCode</property>
                    <property name="nativeName">SellerPostalCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">SellerCity</property>
                    <property name="nativeName">SellerCity</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">SellerCountryCoded</property>
                    <property name="nativeName">SellerCountryCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">SellerContactName</property>
                    <property name="nativeName">SellerContactName</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">BuyerIdent</property>
                    <property name="nativeName">BuyerIdent</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">Buyer:Name1</property>
                    <property name="nativeName">Buyer:Name1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">BuyerName2</property>
                    <property name="nativeName">BuyerName2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">BuyerName3</property>
                    <property name="nativeName">BuyerName3</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">BuyerStreet</property>
                    <property name="nativeName">BuyerStreet</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">BuyerStreetSupplement1</property>
                    <property name="nativeName">BuyerStreetSupplement1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">BuyerStreetSupplement2</property>
                    <property name="nativeName">BuyerStreetSupplement2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">BuyerPostalCode</property>
                    <property name="nativeName">BuyerPostalCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">BuyerCity</property>
                    <property name="nativeName">BuyerCity</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">BuyerCountry</property>
                    <property name="nativeName">BuyerCountry</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">BuyerContactName</property>
                    <property name="nativeName">BuyerContactName</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">OrdersRequestedDeliverByDate</property>
                    <property name="nativeName">OrdersRequestedDeliverByDate</property>
                    <property name="dataType">date-time</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">OrderPaymentTermCoded</property>
                    <property name="nativeName">OrderPaymentTermCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">OrderCurrencyCoded</property>
                    <property name="nativeName">OrderCurrencyCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">ShipToIdent</property>
                    <property name="nativeName">ShipToIdent</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">ShipToName1</property>
                    <property name="nativeName">ShipToName1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">ShipToName2</property>
                    <property name="nativeName">ShipToName2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">ShipToName3</property>
                    <property name="nativeName">ShipToName3</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">ShipToStreet</property>
                    <property name="nativeName">ShipToStreet</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">ShipToStreetSupplement1</property>
                    <property name="nativeName">ShipToStreetSupplement1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">ShipToStreetSupplement2</property>
                    <property name="nativeName">ShipToStreetSupplement2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">39</property>
                    <property name="name">ShipToPostalCode</property>
                    <property name="nativeName">ShipToPostalCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">40</property>
                    <property name="name">ShipToCity</property>
                    <property name="nativeName">ShipToCity</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">41</property>
                    <property name="name">ShipToCountry</property>
                    <property name="nativeName">ShipToCountry</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseHeader]#:#{SellerOrderResponseNumber;STRING;/OrderResponseNumber/SellerOrderResponseNumber},{BuyerOrderResponseNumber;STRING;/OrderResponseNumber/BuyerOrderResponseNumber},{OrderResponseIssueDate;TIMESTAMP;/OrderResponseIssueDate},{OrderResponseDocTypeCoded;STRING;/OrderResponseDocTypeCoded},{OrdersRefNum;STRING;/OrderReference/core:RefNum},{OrderRefDate;TIMESTAMP;/OrderReference/core:RefDate},{SellerIdent;STRING;/SellerParty/core:PartyID/core:Ident},{SellerName1;STRING;/SellerParty/core:NameAddress/core:Name1},{SellerName2;STRING;/SellerParty/core:NameAddress/core:Name2},{SellerName3;STRING;/SellerParty/core:NameAddress/core:Name3},{SellerStreet;STRING;/SellerParty/core:NameAddress/core:Street},{SellerStreetSupplement1;STRING;/SellerParty/core:NameAddress/core:StreetSupplement1},{SellerStreetSupplement2;STRING;/SellerParty/core:NameAddress/core:StreetSupplement2},{SellerPostalCode;STRING;/SellerParty/core:NameAddress/core:PostalCode},{SellerCity;STRING;/SellerParty/core:NameAddress/core:City},{SellerCountryCoded;STRING;/SellerParty/core:NameAddress/core:Country/core:CountryCoded},{SellerContactName;STRING;/SellerParty/core:PrimaryContact/core:ContactName},{BuyerIdent;STRING;/BuyerParty/core:PartyID/core:Ident},{Buyer:Name1;STRING;/BuyerParty/core:NameAddress/core:Name1},{BuyerName2;STRING;/BuyerParty/core:NameAddress/core:Name2},{BuyerName3;STRING;/BuyerParty/core:NameAddress/core:Name3},{BuyerStreet;STRING;/BuyerParty/core:NameAddress/core:Street},{BuyerStreetSupplement1;STRING;/BuyerParty/core:NameAddress/core:StreetSupplement1},{BuyerStreetSupplement2;STRING;/BuyerParty/core:NameAddress/core:StreetSupplement2},{BuyerPostalCode;STRING;/BuyerParty/core:NameAddress/core:PostalCode},{BuyerCity;STRING;/BuyerParty/core:NameAddress/core:City},{BuyerCountry;STRING;/BuyerParty/core:NameAddress/core:Country/core:CountryCoded},{BuyerContactName;STRING;/BuyerParty/core:PrimaryContact/core:ContactName},{OrdersRequestedDeliverByDate;TIMESTAMP;/OriginalOrderHeaderWithChanges/OrderDates/RequestedDeliverByDate},{OrderPaymentTermCoded;STRING;/OriginalOrderHeaderWithChanges/OrderPaymentInstructions/core:PaymentTerms/core:PaymentTerm/core:PaymentTermCoded},{OrderCurrencyCoded;STRING;/OriginalOrderHeaderWithChanges/OrderCurrency/core:CurrencyCoded},{ShipToIdent;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:PartyID/core:Ident},{ShipToName1;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:Name1},{ShipToName2;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:Name2},{ShipToName3;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:Name3},{ShipToStreet;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:Street},{ShipToStreetSupplement1;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:StreetSupplement1},{ShipToStreetSupplement2;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:StreetSupplement2},{ShipToPostalCode;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:PostalCode},{ShipToCity;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:City},{ShipToCountry;STRING;/OriginalOrderHeaderWithChanges/OrderParty/ShipToParty/core:NameAddress/core:Country/core:CountryCoded}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerOrderResponseNumber</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerOrderResponseNumber</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>OrderResponseIssueDate</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>OrderResponseIssueDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>OrderResponseDocTypeCoded</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>OrderResponseDocTypeCoded</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>OrdersRefNum</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>OrdersRefNum</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>OrderRefDate</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>OrderRefDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerIdent</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerIdent</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerName1</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerName1</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerStreet</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerStreet</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerCity</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerCity</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerCountryCoded</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerCountryCoded</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SellerContactName</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SellerContactName</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerIdent</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerIdent</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>Buyer:Name1</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>Buyer:Name1</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerName2</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerName2</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerName3</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerName3</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerStreet</design:name>
              <design:position>16</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerStreet</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerCity</design:name>
              <design:position>17</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerCity</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerCountry</design:name>
              <design:position>18</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerCountry</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerContactName</design:name>
              <design:position>19</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerContactName</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="OrdspLine-x" id="499">
            <list-property name="computedColumns">
                <structure>
                    <property name="name">PriceCalculationNet</property>
                    <expression name="expression">if      (row["PriceTypeCoded_1"] != null &amp;&amp; row["PriceTypeCoded_1"] == "CalculationNet")&#13;
  CalculationNet = row["UnitPriceValue_1"]&#13;
else if (row["PriceTypeCoded_2"] != null &amp;&amp; row["PriceTypeCoded_2"] == "CalculationNet")&#13;
  CalculationNet = row["UnitPriceValue_2"]&#13;
else if (row["PriceTypeCoded_3"] != null &amp;&amp; row["PriceTypeCoded_3"] == "CalculationNet")&#13;
  CalculationNet = row["UnitPriceValue_3"]&#13;
else if (row["PriceTypeCoded_4"] != null &amp;&amp; row["PriceTypeCoded_4"] == "CalculationNet")&#13;
  CalculationNet = row["UnitPriceValue_4"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">PriceCalculationGross</property>
                    <expression name="expression">if      (row["PriceTypeCoded_1"] != null &amp;&amp; row["PriceTypeCoded_1"] == "CalculationGross")&#13;
  CalculationGross = row["UnitPriceValue_1"]&#13;
else if (row["PriceTypeCoded_2"] != null &amp;&amp; row["PriceTypeCoded_2"] == "CalculationGross")&#13;
  CalculationGross = row["UnitPriceValue_2"]&#13;
else if (row["PriceTypeCoded_3"] != null &amp;&amp; row["PriceTypeCoded_3"] == "CalculationGross")&#13;
  CalculationGross = row["UnitPriceValue_3"]&#13;
else if (row["PriceTypeCoded_4"] != null &amp;&amp; row["PriceTypeCoded_4"] == "CalculationGross")&#13;
  CalculationGross = row["UnitPriceValue_4"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">hasSplitQty</property>
                    <expression name="expression">row["SplitQuantityValue"] != null</expression>
                    <property name="dataType">boolean</property>
                </structure>
                <structure>
                    <property name="name">hasAllowanceOrCharge</property>
                    <expression name="expression">row["ACIndicatorCoded"] != null</expression>
                    <property name="dataType">boolean</property>
                </structure>
            </list-property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">BuyerLineItemNum</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerLineItemNum</text-property>
                </structure>
                <structure>
                    <property name="columnName">SellerPartID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">SellerPartID</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerPartID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">BuyerPartID</text-property>
                </structure>
                <structure>
                    <property name="columnName">StandardProductIdentifier</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">StandardProductIdentifier</text-property>
                </structure>
                <structure>
                    <property name="columnName">ItemDescription</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ItemDescription</text-property>
                </structure>
                <structure>
                    <property name="columnName">ReponseQuantityValue</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ReponseQuantityValue</text-property>
                </structure>
                <structure>
                    <property name="columnName">ReponseUOMCoded</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ReponseUOMCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderQuantityValue</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderQuantityValue</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrderUOMCoded</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrderUOMCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">OrdersUnitPriceValue</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">OrdersUnitPriceValue</text-property>
                </structure>
                <structure>
                    <property name="columnName">PriceTypeCoded_1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">PriceTypeCoded_1</text-property>
                </structure>
                <structure>
                    <property name="columnName">UnitPriceValue_1</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">UnitPriceValue_1</text-property>
                </structure>
                <structure>
                    <property name="columnName">PriceTypeCoded_2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">PriceTypeCoded_2</text-property>
                </structure>
                <structure>
                    <property name="columnName">UnitPriceValue_2</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">UnitPriceValue_2</text-property>
                </structure>
                <structure>
                    <property name="columnName">PriceTypeCoded_3</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">PriceTypeCoded_3</text-property>
                </structure>
                <structure>
                    <property name="columnName">UnitPriceValue_3</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">UnitPriceValue_3</text-property>
                </structure>
                <structure>
                    <property name="columnName">PriceTypeCoded_4</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">PriceTypeCoded_4</text-property>
                </structure>
                <structure>
                    <property name="columnName">UnitPriceValue_4</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">UnitPriceValue_4</text-property>
                </structure>
                <structure>
                    <property name="columnName">LineItemTotal</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">LineItemTotal</text-property>
                </structure>
                <structure>
                    <property name="columnName">SplitQuantityValue</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">SplitQuantityValue</text-property>
                </structure>
                <structure>
                    <property name="columnName">ACIndicatorCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">ACIndicatorCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">hdrRequestedDeliverByDate</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">hdrRequestedDeliverByDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">sumTotalMonetaryAmount</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">sumTotalMonetaryAmount</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">BuyerLineItemNum</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">SellerPartID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">BuyerPartID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">StandardProductIdentifier</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">ItemDescription</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">ReponseQuantityValue</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">ReponseUOMCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">OrderQuantityValue</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">OrderUOMCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">OrdersUnitPriceValue</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">PriceTypeCoded_1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">UnitPriceValue_1</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">PriceTypeCoded_2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">UnitPriceValue_2</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">PriceTypeCoded_3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">UnitPriceValue_3</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">PriceTypeCoded_4</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">UnitPriceValue_4</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">LineItemTotal</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">SplitQuantityValue</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">ACIndicatorCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">hdrRequestedDeliverByDate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">sumTotalMonetaryAmount</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">PriceCalculationNet</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">PriceCalculationGross</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">hasSplitQty</property>
                        <property name="dataType">boolean</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">hasAllowanceOrCharge</property>
                        <property name="dataType">boolean</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">BuyerLineItemNum</property>
                    <property name="nativeName">BuyerLineItemNum</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">SellerPartID</property>
                    <property name="nativeName">SellerPartID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">BuyerPartID</property>
                    <property name="nativeName">BuyerPartID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">StandardProductIdentifier</property>
                    <property name="nativeName">StandardProductIdentifier</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">ItemDescription</property>
                    <property name="nativeName">ItemDescription</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">ReponseQuantityValue</property>
                    <property name="nativeName">ReponseQuantityValue</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">ReponseUOMCoded</property>
                    <property name="nativeName">ReponseUOMCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">OrderQuantityValue</property>
                    <property name="nativeName">OrderQuantityValue</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">OrderUOMCoded</property>
                    <property name="nativeName">OrderUOMCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">OrdersUnitPriceValue</property>
                    <property name="nativeName">OrdersUnitPriceValue</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">PriceTypeCoded_1</property>
                    <property name="nativeName">PriceTypeCoded_1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">UnitPriceValue_1</property>
                    <property name="nativeName">UnitPriceValue_1</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">PriceTypeCoded_2</property>
                    <property name="nativeName">PriceTypeCoded_2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">UnitPriceValue_2</property>
                    <property name="nativeName">UnitPriceValue_2</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">PriceTypeCoded_3</property>
                    <property name="nativeName">PriceTypeCoded_3</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">UnitPriceValue_3</property>
                    <property name="nativeName">UnitPriceValue_3</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">PriceTypeCoded_4</property>
                    <property name="nativeName">PriceTypeCoded_4</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">UnitPriceValue_4</property>
                    <property name="nativeName">UnitPriceValue_4</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">LineItemTotal</property>
                    <property name="nativeName">LineItemTotal</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">SplitQuantityValue</property>
                    <property name="nativeName">SplitQuantityValue</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">ACIndicatorCoded</property>
                    <property name="nativeName">ACIndicatorCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">hdrRequestedDeliverByDate</property>
                    <property name="nativeName">hdrRequestedDeliverByDate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">91</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">sumTotalMonetaryAmount</property>
                    <property name="nativeName">sumTotalMonetaryAmount</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseDetail/ListOfOrderResponseItemDetail/OrderResponseItemDetail]#:#{BuyerLineItemNum;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/LineItemNum/core:BuyerLineItemNum},{SellerPartID;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:PartID},{BuyerPartID;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:PartID},{StandardProductIdentifier;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/ItemIdentifiers/core:PartNumbers/core:StandardPartNumber/core:ProductIdentifier},{ItemDescription;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/ItemIdentifiers/core:ItemDescription},{ReponseQuantityValue;DOUBLE;/OriginalItemDetailWithChanges/BaseItemDetail/ResponseQuantity/core:QuantityValue},{ReponseUOMCoded;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/ResponseQuantity/core:UnitOfMeasurement/core:UOMCoded},{OrderQuantityValue;DOUBLE;/OriginalItemDetailWithChanges/BaseItemDetail/OrderedQuantity/core:QuantityValue},{OrderUOMCoded;STRING;/OriginalItemDetailWithChanges/BaseItemDetail/OrderedQuantity/core:UnitOfMeasurement/core:UOMCoded},{OrdersUnitPriceValue;STRING;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price/core:UnitPrice/core:UnitPriceValue},{PriceTypeCoded_1;STRING;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[1]/core:PricingType/core:PriceTypeCoded},{UnitPriceValue_1;BIGDECIMAL;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[1]/core:UnitPrice/core:UnitPriceValue},{PriceTypeCoded_2;STRING;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[2]/core:PricingType/core:PriceTypeCoded},{UnitPriceValue_2;BIGDECIMAL;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[2]/core:UnitPrice/core:UnitPriceValue},{PriceTypeCoded_3;STRING;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[3]/core:PricingType/core:PriceTypeCoded},{UnitPriceValue_3;BIGDECIMAL;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[3]/core:UnitPrice/core:UnitPriceValue},{PriceTypeCoded_4;STRING;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[4]/core:PricingType/core:PriceTypeCoded},{UnitPriceValue_4;BIGDECIMAL;/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price[4]/core:UnitPrice/core:UnitPriceValue},{LineItemTotal;BIGDECIMAL;/OriginalItemDetailWithChanges/PricingDetail/core:LineItemTotal/core:MonetaryAmount},{SplitQuantityValue;DOUBLE;/OriginalItemDetailWithChanges/DeliveryDetail/ListOfSplitQuantity/SplitQuantity/SplitQuantity/core:QuantityValue},{ACIndicatorCoded;STRING;/OriginalItemDetailWithChanges/PricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge/core:IndicatorCoded},{hdrRequestedDeliverByDate;DATE;../../../OrderResponseHeader/OriginalOrderHeaderWithChanges/OrderDates/RequestedDeliverByDate},{sumTotalMonetaryAmount;BIGDECIMAL;../../../OrderResponseSummary/RevisedOrderSummary/OrderTotal/core:MonetaryAmount}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>LineItemNum</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>LineItemNum</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="OrdrspLineListOfPrice" id="10008">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">PriceTypeCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">PriceTypeCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">UnitPriceValue</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">UnitPriceValue</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerLineItemNum</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerLineItemNum</text-property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">PriceTypeCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">UnitPriceValue</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">BuyerLineItemNum</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">PriceTypeCoded</property>
                    <property name="nativeName">PriceTypeCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">UnitPriceValue</property>
                    <property name="nativeName">UnitPriceValue</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">BuyerLineItemNum</property>
                    <property name="nativeName">BuyerLineItemNum</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseDetail/ListOfOrderResponseItemDetail/OrderResponseItemDetail/OriginalItemDetailWithChanges/PricingDetail/core:ListOfPrice/core:Price]#:#{PriceTypeCoded;STRING;/core:PricingType/core:PriceTypeCoded},{UnitPriceValue;BIGDECIMAL;/core:UnitPrice/core:UnitPriceValue},{BuyerLineItemNum;STRING;../../../BaseItemDetail/LineItemNum/core:BuyerLineItemNum}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>PriceTypeCoded</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>PriceTypeCoded</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>UnitPriceValue</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>UnitPriceValue</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerLineItemNum</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerLineItemNum</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="OrdrspLineListOfSplitQuantity" id="10031">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">SplitQuantityValue</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">SplitQuantityValue</text-property>
                </structure>
                <structure>
                    <property name="columnName">SplitEstimatedDeliveryDate</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">SplitEstimatedDeliveryDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerLineItemNum</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerLineItemNum</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">SplitQuantityValue</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">SplitEstimatedDeliveryDate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">BuyerLineItemNum</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">SplitQuantityValue</property>
                    <property name="nativeName">SplitQuantityValue</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">SplitEstimatedDeliveryDate</property>
                    <property name="nativeName">SplitEstimatedDeliveryDate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">91</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">BuyerLineItemNum</property>
                    <property name="nativeName">BuyerLineItemNum</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseDetail/ListOfOrderResponseItemDetail/OrderResponseItemDetail/OriginalItemDetailWithChanges/DeliveryDetail/ListOfSplitQuantity/SplitQuantity]#:#{SplitQuantityValue;DOUBLE;/SplitQuantity/core:QuantityValue},{SplitEstimatedDeliveryDate;DATE;/EstimatedDeliveryDate},{BuyerLineItemNum;STRING;../../../BaseItemDetail/LineItemNum/core:BuyerLineItemNum}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SplitQuantityValue</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>8</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SplitQuantityValue</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SplitEstimatedDeliveryDate</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>91</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>SplitEstimatedDeliveryDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerLineItemNum</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerLineItemNum</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="OrdrspLineListOfStructuredNote" id="10075">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">NoteID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">NoteID</text-property>
                </structure>
                <structure>
                    <property name="columnName">TextTypeCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">TextTypeCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">TextTypeCodedOther</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">TextTypeCodedOther</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerLineItemNum</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerLineItemNum</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">NoteID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">TextTypeCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">TextTypeCodedOther</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">BuyerLineItemNum</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">NoteID</property>
                    <property name="nativeName">NoteID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">TextTypeCoded</property>
                    <property name="nativeName">TextTypeCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">TextTypeCodedOther</property>
                    <property name="nativeName">TextTypeCodedOther</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">BuyerLineItemNum</property>
                    <property name="nativeName">BuyerLineItemNum</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseDetail/ListOfOrderResponseItemDetail/OrderResponseItemDetail/OriginalItemDetailWithChanges/ListOfStructuredNote/core:StructuredNote]#:#{NoteID;STRING;/core:NoteID},{TextTypeCoded;STRING;/core:TextTypeCoded},{TextTypeCodedOther;STRING;/core:TextTypeCodedOther},{BuyerLineItemNum;STRING;../../BaseItemDetail/LineItemNum/core:BuyerLineItemNum}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>ItemDetailResponseCoded</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>ItemDetailResponseCoded</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="OrdrspLineItemAllowancesOrCharges" id="10451">
            <list-property name="computedColumns">
                <structure>
                    <property name="name">ToBePrinted</property>
                    <expression name="expression">if (row["IndicatorCoded"] == "LineItemAllowance")&#13;
  ToBePrinted = "-"&#13;
else&#13;
  ToBePrinted = "+"&#13;
&#13;
if (row["Percent"])&#13;
  if (row["Percent"] != "0")&#13;
    ToBePrinted = ToBePrinted + row["Percent"] + "%"&#13;
  else&#13;
    if (row["PercentageMonetaryAmount"])&#13;
      ToBePrinted = ToBePrinted + row["PercentageMonetaryAmount"]&#13;
    else&#13;
      ToBePrinted = ""    &#13;
else&#13;
  if (row["MonetaryAmount"])&#13;
  {&#13;
    ToBePrinted = ToBePrinted + row["MonetaryAmount"];&#13;
    &#13;
    if (row["CurrencyCoded"])&#13;
      ToBePrinted = ToBePrinted + " " + row["CurrencyCoded"]&#13;
  }&#13;
  else&#13;
    ToBePrinted = ""&#13;
&#13;
//if (ToBePrinted != "" &amp;&amp; reportContext.getLocale().toString().toLocaleUpperCase().indexOf("FR") != -1)&#13;
//  ToBePrinted = ToBePrinted.replace(".",",")&#13;
&#13;
ToBePrinted</expression>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">IndicatorCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">IndicatorCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">BasisCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BasisCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">MethodOfHandlingCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">MethodOfHandlingCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">ServiceCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">ServiceCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">Description</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">Description</text-property>
                </structure>
                <structure>
                    <property name="columnName">MonetaryAmount</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">MonetaryAmount</text-property>
                </structure>
                <structure>
                    <property name="columnName">CurrencyCoded</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">CurrencyCoded</text-property>
                </structure>
                <structure>
                    <property name="columnName">Percent</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">Percent</text-property>
                </structure>
                <structure>
                    <property name="columnName">BuyerLineItemNum</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">BuyerLineItemNum</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">IndicatorCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">BasisCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">MethodOfHandlingCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">ServiceCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">Description</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">MonetaryAmount</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">CurrencyCoded</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">Percent</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">BuyerLineItemNum</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">ToBePrinted</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">IndicatorCoded</property>
                    <property name="nativeName">IndicatorCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">BasisCoded</property>
                    <property name="nativeName">BasisCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">MethodOfHandlingCoded</property>
                    <property name="nativeName">MethodOfHandlingCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">ServiceCoded</property>
                    <property name="nativeName">ServiceCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">Description</property>
                    <property name="nativeName">Description</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">MonetaryAmount</property>
                    <property name="nativeName">MonetaryAmount</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">CurrencyCoded</property>
                    <property name="nativeName">CurrencyCoded</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">Percent</property>
                    <property name="nativeName">Percent</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">BuyerLineItemNum</property>
                    <property name="nativeName">BuyerLineItemNum</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseDetail/ListOfOrderResponseItemDetail/OrderResponseItemDetail/OriginalItemDetailWithChanges/PricingDetail/core:ItemAllowancesOrCharges/core:AllowOrCharge]#:#{IndicatorCoded;STRING;/core:IndicatorCoded},{BasisCoded;STRING;/core:BasisCoded},{MethodOfHandlingCoded;STRING;/core:MethodOfHandlingCoded},{ServiceCoded;STRING;/core:AllowanceOrChargeDescription/core:ServiceCoded},{Description;STRING;/core:AllowanceOrChargeDescription/core:ListOfDescription},{MonetaryAmount;BIGDECIMAL;/core:TypeOfAllowanceOrCharge/core:MonetaryValue/core:MonetaryAmount},{CurrencyCoded;STRING;/core:TypeOfAllowanceOrCharge/core:MonetaryValue/core:Currency/core:CurrencyCoded},{Percent;BIGDECIMAL;/core:TypeOfAllowanceOrCharge/core:PercentageAllowanceOrCharge/core:Percent},{BuyerLineItemNum;STRING;../../../BaseItemDetail/LineItemNum/core:BuyerLineItemNum}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>Percent</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>Percent</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>BuyerLineItemNum</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>BuyerLineItemNum</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="OrdrspSummary-x" id="10487">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">TotalMonetaryAmount</property>
                    <text-property name="heading">TotalMonetaryAmount</text-property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">TotalMonetaryAmount</property>
                        <property name="dataType">decimal</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Ordrsp</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">TotalMonetaryAmount</property>
                    <property name="nativeName">TotalMonetaryAmount</property>
                    <property name="dataType">decimal</property>
                    <property name="nativeDataType">2</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/OrderResponse/OrderResponseSummary/RevisedOrderSummary]#:#{TotalMonetaryAmount;BIGDECIMAL;/OrderTotal/core:MonetaryAmount}#:#<"","rrn:org.xcbl:schemas%xcbl%v4_0%ordermanagement%v1_0%ordermanagement.xsd";"ns5","rrn:org.xcbl:schemas%xcbl%v4_0%materialsmanagement%v1_0%materialsmanagement.xsd";"core","rrn:org.xcbl:schemas%xcbl%v4_0%core%core.xsd";"dsg","http:%%www.w3.org%2000%09%xmldsig#";"ns4","rrn:org.xcbl:schemas%xcbl%v4_0%financial%v1_0%financial.xsd";"xsi","http:%%www.w3.org%2001%XMLSchema-instance">]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>TotalMonetaryAmount</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>2</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>TotalMonetaryAmount</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
    </data-sets>
    <styles>
        <style name="report" id="4">
            <property name="fontFamily">sans-serif</property>
            <property name="fontSize">10pt</property>
        </style>
        <style name="crosstab-cell" id="5">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
        <style name="crosstab" id="6">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <property name="type">a4</property>
            <property name="footerHeight">0.07in</property>
            <page-footer>
                <grid id="711">
                    <property name="width">100%</property>
                    <column id="712">
                        <property name="width">80%</property>
                    </column>
                    <column id="713">
                        <property name="width">20%</property>
                    </column>
                    <row id="714">
                        <cell id="715">
                            <text id="3">
                                <property name="fontFamily">"Calibri"</property>
                                <property name="fontSize">9pt</property>
                                <property name="fontStyle">italic</property>
                                <property name="marginTop">0pt</property>
                                <property name="marginLeft">0pt</property>
                                <property name="marginBottom">0pt</property>
                                <property name="marginRight">0pt</property>
                                <property name="display">inline</property>
                                <property name="contentType">html</property>
                                <text-property name="content"><![CDATA[Powered by Generix Group &amp; printed the <VALUE-OF format="dd/MM/YY HH:mm">new Date()</value-of> - V]]></text-property>
                            </text>
                            <text-data id="9909">
                                <property name="fontSize">8pt</property>
                                <property name="fontStyle">italic</property>
                                <property name="display">inline</property>
                                <expression name="valueExpr">vars["Version"].toString().replace(",",".") + " [" + reportContext.getLocale() + "]"</expression>
                                <property name="contentType">html</property>
                            </text-data>
                        </cell>
                        <cell id="716">
                            <property name="textAlign">right</property>
                            <grid id="8444">
                                <property name="fontSize">9pt</property>
                                <property name="textAlign">right</property>
                                <property name="width">2cm</property>
                                <column id="8445"/>
                                <column id="8446"/>
                                <column id="8447"/>
                                <row id="8448">
                                    <cell id="8449">
                                        <auto-text id="8450">
                                            <property name="type">page-number</property>
                                        </auto-text>
                                    </cell>
                                    <cell id="8451">
                                        <property name="textAlign">center</property>
                                        <text id="8452">
                                            <property name="contentType">plain</property>
                                            <text-property name="content"><![CDATA[/]]></text-property>
                                        </text>
                                    </cell>
                                    <cell id="8453">
                                        <auto-text id="8454">
                                            <property name="textAlign">left</property>
                                            <property name="type">total-page</property>
                                        </auto-text>
                                    </cell>
                                </row>
                            </grid>
                        </cell>
                    </row>
                </grid>
            </page-footer>
        </simple-master-page>
    </page-setup>
    <body>
        <grid name="Buyer_Other" id="664">
            <property name="fontSize">9pt</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">thin</property>
            <property name="width">100%</property>
            <column id="665">
                <property name="width">50%</property>
            </column>
            <column id="666">
                <property name="width">50%</property>
            </column>
            <row id="667">
                <cell id="668">
                    <grid name="Buyer" id="673">
                        <property name="width">100%</property>
                        <property name="dataSet">Ordrsp-x</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">BuyerPostalCode</property>
                                <text-property name="displayName">BuyerPostalCode</text-property>
                                <expression name="expression" type="javascript">dataSetRow["BuyerPostalCode"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">BuyerCity</property>
                                <text-property name="displayName">BuyerCity</text-property>
                                <expression name="expression" type="javascript">dataSetRow["BuyerCity"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">BuyerCountry</property>
                                <text-property name="displayName">BuyerCountry</text-property>
                                <expression name="expression" type="javascript">dataSetRow["BuyerCountry"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">BuyerContactName</property>
                                <text-property name="displayName">BuyerContactName</text-property>
                                <expression name="expression" type="javascript">dataSetRow["BuyerContactName"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <column id="674"/>
                        <row id="675">
                            <cell id="676">
                                <text id="693">
                                    <property name="fontWeight">bold</property>
                                    <property name="display">inline</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Acheteur:"><![CDATA[Acheteur:]]></text-property>
                                </text>
                                <data id="694">
                                    <property name="marginLeft">2pt</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerIdent</property>
                                            <text-property name="displayName">BuyerIdent</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerIdent"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerIdent</property>
                                </data>
                            </cell>
                        </row>
                        <row id="677">
                            <cell id="678">
                                <data id="695">
                                    <property name="fontWeight">normal</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">Buyer:Name1</property>
                                            <text-property name="displayName">Buyer:Name1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["Buyer:Name1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">Buyer:Name1</property>
                                </data>
                                <data id="696">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["BuyerName2"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerName2</property>
                                            <text-property name="displayName">BuyerName2</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerName2"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerName2</property>
                                </data>
                                <data id="697">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["BuyerName3"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerName3</property>
                                            <text-property name="displayName">BuyerName3</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerName3"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerName3</property>
                                </data>
                            </cell>
                        </row>
                        <row id="679">
                            <cell id="680">
                                <data id="698">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerStreet</property>
                                            <text-property name="displayName">BuyerStreet</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerStreet"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerStreet</property>
                                </data>
                                <data id="699">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["BuyerStreetSupplement1"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerStreetSupplement1</property>
                                            <text-property name="displayName">BuyerStreetSupplement1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerStreetSupplement1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerStreetSupplement1</property>
                                </data>
                                <data id="700">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["BuyerStreetSupplement2"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerStreetSupplement2</property>
                                            <text-property name="displayName">BuyerStreetSupplement2</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerStreetSupplement2"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerStreetSupplement2</property>
                                </data>
                            </cell>
                        </row>
                        <row id="681">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(row["BuyerPostalCode"] == null &amp;&amp; row["BuyerCity"] == null)</expression>
                                </structure>
                            </list-property>
                            <cell id="682">
                                <data id="701">
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerPostalCode</property>
                                            <text-property name="displayName">BuyerPostalCode</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerPostalCode"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerPostalCode</property>
                                </data>
                                <data id="480">
                                    <property name="marginLeft">2pt</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerCity</property>
                                            <text-property name="displayName">BuyerCity</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerCity"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerCity</property>
                                </data>
                            </cell>
                        </row>
                        <row id="683">
                            <cell id="684"/>
                        </row>
                        <row id="685">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">!row["BuyerCountry"]</expression>
                                </structure>
                            </list-property>
                            <cell id="686">
                                <data id="702">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">BuyerCountry</property>
                                            <text-property name="displayName">BuyerCountry</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["BuyerCountry"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">BuyerCountry</property>
                                </data>
                            </cell>
                        </row>
                        <row id="687">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">!row["BuyerContactName"]</expression>
                                </structure>
                            </list-property>
                            <cell id="688">
                                <grid name="BuyerContact" id="703">
                                    <column id="704"/>
                                    <column id="705"/>
                                    <row id="706">
                                        <cell id="707">
                                            <text id="709">
                                                <property name="contentType">auto</property>
                                                <text-property name="content" key="Contact:"><![CDATA[Contact:]]></text-property>
                                            </text>
                                        </cell>
                                        <cell id="708">
                                            <data id="710">
                                                <property name="dataSet">Ordrsp-x</property>
                                                <list-property name="boundDataColumns">
                                                    <structure>
                                                        <property name="name">BuyerContactName</property>
                                                        <text-property name="displayName">BuyerContactName</text-property>
                                                        <expression name="expression" type="javascript">dataSetRow["BuyerContactName"]</expression>
                                                        <property name="dataType">string</property>
                                                    </structure>
                                                </list-property>
                                                <property name="resultSetColumn">BuyerContactName</property>
                                            </data>
                                        </cell>
                                    </row>
                                </grid>
                            </cell>
                        </row>
                        <row id="689">
                            <cell id="690"/>
                        </row>
                        <row id="691">
                            <cell id="692"/>
                        </row>
                    </grid>
                </cell>
                <cell id="669">
                    <grid name="Other" id="9910">
                        <property name="width">100%</property>
                        <column id="9911">
                            <property name="width">50%</property>
                        </column>
                        <column id="9912">
                            <property name="width">50%</property>
                        </column>
                        <row id="9913">
                            <cell id="9914">
                                <text id="9934">
                                    <property name="fontWeight">bold</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Reponse commande"><![CDATA[Contact:]]></text-property>
                                </text>
                            </cell>
                            <cell id="9915">
                                <data id="9936">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerOrderResponseNumber</property>
                                            <text-property name="displayName">SellerOrderResponseNumber</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerOrderResponseNumber"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerOrderResponseNumber</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9916">
                            <cell id="9917">
                                <text id="9937">
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Date reponse commande"><![CDATA[Date de réponse à la commande:]]></text-property>
                                </text>
                            </cell>
                            <cell id="9918">
                                <data id="9938">
                                    <structure name="dateTimeFormat">
                                        <property name="category">Short Date</property>
                                        <property name="pattern">Short Date</property>
                                    </structure>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">OrderResponseIssueDate</property>
                                            <text-property name="displayName">OrderResponseIssueDate</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["OrderResponseIssueDate"]</expression>
                                            <property name="dataType">date-time</property>
                                            <property name="allowExport">true</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">OrderResponseIssueDate</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9919">
                            <cell id="9920">
                                <text id="9939">
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Num commande"><![CDATA[N° commande:]]></text-property>
                                </text>
                            </cell>
                            <cell id="9921">
                                <data id="9940">
                                    <property name="fontWeight">normal</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">OrdersRefNum</property>
                                            <text-property name="displayName">OrdersRefNum</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["OrdersRefNum"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">OrdersRefNum</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9922">
                            <cell id="9923">
                                <text id="9941">
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Date commande"><![CDATA[Date commande:]]></text-property>
                                </text>
                            </cell>
                            <cell id="9924">
                                <data id="9942">
                                    <structure name="dateTimeFormat">
                                        <property name="category">Short Date</property>
                                        <property name="pattern">Short Date</property>
                                    </structure>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">OrderRefDate</property>
                                            <text-property name="displayName">OrderRefDate</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["OrderRefDate"]</expression>
                                            <property name="dataType">date-time</property>
                                            <property name="allowExport">true</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">OrderRefDate</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9925">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">true</expression>
                                </structure>
                            </list-property>
                            <cell id="9926">
                                <text id="9943">
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Date livraison"><![CDATA[Date de livraison:]]></text-property>
                                </text>
                            </cell>
                            <cell id="9927">
                                <data id="9944">
                                    <structure name="dateTimeFormat">
                                        <property name="category">Short Date</property>
                                        <property name="pattern">Short Date</property>
                                    </structure>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">OrdersRequestedDeliverByDate</property>
                                            <text-property name="displayName">OrdersRequestedDeliverByDate</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["OrdersRequestedDeliverByDate"]</expression>
                                            <property name="dataType">date-time</property>
                                            <property name="allowExport">true</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">OrdersRequestedDeliverByDate</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9928">
                            <cell id="9929">
                                <text id="9945">
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Devise"><![CDATA[Devise:]]></text-property>
                                </text>
                            </cell>
                            <cell id="9930">
                                <data id="9946">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">OrderCurrencyCoded</property>
                                            <text-property name="displayName">OrderCurrencyCoded</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["OrderCurrencyCoded"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">OrderCurrencyCoded</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9931">
                            <cell id="9932"/>
                            <cell id="9933"/>
                        </row>
                    </grid>
                </cell>
            </row>
        </grid>
        <grid name="Seller_ShipTo" id="9947">
            <property name="fontSize">9pt</property>
            <property name="width">100%</property>
            <column id="9948">
                <property name="width">50%</property>
            </column>
            <column id="9949">
                <property name="width">50%</property>
            </column>
            <row id="9950">
                <cell id="9951">
                    <grid name="Seller" id="9956">
                        <property name="dataSet">Ordrsp-x</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">SellerPostalCode</property>
                                <text-property name="displayName">SellerPostalCode</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SellerPostalCode"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">SellerCity</property>
                                <text-property name="displayName">SellerCity</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SellerCity"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">SellerCountryCoded</property>
                                <text-property name="displayName">SellerCountryCoded</text-property>
                                <expression name="expression" type="javascript">dataSetRow["SellerCountryCoded"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <column id="9957"/>
                        <row id="9958">
                            <cell id="9959">
                                <text id="9970">
                                    <property name="fontWeight">bold</property>
                                    <property name="display">inline</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Vendeur"><![CDATA[Vendeur:]]></text-property>
                                </text>
                                <data id="9971">
                                    <property name="marginLeft">2pt</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerIdent</property>
                                            <text-property name="displayName">SellerIdent</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerIdent"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerIdent</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9960">
                            <cell id="9961">
                                <data id="9972">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerName1</property>
                                            <text-property name="displayName">SellerName1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerName1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerName1</property>
                                </data>
                                <data id="9973">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["SellerName2"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerName2</property>
                                            <text-property name="displayName">SellerName2</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerName2"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerName2</property>
                                </data>
                                <data id="9974">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["SellerName3"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerName3</property>
                                            <text-property name="displayName">SellerName3</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerName3"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerName3</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9962">
                            <cell id="9963">
                                <data id="9975">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerStreet</property>
                                            <text-property name="displayName">SellerStreet</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerStreet"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerStreet</property>
                                </data>
                                <data id="9976">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["SellerStreetSupplement1"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerStreetSupplement1</property>
                                            <text-property name="displayName">SellerStreetSupplement1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerStreetSupplement1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerStreetSupplement1</property>
                                </data>
                                <data id="9977">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["SellerStreetSupplement2"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerStreetSupplement2</property>
                                            <text-property name="displayName">SellerStreetSupplement2</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerStreetSupplement2"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerStreetSupplement2</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9964">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(row["SellerPostalCode"] == null &amp;&amp; row["SellerCity"] == null)</expression>
                                </structure>
                            </list-property>
                            <cell id="9965">
                                <data id="9978">
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerPostalCode</property>
                                            <text-property name="displayName">SellerPostalCode</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerPostalCode"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerPostalCode</property>
                                </data>
                                <data id="9979">
                                    <property name="marginLeft">2pt</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerCity</property>
                                            <text-property name="displayName">SellerCity</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerCity"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerCity</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9966">
                            <cell id="9967"/>
                        </row>
                        <row id="9968">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">!row["SellerCountryCoded"]</expression>
                                </structure>
                            </list-property>
                            <cell id="9969">
                                <data id="9980">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">SellerCountryCoded</property>
                                            <text-property name="displayName">SellerCountryCoded</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["SellerCountryCoded"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">SellerCountryCoded</property>
                                </data>
                            </cell>
                        </row>
                    </grid>
                </cell>
                <cell id="9952">
                    <grid name="ShipTo" id="9981">
                        <property name="dataSet">Ordrsp-x</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">ShipToPostalCode</property>
                                <text-property name="displayName">ShipToPostalCode</text-property>
                                <expression name="expression" type="javascript">dataSetRow["ShipToPostalCode"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">ShipToCity</property>
                                <text-property name="displayName">ShipToCity</text-property>
                                <expression name="expression" type="javascript">dataSetRow["ShipToCity"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                            <structure>
                                <property name="name">ShipToCountry</property>
                                <text-property name="displayName">ShipToCountry</text-property>
                                <expression name="expression" type="javascript">dataSetRow["ShipToCountry"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <column id="9982"/>
                        <row id="9983">
                            <cell id="9984">
                                <text id="9995">
                                    <property name="fontWeight">bold</property>
                                    <property name="display">inline</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content" key="Lieu de livraison"><![CDATA[Lieu de livraison:]]></text-property>
                                </text>
                                <data id="9996">
                                    <property name="marginLeft">2pt</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToIdent</property>
                                            <text-property name="displayName">ShipToIdent</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToIdent"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToIdent</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9985">
                            <cell id="9986">
                                <data id="9997">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToName1</property>
                                            <text-property name="displayName">ShipToName1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToName1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToName1</property>
                                </data>
                                <data id="10001">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["ShipToName2"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToName2</property>
                                            <text-property name="displayName">ShipToName2</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToName2"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToName2</property>
                                </data>
                                <data id="10002">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["ShipToName3"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToName3</property>
                                            <text-property name="displayName">ShipToName3</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToName3"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToName3</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9987">
                            <cell id="9988">
                                <data id="9998">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToStreet</property>
                                            <text-property name="displayName">ShipToStreet</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToStreet"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToStreet</property>
                                </data>
                                <data id="10004">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["ShipToStreetSupplement1"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToStreetSupplement1</property>
                                            <text-property name="displayName">ShipToStreetSupplement1</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToStreetSupplement1"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToStreetSupplement1</property>
                                </data>
                                <data id="10005">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="visibility">
                                        <structure>
                                            <property name="format">all</property>
                                            <expression name="valueExpr" type="javascript">!row["ShipToStreetSupplement2"]</expression>
                                        </structure>
                                    </list-property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToStreetSupplement2</property>
                                            <text-property name="displayName">ShipToStreetSupplement2</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToStreetSupplement2"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToStreetSupplement2</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9989">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(row["ShipToPostalCode"] == null &amp;&amp; row["ShipToCity"] == null)</expression>
                                </structure>
                            </list-property>
                            <cell id="9990">
                                <data id="10003">
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToPostalCode</property>
                                            <text-property name="displayName">ShipToPostalCode</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToPostalCode"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToPostalCode</property>
                                </data>
                                <data id="9999">
                                    <property name="marginLeft">2pt</property>
                                    <property name="display">inline</property>
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToCity</property>
                                            <text-property name="displayName">ShipToCity</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToCity"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToCity</property>
                                </data>
                            </cell>
                        </row>
                        <row id="9991">
                            <cell id="9992"/>
                        </row>
                        <row id="9993">
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">!row["ShipToCountry"]</expression>
                                </structure>
                            </list-property>
                            <cell id="9994">
                                <data id="10000">
                                    <property name="dataSet">Ordrsp-x</property>
                                    <list-property name="boundDataColumns">
                                        <structure>
                                            <property name="name">ShipToCountry</property>
                                            <text-property name="displayName">ShipToCountry</text-property>
                                            <expression name="expression" type="javascript">dataSetRow["ShipToCountry"]</expression>
                                            <property name="dataType">string</property>
                                        </structure>
                                    </list-property>
                                    <property name="resultSetColumn">ShipToCountry</property>
                                </data>
                            </cell>
                        </row>
                    </grid>
                </cell>
            </row>
            <row id="9953">
                <cell id="9954"/>
                <cell id="9955"/>
            </row>
        </grid>
        <grid id="587">
            <property name="fontSize">9pt</property>
            <column id="588"/>
            <column id="589"/>
            <column id="590"/>
            <column id="591"/>
            <row id="612">
                <property name="borderBottomStyle">solid</property>
                <property name="borderBottomWidth">thin</property>
                <cell id="613">
                    <text id="267">
                        <property name="contentType">auto</property>
                        <text-property name="content" key="Methode paiement"><![CDATA[Modalité de paiement:]]></text-property>
                    </text>
                </cell>
                <cell id="614">
                    <data id="487">
                        <property name="dataSet">Ordrsp-x</property>
                        <list-property name="boundDataColumns">
                            <structure>
                                <property name="name">OrderPaymentTermCoded</property>
                                <text-property name="displayName">OrderPaymentTermCoded</text-property>
                                <expression name="expression" type="javascript">dataSetRow["OrderPaymentTermCoded"]</expression>
                                <property name="dataType">string</property>
                            </structure>
                        </list-property>
                        <property name="resultSetColumn">OrderPaymentTermCoded</property>
                    </data>
                </cell>
                <cell id="615">
                    <text id="268">
                        <property name="borderBottomStyle">none</property>
                        <property name="borderLeftStyle">none</property>
                        <property name="borderRightStyle">none</property>
                        <property name="borderTopStyle">none</property>
                        <property name="contentType">auto</property>
                        <text-property name="content" key="Mode livraison"><![CDATA[Mode de livraison:]]></text-property>
                    </text>
                </cell>
                <cell id="616"/>
            </row>
        </grid>
        <grid id="578">
            <column id="579"/>
            <row id="580">
                <cell id="581">
                    <property name="fontSize">9pt</property>
                    <label id="584">
                        <property name="borderBottomStyle">none</property>
                        <property name="borderLeftStyle">none</property>
                        <property name="borderRightStyle">none</property>
                        <property name="borderTopStyle">none</property>
                        <text-property name="text">Note</text-property>
                    </label>
                </cell>
            </row>
            <row id="582">
                <property name="borderBottomStyle">solid</property>
                <property name="borderBottomWidth">thin</property>
                <cell id="583">
                    <text id="656">
                        <property name="contentType">auto</property>
                        <text-property name="content"><![CDATA[  ]]></text-property>
                    </text>
                </cell>
            </row>
            <row id="585">
                <cell id="586"/>
            </row>
        </grid>
        <table name="OrdrspLine-x" id="535">
            <property name="fontSize">11px</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">thin</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">thin</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">thin</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">thin</property>
            <property name="width">100%</property>
            <property name="dataSet">OrdspLine-x</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">BuyerLineItemNum</property>
                    <text-property name="displayName">BuyerLineItemNum</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BuyerLineItemNum"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">SellerPartID</property>
                    <text-property name="displayName">SellerPartID</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SellerPartID"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">BuyerPartID</property>
                    <text-property name="displayName">BuyerPartID</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BuyerPartID"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">ItemDescription</property>
                    <text-property name="displayName">ItemDescription</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ItemDescription"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">ReponseQuantityValue</property>
                    <text-property name="displayName">ReponseQuantityValue</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ReponseQuantityValue"]</expression>
                    <property name="dataType">float</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">ReponseUOMCoded</property>
                    <text-property name="displayName">ReponseUOMCoded</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ReponseUOMCoded"]</expression>
                    <property name="dataType">string</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">OrderQuantityValue</property>
                    <text-property name="displayName">OrderQuantityValue</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OrderQuantityValue"]</expression>
                    <property name="dataType">float</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">OrderUOMCoded</property>
                    <text-property name="displayName">OrderUOMCoded</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OrderUOMCoded"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">OrdersUnitPriceValue</property>
                    <text-property name="displayName">OrdersUnitPriceValue</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OrdersUnitPriceValue"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">StandardProductIdentifier</property>
                    <text-property name="displayName">StandardProductIdentifier</text-property>
                    <expression name="expression" type="javascript">dataSetRow["StandardProductIdentifier"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">hasSplitQty</property>
                    <text-property name="displayName">hasSplitQty</text-property>
                    <expression name="expression" type="javascript">dataSetRow["hasSplitQty"]</expression>
                    <property name="dataType">boolean</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">hdrRequestedDeliverByDate</property>
                    <text-property name="displayName">hdrRequestedDeliverByDate</text-property>
                    <expression name="expression" type="javascript">dataSetRow["hdrRequestedDeliverByDate"]</expression>
                    <property name="dataType">date</property>
                </structure>
                <structure>
                    <property name="name">LineItemTotal</property>
                    <text-property name="displayName">LineItemTotal</text-property>
                    <expression name="expression" type="javascript">dataSetRow["LineItemTotal"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">PriceCalculationNet</property>
                    <text-property name="displayName">PriceCalculationNet</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PriceCalculationNet"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">PriceCalculationGross</property>
                    <text-property name="displayName">PriceCalculationGross</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PriceCalculationGross"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">hasAllowanceOrCharge</property>
                    <text-property name="displayName">hasAllowanceOrCharge</text-property>
                    <expression name="expression" type="javascript">dataSetRow["hasAllowanceOrCharge"]</expression>
                    <property name="dataType">boolean</property>
                </structure>
                <structure>
                    <property name="name">sumTotalMonetaryAmount</property>
                    <text-property name="displayName">sumTotalMonetaryAmount</text-property>
                    <expression name="expression" type="javascript">dataSetRow["sumTotalMonetaryAmount"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <column id="548">
                <property name="width">4%</property>
            </column>
            <column id="549">
                <property name="width">13%</property>
            </column>
            <column id="550">
                <property name="width">15%</property>
            </column>
            <column id="557">
                <property name="width">30%</property>
            </column>
            <column id="561">
                <property name="width">6%</property>
            </column>
            <column id="565">
                <list-property name="visibility">
                    <structure>
                        <property name="format">all</property>
                        <expression name="valueExpr" type="javascript">true</expression>
                    </structure>
                </list-property>
                <property name="width">5%</property>
            </column>
            <column id="569">
                <property name="width">9%</property>
            </column>
            <column id="573">
                <property name="width">5%</property>
            </column>
            <column id="577">
                <property name="width">9%</property>
            </column>
            <column id="10446">
                <property name="width">9%</property>
            </column>
            <header>
                <row id="536">
                    <property name="fontWeight">bold</property>
                    <cell id="537">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="303">
                            <property name="borderBottomStyle">none</property>
                            <property name="borderLeftStyle">none</property>
                            <property name="borderRightStyle">none</property>
                            <property name="borderTopStyle">none</property>
                            <text-property name="text" key="Num">N°</text-property>
                        </label>
                    </cell>
                    <cell id="538">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="306">
                            <text-property name="text" key="Reference">Référence</text-property>
                        </label>
                        <label id="427">
                            <text-property name="text" key="Ref Cli">Réf. Cli.</text-property>
                        </label>
                    </cell>
                    <cell id="539">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="10006">
                            <text-property name="text" key="ean">Code produit</text-property>
                        </label>
                    </cell>
                    <cell id="554">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="307">
                            <text-property name="text" key="description">Description</text-property>
                        </label>
                    </cell>
                    <cell id="558">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="308">
                            <text-property name="text" key="Qty">Qté cdée</text-property>
                        </label>
                    </cell>
                    <cell id="562">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="339">
                            <text-property name="text" key="Unite">Unité</text-property>
                        </label>
                    </cell>
                    <cell id="566">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="513">
                            <text-property name="text" key="Qty conf">Qté Conf.</text-property>
                        </label>
                        <label id="10074">
                            <text-property name="text" key="delivery_date">Date liv.</text-property>
                        </label>
                    </cell>
                    <cell id="570">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="522">
                            <text-property name="text" key="Unite conf">Unité Conf.</text-property>
                        </label>
                    </cell>
                    <cell id="574">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="309">
                            <text-property name="text" key="net_unit_price">P.U. Net</text-property>
                        </label>
                        <label id="10437">
                            <text-property name="text" key="gross_unit_price">P.U. Brut</text-property>
                        </label>
                    </cell>
                    <cell id="10443">
                        <label id="10447">
                            <text-property name="text" key="net_amount">Montant Net HT</text-property>
                        </label>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="540">
                    <property name="borderTopStyle">solid</property>
                    <property name="borderTopWidth">thin</property>
                    <list-property name="highlightRules">
                        <structure>
                            <property name="operator">eq</property>
                            <property name="backgroundColor">#EEEEEE</property>
                            <expression name="testExpr" type="javascript">row.__rownum % 2</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">1</value>
                            </simple-property-list>
                        </structure>
                    </list-property>
                    <cell id="541">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="551">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">BuyerLineItemNum</property>
                        </data>
                    </cell>
                    <cell id="542">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="552">
                            <property name="textAlign">center</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">!row["SellerPartID"]</expression>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">SellerPartID</property>
                        </data>
                        <data id="503">
                            <property name="textAlign">center</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">!row["BuyerPartID"]</expression>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">BuyerPartID</property>
                        </data>
                    </cell>
                    <cell id="543">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="10007">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">StandardProductIdentifier</property>
                        </data>
                    </cell>
                    <cell id="555">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="553">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">ItemDescription</property>
                        </data>
                        <table name="OrdrspLineListOfStructuredNote" id="10076">
                            <property name="dataSet">OrdrspLineListOfStructuredNote</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">row.__rownum == -1</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">NoteID</property>
                                    <text-property name="displayName">NoteID</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["NoteID"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">BuyerLineItemNum</property>
                                    <text-property name="displayName">BuyerLineItemNum</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["BuyerLineItemNum"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["BuyerLineItemNum"]</expression>
                                    <simple-property-list name="value1">
                                        <value>row._outer["BuyerLineItemNum"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                                <structure>
                                    <property name="operator">is-not-null</property>
                                    <expression name="expr" type="javascript">row["NoteID"]</expression>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <column id="10090"/>
                            <detail>
                                <row id="10082">
                                    <cell id="10083">
                                        <property name="paddingTop">0pt</property>
                                        <property name="paddingLeft">0pt</property>
                                        <property name="paddingBottom">0pt</property>
                                        <property name="paddingRight">0pt</property>
                                        <data id="10084">
                                            <property name="resultSetColumn">NoteID</property>
                                        </data>
                                    </cell>
                                </row>
                            </detail>
                        </table>
                    </cell>
                    <cell id="559">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="525">
                            <structure name="numberFormat">
                                <property name="category">Unformatted</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">OrderQuantityValue</property>
                        </data>
                    </cell>
                    <cell id="563">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="526">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">OrderUOMCoded</property>
                        </data>
                    </cell>
                    <cell id="567">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="523">
                            <structure name="numberFormat">
                                <property name="category">Unformatted</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">row["hasSplitQty"]</expression>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">ReponseQuantityValue</property>
                        </data>
                        <data id="10073">
                            <structure name="dateTimeFormat">
                                <property name="category">Short Date</property>
                                <property name="pattern">Short Date</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">row["hasSplitQty"]</expression>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">hdrRequestedDeliverByDate</property>
                        </data>
                        <table name="OrdrspLineListOfSplitQuantity" id="10048">
                            <property name="dataSet">OrdrspLineListOfSplitQuantity</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(!row._outer["hasSplitQty"] || row.__rownum == -1)</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">SplitQuantityValue</property>
                                    <text-property name="displayName">SplitQuantityValue</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["SplitQuantityValue"]</expression>
                                    <property name="dataType">float</property>
                                </structure>
                                <structure>
                                    <property name="name">SplitEstimatedDeliveryDate</property>
                                    <text-property name="displayName">SplitEstimatedDeliveryDate</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["SplitEstimatedDeliveryDate"]</expression>
                                    <property name="dataType">date</property>
                                </structure>
                                <structure>
                                    <property name="name">BuyerLineItemNum</property>
                                    <text-property name="displayName">BuyerLineItemNum</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["BuyerLineItemNum"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["BuyerLineItemNum"]</expression>
                                    <simple-property-list name="value1">
                                        <value>row._outer["BuyerLineItemNum"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <column id="10067"/>
                            <detail>
                                <row id="10056">
                                    <cell id="10057">
                                        <property name="paddingTop">0pt</property>
                                        <property name="paddingBottom">0pt</property>
                                        <property name="paddingRight">0pt</property>
                                        <data id="10058">
                                            <property name="textAlign">right</property>
                                            <property name="resultSetColumn">SplitQuantityValue</property>
                                        </data>
                                    </cell>
                                </row>
                                <row id="10070">
                                    <cell id="10071">
                                        <property name="paddingTop">0pt</property>
                                        <property name="paddingBottom">0pt</property>
                                        <property name="paddingRight">0pt</property>
                                        <data id="10060">
                                            <structure name="dateTimeFormat">
                                                <property name="category">Short Date</property>
                                                <property name="pattern">Short Date</property>
                                            </structure>
                                            <property name="textAlign">right</property>
                                            <property name="resultSetColumn">SplitEstimatedDeliveryDate</property>
                                        </data>
                                    </cell>
                                </row>
                            </detail>
                        </table>
                    </cell>
                    <cell id="571">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <data id="524">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">ReponseUOMCoded</property>
                        </data>
                    </cell>
                    <cell id="575">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <property name="paddingTop">1pt</property>
                        <property name="paddingLeft">1pt</property>
                        <property name="paddingBottom">1pt</property>
                        <property name="paddingRight">1pt</property>
                        <table id="10009">
                            <property name="dataSet">OrdrspLineListOfPrice</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">true</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">PriceTypeCoded</property>
                                    <text-property name="displayName">PriceTypeCoded</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["PriceTypeCoded"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">UnitPriceValue</property>
                                    <text-property name="displayName">UnitPriceValue</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["UnitPriceValue"]</expression>
                                    <property name="dataType">decimal</property>
                                </structure>
                                <structure>
                                    <property name="name">BuyerLineItemNum</property>
                                    <text-property name="displayName">BuyerLineItemNum</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["BuyerLineItemNum"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["BuyerLineItemNum"]</expression>
                                    <simple-property-list name="value1">
                                        <value>row._outer["BuyerLineItemNum"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["PriceTypeCoded"]</expression>
                                    <simple-property-list name="value1">
                                        <value>"CalculationNet"</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <column id="10029"/>
                            <detail>
                                <row id="10017">
                                    <cell id="10020">
                                        <property name="paddingTop">0pt</property>
                                        <property name="paddingLeft">0pt</property>
                                        <property name="paddingBottom">0pt</property>
                                        <property name="paddingRight">0pt</property>
                                        <data id="10021">
                                            <structure name="numberFormat">
                                                <property name="category">Currency</property>
                                                <property name="pattern">###0.0000{RoundingMode=HALF_UP}</property>
                                            </structure>
                                            <property name="textAlign">right</property>
                                            <property name="resultSetColumn">UnitPriceValue</property>
                                        </data>
                                    </cell>
                                </row>
                            </detail>
                        </table>
                        <data id="10449">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.0000{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">PriceCalculationNet</property>
                        </data>
                        <table id="10438">
                            <property name="dataSet">OrdrspLineListOfPrice</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">true</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">PriceTypeCoded</property>
                                    <text-property name="displayName">PriceTypeCoded</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["PriceTypeCoded"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">UnitPriceValue</property>
                                    <text-property name="displayName">UnitPriceValue</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["UnitPriceValue"]</expression>
                                    <property name="dataType">decimal</property>
                                </structure>
                                <structure>
                                    <property name="name">BuyerLineItemNum</property>
                                    <text-property name="displayName">BuyerLineItemNum</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["BuyerLineItemNum"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["BuyerLineItemNum"]</expression>
                                    <simple-property-list name="value1">
                                        <value>row._outer["BuyerLineItemNum"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["PriceTypeCoded"]</expression>
                                    <simple-property-list name="value1">
                                        <value>"CalculationGross"</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <column id="10442"/>
                            <detail>
                                <row id="10439">
                                    <cell id="10440">
                                        <property name="paddingTop">0pt</property>
                                        <property name="paddingLeft">0pt</property>
                                        <property name="paddingBottom">0pt</property>
                                        <property name="paddingRight">0pt</property>
                                        <data id="10441">
                                            <structure name="numberFormat">
                                                <property name="category">Currency</property>
                                                <property name="pattern">###0.0000{RoundingMode=HALF_UP}</property>
                                            </structure>
                                            <property name="textAlign">right</property>
                                            <property name="resultSetColumn">UnitPriceValue</property>
                                        </data>
                                    </cell>
                                </row>
                            </detail>
                        </table>
                        <data id="10450">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.0000{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(row["PriceCalculationGross"] == null || row["PriceCalculationGross"] == row["PriceCalculationNet"])</expression>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">PriceCalculationGross</property>
                        </data>
                    </cell>
                    <cell id="10444">
                        <property name="paddingTop">1pt</property>
                        <property name="paddingLeft">1pt</property>
                        <property name="paddingBottom">1pt</property>
                        <property name="paddingRight">1pt</property>
                        <data id="10448">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">LineItemTotal</property>
                        </data>
                    </cell>
                </row>
                <row id="10452">
                    <property name="borderTopStyle">solid</property>
                    <property name="borderTopWidth">thin</property>
                    <list-property name="highlightRules">
                        <structure>
                            <property name="operator">eq</property>
                            <property name="backgroundColor">#EEEEEE</property>
                            <expression name="testExpr" type="javascript">row.__rownum % 2</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">1</value>
                            </simple-property-list>
                        </structure>
                    </list-property>
                    <list-property name="visibility">
                        <structure>
                            <property name="format">all</property>
                            <expression name="valueExpr" type="javascript">!row["hasAllowanceOrCharge"]</expression>
                        </structure>
                    </list-property>
                    <cell id="10453">
                        <property name="colSpan">3</property>
                        <property name="rowSpan">1</property>
                        <property name="borderRightStyle">dashed</property>
                        <property name="borderRightWidth">thin</property>
                        <property name="verticalAlign">middle</property>
                        <label id="946">
                            <property name="textAlign">center</property>
                            <text-property name="text" key="line_allowances_charges">Remises et charges de ligne</text-property>
                        </label>
                    </cell>
                    <cell id="10475">
                        <property name="colSpan">5</property>
                        <property name="rowSpan">1</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <table id="10464">
                            <property name="width">100%</property>
                            <property name="dataSet">OrdrspLineItemAllowancesOrCharges</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">IndicatorCoded</property>
                                    <text-property name="displayName">IndicatorCoded</text-property>
                                    <expression name="expression" type="javascript">ToBeTranslated = dataSetRow["IndicatorCoded"];&#13;
&#13;
ToBePrinted = reportContext.getMessage(ToBeTranslated, reportContext.getLocale());&#13;
&#13;
if (!ToBePrinted)&#13;
  ToBePrinted = ToBeTranslated;&#13;
&#13;
ToBePrinted;</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">Description</property>
                                    <text-property name="displayName">Description</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["Description"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">ServiceCoded</property>
                                    <text-property name="displayName">ServiceCoded</text-property>
                                    <expression name="expression" type="javascript">if (dataSetRow["ServiceCoded"])&#13;
{&#13;
  ToBeTranslated = dataSetRow["ServiceCoded"];&#13;
&#13;
  ToBePrinted = reportContext.getMessage(ToBeTranslated, reportContext.getLocale());&#13;
&#13;
  if (!ToBePrinted)&#13;
    ToBePrinted = ToBeTranslated;&#13;
&#13;
  ToBePrinted;&#13;
}</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">ToBePrinted</property>
                                    <text-property name="displayName">ToBePrinted</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["ToBePrinted"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">BuyerLineItemNum</property>
                                    <text-property name="displayName">BuyerLineItemNum</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["BuyerLineItemNum"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["BuyerLineItemNum"]</expression>
                                    <simple-property-list name="value1">
                                        <value>row._outer["BuyerLineItemNum"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <column id="10473">
                                <property name="width">25%</property>
                            </column>
                            <column id="10478">
                                <property name="width">50%</property>
                            </column>
                            <column id="10485">
                                <property name="width">25%</property>
                            </column>
                            <header>
                                <row id="10465">
                                    <property name="borderBottomStyle">dashed</property>
                                    <property name="borderBottomWidth">thin</property>
                                    <cell id="10466">
                                        <label id="10467">
                                            <text-property name="text" key="type">Type</text-property>
                                        </label>
                                    </cell>
                                    <cell id="10476">
                                        <label id="10479">
                                            <text-property name="text" key="description">Description</text-property>
                                        </label>
                                    </cell>
                                    <cell id="10483">
                                        <label id="931">
                                            <property name="textAlign">center</property>
                                            <text-property name="text" key="value">Valeur</text-property>
                                        </label>
                                    </cell>
                                </row>
                            </header>
                            <detail>
                                <row id="10468">
                                    <property name="textAlign">center</property>
                                    <cell id="10469">
                                        <data id="10470">
                                            <property name="textAlign">center</property>
                                            <property name="resultSetColumn">IndicatorCoded</property>
                                        </data>
                                    </cell>
                                    <cell id="10477">
                                        <property name="textAlign">center</property>
                                        <data id="10481">
                                            <property name="display">inline</property>
                                            <list-property name="visibility">
                                                <structure>
                                                    <property name="format">all</property>
                                                    <expression name="valueExpr" type="javascript">!row["IndicatorCoded"]</expression>
                                                </structure>
                                            </list-property>
                                            <property name="resultSetColumn">ServiceCoded</property>
                                        </data>
                                        <label id="10482">
                                            <property name="display">inline</property>
                                            <list-property name="visibility">
                                                <structure>
                                                    <property name="format">all</property>
                                                    <expression name="valueExpr" type="javascript">(!row["IndicatorCoded"] || !row["Description"])</expression>
                                                </structure>
                                            </list-property>
                                            <text-property name="text"> - </text-property>
                                        </label>
                                        <data id="10480">
                                            <property name="display">inline</property>
                                            <list-property name="visibility">
                                                <structure>
                                                    <property name="format">all</property>
                                                    <expression name="valueExpr" type="javascript">!row["Description"]</expression>
                                                </structure>
                                            </list-property>
                                            <property name="resultSetColumn">Description</property>
                                        </data>
                                    </cell>
                                    <cell id="10484">
                                        <data id="10486">
                                            <property name="textAlign">center</property>
                                            <property name="resultSetColumn">ToBePrinted</property>
                                        </data>
                                    </cell>
                                </row>
                            </detail>
                        </table>
                    </cell>
                    <cell id="10461">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <property name="paddingTop">1pt</property>
                        <property name="paddingLeft">1pt</property>
                        <property name="paddingBottom">1pt</property>
                        <property name="paddingRight">1pt</property>
                    </cell>
                    <cell id="10462">
                        <property name="paddingTop">1pt</property>
                        <property name="paddingLeft">1pt</property>
                        <property name="paddingBottom">1pt</property>
                        <property name="paddingRight">1pt</property>
                    </cell>
                </row>
            </detail>
            <footer>
                <row id="544">
                    <property name="borderTopStyle">solid</property>
                    <property name="borderTopWidth">medium</property>
                    <list-property name="visibility">
                        <structure>
                            <property name="format">all</property>
                            <expression name="valueExpr" type="javascript">!row["sumTotalMonetaryAmount"]</expression>
                        </structure>
                    </list-property>
                    <cell id="545">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="546">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="547">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="556">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="560">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="564">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="568">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="572">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                    </cell>
                    <cell id="576">
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">thin</property>
                        <label id="451">
                            <property name="fontWeight">bold</property>
                            <property name="borderBottomStyle">none</property>
                            <property name="borderLeftStyle">none</property>
                            <property name="borderRightStyle">none</property>
                            <property name="borderTopStyle">none</property>
                            <property name="textAlign">center</property>
                            <text-property name="text" key="Total">Total H.T	</text-property>
                        </label>
                    </cell>
                    <cell id="10445">
                        <data id="10488">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">sumTotalMonetaryAmount</property>
                        </data>
                    </cell>
                </row>
            </footer>
        </table>
    </body>
    <property name="pageVariables">
        <variable-element name="Version">
            <expression name="value" type="javascript">1.01</expression>
            <property name="type">report</property>
        </variable-element>
    </property>
</report>
