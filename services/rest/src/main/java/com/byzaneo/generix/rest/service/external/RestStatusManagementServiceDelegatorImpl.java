package com.byzaneo.generix.rest.service.external;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.service.external.delegators.RestStatusManagementServiceDelegator;
import com.byzaneo.generix.api.util.*;
import com.byzaneo.generix.rest.StatusManagementService;
import com.byzaneo.generix.service.SecurityService.Resource;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.service.SecurityService;
import com.google.gson.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.text.*;
import java.util.*;
import java.util.regex.Pattern;

import static com.byzaneo.generix.rest.helper.RestServiceHelper.verifyEnvironmentCode;

@Component
@RequiredArgsConstructor
public class RestStatusManagementServiceDelegatorImpl implements RestStatusManagementServiceDelegator {

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  private PermissionHelper permissionHelper;

  @Autowired
  private StatusManagementService statusManagementService;
  private static final Pattern PARTNER_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{1,128}$");

  @Override
  public Response getInvoiceChangedStatus(HttpServletRequest request, String env, UUID requestId,
      InvoiceRequestWithPaginationDTO invoiceRequest) {

    if (!RestServiceHelper.hasBearerToken(request)) {
      return RestServiceHelper.getResponseOnError("403",
          "Authorization header must not be null or empty",
          Response.Status.FORBIDDEN, requestId);
    }
    if (requestId == null)
      requestId = UUID.randomUUID();

    if (invoiceRequest.getPartnerIds() == null || invoiceRequest.getPartnerIds()
        .isEmpty()) {
      return RestServiceHelper.getResponseOnError("400", "partners must not be empty",
          Response.Status.BAD_REQUEST, requestId);
    }

    if (invoiceRequest.getProcessingWay() == null || invoiceRequest.getProcessingWay()
        .isEmpty()) {
      return RestServiceHelper.getResponseOnError("400", "processingWay must not be null",
          Response.Status.BAD_REQUEST, requestId);
    }

    for (String id : invoiceRequest.getPartnerIds()) {
      if (id == null || !PARTNER_ID_PATTERN.matcher(id)
          .matches()) {
        return RestServiceHelper.getResponseOnError("400",
            "Invalid partner ID: '" + id + "'. Must be 1 to 128 alphanumeric characters or underscore",
            Response.Status.BAD_REQUEST, requestId);
      }
    }

    TechnicalUser technicalUser = securityService.getTechnicalUserById(
        RestServiceHelper.getTechnicalUserIdFromRequest(request));
    verifyEnvironmentCode(technicalUser, env);

    boolean hasPermission = permissionHelper.isGrantedTechnicalUser(
        Resource.Api_permissions_get_invoice, Right.READ, technicalUser);
    boolean isDisabled = technicalUser.isDisabled();

    if (!hasPermission || isDisabled) {
      return RestServiceHelper.getResponseOnError("403",
          "Security error: You don't have the rights to access to the API.",
          Response.Status.FORBIDDEN, requestId);
    }

    Date start = parseDateUnchecked(invoiceRequest.getStartDate());
    Date end = parseDateUnchecked(invoiceRequest.getEndDate());

    if (start != null && end != null && start.after(end)) {
      throw new IllegalArgumentException("Start date must be before or equal to end date");
    }

    InvoiceStatusManagementResponse response;
    try {
      response = statusManagementService.getChangedInvoiceStatus(invoiceRequest, technicalUser, start, end);
    }
    catch (IllegalArgumentException e) {
      return RestServiceHelper.getResponseOnError("400", e.getMessage(),
          Response.Status.BAD_REQUEST, requestId);
    }
    catch (Exception e) {
      return RestServiceHelper.getResponseOnError("500", "Internal error",
          Response.Status.INTERNAL_SERVER_ERROR, requestId);
    }

    Gson gson = new GsonBuilder().create();
    return Response.status(Response.Status.OK)
        .entity(gson.toJson(response))
        .header("Request-Id", requestId)
        .build();
  }

  private Date parseDateUnchecked(String dateStr) {
    if (dateStr == null) return null;
    try {
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
      formatter.setLenient(false);
      return formatter.parse(dateStr);
    }
    catch (ParseException e) {
      throw new RuntimeException("Invalid date format", e);
    }
  }
}