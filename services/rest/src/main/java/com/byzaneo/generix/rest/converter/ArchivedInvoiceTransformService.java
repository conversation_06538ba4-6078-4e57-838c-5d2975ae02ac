package com.byzaneo.generix.rest.converter;
import com.byzaneo.generix.api.bean.ArchiveInvoiceForAngularDTO;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.edocument.bean.ArchivedInvoice;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.util.DocumentStyleHelper;
import com.byzaneo.generix.xtrade.util.IndexableTaskHelper;
import com.byzaneo.xtrade.bean.DocumentStatusEntity;
import com.byzaneo.xtrade.service.DocumentStatusService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ArchivedInvoiceTransformService {
    private final DocumentStatusService documentStatusServiceBean;

    public static final String LABEL_FAMILY = "gnxxcblinvlbls";

    private final ModelMapper modelMapper;

    public ArchiveInvoiceForAngularDTO transformToDTO(ArchivedInvoice archivedInvoice, Locale locale, String instanceCode) {
        ArchiveInvoiceForAngularDTO archiveInvoiceForAngularDTO = modelMapper.map(archivedInvoice.getInvoice(), ArchiveInvoiceForAngularDTO.class);

        DocumentStatusEntity documentStatusEntity = documentStatusServiceBean.getDocumentStatusByCode(archivedInvoice.getStatusAsString());
        String status = DocumentStyleHelper.resolveStatusLabelText(documentStatusEntity, locale, instanceCode);
        archiveInvoiceForAngularDTO.setStatus(Map.of(
                "code", archivedInvoice.getStatus().getStatusCode(),
                "value", status,
                "styleClass", IndexableTaskHelper.getValueAwesomeBootstrap(documentStatusEntity, instanceCode)
        ));

        ArchivedInvoiceDTO.ArchiveStatusEnum archiveStatusEnum = ArchivedInvoiceDTO.ArchiveStatusEnum.fromValue(archivedInvoice.getArchiveStatus());
        String labelValue = archiveStatusEnum != null ? archiveStatusEnum.getValue() : "";
        String labelStatus = MessageHelper.getMessage(LABEL_FAMILY + "." + labelValue, "", locale);
        archiveInvoiceForAngularDTO.setArchiveStatus(archiveStatusEnum != null ? Map.of(
                "value", labelStatus,
                "styleClass", IndexableTaskHelper.getValueAwesomeBootstrap(archivedInvoice.getArchiveStatus())
        ) : null);

        archiveInvoiceForAngularDTO.setStage(Map.of(
                "value", IndexableTaskHelper.toStageItem(ArchivedInvoiceDTO.StageEnum.fromValue(archivedInvoice.getStage()).getValue(), locale),
                "name", archivedInvoice.getStage().name(),
                "styleClass", IndexableTaskHelper.getValueAwesomeBootstrap(archivedInvoice.getStage(), locale)
        ));

        if (archivedInvoice.getInvoice().getSummaryBreakdownValueAddedTax() != null) {
            List<ValueAddedTaxDTO> addedTaxDTOs = archivedInvoice.getInvoice().getSummaryBreakdownValueAddedTax()
                    .stream()
                    .map(RestServiceHelper::transformValueAddedTaxToValueAddedTaxDTO)
                    .collect(Collectors.toList());
            archiveInvoiceForAngularDTO.setSummaryBreakdownValueAddedTax(addedTaxDTOs);
        }

        if (archivedInvoice.getInvoice().getDetails() != null) {
            List<InvoiceDetailDTO> invoiceDetailDTOList = archivedInvoice.getInvoice().getDetails()
                    .stream()
                    .map(RestServiceHelper::transformInvoiceDetailIndexToInvoiceDetailDTO)
                    .collect(Collectors.toList());
            archiveInvoiceForAngularDTO.setDetails(invoiceDetailDTOList);
        }

        archiveInvoiceForAngularDTO.setPaymentStatus(InvoiceDTO.PaymentStatusEnum.fromValue(archivedInvoice.getInvoice().getPaymentStatus()) != null
                ? DocumentStyleHelper.resolvePaymentStatusLabelText(archivedInvoice.getInvoice().getPaymentStatus()) : null);

        archiveInvoiceForAngularDTO.setType(RestServiceHelper.getType(archivedInvoice.getInvoice().getType()));
        archiveInvoiceForAngularDTO.setArchiveId(archivedInvoice.getArchiveId());
        archiveInvoiceForAngularDTO.setArchiveUid(archivedInvoice.getArchiveUid());
        String labelInvoiceTypeCoded = MessageHelper.getMessage(LABEL_FAMILY + "." + archivedInvoice.getInvoice().getInvoiceTypeCoded()
                .name(), "", locale);
        archiveInvoiceForAngularDTO.setInvoiceTypeCoded(labelInvoiceTypeCoded);
        return archiveInvoiceForAngularDTO;
    }
}
