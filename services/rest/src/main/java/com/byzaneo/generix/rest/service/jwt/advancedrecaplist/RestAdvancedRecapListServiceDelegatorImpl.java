package com.byzaneo.generix.rest.service.jwt.advancedrecaplist;

import com.byzaneo.angular.service.ExceptionType;
import com.byzaneo.angular.service.RestJwtException;
import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.bean.Template;
import com.byzaneo.generix.edocument.bean.RecapListItem;
import com.byzaneo.generix.edocument.task.bean.RecapListWrapper;
import com.byzaneo.generix.edocument.util.StageEnum;
import com.byzaneo.generix.rest.RestException;
import com.byzaneo.generix.rest.RestSecurityException;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.rest.service.basic.RestCommonService;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.InstanceService;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.TaskService;
import com.byzaneo.generix.service.TransformService;
import com.byzaneo.generix.util.DocumentCompoundTypeHelper;
import com.byzaneo.generix.util.DocumentStyleHelper;
import com.byzaneo.generix.util.FileHelper;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.generix.xtrade.task.AbstractIndexableDocumentTask;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.generix.xtrade.util.IndexableTaskHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.bean.UserTaskDefinition;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.service.UserTaskDefinitionService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentStatusEntity;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.byzaneo.generix.api.bean.BqlUuidDto;
import com.byzaneo.generix.api.bean.portal.advancedrecap.AdvancedRecapDTO;
import com.byzaneo.generix.api.bean.portal.AngularWrapperDTO;
import com.byzaneo.generix.api.service.internal.delegators.RestAdvancedRecapListServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.*;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.getValueAwesomeBootstrap;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static java.lang.System.currentTimeMillis;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static javax.faces.application.FacesMessage.SEVERITY_WARN;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.slf4j.LoggerFactory.getLogger;

@Component
@RequiredArgsConstructor
public class RestAdvancedRecapListServiceDelegatorImpl implements RestAdvancedRecapListServiceDelegator {

    private static final Logger log = getLogger(RestAdvancedRecapListServiceDelegatorImpl.class);

    public static final String ADVANCED_RECAP_TYPE = "ADVANCED_RECAP";
    public static final String EXCEL = "EXCEL";
    public static final String PDF = "PDF";
    public static final String XML = "XML";

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private SecurityService securityService;

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;

    @Autowired
    @Qualifier(TaskService.SERVICE_NAME)
    private TaskService taskService;

    @Autowired
    @Qualifier(InstanceService.SERVICE_NAME)
    private InstanceService instanceService;

    @Autowired
    RestCommonService restCommonService;

    @Autowired
    private I18NService i18NService;

    @Autowired
    @Qualifier(DocumentService.SERVICE_NAME)
    private DocumentService documentService;

    @Autowired
    @Qualifier(TransformService.SERVICE_NAME)
    private TransformService transformService;

    @Autowired
    private UserTaskDefinitionService userTaskDefinitionService;

    @Autowired
    private ModelMapper modelMapper;

    public static final String LABEL_FAMILY = "gnxxcblinvlbls";
    protected static final String PATTERN_DATE = "YYYYMMdd";
    protected static final int csvLimitFileSize = 20000;
    protected static final int pdfLimitFileSize = 2500;
    private String templateUri = DEFAULT_TEMPLATE_URI;
    private static final String DEFAULT_TEMPLATE_URI = "/_global_/BIRT/recaplist/recaplist";
    private static final String RECAP_LIST_DIRECTORY_NAME = "recap_list_dir";

    protected static final String TEMP_DIR = FileUtils.getTempDirectory() + FileSystems.getDefault()
            .getSeparator();

    @Transactional
    @Override
    public Response v1getAdvancedRecapList(HttpServletRequest request, String bql,
                                           UUID requestId, Integer limit,
                                           Integer offset, String legalEntity, String flowDirection, String sortBy,
                                           Sort.Direction order, boolean count,
                                           String portletId, String localeAsString, boolean isCountEnabled) {

        String env = restCommonService.getInstanceCodeFromRequest(request);
        if (requestId == null) {
            requestId = UUID.randomUUID();
        }

        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        TechnicalUser technicalUser = null;
        if (limit == null || limit < 0)
            limit = Integer.MAX_VALUE;
        if (offset == null || offset < 0)
            offset = 0;
        if (StringUtils.isEmpty(sortBy)) {
            sortBy = null;
        }

        log.debug("Searching {} eDocuments : {} (page={}, size={})",
                ADVANCED_RECAP_TYPE, bql, offset, limit);

        try {
            //get the default Legal entity for the current user
            String userLegalEntity = PrincipalHelper.isPartnerUser(user) ? user.getPrimaryGroup()
                    .getCode() : null;

            String owner = PrincipalHelper.isPartnerUser(user) ? user.getPrimaryGroup()
                    .getParent()
                    .getCode()
                    : user.getPrimaryGroup()
                    .getCode();
            final QueryBuilder builder = OrganizationHelper.resolveUserOrganizationsQuery(owner,
                    StringUtils.isEmpty(legalEntity) ? Collections.emptyList() : List.of(legalEntity), parse(bql),
                    AbstractTask.FIELD_OWNERS,
                    AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY, flowDirection, sortBy,
                    order != null ? order.name() : Sort.Direction.ASC.name());

            if (user != null)
                restCommonService.addUserAndRoleInvoiceBqlQueryToTheSearch(builder, user);
            else
                builder.append(securityService.createQueryFromTechnicalUserRole(technicalUser));

            final Query qb = builder.query();
            final RestServiceHelper.RestDocument edoc = RestServiceHelper.RestDocument.valueOfIgnoringCase(ADVANCED_RECAP_TYPE);

            final PageRequest pageable = of(offset, limit);

            pageable.setCount(isCountEnabled);
            pageable.setFromAngular(true);

            log.info("RequestId {} : API {} is searching {} edocuments: {} ({})", requestId, user != null ? user
                    .getLogin() : technicalUser.getClientId(), edoc, qb, pageable);

            if (count == true) {
                long totalCount;
                totalCount = this.documentService.countIndexable(RecapListItem.class, qb);
                return ok(getGson().toJson(totalCount)).header("Request-Id", requestId.toString())
                        .build();
            }else {
                List<RecapListItem> result;
                Page<RecapListItem> page = (Page<RecapListItem>) this.documentService.searchIndexables(
                        RecapListItem.class, qb, pageable);
                result = page.getContent();
                log.debug("API result: {}", result);
                return ok(getGson().toJson(
                        getAdvancedRecapList(result, page, portletId, localeAsString, userLegalEntity,
                                env))).header(
                                "Request-Id", requestId.toString())
                        .build();
            }

        } catch (SecurityException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, ADVANCED_RECAP_TYPE);
            throw new RestSecurityException(e.getMessage());
        } catch (BqlParseException | IllegalArgumentException | NoResultException |
                 NonUniqueResultException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, ADVANCED_RECAP_TYPE);
            throw new RestException(e.getMessage());
        } catch (Exception e) {
            String message = getRootCauseMessage(e);
            log.error("API eDocument search error: {} ( q='{}', type='{}')",
                    message, bql, ADVANCED_RECAP_TYPE);
            throw new RestException("An unexpected error occurred: " + message);
        }
    }


    private AngularWrapperDTO getAdvancedRecapList(List<RecapListItem> recapListItems,
                                                   Page<RecapListItem> page,
                                                   String portletId, String localAsString,
                                                   String userLegalEntity, String instanceCode) {
        AngularWrapperDTO advancedRecapListDTO = new AngularWrapperDTO();
        Locale local = new Locale(isNotBlank(localAsString) ? localAsString : "en");
        List<AdvancedRecapDTO> accountingEntryDTOS = List.copyOf(recapListItems)
                .stream()
                .map(i -> {
                    return transformToAdvancedRecapDTO(i, local, instanceCode);
                })
                .collect(toList());

        advancedRecapListDTO.setContent(accountingEntryDTOS);
        advancedRecapListDTO.setTotal(accountingEntryDTOS.size());
        advancedRecapListDTO.setUserLegalEntity(userLegalEntity);
        advancedRecapListDTO.setTotalElements(page.getTotalElements());
        advancedRecapListDTO.setTotalPages(page.getTotalPages());
        advancedRecapListDTO.setPortletId(portletId);
        return advancedRecapListDTO;
    }


    private AdvancedRecapDTO transformToAdvancedRecapDTO(RecapListItem recapListItem,
                                                         Locale locale, String instanceCode) {
        DocumentStatusService documentStatusServiceBean = getBean(DocumentStatusService.class, DocumentStatusService.SERVICE_NAME);

        AdvancedRecapDTO advancedRecapDTO = modelMapper.map(recapListItem,
                AdvancedRecapDTO.class);

        DocumentStatusEntity documentStatusEntity = documentStatusServiceBean.getDocumentStatusByCode(
                recapListItem.getStatusAsString());
        String status = DocumentStyleHelper.resolveStatusLabelText(documentStatusEntity, locale, instanceCode);
        advancedRecapDTO.setStatus(Map.of(
                "code", recapListItem.getStatus().getStatusCode(),
                "value", status,
                "styleClass", getValueAwesomeBootstrap(documentStatusEntity, instanceCode)));

        advancedRecapDTO.setStage(Map.of(
                "value", IndexableTaskHelper.toStageItem(StageEnum.fromValue(recapListItem.getStage()).getValue(), locale),
                "name", StageEnum.fromValue(recapListItem.getStage()).getValue(),
                "styleClass", IndexableTaskHelper.getValueAwesomeBootstrap(recapListItem.getStage())));
        advancedRecapDTO.setErrors(RecapListWrapper.parseEdocumentErrors(recapListItem.getErrors(), locale));
        advancedRecapDTO.setLegalEntityPostalCode(recapListItem.getLegalEntityPostalCode());
        advancedRecapDTO.setLegalEntityVATNumber(recapListItem.getLegalEntityVATNumber());
        advancedRecapDTO.setLegalEntityName(recapListItem.getLegalEntityName());
        advancedRecapDTO.setLegalEntityAddress(recapListItem.getLegalEntityAddress());
        advancedRecapDTO.setLegalEntitySiren(recapListItem.getLegalEntitySiren());
        advancedRecapDTO.setLegalEntityReference(recapListItem.getLegalEntityReference());
        advancedRecapDTO.setLegalEntityCode(recapListItem.getLegalEntityCode());
        String jsonString = recapListItem.getType();
        JsonObject jsonObject = JsonParser.parseString(jsonString).getAsJsonObject();
        String norm = jsonObject.get("norm").getAsString();
        advancedRecapDTO.setType(norm);
        return advancedRecapDTO;
    }


    @Override
    public Response exportAdvancedRecapAsExcelFile(HttpServletRequest request, String envCode, BqlUuidDto bqlList, String sortBy,
                                                   Sort.Direction order, Long portletId, String localeAsString,
                                                   String legalEntity, FileType fileType, boolean exportArchive) {

        try {
            String bql = "";
            if (bqlList != null && bqlList.getUuid() != null && !bqlList.getUuid().isEmpty()) {
                StringJoiner joiner = new StringJoiner(" OR ");
                bqlList.getUuid().forEach(element -> joiner.add("documentNumber = " + element));
                bql = joiner.toString();
            }
            return executeExportQuery(
                    request, envCode, RecapListItem.FIELD_OWNERS,
                    RecapListItem.FIELD_FROM, RecapListItem.FIELD_REFERENCE,
                    RecapListItem.class, envCode, bql, sortBy,
                    order, portletId, localeAsString,
                    fileType, exportArchive,
                    legalEntity);

        } catch (ObjectOptimisticLockingFailureException ex) {
            log.error("Optimistic locking failure occurred while exporting Agreements: {}", ex.getMessage());
            return Response.status(Status.CONFLICT)
                    .entity("Optimistic locking failure occurred while exporting Agreements. Please try again later.")
                    .build();
        } catch (Exception e) {
            log.error("Error executing export query logic: {}", e.getMessage());
            throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
        }
    }

    /**
     *
     * @param request
     * @param companyField
     * @param emitterField
     * @param recipientField
     * @param factorField
     * @param collectionDataClass
     * @param envCode
     * @param bql
     * @param sortBy
     * @param order
     * @param portletId
     * @param localeAsString
     * @param fileType
     * @param exportArchive
     * @param legalEntity
     * @return
     * @throws Exception
     */
    private Response executeExportQuery(HttpServletRequest request, String companyField, String emitterField,
                                        String recipientField, String factorField, Class collectionDataClass,
                                        String envCode, String bql, String sortBy, Sort.Direction order, Long portletId,
                                        String localeAsString, FileType fileType, boolean exportArchive, String legalEntity) throws Exception {
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }
        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);
        String instanceCode = instance.getCode();
        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        Object task = taskService.getTask(portletId, locale, instanceCode);
        UserTaskDefinition userTaskDefinition = userTaskDefinitionService.getUserTaskDefinitionByPortlet(user, portletId);
        BeanDescriptor descriptor = userTaskDefinition != null ? userTaskDefinition.getDescriptor() : ((AbstractTask) task).getDescriptor();
        ((AbstractTask) task).createDownloadFolder(getDownloadDirectory());
        Sort.Direction directionOrder = null;
        boolean asyncExport = false;

        final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
        String companyCode = userOrgs.getCompanyCode();
        if (legalEntity.isEmpty()) {
            legalEntity = PrincipalHelper.isPartnerUser(user) ? user.getPrimaryGroup()
                    .getCode() : null;
        }
        if (PrincipalHelper.isPartnerUser(user)) {
            legalEntity = companyCode + "-" + legalEntity;
        } else if (PrincipalHelper.isCompanyUser(user)) {
            legalEntity = companyCode;
        }

        Collection<String> perimeter = userOrgs.getCodes();
        Query query = parse(bql);

        final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, query, AbstractTask.FIELD_OWNERS,
                        AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, null)
                .query();

        int itemsCount = Long.valueOf(documentService.countIndexable(collectionDataClass, qb)).intValue();
        if (itemsCount == 0)
            throw new RestJwtException(i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)), ExceptionType.NOT_FOUND);
        File file = null;
        if (order != null) {
            directionOrder = Sort.Direction.valueOf(order.toString());
        }

        int asynchronousLimit = (int) ((AbstractIndexableDocumentTask) task).getAsynchronousListExportRange().getMax();
        int documentExportRangeMax = (int) ((AbstractIndexableDocumentTask) task).getDocumentExportRange().getMax();
        int listExportRangeMax = (int) ((AbstractIndexableDocumentTask) task).getListExportRange().getMax();
        int max = itemsCount > listExportRangeMax ? listExportRangeMax : itemsCount;
        int maxPdfExport = 2500;
        Function<Document, String> vBaseName = viewerBaseName(getLabel("gnxxcblcomlbls", "readable_export_file_name", "", locale), descriptor);

        if (fileType.name().equals(EXCEL)) {
            if (itemsCount < asynchronousLimit) {
                // Synchronous download
                file = restCommonService.processingDownloadFile(listExportRangeMax, documentExportRangeMax, vBaseName, RecapListItem.class, fileType, max, createExportFileName(exportArchive, String.valueOf(fileType).toLowerCase(), locale), descriptor, qb, sortBy,
                        directionOrder, locale, user.getDatePattern(), instance, true, null, user, request, instanceCode, false,
                        exportArchive, legalEntity);
            } else {
                // Asynchronous download
                asyncExport = true;
                String url = request.getRequestURL().toString();
                int recapListItemsCount = Long.valueOf(documentService.countIndexable(RecapListItem.class, qb))
                        .intValue();

                file = restCommonService.asynchronousDownload(listExportRangeMax, documentExportRangeMax, vBaseName,
                        RecapListItem.class, fileType, instance, recapListItemsCount, asynchronousLimit,
                        createExportFileName(exportArchive, String.valueOf(fileType)
                                .toLowerCase(), locale), descriptor, qb, sortBy, directionOrder, user, true, request, instanceCode, url, false, false, null);
            }
        } else if (fileType.name().equals(PDF)) {
            if (itemsCount < maxPdfExport) {
                // Synchronous download
                file = downloadFile(fileType, instance, qb, sortBy, order, descriptor, locale, companyCode, legalEntity, maxPdfExport);
            } else {
                // Asynchronous download
                asyncExport = true;
                String url = request.getRequestURL().toString();
                int recapListItemsCount = Long.valueOf(documentService.countIndexable(RecapListItem.class, qb))
                        .intValue();

                file = restCommonService.asynchronousDownload(listExportRangeMax, documentExportRangeMax, vBaseName,
                        RecapListItem.class, fileType, instance, recapListItemsCount, maxPdfExport,
                        createExportFileName(exportArchive, String.valueOf(fileType)
                                .toLowerCase(), locale), descriptor, qb, sortBy, directionOrder, user, true, request, instanceCode, url, false, true, null);
            }
        }
        if (asyncExport) {
            return ok(i18NService.findByCodeAndLocale("export_oversized_ddl_file_warning_message", locale)).build();
        } else {
            return ok(file, getType(file).getDefaultMime())
                    .header("Content-Disposition", "attachment; filename=" + file.getName())
                    .build();
        }
    }

    /**
     *
     * @param fileType
     * @param instance
     * @param qb
     * @param sort
     * @param order
     * @param descriptor
     * @param locale
     * @param companyCode
     * @param legalEntity
     * @param asynchronousLimit
     * @return
     */
    private File downloadFile(FileType fileType, Instance instance, Query qb, String sort, Sort.Direction order,
                              BeanDescriptor descriptor, Locale locale, String companyCode, String legalEntity, int asynchronousLimit) {
        int recapListItemsCount = Long.valueOf(documentService.countIndexable(RecapListItem.class, qb))
                .intValue();
        if (recapListItemsCount == 0) {
            getCurrentInstance()
                    .addCallbackParam("validationFailed", true);
            MessageHelper.addMessage(null, SEVERITY_WARN, "edctsklbls.ddl_no_file_warning_message");
            return null;
        }
        String recapListFileName = "recaplist-file-" + currentTimeMillis();

        return processingDownloadFile(fileType, recapListItemsCount, recapListFileName,
                qb, sort, Sort.Direction.valueOf(order.toString()), locale, companyCode, legalEntity);
    }


    public String getDownloadDirectory() {
        return TEMP_DIR + RECAP_LIST_DIRECTORY_NAME;
    }

    /**
     * @param fileType
     * @param count
     * @param recapListFileName
     * @param searchQuery
     * @param sortField
     * @param sortOrder
     * @param userLocale
     * @param companyCode
     * @param legalEntity
     * @return
     * @throws ServiceException
     */
    public File processingDownloadFile(FileType fileType, int count, String recapListFileName, Query searchQuery, String sortField, Sort.Direction sortOrder, Locale userLocale,
                                       String companyCode, String legalEntity) throws ServiceException {
        File tempFile = null;
        int limitFileSize = fileType == FileType.PDF ? pdfLimitFileSize : csvLimitFileSize;
        int totalFileNumber = (count % limitFileSize) == 0 ? (count / limitFileSize) : (count / limitFileSize) + 1;
        StringBuilder fileName = new StringBuilder(recapListFileName);
        for (int i = 0; i < totalFileNumber; i++) {
            fileName = fileName.delete(fileName.length() - 3, fileName.length() - 1)
                    .append("_")
                    .append(i);
            final int size = i < totalFileNumber ? limitFileSize : count - i * limitFileSize;
            switch (fileType) {
                case PDF:
                    List<RecapListItem> recapListItems = getRecapListItemForExporting(i, size, sortField, sortOrder, searchQuery);
                    if (recapListItems == null || recapListItems.isEmpty()) {
                        log.warn("No item to print");
                    }

                    RecapListWrapper wrapper = RecapListWrapper.buildWrapper(securityService.getGroupByName(legalEntity), recapListItems, userLocale, companyCode);
                    File xmlFile = generateRecapListItemEditionXmlFile(wrapper, fileName.toString());
                    tempFile = createRecapListFromTemplate(FileType.PDF, xmlFile, userLocale);
                    log.info("End processing file {}", tempFile.getAbsolutePath());
                    try {
                        Files.delete(xmlFile.toPath());
                    } catch (IOException e) {
                        log.warn("Impossible to delete echange xml file {} : {}", xmlFile.getName(), e.getMessage());
                    }
                    break;
                default:
                    break;
            }
        }
        return totalFileNumber == 1 ? tempFile : FileHelper.zipList(TEMP_DIR + recapListFileName + ".zip", getDownloadDirectory());
    }

    /**
     *
     * @param recapListItemWrapper
     * @param recapListsFileName
     * @return
     */
    private File generateRecapListItemEditionXmlFile(RecapListWrapper recapListItemWrapper, String recapListsFileName) {
        File recapListEditionXmlFile = new File(getDownloadDirectory() + FileSystems.getDefault()
                .getSeparator() + recapListsFileName + ".xml");
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(RecapListWrapper.class);
            Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
            jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            jaxbMarshaller.marshal(recapListItemWrapper, recapListEditionXmlFile);
        } catch (JAXBException e) {
            log.error("Impossible de générer un fichier xml correspondant pour les échanges", e.getMessage());
        }
        return recapListEditionXmlFile;
    }

    /**
     *
     * @param fileType
     * @param recapListXmlFile
     * @param userLocale
     * @return
     */
    private File createRecapListFromTemplate(FileType fileType, File recapListXmlFile, Locale userLocale) {
        File fileRecapList = new File(getDownloadDirectory() + FileSystems.getDefault()
                .getSeparator() + FilenameUtils.getBaseName(recapListXmlFile.getName()) + fileType.getExtension());
        Template template = null;
        if (StringUtils.isNotEmpty(templateUri)) {

            template = transformService.getTemplate(templateUri);
            Map<String, Object> config = new HashMap<>();
            config.put("uri", recapListXmlFile.toURI()
                    .toString());
            try (final OutputStream os = new FileOutputStream(fileRecapList)) {
                transformService.transform(
                        template,
                        fileType,
                        os,
                        userLocale,
                        null, null, null,
                        config);
            } catch (ServiceException se) {
                throw se;
            } catch (IOException e) {
                throw new ServiceException(e);
            }
        }
        return fileRecapList;
    }


    /**
     *
     * @param fullPathFileName
     * @param recapListFolder
     * @param out
     * @param archiveName
     */
    private void zipFile(String fullPathFileName, String recapListFolder, ZipOutputStream out, String archiveName) {
        try (FileInputStream in = new FileInputStream(fullPathFileName);) {
            Path fullPathFile = Paths.get(fullPathFileName);
            out.putNextEntry(new ZipEntry(fullPathFile.getFileName()
                    .toString()));

            byte[] b = new byte[1024];
            int bytesRead;

            while ((bytesRead = in.read(b)) > 0) {
                out.write(b, 0, bytesRead);
            }
            out.closeEntry();
            log.info(fullPathFileName + " added to archive : " + archiveName);
        } catch (IOException ioe) {
            log.error("Failed to add file " + fullPathFileName + " to archive " + archiveName, ioe);
        }
    }

    /**
     *
     * @param pageIndex
     * @param size
     * @param sortField
     * @param sortOrder
     * @param searchQuery
     * @return
     */
    private List<RecapListItem> getRecapListItemForExporting(int pageIndex, int size, String
            sortField, Sort.Direction sortOrder,
                                                             Query searchQuery) {
        List<RecapListItem> recapListItems = new ArrayList<>();
        if (size > 0) {
            org.springframework.data.domain.PageRequest pageable = isNotBlank(sortField)
                    ? org.springframework.data.domain.PageRequest.of(pageIndex, size, sortOrder, sortField)
                    : org.springframework.data.domain.PageRequest.of(pageIndex, size);
            Page<RecapListItem> page = documentService.searchIndexables(RecapListItem.class, searchQuery, pageable);
            recapListItems.addAll(page.getContent()
                    .stream()
                    .filter(item ->
                    {
                        if (DocumentCompoundTypeHelper.fromJson(item.getType()) == null) {
                            log.warn("Document type is malformed (will not be added to the export) " + item);
                            return false;
                        }
                        return true;
                    })
                    .collect(toList()));
        }
        return recapListItems;
    }

    /**
     *
     * @param list
     * @param type
     * @param locale
     * @return
     */
    protected String createExportFileName(boolean list, String type, Locale locale) {
        StringBuilder fileName = new StringBuilder("export");

        if (!list) {
            fileName.append("-").append(getLabel("gnxxcblcomlbls", null, locale.equals(Locale.ENGLISH) ? "list" : "liste", getLocale()));
        }

        if (list && !isBlank(type)) {
            fileName.append("-").append(type);
        }

        fileName.append("-")
                .append(new SimpleDateFormat(PATTERN_DATE).format(Calendar.getInstance().getTime()))
                .append("-")
                .append(new SimpleDateFormat("HHmmss-SSSSSS").format(Calendar.getInstance().getTime()));

        return fileName.toString();
    }

    /**
     *
     * @param baseFileName
     * @param desc
     * @return
     */
    private Function<Document, String> viewerBaseName(String baseFileName, BeanDescriptor desc) {
        return document -> Optional.of(document)
                .filter(com.byzaneo.xtrade.bean.Document::isIndexed)
                .map(com.byzaneo.xtrade.bean.Document::<RecapListItem>getIndexValue)
                .map(doc -> viewerBaseName(doc, baseFileName, desc))
                .orElseGet(document::getReference);
    }

    /**
     *
     * @param recapListItem
     * @param baseFileName
     * @param desc
     * @return
     */
    private String viewerBaseName(RecapListItem recapListItem, String baseFileName, BeanDescriptor desc) {
        return ofNullable(recapListItem).map(i -> new StringJoiner("-").add(baseFileName)
                        .add(ofNullable(desc.get("documentNumber")).map(p -> p.getDisplayValue(i))
                                .orElse(i.getEntityRef()))
                        .add(ofNullable(desc.get("documentDate")).map(p -> p.getDisplayValue(i, null, null, PATTERN_DATE, null))
                                .orElse(""))
                        .toString())
                .orElse("document");
    }
}
