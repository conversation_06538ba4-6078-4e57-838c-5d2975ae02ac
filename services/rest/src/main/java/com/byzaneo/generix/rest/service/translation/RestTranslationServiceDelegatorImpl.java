package com.byzaneo.generix.rest.service.translation;

import com.byzaneo.angular.bean.I18NModule;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.service.ExceptionType;
import com.byzaneo.angular.service.RestJwtException;
import com.byzaneo.generix.api.service.internal.delegators.RestTranslationServiceDelegator;
import com.byzaneo.generix.api.service.internal.impl.translation.TranslationService;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.CriteriaDto;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.generix.service.repository.service.translation.SearchResult;
import com.byzaneo.generix.service.repository.service.translation.UpdateTranslationDto;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.spring.UserDetails;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static org.slf4j.LoggerFactory.getLogger;

@Component
@RequiredArgsConstructor
public class RestTranslationServiceDelegatorImpl implements RestTranslationServiceDelegator {

  private static final Logger log = getLogger(RestTranslationServiceDelegatorImpl.class);

  private static final String FILE_NAME = "translations.csv";
  
  private final I18NService i18NService;
  
  private final AccountService accountService;

  private final TranslationService translationService;
  
  private final ModelMapper modelMapper;

  @Override
  @Transactional
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response findI18NTranslations(HttpServletRequest request, CriteriaDto criteria) {
    if(Objects.isNull(criteria)) throw new RestJwtException("Criteria should not be null", ExceptionType.BAD_REQUEST);
    Map<String, String> globalViolationsReport = new HashMap<>();
    Map<String, String> criteriaViolationsReport = RestServiceHelper.checkValidity(criteria);
    if (MapUtils.isNotEmpty(criteriaViolationsReport)) {
      globalViolationsReport.putAll(criteriaViolationsReport);
    }
    criteria.getFilters()
        .forEach(filter -> {
          filter.getClauses()
              .forEach(clause -> {
                Map<String, String> clauseViolationsReport = RestServiceHelper.checkValidity(clause);
                if (MapUtils.isNotEmpty(clauseViolationsReport)) globalViolationsReport.putAll(clauseViolationsReport);
              });
        });
    if (!criteria.getFilters()
        .isEmpty())
      if (MapUtils.isNotEmpty(globalViolationsReport)) {
        throw new RestJwtException(globalViolationsReport.toString(), ExceptionType.BAD_REQUEST);
      }
    SearchResult<I18NTranslationDto> i18NTranslationsWithFilter = i18NService.findI18NTranslationWithFilter(criteria);
    return RestServiceHelper.getResponse(Response.Status.OK, i18NTranslationsWithFilter);
  }

  @Override
  @Transactional
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'UPDATE')")
  public Response editTranslation(HttpServletRequest request,Long id, UpdateTranslationDto i18NTranslation) {
    String login = RestServiceHelper.getJWTUsername(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    if (user.getSwitchUserLogin() != null) {
      user = accountService.getUserByLoginOrOpenID(user.getSwitchUserLogin());
    }
    I18NTranslation updatedI18NTranslation = i18NService.editTranslation(i18NTranslation, user, id);
    I18NTranslationDto I18NTranslationDto = modelMapper.map(updatedI18NTranslation, I18NTranslationDto.class);
    return RestServiceHelper.getResponse(Response.Status.OK, I18NTranslationDto);
  }

  @Override
  @Transactional
  public Response findI18NTranslations(String locale) {
    List<I18NTranslation> i18NTranslationsWithFilter = i18NService.findI18NTranslationsByLocale(new Locale(locale));
    Map<String, String> translations = i18NTranslationsWithFilter.stream()
        .collect(Collectors.toMap(I18NTranslation::getCode, i18NTranslation -> !Objects.isNull(i18NTranslation.getNewValue())
                ? i18NTranslation.getNewValue()
                : i18NTranslation.getDefaultValue(),
            (u, v) -> v.concat(",")
                .concat(v),
            LinkedHashMap::new));
    return RestServiceHelper.getResponse(Response.Status.OK, translations);
  }

  @Override
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response findModules(HttpServletRequest request) {
    List<I18NModule> modules = i18NService.findAllI18NModules();
    return RestServiceHelper.getResponse(Response.Status.OK, modules);
  }

  @Override
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response findLanguages(HttpServletRequest request) {
    List<Locale> languages = i18NService.findLanguages();
    return RestServiceHelper.getResponse(Response.Status.OK,languages);
  }
  private boolean hasCriteria(CriteriaDto criteria){
    if(Objects.isNull(criteria))
      throw new RestJwtException("Criteria should not be null", ExceptionType.BAD_REQUEST);
    return true;
  }


  @Override
  public Response exportTranslationsData(HttpServletRequest request, CriteriaDto criteria) {
    File tmpFile = new File(FileUtils.getTempDirectory(), FILE_NAME);
    if(hasCriteria(criteria)) {
      Map<String, String> globalViolationsReport = new HashMap<>();
      Map<String, String> criteriaViolationsReport = RestServiceHelper.checkValidity(criteria);
      if (MapUtils.isNotEmpty(criteriaViolationsReport)) {
        globalViolationsReport.putAll(criteriaViolationsReport);
      }
      criteria.getFilters()
              .forEach(filter -> {
                filter.getClauses()
                        .forEach(clause -> {
                          Map<String, String> clauseViolationsReport = RestServiceHelper.checkValidity(clause);
                          if (MapUtils.isNotEmpty(clauseViolationsReport))
                            globalViolationsReport.putAll(clauseViolationsReport);
                        });
              });
      if (!criteria.getFilters()
              .isEmpty())
        if (MapUtils.isNotEmpty(globalViolationsReport)) {
          throw new RestJwtException(globalViolationsReport.toString(), ExceptionType.BAD_REQUEST);
        }
    }
    return translationService.exportCSVTranslation(tmpFile);
  }


  public User getAuthenticatedUser() {
    Authentication authentication = SecurityContextHolder.getContext()
            .getAuthentication();

    if (authentication == null || !(authentication.getPrincipal() instanceof UserDetails)) {
      return null;
    }

    return ((UserDetails) authentication.getPrincipal()).getUser();
  }

  @Transactional
  @Override
  public Response importTranslationsData(HttpServletRequest request, String locale, Attachment file) {
    User user = getAuthenticatedUser();
    return translationService.importTranslationsData(user, locale, file);
  }
}
