/*
 * Copyright (c) 2024.
 * Created by Mansour SRIDI
 */

package com.byzaneo.generix.rest.service.jwt;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.*;
import com.byzaneo.generix.api.service.internal.delegators.RestAccountingPostingServiceDelegator;
import com.byzaneo.generix.api.util.PermissionHelper;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.generix.repository.portal.*;
import com.byzaneo.generix.rest.*;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.rest.service.basic.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.bean.aap.*;
import com.byzaneo.generix.service.repository.bean.aap.enums.*;
import com.byzaneo.generix.service.repository.service.*;
import com.byzaneo.generix.service.repository.service.aap.*;
import com.byzaneo.generix.service.repository.util.SortOrderDto;
import com.byzaneo.generix.service.repository.util.*;
import com.byzaneo.generix.util.*;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.generix.xtrade.task.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.*;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.index.MongoIndexOperations;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.byzaneo.xtrade.xcbl.util.CustomLocaleMapper;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;
import com.mongodb.Function;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.ibatis.javassist.tools.rmi.ObjectNotFoundException;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.*;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.*;
import javax.ws.rs.core.Response.Status;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.*;
import java.util.stream.*;

import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.getRequestURL;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.generix.rest.service.jwt.RestReferentialProductServiceDelegatorImpl.PATTERN_DATE;
import static com.byzaneo.generix.service.repository.util.AccountingReferentialHelper.AXE_ANGULARE_WRAPPER;
import static com.byzaneo.generix.service.repository.util.AccountingReferentialHelper.NUMBER_OF_AXES;
import static com.byzaneo.generix.service.repository.util.AccountingReferentialHelper.resolveQueryBuilderForSearchingAccountingReferentials;
import static com.byzaneo.generix.service.repository.util.AccountingReferentialHelper.validateAutomaticCounterpart;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.getValueAwesomeBootstrap;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.in;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;

@Component
@RequiredArgsConstructor
public class RestAccountingPostingServiceDelegatorImpl implements RestAccountingPostingServiceDelegator {

  private static final Logger log = getLogger(RestAccountingPostingServiceDelegatorImpl.class);
  public static final int DEMAT_PARTNER_ID_COLUMN = 0;
  public static final int FINAL_ACCOUNT_NUMBER_COLUMN = 1;
  public static final int AUXILIARY_ACCOUNT_COLUMN = 2;
  public static final int COUNTERPART_ACCOUNT_COLUMN = 3;
  public static final int AUTOMATIC_COUNTERPART_COLUMN = 4;

    @Autowired
    private I18NService i18NService;
    public static final String USER_NOT_FOUND = "user_not_found";

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private SecurityService securityService;

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;

    @Autowired
    @Qualifier(DocumentService.SERVICE_NAME)
    private DocumentService documentService;

    @Autowired
    @Qualifier(TaskService.SERVICE_NAME)
    private TaskService taskService;

    @Autowired
    private UserTaskDefinitionService userTaskDefinitionService;

    @Autowired
    @Qualifier(InstanceService.SERVICE_NAME)
    private InstanceService instanceService;

    @Autowired
    RestCommonService restCommonService;

    @Autowired
    EDocumentService eDocumentService;

    @Autowired
    @Qualifier(DocumentService.INDEX_OPERATIONS_NAME)
    MongoIndexOperations indexOperations;

    @Autowired
    @Qualifier(DocumentStatusService.SERVICE_NAME)
    public DocumentStatusService documentStatusService;

    @Autowired
    @Qualifier(RepositoryService.SERVICE_NAME)
    public RepositoryService repositoryService;

    @Autowired
    @Qualifier(DocumentTimelineService.SERVICE_NAME)
    public DocumentTimelineService documentTimelineService;

    @Autowired
    private AapAccountingEntriesService aapAccountingEntriesService;

    @Autowired
    private AapService aapService;

    @Autowired
    private AccountingReferentialHelper accountingReferentialHelper;


  @Autowired
  private AccountingPostingService accountingPostingService;

  @Autowired
  private AapRefSupplierService aapRefSupplierService;

  @Autowired
  private AapReferentialService aapReferentialService;

    @Autowired
    private ModelMapper modelMapper;

    public static final String ACCOUNTING_POSTING_SCENARIO_TYPE = "ACCOUNTING_POSTING_SCENARIO";

  public static final String ACCOUNTING_POSTING_SUPPLIER_TYPE = "AAP_REF_SUPPLIER";
  @Autowired
  private PermissionHelper permissionHelper;

  private static final Set<String> EXPECTED_HEADERS = new HashSet<>(Arrays.asList(
      "dematPartnerId",
      "finalAccountNumber",
      "auxiliaryAccount",
      "counterpartAccount",
      "automaticCounterpart"
  ));

    /**
     * get list of Entities for the current user
     */
    @Override
    @Transactional
    public Response v1getUserLegalEntities(HttpServletRequest request, String localeAsString) {
        User user = restCommonService.getUser(request, localeAsString);
        try {
            final boolean isPartner = isPartnerUser(user);
            Group primaryGroup = user.getPrimaryGroup();

            List<LegalEntityDTO> legalEntities = getLegalEntitiesList(user,localeAsString);

            // Fetch all required AccountingPostings in a single query
            Map<String, AccountingPosting> accountingPostings = aapService.fetchAccountingPostings(
                    legalEntities.stream().map(LegalEntityDTO::getCode).collect(Collectors.toList())
            );

            List<LegalEntityDTO> legalEntitiesConfig = legalEntities.parallelStream()
                    .map(entry -> {
                        String code = entry.getCode();
                        AccountingPosting accountingPosting = accountingPostings.get(code);
                        entry.setAccountingPosting(entityToDTOAccountingPostingRecord(accountingPosting));
                        return entry;
                    })
                    .collect(Collectors.toList());

            return Response.ok(getGson().toJson(new AccountingPostingDTO(
                            isPartner ? primaryGroup.getCode() : null,
                            legalEntitiesConfig
                    )))
                    .build();

        } catch (Exception e) {
            log.error("Error fetching accounting posting data: {}", e.getMessage(), e);  // Enhanced logging
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(e.getMessage()).build();
        }
    }

    private static AccountingPostingRecordDTO entityToDTOAccountingPostingRecord(AccountingPosting accountingPosting) {
        if (accountingPosting == null) {
            return new AccountingPostingRecordDTO();
        }
        return new AccountingPostingRecordDTO(
                accountingPosting.getId(),
                accountingPosting.getTo(),
                accountingPosting.getMaximumLengthAccountNumbers(),
                OriginSupplierReferential.valueOf(accountingPosting.getOriginOfTheSupplierReferential().name()),
                accountingPosting.getUseAuxiliaryAccountsSuppliers(),
                accountingPosting.getPostEarlyPaymentDiscountSeparately(),
                accountingPosting.getDefaultSupplierAccount(),
                accountingPosting.getDefaultProductPurchaseAccount(),
                accountingPosting.getStartFiscalYear(),
                accountingPosting.getStatusInvoiceImputation(),
                accountingPosting.getStatusInvoiceValidation(),
                AnalyticalImputation.valueOf(accountingPosting.getAnalyticalPostingApplicable().name()),
                accountingPosting.getDefaultAnalyticalAccount(),
                accountingPosting.getPurchaseJournalCode(),
                AccountingPostingRecordDTO.FormatAccounting.valueOf(accountingPosting.getFormatAccountingEntries().name()),
                AccountingPostingRecordDTO.FrequencyExporting.valueOf(accountingPosting.getFrequencyExporting().name())
        );
    }

    /**
     * return list of entities by user
     *
     * @param user
     * @return
     */
    @Transactional
    public List<LegalEntityDTO> getLegalEntitiesList(User user, String localeAsString) {
        Objects.requireNonNull(user, "User cannot be null");

        OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
        String companyCode = userOrgs.getCompanyCode();
        String scopeCodes = String.join("-", UserDetailsService.getUserScopeCodes(user));

        Set<String> legalEntities = computeUserLegalEntities(user, companyCode, scopeCodes);
        Company company = getCompany(user);

        //case of admin user connected
        if (company == null && (legalEntities == null || legalEntities.isEmpty())) {
            Response.status(Response.Status.NOT_FOUND).build();
            return Collections.emptyList();
        }

        List<LegalEntityDTO> legalEntitiesMapped = (legalEntities == null || legalEntities.isEmpty())
                ? getAllLegalEntitiesForCompanyUser(user, company, localeAsString)
                : mapLegalEntities(legalEntities, company, localeAsString);

        return legalEntitiesMapped.stream()
                .sorted(Comparator.comparing(LegalEntityDTO::getName))
                .collect(Collectors.toList());
    }

    private Set<String> computeUserLegalEntities(User user, String companyCode, String scopeCodes) {
        if (isPartnerUser(user) || (!RestServiceHelper.isBackOfficeUserUser(user) && RestServiceHelper.isClientWithPerimeter(user))) {
            return Set.of(companyCode + "-" + scopeCodes);
        }
        return Collections.emptySet();
    }
    private List<LegalEntityDTO> getAllLegalEntitiesForCompanyUser(User user, Company company, String localeAsString) {
        if (!isCompanyUser(user)) {
            return new ArrayList<>();
        }

        return securityService.getPartners(company).stream()
                .map(partner -> transformPartnerToLegalEntityDTO(partner, localeAsString))
                .collect(Collectors.toList());
    }
    private List<LegalEntityDTO> mapLegalEntities(Set<String> legalEntities, Company company, String localeAsString) {
        List<LegalEntityDTO> legalEntitiesMapped = new ArrayList<>();

        for (String legalEntityGroup : legalEntities) {
            for (String legalEntityCode : legalEntityGroup.split("-")) {
                legalEntityCode = legalEntityCode.trim();
                if (!legalEntityCode.isEmpty()) {
                    addLegalEntity(legalEntityCode, company, legalEntitiesMapped, localeAsString);
                } else {
                    log.warn("Skipping empty legal entity code.");
                }
            }
        }

        return legalEntitiesMapped;
    }
    private void addLegalEntity(String legalEntity, Company company, List<LegalEntityDTO> legalEntitiesMapped, String localeAsString) {
        try {
            Partner partner = securityService.getPartner(legalEntity, company);

            if (partner != null) {
                LegalEntityDTO legalEntityDTO = transformPartnerToLegalEntityDTO(partner, localeAsString);
                legalEntitiesMapped.add(legalEntityDTO);
            } else {
                log.warn("Partner not found for legal entity code: {}", legalEntity);
            }
        } catch (Exception e) {
            log.error("An error occurred while adding legal entity: {}", legalEntity, e);
            throw new RuntimeException("Failed to add legal entity: " + legalEntity, e);
        }
    }

    public static LegalEntityDTO transformPartnerToLegalEntityDTO(Organization organization, String localeAsString) {
        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);
        LegalEntityDTO legalEntityDTO = new LegalEntityDTO();
        legalEntityDTO.setId(organization.getCode());
        legalEntityDTO.setCode(organization.getCode());
        legalEntityDTO.setName(organization.getFullnameOrName() + " / " + organization.getCode());
        legalEntityDTO.setSiren(organization.getRegistration());
        legalEntityDTO.setCountry(CustomLocaleMapper.getDisplayCountry(organization.getLocation()
            .getAddress()
            .getCountry(), locale));

        legalEntityDTO.setCity(organization.getLocation()
                .getAddress()
                .getCity());

        legalEntityDTO.setAddress(organization.getLocation()
                .getAddress()
                .getStreetName());

        legalEntityDTO.setFullAddress(organization.getLocation()
                .getAddress()
                .getAddressComplement());

        legalEntityDTO.setPostalCode(organization.getLocation()
                .getAddress()
                .getPostalCode());
        legalEntityDTO.setDuns(organization.getDuns());
        legalEntityDTO.setVatNumber(organization.getVat());

        return legalEntityDTO;
    }

    public static List<AapReferential> distinctByKey(
            List<AapReferential> accountingReferentials) {

        Set<String> auxiliaryLabels = new HashSet<>();
        return accountingReferentials.stream()
                .filter(accountingReferential -> auxiliaryLabels.add(
                        accountingReferential.getAuxiliaryAccount()))
                .collect(toList());
    }

    @Transactional
    public Response v1getAllReferentialsByGroup(String envCode, UriInfo uriInfo) {
        Map<String, List<String>> queryParams = uriInfo.getQueryParameters();
        List<String> filterKeys = queryParams.get("keys");
        List<String> partnerIds = queryParams.get("partner-id");
        List<String> tableIndexes = queryParams.get("table-index");
        String partnerId = partnerIds != null && !partnerIds.isEmpty() ? partnerIds.get(0) : null;
        if (partnerId == null) {
            throw new RestJwtException("partnerId should not be empty", ExceptionType.BAD_REQUEST);
        }
        String tableIndex = tableIndexes != null && !tableIndexes.isEmpty() ? tableIndexes.get(0) : null;
        if (tableIndex == null) {
            throw new RestJwtException("tableIndex should not be empty", ExceptionType.BAD_REQUEST);
        }
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }
        Group group = instance.getGroup();
        Query query = QueryBuilder.createBuilder().and(equal("owner", group.getCode()))
                .and(equal("to", partnerId)).query();
        final PageRequest pageable =  of(
                0,
                Integer.MAX_VALUE, Sort.by(Sort.Direction.ASC, "finalAccountNumber"));
        Page<AapReferential> aapReferentialsPage = aapReferentialService.search(query, pageable);
        List<AapReferential> aapReferentials = aapReferentialsPage.getContent();
        if (filterKeys != null && !filterKeys.isEmpty()) {
            aapReferentials = aapReferentials.stream().filter(
                    accountingReferential ->
                            filterKeys.stream()
                                    .anyMatch(
                                            key -> String.valueOf(accountingReferential.getFinalAccountNumber())
                                                    .startsWith(key))).collect(
                    Collectors.toList());

        }
        if (filterKeys != null && filterKeys.size() == 1 && "4".equals(filterKeys.get(0))) {
            aapReferentials = distinctByKey(aapReferentials);
        }

        List<AccountingReferentialDto> accountingReferentialsDto = RestServiceHelper.mapList(aapReferentials,
                AccountingReferentialDto.class, modelMapper);
        AccountingReferentialWrapperDto accountingReferentialWrapperDto = new AccountingReferentialWrapperDto();
        accountingReferentialWrapperDto.setAccountingReferentials(accountingReferentialsDto);
        accountingReferentialWrapperDto.setTableIndex(tableIndex);
        return ok(getGson().toJson(accountingReferentialWrapperDto))
                .build();
    }

    public Response updateScenariosOrder(HttpServletRequest request, ScenarioOrderListDto scenarioOrderDto, String localeAsString) {
        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);
        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        if (Objects.isNull(user)) {
            return Response.status(Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }

        try {
            Map<String, Integer> ordersMap = scenarioOrderDto.getOrders().stream()
                .collect(Collectors.toMap(
                    ScenarioOrderDto::getId,
                    ScenarioOrderDto::getOrder
                ));
            QueryBuilder qb = QueryBuilder.createBuilder().and(in("id", ordersMap.keySet()));
            List<AccountingPostingScenario> accountingPostingScenarios = aapService.getAccountingPostingScenarios(qb.query());
            accountingPostingScenarios.forEach( accountingPostingScenario -> {
                accountingPostingScenario.setOrder(ordersMap.get(accountingPostingScenario.getId()));
            });
            aapService.saveAccountingPostingScenarios(accountingPostingScenarios);
        } catch (Exception e) {
            e.printStackTrace();
            return RestServiceHelper.getResponse(Status.INTERNAL_SERVER_ERROR,
                    i18NService.findByCodeAndLocale("error_while_saving", locale, user.getFullname()));
        }

        return RestServiceHelper.getResponse(Response.Status.OK, "Order updated");
    }

    private Company getCompany(User user) {
        Company company = null;
        if (isCompanyUser(user)) {
            company = (Company) user.getPrimaryGroup();
        } else if (isPartnerUser(user)) {
            company = (Company) user.getPrimaryGroup()
                    .getParent();
        }
        return company;
    }


    @Override
    @Transactional
    public Response v1SetAccountingPosting(HttpServletRequest request, String localeAsString,
                                           AccountingPostingRecordDTO accountingPostingRecordDTO) {
        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);
        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        if (Objects.isNull(user)) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }
        Map<String, String> violationsReport = RestServiceHelper.checkValidity(
                accountingPostingRecordDTO);
        if (MapUtils.isNotEmpty(violationsReport)) {
            return RestServiceHelper.getResponse(Response.Status.BAD_REQUEST, violationsReport);
        }

        try {
            AccountingPosting accountingPosting = modelMapper.map(accountingPostingRecordDTO, AccountingPosting.class);
            String owner = isPartnerUser(user) ? user.getPrimaryGroup().getParent().getCode()
                    : user.getPrimaryGroup().getCode();
            accountingPosting.setOwner(owner);
            Partner part = null;
            Company company = null;
            if (isPartnerUser(user)) {
                part = (Partner) user.getPrimaryGroup();
            } else {
                company = getCompany(user);
            }
            if (part == null) {
                part = securityService.getPartner(accountingPostingRecordDTO.getTo(), company);
            }
            if (part != null) {
                try {
                    AccountingPosting savedIndexable = aapService.saveAccountingPosting(accountingPosting);
                    part.setAccountPostingId(String.valueOf(savedIndexable.getId()));
                    this.securityService.saveGroup(part);
                    accountingPostingRecordDTO = entityToDTOAccountingPostingRecord(savedIndexable);
                } catch (Exception e) {
                    log.error("error while saving accountingPosting ", e);
                }
            } else {
                throw new ObjectNotFoundException("Partner not found");
            }

        } catch (ObjectNotFoundException e) {
            e.printStackTrace();
            return Response.status(Response.Status.NOT_FOUND)
                    //in this case,the partner is not well configured
                    .entity("aap-management.setting.alert.partnerNotFound")
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR,
                    i18NService.findByCodeAndLocale("error_while_saving", locale, user.getFullname()));
        }
        return RestServiceHelper.getResponse(Response.Status.OK, accountingPostingRecordDTO);

    }


    @Override
    @Transactional
    public Response v1getAapInvActPostingByUuid(HttpServletRequest request, String uuid, String localeAsString) {
        Locale locale = LocaleUtils.toLocale(localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en");
        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        if (Objects.isNull(user)) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }
        AapInvActPosting aapInvActPosting = aapService.v1getAapInvActPostingByUuid(uuid);
        if(!Objects.isNull(aapInvActPosting)) {
            return RestServiceHelper.getResponse(Response.Status.OK, this.generateDto(aapInvActPosting));

        } else {
            return RestServiceHelper.getResponse(Status.NO_CONTENT, null);
        }
    }

    private Object generateDto(AapInvActPosting aapInvActPosting) {
        AapInvActPostingDTO aapInvActPostingDTO = new AapInvActPostingDTO();
        aapInvActPostingDTO.setId(aapInvActPosting.getId());
        aapInvActPostingDTO.setIapStatus(AapInvActPostingDTO.IAPStatus.valueOf(aapInvActPosting.getIapStatus().name()));
        aapInvActPostingDTO.setUserId(aapInvActPosting.getUserId());
        aapInvActPostingDTO.setUuid(aapInvActPosting.getInvoiceId());
        aapInvActPostingDTO.setInvoiceId(aapInvActPosting.getInvoiceId());
        aapInvActPostingDTO.setJournal(aapInvActPosting.getJournal());
        aapInvActPostingDTO.setOtherLabel(aapInvActPosting.getOtherLabel());
        List<AapItemDTO> aapItemDTOS = new ArrayList<>();
        for(AapItem aapItem: aapInvActPosting.getItems()) {
            AapItemDTO aapItemDTO = new AapItemDTO();
            aapItemDTO.setAccount(aapItem.getAapReferential().getFinalAccountNumber());
            aapItemDTO.setAccountLabel(aapItem.getAapReferential().getAccountLabel());
            aapItemDTO.setAuxiliaryAccount(aapItem.getAapReferential().getAuxiliaryAccount());
            aapItemDTO.setAapReferentialLineId(aapItem.getAapReferential().getId());
            aapItemDTO.setAmount(aapItem.getAmount());
            aapItemDTO.setOrder(aapItem.getOrder());
            aapItemDTO.setItemSubId(aapItem.getItemSubId());
            aapItemDTO.setItemType(AapItemDTO.ItemType.valueOf(aapItem.getItemType().name()));
            if(aapItem.getId() != null) {
                aapItemDTO.setId(aapItem.getId());
            }
            aapItemDTO.setItemId(aapItem.getItemId());
            aapItemDTOS.add(aapItemDTO);
        }
        aapInvActPostingDTO.setItems(aapItemDTOS);
        return aapInvActPostingDTO;

    }

    @Override
    @Transactional
    public Response v1SetAapInvActPosting(HttpServletRequest request, String localeAsString,
                                          AapInvActPostingDTO aapInvActPostingDto, boolean saveInTimeLine) {
        Locale locale = new Locale(localeAsString);
        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        if (Objects.isNull(user)) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }
        try {
            Map<String, String> violationsReport = RestServiceHelper.checkValidity(
                    aapInvActPostingDto);
            if (MapUtils.isNotEmpty(violationsReport)) {
                return RestServiceHelper.getResponse(Response.Status.BAD_REQUEST, violationsReport);
            }
            final AapInvActPosting aapInvActPosting;
            AapInvActPosting existingAapInvActPosting;
            String invoiceId;
            if(aapInvActPostingDto.getInvoiceId() != null) {
                invoiceId =aapInvActPostingDto.getInvoiceId();
            } else if(aapInvActPostingDto.getUuid() != null) {
                invoiceId =aapInvActPostingDto.getUuid();
            } else {
                invoiceId = null;
            }
            if (invoiceId != null) {
                Query query = QueryBuilder.createBuilder().and(equal("invoiceId", invoiceId)).query();
                existingAapInvActPosting = aapService.searchAapInvActPostingSingleResult(query);
                AapInvActPosting savedAapInvActPosting;
                if (existingAapInvActPosting != null) {
                 if(aapInvActPostingDto.getItems() != null) {
                        List<AapItem> AapItemList = aapInvActPostingDto.getItems()
                            .stream()
                            .map(aapItem -> this.fromAapItemDTO(aapItem, existingAapInvActPosting))
                            .collect(toList());
                     existingAapInvActPosting.setItems(AapItemList);
                    }
                    existingAapInvActPosting.setJournal(aapInvActPostingDto.getJournal());
                    existingAapInvActPosting.setOtherLabel(aapInvActPostingDto.getOtherLabel());
                    existingAapInvActPosting.setUserId(user.getLogin());
                    existingAapInvActPosting.setInvoiceId(invoiceId);
                    savedAapInvActPosting = aapService.updateApInvActPosting(existingAapInvActPosting);

                } else {
                    aapInvActPosting = this.fromAapInvActPostingDto(aapInvActPostingDto);
                    if(aapInvActPostingDto.getItems() != null) {
                        List<AapItem> AapItemList = aapInvActPostingDto.getItems()
                            .stream()
                            .map( aapItem -> this.fromAapItemDTO(aapItem, aapInvActPosting))
                            .collect(toList());
                        aapInvActPosting.setItems(AapItemList);
                    }
                    aapInvActPosting.setUserId(user.getLogin());
                    aapInvActPosting.setInvoiceId(invoiceId);
                    savedAapInvActPosting = aapService.saveAapInvActPosting(aapInvActPosting);

                }

            if (saveInTimeLine) {
                Document doc = documentService.getDocumentByUUID(aapInvActPostingDto.getInvoiceId());
                DocumentTimeline docTimeline = new DocumentTimeline(doc, new Date(), DocumentTimeline.TimelineAction.UPDATE_ACCOUNT_POSTING_MNL,
                        user.getId() != null ? user.getId() : DocumentTimeline.USER_SYSTEM);
                documentTimelineService.save(docTimeline);
            }

  return RestServiceHelper.getResponse(Response.Status.OK, this.generateDto(savedAapInvActPosting));
            } else {
                return RestServiceHelper.getResponse(Status.BAD_REQUEST, "invoice id should not be null");
            }
        } catch (Exception e) {
            log.error("Exception updating invoice accounting posting: " + e.getMessage(), e);
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR,
                    i18NService.findByCodeAndLocale("error_while_saving", locale, user.getFullname()));
        }
    }

    private AapInvActPosting fromAapInvActPostingDto(AapInvActPostingDTO aapInvActPostingDto) {
        AapInvActPosting aapInvActPosting = new AapInvActPosting();
        aapInvActPosting.setInvoiceId(aapInvActPostingDto.getInvoiceId());
        aapInvActPosting.setUserId(aapInvActPostingDto.getUserId());
        aapInvActPosting.setIapStatus(AapInvActPosting.IAPStatus.valueOf(aapInvActPostingDto.getIapStatus().name()));
        aapInvActPosting.setJournal(aapInvActPostingDto.getJournal());
        InvoiceIndex invoiceIndex = this.documentService.searchIndexable(InvoiceIndex.class, QueryBuilder.createBuilder().and(equal("_uuid", aapInvActPosting.getInvoiceId())).query());

        aapInvActPosting.setOtherLabel(getEntryOtherLabel(invoiceIndex.getInvoiceTypeCoded(), invoiceIndex.getSellerPartyName()));
        if(aapInvActPostingDto.getItems() != null) {
            List<AapItem> aapItems = aapInvActPostingDto.getItems()
                .stream()
                .map(itemDto -> this.fromAapItemDTO(itemDto, aapInvActPosting))
                .collect(
                    Collectors.toUnmodifiableList());
            aapInvActPosting.setItems(aapItems);
        }
        return aapInvActPosting;
    }

    public String getEntryOtherLabel(InvoiceTypeCodeType invoiceTypeCoded, String sellerPartyName) {
        switch (invoiceTypeCoded) {
        case CREDIT_INVOICE:
        case CREDIT_MEMO:
        case CREDIT_NOTE_FINANCIAL_ADJUSTMENT:
        case CREDIT_NOTE_GOODS_AND_SERVICES:
        case FACTORED_CREDIT_NOTE: {
            return "Avoir fournisseur ".concat(sellerPartyName);
        }
        default: {
            return "Facture fournisseur ".concat(sellerPartyName);
        }
        }
    }



    @Override
    @Transactional
    public Response v1UpdateAapInvActPosting(HttpServletRequest request, String localeAsString, String uuid) {
        Locale locale = LocaleUtils.toLocale(localeAsString);
        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);

        if (Objects.isNull(user)) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }
        try {
            Query query = QueryBuilder.createBuilder().and(equal("invoiceId", uuid)).query();
            AapInvActPosting aapInvActPosting = aapService.searchAapInvActPostingSingleResult(query);

            if (aapInvActPosting != null) {
                aapInvActPosting.setIapStatus(AapInvActPosting.IAPStatus.AAP_COMPLETED);
                aapInvActPosting.setUserId(user.getLogin());
                aapInvActPosting = aapService.saveAapInvActPosting(aapInvActPosting);
                Document doc = documentService.getDocumentByUUID(uuid);
                DocumentTimeline docTimeline = new DocumentTimeline(doc, new Date(), DocumentTimeline.TimelineAction.COMPLETE_ACCOUNT_POSTING_MNL,
                    user.getId() != null ? user.getId() : DocumentTimeline.USER_SYSTEM);
                documentTimelineService.save(docTimeline);
                aapAccountingEntriesService.saveAapAccountingEntries(user.getLogin(), aapInvActPosting);
                docTimeline = new DocumentTimeline(doc, new Date(), DocumentTimeline.TimelineAction.AAP_TRANSMITTED,
                    user.getId() != null ? user.getId() : DocumentTimeline.USER_SYSTEM);
                documentTimelineService.save(docTimeline);
            } else {
                throw new RestJwtException("aap invoice accounting posting not found", ExceptionType.NOT_FOUND);
            }

            return RestServiceHelper.getResponse(Response.Status.OK, this.generateDto(aapInvActPosting));
        } catch (Exception e) {
            log.error("Exception updating invoice accounting posting: " + e.getMessage(), e);
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR,
                    i18NService.findByCodeAndLocale("error_while_saving", locale, user.getFullname()));
        }
    }


    @Override
    public Response v1GetAxeByAccountingPostingIdAndAxeCode(HttpServletRequest request, long aapReferentialId, String analytAxeCode,
                                                            String localeAsString) {
        Locale locale = LocaleUtils.toLocale(StringUtils.isNotBlank(localeAsString) ? localeAsString : "en");
        try {

            AapReferential aapReferential = aapService.getAapReferentialById(aapReferentialId);

            List<AccountingReferentialSection> sectionsResult = null;

            if (aapReferential != null && !CollectionUtils.isEmpty(aapReferential.getAxes())) {
                List<AccountingReferentialAxe> axesFound = aapReferential.getAxes()
                        .stream()
                        .filter(axe -> Objects.equals(axe.getAnalytAxeCode(), analytAxeCode))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(axesFound)) {
                    sectionsResult = axesFound.get(0)
                            .getSections();
                }
            } else {
                throw new RestJwtException("Accounting Referential not found", ExceptionType.NOT_FOUND);
            }
            return RestServiceHelper.getResponse(Response.Status.OK, sectionsResult);
        } catch (Exception e) {
            log.error("Exception when getting sections: " + e.getMessage(), e);
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR,
                    i18NService.findByCodeAndLocale("error_getting_sections", locale));
        }

    }

    @Override
    @Transactional
    public Response displayAccountPostingImputationComptable(HttpServletRequest request, String to) {

        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);

        Partner partner;
        Company company;
        company = getCompany(user);
        partner = securityService.getPartner(to, company);

        if (partner != null) {
            try {
                AccountingPosting accountingPosting = aapService.getAccountingPostingByPartnerId(partner.getCode());
                if(accountingPosting == null) {
                    throw new RestJwtException("recipient user has no Accounting Posting", ExceptionType.NOT_FOUND);
                }
                AccountingPostingRecordDTO accountingPostingDTO = modelMapper.map(accountingPosting, AccountingPostingRecordDTO.class);
                return RestServiceHelper.getResponse(Response.Status.OK, accountingPostingDTO);
            } catch (EmptyResultDataAccessException e) {
                throw new RestJwtException("recipient user has no Accounting Posting", ExceptionType.NOT_FOUND);
            }
        } else {
            throw new RestJwtException("recipient user has no Accounting Posting ", ExceptionType.NOT_FOUND);
        }

    }

    public AapItem fromAapItemDTO(AapItemDTO dto, AapInvActPosting aapInvActPosting) {
        AapReferential referential = new AapReferential();
        if(dto.getAapReferentialLineId()!=null) {
            referential = aapService.getAapReferentialById(dto.getAapReferentialLineId());
        }
        final ModelMapper modelMapper = new ModelMapper();
        //modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        AapItem aapItem =  modelMapper.map(dto, AapItem.class);
        aapItem.setAapReferential(referential);
        aapItem.setAapInvActPosting(aapInvActPosting);
        return aapItem;
    }

    @Override
    @Transactional
    public Response v1SetAccountingPostingSupplier(HttpServletRequest request, String localeAsString, AapRefSupplierDTO aapRefSupplierDTO) {
        localeAsString = (localeAsString != null && !localeAsString.isEmpty()) ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);

        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        if (Objects.isNull(user)) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }

        Map<String, String> violationsReport = RestServiceHelper.checkValidity(aapRefSupplierDTO);
        if (MapUtils.isNotEmpty(violationsReport)) {
            return RestServiceHelper.getResponse(Response.Status.BAD_REQUEST, violationsReport);
        }

        try {
            Query queryAapRefSupplier = QueryBuilder.createBuilder()
                    .and(equal("dematPartnerId", aapRefSupplierDTO.getDematPartnerId()))
                    .and(equal("legalEntityId", aapRefSupplierDTO.getLegalEntityId()))
                    .query();

            Query queryAapReferential = QueryBuilder.createBuilder()
                    .and(equal("to", aapRefSupplierDTO.getLegalEntityCode()))
                    .and(equal("finalAccountNumber", aapRefSupplierDTO.getFinalAccountNumber()))
                    .and(equal("auxiliaryAccount", aapRefSupplierDTO.getAuxiliaryAccount()))
                    .query();

            AapReferential aapReferential = aapService.getAapReferential(queryAapReferential);
            AapRefSupplier existingAapRefSupplier = aapService.getAapRefSupplier(queryAapRefSupplier);

            AapRefSupplier aapRefSupplier = (existingAapRefSupplier != null) ? existingAapRefSupplier : new AapRefSupplier();
            aapRefSupplier.setAapReferential(aapReferential);
            aapRefSupplier.setDematPartnerId(aapRefSupplierDTO.getDematPartnerId());
            aapRefSupplier.setLegalEntityId(aapRefSupplierDTO.getLegalEntityId());
            aapRefSupplier.setCounterpartAccountId(aapRefSupplierDTO.getCounterpartAccountId());
            aapRefSupplier.setAutomaticCounterpart(String.valueOf(aapRefSupplierDTO.getAutomaticCounterpart()));

            if (existingAapRefSupplier == null) {
                aapRefSupplier.setCreationDate(new Date());
                aapRefSupplier.setModificationDate(new Date());
            }

            String owner = isPartnerUser(user)
                    ? user.getPrimaryGroup().getParent().getCode()
                    : user.getPrimaryGroup().getCode();
            aapRefSupplier.setOwner(owner);

            AapRefSupplier savedSupplier = aapService.saveAapRefSupplier(aapRefSupplier);

        } catch (Exception e) {
            log.error(i18NService.findByCodeAndLocale("error_while_saving", locale, user.getFullname()), e);
            return RestServiceHelper.getResponse(
                    Response.Status.INTERNAL_SERVER_ERROR,
                    i18NService.findByCodeAndLocale("error_while_saving", locale, user.getFullname())
            );
        }

        return RestServiceHelper.getResponse(Response.Status.OK, aapRefSupplierDTO);
    }

    public boolean isCompanyUser(User user) {
        return PrincipalHelper.isCompanyUser(user);
    }

    public boolean isPartnerUser(User user) {
        Optional<Group> userPrimaryGroup = this.securityService.getPrimaryGroupOfUser(user);
        return userPrimaryGroup.isPresent() &&
                Partner.DESCRIPTION.equals(userPrimaryGroup.get()
                        .getDescription());
    }

    AapReferentialSupplierListDTO getAapReferentialSupplierList(List<AapRefSupplierDTO> aapRefSupplierDTO, long totalElements, long totalPages, String portletId, String localAsString, String userLegalEntity, String instanceCode, String sortBy, Sort.Direction order) {
        AapReferentialSupplierListDTO aapReferentialSupplierListDTO = new AapReferentialSupplierListDTO();
        aapReferentialSupplierListDTO.setContent(aapRefSupplierDTO);
        aapReferentialSupplierListDTO.setTotal(aapRefSupplierDTO.size());
        aapReferentialSupplierListDTO.setUserLegalEntity(userLegalEntity);
        aapReferentialSupplierListDTO.setTotalElements(totalElements);
        aapReferentialSupplierListDTO.setTotalPages(totalPages);
        aapReferentialSupplierListDTO.setPortletId(portletId);

        aapReferentialSupplierListDTO.setTotalElements(totalElements);
        aapReferentialSupplierListDTO.setTotalPages(totalPages);
        aapReferentialSupplierListDTO.setPortletId(portletId);

        return aapReferentialSupplierListDTO;
    }

    private AapRefSupplierDTO transformToAapReferentialSupplierDTO(Exchange exchange, AapRefSupplier aapRefSupplier, Locale locale, String instanceCode) {
        AapRefSupplierDTO aapRefSupplierDTO = modelMapper.map(Optional.ofNullable(aapRefSupplier)
                .orElse(new AapRefSupplier()), AapRefSupplierDTO.class);
        Group group = exchange.getId().getDematPartner();
        aapRefSupplierDTO.setVat(group.getVat());
        aapRefSupplierDTO.setRegistration(group.getRegistration());
        aapRefSupplierDTO.setCode(group.getCode());
        aapRefSupplierDTO.setDuns(group.getDuns());
        aapRefSupplierDTO.setCity(group.getLocation().getAddress().getCity());
        aapRefSupplierDTO.setCountry(group.getLocation().getAddress().getCountryName());
        aapRefSupplierDTO.setPostalCode(group.getLocation().getAddress().getPostalCode());
        aapRefSupplierDTO.setStreetName(group.getLocation().getAddress().getAddressLine());
        aapRefSupplierDTO.setName(group.getFullname());
        aapRefSupplierDTO.setCreation(group.getCreation());
        aapRefSupplierDTO.setDematPartnerId(exchange.getId().getDematPartner().getId());
        aapRefSupplierDTO.setLegalEntityId(exchange.getId().getLegalEntity().getId());
        aapRefSupplierDTO.setLegalEntityCode(exchange.getId().getLegalEntity().getCode());
        aapRefSupplierDTO.setAapStatus(null);
        AapReferential aapReferential = Optional.ofNullable(aapRefSupplier)
                .map(AapRefSupplier::getAapReferential)
                .orElse(null);
        if (aapReferential != null) {
            aapRefSupplierDTO.setFinalAccountNumber(aapReferential.getFinalAccountNumber());
            aapRefSupplierDTO.setAuxiliaryAccount(aapReferential.getAuxiliaryAccount());
            aapRefSupplierDTO.setAccountLabel(aapReferential.getAccountLabel());

            String code = aapReferential.getAapStatus() == com.byzaneo.generix.service.repository.bean.aap.enums.Status.Active ? DocumentStatus.VALIDATED.toString() : DocumentStatus.PENDING.toString();
            DocumentStatusEntity documentStatusEntity = documentStatusService.getDocumentStatusByCode(
                    code);
            String status = DocumentStyleHelper.resolveAccountingPostingStatusLabelText(aapReferential.getAapStatus().name(), locale);
            aapRefSupplierDTO.setAapStatus(Map.of(
                    "code", code,
                    "name", aapReferential.getAapStatus().name(),
                    "value", status,
                    "styleClass", getValueAwesomeBootstrap(documentStatusEntity, instanceCode),
                    "class", "AccountingPostingStatus")
            );

        }
        return aapRefSupplierDTO;
    }

    @Override
    @Transactional
    public Response v1getReferentialSupplier(HttpServletRequest request, String bql,
                                             UUID requestId, Integer limit,
                                             Integer offset, String legalEntity, String flowDirection, String sortBy, Sort.Direction order, boolean count,
                                             String portletId, String localeAsString, boolean isCountEnabled) {
        String env = restCommonService.getInstanceCodeFromRequest(request);
        if (requestId == null) {
            requestId = UUID.randomUUID();
        }

        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        bql = FormatHelper.encodeSpecialCharacters(bql);
        TechnicalUser technicalUser = null;
        if (limit == null || limit < 0)
            limit = Integer.MAX_VALUE;
        if (offset == null || offset < 0)
            offset = 0;
        if (StringUtils.isEmpty(sortBy)) {
            sortBy = null;
        }

        log.debug("Searching {} eDocuments : {} (page={}, size={})",
                ACCOUNTING_POSTING_SCENARIO_TYPE, bql, offset, limit);

        // - search for AccountPosting -
        try {
            Map<String, String> sortedLegalEntities = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            //get the default Legal entity for the current user
            String userLegalEntity = isPartnerUser(user) ? user.getPrimaryGroup().getCode() : null;
            if (StringUtils.isEmpty(legalEntity)) {
                Set<String> legalEntities = new HashSet<>();
                legalEntities =
                        (isPartnerUser(user) || (!RestServiceHelper.isBackOfficeUserUser(user) && RestServiceHelper.isClientWithPerimeter(user)))
                                ? UserDetailsService.getUserScopeCodes(Objects.requireNonNull(user))
                                : null;
                Company company = getCompany(user);

                //case of admin user connected
                if (company == null && legalEntities.isEmpty()) {
                    Response.status(Response.Status.NOT_FOUND)
                            //.entity("No data found")
                            .build();
                }
                Map<String, String> legalEntitiesMapped = new HashMap<>();
                if (legalEntities == null || legalEntities.isEmpty()) {
                    if (isCompanyUser(user)) {
                        Stream<Partner> parterStream = (this.securityService.getPartners(company)).stream();
                        legalEntitiesMapped = parterStream
                                .collect(Collectors.toMap(
                                        p -> p.getCode(), // Key mapper: use p.getCode()
                                        p -> p.getFullnameOrName() + " / " + p.getCode() // Value mapper
                                ));
                    }
                } else {
                    for (String legalEntityElem : legalEntities) {
                        String partnerToAdd = this.securityService.getPartner(legalEntityElem, company).getFullnameOrName() + " / " + legalEntityElem;
                        legalEntitiesMapped.put(legalEntityElem, partnerToAdd);
                    }
                }
                sortedLegalEntities.putAll(legalEntitiesMapped);
            } else {
                /*TODO pk ça?*/
                List<LegalEntityDTO> legalEntities = getLegalEntitiesList(user, localeAsString);
                if (legalEntities.isEmpty() || legalEntities.stream()
                        .noneMatch(entity -> entity.getCode()
                                .contains(legalEntity))) {
                    throw new RestJwtException("legal entity is not valid", ExceptionType.BAD_REQUEST);
                }
            }
            //get all the Legal entities that the user can see
            // get companyCode and perimeter for users
            String owner = isPartnerUser(user) ? user.getPrimaryGroup()
                    .getParent()
                    .getCode()
                    : user.getPrimaryGroup()
                    .getCode();
            //get data from table sec_exchange
            Organization org = getCompanyEntity(getCompany(user), legalEntity);
            long totalElements = 0;
            long totalPage = 0;
            List<AapRefSupplierDTO> result = new ArrayList<>();
            if (org instanceof Partner && org != null && ((Partner) org).getAccountPostingId() != null) {
                final Query queryM = getReferentialSupplierQuery(env, org, bql, user, legalEntity);
                //List<Exchange> exchanges = this.eDocumentService.searchExchanges("INVOIC", org, Exchange.Direction.R).stream().filter(r -> r.getId().getLegalEntity().getCode().equals(legalEntity)).collect(Collectors.toList());

                Sort.Direction direction;
                if ("null".equals(sortBy) || "undefined".equals(sortBy)) {
                    sortBy = "id";
                }
                if (ASC.equals(order)) {
                    direction = Sort.Direction.ASC;
                }
                else {
                    direction = Sort.Direction.DESC;
                }
                Pageable pageable;
                Pageable pageableExchange;
                pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.by(direction, "id"));

                pageableExchange = of(
                    0,
                    Integer.MAX_VALUE);


                Page<Exchange> page = this.eDocumentService.searchExchanges(queryM, pageableExchange);
                final RestServiceHelper.RestDocument edoc = RestServiceHelper.RestDocument.valueOfIgnoringCase(
                        ACCOUNTING_POSTING_SUPPLIER_TYPE);
                log.info("RequestId {} : API {} is searching edocuments for owner : {} ({})", requestId, user != null ? user
                        .getLogin() : technicalUser.getClientId(), edoc, owner);

                Locale local = new Locale(isNotBlank(localeAsString) ? localeAsString : "en");
                Page<AapRefSupplierDTO> listRefSupplier = getAapReferentialSupplierListDto(page.getContent(), local, env, pageable, sortBy,
                        order);

                result = listRefSupplier.getContent()
                        .subList(offset * limit, offset == 0
                                ? (listRefSupplier.getContent()
                                .size() >= limit && offset == 0
                                ? limit
                                : listRefSupplier.getContent()
                                .size())
                                : (Math.min((offset + 1) * limit, listRefSupplier.getContent()
                                .size())));

                totalElements = listRefSupplier.getNumberOfElements() < limit
                        ? listRefSupplier.getNumberOfElements()
                        : listRefSupplier.getContent()
                        .size();
                totalPage = listRefSupplier.getTotalPages();
                log.debug("API result: {}", result);
                if (count == true) {
                    long totalCount;
                    totalCount = listRefSupplier.getContent()
                            .size();
                    return ok(getGson().toJson(totalCount)).header("Request-Id", requestId.toString())
                            .build();
                } else {
                    return ok(getGson().toJson(
                            getAapReferentialSupplierList(result, totalElements, totalPage, portletId, localeAsString, userLegalEntity, env,
                                    sortBy, order))).header(
                                    "Request-Id", requestId.toString())
                            .build();
                }
            } else {
                return ok(getGson().toJson(
                        getAapReferentialSupplierList(result, totalElements, totalPage, portletId, localeAsString, userLegalEntity, env, sortBy,
                                order))).header(
                                "Request-Id", requestId.toString())
                        .build();
            }

        } catch (SecurityException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, ACCOUNTING_POSTING_SUPPLIER_TYPE);
            throw new RestSecurityException(e.getMessage());
        } catch (BqlParseException | IllegalArgumentException | NoResultException |
                 NonUniqueResultException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, ACCOUNTING_POSTING_SUPPLIER_TYPE);
            throw new RestException(e.getMessage());
        } catch (Exception e) {
            String message = getRootCauseMessage(e);
            log.error("API eDocument search error: {} ( q='{}', type='{}')",
                    message, bql, ACCOUNTING_POSTING_SUPPLIER_TYPE,e);
            throw new RestException("An unexpected error occurs: " + message);
        }
    }

    private String getStortByFeild(String sortBy) {
        switch(sortBy) {
        case "name" : return "id.dematPartner.name";
        case "code" : return "id.dematPartner.code";
        case "duns" : return "id.dematPartner.duns";
        case "city" : return "id.dematPartner.location.address.city";
        case "country" : return "id.dematPartner.location.address.country";
        case "postalCode" : return "id.dematPartner.location.address.postalCode";
        case "streetName" : return "id.dematPartner.location.address.streetName";
        default: return "id";
        }
    }

    private Page<AapRefSupplierDTO> getAapReferentialSupplierListDto(List<Exchange> exchanges, Locale local, String env, Pageable pageable, String sortBy, Sort.Direction order) {
        List<AapRefSupplierDTO> listRefSupplier = new ArrayList<>();
        for (Exchange e : exchanges) {
            Map<String, Object> criteriaMap = Map.of("legalEntityId", e.getId().getLegalEntity().getId(), "dematPartnerId", e.getId().getDematPartner().getId());
            Page<AapRefSupplier> aapRefSuppliersPage = aapService.getAapRefSuppliers(criteriaMap, pageable);
            if (aapRefSuppliersPage != null && !aapRefSuppliersPage.getContent().isEmpty()) {
                for (AapRefSupplier aapRefSupplier : aapRefSuppliersPage.getContent()) {
                    listRefSupplier.add(transformToAapReferentialSupplierDTO(e, aapRefSupplier, local, env));
                }
            } else {
                listRefSupplier.add(transformToAapReferentialSupplierDTO(e, null, local, env));
            }
        }

        List<AapRefSupplierDTO> modifiableList = new ArrayList<AapRefSupplierDTO>(listRefSupplier);
        if (modifiableList.size() > 0 && sortBy != null && !sortBy.equals("null")) {
            switch (sortBy) {
            case "name":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getName, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getName).reversed());
                break;
            case "code":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCode, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCode, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "registration":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getRegistration, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getRegistration, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "vat":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getVat, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getVat, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "duns":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getDuns, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getDuns, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "streetName":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getStreetName, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getStreetName, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "postalCode":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getPostalCode, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getPostalCode, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "country":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCountry));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCountry).reversed());
                break;
            case "city":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCity, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCity, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            case "creation":
                if (order.name().equals("ASC"))
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCreation, Comparator.nullsLast(Comparator.naturalOrder())));
                else
                    Collections.sort(modifiableList, Comparator.comparing(AapRefSupplierDTO::getCreation, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                break;
            }
        }
        return new PageImpl<>(modifiableList, pageable, modifiableList.size());
    }

    private String cleanQuickSearch(String bql) {
        StringBuilder newBql = new StringBuilder();
        if (bql != null && bql.contains("_text_")) {
            newBql.append(" and ");
            String val = bql.replace("\"_text_\" ~ ", "");
            List<String> matches = Arrays.asList("id.dematPartner.code", "id.dematPartner.name", "id.dematPartner.registration", "id.dematPartner.vat", "id.dematPartner.duns", "id.dematPartner.location.address.streetName", "id.dematPartner.location.address.postalCode", "id.dematPartner.location.address.city", "id.dematPartner.location.address.country")
                    .stream()
                    .map(p -> p + val.replace("_text_", ""))
                    .collect(toList());
            newBql = new StringBuilder();
            for (int i = 0; i < matches.size(); i++) {
                if (matches.get(i).startsWith("id.dematPartner.location.address.country")) {
                    final String bqlFinal = bql;
                    List<Map.Entry<Locale, String>> loc = I18NHelper.getCountryNames(getLocale(), true, true).entrySet().stream().filter(a -> a.getValue().toUpperCase().equals(bqlFinal.substring(10, bqlFinal.length()).replace("*\"", "").toUpperCase())).collect(toList());
                    newBql.append(matches.get(i).replace(matches.get(i).substring(matches.get(i).indexOf("\"") + 1, matches.get(i).indexOf("\"", matches.get(i).indexOf("\"") + 1)), "_" + (loc.size() > 0 ? (((Locale) loc.get(0).getKey()).getCountry()) : (matches.get(i).substring(matches.get(i).indexOf("\"") + 1, matches.get(i).indexOf("\"", matches.get(i).indexOf("\"") + 1)).substring(0, 2)))));
                } else {
                    newBql.append(matches.get(i).replace(matches.get(i).substring(matches.get(i).indexOf("\"") + 1, matches.get(i).indexOf("\"", matches.get(i).indexOf("\"") + 1)), "'" + matches.get(i).substring(matches.get(i).indexOf("\"") + 1, matches.get(i).indexOf("\"", matches.get(i).indexOf("\"") + 1)) + "'"));
                    ;
                }
                if (i < matches.size() - 1)
                    newBql.append(" OR ");
            }
            bql = bql.replace(val, "");
        } else if (bql.contains("country")) {
            if (!bql.contains("_")) {
                final String bqlFinal = bql;
                List<Map.Entry<Locale, String>> loc = I18NHelper.getCountryNames(getLocale(), true, true)
                        .entrySet()
                        .stream()
                        .filter(a -> a.getValue()
                                .toUpperCase()
                                .equals(bqlFinal.substring(10, bqlFinal.length())
                                        .toUpperCase()))
                        .collect(toList());
                bql = loc.size() > 0
                        ? bql.replace((bql.substring(10, bql.length())), ("_" + ((Locale) loc.get(0)
                        .getKey()).getCountry()))
                        : bql.replace((bql.substring(10, bql.length())), ("_" + bql.substring(10, 12)
                        .toUpperCase()));
            }
            bql = bql.replace("country", "id.dematPartner.location.address.country");
        }

        return bql + newBql.toString();
    }

    private Query getReferentialSupplierQuery(String env, Organization org, String bql, User user, String legalEntity) {
        // Convert apostrophes to ASCII encoding (\u0027)
        if (!bql.contains("country")) {
            bql = bql.replace("'", "\\u0027");
        }
        String bqlOrigine = "id.direction=R and id.kind=INVOIC and id.legalEntity.id=" + org.getId();
        String bqlQuick = cleanQuickSearch(bql);

        if (!bql.isEmpty()) {
            bql = bql.replace("code", "id.dematPartner.code")
                    .replace("name", "id.dematPartner.name")
                    .replace("registration", "id.dematPartner.registration")
                    .replace("vat", "id.dematPartner.vat")
                    .replace("duns", "id.dematPartner.duns")
                    .replace("streetName", "id.dematPartner.location.address.streetName")
                    .replace("postalCode", "id.dematPartner.location.address.postalCode")
                    .replace("city", "id.dematPartner.location.address.city")
                    .replace("country", "id.dematPartner.location.address.country")
                    .replace("creation", "id.dematPartner.creation");
            bql = prefixDematPartnerName(bql, env);
        }

        bql = bql.equals("") ? bqlOrigine : bqlOrigine + " and " + (!bql.contains("country") ? bql : bqlQuick);

        if (!bqlQuick.equals("") && bql.contains("_text_ ~")) {
            String val = bql.substring(bql.indexOf("_text_ ~ \""),
                    bql.indexOf("\"", bql.indexOf("\"", bql.indexOf("_text_ ~ \"")) + 2));
            bql = bql.replace(val, "");
            bql = bql + " (" + bqlQuick + ")";
            String guillemet = bql.substring(bql.indexOf("and \"") + 4, bql.indexOf("and \"") + 5);
            bql = bql.replace(guillemet, "").replace("*", "");
        }
        QueryBuilder qb = QueryBuilder.createBuilder();
        qb.append(bql);
        return qb.query();
    }

    /**
     * Prefixes the value of `id.dematPartner.name` with "env-" if a matching condition is detected.
     */
    private String prefixDematPartnerName(String bql, String env) {
        Pattern pattern = Pattern.compile("id\\.dematPartner\\.name\\s*=\\s*\"([^\"]+)\"");
        Matcher matcher = pattern.matcher(bql);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String originalValue = matcher.group(1);
            String newValue = "id.dematPartner.name = \"" + env + "-" + originalValue + "\"";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(newValue));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }


    private Organization getCompanyEntity(Company company, String legalEntity) {
        if (legalEntity == null || legalEntity.isEmpty()) {
            return company;
        }
        return company.getPartners().size() > 0 ? company.getPartners().stream().filter(r -> r.getCode().equals(legalEntity)).findAny().orElse(null) : company;
    }

    private Response executeExportSupplierQuery(HttpServletRequest request, String envCode, String bql, String sortBy,
                                                Sort.Direction order, Long portletId, String localeAsString,
                                                FileType fileType, boolean allIndexesSelected, boolean exportArchive, String legalEntity,
                                                boolean printSingleDocument, Integer limit, Integer offset) throws Exception {
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }

        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);

        String instanceCode = instance.getCode();

        boolean asyncExport = false;
        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        Object task = taskService.getTask(portletId, locale, instanceCode);
        UserTaskDefinition userTaskDefinition = userTaskDefinitionService.getUserTaskDefinitionByPortlet(user, portletId);
        BeanDescriptor descriptor = userTaskDefinition != null ? userTaskDefinition.getDescriptor() : ((AbstractTask) task).getDescriptor();
        Sort.Direction directionOrder = null;
        // String prefix = exportArchive ? "invoices" : "export";


        final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
        String companyCode = userOrgs.getCompanyCode();
        Collection<String> perimeter = userOrgs.getCodes();
        final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), AbstractTask.FIELD_OWNERS,
                        AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, null)
                .query();
        Organization org = getCompanyEntity(getCompany(user), legalEntity);
        final Query queryM = getReferentialSupplierQuery(envCode, org, bql, user, legalEntity);
        Sort.Direction direction;
        if ("null".equals(sortBy) || "undefined".equals(sortBy)) {
            sortBy = "id";
        }
        if (order.equals("ASC")) {
            direction = Sort.Direction.ASC;
        }
        else {
            direction = Sort.Direction.DESC;
        }
        Pageable pageable;
        Pageable pageableExchange;
        pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.by(direction, "id"));

        pageableExchange = of(
            0,
            Integer.MAX_VALUE);
        Page<Exchange> page = (Page<Exchange>) this.eDocumentService.searchExchanges(queryM, pageableExchange);
        final RestServiceHelper.RestDocument edoc = RestServiceHelper.RestDocument.valueOfIgnoringCase(ACCOUNTING_POSTING_SUPPLIER_TYPE);
        List<AapRefSupplierDTO> result;
        Locale local = new Locale(isNotBlank(localeAsString) ? localeAsString : "en");
        if (sortBy == null || sortBy.equals("") || sortBy.equals("null") || sortBy.equals("undefined")) {
            sortBy = "id";
        }



        Page<AapRefSupplierDTO> listRefSupplier = getAapReferentialSupplierListDto(page.getContent(), local, envCode, pageable, sortBy,
                order);
        int itemsCount = page.getContent().size();
        if (itemsCount == 0)
            throw new RestJwtException(
                    i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)),
                    ExceptionType.NOT_FOUND);
        AbstractTask.Range<Integer> listExportRange = ((AbstractIndexableDocumentTask) task).getListExportRange();
        int max = itemsCount > listExportRange.getMax() ? listExportRange.getMax() : itemsCount;
        int asynchronousLimit = (int) ((AbstractIndexableDocumentTask) task).getAsynchronousProcessLimit();
        int documentExportRangeMax = (int) ((AbstractIndexableDocumentTask) task).getDocumentExportRange().getMax();
        int listExportRangeMax = (int) ((AbstractIndexableDocumentTask) task).getListExportRange().getMax();
        if (fileType.name()
                .equals("EXCEL")) {
            asynchronousLimit = ((AapReferentialSupplierTask) task).getAsynchronousListExportRange()
                    .getMax();
        }

        File file;
        result = listRefSupplier.getContent();
        int totalElements = listRefSupplier.getContent().size();
        int totalPage = listRefSupplier.getTotalPages();
        AapReferentialSupplierListDTO aapReferentialSupplierListDTO = new AapReferentialSupplierListDTO();
        aapReferentialSupplierListDTO = getAapReferentialSupplierList(result, totalElements, totalPage, String.valueOf(portletId), localeAsString, legalEntity, envCode, sortBy, order);
        Function<Document, String> vBaseName = viewerBaseName("refSupplier", descriptor);

        if (order != null) {
            directionOrder = Sort.Direction.valueOf(order.toString());
        }
        if (itemsCount < asynchronousLimit) {

            file = RestCommonServiceImpl.prepareDownloadExcel(createExportFileName(exportArchive, String.valueOf(fileType)
                            .toLowerCase(), locale, false), descriptor, locale, user.getDatePattern(),
                listRefSupplier.getContent(), instanceCode);
        } else {
            // Asynchronous download
            asyncExport = true;
            String url = getRequestURL(request);
            file = restCommonService.asynchronousDownload(listExportRangeMax,documentExportRangeMax, (java.util.function.Function<Document, String>) vBaseName,
                    AapRefSupplierDTO.class, fileType, instance, max, asynchronousLimit,
                    createExportFileName(exportArchive, String.valueOf(fileType)
                            .toLowerCase(), locale, false), descriptor, qb, sortBy, directionOrder, user, true, request, instanceCode, url,
                    allIndexesSelected,
                    exportArchive, null);
        }
        if (asyncExport) {
            return ok(i18NService.findByCodeAndLocale("export_oversized_ddl_file_warning_message", locale)).build();
        } else {
            return ok(file, getType(file).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + file.getName())
                    .build();
        }
    }

    @Override
    @Transactional
    public Response getExportedReferentialSupplierExcelFiles(HttpServletRequest request, String envCode, String bql, String sortBy,
                                                             Sort.Direction order, Long portletId, String localeAsString, FileType fileType, boolean allIndexesSelected, boolean exportArchive,
                                                             String legalEntity, boolean printSingleDocument, Integer limit, Integer offset) {
        try {
            return executeExportSupplierQuery(request, envCode, bql, sortBy, order, portletId, localeAsString,
                    fileType, allIndexesSelected, exportArchive, legalEntity, printSingleDocument, limit, offset);
        } catch (Exception e) {
            log.error("Error executing export query logic: {}", e.getMessage());
            throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
        }
    }


    @Override
    @Transactional
    public Response v1getAccountingReferentials(HttpServletRequest request,
        UUID requestId,
        Long portletId,
        String bql,
        Integer limit,
        Integer offset,
        String legalEntity,
        String sortBy,
        com.byzaneo.generix.api.bean.SortOrderDto order,
        String locale,
        boolean isCountEnabled,
        boolean count) {

        if (Objects.isNull(requestId)) {
            requestId = UUID.randomUUID();
        }
        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
        String partnerId = legalEntity;
        String companyCode = userOrgs.getCompanyCode();

        // if we only want the count of the total results
        if (count) {
            limit = Integer.MAX_VALUE;
            offset = 0;
            order = com.byzaneo.generix.api.bean.SortOrderDto.ASC;
            sortBy = "null";
        }
        if (Objects.equals(sortBy, AXE_ANGULARE_WRAPPER)) {
            sortBy = NUMBER_OF_AXES;
        }

        Sort sort;
        // TODO change order type and remove if test and check why we send sortBy undefined from Angular
        if(sortBy == null || sortBy.equals("undefined") || sortBy.equals("null")) {
            sortBy = "id";
        }
        if (order.equals(SortOrderDto.ASC)) {
            sort = Sort.by(ASC, sortBy);
        }
        else {
            sort = Sort.by(DESC, sortBy);
        }

        final PageRequest pageable = of(
                offset < 0 ? 0 : offset,
                limit < 1 ? -1 : limit,
            sort
            );
        pageable.setCount(isCountEnabled);
        pageable.setFromAngular(true);

        Object task = taskService.getTask(portletId);
        AccountingReferentialTask accountingReferentialTask = (AccountingReferentialTask) task;
        String s = RestServiceHelper.prepareBqlForSql(bql,accountingReferentialTask);
        // build the query
        QueryBuilder qb = resolveQueryBuilderForSearchingAccountingReferentials(companyCode, partnerId, URLDecoder.decode(s, Charset.defaultCharset()), order.toString(), sortBy);

        // get the results
        Page<AapReferential> aapReferentialsPage = aapService.getReferentialPage(qb.query(), pageable);
        // if we only want the count of the total results
        if (count) {
            long totalCount = aapReferentialsPage.getContent()
                    .size();
            return ok(getGson().toJson(totalCount)).header("Request-Id", requestId.toString())
                    .build();
        }

        // transform the results to DTO
        AccountingReferentialListDTO listDTO = new AccountingReferentialListDTO();
        listDTO.setContent(aapReferentialsPage.getContent()
                .stream()
                .map(ref -> this.transformToAccountingRefDTO(ref, user.getLocale()))
                .collect(Collectors.toList()));
        listDTO.setTotal(aapReferentialsPage.getNumberOfElements());
        listDTO.setTotalElements(aapReferentialsPage.getTotalElements());
        listDTO.setTotalPages(aapReferentialsPage.getTotalPages());
        listDTO.setPortletId(String.valueOf(portletId));

        return RestServiceHelper.getResponse(Response.Status.OK, getGson().toJson(listDTO));
    }

    @Override
    public Response getExportedAccountingReferentialsExcelFile(
        HttpServletRequest request,
        String envCode,
        Long portletId,
        String bql,
        Integer limit,
        Integer offset,
        String legalEntity,
        String sortBy,
        com.byzaneo.generix.api.bean.SortOrderDto order,
        String localeAsString,
        FileType fileType
    ) {

        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (Objects.isNull(instance)) {
            throw new RestJwtException("Instance not found.", ExceptionType.NOT_FOUND);
        }

        // get all needed for export
        Locale locale = new Locale(localeAsString);
        String instanceCode = instance.getCode();
        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        Object task = taskService.getTask(portletId, locale, instanceCode);
        UserTaskDefinition userTaskDefinition = userTaskDefinitionService.getUserTaskDefinitionByPortlet(user, portletId);
        BeanDescriptor descriptor = userTaskDefinition != null ? userTaskDefinition.getDescriptor() : ((AbstractTask) task).getDescriptor();
        final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
        String companyCode = userOrgs.getCompanyCode();
        String partnerId = legalEntity;


        AccountingReferentialTask accountingReferentialTask = (AccountingReferentialTask) task;
        bql = RestServiceHelper.prepareBqlForSql(bql,accountingReferentialTask);
        // build the query
        QueryBuilder qb = resolveQueryBuilderForSearchingAccountingReferentials(companyCode, partnerId, bql, order.toString(), sortBy);

        // build a pageable because we need to search indexables
        // the pageable is built with a limit of MAX INTEGER VALUE
        // because we need all the results
        Sort sort;
        if(sortBy == null || sortBy.equals("undefined") || sortBy.equals("null")) {
            sortBy = "id";
        }
        if (order.equals(SortOrderDto.ASC)) {
            sort = Sort.by(ASC, sortBy);
        }
        else {
            sort = Sort.by(DESC, sortBy);
        }

        final PageRequest pageable = of(
            0,
            Integer.MAX_VALUE,
            sort
        );
        pageable.setCount(true);
        pageable.setFromAngular(true);

        // get the results
        Page<AapReferential> accountingReferentialsPage = aapService.getReferentialPage(qb.query(), pageable);

        // check if we have what to export
        int itemsCount = accountingReferentialsPage.getContent()
                .size();
        if (itemsCount == 0) {
            throw new RestJwtException(
                    i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)),
                    ExceptionType.NOT_FOUND);
        }

        // build the Excel file
        String fileName = createExportFileName(false, String.valueOf(fileType)
                .toLowerCase(), locale, true);
        // checking if we should display the axes column in excel file
        if (!this.shouldDisplayAxesColumnInExcelFile(partnerId, companyCode)) {
            descriptor.remove("axes");
        }
        File file = RestCommonServiceImpl.prepareDownloadExcel(fileName, descriptor, locale, user.getDatePattern(),
                accountingReferentialsPage.getContent(), instanceCode);
        return ok(file, getType(file).getDefaultMime()).header("Content-Disposition", "attachment;filename=" + file.getName())
        .build();
  }

  @Override
  public Response getExportTemplateAccountingReferentialsExcelFile(HttpServletRequest request) {
    try {
      String headerNames = "dematPartnerId;finalAccountNumber;auxiliaryAccount;counterpartAccount;automaticCounterpart";
      File tmpFile = RestCommonServiceImpl.prepareDownloadTemplateCSV("supplier_accounts.csv",headerNames);
      return ok(tmpFile, getType(tmpFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + tmpFile.getName()).build();
    } catch (ObjectOptimisticLockingFailureException ex) {
      log.error("Optimistic locking failure occurred while exporting Agreements: {}", ex.getMessage());
      return Response.status(Response.Status.CONFLICT)
          .entity("Optimistic locking failure occurred while exporting Agreements. Please try again later.")
          .build();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  @Transactional(isolation = Isolation.READ_COMMITTED)
  public Response importAccountingPostingReferentialSupplierFile(HttpServletRequest request, Long portletId, String localeAsString,
      String legalEntityId, Attachment file) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Company company = getCompany(user);
    Organization org = getCompanyEntity(company, legalEntityId);
    Locale locale = localeAsString != null && !localeAsString.isEmpty() ? new Locale(localeAsString) : Locale.ENGLISH;
    String lineLabel = i18NService.findByCodeAndLocale("line_label", locale, user.getFullname());
    List<List<String>> records = validateCsvHeadersAndGetRecordsFromAttachment(file);
    if (records.isEmpty()) {
      throw new IllegalArgumentException(i18NService.findByCodeAndLocale("empty_file", locale, user.getFullname()));
    }

    final boolean isPartner = isPartnerUser(user);
    Group primaryGroup = user.getPrimaryGroup();
    String owner = isPartner ? primaryGroup.getParent().getCode() : primaryGroup.getCode();

    Set<String> errorLines = new HashSet<>();
    List<String> successLines = new ArrayList<>();
    String errorLabel = i18NService.findByCodeAndLocale("error_label", locale, user.getFullname());

    int line = 0;
    for (List<String> param : records) {
      try {
        String[] supplierAccounts = param.toArray(new String[0]);
        String dematPartnerCode = supplierAccounts[DEMAT_PARTNER_ID_COLUMN];

        if (isPartner) {
          if (StringUtils.isEmpty(dematPartnerCode) || !dematPartnerCode.equals(primaryGroup.getCode())) {
            throw new IllegalArgumentException(i18NService.findByCodeAndLocale("invalid_partner_code", locale, user.getFullname()) + " "+lineLabel+": " + line);
          }
        }

        Group partner = securityService.getPartner(dematPartnerCode, company);
        if (partner == null) {
          throw new IllegalArgumentException(i18NService.findByCodeAndLocale("unkown_supplier_id", locale, user.getFullname()) + ": " + dematPartnerCode);
        }

        String dematPartnerId = partner.getId();
        AccountingPosting accountingPosting = accountingPostingService.getAccountingPostingByOwnerAndPartnerId(owner, legalEntityId);
        if (accountingPosting == null) {
          throw new IllegalArgumentException(i18NService.findByCodeAndLocale("missing_accounting_posting", locale));
        }

        if (dematPartnerId != null) {
          Query query = new QueryBuilder()
              .and(equal("legalEntityId", org.getId()), equal("owner", owner), equal("dematPartnerId", dematPartnerId))
              .query();

          AapRefSupplier aapRefSupplier = aapService.getAapRefSupplier(query);
          if (aapRefSupplier == null) {
            boolean exchangeExist = org.getLegalEntityExchanges().stream().anyMatch(exchange -> exchange.getId().getDematPartner().equals(dematPartnerId));
            if (!exchangeExist) {
              eDocumentService.searchExchange("INVOIC", partner, org, Exchange.Direction.R, null, true);
            }

            createAapRefSupplierRecord(owner, org.getId(), dematPartnerId, supplierAccounts, accountingPosting, locale, user);
            successLines.add(i18NService.findByCodeAndLocale("success_create_supplier", locale, user.getFullname()) + ": " + dematPartnerCode);
          } else {
            fillAapRefSupplierData(aapRefSupplier, owner, supplierAccounts, accountingPosting, false, locale, user);
            successLines.add(i18NService.findByCodeAndLocale("success_update_supplier", locale, user.getFullname()) + ": " + dematPartnerCode);
          }
        }
      } catch (Exception ex) {
        log.error("Error at"+lineLabel+"  {}: {}", line + 1, ex.getMessage(), ex);
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); // Prevent commit in Oracle
        errorLines.add(errorLabel + " ["+lineLabel+" " + (line + 1) + "]: " + ex.getMessage());
      }
      line++;
    }

    if (!errorLines.isEmpty() && successLines.isEmpty()) {
      List<String> sortedErrorList =  accountingReferentialHelper.sortErrorList(errorLines,lineLabel);
      return Response.status(Status.NOT_ACCEPTABLE)
          .entity(String.join("\n", sortedErrorList))
          .build();
    } else if (!errorLines.isEmpty()) {
      List<String> sortedErrorList =  accountingReferentialHelper.sortErrorList(errorLines,lineLabel);
      StringBuilder responseMessage = new StringBuilder();
      responseMessage.append(String.join("\n", successLines)).append("\n")
          .append(String.join("\n", sortedErrorList));
      return Response.status(Status.NOT_ACCEPTABLE)
          .entity(responseMessage.toString())
          .build();
    } else {
      return Response.ok(i18NService.findByCodeAndLocale("all_data_success", locale, user.getFullname())).build();
    }
  }

  private void createAapRefSupplierRecord(String owner,String legalEntityId,String dematPartnerId,String[] suppliersAccounts,AccountingPosting accountingPosting,Locale locale,User user){
    AapRefSupplier aapRefSupplier = new AapRefSupplier();
    aapRefSupplier.setOwner(owner);
    aapRefSupplier.setDematPartnerId(dematPartnerId);
    aapRefSupplier.setLegalEntityId(legalEntityId);
    aapRefSupplier.setCreationDate(new Date());
    aapRefSupplier.setModificationDate(new Date());
    fillAapRefSupplierData(aapRefSupplier,owner,suppliersAccounts,accountingPosting,true,locale,user);
  }
  private void fillAapRefSupplierData(AapRefSupplier aapRefSupplier,String owner,String[] supplierAccounts,AccountingPosting accountingPosting,boolean isCreation,Locale locale,User user) {
    String finalAccountNumber = (supplierAccounts.length > FINAL_ACCOUNT_NUMBER_COLUMN) ? supplierAccounts[FINAL_ACCOUNT_NUMBER_COLUMN] : null;
    String auxiliaryAccount = (supplierAccounts.length > AUXILIARY_ACCOUNT_COLUMN) ? supplierAccounts[AUXILIARY_ACCOUNT_COLUMN] : null;
    Integer counterpartAccountNumber = (supplierAccounts.length > COUNTERPART_ACCOUNT_COLUMN) ? Integer.valueOf(supplierAccounts[COUNTERPART_ACCOUNT_COLUMN]) : null;
    String automaticCounterpart = (supplierAccounts.length > AUTOMATIC_COUNTERPART_COLUMN) ? supplierAccounts[AUTOMATIC_COUNTERPART_COLUMN] : null;
    Integer maximumLengthAccountNumbers = Integer.parseInt(accountingPosting.getMaximumLengthAccountNumbers());
    accountingReferentialHelper.validateFinalAccountNumberForImport(finalAccountNumber, maximumLengthAccountNumbers,locale);
    AapReferential aapReferential = null;
    //in case UseAuxiliaryAccountsSuppliers is false
    QueryBuilder queryAapReferential = QueryBuilder.createBuilder()
        .and(equal("accountingPostingId", accountingPosting.getId()))
        .and(equal("finalAccountNumber", finalAccountNumber));
    Query query = queryAapReferential.query();
    if(accountingPosting.getUseAuxiliaryAccountsSuppliers()){
      accountingReferentialHelper.validateAuxiliaryAccountForImport(auxiliaryAccount,locale);
      query = queryAapReferential.and(equal("auxiliaryAccount", auxiliaryAccount))
          .query();
    }
    aapReferential = aapService.getAapReferential(query);
    if(aapReferential == null){
      String errorFinalAccountLabel = i18NService.findByCodeAndLocale("unkown_referential_id", locale, user.getFullname())+" : "+finalAccountNumber;
      String errorAuxiliaryAccountLabel = i18NService.findByCodeAndLocale("unkown_auxiliary_account", locale, user.getFullname())+" : "+auxiliaryAccount;
      throw new IllegalArgumentException(accountingPosting.getUseAuxiliaryAccountsSuppliers() == true ? errorFinalAccountLabel+" "+errorAuxiliaryAccountLabel: errorFinalAccountLabel);
    }else{
      if(counterpartAccountNumber != null && automaticCounterpart !=null){
        accountingReferentialHelper.validateCounterpartAccountForImport(counterpartAccountNumber.toString(),maximumLengthAccountNumbers,locale);
        Query aapReferentialCounterPartQuery = QueryBuilder.createBuilder()
            .and(equal("accountingPostingId", accountingPosting.getId()))
            .and(equal("finalAccountNumber", counterpartAccountNumber)).query();
        AapReferential aapReferentialForCounterPart =  aapService.getAapReferential(aapReferentialCounterPartQuery);
        if(aapReferentialForCounterPart == null){
          throw new IllegalArgumentException(i18NService.findByCodeAndLocale("unkown_conterpart_account", locale, user.getFullname())+" : "+counterpartAccountNumber);
        }
        aapRefSupplier.setCounterpartAccountId(aapReferentialForCounterPart.getId().intValue());
        automaticCounterpart = validateAutomaticCounterpart(automaticCounterpart);
        aapRefSupplier.setAutomaticCounterpart(automaticCounterpart);
      }else{
        aapRefSupplier.setAutomaticCounterpart(String.valueOf(AutomaticCounterpartStatus.SUSPENDED));
      }
      aapRefSupplier.setAapReferential(aapReferential);
      aapService.saveAapRefSupplier(aapRefSupplier);
    }
  }

  public List<List<String>> validateCsvHeadersAndGetRecordsFromAttachment(Attachment attachment) {
    // Check if the attachment has a .csv file name
    String fileName = attachment.getContentDisposition().getParameter("filename");
    if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
      throw new RestJwtException("File must have a .csv extension", ExceptionType.BAD_REQUEST);
    }
    List<List<String>> records = new ArrayList<>();

    try (InputStream inputStream = attachment.getDataHandler().getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {

      String headerLine = br.readLine();
      if (headerLine == null) {
        throw new RestJwtException("Empty file", ExceptionType.BAD_REQUEST);
      }

      String[] headers = headerLine.split(";");
      Set<String> headerSet = new HashSet<>(Arrays.asList(headers));

      // Check if the headers match the expected headers
        if (!headerSet.equals(EXPECTED_HEADERS)) {
        throw new RestJwtException("Wrong template file", ExceptionType.BAD_REQUEST);
      }
      String line;
      while ((line = br.readLine()) != null) {
          records.add(Arrays.asList(line.split(";")));
      }
      log.info("Headers are valid");
      return records;

    } catch (IOException e) {
      throw new RestJwtException("Error reading file: "+e.getMessage(), ExceptionType.BAD_REQUEST);
    }
  }

  private Response createErrorResponse(String errorMessage) {
    return Response.status(Status.NOT_ACCEPTABLE)
        .entity(errorMessage)
        .build();
  }

    private boolean shouldDisplayAxesColumnInExcelFile(String partnerCode, String companyCode) {
        Partner partner = securityService.getOrganizationByCode(partnerCode, companyCode);
        if (partner == null || partner.getAccountPostingId() == null)
            return false;

        AccountingPosting accountingPosting = aapService.getAccountingPostingByPartnerId(partner.getId());
        if (accountingPosting == null || accountingPosting.getAnalyticalPostingApplicable() == null)
            return false;

        return !accountingPosting.getAnalyticalPostingApplicable().equals(AnalyticalImputation.NO);
    }

    public AccountingRefDTO transformToAccountingRefDTO(AapReferential accountingReferential, Locale locale) {
        // map the accountingReferential to dto
        AccountingRefDTO accountingReferentialDTO = modelMapper.map(accountingReferential,
                AccountingRefDTO.class);

        // resolve the status for translating and custom styling
        com.byzaneo.generix.service.repository.bean.aap.enums.Status status = accountingReferential.getAapStatus();
        Map<String, String> statusMap = new HashMap<>();
        statusMap.put("code", status.toString());
        statusMap.put("value", getLabel(AccountingReferentialTask.LABEL_FAMILY, status.toString(), "", locale));
        statusMap.put("styleClass", status.toString()
                .equals(com.byzaneo.generix.service.repository.bean.aap.enums.Status.Active.toString()) ? "label-status_green label-state" : "label-status_yellow label-state");

        accountingReferentialDTO.setAapStatus(statusMap);

        // resolve the nature for translating
        Nature nature = accountingReferential.getAapNature();
        Map<String, String> natureMap = new HashMap<>();
        natureMap.put("code", nature.toString());
        natureMap.put("styleClass", "label-nature");
        natureMap.put("value", getLabel(AccountingReferentialTask.LABEL_FAMILY, nature.toString(), "", locale));

        accountingReferentialDTO.setAapNature(natureMap);

        // prepare the list of axes for the front-end
        if (CollectionUtils.isNotEmpty(accountingReferential.getAxes())) {
            accountingReferentialDTO.setAxes(new Axes());
            accountingReferentialDTO.getAxes()
                    .setAxes(new ArrayList<AccountingReferentialAxeDTO>());
            accountingReferential.getAxes().stream().forEach(axe -> {
                AccountingReferentialAxeDTO dto =
                    new AccountingReferentialAxeDTO(accountingReferential.getId(), accountingReferential.getFinalAccountNumber(), axe.getId(), axe.getAnalytAxeCode(),
                        axe.getAnalytAxeLabel(),
                        modelMapper.map(accountingReferential, AccountingReferentialDto.class), null);
                accountingReferentialDTO.getAxes()
                        .getAxes()
                        .add(dto);
            });
        }

        return accountingReferentialDTO;
    }

    protected String createExportFileName(boolean list, String type, Locale locale, boolean fromAAPAccountingReferential) {
        return new StringBuilder("export")
                .append(!list ? "-" : "")
                .append(!list ? getLabel("gnxxcblcomlbls", "list", "", locale) : "")
                .append("-")
                .append(getLabel("gnxaccountingpostinglbls",
                        fromAAPAccountingReferential ? "aap-accounting-referential" : "account-referential-supplier", "", locale))
                .append(list && !isBlank(type) ? "-" + type : "")
                .append("-")
                .append(new SimpleDateFormat(PATTERN_DATE).format(Calendar.getInstance()
                        .getTime()))
                .append("-")
                .append(new SimpleDateFormat("HHmmss-SSSSSS").format(Calendar.getInstance()
                        .getTime()))
                .toString();
    }

    private Function<Document, String> viewerBaseName(String baseFileName, BeanDescriptor desc) {
        return document -> Optional.of(document)
                .filter(com.byzaneo.xtrade.bean.Document::isIndexed)
                .map(com.byzaneo.xtrade.bean.Document::<InvoiceIndex>getIndexValue)
                .map(doc -> viewerBaseName(doc, baseFileName, desc))
                .orElseGet(document::getReference);
    }

    private String viewerBaseName(InvoiceIndex invoice, String baseFileName, BeanDescriptor desc) {
        return ofNullable(invoice).map(i -> new StringJoiner("-").add(baseFileName)
                        .add(ofNullable(desc.get("invoiceNumber")).map(p -> p.getDisplayValue(i))
                                .orElse(i.getEntityRef()))
                        .add(ofNullable(desc.get("invoiceIssueDate")).map(p -> p.getDisplayValue(i, null, null, PATTERN_DATE, null))
                                .orElse(""))
                        .toString())
                .orElse("document");
    }
}
