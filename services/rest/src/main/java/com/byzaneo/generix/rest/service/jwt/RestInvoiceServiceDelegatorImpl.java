package com.byzaneo.generix.rest.service.jwt;

import static com.byzaneo.commons.bean.FileType.ARCHIVE;
import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.generix.edocument.service.InvoiceServiceImpl.INVOIC_TYPE;
import static com.byzaneo.generix.edocument.service.impl.CorrectionInvoiceServiceImpl.PREDICATE_APPRSP_DOF;
import static com.byzaneo.generix.edocument.service.impl.CorrectionInvoiceServiceImpl.PREDICATE_INVOICE_DOF;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.PATTERN_DATE;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.isSelfBillingType;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.checkEmptyString;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.checkObjectNullability;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getResponse;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getResponseOnError;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getUserFromRequest;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.transformXcblPathDTO;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_10_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_1_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_2_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_3_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_4_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_5_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_6_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_7_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_8_STR;
import static com.byzaneo.generix.xcbl.portal.task.TriggerActionId.ACTION_9_STR;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.PREDICATE_INVOICE_XCBL;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getAttachedFiles;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getFilesByType;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.xtrade.api.DocumentStatus.TO_CORRECT;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction;
import static java.nio.file.Files.createTempFile;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static javax.ws.rs.core.Response.ok;
import static javax.ws.rs.core.Response.serverError;
import static org.apache.commons.lang3.LocaleUtils.toLocale;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.File;
import java.io.*;
import java.lang.reflect.*;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.*;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.cxf.jaxrs.ext.multipart.*;
import org.modelmapper.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.bean.SaveInvoiceCorrectionDTO;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.invoice.InvoiceHeaderTypeDto;
import com.byzaneo.generix.api.bean.portal.*;
import com.byzaneo.generix.api.service.internal.delegators.RestInvoiceServiceDelegator;
import com.byzaneo.generix.api.util.PermissionHelper;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.edocument.SafeboxException;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.bean.xcbl.XcblPath;
import com.byzaneo.generix.edocument.exception.*;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.task.portal.ArchivedInvoiceTask;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.generix.repository.portal.AAPAccountingEntriesTask;
import com.byzaneo.generix.rest.*;
import com.byzaneo.generix.rest.helper.*;
import com.byzaneo.generix.rest.service.basic.*;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.exception.AccessTokenException;
import com.byzaneo.generix.rtemachine.repository.VariablesOperations;
import com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.aap.AapAccountingEntriesService;
import com.byzaneo.generix.service.repository.util.SortOrderDto;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.generix.xcbl.portal.task.*;
import com.byzaneo.generix.xcbl.portal.task.bean.InvoiceLifeCycle;
import com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper;
import com.byzaneo.generix.xcbl.service.StandardInvoiceEdition.StandardInvoiceEditionService;
import com.byzaneo.generix.xtrade.task.*;
import com.byzaneo.generix.xtrade.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.*;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.IndexableDocument;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document.ProcessingWay;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.google.gson.Gson;

import lombok.*;

@Component
@RequiredArgsConstructor
public class RestInvoiceServiceDelegatorImpl implements RestInvoiceServiceDelegator {

  private static final Logger log = getLogger(RestCommonServiceImpl.class);

  private static final List<String> multiLanguageXsls = Arrays.asList("DeclaredDataFlux1", "errors");

  private static final List<String> multiLanguageXslsLanguages = Arrays.asList("en", "fr");

  private static final List<String> CDARFlux6_en_xsls = Arrays.asList("CDARFlux6", "errors");

  private static final String OCR_VERIFICATION_ACTION = "OCR_VERIFICATION";

  private static final int MASS_REFUSE_VALIDATE_LIMIT = 1000;

  @Autowired
  RestCommonService restCommonService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  protected transient DocumentService documentService;

  @Autowired
  @Qualifier(WorkflowDocumentStatusService.SERVICE_NAME)
  private transient WorkflowDocumentStatusService workflowDocumentStatusService;

  @Autowired
  @Qualifier(WorkflowStepRedirectService.SERVICE_NAME)
  private transient WorkflowStepRedirectService workflowStepRedirectService;

  @Autowired
  @Qualifier(WorkflowNotificationService.SERVICE_NAME)
  private transient WorkflowNotificationService workflowNotificationService;

  @Autowired
  @Qualifier(DocumentTimelineService.SERVICE_NAME)
  private transient DocumentTimelineService documentTimelineService;

  @Autowired
  @Qualifier(AccountService.SERVICE_NAME)
  private AccountService accountService;

  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private InstanceService instanceService;

  @Autowired
  private RestGenericService restGenericService;

  @Autowired
  @Qualifier(TaskService.SERVICE_NAME)
  private TaskService taskService;

  @Autowired
  private UserTaskDefinitionService userTaskDefinitionService;

  @Autowired
  private PermissionHelper permissionHelper;

  @Autowired
  private I18NService i18NService;

  @Getter
  @Autowired
  @Qualifier(EDocumentService.SERVICE_NAME)
  protected transient EDocumentService eDocumentService;

  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configurationService;

  @Autowired
  private AapAccountingEntriesService aapAccountingEntriesService;

  @Autowired
  @Qualifier(SafeboxService.SERVICE_NAME)
  private SafeboxService safeboxService;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  public transient SecurityService securityService;

  @Autowired
  private ModelMapper modelMapper;

  @Autowired
  @Qualifier(RossumService.SERVICE_NAME)
  private transient RossumService rossumService;

  @Autowired(required = false)
  @Qualifier(VariablesOperations.OPERATIONS_NAME)
  private transient VariablesOperations variablesOperations;

  @Autowired
  private transient CorrectInvoiceService correctInvoiceService;

  @Autowired
  private InvoiceService invoiceService;

  @Autowired
  private ActionModuleService actionModuleService;

  @Autowired
  StandardInvoiceEditionService standardInvoiceEditionService;

  private RteRuntime runtime;

  @Override
  @Transactional(readOnly = true)
  public Response v1EnvironmentsEnvCodeInvoicesGet(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      BaseType base, String flowDirection, String sortBy, com.byzaneo.generix.api.bean.SortOrderDto order, boolean count, String portletId,
      String localeAsString,
      boolean isCountEnabled, String uuid) {
    //aapAccountingEntriesService.saveAapAccountingEntries("b1c88169-9cad-4ca4-bbd1-e0fd8c7481e4");
    if (requestId == null) {
      requestId = UUID.randomUUID();
    }
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    bql = FormatHelper.encodeSpecialCharacters(bql);
    try {
      return restCommonService.environmentsEnvCodeInvoicesGet(request, true, URLDecoder.decode(bql, Charset.defaultCharset()), requestId,
          limit, offset, base, flowDirection, sortBy,
          order, count, portletId, localeAsString, isCountEnabled, uuid, user, null);
    }
    catch (ObjectOptimisticLockingFailureException ex) {
      log.error("Optimistic locking failure occurred while searching for invoices: {}", ex.getMessage());
      return Response.status(Response.Status.CONFLICT)
          .entity("Optimistic locking failure occurred while searching for invoices. Please try again later.")
          .header("Request-Id", requestId.toString())
          .build();
    }
    catch (Exception e) {
      log.error("An unexpected error occurred while searching for invoices: {}", e.getMessage());
      return Response.serverError()
          .entity("An unexpected error occurred while searching for invoices. Please contact support for assistance.")
          .header("Request-Id", requestId.toString())
          .build();
    }

  }

  @Override
  @Transactional(readOnly = true)
  public Response v2EnvironmentsEnvCodeInvoicesCount(HttpServletRequest request,
      String bql, UUID requestId, Integer limit, Integer offset, String flowDirection, String sortBy,
      com.byzaneo.generix.api.bean.SortOrderDto order,
      String localeAsString, List<String> selectedSecurityFields, boolean showOnlyWorkflow) {
    if (requestId == null) {
      requestId = UUID.randomUUID();
    }
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    bql = FormatHelper.encodeSpecialCharacters(bql);
    if (CollectionUtils.isNotEmpty(selectedSecurityFields))
      selectedSecurityFields = asList(selectedSecurityFields.get(0)
          .split(","));
    try {
      Long res = invoiceService.countInvoices(request, URLDecoder.decode(bql, Charset.defaultCharset()),
          limit, offset, flowDirection, sortBy,
          order != null ? order.name() : SortOrderDto.ASC.name(), localeAsString, user,
          selectedSecurityFields, showOnlyWorkflow);
      return ok(getGson().toJson(res)).header("Request-Id", requestId.toString())
          .build();
    }
    catch (RequestException e) {
      return getResponseOnError(e.getExceptionCode(), "Flow direction is invalid. Available values: SENDING, RECEIVING",
          e.getStatus(), requestId);
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("API eDocument search error: {} ( q='{}', type='{}')",
          message, bql, INVOIC_TYPE);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }

  @Override
  @Transactional(readOnly = true)
  public Response v2EnvironmentsEnvCodeInvoicesGet(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      String flowDirection, String sortBy, com.byzaneo.generix.api.bean.SortOrderDto order, String portletId,
      String localeAsString, List<String> selectedSecurityFields, boolean showOnlyWorkflow) {
    if (requestId == null) {
      requestId = UUID.randomUUID();
    }
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    bql = FormatHelper.encodeSpecialCharacters(bql);
    if (CollectionUtils.isNotEmpty(selectedSecurityFields))
      selectedSecurityFields = asList(selectedSecurityFields.get(0)
          .split(","));
    try {
      Page<InvoiceIndex> page = invoiceService.searchInvoices(request, URLDecoder.decode(bql, Charset.defaultCharset()),
          limit, offset, flowDirection, sortBy,
          order != null ? order.name() : SortOrderDto.ASC.name(), localeAsString, user,
          selectedSecurityFields, showOnlyWorkflow);
      List result = page.getContent();
      String instanceCode = getInstance(request).getCode();
      localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
      Locale locale = new Locale(localeAsString);
      invoiceService.setRossumIdIfOCRVerifiable(OrganizationHelper.resolveUserOrganizations(user), result, requestId.toString());

      InvoiceListDTO invoiceListDTO = restCommonService.transformInvoiceList(locale, instanceCode, result, limit, offset, page);
      invoiceListDTO.setPortletId(portletId);
      return ok(getGson().toJson(invoiceListDTO)).header("Request-Id", requestId.toString())
          .build();
    }
    catch (RequestException e) {
      return getResponseOnError(e.getExceptionCode(), "Flow direction is invalid. Available values: SENDING, RECEIVING",
          e.getStatus(), requestId);
    }
    catch (SecurityException e) {
      log.error("API eDocument search error: {} (q='{}', type='{}')",
          e.getMessage(), bql, INVOIC_TYPE);
      throw new RestSecurityException(e.getMessage());
    }
    catch (BqlParseException | IllegalArgumentException | NoResultException | NonUniqueResultException e) {
      log.error("API eDocument search error: {} (q='{}', type='{}')",
          e.getMessage(), bql, INVOIC_TYPE);
      throw new RestException(e.getMessage());
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("API eDocument search error: {} ( q='{}', type='{}')",
          message, bql, INVOIC_TYPE);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }

  @Override
  @Transactional
  public Response v1GetInvoiceHeaderByUuid(HttpServletRequest request, String uuid) {
    try {
      String login = RestServiceHelper.getUserFromRequest(request);
      User user = accountService.getUserByLoginOrOpenID(login);
      Document document = restGenericService.getDocumentsByBql(uuid, user);
      Invoice invoice = standardInvoiceEditionService.getInvoice(document);
      InvoiceHeaderTypeDto invoiceHeaderTypeDto = modelMapper.map(invoice.getInvoiceHeader(), InvoiceHeaderTypeDto.class);
      return RestServiceHelper.getResponse(Response.Status.OK, invoiceHeaderTypeDto);
    } catch (Exception e) {
      log.error("Error executing export query logic: {}", e.getMessage());
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }

  }

  @Override
  public Response getInvoiceStages(HttpServletRequest request, String localeAsString) {
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);
    List<String> stages = asList(DocumentStage.values()).stream()
        .map(DocumentStage::toString)
        .collect(toList());
    Map<Object, String> stageMap = IndexableTaskHelper.toStageItems(stages, locale);
    return RestServiceHelper.getResponse(Response.Status.OK, stageMap);
  }

  public Response v1GetAllTimelinesForInvoice(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    List<DocumentTimeline> documentTimelines = documentTimelineService.getAllTimelinesForDocument(
        restGenericService.getDocumentsByBql(uuid, user)
            .getIndexValue()
            .getEntityId());
    List<DocumentTimelineDto> documentTimelineDtos = RestServiceHelper.mapList(documentTimelines, DocumentTimelineDto.class);
    documentTimelineDtos.forEach(documentTimelineDto -> {
      documentTimelineDto.setDelegatorUsername(null);
      documentTimelineDto.setUserName(invoiceService.getDisplayNameForUser(documentTimelineDto.getUser()));
      if (documentTimelineDto.getDelegatorUser() != null) {
        User userDelegator = accountService.getUserByLoginOrOpenID(documentTimelineDto.getDelegatorUser());
        if (userDelegator != null) {
          documentTimelineDto.setDelegatorUsername(userDelegator.getFullname());
        }
        else {
          documentTimelineDto.setDelegatorUsername(documentTimelineDto.getDelegatorUser());
        }

      }
      setActionMessage(documentTimelineDto, user.getLocaleAsString());
    });
    return RestServiceHelper.getResponse(Response.Status.OK, documentTimelineDtos);
  }

  public void setActionMessage(DocumentTimelineDto documentTimelineDto, String localeAsString) {
    String action = documentTimelineDto.getAction();
    localeAsString = StringUtils.isNotEmpty(localeAsString) ? localeAsString : Locale.ENGLISH.toString();
    Locale localeObject = new Locale(localeAsString);
    String actionMessage = documentTimelineDto.getActionMessage();
    if (StringUtils.equals(action, DocumentTimelineDto.TimelineActionWithoutAction.EP_SUBMITED.name())) {
      actionMessage = TimelineHelper.getLabelForEpSubmitedAction("gnxxcblinvlbls", "ep_submited_message", documentTimelineDto, localeObject);
    } else if (StringUtils.equals(action, DocumentTimelineDto.TimelineActionWithoutAction.EP_ACCEPTED.name())) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "early_payment_seller_accept_add_timeline", documentTimelineDto, localeObject);
    } else if (StringUtils.equals(action, DocumentTimelineDto.TimelineActionWithoutAction.EP_REFUSED.name())) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "early_payment_seller_reject_add_timeline", documentTimelineDto, localeObject);
    } else if (StringUtils.equals(action, DocumentTimelineDto.TimelineActionWithoutAction.VALID.name())  && documentTimelineDto.getUser() == "System") {
      actionMessage = TimelineHelper.getLabelForAutomaticValidation("gnxxcblinvlbls", "valid_action_automatic_message", documentTimelineDto, localeObject);
    } else if (StringUtils.equals(action, DocumentTimelineDto.TimelineActionWithoutAction.REMINDER.name()) && documentTimelineDto.getUser() == "System") {
      actionMessage= TimelineHelper.getLabelForAutomaticValidation("gnxxcblinvlbls", "reminder_action_automatic_message", documentTimelineDto, localeObject);
    } else if (StringUtils.equals(action, "REDIRECT")) {
      actionMessage = TimelineHelper.getLabelForAssignTask("gnxxcblinvlbls", "transferTask_action_message", documentTimelineDto,
          localeObject) + " " + documentTimelineDto.getRedirectUser();
    }
    else if (StringUtils.equals(action, "CTRL_RULES_B2G2024")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParametersByProfile("gnxxcblinvlbls", "control_b2g2024_message", documentTimelineDto, localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_RULES_B2B2024")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParametersByProfile("gnxxcblinvlbls", "control_b2b2024_message", documentTimelineDto, localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_RULES_B2B")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParametersByProfile("gnxxcblinvlbls", "control_b2b_message", documentTimelineDto, localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_RULES_B2G")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParametersByProfile("gnxxcblinvlbls", "control_b2g_message", documentTimelineDto, localeObject);
    }
    else if (StringUtils.equals(action, "UPDATE_ACCOUNT_POSTING_MNL")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvcontrollbls", "update_accounting_posting_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "COMPLETE_ACCOUNT_POSTING_MNL")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvcontrollbls", "complete_accounting_posting_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_TRANSMITTED")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvcontrollbls", "aap_transmitted_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_STARTED")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_started_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_NA")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_na_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_MANUAL_MODE")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_manual_mode_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_PARTIAL")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_partial_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_MISSING_PO")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_missing_po_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_UNBALANCED")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_unbalanced_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_MISSING_RCP")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_missing_rcp_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "AAP_MISSING_REF")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "aap_missing_ref_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "DELETE_ATTACHMENT")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvcontrollbls", "delete_attachment_message", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_MODIF_SIREN_CLIENT")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "control_modif_siren_client", documentTimelineDto,
          ";", localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_MODIF_SIREN_SUPPLIER")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "control_modif_siren_supplier", documentTimelineDto,
          ";", localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_MODIF_TVAINTRA_CLIENT")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "control_modif_tvaintra_client", documentTimelineDto,
          ";", localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_MODIF_TVAINTRA_SUPPLIER")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblinvlbls", "control_modif_tvaintra_supplier", documentTimelineDto,
          ";", localeObject);
    }
    else if (StringUtils.equals(action, "CTRL_SCHEMATRON")) {
      actionMessage = TimelineHelper.getMessage("gnxxcblinvlbls.control_schematron_message", "", localeObject, documentTimelineDto.getComment());
    }
    else if (StringUtils.equals(action, "AAP_EXPORTED")) {
      actionMessage = TimelineHelper.getLabelMultipleCommentParameters("gnxxcblcomlbls", "aap_exported_msg", documentTimelineDto,
          localeObject);
    }
    else if (StringUtils.equals(action, "ACTORS_DESIGNATION")) {
      actionMessage = TimelineHelper.getLabelForDesignationOfActors("gnxxcblinvlbls", "actors_designation_message", documentTimelineDto, localeObject);
    }
    String keyActionMessage = TimelineHelper.getActionMessageKey(documentTimelineDto.getAction(), documentTimelineDto.getComment(), documentTimelineDto.getUser());
    actionMessage = actionMessage != null ?
        actionMessage.concat(" "+ MessageHelper.getMessage(keyActionMessage, "", localeObject))
        : MessageHelper.getMessage(keyActionMessage, "", localeObject);
    if(StringUtils.equals(action,"TASK_CANCEL")) {
      actionMessage = actionMessage != null ? actionMessage.concat(" "+documentTimelineDto.getRedirectUser()) : actionMessage;
    }
    documentTimelineDto.setActionMessage(actionMessage);
  }


  private Response executeExportQueryLogic(HttpServletRequest request, String envCode, String bql, String sortBy,
      com.byzaneo.generix.api.bean.SortOrderDto order, Long portletId, String localeAsString,
      FileType fileType, boolean allIndexesSelected, boolean exportArchive,
      boolean printSingleDocument) {
    try {
      return executeExportQuery(request, envCode, bql, sortBy, order, portletId, localeAsString,
          fileType, allIndexesSelected, exportArchive, printSingleDocument);
    }
    catch (Exception e) {
      log.error("Error executing export query logic: {}", e.getMessage());
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  private Response executeExportQuery(HttpServletRequest request, String envCode, String bql, String sortBy,
      com.byzaneo.generix.api.bean.SortOrderDto order, Long portletId, String localeAsString,
      FileType fileType, boolean allIndexesSelected, boolean exportArchive,
      boolean printSingleDocument) throws Exception {
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }

    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);

    String instanceCode = instance.getCode();

    boolean asyncExport = false;
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Object task = taskService.getTask(portletId, locale, instanceCode);
    UserTaskDefinition userTaskDefinition = userTaskDefinitionService.getUserTaskDefinitionByPortlet(user, portletId);
    BeanDescriptor descriptor = userTaskDefinition != null ? userTaskDefinition.getDescriptor() : ((AbstractTask) task).getDescriptor();
    Sort.Direction directionOrder = null;
    // String prefix = exportArchive ? "invoices" : "export";

    if (sortBy != null && !sortBy.equals("null")) {
      boolean valideSortColumns = false;
      String finalSortBy = sortBy;
      if (task instanceof ArchivedInvoiceTask) {
        PropertyDescriptor propertyDescriptor = descriptor.getProperties()
            .stream()
            .filter(o -> o.getName()
                .contains(finalSortBy))
            .findFirst()
            .orElse(null);
        if (propertyDescriptor != null) {
          sortBy = propertyDescriptor.getName();
          valideSortColumns = descriptor.getProperties()
              .stream()
              .anyMatch(column -> column.getName()
                  .equals(propertyDescriptor.getName()));
        }
      }
      else {
        valideSortColumns = descriptor.getProperties()
            .stream()
            .anyMatch(column -> column.getName()
                .equals(finalSortBy));
      }
      if (!valideSortColumns) {
        throw new RestJwtException("sort column not valide", ExceptionType.BAD_REQUEST);
      }
    }

    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    Query query = null;
    if (task instanceof ArchivedInvoiceTask) {
      query = parse(((ArchivedInvoiceTask) task).getBql(bql));
    }
    else {
      query = parse(bql);
    }
    final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, query, AbstractTask.FIELD_OWNERS,
            AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, null)
        .query();
    Class indexableTypeClass = ((AbstractTask) task).getIndexableType();
    if (task instanceof AAPAccountingEntriesTask) {
      indexableTypeClass = InvoiceIndex.class;
    }
    int itemsCount = 0;
    if (task instanceof ArchivedInvoiceTask) {
      itemsCount = Long.valueOf(eDocumentService.countArchiveInvoices(qb))
          .intValue();
    }
    else {
      itemsCount = Long.valueOf(documentService.countIndexable(indexableTypeClass, qb))
          .intValue();
    }
    if (itemsCount == 0)
      throw new RestJwtException(
          i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)),
          ExceptionType.NOT_FOUND);
    AbstractTask.Range<Integer> listExportRange = ((AbstractIndexableDocumentTask) task).getListExportRange();
    int max = itemsCount > listExportRange.getMax() ? listExportRange.getMax() : itemsCount;
    int asynchronousLimit = (int) ((AbstractIndexableDocumentTask) task).getAsynchronousProcessLimit();
    int documentExportRangeMax = (int) ((AbstractIndexableDocumentTask) task).getDocumentExportRange()
        .getMax();
    int listExportRangeMax = (int) ((AbstractIndexableDocumentTask) task).getListExportRange()
        .getMax();
    if (fileType.name()
        .equals("EXCEL")) {
      asynchronousLimit = task instanceof WorkflowMonitoringTask
          ? ((WorkflowMonitoringTask) task).getAsynchronousListExportRange()
          .getMax()
          : (int) ((AbstractIndexableDocumentTask) task).getAsynchronousListExportRange()
              .getMax();
      ;
    }

    File file;
    Function<Document, String> vBaseName = restCommonService.viewerBaseNameForDocument(
        getLabel("gnxxcblcomlbls", "readable_export_file_name", "", locale));

    if (order != null) {
      directionOrder = Sort.Direction.valueOf(order.toString());
    }
    List<IndexableDocument> indexes = new ArrayList<>();
    if (task instanceof ArchivedInvoiceTask) {
      org.springframework.data.domain.PageRequest pageable = isNotBlank(sortBy)
          ? org.springframework.data.domain.PageRequest.of(0, max, directionOrder, sortBy)
          : org.springframework.data.domain.PageRequest.of(0, max);

      indexes = eDocumentService.searchArchives(indexableTypeClass, qb);
    }
    else {
      indexes = restCommonService.searchIndexables(indexableTypeClass, qb, sortBy, directionOrder, max, itemsCount, request,
          exportArchive);
    }

    if (!permissionHelper.isGranted(SecurityService.Resource.Consultation_Status, Right.CREATE, user)) {
      indexes.forEach(indexableDocument -> {
        documentService.markDocumentConsultStatusAsRead(indexableDocument, true, user.getId());
      });
    }
    if (itemsCount == 1 & !exportArchive && printSingleDocument) {
      Optional<DocumentViewer> doc = null;
      try {
        doc = restCommonService.downloadPdf(instance, (InvoiceIndex) indexes.get(0), TransformService.BirtOutoutFileType.PDF,
            user.getPrimaryGroup() instanceof Partner ? (Partner) user.getPrimaryGroup() : null, user, vBaseName);
      }
      catch (TemplateMissingException e) {
        throw new RestJwtException(i18NService.findByCodeAndLocale("error_missing_template", locale), ExceptionType.INTERNAL_ERROR);
      }
      file = doc.get()
          .getCurrentFile()
          .getFile();
      String baseName = !doc.get().getBaseName().endsWith("-") ? doc.get().getBaseName().concat("-"): doc.get().getBaseName();
      String fileName = baseName.concat(doc.get().getCurrentFile().getFile().getName());
      return ok(file, getType(file).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + fileName)
          .build();
    }
    if (itemsCount < asynchronousLimit) {
      file = restCommonService.processingDownloadFile(listExportRangeMax, documentExportRangeMax, vBaseName, indexableTypeClass, fileType,
          max, createExportFileName(exportArchive, String.valueOf(fileType)
              .toLowerCase(), locale), descriptor, qb, sortBy,
          directionOrder, locale, user.getDatePattern(), instance, true, null, user, request, instanceCode, allIndexesSelected,
          exportArchive, "");
    }
    else {
      // Asynchronous download
      asyncExport = true;
      String url = request.getRequestURL()
          .toString();
      file = restCommonService.asynchronousDownload(listExportRangeMax, documentExportRangeMax, vBaseName, indexableTypeClass, fileType,
          instance, max, asynchronousLimit,
          createExportFileName(exportArchive, String.valueOf(fileType)
              .toLowerCase(), locale), descriptor, qb, sortBy, directionOrder, user, true, request, instanceCode, url, allIndexesSelected,
          exportArchive, null);
    }
    if (asyncExport) {
      return ok(i18NService.findByCodeAndLocale("export_oversized_ddl_file_warning_message", locale)).build();
    }
    else {
      return ok(file, getType(file).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + file.getName())
          .build();
    }
  }

  @Override
  public Response getExportedInvoicesExcelFiles(HttpServletRequest request, String envCode, String bql, String sortBy,
      com.byzaneo.generix.api.bean.SortOrderDto order, Long portletId, String localeAsString, FileType fileType,
      boolean allIndexesSelected, boolean exportArchive,
      boolean printSingleDocument) {
    try {
      Response response = executeExportQueryLogic(request, envCode, bql, sortBy, order, portletId, localeAsString,
          fileType, allIndexesSelected, exportArchive, printSingleDocument);
      return response;
    }
    catch (ObjectOptimisticLockingFailureException ex) {
      log.error("Optimistic locking failure occurred while exporting invoices: {}", ex.getMessage());
      return Response.status(Response.Status.CONFLICT)
          .entity("Optimistic locking failure occurred while exporting invoices. Please try again later.")
          .build();
    }
  }

  @Override
  @Transactional(readOnly = true)
  public Response getInvoiceStepsLifeCycle(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Document document = restGenericService.getDocumentsByBql(uuid, user);
    if (document == null) throw new RestJwtException("Document not found", ExceptionType.NOT_FOUND);
    InvoiceLifeCycle invoiceLifeCycle = new InvoiceLifeCycle(document, user.getLocale());
    InvoiceLifeCycleDto invoiceLifeCycleDto = modelMapper.map(invoiceLifeCycle, InvoiceLifeCycleDto.class);
    return RestServiceHelper.getResponse(Response.Status.OK, invoiceLifeCycleDto);
  }

  public Response updatePaymentStatusForInvoices(HttpServletRequest request, String envCode, String bql, Long portletId,
      String localeAsString) {
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);

    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), AbstractTask.FIELD_OWNERS,
            AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, null)
        .query();
    int itemsCount = Long.valueOf(documentService.countIndexable(InvoiceIndex.class, qb))
        .intValue();
    if (itemsCount == 0)
      throw new RestJwtException(
          i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)),
          ExceptionType.NOT_FOUND);
    List<InvoiceIndex> indexes = documentService.searchIndexables(InvoiceIndex.class, qb, null)
        .getContent();
    try {
      for (InvoiceIndex index : indexes) {
        index.setPaymentStatus(PaymentStatus.EP_PERFORMED);
      }
      documentService.saveIndexables(indexes);
      return ok(i18NService.findByCodeAndLocale("paid_status", locale)).build();
    }
    catch (Exception e) {
      return serverError().build();
    }
  }

  protected List<DocumentFile> getDocumentFilesFromArchive(Archive archive, Group group) {
    List<DocumentFile> documentFiles;
    Document document;
    try {
      document = safeboxService.getArchiveFromS3(archive, group);
      if (document != null) {
        documentFiles = document.getFiles();
      }
      else {
        documentFiles = emptyList();
      }
    }
    catch (IOException | ArchiveDownloadFailedException e) {
      log.error(e.getMessage());
      documentFiles = emptyList();
    }
    catch (SafeboxException e) {
      log.error("Failed to retrieve archive", e);
      getCurrentInstance().addCallbackParam("validationFailed", true);
      FacesMessage fm = MessageHelper.toFacesMessage(e.getMessage(), FacesMessage.SEVERITY_ERROR);
      documentFiles = emptyList();
    }
    return documentFiles;
  }

  @Override
  @Transactional(readOnly = true)
  @Deprecated
  // after removing the InvoiceTask we need a refactor for the api used by archiveInvoice and workflowMonitoring in which this method
  // should be removed and the code for ArchiveInvoice must be moved in ArchiveInvoiceDelegatorService
  public Response getAllAttachmentsFilesWithType(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Group company = getCompany(user);
    Object task = taskService.getTask(Long.parseLong(portletId));
    Function<Document, String> documentVBaseName = restCommonService.viewerBaseNameForDocument(
        getLabel("gnxxcblcomlbls", "readable_export_file_name", "", user.getLocale()));
    Function<ArchivedInvoice, String> archiveVBaseName = viewerBaseNameForArchivedInvoice(
        getLabel("gnxxcblcomlbls", "readable_export_file_name", "", user.getLocale()));
    Map<String, List<DocumentFileDto>> result = new HashMap<>();
    if (task instanceof ArchivedInvoiceTask) {
      ArchivedInvoiceTask archivedInvoiceTask = ((ArchivedInvoiceTask) task);
      Query query = null;
      String bql = "invoice.uuid in (" + uuid + ")";
      query = parse(((ArchivedInvoiceTask) task).getBql(bql));
      ArchivedInvoice archive = eDocumentService.getArchive(ArchivedInvoice.class, query);
      archivedInvoiceTask.updateLastAccessedDate(archive);
      List<DocumentFile> documentFiles = getDocumentFilesFromArchive(archive, company);
      String vBaseName = archiveVBaseName.apply(archive)
          .concat("-");
      List<DocumentFileDto> fileDtos = restCommonService.mapFilesToDtos(documentFiles, vBaseName, true);
      result.put("attachedFiles", fileDtos);
    }

    if ((task instanceof InvoiceTask || task instanceof WorkflowMonitoringTask)) {
      Document document = restGenericService.getDocumentsByBql(uuid, user);
      // Get files for all types in one pass using streams
      Map<String, List<DocumentFile>> filesByType = getFilesByType(document);
      List<DocumentFile> errorFiles = restCommonService.getAllErrorFiles(document);
      String vBaseName = documentVBaseName.apply(document)
          .concat("-");

      // Map the files to DTOs
      for (Map.Entry<String, List<DocumentFile>> entry : filesByType.entrySet()) {
        List<DocumentFileDto> fileDtos = restCommonService.mapFilesToDtos(entry.getValue(), vBaseName, true);
        result.put(entry.getKey(), fileDtos);
      }

      // Add error files to the map
      List<DocumentFileDto> errorFileDtos = restCommonService.mapFilesToDtos(errorFiles, vBaseName, true);
      result.put("errorFiles", errorFileDtos);
    }
    return RestServiceHelper.getResponse(Response.Status.OK, result);
  }

  @Override
  @Transactional(readOnly = true)
  public Response v2GetAllAttachmentsFilesWithType(HttpServletRequest request, String uuid) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Map<String, List<DocumentFileDto>> result = restCommonService.getAllAttachmentsFilesWithType(user, uuid);

    return RestServiceHelper.getResponse(Response.Status.OK, result);
  }

  private static Group getCompany(User user) {
    Group company;
    if (user.getPrimaryGroup()
        .getDescription()
        .equals("PARTNER")) {
      company = user.getPrimaryGroup()
          .getParent();
    }
    else {
      company = user.getPrimaryGroup();
    }
    return company;
  }

  @Override
  @Transactional(readOnly = true)
  public Response getAttachmentsFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return restCommonService.getAttachmentsFilesWithType(request, uuid, AttachedFileType.ATTACHED_FILE, false);
  }

  @Override
  @Transactional(readOnly = true)
  public Response updatePaymentStatusForSelectedInvoices(HttpServletRequest request, String envCode, String bql, Long portletId, String localesAsString) {
    return updatePaymentStatusForInvoices(request, envCode, bql, portletId, localesAsString);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getReportedDataAttachmentsFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId,
      String uuid) {
    return restCommonService.getAttachmentsFilesWithType(request, uuid, AttachedFileType.REPORTED_DATA, false);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getErrorFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return restCommonService.getAttachmentsFilesWithType(request, uuid, AttachedFileType.ERROR_FILES, false);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getTaxOriginalFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return restCommonService.getAttachmentsFilesWithType(request, uuid, AttachedFileType.TAX_ORIGINAL, false);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getRelatedFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      boolean fromLifeCycle) {
    return restCommonService.getAttachmentsFilesWithType(request, uuid, AttachedFileType.RELATED_FILES, fromLifeCycle);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getRectificationFiles(HttpServletRequest request, UUID requestId, BaseType base,
      String portletId, String uuid) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    InvoiceIndex invoiceIndex = restGenericService.getInvoiceIndexByBql(uuid, request, companyCode, perimeter);
    if (ProcessingWay.SENDING.equals(invoiceIndex.getProcessingWay())) {
      String bql = "uuid=" + uuid;
    }
    else if (ProcessingWay.RECEIVING.equals(invoiceIndex.getProcessingWay())) {

    }
    return null;
  }

  public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
    Set<Object> seen = ConcurrentHashMap.newKeySet();
    return t -> isBlank((CharSequence) keyExtractor.apply(t)) || seen.add(keyExtractor.apply(t));
  }

  @Override
  public Response getTimeLineXSL(HttpServletRequest request, UUID requestId, BaseType base, String filename, String locale) {
    return generateXslResponse(filename, locale, multiLanguageXsls, multiLanguageXslsLanguages);
  }

  @Override
  public Response getLifeCycleXSL(HttpServletRequest request, UUID requestId, BaseType base, String filename, String locale) {
    return generateXslResponse(filename, locale, CDARFlux6_en_xsls, multiLanguageXslsLanguages);
  }

  private Response generateXslResponse(String filename, String locale, List<String> xslList, List<String> languages) {
    if (filename != null) {
      String stylesheetPathname;
      if (xslList.contains(filename) && languages.contains(locale)) {
        stylesheetPathname = "xsl/" + filename + "_" + locale + ".xsl";
      } else {
        stylesheetPathname = "xsl/" + filename + ".xsl";
      }

      InputStream inputStream = this.getClass()
              .getClassLoader()
              .getResourceAsStream(stylesheetPathname);

      if (inputStream != null) {
        File stylesheetFile = new File(stylesheetPathname);
        return Response.ok(inputStream, getType(stylesheetFile).getDefaultMime())
                .header("Content-Disposition", "attachment; filename=" + filename + ".xsl")
                .build();
      } else {
        return Response.status(Response.Status.NOT_FOUND).build();
      }
    }
    return Response.status(Response.Status.OK)
        .build();
  }


  @Override
  public Response getAttachedFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String path) {
    File file = new File(path);
    return ok(file, getType(file).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + file.getName())
        .build();
  }

  @Override
  @Transactional(readOnly = true)
  @Deprecated
  // after removing the InvoiceTask we need a refactor for the api used by archiveInvoice and workflowMonitoring in which this method
  // should be removed and the code for ArchiveInvoice must be moved in ArchiveInvoiceDelegatorService
  public Response getAttachmentsZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      Boolean relatedFiles, String locale, boolean fromLifeCycle) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Object task = taskService.getTask(Long.parseLong(portletId));
    String nomFichier = "";

    Set<File> files = new HashSet<>();
    File zipFile = null;
    if (task instanceof ArchivedInvoiceTask) {
      ArchivedInvoiceTask archivedInvoiceTask = ((ArchivedInvoiceTask) task);
      Query query = null;
      String bql = "invoice.uuid in (" + uuid + ")";
      query = parse(((ArchivedInvoiceTask) task).getBql(bql));
      ArchivedInvoice archive = eDocumentService.getArchive(ArchivedInvoice.class, query);
      nomFichier = archive.getArchiveId();
      zipFile = new File(FileUtils.getTempDirectory(), nomFichier);

      archivedInvoiceTask.updateLastAccessedDate(archive);
      List<DocumentFile> documentFiles = getDocumentFilesFromArchive(archive, getCompany(user));
      files.addAll(documentFiles.stream()
          .map(DocumentFile::getFile)
          .filter(Objects::nonNull)
          .collect(Collectors.toList()));
    }
    if (task instanceof InvoiceTask) {
      if (!relatedFiles) {
        if (Locale.ENGLISH.getLanguage()
            .equals(locale)) {
          nomFichier = "attachments_" + uuid + ".zip";
        }
        else {
          nomFichier = "piècesAttachées_" + uuid + ".zip";
        }
      }
      else {
        if (Locale.ENGLISH.getLanguage()
            .equals(locale)) {
          nomFichier = "relatedFiles_" + uuid + ".zip";
        }
        else {
          nomFichier = "fichiersAssociés_" + uuid + ".zip";
        }
      }
      zipFile = new File(FileUtils.getTempDirectory(), nomFichier);
      Document document = restGenericService.getDocumentsByBql(uuid, user);
      InvoiceTask invoiceTask = (InvoiceTask) task;
      if (!relatedFiles) {
        files.addAll(getAttachedFiles(document)
            .stream()
            .map(DocumentFile::getFile)
            .filter(Objects::nonNull)
            .collect(Collectors.toList()));
      }
      else {
        files.addAll(InvoiceTaskHelper.getRelatedFiles(document, fromLifeCycle)
            .stream()
            .map(DocumentFile::getFile)
            .filter(Objects::nonNull)
            .collect(Collectors.toList()));
        List<File> errorFiles = DocumentPdfHelper.getDocumentFiles(document)
            .stream()
            .filter(documentFile -> documentFile.getType()
                .equals(FileType.ERROR))
            .map(documentFile -> documentFile.getFile())
            .collect(toList());
        if (!errorFiles.isEmpty()) {
          files.addAll(errorFiles);
        }
        File errorFile = restCommonService.createErrorFileFromDbErrors(document);
        if (errorFile != null) {
          files.add(errorFile);
        }
      }
    }

    try (FileOutputStream zipOut = new FileOutputStream(zipFile)) {
      if (files.size() > 0) {
        com.byzaneo.commons.util.FileHelper.zip(files, zipOut);
      }
    }
    catch (IOException e) {
      throw new RestJwtException("Cannot create zipfile: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }

    return ok(zipFile, getType(zipFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + zipFile.getName())
        .build();
  }

  @Override
  @Transactional(readOnly = true)
  public Response v2GetAttachmentsZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      Boolean relatedFiles, String locale, boolean fromLifeCycle) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    File zipFile = restCommonService.getAttachmentsZipFile(user, uuid, relatedFiles, locale, fromLifeCycle);
    return ok(zipFile, getType(zipFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + zipFile.getName())
        .build();
  }

  @Override
  @Transactional(readOnly = true)
  public Response getDeclaredAndErrorFilesInZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId,
      String uuid, String attachedFileType) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Set<File> files = new HashSet<>();
    File zipFile = new File(FileUtils.getTempDirectory(),
        attachedFileType.equals(AttachedFileType.ERROR_FILES.getName()) ? "errors_" + uuid + ".zip" : "reported_" + uuid + ".zip");
    Response response = restCommonService.getAttachmentsFilesWithType(request, uuid,
        attachedFileType.equals(AttachedFileType.ERROR_FILES.getName()) ? AttachedFileType.ERROR_FILES : AttachedFileType.REPORTED_DATA,
        false);
    Type listType = new TypeToken<ArrayList<DocumentFileDto>>() {
    }.getType();
    ArrayList<DocumentFileDto> filesAttachedList = new Gson().fromJson(response.getEntity()
        .toString(), listType);
    filesAttachedList.stream()
        .forEach(documentFileDto -> {
          files.add(documentFileDto.getFile());
        });
    try (FileOutputStream zipOut = new FileOutputStream(zipFile)) {
      if (files.size() > 0) {
        com.byzaneo.commons.util.FileHelper.zip(files, zipOut);
      }
    }
    catch (IOException e) {
      //throw new RestJwtException("Cannot create zipfile: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
      Response.serverError()
          .entity("Cannot create zipfile: " + e.getMessage())
          .header("Request-Id", requestId.toString())
          .build();
    }
    return ok(zipFile, getType(zipFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + zipFile.getName())
        .build();
  }

  @Override
  @Transactional(readOnly = true)
  public Response getTaxOriginalZipFile(HttpServletRequest request, String portletId, boolean allIndexesSelected, List<String> uuids,
      boolean hasMissingTaxOriginal, boolean hasNonEmptyTaxOriginal) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Set<File> files = new HashSet<>();
    String baseName = getLabel("gnxxcblcomlbls", "tax_original_export_file_name", "", user.getLocale());

    File zipFile = null;
    try {
      zipFile = createTempFile(baseName.concat("-"), ARCHIVE.getExtension()).toFile();
    }
    catch (IOException e) {
      throw new RestJwtException("Cannot create temporary file: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
      List<String> uuidList;
      try (FileOutputStream zipOut = new FileOutputStream(zipFile)) {
        if (allIndexesSelected) {
          final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
          String companyCode = userOrgs.getCompanyCode();
          Collection<String> perimeter = userOrgs.getCodes();
          final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(""), AbstractTask.FIELD_OWNERS,
                  AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, null)
              .query();
          uuidList = documentService.search(qb)
              .stream()
              .map(invoice -> invoice.getUuid())
              .collect(Collectors.toList());
        }
        else {
          uuidList = asList(uuids.get(0)
              .split(","));
        }
        List<File> taxOriginalFilesList = uuidList.stream()
            .map(uuid -> restGenericService.getDocumentsByBql(uuid, user))
            .filter(Objects::nonNull)
            .flatMap(document -> InvoiceTaskHelper.getTaxOriginalFiles(document)
                .stream())
            .map(DocumentFile::getFile)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        files.addAll(taxOriginalFilesList);
        // Check if all the selected invoices have taxOriginal files.
        hasMissingTaxOriginal = uuidList.size() != taxOriginalFilesList.size();
        hasNonEmptyTaxOriginal = !taxOriginalFilesList.isEmpty();

        if (!files.isEmpty()) {
          com.byzaneo.commons.util.FileHelper.zip(files, zipOut);
        }
      }
      catch (IOException e) {
        throw new RestJwtException("Cannot create zipfile: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
      }
    return ok(zipFile, getType(zipFile).getDefaultMime())
        .header("Content-Disposition", "attachment; filename=" + zipFile.getName())
        .header("hasMissingTaxOriginal", String.valueOf(hasMissingTaxOriginal))
        .header("hasNonEmptyTaxOriginal", String.valueOf(hasNonEmptyTaxOriginal))
        .build();
  }

  public Instance getInstance(HttpServletRequest request) {
    Host host = this.instanceService.resolveHost(request.getServerName());
    if (Objects.isNull(host)) throw new RestJwtException("Host not found", ExceptionType.NOT_FOUND);
    return this.instanceService.resolveInstance(host);
  }

  protected String createExportFileName(boolean list, String type, Locale locale) {
    return new StringBuilder("export").append(
            !list ? "-" + getLabel("gnxxcblcomlbls", null, locale.equals(Locale.ENGLISH) ? "list" : "liste", getLocale()) : "")
        .append("-")
        .append(getLabel("gnxxcblcomlbls", null, locale.equals(Locale.ENGLISH) ? "invoice" : "facture", getLocale()).toLowerCase())
        .append(list && !isBlank(type) ? "-" + type : "")
        .append("-")
        .append(new SimpleDateFormat(PATTERN_DATE).format(Calendar.getInstance()
            .getTime()))
        .append("-")
        .append(new SimpleDateFormat("HHmmss-SSSSSS").format(Calendar.getInstance()
            .getTime()))
        .toString();
  }

  private Function<ArchivedInvoice, String> viewerBaseNameForArchivedInvoice(String baseFileName) {
    return archivedInvoice -> restCommonService.viewerBaseName(baseFileName)
        .apply(archivedInvoice);
  }

  @Override
  @Transactional(readOnly = true)
  public Response triggerActions(HttpServletRequest request, UriInfo uriInfo, String envCode, Long portletId, String sortBy,
      Sort.Direction order) {
    Map<String, List<String>> queryParams = uriInfo.getQueryParameters();

    Map<String, String> actionParams = queryParams.entrySet()
        .stream()
        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
            .size() >= 1 ? entry.getValue()
            .get(0) : "", (value1, value2) -> value2));

    String actionId = actionParams.get("action-id");
    String localeAsString = actionParams.get("locale");

    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);

    if (actionId == null) {
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR,
          i18NService.findByCodeAndLocale("action-id query param should not be null", locale));
    }

    String login = RestServiceHelper.getUserFromRequest(request);

    User user = accountService.getUserByLoginOrOpenID(login);
    if (!isTriggerActionAllowed(actionId, user))
      return RestServiceHelper.getResponseOnError("403", "Operation not permitted", Response.Status.FORBIDDEN);
    try {
      String message = invoiceService.triggerActions(user, envCode, portletId, actionParams);
      return ok(message).build();
    }
    catch (TriggerActionException e) {
      throw new RestJwtException(
          i18NService.findByCodeAndLocale(e.getMessage(), RestServiceHelper.getLocaleFromRequest(request)),
          ExceptionType.BAD_REQUEST);
    }
    catch (Exception e) {
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  private boolean isTriggerActionAllowed(String actionId, User user) {
    if (actionId != null) {
      switch (actionId) {
      case ACTION_1_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_1, Right.CREATE, user);
      case ACTION_2_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_2, Right.CREATE, user);
      case ACTION_3_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_3, Right.CREATE, user);
      case ACTION_4_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_4, Right.CREATE, user);
      case ACTION_5_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_5, Right.CREATE, user);
      case ACTION_6_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_6, Right.CREATE, user);
      case ACTION_7_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_7, Right.CREATE, user);
      case ACTION_8_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_8, Right.CREATE, user);
      case ACTION_9_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_9, Right.CREATE, user);
      case ACTION_10_STR:
        return permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Actions_10, Right.CREATE, user);
      default:
        return false;
      }
    }
    return false;
  }

  @Override
  @Transactional(readOnly = true)
  public Response triggerComplianceActions(HttpServletRequest request, UriInfo uriInfo, String envCode, Long portletId) {
    Map<String, List<String>> queryParams = uriInfo.getQueryParameters();
    List<String> keysToRemove = asList("locale", "trigger-archive", "action-id", "bql");
    Map<String, String> actionParams = queryParams.entrySet()
        .stream()
        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue() != null && entry.getValue()
            .get(0) != null && entry.getValue()
            .size() >= 1 ? entry.getValue()
            .get(0) : "", (value1, value2) -> value2));

    String uuid = actionParams.get("uuid");
    checkEmptyString(uuid, "uuid query param should not be null", ExceptionType.BAD_REQUEST);
    String actionId = actionParams.get("action-id");
    checkEmptyString(uuid, "action-id query param should not be null", ExceptionType.BAD_REQUEST);
    String localeAsString = actionParams.get("locale");
    queryParams.keySet()
        .removeAll(keysToRemove);
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    checkObjectNullability(instance, "instance not found", ExceptionType.NOT_FOUND);
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    try {
      Document document = restGenericService.getDocumentsByBql(uuid, user);
      if (document == null) {
        throw new RestJwtException("document not found", ExceptionType.NOT_FOUND);
      }
      invoiceService.onPostValidationComplianceProcess(document, actionId, companyCode, actionParams, locale);
      String messageSuccess = i18NService.findByCodeAndLocale("actions_confirmation_label", locale);
      return ok(messageSuccess).build();
    }
    catch (Exception e) {
      if (e instanceof InvocationTargetException) {
        Throwable originalException = ((InvocationTargetException) e).getTargetException();
        throw new RestJwtException(originalException.getMessage(), ExceptionType.INTERNAL_ERROR);
      }
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }


  @Override
  public Response removeInvoice(HttpServletRequest request, String envCode, String bql, Long portletId, String localeAsString,
      FileType fileType, boolean allIndexesSelected) {
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);

    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), AbstractTask.FIELD_OWNERS,
            AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, null)
        .query();
    int itemsCount = Long.valueOf(documentService.countIndexable(InvoiceIndex.class, qb))
        .intValue();
    if (itemsCount == 0)
      throw new RestJwtException(
          i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)),
          ExceptionType.NOT_FOUND);
    List<IndexableDocument> indexes = restCommonService.searchIndexables(InvoiceIndex.class, qb, null);
    try {
      documentService.removeDocuments(indexes.toArray(new IndexableDocument[indexes.size()]));
      return ok(i18NService.findByCodeAndLocale("delete_invoice", locale)).build();
    }
    catch (Exception e) {
      return serverError().build();
    }
  }

  @Override
  @Transactional
  public Response removeAttachment(HttpServletRequest request, String envCode, String urlAttachment, String uuid, String portletId,
      String locale) {
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }
    locale = locale != null && !locale.isEmpty() ? locale : "en";
    Locale local = new Locale(locale);

    try {
      String login = RestServiceHelper.getUserFromRequest(request);
      User user = accountService.getUserByLoginOrOpenID(login);
      if (!permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Join, Right.DELETE, user)) {
        return RestServiceHelper.getResponseOnError("403", "Operation not permitted", Response.Status.FORBIDDEN);
      }
      Document document = restGenericService.getDocumentsByBql(uuid.toString(), user);
      List<DocumentFile> files = new ArrayList<>();
      files = getAttachedFiles(document);
      files.addAll(InvoiceTaskHelper.getRelatedFiles(document, false));
      Function<Document, String> vBaseName = restCommonService.viewerBaseNameForDocument(
          getLabel("gnxxcblcomlbls", "readable_export_file_name", "", user.getLocale()));
      String fileName = urlAttachment != null ? urlAttachment.replace(vBaseName.apply(document)
          .concat("-"), "") : "";
      DocumentFile fileToRemove = null;
      if (urlAttachment.equals("undefined")) {
        fileToRemove = document.getFiles()
            .stream()
            .filter(dof -> ((ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.CLIENT_READABLE)))
            .collect(toList())
            .get(0);
        document.removeFile(fileToRemove);
      }
      else {
        fileToRemove = files.stream()
            .filter(r -> r.getUri()
                .endsWith(fileName))
            .collect(toList())
            .get(0);
        document.removeFile(fileToRemove);
      }
      Path internFolderPath = fileToRemove.getFile()
          .toPath()
          .toAbsolutePath();
      Path folderPath = Paths.get(fileToRemove.getFile()
              .getAbsolutePath())
          .resolve(internFolderPath)
          .toAbsolutePath();
      File targetFile = folderPath.resolve(fileToRemove.getFile()
              .getName())
          .toFile();
      targetFile.delete();
      Files.delete(internFolderPath);
      return ok(i18NService.findByCodeAndLocale("msg_confirmation_delete_attachment", local))
          .build();
    }
    catch (Exception e) {
      return serverError().build();
    }

  }

  @Override
  @Transactional
  public Response saveAttachedFile(HttpServletRequest request, Long portletId, String localeAsString, UUID uuid, Attachment file,
      String comment) {

    String login = getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    if (!permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Join, Right.CREATE, user)) {
      throw new RestJwtException("Operation not permitted", ExceptionType.UNAUTHORIZED);
    }
    return restCommonService.attachFile(request, file, localeAsString, true, null, comment, user, null, uuid,
        request.getParameter("readable") != null ? Boolean.parseBoolean(request.getParameter("readable")) : false, portletId);
  }

  public List<XcblPath> onCorrectInvoice(final InvoiceIndex invoice, Locale locale) {
    try {
      Document document = this.documentService.getDocument(invoice);

      if (document.getFiles()
          .stream()
          .filter(PREDICATE_INVOICE_XCBL)
          .count() > 1) {
        throw new CorrectInvoiceException("Document invalid : official index is not unique");
      }
      XcblEditionBuilder.XcblEdition xcblEdition = XcblEditionBuilder.builder(document, PREDICATE_INVOICE_DOF, locale)
          .paths(document, PREDICATE_APPRSP_DOF)
          .build();

      return xcblEdition.getPathes();

    }
    catch (Exception e) {
      // if there was any issue reset the fields and return the appropriate error message
      throw new RuntimeException(e);
    }
  }

  @Override
  @Transactional(readOnly = true)
  public Response correctInvoice(HttpServletRequest request, UUID requestId, String portletId, String uuid, String localeAsString) {
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = toLocale(localeAsString);

    try {
      String login = getUserFromRequest(request);
      User user = accountService.getUserByLoginOrOpenID(login);
      // - check API right -
      if (!permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Correct, Right.READ,
          user) || user.isDisabled()) {
        return RestServiceHelper.getResponseOnError("403", "Security error: You don't have the rights to access to the API.",
            Response.Status.FORBIDDEN,
            requestId);
      }
      final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
      String companyCode = userOrgs.getCompanyCode();
      Collection<String> perimeter = userOrgs.getCodes();
      InvoiceIndex invoiceIndex = restGenericService.getInvoiceIndexByBql(uuid, request, companyCode, perimeter);
      List<XcblPath> xcblPath = onCorrectInvoice(invoiceIndex, locale);
      List<InvoiceCorrectionDTO> xcblPathDTO = xcblPath
          .stream()
          .map(xcbl -> transformXcblPathDTO(xcbl))
          .collect(toList());

      return getResponse(Response.Status.OK, xcblPathDTO);
    }
    catch (Exception e) {
      String message = ExceptionUtils.getRootCauseMessage(e);

      return getResponseOnError("500", message, Response.Status.INTERNAL_SERVER_ERROR,
          requestId);
    }

  }

  public boolean checkInvoiceCorrected(Document document, Locale locale) {
    try {
      log.info("check invoice  corrected");
      XcblEditionBuilder.XcblEdition xcblEdition = XcblEditionBuilder.builder(document, PREDICATE_INVOICE_DOF, locale)
          .paths(document, PREDICATE_APPRSP_DOF)
          .build();

      // No error
      if (xcblEdition.getPathes()
          .isEmpty()) {
        return false;
      }

      return !xcblEdition.getPathes()
          .isEmpty();
    }
    catch (Exception e) {
      // if there was any issue reset the fields and return the appropriate error message
      log.error(e.toString());
      throw new RuntimeException(e);
    }
  }

  @Override
  public Response saveCorrectInvoice(HttpServletRequest request, UUID requestId, String portletId, String invoiceUUID,
      String localeAsString, List<SaveInvoiceCorrectionDTO> listCorrectedInvoiceXcbl) {
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = toLocale(localeAsString);
    Document doc = null;
    DocumentFile dofAppRsp = null;
    XcblEditionBuilder.XcblEdition xcblEdition = null;
    try {
      String login = getUserFromRequest(request);
      User user = accountService.getUserByLoginOrOpenID(login);
      if (!permissionHelper.isGranted(SecurityService.Resource.Portlet_Invoice_Correct, Right.READ,
          user) || user.isDisabled()) {
        return RestServiceHelper.getResponseOnError("403", "Security error: You don't have the rights to access to the API.",
            Response.Status.FORBIDDEN,
            requestId);
      }
      // change to use DTO
      List<com.byzaneo.generix.edocument.bean.SaveInvoiceCorrectionDTO> listCorrectedInvoiceXcblMapped = RestServiceHelper.mapList(listCorrectedInvoiceXcbl, com.byzaneo.generix.edocument.bean.SaveInvoiceCorrectionDTO.class);
      PrepareDocumentForCorrectionRspDTO response = correctInvoiceService.prepareDocumentForCorrection(invoiceUUID, locale,
          listCorrectedInvoiceXcblMapped);
      doc = response.getDocument();
      xcblEdition = response.getXcblEdition();
      dofAppRsp = response.getDofAppRsp();
      // save document
      correctInvoiceService.saveAndDetachCurrentDocument(doc);
      // start process
      List<Report> reports = correctInvoiceService.startProcess(portletId, doc);

      // after invoice correction
      InvoiceIndex invoiceIndex = correctInvoiceService.afterInvoiceCorrection(doc.getId(), reports, dofAppRsp, xcblEdition, user);

      String resultMsg;
      if (TO_CORRECT.equals(doc.getStatusAsEnumValue())) {
        if (checkInvoiceCorrected(doc, locale)) {
          resultMsg = "correct_field_error";
          log.error(resultMsg);
        }
        else {
          resultMsg = "no_error_found";
          log.info(resultMsg);
        }
      }
      else {
        resultMsg = "correct_validation";
        log.info(resultMsg);
      }

      return ok(resultMsg).build();

    }
    catch (CorrectInvoiceException e) {
      correctInvoiceService.rollbackCorrection(doc, dofAppRsp, xcblEdition);
      return getResponseOnError("500", e.getMessage(), Response.Status.INTERNAL_SERVER_ERROR,
          requestId);
    }
    catch (DocumentLockingException e) {
      correctInvoiceService.rollbackCorrection(doc, dofAppRsp, xcblEdition);
      return getResponseOnError("400", e.getMessage(), Response.Status.BAD_REQUEST,
          requestId);
    }
    catch (Exception e) {
      String message = ExceptionUtils.getRootCauseMessage(e);
      log.error(e.toString());
      return getResponseOnError("500", message, Response.Status.INTERNAL_SERVER_ERROR,
          requestId);
    }

  }

  @Transactional
  public Response validateWorkflowDocuments(HttpServletRequest request,
      String bql, UUID requestId,
      Integer limit, Integer offset, String flowDirection, String sortBy, Sort.Direction order, boolean count,
      String portletId, String localeAsString, boolean allIndexesSelected, Integer countSelected, boolean showOnlyWorkflow) {

    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = toLocale(localeAsString);
    if (isEmpty(bql) && !allIndexesSelected) {
      String message = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "error_document_select_line", "", locale);
      return RestServiceHelper.getResponseOnError("500", message, Response.Status.INTERNAL_SERVER_ERROR,
          requestId);
    }
    String login = RestServiceHelper.getUserFromRequest(request);

    User user = accountService.getUserByLoginOrOpenID(login);

    // - check API right -
    if (!permissionHelper.isGranted(SecurityService.Resource.Api_permissions_get_invoice, Right.READ,
        user) || user.isDisabled()) {
      return RestServiceHelper.getResponseOnError("403", "Security error: You don't have the rights to access to the API.",
          Response.Status.FORBIDDEN,
          requestId);
    }
    if (limit == null)
      limit = -1;
    if (offset == null)
      offset = 0;
    log.debug("Searching {} eDocuments : {} (page={}, size={})",
        INVOIC_TYPE, bql, offset, limit);

    if (!(flowDirection == null || flowDirection.isEmpty() || flowDirection.equals("RECEIVING") || flowDirection.equals("SENDING"))) {
      return RestServiceHelper.getResponseOnError("400", "Flow direction is invalid. Available values: SENDING, RECEIVING",
          Response.Status.BAD_REQUEST, requestId);
    }

    try {
      // get companyCode and perimeter for users
      final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
      String companyCode = userOrgs.getCompanyCode();
      Collection<String> perimeter = userOrgs.getCodes();

      final QueryBuilder qbuilder = allIndexesSelected ? OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, null,
          AbstractTask.FIELD_OWNERS,
          AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY, flowDirection) :
          OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), AbstractTask.FIELD_OWNERS,
              AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY, flowDirection);

      Instance instance = getInstance(request);
      Query qb = null;
      if (showOnlyWorkflow) {
        qb = invoiceService.resolveSearchQuery(qbuilder, user, showOnlyWorkflow);
      }

      boolean unauthorizedValidation = false;
      Indexable[] indexes = getIndexables(qb, sortBy, order, request);
      if (indexes.length == 0) {
        String message = MessageHelper.getMessage("gnxxcblinvlbls" + "." + "unauthorizedValidation", "", locale);
        return RestServiceHelper.getResponseOnError("404", message, Response.Status.NOT_FOUND,
            requestId);
      }
      for (Indexable index : indexes) {
        Document document = this.documentService.getDocument(index);
        WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
            workflowDocumentStatusService.getWorkflowForDocument(index.getEntityId()));
        if (wDocStatus != null && (StringUtils.isBlank(wDocStatus.getUserId()) || user.getId()
            .equals(wDocStatus.getUserId()))) {
          boolean result = invoiceService.validateDocument(index, document, user, localeAsString, instance);
          if (!result) {
            String errorMessage = String.format("Couldn't find the workflow status related to the document {}", document.getNumber());
            RestServiceHelper.getResponseOnError("404", errorMessage, Response.Status.NOT_FOUND,
                requestId);
          }
        }
        else
          unauthorizedValidation = true;
      }
      if (unauthorizedValidation || (!allIndexesSelected && indexes.length != countSelected)) {
        log.error("One or more invoices have already been validated or locked by another user.");
        String message = MessageHelper.getMessage("gnxxcblinvlbls" + "." + "unauthorizedValidation", "", locale);
        return RestServiceHelper.getResponseOnError("500", message, Response.Status.INTERNAL_SERVER_ERROR,
            requestId);
      }
      return ok(indexes.length < MASS_REFUSE_VALIDATE_LIMIT
          ? i18NService.findByCodeAndLocale("documents_validated", locale)
          : i18NService.findByCodeAndLocale("mass_validate_or_refuse_limit_reached", locale))
          .build();

    }
    catch (Exception e) {
      String message = ExceptionUtils.getRootCauseMessage(e);
      log.error("API eDocument search error: {} ( q='{}', type='{}')",
          message, bql, INVOIC_TYPE);
      return RestServiceHelper.getResponseOnError("500", message, Response.Status.INTERNAL_SERVER_ERROR,
          requestId);
    }
  }

  @Transactional
  public Response refuseWorkflowDocuments(HttpServletRequest request, String bql, UUID requestId, Integer limit,
      Integer offset, String flowDirection, String sortBy, Sort.Direction order, boolean count, String portletId, String localeAsString,
      boolean allIndexesSelected, Integer countSelected, boolean showOnlyWorkflow) {
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = toLocale(localeAsString);
    String login = RestServiceHelper.getUserFromRequest(request);

    User user = accountService.getUserByLoginOrOpenID(login);

    // - check API right -
    if (!permissionHelper.isGranted(SecurityService.Resource.Api_permissions_get_invoice, Right.READ,
        user) || user.isDisabled()) {
      return RestServiceHelper.getResponseOnError("403", "Security error: You don't have the rights to access to the API.",
          Response.Status.FORBIDDEN,
          requestId);
    }

    if (!(flowDirection == null || flowDirection.isEmpty() || flowDirection.equals("RECEIVING") || flowDirection.equals("SENDING"))) {
      return RestServiceHelper.getResponseOnError("400", "Flow direction is invalid. Available values: SENDING, RECEIVING",
          Response.Status.BAD_REQUEST, requestId);
    }

    try {
      // get companyCode and perimeter for users
      final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
      String companyCode = userOrgs.getCompanyCode();
      Collection<String> perimeter = userOrgs.getCodes();

      final QueryBuilder qbuilder = allIndexesSelected ? OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, null,
          AbstractTask.FIELD_OWNERS,
          AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY, flowDirection) :
          OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), AbstractTask.FIELD_OWNERS,
              AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY, flowDirection);

      Query q = null;
      Indexable[] indexes = new Indexable[0];
      if (showOnlyWorkflow) {
        q = qbuilder.query();
        indexes = getIndexables(q, sortBy, order, request);
        invoiceService.refuseWorkflowDocuments(true, indexes, locale, q, false, true, user, null, sortBy, order, countSelected, "");
      }
      return ok(indexes.length < MASS_REFUSE_VALIDATE_LIMIT
          ? i18NService.findByCodeAndLocale("documents_refused", locale)
          : i18NService.findByCodeAndLocale("mass_validate_or_refuse_limit_reached", locale))
          .build();

    }
    catch (Exception e) {
      String message = ExceptionUtils.getRootCauseMessage(e);
      log.error("API eDocument search error: {} ( q='{}', type='{}')",
          message, bql, INVOIC_TYPE);
      return RestServiceHelper.getResponseOnError("500", message, Response.Status.INTERNAL_SERVER_ERROR,
          requestId);
    }
  }

  private Indexable[] getIndexables(Query q, String sortBy, Sort.Direction order, HttpServletRequest request) {
    Sort.Direction direction = order != null && order.equals(SortOrderDto.ASC) ? Sort.Direction.ASC : Sort.Direction.DESC;
    List<IndexableDocument> invoices = restCommonService.searchIndexables(InvoiceIndex.class, q, sortBy, direction, 0,
        MASS_REFUSE_VALIDATE_LIMIT, request,
        false);
    return invoices.toArray(new Indexable[invoices.size()]);
  }

  @Override
  public Response getCompliance(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Document document = restGenericService.getDocumentsByBql(uuid, user);

    List<ComplianceDto> complianceList = new ArrayList<>();

    String okStatusValue = i18NService.findByCodeAndLocale("invoice_compliance_status_ok", user.getLocale());
    List<DocumentTimeline> documentTimelines = documentTimelineService.getAllTimelinesForDocument(
        restGenericService.getDocumentsByBql(uuid, user)
            .getIndexValue()
            .getEntityId());
    documentTimelines.stream()
        //Compliance should contain only timeline that start with "CTRL_"
        .filter(documentTimeline -> documentTimeline.getAction()
            .startsWith("CTRL_"))
        .forEach(documentTimeline -> {
          ComplianceDto complianceDto = new ComplianceDto(
              "",
              getControlActionMessage(documentTimeline, user.getLocale()),
              "ok",
              okStatusValue,
              documentTimeline.getDate()
          );
          complianceList.add(complianceDto);
        });

    String errorStatusValue = i18NService.findByCodeAndLocale("invoice_compliance_status_error", user.getLocale());
    AtomicReference<Locale> locale = new AtomicReference<>();
    List<DocumentError> eDocumentErrors = EDocumentErrorHelper.getDocumentErrorService()
        .findErrors(document);
    eDocumentErrors.forEach(documentError -> {
      //firstly we will search for translations in xtd_doc_error_translation
      Map<Locale, String> labelsForCode = EDocumentErrorHelper.getDocumentErrorTranslationService()
          .getLabelsForCode(documentError.getErrorCode());
      String errorLabel=null;
      if (!labelsForCode.isEmpty()) {
        locale.set(user.getLocale());
        errorLabel = isBlank(documentError.getErrorCode()) ? documentError.getDefaultLabel() : labelsForCode.get(locale.get());
        if (errorLabel == null) {
          //If the error translation is not available in French (or any other language), we use by default the English translation.
          locale.set(new Locale("en"));
          errorLabel = labelsForCode.get(locale.get());
        }
      }
      String[] errorLabelSplit;
      Map<String, String> rulePatternByLocale = new HashMap<>();
      //Split errorLabel to Rule/Reason based on pattern ', Rule'.
      //If there is new language add its rule here.
      rulePatternByLocale.put("en", ", rule");
      rulePatternByLocale.put("fr", ", règle");
      String splitRule = rulePatternByLocale.get(locale.get()
          .getLanguage());
      String rule = null;
      String reason = null;
      //if errorLabel is null here, it means that the translations for that error code doesn't exist in xtd_doc_error_translation
      //so, we will use default label
      if (errorLabel == null)
        errorLabel = documentError.getDefaultLabel();
      if (errorLabel != null) {
        errorLabelSplit = errorLabel.split(splitRule);
        if (errorLabelSplit != null) {
          rule = (errorLabelSplit.length > 1) ? errorLabelSplit[1] : "";
          reason = (errorLabelSplit.length > 1) ? errorLabelSplit[0] : errorLabel;
        }
        else {
          //in this case error label is given by default label, and don't have a structure like reason, rule
          reason = errorLabel;
        }
      }
      if (reason != null) {
        ComplianceDto complianceDto = new ComplianceDto(
            rule,
            reason,
            "error",
            errorStatusValue,
            documentError.getCreationDate()
        );
        complianceList.add(complianceDto);
      }
    });

    complianceList.sort(Comparator.comparing(ComplianceDto::getCreationDate));
    return RestServiceHelper.getResponse(Response.Status.OK, complianceList);
  }

  @Override
  @Transactional
  public Response addActionInTimeline(HttpServletRequest request, UUID requestId, String uuid, DocumentTimelineDto documentTimelineDto) {
    try {
      String login = RestServiceHelper.getJWTUsername(request);
      User user = accountService.getUserByLoginOrOpenID(login);
      Document document = restGenericService.getDocumentsByBql(uuid, user);
      String action = documentTimelineDto.getActionName();
      if (action == null)
        throw new RestJwtException("Action name should not be null", ExceptionType.BAD_REQUEST);
      documentTimelineService.saveActionInTimeline(document, user, TimelineAction.valueOf(action),
          documentTimelineDto.getComment(), documentTimelineDto.getNumStep());
      return RestServiceHelper.getResponse(Response.Status.OK, documentTimelineDto);
    }
    catch (Exception e) {
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
    }
  }

  @Override
  @Transactional
  public Response addActionsInTimeline(HttpServletRequest request, DocumentTimelineDto documentTimelineDto) {
    try {
      String login = RestServiceHelper.getJWTUsername(request);
      User user = accountService.getUserByLoginOrOpenID(login);

      List<String> uuids = documentTimelineDto.getUuids();

      if (uuids == null || uuids.isEmpty()) {
        throw new RestJwtException("UUIDs list should not be null or empty", ExceptionType.BAD_REQUEST);
      }

      List<Document> documents = uuids.stream()
          .map(uuid -> restGenericService.getDocumentsByBql(uuid, user))
          .filter(Objects::nonNull)
          .collect(Collectors.toList());

      String action = documentTimelineDto.getActionName();
      if (action == null) {
        throw new RestJwtException("Action name should not be null", ExceptionType.BAD_REQUEST);
      }

      documentTimelineService.saveActionsInTimeline(documents, user, TimelineAction.valueOf(action),
          documentTimelineDto.getComment(), documentTimelineDto.getNumStep());

      return RestServiceHelper.getResponse(Response.Status.OK, documentTimelineDto);
    }
    catch (Exception e) {
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
    }
  }

  @Override
  public Response getAllowedActions(HttpServletRequest request, String invoiceUuid) {
    Set<LifeCycleStatusFunctionHelper.Party> parties = new HashSet<>();
    Properties properties = new Properties();
    runtime = new RteRuntime(properties);

    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    if (Company.DESCRIPTION.equals(user.getPrimaryGroup()
        .getDescription()))
      return RestServiceHelper.getResponse(Response.Status.OK, emptyList());
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();

    InvoiceIndex index = restGenericService.getInvoiceIndexByBql(invoiceUuid, request, companyCode, perimeter);

    if (perimeter.contains(index.getTo()))
      parties.add(isSelfBillingType(index.getInvoiceTypeCoded())
          ? LifeCycleStatusFunctionHelper.Party.SELLER
          : LifeCycleStatusFunctionHelper.Party.BUYER);
    if (perimeter.contains(index.getFrom()))
      parties.add(isSelfBillingType(index.getInvoiceTypeCoded())
          ? LifeCycleStatusFunctionHelper.Party.BUYER
          : LifeCycleStatusFunctionHelper.Party.SELLER);
    if (perimeter.contains(index.getBuyerPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.BUYER);
    if (perimeter.contains(index.getSellerPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.SELLER);
    if (perimeter.contains(index.getBillToPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.BILLTOPARTY);
    if (perimeter.contains(index.getSellerAgentPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.SELLERAGENT);
    if (perimeter.contains(index.getBuyerAgentPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.BUYERAGENT);
    if (perimeter.contains(index.getFactorPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.FACTOR);
    if (perimeter.contains(index.getPayerPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.PAYER);
    if (perimeter.contains(index.getRemitToPartyID()))
      parties.add(LifeCycleStatusFunctionHelper.Party.REMITTOPARTY);

    Set<String> allowedActions = ComplianceHelper.getAllowedActions(runtime, index.getStage(), parties);
    return RestServiceHelper.getResponse(Response.Status.OK, allowedActions);
  }

  @Override
  @Transactional
  public Response updateInvoiceStatus(HttpServletRequest request, String localeAsString, String uuid, String statusInvoiceValidation) {
    try {
      String login = RestServiceHelper.getUserFromRequest(request);
      User user = accountService.getUserByLoginOrOpenID(login);
      final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
      String companyCode = userOrgs.getCompanyCode();
      Collection<String> perimeter = userOrgs.getCodes();
      InvoiceIndex invoiceIndex = restGenericService.getInvoiceIndexByBql(uuid, request, companyCode, perimeter);
      if (invoiceIndex != null) {
        DocumentStatusEntityInterface documentStatusEntityInterface = new DocumentStatusEntity();
        documentStatusEntityInterface.setStatusCode(statusInvoiceValidation);
        invoiceIndex.setStatus(documentStatusEntityInterface);
        this.documentService.saveIndexable(invoiceIndex);
        return RestServiceHelper.getResponse(Response.Status.OK, invoiceIndex);
      }
      return RestServiceHelper.getResponse(Response.Status.NO_CONTENT, "No content");
    }
    catch (Exception e) {
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
    }
  }

  @Override
  public Boolean canPerformAction(String userLogin, String actionName) {
    if (OCR_VERIFICATION_ACTION.equalsIgnoreCase(actionName)) {
      return canPerformOCRVerification(userLogin);
    }

    return null;
  }

  private Boolean canPerformOCRVerification(String userLogin) {
    User user = accountService.getUserByLoginOrOpenID(userLogin);
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);

    return rossumService.isRossumServiceActive() && variablesOperations.hasAuthenticationChannelAccessTokenV1ForUse(AccessTokenV1Use.ROSSUM,
        userOrgs.getPartner(), userOrgs.getCompany());
  }

  @Override
  public String fetchRossumUrl(String userLogin, String requestUrl, String rossumId, String uuid, String type)
      throws AccessTokenException, URISyntaxException, IOException, InterruptedException {
    User user = accountService.getUserByLoginOrOpenID(userLogin);
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);

    String rossumKey = variablesOperations.getAccessTokenV1(AccessTokenV1Use.ROSSUM, userOrgs.getPartner(), userOrgs.getCompany());
    return rossumService.getRossumStudioUrl(rossumId, requestUrl + "/ocr-return",
        rossumKey, "invoice=" + uuid + (isNotBlank(type) ? "&type=" + type : ""));
  }

  @Override
  public void updateInvoicesStatus(String userLogin, List<String> invoices, DocumentStatus status, TimelineAction action) {
    List<Document> documents = new ArrayList<>();
    List<DocumentTimeline> documentTimelines = new ArrayList<>();
    User user = accountService.getUserByLoginOrOpenID(userLogin);

    for (String uuid : invoices) {
      Document document = documentService.getDocumentByUUID(uuid);
      InvoiceIndex index = documentService.getIndexable(document);
      document.setStatusWithEnumValue(status);
      index.setStatusWithEnumValue(status);
      document.setIndexValue(index);
      documents.add(document);
      if (action != null)
        documentTimelines.add(new DocumentTimeline(document, new Date(), action, user.getId()));
    }

    documentService.saveDocuments(documents);
    documentTimelineService.storeAll(documentTimelines);
  }

  private String getControlActionMessage(DocumentTimeline documentTimeline, Locale locale) {
    String key = "";
    String[] args = null;
    switch (documentTimeline.getAction()) {
    case "CTRL_RULES_FR":
      key = "control_fr_message";
      break;
    case "CTRL_RULES_EU":
      key = "control_eu_message";
      break;
    case "CTRL_DUPLICATE":
      key = "control_duplicate_message";
      break;
    case "CTRL_RULES_B2G2024":
      key = "control_b2g2024_message";
      args = documentTimeline.getComment()
          .split("-");
      if (args != null && args.length == 2)
        key = "control_b2g2024_message_profile";
      break;
    case "CTRL_RULES_B2B2024":
      key = "control_b2b2024_message";
      args = documentTimeline.getComment()
          .split("-");
      if (args != null && args.length == 2)
        key = "control_b2b2024_message_profile";
      break;
    case "CTRL_RULES_B2B":
      key = "control_b2b_message";
      args = documentTimeline.getComment()
          .split("-");
      if (args != null && args.length == 2)
        key = "control_b2b_message_profile";
      break;
    case "CTRL_RULES_B2G":
      key = "control_b2g_message";
      args = documentTimeline.getComment()
          .split("-");
      if (args != null && args.length == 2)
        key = "control_b2g_message_profile";
      break;
    case "CTRL_MODIF_SIREN_CLIENT":
      key = "control_modif_siren_client";
      args = documentTimeline.getComment()
          .split(";");
      break;
    case "CTRL_MODIF_SIREN_SUPPLIER":
      key = "control_modif_siren_supplier";
      args = documentTimeline.getComment()
          .split(";");
      break;
    case "CTRL_MODIF_TVAINTRA_CLIENT":
      key = "control_modif_tvaintra_client";
      args = documentTimeline.getComment()
          .split(";");
      break;
    case "CTRL_MODIF_TVAINTRA_SUPPLIER":
      key = "control_modif_tvaintra_supplier";
      args = documentTimeline.getComment()
          .split(";");
      break;
    case "LEGAL_ACTION_COMPLETED":
      key = "legal_action_completed";
      break;
    case "LEGAL_ACTION_PAYMENT_RECEIVED":
      key = "legal_action_payment_received";
      break;
    case "LEGAL_ACTION_APPROVED":
      key = "legal_action_approved";
      break;
    case "LEGAL_ACTION_REFUSED":
      key = "legal_action_refused";
      break;
    case "LEGAL_ACTION_APPROVED_SUBROGATE":
      key = "legal_action_approved_subrogate";
      break;
    case "LEGAL_ACTION_PAYMENT_SENT":
      key = "legal_action_payment_sent";
      break;
    case "LEGAL_ACTION_FACTOR":
      key = "legal_action_factor";
      break;
    case "LEGAL_ACTION_FACTOR_CONFIDENTIAL":
      key = "legal_action_factor_confidential";
      break;
    case "AAP_EXPORTED":
      key = "aap_exported";
    case "CTRL_SCHEMATRON":
      key = "control_schematron_message";
      break;
    default:
      return documentTimeline.getComment();
    }
    if (args != null)
      return TimelineHelper.getMessage("gnxxcblinvlbls" + "." + key, "", locale, args);
    else
      return TimelineHelper.getMessage("gnxxcblinvlbls" + "." + key, "", locale, documentTimeline.getComment());
  }

  @Override
  @Transactional
  public Response attachMultiFiles(HttpServletRequest request, Long portletId, UUID invoiceUid,
      MultipartBody documentDetail, UUID requestId, String localeAsString) {

    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    String newlocaleAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(newlocaleAsString);
    if (!permissionHelper.isGranted(SecurityService.Resource.Api_permissions_get_invoice, Right.UPDATE, user) || user.isDisabled()) {
      log.error("Security error: You don't have the rights to access to the API.");
      return RestServiceHelper.getResponseOnError("403", "Forbidden.",
          Response.Status.FORBIDDEN,
          requestId);
    }
    if (documentDetail == null) {
      log.error("The file to attach is required.");
      return RestServiceHelper.getResponseOnError("400", "Bad Request.",
          Response.Status.BAD_REQUEST,
          requestId);
    }
    Document document = restGenericService.getDocumentsByBql(invoiceUid.toString(), user);
    List<DocumentFile> files = new ArrayList<>();
    files = getAttachedFiles(document);
    List<Attachment> attachments = documentDetail.getAllAttachments();
    String comment = documentDetail.getAttachmentObject("comment", String.class);
    List<Attachment> copyAttachments = new ArrayList<>(attachments);
    copyAttachments.remove(attachments.size() - 1);
    if (copyAttachments == null) {
      log.error("The file to attach is required.");
      return RestServiceHelper.getResponseOnError("400", "Bad Request.",
          Response.Status.BAD_REQUEST,
          requestId);
    }
    boolean fileNotExists = checkFilesExists(files, attachments);

    if (!fileNotExists) {
      String messageError = MessageHelper.getMessage("edctsklbls" + "." + "att_attachment_error_joining_filealreadyexists", "", locale);
      throw new RestJwtException(messageError, ExceptionType.INTERNAL_ERROR);
    }
    if (comment != null && comment.length() > 255) {
      log.error("Max length for comment is 255.");
      return RestServiceHelper.getResponseOnError("400", "Bad Request.",
          Response.Status.BAD_REQUEST,
          requestId);
    }
    List<String> paths = new ArrayList<>();
    copyAttachments.forEach(attachment -> {
      File targetFile = restCommonService.attachFileToInvoice(request, attachment, localeAsString, true, requestId, comment, user, null,
          invoiceUid);
      if (targetFile != null) {
        paths.add(targetFile.getAbsolutePath());
      }
    });
    return RestServiceHelper.getResponse(Response.Status.OK, paths);
  }

  private boolean checkFilesExists(List<DocumentFile> files, List<Attachment> attachments) {
    return attachments.stream()
        .noneMatch(attachment -> files.stream()
            .anyMatch(documentFile -> documentFile.getFile()
                .getName()
                .equals(attachment.getContentDisposition()
                    .getFilename())));
  }
}
