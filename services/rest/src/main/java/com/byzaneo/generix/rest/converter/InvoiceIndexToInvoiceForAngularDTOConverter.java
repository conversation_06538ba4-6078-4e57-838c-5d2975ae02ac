package com.byzaneo.generix.rest.converter;
import com.byzaneo.generix.api.bean.InvoiceDTO;
import com.byzaneo.generix.api.bean.InvoiceDetailDTO;
import com.byzaneo.generix.api.bean.InvoiceForAngularDTO;
import com.byzaneo.generix.api.bean.ValueAddedTaxDTO;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.util.DocumentStyleHelper;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import lombok.RequiredArgsConstructor;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.spi.MappingContext;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class InvoiceIndexToInvoiceForAngularDTOConverter implements Converter<InvoiceIndex, InvoiceForAngularDTO> {
    private final ObjectProvider<ModelMapper> objectProvider;

    @Override
    public InvoiceForAngularDTO convert(MappingContext<InvoiceIndex, InvoiceForAngularDTO> context) {
        ModelMapper modelMapper = objectProvider.getIfAvailable();
        InvoiceDTO invoiceDto = modelMapper.map(context.getSource(), InvoiceDTO.class);
        InvoiceForAngularDTO invoiceForAngular = modelMapper.map(invoiceDto, context.getDestinationType());

        invoiceForAngular.setBuyerAddressEmail(Optional.ofNullable(context.getSource().getBuyerAddressEmail()).orElse(""));
        invoiceForAngular.setSellerAddressEmail(Optional.ofNullable(context.getSource().getSellerAddressEmail()).orElse(""));
        invoiceForAngular.setBillToAddressEmail(Optional.ofNullable(context.getSource().getBillToAddressEmail()).orElse(""));
        invoiceForAngular.setShipToAddressEmail(Optional.ofNullable(context.getSource().getShipToAddressEmail()).orElse(""));

        invoiceForAngular.setTranslator(context.getSource().getTranslator());

        invoiceForAngular.setInvoiceCurrencyCoded(InvoiceDTO.CurrencyCodedEnum.fromValue(context.getSource().getInvoiceCurrencyCoded()) != null
                ? InvoiceDTO.CurrencyCodedEnum.fromValue(context.getSource().getInvoiceCurrencyCoded()).getValue()
                : null);

        invoiceForAngular.setBuyerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(context.getSource().getBuyerPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(context.getSource().getBuyerPartyCountryCoded()).getValue()
                : null);

        invoiceForAngular.setSellerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(context.getSource().getSellerPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(context.getSource().getSellerPartyCountryCoded()).getValue()
                : null);

        invoiceForAngular.setBillToPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(context.getSource().getBillToPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(context.getSource().getBillToPartyCountryCoded()).getValue()
                : null);

        invoiceForAngular.setAcquisition(context.getSource().getAcquisition() != null &&
                InvoiceDTO.AcquisitionEnum.fromValue(context.getSource().getAcquisition().name()) != null
                ? InvoiceDTO.AcquisitionEnum.fromValue(context.getSource().getAcquisition().name())
                : null);

        invoiceForAngular.setProcessingWay(context.getSource().getProcessingWay() != null &&
                InvoiceDTO.ProcessingWayEnum.fromValue(context.getSource().getProcessingWay().name()) != null
                ? InvoiceDTO.ProcessingWayEnum.fromValue(context.getSource().getProcessingWay().name())
                : null);

        if (context.getSource().getSummaryBreakdownValueAddedTax() != null) {
            List<ValueAddedTaxDTO> addedTaxDTOs = context.getSource().getSummaryBreakdownValueAddedTax()
                    .stream()
                    .map(addedVal -> RestServiceHelper.transformValueAddedTaxToValueAddedTaxDTO(addedVal))
                    .collect(Collectors.toList());
            invoiceForAngular.setSummaryBreakdownValueAddedTax(addedTaxDTOs);
        }

        if (context.getSource().getDetails() != null) {
            List<InvoiceDetailDTO> invoiceDetailDTOList = context.getSource().getDetails()
                    .stream()
                    .map(inv -> RestServiceHelper.transformInvoiceDetailIndexToInvoiceDetailDTO(inv))
                    .collect(Collectors.toList());
            invoiceForAngular.setDetails(invoiceDetailDTOList);
        }
        invoiceForAngular.setPaymentStatus(InvoiceDTO.PaymentStatusEnum.fromValue(context.getSource().getPaymentStatus()) != null
                ? DocumentStyleHelper.resolvePaymentStatusLabelText(context.getSource().getPaymentStatus()) : null);

        invoiceForAngular.setType(RestServiceHelper.getType(context.getSource().getType()));
        return invoiceForAngular;
    }
}
