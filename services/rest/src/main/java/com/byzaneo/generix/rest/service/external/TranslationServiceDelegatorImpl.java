package com.byzaneo.generix.rest.service.external;

import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.generix.api.dto.translation.*;
import com.byzaneo.generix.api.service.external.delegators.TranslationServiceDelegator;
import com.byzaneo.generix.api.service.internal.impl.translation.TranslationService;
import com.byzaneo.generix.api.util.*;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.service.SecurityService;
import com.byzaneo.xtrade.service.DocumentErrorTranslationService;
import lombok.RequiredArgsConstructor;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.slf4j.LoggerFactory.getLogger;


@Component
@RequiredArgsConstructor
public class TranslationServiceDelegatorImpl implements TranslationServiceDelegator {

    private static final Logger log = getLogger(TranslationServiceDelegatorImpl.class);
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH.mm.ss.SSS");

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private transient SecurityService securityService;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private TranslationService translationService;

    @Autowired
    private I18NService i18NService;

    @Autowired
    private DocumentErrorTranslationService documentErrorTranslationService;

    @Autowired
    @Qualifier(I18nTranslationDAO.DAO_NAME)
    private I18nTranslationDAO i18nTranslationDAO;

    // JDK 17: Using Set.of() for immutable set creation
    private static final Set<String> ALLOWED_LANG_CODES = Set.of(
        "ab", "aa", "af", "ak", "sq", "am", "ar", "an", "hy", "as", "av", "ae", "ay", "az",
        "bm", "ba", "eu", "be", "bn", "bi", "bs", "br", "bg", "my", "ca", "ch", "ce", "ny",
        "zh", "cu", "cv", "kw", "co", "cr", "hr", "cs", "da", "dv", "nl", "dz", "en", "eo",
        "et", "ee", "fo", "fj", "fi", "fr", "fy", "ff", "gd", "gl", "lg", "ka", "de", "el",
        "kl", "gn", "gu", "ht", "ha", "he", "hz", "hi", "ho", "hu", "is", "io", "ig", "id",
        "ia", "ie", "iu", "ik", "ga", "it", "ja", "jv", "kn", "kr", "ks", "kk", "km", "ki",
        "rw", "ky", "kv", "kg", "ko", "kj", "ku", "lo", "la", "lv", "li", "ln", "lt", "lu",
        "lb", "mk", "mg", "ms", "ml", "mt", "gv", "mi", "mr", "mh", "mn", "na", "nv", "nd",
        "nr", "ng", "ne", "no", "nb", "nn", "ii", "oc", "oj", "or", "om", "os", "pi", "ps",
        "fa", "pl", "pt", "pa", "qu", "ro", "rm", "rn", "ru", "se", "sm", "sg", "sa", "sc",
        "sr", "sn", "sd", "si", "sk", "sl", "so", "st", "es", "su", "sw", "ss", "sv", "tl",
        "ty", "tg", "ta", "tt", "te", "th", "bo", "ti", "to", "ts", "tn", "tr", "tk", "tw",
        "ug", "uk", "ur", "uz", "ve", "vi", "vo", "wa", "cy", "wo", "xh", "yi", "yo", "za", "zu"
    );

    // JDK 17: Constants for frontend translation handling
    private static final String DELIMITER = ";";
    private static final Set<String> PROTECTED_LANGUAGES = Set.of("fr", "en");

    @Override
    public Response downloadDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang) {
        requestId = ensureRequestId(requestId);

        // JDK 17: Enhanced logging with text blocks
        log.info("""
            [{}] Received download dictionary request:
            - Type: {}
            - Language: {}
            """, requestId, dictType, lang);

        var error = validateRequest(request, requestId, dictType, Right.READ);
        if (error != null) {
            log.warn("[{}] Request validation failed: {}", requestId, error.getEntity());
            return error;
        }

        var requestedLang = parseAndValidateLangCode(lang, requestId);
        log.debug("[{}] Validated language: {}", requestId, requestedLang);

        // JDK 17: Switch expression for handling different dictionary types
        var response = switch (dictType.toLowerCase()) {
            case "errors" -> translationService.exportTranslationsWithLang(requestedLang);
            case "frontend" -> exportFrontendTranslations(requestedLang, requestId);
            default -> throw new IllegalArgumentException("Unsupported dictionary type: " + dictType);
        };

        log.info("[{}] Successfully processed dictionary download request.", requestId);
        return response;
    }

    @Override
    public Response importDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang, InputStream uploadedInputStream) {
        requestId = ensureRequestId(requestId);

        // JDK 17: Enhanced logging with text blocks
        log.info("""
            [{}] Received import dictionary request:
            - Type: {}
            - Language: {}
            """, requestId, dictType, lang);

        var error = validateRequest(request, requestId, dictType, Right.UPDATE);
        if (error != null) {
            log.warn("[{}] Request validation failed: {}", requestId, error.getEntity());
            return error;
        }

        var requestedLang = parseAndValidateLangCode(lang, requestId);
        log.debug("[{}] Validated language: {}", requestId, requestedLang);

        // JDK 17: Switch expression for handling different dictionary types
        var response = switch (dictType.toLowerCase()) {
            case "errors" -> translationService.importTranslationsWithLang(requestedLang, uploadedInputStream);
            case "frontend" -> importFrontendTranslations(requestedLang, uploadedInputStream, requestId);
            default -> throw new IllegalArgumentException("Unsupported dictionary type: " + dictType);
        };

        log.info("[{}] Successfully processed dictionary import request.", requestId);
        return response;
    }

    private Response validateRequest(HttpServletRequest request, UUID requestId, String dictType, Right right) {
        log.debug("[{}] Validating request with dictType: {} and right: {}", requestId, dictType, right);

        if (!RestServiceHelper.hasBearerToken(request)) {
            log.warn("[{}] Missing or empty Authorization header.", requestId);
            return RestServiceHelper.getResponseOnError(
                Response.Status.FORBIDDEN.toString(),
                "Authorization header must not be null or empty",
                Response.Status.FORBIDDEN,
                requestId
            );
        }

        // JDK 17: Using enhanced switch expression for validation
        var isValidDictType = switch (dictType.toLowerCase()) {
            case "errors", "frontend" -> true;
            default -> false;
        };

        if (!isValidDictType) {
            log.warn("[{}] Invalid dictionary type: {}", requestId, dictType);
            return RestServiceHelper.getResponseOnError(
                Response.Status.BAD_REQUEST.toString(),
                "Invalid dictionary type. Must be 'errors' or 'frontend'",
                Response.Status.BAD_REQUEST,
                requestId
            );
        }

        TechnicalUser technicalUser = securityService.getTechnicalUserById(
            RestServiceHelper.getTechnicalUserIdFromRequest(request)
        );
        log.debug("[{}] Fetched technical user: {}", requestId, technicalUser != null ? technicalUser.getId() : "null");

        boolean hasPermission = permissionHelper.isGrantedTechnicalUser(
            com.byzaneo.generix.service.SecurityService.Resource.Api_permissions_translation,
            right,
            technicalUser
        );

        if (!hasPermission || technicalUser.isDisabled()) {
            log.warn("[{}] User does not have required permission or is disabled.", requestId);
            return RestServiceHelper.getResponseOnError(
                Response.Status.FORBIDDEN.toString(),
                "Security error: You don't have the rights to access to the API.",
                Response.Status.FORBIDDEN,
                requestId
            );
        }

        return null;
    }

    private UUID ensureRequestId(UUID requestId) {
        if (requestId == null) {
            UUID newId = UUID.randomUUID();
            log.debug("Generated new requestId: {}", newId);
            return newId;
        }
        return requestId;
    }

    // JDK 17: Using enhanced validation with text blocks for error messages
    private String parseAndValidateLangCode(String lang, UUID requestId) {
        if (lang == null || lang.isBlank()) {
            log.debug("[{}] No language code provided. Returning null.", requestId);
            return null;
        }

        var trimmedLang = lang.trim();

        if (!ALLOWED_LANG_CODES.contains(trimmedLang)) {
            var errorMessage = """
                Invalid language code: %s.
                Must be a valid ISO 639-1 language code.
                """.formatted(trimmedLang);
            log.error("[{}] {}", requestId, errorMessage.replace("\n", " "));
            throw new IllegalArgumentException(errorMessage);
        }

        return trimmedLang;
    }

    // JDK 17: Frontend translation export method
    private Response exportFrontendTranslations(String requestedLang, UUID requestId) {
        try {
            log.debug("[{}] Exporting frontend translations for language: {}", requestId, requestedLang);

            var translations = i18NService.getTranslations();
            var availableLocales = i18NService.getLocales();

            // JDK 17: Filter locales based on requested language
            var exportLocales = requestedLang != null && !requestedLang.isBlank()
                ? availableLocales.stream()
                    .filter(locale -> requestedLang.equalsIgnoreCase(locale.getLanguage()))
                    .toList()
                : availableLocales;

            var csvContent = generateFrontendCsv(translations, exportLocales, requestId);
            var csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);

            var filename = "frontend_translations.csv";

            return Response.ok(csvBytes, "application/octet-stream")
                .header("Content-Disposition", "attachment; filename=" + filename)
                .header("Content-Length", csvBytes.length)
                .build();

        } catch (Exception e) {
            log.error("[{}] Error exporting frontend translations: {}", requestId, e.getMessage(), e);
            return RestServiceHelper.getResponseOnError(
                Response.Status.INTERNAL_SERVER_ERROR.toString(),
                "Failed to export frontend translations: " + e.getMessage(),
                Response.Status.INTERNAL_SERVER_ERROR,
                requestId
            );
        }
    }

    // JDK 17: Frontend translation import method
    private Response importFrontendTranslations(String requestedLang, InputStream uploadedInputStream, UUID requestId) {
        var errors = new ArrayList<ImportError>();
        var totalImported = 0;

        try {
            log.debug("[{}] Importing frontend translations for language: {}", requestId, requestedLang);

            var csvContent = new String(uploadedInputStream.readAllBytes(), StandardCharsets.UTF_8);
            var lines = csvContent.split("\n");

            if (lines.length < 2) {
                var error = ImportError.builder()
                    .lineNumber(0)
                    .errorMessage("File must contain at least a header and one data row")
                    .build();
                errors.add(error);

                var response = DictionaryImportResponse.builder()
                    .totalImported(0)
                    .errors(errors)
                    .build();

                return Response.ok(response).build();
            }

            totalImported = processFrontendCsvLines(lines, requestedLang, errors, requestId);

            var response = DictionaryImportResponse.builder()
                .totalImported(totalImported)
                .errors(errors)
                .build();

            log.info("[{}] Frontend import completed. Imported: {}, Errors: {}", requestId, totalImported, errors.size());
            return Response.ok(response).build();

        } catch (Exception e) {
            log.error("[{}] Error importing frontend translations: {}", requestId, e.getMessage(), e);
            return RestServiceHelper.getResponseOnError(
                Response.Status.INTERNAL_SERVER_ERROR.toString(),
                "Failed to import frontend translations: " + e.getMessage(),
                Response.Status.INTERNAL_SERVER_ERROR,
                requestId
            );
        }
    }

    // JDK 17: Helper method to generate frontend CSV content
    private String generateFrontendCsv(List<I18NTranslationDto> translations, List<Locale> exportLocales, UUID requestId) {
        var csvBuilder = new StringBuilder();

        // JDK 17: Build header with sorted locales
        var sortedLocales = exportLocales.stream()
            .sorted(Comparator.comparing(Locale::toString))
            .toList();

        csvBuilder.append("module").append(DELIMITER).append("key");
        for (var locale : sortedLocales) {
            csvBuilder.append(DELIMITER).append(locale.toString());
        }
        csvBuilder.append("\n");

        // JDK 17: Group translations by module and key
        // First, we need to get module names for each translation
        var moduleMap = new HashMap<Long, String>();
        var allModules = i18NService.findAllI18NModules();
        allModules.forEach(module -> moduleMap.put(module.getId(), module.getName()));

        var translationMap = translations.stream()
            .filter(t -> moduleMap.containsKey(t.getI18NModuleId()))
            .collect(Collectors.groupingBy(
                t -> moduleMap.get(t.getI18NModuleId()) + ":" + t.getCode(),
                Collectors.toMap(
                    t -> t.getLocale().toString(),
                    t -> t.getNewValue() != null ? t.getNewValue() : (t.getDefaultValue() != null ? t.getDefaultValue() : ""),
                    (existing, replacement) -> replacement
                )
            ));

        // JDK 17: Generate CSV rows
        translationMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                var moduleKey = entry.getKey().split(":", 2);
                var module = moduleKey[0];
                var key = moduleKey[1];
                var localeValues = entry.getValue();

                csvBuilder.append(module).append(DELIMITER).append(key);
                for (var locale : sortedLocales) {
                    var value = localeValues.getOrDefault(locale.toString(), "");
                    csvBuilder.append(DELIMITER).append(escapeCsvValue(value));
                }
                csvBuilder.append("\n");
            });

        log.debug("[{}] Generated CSV with {} rows", requestId, translationMap.size());
        return csvBuilder.toString();
    }

    // JDK 17: Helper method to process frontend CSV lines
    private int processFrontendCsvLines(String[] lines, String requestedLang, List<ImportError> errors, UUID requestId) {
        var totalImported = 0;
        var headers = lines[0].split(DELIMITER);

        if (headers.length < 3 || !"module".equals(headers[0]) || !"key".equals(headers[1])) {
            errors.add(ImportError.builder()
                .lineNumber(1)
                .errorMessage("Invalid header format. Expected: module;key;language1;language2;...")
                .build());
            return 0;
        }

        // JDK 17: Map language columns
        var langColumnIndex = new HashMap<String, Integer>();
        for (var i = 2; i < headers.length; i++) {
            var langCode = headers[i].trim().toLowerCase();
            if (ALLOWED_LANG_CODES.contains(langCode)) {
                if (requestedLang == null || requestedLang.isBlank() || requestedLang.equalsIgnoreCase(langCode)) {
                    langColumnIndex.put(langCode, i);
                }
            }
        }

        // Process data rows
        for (var lineNumber = 2; lineNumber <= lines.length; lineNumber++) {
            var line = lines[lineNumber - 1].trim();
            if (line.isEmpty()) continue;

            var parts = line.split(DELIMITER);
            if (parts.length < 3) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("Row must have at least module, key, and one translation")
                    .build());
                continue;
            }

            var module = parts[0].trim();
            var key = parts[1].trim();

            if (module.isEmpty() || key.isEmpty()) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("Module and key cannot be empty")
                    .build());
                continue;
            }

            totalImported += processTranslationRow(module, key, parts, langColumnIndex, errors, lineNumber, requestId);
        }

        return totalImported;
    }

    // JDK 17: Helper method to process individual translation row
    private int processTranslationRow(String module, String key, String[] parts, Map<String, Integer> langColumnIndex,
                                    List<ImportError> errors, int lineNumber, UUID requestId) {
        var imported = 0;

        // Find module
        var moduleOpt = i18NService.findI18NModuleByName(module);
        if (moduleOpt.isEmpty()) {
            errors.add(ImportError.builder()
                .lineNumber(lineNumber)
                .errorMessage("Unknown module: " + module)
                .build());
            return 0;
        }

        var moduleEntity = moduleOpt.get();

        for (var entry : langColumnIndex.entrySet()) {
            var langCode = entry.getKey();
            var columnIndex = entry.getValue();

            if (columnIndex >= parts.length) continue;

            var value = parts[columnIndex].trim();
            if (value.isEmpty()) continue;

            // JDK 17: Skip protected languages (fr, en) to preserve defaults
            if (PROTECTED_LANGUAGES.contains(langCode)) {
                log.debug("[{}] Skipping protected language: {}", requestId, langCode);
                continue;
            }

            try {
                updateOrCreateTranslation(moduleEntity.getId(), key, langCode, value, requestId);
                imported++;
            } catch (Exception e) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("Failed to import translation for key '" + key + "' and language '" + langCode + "': " + e.getMessage())
                    .build());
            }
        }

        return imported;
    }

    // JDK 17: Helper method to update or create translation
    private void updateOrCreateTranslation(Long moduleId, String key, String langCode, String value, UUID requestId) {
        var locale = new Locale(langCode);

        // Find existing translation using correct property names
        var query = new Query(new AndClause(
            Clauses.equal("code", key),
            Clauses.equal("locale", locale),
            Clauses.equal("i18NModule.id", moduleId)
        ));

        var existingTranslations = i18nTranslationDAO.search(query);

        if (!existingTranslations.isEmpty()) {
            // Update existing
            var existing = existingTranslations.get(0);
            existing.setNewValue(value);
            existing.setNewValueChangedAt(new Date());
            i18nTranslationDAO.store(existing);
            log.debug("[{}] Updated translation for key: {}, locale: {}", requestId, key, langCode);
        } else {
            // Create new - need to get the module entity
            var moduleOpt = i18NService.findI18NModuleById(moduleId);
            if (moduleOpt != null) {
                var newTranslation = new I18NTranslation();
                newTranslation.setCode(key);
                newTranslation.setI18NModule(moduleOpt); // Set the module entity, not just ID
                newTranslation.setLocale(locale);
                newTranslation.setNewValue(value);
                newTranslation.setNewValueChangedAt(new Date());
                i18nTranslationDAO.store(newTranslation);
                log.debug("[{}] Created new translation for key: {}, locale: {}", requestId, key, langCode);
            } else {
                log.error("[{}] Module with ID {} not found", requestId, moduleId);
                throw new RuntimeException("Module not found: " + moduleId);
            }
        }
    }

    // JDK 17: Helper method to escape CSV values
    private String escapeCsvValue(String value) {
        if (value == null) return "";

        // JDK 17: Enhanced string handling
        return value.contains(DELIMITER) || value.contains("\"") || value.contains("\n")
            ? "\"" + value.replace("\"", "\"\"") + "\""
            : value;
    }

}
