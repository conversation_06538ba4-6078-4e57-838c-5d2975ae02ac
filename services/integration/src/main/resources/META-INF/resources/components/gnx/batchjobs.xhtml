<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html" 
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets" xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:b="http://byzaneo.com/ui">
	
	<cc:interface name="batchjobs">
		<cc:attribute name="workspaceName" type="java.lang.String" required="false" default="#{gnxIntegrationISHandler.instance.code}"/>
	</cc:interface>
	<cc:implementation>
		<style>
			/* THIS IS ABOUT ROW BORDER */
			.psJobDetail .form-control {
			    border:	none;
			}
			.psJobDetail .ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
			    opacity:.80;
			}
    	</style>
		<p:growl id="batchJobsGrowl"/>  
		<p:outputPanel id="batchJobsViewPnl" styleClass="psBatchJobsPnl">
			<p:dataTable id="batchJobsTbl" value="#{gnxBatchJobHandler.getBatchJobs()}" var="batchJob" widgetVar="wBatchJobsTbl"
				rows="20" paginator="true" paginatorPosition="bottom" rowKey="#{batchJob.id}" 
				styleClass="datatable-hide-filters" emptyMessage="#{labels.no_records_found}" 
				rowIndexVar="rowIdx" sortBy="#{batchJob.start}" sortOrder="descending"
				selection="#{gnxBatchJobHandler.selectedJob}" selectionMode="single" >
				<p:ajax event="rowSelect" process="@this" oncomplete="PF('wJobDetailDialog').show()" update="@(.psJobDetail)"/>
				
				<!-- GLOBAL ACTIONS -->
				<f:facet name="header">
                      <p:outputPanel styleClass="row">
                          <p:outputPanel styleClass="col-md-11">
                               <p:inputText id="globalFilter"
									  onkeyup="PF('wBatchJobsTbl').filter()" 
									  styleClass="pull-right" style="width: 100%" />
							<p:watermark for="globalFilter" value="#{itglbls.batch_jobs_search_placeholder}" />
						 	<p:defaultCommand target="globalFilter" />
                          </p:outputPanel>
                            <p:outputPanel styleClass="col-md-1">
                            <p:commandButton id="refresh" icon="fa fa-refresh" value="#{labels.refresh}"
									actionListener="#{gnxBatchJobHandler.onRefresh()}" 
									immediate="true" process="@this" update="batchJobsTbl" styleClass="mls" />
                            </p:outputPanel>
                      </p:outputPanel>
				</f:facet>
				<!-- SERVICE TYPE -->
				<p:column headerText="#{itglbls.batch_jobs_service_type}" filterBy="#{batchJob.serviceType}"  styleClass="center" style="width:10%;">
					<h:outputText id="serviceType" value="#{itglbls[batchJob.serviceType]}"/>
				</p:column>
				<!-- Process TYPE -->
				<p:column headerText="#{itglbls.batch_jobs_process_type}" filterBy="#{batchJob.processType}" styleClass="center" style="width:10%;">
					<h:outputText id="processType" value="#{itglbls[batchJob.processType]}"/>
				</p:column>
				<!-- USER -->
				<p:column headerText="#{itglbls.batch_jobs_user}" filterBy="#{batchJob.user.fullname}" styleClass="center" style="width:15%;">
					<h:outputText id="username" value="#{batchJob.user.fullname}" />
				</p:column>
				<!-- Environment -->
				<p:column headerText="#{itglbls.batch_jobs_environment}" filterBy="#{gnxHandler.label(batchJob.instance.name, gnxSessionHandler.locale)}"  styleClass="center" style="width:10%;">
					<h:outputText id="environment" value="#{gnxHandler.label(batchJob.instance.name, gnxSessionHandler.locale)}" />
				</p:column>
				<!-- DATE START -->
				<p:column headerText="#{itglbls.batch_jobs_dateStart}" sortBy="#{batchJob.start}" filterBy="#{batchJob.start}" styleClass="center" style="width:10%;">
					<h:outputText id="dateStart" value="#{batchJob.start}">
					        <f:convertDateTime locale="#{gnxSessionHandler.locale}" 
                    				   type="both" dateStyle="medium" timeStyle="short"
                    				   timeZone="#{secSessionHandler.timeZone}"/>
					</h:outputText>
				</p:column>
				<!-- DATE END -->
				<p:column headerText="#{itglbls.batch_jobs_dateEnd}" sortBy="#{batchJob.end}" filterBy="#{batchJob.end}"  styleClass="center" style="width:10%;">
					<h:outputText id="dateEnd" value="#{batchJob.end}" >
					        <f:convertDateTime locale="#{gnxSessionHandler.locale}" 
                    				   type="both" dateStyle="medium" timeStyle="short"
                    				   timeZone="#{secSessionHandler.timeZone}"/>
					</h:outputText>
				</p:column>
				<!-- JOB ID -->
				<p:column headerText="#{itglbls.batch_jobs_batchId}"  filterBy="#{batchJob.jobId}" styleClass="center" style="width:20%;">
					<h:outputText id="batchId" value="#{batchJob.jobId}" />
				</p:column>
				<!-- STATUS -->
				<p:column  headerText="#{itglbls.batch_jobs_status}" filterBy="#{batchJob.status}"  styleClass="center nowrap" style="width:10%;">
					<h:outputText id="status" value="#{itglbls['batch_jobs_'.concat(batchJob.status)]}" />
				</p:column>
				<!-- ACTIONS -->
				<p:column styleClass="center nowrap" style="width:5%;">
					<p:commandButton id="deleteReport" icon="ui-icon-trash" title="#{comlbls.delete}" 
					actionListener="#{gnxBatchJobHandler.onDeleteReport(batchJob)}" 
					process="@this"
					update="@(.psBatchJobsPnl)">
                     <p:confirm header="#{comlbls.confirm}"
                           message="#{itglbls.batch_jobs_detail_confirmation_delete}"
                           icon="ui-icon-alert"/>
					</p:commandButton>
					<!-- CONFIRM SUPPRESSION DIALOG -->
					<p:confirmDialog global="true" showEffect="fade" hideEffect="fade">
						<p:commandButton value="#{labels.yes}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check" update="@(.psBatchJobsPnl)"/>
						<p:commandButton value="#{labels.no}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close" />
					</p:confirmDialog>
				</p:column>
			</p:dataTable>
		</p:outputPanel>	
		
		<p:dialog id="jobDetailDialog" header="#{itglbls.batch_jobs_detail_header}" widgetVar="wJobDetailDialog" dynamic="true" modal="true" closable="true" width="900" height="350">
				<p:outputPanel styleClass="form-group psJobDetail">
					<p:outputLabel styleClass="col-sm-2 control-label" for="query" value="#{itglbls.batch_jobs_detail_request}" />
					<p:outputPanel styleClass="col-sm-10">
						<b:query id="query" value="#{gnxBatchJobHandler.selectedJob.filter}"  style="height: auto;"
							model="#{gnxBatchJobHandler.getQueryModel(gnxSessionHandler.locale)}"
							widgetVar="wBqlQuery" helpRendered="fase" disabled="true" />
					</p:outputPanel>
					<p:outputLabel styleClass="col-sm-2 control-label" for="userId" value="#{itglbls.batch_jobs_detail_user}" />
					<p:outputPanel styleClass="col-sm-10">
						<h:outputText id="userId" value="#{gnxBatchJobHandler.selectedJob.user.fullname}" styleClass="form-control" />
					</p:outputPanel>
					<p:outputLabel styleClass="col-sm-2 control-label" for="startDateId" value="#{itglbls.batch_jobs_detail_start}" />
					<p:outputPanel styleClass="col-sm-10">
						<h:outputText id="startDateId" value="#{gnxBatchJobHandler.selectedJob.start}" styleClass="form-control" >
							 <f:convertDateTime locale="#{gnxSessionHandler.locale}" 
	                    				   type="both" dateStyle="medium" timeStyle="short"
	                    				   timeZone="#{secSessionHandler.timeZone}"/>
                    	 </h:outputText>
					</p:outputPanel>
					<p:outputLabel styleClass="col-sm-2 control-label" for="endDateId" value="#{itglbls.batch_jobs_detail_end}" />
					<p:outputPanel styleClass="col-sm-10">
						<h:outputText id="endDateId" value="#{gnxBatchJobHandler.selectedJob.end}" styleClass="form-control" >
							 <f:convertDateTime locale="#{gnxSessionHandler.locale}" 
	                    				   type="both" dateStyle="medium" timeStyle="short"
	                    				   timeZone="#{secSessionHandler.timeZone}"/>
                    	 </h:outputText>
					</p:outputPanel>
					<p:outputPanel rendered="#{gnxBatchJobHandler.selectedJob.processType == 'KPI_UPDATE_JOB'}">
					
						<p:outputLabel styleClass="col-sm-2 control-label" for="readId" value="#{itglbls.batch_jobs_detail_read}" /> 
						<p:outputPanel styleClass="col-sm-10">
							<h:outputText id="readId" value="#{gnxBatchJobHandler.selectedJob.numberOfTasksFailed + gnxBatchJobHandler.selectedJob.numberOfTasksSucceeded }" styleClass="form-control" />
						</p:outputPanel>
						
						<p:outputLabel styleClass="col-sm-2 control-label" for="writeId" value="#{itglbls.batch_jobs_detail_write}" /> 
						<p:outputPanel styleClass="col-sm-10">
							<h:outputText id="writeId" value="#{gnxBatchJobHandler.selectedJob.numberOfTasksSucceeded}" styleClass="form-control" />
						</p:outputPanel>
					</p:outputPanel>
					<p:outputLabel styleClass="col-sm-2 control-label" for="statusId" value="#{itglbls.batch_jobs_detail_status}" /> 
					<p:outputPanel styleClass="col-sm-10">
						<h:outputText id="statusId" value="#{itglbls['batch_jobs_'.concat(gnxBatchJobHandler.selectedJob.status)]}" styleClass="form-control" />
					</p:outputPanel>
					<p:outputPanel rendered="#{gnxBatchJobHandler.selectedJob.processType == 'EXPORT_JOB' &amp;&amp; gnxBatchJobHandler.selectedJob.numberOfTasksFailed > 0 }">
						<p:outputLabel styleClass="col-sm-2 control-label" for="errorsId" value="#{itglbls.batch_jobs_detail_error_title}" /> 
						<p:outputPanel styleClass="col-sm-10">
						<h:outputFormat id="errorsId" value="#{itglbls.batch_jobs_detail_error_message}" styleClass="form-control">
							<f:param value="#{gnxBatchJobHandler.selectedJob.numberOfTasksFailed}"/>
							<f:param value="#{gnxBatchJobHandler.selectedJob.numberOfTasksSucceeded + gnxBatchJobHandler.selectedJob.numberOfTasksFailed}"/>
						</h:outputFormat>
						</p:outputPanel>
						<p:outputPanel styleClass="col-sm-10 col-sm-offset-2" style="padding-top:5px">
						  <p:commandButton value="#{comlbls.download}" ajax="false" icon="pi pi-arrow-down">
					        <p:fileDownload value="#{gnxBatchJobHandler.getReportFile()}" />
					    </p:commandButton>     
						</p:outputPanel>
					</p:outputPanel>
					<p:outputPanel rendered="#{gnxBatchJobHandler.selectedJob.errorMessage !=null}">
						<p:outputLabel styleClass="col-sm-2 control-label" for="failId" value="#{itglbls.batch_jobs_detail_error_title}" /> 
						<p:outputPanel styleClass="col-sm-10">
							<h:outputText id="failId" value="#{gnxBatchJobHandler.selectedJob.errorMessage}" styleClass="form-control" />
						</p:outputPanel>
					</p:outputPanel>
				</p:outputPanel>
			<f:facet name="footer">
				<h:panelGroup layout="block" styleClass="right">
					<p:commandButton icon="ui-icon-cancel" value="#{labels.ok}" type="button" onclick="PF('wJobDetailDialog').hide()" styleClass="mls" />
				</h:panelGroup>
			</f:facet>
		</p:dialog>
	</cc:implementation>
</ui:component>