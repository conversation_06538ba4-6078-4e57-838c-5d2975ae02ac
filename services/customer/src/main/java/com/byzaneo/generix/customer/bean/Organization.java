package com.byzaneo.generix.customer.bean;

import javax.persistence.*;

import com.byzaneo.commons.bean.Persistent;

@Entity
@Table(name = "CUSTOMER_ORGANIZATION")
public class Organization implements Persistent<OrganizationId> {

  private static final long serialVersionUID = -8143158385458086732L;

  @EmbeddedId
  private OrganizationId id;

  @Column(name = "LIB_ORGCOM")
  private String label;

  @Override
  public OrganizationId getId() {
    return id;
  }

  @Override
  public void setId(OrganizationId id) {
    this.id = id;
  }

  public String getLabel() {
    return label;
  }

  public void setLabel(String label) {
    this.label = label;
  }

  @Override
  public String toString() {
    return "Organization [id=" + id + ", label=" + label + "]";
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((id == null) ? 0 : id.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    Organization other = (Organization) obj;
    if (id == null) {
      if (other.id != null) return false;
    }
    else if (!id.equals(other.id)) return false;
    return true;
  }

}
