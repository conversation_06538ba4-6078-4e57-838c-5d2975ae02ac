package com.byzaneo.generix.campaign.bean;

import com.byzaneo.commons.bean.AbstractPersitentLongId;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.task.api.TaskResult;
import com.google.common.base.Predicate;
import org.hibernate.annotations.Source;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.byzaneo.generix.campaign.bean.CampaignInstance.State.None;
import static com.byzaneo.generix.campaign.bean.CampaignTaskInstance.RESULT_DESCENDING_DATE_COMPARATOR;
import static com.google.common.collect.Iterables.filter;
import static com.google.common.collect.Lists.newArrayList;
import static java.lang.String.format;
import static java.util.Collections.sort;
import static javax.persistence.CascadeType.ALL;
import static javax.persistence.EnumType.STRING;
import static javax.persistence.FetchType.EAGER;
import static javax.persistence.TemporalType.TIMESTAMP;
import static org.hibernate.annotations.SourceType.DB;

/**
 * Campaign representation
 * 
 * <AUTHOR> Fung <<EMAIL>>
 * @date 12 nov. 2011
 * @company Byzaneo
 */
@Entity
@Table(name = "GCM_CAMPAIGN_INSTANCE")
public class CampaignInstance extends AbstractPersitentLongId {
  private static final long serialVersionUID = 1530430781907201308L;

  /** Campaign definition state */
  public enum State implements com.byzaneo.generix.bean.State {
    /** No State */
    None,
    /** Failed to initialized */
    Failed,
    /** Waiting for authentication */
    Initialized,
    /** Tasks monitoring */
    InProgress,
    /** Qualified to Certification */
    Qualified,
    /** Certified */
    Certified,
    /** Integrated */
    Integrated,
    /** Deployed */
    Deployed;
    // find out programmatically the states list, to avoid data incoherence
    // if the enum structure will have been changed.
    public static List<State> getBeforeQualificationStates() {
      return newArrayList(filter(Arrays.asList(values()), new Predicate<State>() {
        @Override
        public boolean apply(State state) {
          return state.isBeforeQualification();
        }
      }));
    }

    public boolean isBeforeQualification() {
      return this.ordinal() < Qualified.ordinal();
    }
  }

  @ManyToOne
  private Campaign campaign;

  @ManyToOne
  private Partner partner;

  @Column(name = "STATE", nullable = false, length = 32)
  @Enumerated(STRING)
  private State state = None;

  @Column(name = "CREATION_DATE", updatable = false)
  private Date creationDate = new Date();

  @Version
  @Column(name = "MODIFICATION_DATE", updatable = false, columnDefinition = "TIMESTAMP")
  @Source(DB)
  @Temporal(TIMESTAMP)
  private Date modificationDate = new Date();

  @OneToMany(cascade = ALL, orphanRemoval = true, fetch = EAGER)
  @JoinColumn(name = "CAMPAIGN_ID")
  private List<CampaignTaskInstance> taskInstances;

  // REMIND

  @Column(name = "TKI_REMINDED_DATE")
  private Date remindDate;

  @Column(name = "TKI_REMINDED_COUNT")
  private Integer remindCount;

  // ROADMAP

  /** Partner {@link State#Qualified} estimated date */
  @Column(name = "RDM_DATE_QUALIFIED")
  private Date qualificationDate;

  /** Partner {@link State#Integrated} estimated date */
  @Column(name = "RDM_DATE_INTEGRATED")
  private Date integrationDate;

  /** Partner {@link State#Deployed} estimated date */
  @Column(name = "RDM_DATE_DEPLOYED")
  private Date deploymentDate;

  /*
   * CONSTRUCTOR
   */

  public CampaignInstance() {
    super();
  }

  public CampaignInstance(Campaign campaign, Partner partner, State state) {
    this();
    this.campaign = campaign;
    this.partner = partner;
    this.state = state;
  }

  /*
   * FLUENT SETTERS
   */

  public CampaignInstance campaign(Campaign campaign) {
    setCampaign(campaign);
    return this;
  }

  public CampaignInstance state(State state) {
    setState(state);
    return this;
  }

  /*
   * ACCESSORS
   */

  public State getState() {
    return state == null ? this.state = None : state;
  }

  public void setState(State state) {
    this.state = state;
  }

  public Date getCreationDate() {
    return creationDate;
  }

  public Date getModificationDate() {
    return modificationDate;
  }

  public Campaign getCampaign() {
    return campaign;
  }

  public void setCampaign(Campaign campaign) {
    this.campaign = campaign;
  }

  public Partner getPartner() {
    return partner;
  }

  public void setPartner(Partner partner) {
    this.partner = partner;
  }

  public List<CampaignTaskInstance> getTaskInstances() {
    return taskInstances == null ? (taskInstances = new ArrayList<>()) : taskInstances;
  }

  public void setTaskInstances(List<CampaignTaskInstance> taskInstances) {
    this.taskInstances = taskInstances;
  }

  // REMIND

  public Date getRemindDate() {
    return remindDate;
  }

  public void setRemindDate(Date remindDate) {
    this.remindDate = remindDate;
  }

  public Integer getRemindCount() {
    return remindCount == null ? this.remindCount = Integer.valueOf(0) : remindCount;
  }

  public void incrementRemindCount() {
    this.remindCount = this.getRemindCount()
        .intValue() + 1;
  }

  /**
   * @return <code>true</code> if the campaign is remindable and this campaign instance's state is lower than {@value State#Qualified}
   */
  public boolean isRemindable() {
    return isCampaignSet() //
        && this.campaign.isRemindable()//
        && this.getState()
            .isBeforeQualification();
  }

  // helper methods

  public boolean isCampaignSet() {
    return campaign != null;
  }

  // ROADMAP

  public Date getQualificationDate() {
    return qualificationDate;
  }

  public void setQualificationDate(Date qualificationDate) {
    this.qualificationDate = qualificationDate;
  }

  public Date getIntegrationDate() {
    return integrationDate;
  }

  public void setIntegrationDate(Date integrationDate) {
    this.integrationDate = integrationDate;
  }

  public Date getDeploymentDate() {
    return deploymentDate;
  }

  public void setDeploymentDate(Date deploymentDate) {
    this.deploymentDate = deploymentDate;
  }

  /**
   * @return <code>true</code>, if at least one roadmap date has been set.
   */
  public boolean isRoadmaped() {
    return qualificationDate != null || integrationDate != null || deploymentDate != null;
  }

  // RESULTS

  /**
   * @return the last {@link TaskResult} on this campaign instance.
   *         <p>
   *         Note: the campaign task instances must be populated with their results before to call this method.
   *         </p>
   */
  public <R extends TaskResult> R getLastResult() {
    if (getTaskInstances().isEmpty())
      return null;
    List<R> results = new ArrayList<>(this.taskInstances.size());
    for (CampaignTaskInstance cti : this.taskInstances) {
      R result = cti.getLastResult();
      if (result != null)
        results.add(result);
    }
    sort(results, RESULT_DESCENDING_DATE_COMPARATOR);
    return results.isEmpty() ? null : results.get(0);
  }

  /* -- OVERRIDE -- */

  /** @see java.lang.Object#toString() */
  @Override
  public String toString() {
    return format("CampaignInstance [partner=%s, state=%s, remindDate=%s, remindCount=%s]", partner, state, remindDate, remindCount);
  }

  @Override
  public boolean equals(Object o) {
    return super.equals(o);
  }

  @Override
  public int hashCode() {
    return super.hashCode();
  }
}
