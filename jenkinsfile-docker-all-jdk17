pipeline {
    agent any
    //options {
        //skipDefaultCheckout()
    //}
    environment{
        imageName = "chassagne/chassagne"
        dockerImage = ""
    }
    tools {
        maven 'Maven3.8.8'
        jdk 'AdoptOpenJDK 17.0.13'

    }

    stages {
        stage('Checkout repos'){
            steps{
                script {

                    def baseDir = "${WORKSPACE}/${specificWorkspace}"  
                    sh "mkdir -p ${baseDir}"  

                    def gitlabCredentialsId = '1ff6869d-4818-45ff-bbab-1c94a304cf3a'  

                    dir("${baseDir}/chassagne-maven") {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: params.chassagneMavenBranch]],
                            userRemoteConfigs: [[url: 'http://git.chassagne-scm.generixgroup.com/generix/chassagne-maven.git', credentialsId: gitlabCredentialsId]]
                        ])
                    }

                    dir("${baseDir}/byzaneo") {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: params.byzaneoBranch]],
                            userRemoteConfigs: [[url: 'ssh://**********************************:10022/generix/byzaneo.git', credentialsId: gitlabCredentialsId]]
                        ])
                    }

                    dir("${baseDir}/chassagne") {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: params.chassagneBranch]],
                            userRemoteConfigs: [[url: 'ssh://**********************************:10022/generix/chassagne.git', credentialsId: gitlabCredentialsId]]
                        ])
                    }
                }
            }

        }

        stage('Docker build all') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'DockerRegistryCredentials', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                        sh '''
                            cd ${WORKSPACE}/${specificWorkspace}
                            rm -rf chassagne/ui/generic/target
                            export MAVEN_OPTS="--add-opens=java.base/java.lang=ALL-UNNAMED"
                            mvn -s /var/jenkins_home/.m2/custom-settings.xml -U -f chassagne-maven/pom.full.xml -P parent,byzaneo,chassagne  -Dmaven.wagon.http.ssl.insecure=true -D maven.wagon.http.ssl.allowall=true                            
                            cd ${WORKSPACE}/${specificWorkspace}/chassagne/ui/generic
                            mvn -s /var/jenkins_home/.m2/custom-settings.xml  docker:build -Pdocker \
                                -DpushImage \
                                -Dchassagne-docker.version=${DOCKER_TAG} 
                        '''
                    }
                }
            }
        }
       
        stage('Cleaning up') {
            steps{
                sh "docker rmi $imageName:$DOCKER_TAG"
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
    }
}
