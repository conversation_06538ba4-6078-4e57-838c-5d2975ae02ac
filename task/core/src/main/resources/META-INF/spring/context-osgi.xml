<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns="http://www.eclipse.org/gemini/blueprint/schema/blueprint"
	xmlns:beans="http://www.springframework.org/schema/beans"
	xsi:schemaLocation="
	http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.eclipse.org/gemini/blueprint/schema/blueprint 
		http://www.eclipse.org/gemini/blueprint/schema/blueprint/gemini-blueprint.xsd">
	
	<!-- JPA -->
	<reference id="entityManagerInfo" interface="com.byzaneo.infix.jpa.EntityManagerInfo" availability="mandatory" timeout="6000" />

	<!-- TX -->
<!-- 	<reference id="transactionManager" interface="org.springframework.transaction.PlatformTransactionManager" availability="mandatory" timeout="6000" /> -->
	
	<!-- TASK TYPES -->
	<!-- listener
	<beans:bean id="taskTypeListener" class="com.byzaneo.task.listener.TaskTypeListener">
		<beans:constructor-arg ref="taskService" />
	</beans:bean>
	<reference-listener ref="taskTypeListener" bind-method="bind" unbind-method="unbind" />
	 -->
	<!-- list -->
	<list id="taskTypeList" interface="com.byzaneo.task.api.TaskType" availability="optional" />

	<!-- TASK SERVICE -->
	<service id="taskService" ref="tskTaskService" interface="com.byzaneo.task.service.TaskService" />
</beans:beans>