package com.byzaneo.task.util;

import static com.byzaneo.commons.bean.FileType.HTML;
import static com.byzaneo.commons.ui.util.JSFHelper.COMPOSITE_EXTENSION;
import static com.byzaneo.commons.ui.util.JSFHelper.createCompositeComponent;
import static com.byzaneo.commons.ui.util.JSFHelper.getFacesContext;
import static com.byzaneo.commons.ui.util.JSFHelper.getResourceRequestPath;
import static com.byzaneo.commons.ui.util.JSFHelper.getResourceStream;
import static com.byzaneo.commons.ui.util.JSFHelper.resolveResource;
import static com.byzaneo.commons.ui.util.MessageHelper.addMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.util.ClassPathScanningHelper.getFieldsAnnotatedWith;
import static com.byzaneo.commons.util.ClassPathScanningHelper.getMethodsAnnotatedWith;
import static com.byzaneo.commons.util.ClassPathScanningHelper.invokeAnnotatedMedhods;
import static com.byzaneo.commons.util.ExpressionHelper.getProperty;
import static com.byzaneo.commons.util.ExpressionHelper.setProperty;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.task.api.TaskPropertySize.inherited;
import static com.byzaneo.task.api.TaskPropertySize.xxlarge;
import static com.byzaneo.task.api.TaskViewMode.edit;
import static com.byzaneo.task.api.TaskViewMode.view;
import static com.byzaneo.task.bean.TaskViewModel.DEFAULT_GROUP_NAME;
import static com.byzaneo.task.proxy.TaskProxyFactory.createTaskProxy;
import static java.lang.Boolean.TRUE;
import static java.lang.String.format;
import static java.lang.reflect.Modifier.isFinal;
import static java.lang.reflect.Modifier.isStatic;
import static java.lang.reflect.Modifier.isTransient;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Collections.reverse;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static javassist.util.proxy.ProxyFactory.getHandler;
import static javax.faces.application.FacesMessage.SEVERITY_ERROR;
import static javax.faces.application.FacesMessage.SEVERITY_INFO;
import static javax.faces.application.FacesMessage.SEVERITY_WARN;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.contains;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.capitalize;
import static org.apache.commons.lang3.StringUtils.defaultString;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.split;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;
import static org.apache.commons.lang3.StringUtils.trimToNull;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.apache.commons.lang3.text.WordUtils.uncapitalize;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.beans.BeanUtils.copyProperties;

import java.beans.IntrospectionException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;

import javax.annotation.PostConstruct;
import javax.faces.application.*;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.component.UIComponent;
import javax.persistence.PostLoad;
import javax.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

import com.byzaneo.task.bean.AngularPortlet;
import org.apache.commons.lang3.StringUtils;
import org.apache.xerces.dom.ElementNSImpl;
import org.slf4j.Logger;
import org.w3c.dom.DOMException;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.commons.ui.util.JSFHelper.Attribute;
import com.byzaneo.commons.util.GsonHelper;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.annotation.Task.DEFAULT;
import com.byzaneo.task.api.*;
import com.byzaneo.task.bean.TaskViewModel;
import com.byzaneo.task.proxy.*;
import com.byzaneo.task.shadow.ShadowTaskInterface;
import com.google.gson.*;

import javassist.util.proxy.Proxy;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date May 16, 2012
 * @since 1.0
 */
public class TaskHelper {
  private static final Logger log = getLogger(TaskHelper.class);

  private static final List<String> TASK_DEFINITION_FIELD_NAMES = asList("id", "title", "description", "definition", "parent", "children");

  public static final String CC_DEFAULT_LIBRARY = "components/task";

  public static final String CC_DEFAULT_TASK_VIEW_LIBRARY = "view";

  /**
   * Task comparator by title
   */
  public static final Comparator<Object> TASK_COMPARATOR = (t1, t2) -> ofNullable(getDefinition(t1))
      .map(TaskDefinition::getTitle)
      .filter(Objects::nonNull)
      .map(title -> title.compareTo(ofNullable(getDefinition(t2))
          .map(TaskDefinition::getTitle)
          .orElse("")))
      .orElse(0);

  public TaskHelper() {
    super();
  }

  /*
   * -- TASK --
   */

  /**
   * Creates a proxified task object from the given type.
   *
   * @param definition back-end task definition
   * @param type type of the task
   * @return the proxified task from the given type. The task is updated with the definition and the {@link PostLoad} and
   *         {@link PostConstruct} call-backs are invoked.
   * @since 1.1
   * @see TaskProxyFactory#createTaskProxy(TaskType, TaskDefinition)
   * @see #updateTask(Object)
   * @see #invokeCallbackMedhods(Object, Class...)
   */
  public static final <T> T createTask(final TaskDefinition definition, final TaskType type) {
    return createTask(definition, type, null, null);
  }

  public static final <T> T createTask(final TaskDefinition definition, final TaskType type, Locale locale, String instanceCode) {
    if (definition == null || type == null)
      return null;

    // proxified
    T r = createTaskProxy(type, definition);

    // populate
    updateTask(r, locale, instanceCode);

    // callback
    invokeCallbackMedhods(r, PostLoad.class, PostConstruct.class);

    return r;
  }

  public static final <T> T createShadowTask(final TaskDefinition definition, final TaskType type, final ShadowTaskInterface shadowTask) {
    if (definition == null || type == null)
      return null;

    // proxified
    T r = createTaskProxy(type, definition);

    ((Proxy) r).setHandler(new ShadowTaskHandler(type, definition, shadowTask));
    // populate
    updateTask(r);

    // callback
    invokeCallbackMedhods(r, PostLoad.class, PostConstruct.class);

    return r;
  }

  /*
   * -- DEFINITION --
   */

  /**
   * Updates the task from its {@link TaskDefinition#getDefinition()} JSon stored representation.
   * 
   * @param task the task to populate
   */
  public static boolean updateTask(final Object task) {
    return updateTask(task, null, null);
  }

  public static boolean updateTask(final Object task, Locale locale, String instanceCode) {
    final TaskType type = getType(task);
    if (type == null) {
      log.debug("Task type not found for task: {}", task);
      return false;
    }
    final TaskDefinition definition = getDefinition(task);
    if (definition == null) {
      log.debug("Task definition not found for task: {}", task);
      return false;
    }
    // Using the object from Json serialization because all members are accessible (not a proxy)
    final Object source = createGson(type).fromJson(definition.getDefinition(), type.getType());

    if(AngularPortlet.class.isAssignableFrom(type.getType())) {
      String setLocale = "setLocale";
      Class<?>[] setLocaleParameterTypes = new Class<?>[] { Locale.class };
      Object[] setLocaleArguments = new Object[] { locale };

      String setInstanceCode = "setInstanceCode";
      Class<?>[] setInstanceCodeParameterTypes = new Class<?>[] { String.class };
      Object[] setInstanceCodeArguments = new Object[] { instanceCode };

      String setTaskType = "setTaskType";
      Class<?>[] setTaskTypeParameterTypes = new Class<?>[] { Object.class };
      Object[] setTaskTypeArguments = new Object[] { type };

      try {
        Class<?> clazz = type.getType();
        Method setLocaleMethod = clazz.getMethod(setLocale, setLocaleParameterTypes);
        setLocaleMethod.invoke(source, setLocaleArguments);

        Method setTaskTypeMethod = clazz.getMethod(setTaskType, setTaskTypeParameterTypes);
        setTaskTypeMethod.invoke(source, setTaskTypeArguments);

        Method setInstanceCodeMethod = clazz.getMethod(setInstanceCode, setInstanceCodeParameterTypes);
        setInstanceCodeMethod.invoke(source, setInstanceCodeArguments);
      } catch (Exception e) {
        log.debug(e.getMessage());
      }
    }
    // null source
    if (source == null)
      return false;
    // Transient member fields are ignored
    final List<String> ignoreProperties = getTransientFields(source.getClass());
    // TaskDefinition member fields are ignored
    ignoreProperties.addAll(TASK_DEFINITION_FIELD_NAMES);
    // copy properties to task
    copyProperties(source, task, ignoreProperties.toArray(new String[ignoreProperties.size()]));
    return true;
  }

  public static Object fillTask(final Object task, TaskDefinition definition, TaskType type) {
    if (type == null) {
      log.debug("Task type not found for task: {}", task);
      return null;
    }
    if (definition == null) {
      log.debug("Task definition not found for task: {}", task);
      return null;
    }
    // Using the object from Json serialization because all members are accessible (not a proxy)
    final Object source = createGson(type).fromJson(definition.getDefinition(), type.getType());
    // null source
    if (source == null)
      return null;
    // Transient member fields are ignored
    final List<String> ignoreProperties = getTransientFields(source.getClass());
    // TaskDefinition member fields are ignored
    ignoreProperties.addAll(TASK_DEFINITION_FIELD_NAMES);
    // copy properties to task
    copyProperties(source, task, ignoreProperties.toArray(new String[ignoreProperties.size()]));
    return task;
  }

  /**
   * @param clazz
   * @return the transient fields in the given class and its parents
   */
  private static List<String> getTransientFields(@SuppressWarnings("rawtypes") final Class clazz) {
    List<String> transientFields = new ArrayList<>();

    if (!clazz.equals(Object.class)) {
      stream(clazz.getDeclaredFields())
          .filter(f -> isTransient(f.getModifiers()))
          .forEach(f -> transientFields.add(f.getName()));
      transientFields.addAll(getTransientFields(clazz.getSuperclass()));
    }

    return transientFields;
  }

  /**
   * @param task from which the definition will be updated
   * @return the definition updated with the JSon serialization of the task
   */
  public static com.byzaneo.task.bean.TaskDefinition updateDefinition(final Object task) {
    final TaskType type = getType(task);
    com.byzaneo.task.bean.TaskDefinition def = getDefinition(task);
    def.setDefinition(createGson(type).toJson(task, type.getType()));
    updateDefinitionUuid(def);
    return def;
  }

  public static com.byzaneo.task.bean.TaskDefinition updateDefinition(final Object task, TaskType type, com.byzaneo.task.bean.TaskDefinition def) {
    def.setDefinition(createGson(type).toJson(task, type.getType()));
    updateDefinitionUuid(def);
    return def;
  }

  private static void updateDefinitionUuid(TaskDefinition def) {
    final String portalType = "portal/";
    final String type = def.getType();
    if (!isBlank(type) && type.startsWith(portalType) && isBlank(def.getUuid())) {
      def.setUuid(UUID.randomUUID()
          .toString());
    }
  }

  /**
   * @param task
   * @return the definition associated to the task in its proxy handler
   */
  public static com.byzaneo.task.bean.TaskDefinition getDefinition(final Object task) {
    return mapTaskHandler(task, TaskHandler::getDefinition)
        .filter(com.byzaneo.task.bean.TaskDefinition.class::isInstance)
        .map(com.byzaneo.task.bean.TaskDefinition.class::cast)
        .orElse(null);
  }

  /**
   * @param task
   * @return true if task is not null and {@link TaskDefinition#isLocked()} is true
   * @since 1.0 TSK-8
   */
  public static boolean isLocked(final Object task) {
    TaskDefinition def;
    return (def = getDefinition(task)) != null && def.isLocked();
  }

  /**
   * @param task
   * @return the id of the task
   */
  public static Long getTaskDefinitionId(Object task) {
    return of(task).filter(Proxy.class::isInstance)
        .map(Proxy.class::cast)
        .map(proxy -> TaskHelper.getDefinition(task))
        .map(TaskDefinition::getId)
        .map(Long::longValue)
        .orElse(0L);
  }
  /*
   * -- TYPE --
   */

  /**
   * @param task
   * @return the {@link TaskType} of the given task resolved thru its {@link TaskHandler}
   */
  public static TaskType getType(final Object task) {
    return getTypeOf(task)
        .orElse(null);
  }

  /**
   * @param task
   * @return the {@link TaskType} of the given task resolved thru its {@link TaskHandler}
   */
  public static Optional<TaskType> getTypeOf(final Object task) {
    return mapTaskHandler(task, TaskHandler::getType);
  }

  /*
   * -- CONTEXT --
   */

  /**
   * @param task
   * @return the {@link TaskContext} of the given task resolved thru its {@link TaskHandler}
   */
  public static TaskContext getContext(final Object task) {
    return mapTaskHandler(task, TaskHandler::getContext)
        .orElse(null);
  }

  /**
   * Sets the context of the task. The context is holds by the {@link TaskHandler}
   * 
   * @param task
   * @param context
   */
  public static void setContext(final Object task, final TaskContext context) {
    getTaskHandlerOf(task)
        .ifPresent(th -> th.setContext(context));
  }

  /*
   * -- FACES --
   */

  /**
   * @param type type of the task
   * @param resourceName name of the resource
   * @param recursive if <code>true</code>, uses recursion to retrieve the task's resource
   * @return the streamed resource denoted by the given resourceName (<code>null</code> if not found)
   * @see JSFHelper#getResourceStream(String, String, boolean, String)
   * @see #CC_DEFAULT_LIBRARY
   */
  public static InputStream getResourceAsStream(final TaskType type, String resourceName, boolean recursive) {
    return getResourceStream(getFacesLibrary(type, false), resourceName, recursive, CC_DEFAULT_LIBRARY);
  }

  /**
   * @param type type of the task
   * @param resourceName name of the resource
   * @param recursive if <code>true</code>, uses recursion to retrieve the task's resource
   * @return the resource request path denoted by the given resourceName (<code>null</code> if not found)
   * @see JSFHelper#getResourceStream(String, String, boolean, String)
   * @see Resource#getRequestPath()
   * @see #CC_DEFAULT_LIBRARY
   */
  public static String getResourceAsRequestPath(final TaskType type, String resourceName, boolean recursive) {
    return getResourceRequestPath(getFacesLibrary(type, false), resourceName, recursive, CC_DEFAULT_LIBRARY);
  }

  /**
   * @param task task
   * @param mode {@link TaskViewMode} type
   * @return the view resource (<code>null</code> if not found)
   * @see JSFHelper#resolveResource(String, String, boolean, boolean, String, String, String)
   * @see #CC_DEFAULT_LIBRARY
   */
  public static Resource getViewResource(final Object task, final TaskViewMode mode) {
    if (task == null || mode == null)
      return null;
    return getViewResource(getType(task), mode);
  }

  /**
   * @param type type of the task
   * @param mode {@link TaskViewMode} type
   * @return the view resource (<code>null</code> if not found)
   * @see JSFHelper#resolveResource(String, String, boolean, boolean, String, String, String)
   * @see #CC_DEFAULT_LIBRARY
   */
  public static Resource getViewResource(final TaskType type, TaskViewMode mode) {
    if (type == null || mode == null)
      return null;
    final String resourceName = mode.toString() + COMPOSITE_EXTENSION;
    return resolveResource(
        // library
        getFacesLibrary(type, false),
        // name
        resourceName,
        // recursion
        true,
        // don't use library path names as resource name during the recursion
        // since we use the same name for all the task view (the mode)
        false,
        // default library name when recursion reached the root library path
        CC_DEFAULT_LIBRARY,
        // component resource name in the root library
        CC_DEFAULT_TASK_VIEW_LIBRARY.concat("/")
            .concat(resourceName),
        // HTML content type
        HTML.getDefaultMime());
  }

  /**
   * Inserts the given task into the parent component.
   *
   * @param containerId the identifier of the parent component where to insert the task's composite component.
   * @param task the task to insert
   * @param taskType the type of the task (optional if task is not null)
   * @param mode the {@link TaskViewMode} used to view the task (default is {@link TaskViewMode#view})
   * @param attributes of the composite component
   * @return the composite component created
   */
  public static UIComponent insertTask(final String containerId,
      final Object task, final TaskType taskType, final TaskViewMode mode,
      final TaskAttributes attributes) {

    if (containerId == null) {
      log.error("Composite Component container with identifer '{}' not found in view", containerId);
      return null;
    }
    final UIComponent container = getFacesContext().getViewRoot()
        .findComponent(containerId);
    if (container == null) {
      log.error("Composite Component container with identifer '{}' not found in view", containerId);
      return null;
    }
    return insertTask(container, task, taskType, mode, attributes);
  }

  /**
   * Inserts the given task into the parent component.
   *
   * @param container the parent component where to insert the task's composite component.
   * @param task the task to insert
   * @param taskType the type of the task (optional if task is not null)
   * @param mode the {@link TaskViewMode} used to view the task (default is {@link TaskViewMode#view})
   * @param attributes of the composite component
   * @return the composite component created
   */
  public static UIComponent insertTask(final UIComponent container,
      final Object task, final TaskType taskType, final TaskViewMode mode,
      final TaskAttributes attributes) {

    // task container
    if (container == null) {
      log.error("Composite Component container not specified");
      return null;
    }

    // resolves the view mode (default is 'view')
    final String smode = mode == null ? view.toString() : mode.toString();
    // resolves the task type
    final TaskType type = task != null ? getType(task) : taskType;

    // no task
    if (task == null && type == null) {
      log.warn("Task and its type not found");
      createCompositeComponent(container, "components/task", "noTask", "noTask");
    }
    // insert task
    else {
      try {
        String id = getFacesCompositeId(type, mode);
        // for the multilangue id must be null
        if (edit.toString()
            .equals(smode))
          id = null;
        final UIComponent cctask = createCompositeComponent(container,
            getViewResource(type, TaskViewMode.valueOf(smode)),
            // CC ID
            id,
            // Attributes
            attributes.toAttributes());
        if (cctask == null)
          insertMessageTask(
              new FacesMessage(SEVERITY_ERROR, "Error creating task",
                  format("Task: %s, Type: %s, Mode: %s",
                      task, type, smode)),
              container);
        else
          return cctask;
      }
      catch (Exception e) {
        final String details = format("Task: %s, Type: %s, Mode: %s, Error: %s",
            task, type, smode, getRootCauseMessage(e));
        log.error("Error inserting task: ".concat(details), e);
        insertMessageTask(new FacesMessage(SEVERITY_ERROR, "Error creating task", details), container);
      }
    }

    return null;
  }

  /**
   * Inserts the messageTask composite component sets with the faces message in the given container.
   * 
   * @param message message to display
   * @param container parent component where to insert the messageTask
   */
  public static void insertMessageTask(final FacesMessage message, final UIComponent container) {
    createCompositeComponent(container,
        "components/task", "messageTask", "messageTask",
        new Attribute("message", message, message.getClass()));
  }

  /**
   * @return a DSL instance of the {@link TaskAttributes} class
   */
  public static TaskAttributes createAttributes() {
    return new TaskAttributes();
  }

  /**
   * @param type type of the task
   * @param noVersion if true, appends the version to the library path
   * @return to lower case {@link TaskType#getLibrary()}/{@link TaskType#getName()}/{@link TaskType#getVersion()}
   * @see #toFacesCase(String)
   */
  private static String getFacesLibrary(final TaskType type, final boolean noVersion) {
    return format("%s/%s%s",
        type.getLibrary(),
        toFacesCase(type.getName()),
        noVersion ? "" : "/" + toFacesCase(type.getVersion()))
            .replace("//", "/")
            .toLowerCase();
  }

  /**
   * @param type type of the task
   * @param mode view mode
   * @return the {@link #toFacesCase(String)} formatted {@link TaskType#getName()} suffixed by the capitalized given view mode
   */
  private static String getFacesCompositeId(final TaskType type, TaskViewMode mode) {
    String r = uncapitalize(toFacesCase(type.getName()));
    return (r.length() < 4 ? r.toLowerCase() : r).concat(capitalize(mode.toString()));
  }

  /**
   * Escapes special characters, removes white spaces, replace "." by "_"
   * 
   * @param string
   * @return the given string as a faces case formatted string
   */
  private static String toFacesCase(String string) {
    return trimToEmpty(string).replaceAll("[^\\d\\.A-Za-z]", "")
        .replace(".", "_");
  }

  /*
   * -- ANNOTATION --
   */

  // -- ACTION --

  /**
   * @param types
   * @return the list of {@link TaskType} filtered with the types containing least one {@link TaskAction} annotated method.
   */
  public static List<TaskType> getActionTypes(Collection<TaskType> types) {
    if (isEmpty(types))
      return emptyList();

    final List<TaskType> r = new ArrayList<>(types.size());
    for (TaskType type : types)
      if (isActionType(type)) r.add(type);

    return r;
  }

  /**
   * @param type
   * @return checks if the given {@link TaskType} contains at least one {@link TaskAction} annotated method.
   */
  public static boolean isActionType(final TaskType type) {
    return type != null && !getMethodsAnnotatedWith(type.getType(), TaskAction.class).isEmpty();
  }

  /**
   * @param type
   * @return the list of action's names for the {@link TaskAction} annotated methods.
   */
  public static List<String> getActionNames(final TaskType type) {
    if (type == null)
      return emptyList();

    final List<Method> methods = getMethodsAnnotatedWith(type.getType(), TaskAction.class);
    final List<String> r = new ArrayList<>(methods.size());
    for (Method method : methods)
      r.add(getActionName(method));

    return r;
  }

  public static Method getActionMethod(final TaskType type, final String actionName) {
    if (type == null || isBlank(actionName))
      return null;

    final List<Method> methods = getMethodsAnnotatedWith(type.getType(), TaskAction.class);
    for (Method method : methods)
      if (actionName.equals(getActionName(method)))
        return method;

    return null;
  }

  public static String getActionName(final Method method) {
    return of(method)
        .map(m -> m.getAnnotation(TaskAction.class))
        .map(TaskAction::name)
        .orElse(method.getName());
  }

  // -- EVENT --

  /*
   * -- CALLBACK --
   */

  /**
   * The invoked annotated callback methods should not get any arguments
   *
   * @param task
   * @param annotations list of annotation to find on the methods to invoke
   * @return the given task
   * @throws TaskCallbackException in case of invocation problem
   * @since 1.1 TSK-15
   */
  @SafeVarargs
  public static <T> T invokeCallbackMedhods(T task, Class<? extends Annotation>... annotations) throws TaskCallbackException {
    try {
      invokeAnnotatedMedhods(task, annotations);
    }
    catch (Exception e) {
      // ensure this error message is logged
      log.error("An error occured while saving task :" + e.getMessage());
      throw new TaskCallbackException(e, "Error invoking callback method on: %s (%s)", task, getRootCauseMessage(e));
    }
    return task;
  }

  /*
   * -- TASK VIEW MODEL --
   */

  /**
   * @param type the class of the bean
   * @return the BeanDescriptor list of the bean
   */
  public static List<BeanDescriptor> getTaskDescriptors(final Class<?> type) {
    return getTaskDescriptors(type, view);
  }

  /**
   * @param type the class of the bean
   * @param mode the {@link TaskViewMode} of the requested model
   * @return the BeanDescriptor list of the bean
   */
  public static List<BeanDescriptor> getTaskDescriptors(final Class<?> type, final TaskViewMode mode) {
    if (type == null)
      return Collections.emptyList();

    return getViewModel(
        new com.byzaneo.task.bean.TaskType(
            type.getSimpleName(),
            CC_DEFAULT_TASK_VIEW_LIBRARY,
            CC_DEFAULT_LIBRARY,
            type),
        mode == null ? view : mode).getGroups();
  }

  /**
   * @param task the task from which to resolve the model
   * @param mode the {@link TaskViewMode} of the requested model
   * @return the {@link TaskViewModel} for the given task and mode
   * @see #getViewModel(TaskType, TaskViewMode)
   * @since 1.1 TSK-19
   */
  public static TaskViewModel getViewModel(final Object task, final TaskViewMode mode) {
    return getViewModel(getType(task), mode);
  }

  /**
   * @param type the type of the task from which to resolve the model
   * @param mode the {@link TaskViewMode} of the requested model
   * @return the {@link TaskViewModel} for the given {@link TaskType} and {@link TaskViewMode}
   * @since 1.1 TSK-19
   */
  public static TaskViewModel getViewModel(final TaskType type, final TaskViewMode mode) {
    final TaskViewModel model = new TaskViewModel();
    if (type == null || mode == null)
      return model;

    // @TaskViewModel
    com.byzaneo.task.annotation.TaskViewModel atvm = getTaskViewModelAnnotation(type);

    // fields
    for (Field field : getViewModelFields(new ArrayList<Field>(), type.getType())) {
      addPropertyView(type, mode, model, atvm,
          field, field.getName(), field.getType());
    }

    // methods
    for (Method method : getMethodsAnnotatedWith(type.getType(), TaskProperty.class)) {
      addPropertyView(type, mode, model, atvm,
          method, method.getName(), method.getReturnType());
    }

    // Finalizes the model
    if (atvm != null) {
      model.sort(atvm.groups());
      model.setLabelFamily(trimToNull(atvm.labelFamily()));
    }

    return model;
  }

  /*
   * -- CONVERSION --
   */

  /**
   * @param task
   * @return the task as a {@link TaskConversion} object
   */
  public static TaskConversion getConversion(final Object task) {
    return (TaskConversion) task;
  }

  /**
   * <p>
   * The given task should have the {@link Task#externalReference()} annotation property set. Otherwise, <code>null</code> will be returned.
   * </p>
   * <p>
   * At least one task property or mutator method (usually getter) has to be annotated with the {@link TaskExternalMapping} annotation.
   * Otherwise, the external reference instance could not be populated and will be returned as is.
   * </p>
   *
   * @param task to convert to its external referenced type
   * @return a task's external reference instance populated with the {@link TaskExternalMapping} annotated members.
   * @since 1.1 TSK-21
   */
  @SuppressWarnings("unchecked")
  public static <T> T convertToExternalReference(final Object task) {
    final Class<?> type = getType(task).getType();
    final Task atask = type.getAnnotation(Task.class);
    final Class<?> refType = atask.externalReference();

    // no external reference, no conversion
    if (DEFAULT.class.equals(refType)) {
      log.info("No external referenced type for task type: {}", type);
      return null;
    }

    // instantiates the external referenced type
    final Object ref;
    try {
      ref = refType.newInstance();
    }
    catch (InstantiationException | IllegalAccessException e) {
      throw new ServiceException(e, "Error instanciating external referenced type: %s", refType);
    }

    // retrieves the annotated properties & methods
    final List<Field> fields = getFieldsAnnotatedWith(type, TaskExternalMapping.class);
    final List<Method> methods = getMethodsAnnotatedWith(type, TaskExternalMapping.class);
    if (isEmpty(fields) && isEmpty(methods)) {
      log.warn("@TaskExternalMapping annotation not found on any field or mutator method for type: {}", type);
      return (T) ref;
    }

    // FIELDS
    // populates reference instance
    TaskExternalMapping atem;
    Method getter;
    for (Field field : fields) {
      try {
        // @TaskExternalMapping
        atem = field.getAnnotation(TaskExternalMapping.class);
        // getter method (if any)
        getter = new java.beans.PropertyDescriptor(field.getName(), type).getReadMethod();
        // set the reference property
        field.set(ref, getter == null ? field.get(task) : getter.invoke(task));
      }
      catch (Exception e) {
        log.error("Error setting external reference {} field: {} ({})", ref, field, getRootCauseMessage(e));
      }
    }

    // METHODS
    for (Method method : methods) {
      try {
        // the annotated method should have a return type
        // other than void type and no parameters (usually a
        // getter)
        if (method.getReturnType() == null || method.getParameterTypes().length > 0) {
          log.info("@TaskExternalMapping annotated method has parameters or has a void return type (i.e. not a getter): {}", method);
          continue;
        }
        // @TaskExternalMapping
        atem = method.getAnnotation(TaskExternalMapping.class);
        if (isBlank(atem.property())) {
          log.warn("@TaskExternalMapping on a method should define the external reference property to set: {}", method);
          continue;
        }
        // set the reference property
        setProperty(ref,
            atem.property(),
            method.invoke(task));
      }
      catch (Exception e) {
        log.error("Error setting external reference {} from method: {} ({})", ref, method, getRootCauseMessage(e));
      }
    }

    return (T) ref;
  }

  /**
   * @param task
   * @param externalReference
   * @return
   */
  public static void convertFromExternalReference(final Object task, final Object externalReference) {
    final Class<?> type = getType(task).getType();
    final Task atask = type.getAnnotation(Task.class);
    final Class<?> refType = atask.externalReference();

    // no external reference, no conversion
    if (DEFAULT.class.equals(refType) || !externalReference.getClass()
        .isAssignableFrom(refType)) {
      log.info("No external referenced type for task type {} or different external referenced type: {}",
          type, externalReference.getClass());
      return;
    }

    // retrieves the annotated properties & methods
    final List<Field> fields = getFieldsAnnotatedWith(type, TaskExternalMapping.class);
    final List<Method> methods = getMethodsAnnotatedWith(type, TaskExternalMapping.class);
    if (isEmpty(fields) && isEmpty(methods)) {
      log.warn("@TaskExternalMapping annotation not found on any field or mutator method for type: {}", type);
      return;
    }

    // FIELDS
    // populates reference instance
    TaskExternalMapping atem;
    Method getter;
    String fieldname;
    for (Field field : fields) {
      // @TaskExternalMapping
      atem = field.getAnnotation(TaskExternalMapping.class);
      // referenced field name
      fieldname = isBlank(atem.property()) ? field.getName() : atem.property();
      try {
        // getter method (if any)
        getter = new java.beans.PropertyDescriptor(fieldname, externalReference.getClass()).getReadMethod();
        // sets the task property
        setProperty(task,
            field.getName(),
            getter == null ? getProperty(externalReference, fieldname) : getter.invoke(externalReference));
      }
      catch (Exception e) {
        log.error("Error setting task type '{}' field '{}' from external reference field: {} ({})",
            type, field, fieldname, getRootCauseMessage(e));
      }
    }

    // METHODS
    for (Method method : methods) {
      // @TaskExternalMapping
      atem = method.getAnnotation(TaskExternalMapping.class);
      if (isBlank(atem.property())) {
        log.warn("@TaskExternalMapping on a method should define the external reference property to get: {}", method);
        continue;
      }
      // the annotated method should have a return type
      // other than void type and no parameters (usually a
      // getter)
      if (method.getReturnType() == null || method.getParameterTypes().length > 0) {
        log.warn("@TaskExternalMapping annotated method has parameters or has a void return type: {}", method);
        continue;
      }
      // sets the task method
      try {
        method.invoke(task, getProperty(externalReference, atem.property()));
      }
      catch (Exception e) {
        log.error("Error setting task method {} from external referenced property: {} ({})",
            method, atem.property(), getRootCauseMessage(e));
      }
    }
  }

  /**
   * @param task
   * @return
   */
  public static String convertToJson(final Object task) {
    return convertToJsonTree(task).toString();
  }

  /**
   * @param task
   * @return
   */
  public static JsonElement convertToJsonTree(final Object task) {
    // updates the definition from the task properties
    updateDefinition(task);
    // converts the definition to JSon
    return getGson().toJsonTree(getDefinition(task), com.byzaneo.task.bean.TaskDefinition.class);
  }

  /**
   * @param type
   * @param json
   * @return
   */
  public static <T> T convertFromJson(final TaskType type, final String json) {
    return createTask(getGson().fromJson(json, com.byzaneo.task.bean.TaskDefinition.class), type);
  }

  /*
   * -- LABELS --
   */

  public static void info(final Object task, String messageKey, Object... args) {
    log(task, null, SEVERITY_INFO, messageKey, args);
  }

  public static void warn(final Object task, String messageKey, Object... args) {
    log(task, null, SEVERITY_WARN, messageKey, args);
  }

  public static void error(final Object task, Throwable t, String messageKey, Object... args) {
    log(task, t, SEVERITY_ERROR, messageKey, args);
  }

  private static void log(final Object task, Throwable t, Severity severity, String messageKey, Object... args) {
    addMessage(t, severity, label(getType(task), JSFHelper.getLocale(), messageKey, messageKey, args), args);
  }

  public static String label(final Object task, final Locale locale, final String messageKey, final String defaultMessage,
      final Object... args) {
    return label(getType(task), locale, messageKey, defaultMessage, args);
  }

  public static String label(final Object task, final Locale locale, final String messageKey, final String defaultMessage,
                            TaskType taskType, final Object... args) {
    return label(taskType, locale, messageKey, defaultMessage, args);
  }

  public static String label(final TaskType type, final Locale locale, final String messageKey, final String defaultMessage,
      final Object... args) {
    com.byzaneo.task.annotation.TaskViewModel atvm = type.getType()
        .getAnnotation(com.byzaneo.task.annotation.TaskViewModel.class);
    // search labels in the same place
    if (atvm == null) {
      String msg = null;
      try {
        com.byzaneo.task.bean.TaskType typeImpl = (com.byzaneo.task.bean.TaskType) type;
        ResourceBundle bundle = typeImpl.getResourceBundle(locale);
        if (bundle == null) {
          bundle = new PropertyResourceBundle(getResourceAsStream(type, "labels_" + locale.getLanguage() + ".properties", false));
          typeImpl.addResourceBundle(locale, bundle);
        }
        if (isEmpty(args))
          msg = bundle.getString(messageKey);
        else
          msg = MessageFormat.format(bundle.getString(messageKey), args);
      }
      catch (Exception e) {
        log.error("Error getting resource bundles for localization of the task {} ({})", type.getName(), getRootCauseMessage(e));
        msg = defaultMessage;
      }
      return defaultString(msg, defaultMessage);
    }
    // use annotation family var
    else {
      return getMessage(atvm.labelFamily() + "." + messageKey, defaultMessage, locale, args);
    }
  }

  /**
   * @param task
   * @return the {@link com.byzaneo.task.annotation.TaskViewModel#labelFamily()} value of the given task
   */
  public static String getLabelFamily(final Object task) {
    try {
      return getType(task).getType()
          .getAnnotation(com.byzaneo.task.annotation.TaskViewModel.class)
          .labelFamily();
    }
    catch (Exception e) {
      return null;
    }
  }

  /*
   * -- XML --
   */

  /**
   * @param task
   * @return the list of {@link JAXBElement} contained in the given task
   * @throws IntrospectionException see {@link java.beans.PropertyDescriptor#PropertyDescriptor(String, Class)}
   * @throws InvocationTargetException see {@link Method#invoke(Object, Object...)}
   * @throws IllegalArgumentException see {@link Method#invoke(Object, Object...)}
   * @throws IllegalAccessException see {@link Method#invoke(Object, Object...)} TODO list type JAXBElement<String>
   */
  public static List<Object> marshal(Object task) throws IllegalAccessException, IllegalArgumentException,
      InvocationTargetException, IntrospectionException {
    if (task == null)
      return emptyList();
    final List<Object> r = new ArrayList<>();
    final TaskType taskType = getType(task);
    final Field[] fields = taskType.getType()
        .getDeclaredFields();
    for (Field field : fields) {
      if (field.isAnnotationPresent(TaskProperty.class)) {
        Object value = new java.beans.PropertyDescriptor(field.getName(), taskType.getType()).getReadMethod()
            .invoke(task);
        if (value != null && StringUtils.isNotBlank(value.toString())) {
          JAXBElement<String> element = new JAXBElement<>(new QName(field.getName()), String.class, value.toString());
          r.add(element);
        }
      }
    }
    return r;
  }

  /**
   * @param taskType
   * @param elements
   * @return the task of the given type populated with the elements
   */
  public static Object unmarshal(TaskType taskType, List<Object> elements)
      throws NoSuchFieldException, SecurityException, IllegalAccessException,
      IllegalArgumentException, InvocationTargetException, InstantiationException,
      NoSuchMethodException, DOMException, IntrospectionException {
    if (taskType == null || elements == null)
      return null;

    final Object task = TaskTypeHelper.newInstance(taskType);
    for (Object element : elements) {
      if (element instanceof ElementNSImpl) {
        Field field = taskType.getType()
            .getDeclaredField(((ElementNSImpl) element).getTagName());
        new java.beans.PropertyDescriptor(((ElementNSImpl) element).getTagName(),
            taskType.getType()).getWriteMethod()
                .invoke(task, field.getType()
                    .getConstructor(String.class)
                    .newInstance(((ElementNSImpl) element).getTextContent()));
      }
      else {
        throw new IllegalArgumentException("Element not supported.");
      }
    }

    return task;
  }

  /*
   * -- PRIVATES --
   */

  /**
   * @param type task type
   * @param mode view mode
   * @param model where to append the property view
   * @param atvm (optional) task view model
   * @param accessibleObject field or method to process
   * @param name name of field or the method processed
   * @param propertyType type of the field or returned by the method
   * @return <code>true</code> if the property view has been added to the given model
   */
  private static boolean addPropertyView(
      TaskType type,
      TaskViewMode mode,
      TaskViewModel model,
      com.byzaneo.task.annotation.TaskViewModel atvm,
      AccessibleObject accessibleObject,
      String name,
      Class<?> propertyType) {

    // transient or excluded?
    if (accessibleObject.isAnnotationPresent(TaskTransient.class) ||
        (atvm != null && contains(atvm.excludes(), name)))
      return false;

    // @TaskPropertyOverride
    TaskPropertyOverride atpo = findTaskPropertyOverride(atvm, name);
    // @TaskProperty
    TaskProperty atp = atpo != null ? atpo.property() : accessibleObject.getAnnotation(TaskProperty.class);
    // @TaskPropertyMode
    TaskPropertyMode atpm = findTaskPropertyMode(mode, atp);

    // returns false if the annotation is present and has defined
    // modes except the requested one
    if (atp != null && atp.modes().length > 0 && atpm == null)
      return false;

    // default PropertyView
    PropertyDescriptor pv = new PropertyDescriptor(name);
    pv.setRendered(TRUE);
    pv.setSize(inherited.toString());
    // resolves default label expression
    pv.setLabel(resolveLabelExpression(atvm, atp, type.getName(), name));
    // nested bean type resolution
    pv.setType(propertyType);

    // populates the property view with the annotations
    // settings (if any) and add it to the right group
    // (default group if not specified)
    model.getGroup(populatePropertyView(pv, atp, atpm, propertyType))
        .addProperty(pv);

    return true;
  }

  /**
   * @param atvm (optional) TaskViewModel annotation holding the label family property
   * @param atp task property annotation
   * @param taskName the name of the {@link TaskType}
   * @param accessorName the name of the property or the annotated method
   * @return the capitalized accessorName if the TaskViewModel or its label family name are <code>null</code>. Otherwise, creates the
   *         expression: <code>#{familyName['(taskName_)accessorName']}</code>
   */
  private static final String resolveLabelExpression(final com.byzaneo.task.annotation.TaskViewModel atvm,
      TaskProperty atp, final String taskName, final String accessorName) {
    return atvm != null && isNotBlank(atvm.labelFamily()) ?
    // creates label expression based
    // on the @TaskViewModel labelFamily
    // property
        format("#{%s['%s%s']}",
            atvm.labelFamily(),
            atp == null || atp.labelPrefixed() ? taskName.concat("_") : "",
            accessorName)
        :
        // capitalize the label
        capitalize(accessorName);
  }

  /**
   * @param pv property view to populate
   * @param atp the {@link TaskProperty} annotation on the property or method
   * @param atpm the {@link TaskPropertyMode} associated to the requested TaskViewMode
   * @param memberType the type of the field or the method's return
   * @return the populated property view with the annotations settings (if any) and add it to the right group (default group if not
   *         specified)
   */
  private static final String populatePropertyView(final PropertyDescriptor pv,
      final TaskProperty atp, final TaskPropertyMode atpm,
      final Class<?> memberType) {
    String group = DEFAULT_GROUP_NAME;
    Class<?> type = memberType;

    // @TaskProperty
    if (atp != null) {
      // label
      if (isNotBlank(atp.label()))
        pv.setLabel(atp.label());
      // placeHolder
      if (isNotBlank(atp.description())) {
        pv.setDescription(atp.description());
      }
      // converter
      pv.setConverter(trimToNull(atp.converter()));
      // size
      if (!inherited.equals(atp.size()))
        pv.setSize(atp.size()
            .toString());
      // required
      pv.setRequired(atp.required());
      // editable
      pv.setEditable(atp.editable());
      // min
      pv.setMin(atp.min());
      // max
      pv.setMax(atp.max());
      // group
      if (isNotBlank(atp.group()))
        group = atp.group();
      // type
      if (!Object.class.equals(atp.type()))
        type = atp.type();
      // model
      pv.setModelExpression(trimToNull(atp.model()));
      // @TaskPropertyMode
      if (atpm != null) {
        pv.setRendered(atpm.rendered());
        pv.setReadonly(atpm.readonly());
        pv.setEditable(atpm.editable());
        pv.setRequired(atpm.required());
        if (atpm.position() != -1)
          pv.setIndex(atpm.position());
        if (isNotBlank(atpm.group()))
          group = atpm.group();
      }
    }

    // property view type
    resolveViewType(atp, pv, type);

    // expression converter (if property's type is an expression)
    if (pv.getConverter() == null && ("org.activiti.engine.delegate.Expression".equals(memberType.getName()) || // NOSONAR (squid:S1872)
                                                                                                                // Activiti is an optional
                                                                                                                // dependency
        "org.activiti.engine.delegate.Expression".equals(memberType.getName()))) // NOSONAR (squid:S1872) Activiti is an optional dependency
      pv.setConverter("processExpressionConverter");

    return group;
  }

  /**
   * @param mode to match
   * @param atp (optional) holding the modes
   * @return the {@link TaskPropertyMode} within the given annotation with the given mode
   */
  private static final TaskPropertyMode findTaskPropertyMode(final TaskViewMode mode, final TaskProperty atp) {
    return atp != null
        ? asList(atp.modes())
            .stream()
            .filter(m -> mode.equals(m.mode()))
            .findFirst()
            .orElse(null)
        : null;
  }

  /**
   * @param atvm (optional) task view model annotation
   * @param name the name of the overrode property
   * @return the {@link TaskPropertyOverride} with the given name
   */
  private static final TaskPropertyOverride findTaskPropertyOverride(final com.byzaneo.task.annotation.TaskViewModel atvm,
      final String name) {
    return atvm != null
        ? asList(atvm.overrides())
            .stream()
            .filter(o -> o.name()
                .equals(name))
            .findFirst()
            .orElse(null)
        : null;
  }

  /**
   * @param atp task property annotation
   * @param pv task property descriptor
   * @param type of the property value
   * @return the {@link PropertyDescriptor} set with the right type
   */
  private static PropertyDescriptor resolveViewType(TaskProperty atp, PropertyDescriptor pv, Class<?> type) {
    // configuration
    final TaskPropertyType tpt = TaskPropertyType.valueOf(atp, type);
    switch (tpt) {
    case Options:
      pv.setTypeName(tpt.getName());
      pv.setOptionsExpression(atp.options());
      break;
    case Expression:
      pv.setTypeName(tpt.getName());
      pv.setSize(xxlarge.toString());
      break;
    case Enum:
      pv.setTypeName(tpt.getName());
      final Object[] eoptions = type.getEnumConstants();
      pv.setOptions(new LinkedHashMap<Object, String>(eoptions.length + (pv.getRequired() ? 0 : 1)));
      if (!pv.getRequired())
        pv.getOptions()
            .put("", "");
      for (Object eoption : eoptions)
        pv.getOptions()
            .put(eoption, eoption.toString());
      break;
    default:
      pv.setTypeName(tpt.getName());
      break;
    }
    return pv;
  }

  /**
   * @param type of the task
   * @return the {@link com.byzaneo.task.annotation.TaskViewModel} annotation merged with the parent types.
   */
  private static final com.byzaneo.task.annotation.TaskViewModel getTaskViewModelAnnotation(final TaskType type) {
    // gets annotations
    final List<com.byzaneo.task.annotation.TaskViewModel> atvms = new ArrayList<>();
    Class<?> currentType = type.getType();
    while (currentType != null) {
      com.byzaneo.task.annotation.TaskViewModel atvm = currentType.getAnnotation(com.byzaneo.task.annotation.TaskViewModel.class);
      if (atvm != null)
        atvms.add(atvm);
      currentType = currentType.getSuperclass();
    }

    // no annotation...
    if (atvms.isEmpty())
      return null;

    // single annotation...
    if (atvms.size() == 1)
      return atvms.get(0);

    // merge...
    // overrides: adds parents class overrode properties
    // if not already defined in a subclass.
    final List<TaskPropertyOverride> overrideList = new ArrayList<TaskPropertyOverride>();
    for (com.byzaneo.task.annotation.TaskViewModel atvm : atvms) {
      for (TaskPropertyOverride override : atvm.overrides()) {
        if (overrideList.stream()
            .noneMatch(o -> o.name()
                .equals(override.name()))) {
          overrideList.add(override);
        }
      }
    }
    final TaskPropertyOverride[] overrides = overrideList.toArray(new TaskPropertyOverride[overrideList.size()]);

    // labelFamily
    // gets the first label family found in the current
    // then in the parent types annotations
    String lf = null;
    int i = 0;
    while (lf == null && i < atvms.size()) {
      if (isNotBlank(atvms.get(i)
          .labelFamily()))
        lf = atvms.get(i)
            .labelFamily();
      i++;
    }
    final String labelFamily = lf;

    // groups
    reverse(atvms);
    final Set<String> groupSet = new LinkedHashSet<>();
    for (com.byzaneo.task.annotation.TaskViewModel atvm : atvms) {
      if (atvm.groups().length > 0) {
        List<String> groups = asList(atvm.groups());
        groupSet.removeAll(groups);
        groupSet.addAll(groups);
      }
    }
    final String[] groups = groupSet.toArray(new String[groupSet.size()]);

    // excluded
    final Set<String> excludedSet = new LinkedHashSet<>();
    for (com.byzaneo.task.annotation.TaskViewModel atvm : atvms) {
      if (atvm.excludes().length > 0) {
        excludedSet.addAll(asList(atvm.excludes()));
      }
    }
    final String[] excludes = excludedSet.toArray(new String[excludedSet.size()]);

    // returns merged annotations
    return new com.byzaneo.task.annotation.TaskViewModel() {
      @Override
      public Class<? extends Annotation> annotationType() {
        return com.byzaneo.task.annotation.TaskViewModel.class;
      }

      @Override
      public TaskPropertyOverride[] overrides() {
        return overrides;
      }

      @Override
      public String labelFamily() {
        return labelFamily;
      }

      @Override
      public String[] groups() {
        return groups;
      }

      @Override
      public String[] excludes() {
        return excludes;
      }
    };
  }

  /**
   * @return the fields in the given type (superclass fields are processed first). Skips:
   *         <ul>
   *         <li>final fields</li>
   *         <li>static fields</li>
   *         <li>fields without getter and setter</li>
   *         <li>{@link TaskTransient} annotated classes and fields</li>
   *         </ul>
   */
  @SuppressWarnings("squid:S1226")
  private static final List<Field> getViewModelFields(List<Field> fields, final Class<?> type) {
    // skipped type?
    if (Object.class.equals(type) ||
        type.isAnnotationPresent(TaskTransient.class))
      return fields;

    // super class?
    if (type.getSuperclass() != null)
      fields = getViewModelFields(fields, type.getSuperclass());

    // filters fields
    java.beans.PropertyDescriptor pd;
    for (Field field : type.getDeclaredFields()) {
      try {
        if (!field.isAnnotationPresent(TaskTransient.class) &&
            !isStatic(field.getModifiers()) &&
            !isFinal(field.getModifiers()) &&
            (pd = new java.beans.PropertyDescriptor(field.getName(), type)).getReadMethod() != null &&
            pd.getWriteMethod() != null)
          fields.add(field);
      }
      catch (IntrospectionException e) {
        log.debug("Error property descriptor: {} ({})", field.getName(), getRootCauseMessage(e));
      }
    }
    return fields;
  }

  private static final Optional<TaskHandler> getTaskHandlerOf(final Object task) {
    if (task == null)
      return empty();
    try {
      return of((TaskHandler) getHandler((Proxy) task));
    }
    catch (RuntimeException e) {
      log.error("Error getting proxy task handler from object: {} ({})",
          task, getRootCauseMessage(e));
      return empty();
    }
  }

  private static final <T> Optional<T> mapTaskHandler(
      final Object task,
      final Function<? super TaskHandler, T> mapper) {
    return getTaskHandlerOf(task)
        .map(mapper);
  }

  private static final Gson createGson(TaskType type) {
    return GsonHelper.createGson(toDoubleVersion(type.getVersion()), new TaskDefinitionFieldExclusion(type.getType()));
  }

  private static final double toDoubleVersion(String version) {
    String[] digits = split(version, '.');
    return Double.valueOf(digits[0]) + (Double.valueOf(digits[1]) / 10);
  }

  /*
   * -- INNERS --
   */

  public static final class TaskDefinitionFieldExclusion implements ExclusionStrategy {
    private final Class<?> type;

    public TaskDefinitionFieldExclusion(Class<?> type) {
      this.type = type;
    }

    @Override
    public boolean shouldSkipClass(Class<?> type) {
      return false;
    }

    @Override
    public boolean shouldSkipField(FieldAttributes f) {
      // excludes only the following fields if the f declared class
      // corresponds to the task class (avoids exclusion on the task's
      // field class)
      return type.equals(f.getDeclaredClass()) && ("id".equals(f.getName()) ||
          "title".equals(f.getName()) ||
          "description".equals(f.getName()) ||

          "definition".equals(f.getName()));
    }
  }
}
