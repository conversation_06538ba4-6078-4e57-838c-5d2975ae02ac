package com.byzaneo.commons.util;

import static org.apache.commons.io.IOUtils.closeQuietly;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import org.apache.tools.ant.BuildException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> ROSSI
 * @company Byzaneo
 * @version CVS $Revision: 1.1 $ $Date: 2006-11-20 15:19:15 $
 */
public class CatalinaHelper {
  private static final Logger log = LoggerFactory.getLogger(CatalinaHelper.class);

  /** manager webapp's encoding (default: "utf-8"). */
  private static String CHARSET = "utf-8";

  /**
   * private constructor to hide the implicit public one
   */
  private CatalinaHelper() {
  }

  /**
   * Execute the specified command, based on the configured properties. The input stream will be closed upon completion of this task,
   * whether it was executed successfully or not.
   *
   * @param command Command to be executed
   * @param istream InputStream to include in an HTTP PUT, if any
   * @param contentType Content type to specify for the input, if any
   * @param contentLength Content length to specify for the input, if any
   * @exception BuildException if an error occurs
   */
  public static String[] manager(String command, InputStream istream, String username, String password, String url,
      String contentType, int contentLength) {

    if (username == null || password == null || url == null) {
      log.error("Username (" + username + ") or Password (" + password + ") or Manager URL (" + url + ") is missing");
      return new String[0];
    }

    URLConnection conn = null;
    InputStreamReader reader = null;
    StringBuilder buff = new StringBuilder();
    List<String> results = new ArrayList<String>();
    try {

      // Create a connection for this command
      conn = (new URL(url + command)).openConnection();
      HttpURLConnection hconn = (HttpURLConnection) conn;

      // Set up standard connection characteristics
      hconn.setAllowUserInteraction(false);
      hconn.setDoInput(true);
      hconn.setUseCaches(false);
      if (istream != null) {
        hconn.setDoOutput(true);
        hconn.setRequestMethod("PUT");
        if (contentType != null) {
          hconn.setRequestProperty("Content-Type", contentType);
        }
        if (contentLength >= 0) {
          hconn.setRequestProperty("Content-Length", Integer.toString(contentLength));
        }
      }
      else {
        hconn.setDoOutput(false);
        hconn.setRequestMethod("GET");
      }
      hconn.setRequestProperty("User-Agent",
          "Catalina-xTrade/1.0");

      // Set up an authorization header with our credentials
      String input = username + ":" + password;
      String output = new String(Base64Helper.encode(input.getBytes()));
      hconn.setRequestProperty("Authorization",
          "Basic " + output);

      // Establish the connection with the server
      hconn.connect();

      // Send the request data (if any)
      if (istream != null) {
        BufferedOutputStream ostream = new BufferedOutputStream(hconn.getOutputStream(), 1024);
        byte buffer[] = new byte[1024];
        while (true) {
          int n = istream.read(buffer);
          if (n < 0) {
            break;
          }
          ostream.write(buffer, 0, n);
        }
        ostream.flush();
        ostream.close();
        istream.close();
      }

      // Process the response message
      reader = new InputStreamReader(hconn.getInputStream(), CHARSET);
      while (true) {
        int ch = reader.read();
        if (ch < 0) {
          break;
        }
        else if ((ch == '\r') || (ch == '\n')) {
          String line = buff.toString();
          buff.setLength(0);
          if (line.trim()
              .length() > 0) {
            // if ( log.isDebugEnabled() )
            // log.debug("["+url+command+"]"+line)
            results.add(line.trim());
          }
        }
        else {
          buff.append((char) ch);
        }
      }
    }
    catch (Exception e) {
      log.error("Error during catalina manager access", e);
    }
    finally {
      closeQuietly(reader);
      closeQuietly(istream);
    }
    return results.toArray(new String[results.size()]);
  }

  /**
   * @param managerLogin
   * @param managerPassword
   * @return
   */
  public static String getWebAppPath(String managerLogin, String managerPassword, String managerUrl, String webAppName) {
    // Looking for the webapp module to get the docBase in used.
    // http://localhost:8080/manager/jmxproxy/?qry=*Catalina:j2eeType=WebModule,name=//localhost/epcos,*
    String webAppPath = findMBeanProperty(managerUrl, "jmxproxy/?qry=*Catalina:j2eeType=WebModule,name=//localhost/" + webAppName + ",*",
        managerLogin, managerPassword, "docBase");
    if (webAppPath == null) {
      log.error("docBase not found in the WebModule " + webAppName);
      return new String();
    }

    // check if the docbase is a directory
    File dir = new File(webAppPath);
    if (dir.isDirectory()) {
      return webAppPath;
    }

    // it's probable juste the context name in the cataline's webapps directory
    // so, lets search the path of the webapps directory
    // http://localhost:8080/manager/jmxproxy/?qry=*Catalina:type=Engine,*
    String baseDir = findMBeanProperty(managerUrl, "jmxproxy/?qry=*Catalina:type=Engine,*",
        managerLogin, managerPassword, "baseDir");
    if (baseDir == null) {
      log.error("baseDir not found for the current Catalina Engine");
      return new String();
    }

    // constructs the webapp dir
    dir = new File(baseDir + File.separator + "webapps" + File.separator + webAppPath);
    if (dir.isDirectory()) {
      return dir.getAbsolutePath();
    }

    return null;
  }

  public static final String findMBeanProperty(String url, String query, String user, String pwd, String propertyKey) {
    log.info("Quering Catalina MBean looking for property " + propertyKey + " at " + url + query + " [" + user + "/*****]");
    String[] results = manager(query, null, user, pwd, url, null, -1);
    if (isSingleResult(results)) {
      return retreiveMBeanPropertyValue(results, propertyKey);
    }
    else if (isErrorResult(results)) {
      log.error("Error during the MBean property [" + propertyKey + "] retreiving on jmxServer [" + url + query + "] : " + results);
    }
    else {
      log.error("More than one MBean found at " + url + query + " : " + results);
    }
    return null;
  }

  public static final boolean isSingleResult(String[] results) {
    return results != null && results.length > 0 &&
        results[0].trim()
            .startsWith("OK") &&
        results[0].trim()
            .endsWith("1");
  }

  public static final boolean isErrorResult(String[] results) {
    return results == null || results.length == 0 ||
        (results.length > 0 && !results[0].trim()
            .startsWith("OK")) ||
        (results.length > 0 && results[0].trim()
            .endsWith("0"));
  }

  public static final String retreiveMBeanPropertyValue(String[] results, String propKey) {
    for (int i = 0; i < results.length; i++) {
      String property = results[i].trim();
      if (property.startsWith(propKey))
        return property.substring(property.indexOf(':') + 1)
            .trim();
    }
    return null;
  }

  public static void forceStopJDBCDrivers() {
    //04-Feb-2025 16:27:20.301 WARNING [main] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc The web application [generix_ui_generic_war_exploded] registered the JDBC driver [org.postgresql.Driver] but failed to unregister it when the web application was stopped. To prevent a memory leak, the JDBC Driver has been forcibly unregistered.
    log.info("Unregistering JDBC drivers");
    Enumeration<Driver> drivers = DriverManager.getDrivers();
    while (drivers.hasMoreElements()) {
      Driver driver = drivers.nextElement();
      try {
        DriverManager.deregisterDriver(driver);
        log.debug("Unregistered driver: {}", driver.toString());
      }
      catch (SQLException e) {
        log.warn("Error when unregistering driver: {}", driver.toString());
        log.debug(e.getMessage(), e);
      }
    }
  }
}
