package com.byzaneo.commons.util;

import static java.util.Collections.emptyList;
import static javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION;
import static javax.xml.xpath.XPathConstants.BOOLEAN;
import static javax.xml.xpath.XPathConstants.NODE;
import static javax.xml.xpath.XPathConstants.NODESET;
import static javax.xml.xpath.XPathConstants.NUMBER;
import static javax.xml.xpath.XPathConstants.STRING;
import static org.apache.commons.io.IOUtils.closeQuietly;

import java.io.File;
import java.io.InputStream;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import javax.xml.namespace.QName;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Attr;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import com.byzaneo.commons.service.ServiceException;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Dec 14, 2011
 * @since 2.5 COM-95
 */
public class DomHelper {
  private static final Logger log = LoggerFactory.getLogger(DomHelper.class);

  public static final ThreadLocal<DocumentBuilder> DOCUMENT_BUILDER = new ThreadLocal<DocumentBuilder>() {
    @Override
    protected DocumentBuilder initialValue() {
      try {
        return DocumentBuilderFactory.newInstance()
            .newDocumentBuilder();
      }
      catch (ParserConfigurationException ex) {
        throw new IllegalStateException("DomHelper initialization failure.", ex);
      }
    }
  };
  public static final ThreadLocal<DocumentBuilder> DOCUMENT_BUILDER_NS_AWARE = new ThreadLocal<DocumentBuilder>() {
    @Override
    protected DocumentBuilder initialValue() {
      try {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        return factory.newDocumentBuilder();
      }
      catch (ParserConfigurationException ex) {
        throw new IllegalStateException("DomHelper initialization failure.", ex);
      }
    }
  };
  private static final Transformer TRANSFORMER;
  static {
    try {
      TRANSFORMER = TransformerFactory.newInstance()
          .newTransformer();
    }
    catch (Exception e) {
      throw new IllegalStateException("DomHelper initialization failure.", e);
    }
  }

  public static final XPath XPATH = XPathFactory.newInstance()
      .newXPath(); // NOSONAR squid:S2885

  /**
   * private constructor to hide the implicit public one
   */
  private DomHelper() {
  }

  /*
   * PARSE
   */

  /**
   * Parse the given XML string as a {@link Document}
   * 
   * @param xml
   * @return the namespaces aware parsed {@link Document} or null if any error occurs
   * @throws ServiceException
   * @see {@link #parse(String, boolean)}
   */
  public static final Document parse(final String xml) {
    return parse(xml, true);
  }

  /**
   * Parse the given XML string as a {@link Document}
   * 
   * @param xml
   * @param namespaceAware manage namespaces (see {@link DocumentBuilderFactory#setNamespaceAware(boolean)}
   * @return the parsed {@link Document} or null if any error occurs
   */
  public static final Document parse(final String xml, final boolean namespaceAware) {
    try (Reader reader = new StringReader(xml)) {
      return namespaceAware ? DOCUMENT_BUILDER_NS_AWARE.get()
          .parse(new InputSource(reader)) : DOCUMENT_BUILDER.get()
              .parse(new InputSource(reader));
    }
    catch (Exception e) {
      log.error("Error parsing XML", e);
      return null;
    }
  }

  /**
   * @param location
   * @return the namespaces aware parsed {@link Document} or null if any error occurs
   * @see #parseStream(InputStream, boolean)
   */
  public static final Document parseResource(final String location) {
    return parseResource(location, true);
  }

  /**
   * @param location
   * @param namespaceAware manage namespaces (see {@link DocumentBuilderFactory#setNamespaceAware(boolean)}
   * @return the namespaces aware parsed {@link Document} or null if any error occurs
   * @see #parseFile(File, boolean)
   */
  public static final Document parseResource(final String location, final boolean namespaceAware) {
    return parseFile(new File(DomHelper.class.getClassLoader()
        .getResource(location)
        .getFile()), namespaceAware);
  }

  /**
   * Parse the given {@link File} as a {@link Document}
   * 
   * @param file
   * @return the namespaces aware parsed {@link Document} or null if any error occurs
   * @see {@link #parseFile(File, boolean)}
   */
  public static final Document parseFile(final File file) {
    return parseFile(file, true);
  }

  /**
   * Parse the given {@link File} as a {@link Document}
   * 
   * @param file
   * @param namespaceAware manage namespaces (see {@link DocumentBuilderFactory#setNamespaceAware(boolean)}
   * @return the parsed {@link Document} or null if any error occurs
   */
  public static final Document parseFile(final File file, final boolean namespaceAware) {
    try {
      if (file == null) {
        log.error("Error parsing file: " + file);
        return null;
      }
      return namespaceAware ? DOCUMENT_BUILDER_NS_AWARE.get()
          .parse(file) : DOCUMENT_BUILDER.get()
          .parse(file);
    }
    catch (Exception e) {
      log.error("Error parsing file: " + file, e);
      return null;
    }
  }

  /**
   * Parse the given {@link InputStream} as a {@link Document}
   * 
   * @param stream
   * @return the namespaces aware parsed {@link Document} or null if any error occurs
   * @see {@link #parseStream(InputStream, boolean)}
   */
  public static final Document parseStream(final InputStream stream) {
    return parseStream(stream, true);
  }

  /**
   * Parse the given {@link InputStream} as a {@link Document}
   * 
   * @param stream
   * @param namespaceAware manage namespaces (see {@link DocumentBuilderFactory#setNamespaceAware(boolean)}
   * @return the parsed {@link Document} or null if any error occurs
   */
  public static final Document parseStream(final InputStream stream, final boolean namespaceAware) {
    try {
      return namespaceAware ? DOCUMENT_BUILDER_NS_AWARE.get()
          .parse(stream) : DOCUMENT_BUILDER.get()
              .parse(stream);
    }
    catch (Exception e) {
      log.error("Error parsing file: " + stream, e);
      return null;
    }
    finally {
      closeQuietly(stream);
    }
  }

  /*
   * WRITE
   */

  /**
   * @param doc
   * @param file
   * @throws TransformerException
   */
  public static void writeFile(org.w3c.dom.Document doc, File file) throws TransformerException {
    TRANSFORMER.transform(
        new DOMSource(doc),
        new StreamResult(file));
  }

  /*
   * TRANSFORM
   */

  public static final String asXml(final Node node) {
    if (node == null)
      return null;
    try {
      TRANSFORMER.setOutputProperty(OMIT_XML_DECLARATION, "no");
      StringWriter writer = new StringWriter();
      TRANSFORMER.transform(new DOMSource(node), new StreamResult(writer));
      return writer.getBuffer()
          .toString();
    }
    catch (TransformerException e) {
      log.error("Error transforming document to XML.", e);
      return null;
    }
  }

  /*
   * XPATH
   */

  // -- EVALUATE --

  public static final List<Node> evaluateNodeList(final Node node, final String xpath) {
    return evaluateNodeList(node, compileXPath(xpath));
  }

  public static final List<Node> evaluateNodeList(final Node node, final XPathExpression xpath) {
    NodeList nodes = evaluate(node, xpath, NODESET);
    if (nodes == null || nodes.getLength() < 1)
      return emptyList();
    List<Node> r = new ArrayList<>(nodes.getLength());
    for (int i = 0; i < nodes.getLength(); i++)
      r.add(nodes.item(i));
    return r;
  }

  public static final String evaluateString(final Node node, final String xpath) {
    return evaluate(node, compileXPath(xpath), STRING);
  }

  public static final String evaluateString(final Node node, final XPathExpression xpath) {
    return evaluate(node, xpath, STRING);
  }

  public static final Number evaluateNumber(final Node node, final XPathExpression xpath) {
    return evaluate(node, xpath, NUMBER);
  }

  public static final Node evaluateNode(final Node node, final String xpath) {
    return evaluate(node, compileXPath(xpath), NODE);
  }

  public static final Node evaluateNode(final Node node, final XPathExpression xpath) {
    return evaluate(node, xpath, NODE);
  }

  public static final Attr evaluateAttr(final Node node, final XPathExpression xpath) {
    return (Attr) evaluate(node, xpath, NODE);
  }

  public static final Element evaluateElement(final Node node, final XPathExpression xpath) {
    return (Element) evaluate(node, xpath, NODE);
  }

  public static final <T> T evaluate(final Node node, final XPathExpression xpath, final Class<T> type) {
    if (node == null || xpath == null)
      return null;
    QName qname;
    if (Node.class.isAssignableFrom(type))
      qname = NODE;
    else if (List.class.isAssignableFrom(type))
      qname = NODESET;
    else if (Number.class.isAssignableFrom(type))
      qname = NUMBER;
    else if (Boolean.class.isAssignableFrom(type))
      qname = BOOLEAN;
    else // type==null || String.class.isAssignableFrom(type)
      qname = STRING;
    return evaluate(node, xpath, qname);
  }

  @SuppressWarnings("unchecked")
  public static final <T> T evaluate(final Node node, final XPathExpression xpath, final QName qname) {
    if (node == null || xpath == null)
      return null;
    try {
      return (T) xpath.evaluate(node, qname);
    }
    catch (Exception e) {
      log.error("Error evaluating xpath.", e);
      return null;
    }
  }

  // -- COMPILE --

  public final static XPathExpression compileXPath(final String string) {
    return compileXPath(string, XPATH);
  }

  public final static XPathExpression compileXPath(final String expression, final XPath xpath) {
    try {
      return xpath.compile(expression);
    }
    catch (XPathExpressionException e) {
      log.error("Error compiling xPath: " + expression, e);
      return null;
    }
  }
}
