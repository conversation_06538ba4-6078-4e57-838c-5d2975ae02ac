package com.byzaneo.commons.util;

import static java.util.Calendar.MONTH;
import static java.util.Calendar.SECOND;
import static java.util.Locale.ENGLISH;
import static java.util.Locale.FRENCH;
import static javax.xml.xpath.XPathConstants.NODESET;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;

import java.io.StringReader;
import java.io.StringWriter;
import java.net.URL;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import com.byzaneo.commons.bean.DatePeriod;

/**
 * Manages the feed request to the Google Calendar to retrieve events. Example: the public calendar "holiday.calendar.google.com" will give
 * you the public holidays depending of your locale and country.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Dec 29, 2010
 * @since 2.4 COM-72
 */
public class GoogleCalendarHelper {
  private static final Logger log = LoggerFactory.getLogger(GoogleCalendarHelper.class);

  // XML & XPATH
  private static final DocumentBuilder DOCUMENT_BUILDER;
  static {
    try {
      DOCUMENT_BUILDER = DocumentBuilderFactory.newInstance()
          .newDocumentBuilder();
    }
    catch (Exception e) {
      throw new IllegalStateException("Failed to init GoogleCalendarFeedHelper.", e);
    }
  }

  private static final XPath XPATH = XPathFactory.newInstance()
      .newXPath(); // NOSONAR squid:S2885
  private static final String DATE_FORMAT = "yyyy-MM-dd";

  // CACHE
  private static final EnumMap<PublicHolidayCalendar, Map<DatePeriod, String>> PUBLIC_HOLIDAYS_CALENDARS = new EnumMap<>(
      PublicHolidayCalendar.class);

  /**
   * Google Calendar Feed URL.
   * <ul>
   * <li>0: country (example: {@link Country#french})</li>
   * <li>1: locale (example: {@link Locale#FRENCH})</li>
   * <li>2: start date (example: new {@link Date}())</li>
   * <li>3: timezone (exemple: {@link TimeZone#getTimeZone(String)} with 'Europe/Paris')</li>
   * </ul>
   *
   * @see http://code.google.com/apis/calendar/data/2.0/reference.html
   */
  public static final String PUBLIC_HOLIDAYS_FEED = "http://www.google.com/calendar/feeds/%<EMAIL>/public/full?start-min=%sT12:00:00-08:00&ctz=%s&orderby=starttime&sortorder=a";

  /**
   * Google public holidays calendar definition.
   */
  public enum PublicHolidayCalendar {
    french(FRENCH,
        TimeZone.getTimeZone("Europe/Paris")),
    uk(ENGLISH,
        TimeZone.getTimeZone("Europe/London"));
    private Locale locale;
    private TimeZone timeZone;

    private PublicHolidayCalendar(Locale locale, TimeZone timeZone) {
      this.locale = locale;
      this.timeZone = timeZone;
    }

    public Locale getLocale() {
      return locale;
    }

    public TimeZone getTimeZone() {
      return timeZone;
    }
  }

  public static final boolean isPublicHolyday(final PublicHolidayCalendar calendar, final Date date) {
    try {
      return isPublicHoliday(PUBLIC_HOLIDAYS_CALENDARS.get(calendar), date);
    }
    catch (IndexOutOfBoundsException ioobe) {
      log.debug("Error getting isPublicHoliday() : {}", getRootCauseMessage(ioobe));
      Calendar c = Calendar.getInstance(calendar.getTimeZone(), calendar.getLocale());
      c.setTime(date);
      c.add(MONTH, -6);
      Date from = c.getTime();
      log.info("Feeding {} public holidays calendar from: {}", calendar, from);
      PUBLIC_HOLIDAYS_CALENDARS.put(calendar, feedPublicHolidays(calendar, from));
      return isPublicHoliday(PUBLIC_HOLIDAYS_CALENDARS.get(calendar), date);
    }
  }

  /**
   * @param country
   * @param locale
   * @param startDate
   * @param timezone
   * @return {@link LinkedHashMap} with K={@link DatePeriod} of the public holiday and V=Name.
   */
  public static final Map<DatePeriod, String> feedPublicHolidays(final PublicHolidayCalendar calendar, final Date startDate) {
    return feedPublicHolidays(calendar.toString(), calendar.getLocale(), startDate, calendar.getTimeZone());
  }

  /**
   * @param country
   * @param locale
   * @param startDate
   * @param timezone
   * @return {@link LinkedHashMap} with K={@link DatePeriod} of the public holiday and V=Name.
   */
  public static final Map<DatePeriod, String> feedPublicHolidays(final String country, final Locale locale, final Date startDate,
      final TimeZone timezone) {
    final String url = String.format(PUBLIC_HOLIDAYS_FEED, country, locale.getLanguage(), FormatHelper.asString(startDate, DATE_FORMAT),
        timezone.getID());
    log.debug("Google Calendar feed url: {}", url);

    final StringWriter writer = new StringWriter();
    try {
      NetHelper.wget(new URL(url), writer, null, null, true);
      Document feed = DOCUMENT_BUILDER.parse(new InputSource(new StringReader(writer.toString())));
      NodeList entries = (NodeList) XPATH.compile("/feed/entry")
          .evaluate(feed, NODESET);
      Map<DatePeriod, String> r = new LinkedHashMap<DatePeriod, String>(entries.getLength());
      Calendar c = Calendar.getInstance(timezone, locale);
      for (int i = 0; i < entries.getLength(); i++) {
        Element entry = (Element) entries.item(i);
        Date start = FormatHelper.asDate(XPATH.evaluate("when/@startTime", entry), DATE_FORMAT);
        c.setTime(FormatHelper.asDate(XPATH.evaluate("when/@endTime", entry), DATE_FORMAT));
        c.add(SECOND, -1);
        r.put(new DatePeriod(start, c.getTime()), XPATH.evaluate("title", entry));
      }
      return r;
    }
    catch (Exception e) {
      log.debug("Error getting feedPublicHolidays() : {}", getRootCauseMessage(e));
      log.error("Error getting Google Calendar feed: {} ({})", url, e.getMessage());
      return Collections.emptyMap();
    }
  }

  /**
   * @param publicHolidays see {@link #feedPublicHolidays(Country, Locale, Date, TimeZone)}
   * @param date to check
   * @return true if it's a public holiday.
   */
  public static final boolean isPublicHoliday(final Map<DatePeriod, String> publicHolidays, final Date date) {
    if (date == null)
      return false;

    if (!isIncluded(publicHolidays, date))
      throw new IndexOutOfBoundsException("Date '" + date + "' is out of public holidays range: " + publicHolidays);

    for (Map.Entry<DatePeriod, String> entry : publicHolidays.entrySet())
      if (!(date.before(entry.getKey()
          .getStartDate()) || date.after(entry.getKey()
              .getEndDate()))) {
        log.debug("'{}' is a public holiday: {}", date, entry.getValue());
        return true;
      }

    return false;
  }

  /**
   * @param publicHolidays
   * @param date
   * @return true if the date is included in the public holidays covered date period.
   */
  public static final boolean isIncluded(final Map<DatePeriod, String> publicHolidays, final Date date) {
    if (date == null)
      return true;
    if (publicHolidays == null || publicHolidays.isEmpty())
      return false;
    final List<DatePeriod> periods = new ArrayList<DatePeriod>(publicHolidays.keySet());
    return !(date.before(periods.get(0)
        .getStartDate()) || date.after(periods.get(periods.size() - 1)
            .getEndDate()));
  }
}
