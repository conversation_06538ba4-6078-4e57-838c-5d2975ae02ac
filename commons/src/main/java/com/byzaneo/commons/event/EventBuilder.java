package com.byzaneo.commons.event;

import com.byzaneo.commons.api.EventAction;
import com.byzaneo.commons.bean.Persistent;
import com.byzaneo.commons.service.EventService;
import org.slf4j.Logger;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.MethodInvoker;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.security.Principal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static java.lang.Class.forName;
import static java.lang.String.format;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.security.core.context.SecurityContextHolder.getContext;

/**
 * Event DSL builder.
 * <p/>
 * The suffix URI pattern are:
 * <ul>
 * <li>Identifier source: [event.type]/[identifier]/[event.action]</li>
 * <li>Persistent source: peristent/[entity.simpleName]/[event.action]</li>
 * <li>Default (if action, type and source are set): [event.type]/[event.source.simpleName]/[event.action]</li>
 * <li>otherwise: suffix has to be provider</li>
 * </ul>
 * Note: this builder allows to override the suffix URI above by using the {@link #topic(String)} method.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Jul 27, 2015
 * @since 5.0
 */
public final class EventBuilder {
  public static final String EVENT_TYPE_PERSISTENT = "persistent";

  private static final Logger log = getLogger(EventBuilder.class);

  // AUTHENTICATED USER
  private static MethodInvoker authentication;
  private static Class<?> userType;
  private static MethodInvoker primaryGroup;

  static {
    try {
      // authentication
      final Class<?> authType = forName("com.byzaneo.security.spring.UserDetailsService");
      authentication = new MethodInvoker();
      authentication.setStaticMethod(authType.getName()
          .concat(".")
          .concat("getAuthenticatedUser"));
      authentication.setTargetClass(authType);
      authentication.prepare();
      // primary group
      userType = forName("com.byzaneo.security.bean.User");
      primaryGroup = new MethodInvoker();
      primaryGroup.setTargetMethod("getPrimaryGroup");
      primaryGroup.setTargetClass(userType);
      primaryGroup.prepare();
    }
    catch (ClassNotFoundException | NoSuchMethodException e) {
      log.info("Byzaneo security event's authentication not available ({})", getRootCauseMessage(e));
      authentication = null;
    }
  }

  // SERVICE
  private static EventService service;

  // PROPERTIES
  /**
   * the built event
   */
  private final Event event;
  /**
   * the list of TopicFactory
   */
  List<TopicFactory> topicFactories = new ArrayList<>();

  /* -- CONSTRUCTOR -- */

  private EventBuilder(Event event) {
    super();
    this.event = event;
    this.addTopicFactory(PROVIDED_TOPIC_FACTORY)
        .addTopicFactory(IDENTIFIER_TOPIC_FACTORY)
        .addTopicFactory(PERSISTENT_TOPIC_FACTORY);
  }

  /* -- INITIATE BUILDER -- */

  /**
   * @param source of the event (must not be <code>null</code>)
   * @return the builder
   */
  public static final EventBuilder fromSource(final Object source) {
    final EventBuilder builder = new EventBuilder(new Event(source));
    if (source instanceof Persistent) {
      builder.event.sourceId = ((Persistent<?>) source).getId();
      builder.event.type = EVENT_TYPE_PERSISTENT;
    }
    builder.event.sourceType = source.getClass();
    return builder;
  }

  /**
   * @param identifier published by this event (must not be <code>null</code>)
   * @return the builder
   */
  public static final EventBuilder fromIdentifier(final Serializable identifier) {
    final EventBuilder builder = fromSource(identifier);
    builder.event.sourceId = identifier;
    return builder;
  }

  /* -- DSL -- */

  // - ORIGIN -

  public final EventBuilder origin(final Object origin) {
    this.event.origin = origin;
    return this;
  }

  // - TOPIC -

  public final EventBuilder type(final String type) {
    this.event.type = type;
    return this;
  }

  public final EventBuilder action(final EventAction action) {
    this.event.action = action;
    return this;
  }

  public final EventBuilder topic(final String topic) {
    this.event.topic = topic;
    return this;
  }

  // - POST CONSUMER -

  public final EventBuilder callback(final MethodInvoker method) {
    this.event.callback = method;
    return this;
  }

  public final EventBuilder callbackFunction(Function<Object, ?> callbackFunction) {
    this.event.callbackFunction = callbackFunction;
    return this;
  }

  // - OWNER -

  public final EventBuilder owner(final Principal principal) {
    if (principal != null) {
      this.event.owner = new EventOwner(principal);
      this.resolveOwnerPrimaryGroupName();
    }
    return this;
  }

  public final EventBuilder owner(final String identifier, final String name) {
    if (identifier != null || name != null)
      this.event.owner = new EventOwner(identifier, name);
    return this;
  }

  public final EventBuilder ownerPrimaryGroupName(final String name) {
    if (this.event.owner != null)
      this.event.<EventOwner> getOwner()
          .setPrimaryGroupName(name);
    return this;
  }

  /**
   * Allows to populate the built event with the authenticated user (principal).
   *
   * @return the builder
   */
  public final EventBuilder authenticate() {
    // resets current owner
    this.event.owner = null;

    // - principal -
    // Homemade Security
    if (authentication != null) {
      try {
        this.owner((Principal) authentication.invoke());
      }
      catch (final IllegalAccessException | InvocationTargetException e) {
        log.error("Unable to resolve event's authenticated owner: {} ({})",
            this.event, getRootCauseMessage(e));
      }
    }
    // Spring Security
    if (this.event.owner == null) {
      final SecurityContext context = getContext();
      final Authentication auth = context == null ? null : context.getAuthentication();
      if (auth != null && auth.getPrincipal() instanceof UserDetails) {
        this.owner(null, ((UserDetails) auth.getPrincipal()).getUsername());
      }
    }

    log.debug("Event's owner: {}", this.event.owner);
    return this;
  }

  // - TOPIC FACTORY -

  /**
   * @param factory
   * @return this builder
   */
  public final EventBuilder topicFactory(final TopicFactory factory) {
    this.topicFactories.clear();
    return this.addTopicFactory(factory);
  }

  /**
   * @param factory
   * @return this builder
   */
  private final EventBuilder addTopicFactory(final TopicFactory factory) {
    this.topicFactories.add(factory);
    return this;
  }

  public final EventBuilder context(final Map<String, Object> context) {
    if (context != null)
      this.event.context.putAll(context);
    return this;
  }

  /* -- TERMINATE BUILDER -- */

  // - PUBLISH -

  /**
   * Publishes the built event quietly.
   *
   * @return the published event or <code>null</code> if the publication failed.
   * @see #publish(boolean)
   */
  public final Event publish() {
    return this.publish(false);
  }

  /**
   * Publishes the built event.
   *
   * @param throwOnError if <code>true</code>, throws an exception if the publication failed.
   * @return the published event.
   * @see #build(boolean)
   */
  public final Event publish(final boolean throwOnError) {
    return publish(this.build(throwOnError), throwOnError);
  }

  /**
   * Publishes the given event quietly.
   *
   * @param event to publish
   * @return the published event.
   * @see #publish(Event)
   */
  public static final <E extends Event> E publish(final E event) {
    return publish(event, false);
  }

  /**
   * Publishes the given event.
   *
   * @param event to publish
   * @param throwOnError if <code>true</code>, throws an exception if the publication failed.
   * @return the published event.
   */
  public static final <E extends Event> E publish(final E event, final boolean throwOnError) {
    try {
      getService().publish(event);
      return event;
    }
    catch (Exception e) {
      if (throwOnError)
        throw new IllegalArgumentException("Error publishing event: " + event, e);
      log.error("Error publishing event: {} ({})", event, getRootCauseMessage(e));
      return null;
    }
  }

  // - BUILD -

  /**
   * Creates the event quietly.
   *
   * @return the built event or <code>null</code> if the event creation failed.
   * @see #build(boolean)
   */
  public final Event build() {
    return this.build(false);
  }

  /**
   * @param throwOnError if <code>true</code>, throws an exception if the event creation failed.
   * @return the created event.
   */
  public final Event build(final boolean throwOnError) { // NOSONAR : cyclomatic complexity > 10
    // SOURCE
    if (this.event.source == null) {
      // Callback
      try {
        this.event.callback();
        error(throwOnError, "Event source is required");
      }
      catch (InvocationTargetException | IllegalAccessException | ClassNotFoundException | NoSuchMethodException e) {
        error(throwOnError, "Callback invocation failed on null source");
      }
      return null;
    }

    // TOPIC
    for (TopicFactory factory : this.topicFactories) {
      factory.topic(this.event);
      if (this.event.topic != null)
        break;
    }
    if (this.event.topic == null) {
      error(throwOnError, "Topic Resolution failed with: %s", this.topicFactories);
      return null;
    }

    log.debug("Event on: {}", this.event.topic);
    return this.event;
  }

  /* -- PRIVATE -- */

  private static EventService getService() {
    if (service == null)
      service = getBean(EventService.class, EventService.SERVICE_NAME);
    return service;
  }

  private void resolveOwnerPrimaryGroupName() {
    if (userType != null && this.event.owner != null && this.event.owner.getType() != null &&
        userType.isAssignableFrom(this.event.owner.getType()) && this.event.owner.getPrincipal() != null) {
      try {
        primaryGroup.setTargetObject(this.event.owner.getPrincipal());
        this.ownerPrimaryGroupName(
            ((Principal) primaryGroup.invoke()).getName());
      }
      catch (Exception e) {
        log.debug("Unable to resolve event's owner primary group: {} ({})",
            this.event, getRootCauseMessage(e));
      }
    }
  }

  private void error(boolean thrownOnError, String message, Object... arguments) {
    if (thrownOnError)
      throw new IllegalArgumentException(format(message, arguments));
    else
      log.error(format(message, arguments));
  }

  /* -- INNER -- */

  private static final TopicFactory PROVIDED_TOPIC_FACTORY = (e) -> e.topic;
  // TODO: use prefix %s -> getPrefix
  private static final TopicFactory IDENTIFIER_TOPIC_FACTORY = (e) -> e.type != null && e.source.equals(e.sourceId) && e.action != null
      ? format("%s/%s/%s", e.type, e.sourceId, e.action)
      : null;
  private static final TopicFactory PERSISTENT_TOPIC_FACTORY = (e) -> e.type != null && e.source != null && e.action != null
      ? format("%s/%s/%s", e.type, e.source.getClass()
          .getSimpleName(), e.action)
      : null;

}
