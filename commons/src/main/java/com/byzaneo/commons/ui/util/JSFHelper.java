package com.byzaneo.commons.ui.util;

import static com.byzaneo.commons.bean.FileType.HTML;
import static com.sun.faces.application.ApplicationAssociate.getInstance;
import static java.lang.String.format;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Locale.getDefault;
import static java.util.Optional.ofNullable;
import static javax.faces.FactoryFinder.LIFECYCLE_FACTORY;
import static javax.faces.application.ProjectStage.Development;
import static javax.faces.application.ProjectStage.Production;
import static javax.faces.application.ProjectStage.SystemTest;
import static javax.faces.application.ProjectStage.UnitTest;
import static javax.faces.lifecycle.LifecycleFactory.DEFAULT_LIFECYCLE;
import static org.apache.commons.collections4.IteratorUtils.toList;
import static org.apache.commons.collections4.MapUtils.isNotEmpty;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.io.FilenameUtils.getExtension;
import static org.apache.commons.io.FilenameUtils.removeExtension;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.defaultString;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.substringAfterLast;
import static org.apache.commons.lang3.StringUtils.substringBeforeLast;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.springframework.web.jsf.FacesContextUtils.getWebApplicationContext;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;

import java.io.*;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.*;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.*;

import javax.el.*;
import javax.faces.FactoryFinder;
import javax.faces.application.*;
import javax.faces.component.*;
import javax.faces.component.html.*;
import javax.faces.component.visit.*;
import javax.faces.context.*;
import javax.faces.event.ActionEvent;
import javax.faces.lifecycle.*;
import javax.faces.model.SelectItem;
import javax.faces.view.Location;
import javax.faces.view.facelets.*;
import javax.servlet.http.*;

import org.apache.commons.lang3.*;
import org.primefaces.component.datatable.DataTable;
import org.slf4j.*;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.jsf.FacesContextUtils;

import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.util.SpringContextHelper;
import com.google.common.net.HttpHeaders;
import com.sun.faces.application.ApplicationAssociate;
import com.sun.faces.component.visit.FullVisitContext;
import com.sun.faces.facelets.tag.TagAttributeImpl;
import com.sun.faces.facelets.tag.jsf.CompositeComponentTagHandler;
import com.sun.faces.mgbean.BeanBuilder;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo.com
 */
public final class JSFHelper {

  private static final Logger log = LoggerFactory.getLogger(JSFHelper.class);

  /** Composite Component extension */
  public static final String COMPOSITE_EXTENSION = ".xhtml";

  private static final String CONTENT_DISPOSITION_HEADER = "%s;filename=\"%2$s\"; filename*=UTF-8''%2$s";

  private static final String DEFAULT_MIME_TYPE = "application/octet-stream";

  private static final int DEFAULT_SENDFILE_BUFFER_SIZE = 10240;

  private static final String FIREFOX_VERSION_REGEX = "(?<=Firefox)(\\S+)";

  private static final String CHROME_VERSION_REGEX = "(?<=Chrome)(\\S+)";

  private static final String IE_VERSION_REGEX = "(?<=rv)(\\S*\\d)";

  private static final String SAFARI_VERSION_REGEX = "(?<=Safari)(\\S+)";

  private static final String OPR_VERSION_REGEX = "(?<=OPR)(\\S+)";

  private static final String EDGE_VERSION_REGEX = "(?<=Edge)(\\S+)";

  /**
   * <p>
   * The attribute name under which saved data will be stored on the view root component.
   * </p>
   */
  private static final String DATA_KEY = "com.byzaneo.commons.ui.DATA";
  
  public static final String HTTP_SCHEME = "http";

  public static final String HTTPS_SCHEME = "https";

  /**
   * {@link SelectItem} {@link Comparator} on label then value (if label is null).
   * 
   * @since COM-79
   */
  public static final Comparator<SelectItem> SELECT_ITEM_COMPARATOR = new Comparator<SelectItem>() {
    @Override
    public int compare(SelectItem o1, SelectItem o2) {
      if (o1.getLabel() != null)
        return o1.getLabel()
            .compareToIgnoreCase(o2.getLabel());
      return o1.getValue() != null ? o1.getValue()
          .toString()
          .compareToIgnoreCase(o2.getValue()
              .toString())
          : -1;
    }
  };

  /*
   * -- CONVENIENCE ACCESSORS --
   */

  /**
   * <p>
   * Return a <code>Map</code> of the application scope attributes for this web application.
   * </p>
   */
  public static Map<String, Object> getApplicationMap() {
    return getExternalContext().getApplicationMap();
  }

  /**
   * <p>
   * Return the <code>ExternalContext</code> instance for the current request.
   * </p>
   */
  public static final ExternalContext getExternalContext() {
    return getExternalContextOptional().orElse(null);
  }

  private static Optional<ExternalContext> getExternalContextOptional() {
    return ofNullable(getFacesContext())
        .map(FacesContext::getExternalContext);
  }

  /**
   * <p>
   * Return the <code>FacesContext</code> instance for the current request.
   * </p>
   */
  public static final FacesContext getFacesContext() {
    return FacesContext.getCurrentInstance();
  }

  /**
   * <p>
   * Return the configured <code>Lifecycle</code> instance for the current web application.
   * </p>
   */
  public static final Lifecycle getLifecycle() {
    String lifecycleId = getExternalContext().getInitParameter("javax.faces.LIFECYCLE_ID");
    if (lifecycleId == null || lifecycleId.length() == 0) {
      lifecycleId = DEFAULT_LIFECYCLE;
    }
    LifecycleFactory lifecycleFactory = (LifecycleFactory) FactoryFinder.getFactory(LIFECYCLE_FACTORY);
    return lifecycleFactory.getLifecycle(lifecycleId);
  }

  /**
   * @return the {@link HttpServletResponse} for the current request.
   * @since 2.5 COM-93
   */
  public static final HttpServletResponse getResponse() {
    return (HttpServletResponse) getExternalContext().getResponse();
  }

  /**
   * @return the {@link HttpServletRequest} for the current request.
   * @since 2.5 COM-93
   */
  public static final HttpServletRequest getRequest() {
    return (HttpServletRequest) getExternalContext().getRequest();
  }

  /**
   * <p>
   * Return a <code>Map</code> of the request scope attributes for the current request.
   * </p>
   */
  public static final Map<String, Object> getRequestMap() {
    return getExternalContext().getRequestMap();
  }

  /**
   * @return the request parameters.
   */
  public static final Map<String, String> getRequestParameterMap() {
    return getExternalContext().getRequestParameterMap();
  }

  /**
   * @return values (of type String) of the given request parameters names included in the current request.
   */
  public static final String getRequestParameter(String key) {
    return getExternalContext().getRequestParameterMap()
        .get(key);
  }

  /**
   * @return values (of type String) of the given request parameters names included in the current request or defaultValue if null or empty.
   */
  public static final String getRequestParameter(String key, String defaultValue) {
    String r = null;
    return isEmpty(r = getRequestParameter(key)) ? defaultValue : r; // NOSONAR : sub expressions
  }

  /**
   * <p>
   * Return a <code>Map</code> of the session scope attributes for the current user's session. Note that calling this method will cause a
   * session to be created if there is not already one associated with this request.
   * </p>
   */
  public static final Map<String, Object> getSessionMap() {
    return getExternalContext().getSessionMap();
  }

  /**
   * @param key
   * @param defaultValue
   * @return the object bound with the specified name in this session, or <code>defaultValue</code> if no object is bound under the name.
   */
  @SuppressWarnings("unchecked")
  public static final <T> T getSessionAttribute(String key, T defaultValue) {
    final HttpSession session = (HttpSession) getExternalContextOptional().map(ex -> ex.getSession(false))
        .orElse(null);
    Object o;
    return session == null || (o = session.getAttribute(key)) == null ? defaultValue : (T) o;
  }

  /**
   * @return the list of managed beans by name. If any problem occurs, an empty map will be returned.
   * @since 3.1 COM-117
   */
  public static final Map<String, BeanBuilder> getManagedBeans() {
    try {
      return getInstance(getExternalContext()).getBeanManager()
          .getRegisteredBeans();
    }
    catch (Exception e) {
      log.debug("Error getting Map() : {}", getRootCauseMessage(e));
      log.warn("Impossible to get the list of JSF registered beans: {}", getRootCauseMessage(e));
      return emptyMap();
    }
  }

  /*
   * -- EVENT MANIPULATION --
   */

  /**
   * @param event
   * @param index
   * @return the value of the {@link UIParameter} children at the index position
   * @since 2.5
   */
  @SuppressWarnings("unchecked")
  public static final <T> T getEventParameterValue(final ActionEvent event, final int index) {
    return (T) ((UIParameter) event.getComponent()
        .getChildren()
        .get(index)).getValue();
  }

  /**
   * @param event
   * @return the value of the first {@link UIParameter} children
   * @since 2.5
   */
  public static final <T> T getEventParameterValue(final ActionEvent event) {
    return getEventParameterValue(event, 0);
  }

  /*
   * -- BEAN MANIPULATION --
   */

  /**
   * <p>
   * Return any attribute stored in request scope, session scope, or application scope under the specified name. If no such attribute is
   * found, and if this name is the registered name of a managed bean, cause a new instance of this managed bean to be created (and stored
   * in an appropriate scope, if necessary) and returned. If no attribute exists, and no managed bean was created, return <code>null</code>.
   * </p>
   *
   * @param name Name of the attribute to be retrieved
   */
  @SuppressWarnings("unchecked")
  public static final <P extends Serializable> P getManagedBean(Class<P> clazz, String name) {
    return (P) createValueExpression(getFacesContext(), name, clazz)
        .getValue(getFacesContext().getELContext());
  }

  /**
   * @param name Name of the spring bean
   * @return Spring bean or an exception if not found.
   * @see #getSpringBean(Class, String, boolean)
   */
  public static final <P extends Object> P getSpringBean(Class<P> clazz, String name) {
    return getSpringBean(clazz, name, true);
  }

  /**
   * @param clazz
   * @return Spring bean or an exception if not found
   */
  public static final <P extends Object> P getSpringBean(Class<P> clazz) {
    return getSpringContext().getBean(clazz);
  }

  /**
   * @param name Name of the spring bean
   * @param required if <code>true</code> an exception is thrown if the requested bean is not found. Otherwise, returns <code>null</code>.
   * @return Spring bean.
   * @see #getSpringContext()
   */
  public static final <P extends Object> P getSpringBean(Class<P> clazz, String name, boolean required) {
    try {
      return getSpringContext().getBean(name, clazz);
    }
    catch (NullPointerException | NoSuchBeanDefinitionException e) {
      final String msg = format("Spring bean not found: name=%s, class=%s (%s)",
          name, clazz, getRootCauseMessage(e));
      if (required) {
        log.error(msg);
        throw e;
      }
      log.debug(msg);
      return null; // NOSONAR : null is expected
    }
  }

  /**
   * @return the spring application context associated to the current Faces context.
   * @see FacesContextUtils#getWebApplicationContext(FacesContext)
   */
  public static WebApplicationContext getSpringContext() {
    return getWebApplicationContext(getFacesContext());
  }

  /**
   * <p>
   * Replace the value of any attribute stored in request scope, session scope, or application scope under the specified name. If there is
   * no such attribute, create a new request scope attribute under this name, and store the value there.
   * </p>
   */
  public static final void setBean(String name, Object value) {
    setValue("#{" + name + "}", value); // NOI18N
  }

  /*
   * -- SAVE/RESTORE DATA METHODS --
   */

  /**
   * <p>
   * Return the data object stored (typically when the component tree was previously rendered) under the specified key, if any; otherwise,
   * return <code>null</code>.
   * </p>
   * <p>
   * <strong>IMPLEMENTATION NOTE:</strong> Data objects will become available only after the <em>Restore View</em> phase of the request
   * processing lifecycle has been completed. A common place to reinitialize state information, then, would be in the
   * <code>preprocess()</code> event handler of a page bean.
   * </p>
   *
   * @param key Key under which to retrieve the requested data
   */
  public static final Object retrieveData(String key) {
    FacesContext context = getFacesContext();
    if (context == null) {
      return null;
    }
    UIViewRoot view = context.getViewRoot();
    if (view == null) {
      return null;
    }
    Map<?, ?> map = (Map<?, ?>) view.getAttributes()
        .get(DATA_KEY);
    if (map != null) {
      return map.get(key);
    }
    else {
      return null;
    }
  }

  /**
   * <p>
   * Save the specified data object (which <strong>MUST</strong> be <code>Serializable</code>) under the specified key, such that it can be
   * retrieved (via <code>getData()</code>) on a s subsequent request immediately after the component tree has been restored.
   * </p>
   * <p>
   * <strong>IMPLEMENTATION NOTE:</strong> In order to successfully save data objects, this method must be called before the <em>Render
   * Response</em> phase of the request processing lifecycle is executed. A common scenario is to save state information in the
   * <code>prerender()</code> event handler of a page bean.
   * </p>
   *
   * @param key Key under which to store the requested data
   * @param data Data object to be stored
   */
  @SuppressWarnings("unchecked")
  public static final void saveData(String key, Object data) {

    Map<String, Object> map = (Map<String, Object>) getFacesContext().getViewRoot()
        .getAttributes()
        .get(DATA_KEY);
    if (map == null) {
      map = new HashMap<String, Object>();
      getFacesContext().getViewRoot()
          .getAttributes()
          .put(DATA_KEY, map);
    }
    map.put(key, data);

  }

  /*
   * -- VALUE MANIPULATION --
   */

  /**
   * <p>
   * Evaluate the specified value binding expression, and return the value that it points at.
   * </p>
   *
   * @param expr Value binding expression (excluding delimiters)
   */
  @SuppressWarnings("unchecked")
  public static final <T> T getValue(String expr) {
    final FacesContext context = getFacesContext();
    return (T) context.getELContext()
        .getELResolver()
        .getValue(context.getELContext(), null, expr);
  }

  /**
   * <p>
   * Evaluate the specified value binding expression, and update the value that it points at.
   * </p>
   *
   * @param expr Value binding expression (excluding delimiters) that must point at a writeable property
   * @param value New value for the property pointed at by <code>expr</code>
   */
  public static final void setValue(String expr, Object value) {
    ELContext elcontext = getFacesContext().getELContext();
    getApplication().getExpressionFactory()
        .createValueExpression(elcontext, expr, Object.class)
        .setValue(elcontext, value);
  }

  /*
   * -- COMPONENT MANIPULATION --
   */

  /**
   * <p>
   * Erase previously submitted values for all input components on this page. This method <strong>MUST</strong> be called if you have bound
   * input components to database columns, and then arbitrarily navigate the underlying <code>RowSet</code> to a different row in an event
   * handler method.
   * </p>
   */
  public static final void erase() {
    erase(getFacesContext().getViewRoot());
  }

  /**
   * <p>
   * Private helper method for <code>erase()</code> that recursively descends the component tree and performs the required processing.
   * </p>
   *
   * @param component The component to be erased
   */
  public static final void erase(UIComponent component) {

    // Erase the component itself (if needed)
    if (component instanceof EditableValueHolder) {
      ((EditableValueHolder) component).setSubmittedValue(null);
    }
    // Process the facets and children of this component
    Iterator<UIComponent> kids = component.getFacetsAndChildren();
    while (kids.hasNext()) {
      erase(kids.next());
    }

  }

  /**
   * Helper method to set value expression property.
   *
   * @param component The UIComponent to set a value expression for.
   * @param name The name of the value expression property.
   * @param expression The expresion for the value expression.
   */
  public static final void setValueExpression(UIComponent component, String name, String expression) {
    if (expression == null) {
      return;
    }
    FacesContext context = getFacesContext();
    component.setValueExpression(name, createValueExpression(context,
        expression, Object.class));
  }

  /**
   * Helper method to set a method expression property. Create a method expression that returns String and has no input paramaters.
   *
   * @param component The UIComponent to set a value binding for.
   * @param name The name of the method expression property
   * @param expression The expression to create.
   */
  public static void setMethodExpression(UIComponent component, String name, String expression) {
    setMethodExpression(component, name, expression, Object.class, new Class[0]);
  }

  /**
   * Helper method to set a method expression property.
   *
   * @param component The UIComponent to set a value binding for.
   * @param name The name of the method expression property
   * @param expression The expression to create.
   */
  public static void setMethodExpression(UIComponent component, String name,
      String expression, Class<?> out, Class<?>[] in) {
    if (expression == null) {
      return;
    }
    FacesContext context = getFacesContext();
    component.getAttributes()
        .put(name,
            createMethodExpression(context, expression, out, in));
  }

  public static MethodExpression createMethodExpression(FacesContext context,
      String expr, Class<?> out, Class<?>[] in) {

    return context.getApplication()
        .getExpressionFactory()
        .createMethodExpression(context.getELContext(), expr, out, in);
  }

  /*
   * -- VALUE EXPRESSION --
   */

  /**
   * @see #createMethodExpression(FacesContext, String, Class, Class[])
   */
  public static final ValueExpression createValueExpression(final String expr) {
    return createValueExpression(getFacesContext(), expr, Object.class);
  }

  /**
   * @see #createMethodExpression(FacesContext, String, Class, Class[])
   */
  public static final ValueExpression createValueExpression(final FacesContext context, final String expr) {
    return createValueExpression(context, expr, Object.class);
  }

  /**
   * @param context {@link FacesContext}.
   * @param expr #{...} expression language.
   * @param value Class type of the return type.
   * @return {@link ValueExpression}
   */
  public static final ValueExpression createValueExpression(final FacesContext context, final String expr, final Class<?> value) {
    if (isBlank(expr))
      return null;
    String el = expr;
    if (!expr.startsWith("#{")) {
      el = new StringBuilder("#{").append(expr)
          .append("}")
          .toString();
    }
    return context.getApplication()
        .getExpressionFactory()
        .createValueExpression(context.getELContext(), el, value);
  }

  /**
   * Accepts a string specifying an bean reference and returns the instance of a user's bean that is registered in the application
   * configuration file Example: String userLogin = (String){@link #evaluateExpressionLanguage(String)}("myHandler.userLogin");
   *
   * @param el expresion language expression
   * @return Object instance depending of the current context
   */
  @SuppressWarnings("unchecked")
  public static final <T> T evaluateExpressionLanguage(final String el) {
    final FacesContext context = getFacesContext();
    final ValueExpression ve = createValueExpression(context, el);
    return (T) (ve == null ? null : ve.getValue(context.getELContext()));
  }

  /**
   * <p>
   * Help obtain the {@link Application}'s default {@link Locale}. If not resolved, the {@link Locale#getDefault()} is returned.
   * </p>
   */
  public static final Locale getDefaultLocale() {
    Locale locale = null;

    // application default locale
    try {
      locale = getApplication().getDefaultLocale();
    }
    catch (Exception ignored) {
      log.debug("Error getting getDefaultLocale() : {}", getRootCauseMessage(ignored));
    }

    // Java default locale if not resolved
    return (locale == null) ? getDefault() : locale;
  }

  /**
   * <p>
   * Help obtain the {@link FacesContext}'s {@link Locale}.
   * </p>
   * 
   * @see #getDefaultLocale()
   */
  public static final Locale getLocale() {
    Locale locale = null;

    // current view's locale
    try {

      locale = getFacesContext().getViewRoot()
          .getLocale();
    }
    catch (Exception ignored) {
      log.debug("Error getting getFacesContext() : {}", getRootCauseMessage(ignored));
    }

    // Faces application or Java default locale if not resolved
    return (locale == null) ? getDefaultLocale() : locale;
  }

  /**
   * @param locale to set in {@link FacesContext}.
   * @since 2.5 SEC-64
   */
  public static final void setLocale(Locale locale) {
    try {
      getFacesContext().getViewRoot()
          .setLocale(locale);
    }
    catch (Exception ignored) {
      log.debug("Error getting setLocale() : {}", getRootCauseMessage(ignored));
    }
  }

  /**
   * @return the supported locales defined in the faces config.
   * @since 3.0 COM-108
   */
  public static final List<Locale> getSupportedLocales() {
    try {
      return toList(getApplication().getSupportedLocales());
    }
    catch (Exception ignored) {
      log.debug("Error getting List() : {}", getRootCauseMessage(ignored));
      return emptyList();
    }
  }

  /**
   * @return the JSF Application definition for the current context
   */
  public static final Application getApplication() {
    FacesContext context = getFacesContext();
    if (context != null) {
      return getFacesContext().getApplication();
    }

    try {
      ApplicationFactory afactory = (ApplicationFactory) FactoryFinder
          .getFactory("javax.faces.application.ApplicationFactory");
      return afactory.getApplication();
    }
    catch (Exception e) {
      log.debug("Error getting getApplication() : {}", getRootCauseMessage(e));
      log.warn("JSF Application context can't be retreives: " + e.getLocalizedMessage());
    }

    return null;
  }

  /**
   * @return the JSF {@link ResourceHandler} associated to the current {@link Application}
   */
  public static final ResourceHandler getResourceHandler() {
    final Application app = getApplication();
    return app == null ? null : app.getResourceHandler();
  }

  /**
   * attribute name should be unique and should not overwrite any default component attributes, like "id", "name", "value", "binding",
   * "rendered", etc.
   *
   * @param event the ActionEvent of the ActionListener associated with the UI component
   * @param name the name of the parameter
   * @return the value of the name parameter of the parent UI component.
   */
  public static Object getActionAttribute(ActionEvent event, String name) {
    return event.getComponent()
        .getAttributes()
        .get(name);
  }

  /*
   * -- SELECT ITEMS --
   */

  public static final ArrayList<SelectItem> toSelectItems(final Collection<String> strings) {
    return toSelectItems(strings,
        new SelectItemConverter<String>() {
          @Override
          public SelectItem convert(String object) {
            return new SelectItem(object);
          }
        },
        false);
  }

  public static final <T> ArrayList<SelectItem> toSelectItems(final Collection<T> objects, final SelectItemConverter<T> converter,
      final boolean sort) {
    if (objects == null || objects.isEmpty())
      return new ArrayList<SelectItem>(0);

    ArrayList<SelectItem> items = new ArrayList<SelectItem>(objects.size());
    for (T object : objects) {
      items.add(converter.convert(object));
    }

    if (sort)
      Collections.sort(items, SELECT_ITEM_COMPARATOR);

    return items;
  }

  /**
   * Converts an Object to a {@link SelectItem}
   * 
   * @param <T> converted object type.
   */
  public interface SelectItemConverter<T> {
    SelectItem convert(T object);
  }

  /*
   * -- COMPOSITE COMPONENT --
   */

  /**
   * <p>
   * Appends to the given parent a freshly created Composite Component after clearing all its existing children.
   * </p>
   * <p>
   * The {@link Resource} of the component is resolved using the given library and adding to the name the {@link #COMPOSITE_EXTENSION}
   * ({@value #COMPOSITE_EXTENSION}).
   * </p>
   * 
   * @param parent container component
   * @param library composite component's library
   * @param name composite component's name
   * @param id identifier of the created composite component
   * @param attributes of the CC
   * @return <code>true</code> if the composite component has been successfully created
   * @see #createCompositeComponent(UIComponent, String, String, String, String, boolean, String, Attribute...)
   */
  public static final boolean createCompositeComponent(
      final UIComponent parent,
      final String library, final String name,
      final String id, final Attribute... attributes) {
    return createCompositeComponent(parent, library, name, null, null, false, id, attributes) != null;
  }

  /**
   * <p>
   * Appends to the given parent a freshly created Composite Component after clearing all its existing children.
   * </p>
   * <p>
   * The {@link Resource} of the component is resolved using the given library and adding to the name the {@link #COMPOSITE_EXTENSION}
   * ({@value #COMPOSITE_EXTENSION}).
   * </p>
   * 
   * @param parent container component
   * @param library composite component's library
   * @param name composite component's name
   * @param defaultLibrary the library name used when recursion reached the root library path
   * @param defaultName if set, the component view resource will be resolved recursively using the given default name as resource view name
   *          or root view name if recursionOnLibraryName is set to <code>true</code>. The resource name is suffixed by the extension
   *          {@link #COMPOSITE_EXTENSION} ({@value #COMPOSITE_EXTENSION}).
   * @param recursionOnLibraryName use the library name as resource name if resource search recursion is enabled
   * @param id identifier of the created composite component
   * @param attributes of the CC
   * @return the composite component freshly created (since COM-145)
   * @see #resolveResource(String, String, boolean, boolean, String, String, String)
   */
  public static final UIComponent createCompositeComponent( // NOSONAR : cyclomatic complexity > 10
      final UIComponent parent,
      final String library, final String name,
      final String defaultLibrary, final String defaultName,
      boolean recursionOnLibraryName, final String id,
      final Attribute... attributes) {

    // sanity checks
    if (parent == null || library == null || isBlank(name))
      return null;

    // Create a resource
    final Resource resource = resolveResource(
        // resource library and name
        library, name + COMPOSITE_EXTENSION,
        // recursive search if a default name or library is provided
        // (when the root library is reached)
        isNotBlank(defaultName) || isNotBlank(defaultLibrary),
        // resolve recursed resource name from the library name
        // (needs a defaultName for root library search)
        recursionOnLibraryName,
        // default library
        defaultLibrary,
        // default name
        isNotBlank(defaultName) ? defaultName.concat(COMPOSITE_EXTENSION) : null,
        // content type
        HTML.getDefaultMime());

    return createCompositeComponent(parent, resource, id, attributes);
  }

  /**
   * <p>
   * Appends to the given parent a freshly created Composite Component after clearing all its existing children.
   * </p>
   * <p>
   * The {@link Resource} of the component is resolved using the given library and adding to the name the {@link #COMPOSITE_EXTENSION}
   * ({@value #COMPOSITE_EXTENSION}).
   * </p>
   * 
   * @param parent container component
   * @param resource component's resource (view)
   * @param id identifier of the created composite component
   * @param attributes of the CC
   * @return the composite component freshly created (since COM-145)
   */
  public static final UIComponent createCompositeComponent( // NOSONAR : too many parameters
      final UIComponent parent,
      final Resource resource,
      final String id,
      final Attribute... attributes) {

    // sanity checks
    if (parent == null || resource == null)
      return null;

    // gets faces context
    final FacesContext context = getFacesContext();
    if (context == null)
      return null;

    // attributes
    // splits expressions and objects attributes:
    // - expression attributes can be passed to the createComponent
    // method as they are String value
    // - object attributes are added post component creation
    final Map<String, Object> attrexps, attrobjects;
    if (isNotEmpty(attributes)) {
      attrexps = new HashMap<>(attributes.length);
      attrobjects = new HashMap<>(attributes.length);
      for (Attribute attribute : attributes) {
        if (attribute.getObject() != null)
          attrobjects.put(attribute.getName(), attribute.getObject());
        else if (isNotBlank(attribute.getExpression()))
          attrexps.put(attribute.getName(), attribute.getExpression());
      }
    }
    else attrexps = attrobjects = null;

    // creates composite component
    boolean pathResourceName = resource.getResourceName()
        .contains("/");
    final UIComponent composite = context.getApplication()
        .getViewHandler()
        .getViewDeclarationLanguage(context, context.getViewRoot()
            .getViewId())
        .createComponent(
            context,
            "http://xmlns.jcp.org/jsf/composite/".concat(resource.getLibraryName())
                .concat(pathResourceName ? "/".concat(substringBeforeLast(resource.getResourceName(), "/")) : ""),
            removeExtension(pathResourceName ? substringAfterLast(resource.getResourceName(), "/") : resource.getResourceName()),
            attrexps);
    if (isNotEmpty(attrobjects))
      composite.getAttributes()
          .putAll(attrobjects);
    composite.setId(id);

    // adds composite to parent
    parent.getChildren()
        .clear();
    parent.getChildren()
        .add(composite);

    return composite;

    /*
     * // Create a "dummy" tag configuration final ComponentConfig tagConfig = new CompositeConfig(
     * "http://xmlns.jcp.org/jsf/composite/".concat(resource.getLibraryName()), resource.getResourceName(), resource.getResourceName(),
     * COMPONENT_TYPE, null, id + "_" + RandomUtils.nextInt()); // id); //, toTagAttributes(id, attributes)); adds attributes as tag
     * attributes
     * 
     * // Create a tag handler for the resource final CompositeComponentHandler tagHandler = new CompositeComponentHandler(resource,
     * tagConfig, attributes)
     * 
     * // Appends the brand new composite // component to the parent try { try { final FaceletContext faceletContext =
     * (FaceletContext)context.getAttributes().get(FACELET_CONTEXT_KEY); parent.getChildren().clear(); tagHandler.apply(faceletContext,
     * parent); // XXX to test //org.primefaces.util.ComponentUtils.processUpdatesOfFacetsAndChilds(parent, context); } catch (IOException
     * ioe) { log.error("Error creating composite components", ioe); throw new FacesException(ioe); }
     * 
     * return tagHandler.getComponent();
     */
  }

  /*
   * -- RESOURCES --
   */

  /**
   * <b>Example:</b>
   * <p>
   * Recursive with library name recursion option and "task" as default name
   * </p>
   * <ol>
   * <li>seeks resource=copy.xhtml, from library=process/business/ant/copy/1_0_0
   * <li>seeks resource=1_0_0.xhtml, from library=process/business/ant/copy
   * <li>seeks resource=copy.xhtml, from library=process/business/ant
   * <li>seeks resource=ant.xhtml, from library=process/business
   * <li>seeks resource=business.xhtml, from library=process
   * <li>seeks resource=task.xhtml, from the given default library
   * </ol>
   * TODO more examples
   *
   * @param library component's library (required, may be empty)
   * @param name name of the resource (required)
   * @param recursive if true, search the resource by exception against the library path
   * @param recursionOnLibraryName if <code>true</code>, use the library name as resource name during the recursion
   * @param defaultLibrary the resource library to use when library is <code>null</code> or empty (usually when we reached the root library
   *          during recursion)
   * @param defaultName the resource name which will be used when the recursive search is activated. It will be the seek name searched in
   *          the parent library paths. If not specified and recursive is <code>true</code>, the name parameter will be used as resource
   *          name.
   * @param contentType the mime content that this <code>Resource</code> instance will return from {@link Resource#getContentType}. If the
   *          value is <code>null</code>, The content-type of the resource is derived by passing the <em>resourceName</em> to
   *          {@link javax.faces.context.ExternalContext#getMimeType}
   *          </p>
   * @return the resource (or its resolution) denoted by the given name (<code>null</code> if not found)
   * @see ResourceHandler#createResource(String, String, String)
   * @see ResourceHandler#createViewResource(FacesContext, String)
   * @since 3.1
   */
  public final static Resource resolveResource( // NOSONAR : cyclomatic complexity > 10
      final String library, final String name,
      final boolean recursive, final boolean recursionOnLibraryName,
      final String defaultLibrary, final String defaultName, final String contentType) {
    if (library == null || isBlank(name))
      return null;

    // looking for the resource
    final ResourceHandler resourceHandler = getResourceHandler();
    if (resourceHandler == null) {
      log.error(
          "Faces ResourceHandler not found (are you outside a JSF context?). Resource resolution impossible for: {}/{} (default: {}/{})",
          library, name, defaultLibrary, defaultName);
      return null;
    }
    final Resource resource = resourceHandler.createResource(name, library, contentType);
    if (resource != null || !recursive) {
      log.debug("Resolved resource: {}", resource);
      return resource;
    }

    // - recursion -
    final String libraryName = getBaseName(library);
    final int slashidx = library.lastIndexOf("/");
    final boolean rootLibrary = slashidx == -1;
    if (rootLibrary && isBlank(defaultLibrary)) {
      log.warn("Resource root library reached by recursion and no default library is provided for library={} and name={}", library, name);
      return null;
    }
    final String parentLibrary = !rootLibrary ? library.substring(0, slashidx) : defaultLibrary;

    // resolves resource name
    final String rname;
    // recursion on library name and not
    // yet to the root library -> libraryName.ext
    if (recursionOnLibraryName && !rootLibrary) {
      final String ext = getExtension(name);
      rname = libraryName.concat(isBlank(ext) ? "" : ".".concat(ext));
    }
    // root library reached -> defaultName (or
    // name if defaultName is null)
    else if (rootLibrary)
      rname = defaultString(defaultName, name);
    // not root library and no recursion on
    // library name -> name
    else
      rname = name;

    return resolveResource(parentLibrary, rname,
        !rootLibrary, recursionOnLibraryName,
        defaultLibrary, defaultName, contentType);
  }

  /**
   * @param library component's library
   * @param name name of the resource
   * @param recursive if true, search the resource by exception against the library path
   * @param defaultLibrary the library used as root library during recursion
   * @return the stream of the requested resource (<code>null</code> if not found)
   * @see #resolveResource(String, String, boolean, boolean, String, String, String)
   * @since 3.1
   */
  public final static InputStream getResourceStream(final String library, final String name,
      final boolean recursive, final String defaultLibrary) {
    final Resource resource = resolveResource(library, name, recursive, false, defaultLibrary, null, null);
    try {
      return resource == null ? null : resource.getInputStream();
    }
    catch (IOException e) {
      log.error(format("Error resolving resource: %s/%s", library, name), e);
    }
    return null;
  }

  /**
   * @param library component's library
   * @param name name of the resource
   * @param recursive if true, search the resource by exception against the library path
   * @param defaultLibrary the library used as root library during recursion
   * @return the stream of the requested resource (<code>null</code> if not found)
   * @see #resolveResource(String, String, boolean, boolean, String, String, String)
   * @since 3.1
   */
  public final static String getResourceRequestPath(final String library, final String name,
      final boolean recursive, final String defaultLibrary) {
    final Resource resource = resolveResource(library, name, recursive, false, defaultLibrary, null, null);
    return resource != null ? resource.getRequestPath() : null;
  }

  /*
   * -- STAGES --
   */

  public static final boolean isStageDevelopment() {
    return getFacesContext().isProjectStage(Development);
  }

  public static final boolean isStageUnitTest() {
    return getFacesContext().isProjectStage(UnitTest);
  }

  public static final boolean isStageSystemTest() {
    return getFacesContext().isProjectStage(SystemTest);
  }

  public static final boolean isStageProduction() {
    return getFacesContext().isProjectStage(Production);
  }

  /*
   * -- ATTRIBUTES --
   */

  public static final TagAttribute[] toTagAttributes(final String id, final Attribute... attributes) { // NOSONAR : cyclomatic complexity >
                                                                                                       // 10
    // no attributes
    if (isBlank(id) && isEmpty(attributes))
      return new TagAttribute[0];
    // only id attribute
    if (isEmpty(attributes) || (attributes.length == 1 && attributes[0] == null))
      return new TagAttribute[] { new TagAttributeImpl(new Location("", -1, -1), "", "id", "id", id) };

    final List<TagAttribute> r = new ArrayList<TagAttribute>(attributes.length + 1);
    boolean idFound = false;
    for (Attribute attr : attributes) {
      if (attr != null && !attr.isNull() && String.class.equals(attr.getType())) {
        r.add(attr.toTagAttribute());
        if (!idFound) idFound = "id".equals(attr.getName());
      }
    }
    if (!idFound && isNotBlank(id))
      r.add(new TagAttributeImpl(new Location("", -1, -1), "", "id", "id", id));

    return r.toArray(new TagAttribute[r.size()]);
  }

  /**
   * Getting the browse from {@code User-Agent} is unreliable so expect it to not work in the future
   * 
   * @return the browser of {@link JSFHelper#getExternalContext() external context} or value of {@code User-Agent} if no browser if found
   * @see <a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Browser_detection_using_the_user_agent"> DEV-MOZILLA</a>
   */
  public static String getUserBrowser() {
    String header = ofNullable(getExternalContext()).map(ExternalContext::getRequestHeaderMap)
        .map(x -> x.get("User-Agent"))
        .orElse(EMPTY);
    if (header.equals(EMPTY)) {
      return header;
    }
    return getUserBrowserFromHeader(header);
  }

  /**
   * @param header - {@code User-Agent}
   * @return browser + version
   */
  protected static String getUserBrowserFromHeader(String header) {
    if (isBlank(header)) {
      return EMPTY;
    }
    if (header.contains("OPR") || header.contains("Opera")) {
      return "Opera".concat(matches(OPR_VERSION_REGEX, header));
    }
    else if (header.contains("Edge")) {
      return "Edge".concat(matches(EDGE_VERSION_REGEX, header));
    }
    else if (header.contains("Safari") && !header.contains("Chrome")) {
      return "Safari".concat(matches(SAFARI_VERSION_REGEX, header));
    }
    else if (header.contains("Firefox") && !header.contains("Seamonkey")) {
      return "Firefox".concat(matches(FIREFOX_VERSION_REGEX, header));
    }
    else if (header.contains("Chrome") && !header.contains("Chromium")) {
      return "Chrome".concat(matches(CHROME_VERSION_REGEX, header));
    }
    else if (header.contains("; MSIE") || header.contains("Trident/7.0;")) {
      return "Internet Explorer".concat(matches(IE_VERSION_REGEX, header))
          .replace(":", "/");
    }
    else {
      return header;
    }
  }

  private static String matches(String regex, String string) {
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(string);
    if (matcher.find()) {
      return matcher.group();
    }
    return EMPTY;
  }

  /**
   * Get {@link HttpSession#getId() sessionId} form {@link JSFHelper#getExternalContext() externalContext}
   * 
   * @return {@link HttpSession#getId() sessionId}
   */
  public static String getSessionId() {
    return ofNullable(getExternalContext()).map(ExternalContext::getRequest)
        .filter(HttpServletRequest.class::isInstance)
        .map(HttpServletRequest.class::cast)
        .map(HttpServletRequest::getSession)
        .map(HttpSession::getId)
        .orElse("");
  }

  /**
   * Composite Component attribute definition
   */
  public static class Attribute {
    private final String name;

    private String expression;

    private Object object;

    private final Class<?> type;

    public Attribute(String name, Object object, Class<?> type) {
      this.name = name;
      this.object = object;
      this.type = type;
    }

    public Attribute(String name, String expression, Class<?> type) {
      this.name = name;
      this.expression = expression;
      this.type = type;
    }

    public String getName() {
      return name;
    }

    public String getExpression() {
      return expression;
    }

    public Object getObject() {
      return object;
    }

    public Class<?> getType() {
      return type;
    }

    public boolean isNull() {
      return expression == null && object == null;
    }

    public String getValue() {
      return expression != null ? expression : (object != null ? object.toString() : null);
    }

    public TagAttribute toTagAttribute() {
      return new TagAttributeImpl(new Location("", -1, -1), "", this.name, this.name, getValue());
    }
  }

  public static class CompositeComponentHandler extends CompositeComponentTagHandler {
    private UIComponent component;

    private Attribute[] attributes;

    public CompositeComponentHandler(Resource ccResource, ComponentConfig config, Attribute... attributes) {
      super(ccResource, config);
      this.attributes = attributes;
    }

    @Override
    public void setAttributes(FaceletContext ctx, Object instance) {
      if (isEmpty(attributes)) {
        super.setAttributes(ctx, instance);
        return;
      }
      final UIComponent c = (UIComponent) instance;
      final FacesContext facesContext = ctx.getFacesContext();
      final ELContext elContext = facesContext.getELContext();
      final ExpressionFactory elFactory = facesContext.getApplication()
          .getExpressionFactory();
      for (Attribute attribute : attributes) {
        // expression attribute
        if (attribute.getObject() == null && attribute.getExpression() != null)
          c.setValueExpression(
              attribute.getName(),
              elFactory.createValueExpression(elContext, attribute.getExpression(), attribute.getType()));
        // object attribute
        else if (attribute.getObject() != null)
          c.setValueExpression(
              attribute.getName(),
              elFactory.createValueExpression(attribute.getObject(), attribute.getType()));
      }
    }

    @Override
    public void onComponentCreated(FaceletContext ctx, UIComponent c, UIComponent parent) {
      super.onComponentCreated(ctx, c, parent);
      component = c;
    }

    @Override
    public void onComponentPopulated(FaceletContext ctx, UIComponent c, UIComponent parent) {
      super.onComponentCreated(ctx, c, parent);
      component = c;
    }

    public UIComponent getComponent() {
      return component;
    }
  }

  /**
   * @param familyVar resource bundle variable defined in a faces-config.xml
   * @param labelKey the key of the bundle in the familyVar resource bundle
   * @param defaultLabel the default label if the label can be resolved in the resource bundles
   * @param locale (optional) if <code>null</code>, {@link JSFHelper#getLocale()} is used
   * @param args (optional) used to format the label using {@link MessageFormat#format(String, Object...)}
   * @return the label in the asked language based on its family variable defined in a faces-config.xml and its bundle key. If familyVar or
   *         labelKey are <code>null</code> or the label is not found, the defaultLabel is returned.
   * @since 3.2
   * @see ApplicationAssociate#getResourceBundles()
   */
  public static final String getLabel(final String familyVar, final String labelKey, final String defaultLabel, final Locale locale,
      final Object... args) {
    String r = null;
    if (isBlank(familyVar) || isBlank(labelKey)) {
      log.warn("Missing label data: {}.{} [{}]", familyVar, labelKey, locale);
      r = defaultLabel;
    }
    else {
      final String def = isBlank(defaultLabel) ? labelKey : defaultLabel;
      final Locale l = locale == null ? getLocale() : locale;
      ResourceBundle bundle = null;
      try {
        if (ApplicationAssociate.getCurrentInstance() != null) {
          bundle = ApplicationAssociate.getCurrentInstance()
              .getResourceBundles()
              .get(familyVar)
              .getResourceBundle(l);
          r = bundle.getString(labelKey);
        }
        else {
          r = SpringContextHelper.getMessage(labelKey, null, null, l);
        }
      }
      catch (Exception e) {
        log.warn("Error getting label: {}.{} [{}] ({})", familyVar, labelKey, l, e.getMessage());
      }

      // if bundle for locale is not present it will take the one for default locale
      if (isBlank(r) || bundle != null && !bundle.getLocale()
          .equals(l))
        getMissingKeyHandler().addMissingKey(familyVar, labelKey, l);
      if (isBlank(r))
        r = def;
    }

    if (isNotEmpty(args) && args[0] != null)
      r = MessageFormat.format(r, args);

    return r;
  }

  public static final MissingKeyHandler getMissingKeyHandler() {
    return getBean(MissingKeyHandler.class, MissingKeyHandler.BEAN_NAME);
  }

  /**
   * Code coming from http://stackoverflow.com/questions/14378437/find-component-by-id-in-jsf This code find an UIComponent by his id. The
   * JSF page tree is traversed until the provided id is found. This code is a workaround of the binding method. Which unfortunately does
   * not work in task (StackOverFlow exception).
   * 
   * @param id
   * @return
   */
  public static UIComponent findComponent(final String id) {
    Validate.notEmpty(id);
    FacesContext context = FacesContext.getCurrentInstance();
    UIViewRoot root = context.getViewRoot();
    final UIComponent[] found = new UIComponent[1];
    root.visitTree(new FullVisitContext(context), new VisitCallback() {
      @Override
      public VisitResult visit(VisitContext context, UIComponent component) {
        if (Objects.equals(component.getId(), id)) {
          found[0] = component;
          return VisitResult.COMPLETE;
        }
        return VisitResult.ACCEPT;
      }
    });
    return found[0];
  }

  /**
   * Resets the data table with the given id if found, otherwise it will log a warning
   * 
   * @param componentId
   * @return the data table element
   */
  public static DataTable resetDataTable(String componentId) {
    UIComponent component = FacesContext.getCurrentInstance()
        .getViewRoot();
    DataTable dataTable = (DataTable) component.findComponent(componentId);
    if (dataTable != null) {
      dataTable.reset();
    }
    else {
      log.warn("Cannot find data table component '" + componentId + "'. ");
    }
    return dataTable;
  }
  /*
   * -- FILE --
   */

  /**
   * Send the given file to the response. The content type will be determined based on file name. The content length will be set to the
   * length of the file. The {@link FacesContext#responseComplete()} will implicitly be called after successful streaming.
   * 
   * @param file The file to be sent to the response.
   * @param attachment Whether the file should be provided as attachment, or just inline.
   * @throws UncheckedIOException When HTTP response is not available anymore.
   */
  public static void sendFile(File file, boolean attachment) {
    try (FileInputStream fis = new FileInputStream(file)) {
      sendFile(fis, file.getName(), file.length(), attachment);
    }
    catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  /**
   * Send the given file to the response. The content type will be determined based on file name. The content length will be set to the
   * length of the file. The {@link FacesContext#responseComplete()} will implicitly be called after successful streaming.
   * 
   * @param file The file to be sent to the response.
   * @param filename default is {@link File#getName()}
   * @param attachment Whether the file should be provided as attachment, or just inline.
   * @throws UncheckedIOException When HTTP response is not available anymore.
   */
  public static void sendFile(File file, String filename, boolean attachment) {
    try (FileInputStream fis = new FileInputStream(file)) {
      sendFile(fis,
          ofNullable(filename).orElse(file.getName()),
          file.length(),
          attachment);
    }
    catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  /**
   * Send the given byte array as a file to the response. The content type will be determined based on file name. The content length will be
   * set to the length of the byte array. The {@link FacesContext#responseComplete()} will implicitly be called after successful streaming.
   * 
   * @param content The file content as byte array.
   * @param filename The file name which should appear in content disposition header.
   * @param attachment Whether the file should be provided as attachment, or just inline.
   * @throws UncheckedIOException When HTTP response is not available anymore.
   */
  public static void sendFile(byte[] content, String filename, boolean attachment) {
    sendFile(new ByteArrayInputStream(content), filename, content.length, attachment);
  }

  /**
   * Send the given input stream as a file to the response. The content type will be determined based on file name. The content length may
   * not be set because that's not predictable based on input stream. The client may receive a download of an unknown length and thus the
   * download progress may be unknown to the client. Only if the input stream is smaller than the default buffer size, then the content
   * length will be set. The {@link InputStream#close()} will implicitly be called after streaming, regardless of whether an exception is
   * been thrown or not. The {@link FacesContext#responseComplete()} will implicitly be called after successful streaming.
   * 
   * @param content The file content as input stream.
   * @param filename The file name which should appear in content disposition header.
   * @param attachment Whether the file should be provided as attachment, or just inline.
   * @throws UncheckedIOException When HTTP response is not available anymore.
   */
  public static void sendFile(InputStream content, String filename, boolean attachment) {
    sendFile(content, filename, -1, attachment);
  }

  /**
   * Send a file to the response whose content is provided via given output stream callback. The content type will be determined based on
   * file name. The content length will not be set because that would require buffering the entire file in memory or temp disk. The client
   * may receive a download of an unknown length and thus the download progress may be unknown to the client. If this is undesirable, write
   * to a temp file instead and then use {@link #sendFile(File, boolean)}. The {@link FacesContext#responseComplete()} will implicitly be
   * called after successful streaming.
   * 
   * @param filename The file name which should appear in content disposition header.
   * @param attachment Whether the file should be provided as attachment, or just inline.
   * @param outputCallback The output stream callback to write the file content to.
   * @throws UncheckedIOException When HTTP response is not available anymore.
   */
  public static void sendFile(String filename, boolean attachment, Output outputCallback) {
    ExternalContext externalContext = getExternalContext();

    // Prepare the response and set the necessary headers.
    externalContext.setResponseBufferSize(DEFAULT_SENDFILE_BUFFER_SIZE);
    externalContext.setResponseContentType(getMimeType(filename));
    externalContext.setResponseHeader("Content-Disposition", formatContentDispositionHeader(filename, attachment));

    // Not exactly mandatory, but this fixes at least a MSIE quirk: http://support.microsoft.com/kb/316431
    if (((HttpServletRequest) externalContext.getRequest()).isSecure()) {
      externalContext.setResponseHeader("Cache-Control", "public");
      externalContext.setResponseHeader("Pragma", "public");
    }

    try (OutputStream output = externalContext.getResponseOutputStream()) {
      outputCallback.writeTo(output);
    }
    catch (IOException e) {
      throw new UncheckedIOException(e);
    }

    getFacesContext().responseComplete();
  }

  public static String getBrowserUrl() {
    return ofNullable(FacesContext.getCurrentInstance()).map(FacesContext::getExternalContext)
        .map(ExternalContext::getRequest)
        .filter(HttpServletRequest.class::isInstance)
        .map(HttpServletRequest.class::cast)
        .map(JSFHelper::getRequestURL)
        .orElse(null);
  }

  public static String getPort(String scheme) {
    int requestServerPort = getRequest().getServerPort();
    log.info("Request URL server port: " + requestServerPort);
    return HTTPS_SCHEME.equalsIgnoreCase(scheme) && getRequest().getServerPort() == 80 ? ""
        : ":".concat(String.valueOf(getRequest().getServerPort()));
  }

  public static String getScheme() {
    String scheme = getRequest().getHeader(HttpHeaders.X_FORWARDED_PROTO);
    log.info("URL scheme from request header (X_FORWARDED_PROTO): " + scheme);
    if (scheme == null) {
      scheme = getRequest().getScheme();
      log.info("URL scheme from request: " + scheme);
    }
    return scheme;
  }

  public static String getScheme(HttpServletRequest request) {
    String scheme = request.getHeader(HttpHeaders.X_FORWARDED_PROTO);
    log.info("URL scheme from request header (X_FORWARDED_PROTO): " + scheme);
    if (scheme == null) {
      scheme = request.getScheme();
      log.info("URL scheme from request: " + scheme);
    }
    return scheme;
  }

  private static String replaceRequestScheme(String scheme, String requestString) {
    if (HTTPS_SCHEME.equalsIgnoreCase(scheme) && !requestString.startsWith(HTTPS_SCHEME)) {
      String updatedRequestString = StringUtils.replaceFirst(requestString, HTTP_SCHEME, HTTPS_SCHEME);
      log.info("Replaced scheme for Request URL: " + requestString + " to " + updatedRequestString);
      return updatedRequestString;
    }
    return requestString;
  }

  public static String getRequestURL(HttpServletRequest request) {
    return replaceRequestScheme(getScheme(request), request.getRequestURL()
        .toString());
  }

  /**
   * Internal global method to send the given input stream to the response.
   * 
   * @param input The file content as input stream.
   * @param filename The file name which should appear in content disposition header.
   * @param contentLength The content length, or -1 if it is unknown.
   * @param attachment Whether the file should be provided as attachment, or just inline.
   * @throws UncheckedIOException When HTTP response is not available anymore.
   */
  private static void sendFile(InputStream input, String filename, long contentLength, boolean attachment) {
    sendFile(filename, attachment, output -> {
      ExternalContext externalContext = getExternalContext();

      // If content length is known, set it. Note that setResponseContentLength() cannot be used as it takes only int.
      if (contentLength != -1) {
        externalContext.setResponseHeader("Content-Length", String.valueOf(contentLength));
      }

      long size = stream(input, output);

      // This may be on time for files smaller than the default buffer size, but is otherwise ignored anyway.
      if (contentLength == -1 && !externalContext.isResponseCommitted()) {
        externalContext.setResponseHeader("Content-Length", String.valueOf(size));
      }
    });
  }

  /**
   * Stream the given input to the given output via NIO {@link Channels} and a directly allocated NIO {@link ByteBuffer}. Both the input and
   * output streams will implicitly be closed after streaming, regardless of whether an exception is been thrown or not.
   * 
   * @param input The input stream.
   * @param output The output stream.
   * @return The length of the written bytes.
   * @throws IOException When an I/O error occurs.
   */
  private static long stream(InputStream input, OutputStream output) throws IOException {
    try (ReadableByteChannel inputChannel = Channels.newChannel(input);
        WritableByteChannel outputChannel = Channels.newChannel(output)) {
      ByteBuffer buffer = ByteBuffer.allocateDirect(DEFAULT_SENDFILE_BUFFER_SIZE);
      long size = 0;

      while (inputChannel.read(buffer) != -1) {
        buffer.flip();
        size += outputChannel.write(buffer);
        buffer.clear();
      }

      return size;
    }
  }

  /**
   * @see ExternalContext#getMimeType(String)
   */
  private static String getMimeType(String name) {
    return ofNullable(getExternalContext().getMimeType(name))
        .orElse(DEFAULT_MIME_TYPE);
  }

  /**
   * <p>
   * Format an UTF-8 compatible content disposition header for the given filename and whether it's an attachment.
   * 
   * @param filename The filename to appear in "Save As" dialogue.
   * @param attachment Whether the content should be provided as an attachment or inline.
   * @return An UTF-8 compatible content disposition header.
   */
  private static String formatContentDispositionHeader(String filename, boolean attachment) {
    return format(CONTENT_DISPOSITION_HEADER, (attachment ? "attachment" : "inline"), encodeURI(filename));
  }

  /**
   * URI-encode the given string using UTF-8. URIs (paths and filenames) have different encoding rules as compared to URL query string
   * parameters. {@link URLEncoder} is actually only for www (HTML) form based query string parameter values (as used when a webbrowser
   * submits a HTML form). URI encoding has a lot in common with URL encoding, but the space has to be %20 and some chars doesn't
   * necessarily need to be encoded.
   * 
   * @param string The string to be URI-encoded using UTF-8.
   * @return The given string, URI-encoded using UTF-8, or <code>null</code> if <code>null</code> was given.
   * @throws UnsupportedOperationException When this platform does not support UTF-8.
   */
  private static String encodeURI(String string) {
    if (string == null) {
      return null;
    }

    return encodeURL(string)
        .replace("+", "%20")
        .replace("%21", "!")
        .replace("%27", "'")
        .replace("%28", "(")
        .replace("%29", ")")
        .replace("%7E", "~");
  }

  /**
   * URL-encode the given string using UTF-8.
   * 
   * @param string The string to be URL-encoded using UTF-8.
   * @return The given string, URL-encoded using UTF-8, or <code>null</code> if <code>null</code> was given.
   * @throws UnsupportedOperationException When this platform does not support UTF-8.
   */
  private static String encodeURL(String string) {
    if (string == null) {
      return null;
    }

    try {
      return URLEncoder.encode(string, UTF_8.name());
    }
    catch (UnsupportedEncodingException e) {
      throw new UnsupportedOperationException("UTF-8 is apparently not supported on this platform.", e);
    }
  }

  /**
   * Use this if you need an output stream callback.
   */
  @FunctionalInterface
  public interface Output {

    /**
     * This method should be invoked by the method where you're passing this callback instance to.
     * 
     * @param output The callback output stream to write to.
     * @throws IOException When given output stream cannot be written.
     */
    void writeTo(OutputStream output) throws IOException;

  }

  /**
   * Reset all values for a component. <br>
   * https://stackoverflow.com/questions/20440079/reset-all-fields-in-form
   * 
   * @param fullComponentId String representing the component id (ex: "form-taskA-panel1"). <br>
   *          If it is empty, it clears the entire view.
   */
  public static void clearComponent(String fullComponentId) {
    if (fullComponentId == null) {
      log.error("Cannot reset component values. Component id cannot be null.");
      return;
    }

    UIViewRoot root = FacesContext.getCurrentInstance()
        .getViewRoot();
    Iterator<UIComponent> children = root.findComponent(fullComponentId)
        .getFacetsAndChildren();
    clearAllComponentInChilds(children);
  }

  /**
   * Helper method for {@link #clearComponent(String)} <br>
   * Iterates over all children of a component.
   * 
   * @param childrenIt
   */
  private static void clearAllComponentInChilds(Iterator<UIComponent> childrenIt) {
    while (childrenIt.hasNext()) {
      UIComponent component = childrenIt.next();
      if (component instanceof HtmlInputText) {
        HtmlInputText com = (HtmlInputText) component;
        com.resetValue();
      }
      if (component instanceof HtmlSelectOneMenu) {
        HtmlSelectOneMenu com = (HtmlSelectOneMenu) component;
        com.resetValue();
      }
      if (component instanceof HtmlSelectBooleanCheckbox) {
        HtmlSelectBooleanCheckbox com = (HtmlSelectBooleanCheckbox) component;
        com.resetValue();
      }
      if (component instanceof HtmlSelectManyCheckbox) {
        HtmlSelectManyCheckbox com = (HtmlSelectManyCheckbox) component;
        com.resetValue();
      }
      clearAllComponentInChilds(component.getFacetsAndChildren());
    }
  }

  public static String encodeURLWithSpaces(String string) {
    return URLEncoder.encode(string, UTF_8)
        .replace("+", "%20");
  }

}
