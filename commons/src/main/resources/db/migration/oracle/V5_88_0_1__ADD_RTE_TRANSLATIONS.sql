INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('generic_error_xcbl_norm', 'en', 'Unknown message version number and/or release number. Forced to D96A.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('generic_error_xcbl_norm', 'es', 'Número de versión del mensaje y/o número de versión desconocidos. Forzado a D96A.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('generic_error_xcbl_norm', 'fr', 'Le numéro de la version du message et/ou le numéro de l''édition du message sont incorrects. On force à D96A.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'de', 'Parafiscal tax breakdown: The allowance/charge qualifier is incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'de', 'Allowance-Charge line: The discount percentage is not a number{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'de', 'MOA+125 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'de', 'eC082.3039 in NAD+IV is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'de', 'eC080.3036.1 in NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+VA for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+VA for NAD+BY is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'de', 'More than one MOA g15 found after ALC segment (Qualifier : 8){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'de', 'eC080.3036.1 in NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'de', 'eC516.5004 in MOA+113 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'de', 'eC080.3036.1 in NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'de', 'More than one DTM+137.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+GN for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'de', 'More than one MOA+124 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'de', 'More than one segment NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+GN for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'de', 'eC080.3036.1 in NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+GN for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'de', 'segment DTM+171 is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'de', 'MOA+124 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'de', 'More than one segment NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'de', 'eC506.1154 in RFF+PQ is empty\",\"RFF+PQ(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'de', 'ALC(g25) description missing (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'de', 'More than one segment NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'de', 'More than one MOA+128 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'de', 'More than one segment QTY+47 (g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'de', 'eC516.5004 in MOA+9 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'de', 'eC082.3039 in NAD+SE is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'de', 'More than one PCD g25 found after ALC segment{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+VA for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'de', 'More than one MOA g25 found after ALC segment (Qualifier 204 or 23){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'de', 'DTM+137 is missing{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'de', 'segment RFF+PQ is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+GN for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'de', 'More than one segment NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'de', 'MOA+128 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'de', 'More than one MOA+125 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+VA for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'de', 'eC506.1154 in RFF+VA for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0011', 'es', 'Fechas documento: El calificativo del formato del periodo de facturado cion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0010', 'es', 'Fechas documento: El periodo de facturado cion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0001', 'es', 'Recuerdo referencias dirrecciones : El calificativo de la referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0003', 'es', 'Recuerdo referencias dirrecciones : El numero de matriculacion al RCS-RCM del vendedor esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0002', 'es', 'Recuerdo referencias dirrecciones : El numero de matriculacion al RCS-RCM del vendedor falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0017', 'es', 'Fechas documento: La fecha del documento es posterior a la fecha del dia{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0016', 'es', 'Fechas documento: Existe una incoherencia entre las identificaciones del dia y del mes de la fecha del documento{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0015', 'es', 'Fechas documento: La identificacion del dia en la fecha del documento es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0014', 'es', 'Fechas documento: La identificacion del mes en la fecha del documento es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0013', 'es', 'Fechas documento: Las informaciones de fechas/horas/ periodos del encabezamiento del documento faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0012', 'es', 'Fechas documento: El largo del periodo de facturado cion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0009', 'es', 'Fechas documento: La fecha/hora reale de reparto no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontants0002', 'es', 'Condiciones Pago: El importe de descuento condicional no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0008', 'es', 'Fechas documento: La fecha/hora de levantamiento no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0007', 'es', 'Fechas documento: La fecha/hora de expedicion no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0006', 'es', 'Fechas documento: El largo de la fecha/hora del documento es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0005', 'es', 'Fechas documento: El calificativo del formato de la fecha/hora del documento es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0004', 'es', 'Fechas documento: La fecha/hora del documento no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0003', 'es', 'Fechas documento: La fecha/hora del documento esta transmitida en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0002', 'es', 'Fechas documento: La fecha/hora del documento falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0001', 'es', 'Fechas documento: El calificativo de fecha/hora/periodo es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantTaxeLigne0001', 'es', 'Importe IVA linea: La informacion de carga de linea (ALC+C) padre del importe de carga MOA+23 falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0003', 'es', 'Fechas referencias globales: El calificativo del formato del periodo de validez es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0004', 'es', 'Fechas referencias globales: El tiempo del periodo de validez del contrato es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0001', 'es', 'Encabezamiento lote: La identificacion del emisor del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0002', 'es', 'Encabezamiento lote: La identificacion del destiantario del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0003', 'es', 'Encabezamiento lote: La fecha de preparacion del intercambio no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0004', 'es', 'Encabezamiento lote: El largo de la fecha de preparacion del intercambio es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0005', 'es', 'Encabezamiento lote: La identificacion de los añosen la fecha de preparacion del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0002', 'es', 'Reduccion-cargos linea: El importe de carga no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0006', 'es', 'Encabezamiento lote: La identificacion del mes en la fecha de preparacion del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0001', 'es', 'Reduccion-cargos linea: El importe de reduccion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0007', 'es', 'Encabezamiento lote: La identificacion del dia en la fecha de preparacion del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0004', 'es', 'Reduccion-cargos linea: El importe de carga esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0008', 'es', 'Encabezamiento lote: Existe una incoherencia entre las identificaciones del dia y del mes de la fecha de preparacion del intercambio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0003', 'es', 'Reduccion-cargos linea: El importe de reduccion esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0009', 'es', 'Encabezamiento lote: La hora de preparacion del intercambio no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0001', 'es', 'Fechas referencias globales: El calificativo de fecha/hora/periodo del documento en referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0002', 'es', 'Fechas referencias globales: El periodo de validez no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0010', 'es', 'Encabezamiento lote: La duracion de la horade preparacion del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0011', 'es', 'Encabezamiento lote: La identificacion de las horas en la hora de preparacion del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0012', 'es', 'Encabezamiento lote: La identificacion de los minutos en la hora de preparacion del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0005', 'es', 'Reducciones-cargos globales : El calificativo de reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0013', 'es', 'Encabezamiento lote: La referencia de control del intercambio es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0003', 'es', 'Reducciones-cargos globales : El porcentaje y el importe de reduccion faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0004', 'es', 'Reducciones-cargos globales : El porcentaje y el importe de carga faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0001', 'es', 'Reducciones-cargos globales : El codigo pago de la reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0002', 'es', 'Reducciones-cargos globales : El 1ero elemento del intitulado de la reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0001', 'es', 'Referencias globales : El numero de facturado  y el periodo de validez/facturado cion (documento en si o contrato) en referencia en el haber faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0002', 'es', 'Referencias globales : El numero de facturado  en referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0003', 'es', 'Referencias globales : El numero de contrato en referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0020', 'es', 'Importes totales : El importe total IVA incluido falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0021', 'es', 'Importes totales : El importe total IVA incluido no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0022', 'es', 'Importes totales : El importe total sin IVA de las lineas articulo falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0012', 'es', 'Importes totales : La referencia de un anticipo falta para el importe total de anticipo{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0014', 'es', 'Importes totales : El importe total de anticipo no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0015', 'es', 'Importes totales : El importe total neto para pagar falta aun que el importe total de anticipo esta transmitido{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0017', 'es', 'Importes totales : El importe total neto para pagar no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0018', 'es', 'Importes totales : El importe total IVA falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0019', 'es', 'Importes totales : El importe total IVA no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'es', 'Recapitulativo impuesto parafiscal : El calificativo de reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0003', 'es', 'Recapitulativo impuesto parafiscal : El 1ero elemento del intitulado del impuesto parafiscal es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0023', 'es', 'Importes totales : El importe total sin IVA de las lineas articulos no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0024', 'es', 'Importes totales : El importe de descuento no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0025', 'es', 'Importes totales : El importe total IVA esta transmitida por lo menos tres veces, o en doblon sin codigos monedas (un codigo moneda esperado para cada importe){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0008', 'es', 'Reducciones-cargos globales : La tasa de impuesto falta en la ventilacion de impuesto en pie de mensaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0007', 'es', 'Condiciones pago: Las condiciones de penalizacion faltan simultaneamente en texto y bajo forma codificada{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0008', 'es', 'Condiciones pago: Las condiciones de penalizacion estan transmitidas simultaneamente en texto y bajo forma codificada ( puede existir una unica modelizacion){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0005', 'es', 'Condiciones pago: Las condiciones de descuento condicional faltan simultaneamente en texto y bajo la forma codificada{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0006', 'es', 'Condiciones pago: Las condiciones de descuento estan transmitidassimultaneamente en texto y bajo la forma codificada{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0003', 'es', 'Condiciones pago: Las informaciones de condiciones de descuento condicional faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0004', 'es', 'Condiciones pago: Las informaciones de condiciones de penalizacion faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0006', 'es', 'Condiciones pago: La fecha de pago no es numrica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0001', 'es', 'Condiciones pago: Las informaciones de condiciones de pago faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0005', 'es', 'Condiciones pago: La fecha de pago falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ALigne0001', 'es', 'Detalle: El numero de linea no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0002', 'es', 'Condiciones pago: Las informaciones de fecha de vencimiento faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0004', 'es', 'Condiciones pago: La fecha de vencimiento no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0003', 'es', 'Condiciones pago: La fecha de vencimiento falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0002', 'es', 'Condiciones pago: El calificativo de fecha/hora de las condiciones de pago es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0001', 'es', 'Condiciones pago: La fecha de las condiciones de pago esta transmitida en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0036', 'es', 'Dirreciones: La identificacion del organismo encargado de la lista codigo (tipo de codigo) del comprador es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0037', 'es', 'Dirreciones: El 1ero elemento del nombre y dirreccion del comprador es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0038', 'es', 'Dirreciones: El 2do elemento del nombre y dirreccion del comprador es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0039', 'es', 'Dirreciones: El 1ero elemento de la denominacion social del comprador es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AFindeSection0001', 'es', 'Final de seccion: El codigo de identificacion de seccion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0032', 'es', 'Dirreciones: El 1ero elemento del nombre y dirreccion del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0033', 'es', 'Dirreciones: El 2do elemento del nombre y dirreccion del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0034', 'es', 'Dirreciones: Las informaciones del comprador faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0035', 'es', 'Dirreciones: El codigo de identificacion del comprador es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentairesLigne0001', 'es', 'Comentarios linea: El 1ero elemento del texto describiendo la razon de la exoneracion del impuesto a la linea articulo es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0030', 'es', 'Dirreciones: Las informaciones de la facturado  faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0031', 'es', 'Dirreciones: La identificacion del organismo encargado de la lista del codigo (tipo codigo)del vendedor es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0029', 'es', 'Dirreciones: El codigo pais del declarante de IVA es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0025', 'es', 'Dirreciones: El 1ero elemento de la denominacion social del declarante de IVA es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0026', 'es', 'Dirreciones: El 1ero elemento de la dirreccion del declarante de IVA es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0027', 'es', 'Dirreciones: El nombre de la localidad del declarante de IVA es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0028', 'es', 'Dirreciones: El codigo postal del declarante de IVA es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0001', 'es', 'Referencias globales : El numero de BL/aviso de expedicion en referencia falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0021', 'es', 'Dirreciones: El nombre de la localidad de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0001', 'es', 'Fechas referencias linea: La fecha/hora del BL/aviso de expedicion en referencia falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0022', 'es', 'Dirreciones: El codigo postal de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0002', 'es', 'Fechas referencias linea: La fecha/hora del BL/aviso de expedicion en referencia no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0023', 'es', 'Dirreciones: El codigo pais de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0024', 'es', 'Dirreciones: Las informaciones del declarante de IVA estan transmitidas en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0002', 'es', 'Referencias globales : El numero de BL/aviso de expedicion en referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0020', 'es', 'Dirreciones: El 1ero elemento de la dirreccion de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0018', 'es', 'Dirreciones: Las informaciones de la sede social del vendedor estan transmitidas en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0001', 'es', 'Reducciones-cargos linea : El porcentaje de reduccion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0019', 'es', 'Dirreciones: El 1ero elemento de la denominacion social de la sede del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0002', 'es', 'Reducciones-cargos linea : El porcentaje de carga no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0014', 'es', 'Dirreciones: El 1ero elemento de la dirreccion del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0015', 'es', 'Dirreciones: El nombre de la localidad del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0016', 'es', 'Dirreciones: El codigo postal del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0017', 'es', 'Dirreciones: El codigo pais del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0005', 'es', 'Descripcion: El 1ero elemento del intitulado articulo es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0010', 'es', 'Dirreciones: Las informaciones del vendedor faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0004', 'es', 'Descripcion: El 1ero elemento del intitulado articulo es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0011', 'es', 'Dirreciones: Las informaciones del vendedor estan transmitidas en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0003', 'es', 'Descripcion: El codigo de descripcion del intitulado del articulo falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0012', 'es', 'Dirreciones: El codigo de identificacion del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0002', 'es', 'Descripcion: El codigo del tipo de descripcion del objeto es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0013', 'es', 'Dirreciones: El 1ero elemento de la denominacion social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0001', 'es', 'Descripcion: Las informaciones de descripcion del objeto faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0003', 'es', 'Cantidad detalle : El calificativo de cantidad facturado da falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0002', 'es', 'Cantidad detalle : El calificativo de cantidad es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'es', 'Reducciones-cargos linea :El porcentaje de reduccion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0001', 'es', 'Cantidad detalle : Las informaciones de catidad faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0001', 'es', 'MENSAJE argumento no esta definido{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0007', 'es', 'Dirreciones: El nombre de la localidad del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0008', 'es', 'Dirreciones: El codigo postal del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0006', 'es', 'Cantidad detalle : La unidad de medida de la cantidad facturado da es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesMontantsTotaux0001', 'es', 'Referencias importes totales : El numero de anticipo en referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0009', 'es', 'Dirreciones: El codigo pais del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0005', 'es', 'Cantidad detalle : La cantidad facturado da no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0004', 'es', 'Cantidad detalle : El calificativo de cantidad facturado da esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0003', 'es', 'Dirreciones: Las informaciones del socio facturado  estan transmitidas en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0004', 'es', 'Dirreciones: El codigo de identificacion del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0005', 'es', 'Dirreciones: El 1ero elemento de la denominacion social del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0006', 'es', 'Dirreciones: El 1ero elemento de la dirreccion del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004', 'es', 'Reducciones-cargos globales : La tasa de impuesto no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0003', 'es', 'Reducciones-cargos globales : El codigo del tipo de impuesto (VAT) asocia al calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0002', 'es', 'Reducciones-cargos globales : El calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0009', 'es', 'IVA linea : El codigo de la categoria de impuesto es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0001', 'es', 'Reducciones-cargos globales : Las informaciones de impuesto faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0008', 'es', 'IVA linea : La tasa de impuesto falta en la ventilacion de impuesto en pie de mensaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0007', 'es', 'IVA linea : Las informaciones de impuesto estan transmitidas en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004', 'es', 'IVA linea : La tasa de impuesto no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0011', 'es', 'Divisas: La divisa de referencia falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0003', 'es', 'IVA linea : El codigo del tipo de impuesto (VAT) asocia al calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0010', 'es', 'Divisas: Las informaciones de divisa faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0002', 'es', 'IVA linea : El calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0013', 'es', 'Divisas: El codigo de la divisa de facturado cion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0001', 'es', 'IVA linea : La sinformaciones de impuesto faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0012', 'es', 'Divisas: El calificativo de la divisa de referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0001', 'es', 'Dirreciones: Las informaciones relativas a los socios faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0002', 'es', 'Dirreciones: Las informaciones del socio facturado  faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0006', 'es', 'El mensaje no contiene segmento UNB o UNH{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0007', 'es', 'El intercambio contiene varios mensajes{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0004', 'es', 'Divisas: El codigo de la divisa de referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0003', 'es', 'Divisas: El calificativo de la divisa de referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0006', 'es', 'Divisas: El calificativo del uso de la divisa meta es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0005', 'es', 'Divisas: El codigo de la divisa de referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0008', 'es', 'Divisas: El codigo de la divisa meta es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0007', 'es', 'Divisas: El calificativo de la divisa meta es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0010', 'es', 'Importes totales : El importe total IVA incluido esta transmitido por lo menos tres veces, o en doblon sin codigos monedas ( un codigo moneda esperado para cada importe){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0009', 'es', 'Divisas: El codigo de la divisa meta es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0011', 'es', 'Importes totales : El importe total IVA incluido no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0001', 'es', 'Importes totales : las informaciones de los importes totales faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0002', 'es', 'Importes totales : El importe total sin IVA falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0003', 'es', 'Importes totales : El importe total sin IVA esta transmitido por lo menos en tres veces, o en doblo, sin codigos monedas ( un codigo moneda esperado para cada importe){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0004', 'es', 'Importes totales : El importe total sin IVA no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0005', 'es', 'Importes totales : El importe total IVA falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0006', 'es', 'Importes totales : El segundo importe total IVA expresa en la moneda meta falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0002', 'es', 'Divisas: El calificativo del uso de la divisa de referencia es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0007', 'es', 'Importes totales : El importe total IVA esta transmitido en doblon aun que ninguna moneda meta esta presente{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0008', 'es', 'Importes totales : El importe total IVA no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0003', 'es', 'Reduccion-cargos globales: El importe de reduccion esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0009', 'es', 'Importes totales : El importe total IVA incluido falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0001', 'es', 'Reducciones-cargos linea : El calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0002', 'es', 'Reduccion-cargos globales: El importe de carga no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0002', 'es', 'Reduccciones-cargos linea : El codigo del tipo de impuesto (VAT) asocia al calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0001', 'es', 'Reduccion-cargos globales: El importe de reduccion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0003', 'es', 'Reducciones-cargos linea : La tasa de impuesto no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0005', 'es', 'Reducciones-cargos linea : La sinformaciones de impuesto estan transmitidas en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0006', 'es', 'Reducciones-cargos linea : La tasa de impuesto falta en la ventilacion de impuesto en pie de mensaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0004', 'es', 'Reduccion-cargos globales: El importe de la carga esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0003', 'es', 'Ventilacion IVA : El codigo del tipo de impuesto (VAT) asocia al calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004', 'es', 'Ventilacion IVA : El importe imponible no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0003', 'es', 'Condiciones pago : El porcentaje de penalizacion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0005', 'es', 'Ventilacion IVA : La tasa de impuesto no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0002', 'es', 'Condiciones pago : El porcentaje de descuento condicional no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0005', 'es', 'Condiciones pago : Existe una incoherencia entre el indicador de penalizaciony el tipo de porcentaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0004', 'es', 'Condiciones pago : Existe una incoherencia entre el indicador de descuento condicional y el tipo de porcentaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0001', 'es', 'Ventilacion IVA : Las informaciones de impuesto faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0002', 'es', 'Ventilacion IVA : El calificativo de la funcion de impuesto (7) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0002', 'es', 'Comentarios: Las informaciones reglamentarias del vendedor faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0001', 'es', 'Comentarios: Los comentarios de encabezamiento del documento faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0006', 'es', 'Comentarios: El capital social y la divisa del vendedor son incorrectos{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0005', 'es', 'Comentarios: La forma juridica del vendedor es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0004', 'es', 'Comentarios: La denominacion social del vendedor es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0003', 'es', 'Comentarios: Las informaciones reglamentarias del vendedor estan en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0001', 'es', 'Fechas referencias importes totales: El calificativo de fecha/hora de la referencia del importe es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0002', 'es', 'Fechas referencias importes totales: La fecha de anticipo falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0003', 'es', 'Fechas referencias importes totales: La fecha de anticipo no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0004', 'es', 'Fechas documento: La fecha/hora reale de reparto no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0007', 'es', 'Ventilacion IVA : La tasa de impuesto falta en el resto del mensaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0008', 'es', 'Ventilacion IVA : El codigo de la categoria de impuesto es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0009', 'es', 'Ventilacion IVA : La tasa de impuesto esta transmitida en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0001', 'es', 'Fechas documento: El calificativo de fecha/hora/periodo es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0002', 'es', 'Fechas documento: La fecha/hora de expedicion no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0003', 'es', 'Fechas documento: La fecha/hora de levantamiento no es numerica{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0013', 'es', 'Comentarios: La indemnizacion arancelaria en caso de retraso de pago falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0012', 'es', 'Comentarios: El 1ero elemento del texto describiendo la razon de la exoneracion del impuesto en el encabezamiento del documento es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0011', 'es', 'Comentarios: Existe una incoherencia entre el comentario describiendo el motivo de la exoneracion de impuesto y el codigo de exoneracion del impuesto en el encabezamiento del documento{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0010', 'es', 'Comentarios: El 1ero elemento del texto describiendo las condiciones de penalidad es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARegimesTva0001', 'es', 'Impuesto derecho : Existe una incoherencia entre el codigo de exoneracion de impuesto y el comentario de encabezamiento describiendo el motivo de la exoneracion del impuesto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0017', 'es', 'Comentarios: El RCS-RCM y la ciudad del vendedor son incorrectos{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0016', 'es', 'Comentarios: El intitulado de las condiciones de descuento del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0015', 'es', 'Comentarios: El intitulado del régimen del IVA del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0014', 'es', 'Comentarios: Las informaciones relativas a la naturaleza del IVA y del descuento del vendedor faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0009', 'es', 'Comentarios: El texto describiendo las condiciones de penalidad esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0008', 'es', 'Comentarios: El 1ero elemento del texto describiendo las condiciones de descuento condicional es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0007', 'es', 'Comentarios: El texto describiendo las condiciones de descuento condicional est atransmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0001', 'es', 'Principio mensaje: El codigo del documento es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0004', 'es', 'Principio mensaje: El codigo funcion del mensaje es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0003', 'es', 'Principio mensaje: El numero del documento es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0002', 'es', 'Reducciones-cargos globales : El porcentaje de carga no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0001', 'es', 'Reducciones-cargos globales : El porcentaje de reduccion no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0027', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0026', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN de la sede social del vendedor falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0028', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del socio facturado do falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0021', 'es', 'Recuerdo referencias dirrecciones : El numero de matriculacion al RCS-RCM de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0020', 'es', 'Recuerdo referencias dirrecciones : El numero de matriculacion al RCS-RCM de la sede social del vendedor falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0023', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA de la sede social del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0022', 'es', 'Recuerdo referencias dirrecciones : El numero de identficacion al IVA de la sede social del vendedor falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0025', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN  del vendedor falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0024', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN del socio facturado  falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0002', 'es', 'Importes ventilacion IVA : El importe del impuesto falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0001', 'es', 'Importes ventilacion IVA : Las informaciones de importe de impuesto faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0004', 'es', 'Importes ventilacion IVA : El importe del impuesto no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0003', 'es', 'Importes ventilacion IVA : El importe de impuesto esta transmitido en doblon sin codigos monedas (un codigo moneda esperado para cada importe){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0002', 'es', 'Recapitulativo tasa parafiscal: El importe de tasa falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0006', 'es', 'Importes ventilacion IVA : El importe imponible falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0001', 'es', 'Recapitulativo tasa parafiscal: Las informaciones del importe de tasa faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0005', 'es', 'Importes ventilacion IVA : Un importe identico sin corresponder a un importe de impuesto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0008', 'es', 'Importes ventilacion IVA : El importe del impuesto falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0007', 'es', 'Importes ventilacion IVA : El importe imponible no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0005', 'es', 'Reducciones-cargos linea : El calificativo de reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0016', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0004', 'es', 'Reducciones-cargos linea : El porcentaje y el importe de carga faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0015', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN del socio facturado  esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0004', 'es', 'Recapitulativo tasa parafiscal: El importe de tasa no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0003', 'es', 'Reducciones-cargos linea : El porcentaje y el importe de reduccion faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0018', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN del vendedor esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0003', 'es', 'Recapitulativo tasa parafiscal: El importe de tasa esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0002', 'es', 'Reducciones-cargos linea : El 1ero elemento del intitulado de la reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0001', 'es', 'Reducciones-cargos linea : el codigo pago de la reduccion/carga es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0019', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0010', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0007', 'es', 'Precio : El precio bruto sin IVA no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0012', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del declarante del IVA esta tansmitiod en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0008', 'es', 'Precio : La base del precio neto sin IVA no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0011', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del declarante de IVA falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0009', 'es', 'Precio : La unidad de medida del precio neto sin IVA es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0014', 'es', 'Recuerdo referencias dirrecciones : El codigo SIREN y el numero de identificacion al IVA del socio facturado  francés faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0013', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del declarante de IVA es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0003', 'es', 'Precio : El precio neto sin IVA esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0004', 'es', 'Precio : El precio neto sin IVA no es numerico{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005', 'es', 'Precio : El precio bruto sin IVA falta aun que una entrega/carga de linea es presente{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0002', 'es', 'Encabezamiento mensaje : La identificacion del tipo de mensaje es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0006', 'es', 'Precio : El precio bruto sin IVA esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0001', 'es', 'Encabezamiento mensaje : El nuemro de referencia del mensaje es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0001', 'es', 'Precio : Las informaciones de precio faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0002', 'es', 'Precio : el precio neto sin IVA Falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0004', 'es', 'Encabezamiento mensaje : El numero de edicion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0003', 'es', 'Encabezamiento mensaje : El numero de version del mensaje es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACodeComplementaireProduit0001', 'es', 'Codigo interno detalle : el codigo articulo interno esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0006', 'es', 'Encabezamiento mensaje : El codigo atribuido por la asociacion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0005', 'es', 'Encabezamiento mensaje : La agencia de control es incorrecta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0005', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del socio facturado  fuera Francia falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0004', 'es', 'Recuerdo referencias dirrecciones : El numero de matriculacion al RCS-RCM del vendedor es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0007', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del socio facturado  es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0006', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del socio facturado  esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0012', 'es', 'Reducciones-cargos linea : El importe de reduccion falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0009', 'es', 'Recuerdo referencias dirrecciones : El numero de identificacion al IVA del vendedor esta transmitido en doblon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0011', 'es', 'Reducciones-cargos linea : El porcentaje de reduccion falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0008', 'es', 'Recuerdo referencias dirrecciones : El numero de matriculacion al IVA del vendedor falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0010', 'es', 'Reducciones-cargos linea : El codigo del servicio particular de la reduccion es incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004b', 'es', 'Indemnizaciones/cargos globales: Falta el tipo impositivo{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0006', 'es', 'Línea derechos/cargos: El importe del cargo está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0005', 'es', 'Línea Descuento-Cargo: El importe del descuento está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0007', 'es', 'Más de un porcentaje para la carga global (PCD+2){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0006', 'es', 'Más de un porcentaje para la indemnización global (PCD+1){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0014', 'es', 'Cabecera de intercambio: El tiempo de preparación está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0010', 'es', 'Condiciones de pago: Las condiciones de descuento se transmiten por duplicado como código{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0027', 'es', 'Importes totales: Falta el importe total del depósito mientras que se transmite el importe debido{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0009', 'es', 'Condiciones de pago: Las condiciones de penalización se transmiten por duplicado como código{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005bis', 'es', 'El precio: Falta el precio bruto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004b', 'es', 'Desglose del IVA: Falta la base imponible{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0004', 'es', 'Descuentos/cargos de línea: El porcentaje de carga está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0014', 'es', 'Divisa: El calificador de detalles de la moneda de destino está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004b', 'es', 'Línea IVA: Falta el tipo impositivo{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0006', 'es', 'Descuentos/cargos globales: El importe del cargo está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0005', 'es', 'Dietas/cargos globales: El importe de la indemnización está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0006', 'es', 'Desglose del IVA: Falta el tipo impositivo{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0004', 'es', 'Descuentos/cargos globales: El porcentaje de carga está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0003', 'es', 'Dietas/cargos globales: El porcentaje de derechos está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0018', 'es', 'Observaciones (texto libre): La tasa de penalización está duplicada{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0008', 'es', 'Seguramente faltarán datos de derechos/cargos de línea (un segmento PCD sigue directamente a un segmento TAX en gro{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0007', 'es', 'Más de un porcentaje de gastos de línea (PCD+2){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0006', 'es', 'Más de un porcentaje para la indemnización por línea (PCD+1){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'es', 'MOA+125 (g48) esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'es', 'eC082.3039 del NAD+IV esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+LC esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_FORMAT_EMPTY', 'es', 'El formato de la fecha esta vacio. Forzamos la fecha a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_C', 'es', 'Un segmento MOA+204 (g25) no puede seguir un segmento ALC+C{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_RE_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+RE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_A', 'es', 'Un segmento MOA+23 (g25° no puede seguir un segmento ALC+A{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+BY esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_EMPTY', 'es', 'eC186.6060 del segmento QTY+47 g25 seg esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY52_g25_EMPTY', 'es', 'eC186.6060 del segmento QTY+52 g25 seg esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_IDENT_EMPTY', 'es', 'eC082.3039 del NAD+RE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+SE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+BY esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MISSING', 'es', 'El segmento BGM falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+BY esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'es', 'Mas de un segmento MOA g15 después del segmento ALC g15. (Calificativo :8){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+IV esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_g15_EMPTY', 'es', 'eC501.5482 en el segmento PCD g15 esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PERIOD_NOT_WELLFORMATTED', 'es', 'El periodo no es ta correctamente formatado. Forzamos la fecha a 1970-01-01T00:00:0{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MISSING', 'es', 'El segemento NAD+SE FALTA{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_IV_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+IV esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'es', 'eC516.5004 en MOA+113 g48 esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+RE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+CO esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'es', 'Mas de un DTM+137{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+SE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_VALID', 'es', 'La fecha no es valida. Forzamos la fecha a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_g25_EMPTY', 'es', 'eC516.5004 en el segmento MOA g25 esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'es', 'Mas de un segmento MOA+124 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0002', 'es', 'El fichero MENSAJE no existe{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PRI_MISSING', 'es', 'PRI+AAA y PRI+AAB (g25) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0003', 'es', 'El fichero MENSAJE tiene un tamano 0{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+CO esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0004', 'es', 'Imposible de leer el fichero MENSAJE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FTX_REG_MORE_THAN_ONE', 'es', 'Mas de un FTX+REG.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_DESC_MISSING', 'es', 'La descripcion del ALC falta  (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+SE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+IV esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+RE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0009', 'es', 'Un unico UNH esperado en el fichero MENSAJE.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'es', 'El segmento DTM+171 falta después de un segmento MOA+113 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+DL esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_A', 'es', 'Un PCD+2 no puede seguir un ALC+A{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+RE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0005', 'es', 'El nivel de traza no esta definido (LEVEL){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_C', 'es', 'Un PCD+1 no puede seguir un ALC+C{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+BY{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0008', 'es', 'Intercambio EDIFACT invoice D96A requirido.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_NAME_EMPTY', 'es', 'eC080.3036 del NAD+DL esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0012', 'es', 'Un unnico UNB esperado en el fichero MENSAJE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'es', 'MOA+124 (g48) esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'es', 'Mas de un segmetno NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0010', 'es', 'Mensaje EDIFACT incorrecto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0011', 'es', 'UNB requirido en el fichero MENSAJE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_EMPTY', 'es', 'La fecha esta vacia. Forzamos la fecha a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_QUALIFIER_NOT_EXPECTED', 'es', 'El calificativo del PCD(g25) no es correcto después de un ALC+A o ALC+C (g25). Esperado : PCD+1 o PCD+2.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_C', 'es', 'Un PCD+1 no puede seguir un ALC+C{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_A', 'es', 'Un PCD+2 no puede seguir un ALC+A{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_g15_EMPTY', 'es', 'eC516.5004 en el segmento MOA g 15 esta vacio.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+DP{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_LC_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+LC esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_13_g8_MISSING', 'es', 'El segmento DTM+13(g8) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_MORE_THAN_ONE', 'es', 'Mas de un PCD del g15 encontrando después del segmento ALC g15{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_WELLFORMATED', 'es', 'La fecha no esta correctamenteformatada. Forzamos la fecha a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'es', 'eC506.1154 en RFF+PQ esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'es', 'La descripcion para el segemento ALC (g25) falta (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+DL esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_MORE_THAN_ONE', 'es', 'Mas de un segmento MOA+124 g 50{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DL_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+DL esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+CO esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_MISSING', 'es', 'QTY+47 (g25) falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_VALIDATION_EDI', 'es', 'La validacion del mensaje ha fracasdo{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'es', 'Mas de un segmento MOA+128 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_CUX_MORE_THAN_ONE', 'es', 'Mas de un segmento CUX (g7){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_g15_PCD_MOA_EMPTY', 'es', 'MOA+ 8 (g 15) o PCD+1/2 (g15) faltan{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'es', 'Mas de un segmento QTY+47 (g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_IDENT_EMPTY', 'es', 'eC082.3039 del NAD+DP esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_BY_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+BY esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'es', 'eC516.5004 en MOA+ 9g 48 esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+DL{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_MOA204_MISSING', 'es', 'MOA+204 del segmento ALC falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA  para NAD+SE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_IDENT_EMPTY', 'es', 'eC082.3039 del NAD+DL esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'es', 'eC082.3039 del NAD+SE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'es', 'Mas de un segmento PCD g25 después del segmento ALCg25{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_QUALIFIER_NOT_EXPECTED', 'es', 'El calificativo del PCD no es correcto después de un ALC+A o ALC+C. Esperado : PCD+1 o PCD+2.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+LC esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_IDENT_EMPTY', 'es', 'eC082.3039 del NAD+BY esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_NAME_EMPTY', 'es', 'eC080.3036.1 del NAD+DP esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'es', 'Mas de un segmento MOA g25 después del segmento ALC g 25 (Calificativo :204 o 23){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+RE esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'es', 'DTM+137 falta{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'es', 'El segmento RFF+PQ falta después de un segmento MOA+113 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+LC esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'es', 'Mas de un segmento NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'es', 'MOA+128 (g48) esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'es', 'Mas de un segmento MOA+125 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FORMAT_NOT_MANAGED', 'es', 'El formato de la fecha no ha sido gestionado.Forzamos la fecha a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+CO esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA  para NAD+IV esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_g25_EMPTY', 'es', 'eC501.5482 en el segmento PCD g25 esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_EMPTY', 'es', 'MOA+124 (g48) esta vacio{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_MORE_THAN_ONE', 'es', 'Más de un número de registro (RFF+XA) para el vendedor (NAD+SE){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_MORE_THAN_ONE', 'es', 'Más de un código IVA (RFF+VA) para la sede del vendedor (NAD+CO){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_MORE_THAN_ONE', 'es', 'Más de un código de IVA (RFF+VA) para el representante fiscal del IVA (NAD+LC){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_MORE_THAN_ONE', 'es', 'Más de un RFF+VA para NAD+DL{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TOD_4055', 'es', 'No hay código de condiciones de entrega en el segmento TOD que se encuentra en el mensaje{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_MORE_THAN_ONE', 'es', 'Más de un RFF+GN para NAD+RE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+VA para NAD+DP está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_NAME_EMPTY', 'es', 'El primer elemento del nombre ShipFrom (eC080.3036.1 en NAD+SF) está vacío.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_MORE_THAN_ONE', 'es', 'Más de un RFF+GN para NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_1004_EMPTY', 'es', 'Elemento e1004 del BGM esta vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_MORE_THAN_ONE', 'es', 'Más de un RFF+GN por NAD+BY{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_MORE_THAN_ONE', 'es', 'Más de un RFF+VA para NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+GN para NAD+DP está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_MORE_THAN_ONE', 'es', 'Más de un RFF+VA para NAD+RE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_MORE_THAN_ONE', 'es', 'Más de un RFF+GN para NAD+DP{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DP_IDENT_EMPTY', 'es', 'eC506.1154 en RFF+XA para NAD+DP está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_MORE_THAN_ONE', 'es', 'Más de un RFF+VA para NAD+BY{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TAX_7_VAT_E_NO_FTXSIN', 'es', 'TAX+7+IVA+++++E sin FTX+SIN{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_MORE_THAN_ONE', 'es', 'Más de un RFF+VA para NAD+DP{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_MORE_THAN_ONE', 'es', 'Más de un RFF+GN para NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_MORE_THAN_ONE', 'es', 'Más de un código IVA (RFF+VA) para el destinatario de la factura (NAD+IV){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_MORE_THAN_ONE', 'es', 'Más de un RFF+GN por NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_MORE_THAN_ONE', 'es', 'Más de un código IVA (RFF+VA) para el vendedor (NAD+SE){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_MORE_THAN_ONE', 'es', 'Más de un RFF+GN para NAD+L{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_MORE_THAN_ONE', 'es', 'Más de un número de registro (RFF+XA) para la sede del vendedor (NAD+CO){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM1225', 'es', 'Código de función de mensaje desconocido (segmento BGM){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_FORMAT_NOT_MANAGED', 'es', 'Fecha factura : Formato no gestionado. Forzado a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_A_REGLMT_CODE', 'es', 'El código del medio de liquidación (e4471 ALC+A(g15)) debe ser 1 ó 2{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_C_REGLMT_CODE', 'es', 'El código del medio de liquidación (e4471 ALC+C(g15)) debe ser 1 ó 2{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED', 'es', 'Fecha de la factura : La fecha no está formateada correctamente. Forzado a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_g25_PCD_MOA_EMPTY', 'es', 'Falta MOA+204(g25) o MOA+23(g25) o PCD+1(g25) o PCD+2(g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MORE_THAN_ONE', 'es', 'Comienzo del mensaje : Se ha encontrado más de un segmento BGM{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_A_REGLMT_CODE', 'es', 'El código del medio de liquidación (e4471 ALC+A(g38)) debe ser 1 ó 2{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_NOT_VALID', 'es', 'Fecha de la factura : Fecha no válida. Forzado a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_C_REGLMT_CODE', 'es', 'El código del medio de liquidación (e4471 ALC+C(g38)) debe ser 1 ó 2{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_IDENT_EMPTY', 'es', 'El código de identificación ShipFrom (eC082.3039 en NAD+SF) está vacío{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_EMPTY', 'es', 'Fecha de facturación : Fecha vacía. Forzado a 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_FORMAT_EMPTY', 'es', 'Formato de fecha vacío para la fecha de facturación. Forzado a 1970-01-\n01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_MORE_THAN_ONE', 'es', 'Más de un segmento sobre el ShipFrom (NAD+SF){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'fi', 'Parafiscal tax breakdown: The allowance/charge qualifier is incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'fi', 'Allowance-Charge line: The discount percentage is not a number{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0001', 'fr', 'MESSAGE argument n''est pas defini {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0006', 'fr', 'Le message ne contient pas de segment UNB ou UNH {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0007', 'fr', 'L''interchange contient plusieurs messages {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACodeComplementaireProduit0001', 'fr', 'Le code article interne est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0001', 'fr', 'Les commentaires d''entête du document sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0002', 'fr', 'Les informations réglementaires du vendeur sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0003', 'fr', 'Les informations réglementaires du vendeur sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0004', 'fr', 'La dénomination sociale du vendeur est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0005', 'fr', 'La forme juridique du vendeur est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0006', 'fr', 'Le capital social et la devise du vendeur sont incorrects {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0007', 'fr', 'Le texte décrivant les conditions d''escompte conditionnel est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0008', 'fr', 'Le 1er élément du texte décrivant les conditions d''escompte conditionnel est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0009', 'fr', 'Le texte décrivant les conditions de pénalité est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0010', 'fr', 'Le 1er élément du texte décrivant les conditions de pénalité est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0011', 'fr', 'Il existe une incohérence entre le commentaire décrivant le motif de l''exonération de taxe et le code d''exonération de taxe en entête du document {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0012', 'fr', 'Le 1er élément du texte décrivant la raison de l''exonération de taxe en entête du document est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0013', 'fr', 'L''indemnité forfaitaire en cas de retard de paiement est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0014', 'fr', 'Les informations relatives à la nature de la TVA et de l''escompte du vendeur sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0015', 'fr', 'Le libellé du regime de TVA du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0016', 'fr', 'Le libellé des conditions d''escompte du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0017', 'fr', 'Le RCS-RCM et la ville du vendeur sont incorrects {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0018', 'fr', 'L''indemnité forfaitaire en cas de retard de paiement est transmise en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentairesLigne0001', 'fr', 'Le 1er élément du texte décrivant la raison de l''exonération de taxe à la ligne article est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0001', 'fr', 'Les informations de conditions de paiement sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0002', 'fr', 'Les informations de date d''échéance sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0003', 'fr', 'Les informations de conditions d''escompte conditionnel sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0004', 'fr', 'Les informations de conditions de pénalité sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0005', 'fr', 'Les conditions d''escompte conditionnel sont manquantes en texte et en code {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0006', 'fr', 'Les conditions d''escompte conditionnel sont transmises en texte et en code (il ne peut exister qu''1 seule modélisation) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0007', 'fr', 'Les conditions de pénalité sont manquantes en texte et en code {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0008', 'fr', 'Les conditions de pénalité sont transmises en texte et en code (il ne peut exister qu''1 seule modélisation) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0009', 'fr', 'Les conditions d''escompte conditionnel sont transmises en doublon sous forme codée {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0010', 'fr', 'Les conditions de pénalité sont transmises en doublon sous forme codée {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0001', 'fr', 'Le qualifiant de date/heure/période du document en référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0002', 'fr', 'La période de validité n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0003', 'fr', 'Le qualifiant du format de la période de validité est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0004', 'fr', 'La longueur de la période de validité du contrat est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0001', 'fr', 'La date/heure du BL/avis d''expédition en référence est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0002', 'fr', 'La date/heure du BL/avis d''expédition en référence n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0001', 'fr', 'Le qualifiant de date/heure/période est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0002', 'fr', 'La date/heure du document est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0003', 'fr', 'La date/heure du document est transmise en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0004', 'fr', 'La date/heure du document n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0005', 'fr', 'Le qualifiant du format de la date/heure du document est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0006', 'fr', 'La longueur de la date/heure du document est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0007', 'fr', 'La date/heure d''expédition n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0008', 'fr', 'La date/heure d''enlevement n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0009', 'fr', 'La date/heure reelle de livraison n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0010', 'fr', 'La période de facturation n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0011', 'fr', 'Le qualifiant du format de la période de facturation est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0012', 'fr', 'La longueur de la période de facturation est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0013', 'fr', 'Les informations de dates/heures/periodes d''entête du document sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0014', 'fr', 'L''identification du mois dans la date du document est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0015', 'fr', 'L''identification du jour dans la date du document est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0016', 'fr', 'Il existe une incohérence entre les identifications du jour et du mois de la date du document {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0017', 'fr', 'La date du document est postérieure à la date du jour {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0001', 'fr', 'Le qualifiant de date/heure/période à la ligne est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0002', 'fr', 'La date/heure d''expédition à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0003', 'fr', 'La date/heure d''enlevement à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0004', 'fr', 'La date/heure reelle de livraison à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0001', 'fr', 'La date des conditions de paiement est transmise en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0002', 'fr', 'Le qualifiant de date/heure des conditions de paiement est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0003', 'fr', 'La date d''échéance est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0004', 'fr', 'La date d''échéance n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0005', 'fr', 'La date de réglement est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0006', 'fr', 'La date de réglement n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0001', 'fr', 'Le qualifiant de date/heure de la référence du montant d''acompte est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0002', 'fr', 'La date d''acompte est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0003', 'fr', 'La date d''acompte n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0001', 'fr', 'Les informations de description de l''objet sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0002', 'fr', 'Le code du type de description de l''objet est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0003', 'fr', 'Le code de description du libellé de l''article est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0004', 'fr', 'Le 1er élément du libellé article est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0005', 'fr', 'Le 1er élément du libellé article est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0002', 'fr', 'Le qualifiant de l''utilisation de la devise de référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0003', 'fr', 'Le qualifiant de la devise de référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0004', 'fr', 'Le code de la devise de référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0005', 'fr', 'Le code de la devise de référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0006', 'fr', 'Le qualifiant de l''utilisation de la devise cible est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0007', 'fr', 'Le qualifiant de la devise cible est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0008', 'fr', 'Le code de la devise cible est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0009', 'fr', 'Le code de la devise cible est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0010', 'fr', 'Les informations de devise sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0011', 'fr', 'La devise de référence est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0012', 'fr', 'Le qualifiant de la devise de référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0013', 'fr', 'Le code de la devise de facturation est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0014', 'fr', 'Le qualifiant de l''utilisation de la devise cible est absent {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0001', 'fr', 'L''identification de l''emetteur de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0002', 'fr', 'L''identification du destinataire de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0003', 'fr', 'La date de préparation de l''interchange n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0004', 'fr', 'La date de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0005', 'fr', 'L''identification des années dans la date de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0006', 'fr', 'L''identification du mois dans la date de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0007', 'fr', 'L''identification du jour dans la date de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0008', 'fr', 'Il existe une incohérence entre les identifications du jour et du mois de la date de préparation de l''interchange {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0009', 'fr', 'L''heure de préparation de l''interchange n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0010', 'fr', 'La longueur de l''heure de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0011', 'fr', 'L''identification des heures dans l''heure de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0012', 'fr', 'L''identification des minutes dans l''heure de préparation de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0013', 'fr', 'La référence de controle de l''interchange est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0014', 'fr', 'L''heure de préparation de l''interchange est absente{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0015', 'fr', 'La date de préparation de l''interchange est absente{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AFindeSection0001', 'fr', 'Fin de section : Le code d''identification de section est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0001', 'fr', 'Le code du document est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0003', 'fr', 'Le numéro du document est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0004', 'fr', 'Le code fonction du message est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0001', 'fr', 'Les informations relatives aux partenaires sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0002', 'fr', 'Les informations du partenaire facturé sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0003', 'fr', 'Les informations du partenaire facturé sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0004', 'fr', 'Le code d''identification du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0005', 'fr', 'Le 1er élément de la dénomination sociale du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0006', 'fr', 'Le 1er élément de l''adresse du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0007', 'fr', 'Le nom de la localité du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0008', 'fr', 'Le code postal du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0009', 'fr', 'Le code pays du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0010', 'fr', 'Les informations du vendeur sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0011', 'fr', 'Les informations du vendeur sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0012', 'fr', 'Le code d''identification du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0013', 'fr', 'Le 1er élément de la dénomination sociale du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0014', 'fr', 'Le 1er élément de l''adresse du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0015', 'fr', 'Le nom de la localité du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0016', 'fr', 'Le code postal du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0017', 'fr', 'Le code pays du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0018', 'fr', 'Les informations du siège social du vendeur sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0019', 'fr', 'Le 1er élément de la dénomination sociale du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0020', 'fr', 'Le 1er élément de l''adresse du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0021', 'fr', 'Le nom de la localité du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0022', 'fr', 'Le code postal du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0023', 'fr', 'Le code pays du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0024', 'fr', 'Les informations du déclarant de TVA sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0025', 'fr', 'Le 1er élément de la dénomination sociale du déclarant de TVA est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0026', 'fr', 'Le 1er élément de l''adresse du déclarant de TVA est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0027', 'fr', 'Le nom de la localité du déclarant de TVA est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0028', 'fr', 'Le code postal du déclarant de TVA est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0029', 'fr', 'Le code pays du déclarant de TVA est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0030', 'fr', 'Les informations du regler à sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0031', 'fr', 'L''identification de l''organisme responsable de la liste de code (type de code) du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0032', 'fr', 'Le 1er élément du nom et adresse du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0033', 'fr', 'Le 2eme élément du nom et adresse du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0034', 'fr', 'Les informations de l''acheteur sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0035', 'fr', 'Le code d''identification de l''acheteur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0036', 'fr', 'L''identification de l''organisme responsable de la liste de code (type de code) de l''acheteur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0037', 'fr', 'Le 1er élément du nom et adresse de l''acheteur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0038', 'fr', 'Le 2eme élément du nom et adresse de l''acheteur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0039', 'fr', 'Le 1er élément de la dénomination sociale de l''acheteur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ALigne0001', 'fr', 'Le numéro de ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0001', 'fr', 'Le montant de réduction à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0002', 'fr', 'Le montant de charge à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0003', 'fr', 'Le montant de réduction à la ligne est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0004', 'fr', 'Le montant de charge à la ligne est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0005', 'fr', 'Le montant de réduction à la ligne est absent{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0006', 'fr', 'Le montant de charge à la ligne est absent {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0001', 'fr', 'Le montant de réduction globale n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0002', 'fr', 'Le montant de charge globale n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0003', 'fr', 'Le montant de réduction globale est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0004', 'fr', 'Le montant de charge globale est transmis en doublon{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0005', 'fr', 'Le montant de réduction globale est absent{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0006', 'fr', 'Le montant de charge globale est absent  {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontants0002', 'fr', 'Le montant d''escompte conditionnel n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0001', 'fr', 'Récapitulatif taxe parafiscale : Les informations de montant de taxe sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0002', 'fr', 'Récapitulatif taxe parafiscale : Le montant de taxe est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0003', 'fr', 'Récapitulatif taxe parafiscale : Le montant de taxe est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0004', 'fr', 'Récapitulatif taxe parafiscale : Le montant de taxe n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0001', 'fr', 'Les informations de montants totaux sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0002', 'fr', 'Le montant total HT est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0003', 'fr', 'Le montant total HT est transmis au moins en triple, ou en double sans codes monnaies (1 code monnaie attendu pour chaque montant) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0004', 'fr', 'Le montant total HT n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0005', 'fr', 'Le montant total TVA est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0006', 'fr', 'Le second montant total TVA exprime dans la monnaie cible est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0007', 'fr', 'Le montant total TVA est transmis en doublon alors qu''aucune monnaie cible n''est présente {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0008', 'fr', 'Le montant total TVA n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0009', 'fr', 'Le montant total TTC est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0010', 'fr', 'Le montant total TTC est transmis au moins en triple, ou en double sans codes monnaies (1 code monnaie attendu pour chaque montant) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0011', 'fr', 'Le montant total TTC n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0012', 'fr', 'La référence d''un acompte est manquante pour le montant total d''acompte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0014', 'fr', 'Le montant total d''acompte n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0015', 'fr', 'Le montant total net à payer est manquant alors que le montant total d''acompte est transmis {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0017', 'fr', 'Le montant total net à payer n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0018', 'fr', 'Le montant total TVA est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0019', 'fr', 'Le montant total TVA n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0020', 'fr', 'Le montant total TTC est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0021', 'fr', 'Le montant total TTC n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0022', 'fr', 'Le montant total HT des lignes article est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0023', 'fr', 'Le montant total HT des lignes articles n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0024', 'fr', 'Le montant d''escompte n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0025', 'fr', 'Le montant total TVA est transmis au moins en triple, ou en double sans codes monnaies (1 code monnaie attendu pour chaque montant) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0027', 'fr', 'Le montant total d''acompte est manquant alors que le montant total net à payer est transmis {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0001', 'fr', 'Ventilation TVA : Les informations de montant de taxe sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0002', 'fr', 'Ventilation TVA : Le montant de taxe est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0003', 'fr', 'Ventilation TVA : Le montant de taxe est transmis en double {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0004', 'fr', 'Ventilation TVA : Le montant de taxe n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0005', 'fr', 'Ventilation TVA : Un montant indiqué ne correspondant pas à un montant de taxe {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0006', 'fr', 'Ventilation TVA : Le montant taxable est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0007', 'fr', 'Ventilation TVA : Le montant taxable n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0008', 'fr', 'Ventilation TVA : Le montant de taxe est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantTaxeLigne0001', 'fr', 'L''information de charge de ligne (ALC+C) pere du montant de charge MOA+23 est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0001', 'fr', 'Le pourcentage de réduction à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0002', 'fr', 'Le pourcentage de charge à la ligne n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'fr', 'Le pourcentage de réduction à la ligne (PCD+1) est absent{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0004', 'fr', 'Le pourcentage de charge à la ligne (PCD+2) est absent{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0001', 'fr', 'Le pourcentage de réduction globale n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0002', 'fr', 'Le pourcentage de charge globale n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0003', 'fr', 'Le pourcentage de réduction globale (PCD+1) est absent{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0004', 'fr', 'Le pourcentage de charge globale (PCD+2) est absent{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0002', 'fr', 'Le pourcentage d''escompte conditionnel n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0003', 'fr', 'Le pourcentage de pénalité n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0004', 'fr', 'Il existe une incohérence entre l''indicateur d''escompte conditionnel et le type de pourcentage {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0005', 'fr', 'Il existe une incohérence entre l''indicateur de pénalité et le type de pourcentage {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0001', 'fr', 'Les informations de prix sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0002', 'fr', 'Le prix net HT est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0003', 'fr', 'Le prix net HT est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0004', 'fr', 'Le prix net HT n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005', 'fr', 'Le prix brut HT est manquant alors qu''une remise/charge de ligne est présente {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005bis', 'fr', 'Le prix brut HT est manquant{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0006', 'fr', 'Le prix brut HT est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0007', 'fr', 'Le prix brut HT n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0008', 'fr', 'La base du prix net HT n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0009', 'fr', 'L''unité de mesure du prix net HT est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0001', 'fr', 'Les informations de quantité sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0002', 'fr', 'Le qualifiant de quantité est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0003', 'fr', 'Le qualifiant de quantité facturée est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0004', 'fr', 'Le qualifiant de quantité facturée est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0005', 'fr', 'La quantité facturée n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0006', 'fr', 'L''unité de mesure de la quantité facturée est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'fr', 'Récapitulatif taxe parafiscale : Le qualifiant de charge est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0003', 'fr', 'Récapitulatif taxe parafiscale : Le 1er élément du libellé de la taxe parafiscale est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0001', 'fr', 'Le code réglement de la réduction/charge globale est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0002', 'fr', 'Le 1er élément du libellé de la réduction/charge globale est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0003', 'fr', 'Le pourcentage et le montant de réduction globale sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0004', 'fr', 'Le pourcentage et le montant de charge globale sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0005', 'fr', 'Le qualifiant de réduction/charge globale est incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0006', 'fr', 'Doublon de pourcentage de réduction globale (PCD+1) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0007', 'fr', 'Doublon de pourcentage de charge globale (PCD+2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0001', 'fr', 'Le code réglement de la réduction/charge à la ligne est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0002', 'fr', 'Le 1er élément du libellé de la réduction/charge à la ligne est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0003', 'fr', 'Le pourcentage et le montant de réduction à la ligne sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0004', 'fr', 'Le pourcentage et le montant de charge à la ligne sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0005', 'fr', 'Le qualifiant de réduction/charge à la ligne est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0006', 'fr', 'Doublon de pourcentage de réduction à la ligne (PCD+1) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0007', 'fr', 'Doublon de pourcentage de charge à la ligne (PCD+2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0008', 'fr', 'Absence probable de réduction/charge à la ligne (présence d''un segment PCD suivant un segment TAX, g33) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0010', 'fr', 'Le code du service particulier de la réduction à la ligne est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0011', 'fr', 'Le pourcentage de réduction à la ligne est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0012', 'fr', 'Le montant de réduction à la ligne est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0001', 'fr', 'Le numéro de facture et la période de validité/facturation (document lui-meme ou contrat) en référence dans l''avoir sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0002', 'fr', 'Le numéro de facture en référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0003', 'fr', 'Le numéro de contrat en référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0001', 'fr', 'Le numéro de BL/avis d''expédition en référence à la ligne est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0002', 'fr', 'Le numéro de BL/avis d''expédition en référence à la ligne est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0001', 'fr', 'Le qualifiant de la référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0002', 'fr', 'Le numéro d''immatriculation au RCS-RCM du vendeur est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0003', 'fr', 'Le numéro d''immatriculation au RCS-RCM du vendeur est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0004', 'fr', 'Le numéro d''immatriculation au RCS-RCM du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0005', 'fr', 'Le numéro d''identification à la TVA du partenaire facturé hors France est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0006', 'fr', 'Le numéro d''identification à la TVA du partenaire facturé est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0007', 'fr', 'Le numéro d''identification à la TVA du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0008', 'fr', 'Le numéro d''identification à la TVA du vendeur est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0009', 'fr', 'Le numéro d''identification à la TVA du vendeur est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0010', 'fr', 'Le numéro d''identification à la TVA du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0011', 'fr', 'Le numéro d''identification à la TVA du déclarant de TVA est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0012', 'fr', 'Le numéro d''identification à la TVA du déclarant de TVA est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0013', 'fr', 'Le numéro d''identification à la TVA du déclarant de TVA est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0014', 'fr', 'Le code SIREN et le numéro d''identification à la TVA du partenaire facturé francais sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0015', 'fr', 'Le code SIREN du partenaire facturé est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0016', 'fr', 'Le code SIREN du partenaire facturé est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0018', 'fr', 'Le code SIREN du vendeur est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0019', 'fr', 'Le code SIREN du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0020', 'fr', 'Le numéro d''immatriculation au RCS-RCM du siège social du vendeur est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0021', 'fr', 'Le numéro d''immatriculation au RCS-RCM du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0022', 'fr', 'Le numéro d''identification à la TVA du siège social du vendeur est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0023', 'fr', 'Le numéro d''identification à la TVA du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0024', 'fr', 'Le code SIREN du partenaire facturé francais est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0025', 'fr', 'Le code SIREN du vendeur est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0026', 'fr', 'Le code SIREN du siège social du vendeur est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0027', 'fr', 'Le code SIREN du siège social du vendeur est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0028', 'fr', 'Le numéro d''identification à la TVA du partenaire facturé est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesMontantsTotaux0001', 'fr', 'Le numéro d''acompte en référence est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARegimesTva0001', 'fr', 'Il existe une incohérence entre le code d''exonération de taxe et le commentaire d''entête décrivant le motif de l''exonération de taxe {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0001', 'fr', 'Réductions-Charges globales : Les informations de taxe sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0002', 'fr', 'Réductions-Charges globales : Le qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0003', 'fr', 'Réductions-Charges globales : Le code du type de taxe [VAT] associé au qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004', 'fr', 'Réductions-Charges globales : Le taux de taxe n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004b', 'fr', 'Réductions-Charges globales : Le taux de taxe est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0008', 'fr', 'Réductions-Charges globales : Le taux de taxe est manquant dans la ventilation de taxe en pied de message {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0001', 'fr', 'TVA ligne : Les informations de taxe sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0002', 'fr', 'TVA ligne : Le qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0003', 'fr', 'TVA ligne : Le code du type de taxe [VAT] associé au qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004', 'fr', 'TVA ligne : Le taux de taxe n''est pas numérique{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004b', 'fr', 'TVA ligne : Le taux de taxe est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0007', 'fr', 'TVA ligne : Les informations de taxe sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0008', 'fr', 'TVA ligne : Le taux de taxe est manquant dans la ventilation de taxe en pied de message {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0009', 'fr', 'TVA ligne : Le code de la categorie de taxe est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0001', 'fr', 'Réductions-Charges ligne : Le qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0002', 'fr', 'Réductions-Charges ligne : Le code du type de taxe [VAT] associé au qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0003', 'fr', 'Réductions-Charges ligne : Le taux de taxe n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0005', 'fr', 'Réductions-Charges ligne : Les informations de taxe sont transmises en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0006', 'fr', 'Réductions-Charges ligne : Le taux de taxe est manquant dans la ventilation de taxe en pied de message {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0001', 'fr', 'Entête message : Le numéro de référence du message est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0002', 'fr', 'Entête message : L''identification du type de message est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0003', 'fr', 'Entête message : Le numéro de version du message est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0004', 'fr', 'Entête message : Le numéro d''édition est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0005', 'fr', 'Entête message : L''agence de controle est incorrecte {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0006', 'fr', 'Entête message : Le code attribue par l''association est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0001', 'fr', 'Ventilation TVA : Les informations de taxe sont manquantes {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0002', 'fr', 'Ventilation TVA : Le qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0003', 'fr', 'Ventilation TVA : Le code du type de taxe [VAT] associé au qualifiant de la fonction de taxe [7] est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004', 'fr', 'Ventilation TVA : Le montant taxable n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004b', 'fr', 'Ventilation TVA : Le montant taxable est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0005', 'fr', 'Ventilation TVA : Le taux de taxe n''est pas numérique {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0006', 'fr', 'Ventilation TVA : Le taux de taxe est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0007', 'fr', 'Ventilation TVA : Le taux de taxe est manquant dans le reste du message {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0008', 'fr', 'Ventilation TVA : Le code de la categorie de taxe est incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0009', 'fr', 'Ventilation TVA : Le taux de taxe est transmis en doublon {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0002', 'fr', 'Le fichier MESSAGE n''existe pas {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0003', 'fr', 'Le fichierMESSAGE a une taille 0 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0004', 'fr', 'Impossible de lire le fichier MESSAGE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0011', 'fr', 'UNB requis dans le fichier MESSAGE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0012', 'fr', 'Un seul UNB attendu dans le fichier MESSAGE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0009', 'fr', 'Un seul UNH attendu dans le fichier MESSAGE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0008', 'fr', 'Interchange EDIFACT Invoice D96A requis {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MORE_THAN_ONE', 'fr', 'Début message : Le segment BGM est présent en double{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_DESC_MISSING', 'fr', 'La desciption de l''ALC est manquante (eC214.7160.1,eC214.7160.2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_MORE_THAN_ONE', 'fr', 'Plus d''un PCD du g15 trouvé après le segment ALC g15 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_QUALIFIER_NOT_EXPECTED', 'fr', 'Le qualifiant du PCD n''est correct après un ALC+A ou ALC+C. Attendu : PCD+1 ou PCD+2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_A', 'fr', 'Un PCD+2 ne peut pas suivre un ALC+A {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_C', 'fr', 'Un PCD+1 ne peut pas suivre un ALC+C {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_g15_EMPTY', 'fr', 'eC501.5482 dans le segment PCD g15 est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'fr', 'Plus d''un montant de réduction/charge globale (segment MOA g15 du segment ALC g15, qualifiant 8) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_g15_EMPTY', 'fr', 'eC516.5004 dans le segment MOA g15 est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'fr', 'Plus d''un montant total de TVA (segment MOA+124 g48) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'fr', 'Le montant total de TVA (MOA+124 g48) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'fr', 'Plus d''un montant total HT (segment MOA+125 g48) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'fr', 'Le montant total HT (MOA+125 g48) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'fr', 'Plus d''un montant total TTC (segment MOA+128 g48) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'fr', 'Le montant total TTC (MOA+128 g48) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_MORE_THAN_ONE', 'fr', 'Plus d''un segment MOA+124 g50 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_EMPTY', 'fr', 'Le montant de taxe de la ventilation de TVA (MOA+124 g50) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_EMPTY', 'fr', 'La quantité facturée (eC186.6060 du segment QTY+47 g25) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY52_g25_EMPTY', 'fr', 'eC186.6060 du le segment QTY+52 g25 seg est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'fr', 'Plus d''une quantité facturé (segment QTY+47 g25) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_MISSING', 'fr', 'QTY+47(g25) est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PRI_MISSING', 'fr', 'PRI+AAA and PRI+AAB (g25) est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_MOA204_MISSING', 'fr', 'MOA+204 du segment ALC est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_g15_PCD_MOA_EMPTY', 'fr', 'MOA+8(g15) ou PCD+1/2(g15) sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'fr', 'Le 1er élément de la dénomination sociale du partenaire facturé (eC080.3036.1 du NAD+IV) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_NAME_EMPTY', 'fr', 'Le 1er élément de la dénomination sociale du livré par (eC080.3036.1 du NAD+SF) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'fr', 'Le 1er élément de la dénomination sociale du vendeur (eC080.3036.1 du NAD+SE) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_NAME_EMPTY', 'fr', 'eC080.3036.1 du NAD+BY est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'fr', 'Le 1er élément de la dénomination sociale du siège social du vendeur (eC080.3036.1 du NAD+CO) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'fr', 'Le 1er élément de la dénomination sociale du représentant fiscal (eC080.3036.1 du NAD+LC) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_NAME_EMPTY', 'fr', 'eC080.3036.1 du NAD+DP est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_NAME_EMPTY', 'fr', 'eC080.3036.1 du NAD+RE est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_NAME_EMPTY', 'fr', 'eC080.3036.1 du NAD+DL est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MISSING', 'fr', 'Le segment NAD+SE est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MISSING', 'fr', 'Le segment BGM est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_13_g8_MISSING', 'fr', 'Le segment DTM+13 (g8) est manquant {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'fr', 'La date de facture est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FORMAT_NOT_MANAGED', 'fr', 'Le format de la date n''est pas géré. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PERIOD_NOT_WELLFORMATTED', 'fr', 'La période n''est pas correctement formattée. On force les dates à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_WELLFORMATED', 'fr', 'La date n''est pas correctement formattée. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_VALID', 'fr', 'La date n''est pas valide. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_EMPTY', 'fr', 'La date est vide. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_FORMAT_EMPTY', 'fr', 'Le format de la date est vide. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_FORMAT_NOT_MANAGED', 'fr', 'Date de la facture: Le format de la date n''est pas géré. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED', 'fr', 'Date de la facture: La date n''est pas correctement formattée. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_NOT_VALID', 'fr', 'Date de la facture: La date n''est pas valide. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_EMPTY', 'fr', 'Date de la facture: La date est vide. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_FORMAT_EMPTY', 'fr', 'Date de la facture: Le format de la date est vide. On force la date à 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_VALIDATION_EDI', 'fr', 'La validation EDI du message a échoué {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'fr', 'Plus d''une date de facture présente {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FTX_REG_MORE_THAN_ONE', 'fr', 'Plus d''un FTX+REG {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'fr', 'Le code d''identification du facturé (eC082.3039 du NAD+IV) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_IDENT_EMPTY', 'fr', 'Le code d''identification du livré par (eC082.3039 du NAD+SF) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_IDENT_EMPTY', 'fr', 'eC082.3039 du NAD+DL est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_IDENT_EMPTY', 'fr', 'eC082.3039 du NAD+RE est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_IDENT_EMPTY', 'fr', 'eC082.3039 du NAD+DP est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_IDENT_EMPTY', 'fr', 'eC082.3039 du NAD+BY est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'fr', 'Le code d''identification du vendeur (eC082.3039 du NAD+SE) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'fr', 'Plus d''un segment concernant le facturé (NAD+IV) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_MORE_THAN_ONE', 'fr', 'Plus d''un segment concernant le livré par (NAD+SF) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'fr', 'Plus d''un segment concernant le vendeur NAD+SE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_MORE_THAN_ONE', 'fr', 'Plus d''un segment NAD+DL {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_MORE_THAN_ONE', 'fr', 'Plus d''un segment NAD+RE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_MORE_THAN_ONE', 'fr', 'Plus d''un segment NAD+DP {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'fr', 'Plus d''un segment concernant le représentant fiscal (NAD+LC) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'fr', 'Plus d''un segment concernant le siège social du vendeur (NAD+CO) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_MORE_THAN_ONE', 'fr', 'Plus d''un segment NAD+BY {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+SE est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_MORE_THAN_ONE', 'fr', 'Plus d''un RCS-RCM (RFF+XA) pour le vendeur (NAD+SE) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'fr', 'Le code SIREN du vendeur (eC506.1154 dans RFF+GN pour le NAD+SE) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+SE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'fr', 'Le numéro d''identification à la TVA du vendeur (eC506.1154 dans RFF+VA pour le NAD+SE) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_MORE_THAN_ONE', 'fr', 'Plus d''un code TVA intracommunautaire (RFF+VA) pour le vendeur (NAD+SE) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_BY_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+BY est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+GN pour le NAD+BY est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+BY {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+VA pour le NAD+BY est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+VA pour le NAD+BY {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_IV_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+IV est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'fr', 'Le code SIREN du partenaire facturé (eC506.1154 dans RFF+GN pour le NAD+IV) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+IV {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'fr', 'Le numéro d''identification à la TVA du facturé (eC506.1154 dans RFF+VA pour le NAD+IV) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_MORE_THAN_ONE', 'fr', 'Plus d''un code TVA intracommunautaire (RFF+VA) pour le facturé (NAD+IV) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+CO est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_MORE_THAN_ONE', 'fr', 'Plus d''un RCS-RCM (RFF+XA) pour le siège social du vendeur (NAD+CO) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'fr', 'Le code SIREN du siège social du vendeur (eC506.1154 dans RFF+GN pour le NAD+CO) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+CO {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'fr', 'Le numéro d''identification à la TVA du siège social du vendeur (eC506.1154 dans RFF+VA pour le NAD+CO) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_MORE_THAN_ONE', 'fr', 'Plus d''un code TVA intracommunautaire (RFF+VA) pour le siège social du vendeur (NAD+CO) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_LC_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+LC est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'fr', 'Le code SIREN du représentant fiscal (eC506.1154 dans RFF+GN pour le NAD+LC) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+LC {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'fr', 'Le numéro d''identification à la TVA du représentant fiscal (eC506.1154 dans RFF+VA pour le NAD+LC) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_MORE_THAN_ONE', 'fr', 'Plus d''un code TVA intracommunautaire (RFF+VA) pour le représentant fiscal (NAD+LC) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_RE_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+RE est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+GN pour le NAD+RE est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+RE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+VA pour le NAD+RE est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+VA pour le NAD+RE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DL_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+DL est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+GN pour le NAD+DL est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+DL {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+VA pour le NAD+DL est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+VA pour le NAD+DL {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DP_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+XA pour le NAD+DP est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+GN pour le NAD+DL est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+GN pour le NAD+DP {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_IDENT_EMPTY', 'fr', 'eC506.1154 dans RFF+VA pour le NAD+DP est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_MORE_THAN_ONE', 'fr', 'Plus d''un RFF+VA pour le NAD+DP {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_CUX_MORE_THAN_ONE', 'fr', 'Plus d''un segment CUX (g7) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'fr', 'Le montant de l''acompte (eC516.5004 dans MOA+113 g48) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'fr', 'Le montant dû (eC516.5004 dans MOA+9 g48) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'fr', 'La référence de l''acompte (eC506.1154 dans RFF+PQ) est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'fr', 'La référence de l''acompte (segment RFF+PQ) est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'fr', 'La date de l''acompte (segment DTM+171) est manquante {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'fr', 'La description pour le segment ALC(g25) est manquante (eC214.7160.1,eC214.7160.2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'fr', 'Doublon de pourcentage de réduction/charge à la ligne (PCD g25 après le segment ALC g25) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_QUALIFIER_NOT_EXPECTED', 'fr', 'Le qualifiant du PCD(g25) n''est pas correct après un ALC+A ou ALC+C (g25). Attendu : PCD+1 ou PCD+2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_A', 'fr', 'Un PCD+2 ne peut pas suivre un ALC+A {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_C', 'fr', 'Un PCD+1 ne peut pas suivre un ALC+C {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_g25_EMPTY', 'fr', 'eC501.5482 dans le segment PCD g25 est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'fr', 'Doublon de montant de réduction/charge à la ligne (segment MOA g25 après le segment ALC g25) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_g25_EMPTY', 'fr', 'eC516.5004 dans le segment MOA g25 est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_A', 'fr', 'Un segment MOA+23 (g25) ne peut pas suivre un segment ALC+A {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_C', 'fr', 'Un segment MOA+204 (g25) ne peut pas suivre un segment ALC+C {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_1004_EMPTY', 'fr', 'L''élément e1004 du BGM est vide {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_g25_PCD_MOA_EMPTY', 'fr', 'MOA+204(g25) ou MOA+23(g25) ou PCD+1(g25) ou PCD+2(g25) sont manquants {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_C_REGLMT_CODE', 'fr', 'Le code du régement de la charge globale (e4471 du ALC+C(g15)) doit être 1 ou 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_A_REGLMT_CODE', 'fr', 'Le code du régement de la réduction globale (e4471 du ALC+A(g15)) doit être 1 ou 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_C_REGLMT_CODE', 'fr', 'Le code du régement de la charge globale (e4471 de ALC+C(g38)) doit être 1 ou 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_A_REGLMT_CODE', 'fr', 'Le code du régement de la réduction de ligne (e4471 de ALC+A(g38)) doit être 1 ou 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TAX_7_VAT_E_NO_FTXSIN', 'fr', 'TAX+7+VAT++++E sans FTX+SIN {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TOD_4055', 'fr', 'Conditions de livraison (en code) absente malgré la présence du segment TOD{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM1225', 'fr', 'Le code de fonction du message est inconnu (segment BGM){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0010', 'fr', 'L''argument FILENAME n''est pas défini{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0005', 'fr', 'L''argument LEVEL n''est pas défini{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0001', 'it', 'Il Messaggio non è definito{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0010', 'it', 'Messaggio EDI incorretto{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0011', 'nl', 'Documentdatums: De bepaling van het formaat van de facturatieperiode is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0010', 'nl', 'Documentdatums: De facturatieperiode is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0001', 'nl', 'Rappel adresreferenties: De bepaling van de referentie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0003', 'nl', 'Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de verkoper werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0002', 'nl', 'Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0017', 'nl', 'Documentdatums: De documentdatum is groter dan de datum van vandaag{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0016', 'nl', 'Documentdatums: Er bestaat een incoherentie tussen de identificaties van de dag en de maand van de datum van het document{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0015', 'nl', 'Documentdatums: De identificatie van de dag in de datum van het document is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0014', 'nl', 'Documentdatums: De identificatie van de maand in de datum van het document is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0013', 'nl', 'Documentdatums: De info over de datums/uren/periodes op het briefhoofd van het document ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0012', 'nl', 'Documentdatums: De lengte van de facturatieperiode is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0009', 'nl', 'Documentdatums: De werkelijke datum/het werkelijke uur van de levering is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontants0002', 'nl', 'Betalingsvoorwaarden: Het bedrag van de voorwaardelijke discontering is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0008', 'nl', 'Documentdatums: De datum/het uur van ophaling is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0007', 'nl', 'Documentdatums: De datum/het uur van verzending is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0006', 'nl', 'Documentdatums: De lengte van de datum/het uur van het document is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0005', 'nl', 'Documentdatums: De bepaling van het formaat van de datum/het uur van het document is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0004', 'nl', 'Documentdatums: De datum/het uur van het document is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0003', 'nl', 'Documentdatums: De datum/het uur van het document werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0002', 'nl', 'Documentdatums: De datum/het uur van het document ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0001', 'nl', 'Documentdatums: De bepaling van datum/uur/periode is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantTaxeLigne0001', 'nl', 'BTW bedrag lijn: De kosteninfo van de parentlijn (ALC + C) van het kostenbedrag MOA+23 ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0003', 'nl', 'Globale referentiedatums: De bepaling van het formaat van de geldigheidsperiode is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0004', 'nl', 'Globale referentiedatums: De duur van de geldigheidsperiode van het contract is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0001', 'nl', 'Briefhoofd zending: De identificatie van de verzender van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0002', 'nl', 'Briefhoofd zending: De identificatie van de bestemmeling van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0003', 'nl', 'Briefhoofd zending: De voorbereidingsdatum van de interchange is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0004', 'nl', 'Briefhoofd zending: De lengte van de voorbereidingsdatum van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0005', 'nl', 'Briefhoofd zending: De identificatie van de jaren in de voorbereidingsdatum van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0002', 'nl', 'Kortingen-Kosten lijn: Het kostenbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0006', 'nl', 'Briefhoofd zending: De identificatie van de maand in de voorbereidingsdatum van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0001', 'nl', 'Kortingen-Kosten lijn: Het kortingsbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0007', 'nl', 'Briefhoofd zending: De identificatie van de dag in de voorbereidingsdatum van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0004', 'nl', 'Kortingen-Kosten lijn: Het kostenbedrag werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0008', 'nl', 'Briefhoofd zending: Er bestaat een incoherentie tussen de identificaties van de dag en de maand van de voorbereidingsdatum van de interchange{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0003', 'nl', 'Kortingen-Kosten lijn: Het kortingsbedrag werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0009', 'nl', 'Briefhoofd zending: Het uur van de voorbereiding van de interchange is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0001', 'nl', 'Globale referentiedatums: De bepaling van datum/uur/periode van het document als referentie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0002', 'nl', 'Globale referentiedatums: De geldigheidsperiode is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0010', 'nl', 'Briefhoofd zending: De lengte van het uur van de voorbereiding van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0011', 'nl', 'Briefhoofd zending: De identificatie van de uren in het voorbereidingsuur van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0012', 'nl', 'Briefhoofd zending: De identificatie van de minuten in het voorbereidingsuur van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0005', 'nl', 'Globale Kortingen-Kosten: De bepaling van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0013', 'nl', 'Briefhoofd zending: De controlereferentie van de interchange is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0003', 'nl', 'Globale Kortingen-Kosten: Het percentage en het bedrag van de korting ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0004', 'nl', 'Globale Kortingen-Kosten: Het percentage en het kostenbedrag ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0001', 'nl', 'Globale Kortingen-Kosten: De betaalcode van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0002', 'nl', 'Globale Kortingen-Kosten: Het eerste element van het label van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0001', 'nl', 'Globale referenties: Het factuurnummer en de geldigheidsperiode/facturatieperiode (document zelf of contract) als referentie bij de kredietnota ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0002', 'nl', 'Globale referenties: Het factuurnummer als referentie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0003', 'nl', 'Globale referenties: Het contractnummer als referentie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0020', 'nl', 'Totale bedragen: Het totaal bedrag met alle taksen inbegrepen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0021', 'nl', 'Totale bedragen: Het totaal bedrag met alle taksen inbegrepen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0022', 'nl', 'Totale bedragen: Het totaal bedrag zonder taksen van de artikellijnen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0012', 'nl', 'Totale bedragen: De voorschotreferentie ontbreekt voor het totaal bedrag van het voorschot{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0014', 'nl', 'Totale bedragen: Het totaal voorschotbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0015', 'nl', 'Totale bedragen: Het totaal netto te betalen bedrag ontbreekt terwijl het totaal voorschotbedrag doorgestuurd werd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0017', 'nl', 'Totale bedragen: Het totaal netto te betalen bedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0018', 'nl', 'Totale bedragen: Het totaal BTW bedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0019', 'nl', 'Totale bedragen: Het totaal BTW bedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'nl', 'Overzicht parafiscale taks: De bepaling van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0003', 'nl', 'Overzicht parafiscale taks: Het eerste element van het label van de parafiscale taks is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0023', 'nl', 'Totale bedragen: Het totaal bedrag zonder taksen van de artikellijnen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0024', 'nl', 'Totale bedragen: Het voorschotbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0025', 'nl', 'Totale bedragen: Het totaal BTW bedrag wordt minstens driedubbel doorgestuurd, of dubbel doorgestuurd zonder muntcodes (1 muntcode verwacht voor elk bedrag){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0008', 'nl', 'Globale Kortingen-Kosten: Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0007', 'nl', 'Betalingsvoorwaarden: De boetevoorwaarden ontbreken zowel in tekst- als in gecodeerde vorm{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0008', 'nl', 'Betalingsvoorwaarden: De boetevoorwaarden werden zowel in tekst- als in gecodeerde vorm doorgestuurd (er kan slechts 1 model bestaan){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0005', 'nl', 'Betalingsvoorwaarden: De voorwaardelijke disconteringsvoorwaarden ontbreken zowel in tekst- als in gecodeerde vorm{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0006', 'nl', 'Betalingsvoorwaarden: De voorwaardelijke disconteringsvoorwaarden werden zowel in tekst- als in gecodeerde vorm doorgestuurd (er kan slechts 1 model bestaan){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0003', 'nl', 'Betalingsvoorwaarden: De info over de voorwaardelijke disconteringsvoorwaarden ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0004', 'nl', 'Betalingsvoorwaarden: De info over de boetevoorwaarden ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0006', 'nl', 'Betalingsvoorwaarden: De betaaldatum is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0001', 'nl', 'Betalingsvoorwaarden: De info over de betalingsvoorwaarden ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0005', 'nl', 'Betalingsvoorwaarden: De betaaldatum ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ALigne0001', 'nl', 'Detail: Het lijnnummer is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0002', 'nl', 'Betalingsvoorwaarden: De info over de vervaldatum ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0004', 'nl', 'Betalingsvoorwaarden: De vervaldatum is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0003', 'nl', 'Betalingsvoorwaarden: De vervaldatum ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0002', 'nl', 'Betalingsvoorwaarden: De bepaling van datum/uur van de betalingsvoorwaarden is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0001', 'nl', 'Betalingsvoorwaarden: De datum van de betalingsvoorwaarden werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0036', 'nl', 'Adressen: De identificatie van het organisme dat verantwoordelijk is voor de codelijst (codetype) van de aankoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0037', 'nl', 'Adressen: Het eerste element van de naam en het adres van de aankoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0038', 'nl', 'Adressen: Het tweede element van de naam en het adres van de aankoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0039', 'nl', 'Adressen: Het eerste element van de vennootschapsnaam van de aankoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AFindeSection0001', 'nl', 'Einde sectie: De identificatiecode van de sectie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0032', 'nl', 'Adressen: Het eerste element van de naam en het adres van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0033', 'nl', 'Adressen: Het tweede element van de naam en het adres van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0034', 'nl', 'Adressen: De info van de aankoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0035', 'nl', 'Adressen: De identificatiecode van de aankoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentairesLigne0001', 'nl', 'Opmerkingen lijn: Het eerste element van de tekst die de reden voor de vrijstelling van de taks beschrijft op de lijn van het artikel is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0030', 'nl', 'Adressen: De info over de regels ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0031', 'nl', 'Adressen: De identificatie van het organisme dat verantwoordelijk is voor de codelijst (codetype) van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0029', 'nl', 'Adressen: De landcode van de btw-plichtige is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0025', 'nl', 'Adressen: Het eerste element van de vennootschapsnaam van de btw-plichtige is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0026', 'nl', 'Adressen: Het eerste element van het adres van de btw-plichtige is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0027', 'nl', 'Adressen: De naam van de plaats van de btw-plichtige is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0028', 'nl', 'Adressen: De postcode van de btw-plichtige is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0001', 'nl', 'Referenties lijn: Het nummer van het BL/verzendingsbericht als referentie ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0021', 'nl', 'Adressen: De naam van de plaats van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0001', 'nl', 'Referentiedatums lijn: De datum/het uur van het BL/verzendingsbericht als referentie ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0022', 'nl', 'Adressen: De postcode van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0002', 'nl', 'Referentiedatums lijn: De datum/het uur van het BL/verzendingsbericht als referentie is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0023', 'nl', 'Adressen: De landcode van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0024', 'nl', 'Adressen: De info van de btw-plichtige werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0002', 'nl', 'Referenties lijn: Het nummer van het BL/verzendingsbericht als referentie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0020', 'nl', 'Adressen: Het eerste element van het adres van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0018', 'nl', 'Adressen: De info over de maatschappelijke zetel van de verkoper werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0001', 'nl', 'Kortingen-Kosten lijn: Het kortingspercentage is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0019', 'nl', 'Adressen: Het eerste element van de vennootschapsnaam van de maatschappelijke zetel is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0002', 'nl', 'Kortingen-Kosten lijn: Het kostenpercentage is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0014', 'nl', 'Adressen: Het eerste element van het adres van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0015', 'nl', 'Adressen: De naam van de plaats van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0016', 'nl', 'Adressen: De postcode van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0017', 'nl', 'Adressen: De landcode van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0005', 'nl', 'Omschrijving: Het eerste element van het label van het artikel is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0010', 'nl', 'Adressen: De info van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0004', 'nl', 'Omschrijving: Het eerste element van het label van het artikel is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0011', 'nl', 'Adressen: De info van de verkoper werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0003', 'nl', 'Omschrijving: De code van de beschrijving van het label van het artikel ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0012', 'nl', 'Adressen: De identificatiecode van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0002', 'nl', 'Omschrijving: De code van het beschrijvingstype van het object is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0013', 'nl', 'Adressen: Het eerste element van de vennootschapsnaam van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0001', 'nl', 'Omschrijving: De info over de omschrijving van het object ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0003', 'nl', 'Hoeveelheid detail: De bepaling van de gefactureerde hoeveelheid ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0002', 'nl', 'Hoeveelheid detail: De bepaling van de hoeveelheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'nl', 'Kortingen-Kosten lijn: Het kortingspercentage is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0001', 'nl', 'Hoeveelheid detail: De info over de hoeveelheden ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0001', 'nl', 'BERICHT argument is niet bepaald{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0007', 'nl', 'Adressen: De naam van de plaats van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0008', 'nl', 'Adressen: De postcode van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0006', 'nl', 'Hoeveelheid detail: De maateenheid van de gefactureerde hoeveelheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesMontantsTotaux0001', 'nl', 'Referenties totale bedragen: Het voorschotnummer als referentie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0009', 'nl', 'Adressen: De landcode van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0005', 'nl', 'Hoeveelheid detail: De gefactureerde hoeveelheid is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0004', 'nl', 'Hoeveelheid detail: De bepaling van de gefactureerde hoeveelheid werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0003', 'nl', 'Adressen: De info over de gefactureerde partner werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0004', 'nl', 'Adressen: De identificatiecode van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0005', 'nl', 'Adressen: Het eerste element van de vennootschapsnaam van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0006', 'nl', 'Adressen: Het eerste element van het adres van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004', 'nl', 'Globale Kortingen-Kosten:  Het tarief van de taks is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0003', 'nl', 'Globale Kortingen-Kosten: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0002', 'nl', 'Globale Kortingen-Kosten: De bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0009', 'nl', 'BTW lijn: De code van de takscategorie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0001', 'nl', 'Globale Kortingen-Kosten: De info over de taksen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0008', 'nl', 'BTW lijn: Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0007', 'nl', 'BTW lijn: De info over de taksen werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004', 'nl', 'BTW lijn: Het tarief van de taks is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0011', 'nl', 'Deviezen: De referentiemunteenheid ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0003', 'nl', 'BTW lijn: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0010', 'nl', 'Deviezen: De deviesinfo ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0002', 'nl', 'BTW lijn: De bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0013', 'nl', 'Deviezen: De code van de facturatiemunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0001', 'nl', 'BTW lijn: De info over de taksen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0012', 'nl', 'Deviezen: De bepaling van de referentiemunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0001', 'nl', 'Adressen: De info over de partners ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0002', 'nl', 'Adressen: De info over de gefactureerde partner ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0006', 'nl', 'Het bericht bevat geen UNB of UNH segment{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0007', 'nl', 'De interchange bevat meerdere berichten{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0004', 'nl', 'Deviezen: De code van de referentiemunteenheid is niet juist.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0003', 'nl', 'Deviezen: De bepaling van de referentiemunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0006', 'nl', 'Deviezen: De bepaling van het gebruik van de doelmunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0005', 'nl', 'Deviezen: De code van de referentiemunteenheid is niet juist.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0008', 'nl', 'Deviezen: De code van de doelmunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0007', 'nl', 'Deviezen: De bepaling van de doelmunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0010', 'nl', 'Totale bedragen: Het totaal bedrag met alle taksen inbegrepen wordt minstens driedubbel doorgestuurd, of dubbel doorgestuurd zonder muntcodes (1 muntcode verwacht voor elk bedrag){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0009', 'nl', 'Deviezen: De code van de doelmunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0011', 'nl', 'Totale bedragen: Het totaal bedrag met alle taksen inbegrepen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0001', 'nl', 'Totale bedragen: De info van de totale bedragen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0002', 'nl', 'Totale bedragen: Het totaal bedrag zonder taksen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0003', 'nl', 'Totale bedragen: Het totaal bedrag zonder taksen wordt minstens driedubbel doorgestuurd, of dubbel doorgestuurd zonder muntcodes (1 muntcode verwacht voor elk bedrag){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0004', 'nl', 'Totale bedragen: Het totaal bedrag zonder taksen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0005', 'nl', 'Totale bedragen: Het totaal BTW bedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0006', 'nl', 'Totale bedragen: Het tweede totaal BTW bedrag uitgedrukt in de doelmunt ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0002', 'nl', 'Deviezen: De bepaling van het gebruik van de referentiemunteenheid is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0007', 'nl', 'Totale bedragen: Het totaal BTW bedrag wordt dubbel doorgestuurd terwijl geen enkele doelmunt ingevuld is{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0008', 'nl', 'Totale bedragen: Het totaal BTW bedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0003', 'nl', 'Globale Kortingen-Kosten: Het kortingsbedrag werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0009', 'nl', 'Totale bedragen: Het totaal bedrag met alle taksen inbegrepen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0001', 'nl', 'Kortingen-Kosten lijn: De bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0002', 'nl', 'Globale Kortingen-Kosten: Het kostenbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0002', 'nl', 'Kortingen-Kosten lijn: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0001', 'nl', 'Globale Kortingen-Kosten: Het kortingsbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0003', 'nl', 'Kortingen-Kosten lijn: Het tarief van de taks is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0005', 'nl', 'Kortingen-Kosten lijn: De info over de taksen werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0006', 'nl', 'Kortingen-Kosten lijn: Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0004', 'nl', 'Globale Kortingen-Kosten: Het kostenbedrag werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0003', 'nl', 'BTW overzicht: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004', 'nl', 'BTW overzicht: Het belastbaar bedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0003', 'nl', 'Betalingsvoorwaarden: Het boetepercentage is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0005', 'nl', 'BTW overzicht: Het tarief van de taks is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0002', 'nl', 'Betalingsvoorwaarden: Het percentage van de voorwaardelijke discontering is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0005', 'nl', 'Betalingsvoorwaarden: Er bestaat een incoherentie tussen de boeteïndicator en het percentagetype{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0004', 'nl', 'Betalingsvoorwaarden: Er bestaat een incoherentie tussen de indicator van de voorwaardelijke discontering en het percentagetype{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0001', 'nl', 'BTW overzicht: De info over de taksen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0002', 'nl', 'BTW overzicht: De bepaling van de functie van de taks [7] ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0002', 'nl', 'Opmerkingen: De reglementaire info van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0001', 'nl', 'Opmerkingen: De opmerkingen van het briefhoofd van het document ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0006', 'nl', 'Opmerkingen: Het aandelenkapitaal en de munteenheid van de verkoper zijn niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0005', 'nl', 'Opmerkingen: De juridische vorm van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0004', 'nl', 'Opmerkingen: De handelsnaam van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0003', 'nl', 'Opmerkingen: De reglementaire info van de verkoper werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0001', 'nl', 'Referentiedatums totale bedragen: De bepaling van datum/uur van de referentie van het bedrag is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0002', 'nl', 'Referentiedatums totale bedragen: De datum voor het voorschot ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0003', 'nl', 'Referentiedatums totale bedragen: De datum voor het voorschot is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0004', 'nl', 'Detaildatums: De werkelijke datum/het werkelijke uur van de levering is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0007', 'nl', 'BTW overzicht: Het tarief van de taks ontbreekt in de rest van het bericht{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0008', 'nl', 'BTW overzicht: De code van de takscategorie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0009', 'nl', 'BTW overzicht: Het tarief van de taks werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0001', 'nl', 'Detaildatums: De bepaling van datum/uur/periode is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0002', 'nl', 'Detaildatums: De datum/het uur van verzending is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0003', 'nl', 'Detaildatums: De datum/het uur van ophaling is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0013', 'nl', 'Opmerkingen: De vaste vergoeding bij betalingsachterstand ontbreekt.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0012', 'nl', 'Opmerkingen: Het eerste element van de tekst die de reden voor de vrijstelling van de taks beschrijft op het briefhoofd van het document, is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0011', 'nl', 'Opmerkingen: Er bestaat een incoherentie tussen de opmerkingen die het motief voor de vrijstelling van de taks beschrijven en de code van de vrijstelling van de taks op het briefhoofd van het document{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0010', 'nl', 'Opmerkingen: Het eerste element van de tekst die de boetevoorwaarden beschrijft, is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARegimesTva0001', 'nl', 'Taks recht: Er bestaat een incoherentie tussen de code voor de vrijstelling van taks en de opmerkingen op het briefhoofd die het motief voor de vrijstelling van de taks beschrijven{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0017', 'nl', 'Opmerkingen: HET RCS-RCM en de stad van de verkoper zijn niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0016', 'nl', 'Opmerkingen: Het label van de disconteringsvoorwaarden van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0015', 'nl', 'Opmerkingen: Het label van het BTW tarief van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0014', 'nl', 'Opmerkingen: De info over de soort BTW en de discontering van de verkoper ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0009', 'nl', 'Opmerkingen: De tekst die de boetevoorwaarden beschrijft, werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0008', 'nl', 'Opmerkingen: Het eerste element van de tekst die de voorwaardelijke disconteringsvoorwaarden beschrijft, is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0007', 'nl', 'Opmerkingen: De tekst die de voorwaardelijke disconteringsvoorwaarden beschrijft, werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0001', 'nl', 'Begin bericht: De documentcode is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0004', 'nl', 'Begin bericht: De functiecode van het bericht is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0003', 'nl', 'Begin bericht: Het documentnummer is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0002', 'nl', 'Globale Kortingen-Kosten: Het kostenpercentage is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0001', 'nl', 'Globale Kortingen-Kosten: Het kortingspercentage is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0027', 'nl', 'Rappel adresreferenties: De SIREN code van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0026', 'nl', 'Rappel adresreferenties: De SIREN code van de maatschappelijke zetel van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0028', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0021', 'nl', 'Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0020', 'nl', 'Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de maatschappelijke zetel van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0023', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de maatschappelijke zetel van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0022', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de maatschappelijke zetel van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0025', 'nl', 'Rappel adresreferenties: De SIREN code van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0024', 'nl', 'Rappel adresreferenties: De SIREN code van de Franse gefactureerde partner ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0002', 'nl', 'Bedragen BTW overzicht: Het taksbedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0001', 'nl', 'Bedragen BTW overzicht: De info over het taksbedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0004', 'nl', 'Bedragen BTW overzicht: Het taksbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0003', 'nl', 'Bedragen BTW overzicht: Het taksbedrag werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0002', 'nl', 'Overzicht parafiscale taks: Het taksbedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0006', 'nl', 'Bedragen BTW overzicht: Het belastbaar bedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0001', 'nl', 'Overzicht parafiscale taks: De info over het taksbedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0005', 'nl', 'Bedragen BTW overzicht: Het opgegeven bedrag beantwoordt niet aan een taksbedrag{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0008', 'nl', 'Bedragen BTW overzicht: Het taksbedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0007', 'nl', 'Bedragen BTW overzicht: Het belastbaar bedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0005', 'nl', 'Kortingen-Kosten lijn: De bepaling van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0016', 'nl', 'Rappel adresreferenties: De SIREN code van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0004', 'nl', 'Kortingen-Kosten lijn: Het percentage en het kostenbedrag ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0015', 'nl', 'Rappel adresreferenties: De SIREN code van de gefactureerde partner werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0004', 'nl', 'Overzicht parafiscale taks: Het taksbedrag is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0003', 'nl', 'Kortingen-Kosten lijn: Het percentage en het bedrag van de korting ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0018', 'nl', 'Rappel adresreferenties: De SIREN code van de verkoper werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0003', 'nl', 'Overzicht parafiscale taks: Het taksbedrag werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0002', 'nl', 'Kortingen-Kosten lijn: Het eerste element van het label van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0001', 'nl', 'Kortingen-Kosten lijn: De betaalcode van de korting/kost is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0019', 'nl', 'Rappel adresreferenties: De SIREN code van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0010', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0007', 'nl', 'Prijs: De brutoprijs zonder taksen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0012', 'nl', 'Rappel adresreferenties: Het btw-identificatienummer van de btw-plichtige werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0008', 'nl', 'Prijs: De basis van de nettoprijs zonder taksen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0011', 'nl', 'Rappel adresreferenties: Het btw-identificatienummer van de btw-plichtige ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0009', 'nl', 'Prijs: De maateenheid van de nettoprijs zonder taksen is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0014', 'nl', 'Rappel adresreferenties: De SIREN code en het BTW identificatienummer van de Franse gefactureerde partner ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0013', 'nl', 'Rappel adresreferenties: Het btw-identificatienummer van de btw-plichtige is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0003', 'nl', 'Prijs: De nettoprijs zonder taksen werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0004', 'nl', 'Prijs: De nettoprijs zonder taksen is niet numeriek{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005', 'nl', 'Prijs: De brutoprijs zonder taksen ontbreekt terwijl een lijnkorting of lijnkost opgegeven is{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0002', 'nl', 'Briefhoofd bericht: De identificatie van het berichttype is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0006', 'nl', 'Prijs: De brutoprijs zonder taksen werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0001', 'nl', 'Briefhoofd bericht: Het referentienummer van het bericht is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0001', 'nl', 'Prijs: De prijsinfo ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0002', 'nl', 'Prijs: De nettoprijs zonder taksen ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0004', 'nl', 'Briefhoofd bericht: Het uitgavenummer is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0003', 'nl', 'Briefhoofd bericht: Het nummer van de berichtversie is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACodeComplementaireProduit0001', 'nl', 'Interne detailcode: De interne detailcode werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0006', 'nl', 'Briefhoofd bericht: De code die door de vereniging werd toegekend is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0005', 'nl', 'Briefhoofd bericht: Het controleagentschap is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0005', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner buiten Frankrijk ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0004', 'nl', 'Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de verkoper is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0007', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0006', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0012', 'nl', 'Kortingen-Kosten lijn: Het kortingsbedrag ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0009', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de verkoper werd dubbel doorgestuurd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0011', 'nl', 'Kortingen-Kosten lijn: Het kortingspercentage ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0008', 'nl', 'Rappel adresreferenties: Het BTW identificatienummer van de verkoper ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0010', 'nl', 'Kortingen-Kosten lijn: De code van de particuliere service van de korting is niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0010', 'nl', 'Betalingsvoorwaarden: De voorwaardelijke disconteringsvoorwaarden werden dubbel doorgestuurd in gecodeerde vorm.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0027', 'nl', 'Totale bedragen: Het totaal voorschotbedrag ontbreekt terwijl het totaal netto te betalen bedrag doorgestuurd werd{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0009', 'nl', 'Betalingsvoorwaarden: De boetevoorwaarden werden dubbel doorgestuurd in gecodeerde vorm.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0018', 'nl', 'Opmerkingen: De vaste vergoeding bij betalingsachterstand werd dubbel doorgestuurd.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'nl', 'MOA+125 (g48) is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+IV is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+LC is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_FORMAT_EMPTY', 'nl', 'Het datumformaat is niet ingevuld. De datum wordt standaard ingesteld op 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_C', 'nl', 'Een segment MOA+204 (g25) kan niet volgen op een segment ALC+C{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_RE_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+RE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_A', 'nl', 'Een segment MOA+23 (g25) kan niet volgen op een segment ALC+A{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+BY is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_EMPTY', 'nl', 'eC186.6060 van segment QTY+47 g25 seg is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY52_g25_EMPTY', 'nl', 'eC186.6060 van segment QTY+52 g25 seg is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+RE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+SE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+BY is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MISSING', 'nl', 'Segment BGM ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+BY is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'nl', 'Meer dan een segment MOA g15 na segment ALC g15.(Bepaling : 8){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+IV is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_g15_EMPTY', 'nl', 'eC501.5482 in segment PCD g15 is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PERIOD_NOT_WELLFORMATTED', 'nl', 'De periode is niet juist geformatteerd. De datum wordt standaard ingesteld op 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MISSING', 'nl', 'Segment NAD+SE ONTBREEKT{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_IV_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+IV is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'nl', 'eC516.5004 in MOA+113 g48 is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+RE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+CO is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'nl', 'Meer dan een DTM+137.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+SE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_VALID', 'nl', 'De datum is niet geldig. De datum wordt standaard ingesteld op 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_g25_EMPTY', 'nl', 'eC516.5004 in segment MOA g25 is niet ingevuld.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'nl', 'Meer dan een segment MOA+124 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0002', 'nl', 'Het bestand BERICHT bestaat niet{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PRI_MISSING', 'nl', 'PRI+AAA en PRI+AAB (g25) ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0003', 'nl', 'Het bestand BERICHT heeft als grootte 0{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+CO is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0004', 'nl', 'Onmogelijk het bestand BERICHT te lezen{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FTX_REG_MORE_THAN_ONE', 'nl', 'Meer dan een FTX+REG.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_DESC_MISSING', 'nl', 'De omschrijving van ALC ontbreekt (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+SE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+IV is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+RE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0009', 'nl', 'Een enkele UNH verwacht in bestand BERICHT.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'nl', 'Segment DTM+171 ontbreekt na segment MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+DL is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_A', 'nl', 'Een PCD+2 kan niet volgen op een ALC+A{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+RE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0005', 'nl', 'Het logniveau is niet bepaald (LEVEL){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_C', 'nl', 'Een PCD+1 kan niet volgen op een ALC+C{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+BY{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0008', 'nl', 'Interchange EDIFACT Factuur D96A vereist.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+DL is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0012', 'nl', 'Een enkele UNB verwacht in bestand BERICHT.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'nl', 'MOA+124 (g48) is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0010', 'nl', 'Bericht EDIFACT niet juist{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0011', 'nl', 'UNB vereist in bestand BERICHT{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_EMPTY', 'nl', 'De datum is niet ingevuld. De datum wordt standaard ingesteld op 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_QUALIFIER_NOT_EXPECTED', 'nl', 'De bepaling van PCD(g25) is niet juist na een ALC+A of ALC+C (g25). Verwacht : PCD+1 of PCD+2.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_C', 'nl', 'Een PCD+1 kan niet volgen op een ALC+C{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_A', 'nl', 'Een PCD+2 kan niet volgen op een ALC+A{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_g15_EMPTY', 'nl', 'eC516.5004 in segment MOA g15 is niet ingevuld.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+DP{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_LC_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+LC is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_13_g8_MISSING', 'nl', 'Segment DTM+13 (g8) ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_MORE_THAN_ONE', 'nl', 'Meer dan een PCD van g15 gevonden na segment ALC g15{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_WELLFORMATED', 'nl', 'De datum is niet juist geformatteerd. De datum wordt standaard ingesteld op 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'nl', 'eC506.1154 in RFF+PQ is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'nl', 'De omschrijving voor segment ALC(g25) ontbreekt (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+DL is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_MORE_THAN_ONE', 'nl', 'Meer dan een segment MOA+124 g50{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DL_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+DL is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+CO is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_MISSING', 'nl', 'QTY+47(g25) ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_VALIDATION_EDI', 'nl', 'De EDI validering van het bericht is mislukt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'nl', 'Meer dan een segment MOA+128 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_CUX_MORE_THAN_ONE', 'nl', 'Meer dan een segment CUX (g7){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_g15_PCD_MOA_EMPTY', 'nl', 'MOA+8(g15) of PCD+1/2(g15) ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'nl', 'Meer dan een segment QTY+47 (g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+DP is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_BY_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+BY is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'nl', 'eC516.5004 in MOA+9 g48 is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+DL{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_MOA204_MISSING', 'nl', 'MOA+204 van segment ALC ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+SE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+DL is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+SE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'nl', 'Meer dan een segment PCD g25 na segment ALC g25{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_QUALIFIER_NOT_EXPECTED', 'nl', 'De bepaling van PCD is niet juist na een ALC+A of ALC+C. Verwacht : PCD+1 of PCD+2.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+LC is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+BY is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_NAME_EMPTY', 'nl', 'eC080.3036.1 du NAD+DP is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'nl', 'Meer dan een segment MOA g25 na segment ALC g25.(Bepaling : 204 of 23){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+RE is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'nl', 'DTM+137 ontbreekt{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'nl', 'Segment RFF+PQ ontbreekt na segment MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+LC is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'nl', 'MOA+128 (g48) is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'nl', 'Meer dan een segment MOA+125 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FORMAT_NOT_MANAGED', 'nl', 'Het datumformaat wordt niet ondersteund. De datum wordt standaard ingesteld op 1970-01-01T00:00:00{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+CO is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+IV is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_g25_EMPTY', 'nl', 'eC501.5482 in segment PCD g25 is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_EMPTY', 'nl', 'MOA+124 (g50) is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+XA voor NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+DL{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_A_REGLMT_CODE', 'nl', 'e4471 de ALC+A(g15) moet 1 of 2 zijn{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+RE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_C_REGLMT_CODE', 'nl', 'e4471 de ALC+C(g15) moet 1 of 2 zijn{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+VA voor NAD+DP is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_1004_EMPTY', 'nl', 'Element e1004 van BGM is leeg{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_g25_PCD_MOA_EMPTY', 'nl', 'MOA+204(g25) of MOA+23(g25) of PCD+1(g25) of PCD+2(g25) ontbreken{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+BY{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+GN voor NAD+DL is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_A_REGLMT_CODE', 'nl', 'e4471 de ALC+A(g38) moet 1 of 2 zijn{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+RE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+DP{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DP_IDENT_EMPTY', 'nl', 'eC506.1154 in RFF+XA voor NAD+DP is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+BY{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+DP{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_C_REGLMT_CODE', 'nl', 'e4471 de ALC+C(g38) moet 1 of 2 zijn{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+VA voor NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+GN voor NAD+DL{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_MORE_THAN_ONE', 'nl', 'Meer dan een RFF+XA voor NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MORE_THAN_ONE', 'nl', 'Geldigheid begindatum{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_IDENT_EMPTY', 'nl', 'eC082.3039 du NAD+SF is niet ingevuld{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_MORE_THAN_ONE', 'nl', 'Meer dan een segment NAD+SF{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TAX_7_VAT_E_NO_FTXSIN', 'nl', 'TAX+7+VAT++++E zonder FTX+SIN{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'no', 'Parafiscal tax breakdown: The allowance/charge qualifier is incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'no', 'Allowance-Charge line: The discount percentage is not a number{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'no', 'MOA+125 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'no', 'eC082.3039 in NAD+IV is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'no', 'eC080.3036.1 in NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+VA for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+VA for NAD+BY is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'no', 'More than one MOA g15 found after ALC segment (Qualifier : 8){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'no', 'eC080.3036.1 in NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'no', 'eC516.5004 in MOA+113 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'no', 'eC080.3036.1 in NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'no', 'More than one DTM+137.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+GN for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'no', 'More than one MOA+124 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'no', 'More than one segment NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+GN for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'no', 'eC080.3036.1 in NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+GN for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'no', 'segment DTM+171 is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'no', 'MOA+124 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'no', 'More than one segment NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'no', 'eC506.1154 in RFF+PQ is empty\",\"RFF+PQ(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'no', 'ALC(g25) description missing (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'no', 'More than one segment NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'no', 'More than one MOA+128 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'no', 'More than one segment QTY+47 (g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'no', 'eC516.5004 in MOA+9 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'no', 'eC082.3039 in NAD+SE is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'no', 'More than one PCD g25 found after ALC segment{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+VA for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'no', 'More than one MOA g25 found after ALC segment (Qualifier 204 or 23){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'no', 'DTM+137 is missing{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'no', 'segment RFF+PQ is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+GN for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'no', 'More than one segment NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'no', 'MOA+128 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'no', 'More than one MOA+125 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+VA for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'no', 'eC506.1154 in RFF+VA for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'pl', 'Parafiscal tax breakdown: The allowance/charge qualifier is incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'pl', 'Allowance-Charge line: The discount percentage is not a number{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'pl', 'MOA+125 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'pl', 'eC082.3039 in NAD+IV is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'pl', 'eC080.3036.1 in NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+VA for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+VA for NAD+BY is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'pl', 'More than one MOA g15 found after ALC segment (Qualifier : 8){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'pl', 'eC080.3036.1 in NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'pl', 'eC516.5004 in MOA+113 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'pl', 'eC080.3036.1 in NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'pl', 'More than one DTM+137.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+GN for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'pl', 'More than one MOA+124 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'pl', 'More than one segment NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+GN for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'pl', 'eC080.3036.1 in NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+GN for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'pl', 'segment DTM+171 is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'pl', 'MOA+124 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'pl', 'More than one segment NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'pl', 'eC506.1154 in RFF+PQ is empty\",\"RFF+PQ(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'pl', 'ALC(g25) description missing (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'pl', 'More than one segment NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'pl', 'More than one MOA+128 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'pl', 'More than one segment QTY+47 (g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'pl', 'eC516.5004 in MOA+9 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'pl', 'eC082.3039 in NAD+SE is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'pl', 'More than one PCD g25 found after ALC segment{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+VA for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'pl', 'More than one MOA g25 found after ALC segment (Qualifier 204 or 23){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'pl', 'DTM+137 is missing{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'pl', 'segment RFF+PQ is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+GN for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'pl', 'More than one segment NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'pl', 'MOA+128 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'pl', 'More than one MOA+125 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+VA for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'pl', 'eC506.1154 in RFF+VA for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'sv', 'Parafiscal tax breakdown: The allowance/charge qualifier is incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'sv', 'Allowance-Charge line: The discount percentage is not a number{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'sv', 'MOA+125 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'sv', 'eC082.3039 in NAD+IV is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'sv', 'eC080.3036.1 in NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+VA for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+VA for NAD+BY is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'sv', 'More than one MOA g15 found after ALC segment (Qualifier : 8){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'sv', 'eC080.3036.1 in NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'sv', 'eC516.5004 in MOA+113 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'sv', 'eC080.3036.1 in NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'sv', 'More than one DTM+137.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+GN for NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'sv', 'More than one MOA+124 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'sv', 'More than one segment NAD+IV{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+GN for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'sv', 'eC080.3036.1 in NAD+SE is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+GN for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'sv', 'segment DTM+171 is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'sv', 'MOA+124 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'sv', 'More than one segment NAD+LC{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'sv', 'eC506.1154 in RFF+PQ is empty\",\"RFF+PQ(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'sv', 'ALC(g25) description missing (eC214.7160.1,eC214.7160.2).{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'sv', 'More than one segment NAD+SE{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'sv', 'More than one MOA+128 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'sv', 'More than one segment QTY+47 (g25){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'sv', 'eC516.5004 in MOA+9 g48 seg is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'sv', 'eC082.3039 in NAD+SE is empty.{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'sv', 'More than one PCD g25 found after ALC segment{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+VA for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'sv', 'More than one MOA g25 found after ALC segment (Qualifier 204 or 23){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'sv', 'DTM+137 is missing{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'sv', 'segment RFF+PQ is missing after MOA+113(g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+GN for NAD+LC is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'sv', 'More than one segment NAD+CO{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'sv', 'MOA+128 (g48) is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'sv', 'More than one MOA+125 (g48){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+VA for NAD+CO is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'sv', 'eC506.1154 in RFF+VA for NAD+IV is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0001', 'en', 'MESSAGE argument is not defined{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0006', 'en', 'No UNB or UNH segment in the message {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0007', 'en', 'Not able to handle several messages in an interchange {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACodeComplementaireProduit0001', 'en', 'Line internal code: The article internal code is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0001', 'en', 'Comments (free text): The comments in the document header are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0002', 'en', 'Comments (free text): The seller regulatory information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0003', 'en', 'Comments (free text): The seller regulatory information is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0004', 'en', 'Comments (free text): The registered company name of the seller is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0005', 'en', 'Comments (free text): The seller legal form is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0006', 'en', 'Comments (free text): The seller share capital and currency are incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0007', 'en', 'Comments (free text): The text describing the discount terms is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0008', 'en', 'Comments (free text): The 1st element of the text describing the conditions of conditional discount is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0009', 'en', 'Comments (free text): The text describing the conditions of penalty is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0010', 'en', 'Comments (free text): The 1st element of the text describing the penalty terms is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0011', 'en', 'Comments (free text): There is an inconsistency between the comment describing the reason for the tax exemption and the tax exemption code in the header of the document {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0012', 'en', 'Comments (free text): The 1st element of the text describing the reason for the tax exemption in the header of the document is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0013', 'en', 'Comments (free text): Penalty fee is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0014', 'en', 'Comments: The seller VAT and discount type information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0015', 'en', 'Comments: Wrong label for the seller VAT scheme {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0016', 'en', 'Comments: Wrong label for the seller discount term {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0017', 'en', 'Comments: Wrong seller RCS-RCM or City {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentaires0018', 'en', 'Comments (free text): Penalty fee is duplicated {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ACommentairesLigne0001', 'en', 'Line comments (free text): The 1st element of the text describing the reason for the tax exemption on the line item is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0001', 'en', 'Payment Terms: The payment terms information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0002', 'en', 'Payment Terms: The due date information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0003', 'en', 'Payment Terms: The discount terms information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0004', 'en', 'Payment Terms: The penalty terms information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0005', 'en', 'Payment Terms: The discount terms are missing simultaneously in text and in coded form {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0006', 'en', 'Payment Terms: The discount terms are simultaneously transmitted in text and in coded form (only 1 expression can be used) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0007', 'en', 'Payment Terms: The penalty terms are missing simultaneously in text and in coded form {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0008', 'en', 'Payment Terms: The penalty terms are simultaneously transmitted in text and in coded form (only 1 expression can be used) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0009', 'en', 'Payment Terms: The penalty terms are transmitted in duplicate as code {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AConditionsPaiement0010', 'en', 'Payment Terms: The discount terms are are transmitted in duplicate as code {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0001', 'en', 'Global reference dates: The date/time/period qualifier of the reference document is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0002', 'en', 'Global reference dates: The validity period is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0003', 'en', 'Global reference dates: The validity period format qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReference0004', 'en', 'Global reference dates: The length of the contract validity period is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0001', 'en', 'Line reference dates: Missing date/time  for the referenced delivery note/despatch advice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADateReferenceLigne0002', 'en', 'Line reference dates: The date/time  for the referenced delivery note/despatch advice is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0001', 'en', 'Document dates: The date/time/period qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0002', 'en', 'Document dates: The document date/time is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0003', 'en', 'Document dates: The document date/time is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0004', 'en', 'Document dates: The document date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0005', 'en', 'Document dates: The format qualifier of the document date/time is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0006', 'en', 'Document dates: The length of the document date/time is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0007', 'en', 'Document dates: The despatch date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0008', 'en', 'Document dates: The pick-up date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0009', 'en', 'Document dates: The actual delivery date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0010', 'en', 'Document dates: The invoicing period is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0011', 'en', 'Document dates: The invoicing period format qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0012', 'en', 'Document dates: The invoicing period length is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0013', 'en', 'Document dates: The date/time/period information of the document header is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0014', 'en', 'Document dates: The month identification in the document date is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0015', 'en', 'Document dates: The day identification in the document date is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0016', 'en', 'Document dates: There is an inconsistency between the day and month identifications in the document date {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesFacture0017', 'en', 'Document dates: The document date is later than today''s date {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0001', 'en', 'Line dates: The date/time/period qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0002', 'en', 'Line dates: The despatch date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0003', 'en', 'Line dates: The pick-up date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesLigne0004', 'en', 'Line dates: The actual delivery date/time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0001', 'en', 'Payment Terms: The payment terms date is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0002', 'en', 'Payment Terms: The payment terms date/time qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0003', 'en', 'Payment Terms: The due date is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0004', 'en', 'Payment Terms: The due date is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0005', 'en', 'Payment terms: Missing payment date {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesPaiement0006', 'en', 'Payment terms: The payment date is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0001', 'en', 'Total amounts reference dates: The amount reference date/time qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0002', 'en', 'Total amounts reference dates: The prepaid amount date is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADatesReferencesMontantsTotaux0003', 'en', 'Total amounts reference dates: The prepaid amount date is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0001', 'en', 'Description: The item description information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0002', 'en', 'Description: The item description type is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0003', 'en', 'Description: The item description code is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0004', 'en', 'Description: The item description 1st element is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADescriptionObjet0005', 'en', 'Description: The first element of the item description is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0002', 'en', 'Currency: The reference currency details qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0003', 'en', 'Currency: The reference currency qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0004', 'en', 'Currency: The code of the reference currency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0005', 'en', 'Currency: The code of the reference currency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0006', 'en', 'Currency: The details qualifier of the target currency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0007', 'en', 'Currency: The qualifier of the target currency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0008', 'en', 'Currency: The code of the target currency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0009', 'en', 'Currency: The code of the target currency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0010', 'en', 'Currencies: Missing currency information {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0011', 'en', 'Currencies: Missing reference currency {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0012', 'en', 'Currencies: Wrong qualifier for the reference currency {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0013', 'en', 'Currencies: Wrong code for the invoicing currency{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ADevises0014', 'en', 'Currency: The details qualifier of the target currency is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0001', 'en', 'Interchange header: The interchange sender identification is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0002', 'en', 'Interchange header: The interchange recipient identification is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0003', 'en', 'Interchange header: The preparation date is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0004', 'en', 'Interchange header: The length of the preparation date is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0005', 'en', 'Interchange header: The year id in the preparation date is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0006', 'en', 'Interchange header: The month id in the preparation date is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0007', 'en', 'Interchange header: The day id in the preparation date is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0008', 'en', 'Interchange header: There is an inconsistency between the identifications of the day and month in the preparation date {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0009', 'en', 'Interchange header: The preparation time is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0010', 'en', 'Interchange header: The length of the preparation time is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0011', 'en', 'Interchange header: The hour id in the preparation hour is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0012', 'en', 'Interchange header: The minute id in the preparation hour is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0013', 'en', 'Interchange header: The interchange control reference is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AEnteteFacture0014', 'en', 'Interchange header: The preparation time is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AFindeSection0001', 'en', 'End of section: The section id is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0001', 'en', 'Beginning of message: The document code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0003', 'en', 'Beginning of message: The document number is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIdentificationMessage0004', 'en', 'Beginning of message: The message function code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0001', 'en', 'Addresses: The partners information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0002', 'en', 'Addresses: The invoicee information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0003', 'en', 'Addresses: The invoicee information is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0004', 'en', 'Addresses: The invoicee identification code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0005', 'en', 'Addresses: The 1st element in the invoicee name is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0006', 'en', 'Addresses: The 1st element in the invoicee address is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0007', 'en', 'Addresses: The invoicee town name is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0008', 'en', 'Addresses: The invoicee postcode is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0009', 'en', 'Addresses: The invoicee country code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0010', 'en', 'Addresses: The seller information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0011', 'en', 'Addresses: The seller information is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0012', 'en', 'Addresses: The  seller identification code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0013', 'en', 'Addresses: The 1st element in the seller name is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0014', 'en', 'Addresses: The 1st element in the seller address is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0015', 'en', 'Addresses: The seller town name is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0016', 'en', 'Addresses: The seller postcode is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0017', 'en', 'Addresses: The seller country code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0018', 'en', 'Addresses: The seller''s corporate office information is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0019', 'en', 'Addresses: The 1st element of the seller''s corporate office corporate name is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0020', 'en', 'Addresses: The 1st element of the seller''s corporate office address is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0021', 'en', 'Addresses: The seller''s corporate office town name is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0022', 'en', 'Addresses: The seller''s corporate office postal code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0023', 'en', 'Addresses: The seller''s corporate office country code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0024', 'en', 'Addresses: The information of the party declaring the VAT are transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0025', 'en', 'Addresses: The 1st element of the corporate name of the party declaring the VAT is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0026', 'en', 'Addresses: The 1st element of the address of the party declaring the VAT is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0027', 'en', 'Addresses: The town name of the party declaring the VAT is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0028', 'en', 'Addresses: The postal code of the party declaring the VAT is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0029', 'en', 'Addresses: The country code of the party declaring the VAT is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0030', 'en', 'Addresses: The information of the party receiving the commercial invoice remittance is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0031', 'en', 'Addresses: Wrong ID for the organization in charge of the code list (code type) used for the seller {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0032', 'en', 'Addresses: The first element of the seller name and address line is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0033', 'en', 'Addresses: The second element of the seller name and address line is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0034', 'en', 'Addresses: The buyer information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0035', 'en', 'Addresses: Wrong buyer ID code {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0036', 'en', 'Addresses: Wrong ID for the organization in charge of the code list (code type) used for the buyer {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0037', 'en', 'Addresses: The first element of the buyer name and address line is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0038', 'en', 'Addresses: The second element of the buyer name and address line is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AIntervenantsFacture0039', 'en', 'Addresses: The first element of the buyer party name is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ALigne0001', 'en', 'Detail: The line number is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0001', 'en', 'Line allowances/charges: The allowance amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0002', 'en', 'Line allowances/charges: The charge amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0003', 'en', 'Line allowances/charges: The allowance amount is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0004', 'en', 'Line allowances/charges: The charge amount is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0005', 'en', 'Allowance-Charge line: The discount amount is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantLigneReductionCharge0006', 'en', 'Line allowances/charges: The charge amount is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0001', 'en', 'Global allowances/charges: The allowance amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0002', 'en', 'Global allowances/charges: The charge amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0003', 'en', 'Global allowances/charges: The allowance amount is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0004', 'en', 'Global allowances/charges: The charge amount is transmitted twice{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0005', 'en', 'Global allowances/charges: The allowance amount is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantReductionCharge0006', 'en', 'Global allowances/charges: The charge amount is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontants0002', 'en', 'Payment terms: The cash discount amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0001', 'en', 'Parafiscal tax breakdown: The tax information amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0002', 'en', 'Parafiscal tax breakdown: The tax amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0003', 'en', 'Parafiscal tax breakdown: The tax amount is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTaxes0004', 'en', 'Parafiscal tax breakdown: The tax amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0001', 'en', 'Total amounts: The information of total amounts is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0002', 'en', 'Total amounts: The total taxable amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0003', 'en', 'Total amounts: The total taxable amount is transmitted at least in triplicate, or in duplicate without currency codes (1 currency code expected for each amount) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0004', 'en', 'Total amounts: The total taxable amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0005', 'en', 'Total amounts: The total VAT amount  is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0006', 'en', 'Total amounts: The second total VAT amount expressed in the currency target is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0007', 'en', 'Total amounts: The total VAT amount  is transmitted twice whereas no currency target is present {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0008', 'en', 'Total amounts: The total VAT amount  is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0009', 'en', 'Total amounts: The total amount including taxes is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0010', 'en', 'Total amounts: The total amount including taxes is transmitted at least in triplicate, or in duplicate without currency codes (1 currency code expected for each amount) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0011', 'en', 'Total amounts: The total amount including taxes is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0012', 'en', 'Total amounts: The deposit reference is missing for the total deposit amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0014', 'en', 'Total amounts: The total deposit amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0015', 'en', 'Total amounts: The amount due is missing whereas the total deposit amount is transmitted {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0017', 'en', 'Total amounts: The amount due is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0018', 'en', 'Total amounts: Missing VAT total amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0019', 'en', 'Total amounts: The VAT total amount is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0020', 'en', 'Total amounts: Missing total amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0021', 'en', 'Total Amounts: The total amount is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0022', 'en', 'Total amounts: The item line total amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0023', 'en', 'Total amounts: The item line total amount is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0024', 'en', 'Total amounts: The discount amount is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0025', 'en', 'Total amounts: The total VAT amount  is transmitted at least in triplicate, or in duplicate without currency codes (1 currency code expected for each amount) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTotaux0027', 'en', 'Total amounts: The total deposit amount is missing whereas the amount due is transmitted {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0001', 'en', 'VAT breakdown amounts: The tax information amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0002', 'en', 'VAT breakdown amounts: The tax amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0003', 'en', 'VAT breakdown amounts: The tax amount is transmitted in duplicate {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0004', 'en', 'VAT breakdown amounts: The tax amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0005', 'en', 'VAT breakdown: One of the indicated amounts is not a VAT amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0006', 'en', 'VAT breakdown amount: Missing taxable amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0007', 'en', 'VAT breakdown amount: The taxable amount is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantsTva0008', 'en', 'VAT breakdown amount: Missing tax amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AMontantTaxeLigne0001', 'en', 'Line VAT amount: The charge qualifier (ALC+C) of the charge amount (MOA+23) is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0001', 'en', 'Line allowances/charges: The allowance percentage is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0002', 'en', 'Line allowances/charges: The charge percentage is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0003', 'en', 'Allowance-Charge line: The discount percentage is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageLigneReduction0004', 'en', 'Line allowances/charges: The charge percentage is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0001', 'en', 'Global allowances/charges: The allowance percentage is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0002', 'en', 'Global allowances/charges: The charge percentage is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0003', 'en', 'Global allowances/charges: The allowance percentage is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentageReductionCharge0004', 'en', 'Global allowances/charges: The charge percentage is empty{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0002', 'en', 'Payment terms: The cash discount percentage is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0003', 'en', 'Payment terms: The penalty percentage is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0004', 'en', 'Payment terms: An incoherence exists between the cash discount indicator and the percentage type {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APourcentages0005', 'en', 'Payment terms: An incoherence exists between the penalty indicator and the percentage type {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0001', 'en', 'Price: The price information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0002', 'en', 'Price: The net price is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0003', 'en', 'Price: The net price is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0004', 'en', 'Price: The net price is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005', 'en', 'Price: The gross price is missing whereas a discount/charge of line is present {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0005bis', 'en', 'Price: The gross price is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0006', 'en', 'Price: The gross price is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0007', 'en', 'Price: The gross price is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0008', 'en', 'Price: The WT net unit price basis is not a number {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96APrix0009', 'en', 'Price: The WT net price unit of measure is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0001', 'en', 'Line quantity: The information of quantity is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0002', 'en', 'Line quantity: The quantity qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0003', 'en', 'Line quantity: The invoiced quantity qualifier is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0004', 'en', 'Line quantity: The invoiced quantity qualifier is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0005', 'en', 'Line quantity: The invoiced quantity is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AQuantites0006', 'en', 'Item quantity: The invoiced quantity unit of measure is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0001', 'en', 'Parafiscal tax breakdown: The charge qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARecapTaxesParafiscales0003', 'en', 'Parafiscal tax breakdown: The 1st element of the parafiscal tax description is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0001', 'en', 'Global allowances/charges: The allowance/charge settlement code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0002', 'en', 'Global allowances/charges: The 1st element of the allowance/charge description is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0003', 'en', 'Global allowances/charges: The allowance percentage and amount are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0004', 'en', 'Global allowances/charges: The charge percentage and amount are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0005', 'en', 'Global allowances/charges: The allowance/charge qualifier is incorrect{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0006', 'en', 'More than one percentage for global allowance (PCD+1) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionCharge0007', 'en', 'More than one percentage for global charge (PCD+2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0001', 'en', 'Line allowances/charges: The allowance/charge settlement code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0002', 'en', 'Line allowances/charges: The 1st element of the allowance/charge description is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0003', 'en', 'Line allowances/charges: The allowance percentage and amount are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0004', 'en', 'Line allowances/charges: The charge percentage and amount are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0005', 'en', 'Line allowances/charges: The allowance/charge qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0006', 'en', 'More than one percentage for line allowance (PCD+1) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0007', 'en', 'More than one percentage for line charge (PCD+2){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0008', 'en', 'Surely a missing line allowance/charge data (a PCD segment is following directly a TAX segment in group 33) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0010', 'en', 'Allowance-Charge line: The code for the discount special service is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0011', 'en', 'Allowance-Charge line: Missing discount percentage {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReductionChargeLigne0012', 'en', 'Allowance-Charge line: Missing discount amount {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0001', 'en', 'Global references: The invoice number and the period of validity/invoicing (document itself or contract) in reference in the credit note are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0002', 'en', 'Global references: The invoice number in reference is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReference0003', 'en', 'Global references: The contract number in reference is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0001', 'en', 'Line references: The referenced delivery note/despatch advice number is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferenceLigne0002', 'en', 'Line references: The referenced delivery note/despatch advice number is wrong {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0001', 'en', 'Global address references: The reference qualifier is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0002', 'en', 'Global address references: The company registration number (RCS-RCM) of the seller is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0003', 'en', 'Global address references: The company registration number (RCS-RCM) of the seller is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0004', 'en', 'Global address references: The company registration number (RCS-RCM) of the seller is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0005', 'en', 'Global address references: The VAT registration number of the invoicee out of France is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0006', 'en', 'Global address references: The VAT registration number of the invoicee is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0007', 'en', 'Global address references: The VAT registration number of the invoicee is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0008', 'en', 'Global address references: The VAT registration number of the seller is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0009', 'en', 'Global address references: The VAT registration number of the seller is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0010', 'en', 'Global address references: The VAT registration number of the seller is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0011', 'en', 'Global address references: The VAT registration number of the party declaring the VAT is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0012', 'en', 'Global address references: The VAT registration number of the party declaring the VAT is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0013', 'en', 'Global address references: The VAT registration number of the party declaring the VAT is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0014', 'en', 'Global address references: The government reference number (SIREN) and the VAT registration number of the French invoicee are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0015', 'en', 'Global address references: The government reference number (SIREN) of the invoicee is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0016', 'en', 'Global address references: The government reference number (SIREN) of the invoicee is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0018', 'en', 'Global address references: The government reference number (SIREN) of the seller is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0019', 'en', 'Global address references: The government reference number (SIREN) of the seller is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0020', 'en', 'Global address references: The company registration number (RCS-RCM) of the seller''s corporate office is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0021', 'en', 'Global address references: The company registration number (RCS-RCM) of the seller''s corporate office is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0022', 'en', 'Global address references: The VAT registration number of the seller''s corporate office is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0023', 'en', 'Global address references: The VAT registration number of the seller''s corporate office is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0024', 'en', 'Global address references: The government reference number (SIREN) of the invoicee is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0025', 'en', 'Global address references: The government reference number (SIREN) of the seller is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0026', 'en', 'Global address references: The government reference number (SIREN) of the seller''s corporate office is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0027', 'en', 'Global address references: The government reference number (SIREN) of the seller''s corporate office is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesIntervenant0028', 'en', 'Global address references: The VAT registration number of the invoicee is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AReferencesMontantsTotaux0001', 'en', 'Total amounts references: The deposit number in reference is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ARegimesTva0001', 'en', 'Tax duty: An incoherence exists between the tax exemption code and the header comment describing the motive of the tax exemption {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0001', 'en', 'Global allowances/charges: The tax information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0002', 'en', 'Global allowances/charges: The tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0003', 'en', 'Global allowances/charges: The type tax code [VAT] associated with the tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004', 'en', 'Global allowances/charges: The tax rate is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0004b', 'en', 'Global allowances/charges: The tax rate is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxes0008', 'en', 'Global allowances/charges: The tax rate is missing in the tax breakdown in the message foot {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0001', 'en', 'Line VAT: The tax information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0002', 'en', 'Line VAT: The tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0003', 'en', 'Line VAT: The tax type code [VAT] associated with the tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004', 'en', 'Line VAT: The tax rate is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0004b', 'en', 'Line VAT: The tax rate is missing  {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0007', 'en', 'Line VAT: The tax information is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0008', 'en', 'Line VAT: The tax rate is missing in the tax breakdown in the message foot {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigne0009', 'en', 'VAT line: Wrong code for the VAT category {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0001', 'en', 'Line allowances/charges: The tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0002', 'en', 'Line allowances/charges: The type tax code [VAT] associated with the tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0003', 'en', 'Line allowances/charges: The tax rate is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0005', 'en', 'Line allowances/charges: The tax information is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATaxesLigneReductionCharge0006', 'en', 'Line allowances/charges: The tax rate is missing in the tax breakdown in the message foot {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0001', 'en', 'Header message: The reference number of the message is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0002', 'en', 'Header message: The message type identifier incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0003', 'en', 'Header message: The message type version number is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0004', 'en', 'Header message: The message type release number is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0005', 'en', 'Header message: The controlling agency is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96ATypeMessage0006', 'en', 'Header message: The association assigned code is incorrect {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0001', 'en', 'VAT breakdown: The tax information is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0002', 'en', 'VAT breakdown: The tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0003', 'en', 'VAT breakdown: The type tax code [VAT] associated with the tax function qualifier [7] is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004', 'en', 'VAT breakdown: The taxable amount is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0004b', 'en', 'VAT breakdown: The taxable amount is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0005', 'en', 'VAT breakdown: The tax rate is not numerical {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0006', 'en', 'VAT breakdown: The tax rate is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0007', 'en', 'VAT breakdown: The tax rate is missing in the rest of the message {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0008', 'en', 'VAT breakdown: Wrong tax category code {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('D96AVentilationsTva0009', 'en', 'VAT breakdown: The tax rate is transmitted twice {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0002', 'en', 'MESSAGE file does not exist {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0003', 'en', 'MESSAGE file size is 0 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0004', 'en', 'Can not read MESSAGE file {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0011', 'en', 'UNB required in MESSAGE file {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0012', 'en', 'Only one UNB expected in MESSAGE file {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0009', 'en', 'Too much UNH {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0008', 'en', 'EDIFACT Invoice D96A Interchange is requiered {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MORE_THAN_ONE', 'en', 'Beginning of message : More than one BGM segment found{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_DESC_MISSING', 'en', 'ALC description missing (eC214.7160.1,eC214.7160.2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_MORE_THAN_ONE', 'en', 'More than one PCD g15 found after ALC segment {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_QUALIFIER_NOT_EXPECTED', 'en', 'PCD qualifier is not expected after ALC segment. Should be 1 or 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_A', 'en', 'PCD+2 can not follow ALC+A {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_C', 'en', 'PCD+1 can not follow ALC+C {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PCD_g15_EMPTY', 'en', 'eC501.5482 in PCD g15 seg is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_MORE_THAN_ONE', 'en', 'More than one amount for global allowance/charge (MOA g15 following ALC segment, qualifier 8) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_g15_EMPTY', 'en', 'eC516.5004 in MOA g15 seg is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE', 'en', 'More than one tax amount (MOA+124 g48) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g48_EMPTY', 'en', 'The tax amount (MOA+124 g48) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE', 'en', 'More than one taxable amount (MOA+125 g48) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_125_g48_EMPTY', 'en', 'The taxable amount (MOA+125 g48) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE', 'en', 'More than one total amount (MOA+128 g48) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_128_g48_EMPTY', 'en', 'The total amount (MOA+128 g48) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_MORE_THAN_ONE', 'en', 'More than one MOA+124 g50 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA_124_g50_EMPTY', 'en', 'MOA+124 (g50) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_EMPTY', 'en', 'eC186.6060 in QTY+47 g25 seg is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY52_g25_EMPTY', 'en', 'eC186.6060 in QTY+52 g25 seg is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_g25_MORE_THAN_ONE', 'en', 'More than one invoiced quantity (QTY+47 g25) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_QTY47_MISSING', 'en', 'QTY+47 in g25 is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PRI_MISSING', 'en', 'PRI+AAA and PRI+AAB in g25 are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_MOA204_MISSING', 'en', 'MOA+204 with ALC is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_g15_PCD_MOA_EMPTY', 'en', 'MOA+8(g15) or PCD+1/2(g15) are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_NAME_EMPTY', 'en', 'The 1st element in the invoicee name (eC080.3036.1 in NAD+IV) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_NAME_EMPTY', 'en', 'The 1st element in the ShipFrom name (eC080.3036.1 in NAD+SF) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_NAME_EMPTY', 'en', 'The 1st element in the seller name (eC080.3036.1 in NAD+SE) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_NAME_EMPTY', 'en', 'eC080.3036.1 in NAD+BY is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_NAME_EMPTY', 'en', 'The 1st element in the seller headquarter name (eC080.3036.1 in NAD+CO) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_NAME_EMPTY', 'en', 'The 1st element in the name of VAT fiscal representative (eC080.3036.1 in NAD+LC) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_NAME_EMPTY', 'en', 'eC080.3036.1 in NAD+DP is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_NAME_EMPTY', 'en', 'eC080.3036.1 in NAD+RE is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_NAME_EMPTY', 'en', 'eC080.3036.1 in NAD+DL is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MISSING', 'en', 'NAD+SE is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_MISSING', 'en', 'BGM segment is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_13_g8_MISSING', 'en', 'DTM+13 (g8) is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM_137_MISSING', 'en', 'Missing invoice date {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FORMAT_NOT_MANAGED', 'en', 'Format not managed. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_PERIOD_NOT_WELLFORMATTED', 'en', 'Period not well formated. Forced the two dates to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_WELLFORMATED', 'en', 'Date is not correctly formatted. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_NOT_VALID', 'en', 'Invalid date. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_EMPTY', 'en', 'Empty date. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DATE_FORMAT_EMPTY', 'en', 'Empty date format. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_FORMAT_NOT_MANAGED', 'en', 'Invoice date : Format not managed. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED', 'en', 'Invoice date : Date is not correctly formatted. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_NOT_VALID', 'en', 'Invoice date : Invalid date. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_EMPTY', 'en', 'Invoice date : Empty date. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_INVOIC_DATE_FORMAT_EMPTY', 'en', 'Empty date format for invoic date. Forced to 1970-01-01T00:00:00 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_VALIDATION_EDI', 'en', 'EDI validation failed {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM137_MORE_THAN_ONE', 'en', 'More than one invoice date {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_FTX_REG_MORE_THAN_ONE', 'en', 'More than one FTX+REG {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_IDENT_EMPTY', 'en', 'The  invoicee identification code (eC082.3039 in NAD+IV) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_IDENT_EMPTY', 'en', 'The ShipFrom identification code (eC082.3039 in NAD+SF) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_IDENT_EMPTY', 'en', 'eC082.3039 in NAD+DL is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_IDENT_EMPTY', 'en', 'eC082.3039 in NAD+RE is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_IDENT_EMPTY', 'en', 'eC082.3039 in NAD+DP is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_IDENT_EMPTY', 'en', 'eC082.3039 in NAD+BY is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_IDENT_EMPTY', 'en', 'The  seller identification code (eC082.3039 in NAD+SE) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_IV_MORE_THAN_ONE', 'en', 'More than one segment about the invoicee (NAD+IV) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SF_MORE_THAN_ONE', 'en', 'More than one segment about the ShipFrom (NAD+SF) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_SE_MORE_THAN_ONE', 'en', 'More than one segment about the seller (NAD+SE) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DL_MORE_THAN_ONE', 'en', 'More than one segment NAD+DL {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_RE_MORE_THAN_ONE', 'en', 'More than one segment NAD+RE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_DP_MORE_THAN_ONE', 'en', 'More than one segment NAD+DP {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_LC_MORE_THAN_ONE', 'en', 'More than one segment about the VAT fiscal representative (NAD+LC) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_CO_MORE_THAN_ONE', 'en', 'More than one segment about the seller headquarter (NAD+CO) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_NAD_BY_MORE_THAN_ONE', 'en', 'More than one segment NAD+BY {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+SE is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_SE_MORE_THAN_ONE', 'en', 'More than one registration number (RFF+XA) for the seller (NAD+SE) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY', 'en', 'The government reference number (SIREN) of the seller (eC506.1154 in RFF+GN for NAD+SE) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_SE_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+SE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY', 'en', 'The VAT registration number of the seller (eC506.1154 in RFF+VA for NAD+SE) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_SE_MORE_THAN_ONE', 'en', 'More than one VAT code (RFF+VA) for the seller (NAD+SE) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_BY_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+BY is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+GN for NAD+BY is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_BY_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+BY {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY', 'en', 'The VAT registration number of the buyer (eC506.1154 in RFF+VA for NAD+BY) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_BY_MORE_THAN_ONE', 'en', 'More than one RFF+VA for NAD+BY {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_IV_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+IV is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY', 'en', 'The government reference number (SIREN) of the invoicee (eC506.1154 in RFF+GN for NAD+IV) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_IV_MORE_THAN_ONE', 'en', 'More than one VAT code (RFF+VA) for the invoicee (NAD+IV) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY', 'en', 'The VAT registration number of the invoicee (eC506.1154 in RFF+VA for NAD+IV) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_IV_MORE_THAN_ONE', 'en', 'More than one RFF+VA for NAD+IV {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+CO is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_CO_MORE_THAN_ONE', 'en', 'More than one registration number (RFF+XA) for the seller headquarter (NAD+CO) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY', 'en', 'The government reference number (SIREN) of the seller headquarter (eC506.1154 in RFF+GN for NAD+CO) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_CO_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+CO {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY', 'en', 'The VAT registration number of the seller headquarter (eC506.1154 in RFF+VA for NAD+CO) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_CO_MORE_THAN_ONE', 'en', 'More than one VAT code (RFF+VA) for the seller headquarter (NAD+CO) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_LC_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+LC is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY', 'en', 'The government reference number (SIREN) of the VAT fiscal representative (eC506.1154 in RFF+GN for NAD+LC) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_LC_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+LC {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY', 'en', 'The VAT registration number of the VAT fiscal representative (eC506.1154 in RFF+VA for NAD+LC) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_LC_MORE_THAN_ONE', 'en', 'More than one VAT code (RFF+VA) for the VAT fiscal representative (NAD+LC) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_RE_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+RE is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+GN for NAD+RE is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_RE_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+RE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+VA for NAD+RE is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_RE_MORE_THAN_ONE', 'en', 'More than one RFF+VA for NAD+RE {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DL_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+DL is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+GN for NAD+DL is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DL_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+DL {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+VA for NAD+DL is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DL_MORE_THAN_ONE', 'en', 'More than one RFF+VA for NAD+DL {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_XA_DP_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+XA for NAD+DP is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+GN for NAD+DP is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_GN_DP_MORE_THAN_ONE', 'en', 'More than one RFF+GN for NAD+DP {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_IDENT_EMPTY', 'en', 'eC506.1154 in RFF+VA for NAD+DP is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFF_VA_DP_MORE_THAN_ONE', 'en', 'More than one RFF+VA for NAD+DP {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_CUX_MORE_THAN_ONE', 'en', 'More than one segment CUX (g7) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA113_g48_EMPTY', 'en', 'the prepaid amount (eC516.5004 in MOA+113 g48 segment) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_MOA9_g48_EMPTY', 'en', 'The amount due (eC516.5004 in MOA+9 g48 segment) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_g48_EMPTY', 'en', 'The prepaid reference (eC506.1154 in RFF+PQ g48) is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_RFFPQ_MISSING', 'en', 'The prepaid reference (RFF+PQ g48 segment) is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_DTM113_MISSING', 'en', 'The prepaid reference date (DTM+171 segment after MOA+113 g48) is missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_DESC_MISSING', 'en', 'ALC(g25) description missing (eC214.7160.1,eC214.7160.2) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_MORE_THAN_ONE', 'en', 'More than one percentage for line allowance/charge (PCD g25 after ALC segment) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_QUALIFIER_NOT_EXPECTED', 'en', 'PCD(g25) qualifier is not expected after ALC segment. Should be 1 or 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_A', 'en', 'PCD+2 (g25) can not follow ALC+A {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_C', 'en', 'PCD+1 (g25) can not follow ALC+C {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_PCD_g25_EMPTY', 'en', 'eC501.5482 in PCD g25 seg is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_MORE_THAN_ONE', 'en', 'More than one amount for line allowance/charge (MOA g25 found after ALC segment) {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_g25_EMPTY', 'en', 'eC516.5004 in MOA g25 seg is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_A', 'en', 'MOA+23 (g25) can not follow ALC+A {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_C', 'en', 'MOA+204 (g25) can not follow ALC+C {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM_1004_EMPTY', 'en', 'e1004 in BGM is empty {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_g25_PCD_MOA_EMPTY', 'en', 'MOA+204(g25) or MOA+23(g25) or PCD+1(g25) or PCD+2(g25) are missing {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_C_REGLMT_CODE', 'en', 'The settlement means code (e4471 ALC+C(g15)) should be 1 or 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_ALC_A_REGLMT_CODE', 'en', 'The settlement means code (e4471 ALC+A(g15)) should be 1 or 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_C_REGLMT_CODE', 'en', 'The settlement means code (e4471 ALC+C(g38)) should be 1 or 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_LINE_ALC_A_REGLMT_CODE', 'en', 'The settlement means code (e4471 ALC+A(g38)) should be 1 or 2 {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TAX_7_VAT_E_NO_FTXSIN', 'en', 'TAX+7+VAT++++E without FTX+SIN {0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_TOD_4055', 'en', 'No code of delivery terms wheras TOD segment found in the message{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('XCBL_ERROR_BGM1225', 'en', 'Unknown message function code (BGM segment){0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0010', 'en', 'FILENAME argument is not set{0}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('99InternalError0005', 'en', 'Trace level not defined{0}', 1);