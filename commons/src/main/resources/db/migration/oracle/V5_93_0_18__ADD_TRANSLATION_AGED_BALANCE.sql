INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'invoiceNumber', 'Invoice', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'invoiceDueDate', 'Due date', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'invoiceTotal', 'Amount', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'daysToPayment', 'Days to Payment', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'invoiceNumber', 'Facture', 'fr', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'invoiceDueDate', 'Date d''échéance', 'fr', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'invoiceTotal', 'Montant', 'fr', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'daysToPayment', 'Délais de paiement', 'fr', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'payablesNotDue', 'payablesNotDue', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'notOverdueNext30Days', 'notOverdueNext30Days', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'overdue0to30Days', 'overdue0to30Days', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'overdue31to60Days', 'overdue31to60Days', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'overdue61to90Days', 'overdue61to90Days', 'en', SYSDATE, 21);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'overdueMoreThan90Days', 'overdueMoreThan90Days', 'en', SYSDATE, 21);