-- Add parameter column --
ALTER TABLE XTD_DOCUMENT_ERROR ADD ERROR_PARAMS VARCHAR2(255 char);
-- Create link to document --
ALTER TABLE XTD_DOCUMENT ADD CONSTRAINT UK_EYWTXW90LAMUV60K3SC7N455H UNIQUE (DOC_UUID);
ALTER TABLE XTD_DOCUMENT_ERROR ADD CONSTRAINT FKJE6AR4TFIJLAQPMLMESAOVS7W FOREIGN KEY (DOC_UUID) REFERENCES XTD_DOCUMENT(DOC_UUID) ON DELETE CASCADE;

-- PopulateRecapListError --
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'nl', '<PERSON><PERSON><PERSON> van de samenvattende lijst: fout bij het kopi<PERSON><PERSON> van de eigenschappen van het document.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'en', 'Populate RecapList : Error while copying document properties in recaplist item.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'es', 'Alimentación de la lista global: error al copiar las propiedades del documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'fi', 'Populate RecapList : Error while copying document properties in recaplist item.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'fr', 'Alimentation de la liste récapitulative :Erreur en copient les propriétés du document.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'it', 'Alimentazione lista riepilogativa : Errore durante la copia delle proprietà del documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_copy_doc_props', 'pt', 'Alimentação da lista recapitulativa: Erro ao copiar as propriedades do documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_code', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De code ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_code', 'en', 'Populate RecapList : Sender informations are not complete. Missing code.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_code', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. Le code est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_name', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. Le nom est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_name', 'en', 'Populate RecapList : Sender informations are not complete. Missing name.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_name', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De naam ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_legalcode', 'en', 'Populate RecapList : Sender informations are not complete. Missing registration or reference or VAT code.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_legalcode', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. Les codes RCS, TVA et SIREN est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_legalcode', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De RCS, BTW en SIREN codes ontbreken.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_street', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. Le nom de la rue est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_street', 'en', 'Populate RecapList : Sender informations are not complete. Missing street name.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_street', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De naam van de straat ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_postalcode', 'en', 'Populate RecapList : Sender informations are not complete. Missing postal code.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_postalcode', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. Le code postal est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_postalcode', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De postcode ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_city', 'en', 'Populate RecapList : Sender informations are not complete. Missing city.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_city', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. La ville est manquante.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_city', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. De stad ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_country', 'en', 'Populate RecapList : Sender informations are not complete. Missing country.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_country', 'fr', 'Alimentation de la liste récapitulative : Les informations de l''émetteur ne sont pas complètes. Le pays est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_legaleEntity_country', 'nl', 'Opvullen van de samenvattende lijst: De info van de verzender is niet volledig. Het land ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_code', 'en', 'Populate RecapList : Receiver informations are not complete. Missing code.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_code', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. Le code est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_code', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De code ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_name', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De naam ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_name', 'en', 'Populate RecapList : Receiver informations are not complete. Missing name.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_name', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. Le nom est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_legalcode', 'en', 'Populate RecapList : Receiver informations are not complete. Missing registration or reference or VAT code.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_legalcode', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. Les codes RCS, TVA et SIREN est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_legalcode', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De RCS, BTW en SIREN codes ontbreken.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_street', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. Le nom de la rue est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_street', 'en', 'Populate RecapList : Receiver informations are not complete. Missing street name.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_street', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De naam van de straat ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_postalcode', 'en', 'Populate RecapList : Receiver informations are not complete. Missing postal code.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_postalcode', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. Le code postal est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_postalcode', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De postcode ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_city', 'en', 'Populate RecapList : Receiver informations are not complete. Missing city.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_city', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. La ville est manquante.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_city', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. De stad ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_country', 'en', 'Populate RecapList : Receiver informations are not complete. Missing country.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_country', 'fr', 'Alimentation de la liste récapitulative : Les informations du destinataire ne sont pas complètes. Le pays est manquant.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('uncomplete_dematPartner_country', 'nl', 'Opvullen van de samenvattende lijst: De info van de bestemmeling is niet volledig. Het land ontbreekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_software_version', 'en', 'Populate RecapList : Software version can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_software_version', 'es', 'Alimentación de la lista global: no se ha podido encontrar la versión del software.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_software_version', 'fr', 'Alimentation de la liste récapitulative : La version du logiciel n''a pas pu être trouvée.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_software_version', 'it', 'Alimentazione lista riepilogativa : Versione del software non trovata.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_software_version', 'nl', 'Opvullen van de samenvattende lijst: De softwareversie werd niet gevonden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_software_version', 'pt', 'Alimentação da lista recapitulativa: A versão do software não pôde ser encontrada.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'es', 'Alimentación de la lista global: el tipo del documento no está rellenado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'en', 'Populate RecapList : Message type is empty.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'fr', 'Alimentation de la liste récapitulative : Le type du document n''est pas renseigné.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'it', 'Alimentazione lista riepilogativa :Il tipo messaggio è vuoto.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'nl', 'Opvullen van de samenvattende lijst: Het documenttype werd niet verstrekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'pt', 'Alimentação da lista recapitulativa: O tipo de documento não está preenchido.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_type', 'sv', 'Populate RecapList : Message type is empty.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_number', 'es', 'Alimentación de la lista global: el número del documento no está rellenado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_number', 'en', 'Populate RecapList : Message number is empty.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_number', 'fr', 'Alimentation de la liste récapitulative : Le numéro du document n''est pas renseigné.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_number', 'it', 'Alimentazione lista riepilogativa : Il numero di messaggio è vuoto.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_number', 'nl', 'Opvullen van de samenvattende lijst: Het documentnummer werd niet verstrekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_number', 'pt', 'Alimentação da lista recapitulativa: O número do documento não entrou.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'it', 'Alimentazione lista riepilogativa : La data del messaggio è illeggibile.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'en', 'Populate RecapList : Message date is unreadable.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'es', 'Alimentación de la lista global: No se puede leer la fecha del documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'fi', 'Populate RecapList : Message date is unreadable.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'fr', 'Alimentation de la liste récapitulative : La date du document ne peut être lue.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'nl', 'Opvullen van de samenvattende lijst: De datum van het document kan niet gelezen worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_date', 'pt', 'Alimentação da lista recapitulativa: A data do documento não pode ser lida.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_preparation_date', 'en', 'Populate RecapList : Message preparation date is unreadable', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_preparation_date', 'es', 'Alimentación de la lista global: No se puede leer la fecha de preparación del documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_preparation_date', 'fr', 'Alimentation de la liste récapitulative : La date et heure de préparation du document ne peut être lue.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_preparation_date', 'it', 'Alimentazione lista riepilogativa : La data di preparazione del messaggio è illeggibile.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_preparation_date', 'nl', 'Opvullen van de samenvattende lijst: De datum en de tijd van de voorbereiding van het document kunnen niet gelezen worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_message_preparation_date', 'pt', 'Alimentação da lista recapitulativa: A data e hora da preparação do documento pode ser lido.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_taxable_amount', 'en', 'Populate RecapList : Taxable amount is unreadable.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_taxable_amount', 'es', 'Alimentación de la lista global: No se puede leer el importe de la base impositiva.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_taxable_amount', 'fr', 'Alimentation de la liste récapitulative : Le montant taxable ne peut être lu.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_taxable_amount', 'it', 'Alimentazione lista riepilogativa : l''imponibile è illeggibile.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_taxable_amount', 'nl', 'Opvullen van de samenvattende lijst: Het belastbaar bedrag kan niet gelezen worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_taxable_amount', 'pt', 'Alimentação da lista recapitulativa: O valor tributável não pode ser lido.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_ati_amount', 'en', 'Populate RecapList : ATI amount is unreadable.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_ati_amount', 'es', 'Alimentación de la lista global: No se puede leer el importe total con IVA incluido.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_ati_amount', 'fr', 'Alimentation de la liste récapitulative : Le montant total TTC ne peut être lu.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_ati_amount', 'it', 'Alimentazione lista riepilogativa : l''ammontare totale è illeggibile.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_ati_amount', 'nl', 'Opvullen van de samenvattende lijst: Het totaal bedrag alle taksen inbegrepen kan niet gelezen worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_ati_amount', 'pt', 'Alimentação da lista recapitulativa: O montant total c/ IVA não pode ser lido.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_tax_amount', 'en', 'Populate RecapList : Tax amount is unreadable.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_format_tax_amount', 'fr', 'Alimentation de la liste récapitulative : Le montant de taxe ne peut être lu.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_recaplist', 'en', 'eDocument Process : Recap list can not be retrieved or created.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_recaplist', 'es', 'Proceso de facturado  electronica : imposible de recuperar o crear la lista recapitulativa', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_recaplist', 'fr', 'Processus de facture électronique : Impossible de récupérer ou créer la liste récapitulative.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_recaplist', 'it', 'Processo della fattura elettronica: impossibile creare oppure recuperare la lista', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_recaplist', 'nl', 'Proces van elektronische factuur: Onmogelijk de samenvattende lijst op te halen of aan te maken.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unknown_legalEntity', 'en', 'Populate RecapList : Legal entity {0} does not exist in this environment.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unknown_legalEntity', 'fr', 'Alimentation de la liste récapitulative: l''entité légale {0} n''existe pas dans cet environnement.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unknown_legalEntity', 'nl', 'Opvullen van de samenvattende lijst: De rechtspersoon bestaat niet in deze omgeving.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unknown_dematPartner', 'en', 'Populate RecapList : Demat partner {0} does not exist in this environment.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unknown_dematPartner', 'fr', 'Alimentation de la liste récapitulative : Le partenaire de dématérialisation {0} n''existe pas dans cet environnement.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unknown_dematPartner', 'nl', 'Opvullen van de samenvattende lijst: De dematerialisatiepartner bestaat niet in deze omgeving.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_legalEntity_code', 'en', 'Populate RecapList : Legal entity code is empty.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_legalEntity_code', 'fr', 'Alimentation de la liste récapitulative : Le code de l''entité juridique n''est pas renseigné.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_legalEntity_code', 'nl', 'Opvullen van de samenvattende lijst: De code van de rechtspersoon werd niet verstrekt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_dematPartner_code', 'en', 'Populate RecapList : Demat partner code is empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_dematPartner_code', 'fr', 'Alimentation de la liste récapitulative : Le code du partenaire de dématérialisation n''est pas renseigné.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_dematPartner_code', 'nl', 'Opvullen van de samenvattende lijst: De code van de dematerialisatiepartner werd niet verstrekt.', 1);
-- ExchangeNotAllowedException --
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('document_kind_unknown', 'en', 'Document kind is required in a allowed exchange', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('document_kind_unknown', 'es', 'La naturaleza del documento es obligatoria para el intercambio autorizado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('document_kind_unknown', 'fr', 'Le type du document est obligatoire pour l''échange autorisé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('document_kind_unknown', 'it', 'La natura del documento è obbligatoria per l''interscambio', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('document_kind_unknown', 'nl', 'Het documenttype is verplicht voor de toegestane uitwisseling', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('document_kind_unknown', 'pt', 'A natureza do documento é necessário para o intercâmbio autorizado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('recipient_missing', 'en', 'Recipient is required in a allowed exchange', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('recipient_missing', 'es', 'El destinatario (recipient) es obligatorio para el intercambio autorizado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('recipient_missing', 'fr', 'Le destinataire est obligatoire pour l''échange autorisé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('recipient_missing', 'it', 'Destinatario obbligatorio per l''interscambio', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('recipient_missing', 'nl', 'De bestemmeling is verplicht voor de toegestane uitwisseling', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('recipient_missing', 'pt', 'O destinatário é necessário para troca autorizado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_from', 'es', 'El emisor (sender) es obligatorio para el intercambio autorizado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_from', 'en', 'Sender is required in a allowed exchange', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_from', 'fr', 'L''émetteur (sender) est obligatoire pour l''échange autorisé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_from', 'it', 'Mittente obbligatorio per l''interscambio', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_from', 'nl', 'De verzender (sender) is verplicht voor de toegestane uitwisseling', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_from', 'pt', 'O emissor (sender) é obrigatório para o intercâmbio autorizado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_to', 'en', 'Receiver is required in a allowed exchange', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_to', 'es', 'El interlocutor (receiver) es obligatorio para el intercambio autorizado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_to', 'fr', 'L''interlocuteur (recipient) est obligatoire pour l''échange autorisé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_to', 'it', 'Ricevitore obbligatorio per l''interscambio', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_to', 'nl', 'De ontvanger (recipient) is verplicht voor de toegestane uitwisseling', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('wrong_to', 'pt', 'O interlocutor (recipient) é necessário para o intercâmbio autorizado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unauthorized_exchange_receiving', 'en', '{0} is not allowed to receive this type of document from {1}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unauthorized_exchange_receiving', 'fr', '{0} n''est pas autorisé à recevoir ce type de document de {1}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unauthorized_exchange_receiving', 'nl', '{0} mag dit documenttype niet ontvangen van {1}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unauthorized_exchange_sending', 'en', '{0} is not allowed to send this type of document to {1}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unauthorized_exchange_sending', 'fr', '{0} n''est pas autorisé à envoyer ce type de document à {1}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unauthorized_exchange_sending', 'nl', '{0} mag dit documenttype niet verzenden aan {1}', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('partner_information_missing', 'en', 'Some partner information is missing', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('partner_information_missing', 'fr', 'Certaines informations du partenaire sont manquantes', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('processing_way_missing', 'en', 'Processing way was not defined on the document', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('processing_way_missing', 'fr', 'Le sens de traitement n''a pas été défini sur le document', 1);
-- EDocumentProcessException --
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_xcbl_file', 'en', 'eDocument Process : Document should have one and only one associated file with a XCBL file type.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_xcbl_file', 'es', 'Proceso de facturado  electrónica: El documento debería tener un DocumentFile asociado de tipo XCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_xcbl_file', 'fr', 'Processus de facture électronique : Le document devrait avoir un seul fichier associé de type XCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_xcbl_file', 'it', 'Processo eDocument : Il documento dovrebbe avere solo un DocumentFile di tipo XCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_xcbl_file', 'nl', 'Proces van elektronische factuur: Het document mag slechts een gelinkt bestand van het type XCBL hebben.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_xcbl_file', 'pt', 'Processo de fatura eletrónica: O documento deveria ter uma DocumentFile associada com um tipo xCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_processing_way', 'en', 'eDocument Process : Error retrieving process variable ProcessingWay.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_processing_way', 'es', 'Proceso de facturado  electrónica: Error al recuperar la variable ProcessingWay.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_processing_way', 'fr', 'Processus de facture électronique : Erreur lors de la récupération de la variable ProcessingWay.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_processing_way', 'nl', 'Proces van elektronische factuur: Fout bij het ophalen van de variabele ProcessingWay.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('error_processing_way', 'pt', 'Processo de fatura eletrónica: Erro ao recuperar a variável ProcessingWay.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message', 'en', 'eDocument Process : Message is null', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message', 'es', 'Proceso de facturado  electrónica: El mensaje es \"null\".', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message', 'fr', 'Processus de facture électronique : Le message est null.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message', 'it', 'Processo eDocument : Messaggio non valido (null).', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message', 'nl', 'Proces van elektronische factuur: Het bericht is null.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message', 'pt', 'Processo de fatura electrónica: A mensagem está vazia.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_file_uri', 'en', 'eDocument Process : Message file does not exist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_file_uri', 'es', 'Proceso de facturado  electrónica: El fichero que contiene el mensaje no existe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_file_uri', 'fr', 'Processus de facture électronique : Le fichier contenant le message n''existe pas.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_file_uri', 'it', 'Processo eDocument : Il file del messaggio non esiste.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_file_uri', 'nl', 'Proces van elektronische factuur: Het bestand met het bericht bestaat niet.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('null_message_file_uri', 'pt', 'Processo de fatura eletrónica: O ficheiro que contém a mensagem não existe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('not_exists_xcbl_file', 'en', 'eDocument Process : XCBL file for this message does not exist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('not_exists_xcbl_file', 'es', 'Proceso de facturado  electrónica: El fichero XCBL del mensaje no existe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('not_exists_xcbl_file', 'fr', 'Processus de facture électronique : Le fichier XCBL du message n''existe pas.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('not_exists_xcbl_file', 'it', 'Processo eDocument : Il file XCBL file per questo messaggio non esiste.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('not_exists_xcbl_file', 'nl', 'Proces van elektronische factuur: Het XCBL bestand van het bericht bestaat niet.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('not_exists_xcbl_file', 'pt', 'Processo de fatura eletrónica: O ficheiro xCBL da mensagem não existe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unparsable_xcbl_file', 'en', 'eDocument Process : XCBL file for this message can not be parsed.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unparsable_xcbl_file', 'es', 'Proceso de facturado  electrónica: No se puede leer el fichero XCBL del mensaje.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unparsable_xcbl_file', 'fr', 'Processus de facture électronique : Le fichier XCBL du message ne peut être lu.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unparsable_xcbl_file', 'it', 'Processo eDocument : Il file XCBL per questo messaggio non può essere analizzato.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unparsable_xcbl_file', 'nl', 'Proces van elektronische factuur: Het XCBL bestand van het bericht kan niet gelezen worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('unparsable_xcbl_file', 'pt', 'Processo de fatura eletrónica: O ficheiro xCBL da mensagem não pode ser lido.', 1);
-- CertificateVerifyingTaskManager --
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_instance_company', 'en', 'The referenced customer is not an environment customer.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_instance_company', 'es', 'La actividad de la empresa citada no se inscribe en el medio ambiente.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_instance_company', 'fr', 'Le client référencée n''est pas un client de l''environnement.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_instance_company', 'nl', 'De klantenreferentie bestaat niet in deze omgeving.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_instance_company', 'pt', 'A empresa referenciada não é uma empresa do ambiente.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'en', 'The referenced customer can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'de', 'The referenced customer can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'es', 'La empresa citada no existe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'fi', 'The referenced customer can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'fr', 'Le client référencé n''existe pas.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'it', 'La società referenziata non può essere trovata.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'nl', 'De klantenreferentie bestaat niet.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'no', 'The referenced customer can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'pl', 'The referenced customer can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'pt', 'A empresa referenciada não existe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_referenced_company_not_found', 'sv', 'The referenced customer can not be found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_error', 'en', 'Certificate verification failed.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_error', 'es', 'Error al verificar el certificado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_error', 'fr', 'La vérification de certificat a échoué.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_error', 'it', 'Verifica del certificato fallita.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_error', 'nl', 'De verificatie van het certificaat is mislukt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_error', 'pt', 'A verificação de certificado falhou.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_no_cert', 'en', 'No valid certificate found.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_no_cert', 'es', 'No se ha encontrado ningún certificado válido.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_no_cert', 'fr', 'Aucun certificat valide n''a été trouvé.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_no_cert', 'it', 'Nessun certificato valido trovato.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_no_cert', 'nl', 'Er werd geen enkel geldig certificaat gevonden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_cert_verif_no_cert', 'pt', 'Nenhum certificado válido foi encontrado.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_handler_error', 'en', 'Error while trying to get signature handler.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_handler_error', 'es', 'Error al recuperar el controlador de firmas.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_handler_error', 'fr', 'Erreur lors de la récupération du handler de signature.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_handler_error', 'it', 'Errore durante il recupero del gestore di firme.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_handler_error', 'nl', 'Fout bij het ophalen van de handler van de handtekening.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_handler_error', 'pt', 'Erro ao recuperar o registo de assinatura.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_error', 'en', 'Error while signing document.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_error', 'es', 'Error al firmar el documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_error', 'fr', 'La signature du document a échoué.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_error', 'it', 'Errore durante la firma del documento.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_error', 'nl', 'De ondertekening van het document is mislukt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_error', 'pt', 'A assinatura do documento falhou.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_xcbl_publickey_error', 'en', 'Can not insert public key in XCBL file.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_xcbl_publickey_error', 'es', 'No se puede insertar la clave pública en el fichero XCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_xcbl_publickey_error', 'fr', 'La clé publique ne peut pas être insérée dans le fichier XCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_xcbl_publickey_error', 'it', 'Impossibile inserire la chiave pubblica nel file XCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_xcbl_publickey_error', 'nl', 'De publieke sleutel kan niet in het XCBL bestand geschreven worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_xcbl_publickey_error', 'pt', 'A chave pública não pode ser inserida no arquivo xCBL.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_get_publickey_error', 'en', 'Error while retrieving public key.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_get_publickey_error', 'es', 'Error al recuperar la clave pública.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_get_publickey_error', 'fr', 'Erreur lors de la récupération de la clé publique.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_get_publickey_error', 'it', 'Errore durante il recupero della chiave pubblica.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_get_publickey_error', 'nl', 'Fout bij het ophalen van de publieke sleutel.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_get_publickey_error', 'pt', 'Erro ao recuperar a chave pública.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_verif_refused', 'en', 'Signature verification failed.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_verif_refused', 'es', 'Error al verificar la firma.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_verif_refused', 'fr', 'La vérification de signature a échouée.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_verif_refused', 'it', 'Verifica della firma fallita.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_verif_refused', 'nl', 'De verificatie van de handtekening is mislukt.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_verif_refused', 'pt', 'A verificação de assinatura falhou.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_retrieve_trusted_certs', 'en', 'Error reading embedded cert file.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('signature_retrieve_trusted_certs', 'fr', 'Le fichier de certificats embarqués ne peut être lu.', 1);
-- Legal controls --
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'sv', 'The detail of invoice type is not filled.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_references_empty', 'pt', 'Referências da Factura', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'pl', 'The detail of invoice type is not filled.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'no', 'The detail of invoice type is not filled.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_empty', 'nl', 'Het intracommunautaire BTW nummer van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_notunique', 'nl', 'De RCS-RCM van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_notunique', 'nl', 'De stad van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_number_empty', 'nl', 'Het nummer van elk voorschot moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_notunique', 'nl', 'De verkoper kan slechts een bedrijfsnaam hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_empty', 'nl', 'De postcode van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_notunique', 'nl', 'Er mag slechts een taksbedrag voor elk BTW tarief dat in het overzicht staat verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_notunique', 'nl', 'Er kan slechts een hoeveelheid op elke lijn opgegeven worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notequal', 'nl', 'De munteenheid voor de BTW betaling moet de euro zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_empty', 'nl', 'Het intracommunautaire BTW nummer van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting__notunique', 'nl', 'De munteenheid voor de BTW betaling moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_empty', 'nl', 'De stad van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_illegalvalue', 'nl', 'Het voorafbetaald bedrag is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_missing', 'nl', 'De verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_tax_unmatched', 'nl', 'De taks op de lijn verschijnt niet in het overzicht van de taksen onderaan het bericht.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_empty', 'nl', 'De RCS-RCM van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_description_empty', 'nl', 'De omschrijving van een korting of kost op het briefhoofd moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_notunique', 'nl', 'De bruto eenheidsprijs zonder taksen moet op elke lijn uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_illegalvalue', 'nl', 'De datum/de tijd van de voorbereiding van het document is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_empty', 'nl', 'De SIREN van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_notunique', 'nl', 'Het tarief of het verstrekt bedrag van de kost op de lijn moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_empty', 'nl', 'Het totale bedrag met alle taksen inbegrepen moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_illegalvalue', 'nl', 'Het totale taksbedrag is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_illegalvalue', 'nl', 'Het bedrag van de korting op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_empty', 'nl', 'Het voorafbetaald bedrag moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_unmatched', 'nl', 'Het tarief van de taks ontbreekt in de rest van het bericht', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_notunique', 'nl', 'De RCS-RCM van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_empty', 'nl', 'De postcode van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_notunique', 'nl', 'Het tarief of het verstrekt bedrag van de korting op de lijn moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_notunique', 'nl', 'De fiscale vertegenwoordiger kan slechts een bedrijfsnaam hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_latepayment_term_empty', 'nl', 'De vaste boetevergoeding moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_percent_illegalvalue', 'nl', 'Het tarief van de kost op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_empty', 'nl', 'De stad van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_empty', 'nl', 'De bedrijfsnaam van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_notunique', 'nl', 'De stad van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_illegalvalue', 'nl', 'Het totale belastbare bedrag is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_empty', 'nl', 'Het tarief of het bedrag van de parafiscale taks op de lijn moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_percent_illegalvalue', 'nl', 'Het tarief van de parafiscale taks op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_notunique', 'nl', 'Het tarief of het verstrekt bedrag van de parafiscale taks op de lijn moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_empty', 'nl', 'De postcode van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_endDate_illegalvalue', 'nl', 'Het einde van de referentieperiode is niet juist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notunique', 'nl', 'Het tarief van de taks werd dubbel doorgestuurd', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_notunique', 'nl', 'De documentdatum moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_empty', 'nl', 'Het intracommunautaire BTW nummer van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_notunique', 'nl', 'Het begin van de referentieperiode moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_global_tax_unmatched', 'nl', 'Het tarief van de taks ontbreekt in de rest van het bericht', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_notunique', 'nl', 'Het land van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_empty', 'nl', 'De bedrijfsnaam van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_empty', 'nl', 'Het BTW tarief van de korting of kost op het briefhoofd moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_notunique', 'nl', 'De gefactureerde kan slechts een bedrijfsnaam hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_empty', 'nl', 'Het land van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_start_date_illegalvalue', 'nl', 'Het begin van de referentieperiode is niet juist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_empty', 'nl', 'De RCS-RCM van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_notunique', 'nl', 'Het intracommunautaire BTW nummer van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_empty', 'nl', 'De stad van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_empty', 'nl', 'De bedrijfsnaam van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_notunique', 'nl', 'De stad van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_illegalvalue', 'nl', 'Het bedrag van de parafiscale taks op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_term_empty', 'nl', 'De disconteringvoorwaarden moeten verstrekt worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_notunique', 'nl', 'De postcode van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_empty', 'nl', 'De SIREN van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_notunique', 'nl', 'Het land van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_illegalvalue', 'nl', 'Het begin van de referentieperiode is niet juist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_empty', 'nl', 'Het adres van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_end_date_illegalvalue', 'nl', 'Het einde van de referentieperiode is niet juist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_illegalvalue', 'nl', 'Het bedrag van de kost op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_notunique', 'nl', 'Het intracommunautaire BTW nummer van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_percent_illegalvalue', 'nl', 'Het tarief of het bedrag van de kost op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_payment_term_empty', 'nl', 'De vaste boetevergoeding moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_empty', 'nl', 'Het adres van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_penalty_term_empty', 'nl', 'De boetevoorwaarden moeten verstrekt worden.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_empty', 'nl', 'De RCS-RCM van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_illegalvalue', 'nl', 'De documentdatum is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_empty', 'nl', 'Het land van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_notunique', 'nl', 'Het intracommunautaire BTW nummer van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_notunique', 'nl', 'De postcode van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_reference_date_illegalvalue', 'nl', 'De datum van het referentiedocument is niet juist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_empty', 'nl', 'De vervaldatum moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_percent_illegalvalue', 'nl', 'Het tarief van de korting op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_references_empty', 'nl', 'De inlichtingen van het referentiedocument zijn onvolledig', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_illegalvalue', 'nl', 'De datum van de betaling van het voorschot is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_vat_rate_empty', 'nl', 'Het BTW overzicht moet een BTW tarief opgeven', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_empty', 'nl', 'De bedrijfsnaam van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_illegalvalue', 'nl', 'De hoeveelheid van de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_notunique', 'nl', 'De SIREN van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_allowanceorcharge_tax_unmatched', 'nl', 'Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_empty', 'nl', 'Het type korting of kost op het briefhoofd moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_notunique', 'nl', 'Het documenttype moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_rate_empty', 'nl', 'Buyer VAT code', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percent_illegalvalue', 'nl', 'Het percentage van de korting of kost op het briefhoofd is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_notunique', 'nl', 'Er mag slechts een totaal taksbedrag opgegeven worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_notunique', 'nl', 'De stad van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_notunique', 'nl', 'Het adres van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_empty', 'nl', 'De bruto eenheidsprijs zonder taksen moet op elke lijn verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_percent_illegalvalue', 'nl', 'Het tarief of het bedrag van de parafiscale taks op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_empty', 'nl', 'Een taksbedrag moet verstrekt worden voor elk BTW tarief dat in het overzicht staat', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_empty', 'nl', 'Het land van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_empty', 'nl', 'De RCS-RCM van de fiscale vertegenwoordiger moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_illegalvalue', 'nl', 'Het taksbedrag is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_base_item_detail_empty', 'nl', 'De omschrijving van de goederen en diensten moet op elke lijn verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_empty', 'nl', 'Het documentnummer moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_empty', 'nl', 'Het land van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_notunique', 'nl', 'De maatschappelijke zetel kan slechts een bedrijfsnaam hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_notunique', 'nl', 'Het adres van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_illegalvalue', 'nl', 'Het bedrag van de korting of kost op het briefhoofd is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_notunique', 'nl', 'De postcode van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_missing', 'nl', 'De betalingsinfo werd niet verstrekt', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_illegalvalue', 'nl', 'De bruto eenheidsprijs zonder taksen is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notunique', 'nl', 'De munteenheid moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percentagemonetaryvalue_illegalvalue', 'nl', 'Het percentage of het bedrag van de korting of kost op het briefhoofd is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_missing', 'nl', 'De gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_missing', 'nl', 'De globale totalen moeten verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_payable_illegalvalue', 'nl', 'Het totale bedrag met alle taksen inbegrepen dat nog betaald moet worden, is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_empty', 'nl', 'Een belastbaar bedrag moet verstrekt worden voor elk BTW tarief dat in het overzicht staat', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_empty', 'nl', 'Het adres van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_empty', 'nl', 'Het totale taksbedrag moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_empty', 'nl', 'Het totale belastbare bedrag moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_notunique', 'nl', 'Er mag slechts een belastbaar bedrag voor elk BTW tarief dat in het overzicht staat verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_empty', 'nl', 'De datum van de betaling van het voorschot moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_missing', 'nl', 'Het overzicht van de BTW moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_registration_ident_notunique', 'nl', 'De SIREN van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_notunique', 'nl', 'Het adres van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_percent_illegalvalue', 'nl', 'Het tarief of het bedrag van de korting op de lijn is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_empty', 'nl', 'De datum/de tijd van de voorbereiding van het document moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_notunique', 'nl', 'De RCS-RCM van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_empty', 'nl', 'Het tarief of het bedrag van de kost op de lijn moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_notunique', 'nl', 'Het documentnummer moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'nl', 'Het detail van het documenttype moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notunique', 'nl', 'De munteenheid voor de BTW betaling moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_empty', 'nl', 'De stad van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_reference_period_empty', 'nl', 'De referentieperiode moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_notunique', 'nl', 'Het adres van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_empty', 'nl', 'Het documenttype moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_empty', 'nl', 'Het intracommunautaire BTW nummer van de gefactureerde moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_due_date_illegalvalue', 'nl', 'De datum van de betaling van het voorschot is niet juist.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_notunique', 'nl', 'Het land van de gefactureerde moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_missing', 'nl', 'De eenheidsprijs moet op elke lijn verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_empty', 'nl', 'Het adres van de maatschappelijke zetel moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_despatch_date_illegalvalue', 'nl', 'De verzendingsdatum is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_illegalvalue', 'nl', 'De vervaldatum is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_notunique', 'nl', 'De RCS-RCM van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_empty', 'nl', 'Het tarief of het bedrag van de korting op de lijn moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_notunique', 'nl', 'De SIREN van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_notunique', 'nl', 'Er kan slechts een enkel BTW tarief van de korting of kost op het briefhoofd verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_notunique', 'nl', 'Het intracommunautaire BTW nummer van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_registration_ident_notunique', 'nl', 'De SIREN van de fiscale vertegenwoordiger moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_notunique', 'nl', 'De postcode van de verkoper moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_notunique', 'nl', 'Het land van de maatschappelijke zetel moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_empty', 'nl', 'De netto eenheidsprijs zonder taksen moet op elke lijn verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_illegalvalue', 'nl', 'Het totale bedrag met alle taksen inbegrepen is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_notunique', 'nl', 'De netto eenheidsprijs zonder taksen moet op elke lijn uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_empty', 'nl', 'De postcode van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_illegalvalue', 'nl', 'De netto eenheidsprijs zonder taksen is niet juist', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_empty', 'nl', 'De documentdatum moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_structure_notunique', 'nl', 'De juridische vorm van de verkoper mag slechts een keer opgegeven worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_exempt_vat_rate_not_zero', 'nl', 'Het BTW tarief op de lijn moet gelijk zijn aan nul in het kader van een vrijstelling van taks.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registered_name_empty', 'nl', 'De bedrijfsnaam van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_desc_empty_or_notunique', 'nl', 'De omschrijving van de parafiscale taks op de lijn moet verstrekt worden en een parafiscale taks op de lijn kan slechts een omschrijving hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_capital_empty', 'nl', 'Het aandelenkapitaal van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_structure_empty', 'nl', 'De juridische vorm van de verkoper moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_desc_empty_or_notunique', 'nl', 'De omschrijving van de kost op de lijn moet verstrekt worden en een kost op de lijn kan slechts een omschrijving hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_empty', 'nl', 'Het BTW tarief op de lijn moet verstrekt worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_notunique', 'nl', 'Het BTW tarief op de lijn moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_regulatory_vat_empty_or_notunique', 'nl', 'Het BTW tarief dat bij een parafiscale taks op de lijn toegepast wordt, moet verstrekt worden en het BTW tarief dat bij een parafiscale taks op de lijn toegepast wordt, moet uniek zijn', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_desc_empty_or_notunique', 'nl', 'De omschrijving van de korting op de lijn moet verstrekt worden en een korting op de lijn kan slechts een omschrijving hebben', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notequal', 'nl', 'De munteenheid voor de betaling van de BTW werd niet verstrekt en de munteenheid is niet de euro.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registered_name_notunique', 'nl', 'De bedrijfsnaam van de verkoper mag slechts een keer opgegeven worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_capital_notunique', 'nl', 'Het aandelenkapitaal van de verkoper mag slechts een keer opgegeven worden', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notuniquevalue', 'nl', 'Het tarief van de taks werd dubbel doorgestuurd', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_description_empty', 'fr', 'La description d''une remise ou charge en entête doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_empty', 'fr', 'Le type d''une remise ou charge en entête doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percent_illegalvalue', 'fr', 'Le pourcentage d''une remise ou charge en entête est inccorect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_illegalvalue', 'fr', 'Le montant d''une remise ou charge en entête est inccorect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percentagemonetaryvalue_illegalvalue', 'fr', 'Le pourcentage ou le montant d''une remise ou charge en entête est inccorect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_empty', 'fr', 'Le taux de TVA d''une remise ou charge en entête doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_notunique', 'fr', 'Un seul taux de TVA de remise ou charge en entête peut être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_missing', 'fr', 'Le facturé à doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_empty', 'fr', 'Le pays du facturé à doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_notunique', 'fr', 'Le pays du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_empty', 'fr', 'La raison sociale du facturé à doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_notunique', 'fr', 'Le facturé à ne peut avoir plus d''une raison sociale', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_empty', 'fr', 'L''adresse du facturé à doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_notunique', 'fr', 'L''adresse du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_empty', 'fr', 'Le code postal du facturé à doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_notunique', 'fr', 'Le code postal du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_empty', 'fr', 'La ville du facturé à doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_notunique', 'fr', 'La ville du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_empty', 'fr', 'Le SIREN du facturé à doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_notunique', 'fr', 'Le SIREN du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_registration_ident_notunique', 'fr', 'Le RCS-RCM du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_empty', 'fr', 'Le code TVA intracommunautaire du facturé à doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_notunique', 'fr', 'Le code TVA intracommunautaire du facturé à doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_empty', 'fr', 'La date/heure de préparation du document doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_illegalvalue', 'fr', 'La date/heure de préparation du document n''est pas correcte', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_empty', 'fr', 'Le pays du représentant fiscal doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_notunique', 'fr', 'Le pays du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_empty', 'fr', 'La raison sociale du représentant fiscal doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_notunique', 'fr', 'Le représentant fiscal ne peut avoir plus d''une raison sociale', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_empty', 'fr', 'L''adresse du représentant fiscal doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_notunique', 'fr', 'L''adresse du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_empty', 'fr', 'Le code postal du représentant fiscal doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_notunique', 'fr', 'Le code postal du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_empty', 'fr', 'La ville du représentant fiscal doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_notunique', 'fr', 'La ville du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_empty', 'fr', 'Le SIREN du représentant fiscal doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_notunique', 'fr', 'Le SIREN du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_registration_ident_notunique', 'fr', 'Le RCS-RCM du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_empty', 'fr', 'Le code TVA intracommunautaire du représentant fiscal doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_notunique', 'fr', 'Le code TVA intracommunautaire du représentant fiscal doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_empty', 'fr', 'La date d''échéance doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_illegalvalue', 'fr', 'La date d''échéance n''est pas correcte', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_despatch_date_illegalvalue', 'fr', 'La date d''expédition n''est pas correcte', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_empty', 'fr', 'Le pays du siège social doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_notunique', 'fr', 'Le pays du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_empty', 'fr', 'La raison sociale du siège social doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_notunique', 'fr', 'Le siège social ne peut avoir plus d''une raison sociale', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_empty', 'fr', 'L''adresse du siège social doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_notunique', 'fr', 'L''adresse du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_empty', 'fr', 'Le code postal du siège social doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_notunique', 'fr', 'Le code postal du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_empty', 'fr', 'La ville du siège social doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_notunique', 'fr', 'La ville du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_empty', 'fr', 'Le SIREN du siège social doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_notunique', 'fr', 'Le SIREN du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_empty', 'fr', 'Le RCS-RCM du siège social doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_notunique', 'fr', 'Le RCS-RCM du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_empty', 'fr', 'Le code TVA intracommunautaire du siège social doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_notunique', 'fr', 'Le code TVA intracommunautaire du siège social doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notunique', 'fr', 'La devise de facturation doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notequal', 'fr', 'La devise de paiement de la TVA n''est pas renseignée et la devise de facturation n''est pas l''euro.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_empty', 'fr', 'La date du document doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_notunique', 'fr', 'La date du document doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_illegalvalue', 'fr', 'La date du document n''est pas correcte', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_empty', 'fr', 'Le numéro du document doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_notunique', 'fr', 'Le numéro du document doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_references_empty', 'fr', 'Les renseignements du document de référence sont incomplets', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_reference_date_illegalvalue', 'fr', 'La date du document de référence est incorrecte.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_empty', 'fr', 'Le type de document doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_notunique', 'fr', 'Le type de document doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'fr', 'Le nom du document (i.e. le type) doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_notunique', 'fr', 'Le début de la période de référence doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_reference_period_empty', 'fr', 'La période de référence doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_start_date_illegalvalue', 'fr', 'Le début de la période de référence est incorrecte.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_end_date_illegalvalue', 'fr', 'La fin de la période de référence est incorrecte.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_illegalvalue', 'fr', 'Le début de la période de référence est incorrecte.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_endDate_illegalvalue', 'fr', 'La fin de la période de référence est incorrecte.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_missing', 'fr', 'Les informations de paiement ne sont pas renseignées', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_payment_term_empty', 'fr', 'L''indemnité forfaitaire de pénalité doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_penalty_term_empty', 'fr', 'Les conditions de pénalités doivent être renseignées.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_term_empty', 'fr', 'Les conditions d''escompte doivent être renseignées.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_latepayment_term_empty', 'fr', 'L''indemnité forfaitaire de pénalité doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_due_date_illegalvalue', 'fr', 'La date de paiement de l''acompte est incorrecte.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_missing', 'fr', 'Le vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_empty', 'fr', 'Le pays du vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_notunique', 'fr', 'Le pays du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_empty', 'fr', 'La raison sociale du vendeur doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_notunique', 'fr', 'Le vendeur ne peut avoir plus d''une raison sociale', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_empty', 'fr', 'L''adresse du vendeur doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_notunique', 'fr', 'L''adresse du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_empty', 'fr', 'Le code postal du vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_notunique', 'fr', 'Le code postal du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_empty', 'fr', 'La ville du vendeur doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_notunique', 'fr', 'La ville du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_empty', 'fr', 'Le SIREN du vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_notunique', 'fr', 'Le SIREN du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registered_name_empty', 'fr', 'La raison sociale du vendeur doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registered_name_notunique', 'fr', 'La raison sociale du vendeur ne doit être indiquée qu''une seule fois', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_structure_empty', 'fr', 'La forme juridique du vendeur doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_structure_notunique', 'fr', 'La forme juridique du vendeur ne doivt être indiquée qu''une seule fois', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_capital_empty', 'fr', 'Le capital social du vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_capital_notunique', 'fr', 'Le capital social du vendeur ne doit être indiqué qu''une seule fois', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_empty', 'fr', 'Le RCS-RCM du vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_notunique', 'fr', 'Le RCS-RCM du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_empty', 'fr', 'Le code TVA intracommunautaire du vendeur doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_notunique', 'fr', 'Le code TVA intracommunautaire du vendeur doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting__notunique', 'fr', 'La devise de paiement de la TVA doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notequal', 'fr', 'La devise de paiement de la TVA doit être l''euro', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notunique', 'fr', 'La devise de paiement de la TVA doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_desc_empty_or_notunique', 'fr', 'La description de la taxe parafiscale à la ligne doit être renseignée et la taxe parafiscale à la ligne ne peut avoir qu''une seule description', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_empty', 'fr', 'Le taux ou le montant de la taxe parafiscale à la ligne doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_notunique', 'fr', 'Le taux ou le montant renseigné de la taxe parafiscale à la ligne doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_illegalvalue', 'fr', 'Le montant de la taxe parafiscale à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_percent_illegalvalue', 'fr', 'Le taux de la taxe parafiscale à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_percent_illegalvalue', 'fr', 'Le taux ou le montant de la taxe parafiscale à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_regulatory_vat_empty_or_notunique', 'fr', 'Le taux de TVA appliqué à une taxe parafiscale à la ligne doit être renseigné et le taux de TVA appliqué à une taxe parafiscale à la ligne doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_desc_empty_or_notunique', 'fr', 'La description d''une charge à la ligne doit être renseignée et une charge à la ligne ne peut avoir qu''une seule description', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_empty', 'fr', 'Le taux ou le montant d''une charge à la ligne doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_notunique', 'fr', 'Le taux ou le montant renseigné d''une charge à la ligne doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_illegalvalue', 'fr', 'Le montant de la charge à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_percent_illegalvalue', 'fr', 'Le taux de la charge à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_percent_illegalvalue', 'fr', 'Le taux ou le montant de la charge à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_desc_empty_or_notunique', 'fr', 'La description d''une remise à la ligne doit être renseignée et une remise à la ligne ne peut avoir qu''une seule description', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_empty', 'fr', 'Le taux ou le montant d''une remise à la ligne doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_notunique', 'fr', 'Le taux ou le montant renseigné d''une remise à la ligne doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_illegalvalue', 'fr', 'Le montant de la remise à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_percent_illegalvalue', 'fr', 'Le taux de la remise à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_percent_illegalvalue', 'fr', 'Le taux ou le montant de la remise à la ligne n''est pas correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_base_item_detail_empty', 'fr', 'La description des biens et des services doit être renseignée sur chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_missing', 'fr', 'Le prix unitaire doit être renseigné sur chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_empty', 'fr', 'Le prix unitaire brut HT doit être renseigné sur chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_notunique', 'fr', 'Le prix unitaire brut HT doit être unique à chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_illegalvalue', 'fr', 'Le prix unitaire brut HT est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_empty', 'fr', 'Le prix unitaire net HT doit être renseigné sur chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_notunique', 'fr', 'Le prix unitaire net HT doit être unique à chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_illegalvalue', 'fr', 'Le prix unitaire net HT est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_empty', 'fr', 'Le taux de TVA à la ligne doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_rate_empty', 'fr', 'Le taux de TVA à la ligne doit être renseigné.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_notunique', 'fr', 'Le taux de TVA à la ligne doit être unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_exempt_vat_rate_not_zero', 'fr', 'Le taux de TVA à la ligne doit être égal à zéro dans le cadre d''une exemption de taxe.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_notunique', 'fr', 'Une seule quantité peut être indiquée sur chaque ligne', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_illegalvalue', 'fr', 'La quantité de la ligne est incorrecte', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_empty', 'fr', 'La date de paiement d''un acompte doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_illegalvalue', 'fr', 'La date de paiement par acompte est incorrecte', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_number_empty', 'fr', 'Le numéro de chaque acompte doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_empty', 'fr', 'Le montant pré-payé doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_illegalvalue', 'fr', 'Le montant pré-payé est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_missing', 'fr', 'Les totaux globaux doivent être renseignés', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_empty', 'fr', 'Le montant de taxe total doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_notunique', 'fr', 'Un seul montant taxe total doit être indiqué', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_illegalvalue', 'fr', 'Le montant de taxe total est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_empty', 'fr', 'Le montant taxable total doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_illegalvalue', 'fr', 'Le montant taxable total est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_empty', 'fr', 'Le montant total TTC doit être renseigné', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_illegalvalue', 'fr', 'Le montant total TTC est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_payable_illegalvalue', 'fr', 'Le montant total TTC restant à payer est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_missing', 'fr', 'La ventilation de TVA doit être renseignée', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_empty', 'fr', 'Un montant de taxe doit être renseigné pour chaque taux de TVA ventilé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_notunique', 'fr', 'Un seul montant de taxe doit être renseigné pour chaque taux de TVA ventilé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_empty', 'fr', 'Un montant taxable doit être renseigné pour chaque taux de TVA ventilé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_notunique', 'fr', 'Un seul montant taxable doit être renseigné pour chaque taux de TVA ventilé', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_illegalvalue', 'fr', 'Le montant de taxe est incorrect', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_vat_rate_empty', 'fr', 'La ventilation de TVA doit indiqué un taux de TVA', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_tax_unmatched', 'fr', 'La taxe à la ligne n''apparait pas dans la ventilation de taxe en pied de message.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_global_tax_unmatched', 'fr', 'Le taux de taxe est manquant dans le reste du message', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_allowanceorcharge_tax_unmatched', 'fr', 'Le taux de taxe est manquant dans la ventilation de taxe en pied de message', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notunique', 'fr', 'Le taux de taxe est transmis en doublon dans le récapitulatif de TVA', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_unmatched', 'fr', 'Le taux de taxe est manquant dans le reste du message', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notuniquevalue', 'fr', 'Le taux de taxe est transmis en doublon dans le récapitulatif de TVA', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'fi', 'The detail of invoice type is not filled.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_empty', 'es', 'El codigo IVA intracomunitario de la sede social debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_notunique', 'es', 'La referencia gubernamental (SIREN) del facturado do debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_notunique', 'es', 'La ciudad de la sede social debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_number_empty', 'es', 'El numero de cada anticipo debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_notunique', 'es', 'El facturado do no puede tener más de una razon social', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_empty', 'es', 'El codigo postal de la sede social debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_notunique', 'es', 'Un unico importe de impuesto debe de ser informado para cada impuesto de IVA ventilado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_notunique', 'es', 'Una sola cantidad puede ser indicada en cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notequal', 'es', 'La divisa de pago del IVA debe de ser el euro', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_empty', 'es', 'El codigo IVA intracomunitario del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting__notunique', 'es', 'La divisa de pago del IVA debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_empty', 'es', 'La ciudad del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_illegalvalue', 'es', 'El importe pre-pagado es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_missing', 'es', 'El facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_tax_unmatched', 'es', 'La tasa a la linea no aparece en la ventilacion de impusto en pied de mensaje', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_empty', 'es', 'La referencia gubernamental (SIREN) del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_description_empty', 'es', 'La descripcion de una entrega o carga en encabezamiento debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_notunique', 'es', 'El precio unitario bruto sin IVA debe de ser unico a cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_illegalvalue', 'es', 'La fecha/hora de preparacion del documento no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_empty', 'es', 'El numero de registro (RCS-RCM) de la sede social debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_notunique', 'es', 'La tasa o el importe informado de una carga a la linea debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_empty', 'es', 'El importe total IVA incluido debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_illegalvalue', 'es', 'El importe del impuesto totales incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_illegalvalue', 'es', 'El importe de la entrega a la linea no esta correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_empty', 'es', 'El importe pre-pagado debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_unmatched', 'es', 'La tasa de impuesto falta en el resto del mensaje', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_notunique', 'es', 'La referencia gubernamental (SIREN) de la sede social debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_empty', 'es', 'El codigo postal del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_notunique', 'es', 'La tasa o el importe informado de una entrega a la linea debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_notunique', 'es', 'El representante fiscal no puede tener más de una razon social', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_latepayment_term_empty', 'es', 'La indemnizacion arancelaria de penalidad debe de ser infrormada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_percent_illegalvalue', 'es', 'La tasa de la carga a la linea no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_empty', 'es', 'La ciudad del representante fiscal debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_empty', 'es', 'La razon social del representante fiscal debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_notunique', 'es', 'La ciudad del facturado do debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_illegalvalue', 'es', 'El importe imponible total es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_empty', 'es', 'La tasa o el importe del impuesto parafiscal a la linea debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_percent_illegalvalue', 'es', 'La tasa del impuesto parafiscal a la linea no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_notunique', 'es', 'La tasa o el importe informado del impuesto parafiscal a la linea debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_empty', 'es', 'El codigo postal del representante fiscal debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_endDate_illegalvalue', 'es', 'El final del periodo de referencia es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notunique', 'es', 'La tasa de impuesto esta transmitida en doblon', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_notunique', 'es', 'La fecha del documento tiene que ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_empty', 'es', 'El codigo IVA intracomunitariodel representante fiscal debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_notunique', 'es', 'El principio del periodo de referencia debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_global_tax_unmatched', 'es', 'La tasa de impuesto falta en el resto del mensaje', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_notunique', 'es', 'El pais del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_empty', 'es', 'La razon social del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_empty', 'es', 'La tasa del IVA de una entrega o carga en encabezamiento es incorrecta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_notunique', 'es', 'El facturado do no puede tener más de una razon social', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_empty', 'es', 'El pais de la sede social tiene que ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_start_date_illegalvalue', 'es', 'El principio del periodo de referencia es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_empty', 'es', 'La referencia gubernamental (SIREN) del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_notunique', 'es', 'El codigo IVA intracomunitario de la sede social debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_empty', 'es', 'La ciudad del facturado do tiene que ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_empty', 'es', 'La razon social del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_notunique', 'es', 'La ciudad del facturado do debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_illegalvalue', 'es', 'El importe de la tasa parafiscal a la linea no es correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_term_empty', 'es', 'Las condiciones de descuento debe de ser informadas', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_notunique', 'es', 'El codigo postal del representante fiscal debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_empty', 'es', 'El numero de registro (RCS-RCM) del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_notunique', 'es', 'El pais del representante fiscal debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_illegalvalue', 'es', 'El principio del periodo de referencia es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_empty', 'es', 'La dirreccion del representante fiscal debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_end_date_illegalvalue', 'es', 'El final del periodo de referencia es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_illegalvalue', 'es', 'El importe de la carga a la linea no es correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_notunique', 'es', 'El codigo IVA intracomunitario del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_percent_illegalvalue', 'es', 'La tasa o el importe de la carga a la linea no es correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_payment_term_empty', 'es', 'La indemnizacion arancelaria de penalidad no esta informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_empty', 'es', 'La dirreccion del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_penalty_term_empty', 'es', 'Las condiciones de penalidades deben de ser informadas', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_empty', 'es', 'La referencia gubernamental (SIREN) de la sede social tiene que ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_illegalvalue', 'es', 'La fecha del documento no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_empty', 'es', 'El pais del representante fiscal debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_notunique', 'es', 'El codigo IVA del representante fiscal debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_notunique', 'es', 'El codigo postal de la sede social debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_reference_date_illegalvalue', 'es', 'La fecha del documento de referencia es incorrecta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_empty', 'es', 'La fecha de vencimiento debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_percent_illegalvalue', 'es', 'La tasa de la entrega a la linea no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_references_empty', 'es', 'Las informaciones del documento de referencia son incompletas', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_illegalvalue', 'es', 'La fecha de pago por anticipo es incorrecta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_vat_rate_empty', 'es', 'La ventilacion de IVA debe de indicar una tasa de IVA', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_empty', 'es', 'La razon social de la sede social debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_illegalvalue', 'es', 'La cantidad de la linea es incorrecta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_notunique', 'es', 'El numero de registro (RCS-RCM) del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_allowanceorcharge_tax_unmatched', 'es', 'La tasa de impuesto falta en la ventilacion de impuesto en pie de mensaje', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_empty', 'es', 'El tipo de una entrega o carga en encabezamiento debed de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_notunique', 'es', 'El tipo de documento debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_rate_empty', 'es', 'La tasa del IVA a la linea debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percent_illegalvalue', 'es', 'El porcentaje de una entrega o carga en encabezamiento es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_notunique', 'es', 'Un unico importe impuesto total debe de ser indicado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_notunique', 'es', 'La ciudad del representante fiscal debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_notunique', 'es', 'La dirreccion de la sede social debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_empty', 'es', 'El precio unitario bruto sin IVA debe de ser informado en cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_percent_illegalvalue', 'es', 'La tasa o el importe de la tasa parafiscal a la linea no es correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_empty', 'es', 'Un importe de impuesto debe de ser informado para cada impuesto de IVA ventilado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_empty', 'es', 'El pais del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_empty', 'es', 'La referencia gubernamental (SIREN) del representante fiscal debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_illegalvalue', 'es', 'El importe de impuesto es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_base_item_detail_empty', 'es', 'La descripcion de los bienes y de los servicios debe de ser informado en cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_empty', 'es', 'El numero del documento debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_empty', 'es', 'El pais del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_notunique', 'es', 'La sede social no puede tener más de una razon social', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_notunique', 'es', 'La dirreccion del facturado do debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_illegalvalue', 'es', 'El importe de una entrega o carga en encabezamiento es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_notunique', 'es', 'El codigo postal del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_missing', 'es', 'Las informaciones de pago no son informadas', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_illegalvalue', 'es', 'El precio unitario bruto sin IVA es correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notunique', 'es', 'La divisa de facturado cion debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percentagemonetaryvalue_illegalvalue', 'es', 'El porcentaje o el importe de una entrega o carga en encabezamiento es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_missing', 'es', 'El facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_missing', 'es', 'Los totales globales deben de ser informados', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_payable_illegalvalue', 'es', 'El importe total IVA incluido que queda para pagar es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_empty', 'es', 'Un importe imponible debe de ser informado para cada impuesto de IVA ventilado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_empty', 'es', 'La dirreccion del facturado do debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_empty', 'es', 'El importe del impuesto total debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_empty', 'es', 'El importe imponible total debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_notunique', 'es', 'Un unico importe imponible debe de ser informado para cada impuesto de IVA ventilado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_empty', 'es', 'La fecha de pago de un anticipo debe de ser iinformada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_missing', 'es', 'La ventilacion de IVA debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_registration_ident_notunique', 'es', 'El numero de registro (RCS-RCM) del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_notunique', 'es', 'La dirreccion del representante fiscal debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_percent_illegalvalue', 'es', 'La tasa o el importe de la entrega a la linea no es correcto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_empty', 'es', 'La fecha/hora de preparacion del documento debe de ser informadas', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_notunique', 'es', 'La referencia gubernamental (SIREN) del representante fiscal debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_empty', 'es', 'La tasa o el importe de una carga a la linea debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_notunique', 'es', 'El numero del documento debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'es', 'El detalle del tipo de documento debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notunique', 'es', 'La divisa de pago del IVA debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_empty', 'es', 'La ciudad de la sede social debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_reference_period_empty', 'es', 'El periodo de referencia debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_notunique', 'es', 'La dirreccion del facturado do debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_empty', 'es', 'El tipo de documento debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_empty', 'es', 'El codigo IVA intracomunitario del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_due_date_illegalvalue', 'es', 'La fecha de pago del anticipo es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_notunique', 'es', 'El pais del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_missing', 'es', 'El precio unitario debe de ser indormado en cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_empty', 'es', 'La dirreccion de la sede social debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_despatch_date_illegalvalue', 'es', 'La fecha de expedicion no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_illegalvalue', 'es', 'La fecha de vencimiento no es correcta', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_notunique', 'es', 'La referencia gubernamental (SIREN) del facturado do debe de ser unica', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_empty', 'es', 'La tasa o el importe de una entrega a la linea debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_notunique', 'es', 'El numero de registro (RCS-RCM) de la sede social debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_notunique', 'es', 'Una sola tasa del IVA de entrega o carga en encabezamiento puede ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_notunique', 'es', 'El codigo IVA intracomunitario del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_registration_ident_notunique', 'es', 'El numero de registro (RCS-RCM) del representante fiscal debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_notunique', 'es', 'El codigo postal del facturado do debe de ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_notunique', 'es', 'El pais de la sede social tiene que ser unico', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_empty', 'es', 'El precio unitario neto sin IVA debe de ser informado en cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_illegalvalue', 'es', 'El importe total IVA incluido es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_notunique', 'es', 'El precio unitario neto sin IVA debe de ser unico a cada linea', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_empty', 'es', 'El codigo postal del facturado do debe de ser informado', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_illegalvalue', 'es', 'El precio unitario neto sin IVA es incorrecto', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_empty', 'es', 'La fecha del documento debe de ser informada', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_description_empty', 'en', 'Description for a charge or an allowance in header must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_empty', 'en', 'The type of a charge or an allowance in header must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percent_illegalvalue', 'en', 'The percentage of a charge or an allowance is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_monetaryvalue_illegalvalue', 'en', 'The amount of a charge or an allowance is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_type_allowance_charge_percentagemonetaryvalue_illegalvalue', 'en', 'The amount or percentage of a charge or an allowance is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_empty', 'en', 'The VAT rate of a charge or an allowance in header must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_alloworcharge_vat_rate_notunique', 'en', 'Only one VAT rate for a charge or an allowance in header is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_missing', 'en', 'Bill to party must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_empty', 'en', 'The Bill to party country must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_country_notunique', 'en', 'Only one country is allowed for the Bill to party', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_empty', 'en', 'The Bill to party name must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_name_notunique', 'en', 'Only one name is allowed for the Bill to party', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_empty', 'en', 'Bill to party adress must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_street_notunique', 'en', 'Only one adress is allowed for the Bill to party', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_empty', 'en', 'Bill to party postal code must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_postalcode_notunique', 'en', 'Only one postal code is allowed for the Bill to party', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_empty', 'en', 'Bill to party city must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_address_city_notunique', 'en', 'Only one city is allowed for the Bill to party', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_empty', 'en', 'Bill to party administrative reference must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_gov_reference_notunique', 'en', 'Only one administrative reference of the Bill to party is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_registration_ident_notunique', 'en', 'Only one registration number of the Bill to party is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_empty', 'en', 'Bill to party VAT identification number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_billtoparty_vat_ident_notunique', 'en', 'Only one VAT identification nummber is allowed for the Bill to party', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_empty', 'en', 'The preparation date/time of the document must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_date_preparation_date_time_document_illegalvalue', 'en', 'The preparation date/time of the document is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_empty', 'en', 'The Tax representative country must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_country_notunique', 'en', 'Only one country is allowed for the Tax representative', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_empty', 'en', 'The Tax representative name must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_name_notunique', 'en', 'Only one name is allowed for the Tax representative', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_empty', 'en', 'Tax representative adress must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_street_notunique', 'en', 'Only one adress is allowed for the Tax representative', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_empty', 'en', 'Tax representative postal code must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_postalcode_notunique', 'en', 'Only one postal code is allowed for the Tax representative', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_empty', 'en', 'Tax representative city must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_address_city_notunique', 'en', 'Only one city is allowed for the Tax representative', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_empty', 'en', 'Tax representative administrative reference must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_gov_reference_notunique', 'en', 'Only one administrative reference of the Tax representative is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_registration_ident_notunique', 'en', 'Only one registration number of the Tax representative is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_empty', 'en', 'Tax representative VAT identification number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_declarantsagentorrepresentative_vat_ident_notunique', 'en', 'Only one VAT identification nummber is allowed for the Tax representative', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_empty', 'en', 'Due date must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_due_date_illegalvalue', 'en', 'Due date is not correct.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_despatch_date_illegalvalue', 'en', 'Despatch date is not correct.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_empty', 'en', 'The Seller''s Headoffice country must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_country_notunique', 'en', 'Only one country is allowed for the Seller''s Headoffice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_empty', 'en', 'The Seller''s Headoffice name must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_name_notunique', 'en', 'Only one name is allowed for the Seller''s Headoffice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_empty', 'en', 'Seller''s Headoffice adress must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_street_notunique', 'en', 'Only one adress is allowed for the Seller''s Headoffice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_empty', 'en', 'Seller''s Headoffice postal code must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_postalcode_notunique', 'en', 'Only one postal code is allowed for the Seller''s Headoffice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_empty', 'en', 'Seller''s Headoffice city must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_address_city_notunique', 'en', 'Only one city is allowed for the Seller''s Headoffice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_empty', 'en', 'Seller''s Headoffice administrative reference must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_gov_reference_notunique', 'en', 'Only one administrative reference of the Seller''s Headoffice is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_empty', 'en', 'Seller''s Headoffice registration number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_registration_ident_notunique', 'en', 'Only one registration number of the Seller''s Headoffice is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_empty', 'en', 'Seller''s Headoffice VAT identification number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_headoffice_vat_ident_notunique', 'en', 'Only one VAT identification nummber is allowed for the Seller''s Headoffice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notunique', 'en', 'Only one invoice currency is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_currency_notequal', 'en', 'Vat currency code is empty and invoice currency is not euro', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_empty', 'en', 'The issue date must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_notunique', 'en', 'Only one issue date is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_issue_date_illegalvalue', 'en', 'Issue date has an illegal value', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_empty', 'en', 'The invoice number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_number_notunique', 'en', 'Only one invoice number is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_references_empty', 'en', 'Some reference information is missing', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_reference_date_illegalvalue', 'en', 'Reference date has an illegal value', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_empty', 'en', 'The invoice type must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_notunique', 'en', 'Only one invoice type is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'en', 'The name of the document (i.e. invoice type) is empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_notunique', 'en', 'Only one invoicing period start date is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_reference_period_empty', 'en', 'One invoicing period start date must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_start_date_illegalvalue', 'en', 'Invoicing period start date has an illegal value', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicing_period_end_date_illegalvalue', 'en', 'Invoicing period end date has an illegal value', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_startDate_illegalvalue', 'en', 'Invoicing period start date has an illegal value', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoicingperiod_invoicing_period_endDate_illegalvalue', 'en', 'Invoicing period end date has an illegal value', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_missing', 'en', 'Some payment terms information is missing', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_payment_term_empty', 'en', 'Late payment term must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_penalty_term_empty', 'en', 'Penalty term must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_term_empty', 'en', 'Discount term must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_latepayment_term_empty', 'en', 'Late payment term must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_paymentterms_discount_due_date_illegalvalue', 'en', 'Discount due date is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_missing', 'en', 'Seller must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_empty', 'en', 'The Seller country must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_country_notunique', 'en', 'Only one country is allowed for the Seller', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_empty', 'en', 'The Seller name must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_name_notunique', 'en', 'Only one name is allowed for the Seller', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_empty', 'en', 'Seller adress must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_street_notunique', 'en', 'Only one adress is allowed for the Seller', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_empty', 'en', 'Seller postal code must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_postalcode_notunique', 'en', 'Only one postal code is allowed for the Seller', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_empty', 'en', 'Seller city must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_address_city_notunique', 'en', 'Only one city is allowed for the Seller', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_empty', 'en', 'Seller administrative reference must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_gov_reference_notunique', 'en', 'Only one administrative reference of the Seller is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registered_name_empty', 'en', 'The Seller registered name must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registered_name_notunique', 'en', 'Only one registered name for the Seller is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_structure_empty', 'en', 'The Seller legal structure must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_structure_notunique', 'en', 'Only one legal structure for the Seller is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_capital_empty', 'en', 'The Seller legal capital must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_legal_capital_notunique', 'en', 'Only one legal capital for the Seller is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_empty', 'en', 'The Seller registration number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_registration_ident_notunique', 'en', 'Only one registration number of the Seller is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_empty', 'en', 'Seller VAT identification number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_vat_ident_notunique', 'en', 'Only one VAT identification nummber is allowed for the Seller', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting__notunique', 'en', 'Only one tax accounting currency is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notequal', 'en', 'The tax accounting currency must indicate euro', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_accounting_currency_notunique', 'en', 'Only one tax accounting currency is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_desc_empty_or_notunique', 'en', 'Description of a parafiscal tax in a line must not be left empty and only one description is allowed for each parafiscal tax in a line', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_empty', 'en', 'A rate or percentage must be indicated for each parafiscal tax in details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_type_notunique', 'en', 'Only one rate and percentage is allowed for each parafiscal tax in details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_illegalvalue', 'en', 'The amount of parafical tax in a line is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_percent_illegalvalue', 'en', 'The percenage of parafiscal tax in a line is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_amount_percent_illegalvalue', 'en', 'The amount or percentage or parafical tax in a line is not correct.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_paratax_regulatory_vat_empty_or_notunique', 'en', 'The VAT rate of a parafiscal tax in the details of the invoice must not be left empty with VAT information and only one VAT rate is allowed for each parafiscal tax in the details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_desc_empty_or_notunique', 'en', 'Description of a charge in a line must not be left empty and only one description is allowed for each charge in a line', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_empty', 'en', 'A rate or percentage must be indicated for each charge tax in details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_type_notunique', 'en', 'Only one rate and percentage is allowed for each charge in details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_illegalvalue', 'en', 'The amount of charge in a line is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_percent_illegalvalue', 'en', 'The percentage of charge in a line is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_charge_amount_percent_illegalvalue', 'en', 'The amount or percentage or charge in a line is not correct.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_desc_empty_or_notunique', 'en', 'Description of a allowance in a line must not be left empty and only one description is allowed for each allowance in a line', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_empty', 'en', 'A rate or percentage must be indicated for each allowance tax in details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_type_notunique', 'en', 'Only one rate and percentage is allowed for each allowance in details of the invoice', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_illegalvalue', 'en', 'The amount of allowance in a line is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_percent_illegalvalue', 'en', 'The percentage of allowance in a line is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_allowance_amount_percent_illegalvalue', 'en', 'The amount or percentage or allowance in a line is not correct.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_base_item_detail_empty', 'en', 'Item details are mandatory', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_missing', 'en', 'Item unit price must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_empty', 'en', 'Item gross unit price must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_notunique', 'en', 'Only one gross unit price is allowed for each item line', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_gross_unit_price_illegalvalue', 'en', 'Item gross unit prise is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_empty', 'en', 'Item net unit price must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_notunique', 'en', 'Only one net unit price is allowed for each item line', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_net_unit_price_illegalvalue', 'en', 'Item net unit price is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_empty', 'en', 'VAT rate for an item line must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_rate_empty', 'en', 'VAT rate for an item line must not be left empty.', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_vat_notunique', 'en', 'Only one VAT rate for each item line is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_invoicepricingdetail_exempt_vat_rate_not_zero', 'en', 'VAT rate for an item line must be zero in case of exemption', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_notunique', 'en', 'Only one quantity is allowed on each line', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicedetail_quantity_value_illegalvalue', 'en', 'Quantity value is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_empty', 'en', 'Payment date of early payment must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_date_illegalvalue', 'en', 'Payment date is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_payment_number_empty', 'en', 'Early payment number must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_empty', 'en', 'Prepaid amount must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_actualpayment_prepaid_amount_illegalvalue', 'en', 'Prepaid amount is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_missing', 'en', 'Invoice totals must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_empty', 'en', 'The total tax amount must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_notunique', 'en', 'Only one total tax amount is allowed', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_monetary_amount_illegalvalue', 'en', 'Tax amount is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_empty', 'en', 'The total taxable amount must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_taxable_amount_illegalvalue', 'en', 'Taxable amount is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_empty', 'en', 'The whole total amount must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_illegalvalue', 'en', 'Total amount is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_invoicetotals_total_amount_payable_illegalvalue', 'en', 'Total amount payable is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_missing', 'en', 'The tax summary must be indicated', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_empty', 'en', 'The tax amount of each line of the tax summary must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_notunique', 'en', 'Only one tax amount is allowed for each line of the tax summary', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_empty', 'en', 'The taxable amount of each line of the tax summary must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_taxable_amount_notunique', 'en', 'Only one taxable amount is allowed for each line of the tax summary', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_tax_amount_illegalvalue', 'en', 'The tax amount is not correct', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoicesummary_taxsummary_vat_rate_empty', 'en', 'The VAT rate of each line of the tax summary must not be left empty', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_tax_unmatched', 'en', 'The VAT rate is missing in the tax breakdown in the message foot', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_global_tax_unmatched', 'en', 'The VAT rate is missing in the rest of the message', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_line_allowanceorcharge_tax_unmatched', 'en', 'The VAT rate of a special levy is missing in the tax breakdown in the message foot', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notunique', 'en', 'The VAT rate in tax summary must be unique', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_unmatched', 'en', 'The VAT rate is missing in the rest of the message', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoice_tax_taxsummary_tax_notuniquevalue', 'en', 'Each VAT rate is allowed only once', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoice_type_other_empty', 'de', 'The detail of invoice type is not filled.', 1);