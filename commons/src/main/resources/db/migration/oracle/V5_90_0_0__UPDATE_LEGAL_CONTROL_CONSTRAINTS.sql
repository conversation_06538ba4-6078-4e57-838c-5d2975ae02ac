UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoiceheader_former_invoice_type_not_in_list'
WHERE ERROR_CODE = 'invoiceheader_former_invoice_type_notequal';

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_former_invoice_former_invoice_type_empty', 'en', 'The invoice type code (EXT-FR-FE-02) of a former invoice is mandatory, rule G1.01', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_former_invoice_former_invoice_type_not_in_list', 'en', 'The invoice type code (EXT-FR-FE-02) of a former invoice shall belong to the list of authorized codes, rule G1.01', 1);

UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoiceheader_fr_requirements_business_process_type_fr_empty'
WHERE ERROR_CODE = 'invoiceheader_business_process_type_fr_empty';
UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoiceheader_fr_requirements_business_process_type_fr_notunique'
WHERE ERROR_CODE = 'invoiceheader_business_process_type_fr_notunique';
UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoiceheader_fr_requirements_business_process_type_fr_not_in_list'
WHERE ERROR_CODE = 'invoiceheader_business_process_type_fr_notequal';

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_fr_requirements_profile_type_notunique', 'en', 'The profile type (BT-24) should be unique, rule S1.06', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_buyer_order_number_b2g_fr_empty_or_notunique', 'en', 'The buyer order number (BT-13) shall be present and shall be unique, rule G1.11', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_tax_currency_fr_not_in_list', 'en', 'The invoice VAT currency code (BT-6) should belong to the ISO-4217 code list, rule G1.10', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoiceparty_sellerparty_notunique', 'en', 'Seller (BG-4) should be unique, rule G6.08', 1);
INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_invoiceparty_buyerparty_notunique', 'en', 'Buyer (BG-7) should be unique, rule G6.08', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_sellerparty_siret_qualifier_empty', 'en', 'Seller ID qualifier (BT-29b-1) can not be empty, rule G1.11', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_buyerparty_address_name_notunique', 'en', 'The Buyer name (BG-8) should be unique, rule G6.08', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_buyerparty_address_country_notunique', 'en', 'The Buyer country (BT-55) should be unique, rule G6.08', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_buyerparty_gov_reference_notunique', 'en', 'The Buyer administrative reference (BT-47) should be unique, rule G6.08', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_shiptoparty_address_country_notunique', 'en', 'The Ship to country code (BT-80) should be unique, rule G6.08', 1);

INSERT INTO XTD_DOC_ERROR_TRANSLATION (ERROR_CODE, LANGUAGE_CODE, ERROR_LABEL, STANDARD_ERROR) VALUES ('invoiceheader_party_tax_information_ident_notunique', 'en', 'The party tax identifier (EXT-FR-FE-27) should be unique, rule G6.18', 1);

DELETE FROM XTD_DOC_ERROR_TRANSLATION
WHERE ERROR_CODE='invoiceheader_alloworcharge_monetary_range_monetary_limit_value_too_large';
DELETE FROM XTD_DOC_ERROR_TRANSLATION
WHERE ERROR_CODE='invoiceheader_alloworcharge_monetary_range_monetary_limit_value_not_decimal';
DELETE FROM XTD_DOC_ERROR_TRANSLATION
WHERE ERROR_CODE='invoiceheader_alloworcharge_monetary_range_monetary_limit_value_notunique';
UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_LABEL = 'The monetary limit value of a charge or an allowance (BT-93 and BT-100) can not have more than 19 digits, including 2 decimals, rule G1.14'
WHERE ERROR_CODE = 'invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_too_large';
UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_LABEL = 'The monetary limit value of a charge or an allowance (BT-93 and BT-100) should be a number with maximum 2 decimals, rule G1.14'
WHERE ERROR_CODE = 'invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_not_decimal';
UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_LABEL = 'The monetary limit value of a charge or an allowance (BT-93 and BT-100) should be unique, rule G1.14'
WHERE ERROR_CODE = 'invoiceheader_alloworcharge_basis_monetary_range_monetary_limit_value_notunique';

UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_not_in_list'
WHERE ERROR_CODE = 'invoicedetail_list_of_related_invoice_ref_line_former_invoice_type_notequal';

UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoicedetail_invoicepricingdetail_basis_of_fees_applicable_to_the_invoice_line_not_decimal'
WHERE ERROR_CODE = 'invoicedetail_invoicepricingdetail_basis_of_fees_applicable_to_the_invoice_line_line_not_decimal';
UPDATE XTD_DOC_ERROR_TRANSLATION SET ERROR_CODE = 'invoicedetail_invoicepricingdetail_basis_of_fees_applicable_to_the_invoice_line_notunique'
WHERE ERROR_CODE = 'invoicedetail_invoicepricingdetail_basis_of_fees_applicable_to_the_invoice_line_line_notunique';
