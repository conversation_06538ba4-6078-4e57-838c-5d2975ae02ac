ALTER TABLE aap_RefSupplier
    ADD COLUMN counterpart_Account_Id integer;

ALTER TABLE aap_RefSupplier
    ADD COLUMN automatic_counterpart varchar(255);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.header', 'Données comptables de', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.header', 'Accounting Data of', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.account', 'Compte ', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.account', 'Account ', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.auxiliaryAccount', 'Compte auxiliaire ', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.auxiliaryAccount', 'Auxiliary account ', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.label', 'Libellé ', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.label', 'Label', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.counterpartAccount', 'Compte de contrepartie', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.counterpartAccount', 'Counterpart account', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.automaticCounterpart', 'Contrepartie automatique', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.automaticCounterpart', 'Automatic counterpart', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.withoutConfirmation', 'Sans confirmation', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.withoutConfirmation', 'Without confirmation', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.withConfirmation', 'Avec confirmation', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.withConfirmation', 'With confirmation', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.suspended', 'Suspendue ', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.suspended', 'Suspended', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.update.error', 'Erreur lors de la mise à jour des informations comptables du fournisseur.', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.update.error', 'Error while updating supplier accounting information.', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.update.success', 'Mise à jour des informations comptables du fournisseur réussie.', 'fr', NOW(), 11);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-ref-supplier-accounting-dialog.update.success', 'Supplier accounting information updated successfully.', 'en', NOW(), 11);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('error', 'Erreur', 'fr', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('error', 'Error', 'en', NOW(), 1);