package com.byzaneo.commons.test.service;

import com.byzaneo.commons.bean.LabelSet;
import com.byzaneo.commons.dao.LabelDAO;
import com.byzaneo.commons.service.ResourceBundleService;
import com.byzaneo.commons.service.ResourceBundleServiceImpl.LabelBundle;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;
import java.util.Locale;

import static com.byzaneo.commons.service.ResourceBundleService.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date 27 juil. 2009
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = { "classpath:/commons.beans.xml" }, loader = SystemPropertyContextLoader.class)
class ResourceBundleServiceITCase {

  private static final String FAMILY = "Quote";

  @Autowired
  private ResourceBundleService bundleService;

  @Autowired
  private LabelDAO labelao;

  @BeforeEach
  void testImports(@Autowired ResourceBundleService bundleService, @Autowired LabelDAO labelao) throws Exception {
    this.bundleService.imports("./src/test/resources/bundles", FAMILY, XMLENTITY_DECODER);
  }

  @Test
  void exports() throws Exception {
    this.bundleService.exports("./target/labels", FAMILY, null);
    this.bundleService.exports("./target/labels/unicode", FAMILY, UNICODE_ENCODER);
    this.bundleService.exports("./target/labels/htmlcode", FAMILY, XMLENTITY_ENCODER);
  }

  @Test
  void bundle() throws Exception {
    LabelBundle bundle = this.bundleService.getBundle(FAMILY, Locale.CHINA, Locale.US, UNICODE_ENCODER);
    assertTrue(bundle.getKeys()
        .hasMoreElements());
    // Enumeration<String> en = bundle.getKeys();
    // while (en.hasMoreElements()) {
    // String key = en.nextElement();
    // //System.out.println(key+" = "+bundle.getString(key));
    // }
  }

  @Test
  void label() throws Exception {
    if (this.labelao.findByFamilyAndKey("Test", "test_key") == null) {
      LabelSet ls = new LabelSet("Test", "test_key");
      ls.addLabel("[{0}] Bảng chào giá {1}", new Locale("vi"));
      ls.addLabel("[{0}] Offerta Commerciale {1}", new Locale("it"));
      ls.addLabel("[{0}] Quote {1}", new Locale("en"));
      this.bundleService.save(ls);
    }

    // Full
    String label = this.bundleService.getLabel("Test", "test_key", new Locale("zh"), new Locale("en"), "{0} = {1}", UNICODE_ENCODER, "toto",
        "short");
    assertFalse(label.isEmpty());
    // //System.out.println(label);

    // No locale
    label = this.bundleService.getLabel("Test", "test_key", new Locale("fr"), new Locale("en"), "{0} = {1}", null, "toto", "short");
    assertFalse(label.isEmpty());
    // //System.out.println(label);

    // Full locale
    label = this.bundleService.getLabel("Test", "test_key", new Locale("zh", "CN"), new Locale("en"), "{0} = {1}", null, "toto", "short");
    assertFalse(label.isEmpty());
    // //System.out.println(label);

    // Not found (with default)
    label = this.bundleService.getLabel("Test", "test_key_notfound", new Locale("zh", "CN"), new Locale("en"), "{0} = {1}", null, "toto",
        "short");
    assertFalse(label.isEmpty());
    // //System.out.println(label);

    // Not found (without default)
    label = this.bundleService.getLabel("Test", "test_key_notfound", new Locale("zh", "CN"), new Locale("en"), null, null);
    assertTrue(label.isEmpty());
    // //System.out.println(label);

    // minimal
    label = this.bundleService.getLabel("Test", "test_key", null, null, null, null);
    assertFalse(label.isEmpty());
    // //System.out.println(label);

  }

  @Test
  void labelSet() throws Exception {
    LabelSet ls = this.labelao.findByFamilyAndKey("Test", "test2");
    if (ls == null) {
      ls = new LabelSet("Test", "test2");
      ls.addLabel("[{0}] Bảng chào giá {1}", new Locale("vi"));
      ls.addLabel("[{0}] Offerta Commerciale {1}", new Locale("it"));
      ls.addLabel("[{0}] Quote {1}", new Locale("en"));
      ls = this.bundleService.save(ls);
    }
    Long id = ls.getId();

    // By id
    LabelSet resultat = this.bundleService.getLabelSet(id);
    assertNotNull(resultat);
    // //System.out.println(resultat);

    // By family & key
    resultat = this.bundleService.getLabelSet("Test", "test2");
    assertNotNull(resultat);
    // //System.out.println(resultat);

    // By string family.key
    resultat = this.bundleService.getLabelSet("Test.test2");
    assertNotNull(resultat);
    // //System.out.println(resultat);

    // incorrect family.key
    try {
      resultat = this.bundleService.getLabelSet("Test");
    }
    catch (ServiceException e) {
      return;
    }
    fail();
  }

  @Test
  void search() throws Exception {
    List<LabelSet> result = this.labelao
        .search("family IN (AAA,Test) AND key = 111 AND labels.locale = fr ORDER BY key DESC", (Pageable) null)
        .getContent();
    assertEquals(1, result.size());
  }

}
