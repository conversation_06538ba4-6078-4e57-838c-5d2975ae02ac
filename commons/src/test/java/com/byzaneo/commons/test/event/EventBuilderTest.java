package com.byzaneo.commons.test.event;

import com.byzaneo.commons.api.EventAction;
import com.byzaneo.commons.bean.Job;
import com.byzaneo.commons.event.Event;
import com.byzaneo.commons.event.TopicFactory;

import org.junit.jupiter.api.Test;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.userdetails.User;

import java.security.Principal;

import static com.byzaneo.commons.api.EventAction.create;
import static com.byzaneo.commons.event.EventBuilder.fromIdentifier;
import static com.byzaneo.commons.event.EventBuilder.fromSource;
import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.core.context.SecurityContextHolder.getContext;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jul 28, 2015
 * @since 5.0
 */
class EventBuilderTest {

  @Test
  void nullEventSource() throws Exception {
    try {
      fromSource(null);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void eventNoTopic() throws Exception {
    try {
      fromSource(new Object()).build(true);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void event() throws Exception {
    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source).topic(topic)
        .build(true);
    assertEquals("test/event/builder", event.getTopic());
    assertEquals(source, event.getSource());
    assertEquals(source.getClass(), event.getSourceType());
  }

  // - PERSISTENT -

  @Test
  void persistentNoAction() throws Exception {
    try {
      fromSource(new Job()).build(true);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void persistentNoId() throws Exception {
    Job persistent = new Job();
    Event event = fromSource(persistent)
        .action(create)
        .build(true);
    assertEquals("persistent/Job/create", event.getTopic());
    assertEquals(persistent, event.getSource());
  }

  @Test
  void persistentWithId() throws Exception {
    Job persistent = new Job();
    persistent.setId("1234");
    Event event = fromSource(persistent)
        .action(create)
        .build(true);
    assertEquals("persistent/Job/create", event.getTopic());
    assertEquals(persistent, event.getSource());
    assertEquals(persistent.getClass(), event.getSourceType());
    assertEquals(persistent.getId(), event.getSourceId());
  }

  // - IDENTIFIER -

  @Test
  void identifierNull() throws Exception {
    try {
      fromIdentifier(null);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void identifierNoActionAndNoType() throws Exception {
    try {
      fromIdentifier("1234").build(true);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void identifierNoType() throws Exception {
    try {
      fromIdentifier("1234").action(create)
          .build(true);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void identifierNoAction() throws Exception {
    try {
      fromIdentifier("1234").type("test")
          .build(true);
      fail();
    }
    catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  void identifier() throws Exception {
    String id = "1234";
    Event event = fromIdentifier(id)
        .action(create)
        .type("test")
        .build(true);
    assertEquals("test/1234/create", event.getTopic());
    assertEquals(id, event.getSource());
    assertEquals(id, event.getSourceId());
  }

  // - AUTHENTICATED -

  @Test
  void noAuthentication() throws Exception {
    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source)
        .topic(topic)
        .authenticate()
        .build(true);

    assertNull(event.getOwner());
  }

  @Test
  void authenticated() throws Exception {
    String username = "<EMAIL>";
    getContext().setAuthentication(new TestingAuthenticationToken(new User(username, "pwd", emptyList()), username));

    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source)
        .topic(topic)
        .authenticate()
        .build(true);

    getContext().setAuthentication(null);

    assertEquals(username, event.getOwner()
        .getName());
  }

  @Test
  void ownerPrincipal() throws Exception {
    Principal principal = new Principal() {
      @Override
      public String getName() {
        return "<EMAIL>";
      }
    };

    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source)
        .topic(topic)
        .owner(principal)
        .build(true);

    assertEquals(principal.getName(), event.getOwner()
        .getName());
  }

  @Test
  void ownerIdentifier() throws Exception {
    String uid = "uid";

    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source)
        .topic(topic)
        .owner(uid, null)
        .build(true);

    assertEquals(uid, event.getOwner()
        .getIdentifier());
  }

  @Test
  void ownerName() throws Exception {
    String uname = "uname";

    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source)
        .topic(topic)
        .owner(null, uname)
        .build(true);

    assertEquals(uname, event.getOwner()
        .getName());
  }

  @Test
  void ownerIdAndName() throws Exception {
    String uname = "uname";
    String uid = "uid";

    Object source = new Object();
    String topic = "test/event/builder";
    Event event = fromSource(source)
        .topic(topic)
        .owner(uid, uname)
        .build(true);

    assertEquals(uid, event.getOwner()
        .getIdentifier());
    assertEquals(uname, event.getOwner()
        .getName());
  }

  // - TOPIC FACTORY -

  @Test
  void topicFactory() {
    TopicFactory factory = event -> event.getAction()
        .toString()
        .concat("/")
        .concat(event.getSourceType()
            .getSimpleName());

    Object source = "";
    Event event = fromSource(source)
        .action(EventAction.create)
        .topicFactory(factory)
        .build(true);

    assertEquals("create/String", event.getTopic());
  }
}