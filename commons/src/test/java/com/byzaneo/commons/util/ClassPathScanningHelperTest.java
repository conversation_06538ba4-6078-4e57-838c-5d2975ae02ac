package com.byzaneo.commons.util;

import static com.byzaneo.commons.util.ClassPathScanningHelper.findAnnotatedClasses;
import static com.byzaneo.commons.util.ClassPathScanningHelper.findClassesByType;
import static com.byzaneo.commons.util.ClassPathScanningHelper.findResources;
import static com.byzaneo.commons.util.ClassPathScanningHelper.getMethodsAnnotatedWith;
import static org.junit.jupiter.api.Assertions.*;

import java.lang.annotation.*;
import java.lang.reflect.Method;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import com.byzaneo.commons.job.*;

class ClassPathScanningHelperTest {

  @Test
  void getAnnotatedValues() {
    assertTrue(ClassPathScanningHelper.getAnnotatedValues(null, null)
        .isEmpty());
    assertTrue(ClassPathScanningHelper.getAnnotatedValues(null, VoidAnnotation.class)
        .isEmpty());
    assertTrue(ClassPathScanningHelper.getAnnotatedValues(EnumHelperTestEnum.class, null)
        .isEmpty());
  }

  public static enum VoidEnum {

  }

  @Retention(RetentionPolicy.RUNTIME)
  @Target(ElementType.FIELD)
  public @interface VoidAnnotation {
  }

  @Test
  void obtainEnumVal() {
    List<EnumHelperTestEnum> annotatedValues = ClassPathScanningHelper.getAnnotatedValues(EnumHelperTestEnum.class,
        EnumHelperTestAnnotation.class);
    assertTrue(annotatedValues.contains(EnumHelperTestEnum.TITI));
    assertTrue(annotatedValues.contains(EnumHelperTestEnum.TATA));

    annotatedValues = ClassPathScanningHelper.getAnnotatedValues(EnumHelperTestEnum.class, VoidAnnotation.class);
    assertTrue(annotatedValues.isEmpty());
  }

  @Test
  void voidEnum() {
    List<VoidEnum> annotatedValues = ClassPathScanningHelper.getAnnotatedValues(VoidEnum.class, EnumHelperTestAnnotation.class);
    assertTrue(annotatedValues.isEmpty());
  }

  @Test
  void getAnnotatedValuesFromClass() {
    // null param cases
    assertTrue(ClassPathScanningHelper.getAnnotatedValuesFromClass(null, null)
        .isEmpty());
    assertTrue(ClassPathScanningHelper.getAnnotatedValuesFromClass(null, VoidAnnotation.class)
        .isEmpty());
    assertTrue(ClassPathScanningHelper.getAnnotatedValuesFromClass(EnumHelperTestEnum.class, null)
        .isEmpty());

    // regular case
    Class<?> unEnumTypedClass = EnumHelperTestEnum.class;// lose the info that unEnumTypedClass is the class of an enum
    List<?> annotatedValues = ClassPathScanningHelper.getAnnotatedValuesFromClass(unEnumTypedClass, EnumHelperTestAnnotation.class);
    assertEquals(2, annotatedValues.size());
    assertTrue(annotatedValues.contains(EnumHelperTestEnum.TITI));
    assertTrue(annotatedValues.contains(EnumHelperTestEnum.TATA));

    // test non enum type
    unEnumTypedClass = String.class;
    try {
      annotatedValues = ClassPathScanningHelper.getAnnotatedValuesFromClass(unEnumTypedClass, EnumHelperTestAnnotation.class);
      fail("exception expected");
    }
    catch (IllegalArgumentException e) {
      assertEquals("the type " + unEnumTypedClass.getCanonicalName() + " is not an Enum", e.getMessage());
    }
  }

  @Test
  void classesByTypeInterface1() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class);
    // types.forEach(System.out::println);
    assertEquals(5, types.size());
  }

  @Test
  void classesByTypeInterface2() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, "com.byzaneo.commons");
    // types.forEach(System.out::println);
    assertEquals(5, types.size());
  }

  @Test
  void classesByTypeIncludeInterfacesAndAbstract() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, false, false);
    // types.forEach(System.out::println);
    assertEquals(7, types.size());
  }

  @Test
  void classesByTypeExcludeInterfaces() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, true, false);
    assertEquals(5, types.size());
  }

  @Test
  void classesByTypeExcludeAbstractClasses() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, false, true);
    assertEquals(4, types.size());
  }

  @Test
  void classesByTypeExcludeInterfacesAndAbstractClasses() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, true, true);
    assertEquals(4, types.size());
    types = findClassesByType(null, true, true);
    assertEquals(0, types.size());
  }

  @Test
  void classesByTypeNonConcreteType() throws Exception {
    List<Class<? extends AbstractTrigger>> types = findClassesByType(AbstractTrigger.class, "com.byzaneo.commons");
    // types.forEach(System.out::println);
    assertEquals(5, types.size());
  }

  @Test
  void classesByTypeConcreteType() throws Exception {
    List<Class<? extends EventTrigger>> types = findClassesByType(EventTrigger.class, "com.byzaneo.commons");
    // types.forEach(System.out::println);
    assertEquals(1, types.size());
  }

  @Test
  void classesByTypeConcrete() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, null, true, true, true);
    // types.forEach(System.out::println);
    assertEquals(4, types.size());
  }

  @Test
  void classesByTypeInterfaceAbstract() throws Exception {
    List<Class<? extends Trigger>> types = findClassesByType(Trigger.class, null, false, false, true);
    // types.forEach(System.out::println);
    assertEquals(7, types.size());
  }

  public interface ITrigger extends Trigger {
  }

  @Test
  void resourcesProperties() throws Exception {
    Resource[] resources = findResources(null);
    assertEquals(0, resources.length);
    resources = findResources(".*\\.properties");
    // java.util.Arrays.stream(resources).forEach(System.out::println);
    assertTrue(resources.length > 1);
  }

  @Test
  void resourcesLabels() throws Exception {
    Resource[] resources = findResources("labels", null);
    assertEquals(0, resources.length);
    resources = findResources("labels", ".*\\.properties");
    // java.util.Arrays.stream(resources).forEach(System.out::println);
    assertTrue(resources.length > 4);
  }

  @Test
  void resourcesFolders() throws Exception {
    Resource[] resources = findResources("/META-INF/spring/", ".*");
    // java.util.Arrays.stream(resources).forEach(System.out::println);
    assertTrue(resources.length > 4);
  }

  @Test
  void resourcesSubfolders() throws Exception {
    Resource[] resources = findResources("/META-INF/maven", ".*\\.xml");
    // java.util.Arrays.stream(resources).forEach(System.out::println);
    assertTrue(resources.length > 10);
  }

  @Test
  void testFindAnnotatedClasses() {
    List<Class<?>> annotatedClasses = findAnnotatedClasses(Service.class, "com.byzaneo.commons");
    assertEquals(12, annotatedClasses.size());
  }

  @Test
  void testGetMethodsAnnotatedWith() {
    List<Method> methodsAnnotatedWith = getMethodsAnnotatedWith(null, SafeVarargs.class);
    assertEquals(0, methodsAnnotatedWith.size());
    methodsAnnotatedWith = getMethodsAnnotatedWith(ClassPathScanningHelper.class, SafeVarargs.class);
    assertEquals(4, methodsAnnotatedWith.size());
  }
}
