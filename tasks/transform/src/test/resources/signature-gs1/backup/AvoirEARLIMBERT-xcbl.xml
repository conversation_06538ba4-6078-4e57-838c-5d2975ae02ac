<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <InvoiceHeader>
        <InvoiceNumber>170353</InvoiceNumber>
        <InvoiceIssueDate>2017-10-30T00:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>785367</core:BuyerOrderNumber>
                <core:PurchaseOrderDate>2017-10-19T00:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <ListOfRelatedInvoiceRef>
                <RelatedInvoiceRef>
                    <RelatedInvoiceType>
                        <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
                    </RelatedInvoiceType>
                    <InvoiceNumber>
                        <core:RefNum>170348</core:RefNum>
                    </InvoiceNumber>
                </RelatedInvoiceRef>
            </ListOfRelatedInvoiceRef>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CreditNoteGoodsAndServices</InvoiceTypeCoded>
            <InvoiceTypeCodedOther>GoodsInvoice</InvoiceTypeCodedOther>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>Other</core:CurrencyCoded>
            <core:CurrencyCodedOther>EUR</core:CurrencyCodedOther>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <InvoiceDates>
            <InvoiceDueDate>2017-11-30T00:00:00</InvoiceDueDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2018-11-08T11:37:44</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>DocumentReceivedDateTime</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
                <core:DateCoded>
                    <core:Date>2018-11-08T11:37:44</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                    </core:Agency>
                    <core:Ident/>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>CFS FRANCE SAS SALON DE PROVENCE</core:Name1>
                    <core:Street>ENTREPOT 492</core:Street>
                    <core:PostalCode>14095</core:PostalCode>
                    <core:City>CAEN CEDEX</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR37440283752</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                    </core:Agency>
                    <core:Ident>NOGLNNOGLN</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:AgencyCodedOther>SIREN</core:AgencyCodedOther>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>5010179410001</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>LegalCapital</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>11400 euros</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>EARL</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>EARL IMBERT</core:Name1>
                    <core:Street>1032 Chemin de Richelme</core:Street>
                    <core:PostalCode>13130</core:PostalCode>
                    <core:City>Berre l'etang</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR35501017941</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </SellerParty>
            <BillToParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                    </core:Agency>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>CFS FRANCE SAS SALON DE PROVENCE</core:Name1>
                    <core:Street>ENTREPOT 492</core:Street>
                    <core:PostalCode>14095</core:PostalCode>
                    <core:City>CAEN CEDEX</core:City>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR37440283752</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Pour tout paiement apres la date d'echeance une indemnite forfaitaire de 40 euros sera appliquee.</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Aucun escompte ne sera accorde</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>un interet de retard calcule au taux de 3 fois le taux d'interet legal sur le montant TTC.</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Other</core:PaymentTermCoded>
                </core:PaymentTerm>
            </core:PaymentTerms>
            <core:PaymentMethod>
                <core:PaymentMeanCoded>Other</core:PaymentMeanCoded>
            </core:PaymentMethod>
        </InvoicePaymentInstructions>
        <InvoiceHeaderNote>-</InvoiceHeaderNote>
        <ListOfNameValueSet>
            <core:NameValueSet>
                <core:SetName>FreeText</core:SetName>
                <core:ListOfNameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText02</core:Name>
                        <core:Value>492</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText05</core:Name>
                        <core:Value>00000008</core:Value>
                    </core:NameValuePair>
                </core:ListOfNameValuePair>
            </core:NameValueSet>
        </ListOfNameValueSet>
    </InvoiceHeader>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>-162.47</core:MonetaryAmount>
            </InvoiceTotal>
            <TaxableValue>
                <core:MonetaryAmount>-154.00</core:MonetaryAmount>
            </TaxableValue>
            <TotalAmountPayable>
                <core:MonetaryAmount>-162.47</core:MonetaryAmount>
            </TotalAmountPayable>
            <TotalTaxAmount>
                <core:MonetaryAmount>-8.47</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>
        <ListOfTaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>5.50</core:TaxCategoryCodedOther>
                <core:TaxableAmount>-154.00</core:TaxableAmount>
                <core:TaxAmount>-8.47</core:TaxAmount>
            </core:TaxSummary>
        </ListOfTaxSummary>
    </InvoiceSummary>
</Invoice>
