/**
 * 
 */
package com.byzaneo.generix.transform.task.business;

import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.StringHelper.toObjectMap;
import static com.byzaneo.generix.api.Variable.instance_code;
import static com.byzaneo.generix.transform.rte.RteEngine.Config.ThrowOnError;
import static com.byzaneo.generix.transform.rte.RteEngine.Config.Timeout;
import static com.byzaneo.transform.engine.TransformEngineBuilder.engine;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OUTPUT_DIR;
import static com.byzaneo.xtrade.process.VariableHelper.getLong;
import static com.byzaneo.xtrade.process.VariableHelper.getMap;
import static com.byzaneo.xtrade.process.VariableHelper.getString;
import static com.byzaneo.xtrade.process.VariableHelper.getVariable;
import static com.byzaneo.xtrade.process.VariableHelper.isDead;
import static com.byzaneo.xtrade.util.DocumentHelper.getType;
import static com.byzaneo.xtrade.util.DocumentHelper.searchFiles;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.util.Assert.notNull;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import org.activiti.engine.delegate.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.edocument.exception.TemplateMissingException;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.TransformService.Transform;
import com.byzaneo.generix.transform.task.util.*;
import com.byzaneo.generix.transform.task.util.DocumentFileGenerationStrategy.GenerationStrategy;
import com.byzaneo.security.bean.*;
import com.byzaneo.task.annotation.*;
import com.byzaneo.transform.birt.engine.*;
import com.byzaneo.transform.birt.engine.BIRTEngine.InvocationPlace;
import com.byzaneo.transform.engine.*;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.process.VariableHelper;
import com.byzaneo.xtrade.process.el.FixedValue;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix Group
 * @date 6 nov. 2017
 */
@Task(name = "Birt", library = "process/business/transform", version = "1.0.0")
@TaskViewModel(labelFamily = RteSourceTask.LABEL_FAMILY, groups = { "engine", "input", "output" }, excludes = { "engineParams",
    "outputDirectory", "documentStatus" }, overrides = {
    @TaskPropertyOverride(name = "source", property = @TaskProperty(group = "engine", required = true, options = "gnxTransformAppHandler.getTemplates(gnxInstanceService.getInstanceByCode(cc.attrs.owner.project.workspace.name), 'BIRT', null, true)"))
})
public class BirtTask extends AbstractTransformTask {

  private static final long serialVersionUID = 1L;

  private static final Logger log = getLogger(BirtTask.class);

  public enum DocumentParameters {
    FROM,
    TO;
  }

  protected transient EDocumentService eDocumentService;
  protected transient SecurityService securityService;
  protected transient InstanceService instanceService;

  private transient int count = 0;

  private transient boolean getTemplateFromPartner;

  /** {@link GenerationStrategy} (default: {@link GenerationStrategy#systematic} */
  @TaskProperty(group = "output", required = true, type = GenerationStrategy.class, labelPrefixed = false)
  protected Expression generationStrategy = new FixedValue(DocumentFileGenerationStrategy.GenerationStrategy.SYSTEMATIC.toString());

  @TaskProperty(group = "engine", type = Boolean.class, labelPrefixed = false)
  protected Expression selectTemplateAutomatically = new FixedValue(FALSE);

  @TaskProperty(group = "engine", type = DocumentParameters.class, labelPrefixed = false, required = true)
  protected Expression partnerField;

  public BirtTask() {
    super();
    eDocumentService = getBean(EDocumentService.class, EDocumentService.SERVICE_NAME);
    securityService = getBean(SecurityService.class, SecurityService.SERVICE_NAME);
    instanceService = getBean(InstanceService.class, InstanceService.SERVICE_NAME);
  }

  @Override
  public void executeTask(final DelegateExecution execution) throws Exception {
    // source
    notNull(source, "Source is required");
    String templateUri = getString(execution, this.source);
    DocumentParameters docParameter = VariableHelper.getEnum(execution, partnerField, DocumentParameters.class, DocumentParameters.FROM);
    final Template sourceTemplate = this.transformService.getTemplate(templateUri);
    notNull(sourceTemplate, "Template not found for: " + templateUri);
    getTemplateFromPartner = VariableHelper.getBoolean(execution, selectTemplateAutomatically, FALSE);
    // output directory
    final File outdir = resolveOutputDir(execution);
    if (outdir == null) {
      log.warn("Process output directory variable not set ({})", PROCESS_OUTPUT_DIR);
      return;
    }

    final DocumentFileOutput stdout = new DocumentFileOutput(execution, outdir);

    // engine
    if (!getTemplateFromPartner)
      log.info("Transform template: {}", sourceTemplate.getName());
    TransformEngineBuilder builder = createEngineBuilder(execution, sourceTemplate);

    // contextual document files processing...
    this.processDocument(builder, execution, sourceTemplate, stdout, docParameter);
    this.count = 0;
  }

  @Override
  protected TransformEngineBuilder createEngineBuilder(DelegateExecution execution, Template sourceTemplate) {
    return engine(Transform.BIRT.getEngine())
        .withStylesheet(sourceTemplate.getFile(), FileType.RPTDESIGN)
        .config(execution.getVariables())
        .config(BIRTEngine.CALLED_FROM, InvocationPlace.PROCESS)
        .config(ThrowOnError.toString(), FALSE)
        .config(Timeout.toString(), getLong(execution, this.timeout, DEFAULT_TIMEOUT) * 1000L);
  }

  protected void processDocument(TransformEngineBuilder builder, final DelegateExecution execution, final Template sourceTemplate,
      final DocumentFileOutput stdout, DocumentParameters docParam) {
    // document's files to process
    final List<Document> documents = VariableHelper.getDocuments(execution, contextualDocumentsQuery);
    final Instance instance = instanceService.getInstanceByCode(getVariable(execution, instance_code.toString(), null));
    if (log.isInfoEnabled()) {
      log.info("Contextual documents ({}): {}", getString(execution, contextualDocumentsQuery, ""),
          documents.size());
    }
    if (CollectionUtils.isEmpty(documents)) {
      return;
    }

    List<DocumentFile> dofs = searchFiles(documents, getString(execution, query));
    if (log.isInfoEnabled()) {
      log.info("Document files ({}): {}", getString(execution, query, ""), dofs.size());
    }
    if (CollectionUtils.isEmpty(dofs)) {
      return;
    }
    GenerationStrategy strategy = GenerationStrategy.valueOf(this.generationStrategy.getExpressionText());
    dofs.stream()
        .filter(dof -> isProcessable(execution, dof))
        .forEach(dof -> process(builder, execution, sourceTemplate, strategy, docParam, instance, dof, stdout));
    log.info("{}/{} Document(s) file(s) processed successfully", count, dofs.size());
  }

  protected static boolean isProcessable(DelegateExecution execution, DocumentFile dof) {
    boolean processable = dof != null && dof.getFile() != null && dof.getFile()
        .isFile() && dof.getDocument() != null && !isDead(execution, dof.getDocument());
    if (processable) {
      log.info("Processing {}...", dof.getFile()
          .getName());
    }
    else {
      log.warn("Ignored document file: {}", dof);
    }
    return processable;
  }

  @SuppressWarnings("squid:S2259")
  protected void process(TransformEngineBuilder builder, final DelegateExecution execution, Template sourceTemplate,
      GenerationStrategy strategy, DocumentParameters docParam, Instance instance,
      final DocumentFile dof, final DocumentFileOutput stdout) {
    try (final InputSource inputSource = new InputSource(dof.getFile(), dof.getType())) {
      // prepares result
      stdout.setOutFile(dof);
      final OutputResult result = new OutputResult(stdout.outFile, stdout.type);

      if (getTemplateFromPartner) {
        Partner partner = null;
        if (DocumentParameters.FROM.equals(docParam))
          partner = securityService.getPartner(dof.getDocument()
              .getFrom(), (Company) instance.getGroup());
        else
          partner = securityService.getPartner(dof.getDocument()
              .getTo(), (Company) instance.getGroup());

        Optional<Template> template = eDocumentService.getTemplateFromPartnerOrCompany(dof.getDocument(), null, instance, partner,
            getType(dof.getDocument()), stdout.type);
        if (!template.isPresent())
          throw new TemplateMissingException();
        sourceTemplate = template.get();
        builder.withStylesheet(sourceTemplate.getFile(), FileType.RPTDESIGN);
        log.info("Transform template: {}", sourceTemplate.getName());
      }

      // transforms...
      Locale language = null;
      Map<String, Object> config = toObjectMap(getMap(execution, engineConfig));
      if (config == null || !config.containsKey(BIRTRuntimeEngine.BIRT_LOCALE)) {
        language = VariableHelper.getVariable(execution, "instance_language", Locale.ENGLISH);
      }

      builder.addParameters(config)
          .addParameters(TransformHelper.getGlobalParameters(dof))
          .addParameters(this.transformService.buildBirtParams(sourceTemplate, language))
          .addParameter("uri", dof == null ? "" : dof.getUri())
          .transform(inputSource, result, true);

      // handles result
      final DocumentFile dfile = new DocumentFile(stdout.outFile, stdout.type,
          BirtTask.class.getSimpleName(), execution.getId(), null, stdout.getDfComment(), stdout.getDfInArchive());
      strategy.process(dof.getDocument(), dfile);
      count++;
    }
    catch (TemplateMissingException e) {
      log.error("No template found for document " + dof.getDocument()
          .getNumber());
      VariableHelper.addDeads(execution, dof.getDocument());
    }
    catch (Exception e) {
      final String message = String.format("Error while transforming: template=%s (%s) : %s",
          sourceTemplate.getUri(),
          dof != null ? ", " + dof.getUri() : "",
          getRootCauseMessage(e));
      log.error(message);
      if (dof != null)
        VariableHelper.addDeads(execution, dof.getDocument());
    }
  }

  @Override
  public boolean render(boolean error, String property) {

    switch (property) {
    case "selectTemplateAutomatically":
      return this.selectTemplateAutomatically != null && TRUE.toString()
          .equals(this.selectTemplateAutomatically.getExpressionText());
    default:
      return super.render(error, property);
    }
  }

  public Map<String, String> getGenerationStrategies() {
    List<GenerationStrategy> strategies = Arrays.asList(GenerationStrategy.values());
    final Locale locale = getLocale();
    return strategies.stream()
        .collect(
            Collectors.toMap(stgy -> MessageHelper.getMessage("labels." + stgy.name(), stgy.name(), locale), GenerationStrategy::name));
  }

  public Expression getGenerationStrategy() {
    return generationStrategy;
  }

  public void setGenerationStrategy(Expression generationStrategy) {
    this.generationStrategy = generationStrategy;
  }

  public Expression getSelectTemplateAutomatically() {
    return selectTemplateAutomatically;
  }

  public void setSelectTemplateAutomatically(Expression selectTemplateAutomatically) {
    this.selectTemplateAutomatically = selectTemplateAutomatically;
  }

  public Expression getPartnerField() {
    return partnerField;
  }

  public void setPartnerField(Expression partnerField) {
    this.partnerField = partnerField;
  }
}
