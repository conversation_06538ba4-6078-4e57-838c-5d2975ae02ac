<?xml version="1.0" encoding="UTF-8"?>
<descriptor
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="https://commons.chassagne-repo.generixgroup.com/schema/commons/bean-descriptor.xsd"
	name="gnxAddress"
	type="com.byzaneo.generix.service.repository.bean.address.Address">
	<label>Address</label>
	<link name="gnxPartner">
		<property>parent.code</property>
	</link>
	<properties>
		<property name="typeAddress"
			type="com.byzaneo.generix.service.repository.bean.address.TypeAddressEnum"
			required="true">
			<label>typeAddress</label>
		</property>
		<property name="subTypeAddress"
			type="com.byzaneo.generix.service.repository.bean.address.InvoiceNatureEnum"
			required="true">
			<label>subTypeAddress</label>
		</property>
		<property name="identification" type="java.lang.String"
			regex="^[\w\-]{0,255}+$" required="true">
			<label>identification</label>
		</property>
		<property name="vatCode" type="java.lang.String"
			required="true"
			regex="^[a-zA-Z 0-9]*$">
			<label>vatCode</label>
		</property>
		<property name="siret" type="java.lang.String" regex="^[a-zA-Z 0-9]{14,14}">
			<label>siret</label>
		</property>
		<property name="rcs" type="java.lang.String"
			regex="^[a-zA-Z 0-9]*$">
			<label>rcs</label>
		</property>
		<property name="name" type="java.lang.String" required="true">
			<label>name</label>
		</property>
		<property name="street" type="java.lang.String"	required="true">
			<label>street</label>
		</property>
		<property name="postalCode" type="java.lang.String" required="true">
			<label>postalCode</label>
		</property>
		<property name="city" type="java.lang.String" required="true">
			<label>city</label>
		</property>
		<property name="country.countryCoded" type="java.lang.String"
			regex="\b(?!\b(zz|ZZ)\b)\w+\b" required="true">
			<label>countryCoded</label>
		</property>
		<property name="codeAl" type="java.lang.String"
			regex="^[\w\-]{0,255}+$">
			<label>codeAl</label> <!-- now its id2 instead of codeAl -->
		</property>
		<property name="link.identification" type="java.lang.String"
			regex="^[a-zA-Z0-9]*$">
			<label>link</label>
		</property>
		<property name="streetSupplement1" type="java.lang.String">
			<label>streetSupplement1</label>
		</property>
		<property name="partnerCode" type="java.lang.String">
			<label>partnerCode</label>
		</property>
		<property name="minAmount" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>minAmount</label>
		</property>
		<property name="maxAmount" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>maxAmount</label>
		</property>
		<property name="minWeight" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>minWeight</label>
		</property>
		<property name="maxWeight" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>maxWeight</label>
		</property>
		<property name="minVolume" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>minVolume</label>
		</property>
		<property name="maxVolume" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>maxVolume</label>
		</property>
		<property name="minUnits" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>minUnits</label>
		</property>
		<property name="maxUnits" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>maxUnits</label>
		</property>
		<property name="minParcel" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>minParcel</label>
		</property>
		<property name="maxParcel" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>maxParcel</label>
		</property>
		<property name="minPallets" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>minPallets</label>
		</property>
		<property name="maxPallets" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>maxPallets</label>
		</property>
		<property name="franco" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>franco</label>
		</property>
		<property name="carriageCost" type="java.math.BigDecimal"
			regex="(?:\d+(?:\.|\,\d+)?|\.|\,\d+)">
			<label>carriageCost</label>
		</property>
		<property name="deliveryTime" type="java.lang.Integer"
			regex="^[0-9]*$">
			<label>deliveryTime</label>
		</property>
		<property name="minimumMorningMonday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningMonday</label>
		</property>
		<property name="maximumMorningMonday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningMonday</label>
		</property>
		<property name="minimumAfternoonMonday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonMonday</label>
		</property>
		<property name="maximumAfternoonMonday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonMonday</label>
		</property>
		<property name="minimumMorningTuesday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningTuesday</label>
		</property>
		<property name="maximumMorningTuesday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningTuesday</label>
		</property>
		<property name="minimumAfternoonTuesday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonTuesday</label>
		</property>
		<property name="maximumAfternoonTuesday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonTuesday</label>
		</property>
		<property name="minimumMorningWednesday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningWednesday</label>
		</property>
		<property name="maximumMorningWednesday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningWednesday</label>
		</property>
		<property name="minimumAfternoonWednesday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonWednesday</label>
		</property>
		<property name="maximumAfternoonWednesday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonWednesday</label>
		</property>
		<property name="minimumMorningThursday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningThursday</label>
		</property>
		<property name="maximumMorningThursday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningThursday</label>
		</property>
		<property name="minimumAfternoonThursday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonThursday</label>
		</property>
		<property name="maximumAfternoonThursday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonThursday</label>
		</property>
		<property name="minimumMorningFriday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningFriday</label>
		</property>
		<property name="maximumMorningFriday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningFriday</label>
		</property>
		<property name="minimumAfternoonFriday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonFriday</label>
		</property>
		<property name="maximumAfternoonFriday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonFriday</label>
		</property>
		<property name="minimumMorningSaturday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningSaturday</label>
		</property>
		<property name="maximumMorningSaturday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningSaturday</label>
		</property>
		<property name="minimumAfternoonSaturday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonSaturday</label>
		</property>
		<property name="maximumAfternoonSaturday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonSaturday</label>
		</property>
		<property name="minimumMorningSunday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>minimumMorningSunday</label>
		</property>
		<property name="maximumMorningSunday" type="java.lang.String"
			regex="\b(1[0-2]|0?[0-9]):([0-5][0-9]|[0])">
			<label>maximumMorningSunday</label>
		</property>
		<property name="minimumAfternoonSunday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>minimumAfternoonSunday</label>
		</property>
		<property name="maximumAfternoonSunday" type="java.lang.String"
			regex="\b(1[2-9]|2[0-3]):([0-5][0-9]|[0])">
			<label>maximumAfternoonSunday</label>
		</property>
		<property name="iban" type="java.lang.String">
			<label>iban</label>
		</property>
		<property name="bic" type="java.lang.String">
			<label>bic</label>
		</property>
		<property name="currency" type="java.lang.String">
			<label>currency</label>
		</property>
	</properties>
</descriptor>