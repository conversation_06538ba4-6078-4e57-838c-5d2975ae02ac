/**
 *
 */
package com.byzaneo.generix.xcbl.portal.task.util;

import static com.byzaneo.generix.edocument.util.AsnXcblHelper.getBuyerOrderNumber;
import static com.byzaneo.generix.xcbl.portal.task.util.AdvanceShipmentNoticeTaskHelper.getBuyerLineItemNum;
import static java.time.temporal.ChronoUnit.DAYS;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Instant;
import java.util.*;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.byzaneo.commons.util.JAXBHelper;
import com.byzaneo.generix.edocument.service.OrderService;
import com.byzaneo.generix.edocument.util.AsnXcblHelper;
import com.byzaneo.generix.xcbl.portal.task.AdvanceShipmentNoticeTask;
import com.byzaneo.generix.xcbl.ui.SessionCacheHandler.LineItem;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.OrderHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix group
 * @date 3 nov. 2015
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AdvanceShipmentNoticeHelperTest {

  @InjectMocks
  private AdvanceShipmentNoticeTask task;

  @Mock
  private DocumentService documentService;

  @Mock
  private OrderService orderService;

  @BeforeEach
  public void before() {
    Mockito.reset(documentService);
  }

  @Test
  public void testContainsPullOrder() {

    Document lastAsn = createChildASNDocument();

    OrderIndex orderFalse = createOrder("BuyerOrderNumber", "City", "street", "75001", null);
    OrderIndex orderTrue = createOrder("BuyerOrderNumber", "City", "street", "75001", "BlanketOrder");

    ASNItemDetailType detail1 = createASNItemDetailType(new BigDecimal("0.0"), new BigDecimal("8"), new BigDecimal("8"), "XXX", 1);
    ASNItemDetailType detail2 = createASNItemDetailType(new BigDecimal("2"), new BigDecimal("2"), new BigDecimal("4"), "XXX", 2);

    Map<LineItem, OrderIndex> orderLines = new HashMap<>();
    LineItem key1 = new LineItem(getBuyerOrderNumber(detail1), AdvanceShipmentNoticeTaskHelper.getBuyerLineItemNum(detail1));
    orderLines.put(key1, orderFalse);

    LineItem key2 = new LineItem(getBuyerOrderNumber(detail2), AdvanceShipmentNoticeTaskHelper.getBuyerLineItemNum(detail2));
    orderLines.put(key2, orderTrue);

    Document orderRespDoc = createDocument(lastAsn, orderFalse, DocumentType.ORDRSP);

    assertEquals(false, AdvanceShipmentNoticeTaskHelper.containsPullOrders(createDocument(lastAsn, orderFalse, DocumentType.ORDERS), null));

    assertEquals(false, AdvanceShipmentNoticeTaskHelper.containsPullOrders(orderRespDoc, Collections.singletonList(orderFalse)));

    OrderIndex order = orderLines.get(new LineItem(getBuyerOrderNumber(detail1), getBuyerLineItemNum(detail1)));
    assertEquals(false, AdvanceShipmentNoticeTaskHelper.containsPullOrders(orderRespDoc, Collections.singletonList(order)));

    OrderIndex order2 = orderLines.get(new LineItem(getBuyerOrderNumber(detail2), getBuyerLineItemNum(detail2)));
    assertEquals(true, AdvanceShipmentNoticeTaskHelper.containsPullOrders(orderRespDoc, Collections.singletonList(order2)));

    assertEquals(true, AdvanceShipmentNoticeTaskHelper.containsPullOrders(orderRespDoc, orderLines.values()));
  }

  private Document createDocument(Document childDoc, OrderIndex orderIndex, DocumentType type) {
    Document document = new Document();
    document.setId(0L);
    document.setType(type);
    document.setCreationDate(Date.from(Instant.now()
        .minus(1, DAYS)));
    document.setStatusWithEnumValue(DocumentStatus.SENT_PARTIALLY);
    document.addChild(childDoc);
    document.setIndexValue(orderIndex);
    return document;
  }

  private OrderIndex createOrder(String buyerOrderNUmber, String city, String street, String postalCode, String orderType) {
    OrderIndex orderIndex = new OrderIndex();
    orderIndex.setBuyerOrderNumber(buyerOrderNUmber);
    orderIndex.setOrderType(orderType);

    NameAddressType nameAdresseType = new NameAddressType();
    nameAdresseType.setCity(city);
    nameAdresseType.setStreet(street);
    nameAdresseType.setPostalCode(postalCode);

    return orderIndex;
  }

  private Document createChildASNDocument() {
    Document lastAsn = new Document();
    lastAsn.setType(DocumentType.DESADV);
    lastAsn.setCreationDate(Date.from(Instant.now()));
    return lastAsn;
  }

  @Test
  public void testCreateASNSummaryEmptyOrders() {
    assertNotNull(AsnXcblHelper.createASNSummary(null)
        .getTransportPackagingTotals());
  }

  @Test
  public void testCreateASNSummary() {
    final ASNSummaryType summary = AsnXcblHelper.createASNSummary(Arrays.asList(Mockito.mock(Order.class)));
    assertTrue(summary.getNumberOfLines() == 1);
  }

  @Test
  public void testInitASNWithOrderResponse() {
    LanguageType language = new LanguageType();
    language.setLanguageCoded(LanguageCodeType.FR);
    CurrencyType currency = new CurrencyType();
    currency.setCurrencyCoded(CurrencyCodeType.EUR);
    PartyType party = new PartyType();
    party.setPartyID(new IdentifierType());
    party.getPartyID()
        .setIdent("seller party");

    OrderResponse response = mock(OrderResponse.class, RETURNS_DEEP_STUBS);
    when(response.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getBuyerOrderResponseNumber())
            .thenReturn("RESPONSE-101");
    when(response.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getSellerOrderResponseNumber())
            .thenReturn("SELLER-001");
    when(response.getOrderResponseHeader()
        .getOrderReference()
        .getRefNum()).thenReturn("ORDER-101");
    when(response.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderLanguage())
            .thenReturn(language);
    when(response.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderCurrency())
            .thenReturn(currency);

    when(response.getOrderResponseHeader()
        .getSellerParty()).thenReturn(party);
    when(response.getOrderResponseHeader()
        .getOrderResponseHeaderNote()).thenReturn("Notes pour la commande");

    AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrderResponse(response, new Date());

    assertEquals("ASN-RESPONSE-101", asn.getASNHeader()
        .getASNNumber());
    assertEquals("ORDER-101", asn.getASNHeader()
        .getASNOrderNumber()
        .get(0)
        .getBuyerOrderNumber());
    assertEquals(ASNPurposeCodeType.ORIGINAL, asn.getASNHeader()
        .getASNPurpose()
        .getASNPurposeCoded());
    assertEquals(ASNTypeCodeType.OTHER, asn.getASNHeader()
        .getASNType()
        .getASNTypeCoded());
    assertEquals("DespatchAdvice", asn.getASNHeader()
        .getASNType()
        .getASNTypeCodedOther());
    assertEquals(LanguageCodeType.FR, asn.getASNHeader()
        .getASNLanguage()
        .getLanguageCoded());
    assertEquals(CurrencyCodeType.EUR, asn.getASNHeader()
        .getASNCurrency()
        .getCurrencyCoded());
    assertEquals("seller party", asn.getASNHeader()
        .getASNParty()
        .getSellerParty()
        .getPartyID()
        .getIdent());
    assertEquals("EAN",
        asn.getASNHeader()
            .getASNParty()
            .getSellerParty()
            .getPartyID()
            .getAgency()
            .getAgencyCoded());
    assertEquals("LocationCode", asn.getASNHeader()
        .getASNParty()
        .getSellerParty()
        .getPartyID()
        .getAgency()
        .getCodeListIdentifierCoded());
    assertEquals("Notes pour la commande", asn.getASNHeader()
        .getASNHeaderNote());
  }

  @Test
  public void testInitASNWithOrderResponse_DetailBuyerNum() {
    LanguageType language = new LanguageType();
    language.setLanguageCoded(LanguageCodeType.FR);
    CurrencyType currency = new CurrencyType();
    currency.setCurrencyCoded(CurrencyCodeType.EUR);
    PartyType party = new PartyType();
    party.setPartyID(new IdentifierType());
    party.getPartyID()
        .setIdent("seller party");

    OrderResponse response = mock(OrderResponse.class, RETURNS_DEEP_STUBS);
    when(response.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getBuyerOrderResponseNumber())
            .thenReturn("RESPONSE-101");
    when(response.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getSellerOrderResponseNumber())
            .thenReturn("SELLER-001");
    when(response.getOrderResponseHeader()
        .getOrderReference()
        .getRefNum()).thenReturn("ORDER-101");
    when(response.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderLanguage())
            .thenReturn(language);
    when(response.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderCurrency())
            .thenReturn(currency);

    when(response.getOrderResponseHeader()
        .getSellerParty()).thenReturn(party);

    List<OrderResponseItemDetailType> listDetail = new ArrayList<>();
    OrderResponseItemDetailType ordrspDetail = new OrderResponseItemDetailType();
    ordrspDetail.setOriginalItemDetailWithChanges(new OriginalItemDetailWithChangesType());
    ordrspDetail.getOriginalItemDetailWithChanges()
        .setBaseItemDetail(new ResponseBaseItemDetailType());
    LineItemNumType lineItem = new LineItemNumType();
    lineItem.setBuyerLineItemNum(123);
    lineItem.setSellerLineItemNum(432);
    ordrspDetail.getOriginalItemDetailWithChanges()
        .getBaseItemDetail()
        .setLineItemNum(lineItem);
    listDetail.add(ordrspDetail);
    when(response.getOrderResponseDetail()
        .getListOfOrderResponseItemDetail()
        .getOrderResponseItemDetail()).thenReturn(listDetail);

    AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrderResponse(response, new Date());

    assertEquals(123, asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getLineItemNum()
        .getBuyerLineItemNum());
    assertEquals(new Integer(432), asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getLineItemNum()
        .getSellerLineItemNum());
  }

  @Test
  public void testInitASNWithOrderResponse_DetailBuyerNumNull() {
    LanguageType language = new LanguageType();
    language.setLanguageCoded(LanguageCodeType.FR);
    CurrencyType currency = new CurrencyType();
    currency.setCurrencyCoded(CurrencyCodeType.EUR);
    PartyType party = new PartyType();
    party.setPartyID(new IdentifierType());
    party.getPartyID()
        .setIdent("seller party");

    OrderResponse response = mock(OrderResponse.class, RETURNS_DEEP_STUBS);
    when(response.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getBuyerOrderResponseNumber())
            .thenReturn("RESPONSE-101");
    when(response.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getSellerOrderResponseNumber())
            .thenReturn("SELLER-001");
    when(response.getOrderResponseHeader()
        .getOrderReference()
        .getRefNum()).thenReturn("ORDER-101");
    when(response.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderLanguage())
            .thenReturn(language);
    when(response.getOrderResponseHeader()
        .getOriginalOrderHeaderWithChanges()
        .getOrderCurrency())
            .thenReturn(currency);
    when(response.getOrderResponseHeader()
        .getSellerParty()).thenReturn(party);

    List<OrderResponseItemDetailType> listDetail = new ArrayList<>();
    OrderResponseItemDetailType ordrspDetail = new OrderResponseItemDetailType();
    ordrspDetail.setOriginalItemDetailWithChanges(new OriginalItemDetailWithChangesType());
    ordrspDetail.getOriginalItemDetailWithChanges()
        .setBaseItemDetail(new ResponseBaseItemDetailType());
    LineItemNumType lineItem = new LineItemNumType();
    ordrspDetail.getOriginalItemDetailWithChanges()
        .getBaseItemDetail()
        .setLineItemNum(lineItem);
    listDetail.add(ordrspDetail);
    when(response.getOrderResponseDetail()
        .getListOfOrderResponseItemDetail()
        .getOrderResponseItemDetail()).thenReturn(listDetail);

    AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrderResponse(response, new Date());

    assertEquals(1, asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getLineItemNum()
        .getBuyerLineItemNum());
    assertNull(asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getLineItemNum()
        .getSellerLineItemNum());
  }

  @Test
  public void testInitASNWithOrdRspNull() {
    AdvanceShipmentNotice desadv = AsnXcblHelper.initASNWithOrderResponse(null, null);
    assertNotNull(desadv);
  }

  @Test
  public void testInitASNWithOrdRsp() throws Exception {
    File file = new File("./src/test/resources/samples/ABC/OrderResponseSample-ABC_xCBL40.xml");
    OrderResponse orderResponse = JAXBHelper.unmarshal(OrderResponse.class, file);
    Date orderIssueDate = DateUtils.parseDate("25/02/2017 16:32:33", "dd/MM/yyyy HH:mm:ss");
    AdvanceShipmentNotice desadv = AsnXcblHelper.initASNWithOrderResponse(orderResponse, orderIssueDate);
    assertEquals(orderResponse.getOrderResponseHeader()
        .getOrderReference()
        .getRefNum(),
        desadv.getASNHeader()
            .getASNOrderNumber()
            .get(0)
            .getBuyerOrderNumber());
    assertEquals(orderIssueDate, desadv.getASNHeader()
        .getASNOrderNumber()
        .get(0)
        .getPurchaseOrderDate());
  }

  @Test
  public void testCreateNextPartialASN() {
    AdvanceShipmentNotice oldAsn = new AdvanceShipmentNotice();
    oldAsn.setOwners("OWN_TST");
    oldAsn.setASNHeader(new ASNHeaderType());
    oldAsn.setASNDetail(new ASNDetailType());
    oldAsn.setASNSummary(new ASNSummaryType());
    oldAsn.getASNSummary()
        .setTransportPackagingTotals(new TransportPackagingTotalsType());

    oldAsn.getASNHeader()
        .setASNNumber("ASN_TST_NUMBER");
    Date creationDate = null;
    try {
      creationDate = DateUtils.parseDate("23/01/2016", "dd/MM/yyyy");
    }
    catch (ParseException e) {
      e.printStackTrace();
      fail();
    }

    oldAsn.getASNHeader()
        .setASNIssueDate(creationDate);
    oldAsn.getASNHeader()
        .setASNDates(new ASNDatesType());

    ASNItemDetailType detail1 = createASNItemDetailType(new BigDecimal("0.0"), new BigDecimal("8"), new BigDecimal("8"), null, 0);
    ASNItemDetailType detail2 = createASNItemDetailType(new BigDecimal("2"), new BigDecimal("2"), new BigDecimal("4"), null, 0);

    oldAsn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());
    oldAsn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(detail1);
    oldAsn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(detail2);

    OrderIndex order = new OrderIndex();
    order.setOrderType("BlanketOrder");

    AdvanceShipmentNotice newAsn = AsnXcblHelper.createNextPartialASN(oldAsn, "NEW_NUMBER", OrderHelper.isPullOrder(order));

    // Tests
    assertEquals("OWN_TST", newAsn.getOwners());
    assertEquals("NEW_NUMBER", newAsn.getASNHeader()
        .getASNNumber());
    assertEquals(1, newAsn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .size());

  }

  private ASNItemDetailType createASNItemDetailType(BigDecimal bakOrderedQty, BigDecimal shippedQty, BigDecimal orderedQty,
      String buyerOrderNUmber, int buyerLineItemNum) {
    ASNItemDetailType detail1 = new ASNItemDetailType();
    detail1.setASNBaseItemDetail(new ASNBaseItemDetailType());
    detail1.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());

    QuantityType backOrderedQty1 = new QuantityType();
    backOrderedQty1.setQuantityValue(new QuantityValueType());
    backOrderedQty1.getQuantityValue()
        .setValue(bakOrderedQty);
    detail1.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(backOrderedQty1);

    QuantityType shippedQty1 = new QuantityType();
    shippedQty1.setQuantityValue(new QuantityValueType());
    shippedQty1.getQuantityValue()
        .setValue(shippedQty);
    detail1.getASNBaseItemDetail()
        .getASNQuantities()
        .setShippedQuantity(shippedQty1);

    QuantityType orderedQty1 = new QuantityType();
    orderedQty1.setQuantityValue(new QuantityValueType());
    orderedQty1.getQuantityValue()
        .setValue(orderedQty);
    detail1.getASNBaseItemDetail()
        .getASNQuantities()
        .setOrderedQuantity(orderedQty1);

    ASNLineItemReferencesType lineItemRef = new ASNLineItemReferencesType();
    PurchaseOrderReferenceType purchaseOrderReferenceType = new PurchaseOrderReferenceType();
    purchaseOrderReferenceType.setBuyerOrderNumber(buyerOrderNUmber);
    lineItemRef.setPurchaseOrderReference(purchaseOrderReferenceType);
    detail1.getASNBaseItemDetail()
        .setASNLineItemReferences(lineItemRef);

    LineItemNumType lineItemNumType = new LineItemNumType();
    lineItemNumType.setBuyerLineItemNum(buyerLineItemNum);
    detail1.getASNBaseItemDetail()
        .setLineItemNum(lineItemNumType);
    return detail1;
  }

  @Test
  public void testSellerAdressNotPresentInOrder_null_address() {

    AdvanceShipmentNotice asn = initAsn(null);

    assertTrue(AsnXcblHelper.isSellerAddressNotPresent(asn));
  }

  @Test
  public void testSellerAdressNotPresentInOrder_empty_address() {

    AdvanceShipmentNotice asn = initAsn(new NameAddressType());

    assertTrue(AsnXcblHelper.isSellerAddressNotPresent(asn));
  }

  @Test
  public void testSellerAdressNotPresentInOrder_incomplet_address_no_postal_code() {

    NameAddressType address = new NameAddressType();
    address.setStreet("rue charolais");
    AdvanceShipmentNotice asn = initAsn(address);

    assertTrue(AsnXcblHelper.isSellerAddressNotPresent(asn));

  }

  @Test
  public void testSellerAdressNotPresentInOrder_incomplet_address_no_street() {

    NameAddressType address = new NameAddressType();
    address.setStreet("rue charolais");
    address.setCity("paris");
    AdvanceShipmentNotice asn = initAsn(address);

    assertTrue(AsnXcblHelper.isSellerAddressNotPresent(asn));

  }

  @Test
  public void testSellerAdressNotPresentInOrder_correct_address() {
    NameAddressType address = new NameAddressType();
    address.setStreet("rue charolais");
    address.setCity("paris");
    address.setPostalCode("75012");
    AdvanceShipmentNotice asn = initAsn(address);

    assertFalse(AsnXcblHelper.isSellerAddressNotPresent(asn));
  }

  private AdvanceShipmentNotice initAsn(NameAddressType address) {
    PartyType sellerParty = new PartyType();
    sellerParty.setNameAddress(address);
    ASNPartyType asnParty = new ASNPartyType();
    asnParty.setSellerParty(sellerParty);
    ASNHeaderType asnHeader = new ASNHeaderType();
    asnHeader.setASNParty(asnParty);
    AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(asnHeader);

    return asn;
  }
}
