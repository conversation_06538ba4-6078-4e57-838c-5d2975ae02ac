type = Tipo
reference = Referencia
from = Emisor
to = Destinatario
status = Estado
invoiceDetail_listOfInvoiceItemDetail_invoiceItemDetail[0]_deliveryDetail_shipToLocation_locationIdentifier_locationDescription = Lugar de llegada
invoiceHeader_invoiceParty_sellerParty_partyID_ident = Identificador
invoiceHeader_invoiceNumber = N\u00FAmero cronol\u00F3gico
invoiceHeader_invoiceParty_sellerParty_nameAddress_name1 = Nombre del transportista
owners = Entidad jur\u00EDdica
invoiceHeader_invoiceDates_invoiceDueDate = Fecha de emisi\u00F3n
commercial_invoice = PREFAC
invoiceHeader_invoiceIssueDate = Fecha de creaci\u00F3n
invoiceHeader_invoiceReferences[0]_otherInvoiceReferences_referenceCoded[0]_primaryReference_refNum = N.\u00BA prefactura
invoiceHeader_invoiceType_invoiceTypeCoded = Tipo
error_in_invoice_download = La descarga del pdf ha fallado.
invoiceDetail_listOfInvoiceItemDetail_invoiceItemDetail[0]_invoiceBaseItemDetail_itemIdentifiers_partNumbers_buyerPartNumber_partID = N.\u00BA OT orden de transporte
invoiceDetail_listOfInvoiceItemDetail_invoiceItemDetail[0]_deliveryDetail_listOfScheduleLine_scheduleLine[0]_transportRouting_custShippingContractNum = DPO
invoiceDetail_listOfInvoiceItemDetail_invoiceItemDetail[0]_deliveryDetail_shipFromLocation_locationIdentifier_locationDescription = Lugar de salida
self_billed_invoice = FDIRECT
invoiceDetail_listOfInvoiceItemDetail_invoiceItemDetail[0]_lineItemDates_actualShipDate = Fecha de carga
invoiceHeader_invoiceDates_listOfOtherInvoiceDates_dateCoded[0]_date = D\u00E9cada