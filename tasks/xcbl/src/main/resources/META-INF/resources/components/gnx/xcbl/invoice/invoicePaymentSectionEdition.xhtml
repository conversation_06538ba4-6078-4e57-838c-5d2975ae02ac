<p:outputPanel xmlns="http://www.w3.org/1999/xhtml"
               xmlns:h="http://xmlns.jcp.org/jsf/html"
               xmlns:f="http://xmlns.jcp.org/jsf/core"
               xmlns:pe="http://primefaces.org/ui/extensions"
               xmlns:p="http://primefaces.org/ui"
               xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
               xmlns:b="http://byzaneo.com/ui"
               xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
               rendered="#{cc.attrs.value.wizard.currentSection.name == 'InvoicePaymentSectionEdition'}">
    
    <ui:param name="resourceBundle" value="#{cc.attrs.value.getBundle('InvoicePaymentSectionEdition')}"/>
    
    <p:panelGrid>
        <!-- * PAYMENT ZONE * -->
        <p:row>
            <!-- * SECTION TITLE * -->
            <p:column style="width:30%;">
                <h:outputText value="#{resourceBundle.payment}"
                              styleClass="wizard-header-title text-muted"/>
            </p:column>
            <!-- * SECTION CONTENT * -->
            <p:column>
                <!-- CURRENCY -->
                <p:outputPanel styleClass="form-group col-sm-6 text-left">
                    <p:outputLabel for="invoiceCurrency" value="#{gnxxcblinvlbls.invoice_currency}"/>
                    <p:selectOneMenu id="invoiceCurrency"
                                     value="#{cc.attrs.value.wizard.currentSection.document.invoiceHeader.invoiceCurrency.currencyCoded}"
                                      required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('invoiceCurrency')}"
                                     requiredMessage="#{gnxxcblcomlbls.value_required}"
                                     disabled="#{cc.attrs.value.wizard.currentSection.isDeviseDisabled()}"
                                     styleClass="selectonemenu-resize">
                        <f:selectItems value="#{cc.attrs.value.wizard.currentSection.currencyItems}"/>
                        <p:ajax listener="#{cc.attrs.value.wizard.currentSection.onChangeCurrency()}"
                                process="@this" update="psIban psBic psCos" global="false"/>
                    </p:selectOneMenu>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="invoiceCurrency"/>
                    </p:outputPanel>
                </p:outputPanel>
                <!-- PAYMENT CHOICE -->
                <p:outputPanel styleClass="form-group col-sm-6 text-left">
                    <p:outputLabel for="paymentChoice" value="#{resourceBundle.payment_choice}"/>
                    <p:selectOneMenu id="paymentChoice"
                                     value="#{cc.attrs.value.wizard.currentSection.document.invoiceHeader.invoicePaymentInstructions.paymentMethod[0].paymentMeanCoded}"
                                     required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('paymentChoice')}"
                                     requiredMessage="#{gnxxcblcomlbls.value_required}"
                                     disabled="#{cc.attrs.value.wizard.currentSection.isPaymentChoiceDisabled()}"
                                     styleClass="selectonemenu-resize">
                        <f:selectItems value="#{cc.attrs.value.wizard.currentSection.paymentItems}"
                                       var="item" itemValue="#{item}" itemLabel="#{item.label}"/>
                    </p:selectOneMenu>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="paymentChoice"/>
                    </p:outputPanel>
                </p:outputPanel>
                <!-- IBAN -->
                <p:outputPanel id="psIban" rendered="#{cc.attrs.value.wizard.currentSection.showIban()}" styleClass="form-group col-sm-6 text-left">
                    <p:outputLabel for="iban" value="#{resourceBundle.iban}"/>
                    <p:inputText id="iban"
                                 value="#{cc.attrs.value.wizard.currentSection.iban}"
                                 required="#{!cc.attrs.value.wizard.currentSection.isFactor}"
                                 requiredMessage="#{gnxxcblcomlbls.value_required}"
                                 validatorMessage="#{labels.bank_account_iban_error_notValid}"
                                 disabled="#{cc.attrs.value.wizard.currentSection.isFactor}"
                                 maxlength="34"
                                 styleClass="form-control psIbanInput input-disabled">
                        <f:validator validatorId="custom.ibanValidator"/>
                    </p:inputText>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="iban"/>
                    </p:outputPanel>
                </p:outputPanel>
                <!-- BIC -->
                <p:outputPanel id="psBic"  rendered="#{cc.attrs.value.wizard.currentSection.showIban()}" styleClass="form-group col-sm-6 text-left">
                    <p:outputLabel for="bic" value="#{resourceBundle.bic}"/>
                    <p:inputText id="bic"
                                 value="#{cc.attrs.value.wizard.currentSection.bic}"
                                 required="#{!cc.attrs.value.wizard.currentSection.isFactor}"
                                 requiredMessage="#{gnxxcblcomlbls.value_required}"
                                 validatorMessage="#{labels.bank_account_bic_error_notValid}"
                                 disabled="#{cc.attrs.value.wizard.currentSection.isFactor}"
                                 maxlength="11"
                                 styleClass="form-control input-disabled"
                                 converter="trimConverter">
                        <f:validateLength minimum="8" />
                        <f:validator validatorId="xssValidator" />
                    </p:inputText>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="bic"/>
                    </p:outputPanel>
                </p:outputPanel>
                <!-- FACTOR -->
                <p:outputPanel rendered="#{cc.attrs.value.wizard.currentSection.showIban()}"
                               styleClass="form-group col-sm-6 text-left bold">
                    <p:selectBooleanCheckbox id="cbxFactor"
                                             value="#{cc.attrs.value.wizard.currentSection.isFactor}"
                                             style="font-size: medium !important;"
                                             styleClass="form-control"
                                             itemLabel="#{resourceBundle.factor}">
                        <p:ajax event="change"
                                process="@this" update="iban bic" global="false"/>
                    </p:selectBooleanCheckbox>
                </p:outputPanel>

                <!-- PENALTY CONDITIONS -->
                <p:outputPanel styleClass="form-group col-sm-12 text-left">
                    <p:outputLabel for="penaltyConditions"
                                   value="#{resourceBundle.penalty_condition}"/>
                    <p:inputText id="penaltyConditions"
                                 value="#{cc.attrs.value.wizard.currentSection.getPenalytyOrEscompteConditions('LatePayment').paymentTermDescription.value}"
                                 validator="xssValidator"
                                 required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('penaltyConditions')}"
                                 requiredMessage="#{gnxxcblcomlbls.value_required}"
                                 placeholder="#{resourceBundle.penalty_condition}"
                                 styleClass="form-control"/>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="penaltyConditions"/>
                    </p:outputPanel>
                </p:outputPanel>

                <!-- DISCOUNT CONDITIONS -->
                <p:outputPanel styleClass="form-group col-sm-12 text-left">
                    <p:outputLabel for="escompteConditions"
                                   value="#{resourceBundle.escompte_condition}"/>
                    <p:inputText id="escompteConditions"
                                 value="#{cc.attrs.value.wizard.currentSection.getPenalytyOrEscompteConditions('Discount').paymentTermDescription.value}"
                                 validator="xssValidator"
                                 required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('escompteConditions')}"
                                 requiredMessage="#{gnxxcblcomlbls.value_required}"
                                 placeholder="#{resourceBundle.escompte_condition}"
                                 styleClass="form-control"/>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="escompteConditions"/>
                    </p:outputPanel>
                </p:outputPanel>

                <!-- PENALTY TERMS -->
                <p:outputPanel styleClass="form-group col-sm-12 text-left">
                    <p:outputLabel for="penaltyTerms" value="#{resourceBundle.penalty_terms}"/>
                    <p:inputText id="penaltyTerms"
                                 value="#{cc.attrs.value.wizard.currentSection.getPenalytyOrEscompteConditions('PenaltyTerms').paymentTermDescription.value}"
                                 validator="xssValidator"
                                 required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('penaltyTerms')}"
                                 requiredMessage="#{gnxxcblcomlbls.value_required}"
                                 placeholder="#{resourceBundle.penalty_terms}"
                                 styleClass="form-control"/>
                    <p:outputPanel styleClass="custom-error">
                        <p:message for="penaltyTerms"/>
                    </p:outputPanel>
                </p:outputPanel>

                <!-- VAT -->
	            <!-- SIMPLE -->
	            <p:outputPanel rendered="#{not cc.attrs.value.wizard.currentSection.isAdvancedVatSystem()}" styleClass="form-group" style="height: 60px;">
                        <!-- EXEMPT TVA -->
                       <p:outputPanel styleClass="col-sm-12 text-left">
		                     <p:outputLabel value="#{resourceBundle.invoice_exo_tva}"/>
		                </p:outputPanel>
                        <p:outputPanel styleClass="col-sm-1">
                            <p:selectBooleanCheckbox id="TVA_"
                            						 value="#{cc.attrs.value.wizard.currentSection.taxFree}"
                                                     required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('tvaExemptCheckbox')}"
                                                     styleClass="form-control">
                                <p:ajax update="@(.tvaExempt)" listener="#{cc.attrs.value.wizard.currentSection.onChangeTaxTvaExempt()}"/>
                            </p:selectBooleanCheckbox>
                        </p:outputPanel>
                        <p:outputPanel styleClass="col-sm-4 tvaExempt">
                            <p:selectOneMenu id="taxFree"
                                             style="float:left;width:100%"
                                             rendered="#{cc.attrs.value.wizard.currentSection.taxFree}"
                                             placeholder="#{resourceBundle.invoice_exo_tva_placeholder}"
                                             required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('taxFree') and cc.attrs.value.wizard.currentSection.taxFree}"
                                             requiredMessage="#{resourceBundle.invoice_exo_tva_placeholder} : #{gnxxcblcomlbls.value_required}"
                                             value="#{cc.attrs.value.wizard.currentSection.document.invoiceHeader.taxReference[0].reasonTaxExemptCodedOther}">
                                <f:selectItems value="#{cc.attrs.value.wizard.currentSection.exemptTVAItems}"/>
                            </p:selectOneMenu>
                        </p:outputPanel>

               </p:outputPanel>
	            
	                <!-- ADVANCED -->
                <p:outputPanel rendered="#{cc.attrs.value.wizard.currentSection.isAdvancedVatSystem()}" styleClass="form-group text-left psVatZone" style="padding: 1% 0 0 0">
                    <p:outputPanel styleClass="col-sm-2">
                        <p:outputLabel for="vatSystem"
                                       value="#{resourceBundle.vat_system}"/>
                        <p:outputPanel styleClass="custom-error">
                            <p:message for="vatSystem"/>
                        </p:outputPanel>
                    </p:outputPanel>
                    <p:selectOneRadio id="vatSystem"
                                      value="#{cc.attrs.value.wizard.currentSection.vatIdent}"
                                      required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('vatSystem')}"
                                      requiredMessage="#{gnxxcblcomlbls.value_required}"
                                      layout="custom">
                        <!-- VAT ON AMOUNTS RECEIVED-->
                        <f:selectItem itemLabel="#{resourceBundle.vat_on_amounts_received}"
                                      itemValue="ENC" />
                        <!-- VAT PAID ON CHARGES -->
                        <f:selectItem itemLabel="#{resourceBundle.vat_paid_on_charges}"
                                      itemValue="DEB" />
                        <!-- EXEMPT TVA -->
                        <f:selectItem itemLabel="#{resourceBundle.exo_tva}"
                                      itemValue="EXO" />
                        <p:ajax process="@this" update="@(.psVatZone) tvaExempt" global="false"/>
                    </p:selectOneRadio>

                    <h:panelGrid columns="2"
                                 styleClass="col-sm-4">
                        <!-- VAT ON AMOUNTS RECEIVED-->
                        <p:radioButton id="opt1" for="vatSystem" itemIndex="0" />
                        <h:outputLabel for="opt1"
                                       value="#{resourceBundle.vat_on_amounts_received}" />

                        <!-- VAT PAID ON CHARGES -->
                        <p:radioButton id="opt2" for="vatSystem" itemIndex="1" />
                        <h:outputLabel for="opt2"
                                       value="#{resourceBundle.vat_paid_on_charges}" />

                        <!-- EXEMPT TVA -->
                        <p:radioButton id="opt3" for="vatSystem" itemIndex="2" />
                        <h:outputLabel for="opt3"
                                       value="#{resourceBundle.exo_tva}" />
                    </h:panelGrid>
                    <p:outputPanel id="tvaExempt" styleClass="col-sm-5" style="padding: 4% 0 0 0">
                        <p:selectOneMenu id="exoTva"
                                         rendered="#{cc.attrs.value.wizard.currentSection.vatIdent=='EXO'}"
                                         value="#{cc.attrs.value.wizard.currentSection.document.invoiceHeader.taxReference[0].reasonTaxExemptCodedOther}"
                                         required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('exoTva')}"
                                         requiredMessage="#{gnxxcblcomlbls.value_required}"
                                         styleClass="selectonemenu-resize">
                            <f:selectItems value="#{cc.attrs.value.wizard.currentSection.exemptTVAItems}"/>
                        </p:selectOneMenu>
                        <p:outputPanel styleClass="custom-error">
                            <p:message for="exoTva"/>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:separator styleClass="wizard-zone-separator"/>
    <!-- * FEE ZONE * -->
    <p:panelGrid>
        <p:row>
            <!-- * SECTION TITLE * -->
            <p:column style="width:30%;">
                <h:outputText value="#{resourceBundle.fee}"
                              styleClass="wizard-header-title text-muted"/>
            </p:column>
            <!-- * SECTION CONTENT * -->
            <p:column>
                <!-- COSTS -->
                <p:outputPanel id="psCos" styleClass="form-group col-sm-6 text-left">
                    <p:outputLabel for="vendorFreight" value="#{resourceBundle.shipping_fee}"/>
                    <pe:inputNumber id="vendorFreight"
                                    value="#{cc.attrs.value.wizard.currentSection.vendorFreight}"
                                    required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('vendorFreight')}"
                                    placeholder="0,00"
                                    styleClass="input-number-resize"
                                    decimalSeparator=","
                                    symbol=" #{cc.attrs.value.wizard.currentSection.getCurrencySign(cc.attrs.value.wizard.currentSection.document.invoiceHeader.invoiceCurrency.currencyCoded)}"
                                    symbolPosition="s"
                                    thousandSeparator="&#160;"/>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
    <p:separator styleClass="wizard-zone-separator"/>
    <!-- * COMMENT ZONE * -->
    <p:panelGrid>
        <p:row>
            <!-- * SECTION TITLE * -->
            <p:column style="width:30%;">
                <h:outputText
                        value="#{resourceBundle.comment} "
                        styleClass="wizard-header-title text-muted">
                </h:outputText>
                <g:rgpdTooltip pfTooltip="true" />
            </p:column>
            <!-- * SECTION CONTENT * -->
            <p:column>
                <!-- COMMENT -->
                <p:outputPanel styleClass="form-group col-sm-12">
                    <p:inputTextarea id="comment"
                                     value="#{cc.attrs.value.wizard.currentSection.comment}"
                                     validator="xssValidator"
                                     required="#{cc.attrs.value.wizard.currentSection.isFieldRequired('comment')}"
                                     styleClass="input-textarea-resize"
                                     rows="2"
                                     autoResize="false"/>
                	<p:outputPanel styleClass="custom-error">
                        <p:message for="comment"/>
                    </p:outputPanel>
                </p:outputPanel>
            </p:column>
        </p:row>
    </p:panelGrid>
</p:outputPanel>