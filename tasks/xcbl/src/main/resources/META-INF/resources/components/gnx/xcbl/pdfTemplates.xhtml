<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
             
>
    <!-- INTERFACE -->
    <cc:interface name="pdfTemplates">
        <cc:attribute name="value" required="true" type="com.byzaneo.generix.xcbl.ui.edition.DocumentSectionEdition" />
        <cc:attribute name="isEditable" default="true" />
        <cc:attribute name="allowQuickStep" default="false" />
        <cc:attribute name="maxDocumentSizeInByte" default="17000000" />
        <cc:attribute name="isRendered" default="true" />
    </cc:interface>
    <!-- IMPLEMENTATION -->
    <cc:implementation>
    
      <script type="text/javascript">
      
      	<!-- if allowQuickStep attribute is true if only one tempalte is found for a partner the next step  button is automatically pressed-->
		function goToNextStep (){
			
			if( '#{cc.attrs.allowQuickStep}' &amp;&amp;  $("[id='" + PF('wTempateList').id + "_list']").children().length ==1)
				{
	          		jQuery('.psNextStepButton').click();
				}
	    }		

     </script>
     <p:outputPanel rendered="#{cc.attrs.isRendered}">
        <p:outputPanel styleClass="pdf-template-panel">
            <p:outputPanel styleClass="autocomplete-panel">
                <p:autoComplete id="selectedPartnerId"
                                value="#{cc.attrs.value.selectedPartner}"
                                completeMethod="#{gnxSecurityAppHandler.onCompletePartner}"
                                converter="partnerCodeConverter"
                                var="partner" maxResults="10" itemValue="#{partner}"
                                itemLabel="#{partner.code.concat(' - ').concat(partner.fullname)}"
                                styleClass="autocomplete-center w-100"
                                placeholder="#{cc.attrs.value.partnerTemplateLabel}"
                                inputStyleClass="w-100">
                                
                    <p:ajax event="itemSelect" listener="#{cc.attrs.value.onSelectPartner()}" process="@this"  update="@(.uploadFileBtn) @(.dtlTemplatesList) @(.add-template-btn)"  oncomplete="goToNextStep()" />
                    <p:column styleClass="autocomplete-partner-column nowrap">
                    	<h:outputText value="#{partner.code}"/>
                    </p:column>
                    <p:column styleClass="autocomplete-partner-fullname-column nowrap"><h:outputText value="#{partner.fullname}"/></p:column>
                </p:autoComplete>
            </p:outputPanel>
            
            <p:fileUpload label="#{labels.entry_add}"
	            fileUploadListener="#{cc.attrs.value.onFileUploadTemplate}"
	            widgetVar="wFileUploadTemplate" mode="advanced" auto="true"
	            multiple="true"
	            sizeLimit="#{cc.attrs.maxDocumentSizeInByte}"
	            styleClass="btn-upload-orange drag-drop-section add-template-btn"
	            allowTypes="/(\.|\/)(xml)$/" invalidFileMessage="#{gnxspdfadinvpdflbls.invalid_file_xml}" update="@(.dtlTemplatesList)"
	            rendered="#{cc.attrs.isEditable}"
	            disabled="#{cc.attrs.value.selectedPartner == null}"/>
	            
            <p:outputPanel styleClass="list-item-custom">
                <p:dataList styleClass="dtlTemplatesList"
                			widgetVar="wTempateList"
                			var="template"
                			value="#{cc.attrs.isRendered == true ? cc.attrs.value.templates : null}"
                            type="ordered" emptyMessage="#{cc.attrs.value.selectedPartner == null ? '' : labels.no_records_found}">

                       <!-- NON EDITABLE TEMPLATE ITEM -->
                    <p:outputPanel rendered="#{not cc.attrs.isEditable}" 
                    			   styleClass="select-item-custom #{(cc.attrs.value.selectedTemplate == template) ? 'select-item-custom-hover' : ''}">
                       	<p:commandLink id="ajaxPtn" actionListener="#{cc.attrs.value.setSelectedTemplate(template)}" update="@(.list-item-custom)">
					        <p:outputPanel styleClass="icon-item-custom margin-bottom"/>
					        <p:tooltip for="templateIcon" position="bottom">
					            <h:panelGrid columns="3" columnClasses="right bold,,">
					                <h:outputText value="#{labels.name}" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.name}" />
					                <h:outputText value="#{labels.company}" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.customer}" />
					                <h:outputText value="#{labels.partner}" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.partner}" />
					                <h:outputText value="URI" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.uri}" />
					            </h:panelGrid>
					        </p:tooltip>
					        <h:outputText value="#{template.name}"/>
                        </p:commandLink>
                    </p:outputPanel>
  
                    <!--  EDITABLE TEMPLATE ITEM -->
                    <p:outputPanel styleClass="select-item-custom psTemplate" rendered="#{cc.attrs.isEditable}">
                         <!-- GENERIX TEMPLATE -->
                         <p:commandButton id="deleteTemplate" style="margin-left: 50px !important;"
				                         icon="fa fa-times-circle"
				                         actionListener="#{cc.attrs.value.onRemoveTemplate(template)}"
				                         onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
				                         process="@this"
				                         styleClass="displayOnGenerixTemplate delete-template"
				                         update="@(.list-item-custom)"/>
                         <!-- GENERIXCUSTOMER TEMPLATE -->
                         <p:commandButton id="deleteTemplateGnxCustomer" style="margin-right: 0px;"
				                         icon="ui-icon ui-icon-close"
				                         actionListener="#{cc.attrs.value.onRemoveTemplate(template)}"
				                         onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
				                         process="@this"
				                         styleClass="displayOnGenerixCustomerTemplate delete-template"
				                         update="@(.list-item-custom)"/>
				                        
				        <p:outputPanel id="templateIcon" styleClass="icon-item-custom margin-bottom"/>
				        <p:tooltip id="templateIconTip" for="templateIcon" position="bottom">
				            <h:panelGrid columns="3" columnClasses="right bold,,">
				                <h:outputText value="#{labels.name}" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.name}" />
				                <h:outputText value="#{labels.company}" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.customer}" />
				                <h:outputText value="#{labels.partner}" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.partner}" />
				                <h:outputText value="URI" /><h:outputText value="&#160;&#160;" /><h:outputText value="#{template.uri}" />
				            </h:panelGrid>
				        </p:tooltip>
				        <p:inplace id="templateName" editor="true" styleClass="template-editor margin-bottom" saveLabel="#{labels.validate}" cancelLabel="#{labels.cancel}">
				            <p:ajax event="save" listener="#{cc.attrs.value.onChangeTemplateName(template)}" update="@(.psTemplate)" />
				            <f:facet name="output">
                                <!-- GENERIX TEMPLATE -->
				                <p:outputPanel styleClass="displayOnGenerixTemplate ellipsis">
				                    <h:outputText value="#{template.name}"/>
				                </p:outputPanel>
                                <!-- GENERIXCUSTOMER TEMPLATE -->
                                <p:outputPanel styleClass="displayOnGenerixCustomerTemplate templateLabelWrapper ellipsis text-center" style="width:100%">
                                        <p:outputPanel styleClass="templateModelType">
                                            <h:outputText styleClass="text-muted"
                                                value="#{gnxspdfadinvpdflbls.template_model}" />
                                        </p:outputPanel>
                                        <p:outputPanel styleClass="templateName">
                                            <h:outputText
                                                value="#{template.name}" />
                                        </p:outputPanel>
                                </p:outputPanel>
				            </f:facet>
				            <f:facet name="input">
				                <p:inputText styleClass="inplace-template-edit" value="#{template.name}" validator="xssValidator" required="true" label="#{template.name}"/>
				            </f:facet>
				        </p:inplace>
					</p:outputPanel>
                </p:dataList>
            </p:outputPanel>
        </p:outputPanel>
        
        <p:outputPanel styleClass="displayOnGenerixCustomerTemplate uploadFileBtn row pull-right">
            <p:commandButton
                rendered="#{cc.attrs.value.selectedPartner != null}"
                type="button"
                value="selectionner"
                onclick="PF('wFileUploadTemplate').jq.find('input:file').click()"
                styleClass="displayOnGenerixCustomerTemplate customer-template-fileupload-btn btn btn-primary"/>
        </p:outputPanel>
        
        
	 
    </p:outputPanel>
    </cc:implementation>
</ui:component>