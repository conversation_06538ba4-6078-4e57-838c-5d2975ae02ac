<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui" 
	xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets" xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions" xmlns:b="http://byzaneo.com/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core" xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx">
	<cc:interface name="planningScheduleEdit" />
	<cc:implementation>
		<!-- init client locale -->
		#{cc.attrs.value.setLanguage(cc.attrs.locale)}
		<!-- * COLUMNS * -->
		<p:fieldset legend="#{comApplicationHandler.label('dmttsklbls','im_columns')}" toggleable="true">
			<p:outputPanel styleClass="form-group">
				<p:outputLabel value="#{labels.sort_by}" styleClass="col-sm-2 control-label" />
				<p:outputPanel styleClass="col-sm-8">												
					<p:selectOneMenu id="defaultSortColSlct" value="#{cc.attrs.value.sortBy}" style="width:100%">
						<f:selectItem itemLabel="#{labels.select_one}" itemValue="" />  
						<f:selectItems value="#{cc.attrs.value.getDescriptor(cc.attrs.owner).getProperties()}" var="column" 
							itemLabel="#{gnxHandler.label(column.label, cc.attrs.locale, cc.attrs.defaultLocale)}" 
							itemValue="#{column.name}"/>
					</p:selectOneMenu>
				</p:outputPanel>
				<p:outputPanel styleClass="col-sm-2">
					<p:selectBooleanCheckbox id="defaultSortOrdSlct" value="#{cc.attrs.value.sortAsc}" itemLabel="#{labels.sort_order_ascending}" styleClass="form-control" />
				</p:outputPanel>
			</p:outputPanel>
			<p:dataTable id="scheduleColumnTable" value="#{cc.attrs.value.getDescriptor(cc.attrs.owner).getProperties()}" 
				var="column" emptyMessage="#{labels.no_records_found}"
				styleClass="datatable-noheader" rowStyleClass="#{column.rendered ? null : 'disabled'}"
				draggableRows="true">
				<p:ajax event="rowReorder" listener="#{cc.attrs.value.onColumnReorder}" update="@none" />
				<p:column style="width:30px;" styleClass="center">
					<p:selectBooleanCheckbox value="#{column.rendered}" id="scheduleColRdr">
						<p:ajax process="scheduleColumnTable" immediate="true" update="scheduleColumnTable" />
					</p:selectBooleanCheckbox>
				</p:column>
				<p:column style="width:20%;" styleClass="bold nowrap">
					<h:outputText value="#{column.simpleName}" title="#{column.name}" />
				</p:column>
				<p:column styleClass="center">
					<p:inputText id="scheduleColIpt" value="#{column.label}" style="width:98%;" disabled="#{not column.rendered}" >
						<!-- needs to submit value on blur to preserve it on column disabling -->
						<!-- <p:ajax event="blur" process="scheduleColIpt" immediate="true" update="scheduleColumnTable" /> -->
						<f:validator validatorId="xssValidator" />
					</p:inputText>
					<b:labelSet id="lsScheduleColIpt" for="scheduleColIpt" locales="#{cc.attrs.locales}" localeSelected="#{cc.attrs.locale}" defaultLocale="#{cc.attrs.defaultLocale}" selector="false" />
				</p:column>
				<p:column style="width:8%;" styleClass="bold nowrap">
					<h:outputText value="#{comApplicationHandler.label('comtsklbls','style_label')}" title="#{comApplicationHandler.label('comtsklbls','style_label')}" />
				</p:column>
				<p:column>
					<p:inputText id="scheduleStyleColIpt" value="#{column.style}" validator="xssValidator" style="width:70%;" disabled="#{not column.rendered}" />
				</p:column>
				<p:column>
					<p:selectBooleanButton value="#{column.searchable}"
						onLabel="#{gnxxcblorderlbls.recherchable}"
						offLabel="#{gnxxcblorderlbls.non_recherchable}"
						onIcon="ui-icon-check"
						offIcon="ui-icon-close"
						style="width:180px" />
				</p:column>
				<p:column style="width:150px;" styleClass="right nowrap" >
					<p:selectOneMenu id="scheduleColIptRenderGps" value="#{column.renderRestrict}"  disabled="#{not column.rendered}">
						<f:selectItem itemValue="None" itemLabel="#{labels.all}" />
						<f:selectItem itemValue="Company" itemLabel="#{labels.company}" />
						<f:selectItem itemValue="Partners" itemLabel="#{labels.partners}" />
					</p:selectOneMenu>
				</p:column>
				<p:column style="width:25px;cursor:pointer;cursor:hand;" styleClass="center">
					<h:panelGroup styleClass="fa fa-arrows-v fa-lg" style="color: grey;"/>
				</p:column>
			</p:dataTable>
		</p:fieldset>
		
		<!-- ADVANCHE SEARCH -->
		<g:advancedSearchEdition gnxxcblcomlbls="#{gnxxcblcomlbls}" value="#{cc.attrs.value}" />
		
		<p:fieldset legend="#{labels.query}" toggleable="true">
			<b:query id="query" value="#{cc.attrs.value.bqlFilter}" model="#{cc.attrs.value.getQueryModel(cc.attrs.owner)}"
				completeMethod="#{cc.attrs.value.onCompleteQuery}" widgetVar="wBqlQuery" helpRendered="fase" />
		</p:fieldset>
 	</cc:implementation>
</ui:component>