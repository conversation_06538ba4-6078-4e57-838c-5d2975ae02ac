<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:c="http://java.sun.com/jsp/jstl/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:gnx="http://webui.generix.com/jsf"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:b="http://byzaneo.com/ui"
              xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
              xmlns:t="http://xmlns.jcp.org/jsf/composite/components/gnx/customCalendar"
              xmlns:pe="http://primefaces.org/ui/extensions">
    <cc:interface name="paymentView">
        <cc:attribute name="value" class="com.byzaneo.generix.xcbl.portal.task.PaymentTask"/>
        <cc:attribute name="owner" class="com.byzaneo.generix.bean.Instance"/>
        <cc:attribute name="locale" class="java.util.Locale"/>
        <cc:attribute name="defaultLocale" class="java.util.Locale"/>
    </cc:interface>

    <cc:implementation>
        <script type="text/javascript">

            function resolveErrorMessages(args) {
                $('#tskForm-taskMsgTop').css('display', 'none');
                $('#tskForm-taskMsgBot').css('display', 'none');
            }

            function disableOrEnableCommandButton() {
                if (PF('wGlobalAccepEarlyPaymentTable').getSelectedRowsCount() > 0) {
                    PF('wvglobalAcceptAcceptButtonId').enable();
                } else {
                    PF('wvglobalAcceptAcceptButtonId').disable();
                }
            }

            function showStatus() {
                PF('statusDialog').show();
            }

            function hideStatus() {
                PF('statusDialog').hide();
            }

            function updateInvoiceSelectionMessage() {
                updateIndexableSelectionMessage('#{gnxxcblinvlbls.all_invoices_selected}', '#{gnxxcblinvlbls.one_invoice_selected}', '#{gnxxcblinvlbls.multiple_invoices_selected}');
            }

            function showSelectedInvoices() {
                showSelectedIndexables('#{gnxxcblinvlbls.all_invoices_selected}', '#{gnxxcblinvlbls.one_invoice_selected}', '#{gnxxcblinvlbls.multiple_invoices_selected}');
            }

            function selectAllInvoiceRows() {
                selectAllRows('#{gnxxcblinvlbls.all_invoices_selected}')
            }

            function onAcceptHonnor(checkboxStatus) {
                var button = $('#tskForm-paymentView-finalAcceptButtonId');
                if (checkboxStatus) {
                    PF('wFinalAcceptButton').enable();
                } else {
                    PF('wFinalAcceptButton').disable();
                }

            }

            function dateInPast(dateToCompare) {
                if (firstDate.setHours(0, 0, 0, 0) &lt;= new Date()) {
                    return true;
                }
                return false;
            };

        </script>

        #{cc.attrs.value.setLanguage(gnxSessionHandler.locale.toString())}
        <style type="text/css">

            .ui-datatable-tablewrapper {
                min-height: 530px;
            }

            html.progress-cursor, html.progress-cursor * {
                cursor: progress !important;
            }

        </style>
        <ui:param name="taskProperties" value="#{cc.attrs.value.getDescriptor().getProperties()}"/>
        <ui:param name="taskBean" value="#{cc.attrs.value}"/>

        <ui:include src="/components/gnx/formPageTemplate.xhtml"/>

        <p:outputPanel id="invoiceViewPnl" styleClass="psDocumentPnl portletPosition">
            <!-- * LIST *   -->
            <p:outputPanel styleClass="psTaskList" rendered="#{cc.attrs.value.viewer==null}">
                <!-- ADVANCE SEARCH -->
                <p:outputPanel styleClass="col-md-12">
                    <p:outputPanel styleClass="form-group row">
                        <p:outputPanel styleClass="col-sm-11">
                            <g:search taskBean="#{taskBean}"
                                      quickSearchDisable="#{cc.attrs.value.quickSearchMode == 'DISABLE' ? 'true' : 'false'}"/>
                        </p:outputPanel>
                        <g:editColumnsFO value="#{cc.attrs.value}" locales="#{cc.attrs.locales}"
                                         defaultLocale="#{cc.attrs.defaultLocale}" localeSelected="#{cc.attrs.locale}"/>
                    </p:outputPanel>
                </p:outputPanel>
                <!-- TABLE -->
                <p:outputPanel styleClass="resultTablePnl" rendered="#{not cc.attrs.value.editFOColumns}">
                    <p:dataTable id="resultTable"
                                 styleClass="psPaymentTable"
                                 widgetVar="wResultTable"
                                 value="#{cc.attrs.value.dataModel}" lazy="true" var="doc"
                                 selection="#{cc.attrs.value.multiSelected}"
                                 rows="15"
                                 paginator="true"
                                 paginatorPosition="bottom"
                                 paginatorTemplate="#{cc.attrs.value.countEnabled  ? '{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}' : '{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {RowsPerPageDropdown}'}"
                                 currentPageReportTemplate="#{cc.attrs.value.countEnabled  ? '{startRecord}-{endRecord}/{totalRecords}' : '{startRecord}-{endRecord}' }"
                                 rowsPerPageTemplate="15,50,100,500"
                                 pageLinks="11"
                                 scrollable="false"
                                 resizableColumns="true"
                                 rowKey="#{doc.id}"
                                 emptyMessage="#{labels.no_records_found}"
                                 rowIndexVar="rowIndex"
                                 sortOrder="#{cc.attrs.value.sortAsc ? 'ascending' : 'descending'}"
                                 sortBy="#{cc.attrs.value.sortBy}"
                                 sortField="#{cc.attrs.value.sortBy}"
                                 rendered="#{cc.attrs.value.logs==null}"
                                 rowSelectMode="checkbox">

                        <p:ajax event="rowSelectCheckbox"
                                async="true"
                                global="false"
                                ignoreAutoUpdate="true"
                                immediate="true"
                                onstart="updateInvoiceSelectionMessage();"
                                listener="#{cc.attrs.value.onSelectCheckbox}"
                                process="@this"
                                update="@none"/>

                        <p:ajax event="rowUnselectCheckbox"
                                async="true"
                                global="false"
                                ignoreAutoUpdate="true"
                                immediate="true"
                                onstart="if(PrimeFaces.widgets.wSelectOneMenu.getSelectedValue() === 'true') {unselectAllRows();} else {updateInvoiceSelectionMessage();}"
                                listener="#{cc.attrs.value.onUnselectCheckbox}"
                                process="@this"
                                update="@none"/>

                        <p:ajax event="toggleSelect"
                                async="true"
                                global="true"
                                ignoreAutoUpdate="true"
                                immediate="true"
                                onstart="if(PrimeFaces.widgets.wSelectOneMenu.getSelectedValue() === 'true') {unselectAllRows();} else {updateInvoiceSelectionMessage();}"
                                listener="#{cc.attrs.value.onToggleSelect}"
                                process="@this"
                                update="@none"/>

                        <p:ajax event="page" oncomplete="updateRowCount(xhr, status, args); updateInvoiceSelectionMessage();"/>
                        <p:ajax event="sort" oncomplete="resetOrderBy();updateInvoiceSelectionMessage();" process="@this"/>

                        <!-- * COLUMNS * -->
                        <!-- Actions column -->
                        <p:column styleClass="center actions-column" exportable="false">
                            <f:facet name="header">
                                <p:outputPanel styleClass="psActions">
                                    <p:commandButton id="actions"
                                                     styleClass="psActionsBtn globalActionsButton"
                                                     title="#{labels.actions}"
                                                     icon="fa fa-ellipsis-h" type="button"/>
                                    <p:tieredMenu id="globalMenu" styleClass="xcbltask-tieredmenu jqsactions"
                                                  overlay="true" trigger="actions"
                                                  my="left top" at="left bottom">
                                        <p:menuitem id="actionMenuExportList"
                                                    styleClass="fileExportAction"
                                                    value="#{gnxxcblorderlbls.download_list}"
                                                    actionListener="#{cc.attrs.value.onDownloadList(cc.attrs.owner)}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args &amp;&amp; args.download){jQuery('.jqsDownload').click()} showSelectedInvoices();"
                                                    immediate="true"
                                                    process="@this"
                                                    update="@(.psDocumentPnl)"
                                                    rendered="#{cc.attrs.value.listExportAllowed}"/>
                                        <p:menuitem id="actionMenuRejectPayment"
                                                    value="#{gnxxcblpaymentlbls.early_payment_seller_reject}"
                                                    oncomplete="if (args &amp;&amp; args.canPerformAction){ PF('early_payment_seller_reject_wgv').show()} resolveErrorMessages(args);"
                                                    actionListener="#{cc.attrs.value.onGlobalPaymentAction()}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    immediate="true"
                                                    process="@this"
                                                    update="@(.early_payment_seller_honor_style)"
                                                    rendered="#{gnxSessionHandler.isGranted('Portlet_Payment_Accept_Reject_Early_Payment', 'CREATE') == true}"/>
                                        <p:menuitem id="actionMenuAcceptPayment"
                                                    value="#{gnxxcblpaymentlbls.early_payment_seller_accept}"
                                                    oncomplete="if (args &amp;&amp; args.canPerformAction){ PF('wGlobalAccepEarlyPayment').show()} resolveErrorMessages(args); "
                                                    actionListener="#{cc.attrs.value.onGlobalPaymentAction()}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    immediate="true"
                                                    process="@this"
                                                    update="@(.global-accept-dialog)"
                                                    rendered="#{gnxSessionHandler.isGranted('Portlet_Payment_Accept_Reject_Early_Payment', 'CREATE') == true}"/>
                                        <p:menuitem id="globalTrigger1" value="#{cc.attrs.value.getTriggerActionName('ACTION_1')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_1')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_1')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_1')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_1', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger2" value="#{cc.attrs.value.getTriggerActionName('ACTION_2')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_2')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_2')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_2')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_2', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger3" value="#{cc.attrs.value.getTriggerActionName('ACTION_3')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_3')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_3')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_3')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_3', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger4" value="#{cc.attrs.value.getTriggerActionName('ACTION_4')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_4')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_4')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_4')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_4', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger5" value="#{cc.attrs.value.getTriggerActionName('ACTION_5')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_5')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_5')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_5')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_5', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger6" value="#{cc.attrs.value.getTriggerActionName('ACTION_6')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_6')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_6')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_6')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_6', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger7" value="#{cc.attrs.value.getTriggerActionName('ACTION_7')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_7')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_7')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_7')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_7', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger8" value="#{cc.attrs.value.getTriggerActionName('ACTION_8')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_8')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_8')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_8')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_8', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger9" value="#{cc.attrs.value.getTriggerActionName('ACTION_9')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_9')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_9')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_9')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_9', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                        <p:menuitem id="globalTrigger10" value="#{cc.attrs.value.getTriggerActionName('ACTION_10')}"
                                                    actionListener="#{cc.attrs.value.onTriggerGlobalAction('ACTION_10')}"
                                                    rendered="#{cc.attrs.value.getTriggerActionName('ACTION_10')!=null &amp;&amp; cc.attrs.value.isTriggerActionGlobal('ACTION_10')  &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_10', 'CREATE')}"
                                                    onclick="jQuery('.jqsactions').hide();'"
                                                    oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                                    update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                    </p:tieredMenu>
                                </p:outputPanel>
                            </f:facet>
                            <p:commandButton id="actionInv"
                                             title="#{gnxxcblinvlbls.actions}"
                                             type="button"
                                             styleClass="invoiceActionButton"
                                             icon="fa fa-ellipsis-h"/>

                            <p:tieredMenu id="columnTieredMenu" styleClass="psMenuExport xcbltask-tieredmenu"
                                          overlay="true"
                                          trigger="actionInv"
                                          my="left top"
                                          at="left bottom"
                                          rendered="#{(doc.paymentStatus=='EP_ELIGIBLE'and doc.processingWay=='RECEIVING') or doc.paymentStatus=='EP_SUBMITTED'}"
                                          style="width:290px;">
                                <p:menuitem id="idMenuItem_Early_payment_Buyer"
                                            value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_title}"
                                            global="false"
                                            ignoreAutoUpdate="true"
                                            actionListener="#{cc.attrs.value.onShowEarlyPaymentDialog(doc,rowIndex)}"
                                            process="@this"
                                            rendered="#{!cc.attrs.value.checkDueDateInPast(doc) and  doc.paymentStatus=='EP_ELIGIBLE'and doc.processingWay=='RECEIVING' and gnxSessionHandler.isGranted('Portlet_Payment_Suggest_Early_Payment', 'CREATE')}"
                                            update="@(.offering_an_early_paymentId)"
                                            oncomplete="PF('offering_an_early_payment').show()"
                                            onclick="jQuery('.psMenuExport').hide();"
                                >
                                </p:menuitem>
                                <p:menuitem id="idMenuItem_Early_payment_Seller"
                                            value="#{gnxxcblpaymentlbls.early_payment_seller_title}"
                                            global="false"
                                            ignoreAutoUpdate="true"
                                            process="@this"
                                            actionListener="#{cc.attrs.value.onShowEarlyPaymentSellerDialog(doc,rowIndex)}"
                                            rendered="#{!cc.attrs.value.checkDueDateInPast(doc) and doc.paymentStatus=='EP_SUBMITTED' and gnxSessionHandler.isGranted('Portlet_Payment_Accept_Reject_Early_Payment', 'CREATE')}"
                                            update="@(.offering_an_early_paymentId)"
                                            oncomplete="PF('offering_an_early_payment').show();"
                                            onclick="jQuery('.psMenuExport').hide();"
                                >
                                </p:menuitem>
                                <p:menuitem id="trigger1" value="#{cc.attrs.value.getTriggerActionName('ACTION_1')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_1')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_1') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_1')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_1', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger2" value="#{cc.attrs.value.getTriggerActionName('ACTION_2')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_2')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_2') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_2')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_2', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger3" value="#{cc.attrs.value.getTriggerActionName('ACTION_3')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_3')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_3') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_3')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_3', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger4" value="#{cc.attrs.value.getTriggerActionName('ACTION_4')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_4')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_4') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_4')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_4', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger5" value="#{cc.attrs.value.getTriggerActionName('ACTION_5')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_5')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_5') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_5')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_5', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide()"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger6" value="#{cc.attrs.value.getTriggerActionName('ACTION_6')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_6')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_6') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_6')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_6', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide()"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger7" value="#{cc.attrs.value.getTriggerActionName('ACTION_7')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_7')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_7') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_7')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_7', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide()"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger8" value="#{cc.attrs.value.getTriggerActionName('ACTION_8')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_8')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_8') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_8')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_8', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger9" value="#{cc.attrs.value.getTriggerActionName('ACTION_9')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_9')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_9') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_9')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_9', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                                <p:menuitem id="trigger10" value="#{cc.attrs.value.getTriggerActionName('ACTION_10')}"
                                            actionListener="#{cc.attrs.value.onTriggerAction(doc,'ACTION_10')}"
                                            styleClass=" #{cc.attrs.value.isLocked(doc) &amp;&amp; cc.attrs.value.isTriggerActionActive('ACTION_10') ? 'triggerActionActive' : ''}"
                                            rendered="#{cc.attrs.value.getTriggerActionName('ACTION_10')!=null &amp;&amp; gnxSessionHandler.isGranted('Portlet_Payment_Actions_10', 'CREATE')}"
                                            onclick="jQuery('.psMenuExport').hide();"
                                            oncomplete="if (args.templateExists) {PF('wShowFormPage').show();}"
                                            update="@(.formPageView,.formPageHiddenButtons)" process="@this" global="false"/>
                            </p:tieredMenu>

                        </p:column>

                        <!-- columns -->
                        <p:column styleClass="multiSelectColumn" selectionMode="multiple">
                            <f:facet name="header">
                                <p:outputPanel>
                                    <!-- "height:inherit" didn't work in IE -->
                                    <p:selectOneMenu styleClass="multiSelectMenu"
                                                     widgetVar="wSelectOneMenu"
                                                     value="#{cc.attrs.value.allIndexesSelected}">
                                        <f:selectItem itemValue="true" itemLabel="#{gnxxcblinvlbls.select_all}"/>
                                        <f:selectItem itemValue="false" itemLabel="#{gnxxcblinvlbls.unselect_all}"/>
                                        <p:ajax event="itemSelect"
                                                listener="#{cc.attrs.value.resetSelected}"
                                                oncomplete="if(PrimeFaces.widgets.wSelectOneMenu.getSelectedValue() === 'true') {selectAllInvoiceRows();} else {unselectAllRows();}"/>
                                    </p:selectOneMenu>
                                </p:outputPanel>
                            </f:facet>
                        </p:column>
                        <p:column styleClass="small-column toggler#{rowIndex}">
                            <p:rowToggler rendered="#{cc.attrs.value.hasAttachFiles(doc)}"/>
                            <p:tooltip for="@(.toggler#{rowIndex} .ui-row-toggler)"
                                       rendered="#{cc.attrs.value.hasAttachFiles(doc)}"
                                       value="#{doc.attachFileCount}"
                                       position="bottom"/>
                        </p:column>

                        <!-- Properties columns -->
                        <gnx:pcolumn property="#{taskProperties.get(0)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(1)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(2)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(3)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(4)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(5)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(6)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(7)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(8)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(9)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(10)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(11)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(12)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(13)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(14)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(15)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(16)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(17)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(18)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(19)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(20)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(21)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(22)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(23)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(24)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(25)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(26)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(27)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(28)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(29)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(30)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(31)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(32)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(33)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(34)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(35)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(36)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(37)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(38)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(39)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(40)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(41)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(42)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(43)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(44)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(45)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(46)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(47)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(48)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(49)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(50)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(51)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(52)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(53)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(54)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(55)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(56)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(57)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(58)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(59)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(60)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(61)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(62)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(63)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(64)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(65)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(66)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(67)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(68)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(69)}"/>
                        <gnx:pcolumn property="#{taskProperties.get(70)}"/>
                        <p:rowExpansion rendered="#{cc.attrs.value.isRowExpanded(doc)}">
                            <p:dataTable
                                    value="#{cc.attrs.value.getRowExpansionDataModel(doc)}"
                                    rendered="#{cc.attrs.value.isRowExpanded(doc)}"
                                    var="attachedFile"
                                    lazy="true"
                                    rows="100"
                                    paginator="false"
                                    rowKey="#{attachedFile.id}"
                                    rowIndexVar="index"
                                    emptyMessage="#{gnxxcblasnlbls.asn_no_attachment_found}"
                                    styleClass="fileTable">

                                <p:column headerText="#{gnxxcblinvlbls.attachment_name}" styleClass="center">
                                    <p:commandLink actionListener="#{cc.attrs.value.showAttachFiles(doc,index)}"
                                                   update="@(.psDocumentPnl)">
                                        <h:outputText value="#{cc.attrs.value.getReducedFileName(attachedFile.file.name)}"/>
                                    </p:commandLink>
                                </p:column>
                                <p:column headerText="#{gnxxcblinvlbls.comment}" styleClass="center simple-column">
                                    <h:outputText value="#{attachedFile.comment}"/>
                                </p:column>
                                <p:column headerText="#{gnxxcblinvlbls.add_date}" styleClass="center simple-column">
                                    <h:outputText value="#{attachedFile.creationDate}">
                                        <f:convertDateTime pattern="#{gnxSessionHandler.userDateFormat}"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{gnxxcblinvlbls.size}" styleClass="center simple-column">
                                    <h:outputText value="#{cc.attrs.value.getFileSize(attachedFile)}"/>
                                </p:column>
                            </p:dataTable>
                        </p:rowExpansion>
                    </p:dataTable>
                </p:outputPanel>
                <p:ajaxStatus onstart="PF('statusDialog').show()" oncomplete="PF('statusDialog').hide()"/>

            </p:outputPanel>
            <!-- * VIEW * -->
            <p:outputPanel styleClass="psDocumentViewerTimeline col-md-12" rendered="#{cc.attrs.value.viewer!=null}">
                <p:outputPanel styleClass="form-group row">
                    <h:panelGroup id="timelinePanel" styleClass="col-sm-4" layout="block" rendered="#{cc.attrs.value.showTimeline()}">
                        <p:outputPanel styleClass="timelineBorder">
                            <h:form id="timelineForm">
                                <p:outputPanel
                                        styleClass="steps d-flex flex-wrap flex-sm-nowrap justify-content-between p-3 timelineStyleClass"
                                        style="margin-top:15px;">
                                    <p:outputPanel styleClass="step">
                                        <p:dataList value="#{cc.attrs.value.invoiceTimelines}" var="doc" type="none">
                                            <p:outputPanel styleClass="col-md-12">
                                                <p:outputPanel styleClass="form-group row">
                                                    <p:outputPanel styleClass="col-sm-12 row">
                                                        <p:outputPanel styleClass="col-sm-3">
                                                            <h:outputText styleClass="step-title" value="#{doc.date}">
                                                                <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both"
                                                                                   dateStyle="medium" timeStyle="short"
                                                                                   timeZone="#{secSessionHandler.timeZone}"
                                                                                   pattern="#{gnxSessionHandler.userDateFormat}"/>
                                                            </h:outputText>
                                                        </p:outputPanel>
                                                        <p:outputPanel styleClass="timeline col-sm-2">
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-paperclip"
                                                                           rendered="#{doc.action eq 'ATTACHFILE'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-comments"
                                                                           rendered="#{doc.action eq 'COMMENT'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-user"
                                                                           rendered="#{doc.action eq 'LOCK'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-times-circle"
                                                                           rendered="#{doc.action eq 'REFUSE'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-user"
                                                                           rendered="#{doc.action eq 'UNLOCK'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-check-circle"
                                                                           rendered="#{doc.action eq 'VALID'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-stop"
                                                                           style="font-size: 22px;" rendered="#{doc.action eq 'END'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-play"
                                                                           rendered="#{doc.action eq 'INIT'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-sign-out"
                                                                           rendered="#{doc.action eq 'WKF_EXIT'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-pencil"
                                                                           rendered="#{doc.action eq 'ENRICH'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-clipboard"
                                                                           rendered="#{doc.action eq 'REDIRECT'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-arrow-circle-left"
                                                                           rendered="#{doc.action eq 'TASK_DONE'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban"
                                                                           rendered="#{doc.action eq 'TASK_CANCEL'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-pencil-square-o"
                                                                           rendered="#{doc.action eq 'EDIT'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-pencil-square-o"
                                                                           rendered="#{doc.action eq 'FIX'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-bell-o fontSizeInvoicePayment"
                                                                           rendered="#{doc.action eq 'REMINDER'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-bell-slash-o fontSizeInvoicePayment"
                                                                           rendered="#{doc.action eq 'PAUSE'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-wrench"
                                                                           rendered="#{doc.action eq 'CUSTOM'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_EMPTY_SELLER_PARTY'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_EMPTY_ORDER_NUMBER'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_ORDER_NOT_FOUND'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_EMPTY_RECEPTION_NUMBER'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_RECEPTION_NOT_FOUND'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-calendar"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_START'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-check-square-o"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_SUCCESS'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-exclamation"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_GAP_FOUND'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-calendar"
                                                                           rendered="#{doc.action eq 'RECONCILIATION_RELAUNCH'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-envelope-open" 
                                                            			   rendered="#{doc.action eq 'CONSULT'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-file-invoice-dollar fontSizeInvoicePayment"  
                                                                           rendered="#{doc.action eq 'EP_SUBMITED'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-handshake fontSizeInvoicePayment"  
                                                                           rendered="#{doc.action eq 'EP_ACCEPTED'}"/>
                                                            <p:outputPanel styleClass="timelineActionIcon fa fa-ban fontSizeInvoicePayment"
                                                                           rendered="#{doc.action eq 'EP_REFUSED'}"/>

                                                        </p:outputPanel>

                                                        <p:outputPanel styleClass="col-sm-7 timelineActions left">
                                                            <h:outputText styleClass="timelineUsername step-title"
                                                                          value="#{cc.attrs.value.getUsernameById(doc.user)} "
                                                                          rendered="#{doc.action ne 'INIT' and doc.action ne 'END' and doc.user ne 'System'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.init_action_message} #{cc.attrs.value.getTimelineComment(doc.id, doc.comment)}"
                                                                          rendered="#{doc.action eq 'INIT'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.wkf_exit_action_message}"
                                                                          rendered="#{doc.action eq 'WKF_EXIT'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{not empty doc.comment ? gnxxcblinvlbls.attatch_file_action_message_comment : gnxxcblinvlbls.attatch_file_action_message} "
                                                                          rendered="#{doc.action eq 'ATTACHFILE'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{not empty doc.comment ? gnxxcblinvlbls.refuse_action_message_comment : gnxxcblinvlbls.refuse_action_message} "
                                                                          rendered="#{doc.action eq 'REFUSE'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.custom_action_message}"
                                                                          rendered="#{doc.action eq 'CUSTOM'}"/>
                                                            <h:outputText styleClass="step-title timelineCustomAction"
                                                                          value="#{cc.attrs.value.getTimelineComment(doc.id, doc.comment)}"
                                                                          rendered="#{doc.action eq 'CUSTOM'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.transferTask_action_message}"
                                                                          rendered="#{doc.action eq 'REDIRECT'}"/>
                                                            <h:outputText styleClass="timelineUsername step-title"
                                                                          value="#{doc.redirectUser}"
                                                                          rendered="#{doc.action eq 'REDIRECT'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.give_back_control_action_message} "
                                                                          rendered="#{doc.action eq 'TASK_DONE'}"/>
                                                            <h:outputText styleClass="timelineUsername step-title"
                                                                          value="#{doc.redirectUser}"
                                                                          rendered="#{doc.action eq 'TASK_DONE'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.cancel_assign_task_action_message} "
                                                                          rendered="#{doc.action eq 'TASK_CANCEL'}"/>
                                                            <h:outputText styleClass="timelineUsername step-title"
                                                                          value="#{doc.redirectUser}"
                                                                          rendered="#{doc.action eq 'TASK_CANCEL'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value=" #{gnxxcblinvlbls.transferTask_action_message_comment} "
                                                                          rendered="#{doc.action eq 'REDIRECT'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{doc.action eq 'COMMENT' and doc.user ne 'System' ? gnxxcblinvlbls.comment_action_message : gnxxcblinvlbls.comment_automatic_message}"
                                                                          rendered="#{doc.action eq 'COMMENT'}"/>
                                                            <h:outputText styleClass="step-title timelineComment" escape="false"
                                                                          value="&lt;br /&gt; #{cc.attrs.value.getTimelineComment(doc.id, doc.comment)}"
                                                                          rendered="#{doc.comment != null and doc.comment ne '' and doc.action ne 'INIT' and doc.action ne 'CUSTOM' and doc.action ne 'VALID' and doc.action ne 'END' and doc.action ne 'REMINDER'
                                                           and doc.action ne 'RECONCILIATION_START' and doc.action ne 'RECONCILIATION_EMPTY_SELLER_PARTY' and doc.action ne 'RECONCILIATION_EMPTY_ORDER_NUMBER'
                                                           and doc.action ne 'RECONCILIATION_ORDER_NOT_FOUND' and doc.action ne 'RECONCILIATION_EMPTY_RECEPTION_NUMBER' and doc.action ne 'RECONCILIATION_RECEPTION_NOT_FOUND' and doc.action ne 'RECONCILIATION_GAP_FOUND' and doc.action ne 'RECONCILIATION_SUCCESS'
                                                           and doc.action ne 'EP_SUBMITED' and doc.action ne 'EP_ACCEPTED' and doc.action ne 'EP_REFUSED'}"/>
                                                            <p:commandLink action="#{cc.attrs.value.showFullTimelineComment(doc.id)}"
                                                                           process="@this"
                                                                           global="false"
                                                                           value=" see more"
                                                                           rendered="#{doc.comment != null and doc.comment ne '' and cc.attrs.value.timelineTruncatedComments.get(doc.id)}"
                                                                           update="@(.timelineStyleClass)">
                                                            </p:commandLink>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.lock_action_message}"
                                                                          rendered="#{doc.action eq 'LOCK'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.unlock_action_message}"
                                                                          rendered="#{doc.action eq 'UNLOCK'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.validate_action_message}"
                                                                          rendered="#{doc.action eq 'VALID' and doc.user ne 'System'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.end_action_message}"
                                                                          rendered="#{doc.action eq 'END'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.enrich_action_message}"
                                                                          rendered="#{doc.action eq 'ENRICH'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.edit_action_message}"
                                                                          rendered="#{doc.action eq 'EDIT'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.fix_action_message}"
                                                                          rendered="#{doc.action eq 'FIX'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_start_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_START'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_empty_seller_party_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_EMPTY_SELLER_PARTY'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_empty_order_number_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_EMPTY_ORDER_NUMBER'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_order_not_found_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_ORDER_NOT_FOUND'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_empty_reception_number_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_EMPTY_RECEPTION_NUMBER'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_reception_not_found_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_RECEPTION_NOT_FOUND'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_gap_found_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_GAP_FOUND'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.reconciliation_success_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_SUCCESS'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{gnxxcblinvlbls.relaunch_reconciliation_message}"
                                                                          rendered="#{doc.action eq 'RECONCILIATION_RELAUNCH'}"/>
                                                            <h:outputText styleClass="step-title" 
                                                            			  value="#{gnxxcblinvlbls.consult_message}" 
                                                            			  rendered="#{doc.action eq 'CONSULT'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{xtdTimelineHandler.getLabelForEpSubmitedAction('gnxxcblinvlbls','ep_submited_message',doc,gnxSessionHandler.locale)}"
                                                                          rendered="#{doc.action eq 'EP_SUBMITED'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','early_payment_seller_accept_add_timeline',doc,gnxSessionHandler.locale)}"
                                                                          rendered="#{doc.action eq 'EP_ACCEPTED'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','early_payment_seller_reject_add_timeline',doc,gnxSessionHandler.locale)}"
                                                                          rendered="#{doc.action eq 'EP_REFUSED'}"/>


                                                            <!-- Automatic messages -->
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{xtdTimelineHandler.getLabelForAutomaticValidation('gnxxcblinvlbls','valid_action_automatic_message',doc,gnxSessionHandler.locale)}"
                                                                          rendered="#{doc.action eq 'VALID' and doc.user eq 'System'}"/>
                                                            <h:outputText styleClass="step-title"
                                                                          value="#{xtdTimelineHandler.getLabelForAutomaticReminder('gnxxcblinvlbls','reminder_action_automatic_message', doc,gnxSessionHandler.locale)}"
                                                                          rendered="#{doc.action eq 'REMINDER' and doc.user eq 'System'}"/>
                                                        </p:outputPanel>

                                                    </p:outputPanel>

                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </p:dataList>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </h:form>
                        </p:outputPanel>
                    </h:panelGroup>
                    <p:outputPanel
                            styleClass="#{cc.attrs.value.showTimeline() ? 'col-sm-8' : 'col-sm-12'} control-label timelineBorder">
                        <!-- Show/hide timeline action -->
                        <p:commandLink action="#{cc.attrs.value.onShowHideTimeline()}"
                                       process="@this"
                                       global="false"
                                       style="margin-right:10px; color:black;height: 22px"
                                       styleClass="btn fa fa-bars fa-bars-invoice"
                                       rendered="#{cc.attrs.value.invoiceTimelines.size() > 0}"
                                       update="@(.psDocumentViewerTimeline)">
                        </p:commandLink>
                        <!-- Back and Export -->
                        <p:outputPanel styleClass="btn-group show">
                            <p:commandButton value="#{labels.back}" styleClass="btn-sm btn btn-outline-primary"
                                             onclick="jQuery('.jqsBackBtn').click();"/>
                            <p:commandButton id="export-trigger"
                                             styleClass="btn-sm btn btn-outline-primary dropdown-toggle dropdown-toggle-split splitButtonDropdownToggle individualActionsButton"
                                             type="button"
                                             rendered="#{gnxSessionHandler.isGranted('Portlet_Invoice_Export', 'CREATE') == true}"/>
                        </p:outputPanel>
                        <p:tieredMenu styleClass="jqsactions xcbltask-tieredmenu" my="left top" at="left bottom"
                                      overlay="true" trigger="tskForm-paymentView-export-trigger">
                            <p:menuitem id="exportCsv" value="#{gnxxcblorderlbls.download_csv}"
                                        global="false"
                                        update="@none"
                                        ignoreAutoUpdate="true"
                                        rendered="#{gnxSessionHandler.isGranted('Portlet_Invoice_Export', 'CREATE') == true}"
                                        onclick="jQuery('.jqsactions').hide(); jQuery('.jsqExportBtn').click();"/>
                        </p:tieredMenu>
                        <p:commandButton id="backbtn"
                                         style="display:none"
                                         immediate="true"
                                         process="@this"
                                         update="@(.psDocumentPnl)"
                                         styleClass="jqsBackBtn"
                                         oncomplete="updateColumns(true); resetOrderBy();">
                            <f:setPropertyActionListener target="#{cc.attrs.value.viewer}" value="#{null}"/>
                        </p:commandButton>
                        <p:commandButton id="exportBtn"
                                         style="display:none"
                                         actionListener="#{cc.attrs.value.onDownloadCsv(cc.attrs.owner)}"
                                         oncomplete="if (args &amp;&amp; args.download){jQuery('.jqsDownload').click()}"
                                         immediate="true"
                                         update="@none"
                                         process="@this"
                                         styleClass="jsqExportBtn">
                        </p:commandButton>
                        <!-- document title-->
                        <h:outputText
                                value="#{cc.attrs.value.viewer.baseName.length() gt 35 ? cc.attrs.value.viewer.baseName.substring(0,33).concat('...') : cc.attrs.value.viewer.baseName}"
                                title="#{cc.attrs.value.viewer.baseName}" style="float: right; padding-top: 15px;"
                                styleClass="title2 document-viewer-title"/>
                        <g:documentViewer value="#{cc.attrs.value.viewer}" showTitle="false" rendered="#{cc.attrs.value.viewer!=null}">
                        </g:documentViewer>
                    </p:outputPanel>
                </p:outputPanel>
            </p:outputPanel>
            <!-- LOADING -->
            <g:loading/>

            <!-- DOWNLOAD -->
            <p:commandLink id="download" ajax="false" value=""
                           actionListener="#{cc.attrs.value.onDownload()}"
                           styleClass="jqsDownload hidden"/>
        </p:outputPanel>

        <p:dialog modal="true" closable="true" resizable="false"
                  header="#{cc.attrs.value.isBuyer()?gnxxcblpaymentlbls.payment_offering_an_early_payment_title:gnxxcblpaymentlbls.early_payment_seller_popin_title}"
                  widgetVar="offering_an_early_payment"
                  draggable="false"
                  styleClass="psModelDialog responsive-dialog offering_an_early_paymentId" width="1000px"
                  closeOnEscape="true">
            <p:ajax event="close" actionListener="#{cc.attrs.value.onCancel()}" update="@this"/>
            <p:outputPanel id="offering_an_early_paymentId" styleClass="offering_an_early_paymentPanalStyle">
                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-6 order-md-2">
                        <p:outputLabel for="idInputTextInvoiceNumber" styleClass="col-sm-6 title4"
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_invoice_number} : "/>
                        <p:outputLabel id="idInputTextInvoiceNumber" styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.indexable.invoiceNumber}"/>
                    </p:outputPanel>
                    <p:outputPanel rendered="#{cc.attrs.value.isBuyer()}" styleClass="col-md-6 order-md-2">
                        <p:outputLabel styleClass="col-sm-6 title4" style="vertical-align:top "
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_seller} : "/>
                        <p:outputLabel styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.indexable.sellerPartyID}-#{cc.attrs.value.paymentData.indexable.sellerPartyName}"/>
                    </p:outputPanel>
                    <p:outputPanel rendered="#{!cc.attrs.value.isBuyer()}" styleClass="col-md-6 order-md-2">
                        <p:outputLabel styleClass="col-sm-6 title4" style="vertical-align:top "
                                       value="#{gnxxcblpaymentlbls.early_payment_seller_customer} : "/>
                        <p:outputLabel styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.indexable.buyerPartyID}-#{cc.attrs.value.paymentData.indexable.buyerPartyName}"/>
                    </p:outputPanel>
                </p:outputPanel>

                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-6 order-md-2">
                        <p:outputLabel for="idInputTextInvoiceIssueDate" styleClass="col-sm-6 title4"
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_date} : "/>
                        <p:outputLabel id="idInputTextInvoiceIssueDate" styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.indexable.invoiceDueDate}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" timeZone="#{secSessionHandler.timeZone}"
                                               pattern="#{gnxSessionHandler.userDateFormat}"></f:convertDateTime>
                        </p:outputLabel>
                    </p:outputPanel>
                    <p:outputPanel styleClass="col-md-6 order-md-2">
                        <p:outputLabel for="idInputTextTaxableValue" styleClass="col-sm-6 title4"
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_total_amount_exct} : "/>
                        <p:outputLabel id="idInputTextTaxableValue" styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.indexable.taxableValue}">
                            <f:converter converterId="gnxBigDecimalConverter"/>
                            <f:attribute name="symbol"
                                         value="#{cc.attrs.value.getSignByCode(cc.attrs.value.paymentData.indexable.invoiceCurrencyCoded)}"/>
                        </p:outputLabel>
                    </p:outputPanel>

                </p:outputPanel>
                <h:outputText value="&lt;br/&gt;" escape="false"/>
                <p:separator/>
                <h:outputText value="&lt;br/&gt;" escape="false"/>


                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-sm-2 order-md-1">
                        <p:outputLabel for="idRadioSelectShowOption" style="padding-top: 0.7rem;padding-left: 15px;" styleClass="title4"
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount} :"/>
                    </p:outputPanel>
                    <p:outputPanel styleClass="col-sm-8 order-md-1">
                        <p:selectOneRadio id="idRadioSelectShowOption" value="AMOUNT"
                                          styleClass="custom-control custom-radio grid-radio">
                            <f:selectItem
                                    itemLabel="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_earlyPaymentAmountDiscount}"
                                    itemValue="AMOUNT"/>
                            <f:selectItem
                                    itemLabel="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount_percentage}"
                                    itemValue="DISCOUNT"/>
                            <f:selectItem itemLabel="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_jpa_days}"
                                          itemValue="JPA"/>
                            <p:ajax event="change" update="idCalendarPanal addCalenderMetaData"
                                    oncomplete="$('.ui-datepicker-trigger').click();"
                                    listener="#{cc.attrs.value.onRadioChange}" global="true"/>
                        </p:selectOneRadio>
                    </p:outputPanel>
                </p:outputPanel>
                <p:outputPanel id="idCalendarPanal">

                    <p:outputLabel for="addCalenderMetaData" styleClass="col-md-4 order-md-2 title4"
                                   value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_payment_date} :"/>

                    <t:multiCustomCalendar maxDate="#{cc.attrs.value.paymentData.paymentDateMaxDate}"
                                           minDate="#{cc.attrs.value.paymentData.paymentDateMinDate}"
                                           value="#{cc.attrs.value.paymentData}"
                                           id="addCalenderMetaData"
                                           onDateSelect="#{cc.attrs.value.onDateSelect}" styleClass="dialogCalender"/>

                </p:outputPanel>

                <h:outputText value="&lt;br/&gt;" escape="false"/>
                <p:separator/>
                <h:outputText value="&lt;br/&gt;" escape="false"/>

                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-6 order-md-2">
                        <p:outputLabel for="idInputTextEscomptePercentage" styleClass="col-sm-6 title4"
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount_percentage} : "/>
                        <p:outputLabel id="idInputTextEscomptePercentage" styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.escomptePercentage}">
                            <f:converter converterId="gnxBigDecimalConverter"/>
                            <f:attribute name="symbol" value="%"/>
                        </p:outputLabel>


                    </p:outputPanel>
                    <p:outputPanel styleClass="col-md-6 order-md-2">
                        <p:outputLabel for="idInputTextEscmpte" styleClass="col-sm-6 title4"
                                       value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount} : "/>
                        <p:outputLabel id="idInputTextEscmpte" styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.escompte}">
                            <f:converter converterId="gnxBigDecimalConverter"/>
                            <f:attribute name="symbol"
                                         value="#{cc.attrs.value.getSignByCode(cc.attrs.value.paymentData.indexable.invoiceCurrencyCoded)}"/>
                        </p:outputLabel>
                    </p:outputPanel>
                </p:outputPanel>

                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-6 order-md-2">
                        <p:outputLabel for="idInputTextJpa_days" styleClass="col-sm-6 title4"
                                       title="#{gnxxcblpaymentlbls.early_payment_dpe_tooltip}"
                                       value="#{gnxxcblpaymentlbls.early_payment_dpe} : "/>
                        <p:outputLabel id="idInputTextJpa_days" styleClass="col-sm-6 title4"
                                       value="#{cc.attrs.value.paymentData.jpa} #{gnxxcblpaymentlbls.payment_offering_an_early_payment_early_jpa_unit}"/>
                        
                    </p:outputPanel>
                </p:outputPanel>
                
                <p:outputPanel styleClass="row">
                   <p:outputPanel styleClass="col-md-6 order-md-2" rendered="#{cc.attrs.value.isBuyer() and cc.attrs.value.paymentData.indexable != null}">
                      <p:outputLabel for="discountAnnualRate" styleClass="col-sm-6 title4"
                                       value="#{gnxxcblpaymentlbls.early_payment_discount_annual_rate} : "/>
                        <pe:inputNumber id="discountAnnualRate" value="#{cc.attrs.value.paymentData.indexable.earlyPaymentAnnualDiscountRate}" styleClass="psDiscountAnnualRate"
                            maxValue="100.00" minValue="0.00" symbol="%" symbolPosition="suffix">
                            <p:ajax event="blur"
                                    listener="#{cc.attrs.value.refreshPopinData}"
        							update="idCalendarPanal idInputTextEscmpte idInputTextEscomptePercentage"
        							global="false" />
                        </pe:inputNumber>
                   </p:outputPanel>
                </p:outputPanel>

                <p:outputPanel id="buttons-panel"
                               styleClass="ui-dialog-footer text-right responsive-buttons pull-right">
                  <p:outputPanel styleClass="validationButtonsDlg">  
                    <p:commandButton id="cancelButton"
                                     value="#{labels.cancel}"
                                     actionListener="#{cc.attrs.value.onCancel()}"
                                     onstart="PF('offering_an_early_payment').hide();"
                                     immediate="true"
                                     styleClass="btn btn-secondary"
                                     process="@this">
                    </p:commandButton>
                    <p:commandButton id="okButton"
                                     rendered="#{cc.attrs.value.isBuyer()}"
                                     value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_early_bouton_validate}"
                                     actionListener="#{cc.attrs.value.saveEarlyPayment()}"
                                     onstart="PF('offering_an_early_payment').hide();"
                                     immediate="true"
                                     styleClass="attach-button btn btn-primary"
                                     process="@this"
                                     update="resultTable">
                    </p:commandButton>
                    <p:commandButton id="rejectButtonId"
                                     rendered="#{!cc.attrs.value.isBuyer()}"
                                     value="#{gnxxcblpaymentlbls.early_payment_seller_reject}"
                                     onclick="PF('early_payment_seller_reject_wgv').show(); return false;"
                                     styleClass="btn btn-outline-danger">
                    </p:commandButton>
                    <p:commandButton id="acceptButtonId"
                                     rendered="#{!cc.attrs.value.isBuyer()}"
                                     immediate="true"
                                     value="#{gnxxcblpaymentlbls.early_payment_seller_accept}"
                                     onclick="PF('early_payment_seller_honor_wgv').show(); return false;"
                                     styleClass="btn btn-outline-success">
                    </p:commandButton>
                 </p:outputPanel>
                </p:outputPanel>
            </p:outputPanel>
        </p:dialog>
        <p:dialog modal="true" closable="true" resizable="false"
                  onShow="onAcceptHonnor(false);PF('accpetHonorBCBwWV').uncheck();"
                  widgetVar="early_payment_seller_honor_wgv"
                  draggable="false"
                  styleClass="psModelDialog responsive-dialog invoice-attachment"
                  closeOnEscape="true">
            <p:messages autoUpdate="true" showDetail="false" showSummary="true" closable="true" styleClass="dialogMessage"/>
            <p:ajax event="close" actionListener="#{cc.attrs.value.onCancelHonorPopup()}" update="@(.psPaymentTable)"/>
            <p:focus for="accpetHonorBCB"/>
            <p:outputPanel id="early_payment_seller_honor_id" styleClass="early_payment_seller_honor_style">
                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-sm-1" style="padding-top: 2%;padding-right: 0px;">
                        <p:selectBooleanCheckbox id="accpetHonorBCB" required="true" value="false" widgetVar="accpetHonorBCBwWV"
                                                 onchange="onAcceptHonnor(this.checked)"/>
                    </p:outputPanel>
                    <p:outputPanel styleClass="col-sm-10" style="padding-left: 0px;">
                        <p:outputLabel for="accpetHonorBCB" styleClass="title4"
                                       value="#{gnxxcblpaymentlbls.early_payment_seller_accept_honneur_checkbox}"/>
                    </p:outputPanel>
                </p:outputPanel>
                <p:outputPanel styleClass="ui-dialog-footer text-right responsive-buttons pull-right">
                    <p:commandButton id="finalAcceptCancelButtonId" ajax="false" immediate="true" type="reset"
                                     value="#{labels.cancel}"
                                     onclick="PF('early_payment_seller_honor_wgv').hide();return false;"
                                     styleClass="btn btn-secondary">

                    </p:commandButton>
                    <p:commandButton id="finalAcceptButtonId"
                                     widgetVar="wFinalAcceptButton"
                                     value="#{gnxxcblpaymentlbls.early_payment_seller_accept}"
                                     styleClass="btn btn-outline-success"
                                     actionListener="#{cc.attrs.value.onAcceptProposition()}"
                                     immediate="true"
                                     onstart="PF('early_payment_seller_honor_wgv').hide();PF('offering_an_early_payment').hide();PF('wGlobalAccepEarlyPayment').hide()"
                                     process="@this,globalAccepEarlyPaymentTable  @(.psPaymentTable)"
                                     update="resultTable">
                    </p:commandButton>
                </p:outputPanel>

            </p:outputPanel>
        </p:dialog>

        <p:dialog id="early_payment_seller_reject" modal="true" resizable="false"
                  hideEffect="fade" closeOnEscape="true" closable="true"
                  widgetVar="early_payment_seller_reject_wgv"
                  draggable="false"
                  styleClass="psModelDialog responsive-dialog invoice-attachment">
            <p:messages autoUpdate="true" showDetail="false" showSummary="true" closable="true" styleClass="dialogMessage"/>
            <p:outputPanel id="early_payment_seller_reject_id" styleClass="early_payment_seller_honor_style">
                <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-12">
                        <p:outputLabel for="inputTextArearejectid" value="#{gnxxcblpaymentlbls.early_payment_seller_reject_comment}"
                                       styleClass="title4"/>
                    </p:outputPanel>
                    <p:outputPanel styleClass="col-md-12">
                        <p:inputTextarea id="inputTextArearejectid" value="#{cc.attrs.value.rejectComment}" validator="xssValidator"
                                         styleClass="title4 psFaqText form-control form-control-rounded"/>
                    </p:outputPanel>
                </p:outputPanel>
                <p:outputPanel styleClass="ui-dialog-footer text-right responsive-buttons pull-right">
                    <p:commandButton id="finalRejectCancelButtonId" ajax="false"
                                     value="#{labels.cancel}"
                                     immediate="true"
                                     onclick="PF('early_payment_seller_reject_wgv').hide(); return false;"
                                     styleClass="btn btn-secondary">
                    </p:commandButton>
                    <p:commandButton id="finalRejectButtonId"
                                     value="#{gnxxcblpaymentlbls.early_payment_seller_reject}"
                                     action="#{cc.attrs.value.onConfirmRejectPayment()}"
                                     onclick="{PF('early_payment_seller_reject_wgv').hide();PF('offering_an_early_payment').hide()}"
                                     styleClass="btn btn-outline-danger"
                                     update="resultTable">
                    </p:commandButton>
                </p:outputPanel>
            </p:outputPanel>
        </p:dialog>

        <p:dialog modal="true" closable="true" resizable="false"
                  header="#{gnxxcblpaymentlbls.early_payment_seller_popin_title_global}"
                  widgetVar="wGlobalAccepEarlyPayment"
                  draggable="false"
                  styleClass="psModelDialog responsive-dialog global-accept-dialog"
                  closeOnEscape="true">
            <p:messages autoUpdate="true" showDetail="false" showSummary="true" closable="true" styleClass="dialogMessage"/>
            <p:ajax event="close" actionListener="#{cc.attrs.value.onCancel()}" update="@(.psPaymentTable)"/>
            <p:outputPanel id="globalAccepEarlyPaymentId" styleClass="globalAccepEarlyPayment">
                <p:outputPanel styleClass="row">
                    <p:outputLabel for="idRadioSelectShowOption" style="padding-top: 0.7rem;padding-left: 15px;" styleClass="calendarLocal"
                                   value="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount}:"/>
                    <p:selectOneRadio id="idRadioSelectShowOptionGlobal" value="AMOUNT" styleClass="custom-control custom-radio grid-radio grid-radio-datatable">

                        <f:selectItem
                                itemLabel="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_earlyPaymentAmountDiscount}"
                                itemValue="AMOUNT"/>
                        <f:selectItem
                                itemLabel="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount_percentage}"
                                itemValue="DISCOUNT"/>
                        <f:selectItem itemLabel="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_jpa_days}"
                                      itemValue="JPA"/>
                        <p:ajax event="change" update="@(.globalAccepEarlyPaymentDTStyle)"
                                listener="#{cc.attrs.value.onRadioChange}"/>

                    </p:selectOneRadio>
                </p:outputPanel>

                <!--hiddenText to store the id selected when changing a date from the calender-->
                <h:inputHidden id="selectedIndexableId" name="selectedIndexableId" styleClass="globalAccepEarlyPaymentIDRow"
                               value="#{cc.attrs.value.acceptGlobalIndexableId}"/>

                <p:dataTable id="globalAccepEarlyPaymentTable"
                             styleClass="psPaymentTable globalAccepEarlyPaymentDTStyle"
                             widgetVar="wGlobalAccepEarlyPaymentTable"
                             value="#{cc.attrs.value.globalSelection}"
                             var="paymentData"
                             selection="#{cc.attrs.value.globalInnerSelection}"
                             rows="50"
                             paginator="false"
                             scrollable="true"
                             resizableColumns="false"
                             rowKey="#{paymentData.indexable.id}"
                             emptyMessage="#{labels.no_records_found}"
                             rowIndexVar="rowIndex"
                             sortOrder="'ascending'"
                             rowSelectMode="checkbox">
                    <p:ajax event="rowSelectCheckbox" global="true" update="@(.globalAcceptAcceptButtonStyle)"
                            onstart="disableOrEnableCommandButton();"/>
                    <p:ajax event="rowUnselectCheckbox" global="true" update="@(.globalAcceptAcceptButtonStyle)"
                            onstart="disableOrEnableCommandButton();"/>

                    <f:ajax event="rowSelect" global="true" update="@(.globalAcceptAcceptButtonStyle)"
                            onstart="disableOrEnableCommandButton();"/>
                    <f:ajax event="rowUnselect" global="true" update="@(.globalAcceptAcceptButtonStyle)"
                            onstart="disableOrEnableCommandButton();"/>
                    <p:ajax event="toggleSelect"
                            async="true"
                            global="true"
                            ignoreAutoUpdate="true"
                            immediate="true"
                            process="@this"
                            update="@(.globalAcceptAcceptButtonStyle)"
                            onstart="disableOrEnableCommandButton();"/>

                    <!-- columns -->
                    <p:column styleClass="center actions-column multiSelectColumn" selectionMode="multiple" style="width:5%;"/>
                    <p:column headerText="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_invoice_number}" styleClass="center">
                        <h:outputText value="#{paymentData.indexable.invoiceNumber}"/>
                    </p:column>
                    <p:column headerText="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_payment_date}" styleClass="center">
                        <t:multiCustomCalendar maxDate="#{paymentData.paymentDateMaxDate}"
                                               minDate="#{paymentData.paymentDateMinDate}"
                                               value="#{paymentData}"
                                               id="addCalenderMetaData"
                                               onDateSelect="#{cc.attrs.value.onDateSelect}"
                                               process="@(.globalAccepEarlyPaymentDTStyle)"
                                               onStart="setSelectedIndexableId('#{paymentData.indexable.id}')"
                                               styleClass="calenderInDatatable"/>
                    </p:column>
                    <p:column headerText="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_date}" styleClass="center">
                        <p:outputLabel id="idInputTextGlobalAcceptInvoiceIssueDate" styleClass="col-sm-6"
                                       value="#{paymentData.indexable.invoiceDueDate}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" timeZone="#{secSessionHandler.timeZone}"
                                               pattern="#{gnxSessionHandler.userDateFormat}"></f:convertDateTime>
                        </p:outputLabel>
                    </p:column>
                    <p:column styleClass="center">
                        <f:facet name="header">
                           <h:outputText value="#{gnxxcblpaymentlbls.early_payment_dpe}" title="#{gnxxcblpaymentlbls.early_payment_dpe_tooltip}" />
                        </f:facet>
                        <h:outputText value="#{paymentData.jpa} #{gnxxcblpaymentlbls.payment_offering_an_early_payment_early_jpa_unit}"/>
                    </p:column>
                    <p:column headerText="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_total_amount_exct}" styleClass="center">
                        <h:outputText value="#{paymentData.indexable.taxableValue}">
                            <f:converter converterId="gnxBigDecimalConverter"/>
                            <f:attribute name="symbol" value="#{cc.attrs.value.getSignByCode(paymentData.indexable.invoiceCurrencyCoded)}"/>
                        </h:outputText>


                    </p:column>
                    <p:column headerText="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount_percentage}" styleClass="center">
                        <h:outputText value="#{paymentData.escomptePercentage}">
                            <f:converter converterId="gnxBigDecimalConverter"/>
                            <f:attribute name="symbol" value="%"/>
                        </h:outputText>
                    </p:column>
                    <p:column headerText="#{gnxxcblpaymentlbls.payment_offering_an_early_payment_discount}" styleClass="center">
                        <h:outputText value="#{paymentData.escompte}">
                            <f:converter converterId="gnxBigDecimalConverter"/>
                            <f:attribute name="symbol" value="#{cc.attrs.value.getSignByCode(paymentData.indexable.invoiceCurrencyCoded)}"/>
                        </h:outputText>
                    </p:column>


                </p:dataTable>

                <p:outputPanel id="global-accept-buttons-panel"
                               styleClass="ui-dialog-footer text-right responsive-buttons pull-right globalAcceptAcceptButtonStyle">
                    <p:commandButton id="globalAcceptCancelButton"
                                     value="#{labels.cancel}"
                                     onclick="PF('wGlobalAccepEarlyPayment').hide();"
                                     actionListener="#{cc.attrs.value.onCancel()}"
                                     styleClass="btn btn-secondary"
                                     update="@(.psPaymentTable)">
                    </p:commandButton>
                    <p:commandButton id="globalAcceptAcceptButtonId" ajax="false" widgetVar="wvglobalAcceptAcceptButtonId"
                                     rendered="#{cc.attrs.value.isBuyer()}"
                                     disabled="true"
                                     process="globalAccepEarlyPaymentTable"
                                     value="#{gnxxcblpaymentlbls.early_payment_seller_accept}"
                                     onclick="PF('early_payment_seller_honor_wgv').show(); return false;"
                                     styleClass="btn btn-outline-success">
                    </p:commandButton>

                </p:outputPanel>
            </p:outputPanel>
        </p:dialog>
    </cc:implementation>
</ui:component>
