<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:b="http://byzaneo.com/ui">

    <cc:interface name="standardOREdition">
        <cc:attribute name="value" class="com.byzaneo.generix.xcbl.portal.task.StandardOrderResponseEditionTask" required="true"/>
    </cc:interface>

    <cc:implementation>
    
    	<p:fieldset legend="#{labels.configuration}" toggleable="true">
    	    <!-- RTE control configuration -->
            <p:outputPanel styleClass="form-group">
                    <p:outputLabel for="selectRteId"
                                   styleClass="col-sm-2 control-label"
                                   value="#{labels.select_rte_label}" />
                      <p:selectOneMenu id="selectRteId" value="#{cc.attrs.value.rteSource}" style="width: 75%;">
                        <f:selectItem itemLabel="#{labels.select_rte_source}" />
                        <f:selectItems value="#{gnxTransformAppHandler.getTemplates(cc.attrs.value.instance, 'RTE_SOURCE', null, true)}" />
                    </p:selectOneMenu>
            </p:outputPanel>
            <!-- Make working copy of the output file -->
            <p:outputPanel styleClass="form-group">
                <p:outputLabel value="#{labels.outputFile_working_copy}"
                               styleClass="col-sm-2 control-label"/>
                <p:outputPanel styleClass="col-sm-4">
                    <p:selectBooleanCheckbox value="#{cc.attrs.value.makeCopyOfOutputFile}" styleClass="form-control">
                       <p:ajax process="@this" update="ouputFolderId" global="false"/>
                    </p:selectBooleanCheckbox>
                </p:outputPanel>
            </p:outputPanel>
        	<h:panelGroup id="outputFolder">
	        	<p:outputPanel styleClass="form-group row">
	            	<p:outputLabel for="ouputFolderId"
								styleClass="col-sm-2 control-label"
								value="#{gnxxcblcomlbls.output_folder}" />
	                <p:inputText id="ouputFolderId" styleClass="col-sm-9" validator="xssValidator" value="#{cc.attrs.value.outputDir}" placeholder="out/{owner}/ordrsp"
	                    disabled="#{not cc.attrs.value.makeCopyOfOutputFile}"/>
	                <p:outputPanel style="float:right" styleClass="col-sm-1">
						<p:commandLink title="#{gnxxcblcomlbls.output_folder_tooltip}"
			            				styleClass="fa fa-question-circle">
			        	</p:commandLink>
			        </p:outputPanel>
	            </p:outputPanel>
            </h:panelGroup>
            <!-- Post-validation process -->
			<p:outputPanel styleClass="form-group psValidationProcessPnl">
				<p:outputLabel value="#{labels.select_validation_process}"
					styleClass="col-sm-2 control-label" for="validationProcess" />
				<p:selectOneMenu id="validationProcess"
					value="#{cc.attrs.value.validationProcessId}" style="width: 75%;">
					<p:ajax event="change" process="@this"
						update="@(.psValidationProcessPnl)" />
					<f:selectItem itemLabel="#{labels.select}" itemValue="" />
					<f:selectItems
						value="#{gnxIntegrationAppHandler.getBusinessProcesses(cc.attrs.owner)}"
						var="process" itemLabel="#{process.name}"
						itemValue="#{process.id}" />
				</p:selectOneMenu>
			</p:outputPanel>
            <p:outputPanel styleClass="form-group">
            	<p:outputLabel value="#{gnxxcblcomlbls.use_ordering_parties}"
                        styleClass="col-sm-2 control-label"/>
            	<p:outputPanel styleClass="col-sm-4">
            		<p:selectBooleanCheckbox
            			value="#{cc.attrs.value.useOrderingPartyConfig}"
                        styleClass="form-control"/>
                </p:outputPanel>
           </p:outputPanel>
           
           <p:outputPanel styleClass="form-group" >
				<p:outputLabel value="#{gnxxcblordrsplbls.ordrsp_action_type}" styleClass="col-sm-2 control-label" />
				<p:outputPanel styleClass="col-sm-7">
					<p:selectOneRadio id="ordRspActionType" value="#{cc.attrs.value.ordRspActionType}" >
				           <f:selectItem itemLabel="#{gnxxcblordrsplbls.accepted}" itemValue="ACCEPTED" />
				           <f:selectItem itemLabel="#{gnxxcblordrsplbls.accepted_with_amendment}" itemValue="ACCEPTED_WITH_AMENDMENT" />
				           <f:selectItem itemLabel="#{gnxxcblordrsplbls.ALL}" itemValue="ALL" />
					</p:selectOneRadio>
				</p:outputPanel>
			</p:outputPanel>
			
			<p:outputPanel styleClass="form-group">
				<p:outputLabel value="#{gnxxcblordrsplbls.refuse_orders}" styleClass="col-sm-2 control-label" />
				<p:outputPanel styleClass="col-sm-4">
					 <p:selectBooleanCheckbox value="#{cc.attrs.value.deleteOrders}">
					 	<p:ajax process="@this"
							update="@(.headerTable)"
							listener="#{cc.attrs.value.onToggleDeleteOrders()}"
							global="false" />
					</p:selectBooleanCheckbox>
				</p:outputPanel>
			</p:outputPanel>
    	</p:fieldset>
    	
    	<p:spacer height="50px" width="100%"/>
    	<!-- EDITER -->
        <p:fieldset legend="#{gnxxcblinvlbls.edit}" toggleable="true">
            <p:tabView>
                <p:tab title="#{gnxxcblinvlbls.header}">
                    <!-- HEADER PROPERTIES -->
                    <p:dataTable id="headerTbl"
                                 value="#{cc.attrs.value.editableProperties.editableHeader.getRenderedProperties()}"
                                 var="column" emptyMessage="#{labels.no_records_found}"
                                 styleClass="datatable-noheader headerTable">
                        <f:facet name="header" >
                                <p:outputPanel>
                                    <p:outputPanel style="width:50%;display: inline-block;" />
                                    <!-- CHECK ALL EDITABLE PROPERTIES -->
                                    <p:outputPanel style="width:25%;display: inline-block;">
                                        <h:outputText value="#{gnxxcblcomlbls.editable_fields}" />
                                        <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableHeader.globalEditable}" styleClass="form-control globalEditable" style="padding-left: 15px; !important">
                                            <p:ajax update="@(.headerTable)" process="@(.headerTable)" listener="#{cc.attrs.value.editableProperties.editableHeader.onSelectGlobalEditable()}"></p:ajax>
                                        </p:selectBooleanCheckbox>
                                    </p:outputPanel>
                                    <!-- CHECK ALL REQUIRED PROPERTIES -->
                                    <p:outputPanel style="width:25%;display: inline-block;">
                                        <h:outputText value="#{gnxxcblcomlbls.mandatory_fields}" />
                                        <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableHeader.globalRequired}" styleClass="form-control globalEditable" style="padding-left: 25px; !important">
                                            <p:ajax update="@(.headerTable)" process="@(.headerTable)" listener="#{cc.attrs.value.editableProperties.editableHeader.onSelectGlobalRequired()}"></p:ajax>
                                        </p:selectBooleanCheckbox>
                                    </p:outputPanel>
                                </p:outputPanel>
                        </f:facet>
                        <p:column style="width:50%;" styleClass="nowrap">
                            <h:outputText value="#{gnxxcblordrsplbls[column.label]}"/>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                <p:selectBooleanCheckbox value="#{column.editable}" disabled="#{column.required eq 'true' or column.readonly eq 'true'}">
                                    <p:ajax update="@(.headerTable)" process="@(.headerTable)" listener="#{cc.attrs.value.editableProperties.editableHeader.onSelect(column, 'editableColumn')}" />
                                </p:selectBooleanCheckbox>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                <p:selectBooleanCheckbox disabled="#{column.readonly eq 'true'}" rendered="#{cc.attrs.value.editableProperties.editableHeader.isRequiredPropertyRendered(column)}" value="#{column.required}" >
                                    <p:ajax update="@(.headerTable)" process="@(.headerTable)" listener="#{cc.attrs.value.editableProperties.editableHeader.onSelect(column, 'requiredColumn')}" />
                                </p:selectBooleanCheckbox>
                        </p:column>
                    </p:dataTable>
                </p:tab>

                <p:tab title="#{gnxxcblordrsplbls.parties}">
                    <p:tabView>
                        <p:tab title="#{gnxxcblordrsplbls.seller_info}">
                            <!-- SELLER PARTY PROPERTIES -->
                            <p:dataTable id="sellerPartyTbl"
                                         value="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.getRenderedProperties()}"
                                         var="column" emptyMessage="#{labels.no_records_found}"
                                         styleClass="datatable-noheader sellerPartyTable">
                                <f:facet name="header" >
                                    <p:outputPanel>
                                        <p:outputPanel style="width:50%;display: inline-block;" />
                                        <!-- CHECK ALL EDITABLE PROPERTIES -->
                                        <p:outputPanel style="width:25%;display: inline-block;">
                                            <h:outputText value="#{gnxxcblcomlbls.editable_fields}" />
                                            <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.globalEditable}" styleClass="form-control" style="padding-left: 15px; !important">
                                                <p:ajax update="@(.sellerPartyTable)" process="@(.sellerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.onSelectGlobalEditable()}"></p:ajax>
                                            </p:selectBooleanCheckbox>
                                        </p:outputPanel>
                                        <!-- CHECK ALL REQUIRED PROPERTIES -->
                                        <p:outputPanel style="width:25%;display: inline-block;">
                                            <h:outputText value="#{gnxxcblcomlbls.mandatory_fields}" />
                                            <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.globalRequired}" styleClass="form-control" style="padding-left: 25px; !important">
                                                <p:ajax update="@(.sellerPartyTable)" process="@(.sellerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.onSelectGlobalRequired()}"></p:ajax>
                                            </p:selectBooleanCheckbox>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </f:facet>
                                <p:column style="width:50%;" styleClass="nowrap">
                                    <h:outputText value="#{gnxxcblinvlbls[column.label]}"/>
                                </p:column>
                                <p:column styleClass="center" style="width:25%;">
                                        <p:selectBooleanCheckbox value="#{column.editable}" disabled="#{column.required eq 'true'}">
                                            <p:ajax update="@(.sellerPartyTable)" process="@(.sellerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.onSelect(column, 'editableColumn')}" />
                                        </p:selectBooleanCheckbox>
                                </p:column>
                                <p:column styleClass="center" style="width:25%;">
                                        <p:selectBooleanCheckbox rendered="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.isRequiredPropertyRendered(column)}" value="#{column.required}">
                                            <p:ajax update="@(.sellerPartyTable)" process="@(.sellerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableSellerParty.onSelect(column, 'requiredColumn')}" />
                                        </p:selectBooleanCheckbox>
                                </p:column>
                            </p:dataTable>
                            <p:separator/>
                        </p:tab>
                        <p:tab title="#{gnxxcblinvlbls.Buyer}">
                            <!-- BUYER PARTY PROPERTIES -->
                            <p:dataTable id="buyerPartyTbl"
                                         value="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.getRenderedProperties()}"
                                         var="column" emptyMessage="#{labels.no_records_found}"
                                         styleClass="datatable-noheader buyerPartyTable">
                                <f:facet name="header" >
                                    <p:outputPanel>
                                        <p:outputPanel style="width:50%;display: inline-block;" />
                                        <!-- CHECK ALL EDITABLE PROPERTIES -->
                                        <p:outputPanel style="width:25%;display: inline-block;">
                                            <h:outputText value="#{gnxxcblcomlbls.editable_fields}" />
                                            <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.globalEditable}" styleClass="form-control globalEditable" style="padding-left: 15px; !important">
                                                <p:ajax update="@(.buyerPartyTable)" process="@(.buyerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.onSelectGlobalEditable()}"></p:ajax>
                                            </p:selectBooleanCheckbox>
                                        </p:outputPanel>
                                        <!-- CHECK ALL REQUIRED PROPERTIES -->
                                        <p:outputPanel style="width:25%;display: inline-block;">
                                            <h:outputText value="#{gnxxcblcomlbls.mandatory_fields}" />
                                            <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.globalRequired}" styleClass="form-control globalEditable" style="padding-left: 25px; !important">
                                                <p:ajax update="@(.buyerPartyTable)" process="@(.buyerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.onSelectGlobalRequired()}"></p:ajax>
                                            </p:selectBooleanCheckbox>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </f:facet>
                                <p:column style="width:50%;" styleClass="nowrap">
                                    <h:outputText value="#{gnxxcblinvlbls[column.label]}"/>
                                </p:column>
                                <p:column styleClass="center" style="width:25%;">
                                        <p:selectBooleanCheckbox value="#{column.editable}" disabled="#{column.required eq 'true'}">
                                            <p:ajax update="@(.buyerPartyTable)" process="@(.buyerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.onSelect(column, 'editableColumn')}" />
                                        </p:selectBooleanCheckbox>
                                </p:column>
                                <p:column styleClass="center" style="width:25%;">
                                        <p:selectBooleanCheckbox rendered="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.isRequiredPropertyRendered(column)}" value="#{column.required}">
                                            <p:ajax update="@(.buyerPartyTable)" process="@(.buyerPartyTable)" listener="#{cc.attrs.value.editableProperties.editableParties.editableBuyerParty.onSelect(column, 'requiredColumn')}" />
                                        </p:selectBooleanCheckbox>
                                </p:column>
                            </p:dataTable>
                            <p:separator/>
                        </p:tab>
                	</p:tabView>
                </p:tab>
                <p:tab title="#{gnxxcblordrsplbls.header_note}">
                    <p:dataTable id="commentTbl"
                                 value="#{cc.attrs.value.editableProperties.editableComment.getRenderedProperties()}"
                                 var="column" emptyMessage="#{labels.no_records_found}"
                                 styleClass="datatable-noheader commentTable">
                        <f:facet name="header" >
                            <p:outputPanel>
                                <p:outputPanel style="width:50%;display: inline-block;" />
                                <p:outputPanel style="width:25%;display: inline-block;">
                                    <h:outputText value="#{gnxxcblcomlbls.editable_fields}" />
                                </p:outputPanel>
                                <p:outputPanel style="width:25%;display: inline-block;">
                                    <h:outputText value="#{gnxxcblcomlbls.mandatory_fields}" />
                                </p:outputPanel>
                            </p:outputPanel>
                        </f:facet>
                        <p:column style="width:50%;" styleClass="nowrap">
                            <h:outputText value="#{gnxxcblinvlbls[column.label]}"/>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                <p:selectBooleanCheckbox value="#{column.editable}" disabled="#{column.required eq 'true'}">
                                    <p:ajax update="@(.commentTable)" process="@(.commentTable)" listener="#{cc.attrs.value.editableProperties.editableComment.onSelect(column, 'editableColumn')}" />
                                </p:selectBooleanCheckbox>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                        <p:selectBooleanCheckbox value="#{column.required}">
                                            <p:ajax update="@(.commentTable)" process="@(.commentTable)" listener="#{cc.attrs.value.editableProperties.editableComment.onSelect(column, 'requiredColumn')}" />
                                        </p:selectBooleanCheckbox>
                                </p:column>
                    </p:dataTable>
                    <p:separator/>
                </p:tab>            
                <p:tab title="#{gnxxcblordrsplbls.lines}">
                    <p:dataTable id="detailsTbl"
                                 value="#{cc.attrs.value.editableProperties.editableDetails.getRenderedProperties()}"
                                 var="column" emptyMessage="#{labels.no_records_found}"
                                 styleClass="datatable-noheader detailsTable">
                        <f:facet name="header" >
                            <p:outputPanel>
                                <p:outputPanel style="width:50%;display: inline-block;" />
                                <p:outputPanel style="width:25%;display: inline-block;">
                                    <h:outputText value="#{gnxxcblcomlbls.editable_fields}" />
                                    <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableDetails.globalEditable}" styleClass="form-control" style="padding-left: 15px; !important">
                                        <p:ajax update="@(.detailsTable)" process="@(.detailsTable)" listener="#{cc.attrs.value.editableProperties.editableDetails.onSelectGlobalEditable()}"></p:ajax>
                                    </p:selectBooleanCheckbox>
                                </p:outputPanel>
                                <p:outputPanel style="width:25%;display: inline-block;">
                                    <h:outputText value="#{gnxxcblcomlbls.mandatory_fields}" />
                                    <p:selectBooleanCheckbox value="#{cc.attrs.value.editableProperties.editableDetails.globalRequired}" styleClass="form-control" style="padding-left: 25px; !important">
                                        <p:ajax update="@(.detailsTable)" process="@(.detailsTable)" listener="#{cc.attrs.value.editableProperties.editableDetails.onSelectGlobalRequired()}"></p:ajax>
                                    </p:selectBooleanCheckbox>
                                </p:outputPanel>
                            </p:outputPanel>
                        </f:facet>
                        <p:column style="width:50%;" styleClass="nowrap">
                            <h:outputText value="#{gnxxcblordrsplbls[column.label]}"/>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                <p:selectBooleanCheckbox value="#{column.editable}" disabled="#{column.required eq 'true' or column.readonly eq 'true'}">
                                    <p:ajax update="@(.detailsTable)" process="@(.detailsTable)" listener="#{cc.attrs.value.editableProperties.editableDetails.onSelect(column, 'editableColumn')}" />
                                </p:selectBooleanCheckbox>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                <p:selectBooleanCheckbox value="#{column.required}"
                                	rendered="#{cc.attrs.value.editableProperties.editableDetails.isRequiredPropertyRendered(column)}" disabled="#{column.readonly eq 'true'}">
                                    <p:ajax update="@(.detailsTable)" process="@(.detailsTable)" listener="#{cc.attrs.value.editableProperties.editableDetails.onSelect(column, 'requiredColumn')}" />
                                </p:selectBooleanCheckbox>
                        </p:column>
                    </p:dataTable>
                    <p:separator/>
                </p:tab>
                <p:tab title="#{gnxxcblordrsplbls.footer}">
                    <p:dataTable id="footerTbl"
                                 value="#{cc.attrs.value.editableProperties.editableFooter.getRenderedProperties()}"
                                 var="column" emptyMessage="#{labels.no_records_found}"
                                 styleClass="datatable-noheader footerTable">
                        <f:facet name="header" >
                            <p:outputPanel>
                                <p:outputPanel style="width:50%;display: inline-block;" />
                                <p:outputPanel style="width:25%;display: inline-block;">
                                    <h:outputText value="#{gnxxcblcomlbls.editable_fields}" />
                                </p:outputPanel>
                                <p:outputPanel style="width:25%;display: inline-block;">
                                    <h:outputText value="#{gnxxcblcomlbls.mandatory_fields}" />
                                </p:outputPanel>
                            </p:outputPanel>
                        </f:facet>
                        <p:column style="width:50%;" styleClass="nowrap">
                            <h:outputText value="#{gnxxcblordrsplbls[column.label]}"/>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                <p:selectBooleanCheckbox value="#{column.editable}" disabled="#{column.required eq 'true' or column.readonly eq 'true'}">
                                    <p:ajax update="@(.footerTable)" process="@(.footerTable)" listener="#{cc.attrs.value.editableProperties.editableFooter.onSelect(column, 'editableColumn')}" />
                                </p:selectBooleanCheckbox>
                        </p:column>
                        <p:column styleClass="center" style="width:25%;">
                                        <p:selectBooleanCheckbox value="#{column.required}" disabled="#{column.readonly eq 'true'}">
                                            <p:ajax update="@(.footerTable)" process="@(.footerTable)" listener="#{cc.attrs.value.editableProperties.editableFooter.onSelect(column, 'requiredColumn')}" />
                                        </p:selectBooleanCheckbox>
                                </p:column>
                    </p:dataTable>
                    <p:separator/>
                </p:tab>
            </p:tabView>
        </p:fieldset>
    </cc:implementation>
</ui:component>