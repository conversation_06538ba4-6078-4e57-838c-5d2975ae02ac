<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
              xmlns:xcbl="http://xmlns.jcp.org/jsf/composite/components/gnx/xcbl"
              xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx/front">
    <cc:interface name="aipView">
        <cc:attribute name="value" type="com.byzaneo.generix.xcbl.portal.task.UploadPdfTask" required="true" />
    </cc:interface>

    <cc:implementation>
        <script type="text/javascript">
            function simulateClickChoose(className, attr) {
                PF('wFileUpload').jq.find('input:file').attr('accept', '.pdf');
                if (attr)
                    $(document.getElementsByClassName(className)).find(attr)[0]
                        .click();
            }

            function simulateClickImport(className, fileupload) {
                if (fileupload.files.length != 0) {
                	document.getElementsByClassName(className)[0].click();
                }
                else {
                    PF('wWizard').next();
                }
            }

            function handleMultiFileUploadRequest(fileupload) {
                if (fileupload.files.length === 0) {
                    PF('wWizard').next();
                }
            }
            
        </script>
        <gnx:errorMessage msg="#{gnxspdfadinvpdflbls.error_upload_pdf}"
            rendered="#{cc.attrs.value.isPartnerUserConnected() and !gnxSessionHandler.isGranted('Portlet_UploadPdf', 'CREATE')}"/>
        <gnx:errorMessage msg="#{gnxspdfadinvpdflbls.error_associate_template}"
            rendered="#{!cc.attrs.value.isPartnerUserConnected() and !gnxSessionHandler.isGranted('Portlet_UploadPdf', 'ADMIN')}"/>
        <p:panelGrid styleClass="uploadpdf flatStyle lightboxPnlGrd portletContentMargin">
            <!-- PARTNER -->
            <p:row rendered="#{cc.attrs.value.isPartnerUserConnected() and gnxSessionHandler.isGranted('Portlet_UploadPdf', 'CREATE')}" styleClass="product-card product-list margin-bottom-none">
                <p:column styleClass="step-info-description col-md-4 bg-company alignment-product-thumb product-thumb text-center">
                    <p:outputPanel id="taskDescriptionPnl" autoUpdate="true"
                                   styleClass="step-block">
                        <!-- CIRCLE NR - GENERIX TEMPLATE -->
                        <p:outputPanel styleClass="circle circle-number displayOnGenerixTemplate">
                            <h:outputText
                                    value="#{gnxspdfadinvpdflbls['number_'.concat(cc.attrs.value.tabId)]}"
                                    styleClass="circle-content-number"/>
                        </p:outputPanel>
                        
                        <!-- SPACE - GENERIX TEMPLATE -->
                        <p:spacer width="100%" height="10" styleClass="displayOnGenerixTemplate" />
                        
                        <!-- TITLE - GENERIX and GENERIXCUSTOMER TEMPLATE -->
                        <p:outputPanel>
                            <h:outputText value="#{gnxHandler.label(cc.attrs.value['title_'.concat(cc.attrs.value.tabId)], gnxSessionHandler.locale)}"
                             			  escape="false" styleClass="bold-label title-label"/>
                        </p:outputPanel>
                        
                        <!-- SPACE - GENERIX TEMPLATE -->
                        <p:spacer width="100%" height="10" styleClass="displayOnGenerixTemplate" />
                        
                        <!-- SPACE - GENERIXCUSTOMER TEMPLATE -->
                        <br class="displayOnGenerixCustomerTemplate spaceHeight" />
                        
                        <!-- DESCRIPTION - GENERIX and GENERIXCUSTOMER TEMPLATE-->
                        <p:outputPanel>
                            <h:outputText value="#{gnxHandler.label(cc.attrs.value['description_'.concat(cc.attrs.value.tabId)], gnxSessionHandler.locale)}"
                                    escape="false" styleClass="bold-label description-label"/>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:column>
                <p:column styleClass="step-content product-info">
                    <p:wizard widgetVar="wWizard" styleClass="wizardSteps" 
                              flowListener="#{cc.attrs.value.onFlowProcess}" showNavBar="false">
                        <!-- STEP 1 -->
                        <p:tab id="first_step">
                            <!-- STEP 1 TITLE -->
                            <f:facet name="title">
                                <h:outputText value="1" styleClass="displayOnGenerixTemplate" />
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep == 'first_step'}"
                                    styleClass="step-one-active text-capitalize btn btn-info btn-arrow-right btn-arrow-right-start rounded-0 justify-content-center displayOnGenerixCustomerTemplate">
                                    <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_1}" />
                                </p:outputPanel>
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep != 'first_step'}"
                                    styleClass="step-one-inactive text-capitalize btn btn-info btn-arrow-right btn-arrow-right-start rounded-0 justify-content-center displayOnGenerixCustomerTemplate">
                                    <h:outputText styleClass="step-indicator icon-circle-check" />
                                    <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_1}" />
                                </p:outputPanel>
                            </f:facet>
                            <!-- STEP 1 CONTENT -->
                            <p:outputPanel>
                                <!-- CHOOSE TEMPLATES -->
                                <p:outputPanel styleClass="uploadPdfChooseTemplate">
                                    <p:outputPanel styleClass="row justify-content-center" style="width: 99% !important;">
                                    <p:outputPanel styleClass="list-item-custom">
                                        <p:dataList value="#{cc.attrs.value.templates}" var="template"
                                                    type="ordered" emptyMessage="#{labels.no_records_found}">
                                            <p:outputPanel
                                                    styleClass="select-item-custom #{(cc.attrs.value.selectedTemplate == template) ? 'select-item-custom-hover' : ''}"
                                                    pt:title="#{template.name}">
                                                <p:commandLink id="ajaxPtn"
                                                               actionListener="#{cc.attrs.value.setSelectedTemplate(template)}"
                                                               update="@(.list-item-custom)">
                                                    <p:outputPanel styleClass="icon-item-custom"/>
                                                    <p:outputPanel styleClass="templateLabelWrapper ellipsis text-center">
                                                            <!-- GENERIXCUSTOMER -->
                                                            <p:outputPanel styleClass="displayOnGenerixCustomerTemplate templateModelType">
                                                                <h:outputText styleClass="text-muted"
                                                                    value="#{gnxspdfadinvpdflbls.template_model}" />
                                                            </p:outputPanel>
                                                            <p:outputPanel styleClass="templateName">
                                                                <h:outputText
                                                                    value="#{template.name}" />
                                                            </p:outputPanel>
                                                    </p:outputPanel>
                                                </p:commandLink>
                                            </p:outputPanel>
                                        </p:dataList>
                                    </p:outputPanel>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <!-- BUTTONS -->
                                <p:outputPanel styleClass="row pull-right step-buttons-panel">
                                    <!-- GENERIX TEMPLATE -->
                                    <p:commandButton type="button" onclick="PF('wWizard').next();"
                                                     value="#{gnxspdfadinvpdflbls.next}"
                                                     styleClass="displayOnGenerixTemplate reset-btn btn-orange float-right"/>
                                    <p:button value="#{gnxspdfadinvpdflbls.cancel}"
                                              onclick="cancel()" target="_parent" outcome="/ui/portal"
                                              styleClass="displayOnGenerixTemplate reset-btn btn-grey float-right">
                                        <f:param name="page"
                                                 value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}"/>
                                    </p:button>
                                    
                                    <!-- GENERIXCUSTOMER TEMPLATE -->
                                    <p:button value="#{gnxspdfadinvpdflbls.cancel}"
                                              onclick="cancel()" target="_parent" outcome="/ui/portal"
                                              styleClass="displayOnGenerixCustomerTemplate btn btn-secondary">
                                        <f:param name="page"
                                                 value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}"/>
                                    </p:button>
                                    <p:commandButton value="#{gnxspdfadinvpdflbls.next}"  ignoreAutoUpdate="true"
                                                     onclick="PF('wWizard').next();" update="@(.wizardSteps)"
                                                     styleClass="displayOnGenerixCustomerTemplate btn btn-primary">
                                     </p:commandButton>
                                    <p:remoteCommand name="cancel" action="#{cc.attrs.value.onCancel()}"/>
                                </p:outputPanel>
                            </p:outputPanel>
                        </p:tab>
                        
                        <!-- STEP 2 -->
                        <p:tab id="second_step">
                            <!-- STEP 2 TITLE -->
                            <f:facet name="title">
                                <h:outputText value="2" styleClass="displayOnGenerixTemplate" />
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep == 'second_step'}"
                                    styleClass="step-two-active btn btn-info btn-arrow-right btn-arrow-right-intermediate rounded-0 justify-content-center text-capitalize displayOnGenerixCustomerTemplate">
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_2}" />
                                </p:outputPanel>
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep != 'second_step' and cc.attrs.value.stepTwoCompleted}"
                                    styleClass="step-two-inactive btn btn-info btn-arrow-right btn-arrow-right-intermediate rounded-0 justify-content-center text-capitalize displayOnGenerixCustomerTemplate">
                                        <h:outputText styleClass="step-indicator icon-circle-check" />
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_2}" />                                    
                                </p:outputPanel>
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep != 'second_step' and !cc.attrs.value.stepTwoCompleted}"
                                    styleClass="step-two-inactive btn btn-info btn-arrow-right btn-arrow-right-intermediate rounded-0 justify-content-center text-capitalize displayOnGenerixCustomerTemplate">
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_2}" />                                    
                                </p:outputPanel>
                            </f:facet>
                            <!-- STEP 2 CONTENT -->
							<p:outputPanel styleClass="uploadPdfFileUploadPanelWrapper position-relative">
                                <!-- UPLOAD FILES -->
								<p:panel styleClass="uploadPdfFileUploadPanel">
                                    <p:outputPanel>
    									<p:fileUpload  
                                            fileUploadListener="#{cc.attrs.value.onFileUpload}" 
                                            widgetVar="wFileUpload" 
                                            styleClass="container-fluid drag-drop-custom"
                                            mode="advanced"
                                            multiple="true"
                                            dragDropSupport="true" 
                                            allowTypes="/(\.|\/)(pdf)$/" invalidFileMessage="#{gnxspdfadinvpdflbls.invalid_file_pdf}"
                                            required="true" requiredMessage="#{gnxspdfadinvpdflbls.required_files_upload}"
                                            label="#{gnxspdfadinvpdflbls.choose}" uploadLabel="#{gnxspdfadinvpdflbls.upload}" cancelLabel="#{gnxspdfadinvpdflbls.cancel}"
                                            oncomplete="handleMultiFileUploadRequest(PF('wFileUpload'));" 
                                            sizeLimit="#{cc.attrs.value.maxDocumentSizeInByte}" />
    									<p:panel styleClass="drag-drop-panel">
    										<h:outputText value="#{gnxspdfadinvpdflbls.drag_drop}" styleClass="displayOnGenerixTemplate" />
                                            <p:outputPanel styleClass="displayOnGenerixCustomerTemplate" style="height:inherit; width: 185px !important">
                                                <p:outputPanel styleClass="iconFileAddPanel text-center">
                                                    <h:outputText styleClass="icon-file-add" />
                                                </p:outputPanel>
                                                <p:outputPanel>
                                                    <h:outputText value="#{gnxspdfadinvpdflbls.drag_drop}" />
                                                </p:outputPanel>
                                            </p:outputPanel>
    									</p:panel>
                                    </p:outputPanel>
								</p:panel>
								<!-- BUTTONS - GENERIX TEMPLATE -->
                                <p:outputPanel styleClass="lightBoxPnlGrdFooter displayOnGenerixTemplate">
                                    <p:commandButton value="#{gnxspdfadinvpdflbls.next}" onclick="simulateClickImport('ui-fileupload-upload', PF('wFileUpload'));" styleClass="displayOnGenerixTemplate btn-gnx btn-color-company float-right" ignoreAutoUpdate="true" style="padding: 5px 20px;" />
                                    <p:commandButton value="#{gnxspdfadinvpdflbls.back}" onclick="PF('wWizard').back();" styleClass="displayOnGenerixTemplate btn-gnx btn-gnx-default float-right" style="padding: 5px 20px;" />
									<p:button value="#{gnxspdfadinvpdflbls.cancel}" onclick="cancel()" target="_parent" outcome="/ui/portal" styleClass="displayOnGenerixTemplate btn-gnx btn-gnx-default float-right" style="padding: 5px 20px;">
                                        <f:param name="page" value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}" />
                                    </p:button>
                                </p:outputPanel>
                                    
                                <!-- BUTTONS - GENERIXCUSTOMER TEMPLATE -->
                                <p:outputPanel styleClass="row pull-right step-buttons-panel displayOnGenerixCustomerTemplate">
                                    <p:commandButton
                                        type="button"
                                        value="#{gnxspdfadinvpdflbls.select_files}"
                                        onclick="PF('wFileUpload').jq.find('input:file').click();"
                                        styleClass="displayOnGenerixCustomerTemplate btn btn-outline-info float-right" />
                                    <p:button
                                        value="#{gnxspdfadinvpdflbls.cancel}"
                                        onclick="cancel()"
                                        target="_parent"
                                        outcome="/ui/portal"
                                        styleClass="displayOnGenerixCustomerTemplate btn btn-secondary">
                                        <f:param
                                            name="page"
                                            value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}" />
                                    </p:button>
                                    <p:commandButton
                                        value="#{gnxspdfadinvpdflbls.back}"
                                        onclick="PF('wWizard').back();"
                                        oncomplete="rc()"
                                        styleClass="displayOnGenerixCustomerTemplate btn btn-outline-info float-right" />
                                    <p:commandButton ignoreAutoUpdate="true" onsuccess="rc()"
                                        value="#{gnxspdfadinvpdflbls.next}" 
                                        onclick="simulateClickImport('ui-fileupload-upload', PF('wFileUpload')); PF('wWizard').next()"
                                        styleClass="displayOnGenerixCustomerTemplate btn btn-primary"/>
								</p:outputPanel>
                                <p:remoteCommand name="rc" global="false" ignoreAutoUpdate="true" process="@this" update="@(.wizardSteps)" />
                                <p:remoteCommand name="cancel" ignoreAutoUpdate="true" actionListener="#{cc.attrs.value.onCancel()}" />
						  </p:outputPanel>
						</p:tab>

                        <!-- STEP 3 -->
                        <p:tab id="third_step">
                            <!-- STEP 3 TITLE -->
                            <f:facet name="title">
                                <h:outputText value="3" styleClass="displayOnGenerixTemplate" />
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep == 'third_step'}"
                                    styleClass="step-three-active btn btn-info btn-arrow-right btn-arrow-right-intermediate rounded-0 justify-content-center displayOnGenerixCustomerTemplate text-capitalize">
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_3}" />
                                    </p:outputPanel>
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep != 'third_step' and cc.attrs.value.stepThreeCompleted}"
                                    styleClass="step-three-inactive btn btn-info btn-arrow-right btn-arrow-right-intermediate rounded-0 justify-content-center displayOnGenerixCustomerTemplate text-capitalize">
                                        <h:outputText styleClass="step-indicator icon-circle-check" />
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_3}" />
                                </p:outputPanel>
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep != 'third_step' and !cc.attrs.value.stepThreeCompleted}"
                                    styleClass="step-three-inactive btn btn-info btn-arrow-right btn-arrow-right-intermediate rounded-0 justify-content-center displayOnGenerixCustomerTemplate text-capitalize">
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_3}" />
                                </p:outputPanel>
                            </f:facet>
                            <!-- STEP 3 CONTENT -->
							<p:outputPanel styleClass="uploadPdfDownloadPanelWrapper">
                                <!-- DOWNLOADED FILES -->
								<p:outputPanel styleClass="uploadPdfDownloadPanel">
									<p:outputPanel styleClass="list-itemInvoice-custom container-fluid">
										<p:dataList  value="#{cc.attrs.value.uploadedFiles}" var="uploadedFile" type="ordered" emptyMessage="#{labels.no_records_found}">
                                                <p:outputPanel styleClass="ellipsis-list">
                                                    <h:outputText value="#{cc.attrs.value.getNormalizedFileName(uploadedFile.fileName)}" styleClass="uploadedFileName" />
                                                    <h:outputText value=" (#{cc.attrs.value.displayReadableSize(uploadedFile.size)})" style="overflow: hidden;color:#999;" />
    											</p:outputPanel>
    											<p:outputPanel styleClass="text-success font-weight-normal float-right d-flex btn-ok-process-list download-text">
                                                    <h:outputText value="#{gnxspdfadinvpdflbls.download}" />
                                                    <p:outputPanel styleClass="separator">
                                                        <p:outputPanel styleClass="separator-content" style="border-color:#dddddd; height:25px;" />
                                                    </p:outputPanel>
                                                    <p:commandLink icon="ui-icon-close" onsuccess="rc()"
                                                                   actionListener="#{cc.attrs.value.onDeleteFile(uploadedFile)}"
                                                                   process="@this" update="@(.list-itemInvoice-custom)"
                                                                   style="padding: 0 5px; margin: auto;">
                                                        <h:outputText styleClass="ui-icon ui-icon-close" />
                                                    </p:commandLink>
    											</p:outputPanel>
										</p:dataList>
									</p:outputPanel>
								</p:outputPanel>
                                 <!-- BUTTONS - GENERIX TEMPLATE -->
								 <p:outputPanel styleClass="lightBoxPnlGrdFooter displayOnGenerixTemplate" style="height:50px;">
                                    <p:commandButton type="button" onclick="PF('wWizard').next()" value="#{labels.send}" styleClass="displayOnGenerixTemplate btn-gnx btn-color-company float-right" style="padding: 5px 20px;" />
									<p:commandButton value="#{gnxspdfadinvpdflbls.back}" onclick="PF('wWizard').back()" styleClass="displayOnGenerixTemplate btn-gnx btn-gnx-default float-right" style="padding: 5px 20px;" />
									<p:button value="#{gnxspdfadinvpdflbls.cancel}" onclick="cancel()" target="_parent" outcome="/ui/portal" styleClass="displayOnGenerixTemplate btn-gnx btn-gnx-default float-right" style="padding: 5px 20px;">
										<f:param name="page" value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}" />
									</p:button>
                                 </p:outputPanel>
								 <!-- BUTTONS - GENERIXCUSTOMER TEMPLATE -->
                                 <p:outputPanel styleClass="row pull-right step-buttons-panel lightBoxPnlGrdFooter displayOnGenerixCustomerTemplate">
                                    <p:button value="#{gnxspdfadinvpdflbls.cancel}" onclick="cancel()" target="_parent" outcome="/ui/portal" styleClass="displayOnGenerixCustomerTemplate btn btn-secondary" >
                                        <f:param name="page" value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}" />
                                    </p:button>
                                    <p:commandButton value="#{gnxspdfadinvpdflbls.back}" onclick="PF('wWizard').back();" onsuccess="rc()" styleClass="displayOnGenerixCustomerTemplate btn btn-outline-info float-right" />
                                    <p:commandButton update="@(.wizardSteps)" onclick="PF('wWizard').next();" ignoreAutoUpdate="true" value="#{labels.send}" styleClass="displayOnGenerixCustomerTemplate btn btn-primary" />
								</p:outputPanel>
                                    <p:remoteCommand name="cancel" ignoreAutoUpdate="true" action="#{cc.attrs.value.onCancel()}" />
							</p:outputPanel>
						</p:tab>

                        <!-- STEP 4 -->
                        <p:tab id="four_step">
                            <!-- STEP 4 TITLE -->
                            <f:facet name="title">
                                <h:outputText value="4" styleClass="displayOnGenerixTemplate" />
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep == 'four_step'}"
                                    styleClass="step-four-active btn btn-info btn-arrow-right btn-arrow-right-final rounded-0 justify-content-center displayOnGenerixCustomerTemplate text-capitalize">
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_4}" />
                                </p:outputPanel>
                                <p:outputPanel
                                    rendered="#{cc.attrs.value.currentStep != 'four_step'}"
                                    styleClass="step-four-inactive btn btn-info btn-arrow-right btn-arrow-right-final rounded-0 justify-content-center displayOnGenerixCustomerTemplate text-capitalize">
                                        <h:outputText value="#{gnxspdfadinvpdflbls.wizard_step_title_4}" />
                                </p:outputPanel>
                            </f:facet>
                            <!-- STEP 4 CONTENT -->
                            <p:outputPanel styleClass="uploadPdfSendPanelWrapper">
                                <!-- SEND -->
                                <p:outputPanel styleClass="row uploadPdfSendPanel">
                                    <p:outputPanel styleClass="list-itemInvoice-custom container-fluid">
                                        <p:dataList value="#{cc.attrs.value.executions}" styleClass="execution-list" itemStyleClass="#{execution.state}"
                                                    var="execution" type="ordered"
                                                    emptyMessage="#{labels.no_records_found}">
                                            <p:panelGrid styleClass="psDeployState w-100">
                                                <p:row>
                                                    <p:column styleClass="filename-col">
                                                        <p:outputPanel styleClass="ellipsis-list">
                                                            <h:outputText value="#{execution.filename}"
                                                                          styleClass="executionFileName"/>
                                                        </p:outputPanel>
                                                    </p:column>
                                                    <p:column styleClass="progress-col">
                                                        <h:panelGroup styleClass="fa fa-refresh fa-spin"
                                                                      rendered="#{execution.state == 'IN_PROGRESS'}"/>
                                                    </p:column>
                                                    <p:column styleClass="state-col">
                                                        <!-- GENERIX TEMPLATE -->
                                                        <h:outputText
                                                                value="#{gnxspdfadinvpdflbls[execution.state.concat('_invoice')]}"
                                                                styleClass="displayOnGenerixTemplate btn-#{execution.state}-process-list"
                                                                rendered="#{execution.state == 'ok' or execution.state == 'ko'}"/>
                                                        <!-- GENERIXCUSTOMER TEMPLATE -->
                                                        <h:outputText value=""
                                                                styleClass="displayOnGenerixCustomerTemplate process-list btn-#{execution.state}-process-list"
                                                                rendered="#{execution.state == 'ok' or execution.state == 'ko'}"/>
                                                    </p:column>
                                                </p:row>
                                            </p:panelGrid>
                                        </p:dataList>
                                    </p:outputPanel>
                                </p:outputPanel>
                                    <!-- BUTTONS - GENERIX TEMPLATE -->
                                    <p:outputPanel styleClass="displayOnGenerixTemplate" style="position:absolute;bottom:5px;left:5px; width:100%">
                                        <p:button value="#{gnxspdfadinvpdflbls.history_invoice}"
                                                  target="_parent" outcome="/ui/portal"
                                                  styleClass="reset-btn btn-grey"
                                                  rendered="#{cc.attrs.value.historySequenceId != null}"
                                                  style="float: left;margin: 0 18px !important;padding: 5px 10px;">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, cc.attrs.value.historySequenceId)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.ko_invoices}"
                                                  target="_parent" outcome="/ui/portal"
                                                  styleClass="reset-btn btn-ko-process"
                                                  rendered="#{cc.attrs.value.errorSequenceId != null}"
                                                  style="float: left;margin: 0 10px !important;">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, cc.attrs.value.errorSequenceId)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.ok_invoices}"
                                                  target="_parent" outcome="/ui/portal"
                                                  styleClass="reset-btn btn-ok-process"
                                                  rendered="#{cc.attrs.value.historySequenceId != null}"
                                                  style="float: left;margin: 0 10px !important;">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, cc.attrs.value.historySequenceId)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.cancel}"
                                                  onclick="cancel()" target="_parent" outcome="/ui/portal"
                                                  styleClass="reset-btn btn-grey"
                                                  style="float: left;margin: 0 10px !important;">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.end}" target="_parent"
                                                  outcome="/ui/portal"
                                                  styleClass="reset-btn btn-orange float-right"
                                                  style="padding: 5px 20px;right: 10px;">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}"/>
                                        </p:button>
                                    </p:outputPanel>
                                    
                                    <!-- BUTTONS - GENERIXCUSTOMER TEMPLATE -->
                                    <p:outputPanel styleClass="row pull-right step-buttons-panel displayOnGenerixCustomerTemplate">
                                        <p:button value="#{gnxspdfadinvpdflbls.history_invoice}"
                                                  target="_parent" outcome="/ui/portal"
                                                  styleClass="btn btn-outline-info float-right"
                                                  rendered="#{cc.attrs.value.historySequenceId != null}">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, cc.attrs.value.historySequenceId)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.ko_invoices}"
                                                  target="_parent" outcome="/ui/portal"
                                                  styleClass="btn btn-outline-danger float-right"
                                                  rendered="#{cc.attrs.value.errorSequenceId != null}">
                                           <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, cc.attrs.value.errorSequenceId)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.ok_invoices}"
                                                  target="_parent" outcome="/ui/portal"
                                                  styleClass="btn btn-outline-success float-right"
                                                  rendered="#{cc.attrs.value.historySequenceId != null}">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, cc.attrs.value.historySequenceId)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.cancel}"
                                                  onclick="cancel()" target="_parent" outcome="/ui/portal"
                                                  styleClass="reset-btn btn btn-secondary float-right">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}"/>
                                        </p:button>
                                        <p:button value="#{gnxspdfadinvpdflbls.end}" target="_parent"
                                                  outcome="/ui/portal"
                                                  styleClass="btn btn-primary float-right">
                                            <f:param name="page"
                                                     value="#{cc.attrs.value.goToNextPortlet(cc.attrs.owner, null)}"/>
                                        </p:button>
                                    </p:outputPanel>
                                    
                                    <p:remoteCommand name="cancel" ignoreAutoUpdate="true"
                                                     action="#{cc.attrs.value.onCancel()}"/>
                                <p:poll interval="3" immediate="true" process="@this"
                                        update="@(.execution-list)" global="false" delay="10"
                                        ignoreAutoUpdate="true"/>
                            </p:outputPanel>
                        </p:tab>
                    </p:wizard>
                </p:column>
            </p:row>
            <!-- CUSTOMER -->
            <p:row rendered="#{!cc.attrs.value.isPartnerUserConnected() and gnxSessionHandler.isGranted('Portlet_UploadPdf', 'ADMIN')}" styleClass="product-card product-list margin-bottom-none lightboxPnlGrd ">
                <!-- GENERIX TEMPLATE -->
                <p:column styleClass="col-md-4 bg-company step-block displayOnGenerixTemplate" style="text-align: center;vertical-align: middle; margin:0; padding:0 15px !important;position: relative;width: 30%">
                    <p:outputPanel styleClass="circle circle-number icon-user"/>
                    <h:outputText value="#{gnxspdfadinvpdflbls.title_company}" escape="false" styleClass="bold-label" style="display: block;"/>
                    <h:outputText value="#{gnxspdfadinvpdflbls.description_company.concat(' ').concat(secSessionHandler.primaryGroup.fullname)}"
                                escape="false" styleClass="bold-label" style="font-size: 2.5rem;font-weight: normal;"/>
                </p:column>
                
                <!-- GENERIXCUSTOMER TEMPLATE -->
                <p:column styleClass="step-info-description col-md-4 bg-company alignment-product-thumb product-thumb text-center displayOnGenerixCustomerTemplate" >
                    <p:outputPanel >
                        <p:outputPanel styleClass="circle circle-number icon-user displayOnGenerixTemplate"/>
                        <p:outputPanel>
                            <h:outputText value="#{gnxspdfadinvpdflbls.title_company}" escape="false" styleClass="bold-label title-label" style="display: block;"/>
                        </p:outputPanel>
                        <p:outputPanel>
                            <h:outputText value="#{gnxspdfadinvpdflbls.description_company.concat(' ').concat(secSessionHandler.primaryGroup.fullname)}"
                                        escape="false" styleClass="displayOnGenerixTemplate bold-label description-label" style="font-size: 2.5rem;font-weight: normal;"/>
                            <h:outputText value="#{gnxspdfadinvpdflbls.description_company.concat(' ').concat(secSessionHandler.primaryGroup.fullname)}"
                                        escape="false" styleClass="bold-label description-label" style="font-weight: normal;"/>
                        </p:outputPanel>
                    </p:outputPanel>                
                </p:column>
                
                <!-- GENERIX and GENERIXCUSTOMER TEMPLATES -->
                <p:column style="vertical-align: top; margin:0; padding:0;" styleClass="product-info">
                    <xcbl:pdfTemplates value="#{cc.attrs.value.templateSelectionHandler}" />
                    <p:growl id="messagesGnxTemplate" showDetail="true"/>
                </p:column>
            </p:row>
            
        </p:panelGrid>
    </cc:implementation>
</ui:component>
