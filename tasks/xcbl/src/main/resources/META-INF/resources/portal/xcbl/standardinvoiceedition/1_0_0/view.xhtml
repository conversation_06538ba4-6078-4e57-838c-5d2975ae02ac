<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:pe="http://primefaces.org/ui/extensions"
              xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx/front">
              
    <cc:interface name="standardInvoiceEditionView">
        <cc:attribute name="value" class="com.byzaneo.generix.xcbl.portal.task.StandardInvoiceEditionTask" required="true"/>
    </cc:interface>
    
	<cc:implementation>
	<p:panel id="idInvoiceEdit" closable="true" visible="true"
		widgetVar="wInvoiceEditPnl" closeSpeed="0"
		style="margin-bottom:10px; border: 0 solid black;"
		rendered="#{cc.attrs.value.invoice != null and (cc.attrs.value.isPartnerUserConnected() or cc.attrs.value.editInvoice)}"
		styleClass="container-fluid psInvoiceEdit">
		<ui:param name="invoice" value="#{cc.attrs.value.invoice}" />

		<style type="text/css">
.position-groupe {
	width: 90%;
	margin: 0 auto;
}

.ui-corner-all a[id*="toggler"] {
	position: absolute;
	right: 50px;
	top: 15px;
}
</style>

		<script type="text/javascript">
            function checkValidation(){
            	var validationFailedFields = $('.ui-state-error');
            	for (var i = 0; i &lt; validationFailedFields.length; i++){
            		// for the label that has the state in error find the parent tab id so as to compare with the list of the tab view headers
            		var id = validationFailedFields[i].closest('.ui-tabs-panel').id;
            		var tabs = $('.stdInvoiceTabHeader');
            		for (var j = 0; j &lt; tabs.length; j++){
            			if(tabs[j].parentElement.href.indexOf(id) > -1){
            				tabs[j].setAttribute("style", "color:red");
            				break;
            			}
            		}
            		var partiesTabs = $('.stdInvoicePartiesTabHeader');
            		var partiesError = 0;
            		for (var j = 0; j &lt; partiesTabs.length; j++){
            			if(partiesTabs[j].firstChild.href.indexOf(id) > -1){
            				partiesTabs[j].firstChild.setAttribute("style", "color:red");
            				partiesError++;
            				break;
            			}
            		}
            		if(partiesError > 0){
            			var partyTab = $('.stdInvoicePartyTabHeader')[0];
            			partyTab.setAttribute("style", "color:red");
            		}
            	}
            	removeAllMessages();
            }
            
            // checks if the parties tabs(bill to, buyer, etc) or the other tabs are contained in the args values
            function colorTabsBasedOnJavaErrors(args){
            	if(args){
            		var partiesTabs = $('.stdInvoicePartiesTabHeader');
            		var partiesError = 0;
            		var argsValues = Object.values(args);
            		for (var j = 0; j &lt; partiesTabs.length; j++){
            			var partyTab = partiesTabs[j].firstChild.innerHTML;
            			if(argsValues.indexOf(partyTab) > -1) {
            				partiesTabs[j].firstChild.setAttribute("style", "color:red");
            				partiesError++;
            			}
            		}
            		if(partiesError > 0){
            			var partyTab = $('.stdInvoicePartyTabHeader')[0];
            			partyTab.setAttribute("style", "color:red");
            		}
            		var linesTab = $('.stdInvoiceTabHeader');
            		for (var j = 0; j &lt; linesTab.length; j++){
            			var linesTabValue = linesTab[j].innerHTML;
            			if(argsValues.indexOf(linesTabValue) > -1)
            				linesTab[j].setAttribute("style", "color:red");
            		}
            	}
            }
        </script>
            
		#{cc.attrs.value.setLanguage(gnxSessionHandler.locale.toString())}
		<!-- PANEL EDIT INVOICE -->
		<p:outputPanel styleClass="panel panel-default" rendered="#{not gnxSessionHandler.unishopLikeCSSExists()}">
			<!-- PANEL HEADER -->
			<p:outputPanel styleClass="panel-heading" />

			<!-- PANEL BODY -->
			<p:outputPanel styleClass="panel-body">
				<p:outputPanel styleClass="container-fluid">
					<p:outputPanel styleClass="row">
						<!-- INVOICE INFO -->
						<p:outputPanel styleClass="col-md-6">
							<p:panel id="invInfoToggleable" toggleable="true"
								toggleSpeed="500" styleClass="border-gnx"
								style="margin-bottom:10px">
								<f:facet name="header">
									<h:outputText id="invNumberHeader"
										value="#{gnxxcblinvlbls.header_info} #{invoice.invoiceHeader.invoiceNumber.value}" />
								</f:facet>
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="invoiceNumber"
										styleClass="col-sm-4 control-label" style="padding-left:0;"
										value="#{gnxxcblinvlbls.invoice_number}" />
									<p:outputPanel styleClass="col-sm-4">
										<p:inputText id="invoiceNumber"
											value="#{invoice.invoiceHeader.invoiceNumber.value}"
											required="true"
											requiredMessage="#{gnxxcblinvlbls.invoice_number} : #{gnxxcblcomlbls.value_required}"
											validatorMessage="#{gnxxcblinvlbls.validate_message}"
											disabled="#{cc.attrs.value.editInvoiceInError}"
											style="width: 100% !important;">
											<p:ajax event="keyup" update="invNumberHeader" global="false" />
											<f:validateRegex pattern="^[\w\-]+$" />
										</p:inputText>
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel id="invoiceTypeSelect">
									<p:outputPanel styleClass="form-group">
										<p:outputLabel for="invoiceType"
											styleClass="col-sm-4 control-label"
											value="#{gnxxcblinvlbls.inv_type}" />
										<p:outputPanel styleClass="col-sm-4">
											<p:selectOneMenu id="invoiceType"
												value="#{invoice.invoiceHeader.invoiceType.invoiceTypeCoded}"
												disabled="#{cc.attrs.value.editInvoiceInError}"
												required="true"
												requiredMessage="#{gnxxcblinvlbls.inv_type} : #{gnxxcblcomlbls.value_required}"
												styleClass="selectonemenu-resize">
												<f:selectItem />
												<f:selectItems value="#{cc.attrs.value.invoiceTypeItems}" />
												<p:ajax event="change" update="invoiceTypeSelect"
													immediate="true" global="false" />
											</p:selectOneMenu>
										</p:outputPanel>
									</p:outputPanel>
									<p:outputPanel styleClass="form-group"
										rendered="#{invoice.invoiceHeader.invoiceType.invoiceTypeCoded == 'OTHER'}">
										<p:outputLabel for="invoiceTypeOther"
											styleClass="col-sm-4 control-label"
											value="#{gnxxcblinvlbls.other}" />
										<p:outputPanel styleClass="col-sm-4">
											<p:inputText id="invoiceTypeOther" required="true"
												value="#{invoice.invoiceHeader.invoiceType.invoiceTypeCodedOther}"
												validator="xssValidator"
												requiredMessage="#{gnxxcblinvlbls.other} : #{gnxxcblcomlbls.value_required}"
												style="width: 100% !important;" />
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel id="invoiceCurrencySelect"
									styleClass="form-group">
									<p:outputLabel for="invoiceCurrency"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.invoice_currency}" />
									<p:outputPanel styleClass="col-sm-4">
										<p:selectOneMenu id="invoiceCurrency" required="true"
											requiredMessage="#{gnxxcblinvlbls.invoice_currency} : #{gnxxcblcomlbls.value_required}"
											value="#{invoice.invoiceHeader.invoiceCurrency.currencyCoded}"
											styleClass="selectonemenu-resize">
											<f:selectItem />
											<f:selectItems value="#{cc.attrs.value.currencyItems}" />
											<p:ajax event="change" update="invoiceCurrencySelect"
												immediate="true" global="false" />
										</p:selectOneMenu>
									</p:outputPanel>
									<p:outputPanel
										rendered="#{invoice.invoiceHeader.invoiceCurrency.currencyCoded == 'OTHER'}">
										<p:outputLabel for="invoiceCurrencyOther"
											styleClass="col-sm-1 control-label"
											value="#{gnxxcblinvlbls.other}" />
										<p:outputPanel styleClass="col-sm-4">
											<p:inputText id="invoiceCurrencyOther"
												value="#{invoice.invoiceHeader.invoiceCurrency.currencyCodedOther}"
												validator="xssValidator" required="true"
												requiredMessage="#{gnxxcblinvlbls.other} : #{gnxxcblcomlbls.value_required}"
												style="width: 100% !important;" />
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="invoiceIssueDate"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.issue_date}" />
									<p:outputPanel styleClass="col-sm-4">
										<p:calendar id="invoiceIssueDate" navigator="true"
											widgetVar="issueDate" readonlyInput="true"
											value="#{invoice.invoiceHeader.invoiceIssueDate.value}"
											disabled="#{cc.attrs.value.editInvoiceInError}"
											locale="#{gnxSessionHandler.locale}"
											timeZone="#{secSessionHandler.timeZone.ID}"
											pattern="#{gnxSessionHandler.userDateFormat}" required="true"
											requiredMessage="#{gnxxcblinvlbls.issue_date} : #{gnxxcblcomlbls.value_required}"
											styleClass="input-calendar-resize" />
									</p:outputPanel>
								</p:outputPanel>
							</p:panel>
						</p:outputPanel>
						<p:outputPanel styleClass="col-md-1" />
						<!--INVOICES DATES -->
						<p:outputPanel styleClass="col-md-6">
							<p:panel id="invoiceDateToggleable"
								header="#{gnxxcblinvlbls.invoice_dates}" toggleable="true"
								collapsed="false" toggleSpeed="500" styleClass="border-gnx"
								style="margin-bottom:10px">

								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="invoiceDueDate"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.due_date}" />
									<p:outputPanel styleClass="col-sm-4">
										<p:calendar id="invoiceDueDate" navigator="true"
											readonlyInput="true"
											value="#{invoice.invoiceHeader.invoiceDates.invoiceDueDate.value}"
											mindate="#{cc.attrs.value.todayAsStringUsingUserPattern}"
											widgetVar="dueDate" locale="#{gnxSessionHandler.locale}"
											timeZone="#{secSessionHandler.timeZone.ID}"
											pattern="#{gnxSessionHandler.userDateFormat}" required="true"
											requiredMessage="#{gnxxcblinvlbls.due_date} : #{gnxxcblcomlbls.value_required}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="actualShipDate"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.actual_ship_date}" />
									<p:outputPanel styleClass="col-sm-8">
										<p:calendar id="actualShipDate" readonlyInput="true"
											styleClass="form-control-static" navigator="true"
											widgetVar="actualShipDate"
											value="#{invoice.invoiceHeader.invoiceDates.actualShipDate.value}"
											locale="#{gnxSessionHandler.locale}"
											timeZone="#{secSessionHandler.timeZone.ID}"
											pattern="#{gnxSessionHandler.userDateFormat}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="actualDeliveryDate"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.actual_delivery_date}" />
									<p:outputPanel styleClass="col-sm-8">
										<p:calendar id="actualDeliveryDate" readonlyInput="true"
											styleClass="form-control-static" navigator="true"
											widgetVar="actualDeliveryDate"
											value="#{invoice.invoiceHeader.invoiceDates.actualDeliveryDate.value}"
											locale="#{gnxSessionHandler.locale}"
											timeZone="#{secSessionHandler.timeZone.ID}"
											pattern="#{gnxSessionHandler.userDateFormat}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="form-group"
									rendered="#{cc.attrs.value.listOfDateCoded[0] != null}">
									<p:outputLabel for="dateOfOperation"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.date_of_operation}" />
									<p:outputPanel styleClass="col-sm-8">
										<p:calendar id="dateOfOperation" readonlyInput="true"
											styleClass="form-control-static" navigator="true"
											value="#{cc.attrs.value.listOfDateCoded[0].date.value}"
											locale="#{gnxSessionHandler.locale}"
											timeZone="#{secSessionHandler.timeZone.ID}"
											pattern="#{gnxSessionHandler.userDateFormat}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="psInvoicingPeriod"
									rendered="#{invoice.invoiceHeader.invoiceDates.invoicingPeriod != null}">
									<p:outputPanel styleClass="form-group">
										<p:outputLabel for="invoicingStartDate"
											styleClass="col-sm-4 control-label"
											value="#{gnxxcblinvlbls.invoicing_start_date}"
											title="#{gnxxcblinvlbls.invoicing_credit}" />
										<p:outputPanel styleClass="col-sm-8">
											<p:calendar id="invoicingStartDate" readonlyInput="true"
												navigator="true"
												value="#{invoice.invoiceHeader.invoiceDates.invoicingPeriod.startDate.value}"
												locale="#{gnxSessionHandler.locale}"
												timeZone="#{secSessionHandler.timeZone.ID}"
												pattern="#{gnxSessionHandler.userDateFormat}">
												<p:ajax event="dateSelect" update="@(.psInvoicingPeriod)"
													global="false" />
											</p:calendar>
										</p:outputPanel>
									</p:outputPanel>
									<p:outputPanel styleClass="form-group"
										rendered="#{not empty invoice.invoiceHeader.invoiceDates.invoicingPeriod.startDate.value}">
										<p:outputLabel for="invoicingEndDate"
											styleClass="col-sm-4 control-label"
											value="#{gnxxcblinvlbls.invoicing_end_date}" />
										<p:outputPanel styleClass="col-sm-8">
											<p:calendar id="invoicingEndDate" navigator="true"
												value="#{invoice.invoiceHeader.invoiceDates.invoicingPeriod.endDate.value}"
												readonlyInput="true" locale="#{gnxSessionHandler.locale}"
												timeZone="#{secSessionHandler.timeZone.ID}" required="true"
												pattern="#{gnxSessionHandler.userDateFormat}">
												<f:attribute name="startDate"
													value="#{invoice.invoiceHeader.invoiceDates.invoicingPeriod.startDate.value}" />
												<f:validator validatorId="primeDateRangeValidator" />
												<p:ajax event="dateSelect" update="@(.psInvoicingPeriod)"
													global="false" />
											</p:calendar>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
							</p:panel>
						</p:outputPanel>
					</p:outputPanel>

					<!-- PARTY PANEL -->
					<p:outputPanel styleClass="row">
						<p:outputPanel styleClass="col-md-12 party-panel">
							<p:panel header="#{gnxxcblinvlbls.invoice_party_header}"
								toggleable="true" collapsed="false" toggleSpeed="500"
								style="margin-bottom:10px" styleClass="partyPanel border-gnx">
								<f:facet name="header">
									<p:outputPanel styleClass="row position-groupe">
										<p:outputPanel styleClass="col-sm-5 col-sm-offset-3">
											<h:outputText value="#{gnxxcblinvlbls.invoice_party_header}" />
										</p:outputPanel>

										<p:outputPanel styleClass="col-sm-4">
											<p:commandButton styleClass="btn-gnx btn-gnx-ajouts"
												value="#{gnxxcblinvlbls.add_entity}"
												process="@(.psListOfParty) @this" style="margin-bottom:5px;"
												actionListener="#{cc.attrs.value.onAddPartyCoded()}"
												update="@(.psListOfParty)" />
										</p:outputPanel>
									</p:outputPanel>
								</f:facet>

								<p:tabView value="#{cc.attrs.value.listOfPartyCoded}"
									var="party" styleClass="psListOfParty" id="psListOfParty"
									activeIndex="#{cc.attrs.value.activeIndex}">
									<p:ajax event="tabClose" process="@this"
										listener="#{cc.attrs.value.onRemovePartyCoded}"
										update="@(.psListOfParty)" global="false" />
									<p:tab title="#{gnxxcblinvlbls[party.partyRoleCoded]}"
										closable="#{cc.attrs.value.getPartyClosable(party.partyRoleCoded)}">
										<p:outputPanel id="tabPrty">
											<p:outputPanel styleClass="form-group"
												rendered="#{cc.attrs.value.getPartyClosable(party.partyRoleCoded)}">
												<p:outputLabel for="partyRoleCoded"
													styleClass="col-sm-4 control-label"
													value="#{gnxxcblinvlbls.party_role_coded}" />
												<p:outputPanel styleClass="col-sm-8">
													<p:selectOneMenu id="partyRoleCoded"
														value="#{party.partyRoleCoded}" styleClass="input-medium"
														required="true"
														requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.party_role_coded} : #{gnxxcblcomlbls.value_required}">
														<f:selectItem />
														<f:selectItems
															value="#{cc.attrs.value.partyRoleCodedItems}" />
													</p:selectOneMenu>
												</p:outputPanel>
											</p:outputPanel>

											<p:outputPanel styleClass="form-group">
												<p:outputLabel for="otherPartyId"
													styleClass="col-sm-4 control-label"
													value="#{gnxxcblinvlbls.identifier}" />
												<p:outputPanel styleClass="col-sm-8">
													<ui:param name="completeMethod"
														value="#{comApplicationHandler.getOnCompleteMethod(gnxRepositoryInvoiceAppHandler.getCompleteAddressMethod(party.partyRoleCoded))}" />
													<p:autoComplete id="otherPartyId"
														value="#{party.partyID.ident.value}" required="true"
														disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded) or party.partyRoleCoded == 'Seller'}"
														completeMethod="#{completeMethod}" minQueryLength="2"
														converter="gnxAddressToPartyConverter"
														validator="xssValidator"
														validatorMessage="#{gnxxcblinvlbls.identifier_incorrect}"
														forceSelection="false" var="address"
														itemValue="#{gnxRepositoryInvoiceAppHandler.getAddressAsString(address)}"
														itemLabel="#{gnxRepositoryInvoiceAppHandler.getAddressAsString(address)}"
														requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.identifier} : #{gnxxcblcomlbls.value_required}"
														converterMessage="#{party.partyRoleCoded}"
														style="width: 50% !important;">
														<p:ajax event="itemSelect"
															listener="#{cc.attrs.value.onSelectAddress}"
															process="@this" update="otherParty" global="false" />
														<p:column>
															<h:outputText
																value="#{address.identification} - #{address.name} - #{address.city}" />
														</p:column>
													</p:autoComplete>
												</p:outputPanel>
											</p:outputPanel>
											<p:outputPanel id="otherParty">
												<p:outputPanel styleClass="form-group">
													<p:outputLabel for="otherPartyName"
														styleClass="col-sm-4 control-label"
														value="#{gnxxcblinvlbls.name}" />
													<p:outputPanel styleClass="col-sm-8">
														<p:inputText id="otherPartyName" styleClass="input-medium"
															value="#{party.nameAddress.name1.value}" required="true"
															validator="xssValidator"
															validatorMessage="#{gnxxcblinvlbls.name_incorrect}"
															disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
															requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.name} : #{gnxxcblcomlbls.value_required}" />
													</p:outputPanel>
												</p:outputPanel>
												<p:outputPanel styleClass="form-group">
													<p:outputLabel for="otherPartyStreet"
														styleClass="col-sm-4 control-label"
														value="#{gnxxcblinvlbls.street}" />
													<p:outputPanel styleClass="col-sm-8">
														<p:inputText id="otherPartyStreet"
															styleClass="input-medium"
															value="#{party.nameAddress.street}" required="true"
															validator="xssValidator"
															validatorMessage="#{gnxxcblinvlbls.street_incorrect}"
															disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
															requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.street} : #{gnxxcblcomlbls.value_required}" />
													</p:outputPanel>
												</p:outputPanel>
												<p:outputPanel styleClass="form-group">
													<p:outputLabel for="otherPartyPostalCode"
														styleClass="col-sm-4 control-label"
														value="#{gnxxcblinvlbls.postal_code}" />
													<p:outputPanel styleClass="col-sm-1">
														<p:inputText id="otherPartyPostalCode"
															value="#{party.nameAddress.postalCode.value}"
															validator="xssValidator"
															validatorMessage="#{gnxxcblinvlbls.postal_code_incorrect}"
															disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
															required="true"
															requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.postal_code} : #{gnxxcblcomlbls.value_required}"
															style="width: 100% !important;" />
													</p:outputPanel>
													<p:outputLabel for="otherPartyCity"
														styleClass="col-sm-1 control-label"
														value="#{gnxxcblinvlbls.city}" />
													<p:outputPanel styleClass="col-sm-4">
														<p:inputText id="otherPartyCity" styleClass="input-medium"
															value="#{party.nameAddress.city.value}" required="true"
															validator="xssValidator"
															validatorMessage="#{gnxxcblinvlbls.city_incorrect}"
															disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
															requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.city} : #{gnxxcblcomlbls.value_required}" />
													</p:outputPanel>
												</p:outputPanel>
												<p:outputPanel id="otherPartyCountrySelect">
													<p:outputPanel styleClass="form-group">
														<p:outputLabel for="otherPartyCountry"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.country}" />
														<p:outputPanel styleClass="col-sm-2">
															<p:selectOneMenu id="otherPartyCountry" required="true"
																requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.country} : #{gnxxcblcomlbls.value_required}"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
																value="#{party.nameAddress.country.countryCoded}"
																styleClass="selectonemenu-resize">
																<f:selectItem />
																<f:selectItems value="#{cc.attrs.value.countryItems}" />
																<p:ajax event="change" update="otherPartyCountrySelect"
																	immediate="true" global="false" />
															</p:selectOneMenu>
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.nameAddress.country.countryCoded == 'OTHER'}">
														<p:outputLabel for="otherPartyCountryOther"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.other}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="otherPartyCountryOther"
																styleClass="input-medium" required="true"
																value="#{party.nameAddress.country.countryCodedOther}"
																validator="xssValidator"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
																requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.other} : #{gnxxcblcomlbls.value_required}" />
														</p:outputPanel>
													</p:outputPanel>
												</p:outputPanel>
												<p:outputPanel styleClass="form-group"
													rendered="#{not empty tel}">
													<p:outputLabel for="otherPartyTel"
														styleClass="col-sm-4 control-label"
														value="#{gnxxcblinvlbls.tel}" />
													<ui:repeat var="contactNumber"
														value="#{party.primaryContact.listOfContactNumber.contactNumber}">
														<ui:param name="tel"
															value="#{contactNumber.contactNumberTypeCoded == 'TELEPHONE_NUMBER' ? contactNumber.contactNumberValue.value : tel}" />
													</ui:repeat>
													<p:outputPanel styleClass="col-sm-8">
														<p:inputText id="otherPartyTel" styleClass="input-medium"
															value="#{tel}" validator="xssValidator"
															disabled="#{!cc.attrs.value.newInvoice or not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
													</p:outputPanel>
												</p:outputPanel>
												<p:outputPanel
													rendered="#{party.partyRoleCoded != 'ShipTo'}">
													<p:outputPanel styleClass="form-group"
														rendered="#{party.listOfIdentifier.identifier[0] != null}">
														<p:outputLabel for="otherPartyTvaIdent"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.recordNumber}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="otherPartyTvaIdent"
																styleClass="input-small"
																value="#{party.listOfIdentifier.identifier[0].ident.value}"
																validator="xssValidator"
																validatorMessage="#{gnxxcblinvlbls.recordNumber_incorrect}"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.listOfIdentifier.identifier[1] != null}">
														<p:outputLabel for="otherPartyNumber"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.gouv_ref}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="otherPartyNumber"
																styleClass="input-small"
																value="#{party.listOfIdentifier.identifier[1].ident.value}"
																validator="xssValidator"
																validatorMessage="#{gnxxcblinvlbls.gouv_ref_incorrect}"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.listOfIdentifier.identifier[2] != null
                                        			and cc.attrs.value.partner.location.address.country.getCountry() == 'PT'
                                        			and party.partyRoleCoded == 'Seller'}">
														<p:outputLabel for="governmentHashCode"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.gouv_hashcode}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="governmentHashCode"
																styleClass="input-small"
																value="#{party.listOfIdentifier.identifier[2].ident.value}"
																validator="xssValidator" required="true"
																requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.gouv_hashcode} : #{gnxxcblcomlbls.value_required}"
																placeholder="HASHCODE"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.listOfIdentifier.identifier[3] != null
                                        			and cc.attrs.value.partner.location.address.country.getCountry() == 'PT'
                                        			and party.partyRoleCoded == 'Seller'}">
														<p:outputLabel for="NIF"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.NIF}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="NIF" styleClass="input-small"
																value="#{party.listOfIdentifier.identifier[3].ident.value}"
																validator="xssValidator" required="true"
																requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.NIF} : #{gnxxcblcomlbls.value_required}"
																placeholder="NIF"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.partyTaxInformation != null}">
														<p:outputLabel for="otherPartyTaxIdent"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.tva_ident}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="otherPartyTaxIdent"
																styleClass="input-small"
																value="#{party.partyTaxInformation.taxIdentifier.ident.value}"
																validator="xssValidator"
																validatorMessage="#{gnxxcblinvlbls.tva_ident_incorrect}"
																required="#{cc.attrs.value.taxIsMandatory(party.partyRoleCoded)}"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.partyRoleCoded == 'Seller'}">
														<p:outputLabel for="legalFormAndShareCapital"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.legal_form_share_capital}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="legalFormAndShareCapital"
																styleClass="input-small"
																value="#{invoice.invoiceHeader.invoiceHeaderNote}"
																validator="xssValidator"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.partyRoleCoded == 'Buyer'}">
														<p:outputLabel for="buyerPartyContactName"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.contactName}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="buyerPartyContactName"
																styleClass="input-medium"
																value="#{cc.attrs.value.buyerContactName}"
																validator="xssValidator"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
																requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.contactName} : #{gnxxcblcomlbls.value_required}" />
														</p:outputPanel>
													</p:outputPanel>
													<p:outputPanel styleClass="form-group"
														rendered="#{party.partyRoleCoded == 'Buyer'}">
														<p:outputLabel for="buyerPartyMail"
															styleClass="col-sm-4 control-label"
															value="#{gnxxcblinvlbls.mail}" />
														<p:outputPanel styleClass="col-sm-8">
															<p:inputText id="buyerPartyMail"
																styleClass="input-medium"
																value="#{cc.attrs.value.contactMail}"
																validator="xssValidator"
																disabled="#{not cc.attrs.value.isPropertyDescriptorEditable(party.partyRoleCoded)}"
																requiredMessage="(#{gnxxcblinvlbls[party.partyRoleCoded]}) #{gnxxcblinvlbls.mail} : #{gnxxcblcomlbls.value_required}"
																validatorMessage="#{gnxxcblinvlbls.mail_not_valid}">
																<f:validateRegex
																	pattern="^[_A-Za-z0-9-\+]+(\.[_A-Za-z0-9-]+)*@[_A-Za-z0-9-]+(\.[_A-Za-z0-9-]+)*(\.[_A-Za-z]{2,})$" />
															</p:inputText>
														</p:outputPanel>
													</p:outputPanel>
												</p:outputPanel>
											</p:outputPanel>
										</p:outputPanel>
									</p:tab>
								</p:tabView>
							</p:panel>
						</p:outputPanel>
					</p:outputPanel>

					<p:outputPanel styleClass="row">
						<!-- PAYMENT INSTRUCTION -->
						<p:outputPanel styleClass="col-md-6">
							<p:panel id="taxToggleable"
								header="#{gnxxcblinvlbls.payment_instructions} "
								toggleable="true" collapsed="false" toggleSpeed="500"
								style="margin-bottom:10px"
								styleClass="psTaxToggleable border-gnx">
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="netDueDate"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.net_due_date}" />
									<p:outputPanel styleClass="col-sm-8">
										<p:calendar id="netDueDate" navigator="true"
											readonlyInput="true"
											value="#{invoice.invoiceHeader.invoicePaymentInstructions.paymentTerms.netDueDate.value}"
											locale="#{gnxSessionHandler.locale}"
											timeZone="#{secSessionHandler.timeZone.ID}"
											pattern="#{gnxSessionHandler.userDateFormat}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="paymentTermsNote"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.payment_terms_note}">
										<g:rgpdTooltip tooltipId="1" />
									</p:outputLabel>
									<p:outputPanel styleClass="col-sm-8">
										<p:inputText id="paymentTermsNote"
											styleClass="form-control input-sm"
											value="#{invoice.invoiceHeader.invoicePaymentInstructions.paymentTerms.paymentTermsNote.value}"
											validator="xssValidator"
											validatorMessage="#{gnxxcblinvlbls.payment_terms_note_incorrect}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="form-group">
									<p:outputLabel for="discountTerms"
										styleClass="col-sm-4 control-label"
										value="#{gnxxcblinvlbls.discount_terms}">
										<g:rgpdTooltip tooltipId="2" />
									</p:outputLabel>
									<p:outputPanel styleClass="col-sm-8">
										<p:inputText id="discountTerms"
											styleClass="form-control input-sm"
											value="#{cc.attrs.value.getFirstDiscountPaymentType(invoice.invoiceHeader.invoicePaymentInstructions.paymentTerms).paymentTermDescription.value}"
											validator="xssValidator"
											validatorMessage="#{gnxxcblinvlbls.discount_terms_incorrect}" />
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="psTaxExempt">
									<p:outputPanel styleClass="form-group">
										<p:outputLabel for="taxExempt"
											styleClass="col-sm-4 control-label"
											value="#{gnxxcblinvlbls.tax_exempt}" />
										<p:outputPanel styleClass="col-sm-8">
											<p:selectBooleanCheckbox id="taxExempt"
												value="#{cc.attrs.value.taxExempt}">
												<p:ajax process="@this"
													update="@(.psTaxExempt) @(.psDetailTable) @(.psSummary)"
													listener="#{cc.attrs.value.addTaxExemptInfo()}"
													global="false" />
											</p:selectBooleanCheckbox>
										</p:outputPanel>
									</p:outputPanel>
									<p:outputPanel rendered="#{cc.attrs.value.taxExempt}">
										<p:outputPanel styleClass="form-group">
											<p:outputLabel for="reasonTaxExemptCodedOther"
												styleClass="col-sm-4 control-label"
												value="#{gnxxcblinvlbls.reason_tax_exempt_coded_other}" />
											<p:outputPanel styleClass="col-sm-8">
												<p:inputText id="reasonTaxExemptCodedOther"
													styleClass="form-control"
													value="#{invoice.invoiceHeader.taxReference[0].reasonTaxExemptCodedOther}"
													validator="xssValidator" required="true"
													requiredMessage="#{gnxxcblinvlbls.reason_tax_exempt_coded_other} : #{gnxxcblcomlbls.value_required}" />
											</p:outputPanel>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
							</p:panel>
						</p:outputPanel>

						<!-- REFERENCES -->
						<p:outputPanel styleClass="col-md-6">
							<p:panel id="references"
								header="#{gnxxcblinvlbls.invoice_references} " toggleable="true"
								collapsed="false" toggleSpeed="500" styleClass="border-gnx"
								style="margin-bottom:10px">
								<f:facet name="header">
									<p:outputPanel styleClass="row position-groupe">
										<p:outputPanel styleClass="col-sm-5 col-sm-offset-3">
											<h:outputText value="#{gnxxcblinvlbls.invoice_references}" />
										</p:outputPanel>
										<p:outputPanel styleClass="col-sm-4">
											<p:outputPanel styleClass="form-group right"
												style="padding-right:0">
												<p:selectOneMenu id="typeInvoiceReference"
													style="width:100%"
													value="#{cc.attrs.value.currentInvoiceReferenceType}">
													<p:ajax event="change"
														process="@this @(.psInvoiceReference)"
														listener="#{cc.attrs.value.onSelectNewReference()}"
														update="@(.psInvoiceReference) @(.psInvoiceSummary)"
														oncomplete="editLastDatatableRowRemoveCloseButton('invoiceReferences')"
														global="false" />
													<f:selectItem
														itemLabel="#{gnxxcblinvlbls.add_invoice_reference}" />
													<f:selectItems
														value="#{cc.attrs.value.listInvoiceReferenceType()}" />
												</p:selectOneMenu>

											</p:outputPanel>
										</p:outputPanel>
									</p:outputPanel>
								</f:facet>

								<p:dataTable id="invoiceReferences"
									value="#{cc.attrs.value.invoiceReferences}"
									var="invoiceReference" editable="true"
									styleClass="psInvoiceReference"
									emptyMessage="#{labels.no_records_found}">

									<p:column headerText="#{gnxxcblinvlbls.invoice_reference_type}">
										<h:outputText
											value="#{cc.attrs.value.i18nInvoiceReferenceType(invoiceReference.type)}" />
									</p:column>
									<p:column headerText="#{gnxxcblinvlbls.invoice_reference_num}">
										<p:cellEditor>
											<f:facet name="output">
												<h:outputText value="#{invoiceReference.number}" />
											</f:facet>
											<f:facet name="input">
												<p:inputText style="width:100%"
													styleClass="form-control input-sm"
													value="#{invoiceReference.number}" required="true"
													validator="xssValidator"
													requiredMessage="#{gnxxcblinvlbls.invoice_reference_num} : #{gnxxcblcomlbls.value_required}" />
											</f:facet>
										</p:cellEditor>
									</p:column>
									<p:column headerText="#{gnxxcblinvlbls.invoice_reference_date}"
										style="text-align: center">
										<p:cellEditor>
											<f:facet name="input">
												<p:calendar id="invoiceReferenceDate" navigator="true"
													pattern="#{gnxSessionHandler.userDateFormat}"
													readonlyInput="true" value="#{invoiceReference.date}"
													widgetVar="invoiceReferenceDate"
													locale="#{secSessionHandler.locale}"
													timeZone="#{secSessionHandler.timeZone.ID}"
													maxdate="#{cc.attrs.value.todayAsStringUsingUserPattern}" />
											</f:facet>
											<f:facet name="output">
												<h:outputText
													value="#{cc.attrs.value.formatDate(invoiceReference.date, secSessionHandler.timeZone, 'dd/MM/yyyy')}" />
											</f:facet>

										</p:cellEditor>
									</p:column>
									<p:column
										headerText="#{gnxxcblinvlbls.invoice_reference_period_start_date}"
										style="text-align: center">
										<p:cellEditor>
											<f:facet name="input">
												<p:calendar id="invoiceReferenceStartDate" navigator="true"
													pattern="#{gnxSessionHandler.userDateFormat}"
													readonlyInput="true"
													value="#{invoiceReference.periodStartDate.value}"
													widgetVar="invoiceReferenceStartDate"
													locale="#{secSessionHandler.locale}"
													timeZone="#{secSessionHandler.timeZone.ID}" />
											</f:facet>
											<f:facet name="output">
												<h:outputText
													value="#{cc.attrs.value.formatDate(invoiceReference.periodStartDate.value, secSessionHandler.timeZone, 'dd/MM/yyyy')}" />
											</f:facet>
										</p:cellEditor>
									</p:column>
									<p:column
										headerText="#{gnxxcblinvlbls.invoice_reference_period_end_date}"
										style="text-align: center">
										<p:cellEditor>
											<f:facet name="input">
												<p:calendar id="invoiceReferenceEndDate" navigator="true"
													pattern="#{gnxSessionHandler.userDateFormat}"
													readonlyInput="true"
													value="#{invoiceReference.periodEndDate.value}"
													widgetVar="invoiceReferenceEndDate"
													locale="#{secSessionHandler.locale}"
													timeZone="#{secSessionHandler.timeZone.ID}"
													mindate="#{cc.attrs.value.todayAsStringUsingUserPattern}" />
											</f:facet>
											<f:facet name="output">
												<h:outputText
													value="#{cc.attrs.value.formatDate(invoiceReference.periodEndDate.value, secSessionHandler.timeZone, 'dd/MM/yyyy')}" />
											</f:facet>
										</p:cellEditor>
									</p:column>

									<p:ajax event="rowEditInit"
										listener="#{cc.attrs.value.onInvoiceReferenceRowEditInit}"
										update="@(.psInvoiceValidation)" global="false" />
									<p:ajax event="rowEdit"
										listener="#{cc.attrs.value.onInvoiceReferenceRowEdit}"
										update="@(.psInvoiceReference) @(.psInvoiceSummary) @(.psInvoiceValidation)"
										global="false" />
									<p:ajax event="rowEditCancel"
										listener="#{cc.attrs.value.onInvoiceReferenceRowCancel}"
										update="@(.psInvoiceReference) @(.psInvoiceSummary) @(.psInvoiceValidation)"
										global="false" />

									<p:column style="width:70px;">
										<p:rowEditor style="float:left;"
											rendered="#{invoiceReference.editable}" />
										<p:commandLink style="margin-left: 15px;"
											styleClass="ui-icon ui-icon-trash"
											disabled="#{not invoiceReference.editable}"
											action="#{cc.attrs.value.onRemoveInvoiceReference(invoiceReference)}"
											process="@this"
											update="@(.psInvoiceReference) @(.psInvoiceSummary) @(.psInvoiceValidation)"
											title="#{gnxxcblinvlbls.remove_invoice_reference}" />
									</p:column>
								</p:dataTable>
							</p:panel>
						</p:outputPanel>
					</p:outputPanel>

					<!-- INVOICE ALLOWANCE OR CHARGE AND TAX -->
					<p:outputPanel styleClass="row">
						<!-- INVOICE ALLOWANCE OR CHARGE -->
						<p:outputPanel styleClass="col-md-12">
							<p:panel id="allwcChrgToggleable"
								header="#{gnxxcblinvlbls.allowance_or_charge} "
								toggleable="true" collapsed="false" toggleSpeed="500"
								styleClass="border-gnx" style="margin-bottom:10px">

								<f:facet name="header">
									<p:outputPanel styleClass="row position-groupe">
										<p:outputPanel styleClass="col-sm-7 col-sm-offset-3">
											<h:outputText value="#{gnxxcblinvlbls.allowance_or_charge}" />
										</p:outputPanel>
										<p:outputPanel styleClass="col-sm-2 ">
											<p:outputPanel styleClass="form-group right"
												style="padding-right:0">
												<p:selectOneMenu id="refAllowOrChrg" style="width:100%"
													value="#{cc.attrs.value.currentAllowOrChrg}">
													<p:ajax event="itemSelect"
														process="@this @(.psAllowOrCharge)"
														listener="#{cc.attrs.value.onSelectAllowOrCharge()}"
														update="@(.psAllowOrCharge) @(.psInvoiceSummary) @(.psInvoiceValidation)"
														oncomplete="editLastDatatableRowRemoveCloseButton('allowOrCharge')"
														global="false" />
													<f:selectItem itemLabel="#{gnxxcblinvlbls.add_alloworchrg}" />
													<f:selectItem itemValue=" "
														itemLabel="&lt;div class='separator'&gt; &lt;hr/&gt; &lt;/div&gt;"
														itemDisabled="true"
														itemEscaped="&lt;div class='separator'&gt; &lt;hr/&gt; &lt;/div&gt;" />
													<f:selectItem itemValue="Allowance"
														itemLabel="#{gnxxcblinvlbls.add_allowance}" />
													<f:selectItem itemValue="Charge"
														itemLabel="#{gnxxcblinvlbls.add_charge}" />
													<f:selectItem itemValue=" "
														itemLabel="&lt;div class='separator'&gt; &lt;hr/&gt; &lt;/div&gt;"
														itemDisabled="true"
														itemEscaped="&lt;div class='separator'&gt; &lt;hr/&gt; &lt;/div&gt;" />
													<f:selectItems
														value="#{cc.attrs.value.listOfGlobalTaxItems}" />
												</p:selectOneMenu>
											</p:outputPanel>
										</p:outputPanel>
									</p:outputPanel>

								</f:facet>

								<p:outputPanel styleClass="row">
									<p:outputPanel styleClass="col-md-12">
										<p:dataTable id="allowOrCharge"
											value="#{cc.attrs.value.allowOrChargeTypes}" var="alwcChrg"
											editable="true" styleClass="psAllowOrCharge"
											emptyMessage="#{labels.no_records_found}">

											<p:ajax event="rowEdit"
												listener="#{cc.attrs.value.onRowEdit}"
												update="@(.psAllowOrCharge) @(.psInvoiceSummary) @(.psInvoiceValidation) @(.psSummary)"
												global="false" />
											<p:ajax event="rowEditCancel"
												listener="#{cc.attrs.value.onRowCancel}"
												update="@(.psAllowOrCharge) @(.psInvoiceValidation)"
												global="false" />

											<p:column headerText="#{gnxxcblinvlbls.indicator_coded}">
												<p:cellEditor>
													<f:facet name="output">
														<h:outputText
															value="#{gnxxcblinvlbls[alwcChrg.indicatorCoded]}" />
													</f:facet>
													<f:facet name="input">
														<p:selectOneMenu value="#{alwcChrg.indicatorCoded}"
															style="width:100%;margin-top:2px;" required="true"
															requiredMessage="#{gnxxcblinvlbls.indicator_coded} : #{gnxxcblcomlbls.value_required}">
															<f:selectItem />
															<f:selectItems
																value="#{cc.attrs.value.indicatorCodeItems}" />
														</p:selectOneMenu>
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.description}"
												style="width: 50%">
												<p:cellEditor>
													<f:facet name="output">
														<h:outputText
															value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}" />
													</f:facet>
													<f:facet name="input">
														<p:inputText style="width:100%"
															styleClass="form-control input-sm"
															value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}"
															required="true" validator="xssValidator"
															requiredMessage="#{gnxxcblinvlbls.description} : #{gnxxcblcomlbls.value_required}" />
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.inv_type}">
												<p:cellEditor>
													<f:facet name="output">
														<h:outputText
															value="#{gnxxcblinvlbls[alwcChrg.basisCoded]}" />
													</f:facet>
													<f:facet name="input">
														<p:selectOneMenu value="#{alwcChrg.basisCoded}"
															style="width:100%;margin-top:2px;" required="true"
															requiredMessage="#{gnxxcblinvlbls.inv_type} : #{gnxxcblcomlbls.value_required}">
															<f:selectItem />
															<f:selectItems value="#{cc.attrs.value.basisCodeItems}" />
															<f:ajax event="change" render="amountOrPercent"
																listener="#{cc.attrs.value.initAllowanceChargeWhenChangingBasisCoded(alwcChrg)}"
																global="false" />
														</p:selectOneMenu>
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.value}"
												styleClass="right">
												<p:cellEditor id="amountOrPercent">
													<f:facet name="output">
														<h:outputText
															value="#{alwcChrg.indicatorCoded.value() == 'Allowance' ? '-' : '+'}" />
														<h:outputText
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge != null}"
															value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value} %" />
														<h:outputText
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue != null}"
															value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount.value} " />
													</f:facet>
													<f:facet name="input">
														<pe:inputNumber
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge != null}"
															value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}"
															required="true"
															requiredMessage="#{gnxxcblinvlbls.value} : #{gnxxcblcomlbls.value_required}"
															styleClass="input-number-resize" maxValue="100.00"
															minValue="-100.00" symbol="%" symbolPosition="suffix" />
														<pe:inputNumber
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue != null}"
															required="true"
															requiredMessage="#{gnxxcblinvlbls.value} : #{gnxxcblcomlbls.value_required}"
															styleClass="input-number-resize"
															value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount.value}"
															minValue="-9999999.00" />
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.tax}"
												styleClass="right">
												<p:cellEditor id="allChgTax">
													<f:facet name="output">
														<h:outputText
															value="#{alwcChrg.tax[0].taxCategoryCodedOther.value}" />
													</f:facet>
													<f:facet name="input">
														<p:selectOneMenu
															rendered="#{not empty cc.attrs.value.taxItems}"
															value="#{alwcChrg.tax[0].taxCategoryCodedOther.value}"
															styleClass="selectonemenu-resize">
															<f:selectItem />
															<f:selectItems value="#{cc.attrs.value.taxItems}" />
														</p:selectOneMenu>
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.vat_amount}"
												styleClass="right">
												<p:cellEditor id="allChgTaxAmount">
													<f:facet name="input">
														<h:outputText
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue != null}"
															styleClass="remChgVatAmount"
															value="#{alwcChrg.tax[0].taxAmount.value}" />
														<h:outputText
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge != null}"
															styleClass="remChgVatAmount" value="" />
													</f:facet>

													<f:facet name="output">
														<h:outputText styleClass="remChgVatAmount"
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue != null}"
															value="#{alwcChrg.tax[0].taxAmount.value}" />
														<h:outputText styleClass="remChgVatAmount"
															rendered="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge != null}"
															value="" />
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column style="width:70px;">
												<p:rowEditor style="float:left;" />
												<p:commandLink styleClass="ui-icon ui-icon-trash"
													action="#{cc.attrs.value.onRemoveAllowOrCharge(alwcChrg)}"
													process="@this"
													update="@(.psAllowOrCharge) @(.psInvoiceSummary) @(.psSummary) @(.psInvoiceValidation)"
													title="#{gnxxcblinvlbls.remove_alloworchrg}" />
											</p:column>
										</p:dataTable>
									</p:outputPanel>
								</p:outputPanel>
							</p:panel>
						</p:outputPanel>
					</p:outputPanel>

					<!-- INVOICE DETAILS -->
					<p:outputPanel styleClass="row psInvoiceDetails"
						id="invoice_details">

						<p:outputPanel styleClass="col-md-12">
							<p:panel id="detailsTooglable"
								header="#{gnxxcblinvlbls.details} " toggleable="true"
								collapsed="false" toggleSpeed="200"
								style="margin-bottom:10px;width:100%"
								styleClass="psDetails border-gnx" widgetVar="wDetails">


								<f:facet name="header">
									<p:outputPanel styleClass="row position-groupe">
										<p:outputPanel styleClass="col-sm-4 col-sm-offset-3">
											<h:outputText value="#{gnxxcblinvlbls.details}" />
										</p:outputPanel>

										<p:outputPanel styleClass="col-sm-2 col-sm-offset-3">
											<p:outputPanel styleClass="form-group right"
												style="padding-right:0">
												<p:selectOneMenu id="product"
													value="#{cc.attrs.value.productSelected}"
													style="width:100%">
													<f:selectItem itemLabel="#{gnxxcblinvlbls.add_product}" />

													<f:selectItems value="#{cc.attrs.value.productItems}" />
													<p:ajax event="change"
														listener="#{cc.attrs.value.onSelectProduct()}"
														process="@this"
														update="@(.psDetails) @(.psInvoiceValidation)"
														oncomplete="editLastDatatableRowRemoveCloseButton('detailsDatatable')"
														global="false" />
												</p:selectOneMenu>
											</p:outputPanel>
										</p:outputPanel>
									</p:outputPanel>
								</f:facet>


								<p:outputPanel styleClass="row">
									<p:outputPanel styleClass="col-md-12">
										<p:dataTable id="detailsDatatable"
											value="#{cc.attrs.value.details}" var="detail" rows="10"
											paginator="true"
											paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
											rowsPerPageTemplate="5,10,15" paginatorPosition="bottom"
											styleClass="psDetailTable" resizableColumns="true"
											emptyMessage="#{labels.no_records_found}"
											style="width:100%; overflow:hidden"
											rendered="#{cc.attrs.value.product == null}">

											<p:column width="12%"
												headerText="#{gnxxcblinvlbls.buyer_part_number}"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemBuyerPartNumber').rendered}">
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.itemIdentifiers.partNumbers.buyerPartNumber.partID}" />
											</p:column>
											<p:column width="12%"
												headerText="#{gnxxcblinvlbls.seller_part_number}"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemSellerPartNumber').rendered}">
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.itemIdentifiers.partNumbers.sellerPartNumber.partID.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.product_identifier}"
												width="10%"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemProductIdentifier').rendered}">
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.itemIdentifiers.partNumbers.standardPartNumber.productIdentifier.value}"
													rendered="#{cc.attrs.value.product == null}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.item_description}"
												width="26%"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemDescription').rendered}">
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.itemIdentifiers.itemDescription.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.quantity_value}"
												styleClass="center" width="6%"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemInvoicedQuantityValue').rendered}">
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.invoicedQuantity.quantityValue.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.free_quantity_value}"
												styleClass="center" width="6%"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemFreeQuantityValue').rendered}">
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.listOfQuantityCoded.quantityCoded[0].quantityValue.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.unit_of_measurement}"
												width="5%"
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemQuantityUnitOfMeasurement').rendered}">
												<h:outputText
													value="#{gnxRepositoryInvoiceAppHandler.getUOMCodedFromString(detail.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCoded)}"
													rendered="#{detail.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCoded != 'Other'}" />
												<h:outputText
													value="#{detail.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCodedOther.value}"
													rendered="#{detail.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCoded == 'Other'}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.unit_net_price}"
												styleClass="right" width="9%">
												<h:outputText
													value="#{detail.invoicePricingDetail.listOfPrice.price[cc.attrs.value.getCalculationNet(detail)].unitPrice.unitPriceValue.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.total_net}"
												styleClass="right" width="9%">
												<h:outputText value="#{cc.attrs.value.getNetTotal(detail)}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.tax}" width="6%"
												styleClass="right">
												<h:outputText
													value="#{detail.invoicePricingDetail.tax[0].taxPercent.value}">
													<f:converter converterId="gnxBigDecimalConverter" />
													<f:attribute name="scale"
														value="#{cc.attrs.value.taxPrecision}" />
													<f:attribute name="symbol" value="%" />
												</h:outputText>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.vat_amount}"
												width="6%" styleClass="right">
												<h:outputText
													value="#{detail.invoicePricingDetail.tax[0].taxAmount}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.gross_amount}"
												styleClass="right" width="9%">
												<h:outputText
													value="#{detail.invoicePricingDetail.lineItemTotal.monetaryAmount.value}" />
											</p:column>

											<p:column width="70px;">
												<p:commandLink styleClass="ui-icon ui-icon-pencil"
													style="float:left;"
													action="#{cc.attrs.value.onSelectItemDetail(detail)}"
													process="@this"
													update="@(.psDetails) @(.psSummary) @(.psInvoiceValidation) @(.psItemDetail) @(.psLineItemTotal)"
													title="#{gnxxcblinvlbls.add_product}" />
												<p:commandLink styleClass="ui-icon ui-icon-trash"
													style="float:left;"
													action="#{cc.attrs.value.removeInvoiceItemDetails(detail)}"
													process="@this" update="@(.psDetails) @(.psSummary)"
													title="#{gnxxcblinvlbls.remove_product}" />
												<p:outputLabel styleClass="ui-icon ui-icon-tag"
													style="float:left;"
													rendered="#{cc.attrs.value.hasAllowOrCharge(detail)}"
													title="#{cc.attrs.value.getInlineAllowOfCharge(detail)}" />
											</p:column>
										</p:dataTable>
									</p:outputPanel>
								</p:outputPanel>

								<!-- ADD OR EDIT PRODUCT -->
								<p:outputPanel styleClass="row psItemDetail"
									rendered="#{cc.attrs.value.product != null}">
									<p:messages id="messagesItemDetail"
										styleClass="psMessagesItemDetail" />
									<p:outputPanel styleClass="col-sm-6">
										<p:outputPanel styleClass="form-group"
											rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemBuyerPartNumber').rendered}">
											<p:outputLabel for="buyerReference"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.buyer_part_number}" />
											<p:outputPanel styleClass="col-sm-9">
												<p:inputText id="buyerReference" styleClass="form-control"
													value="#{cc.attrs.value.buyerPartID}" required="true"
													validator="xssValidator"
													requiredMessage="#{gnxxcblinvlbls.buyer_part_number} : #{gnxxcblcomlbls.value_required}" />
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group"
											rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemSellerPartNumber').rendered}">
											<p:outputLabel for="sellerReference"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.seller_part_number}" />
											<p:outputPanel styleClass="col-sm-9">
												<p:inputText id="sellerReference" styleClass="form-control"
													value="#{cc.attrs.value.sellerPartID}" required="true"
													validator="xssValidator"
													requiredMessage="#{gnxxcblinvlbls.seller_part_number} : #{gnxxcblcomlbls.value_required}" />
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group"
											rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemProductIdentifier').rendered}">
											<p:outputLabel for="sellerReference"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.product_identifier}" />
											<p:outputPanel styleClass="col-sm-9">
												<p:inputText id="productIdentifier"
													styleClass="form-control"
													value="#{cc.attrs.value.standardProductIdentifier}"
													required="true" validator="xssValidator"
													requiredMessage="#{gnxxcblinvlbls.product_identifier} : #{gnxxcblcomlbls.value_required}" />
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group">
											<p:outputLabel for="itemDescription"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.item_description}" />
											<p:outputPanel styleClass="col-sm-9">
												<p:inputText id="itemDescription" styleClass="form-control"
													value="#{cc.attrs.value.product.invoiceBaseItemDetail.itemIdentifiers.itemDescription.value}"
													required="true" validator="xssValidator"
													requiredMessage="#{gnxxcblinvlbls.item_description} : #{gnxxcblcomlbls.value_required}" />
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group">
											<h:outputLabel for="quantityValue"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.quantity_value} *" />
											<p:outputPanel styleClass="col-sm-3">
												<pe:inputNumber id="quantityValue"
													value="#{cc.attrs.value.product.invoiceBaseItemDetail.invoicedQuantity.quantityValue.value}"
													styleClass="input-number-resize" minValue="0"
													required="true"
													requiredMessage="#{gnxxcblinvlbls.quantity_value} : #{gnxxcblcomlbls.value_required}">
													<p:ajax event="blur"
														listener="#{cc.attrs.value.refreshTotalLine}"
														update="@(.psLineItemTotal) @(.unitNetAndCostPrice)"
														global="false" />
												</pe:inputNumber>
											</p:outputPanel>
											<p:outputPanel
												rendered="#{cc.attrs.value.getDetailColumnDescriptor('itemFreeQuantityValue').rendered}">
												<h:outputLabel for="freeQuantityValue"
													styleClass="col-sm-3 control-label"
													value="#{gnxxcblinvlbls.free_quantity_value}" />
												<p:outputPanel styleClass="col-sm-3">
													<pe:inputNumber id="freeQuantityValue"
														value="#{cc.attrs.value.product.invoiceBaseItemDetail.listOfQuantityCoded.quantityCoded[0].quantityValue.value}"
														styleClass="input-number-resize" minValue="0" />
												</p:outputPanel>
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group psUnitOfMeasurement">
											<p:outputLabel for="unitOfMeasurement"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.unit_of_measurement}" />
											<p:outputPanel styleClass="col-sm-3">
												<p:autoComplete id="unitOfMeasurement" dropdown="true"
													title="#{labels.selection_or_enter}"
													value="#{cc.attrs.value.product.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement}"
													required="true"
													requiredMessage="#{gnxxcblinvlbls.unit_of_measurement} : #{gnxxcblcomlbls.value_required}"
													converter="gnxInvoiceUnitOfMeasurementConverter"
													completeMethod="#{gnxRepositoryInvoiceAppHandler.onCompleteUOMCoded}"
													var="unit" itemValue="#{unit}"
													itemLabel="#{gnxRepositoryInvoiceAppHandler.getUOMCodedFromUOMtype(unit, false)}"
													styleClass="input-autocomplete-resize">
													<p:column>
														<h:outputText
															value="#{gnxRepositoryInvoiceAppHandler.toStringUOMCodedFromUOMtype(unit, false)}" />
													</p:column>
													<p:ajax event="itemSelect" process="@this"
														update="@(.psUnitOfMeasurement)" immediate="true"
														global="false" />
													<p:ajax event="query" process="@this" update="@this"
														immediate="true" global="false" />
												</p:autoComplete>
											</p:outputPanel>
											<p:outputPanel styleClass="otherUnitOfMeasurementPanel"
												rendered="#{cc.attrs.value.product.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCoded == 'Other'}">
												<p:outputLabel for="otherUnitOfMeasurement"
													styleClass="col-sm-3 control-label"
													value="#{gnxxcblcomlbls.uom_other_unit}" />
												<p:outputPanel styleClass="col-sm-3">
													<p:inputText id="otherUnitOfMeasurement"
														styleClass="form-control"
														widgetVar="wOtherUnitOfMeasurement"
														value="#{cc.attrs.value.product.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCodedOther.value}"
														validator="xssValidator"
														disabled="#{cc.attrs.value.product.invoiceBaseItemDetail.invoicedQuantity.unitOfMeasurement.UOMCoded != 'Other'}" />
												</p:outputPanel>
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group unitNetAndCostPrice">
											<p:outputPanel styleClass="psCalculationNet">
												<h:outputLabel for="calculationNet"
													styleClass="col-sm-3 control-label"
													value="#{gnxxcblinvlbls.unit_net_price} *" />
												<p:outputPanel styleClass="col-sm-3">
													<pe:inputNumber id="calculationNet"
														value="#{cc.attrs.value.product.invoicePricingDetail.listOfPrice.price[cc.attrs.value.getCalculationNet(cc.attrs.value.product)].unitPrice.unitPriceValue.value}"
														disabled="true" required="true"
														requiredMessage="#{gnxxcblinvlbls.unit_net_price} : #{gnxxcblinvlbls.value_required}"
														minValue="-9999999.00"
														decimalPlaces="#{cc.attrs.value.detailPrecision}"
														styleClass="input-number-resize" />
												</p:outputPanel>
												<h:outputLabel for="calculationGross"
													styleClass="col-sm-3 control-label"
													value="#{gnxxcblinvlbls.gross_price} *" />
											</p:outputPanel>
											<p:outputPanel styleClass="col-sm-3">
												<pe:inputNumber id="calculationGross"
													value="#{cc.attrs.value.product.invoicePricingDetail.listOfPrice.price[cc.attrs.value.getCalculationGross(cc.attrs.value.product)].unitPrice.unitPriceValue.value}"
													required="true"
													requiredMessage="#{gnxxcblinvlbls.gross_price} : #{gnxxcblcomlbls.value_required}"
													minValue="0"
													decimalPlaces="#{cc.attrs.value.detailPrecision}"
													styleClass="input-number-resize">
													<p:ajax event="blur"
														listener="#{cc.attrs.value.refreshTotalLine}"
														update="@(.psLineItemTotal) @(.psCalculationNet)"
														global="false" />
												</pe:inputNumber>
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group"
											rendered="#{not cc.attrs.value.taxExempt}">
											<p:outputLabel for="productTax"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.tax}" />
											<p:outputPanel styleClass="col-sm-3">
												<p:selectOneMenu id="productTax" style="width:100%"
													rendered="#{not empty cc.attrs.value.taxItems}"
													value="#{cc.attrs.value.product.invoicePricingDetail.tax[0].taxCategoryCodedOther}"
													required="true"
													requiredMessage="#{gnxxcblinvlbls.tax} : #{gnxxcblcomlbls.value_required}"
													styleClass="selectonemenu-resize">
													<f:selectItem />
													<f:selectItems value="#{cc.attrs.value.taxItems}" />
												</p:selectOneMenu>
												<pe:inputNumber rendered="#{empty cc.attrs.value.taxItems}"
													styleClass="input-number-resize"
													value="#{cc.attrs.value.product.invoicePricingDetail.tax[0].taxPercent.value}"
													required="true" maxValue="100" minValue="0.000"
													requiredMessage="#{gnxxcblinvlbls.tax} : #{gnxxcblcomlbls.value_required}">
												</pe:inputNumber>
											</p:outputPanel>
										</p:outputPanel>
										<p:outputPanel styleClass="form-group psLineItemTotal">
											<h:outputLabel for="totalLineUnitCost"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblordrsplbls.net_item_price}" />
											<p:outputPanel styleClass="col-sm-3">
												<pe:inputNumber id="totalLineUnitCost" disabled="true"
													value="#{cc.attrs.value.product.invoicePricingDetail.lineItemSubTotal.monetaryAmount.value}"
													decimalPlaces="#{cc.attrs.value.detailPrecision}"
													minValue="-9999999.00" styleClass="input-number-resize" />
											</p:outputPanel>

											<h:outputLabel for="totalLineNetItem"
												styleClass="col-sm-3 control-label"
												value="#{gnxxcblinvlbls.gross_amount}" />
											<p:outputPanel styleClass="col-sm-3">
												<pe:inputNumber id="totalLineNetItem" disabled="true"
													value="#{cc.attrs.value.product.invoicePricingDetail.lineItemTotal.monetaryAmount.value}"
													decimalPlaces="#{cc.attrs.value.detailPrecision}"
													minValue="-9999999.00" styleClass="input-number-resize" />
											</p:outputPanel>
										</p:outputPanel>

									</p:outputPanel>
									<!-- EDIT LINE ALLOWANCE OR CHARGE -->
									<p:outputPanel styleClass="col-sm-6">
										<p:outputPanel styleClass="psAllChrg">
											<p:dataTable id="allowOrChargeDlg"
												value="#{cc.attrs.value.product.invoicePricingDetail.itemAllowancesOrCharges.allowOrCharge}"
												var="alwcChrg" editable="true"
												styleClass="psAllowOrChargeDlg"
												emptyMessage="#{labels.no_records_found}">

												<f:facet name="header">
													<p:outputPanel styleClass="row">
														<p:outputPanel styleClass="col-md-6" />
														<p:outputPanel styleClass="col-md-6">
															<p:outputPanel styleClass="form-group right"
																style="padding-right:0">
																<p:selectOneMenu id="refAllowOrChrgLine"
																	style="width:100%"
																	value="#{cc.attrs.value.currentAllowOrChrgLine}">
																	<p:ajax event="itemSelect"
																		process="@this @(.allowOrChargeDlg)"
																		listener="#{cc.attrs.value.onSelectAllowOrChargeLine()}"
																		update="@(.psAllowOrChargeDlg) @(.psInvoiceItemValidation)"
																		oncomplete="editLastDatatableRowRemoveCloseButton('allowOrChargeDlg')"
																		global="false" />
																	<f:selectItem
																		itemLabel="#{gnxxcblinvlbls.add_alloworchrg}" />
																	<f:selectItems value="#{cc.attrs.value.listOfTaxLines}" />
																</p:selectOneMenu>
															</p:outputPanel>
														</p:outputPanel>
													</p:outputPanel>
												</f:facet>
												<p:ajax event="rowEdit"
													listener="#{cc.attrs.value.onRowEditAllOrChrgLine}"
													update="@(.psAllowOrChargeDlg)
                                                			@(.psAllowOrChrgLink)
                                                			@(.psInvoiceItemValidation)
                                                			@(.unitNetAndCostPrice)
                                                			@(.psLineItemTotal)
                                                			@(.psMessagesItemDetail)"
													ignoreAutoUpdate="true" global="false" />
												<p:ajax event="rowEditCancel"
													listener="#{cc.attrs.value.onRowCancelLine}"
													update="@(.psInvoiceItemValidation) @(.psMessagesItemDetail)"
													global="false" />
												<p:ajax event="rowEditInit"
													listener="#{cc.attrs.value.onRowInit}"
													update="@(.psInvoiceValidation)" global="false" />
												<p:column headerText="#{gnxxcblinvlbls.indicator_coded}">
													<p:cellEditor>
														<f:facet name="output">
															<h:outputText
																value="#{gnxxcblinvlbls[alwcChrg.indicatorCoded]}" />
														</f:facet>
														<f:facet name="input">
															<p:selectOneMenu value="#{alwcChrg.indicatorCoded}"
																style="width:100%;margin-top:2px;" required="true"
																requiredMessage="#{gnxxcblinvlbls.indicator_coded} : #{gnxxcblcomlbls.value_required}">
																<f:selectItem />
																<f:selectItems
																	value="#{cc.attrs.value.lineIndicatorCodeItems}" />
															</p:selectOneMenu>
														</f:facet>
													</p:cellEditor>
												</p:column>
												<p:column headerText="#{gnxxcblinvlbls.description}">
													<p:cellEditor>
														<f:facet name="output">
															<h:outputText
																value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}" />
														</f:facet>
														<f:facet name="input">
															<h:inputText validator="xssValidator"
																value="#{alwcChrg.allowanceOrChargeDescription.listOfDescription.value}"
																styleClass="form-control input-sm" required="true"
																requiredMessage="#{gnxxcblinvlbls.description} : #{gnxxcblcomlbls.value_required}" />
														</f:facet>
													</p:cellEditor>
												</p:column>
												<p:column headerText="#{labels.type}">
													<p:cellEditor>
														<f:facet name="output">
															<h:outputText
																value="#{gnxxcblinvlbls[alwcChrg.basisCoded]}" />
														</f:facet>
														<f:facet name="input">
															<p:selectOneMenu value="#{alwcChrg.basisCoded}"
																style="width:100%;margin-top:2px;" required="true"
																requiredMessage="#{gnxxcblinvlbls.type} : #{gnxxcblcomlbls.value_required}">
																<f:selectItem />
																<f:selectItems value="#{cc.attrs.value.basisCodeItems}" />
																<f:ajax event="change" render="lineAmountOrPercent"
																	listener="#{cc.attrs.value.initAllowanceChargeWhenChangingBasisCoded(alwcChrg)}"
																	global="false" />
															</p:selectOneMenu>
														</f:facet>
													</p:cellEditor>
												</p:column>
												<p:column headerText="#{gnxxcblinvlbls.value}">
													<p:cellEditor id="lineAmountOrPercent">
														<f:facet name="output">
															<h:outputText
																rendered="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge != null}"
																value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}" />
															<h:outputText
																rendered="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue != null}"
																value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount.value}" />
														</f:facet>
														<f:facet name="input">
															<pe:inputNumber
																rendered="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge != null}"
																value="#{alwcChrg.typeOfAllowanceOrCharge.percentageAllowanceOrCharge.percent.value}"
																required="true"
																requiredMessage="#{gnxxcblinvlbls.value} : #{gnxxcblinvlbls.value_required}"
																styleClass="input-number-resize" maxValue="100.00"
																minValue="-100.00" symbol="%" symbolPosition="suffix" />
															<pe:inputNumber
																rendered="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue != null}"
																value="#{alwcChrg.typeOfAllowanceOrCharge.monetaryValue.monetaryAmount.value}"
																required="true"
																requiredMessage="#{gnxxcblinvlbls.value} : #{gnxxcblinvlbls.value_required}"
																styleClass="input-number-resize" />
														</f:facet>
													</p:cellEditor>
												</p:column>
												<p:column style="width:70px">
													<p:rowEditor style="float:left;" />
													<p:commandLink styleClass="ui-icon ui-icon-trash"
														action="#{cc.attrs.value.onRemoveLineAllowOrCharge(cc.attrs.value.product.invoicePricingDetail.itemAllowancesOrCharges.allowOrCharge, alwcChrg)}"
														process="@this"
														update="@(.psAllowOrChargeDlg)
		                                                    		@(.psAllowOrChrgLink)
		                                                    		@(.psInvoiceItemValidation)
		                                                    		@(.unitNetAndCostPrice)
		                                                    		@(.psLineItemTotal)
		                                                    		@(.psMessagesItemDetail)"
														title="#{gnxxcblinvlbls.remove_alloworchrg}" />
												</p:column>
											</p:dataTable>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								<p:outputPanel styleClass="row psInvoiceItemValidation"
									rendered="#{cc.attrs.value.product != null}">
									<p:outputPanel styleClass="col-md-12 right">
										<p:commandButton
											icon="#{cc.attrs.value.allowOrChrgLineEdit ? 'fa fa-lock fa-fw' : cc.attrs.value.isEdited(cc.attrs.value.product) ? 'fa fa-pencil fa-fw' : 'fa fa-plus fa-fw'}"
											styleClass="btn-success-bootstrap"
											value="#{cc.attrs.value.isEdited(cc.attrs.value.product) ? labels.modify : labels.add}"
											action="#{cc.attrs.value.saveProduct(cc.attrs.value.product)}"
											process="@(.psDetails)"
											update="@(.psDetails) @(.psSummary) @(.psInvoiceValidation) @(.psAllowOrCharge)"
											disabled="#{cc.attrs.value.allowOrChrgLineEdit}"
											ignoreAutoUpdate="true">
										</p:commandButton>
										<p:commandButton icon="fa fa-ban fa-fw"
											value="#{labels.cancel}"
											action="#{cc.attrs.value.onCancelProduct()}" process="@this"
											update="@(.psDetails) @(.psInvoiceValidation) @(.psAllowOrCharge)"
											styleClass="mls btn-danger-bootstrap" immediate="true">
											<p:resetInput target="invoice_details" />
										</p:commandButton>
									</p:outputPanel>
								</p:outputPanel>
							</p:panel>
						</p:outputPanel>
					</p:outputPanel>

					<!-- INVOICE SUMMARY -->
					<p:outputPanel styleClass="row">
						<p:outputPanel styleClass="col-md-12">
							<p:panel id="summary" header="#{gnxxcblinvlbls.summary}"
								toggleable="true" collapsed="true" toggleSpeed="500"
								style="margin-bottom:10px" styleClass="psSummary border-gnx">
								<p:outputPanel styleClass="row">
									<p:outputPanel styleClass="col-md-6">
										<p:dataTable id="listOfActualPayment"
											value="#{cc.attrs.value.actualPayments}" var="pay"
											editable="true" styleClass="psListOfActualPayment"
											emptyMessage="#{labels.no_records_found}">
											<f:facet name="header">
												<p:outputPanel styleClass="row invoice-summary-st">
													<p:outputPanel styleClass="col-md-8 col-md-offset-2">
														<h:outputText
															value="#{gnxxcblinvlbls.list_actual_payment}" />
													</p:outputPanel>
													<p:outputPanel styleClass="col-md-2">
														<p:commandButton value="#{labels.add}" immediate="true"
															process="@this"
															actionListener="#{cc.attrs.value.onAddActualPayment()}"
															styleClass="btn-gnx btn-gnx-ajouts"
															update="@(.psListOfActualPayment)"
															oncomplete="editLastDatatableRowRemoveCloseButton('listOfActualPayment')" />
													</p:outputPanel>
												</p:outputPanel>
											</f:facet>
											<p:column headerText="#{gnxxcblinvlbls.payment_date}">
												<p:cellEditor>
													<f:facet name="output">
														<p:calendar navigator="true" value="#{pay.paymentDate.value}"
															disabled="true" locale="#{gnxSessionHandler.locale}"
															timeZone="#{secSessionHandler.timeZone.ID}"
															pattern="#{gnxSessionHandler.userDateFormat}" />
													</f:facet>
													<f:facet name="input">
														<p:calendar id="paymentDate" navigator="true"
															readonlyInput="true" value="#{pay.paymentDate.value}"
															locale="#{gnxSessionHandler.locale}"
															timeZone="#{secSessionHandler.timeZone.ID}"
															pattern="#{gnxSessionHandler.userDateFormat}"
															styleClass="input-calendar-resize" required="true"
															requiredMessage="#{gnxxcblinvlbls.payment_date} : #{gnxxcblcomlbls.value_required}" />
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.ref_num}">
												<p:cellEditor>
													<f:facet name="output">
														<h:outputText value="#{pay.otherPaymentInfo.value}" />
													</f:facet>
													<f:facet name="input">
														<p:inputText value="#{pay.otherPaymentInfo.value}"
															style="width:100%" required="true"
															validator="xssValidator"
															requiredMessage="#{gnxxcblinvlbls.ref_num} : #{gnxxcblcomlbls.value_required}" />
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.payment_amount}"
												styleClass="right">
												<p:cellEditor>
													<f:facet name="output">
														<h:outputText
															value="#{pay.paymentAmount.invoiceCurrencyAmt.monetaryAmount.value}" />
													</f:facet>
													<f:facet name="input">
														<pe:inputNumber
															value="#{pay.paymentAmount.invoiceCurrencyAmt.monetaryAmount.value}"
															styleClass="input-number-resize" required="true"
															requiredMessage="#{gnxxcblinvlbls.payment_amount} : #{gnxxcblcomlbls.value_required}" />
													</f:facet>
												</p:cellEditor>
											</p:column>
											<p:column style="width:10%">
												<p:rowEditor style="float:left;" />
												<p:commandLink styleClass="ui-icon ui-icon-trash"
													action="#{cc.attrs.value.onRemoveActualPayment(pay)}"
													process="@this" update="@(.psListOfActualPayment)" />
											</p:column>
										</p:dataTable>
									</p:outputPanel>

									<p:outputPanel styleClass="col-md-6">
										<p:dataTable id="listOfTaxSummary"
											value="#{cc.attrs.value.taxSummaries}" var="tax"
											styleClass="psListOfTaxSummary"
											emptyMessage="#{labels.no_records_found}"
											rendered="#{not cc.attrs.value.taxExempt}">
											<f:facet name="header">
												<p:outputPanel styleClass="row invoice-summary-st">
													<p:outputPanel styleClass="col-md-12">
														<h:outputText value="#{gnxxcblinvlbls.list_tax_summary}" />
													</p:outputPanel>
												</p:outputPanel>
											</f:facet>
											<p:column headerText="#{gnxxcblinvlbls.tax_category}">
												<h:outputText value="#{tax.taxCategoryCodedOther.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.taxable_amount}"
												styleClass="right">
												<h:outputText value="#{tax.taxableAmount.value}" />
											</p:column>
											<p:column headerText="#{gnxxcblinvlbls.tax_amounty}"
												styleClass="right">
												<h:outputText
													value="#{tax.taxAmountInTaxAccountingCurrency.value}" />
											</p:column>
										</p:dataTable>
									</p:outputPanel>
								</p:outputPanel>

								<f:facet name="footer">
									<p:outputPanel styleClass="row psInvoiceSummary">
										<p:outputPanel styleClass="right">
											<p:outputPanel styleClass="col-md-4">
												<p:outputPanel styleClass="form-group"
													rendered="#{not cc.attrs.value.taxExempt}">
													<p:outputLabel for="taxableValue"
														styleClass="col-sm-6 control-label"
														style="font-weight: bold;"
														value="#{gnxxcblinvlbls.taxable_value} " />
													<pe:inputNumber id="taxableValue"
														styleClass="col-sm-6 input-number-resize"
														style="text-align:right; border: none;"
														value="#{invoice.invoiceSummary.invoiceTotals.taxableValue.monetaryAmount.value}"
														disabled="true"
														decimalPlaces="#{cc.attrs.value.footerPrecision}"
														minValue="-9999999.00" />
												</p:outputPanel>
											</p:outputPanel>
											<p:outputPanel styleClass="col-md-4">
												<p:outputPanel styleClass="form-group"
													rendered="#{not cc.attrs.value.taxExempt}">
													<p:outputLabel for="totalTaxAmount"
														styleClass="col-sm-6 control-label"
														style="font-weight: bold;"
														value="#{gnxxcblinvlbls.total_tax_amount} " />
													<pe:inputNumber id="totalTaxAmount"
														styleClass="col-sm-6 input-number-resize" disabled="true"
														style="text-align:right;border: none;"
														value="#{invoice.invoiceSummary.invoiceTotals.totalTaxAmount.monetaryAmount.value}"
														decimalPlaces="#{cc.attrs.value.footerPrecision}"
														minValue="-9999999.00" />
												</p:outputPanel>
											</p:outputPanel>
											<p:outputPanel styleClass="col-md-4">
												<p:outputPanel styleClass="form-group">
													<p:outputLabel for="invoiceTotal"
														styleClass="col-sm-6 control-label"
														style="font-weight: bold;"
														value="#{gnxxcblinvlbls.invoice_total}"
														rendered="#{not cc.attrs.value.taxExempt}" />
													<p:outputLabel for="invoiceTotal"
														styleClass="col-sm-6 control-label"
														style="font-weight: bold;"
														value="#{gnxxcblinvlbls.total_net_amount}"
														rendered="#{cc.attrs.value.taxExempt}" />
													<pe:inputNumber id="invoiceTotal" disabled="true"
														styleClass="col-sm-6 input-number-resize"
														style="text-align:right; border: none;"
														value="#{invoice.invoiceSummary.invoiceTotals.invoiceTotal.monetaryAmount.value}"
														decimalPlaces="#{cc.attrs.value.footerPrecision}"
														minValue="-9999999.00" />
												</p:outputPanel>
											</p:outputPanel>
										</p:outputPanel>
									</p:outputPanel>
								</f:facet>
							</p:panel>
						</p:outputPanel>
					</p:outputPanel>
				</p:outputPanel>
			</p:outputPanel>

			<!-- PANEL FOOTER -->
			<p:outputPanel styleClass="panel-footer">
				<p:outputPanel styleClass="right psInvoiceValidation">
					<ui:param name="notifyThenRedirect"
						value="$('body', parent.document).animate({scrollTop: 0});setTimeout(function(){if (!args.validationFailed){window.open('#{request.contextPath}#{previousPage}','_parent');}}, 2000);" />
					<p:dialog modal="true" widgetVar="inProgress"
						header="#{gnxxcblcomlbls.downloading}" draggable="false"
						closable="false" resizable="false">
						<p:graphicImage name="/images/ajaxloadingbar.gif" width="100%"
							height="19px" />
					</p:dialog>
					<p:commandButton id="idConfirmInvoice"
						styleClass="btn-gnx btn-color-company"
						disabled="#{cc.attrs.value.getValidationEnabled()}"
						onstart="needToConfirm=false;" process="@(.psInvoiceEdit) @this"
						value="#{labels.send}" update="@(.psInvoiceEdit)"
						rendered="#{not cc.attrs.value.editInvoiceInError}"
						action="#{cc.attrs.value.createINVDocument()}"
						oncomplete="#{notifyThenRedirect};">
					</p:commandButton>
					<p:commandButton id="idUpdateInvoice"
						styleClass="btn-gnx btn-color-company"
						disabled="#{cc.attrs.value.getValidationEnabled()}"
						onstart="needToConfirm=false;" process="@(.psInvoiceEdit)"
						value="#{labels.modify}" update="@(.psDocumentPnl)"
						rendered="#{cc.attrs.value.editInvoiceInError}"
						actionListener="#{cc.attrs.value.updateInvoiceDocument()}">
					</p:commandButton>
					<p:button value="#{labels.cancel}"
						styleClass="mls btn-gnx btn-gnx-default" outcome="#{gnxPortalSessionHandler.getDocumentId()==-1 ? '/ui/portal' : cc.attrs.value.getPreviousTaskUrl(gnxPortalSessionHandler.portal)}"
						target="_parent"/>
				</p:outputPanel>
			</p:outputPanel>
		</p:outputPanel>
		
		<ui:include src="customer_view.xhtml" />
		
		</p:panel>
		<gnx:errorMessage rendered="#{!cc.attrs.value.isPartnerUserConnected() and !cc.attrs.value.editInvoice}"
                          msg="#{gnxxcblinvlbls.error_create_invoice}" />
	</cc:implementation>
</ui:component>