<p:panel xmlns="http://www.w3.org/1999/xhtml"
	xmlns:b="http://byzaneo.com/ui" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:wkf="http://xmlns.jcp.org/jsf/composite/components/gnx/workflow"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
	<p:outputPanel styleClass="psWorkflowTab" rendered="#{cc.attrs.value.isWorkflowProcessNone() and cc.attrs.value.activeTabIndex == 0}">
		<p:outputPanel>
			<p:outputPanel styleClass="row">
				<p:outputPanel styleClass="col-md-10">
					<g:search id="searchWorkflowMonitoring" taskBean="#{taskBean}" />
				</p:outputPanel>
				<p:outputPanel styleClass="col-md-2">
					<p:selectOneMenu value="#{cc.attrs.value.legalEntityValue}"
						styleClass="wkfChoosePartnerSelectOneMenu" filter="true"
						filterMatchMode="contains" id="selectLegalEntityMonitoring"
						rendered="#{cc.attrs.value.isPartnerWithPerimeterOrClient()}"
						process="@this" onchange="filterByPartnerCode();"
						panelStyleClass="wkfChoosePartnerPanel">
						<f:selectItem itemLabel="#{cc.attrs.value.isClientWithoutPerimeter() ? gnxworkflowlbls.selection_legal_entity : '' }"
							itemDisabled="#{!cc.attrs.value.isClientWithoutPerimeter()}" 
							itemValue="null" />
						<f:selectItems value="#{cc.attrs.value.getLegalEntityValues()}" />
					</p:selectOneMenu>
				</p:outputPanel>
			</p:outputPanel>
			<p:spacer />
			<p:outputPanel styleClass="resultTablePnl">
				<p:dataTable id="resultTableMonitoring" styleClass="psWorkflowTable"
					widgetVar="wResultTable"
					value="#{cc.attrs.value.workflowDataModel}" var="doc" rows="15"
					paginator="true" paginatorPosition="bottom" scrollable="false"
					resizableColumns="true" rowKey="#{doc.id}"
					emptyMessage="#{labels.no_records_found}" rowIndexVar="rowIndex"
					draggableRows="#{gnxSessionHandler.isGranted('Workflow_Monitoring', 'ADMIN') and not cc.attrs.value.draggableRows}" selectionMode="single"
					selection="#{cc.attrs.value.selectedWorkflow}"
					paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
					currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
					rowsPerPageTemplate="15,50,100,500" pageLinks="11" lazy="true">

					<p:ajax event="rowReorder"
						listener="#{cc.attrs.value.onReorderWorkflows}"
						update="resultTableMonitoring" />
					<p:ajax event="rowSelect"
						listener="#{cc.attrs.value.onReadWorkflow}"
						update="@(.psWorkflowGlobalPnl)" process="@this"
						oncomplete="PF('wDlgReadWorkflow').show()" />


					<!-- * COLUMNS * -->
					<p:column styleClass="center actions-column" exportable="false">
						<f:facet name="header">
							<p:outputPanel styleClass="psActions">
								<!-- Global actions -->
								<p:commandButton id="actionsMonitoring"
									styleClass="psActionsBtn globalActionsButton"
									title="#{labels.actions}" icon="fa fa-ellipsis-h" type="button" />
								<p:tieredMenu id="globalMenuMonitoring"
									styleClass="xcbltask-tieredmenu jqsactions" overlay="true"
									trigger="actionsMonitoring" my="left top" at="left bottom">
									<p:menuitem id="itmAddMonitoring"
										actionListener="#{cc.attrs.value.onAddWorkflow()}"
										value="#{labels.add}" process="@this"
										rendered="#{gnxSessionHandler.isGranted('Workflow_Monitoring', 'CREATE')}"
										onclick="$('.jqsactions').hide()" update="@(.psDocumentPnl)">
									</p:menuitem>
								</p:tieredMenu>
							</p:outputPanel>
						</f:facet>
						<p:commandButton id="actionWorkflowMonitoring" type="button"
							styleClass="invoiceActionButton" icon="fa fa-ellipsis-h" />
						<p:tieredMenu id="columnTieredMenuMonitoring"
							styleClass="psMenuExport xcbltask-tieredmenu" overlay="true"
							trigger="actionWorkflowMonitoring" my="left top" at="left bottom"
							style="width:290px;">
							<p:menuitem id="itmEditMonitoring"
								actionListener="#{cc.attrs.value.onEditWorkflow(doc)}"
								value="#{gnxworkflowlbls.modify_workflow}" 
								process="@this"
								rendered="#{gnxSessionHandler.isGranted('Workflow_Monitoring', 'UPDATE')}"
								onclick="$('.psMenuExport').hide()"
								update="@(.psDocumentPnl)">
							</p:menuitem>
							<p:menuitem id="itmDuplicateEditMonitoring"
								actionListener="#{cc.attrs.value.onDuplicateAndEditWorkflow(doc)}"
								value="#{gnxworkflowlbls.duplicate_and_modify_workflow}"
								process="@this"
								rendered="#{gnxSessionHandler.isGranted('Workflow_Monitoring', 'EXECUTE')}"
								onclick="$('.psMenuExport').hide()"
								update="@(.psDocumentPnl)">
							</p:menuitem>
							<p:menuitem id="itmReplaceWithNewVersionMonitoring"
								actionListener="#{cc.attrs.value.onReplaceWithANewVersion(doc)}"
								value="#{gnxworkflowlbls.replace_workflow}"
								process="@this"
								rendered="#{gnxSessionHandler.isGranted('Workflow_Monitoring', 'EXECUTE')}"
								onclick="$('.psMenuExport').hide()"
								update="@(.psDocumentPnl)">
							</p:menuitem>
							<p:menuitem id="itmDisableMonitoring"
								value="#{gnxworkflowlbls.disable_workflow}"
								rendered="#{not cc.attrs.value.isWorkflowDisabled(doc) and gnxSessionHandler.isGranted('Workflow_Monitoring', 'EXECUTE')}"
								actionListener="#{cc.attrs.value.onDisableWorkflow(doc)}"
								process="@this" onclick="$('.psMenuExport').hide()"
								update="@(.psDocumentPnl)">
							</p:menuitem>
							<p:menuitem id="itmDeleteMonitoring"
								value="#{gnxworkflowlbls.delete_workflow}"
								actionListener="#{cc.attrs.value.onDeleteWorkflow(doc)}"
								process="@this" onclick="$('.psMenuExport').hide()"
								update="@(.psDocumentPnl)"
								rendered="#{cc.attrs.value.isWorkflowDisabled(doc) and gnxSessionHandler.isGranted('Workflow_Monitoring', 'DELETE')}">
							</p:menuitem>
							<p:menuitem id="itmEnableMonitoring"
								value="#{gnxworkflowlbls.enable_workflow}"
								rendered="#{cc.attrs.value.isWorkflowDisabled(doc) and gnxSessionHandler.isGranted('Workflow_Monitoring', 'EXECUTE')}"
								actionListener="#{cc.attrs.value.onEnableWorkflow(doc)}"
								process="@this" onclick="$('.psMenuExport').hide()"
								oncomplete="PF('wEnableWorkflowDialog').show();">
							</p:menuitem>
						</p:tieredMenu>
					</p:column>

					<p:column headerText="#{gnxworkflowlbls.name}"
						styleClass="idx-col-text">
						<h:outputText value="#{doc.name}" title="#{doc.name}" />
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.version}"
						styleClass="idx-col-text">
						<h:outputText value="#{doc.version}" title="#{doc.version}" />
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.collection}"
						styleClass="idx-col-text">
						<h:outputText value="#{doc.collection}" title="#{doc.collection}" />
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.numberOfSteps}"
						styleClass="idx-col-text">
						<h:outputText value="#{doc.workflowSteps.size()}"
							title="#{doc.workflowSteps.size()}" />
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.description}"
						styleClass="idx-col-text">
						<h:outputText value="#{doc.description}"
							title="#{doc.description}" />
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.endDate}"
						styleClass="idx-col-text">
						<h:outputText value="#{doc.endDate}" title="#{doc.endDate}">
							<f:convertDateTime locale="#{gnxSessionHandler.locale}"
								type="date" timeZone="#{secSessionHandler.timeZone}"
								pattern="#{gnxSessionHandler.userDateFormat}" />
						</h:outputText>
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.lastModifiedBy}"
							  styleClass="idx-col-text">
						<h:outputText value="#{doc.lastModifiedBy}"
									  title="#{doc.lastModifiedBy}" />
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.modificationDate}"
							  styleClass="idx-col-text">
						<h:outputText value="#{doc.modificationDate}" title="#{doc.modificationDate}">
							<f:convertDateTime locale="#{gnxSessionHandler.locale}"
											   type="date" timeZone="#{secSessionHandler.timeZone}"
											   pattern="#{gnxSessionHandler.userDateFormat}" />
						</h:outputText>
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.lastUseDate}"
							  styleClass="idx-col-text">
						<h:outputText value="#{doc.lastUseDate}" title="#{doc.lastUseDate}">
							<f:convertDateTime locale="#{gnxSessionHandler.locale}"
											   type="date" timeZone="#{secSessionHandler.timeZone}"
											   pattern="#{gnxSessionHandler.userDateFormat}" />
						</h:outputText>
					</p:column>
					<p:column headerText="#{gnxworkflowlbls.numberOfUses}"
							  styleClass="idx-col-text">
						<h:outputText value="#{doc.numberOfUses}"
									  title="#{doc.numberOfUses}" />
					</p:column>

				</p:dataTable>
			</p:outputPanel>
		</p:outputPanel>
		<p:dialog id="enableWorkflowDialogMonitoring" widgetVar="wEnableWorkflowDialog"
			styleClass="responsive-popup psEnableWorkflow" draggable="true"
			resizable="false" modal="true" closable="true"
			header="#{gnxworkflowlbls.enable_workflow_message}"
			onShow="if(typeof centerDialogToWindow === 'function') {centerDialogToWindow($('.psEnableWorkflow'));}">

			<p:outputPanel styleClass="col-md-12 right">
				<p:commandButton value="#{labels.no}"
					oncomplete="PF('wEnableWorkflowDialog').hide();"
					update="@(.psWorkflowTab)"
					actionListener="#{cc.attrs.value.onCancelEnableWorkflow()}"
					process="@this" styleClass="btn btn-secondary" immediate="true"
					global="false">
				</p:commandButton>
				<p:commandButton styleClass="btn btn-primary" immediate="true"
					global="false" value="#{labels.yes}" process="@this"
					actionListener="#{cc.attrs.value.enableWorkflow()}"
					update="@(.psWorkflowTab)"
					oncomplete="PF('wEnableWorkflowDialog').hide();">
				</p:commandButton>
			</p:outputPanel>
		</p:dialog>

		<p:remoteCommand name="filterByPartnerCode"
			process="@this selectLegalEntityMonitoring"
			onstart="PF('wResultTable').filter()"
			actionListener="#{cc.attrs.value.onAdvancedSearchModel()}"
			update="@(.resultTableMonitoring) @(.psActions)" />
	</p:outputPanel>
</p:panel>