<?xml version="1.0" encoding="UTF-8"?>
<descriptor name="reconciliationManagement" sortBy="sortNumber" sortAsc="true" version="1">
	<properties>
		<property name="name"
				  type="java.lang.String" rendered="true" render-restrict="None"
				  readonly="true" editable="false" required="false" sortable="false" searchable="true">
			<label>name</label>
		</property>
		<property name="description" render-restrict="None"
				  type="java.lang.String" rendered="true" readonly="true" editable="false"
				  required="false" sortable="false" searchable="true">
			<label>description</label>
		</property>
		<property editable="true" path="status" name="status"
				  readonly="false"  rendered="true" render-restrict="None"  required="false"
				  sortable="false" searchable="false" type="com.byzaneo.xtrade.api.DocumentStatusEntityInterface">
			<label>status</label>
			<options-expression>#{cc.attrs.taskBean.getValues('status')}</options-expression>
		</property>
		<property name="lastModifiedBy"
				  type="java.lang.String" rendered="true" render-restrict="None"
				  readonly="true" editable="false" required="false" sortable="false" searchable="true">
			<label>lastModifiedBy</label>
		</property>
	</properties>
</descriptor>
