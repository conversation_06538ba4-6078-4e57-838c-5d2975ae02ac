<?xml version="1.0" encoding="UTF-8"?>
<descriptor name="Contract" sortBy="shippingScheduleHeader.scheduleID" sortAsc="true" version="8">
    <properties>
        <property
                name="listOfMaterialGroupedShippingDetail.materialGroupedShippingDetail[0].baseShippingDetail.itemIdentifiers.partNumbers.sellerPartNumber.partID"
                type="java.lang.String" rendered="true" render-restrict="Partners" readonly="false" editable="true"
                required="false" sortable="false">
            <label>productCode</label>
        </property>
        <property
                name="listOfMaterialGroupedShippingDetail.materialGroupedShippingDetail[0].baseShippingDetail.itemIdentifiers.itemDescription"
                type="java.lang.String" rendered="true" render-restrict="Partners" readonly="false" editable="true"
                required="false" sortable="false" converter="descriptionConverter">
            <label>productDescription</label>
        </property>
        <property name="shippingScheduleHeader.scheduleID" type="java.lang.String" rendered="true" readonly="false"
                  editable="false" required="false" sortable="true">
            <label>contractNumber</label>
        </property>
        <property
                name="listOfMaterialGroupedShippingDetail.materialGroupedShippingDetail[0].baseShippingDetail.itemScheduleReference.contractReferences.contract[0].validityDates.startDate"
                type="java.lang.String" rendered="true" readonly="false" editable="false" required="false"
                sortable="false">
            <label>contractBegin</label>
        </property>
        <property
                name="listOfMaterialGroupedShippingDetail.materialGroupedShippingDetail[0].baseShippingDetail.itemScheduleReference.contractReferences.contract[0].validityDates.endDate"
                type="java.lang.String" rendered="true" readonly="false" editable="false" required="false"
                sortable="false">
            <label>contractEnd</label>
        </property>
        <property name="shippingScheduleHeader.termsOfDelivery.termsOfDeliveryDescription" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>salesCondition</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.buyerParty.partyID.ident" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>buyerPartyIdent</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.buyerParty.nameAddress.name1" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>buyerPartyName1</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.sellerParty.partyID.ident" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>sellerPartyIdent</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.sellerParty.nameAddress.name1" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>sellerPartyName1</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.shipFromParty.partyID.ident" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>shipFromIdent</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.shipFromParty.nameAddress.city" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>shipFromCity</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.shipToParty.partyID.ident" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>shipToIdent</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.shipToParty.nameAddress.name1" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>shipToName1</label>
        </property>
        <property name="shippingScheduleHeader.scheduleParty.shipToParty.nameAddress.city" type="java.lang.String"
                  rendered="true" readonly="false" editable="true" required="false" sortable="false">
            <label>shipToCity</label>
        </property>
        <property
                name="shippingScheduleHeader.listOfTransportRouting.transportRouting[0].transportMeans.transportMeansCoded"
                type="java.lang.String" rendered="true" readonly="false" editable="true" required="false"
                sortable="false">
            <label>transportType</label>
        </property>
        <property name="from" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>from</label>
        </property>
        <property name="to" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>to</label>
        </property>
        <property name="creationDate" type="java.util.Date" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>creationDate</label>
        </property>
        <property name="modificationDate" type="java.util.Date" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>modificationDate</label>
        </property>
        <property name="reference" type="java.lang.String" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>reference</label>
        </property>
        <property name="owners" type="java.lang.String" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>owners</label>
        </property>
        <property name="status" type="com.byzaneo.xtrade.api.DocumentStatusEntityInterface" rendered="true"
                  readonly="false" editable="true" required="false" sortable="true">
            <label>status</label>
        </property>
        <property name="type" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>type</label>
        </property>
        <property name="id" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>id</label>
        </property>
        <property editable="true" path="consultStatus" name="consultStatus"
                  readonly="false" render-restrict="None" rendered="false" required="false"
                  sortable="true" type="com.byzaneo.xtrade.api.DocumentConsultStatus"
                  styleClass="iconColumn">
            <label>consultStatus</label>
            <options-expression>#{cc.attrs.taskBean.getValues('consultStatus')}</options-expression>
        </property>
        <property name="acquisition" type="com.byzaneo.xtrade.bean.Document.AcquisitionType" rendered="false"
                  render-restrict="None"
                  readonly="true" path="acquisition" editable="false" required="false" sortable="true">
            <label>acquisition</label>
            <options-expression>#{cc.attrs.taskBean.getValues('acquisition')}</options-expression>
        </property>
        <property editable="true" name="processingWay" path="processingWay"
                  readonly="false" render-restrict="None" rendered="false" required="false"
                  sortable="true" type="com.byzaneo.xtrade.bean.Document.ProcessingWay">
            <label>processingWay</label>
            <options-expression>#{cc.attrs.taskBean.getValues('processingWay')}</options-expression>
        </property>
        <property name="translator" type="java.lang.String" sortable="false" rendered="false">
            <label>translator</label>
        </property>
        <property name="transport" type="java.lang.String" sortable="false" rendered="false">
            <label>transport</label>
        </property>
        <property name="nature" type="java.lang.String" sortable="false" rendered="false" required="false">
            <label>nature</label>
        </property>
        <property name="immatpdp" type="java.lang.String" sortable="false" rendered="false" required="false">
            <label>immatpdp</label>
        </property>
        <property name="b2gRules" type="java.lang.String" sortable="false" rendered="false" required="false">
            <label>b2gRules</label>
        </property>
        <property name="ereportingOption" type="java.lang.String" sortable="false" rendered="false" required="false">
            <label>ereportingOption</label>
        </property>
        <property name="countryRules1" type="java.lang.String" sortable="false" rendered="false" required="false">
            <label>countryRules1</label>
        </property>
        <property name="countryRules2" type="java.lang.String" sortable="false" rendered="false" required="false">
            <label>countryRules2</label>
        </property>
    </properties>
</descriptor>