package com.byzaneo.generix.xcbl.portal.task;

import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.generix.bean.ValuesControl.LIST_ID;
import static com.byzaneo.generix.commons.task.util.LegalControlsTaskHelper.createTimelineByConstraint;
import static com.byzaneo.generix.edocument.bean.ResultingFileAction.ADD_FILE_TO_INVOICE;
import static com.byzaneo.generix.edocument.bean.ResultingFileAction.CLIENT_READABLE;
import static com.byzaneo.generix.edocument.ui.DocumentViewerHandler.toURI;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.addAllowOrChargeLine;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getAllowOrChargeFromSelectedDetail;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getPercentageAllowanceOrChargeType;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.updateIndicatorCodedOfAllowOrCharge;
import static com.byzaneo.generix.edocument.util.EDocumentServiceHelper.getExistingPdfFile;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.computeInvoiceTaxOnComputedPrice;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.setRightChargeAllowanceIndicatorCodeForLine;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getProductIdentifier;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getQuantity;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.obtainPartId;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.obtainProductIdentifier;
import static com.byzaneo.generix.edocument.util.InvoicePriceXcblHelper.buildInvoicePriceType;
import static com.byzaneo.generix.edocument.util.InvoicePriceXcblHelper.getUnitPriceValue;
import static com.byzaneo.generix.edocument.util.InvoiceReferenceXcblHelper.getPeriodEndDateFromInvoiceReference;
import static com.byzaneo.generix.edocument.util.InvoiceReferenceXcblHelper.getPeriodStartDateFromInvoiceReference;
import static com.byzaneo.generix.edocument.util.InvoiceReferenceXcblHelper.mapInvoiceReferenceToXcblType;
import static com.byzaneo.generix.edocument.util.InvoiceReferenceXcblHelper.unmapInvoiceReferenceToXcblType;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.BILL_TO;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.BUYER;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.BUYER_AGENT;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.DATE_OF_OPERATIONS;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.DECLARANTS_AGENT;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.DISCOUNT_PAYMENT;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.FACTOR;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.HEAD_OFFICE;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.INVOICDIR;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.LATE_PAYMENT;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.ORDER_RESPONSE_NUMBER;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.PAYER;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.PAY_TO;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.PENALTY_TERMS;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.REMIT_TO;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SELLER;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SELLER_AGENT;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SHIP_FROM;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SHIP_TO;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.applyAllowOrChrgOnTaxableValue;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.applyTaxOnHeaderAllowanceCharge;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.applyTaxOnInvoiceItemDetails;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.attachInvoiceDocument;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.clearLegalFormAndShareCapital;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.clearPartyValues;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.computeAmountToBePaid;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.computeInvoiceTotal;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.computeInvoiceTotalTaxable;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.createInvoiceDocumentFromParent;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.createInvoiceTaxSummary;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.createParafiscalTaxSummaries;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getInvoiceCurrency;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getInvoiceTotals;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getTypeAddress;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initInvoiceWithASN;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initInvoiceWithOrderStandard;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initLegalFormAndShareCapital;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initNewInvoice;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.obtainToFromXcbl;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.populateInvoiceDates;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.setTotalAmountPayable;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.updateLegalFormAndShareCapital;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.updatePartyWithInformationFromPartner;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.updateSupplierContactInfo;
import static com.byzaneo.generix.edocument.util.PartyCodeXcblHelper.createInvoicePartyCoded;
import static com.byzaneo.generix.edocument.util.PartyCodeXcblHelper.getFactorPartyIdent;
import static com.byzaneo.generix.edocument.util.PartyCodeXcblHelper.getSellerPartyIdent;
import static com.byzaneo.generix.edocument.util.TaxSummaryXcblHelper.populateInvoiceParafiscalTaxSummary;
import static com.byzaneo.generix.edocument.util.TaxSummaryXcblHelper.populateInvoiceTaxSummary;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.generix.edocument.util.XcblHelper.creatActualPayement;
import static com.byzaneo.generix.edocument.util.XcblHelper.createOrUpdateXcblFile;
import static com.byzaneo.generix.edocument.validation.bean.ConstraintType.FR_B2B;
import static com.byzaneo.generix.edocument.validation.bean.LegislationType.PROPERTY_BASED;
import static com.byzaneo.generix.service.repository.bean.address.Address.FIELD_IDENTIFICATION;
import static com.byzaneo.generix.service.repository.bean.address.Address.FIELD_TYPE;
import static com.byzaneo.generix.util.DocumentCompoundTypeHelper.createDocumentCompoundType;
import static com.byzaneo.generix.util.DocumentCompoundTypeHelper.fromJson;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionHelper.computeDecimalPlaces;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionHelper.getSubTotalMonetaryAmmount;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionHelper.getSubTotalMonetaryAmmountOptional;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionHelper.hasExemptWithValue;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionHelper.initDateOfOperation;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceEditionHelper.initInvoiceDateCoded;
import static com.byzaneo.generix.xcbl.portal.task.util.OrderingPartyHelper.getOrderingPartyConfiguration;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.security.spring.UserDetailsService.getUserScopeCodes;
import static com.byzaneo.xtrade.api.DocumentStatus.INVOICED;
import static com.byzaneo.xtrade.api.DocumentStatus.READ;
import static com.byzaneo.xtrade.api.DocumentStatus.SENT;
import static com.byzaneo.xtrade.api.DocumentStatus.SENT_PARTIALLY;
import static com.byzaneo.xtrade.api.DocumentType.DESADV;
import static com.byzaneo.xtrade.api.DocumentType.INVOIC;
import static com.byzaneo.xtrade.api.DocumentType.ORDERS;
import static com.byzaneo.xtrade.api.DocumentType.ORDRSP;
import static com.byzaneo.xtrade.api.IndexableDocument.FIELD_STAGE;
import static com.byzaneo.xtrade.api.IndexableDocument.FIELD_STATUS;
import static com.byzaneo.xtrade.bean.Document.AcquisitionType.PORTAL;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.ATTACHFILE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.CTRL_DUPLICATE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.REFUSE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.USER_SYSTEM;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.WRITE_ASYNC;
import static com.byzaneo.xtrade.util.DocumentHelper.getIndexValueIdOrReference;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getComplexStringValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getEndDateFromInvoiceValidityDatesType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getIdentValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceNumber;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getListOfDescriptionFromInvoiceAllowanceOrCharge;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getStartDateFromInvoiceValidityDatesType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxableValueFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTotalTaxAmountFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.ALLOWANCE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.CHARGE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.SERVICE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageCodeType.fromValue;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_GROSS;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_NET;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.NET_ITEM_PRICE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.UNIT_COST_PRICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.COMMERCIAL_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.CORRECTED_FACTORED_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.CORRECTED_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.CREDIT_NOTE_GOODS_AND_SERVICES;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.FACTORED_CREDIT_NOTE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.PREPAYMENT_INVOICE_CREDIT_NOTE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.SELF_BILLED_CREDIT_NOTE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.SELF_BILLED_FACTORED_CREDIT_NOTE;
import static java.lang.Long.valueOf;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.HALF_EVEN;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toList;
import static javax.faces.application.FacesMessage.SEVERITY_ERROR;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.apache.commons.lang3.time.DateUtils.addDays;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.springframework.util.Assert.notNull;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.*;

import javax.faces.application.FacesMessage;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.component.*;
import javax.faces.context.FacesContext;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.model.SelectItem;
import javax.persistence.PostLoad;
import javax.xml.bind.JAXBException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.primefaces.component.autocomplete.AutoComplete;
import org.primefaces.component.tabview.*;
import org.primefaces.context.RequestContext;
import org.primefaces.event.*;
import org.primefaces.model.UploadedFile;
import org.slf4j.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.DocumentCompoundType;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.bean.chorus.ChorusParty;
import com.byzaneo.generix.bean.chorus.service.Service;
import com.byzaneo.generix.bean.chorus.structure.Structure;
import com.byzaneo.generix.commons.task.util.DuplicateInvoiceHelper;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.repository.AttachedFileRepository;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.generix.edocument.validation.ConstraintsParser;
import com.byzaneo.generix.edocument.validation.bean.*;
import com.byzaneo.generix.edocument.validation.validator.XCBLValidator;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.TransformService.BirtOutoutFileType;
import com.byzaneo.generix.service.chorus.ChorusProService;
import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.generix.service.repository.bean.*;
import com.byzaneo.generix.service.repository.bean.address.*;
import com.byzaneo.generix.service.repository.service.RepositoryService;
import com.byzaneo.generix.service.repository.util.product.AllowanceOrCharge;
import com.byzaneo.generix.util.InstanceHelper;
import com.byzaneo.generix.xcbl.api.*;
import com.byzaneo.generix.xcbl.editable.*;
import com.byzaneo.generix.xcbl.portal.task.bean.InvoiceMessageConfiguration;
import com.byzaneo.generix.xcbl.portal.task.converter.*;
import com.byzaneo.generix.xcbl.portal.task.util.*;
import com.byzaneo.generix.xcbl.ui.TimelineHandler;
import com.byzaneo.generix.xcbl.ui.edition.InvoiceEdition;
import com.byzaneo.generix.xtrade.util.IndexableTaskHelper;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.*;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.bean.TaskDefinition;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.transform.engine.InputSource;
import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document.AcquisitionType;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction;
import com.byzaneo.xtrade.ipm.bean.Deployment;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.api.XcblOrderingParty;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import com.rits.cloning.Cloner;

import lombok.*;

@Task(name = "StandardInvoiceEdition", library = "portal/xcbl", version = "1.0.0")
@TaskViewModel(labelFamily = StandardInvoiceEditionTask.LABEL_FAMILY)
public class StandardInvoiceEditionTask extends AbstractXcblDocumentTask<InvoiceIndex> implements InvoiceEdition {
  private static final long serialVersionUID = 878896775172600972L;

  protected static final Logger log = LoggerFactory.getLogger(StandardInvoiceEditionTask.class);

  public static final String LABEL_FAMILY = "gnxxcblinvlbls";

  private transient OrderResponse ordRspLinked;

  private Order orderLinked;

  private boolean duplicateInvoiceNumber;

  private List<TaxMandatory> taxMandatories;

  private BeanDescriptor detailDescriptor;

  private transient Map<String, InvoiceReference> mapInvoiceReferences = new HashMap<>();

  private int detailPrecision = 2;

  private int footerPrecision = 2;

  private transient boolean editInvoiceInError;

  public transient static final String[] invoiceTypeExclude = { "METERED_SERVICES_INVOICE", "INVOICING_DATA_SHEET",
      "COMMISSION_NOTE", "DELCREDERE_INVOICE", "CREDIT_MEMO", "DETOUR_BILLING",
      "THIRD_PARTY_CONSOLIDATED_INVOICE", "DEBIT_INVOICE", "DEBIT_MEMO" };

  private transient List<InvoicePartyCodedType> listOfPartyCodedTabs;

  private transient List<InvoiceDateCodedType> listOfDateCoded;

  private transient List<InvoicePartyCodedType> listOfPartyCoded;

  private transient int activeIndex;

  private transient Invoice invoiceXcbl;

  private transient Invoice originalXcbl;

  public transient static final String[] DEFAULT_PARTY = { BILL_TO, BUYER, SELLER, SHIP_TO };

  public transient static final String[] INVOICE_EDITION_PARTY = { BILL_TO, BUYER, SELLER, SHIP_TO, SHIP_FROM, REMIT_TO,
      DECLARANTS_AGENT, HEAD_OFFICE, FACTOR, BUYER_AGENT, PAYER, SELLER_AGENT };

  public transient static final String[] OTHER_PARTIES = { HEAD_OFFICE, DECLARANTS_AGENT, FACTOR,
      PAY_TO, REMIT_TO, BUYER_AGENT, PAYER, SELLER_AGENT };

  public transient static final String[] INVOICE_CHORUS_PARTY = { BUYER, SELLER };

  public transient static final String VAT_EXONERATION = "VAT_EXONERATION";

  public transient static final String PARAFISCAL_TAX = "PARAFISCAL_TAX";

  public transient static final int MAX_FILE_SIZE_BYTES = Integer.MAX_VALUE;

  private EditableInvoiceProperties editableProperties;

  private RelatedInvoiceRefType relatedInvoiceRef;

  @Autowired
  @Qualifier(RepositoryService.SERVICE_NAME)
  private transient RepositoryService repositoryService;

  @Autowired
  @Qualifier(InvoiceService.SERVICE_NAME)
  private transient InvoiceService invoiceService;

  @Autowired
  @Qualifier(DocumentTimelineService.SERVICE_NAME)
  private transient DocumentTimelineService documentTimelineService;

  @Autowired
  private transient OrderService orderService;

  @Autowired
  @Qualifier(DocumentViewerService.SERVICE_NAME)
  private transient DocumentViewerService documentViewerService;

  @Autowired
  @Qualifier(ChorusProService.SERVICE_NAME)
  private transient ChorusProService chorusProService;

  @Autowired
  @Qualifier(BankAccountService.SERVICE_NAME)
  private transient BankAccountService bankAccountService;

  @Autowired
  @Qualifier(ValuesControlService.SERVICE_NAME)
  private transient ValuesControlService valuesControlService;

  @Autowired
  @Qualifier(WorkflowService.SERVICE_NAME)
  private transient WorkflowService workflowService;

  @Autowired
  @Qualifier(WorkflowDocumentStatusService.SERVICE_NAME)
  private transient WorkflowDocumentStatusService workflowDocumentStatusService;

  @Autowired
  @Qualifier(WorkflowNotificationService.SERVICE_NAME)
  private transient WorkflowNotificationService workflowNotificationService;

  @Autowired
  @Qualifier(AttachedFileRepository.BEAN_NAME)
  private transient AttachedFileRepository attachedFileRepository;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  @Qualifier(IntegrityService.SERVICE_NAME)
  private transient IntegrityService integrityService;

  private static final Cloner cloner = new Cloner();

  private transient InvoicePartyCodedType selectedParty;

  private transient boolean newInvoice;

  private transient boolean taxExempt;

  /* Used to avoid any modifications for taxExempt field occurrences in Invoice Edition in order to disable computations for tax exempt. */
  private transient boolean taxExemptEditInvoice;

  private transient List<InvoiceItemDetailType> details;

  private transient Map<String, InvoiceTaxSummaryType> taxSummaries;

  private transient Map<String, TotalAllowOrChargeType> parafiscalTaxSummaries;

  private transient List<TotalAllowOrChargeType> parafiscalTaxSummariesEditInvoice;

  private transient List<InvoiceAllowOrChargeType> allowOrChargeTypes;

  private transient String currentInvoiceReferenceType;

  private transient boolean invoiceReferenceEdit;

  private transient List<InvoiceReference> invoiceReferences;

  private transient InvoiceReference currentInvoiceReference;

  private transient final Collection<SelectItem> invoiceReferenceTypes = Collections
      .unmodifiableList(stream(InvoiceReferenceType.values())
          .filter(ref -> !ref.name()
              .equals(InvoiceReferenceType.INVOICE.toString()))
          .map(reference -> new SelectItem(reference.name(), i18nInvoiceReferenceType(reference)))
          .collect(toList()));

  private transient String currentAllowOrChrg;

  private transient Map<String, ObjectAttribute> listOfGlobalTax;

  private transient boolean allowOrChrgEdit;

  private transient Map<String, Tax> listCodeTva;

  private transient static final String TAX_FORMAT = "%.0" + TAX_PRECISION + "f";

  private static final String NEW = "New";

  private static final String NEW_TAB = "+";

  private final static String RECEIVING_NUMBER = "ReceivingNumber";

  public static final String INVOICE_POST_VALIDATION = "invoiceValidation";

  private boolean showPdf = true;

  private boolean documentHasNoPdf;

  private String statusAfterInvoiceModification;

  private String outputDir = "out/{owner}/invoice";

  private transient String productSelected;
  @Getter
  @Setter
  private String documentProperties;
  private transient InvoiceItemDetailType product;

  private transient List<InvoiceItemDetailType> detailsBackup;

  private transient IndicatorCodeType currentAllowOrChrgLine;

  private transient Map<String, IndicatorCodeType> listOfTaxLine;

  private transient List<InvoiceActualPaymentType> actualPayments;

  private transient boolean lockForMe;

  private transient XcblEditionBuilder.XcblEdition xcblEdition;

  // freeText

  private FreeTextProperties freeTextProperties;

  // edit invoice

  private boolean editInvoice;

  private transient List<InvoiceTaxSummaryType> taxSummariesEditInvoice;

  // enables addition of products or allowances or charges
  private transient boolean allowAddition;

  private transient int netPriceDecimalPlaces;

  private transient int grossPriceDecimalPlaces;

  private transient int netPriceIndex;

  private transient int grossPriceIndex;

  private int defaultValueForMaxNumberOfDaysDelay = 30;

  // used to display message in case at least one field has color when clicking on "Invoice" button when modifying an invoice for the first
  // time
  private transient boolean ocrFound;

  // when clicking on "Invoice" button when you modify the invoice for the first time in case there are some "ocrRate" attributes in the xml
  // they must disappear, but in case the ocrRate is greater than 0.85 then the field has no color
  private transient boolean ocrFoundNoColorDisplayed;

  private boolean linkToAddressRefBillTo = true;

  private boolean linkToAddressRefBuyer = true;

  @Getter
  @Setter
  private boolean linkToContactScopeSeller = true;

  private boolean linkToAddressRefShipTo = true;

  private boolean linkToAddressRefBillToEdit = false;

  private boolean linkToAddressRefBuyerEdit = false;

  @Getter
  @Setter
  private boolean linkToAddressRefSellerEdit = false;

  private boolean linkToAddressRefShipToEdit = false;

  private boolean chorusPortlet;

  private boolean costAllocationNumberControl = false;

  private transient boolean chorusAvailable = false;

  private transient ChorusInvoiceHelper chorusInvoiceHelper;

  private boolean useOrderingPartyConfig;

  @Getter
  @Setter
  private LegislationType legalControlsSource;

  private List<SelectItem> legalControlsValues = AbstractXcblEditionTaskHelper.getLegalControlsValues(LABEL_FAMILY);

  @Getter
  @Setter
  private String rteSource;

  private String valueControlPaymentChoiceFromBO = "PAYMENT_MEAN";

  @Getter
  @Setter
  private transient String selectedPaymentChoice;

  private transient File serializedXcblFile;

  private transient XcblRteValidationEngine xcblRteValidationEngine;

  private VATType vatType;

  private transient boolean ocrAcquisitionType;

  private String rteSourceRefuse;

  private transient String refuseComment;

  private transient Map<String, String> parafiscalTaxItems = new HashMap<>();
  @Getter
  private transient Integer workflowId;
  private transient WorkflowDocumentStatus currentDocumentStatus;

  @Getter
  @Setter
  private boolean replayWorkflow;

  @Getter
  @Setter
  private boolean makeCopyOfOutputFile = true;

  @Getter
  @Setter
  private String validationProcessId;

  private transient String sellerIban;

  private transient String sellerBic;

  private transient boolean changedCurrency;

  @Getter
  @Setter
  private boolean memberSingleSubject = false;

  @Getter
  @Setter
  private transient String fileComment;

  private transient List<DocumentFile> attachments;

  private transient List<DocumentFile> addedFiles = new ArrayList<>();

  private final String memberOfASingleSubject = "Membre d’un assujetti unique";

  private final String txd = "TXD";



  public static final List<InvoiceTypeCodeType> referencedTypes = Arrays.asList(CREDIT_NOTE_GOODS_AND_SERVICES, FACTORED_CREDIT_NOTE,
      PREPAYMENT_INVOICE_CREDIT_NOTE, SELF_BILLED_CREDIT_NOTE, SELF_BILLED_FACTORED_CREDIT_NOTE, CORRECTED_INVOICE,
      CORRECTED_FACTORED_INVOICE);

  @Getter
  @Setter
  public transient InvoiceTypeCodeType oldInvoiceTypeValue;
  private transient Document tempDoc;

  public enum VATType {
    DEB("DEB"),
    ENC("ENC"),
    EXEMPT("NoTaxApplies");

    private String value;

    private VATType(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }
  }

  @Getter
  @Setter
  private boolean checkDuplicate;

  @Getter
  @Setter
  private List<String> documentStatusesNotDuplicate = new ArrayList<>();

  @Getter
  @Setter
  private List<String> documentStagesNotDuplicate = new ArrayList<>();

  public StandardInvoiceEditionTask() {
    super();
  }

  @PostLoad
  @Override
  public void postLoad() {
    super.postLoad();
    EditableInvoiceProperties editableProperties = getEditableProperties();
    EditableInvoiceParties editableParties = editableProperties.getEditableParties();
    AbstractXcblEditionTaskHelper.verifyMigration(AbstractXcblEditionTaskHelper
        .getEditionTabs(Arrays.asList(editableProperties.getEditableHeader(), editableProperties.getEditablePayment(),
            editableParties.getEditableBillToParty(),
            editableParties.getEditableBuyerParty(),
            editableParties.getEditableSellerParty(),
            editableParties.getEditableShipToParty(),
            editableParties.getEditableOtherParty(),
            editableProperties.getEditableNotes(),
            editableProperties.getEditableAllowOrCharge(), editableProperties.getEditableDetails(),
            editableProperties.getEditableFooter())));
  }

  @Override
  protected void indexProperties(BeanDescriptor descriptor, boolean global) {
    return;
  }

  public Invoice getInvoice() {
    try {
      if (invoiceXcbl == null) {
        resolveOrganizationCodes();
        if (isChorusPortlet()) {
          chorusAvailable = chorusProService.checkChorusService();
          if (!chorusAvailable)
            TaskHelper.warn(this, "chorus_warn_pro_unavailable", "error_in_conversion");
        }
        // in case the document is not found by id try to search it by reference
        String documentReference = getPortalSessionHandler().getDocumentReference();

        String documentId = getPortalSessionHandler().getDocumentId();
        String workFlowdocumentId = getPortalSessionHandler().getWorkflowDocumentId();
        if (documentId != null && valueOf(documentId) >= 0) {
          document = documentService.getDocument(Document.class,
              valueOf(documentId), true);
          document = document != null ? document : documentService.populateIndex(documentService.getDocumentByReference(documentReference));
          if (document != null) {
            chooseConfiguration(document.getIndexValue());
            if (ORDERS.name()
                .equals(document.getType())) {
              Order xcblOrder = orderService.convertToOrderType((OrderIndex) document.getIndexValue(), false);
              invoiceXcbl = initInvoiceWithOrderStandard(xcblOrder, detailPrecision, footerPrecision, defaultValueForMaxNumberOfDaysDelay);
              invoiceXcbl.setOrderingParty(xcblOrder.getOrderingParty());
              initInvoiceEntryPreferences();
            }
            else if (isFromAsn()) {
              getOrderAndOrdRSPLinkingToAsn(document);
              AdvanceShipmentNotice asn = document.getIndexValue();
              invoiceXcbl = initInvoiceWithASN(document.getIndexValue(), orderLinked, ordRspLinked, defaultValueForMaxNumberOfDaysDelay);
              invoiceXcbl.setOrderingParty(asn.getOrderingParty());
              initInvoiceEntryPreferences();
            }
            else if (isInvoiceType()) {
              initInvoiceInEditionMode();
              addTaxExemptInfoIfIsVatTypeExempt();
              oldInvoiceTypeValue = getInvoiceType();
            }

            initLegalFormAndShareCapital(invoiceXcbl, this.partner);
            initFreeText();
            initBillingFramework();
            lockForMe = true;
          }
        }
        else if (workFlowdocumentId != null && valueOf(workFlowdocumentId) > 0) {
          document = documentService.getDocument(Document.class, valueOf(workFlowdocumentId), true);
          initInvoiceInEditionMode();
        }
        else if (documentId != null &&
            (valueOf(documentId) == -2 || valueOf(documentId) == -1)) {
          newInvoice = true;
          this.invoiceXcbl = new Invoice();
          initNewInvoice(this.invoiceXcbl, this.partner, defaultValueForMaxNumberOfDaysDelay);
          initLegalFormAndShareCapital(invoiceXcbl, this.partner);
          initFreeText();
          initInvoiceDateOfOperationInCreation();
          initBillingFramework();
          initInvoiceEntryPreferences();
          lockForMe = true;
          linkToContactScopeSeller = linkToContactScopeSeller && currentUserHasScope();
          oldInvoiceTypeValue = getInvoiceType();
        }

        List<RelatedInvoiceRefType> invoiceReferenceType = Optional.ofNullable(invoiceXcbl)
            .map(Invoice::getInvoiceHeader)
            .map(InvoiceHeaderType::getInvoiceReferences)
            .map(l -> l.get(0))
            .map(InvoiceReferencesType::getListOfRelatedInvoiceRef)
            .map(ListOfRelatedInvoiceRefType::getRelatedInvoiceRef)
            .orElse(Collections.emptyList());
        if (!invoiceReferenceType.isEmpty())
          for (RelatedInvoiceRefType invRef : invoiceReferenceType) {
            if (COMMERCIAL_INVOICE.equals(invRef.getRelatedInvoiceType()
                .getInvoiceTypeCoded()))
              relatedInvoiceRef = invRef;
          }
        Locale locale = getCurrentUser().getLocale();
        valuesControlService.getValues(PARAFISCAL_TAX)
            .forEach(value ->
            {
              this.parafiscalTaxItems.put(value.getListValue(), value.getLabel(locale));
            });
        initValueControlsFields();
        ValueControl defaultPaymentChoice = valuesControlService.getValueByDefault(valueControlPaymentChoiceFromBO);
        if (defaultPaymentChoice != null && (this.partner != null && isBlank(this.partner.getInvoicePreferencesPaymentChoice())))
          this.selectedPaymentChoice = defaultPaymentChoice.getListValue();
      }

    }
    catch (Exception e) {
      TaskHelper.error(this, e, "error_in_conversion");
    }

    return invoiceXcbl;
  }

  private void initInvoiceDateOfOperationInCreation() {
    initDateOfOperation(invoiceXcbl);
    initInvoiceDateCoded(invoiceXcbl);
    listOfDateCoded = new ArrayList<>();
    listOfDateCoded.add(InvoiceEditionHelper.getDateOfOperation(invoiceXcbl)
        .get());
  }

  private void initInvoiceEntryPreferences() {
    final Integer paymentDate = this.partner.getInvoicePreferencesPaymentDate();
    if (paymentDate != null) {
      ComplexDateType netDueDate = new ComplexDateType();
      netDueDate.setValue(addDays(new Date(), paymentDate));
      this.invoiceXcbl.getInvoiceHeader()
          .getInvoicePaymentInstructions()
          .getPaymentTerms()
          .setNetDueDate(netDueDate);
      this.invoiceXcbl.getInvoiceHeader()
          .getInvoiceDates()
          .setInvoiceDueDate(netDueDate);
    }

    final String penaltyTerms = this.partner.getInvoicePreferencesPenaltyTerms();
    if (!isBlank(penaltyTerms)) {
      addPaymentTerm(PENALTY_TERMS, penaltyTerms);
    }

    final String discountTerms = this.partner.getInvoicePreferencesDiscountTerms();
    if (!isBlank(discountTerms)) {
      addPaymentTerm(DISCOUNT_PAYMENT, discountTerms);
    }

    final String latePayment = this.partner.getInvoicePreferencesLatePayment();
    if (!isBlank(latePayment)) {
      addPaymentTerm(LATE_PAYMENT, latePayment);
    }

    final String vatType = this.partner.getInvoicePreferencesVatType();
    if (!isBlank(vatType)) {
      setVATType(VATType.valueOf(vatType));
      if (VATType.EXEMPT.equals(getVATType())) {
        addTaxExemptInfoIfIsVatTypeExempt();
        // addTaxExemptInfoIfIsVatTypeExempt will add an element in invoiceXcbl.getInvoiceHeader().getTaxReference() so that we
        final String reasonExemption = this.partner.getInvoicePreferencesReasonExemption();
        if (!isBlank(reasonExemption)) {
          this.invoiceXcbl.getInvoiceHeader()
              .getTaxReference()
              .get(0)
              .setReasonTaxExemptCodedOther(reasonExemption);
        }
      }
    }

    final String paymentChoice = this.partner.getInvoicePreferencesPaymentChoice();
    if (!isBlank(paymentChoice)) {
      this.selectedPaymentChoice = paymentChoice;
    }
  }

  private void addPaymentTerm(final String code, final String value) {
    List<InvoicePaymentTermType> paymentTerms = this.invoiceXcbl.getInvoiceHeader()
        .getInvoicePaymentInstructions()
        .getPaymentTerms()
        .getPaymentTerm();
    final Optional<InvoicePaymentTermType> existingPaymentTerm = paymentTerms.stream()
        .filter(termType -> code.equals(termType.getPaymentTermCoded()))
        .findFirst();

    if (existingPaymentTerm.isPresent()) {
      int index = paymentTerms.indexOf(existingPaymentTerm.get());
      existingPaymentTerm.get()
          .getPaymentTermDescription()
          .setValue(value);
      this.invoiceXcbl.getInvoiceHeader()
          .getInvoicePaymentInstructions()
          .getPaymentTerms()
          .getPaymentTerm()
          .add(index, existingPaymentTerm.get());
    }
    else {
      final InvoicePaymentTermType paymentTerm = new InvoicePaymentTermType();
      paymentTerm.setPaymentTermCoded(code);
      ComplexStringType description = new ComplexStringType();
      description.setValue(value);
      paymentTerm.setPaymentTermDescription(description);
      this.invoiceXcbl.getInvoiceHeader()
          .getInvoicePaymentInstructions()
          .getPaymentTerms()
          .getPaymentTerm()
          .add(paymentTerm);
    }
  }

  private void chooseConfiguration(XcblOrderingParty index) {
    if (StringUtils.isEmpty(index.getOrderingParty()))
      return;
    OrderingParty orderingParty = orderingPartyService.getByIdentifier(index.getOrderingParty());
    if (isUseOrderingPartyConfig() && orderingParty != null && StringUtils.isNotEmpty(orderingParty.getInvoiceMessageConfig())) {
      InvoiceMessageConfiguration messageConfig = getOrderingPartyConfiguration(orderingParty.getInvoiceMessageConfig(),
          InvoiceMessageConfiguration.class);
      this.editableProperties = messageConfig.getEditableProperties();
      this.freeTextProperties = messageConfig.getFreeTextProperties();
      this.outputDir = messageConfig.getOutputDir();
      this.duplicateInvoiceNumber = messageConfig.isDuplicateInvoiceNumber();
      this.legalControlsSource = messageConfig.getLegalControlsSource();
      this.rteSource = messageConfig.getRteSource();
      this.rteSourceRefuse = messageConfig.getRteSourceRefuse();
      this.linkToAddressRefBillTo = messageConfig.isLinkToAddressRefBillTo();
      this.linkToAddressRefBuyer = messageConfig.isLinkToAddressRefBuyer();
      this.linkToContactScopeSeller = messageConfig.isLinkToContactScopeSeller() && currentUserHasScope();
      this.linkToAddressRefShipTo = messageConfig.isLinkToAddressRefShipTo();
      this.validationProcessId = messageConfig.getValidationProcessId();
      this.makeCopyOfOutputFile = messageConfig.isMakeCopyOfOutputFile();
      this.valueControlPaymentChoiceFromBO = editableProperties.getEditablePayment()
          .getColumnDescriptor("invoicePaymentChoice")
          .getDescription();
      this.checkDuplicate = messageConfig.isCheckDuplicate();
      this.documentStatusesNotDuplicate = messageConfig.getDocumentStatusesNotDuplicate();
      this.documentStagesNotDuplicate = messageConfig.getDocumentStagesNotDuplicate();
    }
  }

  private void initFreeText() {
    List<FreeText> displayedFreeText = getFreeTextProperties().getDisplayed();
    for (FreeText ft : displayedFreeText) {
      NameValuePairType nvp = InvoiceXcblHelper.addFreeText(invoiceXcbl.getInvoiceHeader(), ft.getName());
      ft.setValue(nvp);
    }
  }

  private void initInvoiceInEditionMode() throws Exception {
    this.invoiceXcbl = InvoiceTaskHelper.createInvoiceBasedOnXcbl(this.document);
    InvoiceDatesType invoiceDates = Optional.ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceDates)
        .orElse(null);
    if (invoiceDates == null)
      invoiceXcbl.getInvoiceHeader()
          .setInvoiceDates(InvoiceXcblHelper.initInvoiceDates());
    else if (invoiceDates.getInvoicingPeriod() == null || invoiceDates.getInvoicingPeriod()
        .getStartDate() == null)
      invoiceDates.setInvoicingPeriod(InvoiceXcblHelper.initInvoicingPeriod());
    this.originalXcbl = cloner.deepClone(this.invoiceXcbl);
    this.actualPayments = InvoiceEditionHelper.getActualPayments(invoiceXcbl);
    InvoiceEditionHelper.initInvoiceForEdition(this.invoiceXcbl, this.originalXcbl);
    listOfDateCoded = new ArrayList<>();
    listOfDateCoded.add(InvoiceEditionHelper.getDateOfOperation(invoiceXcbl)
        .get());
    this.taxExemptEditInvoice = hasExemptWithValue(invoiceXcbl);
    initFreeText();
    this.ocrAcquisitionType = AcquisitionType.OCR.equals(((InvoiceIndex) document.getIndexValue()).getAcquisition());
    this.vatType = InvoiceEditionHelper.getVatTypeOperation(invoiceXcbl);
    if (viewer == null) {
      viewer = view(document, currentSession().getInstance(), document.getIndexValue(), BirtOutoutFileType.PDF).orElse(null);
    }
    this.initMemberSingleSubject();
    this.initBillingFramework();
  }

  private Optional<DocumentViewer> view(Document document, Instance instance, InvoiceIndex indexable, BirtOutoutFileType type) {
    final Optional<DocumentViewer> view = this.documentViewerService.view(document, indexable, instance, this.partner, this.getCurrentUser()
        .getLocale(), this.viewerBaseName(), false, false, null, type);
    // document not found
    if (!view.isPresent()) {
      error("Document not found");
    }

    // To display the pdf, we have to be in editInvoice mode and has a pdf that is not generated by Birt
    if (!editInvoice || documentHasNoPdf(document)) {
      log.info("No existing pdf to display for the selected document");
      return empty();
    }

    // performs straight validation to gets user's feed-backs...
    // to avoid unnecessary transformations, we could
    // change the document visualization workflow
    if (!view.map(DocumentViewer::isValid)
        .get()) {
      TaskHelper.error(this, null, LABEL_FAMILY + ".error_missing_template");
      return empty();
    }
    else {
      markDocumentsAsRead(indexable);
    }
    return view;
  }

  private boolean documentHasNoPdf(Document document) {
    return documentHasNoPdf = getExistingPdfFile(document, PDF) == null;
  }

  public boolean documentHasPdf() {
    return editInvoice && !documentHasNoPdf;
  }

  public String getViewerURI() {
    if (editInvoice) {
      return toURI(document.getIndexValue(),
          this.getInstance(),
          this.getCurrentUser()
              .getLocale(),
          this.viewerBaseName((InvoiceIndex) document.getIndexValue()),
          false, false, null, BirtOutoutFileType.PDF);
    }

    return EMPTY;
  }

  private Function<Document, String> viewerBaseName() {
    return document -> of(document).filter(com.byzaneo.xtrade.bean.Document::isIndexed)
        .map(com.byzaneo.xtrade.bean.Document::<InvoiceIndex> getIndexValue)
        .map(this::viewerBaseName)
        .orElseGet(document::getReference);
  }

  private void markDocumentsAsRead(Indexable indexable) {
    if (!isUserSwitched()) {
      this.document = documentService.getDocument(indexable);
      if (document != null && isDocumentStatusInitial()) {
        document.setStatusWithEnumValue(READ);
        documentService.saveDocument(document);
      }
    }
  }

  public BasisCodeType getAllowOrChargeBasisCodeType(InvoiceAllowOrChargeType allowOrCharge) {
    if (allowOrCharge.getTypeOfAllowanceOrCharge()
        .getMonetaryValue() != null)
      return BasisCodeType.MONETARY_AMOUNT;
    else if (allowOrCharge.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge() != null)
      return BasisCodeType.PERCENT;
    return allowOrCharge.getBasisCoded();
  }

  private String getInvoiceDocType() {
    String rawType = this.document.getType();
    final DocumentCompoundType compoundType = fromJson(rawType);
    return compoundType == null ? rawType : compoundType.getKind();
  }

  private boolean isInvoiceType() {
    if (document == null)
      return false;
    return DocumentType.INVOIC.name()
        .equals(getInvoiceDocType());
  }

  private void getOrderAndOrdRSPLinkingToAsn(final Document document) {
    if (document.getParent() == null) return;

    if (ORDERS.name()
        .equals(document.getParent()
            .getType())) {
      orderLinked = orderService.convertToOrderType((OrderIndex) documentService.getIndexable(getDocument().getParent()), false);
      ordRspLinked = documentService.getIndexable(
          of(getDocument().getParent())
              .map(com.byzaneo.xtrade.bean.Document::getChildren)
              .map(Set::stream)
              .orElse(Stream.empty())
              .filter(doc -> ORDRSP.name()
                  .equals(doc.getType()))
              .findFirst()
              .orElse(null));
    }
    if (ORDRSP.name()
        .equals(document.getParent()
            .getType())) {
      ordRspLinked = documentService.getIndexable(getDocument().getParent());
      orderLinked = orderService.convertToOrderType((OrderIndex) documentService.getIndexable(getDocument().getParent()
          .getParent()), false);
    }
  }

  @Override
  public Logs importDocuments(List<InputStream> is) {
    return null;
  }

  @Override
  public String getPathDescriptor() {
    return "descriptors/invoice-columns.xml";
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {

  }

  private BeanDescriptor getBeanDescriptorByPath(String path) {
    BeanDescriptor descriptor;
    if (beanService == null) {
      beanService = SpringContextHelper.getBean(BeanService.class, BeanService.SERVICE_NAME);
    }
    try {
      descriptor = beanService.fromClasspath(path);
    }
    catch (IOException e) {
      log.error("Failed to get descriptor", e);
      return null;
    }
    return descriptor;
  }

  public List<PropertyDescriptor> getPropertiesRendered() {
    return getDetailDescriptor().getProperties()
        .stream()
        .filter(pd -> !pd.getRequired())
        .collect(toList());
  }

  public List<SelectItem> getInvoiceTypeItems() {
    List<SelectItem> items = stream(InvoiceTypeCodeType.values()).filter(
            type -> type != InvoiceTypeCodeType.OTHER && !asList(invoiceTypeExclude)
                .contains(type.name()))
        .map(type -> new SelectItem(type,
            MessageHelper.getMessage(LABEL_FAMILY + "." + type.name(), type.name(), selectedLanguage)))
        .collect(toCollection(ArrayList::new));
    items.add(new SelectItem(InvoiceTypeCodeType.OTHER,
        MessageHelper.getMessage(LABEL_FAMILY + ".other", "Other", selectedLanguage)));
    return items;
  }

  public List<SelectItem> getCurrencyItems() {
    List<SelectItem> items = stream(CurrencyCodeType.values()).filter(currency -> currency != CurrencyCodeType.OTHER)
        .map(item -> new SelectItem(item, item.name()))
        .collect(toCollection(ArrayList::new));
    items.add(new SelectItem(CurrencyCodeType.OTHER,
        MessageHelper.getMessage(LABEL_FAMILY + ".other", "Other", selectedLanguage)));
    return items;
  }

  public List<InvoiceDateCodedType> getListOfDateCoded() {
    // for edition, listOfDateCoded is initialized in getInvoice()
    if (listOfDateCoded == null) {
      listOfDateCoded = new ArrayList<>();
      // Date of operation
      InvoiceDateCodedType datType = new InvoiceDateCodedType();
      datType.setDateQualifier(new DateQualifierType());
      datType.getDateQualifier()
          .setDateQualifierCoded("DateOfOperation");
      datType.setDate(new ComplexDateType());
      listOfDateCoded.add(datType);
    }
    return listOfDateCoded;
  }

  public String getPaymentChoice() {
    if (!this.editInvoice) {
      return selectedPaymentChoice;
    }
    Optional<PaymentMethodType> paymentMean = ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoicePaymentInstructions)
        .map(InvoicePaymentInstructionsType::getPaymentMethod)
        .filter(l -> !l.isEmpty())// returns empty list if missing
        .map(l -> l.get(0));
    if (paymentMean.isPresent()) {
      PaymentMethodType payment = paymentMean.get();
      return "Other".equals(payment.getPaymentMeanCoded())
          ? "Other_" + payment.getPaymentMeanCodedOther()
          : payment.getPaymentMeanCoded();
    }
    return null;
  }

  public void setPaymentChoice(String paymentChoice) {
    if (invoiceXcbl == null)
      return;
    InvoiceHeaderType header = invoiceXcbl.getInvoiceHeader();
    if (header.getInvoicePaymentInstructions() == null)
      header.setInvoicePaymentInstructions(new InvoicePaymentInstructionsType());
    List<String> paymentValues = getValueControlValues(valueControlPaymentChoiceFromBO).stream()
        .map(vc -> vc.getListValue())
        .collect(Collectors.toList());
    List<PaymentMethodType> payments = header.getInvoicePaymentInstructions()
        .getPaymentMethod();
    payments.removeIf(method -> paymentValues.contains(method.getPaymentMeanCoded()));
    if (payments.size() > 0)
      payments.removeIf(method -> paymentValues.contains("Other_" + method.getPaymentMeanCodedOther()));
    if (paymentChoice != null) {
      PaymentMethodType method = new PaymentMethodType();
      if (paymentChoice.startsWith("Other_")) {
        method.setPaymentMeanCoded("Other");
        method.setPaymentMeanCodedOther(paymentChoice.substring(paymentChoice.indexOf("_") + 1));
      }
      else
        method.setPaymentMeanCoded(paymentChoice);
      payments.add(0, method);
      selectedPaymentChoice = paymentChoice;
    }
  }

  public boolean isPartyFactor() {
    return InvoiceEditionHelper.isPartyFactor(invoiceXcbl);
  }

  public String getIBAN(String currency) {
    if (this.editInvoice && !changedCurrency) {
      return InvoiceXcblHelper.getIBAN(invoiceXcbl);
    }
    return bankAccountService.getIBAN(partner, currency);
  }

  public String getBIC(String currency) {
    if (this.editInvoice && !changedCurrency) {
      return InvoiceXcblHelper.getBIC(invoiceXcbl);
    }
    return bankAccountService.getBIC(partner, currency);
  }

  public List<ValueControl> getTvaExemptionItems() {
    return valuesControlService.getValues(VAT_EXONERATION);
  }

  public List<String> onCompleteValueControl(String query) {
    final UIComponent component = UIComponent.getCurrentComponent(FacesContext.getCurrentInstance());

    String listId = (String) component.getAttributes()
        .get(LIST_ID);
    if (StringUtils.isEmpty(query))
      return getValueControlValues(listId).stream()
          .map(vc -> vc.getListValue())
          .collect(Collectors.toList());
    return getValueControlValues(listId).stream()
        .filter(vc -> vc.getLabel(selectedLanguage)
            .contains(query) ||
            vc.getListValue()
                .contains(query))
        .map(vc -> vc.getListValue())
        .collect(Collectors.toList());
  }

  public String getLabelForValueControl(String listId, String listValue) {
    return valuesControlService.getLabelValue(listId, listValue, selectedLanguage, true);
  }

  public String getLabelForItem(Object item) {
    if (item == null)
      return null;
    if (item instanceof Product) {
      Product product = (Product) item;
      return product != null ? product.getReference() + " - " + product.getProductName() : "";
    }
    if (item instanceof Partner) {
      Partner partner = (Partner) item;
      return partner != null ? partner.getCode() + " - " + partner.getName() : "";
    }
    if (item instanceof Address) {
      Address address = (Address) item;
      return address != null ? address.getIdentification() + " - " + address.getName() + " - " + address.getCity() : "";
    }
    if (item instanceof String)
      return (String) item;
    return null;
  }

  public void initValueControlsFields() {
    for (FreeText ft : freeTextProperties.getDisplayed()) {
      if (StringUtils.isEmpty(ft.getValue()
          .getValue())) {
        ValueControl vc = getSelectedValueControl(ft);
        if (vc != null)
          ft.getValue()
              .setValue(vc.getListValue());
      }
    }
  }

  public List<ValueControl> getValueControlValues(String value) {
    List<ValueControl> controls = valuesControlService.getValues(value);
    for (ValueControl control : controls)
      if (control.isByDefault()) {
        controls.remove(control);
        controls.add(0, control);
        break;
      }
    return controls;
  }

  public ValueControl getSelectedValueControl(PropertyDescriptor pd) {
    if (!pd.getRequired())
      return null;
    List<ValueControl> controls = getValueControlValues(pd.getDescription());
    for (ValueControl control : controls) {
      if (control.isByDefault())
        return control;
    }
    return controls.size() > 0 ? controls.get(0) : null;
  }

  public ValueControl getSelectedValueControl(FreeText freeText) {
    if ((!freeText.isRequired() && !freeText.isStrict()) || StringUtils.isEmpty(freeText.getValueControl()))
      return null;
    ValueControl defaultControl = valuesControlService.getValueByDefault(freeText.getValueControl());
    if (defaultControl != null)
      return defaultControl;
    if (!freeText.isRequired())
      return null;
    List<ValueControl> controls = getValueControlValues(freeText.getValueControl());
    return controls.size() > 0 ? controls.get(0) : null;
  }

  public void onTabChangePartyCoded(TabChangeEvent event) {
    Tab tab = event.getTab();

    if (tab != null && NEW_TAB.equals(tab.getTitle()))
      getCurrentInstance().addCallbackParam("addNewPartyCodedTab", true);
  }

  public void onAddPartyCoded() {
    InvoicePartyCodedType partyCodedType = createInvoicePartyCoded();
    partyCodedType.setPartyRoleCoded("new");

    listOfPartyCoded.add(partyCodedType);
    listOfPartyCodedTabs.add(listOfPartyCodedTabs.size() - 1, partyCodedType);

    // To avoid having an empty title (when we modify an invoice)
    for (int i = 0; i < listOfPartyCodedTabs.size() - 1; i++) {
      InvoicePartyCodedType partyCoded = listOfPartyCodedTabs.get(i);
      if (partyCoded.getPartyRoleCoded() == null) partyCoded.setPartyRoleCoded("new");
    }

    activeIndex = getListOfPartyCoded().size() == 3 ? 0 : getListOfPartyCoded().size() - 1;
  }

  public String getBillToName() {
    return PartyCodeXcblHelper.getBillToName(listOfPartyCoded, chorusPortlet);
  }

  public String getBillToTva() {
    return PartyCodeXcblHelper.getBillToTva(listOfPartyCoded, chorusPortlet);
  }

  public String getBillToSiren() {
    return PartyCodeXcblHelper.getBillToSiren(listOfPartyCoded, chorusPortlet);
  }

  public List<InvoicePartyCodedType> getListOfPartyCoded() {
    if (invoiceXcbl != null && listOfPartyCoded == null) {
      if (editInvoice) {
        listOfPartyCoded = InvoiceEditionHelper.getListOfPartyCoded(invoiceXcbl,
            chorusPortlet ? INVOICE_CHORUS_PARTY : INVOICE_EDITION_PARTY);
        // for invoices that were created before AIO-10412, when you want to modify it, there is a possibility that
        // listOfPartyCoded[x].partyTaxInformation.taxIdentifier.ident to be null which will result in a NPE since now ident is
        // ComplexStringType instead of String, so we need to instantiate it
        for (InvoicePartyCodedType party : listOfPartyCoded) {
          InvoiceIdentifierType taxIdentifier = Optional.of(party)
              .map(InvoicePartyCodedType::getPartyTaxInformation)
              .map(InvoicePartyTaxInformationType::getTaxIdentifier)
              .orElse(null);
          if (taxIdentifier != null && taxIdentifier.getIdent() == null) {
            taxIdentifier.setIdent(new ComplexStringType());
          }
          InvoicePartyTaxInformationType partyTaxInformationType = Optional.of(party)
              .map(InvoicePartyCodedType::getPartyTaxInformation)
              .orElse(null);
          if (taxIdentifier == null && partyTaxInformationType == null) {
            party.setPartyTaxInformation(InvoiceXcblHelper.createInvoicePartyTaxInformationType());
          }
          else if (taxIdentifier == null && partyTaxInformationType != null) {
            partyTaxInformationType.setTaxIdentifier(InvoiceXcblHelper.createInvoiceIdentifierType());
          }
        }
      }
      else {
        listOfPartyCoded = InvoiceXcblHelper.getListOfPartyCoded(invoiceXcbl, chorusPortlet ? INVOICE_CHORUS_PARTY : DEFAULT_PARTY);
        for (InvoicePartyCodedType party : listOfPartyCoded) {
          if (party != null && party.getPartyID() != null) {
            InvoiceXcblHelper.updateInvoiceParty(party, getPartyInfo(getIdentValue(party.getPartyID())));
            if (SELLER.equals(party.getPartyRoleCoded()))
              updateSupplierContactInfo(party, getCurrentUser());
          }
        }
      }
    }
    return listOfPartyCoded;
  }

  public List<InvoicePartyCodedType> getListOfPartyCodedTabs() {
    if (listOfPartyCodedTabs == null) {
      listOfPartyCodedTabs = new ArrayList<InvoicePartyCodedType>(getListOfPartyCoded());
      // Add the new tab with + character at the end
      if (!chorusPortlet)
        listOfPartyCodedTabs.add(createInvoicePartyCoded());
    }

    return listOfPartyCodedTabs;
  }

  public void setListOfPartyCoded(List<InvoicePartyCodedType> partyCodedList) {
    listOfPartyCoded = partyCodedList;
  }

  public void onRemovePartyCoded(TabCloseEvent event) {
    FacesContext context = FacesContext.getCurrentInstance();
    Map<String, String> params = context.getExternalContext()
        .getRequestParameterMap();
    TabView tabView = (TabView) event.getComponent();
    String activeIndexValue = params.get(tabView.getClientId(context) + "_tabindex");
    listOfPartyCoded.remove(Integer.parseInt(activeIndexValue));
    listOfPartyCodedTabs.remove(Integer.parseInt(activeIndexValue));
    activeIndex = listOfPartyCodedTabs.size() - 2;
  }

  public void onRemovePartyCoded(InvoicePartyCodedType partyCodedType) {
    if (listOfPartyCoded != null && partyCodedType != null) {
      listOfPartyCoded.remove(partyCodedType);
    }
  }

  public boolean getPartyClosable(String partyRole) {
    List<InvoicePartyCodedType> availablePartyCoded = InvoiceEditionHelper.getListOfPartyCoded(invoiceXcbl, INVOICE_EDITION_PARTY);
    boolean isMandatory = asList(DEFAULT_PARTY).contains(partyRole) || (this.editInvoice && InvoiceXcblHelper.PAY_TO.equals(partyRole)) ||
        (memberSingleSubject && DECLARANTS_AGENT.equals(partyRole)) || partyRole.isEmpty() ||
        availablePartyCoded.stream()
            .map(InvoicePartyCodedType::getPartyRoleCoded)
            .anyMatch(partyCoded -> partyCoded.equals(partyRole));
    return !isMandatory;
  }

  public List<SelectItem> getPartyRoleCodedItems(String role) {

    List<String> availablePartyCoded = ofNullable(listOfPartyCoded).orElse(emptyList())
        .stream()
        .map(InvoicePartyCodedType::getPartyRoleCoded)
        .collect(toList());
    List<SelectItem> itemsToBeSelected = new ArrayList<>();
    for (String otherParty : asList(OTHER_PARTIES)) {
      if (!availablePartyCoded.contains(otherParty) || otherParty.equals(role)) {
        itemsToBeSelected.add(new SelectItem(otherParty,
            MessageHelper.getMessage(LABEL_FAMILY + "." + otherParty, otherParty, selectedLanguage)));
      }
    }
    return itemsToBeSelected;
  }

  public boolean isCompleteAddressMethod(String party) {
    switch (party) {
    case BILL_TO:
      return this.editInvoice ? linkToAddressRefBillToEdit : linkToAddressRefBillTo;
    case BUYER:
      return this.editInvoice ? linkToAddressRefBuyerEdit : linkToAddressRefBuyer;
    case SELLER:
      return this.editInvoice ? linkToAddressRefSellerEdit : linkToContactScopeSeller;
    case SHIP_TO:
      return this.editInvoice ? linkToAddressRefShipToEdit : linkToAddressRefShipTo;
    default:
      return !"new".equals(party);
    }
  }

  public List<Partner> getPartnersInTheScope(String query) {
    User user = this.getCurrentUser();
    Set<Partner> partners = user.getUserGroupAssociations()
        .stream()
        .filter(uga -> "PARTNER".equals(uga.getId()
            .getGroup()
            .getDescription()))
        .map(uga -> (Partner) uga.getId()
            .getGroup())
        .filter(p -> p.getCode()
            .contains(query) ||
            p.getName()
                .contains(query))
        .collect(Collectors.toSet());
    if (!user.getPerimeters()
        .isEmpty())
      user.getPerimeters()
          .forEach(per -> partners.addAll(Arrays.asList(per.getSelectedPartners())
              .stream()
              .filter(p -> p.getCode()
                  .contains(query) ||
                  p.getName()
                      .contains(query))
              .collect(Collectors.toSet())));
    return new ArrayList<Partner>(partners);
  }

  public Partner getSelectedPartnerFromScope(String code) {
    User user = this.getCurrentUser();
    Optional<Partner> partner = user.getUserGroupAssociations()
        .stream()
        .filter(uga -> "PARTNER".equals(uga.getId()
            .getGroup()
            .getDescription()))
        .map(uga -> (Partner) uga.getId()
            .getGroup())
        .filter(p -> p.getCode()
            .equals(code))
        .findFirst();
    if (partner.isPresent())
      return partner.get();
    else {
      if (!user.getPerimeters()
          .isEmpty())
        for (Perimeter per : user.getPerimeters()) {
          Optional<Partner> part = Arrays.asList(per.getSelectedPartners())
              .stream()
              .filter(p -> p.getCode()
                  .equals(code))
              .findFirst();
          if (part.isPresent())
            return part.get();
        }
    }
    return null;
  }

  public void onSelectPartnerInTheScope(SelectEvent e) {
    String code = (String) e.getObject();
    Partner partner = getSelectedPartnerFromScope(code);
    final String partyType = ((AutoComplete) e.getComponent()).getConverterMessage();
    if (partner != null && partyType != null) {
      InvoicePartyCodedType party = getPartyFromListOfPartyCoded(partyType);
      updatePartyWithInformationFromPartner(party, partner);
      clearLegalFormAndShareCapital(party.getListOfIdentifier()
          .getIdentifier());
      initLegalFormAndShareCapital(invoiceXcbl, partner);
      if (!isSelfBillingType()) {
        String currency = invoiceXcbl.getInvoiceHeader()
            .getInvoiceCurrency()
            .getCurrencyCoded()
            .toString();
        sellerIban = bankAccountService.getIBAN(partner, currency);
        sellerBic = bankAccountService.getBIC(partner, currency);
      }
    }
  }

  private boolean currentUserHasScope() {
    User user = this.getCurrentUser();
    return !user.getPerimeters()
        .isEmpty() ||
        user.getUserGroupAssociations()
            .stream()
            .filter(uga -> "PARTNER".equals(uga.getId()
                .getGroup()
                .getDescription()))
            .count() > 1;
  }

  public void onChangeOtherParty(String partyRoleCoded) {
    final InvoicePartyCodedType party = createInvoicePartyCoded();
    listOfPartyCodedTabs.stream()
        .filter(p -> partyRoleCoded.equals(p.getPartyRoleCoded()))
        .forEach(p ->
        {
          p.setListOfIdentifier(party.getListOfIdentifier());
          p.setNameAddress(party.getNameAddress());
          p.setOtherContacts(party.getOtherContacts());
          p.setPartyID(party.getPartyID());
          p.setPartyRoleCodedOther(party.getPartyRoleCodedOther());
          p.setPartyTaxInformation(party.getPartyTaxInformation());
          p.setPrimaryContact(party.getPrimaryContact());
        });
  }

  public void onSelectAddress(SelectEvent e) {
    final String role = ((AutoComplete) e.getComponent()).getConverterMessage();
    if (isBlank(role))
      return;

    final InvoicePartyCodedType party = getPartyFromListOfPartyCoded(role);

    String identifier = (String) e.getObject();
    if (party == null || isBlank(identifier))
      return;

    Optional<Address> address = this.repositoryService.searchOne(
        Address.class,
        createBuilder().and(equal(FIELD_IDENTIFICATION, identifier), equal(FIELD_TYPE, getTypeAddress(role)))
            .query());
    if (!address.isEmpty() && TypeAddressEnum.SELLER.equals(getTypeAddress(role))) {
      sellerIban = address.get()
          .getIban();
      sellerBic = address.get()
          .getBic();
    }
    address.map(adr -> PartyCodeXcblHelper.convert(adr, party.getListOfIdentifier()))
        .ifPresent(selected ->

        {
          cloner.copyPropertiesOfInheritedClass(selected, party);
          selectedParty = party;
        });
  }

  public int getEmailAddressIndex(ListOfInvoiceContactNumberType listOfContactNumberType) {
    return PartyCodeXcblHelper.getEmailAddressIndex(listOfContactNumberType);
  }

  public boolean isDefaultParty(String partyRole) {
    return Arrays.asList(DEFAULT_PARTY)
        .contains(partyRole);
  }

  @Override
  public List<SelectItem> getCountryItems() {
    return CustomLocaleMapper.getCountryItems();
  }

  public String getMessage(final String messageKey, final String defaultMessage, final Locale locale, final Object... args) {
    return MessageHelper.getMessage(messageKey, defaultMessage, locale, args);
  }

  public List<TaxMandatory> getTaxMandatories() {
    if (taxMandatories == null) {
      List<String> roles = asList("BillTo", "Buyer", "Seller", "ShipTo", "ShipFrom");
      taxMandatories = new ArrayList<>();
      for (String role : roles) {
        taxMandatories.add(new TaxMandatory(role, Boolean.FALSE));
      }
    }
    return taxMandatories;
  }

  public void setTaxMandatories(List<TaxMandatory> taxMandatories) {
    this.taxMandatories = taxMandatories;
  }

  public boolean getDuplicateInvoiceNumber() {
    return this.duplicateInvoiceNumber;
  }

  public void setDuplicateInvoiceNumber(boolean duplicateInvoiceNumber) {
    this.duplicateInvoiceNumber = duplicateInvoiceNumber;
  }

  public BeanDescriptor getDetailDescriptor() {
    if (detailDescriptor == null) {
      detailDescriptor = getBeanDescriptorByPath("descriptors/invoice-details-columns.xml");
    }
    return detailDescriptor;
  }

  public EditableInvoiceProperties getEditableProperties() {
    if (this.editableProperties == null)
      this.editableProperties = new EditableInvoiceProperties();
    return editableProperties;
  }

  public void setEditableProperties(EditableInvoiceProperties editableProperties) {
    this.editableProperties = editableProperties;
  }

  public void setDetailDescriptor(BeanDescriptor detailDescriptor) {
    this.detailDescriptor = detailDescriptor;
  }

  public int getDetailPrecision() {
    return detailPrecision;
  }

  public void setDetailPrecision(int detailPrecision) {
    this.detailPrecision = detailPrecision;
  }

  public int getFooterPrecision() {
    return footerPrecision;
  }

  public void setFooterPrecision(int footerPrecision) {
    this.footerPrecision = footerPrecision;
  }

  public boolean isEditInvoiceInError() {
    return editInvoiceInError;
  }

  public void setEditInvoiceInError(boolean editInvoiceInError) {
    this.editInvoiceInError = editInvoiceInError;
  }

  public boolean isEditInvoice() {
    return editInvoice;
  }

  public void setEditInvoice(boolean editInvoice) {
    this.editInvoice = editInvoice;
  }

  public int getActiveIndex() {
    return activeIndex;
  }

  public void setActiveIndex(int activeIndex) {
    this.activeIndex = activeIndex;
  }

  public InvoicePartyCodedType getSelectedParty() {
    return selectedParty;
  }

  public void setSelectedParty(InvoicePartyCodedType selectedParty) {
    this.selectedParty = selectedParty;
  }

  public boolean getNewInvoice() {
    return newInvoice;
  }

  public void setNewInvoice(boolean newInvoice) {
    this.newInvoice = newInvoice;
  }

  public boolean taxIsMandatory(String role) {

    // THERE IS NO ROLE IN PARAMETER
    if (StringUtils.isBlank(role)) {
      return false;
    }

    // RETRIEVE TAX MANDATORY BEAN
    for (TaxMandatory item : getTaxMandatories()) {
      if (role.equals(item.getRole())) {
        return item.getTax();
      }
    }
    return false;
  }

  public InvoicePaymentTermType getFirstDiscountPaymentType(final InvoicePaymentTermsType paymentTerms) {
    final List<InvoicePaymentTermType> paymentsTerms = paymentTerms.getPaymentTerm();
    final Optional<InvoicePaymentTermType> discountPaymentType = paymentsTerms.stream()
        .filter(termType -> DISCOUNT_PAYMENT.equals(termType.getPaymentTermCoded()))
        .findFirst();

    if (discountPaymentType.isPresent()) {
      if (discountPaymentType.get()
          .getPaymentTermDescription() == null) {
        discountPaymentType.get()
            .setPaymentTermDescription(new ComplexStringType());
      }
      return discountPaymentType.get();
    }

    final InvoicePaymentTermType discount = new InvoicePaymentTermType();
    discount.setPaymentTermCoded(DISCOUNT_PAYMENT);
    discount.setPaymentTermDescription(new ComplexStringType());
    paymentsTerms.add(discount);
    return discount;
  }

  public InvoicePaymentTermType getFirstLatePaymentType(final InvoicePaymentTermsType paymentTerms) {
    final List<InvoicePaymentTermType> paymentsTerms = paymentTerms.getPaymentTerm();
    final Optional<InvoicePaymentTermType> latePaymentType = paymentsTerms.stream()
        .filter(termType -> LATE_PAYMENT.equals(termType.getPaymentTermCoded()))
        .findFirst();

    if (latePaymentType.isPresent()) {
      if (latePaymentType.get()
          .getPaymentTermDescription() == null) {
        latePaymentType.get()
            .setPaymentTermDescription(new ComplexStringType());
      }
      return latePaymentType.get();
    }
    final InvoicePaymentTermType late = new InvoicePaymentTermType();
    late.setPaymentTermCoded(LATE_PAYMENT);
    late.setPaymentTermDescription(new ComplexStringType());
    paymentsTerms.add(late);
    return late;

  }

  public InvoicePaymentTermType getFirstPenaltyTermsPaymentType(final InvoicePaymentTermsType paymentTerms) {
    final List<InvoicePaymentTermType> paymentsTerms = paymentTerms.getPaymentTerm();
    final Optional<InvoicePaymentTermType> latePaymentType = paymentsTerms.stream()
        .filter(termType -> PENALTY_TERMS.equals(termType.getPaymentTermCoded()))
        .findFirst();

    if (latePaymentType.isPresent()) {
      if (latePaymentType.get()
          .getPaymentTermDescription() == null) {
        latePaymentType.get()
            .setPaymentTermDescription(new ComplexStringType());
      }
      return latePaymentType.get();
    }

    final InvoicePaymentTermType penaltyTerms = new InvoicePaymentTermType();
    penaltyTerms.setPaymentTermCoded(PENALTY_TERMS);
    penaltyTerms.setPaymentTermDescription(new ComplexStringType());
    paymentsTerms.add(penaltyTerms);
    return penaltyTerms;
  }

  public boolean getTaxExempt() {
    return taxExempt;
  }

  public void setTaxExempt(boolean taxExempt) {
    this.taxExempt = taxExempt;
  }

  public boolean isTaxExemptEditInvoice() {
    return taxExemptEditInvoice;
  }

  public void setTaxExemptEditInvoice(boolean taxExemptEditInvoice) {
    this.taxExemptEditInvoice = taxExemptEditInvoice;
  }

  public void addTaxExemptInfoForEdition() {
    // InvoiceEditionHelper automatically create an empty exempt to be used in view.
    // Only treat the case where the exempt value must be deleted.
    if (!taxExemptEditInvoice) {
      // Remove only exempt value
      // InvoiceEditionHelper#updateTaxReference will remove the whole exempt object if it has no value when saving the edited invoice.
      this.invoiceXcbl.getInvoiceHeader()
          .getTaxReference()
          .stream()
          .filter(
              t -> TaxCategoryCodeType.EXEMPT_FROM_TAX == t.getTaxCategoryCoded())
          .findAny()
          .ifPresent(t -> t.setReasonTaxExemptCodedOther(""));
    }
  }

  public void addTaxExemptInfo() {
    TaxReferenceType taxReferenceType = TaxXcblHelper.createTaxReferenceType();
    this.invoiceXcbl.getInvoiceHeader()
        .getTaxReference()
        .add(taxReferenceType);
    if (taxSummaries != null)
      this.taxSummaries.clear();
    // if VAT exempted, force product VAT to 0 for all the products in the list when calculating the pricing details
    // if VAT not exempted, recalculate the pricing details with the initial VAT
    populateInvoiceItemDetailOnTaxExempt();
    if (this.taxExempt) {
      BigDecimal totalTaxableAmount = getTaxableValueFromInvoiceSummary(this.invoiceXcbl);
      InvoiceMonetaryValueType invoiceTotal = this.invoiceXcbl.getInvoiceSummary()
          .getInvoiceTotals()
          .getInvoiceTotal();
      invoiceTotal.setMonetaryAmount(toComplexBigDecimalType(invoiceTotal.getMonetaryAmount(), totalTaxableAmount));
    }
    else {
      applyTaxOnInvoiceItemDetails(this.invoiceXcbl, this.details, this.taxSummaries, detailPrecision, footerPrecision,
          this.parafiscalTaxSummaries);
      computeInvoiceTotal(this.invoiceXcbl, this.taxExempt, this.footerPrecision);
      computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
    }
  }

  /**
   * if VAT exempted, force product VAT to 0 for all the products in the list when calculating the pricing details if VAT not exempted,
   * recalculate the pricing details with the initial VAT
   */
  private void populateInvoiceItemDetailOnTaxExempt() {
    if (this.details != null)
      this.details.forEach(item -> getProductIdentifier(item)
          .map(prodId -> this.repositoryService.searchOne(Product.class, prodId, this.partner))
          .map(prdct ->
          {
            if (this.taxExempt) {
              prdct.setVat(0D);
            }
            return prdct;
          })
          .ifPresent(prdct -> InvoiceItemDetailXcblHelper.populateInvoiceItemDetail(item, prdct, this.detailPrecision, this.detailPrecision,
              TaxCategoryCodeType.OTHER)));
  }

  public List<InvoiceTaxSummaryType> getTaxSummaries() {
    return taxSummaries == null ? emptyList() : new ArrayList<>(taxSummaries.values());
  }

  public List<InvoiceTaxSummaryType> getTaxSummariesEditInvoice() {
    if (this.taxSummariesEditInvoice == null)
      this.taxSummariesEditInvoice = InvoiceEditionHelper.getTaxSummaries(invoiceXcbl);
    return taxSummariesEditInvoice;
  }

  public void setTaxSummariesEditInvoice(List<InvoiceTaxSummaryType> taxSummariesEditInvoice) {
    this.taxSummariesEditInvoice = taxSummariesEditInvoice;
  }

  public void onAddTaxSummariesEditInvoice() {
    InvoiceTaxSummaryType taxSummaryType = createInvoiceTaxSummary();
    taxSummariesEditInvoice.add(taxSummaryType);
  }

  public void onRemoveTaxSummariesEditInvoice(InvoiceTaxSummaryType taxSummaryType) {
    if (taxSummariesEditInvoice != null && taxSummaryType != null)
      taxSummariesEditInvoice.remove(taxSummaryType);
  }

  public List<TotalAllowOrChargeType> getParafiscalTaxSummaries() {
    return parafiscalTaxSummaries == null ? emptyList() : new ArrayList<>(parafiscalTaxSummaries.values());
  }

  public List<TotalAllowOrChargeType> getParafiscalTaxSummariesEditInvoice() {
    if (this.parafiscalTaxSummariesEditInvoice == null) {
      this.parafiscalTaxSummariesEditInvoice = InvoiceEditionHelper.getTotalAllowOrCharge(invoiceXcbl);
      parafiscalTaxSummariesEditInvoice
          .forEach(tax ->
          {
            String description = parafiscalTaxItems.get(tax.getAllowanceOrChargeDescription()
                .getRefID());
            tax.getAllowanceOrChargeDescription()
                .setListOfDescription(description != null ? description : "");
          });
    }
    return parafiscalTaxSummariesEditInvoice;
  }

  public void setParafiscalTaxSummariesEditInvoice(List<TotalAllowOrChargeType> parafiscalTaxSummariesEditInvoice) {
    this.parafiscalTaxSummariesEditInvoice = parafiscalTaxSummariesEditInvoice;
  }

  public void onAddParafiscalTaxSummariesEditInvoice() {
    TotalAllowOrChargeType totalAllowOrCharge = createParafiscalTaxSummaries();
    parafiscalTaxSummariesEditInvoice.add(totalAllowOrCharge);
  }

  public void onRemoveParafiscalTaxSummariesEditInvoice(TotalAllowOrChargeType totalAllowOrCharge) {
    if (parafiscalTaxSummariesEditInvoice != null && totalAllowOrCharge != null)
      parafiscalTaxSummariesEditInvoice.remove(totalAllowOrCharge);
  }

  public void updateParafiscalTaxCode(TotalAllowOrChargeType totalAllowOrCharge) {
    String description = ofNullable(totalAllowOrCharge)
        .map(TotalAllowOrChargeType::getAllowanceOrChargeDescription)
        .map(AllowOrChgDescType::getListOfDescription)
        .orElse(null);
    if (description != null) {
      totalAllowOrCharge.getAllowanceOrChargeDescription()
          .setRefID(getEanCode(description));
    }
  }

  public int getFreeQtyIndex(ListOfQuantityCodedType listOfQuantityCodedType) {
    if (listOfQuantityCodedType == null)
      return -1;
    QuantityCodedType freeQty = InvoiceEditionHelper.getFreeQuantity(listOfQuantityCodedType);
    return listOfQuantityCodedType.getQuantityCoded()
        .indexOf(freeQty);
  }

  public List<InvoiceItemDetailType> getDetails() {
    if (this.editInvoice && this.details == null) {
      this.details = ofNullable(this.invoiceXcbl.getInvoiceDetail())
          .map(InvoiceDetailType::getListOfInvoiceItemDetail)
          .map(ListOfInvoiceItemDetailType::getInvoiceItemDetail)
          .orElse(null);
    }

    if (details == null) {
      taxSummaries = new TreeMap<>();
      parafiscalTaxSummaries = new TreeMap<>();
      if (newInvoice) {
        details = new ArrayList<>();
      }
      else if (this.invoiceXcbl != null) {
        details = cloner.deepClone(this.invoiceXcbl.getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail());
        BigDecimal invoicTotalTaxAmount = ZERO;
        BigDecimal invoicTotalTaxableAmount = ZERO;

        for (InvoiceItemDetailType item : details) {
          String productIdentifier = obtainProductIdentifier(item);

          // Retrieve product from repository based first on product identifier otherwise on buyer part number
          Product product = productIdentifier.isEmpty()
              ? this.repositoryService.searchProductByBuyerPartNumber(obtainPartId(item), this.partner)
              : this.repositoryService.searchOne(Product.class, productIdentifier, this.partner);

          // since AIO-6676
          if (product != null && !getPrice(item)
              .isEmpty()) {
            product.setCalculationGross(getUnitPriceValue(getPrice(item)
                .get(getCalculationGross(item)))
                .map(BigDecimal::doubleValue)
                .orElse(null));
            product.setCalculationNet(getUnitPriceValue(getPrice(item)
                .get(getCalculationNet(item)))
                .map(BigDecimal::doubleValue)
                .orElse(null));
          }

          if (getPrice(item)
              .isEmpty()) {
            getPrice(item)
                .add(buildInvoicePriceType(CALCULATION_NET, ZERO));
            getPrice(item)
                .add(buildInvoicePriceType(CALCULATION_GROSS, ZERO));
            getPrice(item)
                .add(buildInvoicePriceType(UNIT_COST_PRICE, ZERO));
            getPrice(item)
                .add(buildInvoicePriceType(NET_ITEM_PRICE, ZERO));
          }

          InvoiceItemDetailXcblHelper.populateInvoicePricingDetail(item, product, detailPrecision, detailPrecision,
              TaxCategoryCodeType.OTHER);

          // Assuming that only handles VAT
          InvoiceTaxType tax = item.getInvoicePricingDetail()
              .getTax()
              .get(0);

          // Compute summary tax
          String taxCategory = Optional.ofNullable(tax)
              .map(InvoiceTaxType::getTaxCategoryCodedOther)
              .map(ComplexStringType::getValue)
              .orElse(null);
          if (taxCategory != null) {
            BigDecimal invoiceQty = item.getInvoiceBaseItemDetail()
                .getInvoicedQuantity()
                .getQuantityValue()
                .getValue();
            List<InvoiceAllowOrChargeType> allowOrCharges = item.getInvoicePricingDetail()
                .getItemAllowancesOrCharges()
                .getAllowOrCharge();
            InvoiceTaxSummaryXcblHelper.populateInvoiceTaxSummary(this.taxSummaries, tax, footerPrecision);
            populateInvoiceParafiscalTaxSummary(this.parafiscalTaxSummaries, allowOrCharges, footerPrecision, invoiceQty);
            invoicTotalTaxAmount = invoicTotalTaxAmount.add(tax.getTaxAmount()
                .getValue());
          }
          if (Optional.ofNullable(item)
              .map(InvoiceItemDetailType::getInvoicePricingDetail)
              .map(InvoicePricingDetailType::getLineItemTotal)
              .isPresent()) {
            invoicTotalTaxableAmount = invoicTotalTaxableAmount.add(
                item.getInvoicePricingDetail()
                    .getLineItemTotal()
                    .getMonetaryAmount()
                    .getValue());
            // TODO change this adter summary is modified
          }
        }

        InvoiceMonetaryValueType taxableValue = invoiceXcbl.getInvoiceSummary()
            .getInvoiceTotals()
            .getTaxableValue();
        taxableValue.setMonetaryAmount(toComplexBigDecimalType(taxableValue.getMonetaryAmount(), invoicTotalTaxableAmount));
        InvoiceMonetaryValueType invoiceSubTotal = invoiceXcbl.getInvoiceSummary()
            .getInvoiceTotals()
            .getInvoiceSubTotal();
        invoiceSubTotal.setMonetaryAmount(toComplexBigDecimalType(invoiceSubTotal.getMonetaryAmount(), invoicTotalTaxableAmount));
        InvoiceMonetaryValueType invoiceTotalTaxAmount = invoiceXcbl.getInvoiceSummary()
            .getInvoiceTotals()
            .getTotalTaxAmount();
        invoiceTotalTaxAmount
            .setMonetaryAmount(toComplexBigDecimalType(invoiceTotalTaxAmount.getMonetaryAmount(), invoicTotalTaxAmount));
        InvoiceXcblHelper.applyAllowOrChrgOnTaxableValue(this.invoiceXcbl, this.allowOrChargeTypes, invoicTotalTaxableAmount,
            this.taxExempt,
            footerPrecision);
        InvoiceXcblHelper.applyTaxOnHeaderAllowanceCharge(this.allowOrChargeTypes, this.taxSummaries, this.invoiceXcbl, this.getDetails(),
            this.taxExempt, this.detailPrecision, this.footerPrecision);
        InvoiceXcblHelper.computeInvoiceTotal(this.invoiceXcbl, this.taxExempt, this.footerPrecision);
        computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
      }
    }
    return details;
  }

  private List<InvoicePriceType> getPrice(InvoiceItemDetailType item) {
    return of(item).map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoicePricingDetailType::getListOfPrice)
        .map(ListOfInvoicePriceType::getPrice)
        .orElseGet(() -> new ArrayList<>());
  }

  public void setDetails(List<InvoiceItemDetailType> details) {
    this.details = details;
  }

  public void setAllowOrChargeTypes(List<InvoiceAllowOrChargeType> allowOrChargeTypes) {
    this.allowOrChargeTypes = allowOrChargeTypes;
  }

  public List<InvoiceAllowOrChargeType> getAllowOrChargeTypes() {
    return isEmpty(allowOrChargeTypes)
        ? initInvoiceHeaderAllowOrCharge()
        : allowOrChargeTypes;
  }

  private List<InvoiceAllowOrChargeType> initInvoiceHeaderAllowOrCharge() {
    allowOrChargeTypes = ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceAllowancesOrCharges)
        .map(ListOfInvoiceAllowOrChargeType::getAllowOrCharge)
        .orElse(new ArrayList<>());
    if (isNotEmpty(allowOrChargeTypes))
      allowOrChargeTypes.stream()
          .filter(allchr -> allchr.getAllowanceOrChargeDescription()
              .getServiceCoded() == null)
          .forEach(allchr -> allchr.getAllowanceOrChargeDescription()
              .setServiceCoded(allchr.getIndicatorCoded()
                  .value()));
    return allowOrChargeTypes;
  }

  public int getCalculationNet(InvoiceItemDetailType detail) {
    int index = InvoiceItemDetailXcblHelper.getPriceIndex(detail, NET_ITEM_PRICE);
    if (index < 0) {
      index = InvoiceItemDetailXcblHelper.getPriceIndex(detail, CALCULATION_NET);
    }
    return index;
  }

  public int getCalculationGross(InvoiceItemDetailType detail) {
    int index = InvoiceItemDetailXcblHelper.getPriceIndex(detail, UNIT_COST_PRICE);
    if (index < 0) {
      index = InvoiceItemDetailXcblHelper.getPriceIndex(detail, CALCULATION_GROSS);
    }
    return index;
  }

  public int findNetPriceIndex(InvoiceItemDetailType detail) {
    int index = -1;
    List<InvoicePriceType> priceList = getPrice(detail);
    Optional<InvoicePriceType> optPriceType = InvoiceItemDetailXcblHelper.getPriceType(detail, NET_ITEM_PRICE);
    if (optPriceType.isPresent()) {
      index = priceList.indexOf(optPriceType.get());
      if (index > -1)
        netPriceDecimalPlaces = computeDecimalPlaces(optPriceType);
    }
    else {
      optPriceType = InvoiceItemDetailXcblHelper.getPriceType(detail, CALCULATION_NET);
      if (optPriceType.isPresent()) {
        index = priceList.indexOf(optPriceType.get());
        if (index > -1)
          netPriceDecimalPlaces = computeDecimalPlaces(optPriceType);
      }
    }
    if (index < 0) {
      index = InvoiceEditionHelper.addPriceType(detail, priceList, CALCULATION_NET);
    }
    netPriceIndex = index;
    return index;
  }

  public int findGrossPriceIndex(InvoiceItemDetailType detail) {
    int index = -1;
    List<InvoicePriceType> priceList = getPrice(detail);
    Optional<InvoicePriceType> optPriceType = InvoiceItemDetailXcblHelper.getPriceType(detail, UNIT_COST_PRICE);
    if (optPriceType.isPresent()) {
      index = priceList.indexOf(optPriceType.get());
      if (index > -1)
        grossPriceDecimalPlaces = computeDecimalPlaces(optPriceType);
    }
    else {
      optPriceType = InvoiceItemDetailXcblHelper.getPriceType(detail, CALCULATION_GROSS);
      if (optPriceType.isPresent()) {
        index = priceList.indexOf(optPriceType.get());
        if (index > -1)
          grossPriceDecimalPlaces = computeDecimalPlaces(optPriceType);
      }
    }
    if (index < 0) {
      index = InvoiceEditionHelper.addPriceType(detail, priceList, CALCULATION_GROSS);
    }
    this.grossPriceIndex = index;
    InvoiceItemDetailXcblHelper.initPriceType(priceList.get(index));
    return index;
  }

  public boolean isAllowAddition() {
    return allowAddition;
  }

  public void setAllowAddition(boolean allowAddition) {
    this.allowAddition = allowAddition;
  }

  public String getCurrentInvoiceReferenceType() {
    return currentInvoiceReferenceType;
  }

  public void setCurrentInvoiceReferenceType(final String currentInvoiceReferenceType) {
    this.currentInvoiceReferenceType = currentInvoiceReferenceType;
  }

  public void onSelectNewReference() {
    if (currentInvoiceReferenceType != null) {
      final InvoiceReference invoiceReference = new InvoiceReference(InvoiceReferenceType.valueOf(currentInvoiceReferenceType));

      invoiceReference.setNumber(new ComplexStringType());
      invoiceReference.setDate(new ComplexDateType());
      invoiceReference.setPeriodEndDate(new ComplexDateType());
      invoiceReference.setPeriodStartDate(new ComplexDateType());

      if (this.invoiceReferenceEdit) {
        invoiceReferences.remove(currentInvoiceReference);
      }
      this.invoiceReferences.add(invoiceReference);
    }
    currentInvoiceReferenceType = null;
  }

  public void onSelectNewReferenceCustomer() {
    currentInvoiceReferenceType = "CONTRACT";
    this.onSelectNewReference();
  }

  public Collection<SelectItem> listInvoiceReferenceType() {
    return invoiceReferenceTypes;
  }

  public String i18nInvoiceReferenceType(final InvoiceReferenceType type) {
    return i18n(LABEL_FAMILY + ".invoice_reference_type_" + type.name());
  }

  public Collection<InvoiceReference> getInvoiceReferences() {
    if (invoiceReferences == null) {
      if (invoiceXcbl != null && invoiceXcbl.getInvoiceHeader() != null && invoiceXcbl.getInvoiceHeader()
          .getInvoiceReferences() != null) {
        initInvoiceReferences();
      }
      else {
        invoiceReferences = emptyList();
      }
    }
    return invoiceReferences;
  }

  private void initInvoiceReferences() {
    invoiceReferences = new LinkedList<>();
    List<InvoiceReferencesType> invoiceReferenceTypeListe = Optional.ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceReferences)
        .orElse(Collections.emptyList());
    for (final InvoiceReferencesType invoiceReferencesType : invoiceReferenceTypeListe) {
      unmapInvoiceReferenceToXcblType(invoiceReferencesType)
          .forEach(this.invoiceReferences::add);
    }
  }

  public void onInvoiceReferenceRowEditInit(final RowEditEvent event) {
    InvoiceReference invoiceReference = (InvoiceReference) event.getObject();
    InvoiceReference initInvoiceReference = XcblHelper.cloner.deepClone(invoiceReference);
    mapInvoiceReferences.put(initInvoiceReference.getUuid(), initInvoiceReference);
    invoiceReferenceEdit = true;
  }

  public void onInvoiceReferenceRowEdit(final RowEditEvent event) {
    final InvoiceReference invoiceReference = (InvoiceReference) event.getObject();

    if (checkInvoiceReference(invoiceReference)) {
      invoiceReferenceEdit = false;
      mapInvoiceReferences.remove(invoiceReference.getUuid());
    }
    else {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
    }
  }

  public void onInvoiceReferenceRowCancel(final RowEditEvent event) {
    InvoiceReference invoiceReference = (InvoiceReference) event.getObject();
    InvoiceReference initInvoiceReference = mapInvoiceReferences.get(invoiceReference.getUuid());
    if (!(initInvoiceReference.getDate()
        .getValue() == null &&
        initInvoiceReference.getPeriodEndDate()
            .getValue() == null &&
        initInvoiceReference.getPeriodStartDate()
            .getValue() == null &&
        invoiceReference.getDate()
            .getValue() != null &&
        invoiceReference.getPeriodEndDate()
            .getValue() != null &&
        invoiceReference.getPeriodStartDate()
            .getValue() != null)) {
      invoiceReference.setDate(initInvoiceReference.getDate());
    }
    invoiceReference.setPeriodStartDate(initInvoiceReference.getPeriodStartDate());
    invoiceReference.setPeriodEndDate(initInvoiceReference.getPeriodEndDate());
    mapInvoiceReferences.remove(invoiceReference.getUuid());
    invoiceReferenceEdit = false;
  }

  public boolean checkInvoiceReference(final InvoiceReference invoiceReference) {
    notNull(invoiceReference, "Invoice reference is required");

    boolean dataPresence = checkInvoiceDataPresence(invoiceReference);
    boolean dateCoherence = checkInvoiceDateCoherence(invoiceReference);

    return dataPresence && dateCoherence && checkInvoiceDatePresence(invoiceReference);
  }

  // TODO : Refacto -> InvoiceXcblHelper
  private boolean checkInvoiceDataPresence(final InvoiceReference invoiceReference) {
    boolean hasType = invoiceReference.getType() != null;
    if (!hasType) {
      error(LABEL_FAMILY + ".invoice_reference_type_required");
    }

    boolean hasNumber = StringUtils.isNotEmpty(InvoiceReferenceXcblHelper.getInvoiceReferenceNumber(invoiceReference));
    if (!hasNumber) {
      MessageHelper.addMessage(FacesMessage.SEVERITY_ERROR,
          getMessage(LABEL_FAMILY + ".invoice_reference_num", "Reference number",
              selectedLanguage) + " " +
              getMessage("gnxxcblcomlbls.value_required",
                  " is required", selectedLanguage));
    }

    return hasType && hasNumber;
  }

  // TODO : Refacto -> InvoiceXcblHelper
  private boolean checkInvoiceDateCoherence(final InvoiceReference invoiceReference) {
    if (InvoiceReferenceXcblHelper.getInvoiceReferenceDate(invoiceReference) == null) {
      if (getPeriodEndDateFromInvoiceReference(invoiceReference) == null ||
          getPeriodStartDateFromInvoiceReference(invoiceReference) == null) {
        error(LABEL_FAMILY + ".invoice_reference_date_or_period_required");
        return false;
      }
      if (getPeriodEndDateFromInvoiceReference(invoiceReference)
          .before(getPeriodStartDateFromInvoiceReference(invoiceReference))) {
        error(LABEL_FAMILY + ".invoice_reference_period_coherence");
        return false;
      }
    }
    else if (getPeriodStartDateFromInvoiceReference(invoiceReference) != null ||
        getPeriodEndDateFromInvoiceReference(invoiceReference) != null) {
      error(LABEL_FAMILY + ".invoice_reference_date_only");
      return false;
    }

    return true;
  }

  // TODO : Refacto -> InvoiceXcblHelper
  private boolean checkInvoiceDatePresence(final InvoiceReference invoiceReference) {
    if (invoiceReference.getType()
        .isPeriodRequired()) {
      if (getPeriodStartDateFromInvoiceReference(invoiceReference) == null ||
          getPeriodEndDateFromInvoiceReference(invoiceReference) == null) {
        error(LABEL_FAMILY + ".invoice_reference_period_required");
        return false;
      }
    }
    else if ((InvoiceReferenceXcblHelper.getInvoiceReferenceDate(invoiceReference) == null) &&
        (getPeriodStartDateFromInvoiceReference(invoiceReference) == null ||
            getPeriodEndDateFromInvoiceReference(invoiceReference) == null)) {
      error(LABEL_FAMILY + ".invoice_reference_date_required");
      return false;
    }

    return true;
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  public void error(String msgkey) {
    MessageHelper.error(msgkey);
  }

  public void onRemoveInvoiceReference(final InvoiceReference invoiceReference) {
    if (invoiceReference != null) {
      invoiceReferenceEdit = false;
      invoiceReferences.remove(invoiceReference);
    }
  }

  public String getCurrentAllowOrChrg() {
    return currentAllowOrChrg;
  }

  public void setCurrentAllowOrChrg(String currentAllowOrChrg) {
    this.currentAllowOrChrg = currentAllowOrChrg;
  }

  public void onSelectAllowOrCharge() {
    if (currentAllowOrChrg != null) {
      InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();

      if (listOfGlobalTax.containsKey(currentAllowOrChrg)) {
        ObjectAttribute object = listOfGlobalTax.get(currentAllowOrChrg);
        InvoiceAllowOrChargeXcblHelper.populateAllowanceOrCharge(allowOrChargeType, object, false);
      }
      else {
        allowOrChargeType.getTypeOfAllowanceOrCharge()
            .setMonetaryValue(null);
        updateIndicatorCodedOfAllowOrCharge(allowOrChargeType, currentAllowOrChrg);
      }

      if (this.allowOrChrgEdit) {
        allowOrChargeTypes.set(this.allowOrChargeTypes.size() - 1, allowOrChargeType);
      }
      else {
        this.allowOrChargeTypes.add(allowOrChargeType);
        this.allowOrChrgEdit = true;
      }
      currentAllowOrChrg = null;
    }
  }

  public void onAddAllowOrCharge(String type) {
    if (this.editInvoice)
      this.allowAddition = true;
    currentAllowOrChrg = type;
    getListOfGlobalTax(true);
    onSelectAllowOrCharge();
  }

  public Map<String, ObjectAttribute> getListOfGlobalTax(boolean excludeRegulatoryTaxes) {
    if (listOfGlobalTax == null) {
      listOfGlobalTax = new TreeMap<>();
      List<InvoiceParameter> invoiceParameters = repositoryService.findAllByGroup(InvoiceParameter.class, this.partner);
      if (isNotEmpty(invoiceParameters)) {
        listOfGlobalTax = invoiceParameters.get(0)
            .getObjectAttribute()
            .stream()
            .filter(objAttribute -> !excludeRegulatoryTaxes ||
                !StringUtils.equals(objAttribute.getCodeAttribute(), AllowanceOrCharge.TAX_REGULATORY_TAX.toString()))
            .collect(Collectors.toMap(ob -> ob.getDescription() + "-" + ob.getDoubleValue()
                .toString(), Function.identity(), (key1, key2) -> key1));
      }
    }
    return listOfGlobalTax;
  }

  public List<SelectItem> getListOfGlobalTaxItems() {
    return getListOfGlobalTax(true).values()
        .stream()
        .map(
            item -> new SelectItem(item.getDescription() + "-" + item.getDoubleValue(),
                item.getDescription()))
        .collect(toCollection(ArrayList::new));
  }

  public void onRowInit(RowEditEvent event) {
    if (!this.editInvoice)
      MessageHelper.addMessage(null, FacesMessage.SEVERITY_WARN, "gnxxcblcomlbls.validation_msg");
  }

  public void onInvoiceAllowOrChargeRowEditInit(final RowEditEvent event) {
    this.allowOrChrgEdit = true;
  }

  public void onRowEdit(RowEditEvent event) {
    InvoiceAllowOrChargeType allowOrChargeType = (InvoiceAllowOrChargeType) event.getObject();
    String serviceCoded = allowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCoded();
    if (serviceCoded == null || ALLOWANCE.value()
        .equals(serviceCoded) || CHARGE.value()
        .equals(serviceCoded))
      allowOrChargeType.getAllowanceOrChargeDescription()
          .setServiceCoded(allowOrChargeType.getIndicatorCoded()
              .value());
    if (this.editInvoice) {
      this.allowOrChrgEdit = false;
      return;
    }
    // recalcul all
    BigDecimal refreshCurrentTotalTaxableAmount = computeInvoiceTotalTaxable(this.invoiceXcbl, this.getDetails(), this.footerPrecision,
        this.taxExempt);
    applyAllowOrChrgOnTaxableValue(this.invoiceXcbl, this.allowOrChargeTypes, refreshCurrentTotalTaxableAmount, this.taxExempt,
        footerPrecision);
    applyTaxOnHeaderAllowanceCharge(this.allowOrChargeTypes, this.taxSummaries, this.invoiceXcbl, this.getDetails(), this.taxExempt,
        this.detailPrecision, this.footerPrecision);
    computeInvoiceTotal(this.invoiceXcbl, this.taxExempt, this.footerPrecision);
    computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
    this.allowOrChrgEdit = false;
    this.allowAddition = false;
  }

  public void onRowCancel(RowEditEvent event) {
    InvoiceAllowOrChargeType allowOrChargeType = (InvoiceAllowOrChargeType) event.getObject();
    if (isBlank(getListOfDescriptionFromInvoiceAllowanceOrCharge(allowOrChargeType)) || allowOrChargeType.getIndicatorCoded() == null ||
        allowOrChargeType.getBasisCoded() == null) {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      return;
    }
    this.allowOrChrgEdit = false;
    this.allowAddition = false;
  }

  public List<SelectItem> getIndicatorCodeItems() {
    return asList(new SelectItem(IndicatorCodeType.ALLOWANCE,
            getMessage(LABEL_FAMILY + ".ALLOWANCE", "Allowance", selectedLanguage)),
        new SelectItem(IndicatorCodeType.CHARGE,
            getMessage(LABEL_FAMILY + ".CHARGE", "Charge", selectedLanguage)));
  }

  public List<SelectItem> getTaxItems() {
    return getListCodeTva().values()
        .stream()
        .map(tva -> new SelectItem(String.format(Locale.ROOT, TAX_FORMAT, tva.getPercent()),
            String.format(Locale.ROOT, TAX_FORMAT + "%%", tva.getPercent())))
        .collect(toList());
  }

  public Map<String, Tax> getListCodeTva() {
    if (listCodeTva == null) {
      listCodeTva = new TreeMap<>();
      List<Tax> liste = repositoryService.findAllByGroup(Tax.class, this.partner);
      liste.forEach(tax -> listCodeTva.put(String.format(Locale.ROOT, TAX_FORMAT, tax.getPercent()), tax));
    }
    return listCodeTva;
  }

  public void onRemoveAllowOrCharge(InvoiceAllowOrChargeType allowOrChargeType) {
    if (allowOrChargeType != null) {
      this.allowOrChargeTypes.remove(allowOrChargeType);
    }
    if (this.editInvoice){
      this.allowOrChrgEdit = false;
      return;
    }
    BigDecimal currentTotalTaxableAmount = computeInvoiceTotalTaxable(this.invoiceXcbl, this.getDetails(), this.footerPrecision,
        this.taxExempt);
    applyAllowOrChrgOnTaxableValue(this.invoiceXcbl, this.allowOrChargeTypes, currentTotalTaxableAmount, this.taxExempt, footerPrecision);
    applyTaxOnHeaderAllowanceCharge(this.allowOrChargeTypes, this.taxSummaries, this.invoiceXcbl, this.getDetails(), this.taxExempt,
        this.detailPrecision, this.footerPrecision);
    this.allowOrChrgEdit = false;
  }

  public String getProductSelected() {
    return this.productSelected;
  }

  public void setProductSelected(String productSelected) {
    this.productSelected = productSelected;
  }

  public List<SelectItem> getProductItems() { // TODO Implement lazy loading
    // on search query on list of
    // product
    resolveOrganizationCodes();

    List<Product> products = this.repositoryService.findAllByGroup(Product.class, partner);

    List<SelectItem> items = products == null || isEmpty(products) ? new ArrayList<>(1)
        : products.stream()
            .map(product -> new SelectItem(product.getReference(), product.getProductName()))
            .collect(toCollection(ArrayList::new));

    items.add(0, new SelectItem(NEW, getMessage(LABEL_FAMILY + ".new", NEW, selectedLanguage)));
    return items;
  }

  public List<Product> onCompleteProduct(String query) {
    resolveOrganizationCodes();
    return repositoryService.searchProductsByRefOrName(companyCode, partnerCode, query, 100, Product.FIELD_REFERENCE);
  }

  public void onSelectProduct() {
    if (!isBlank(this.productSelected)) {
      this.product = InvoiceItemDetailXcblHelper.createInvoiceItemDetail();
      this.product.getInvoiceBaseItemDetail()
          .getLineItemNum()
          .setBuyerLineItemNum(this.details.size() + 1);
      if (!NEW.equals(productSelected)) {
        Product prdct = this.repositoryService.searchOne(Product.class, this.productSelected, partner);
        // if VAT exempted, force product VAT to 0 when calculating the pricing details
        if (this.taxExempt) {
          prdct.setVat(0D);
        }
        InvoiceItemDetailXcblHelper.populateInvoiceItemDetail(product, prdct, detailPrecision, detailPrecision, TaxCategoryCodeType.OTHER);
      }
      else {
        InvoiceItemDetailXcblHelper.populateItemDetailWithUnitPrice(product, null, detailPrecision);
        product.getInvoicePricingDetail()
            .getTax()
            .add(createInvoiceTaxtype());
      }

      this.details.add(product);
      // Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemReferences/CostAllocation/CostAllocationNumber/RefNum
      for (InvoiceItemDetailType invoiceItemDetailType : this.details) {
        if (invoiceItemDetailType.getInvoiceBaseItemDetail()
            .getLineItemReferences() == null) {
          InvoiceReferencesType invoiceReferencesType = InvoiceXcblHelper.initInvoiceReferencesType(null);
          invoiceItemDetailType.getInvoiceBaseItemDetail()
              .setLineItemReferences(invoiceReferencesType);
        }
      }
      this.productSelected = null;
    }
  }

  public void onSelectProductCustomer(String value) {
    productSelected = value;
    onSelectProduct();
  }

  public InvoiceItemDetailType getProduct() {
    return product;
  }

  public PropertyDescriptor getDetailColumnDescriptor(String name) {
    if (isBlank(name)) {
      return new PropertyDescriptor("");
    }
    PropertyDescriptor prop = getDetailDescriptor().get(name);
    return prop == null ? new PropertyDescriptor("") : prop;
  }

  public BigDecimal getNetTotal(final InvoiceItemDetailType selectedItem) {
    if (this.editInvoice) {
      // in editInvoice the net total should not be recalculated
      BigDecimal netTotal = Optional.ofNullable(selectedItem)
          .map(InvoiceItemDetailType::getInvoicePricingDetail)
          .map(InvoicePricingDetailType::getLineItemSubTotal)
          .map(InvoiceMonetaryValueType::getMonetaryAmount)
          .map(ComplexBigDecimalType::getValue)
          .orElse(null);
      return netTotal != null ? netTotal.setScale(detailPrecision, HALF_EVEN) : null;
    }
    int index = getCalculationNet(selectedItem);
    if (ofNullable(getPrice(selectedItem)
        .get(index)
        .getUnitPrice()
        .getUnitPriceValue()
        .getValue()).isPresent()) {

      BigDecimal netTotal = getUnitPriceValue(getPrice(selectedItem)
          .get(index)).filter(Objects::nonNull)
          .get()
          .multiply(selectedItem.getInvoiceBaseItemDetail()
              .getInvoicedQuantity()
              .getQuantityValue()
              .getValue());
      return netTotal.setScale(detailPrecision, HALF_EVEN);
    }
    return null;
  }

  public int getTaxPrecision() {
    return TAX_PRECISION;
  }

  public void onSelectItemDetail(InvoiceItemDetailType detail) {
    this.detailsBackup = cloner.deepClone(this.getDetails());
    this.product = detail;
  }

  public void removeInvoiceItemDetails(InvoiceItemDetailType detail) {
    if (detail == null) {
      return;
    }
    if (this.editInvoice) {
      this.details.remove(detail);
      return;
    }
    this.details.remove(detail);

    if (getSubTotalMonetaryAmmountOptional(detail)
        .isPresent()) {

      InvoiceTotalsType invoiceTotals = getInvoiceTotals(invoiceXcbl).get();

      BigDecimal netAmount = getSubTotalMonetaryAmmount(detail).getValue();
      BigDecimal totalSubAmount = Optional.ofNullable(invoiceTotals)
          .map(InvoiceTotalsType::getInvoiceSubTotal)
          .map(InvoiceMonetaryValueType::getMonetaryAmount)
          .map(ComplexBigDecimalType::getValue)
          .orElse(null);

      if (netAmount != null) {
        if (totalSubAmount != null) {
          BigDecimal amount = totalSubAmount.subtract(netAmount)
              .setScale(this.footerPrecision, HALF_EVEN);
          invoiceTotals.getInvoiceSubTotal()
              .setMonetaryAmount(toComplexBigDecimalType(invoiceTotals.getInvoiceSubTotal()
                  .getMonetaryAmount(), amount));
        }
        BigDecimal totalTaxableAmount = getTaxableValueFromInvoiceSummary(invoiceTotals.getTaxableValue());
        if (totalTaxableAmount != null) {
          BigDecimal amount = totalTaxableAmount.subtract(netAmount)
              .setScale(this.footerPrecision, HALF_EVEN);
          InvoiceMonetaryValueType taxableValue = invoiceTotals.getTaxableValue();
          taxableValue.setMonetaryAmount(toComplexBigDecimalType(taxableValue.getMonetaryAmount(), amount));
        }
      }
      if (!taxExempt) {
        BigDecimal taxAmount = detail.getInvoicePricingDetail()
            .getTax()
            .get(0)
            .getTaxAmount()
            .getValue();
        BigDecimal totalTaxAmount = getTotalTaxAmountFromInvoiceSummary(invoiceTotals.getTotalTaxAmount());
        if (taxAmount != null && totalTaxAmount != null) {
          BigDecimal amount = totalTaxAmount.subtract(taxAmount)
              .setScale(this.footerPrecision, HALF_EVEN);
          InvoiceMonetaryValueType totalTAmount = invoiceTotals.getTotalTaxAmount();
          totalTAmount.setMonetaryAmount(toComplexBigDecimalType(totalTAmount.getMonetaryAmount(), amount));
        }
        this.taxSummaries.clear();
        this.parafiscalTaxSummaries.clear();
        populateInvoiceTaxSummary(this.taxSummaries, details, footerPrecision);
        if (!details.isEmpty()) {
          for (InvoiceItemDetailType itemDetail : details) {
            List<InvoiceAllowOrChargeType> allowOrCharges = ofNullable(itemDetail.getInvoicePricingDetail())
                .map(OcrPricingDetailType::getItemAllowancesOrCharges)
                .map(ListOfInvoiceAllowOrChargeType::getAllowOrCharge)
                .orElse(emptyList())
                .stream()
                .filter(i -> SERVICE.equals(i.getIndicatorCoded()))
                .collect(toList());
            BigDecimal invoiceQty = itemDetail.getInvoiceBaseItemDetail()
                .getInvoicedQuantity()
                .getQuantityValue()
                .getValue();
            populateInvoiceParafiscalTaxSummary(this.parafiscalTaxSummaries, allowOrCharges, footerPrecision, invoiceQty);
          }
        }
      }
    }
    BigDecimal refreshCurrentTotalTaxableAmount = computeInvoiceTotalTaxable(this.invoiceXcbl, this.getDetails(), this.footerPrecision,
        this.taxExempt);
    applyAllowOrChrgOnTaxableValue(this.invoiceXcbl, this.allowOrChargeTypes, refreshCurrentTotalTaxableAmount, this.taxExempt,
        footerPrecision);
    applyTaxOnHeaderAllowanceCharge(this.allowOrChargeTypes, this.taxSummaries, this.invoiceXcbl, this.getDetails(), this.taxExempt,
        this.detailPrecision, this.footerPrecision);
    computeInvoiceTotal(this.invoiceXcbl, this.taxExempt, this.footerPrecision);
    computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
  }

  public String getTaxCategoryCoded(InvoiceAllowOrChargeType allwOrChg) {
    if (allwOrChg == null || CollectionUtils.isEmpty(allwOrChg.getTax()))
      return null;
    return Optional.ofNullable(allwOrChg)
        .map(InvoiceAllowOrChargeType::getTax)
        .map(List::stream)
        .orElse(Stream.empty())
        .findFirst()
        .map(InvoiceTaxType::getTaxCategoryCoded)
        .map(TaxCategoryCodeType::toString)
        .orElse("");
  }

  public QuantityValueType getFreeQuantityValue(ListOfQuantityCodedType listOfQuantityCodedType) {
    return InvoiceEditionHelper.getFreeQuantityValue(listOfQuantityCodedType);
  }

  public Boolean hasAllowOrCharge(final InvoiceItemDetailType detail) {
    notNull(detail, "InvoiceDetail is mandatory");

    // GNX-2517
    // correction npe sur portlet facturation.
    // La méthode est appelée sur la portlet d'édition d'un facture
    // (edit-invoice.xhtml)
    // Les propriétés du pricing détail sont null si l'utilisateur provient
    // de la portlet de
    // commande depuis l'action "Facturé" d'une commande.
    return detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges() != null &&
        !detail.getInvoicePricingDetail()
            .getItemAllowancesOrCharges()
            .getAllowOrCharge()
            .isEmpty();
  }

  public String getInlineAllowOfCharge(final InvoiceItemDetailType detail) {
    notNull(detail, "InvoiceDetail is mandatory");

    return detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .stream()
        .map(InvoiceAllowOrChargeXcblHelper::inline)
        .collect(joining("\n"));
  }

  public ComplexStringType getBuyerPartID() {
    return InvoiceItemDetailXcblHelper.getBuyerPartID(product);
  }

  public void setBuyerPartID(String buyerPartID) {
    InvoiceItemDetailXcblHelper.setBuyerPartID(buyerPartID, product);
  }

  public ComplexStringType getSellerPartID() {
    return InvoiceItemDetailXcblHelper.getSellerPartID(product);
  }

  public void setSellerPartID(String sellerPartID) {
    InvoiceItemDetailXcblHelper.setSellerPartID(sellerPartID, product);
  }

  public ComplexStringType getBuyerOrderNumber() {
    return InvoiceItemDetailXcblHelper.getBuyerOrderNumber(product);
  }

  public void setBuyerOrderNumber(String buyerOrderNumber) {
    InvoiceItemDetailXcblHelper.setBuyerOrderNumber(buyerOrderNumber, product);
  }

  public ComplexStringType getAsnNumber() {
    return InvoiceItemDetailXcblHelper.getAsnNumber(product);
  }

  public void setAsnNumber(String asnNumber) {
    InvoiceItemDetailXcblHelper.setAsnNumber(asnNumber, product);
  }

  public ComplexStringType getASNNumber(InvoiceItemDetailType detail) {
    InvoiceOCRReferenceType asnNumber = Optional.ofNullable(detail)
        .map(InvoiceItemDetailType::getInvoiceBaseItemDetail)
        .map(InvoiceBaseItemDetailType::getLineItemReferences)
        .map(InvoiceReferencesType::getASNNumber)
        .orElse(null);
    return asnNumber != null ? asnNumber.getRefNum() : null;
  }

  public ComplexStringType getStandardProductIdentifier() {
    return InvoiceItemDetailXcblHelper.getStandardProductIdentifier(product);
  }

  public void setStandardProductIdentifier(String standardProductIdentifier) {
    InvoiceItemDetailXcblHelper.setStandardProductIdentifier(standardProductIdentifier, product);
  }

  public ComplexStringType getItemDesription() {
    return InvoiceItemDetailXcblHelper.getItemDesription(product);
  }

  public void refreshTotalLine(AjaxBehaviorEvent event) {
    applyAllowanceOrChargeOnSelectedDetail();
  }

  private void computeTaxAmountValue(BigDecimal netPrice, InvoiceItemDetailType detail) {
    InvoiceTaxType tax = ofNullable(detail).map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(OcrPricingDetailType::getTax)
        .orElse(null)
        .get(0);
    if (tax != null) {
      String taxValue = tax.getTaxCategoryCodedOther()
          .getValue();
      BigDecimal netPriceComputed = netPrice
          .multiply(getQuantity(detail).orElse(ZERO));
      BigDecimal taxAmount = netPriceComputed.multiply(taxValue != null ? new BigDecimal(taxValue) : BigDecimal.ZERO)
          .divide(new BigDecimal(100), HALF_EVEN);
      tax.setTaxAmount(toComplexBigDecimalType(tax.getTaxAmount(), taxAmount));
    }
  }

  private void applyAllowanceOrChargeOnSelectedDetail() {
    applyAllowanceOrChargeOnDetail(product);
  }

  private void applyAllowanceOrChargeOnDetail(InvoiceItemDetailType detail) {
    List<InvoiceAllowOrChargeType> allowOrCharges = getAllowOrChargeFromSelectedDetail(detail);

    BigDecimal lineItemTotal;
    BigDecimal lineItemSubTotal;

    BigDecimal unitCostPrice = getUnitPriceValue(getPrice(detail)
        .get(getCalculationGross(detail))).filter(Objects::nonNull)
        .orElse(ZERO);
    unitCostPrice = unitCostPrice.setScale(detailPrecision, HALF_EVEN);
    BigDecimal netPriceComputed = unitCostPrice;

    for (InvoiceAllowOrChargeType allOrChrg : allowOrCharges) {
      netPriceComputed = InvoiceAllowOrChargeXcblHelper.computeInvoiceAllowanceOrChargeOnSelectedDetail(allOrChrg, netPriceComputed)
          .setScale(detailPrecision, HALF_EVEN);
      computeInvoiceTaxOnComputedPrice(allOrChrg, detail, netPriceComputed);
    }

    // Update net price after allowance or charge applied
    InvoicePriceType invoicePriceType = getPrice(detail)
        .get(getCalculationNet(detail));
    invoicePriceType
        .getUnitPrice()
        .setUnitPriceValue(toComplexBigDecimalType(invoicePriceType
            .getUnitPrice()
            .getUnitPriceValue(), netPriceComputed));

    // compute the tax amount value based on net price and tax
    computeTaxAmountValue(netPriceComputed, detail);

    lineItemTotal = netPriceComputed
        .multiply(detail.getInvoiceBaseItemDetail()
            .getInvoicedQuantity()
            .getQuantityValue()
            .getValue());

    lineItemSubTotal = unitCostPrice
        .multiply(detail.getInvoiceBaseItemDetail()
            .getInvoicedQuantity()
            .getQuantityValue()
            .getValue());

    detail.getInvoicePricingDetail()
        .getLineItemTotal()
        .setMonetaryAmount(toComplexBigDecimalType(detail.getInvoicePricingDetail()
            .getLineItemTotal()
            .getMonetaryAmount(), lineItemTotal != null ? lineItemTotal : ZERO));

    InvoiceMonetaryValueType lineItemSubTotalMonetaryValue = of(detail).map(InvoiceItemDetailType::getInvoicePricingDetail)
        .map(InvoiceItemDetailXcblHelper.INVOICE_LINE_ITEM_SUB_TOTAL)
        .orElse(new InvoiceMonetaryValueType());
    detail.getInvoicePricingDetail()
        .setLineItemSubTotal(lineItemSubTotalMonetaryValue);

    lineItemSubTotalMonetaryValue
        .setMonetaryAmount(toComplexBigDecimalType(lineItemSubTotalMonetaryValue.getMonetaryAmount(),
            lineItemSubTotal != null ? lineItemSubTotal : ZERO));
  }

  public IndicatorCodeType getCurrentAllowOrChrgLine() {
    return currentAllowOrChrgLine;
  }

  public void setCurrentAllowOrChrgLine(IndicatorCodeType currentAllowOrChrgLine) {
    this.currentAllowOrChrgLine = currentAllowOrChrgLine;
  }

  public void onSelectAllowOrChargeLine() {
    if (currentAllowOrChrgLine != null) {
      if (this.product.getInvoicePricingDetail()
          .getItemAllowancesOrCharges() == null) {
        this.product.getInvoicePricingDetail()
            .setItemAllowancesOrCharges(new ListOfInvoiceAllowOrChargeType());
      }
      InvoiceAllowOrChargeType allowOrChargeType = currentAllowOrChrgLine.equals(IndicatorCodeType.SERVICE)
          ? InvoiceAllowOrChargeXcblHelper.createInvoiceParafiscalTax()
          : InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
      allowOrChargeType.setIndicatorCoded(currentAllowOrChrgLine);
      allowOrChargeType.getTypeOfAllowanceOrCharge()
          .setMonetaryValue(null);

      addAllowOrChargeLine(allowOrChargeType, this.allowOrChrgLineEdit, this.product);
      this.allowOrChrgLineEdit = true;
      this.currentAllowOrChrgLine = null;
    }
  }

  public List<ValueControl> getParafiscalTaxDescriptionItems() {
    return valuesControlService.getValues(PARAFISCAL_TAX);
  }

  public void onSelectAllowLine() {
    if (this.editInvoice) {
      this.allowAddition = true;
      this.allowOrChrgLineEdit = false;
    }
    currentAllowOrChrgLine = IndicatorCodeType.LINE_ITEM_ALLOWANCE;
    onSelectAllowOrChargeLine();
  }

  public void onSelectChargeLine() {
    if (this.editInvoice) {
      this.allowAddition = true;
      this.allowOrChrgLineEdit = false;
    }
    currentAllowOrChrgLine = IndicatorCodeType.LINE_ITEM_CHARGE;
    onSelectAllowOrChargeLine();
  }

  public void onSelectParafiscalTaxLine() {
    if (this.editInvoice) {
      this.allowAddition = true;
      this.allowOrChrgLineEdit = false;
    }
    currentAllowOrChrgLine = IndicatorCodeType.SERVICE;
    onSelectAllowOrChargeLine();
  }

  public Map<String, IndicatorCodeType> getListOfTaxLines() {
    if (listOfTaxLine == null) {
      listOfTaxLine = new TreeMap<>();
      listOfTaxLine.put(getMessage(LABEL_FAMILY + ".ALLOWANCE", "Allowance", selectedLanguage), IndicatorCodeType.LINE_ITEM_ALLOWANCE);
      listOfTaxLine.put(getMessage(LABEL_FAMILY + ".CHARGE", "Charge", selectedLanguage), IndicatorCodeType.LINE_ITEM_CHARGE);
    }
    return listOfTaxLine;
  }

  public void onRowEditAllOrChrgLine(RowEditEvent event) {
    if (this.editInvoice) {
      return;
    }
    InvoiceAllowOrChargeType allwOrChrg = (InvoiceAllowOrChargeType) event.getObject();
    setRightChargeAllowanceIndicatorCodeForLine(allwOrChrg);
    allwOrChrg.getAllowanceOrChargeDescription()
        .setServiceCoded(allwOrChrg.getIndicatorCoded()
            .name());
    if (allwOrChrg.getIndicatorCoded()
        .equals(IndicatorCodeType.SERVICE)) {
      String eanCode = getEanCode(allwOrChrg.getAllowanceOrChargeDescription()
          .getListOfDescription()
          .getValue());
      allwOrChrg.getAllowanceOrChargeDescription()
          .setRefID(eanCode);
    }
    applyAllowanceOrChargeOnSelectedDetail();
    this.allowOrChrgLineEdit = false;
  }

  public String getEanCode(String selectedValueLabel) {
    return getParafiscalTaxDescriptionItems()
        .stream()
        .filter(p -> p.getLabel(getCurrentUser().getLocale())
            .equals(selectedValueLabel))
        .map(p -> p.getListValue())
        .findFirst()
        .orElse(null);
  }

  public void onRowCancelLine(RowEditEvent event) {
    InvoiceAllowOrChargeType allowOrChargeType = (InvoiceAllowOrChargeType) event.getObject();
    if (isBlank(InvoiceHelper.getComplexStringValue(allowOrChargeType.getAllowanceOrChargeDescription()
        .getListOfDescription())) || allowOrChargeType.getIndicatorCoded() == null || allowOrChargeType.getBasisCoded() == null) {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      return;
    }
    this.allowOrChrgLineEdit = false;
  }

  public List<SelectItem> getLineIndicatorCodeItems() {
    return asList(new SelectItem(IndicatorCodeType.LINE_ITEM_ALLOWANCE,
            getMessage(LABEL_FAMILY + ".ALLOWANCE", "Allowance", selectedLanguage)),
        new SelectItem(IndicatorCodeType.LINE_ITEM_CHARGE,
            getMessage(LABEL_FAMILY + ".CHARGE", "Charge", selectedLanguage)),
        new SelectItem(IndicatorCodeType.SERVICE,
            getMessage(LABEL_FAMILY + ".SERVICE", "Service", selectedLanguage)));
  }

  /**
   * called when change the type of allowanceCharge <br> from percent to monetary or reversly.
   */
  public void initAllowanceChargeWhenChangingBasisCoded(InvoiceAllowOrChargeType alwOrCharge) {

    if (BasisCodeType.MONETARY_AMOUNT == alwOrCharge.getBasisCoded()) {
      initMonetaryAllowanceOrChargeToZero(alwOrCharge);
    }
    if (BasisCodeType.PERCENT == alwOrCharge.getBasisCoded()) {
      initPercentAllowanceOrChargeToZero(alwOrCharge);
    }
  }

  // TODO -> Refacto : AllowOrChargeXcblHelper
  private void initMonetaryAllowanceOrChargeToZero(InvoiceAllowOrChargeType alwOrCharge) {
    InvoiceMonetaryValueType monetary = new InvoiceMonetaryValueType();
    monetary.setCurrency(invoiceXcbl.getInvoiceHeader()
        .getPaymentCurrency());
    monetary.setMonetaryAmount(toComplexBigDecimalType(monetary.getMonetaryAmount(), ZERO));
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(monetary);
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setPercentageAllowanceOrCharge(null);
  }

  // TODO -> Refacto : AllowOrChargeXcblHelper
  private void initPercentAllowanceOrChargeToZero(InvoiceAllowOrChargeType alwOrCharge) {
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setPercentageAllowanceOrCharge(getPercentageAllowanceOrChargeType(ZERO));
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .setCurrency(invoiceXcbl.getInvoiceHeader()
            .getPaymentCurrency());
    alwOrCharge.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(null);
  }

  public void onRemoveLineAllowOrCharge(List<InvoiceAllowOrChargeType> liste,
      InvoiceAllowOrChargeType allowOrChargeType) {
    if (allowOrChargeType != null) {
      liste.remove(allowOrChargeType);
      if (!this.editInvoice)
        applyAllowanceOrChargeOnSelectedDetail();
    }

    this.allowOrChrgLineEdit = false;
  }

  public boolean isEdited(final InvoiceItemDetailType detail) {
    // verify if the detail line has the productReference set
    // if the product is not new the reference should be set in InvoiceTaskHelper.populateInvoiceBaseItemDetail()
    return ofNullable(detail.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getItemDescription()
        .getValue())
        .isPresent() &&
        ofNullable(
            detail.getInvoiceBaseItemDetail()
                .getInvoicedQuantity()
                .getQuantityValue()
                .getValue())
            .isPresent();
  }

  public void saveProduct(InvoiceItemDetailType detail) {
    if (this.editInvoice) {
      InvoiceTaxType taxType = detail.getInvoicePricingDetail()
          .getTax()
          .get(0);
      if (StringUtils.isNotEmpty(taxType.getTaxCategoryCodedOther()
          .getValue()))
        taxType.setTaxCategoryCoded(TaxCategoryCodeType.OTHER);
      computeInvoiceTotalTaxable(this.invoiceXcbl, this.getDetails(), footerPrecision, this.taxExempt);
      InvoiceItemDetailXcblHelper.copyPriceValueFromTo(this.getDetails(), NET_ITEM_PRICE, CALCULATION_NET);
      InvoiceItemDetailXcblHelper.copyPriceValueFromTo(this.getDetails(), UNIT_COST_PRICE, CALCULATION_GROSS);
      return;
    }

    int index = getCalculationNet(detail);
    BigDecimal unitNetPrice = InvoicePriceXcblHelper.getUnitPriceValue(getPrice(detail)
            .get(index))
        .orElse(ZERO);

    if (unitNetPrice.compareTo(ZERO) < 0) {
      addMessage(null, SEVERITY_ERROR, LABEL_FAMILY + ".error_unit_price_negative", "messagesItemDetail");
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      return;
    }

    BigDecimal quantity = detail.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getQuantityValue()
        .getValue();
    detail.getInvoicePricingDetail()
        .getLineItemTotal()
        .setMonetaryAmount(toComplexBigDecimalType(detail.getInvoicePricingDetail()
            .getLineItemTotal()
            .getMonetaryAmount(), quantity.multiply(unitNetPrice)));
    if (!this.taxExempt) {
      if (this.getListCodeTva()
          .isEmpty()) {
        InvoiceTaxType invTax = detail.getInvoicePricingDetail()
            .getTax()
            .get(0);
        invTax.setTaxCategoryCodedOther(toComplexStringType(invTax.getTaxCategoryCodedOther(),
            invTax.getTaxPercent()
                .getValue()
                .toString()));
      }
      else {
        InvoiceTaxType invTax = detail.getInvoicePricingDetail()
            .getTax()
            .get(0);
        Tax tax = this.getListCodeTva()
            .get(invTax.getTaxCategoryCodedOther()
                .getValue());
        // TODO check referentiel format of tax value
        String percentage = String.format(Locale.ROOT, TAX_FORMAT, tax.getPercent());
        invTax.setTaxCategoryCodedOther(toComplexStringType(invTax.getTaxCategoryCodedOther(), percentage));
        invTax.getTaxPercent()
            .setValue(new BigDecimal(percentage));
      }
      this.taxSummaries.clear();
      this.parafiscalTaxSummaries.clear();
      applyAllowanceOrChargeOnDetails(this.getDetails());
      applyTaxOnInvoiceItemDetails(this.invoiceXcbl, this.getDetails(), this.taxSummaries, detailPrecision, footerPrecision,
          this.parafiscalTaxSummaries);
    }
    // according to AIO-13778 the parafiscal charges are not related to vat exemption
    else
      addParafiscalWhenVatExempt();

    BigDecimal totalTaxableAmount = computeInvoiceTotalTaxable(this.invoiceXcbl, this.getDetails(), footerPrecision, this.taxExempt);

    InvoiceMonetaryValueType taxableValue = this.invoiceXcbl.getInvoiceSummary()
        .getInvoiceTotals()
        .getTaxableValue();
    taxableValue.setMonetaryAmount(toComplexBigDecimalType(taxableValue.getMonetaryAmount(), totalTaxableAmount));
    applyAllowOrChrgOnTaxableValue(this.invoiceXcbl, this.allowOrChargeTypes, totalTaxableAmount, this.taxExempt, footerPrecision);
    applyTaxOnHeaderAllowanceCharge(this.allowOrChargeTypes, this.taxSummaries, this.invoiceXcbl, this.getDetails(), this.taxExempt,
        this.detailPrecision, this.footerPrecision);
    computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
    // AIO-12225
    // The overall problem is that for some clients, we use NetItemPrice / UnitCostPrice, and in the converter / OCR we use CalculationGross
    // / CalculationNet.
    // We have to always copy net prices in both places (NetItemPrice / CalculationNet) and gross prices in (UnitCostPrice /
    // CalculationGross)
    InvoiceItemDetailXcblHelper.copyPriceValueFromTo(this.getDetails(), NET_ITEM_PRICE, CALCULATION_NET);
    InvoiceItemDetailXcblHelper.copyPriceValueFromTo(this.getDetails(), UNIT_COST_PRICE, CALCULATION_GROSS);
    this.product = null;
    detailsBackup = null;
  }

  private void addParafiscalWhenVatExempt() {
    this.parafiscalTaxSummaries.clear();
    this.getDetails()
        .forEach(item ->
        {
          List<InvoiceAllowOrChargeType> allowOrCharge = ofNullable(item.getInvoicePricingDetail())
              .map(OcrPricingDetailType::getItemAllowancesOrCharges)
              .map(ListOfInvoiceAllowOrChargeType::getAllowOrCharge)
              .orElse(emptyList());
          List<IndicatorCodeType> indicator = allowOrCharge
              .stream()
              .map(i -> i.getIndicatorCoded())
              .collect(toList());
          if (indicator.contains(SERVICE)) {
            List<InvoiceAllowOrChargeType> allowOrChargeType = allowOrCharge
                .stream()
                .filter(i -> SERVICE.equals(i.getIndicatorCoded()))
                .collect(toList());
            BigDecimal invoiceQty = item.getInvoiceBaseItemDetail()
                .getInvoicedQuantity()
                .getQuantityValue()
                .getValue();
            TaxSummaryXcblHelper.populateInvoiceParafiscalTaxSummary(parafiscalTaxSummaries, allowOrChargeType, footerPrecision,
                invoiceQty);
          }
        });
  }

  private void applyAllowanceOrChargeOnDetails(final List<InvoiceItemDetailType> details) {
    details.forEach(this::applyAllowanceOrChargeOnDetail);
  }

  public void onCancelProduct() {
    if (detailsBackup != null) {
      this.details = cloner.deepClone(detailsBackup);
    }

    // the product should be removed no matter if it's a new product or an existing product to be edited
    if (details != null) {
      this.details.remove(product);
    }
    JSFHelper.clearComponent("tskForm-standardInvoiceEditionView-productPanel");
    this.product = null;
    detailsBackup = null;
  }

  public List<InvoiceActualPaymentType> getActualPayments() {
    return actualPayments == null ? actualPayments = new ArrayList<>() : actualPayments;
  }

  public void onAddActualPayment() {
    InvoiceActualPaymentType actualPayment = creatActualPayement();
    actualPayments.add(actualPayment);
  }

  public void onRemoveActualPayment(InvoiceActualPaymentType payment) {
    if (actualPayments != null && payment != null) {
      actualPayments.remove(payment);
      computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
    }
    if (actualPayments.isEmpty()) {
      setTotalAmountPayable(this.invoiceXcbl, null);
    }
  }

  public void onAddActualPaymentRowEdit(RowEditEvent event) {
    if (this.editInvoice) {
      return;
    }
    computeAmountToBePaid(this.invoiceXcbl, this.actualPayments, this.detailPrecision);
  }

  public Boolean getValidationEnabled() {
    if (this.editInvoice)
      return invoiceReferenceEdit || allowOrChrgEdit;
    return allowOrChrgEdit || this.product != null || invoiceReferenceEdit;
  }

  /**
   * Saves an invoice document. Validation is done in {@link StandardInvoiceEditionTask#isInvoiceValidated()} and it's called from the xhtml
   * file. In this way the validation function is done when hitting the "Validate" button and not after clicking "Confirm" from the
   * confirmation dialog.
   */
  public void createINVDocument() {
    if (!this.editInvoice && isInvoiceTotalMonetaryAmountNegative(invoiceXcbl)) {
      error(LABEL_FAMILY + ".error_total_price_negative");
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      return;
    }
    log.debug("Start creating Invoice ...");
    if (!this.editInvoice) {
      removeEmptyDateOfOperation();
      removeEmptyInvoicingPeriod();
      InvoiceXcblHelper.setPartyRoleCodedToNull(invoiceXcbl, listOfPartyCoded);
    }
    InvoiceXcblHelper.cleanEmptyTagsInvoiceReferencesHeader(invoiceXcbl);
    InvoiceXcblHelper.cleanEmptyTagsInvoiceReferencesDetails(invoiceXcbl.getInvoiceDetail());
    InvoiceXcblHelper.cleanInvoiceItemDetails(invoiceXcbl.getInvoiceDetail(), details, taxExempt);
    if (taxExempt) {
      InvoiceXcblHelper.cleanInvoiceSummaryTotals(invoiceXcbl.getInvoiceSummary()
          .getInvoiceTotals());
    }
    try {
      if (document != null && this.editInvoice) {
        this.lockForMe = false;
        editStandardInvoice();
        addAttachedFiles();
        onPostValidationProcess(document, validationProcessId, INVOICE_POST_VALIDATION, DocumentType.INVOIC.toString());
        if (getLegalControlsSource() != null)
          documentTimelineService.save(
              createTimelineByConstraint(document, PROPERTY_BASED.equals(legalControlsSource) ? FR_B2B : legalControlsSource.code()));
        if (checkDuplicate)
          documentTimelineService.save(new DocumentTimeline(document, new Date(), CTRL_DUPLICATE, USER_SYSTEM));
        return;
      }
      this.lockForMe = false;
      if (document != null) {
        if (checkRevisionDocument()) {
          InvoiceXcblHelper.removeEmptyFreeText(invoiceXcbl);
          invoiceXcbl.setAcquisition(PORTAL);
          final Document invoiceDocument = createInvoiceDocumentFromParent(document, invoiceXcbl, XcblHelper.getWorkDir(),
              InvoiceXcblHelper.DOC_INVOICE_DIR,
              this.serializedXcblFile, outputDir, makeCopyOfOutputFile);
          invoiceDocument.setAcquisition(PORTAL);
          attachInvoiceDocument(document, invoiceDocument);
          // Condition a modifier si on rajoute le cas "Expedie-partiellement"
          if (DESADV.name()
              .equals(document.getType()) &&
              (document.getStatusAsEnumValue() == SENT || document.getStatusAsEnumValue() == SENT_PARTIALLY)) {
            document.setStatusWithEnumValue(INVOICED);
          }
          documentService.saveDocument(document);
          updateDocumentRootStatus(document, new DocumentStatusEntity(INVOICED));
          // Link invoice and document if needed
          if (isFromAsn())
            eDocumentService.createDocumentLinks(
                asList(document.getIndexReference(),
                    invoiceDocument.getIndexReference()));
        }
        else {
          addMessage(null, FacesMessage.SEVERITY_ERROR,
              "gnxxcblcomlbls.error_saving_concurrent_access", TASK_MESSAGES);
          return;
        }
      }
      else { // Saving Invoice created from scratch
        resolveOrganizationCodes();
        InvoiceXcblHelper.removeEmptyFreeText(invoiceXcbl);
        invoiceXcbl.setAcquisition(PORTAL);

        document = InvoiceXcblHelper.createInvoiceDocument(invoiceXcbl, this.companyCode, EDocumentService.OFFICIAL_INDEX,
            XcblHelper.getWorkDir(), InvoiceXcblHelper.DOC_INVOICE_DIR, outputDir, makeCopyOfOutputFile);
        document.setAcquisition(PORTAL);
        documentService.saveDocument(document);
        // Update order status
        updateDocumentRootStatus(document, new DocumentStatusEntity(INVOICED));
        this.newInvoice = false;
      }
      addAttachedFiles();
      launchPostValidationProcess();
      addMessage(null, FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".info_confirm_invoice", TASK_MESSAGES,
          invoiceXcbl.getInvoiceHeader()
              .getInvoiceNumber()
              .getValue());
    }
    catch (Exception e) {
      TaskHelper.error(this, e, LABEL_FAMILY + ".error_in_invoice_generation: " + e.getMessage());
    }
    log.debug("End");
    if (getLegalControlsSource() != null)
      documentTimelineService.save(
          createTimelineByConstraint(document, PROPERTY_BASED.equals(legalControlsSource) ? FR_B2B : legalControlsSource.code()));
    if (checkDuplicate)
      documentTimelineService.save(new DocumentTimeline(document, new Date(), CTRL_DUPLICATE, USER_SYSTEM));
  }

  public void launchPostValidationProcess() {
    Document doc = getInvoiceDocument();

    if (doc != null)
      onPostValidationProcess(doc, validationProcessId, INVOICE_POST_VALIDATION, DocumentType.INVOIC.toString());
  }

  private void removeEmptyDateOfOperation() {
    ListOfInvoiceDateCodedType otherDates = ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceDates)
        .map(InvoiceDatesType::getListOfOtherInvoiceDates)
        .orElse(null);
    if (otherDates != null) {
      Optional<InvoiceDateCodedType> dateOfOperation = otherDates.getDateCoded()
          .stream()
          .filter(dateCoded -> DATE_OF_OPERATIONS.equals(dateCoded.getDateQualifier()
              .getDateQualifierCoded()))
          .findFirst();
      if (dateOfOperation.isPresent()) {
        InvoiceDateCodedType dp = dateOfOperation.get();
        Date date = Optional.ofNullable(dp)
            .map(InvoiceDateCodedType::getDate)
            .map(ComplexDateType::getValue)
            .orElse(null);
        if (date == null)
          otherDates.getDateCoded()
              .remove(dp);
      }
    }
  }

  private void removeEmptyInvoicingPeriod() {
    InvoiceValidityDatesType invoicingPeriod = invoiceXcbl.getInvoiceHeader()
        .getInvoiceDates()
        .getInvoicingPeriod();
    if (invoicingPeriod != null &&
        getStartDateFromInvoiceValidityDatesType(invoicingPeriod) == null) {
      invoiceXcbl.getInvoiceHeader()
          .getInvoiceDates()
          .setInvoicingPeriod(null);
    }
  }

  private void editStandardInvoice() throws Exception, FileNotFoundException, JAXBException, IOException {
    XcblHelper.setOcrFound(ocrFound || ocrFoundNoColorDisplayed);
    prepareEditedInvoiceForSaving();
    document.setThirdParty(getFactorPartyIdent(invoiceXcbl));
    InvoiceIndex originalInvoiceIndex = document.getIndexValue();
    InvoiceEditionHelper.copyXcblProperties(originalInvoiceIndex, this.invoiceXcbl);
    InvoiceXcblHelper.updateInvoiceDocument(document, invoiceXcbl);
    List<DocumentFile> birtDocFiles = EDocumentServiceHelper.getBirtGeneratedFiles(this.document,
        Arrays.asList(new FileType[] { FileType.PDF, FileType.EXCEL }));
    Document updateDoc = this.eDocumentService.deleteDocumentFile(this.document, birtDocFiles);
    if (updateDoc != null)
      this.document = updateDoc;
    if (statusAfterInvoiceModification != null) {
      this.invoiceXcbl.setStatusWithString(statusAfterInvoiceModification);
      this.document.setStatusWithString(statusAfterInvoiceModification);
    }
    this.document.setIndexValue(invoiceXcbl);
    this.documentService.saveDocument(this.document);
    String workflowDocumentId = getPortalSessionHandler().getWorkflowDocumentId();
    if (workflowDocumentId != null && valueOf(workflowDocumentId) > 0) {
      documentTimelineService.saveActionInTimeline(document, getCurrentUser(), TimelineAction.ENRICH, EMPTY, -1);
    }
    else {
      documentTimelineService.saveActionInTimeline(document, getCurrentUser(), TimelineAction.EDIT, EMPTY, -1);
    }

    ocrFound = false;
    ocrFoundNoColorDisplayed = false;
    XcblHelper.setOcrFound(false);
  }

  public boolean hasTaxAmount(InvoiceAllowOrChargeType allwOrChg) {
    return InvoiceEditionHelper.hasTaxAmount(allwOrChg);
  }

  private void prepareEditedInvoiceForSaving() {
    if (chorusPortlet) {
      chorusInvoiceHelper.prepareInvoiceForSaving(invoiceXcbl);
      InvoicePartyType parties = invoiceXcbl.getInvoiceHeader()
          .getInvoiceParty();
      parties.setBillToParty(parties.getBuyerParty());
    }

    ListOfInvoiceTaxSummaryType listOfTaxSummaryType = new ListOfInvoiceTaxSummaryType();
    listOfTaxSummaryType.getTaxSummary()
        .addAll(getTaxSummariesEditInvoice());
    invoiceXcbl.getInvoiceSummary()
        .setListOfTaxSummary(listOfTaxSummaryType);
    AllowOrChargeSummaryType allowOrChargeSummaryType = new AllowOrChargeSummaryType();
    allowOrChargeSummaryType.getTotalAllowOrCharge()
        .addAll(getParafiscalTaxSummariesEditInvoice());
    invoiceXcbl.getInvoiceSummary()
        .setAllowOrChargeSummary(allowOrChargeSummaryType);
    InvoiceEditionHelper.prepareEditedInvoiceForSaving(this.invoiceXcbl, this.originalXcbl, this.actualPayments, this.footerPrecision,
        this.detailPrecision,
        listOfPartyCoded, partner, allowOrChargeTypes);
    removeEmptyDateOfOperation();
    populateInvoiceHeaderReferences();
    InvoiceXcblHelper.addIbanAndBic(sellerIban, sellerBic, invoiceXcbl);
  }

  public void populateVAT(Invoice invoice) {
    List<TaxReferenceType> taxRefs = Optional.ofNullable(invoice)
        .map(InvoiceType::getInvoiceHeader)
        .map(InvoiceHeaderType::getTaxReference)
        .orElse(emptyList());
    String reason = !taxRefs.isEmpty() ? taxRefs.get(0)
        .getReasonTaxExemptCodedOther() : null;
    taxRefs.clear();

    if (VATType.DEB.equals(vatType)) {
      taxRefs.add(TaxXcblHelper.createTaxReferenceType(VATType.DEB.getValue()));
    }
    if (VATType.ENC.equals(vatType)) {
      taxRefs.add(TaxXcblHelper.createTaxReferenceType(VATType.ENC.getValue()));
    }
    if (VATType.EXEMPT.equals(vatType)) {
      taxRefs.add(TaxXcblHelper.createExemptTaxReferenceType(VATType.EXEMPT.getValue(), reason));
    }
  }

  public void addTaxExemptInfoIfIsVatTypeExempt() {
    if (vatType != null && vatType.getValue() != null) {
      if (VATType.EXEMPT.equals(vatType)) {
        if (isEditInvoice()) {
          this.taxExemptEditInvoice = true;
          addTaxExemptInfoForEdition();
        }
        else {
          this.taxExempt = true;
          addTaxExemptInfo();
        }
      }
      else {
        if (isEditInvoice()) {
          this.taxExemptEditInvoice = false;
        }
        else
          this.taxExempt = false;
      }
    }
    else {
      this.taxExempt = false;
      this.taxExemptEditInvoice = false;
    }
  }

  protected Document removeGeneratedBirtFiles() {
    List<DocumentFile> birtDocFiles = EDocumentServiceHelper.getBirtGeneratedFiles(this.document,
        Arrays.asList(new FileType[] { FileType.PDF, FileType.EXCEL }));
    Document updateDoc = this.eDocumentService.deleteDocumentFile(this.document, birtDocFiles);
    if (updateDoc != null)
      this.document = updateDoc;
    return updateDoc;
  }

  // This method has Object as parameter instead of BigDecimal as jsf converts null to 0 in case the type of parameter is BigDecimal
  public String getBorderColor(Object ocrRate) {
    if (ocrRate == null)
      return "";
    double rate = ((BigDecimal) ocrRate).doubleValue();
    if (rate <= 0.6) {
      ocrFound = true;
      return "ocr-rate-red";
    }
    else if (rate < 0.85) {
      ocrFound = true;
      return "ocr-rate-orange";
    }
    ocrFoundNoColorDisplayed = true;
    return "";
  }

  public boolean isValidUOMCoded(InvoiceUnitOfMeasurementType uom) {
    // UOMCoded does not have ocrRate. If a wrong value is provided, FO will display that value as String.
    // This method is used as a condition in FO to check if the field must be displayed in warning.
    // Normally this should not happen is the xcbl is validated before.
    return UnitOfMeasurement.isValidUMOCoded(uom.getUOMCoded()) ? true : false;
  }

  /**
   * Check if at least one field not visible in Details table (but visible in edit panel) needs to be checked because of its confidence rate
   * (ocrRate)
   *
   * @param detail
   * @return true if at least one "hidden" field in Details requires attention.
   */
  public boolean hasOcrForDetails(InvoiceItemDetailType detail) {
    return InvoiceEditionHelper.hasOcrForDetails(detail);
  }

  public String getOcrWarningTitle() {
    return MessageHelper.getMessage(LABEL_FAMILY + ".ocr_warning_details",
        "Please note that at least one field not visible on the screen must be checked on this line.", selectedLanguage);
  }

  public boolean isOcrFound() {
    return ocrFound;
  }

  public void setOcrFound(boolean ocrFound) {
    this.ocrFound = ocrFound;
  }

  public boolean isOcrFoundNoColorDisplayed() {
    return ocrFoundNoColorDisplayed;
  }

  public void setOcrFoundNoColorDisplayed(boolean ocrFoundNoColorDisplayed) {
    this.ocrFoundNoColorDisplayed = ocrFoundNoColorDisplayed;
  }

  public boolean isChorusPortlet() {
    return chorusPortlet;
  }

  public boolean isChorusParty(String partyRole) {
    return chorusPortlet && InvoiceXcblHelper.BUYER.equals(partyRole);
  }

  public void setChorusPortlet(boolean chorusPortlet) {
    this.chorusPortlet = chorusPortlet;
  }

  public ChorusInvoiceHelper getChorusInvoiceHelper() {
    if (chorusInvoiceHelper == null && invoiceXcbl != null)
      chorusInvoiceHelper = new ChorusInvoiceHelper(invoiceXcbl);
    return chorusInvoiceHelper;
  }

  public boolean isInvoiceValidated() {
    if (!editInvoice) {
      this.prepareInvoiceForSaving();
    }
    this.serializedXcblFile = null; /* make sure this field is initialized to null everytime an invoice is created */
    boolean result = this.editInvoice || checkCostAllocationConstraints();
    boolean checkStartDateBeforeEndDate = this.editInvoice || checkStartDateBeforeEndDate();
    if (!checkStartDateBeforeEndDate) {
      // check that the error message is not already present
      String message = getMessage(LABEL_FAMILY + ".error_date", null, null);

      if (!messageExistsIncontext(message)) {
        TaskHelper.error(this, null, LABEL_FAMILY + ".error_date");
      }
      result = false;
    }

    RequestContext requestContext = RequestContext.getCurrentInstance();
    String headerTab = getMessage(LABEL_FAMILY + ".header", "Header", selectedLanguage);

    if (!this.editInvoice && isBlank(this.invoiceXcbl.getInvoiceHeader()
        .getInvoiceNumber()
        .getValue())) {
      TaskHelper.error(this, null, "error_empty_invoice_number");
      requestContext.addCallbackParam(headerTab, headerTab);
      result = false;
    }

    if (!this.editInvoice && checkDuplicateNumber()) {
      TaskHelper.error(this, null, "error_duplicate_invoice_number");
      requestContext.addCallbackParam(headerTab, headerTab);
      result = false;
    }

    if (!this.editInvoice && this.details.isEmpty()) {
      TaskHelper.error(this, null, "error_empty_invoice_item_details");
      String linesTab = getMessage(LABEL_FAMILY + ".lines", "Lines", selectedLanguage);
      requestContext.addCallbackParam(linesTab, linesTab);
      result = false;
    }
    if (!this.editInvoice) {
      for (InvoicePartyCodedType party : listOfPartyCoded) {
        if ("ShipTo".equals(party.getPartyRoleCoded()) || party.getListOfIdentifier() == null) {
          continue;
        }
        if (!chorusPortlet && isBlank(getIdentValue(party.getListOfIdentifier()
            .getIdentifier()
            .get(0))) && isBlank(getIdentValue(
            party.getListOfIdentifier()
                .getIdentifier()
                .get(1))) &&
            isBlank(getIdentValue(party.getPartyTaxInformation()
                .getTaxIdentifier()))) {
          String tab = getMessage(LABEL_FAMILY + "." + party.getPartyRoleCoded(),
              party.getPartyRoleCoded(), selectedLanguage);
          TaskHelper.error(this, null, "error_empty_party_identifier", tab);
          requestContext.addCallbackParam(tab, tab);
          result = false;
        }
      }
    }
    if (!taxExempt && !this.editInvoice) {
      for (InvoiceItemDetailType item : details) {
        if (item.getInvoicePricingDetail()
            .getTax()
            .get(0)
            .getTaxCategoryCodedOther()
            .getValue() == null) {
          TaskHelper.error(this, null, "error_empty_invoice_item_vat", getComplexStringValue(item.getInvoiceBaseItemDetail()
              .getItemIdentifiers()
              .getItemDescription()));
          result = false;
        }
      }
    }

    boolean doCheckRteValidation = getRteSource() != null;
    boolean doCheckLegalControlsValidation = this.getLegalControlsSource() != null;

    // if RTE source is set we need to perform the RTE validation
    if (doCheckRteValidation) {
      result = checkRteValidation(document, invoiceXcbl, getRteSource(), outputDir, company, selectedLanguage, result,
          doCheckLegalControlsValidation);
    }

    // if LegalControls source is set we need to perform the legal controls validation
    if (doCheckLegalControlsValidation) {
      InvoiceXcblHelper.prepareInvoicePartyForLegalControls(invoiceXcbl, listOfPartyCoded, partner, chorusPortlet);
      List<InvoiceItemDetailType> detailsList = ofNullable(invoiceXcbl).map(Invoice::getInvoiceDetail)
          .map(InvoiceDetailType::getListOfInvoiceItemDetail)
          .map(ListOfInvoiceItemDetailType::getInvoiceItemDetail)
          .orElse(null);
      if (detailsList != null)
        invoiceXcbl.getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail()
            .addAll(details);
      result = checkLegalControlsValidation(document, invoiceXcbl, getLegalControlsSource(), outputDir, selectedLanguage,
          result, doCheckRteValidation);
    }
    if (checkDuplicate) {
      try {
        // we create a temporary document to be able to check if a duplicate exists for the invoice
        tempDoc = new Document();
        if (editInvoice && document != null)
          // we set the uuid because the invoice already exists
          tempDoc.setUuid(document.getUuid());
        List<Clause> statusAndStageQuery = new ArrayList<>();
        if (!isEmpty(documentStatusesNotDuplicate) && !isEmpty(documentStagesNotDuplicate))
          statusAndStageQuery.add(new OrClause(Clauses.notIn(FIELD_STATUS, documentStatusesNotDuplicate),
              Clauses.notIn(FIELD_STAGE, documentStagesNotDuplicate)));
        else if (!isEmpty(documentStatusesNotDuplicate))
          statusAndStageQuery.add(Clauses.notIn(FIELD_STATUS, documentStatusesNotDuplicate));
        else if (!isEmpty(documentStagesNotDuplicate))
          statusAndStageQuery.add(Clauses.notIn(FIELD_STAGE, documentStagesNotDuplicate));

        invoiceXcbl.setFrom(PartyCodeXcblHelper.getSellerPartyIdent(invoiceXcbl));
        tempDoc.setIndexValue(invoiceXcbl);
        integrityService.createTemporaryEntry(tempDoc);
        if (!DuplicateInvoiceHelper.checkDuplicate(tempDoc, Document.ProcessingWay.SENDING, statusAndStageQuery,
            null, null,
            documentService, integrityService)) {
          TaskHelper.error(this, null, "duplicate_check_error");
          deleteIntegrityEntry();
          result = false;
        }
      }
      catch (Exception e) {
        MessageHelper.error(e, "labels.exception_message", getRootCauseMessage(e));
        deleteIntegrityEntry();
        result = false;
      }
    }
    Set<String> allTaxes = new HashSet<>();
    Set<String> duplicateTaxes = getTaxSummariesEditInvoice().stream()
        .map(tax -> tax.getTaxCategoryCodedOther()
            .getValue())
        .filter(tax -> !allTaxes.add(tax))
        .collect(Collectors.toSet());
    for (String duplicateTax : duplicateTaxes) {
      TaskHelper.error(this, null, "error_existing_vat_rate", "" + duplicateTax + "%", duplicateTax);
      result = false;
    }
    if (!result || !checkPresenceOfAsnNumber()) {
      requestContext.addCallbackParam("validationFailed", true);
    }
    return result;
  }

  private void deleteIntegrityEntry() {
    if (tempDoc != null) {
      integrityService.deleteTemporaryEntry(tempDoc.getUuid());
      tempDoc = null;
    }
  }

  public void onCloseConfirm() {
    deleteIntegrityEntry();
  }

  public Map<String, String> getAllDocumentStage() {
    // get all the document stages from enum translated to user selected language
    return IndexableTaskHelper.toStageItems(stream(DocumentStage.values())
            .map(DocumentStage::toString)
            .collect(
                toList()), getLocale())
        .entrySet()
        .stream()
        .map(e -> Map.entry(e.getValue(), (String) e.getKey()))
        .sorted(Map.Entry.comparingByKey())
        .collect(Collectors.toMap(o -> o.getKey(), o -> o.getValue(), (e1, e2) -> e1, TreeMap::new));
  }

  public boolean isOcrAcquisitionType() {
    return ocrAcquisitionType;
  }

  public void setOcrAcquisitionType(boolean ocrAcquisitionType) {
    this.ocrAcquisitionType = ocrAcquisitionType;
  }

  public boolean checkPresenceOfAsnNumber() {
    List<InvoiceReference> asnNumberHeader = this.invoiceReferences
        .stream()
        .filter(asn -> InvoiceReferenceType.DISPATCH_ADVICE.equals(asn.getType()) && asn.getNumber() != null &&
            StringUtils.isNotEmpty(asn.getNumber()
                .getValue()))
        .collect(toList());
    List<InvoiceItemDetailType> asnNumberDetails = details.stream()
        .filter(a -> Optional.ofNullable(a.getInvoiceBaseItemDetail())
            .map(InvoiceBaseItemDetailType::getLineItemReferences)
            .map(InvoiceReferencesType::getASNNumber)
            .map(InvoiceOCRReferenceType::getRefNum)
            .isPresent() &&
            StringUtils.isNotEmpty(a.getInvoiceBaseItemDetail()
                .getLineItemReferences()
                .getASNNumber()
                .getRefNum()
                .getValue()))
        .collect(Collectors.toList());
    if (asnNumberHeader.size() > 0 && asnNumberDetails.size() > 0) {
      // check that the error message is not already present
      String message = getMessage(LABEL_FAMILY + ".error_asnNumber_double", null, null);

      if (!messageExistsIncontext(message)) {
        TaskHelper.error(this, null, LABEL_FAMILY + ".error_asnNumber_double");
      }
      return false;
    }
    return true;
  }

  /**
   * Method used to check that cost allocation is field in header or in details -check if all cost allocation details are field
   *
   * @return
   */
  private boolean checkCostAllocationConstraints() {
    // check double cost allocation constaint
    // get cost allocation from header
    List<InvoiceReferencesType> costAllocationHeader = this.invoiceXcbl.getInvoiceHeader()
        .getInvoiceReferences()
        .stream()
        .filter(a -> a.getCostAllocation() != null && a.getCostAllocation()
            .getCostAllocationNumber() != null &&
            StringUtils.isNotEmpty(a.getCostAllocation()
                .getCostAllocationNumber()
                .getRefNum()))
        .collect(toList());
    // get all cost allocations from details
    List<InvoiceItemDetailType> costAllocationDetails = this.details.stream()
        .filter(a -> a.getInvoiceBaseItemDetail()
            .getLineItemReferences()
            .getCostAllocation() != null &&
            StringUtils.isNotEmpty(a.getInvoiceBaseItemDetail()
                .getLineItemReferences()
                .getCostAllocation()
                .getCostAllocationNumber()
                .getRefNum()))
        .collect(Collectors.toList());
    if (costAllocationHeader.size() > 0 && costAllocationDetails.size() > 0) {
      // check that the error message is not already present
      String message = getMessage(LABEL_FAMILY + ".error_const_allocation_double", null, null);

      if (!messageExistsIncontext(message)) {
        TaskHelper.error(this, null, LABEL_FAMILY + ".error_const_allocation_double");
      }
      return false;
    }

    // check all cost details are field or the costallocation header is field
    if ((costAllocationNumberControl && costAllocationDetails.size() != this.details.size() && costAllocationHeader.size() == 0) ||
        (costAllocationNumberControl && costAllocationDetails.size() == 0 && costAllocationHeader.size() == 0)) {
      // check that the error message is not already present
      String message = getMessage(LABEL_FAMILY + ".error_const_allocation_not_filed", null, null);

      if (!messageExistsIncontext(message)) {
        TaskHelper.error(this, null, LABEL_FAMILY + ".error_const_allocation_not_filed");
      }
      return false;
    }
    return true;
  }

  //@formatter:off
  /**
   * Validates the xcbl file regarding legal controls. <br>
   * <br>
   * Some explanations are needed: The legal controls validation and RTE validation are somehow similar :
   * they both need a temporary file (the same) in order to do the validation which can be OK or not
   * and if not we must send a list of validation errors to the frontEnd. <br>
   *
   * We have two settings in the backoffice that say: <br>
   * - check legal controls (or not) <br>
   * - check RTE (or not) <br>
   *  <br>
   * Case 1: check legal controls AND check RTE  <br>
   * Legal controls generate the file, store the errors (if any)  <br>
   * but don't delete the file because it will be used by rte validation <br>
   * rte validation don't generate the file (it exists already) but checks it. <br>
   * If there are errors from legal controls or from rte validation (or both)  <br>
   * the file is deleted by rte validation <br>
   *  <br>
   * Case 2: check ONLY legal controls ( AND don't check RTE ) <br>
   * Legal controls validation generates the file and checks it. <br>
   * If there are errors Legal controls deletes the file. <br>
   * (rte validation does nothing) <br>
   *  <br>
   * Case 3: check ONLY RTE (AND son't check legal controls)  <br>
   * rte validation generates the file and checks it. <br>
   * If there are errors rte validation deletes the file. <br>
   * (rte validation does nothing) <br>
   *  <br>
   * Case 4: DO NOT check any of these two.  <br>
   * That's easy, we do not generate and do not delete our file <br>
   *
   * Validate the xcbl file using the rte template
   * @param parentDocument
   * @param invoiceXcbl
   * @param legalControlsSource
   * @param outputDir
   * @param company
   * @param language
   * @param isValid
   * @param validateRteAlso
   * @return true if all validations were ok (includig the ones before this method call, false otherwise
   */
  //@formatter:on
  private boolean checkLegalControlsValidation(Document parentDocument, Invoice invoiceXcbl, LegislationType legislation,
      String outputDir, Locale language, boolean isValid, boolean validateRteAlso) {
    MutablePair<String, String> toFrom = this.obtainToAndFrom(parentDocument, invoiceXcbl);

    return checkLegalControlsValidation(invoiceXcbl, legislation, outputDir, language, isValid, toFrom.getLeft(),
        toFrom.getRight(), validateRteAlso);

  }

  private boolean checkLegalControlsValidation(Invoice invoiceXcbl, LegislationType legislation, String outputDir,
      Locale language, boolean isValid, String to, String from, boolean validateRteAlso) {
    boolean result = isValid;

    File file;

    if (!validateRteAlso || this.serializedXcblFile == null) {
      // create the actual xcblFile
      file = XcblHelper.createXcblFile(InvoiceXcblHelper.INVOICDIR, XcblHelper.getWorkDir(), getInvoiceNumber(invoiceXcbl), null,
          InvoiceXcblHelper.DOC_INVOICE_DIR,
          createDocumentCompoundType(INVOIC.name(), InvoiceXcblHelper.USER_RECORD), from, to);

      try {
        XcblHelper.marshalXcblDocument(file, invoiceXcbl);
        this.serializedXcblFile = file;
      }
      catch (IOException | JAXBException e) {
        log.error("An error occured while writing xcbl file", e);
        return false;
      }
    }
    else {
      file = this.serializedXcblFile;
    }

    // initialize the Validator to load constraints by type of legislation
    XCBLValidator validator = new XCBLValidator(ConstraintsParser.getInstance()
        .loadValidator(legislation.code()));

    ConstraintType constraintType;
    if (legislation.isPropertyBased()) {
      constraintType = FR_B2B;
    }
    else {
      constraintType = legislation.code();
    }

    Set<String> errorMessages = validator.validate(file, constraintType);
    if (CollectionUtils.isNotEmpty(errorMessages)) {
      result = false;
      errorMessages.forEach(message -> {
        String msg = getMessage("edocvldlbls." + message,
            "gnxxcblcomlbls.legal_controls_validation_general_error", selectedLanguage);
        TaskHelper.error(this, null, msg);
      });
    }

    if (!result) {
      FileUtils.deleteQuietly(file);
      this.serializedXcblFile = null;
    }

    return result;
  }

  private MutablePair<String, String> obtainToAndFrom(Document parentDocument, Invoice invoiceXcbl) {
    MutablePair<String, String> result = new MutablePair<String, String>();

    if (parentDocument == null) {
      String to = obtainToFromXcbl(invoiceXcbl);
      String from = getSellerPartyIdent(invoiceXcbl);
      result.setLeft(to);
      result.setRight(from);
      return result;
    }
    // if the parentDocument is order swap to and from
    boolean isOrderType = ORDERS.name()
        .equals(parentDocument.getType());
    String to = isOrderType ? parentDocument.getFrom() : parentDocument.getTo();
    String from = isOrderType ? parentDocument.getTo() : parentDocument.getFrom();

    result.setLeft(to);
    result.setRight(from);

    return result;
  }

  /**
   * Validate the xcbl file using the rte template
   *
   * @param parentDocument
   * @param invoiceXcbl
   * @param rteSource
   * @param outputDir
   * @param company
   * @param language
   * @param isValid
   * @param validateLegalControlsAlso
   * @return
   */
  private boolean checkRteValidation(Document parentDocument, Invoice invoiceXcbl, String rteSource, String outputDir, Company company,
      Locale language, boolean isValid, boolean validateLegalControlsAlso) {
    // TODO make abstract class for all StandardEdition and reuse code of this method
    MutablePair<String, String> toFrom = this.obtainToAndFrom(parentDocument, invoiceXcbl);

    return checkRteValidation(invoiceXcbl, rteSource, outputDir, company, language, isValid, toFrom.getLeft(), toFrom.getRight(),
        validateLegalControlsAlso);
  }

  private boolean checkRteValidation(Invoice invoiceXcbl, String rteSource, String outputDir, Company company, Locale language,
      boolean isValid, String to, String from, boolean validateLegalControlsAlso) {
    boolean result = isValid;

    File file;
    InputSource inputSource;

    if (!validateLegalControlsAlso || this.serializedXcblFile == null) {
      // create the actual xcblFile If the validation fails or if it failed before delete it
      file = XcblHelper.createXcblFile(InvoiceXcblHelper.INVOICDIR, XcblHelper.getWorkDir(), getInvoiceNumber(invoiceXcbl), null,
          InvoiceXcblHelper.DOC_INVOICE_DIR,
          createDocumentCompoundType(INVOIC.name(), InvoiceXcblHelper.USER_RECORD), from, to);
      inputSource = new InputSource(file, FileType.XCBL);
      try {
        XcblHelper.marshalXcblDocument(file, invoiceXcbl);
      }
      catch (IOException | JAXBException e) {
        log.error("An error occured while writing xcbl file", e);
        try {
          inputSource.close();
        }
        catch (IOException e1) {
          log.error("Another error occured while closing input stream", e1);
        }
        return false;
      }
    }
    else {
      // we have the file
      file = this.serializedXcblFile;
      inputSource = new InputSource(file, FileType.XCBL);
    }

    XcblRteValidationEngine engine = getXcblRteValidationEngine(rteSource, company);
    // validate xcbl using RTE engine
    XcblRteValidationResult validationResult = engine.validate(inputSource, language);

    // if the validation was successful keep the generated file in memory (do not marshall the xcbl again )
    if (validationResult.isValid()) {
      this.serializedXcblFile = file;
    }
    else {
      String messages = validationResult.getMessage();

      messages.lines()
          .forEach(message -> TaskHelper.error(this, null,
              StringUtils.isNotEmpty(message) ? message : "gnxxcblcomlbls.rte_validation_general_error"));
      result = false;
    }

    // even if the invoice was invalidated before remove the temporary file
    if (!result) {
      // we do have errors and will not validate legal controls
      if (!validateLegalControlsAlso) {
        FileUtils.deleteQuietly(file);
        this.serializedXcblFile = null;
      }
    }

    return result;
  }

  private XcblRteValidationEngine getXcblRteValidationEngine(String rteSource, Company company) {
    if (xcblRteValidationEngine == null) {
      xcblRteValidationEngine = new XcblRteValidationEngine(rteSource, company, getInstance());
    }
    return xcblRteValidationEngine;
  }

  public boolean canRefuseInvoice() {
    if (editInvoice) {
      InvoiceIndex inv = (InvoiceIndex) document.getIndexValue();
      return !DocumentStatus.REFUSED.equals(inv.getStatusAsEnumValue()) &&
          getUserScopeCodes(getAuthenticatedUser()).contains(inv.getBuyerPartyID());
    }
    return false;
  }

  public void onRefuseInvoice() {
    if (document != null) {
      document.setStatusWithEnumValue(DocumentStatus.REFUSED);
      ((InvoiceIndex) document.getIndexValue()).setStatusWithEnumValue(DocumentStatus.REFUSED);
      documentService.saveDocument(document);
      addAttachedFiles();
      documentTimelineService.save(new DocumentTimeline(document, new Date(), REFUSE, getCurrentUser().getId(), refuseComment));
      if (rteSourceRefuse != null) {
        final Deployment deployment = this.projectService.getDeployment(rteSourceRefuse);
        if (deployment == null) {
          error("gnxxcblcomlbls.error_process_not_deployed");
          return;
        }
        try {
          new ProcessReportExecution(document.getNumber(),
              this.projectService.startAsync(
                  deployment,
                  new HashMap<String, Object>() {
                    {
                      put(DOCUMENTS.toString(), singletonList(document));
                      put(WRITE_ASYNC.toString(), false);
                      put("pREFUSE_COMMENT", refuseComment);
                      /*
                       * even if the process is asynchronous we need to generate the reports synchronous to be able to obtain the report
                       * status of the process
                       */
                    }
                  }));
        }
        catch (Exception e) {
          error("Cannot run post refusal process");
        }
      }
    }
  }

  public void onCancelRefuseInvoice() {
    this.refuseComment = null;
  }

  public List<SelectItem> getLegalControlsValues() {
    return this.legalControlsValues;
  }

  // TODO CHORUS PRO
  public List<SelectItem> getPaymentChoiceValues() {
    List<SelectItem> items = stream(PaymentChoiceEnum.values())
        .map(item -> new SelectItem(item, MessageHelper.getMessage(LABEL_FAMILY + "." + item.name(), item.name(), selectedLanguage)))
        .collect(toCollection(ArrayList::new));
    return items;
  }

  public List<SelectItem> getBillingFrameworkValues() {
    List<SelectItem> items = stream(ChorusInvoiceHelper.BillingFramework.values())
        .map(item -> new SelectItem(item, MessageHelper.getMessage(LABEL_FAMILY + "." + item.name(), item.name(), selectedLanguage)))
        .collect(toCollection(ArrayList::new));
    return items;
  }

  public List<SelectItem> getVATTypeValues() {
    List<SelectItem> items = stream(VATType.values())
        .map(item -> new SelectItem(item, MessageHelper.getMessage(LABEL_FAMILY + "." + item.name(), item.name(), selectedLanguage)))
        .collect(toCollection(ArrayList::new));
    return items;
  }

  private void prepareInvoiceForSaving() {
    if (this.invoiceXcbl.getInvoiceHeader()
        .getInvoiceLanguage() == null ||
        this.invoiceXcbl.getInvoiceHeader()
            .getInvoiceLanguage()
            .getLanguageCoded() == null) {
      this.invoiceXcbl.getInvoiceHeader()
          .setInvoiceLanguage(new LanguageType());
      this.invoiceXcbl.getInvoiceHeader()
          .getInvoiceLanguage()
          .setLanguageCoded(fromValue(this.currentSession()
              .getLocale()
              .getLanguage()));
    }
    if (chorusPortlet)
      chorusInvoiceHelper.prepareInvoiceForSaving(invoiceXcbl);

    populateInvoiceParty(this.invoiceXcbl, listOfPartyCoded, partner);

    populateInvoiceDates(this.invoiceXcbl, this.listOfDateCoded);

    populateActualPayments();

    // Invoice header allowance or charge
    populateInvoiceHeaderAllowOrCharge();

    // Invoice header references management
    populateInvoiceHeaderReferences();

    InvoiceXcblHelper.addIbanAndBic(sellerIban, sellerBic, invoiceXcbl);

    if (!this.getEditableProperties()
        .getEditablePayment()
        .getColumnDescriptor("invoicePaymentChoice")
        .getEditable()) {
      this.setPaymentChoice(selectedPaymentChoice);
    }

    if (!this.taxExempt) {
      ListOfInvoiceTaxSummaryType listOfTaxSummaryType = new ListOfInvoiceTaxSummaryType();
      listOfTaxSummaryType.getTaxSummary()
          .addAll(getTaxSummaries());
      invoiceXcbl.getInvoiceSummary()
          .setListOfTaxSummary(listOfTaxSummaryType);
    }
    // since AIO-13778 parafiscal taxes do not take into account the vat exemption
    AllowOrChargeSummaryType allowOrChargeSummaryType = new AllowOrChargeSummaryType();
    allowOrChargeSummaryType.getTotalAllowOrCharge()
        .addAll(getParafiscalTaxSummaries());
    invoiceXcbl.getInvoiceSummary()
        .setAllowOrChargeSummary(allowOrChargeSummaryType);
  }

  private boolean isInvoiceTotalMonetaryAmountNegative(Invoice invoiceXcbl) {
    Optional<BigDecimal> amount = Optional.ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceSummary)
        .map(InvoiceSummaryType::getInvoiceTotals)
        .map(InvoiceTotalsType::getInvoiceTotal)
        .map(InvoiceMonetaryValueType::getMonetaryAmount)
        .map(ComplexBigDecimalType::getValue);
    return amount.isPresent() && amount.get()
        .compareTo(ZERO) < 0;
  }

  private void populateActualPayments() {
    if (!getActualPayments().isEmpty()) {
      ListOfInvoiceActualPaymentType listOfActualPaymentType = new ListOfInvoiceActualPaymentType();
      listOfActualPaymentType.getActualPayment()
          .addAll(actualPayments);
      this.invoiceXcbl.getInvoiceSummary()
          .setListOfActualPayment(listOfActualPaymentType);
    }
  }

  private Optional<InvoiceReferenceCodedType> getInvoiceReferencesReceivingNumber(List<InvoiceReferencesType> invoiceRefs) {
    List<InvoiceReferenceCodedType> references = invoiceRefs.stream()
        .filter(invRef -> invRef.getOtherInvoiceReferences() != null && isNotEmpty(invRef.getOtherInvoiceReferences()
            .getReferenceCoded()))
        .map(invRef -> invRef.getOtherInvoiceReferences())
        .map(ListOfInvoiceReferenceCodedType::getReferenceCoded)
        .findFirst()
        .orElse(new ArrayList<>());
    Optional<InvoiceReferenceCodedType> referenceTypeCoded = references.stream()
        .filter(refCoded -> RECEIVING_NUMBER.equals(refCoded.getReferenceTypeCoded()))
        .findFirst();
    return referenceTypeCoded;
  }

  private void populateInvoiceHeaderReferences() {
    List<InvoiceReferencesType> invoiceRefs = this.invoiceXcbl.getInvoiceHeader()
        .getInvoiceReferences();
    if (CollectionUtils.isNotEmpty(this.invoiceReferences)) {
      Optional<InvoiceReferencesType> invoiceReferencesTypeCostAllocation = invoiceRefs.stream()
          .filter(a -> a.getCostAllocation() != null)
          .findFirst();
      ListOfInvoiceReferenceCodedType otherRefs = cloner.deepClone(invoiceRefs
          .get(0)
          .getOtherInvoiceReferences());
      invoiceRefs.clear();
      invoiceRefs.add(mapInvoiceReferenceToXcblType(this.invoiceReferences));
      // we should merge with what there was
      if (otherRefs != null && isNotEmpty(otherRefs.getReferenceCoded()))
        if (invoiceRefs.get(0)
            .getOtherInvoiceReferences() == null)
          invoiceRefs.get(0)
              .setOtherInvoiceReferences(otherRefs);
        else
          otherRefs.getReferenceCoded()
              .stream()
              .filter(ref -> !ORDER_RESPONSE_NUMBER.equals(Optional.ofNullable(ref)
                  .map(InvoiceReferenceCodedType::getReferenceTypeCodedOther)
                  .orElse(null)))
              .forEach(invoiceRefs.get(0)
                  .getOtherInvoiceReferences()
                  .getReferenceCoded()::add);
      // put cost allocation
      if (invoiceReferencesTypeCostAllocation.isPresent() &&
          invoiceReferencesTypeCostAllocation.get()
              .getCostAllocation() != null) {
        invoiceRefs.get(0)
            .setCostAllocation(invoiceReferencesTypeCostAllocation.get()
                .getCostAllocation());
      }
    }

    if (invoiceRefs.isEmpty()) {
      invoiceRefs.add(new InvoiceReferencesType());
    }
    if (invoiceRefs.get(0)
        .getListOfRelatedInvoiceRef() == null) {
      invoiceRefs.get(0)
          .setListOfRelatedInvoiceRef(new ListOfRelatedInvoiceRefType());
    }

    List<RelatedInvoiceRefType> invoiceReferencesType = invoiceRefs.get(0)
        .getListOfRelatedInvoiceRef()
        .getRelatedInvoiceRef();
    // add in XCBL
    if (relatedInvoiceRef != null)
      invoiceReferencesType.add(relatedInvoiceRef);
    populateVAT(this.invoiceXcbl);

  }

  private void populateInvoiceHeaderAllowOrCharge() {
    if (!allowOrChargeTypes.isEmpty()) {
      ListOfInvoiceAllowOrChargeType listOfAllowOrChargeType = new ListOfInvoiceAllowOrChargeType();
      clearInvoiceTypeOfAllowanceOrCharge(allowOrChargeTypes);
      listOfAllowOrChargeType.getAllowOrCharge()
          .addAll(allowOrChargeTypes);
      this.invoiceXcbl.getInvoiceHeader()
          .setInvoiceAllowancesOrCharges(listOfAllowOrChargeType);
    }
  }

  private void initBillingFramework() {
    if (this.invoiceXcbl.getInvoiceHeader()
        .getCountrySpecificRequirements() == null) {
      CountrySpecificRequirementsType countrySpecificRequirementsType = new CountrySpecificRequirementsType();
      ListOfCountryRequirementType listOfCountryRequirementType = new ListOfCountryRequirementType();
      countrySpecificRequirementsType.setOtherCountryRequirements(listOfCountryRequirementType);
      this.invoiceXcbl.getInvoiceHeader()
          .setCountrySpecificRequirements(countrySpecificRequirementsType);
      CountryRequirementType countryRequirementType = new CountryRequirementType();
      CountryType countryType = new CountryType();
      ListOfSpecificRequirementType listOfSpecificRequirementType = new ListOfSpecificRequirementType();
      SpecificRequirementType specificRequirementType = new SpecificRequirementType();
      ReferenceType referenceType = new ReferenceType();
      countryType.setCountryCoded(CountryCodeType.FR);
      countryRequirementType.setCountry(countryType);
      specificRequirementType.setRequirementTypeCoded("BillingType");
      specificRequirementType.setRequirementReference(referenceType);
      listOfSpecificRequirementType.getSpecificRequirement()
          .add(specificRequirementType);
      countryRequirementType.setListOfSpecificRequirement(listOfSpecificRequirementType);

      this.invoiceXcbl.getInvoiceHeader()
          .getCountrySpecificRequirements()
          .getOtherCountryRequirements()
          .getCountryRequirement()
          .add(countryRequirementType);
    }
  }

  public boolean isFromAsn() {
    return DESADV.name()
        .equals(document.getType());
  }

  // TODO : Refacto -> InvoiceXcblHelper
  boolean checkStartDateBeforeEndDate() {
    InvoiceValidityDatesType invoicingPeriod;
    try {
      invoicingPeriod = invoicingPeriod();
      if (invoicingPeriod == null) {
        return true;
      }
    }
    catch (NullPointerException e) {
      // most probably no period is defined, then question is not relevant
      return true;
    }

    Date startDate = getStartDateFromInvoiceValidityDatesType(invoicingPeriod);
    Date endDate = getEndDateFromInvoiceValidityDatesType(invoicingPeriod);
    return startDate == null || startDate.before(endDate) || startDate.equals(endDate);
  }

  public boolean messageExistsIncontext(String message) {

    List<FacesMessage> messageList = FacesContext.getCurrentInstance()
        .getMessageList();
    return messageList.stream()
        .anyMatch(facesMessage -> message.equals(facesMessage.getDetail()));
  }

  protected boolean checkDuplicateNumber() {
    return duplicateInvoiceNumber && invoiceService.checkDuplicateInvoice(invoiceXcbl, InvoiceHelper.getIdentValue(listOfPartyCoded.get(2)
        .getPartyID()));
  }

  public void populateInvoiceParty(Invoice invoice, List<InvoicePartyCodedType> listOfPartyCoded, Partner partner) {
    InvoiceXcblHelper.populateInvoiceParty(invoiceXcbl, listOfPartyCoded, partner);
    if (this.chorusPortlet) {
      InvoicePartyType parties = invoiceXcbl.getInvoiceHeader()
          .getInvoiceParty();
      parties.setBillToParty(parties.getBuyerParty());
    }
  }

  InvoiceValidityDatesType invoicingPeriod() {
    return invoiceXcbl.getInvoiceHeader()
        .getInvoiceDates()
        .getInvoicingPeriod();
  }

  public void updateInvoiceDocument() {
    log.debug("Start updating Invoice ...");
    try {
      if (isInvoiceValidated()) {
        prepareInvoiceForSaving();
        document.setStatusWithEnumValue(INVOICED);
        document.setIndexValue(invoiceXcbl);
        invoiceService.removeFromDocumentGeneratedFiles(document, asList(FileType.HTML, FileType.PDF, FileType.CSV));
        createOrUpdateXcblFile(document, invoiceXcbl, INVOICDIR, document.getNumber(), null, null,
            getConfigurationService().getString(ConfigurationKey.OUTPUT_DIR), null, serializedXcblFile, null, false);
        documentService.saveDocument(document);
        addAttachedFiles();
        MessageHelper.addMessage(null, FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".info_confirm_action", TASK_MESSAGES);
        this.lockForMe = false;
        this.editInvoiceInError = false;
        resetEdition();
      }
      else {
        addMessage(null, FacesMessage.SEVERITY_ERROR, "gnxxcblcomlbls.error_saving_concurrent_access",
            TASK_MESSAGES);
      }
    }
    catch (Exception e) {
      log.error("Failed to update invoice {}", e);
      addMessage(null, FacesMessage.SEVERITY_ERROR, "gnxxcblcomlbls.error_saving_concurrent_access", TASK_MESSAGES);
    }
    log.debug("End");
  }

  public void addMessage(Throwable t, Severity severity, String messageId, String forClientId, Object... args) {
    MessageHelper.addMessage(t, severity, messageId, forClientId, args);
  }

  public void resetEdition() {
    birtFile = null;
    files = null;
    document = null;
    invoiceXcbl = null;
    editInvoiceInError = false;
    xcblEdition = null;
  }

  public void resetIbanAndBic() {
    if (!ocrAcquisitionType) {
      sellerIban = null;
      sellerBic = null;
      changedCurrency = true;
    }
  }

  public void onChangePortletMode() {
    TaskDefinition taskDefinition = TaskHelper.getDefinition(this);
    // do not display the message when the portlet is created, only if it is edited
    if (taskDefinition.getTitle() != null) {
      this.addMessage(null, FacesMessage.SEVERITY_WARN, LABEL_FAMILY + ".creation_edition_change_message", TASK_MESSAGES);
    }
    if (editInvoice) {
      getEditableProperties()
          .getEditableFooter()
          .getColumnDescriptor("amountToBePaid")
          .setRendered(true);
    }
    else {
      getEditableProperties()
          .getEditableFooter()
          .getColumnDescriptor("amountToBePaid")
          .setRendered(false);
    }
  }

  public void onToggleChorusPro() {
    if (chorusPortlet) {
      getEditableProperties()
          .getEditableHeader()
          .getColumnDescriptor("invoiceMarketNumber")
          .setRendered(true);
      getEditableProperties()
          .getEditableHeader()
          .getColumnDescriptor("invoiceCommitmentNumber")
          .setRendered(true);
    }
    else {
      getEditableProperties()
          .getEditableHeader()
          .getColumnDescriptor("invoiceMarketNumber")
          .setRendered(false);
      getEditableProperties()
          .getEditableHeader()
          .getColumnDescriptor("invoiceCommitmentNumber")
          .setRendered(false);
    }
  }

  public Invoice getXcblInvoice() {
    return this.invoiceXcbl;
  }

  public void setIndexable(Invoice indexable) {
    this.invoiceXcbl = indexable;
  }

  public XcblEditionBuilder.XcblEdition getXcblEdition() {
    return xcblEdition;
  }

  public void setInvoiceService(InvoiceService invoiceService) {
    this.invoiceService = invoiceService;
  }

  public boolean isLockForMe() {
    return this.lockForMe;
  }

  public int getNetPriceDecimalPlaces() {
    return netPriceDecimalPlaces;
  }

  public void setNetPriceDecimalPlaces(int netPriceDecimalPlaces) {
    this.netPriceDecimalPlaces = netPriceDecimalPlaces;
  }

  public int getGrossPriceDecimalPlaces() {
    return grossPriceDecimalPlaces;
  }

  public void setGrossPriceDecimalPlaces(int grossPriceDecimalPlaces) {
    this.grossPriceDecimalPlaces = grossPriceDecimalPlaces;
  }

  public int getNetPriceIndex() {
    return netPriceIndex;
  }

  public int getGrossPriceIndex() {
    return grossPriceIndex;
  }

  public String getOutputDir() {
    return outputDir;
  }

  public void setOutputDir(String outputDir) {
    this.outputDir = outputDir;
  }

  public String getStatusAfterInvoiceModification() {
    return statusAfterInvoiceModification;
  }

  public void setStatusAfterInvoiceModification(String statusAfterInvoiceModification) {
    this.statusAfterInvoiceModification = statusAfterInvoiceModification;
  }

  public InvoiceIdentifierType getFormJuridique(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null) {
      ListOfInvoiceIdentifierType listOfInvoiceIdentifierType = new ListOfInvoiceIdentifierType();
      InvoiceIdentifierType legalForm = XcblHelper.initLegalFormIdentifierType();
      listOfInvoiceIdentifierType.getIdentifier()
          .add(legalForm);
      return legalForm;
    }

    Optional<InvoiceIdentifierType> foundFormJuridique = PartyCodeXcblHelper.getFormJuridique(listOfIdentifier);
    if (!foundFormJuridique.isPresent()) {
      InvoiceIdentifierType legalForm = XcblHelper.initLegalFormIdentifierType();
      listOfIdentifier.getIdentifier()
          .add(legalForm);
      return legalForm;
    }
    return foundFormJuridique.get();
  }

  // -- Share Capital --

  public InvoiceIdentifierType getShareCapital(ListOfInvoiceIdentifierType listOfIdentifier) {
    if (listOfIdentifier == null) {
      ListOfInvoiceIdentifierType listOfInvoiceIdentifierType = new ListOfInvoiceIdentifierType();
      InvoiceIdentifierType shareCapital = XcblHelper.initShareCapitalIdentifierType();
      listOfInvoiceIdentifierType.getIdentifier()
          .add(shareCapital);
      return shareCapital;
    }

    Optional<InvoiceIdentifierType> foundShareCapital = PartyCodeXcblHelper.getShareCapital(listOfIdentifier);
    if (!foundShareCapital.isPresent()) {
      InvoiceIdentifierType shareCapital = XcblHelper.initLegalFormIdentifierType();
      listOfIdentifier.getIdentifier()
          .add(shareCapital);
      return shareCapital;
    }
    return foundShareCapital.get();
  }

  // -- SIREN --

  // Used for FO rendering
  public boolean hasSiren(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.hasSiren(listOfIdentifier);
  }

  public int getSirenIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.getSirenIndex(listOfIdentifier);
  }

  // -- RCS-RCM --

  // Used for FO rendering
  public boolean hasRcsRcm(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.hasRcsRcm(listOfIdentifier);
  }

  public int getRcsRcmIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.getRcsRcmIndex(listOfIdentifier);
  }

  // -- PT - GOVERNMENT-HASHCODE

  public void onChangeSupplierCountry(CountryCodeType countryCoded, ListOfInvoiceIdentifierType listOfIdentifier) {
    InvoiceXcblHelper.addIdentifiersForPortugal(countryCoded, listOfIdentifier);
  }

  // Used for FO rendering
  public boolean hasGovernmentHashcode(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.hasGovernmentHashcode(listOfIdentifier);
  }

  public int getGovernmentHashcodeIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.getGovernmentHashcodeIndex(listOfIdentifier);
  }

  // -- PT - NIF

  // Used for FO rendering
  public boolean hasNIF(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.hasNIF(listOfIdentifier);
  }

  public int getNIFIndex(ListOfInvoiceIdentifierType listOfIdentifier) {
    return PartyCodeXcblHelper.getNIFIndex(listOfIdentifier);
  }

  // -- DateOfOperation --
  public boolean hasDateOfOperation() {
    return InvoiceEditionHelper.hasDateOfOperation(listOfDateCoded);
  }

  public int getDateOfOperationIndex() {
    return InvoiceEditionHelper.getDateOfOperationIndex(listOfDateCoded);
  }

  // -- FreeText -- //

  public FreeTextProperties getFreeTextProperties() {
    if (freeTextProperties == null)
      freeTextProperties = new FreeTextProperties();
    return freeTextProperties;
  }

  public void setFreeTextProperties(FreeTextProperties freeTextProperties) {
    this.freeTextProperties = freeTextProperties;
  }

  /**
   * This class represents the required tax id for an invoice party.<br/>
   * Fields :<br/>
   * . role - is invoice party<br/>
   * . tax - is required tax or not<br/>
   *
   * <AUTHOR>
   */
  public class TaxMandatory implements Serializable {

    private static final long serialVersionUID = -1327988888918608029L;

    // FIELDS //

    private String role;

    private Boolean tax;

    // CONSTRUCTOR //

    public TaxMandatory(String role, Boolean tax) {
      this.role = role;
      this.tax = tax;
    }

    // GETTERS AND SETTERS //

    public String getRole() {
      return role;
    }

    public void setRole(String role) {
      this.role = role;
    }

    public Boolean getTax() {
      return tax;
    }

    public void setTax(Boolean tax) {
      this.tax = tax;
    }
  }

  public boolean showPdf() {
    return showPdf;
  }

  public void onShowHidePdf() {
    showPdf = !showPdf;
  }

  public boolean isDocumentHasNoPdf() {
    return documentHasNoPdf;
  }

  public void setDocumentHasNoPdf(boolean documentHasNoPdf) {
    this.documentHasNoPdf = documentHasNoPdf;
  }

  private transient StructureConverter structureConverter = new StructureConverter(new ArrayList<>());

  private transient ServiceConverter serviceConverter = new ServiceConverter(new ArrayList<>());

  private transient Structure selectedstructure = null;

  private transient Service selectedService = null;

  private transient List<Service> availableServices = new ArrayList<>();

  public List<Structure> onSearchStructures(String query) {
    List<Structure> structures = chorusProService.findStructures(query);
    structureConverter.setStrucutreList(structures);
    return structures;
  }

  public List<Service> onSearchServices(String query) {

    return availableServices.stream()
        .filter(service -> StringUtils.startsWith(service.getCodeService(), query) ||
            StringUtils.startsWith(service.getLibelleService(), query))
        .collect(Collectors.toList());

  }

  public StructureConverter getStructureConverter() {
    return structureConverter;
  }

  public ServiceConverter getServiceConverter() {
    return serviceConverter;
  }

  public void setSelectedStructure(Structure structure) {
    // carrierselected is not a reference so that modifications can be made on it without changing the original object
    if (structure != null) {
      selectedstructure = new Structure();
      BeanUtils.copyProperties(structure, selectedstructure);
    }
  }

  public void onSelectStructure(SelectEvent e) {
    Structure structure = (Structure) e.getObject();
    if (structure == null)
      return;

    availableServices = chorusProService.findServices(structure.getIdStructureCPP());
    serviceConverter.setServiceList(availableServices);
    if (availableServices.size() == 1) {
      selectedService = availableServices.get(0);
    }
    if (!hasServices()) {
      ChorusParty structureDetails = chorusProService.findStructureDetails(structure.getIdStructureCPP());
      onSelectChorusParty("Buyer", structureDetails);
    }
    else if (availableServices.size() == 1) {
      ChorusParty serviceDetails = chorusProService.findServiceDetails(selectedstructure.getIdStructureCPP(),
          availableServices.get(0)
              .getIdService());
      onSelectChorusParty("Buyer", serviceDetails);
    }
    else {
      // reset form
      selectedService = null;
      onSelectChorusParty("Buyer", null);
    }
  }

  public boolean hasServices() {
    return availableServices.size() != 0;
  }

  public void onSelectService(SelectEvent e) {
    final String role = ((AutoComplete) e.getComponent()).getConverterMessage();
    if (isBlank(role))
      return;

    Service service = (Service) e.getObject();
    if (service == null)
      return;
    ChorusParty serviceDetails = chorusProService.findServiceDetails(selectedstructure.getIdStructureCPP(),
        service.getIdService());
    onSelectChorusParty("Buyer", serviceDetails);
  }

  private void onSelectChorusParty(final String role, ChorusParty chorusParty) {
    InvoicePartyCodedType partyFromStructure = chorusParty == null ? ChorusInvoiceHelper.initInvoiceParty(selectedstructure, role)
        : ChorusInvoiceHelper.convert(role, selectedstructure, chorusParty, null);

    final InvoicePartyCodedType party = getPartyFromListOfPartyCoded(role);

    cloner.copyPropertiesOfInheritedClass(partyFromStructure, party);
    selectedParty = party;
  }

  public void checkWorkflow() {
    if (!editInvoice || !replayWorkflow || document.getProcessingWay() == null || QueryHelper.search(getBqlFilter(), InvoiceIndex.class,
            Collections.singletonList(document.getIndexValue()))
        .isEmpty())
      return;

    currentDocumentStatus = workflowDocumentStatusService.getCurrentWorkflowDocumentStatusDocId(document.getId());
    Workflow workflow = workflowService.getApplicableWorkflow(INVOIC, document, this.isDedicatedPortal());
    workflowId = Optional.ofNullable(workflow)
        .map(Workflow::getId)
        .orElse(null);

    if (currentDocumentStatus == null && workflow != null || replayWorkflow) {
      if (currentDocumentStatus != null)
        workflowDocumentStatusService.removeWorkflowDocumentStatus(currentDocumentStatus);
      if(workflow != null) {
        assignToWorkflow(document, workflow);
        documentTimelineService.save(new DocumentTimeline(document, new Date(), TimelineAction.INIT, USER_SYSTEM, workflow.getName(), 1));
      }
    }
    else if (currentDocumentStatus != null && (workflow == null || !workflow.equals(currentDocumentStatus.getWorkflow())))
      RequestContext.getCurrentInstance()
          .addCallbackParam("confirmWorkflow", true);

  }

  public void onWorkflowChange() {
    if (currentDocumentStatus != null)
      workflowDocumentStatusService.removeWorkflowDocumentStatus(currentDocumentStatus);
    if (workflowId != null) {
      Workflow workflow = workflowService.getWorkflow(workflowId);
      assignToWorkflow(document, workflow);
      documentTimelineService.save(
          new DocumentTimeline(document, new Date(), TimelineAction.REINIT, USER_SYSTEM, currentDocumentStatus.getWorkflow()
              .getName() + TimelineHandler.COMMENT_SEPARATOR + workflow.getName(), 1));
    }
    else
      documentTimelineService.save(
          new DocumentTimeline(document, new Date(), TimelineAction.DISCONNECTED, USER_SYSTEM, currentDocumentStatus.getWorkflow()
              .getName(), 1));
  }

  protected void assignToWorkflow(Document document, Workflow workflow) {
    InvoiceIndex index = document.getIndexValue();
    WorkflowDocumentStatus workflowDocumentStatus = workflowService.createWorkflowStatus(document, workflow, index.getSellerAddressEmail(),
        index.getBuyerAddressEmail(),
        index.getBillToAddressEmail(), index.getSellerAddressEmail());
    workflowDocumentStatusService.save(workflowDocumentStatus);
    InvoiceTaskHelper.copyWorkflowInfo(document, workflow, workflowDocumentStatus, securityService, documentService);
    documentService.saveDocument(document);

    Map<Document, Map<Locale, DocumentRecipients>> documentRecipients = Collections.singletonMap(document, new HashMap<>());
    Map<Locale, DocumentRecipients> recipients = documentRecipients.get(document);
    workflowNotificationService.getUsersToBeNotifiedForStep(workflow, 1, workflowDocumentStatus)
        .forEach(
            user -> workflowNotificationService.addRecipient(InstanceHelper.language(getInstance()), recipients, user,
                DocumentRecipients::addToInternetAddress));
    workflowNotificationService.notify(documentRecipients, getInstance(), new HashMap<String, Object>(), workflow.getWorkflowSteps()
        .get(0)
        .getTemplate());
  }

  public void onCopyBillToInformation(String partyCode) {
    InvoicePartyCodedType billTo = getListOfPartyCodedTabs().stream()
        .filter(p -> BILL_TO.equals(p.getPartyRoleCoded()))
        .findAny()
        .orElse(null);
    InvoicePartyCodedType currentParty = getListOfPartyCodedTabs().stream()
        .filter(p -> partyCode.equals(p.getPartyRoleCoded()))
        .findAny()
        .orElse(null);

    if (billTo == null || currentParty == null) {
      return;
    }

    currentParty.getPartyID()
        .setIdent(billTo.getPartyID()
            .getIdent());

    InvoiceNameAddressType billToNameAddress = billTo.getNameAddress();
    InvoiceNameAddressType currentPartyNameAddress = currentParty.getNameAddress();

    currentPartyNameAddress
        .setName1(billToNameAddress
            .getName1());

    currentPartyNameAddress
        .setStreet(billToNameAddress
            .getStreet());

    currentPartyNameAddress
        .setStreetSupplement1(billToNameAddress
            .getStreetSupplement1());

    currentPartyNameAddress
        .setPostalCode(billToNameAddress
            .getPostalCode());

    currentPartyNameAddress.setCity(billToNameAddress
        .getCity());

    currentPartyNameAddress.setCountry(billToNameAddress.getCountry());

    if (BUYER.equals(partyCode)) {
      ListOfInvoiceIdentifierType billToListOfIdenfifier = billTo.getListOfIdentifier();
      ListOfInvoiceIdentifierType currentPartyListOfIdenfifier = currentParty.getListOfIdentifier();

      final int indexRN = getRcsRcmIndex(currentPartyListOfIdenfifier);
      if (indexRN != -1) {
        final InvoiceIdentifierType identifierRN = currentPartyListOfIdenfifier
            .getIdentifier()
            .get(indexRN);
        identifierRN.setIdent(billToListOfIdenfifier
            .getIdentifier()
            .get(getRcsRcmIndex(billToListOfIdenfifier))
            .getIdent());
        currentPartyListOfIdenfifier
            .getIdentifier()
            .add(indexRN, identifierRN);
      }

      final int indexGR = getSirenIndex(currentPartyListOfIdenfifier);
      if (indexGR != -1) {
        final InvoiceIdentifierType identifierGR = currentPartyListOfIdenfifier
            .getIdentifier()
            .get(indexGR);
        identifierGR.setIdent(billToListOfIdenfifier
            .getIdentifier()
            .get(getSirenIndex(billToListOfIdenfifier))
            .getIdent());
        currentPartyListOfIdenfifier
            .getIdentifier()
            .add(indexGR, identifierGR);
      }

      currentParty.getPartyTaxInformation()
          .getTaxIdentifier()
          .setIdent(billTo.getPartyTaxInformation()
              .getTaxIdentifier()
              .getIdent());
    }

    currentParty.getPrimaryContact()
        .setContactName(billTo.getPrimaryContact()
            .getContactName());

    final int indexEmail = getEmailAddressIndex(billTo.getPrimaryContact()
        .getListOfContactNumber());
    if (indexEmail != -1) {
      final InvoiceContactNumberType contactEmail = currentParty.getPrimaryContact()
          .getListOfContactNumber()
          .getContactNumber()
          .get(indexEmail);
      contactEmail.setContactNumberValue(billTo.getPrimaryContact()
          .getListOfContactNumber()
          .getContactNumber()
          .get(getEmailAddressIndex(billTo.getPrimaryContact()
              .getListOfContactNumber()))
          .getContactNumberValue());
      currentParty.getPrimaryContact()
          .getListOfContactNumber()
          .getContactNumber()
          .add(indexEmail, contactEmail);
    }

  }

  private void initMemberSingleSubject() {
    if (this.invoiceXcbl.getInvoiceHeader()
        .getListOfStructuredNote() == null) {
      this.memberSingleSubject = false;
    }
    else {
      this.memberSingleSubject = this.invoiceXcbl.getInvoiceHeader()
          .getListOfStructuredNote()
          .getStructuredNote()
          .stream()
          .anyMatch(sn -> memberOfASingleSubject.equals(sn.getGeneralNote()) && "TDX".equals(sn.getTextTypeCodedOther()));
    }
  }

  public void onChangeMemberSingleSubject(AjaxBehaviorEvent event) {
    UIComponent component = event.getComponent();
    if (component != null) {
      UIInput inputComponent = (UIInput) component;
      Boolean value = (Boolean) inputComponent.getValue();
      ListOfStructuredNoteType structuredNodes = this.invoiceXcbl.getInvoiceHeader()
          .getListOfStructuredNote();
      if (value) {
        StructuredNoteType structuredNote = new StructuredNoteType();
        structuredNote.setGeneralNote(memberOfASingleSubject);
        structuredNote.setTextTypeCoded(TextTypeCodeType.OTHER);
        structuredNote.setTextTypeCodedOther(txd);
        if (structuredNodes == null) {
          ListOfStructuredNoteType listOfStructuredNote = new ListOfStructuredNoteType();
          listOfStructuredNote.getStructuredNote()
              .add(structuredNote);
          this.invoiceXcbl.getInvoiceHeader()
              .setListOfStructuredNote(listOfStructuredNote);
        }
        else {
          structuredNodes
              .getStructuredNote()
              .add(structuredNote);
        }
        this.openPartyTab(DECLARANTS_AGENT);
      }
      else {
        Optional<StructuredNoteType> structuredNote = structuredNodes
            .getStructuredNote()
            .stream()
            .filter(sn -> memberOfASingleSubject.equals(sn.getGeneralNote()) && txd.equals(sn.getTextTypeCodedOther()))
            .findAny();
        structuredNote.ifPresent(structuredNodes
            .getStructuredNote()::remove);
      }
    }
  }

  public void openPartyTab(String partyRole) {
    if (!listOfPartyCodedTabs.stream()
        .anyMatch(p -> partyRole.equals(p.getPartyRoleCoded()))) {
      InvoicePartyCodedType partyCodedType = createInvoicePartyCoded();
      partyCodedType.setPartyRoleCoded(partyRole);
      listOfPartyCoded.add(partyCodedType);
      if (this.editInvoice) {
        listOfPartyCodedTabs.add(partyCodedType);
      }
      else {
        listOfPartyCodedTabs.add(listOfPartyCodedTabs.size() - 1, partyCodedType);
      }
    }
  }

  public void onCancelConflictReadableFile() {
    // make uploaded file not clientReadable
    this.uploadedFileReadable = false;
    // attach the file with actionName as addFileToInvoice
    this.saveAttachedFile();
  }

  public void onConfirmConflictReadableFile() {
    String uploadedFileName = this.uploadedFile.getFileName();
    // attach the file with actionName as clientReadable
    this.saveAttachedFile();
    // update the other attached file actionName from clientReadable to addFileToInvoice
    this.eDocumentService.updateAttachedFilesReadableByAddFileToInvoice(this.document, uploadedFileName);
  }

  public void onUploadFile(FileUploadEvent event) {
    try {
      UploadedFile file = event.getFile();
      if (!getBaseName(file.getFileName()).matches("[a-zA-Z_\\-.0-9]*"))
        error("gnxxcblcomlbls.attachment_error_incorrect_file_name");
      else if (!file.getFileName()
          .contains(".") ||
          !AcceptedFilesExtension.getAcceptedFilesExtensions()
              .contains(file.getFileName()
                  .substring(file.getFileName()
                      .lastIndexOf("."))))
        MessageHelper.error("gnxxcblcomlbls.attachment_types_unaccepted", AcceptedFilesExtension.getAcceptedFilesExtensions()
            .stream()
            .map(type -> type.replace(".", ""))
            .collect(Collectors.joining(", ")));
      else if (attachments.stream()
          .map(DocumentFile::getFile)
          .map(File::getName)
          .anyMatch(name -> file.getFileName()
              .equals(name)))
        error("edctsklbls.att_attachment_error_joining_filealreadyexists");
      else if (file.getSize() < MAX_FILE_SIZE_BYTES) {
        uploadedFile = file;
        // check if the uploaded file is PDF type
        uploadedFilePDF = Arrays.stream(FileType.PDF.getMimes())
            .anyMatch(mime -> mime.equals(uploadedFile.getContentType()));
      }
      else {
        MessageHelper.error("gnxxcblcomlbls.error_import_csv_file_too_large", MAX_FILE_SIZE_BYTES / 1024 / 1024);
      }
    }
    catch (Exception e) {
      MessageHelper.error(e, "labels.exception_message", getRootCauseMessage(e));
    }
  }

  public void onCancelAttachFile() {
    uploadedFile = null;
    fileComment = null;
    uploadedFilePDF = false;
    uploadedFileReadable = false;
  }

  public void onSaveAttachedFile() {
    if (this.uploadedFileReadable && this.eDocumentService.getAttachedFilesReadable(this.document) > 0) {
      getCurrentInstance().addCallbackParam("showConflictReadableFileModal", true);
    }
    else {
      this.saveAttachedFile();
    }
  }

  public void saveAttachedFile() {
    try {
      if (uploadedFile == null)
        error("gnxxcblcomlbls.attachment_error_no_file");
      else {
        File tmpFile = new File(FileUtils.getTempDirectory(), uploadedFile.getFileName());
        FileHelper.toFile(uploadedFile.getContents(), tmpFile);
        addedFiles.add(new DocumentFile(tmpFile, getType(tmpFile),
            this.uploadedFileReadable ? CLIENT_READABLE.getName() : ADD_FILE_TO_INVOICE.getName(), null, document, getFileComment()));
        MessageHelper.info("gnxxcblcomlbls.attachment_successfully_added");
      }
    }
    catch (final Exception e) {
      MessageHelper.error("gnxxcblcomlbls.attachment_error_joining", e);
    }
    finally {
      uploadedFile = null;
      fileComment = null;
      attachments = null;
      uploadedFilePDF = false;
      uploadedFileReadable = false;
    }
  }

  private void addAttachedFiles() {
    Document doc = getInvoiceDocument();
    if (doc != null) {
      for (DocumentFile dof : addedFiles) {
        try (InputStream inputStream = new FileInputStream(dof.getFile())) {
          File f = attachedFileRepository.save(new InMemoryFile(dof.getFile()
                  .getName(), inputStream.readAllBytes()),
              new AttachedToInvoiceFileLocation(doc.getOwners(), getIndexValueIdOrReference(doc)));
          // set the right file
          dof.setFile(f);
          doc.addFile(dof);
          documentTimelineService.saveActionInTimeline(doc, getCurrentUser(), ATTACHFILE, dof.getComment(), -1);
        }
        catch (Exception e) {
          MessageHelper.error("gnxxcblcomlbls.attachment_error_joining", e);
        }
      }
      documentService.save(doc);
    }
  }

  private Document getInvoiceDocument() {
    if (document.getType()
        .contains(INVOIC.name()))
      return document;
    else {
      Document doc = document.getChildren()
          .stream()
          .filter(d -> d.getType()
              .contains(INVOIC.name()))
          .findFirst()
          .orElse(null);
      if (doc == null)
        log.error("No invoice found");
      else
        return doc;
    }
    return null;
  }

  public List<DocumentFile> getAttachedFiles() {
    if (attachments == null) {
      if (!editableProperties.getEditableFilesProperties()
          .getColumnDescriptor("manage_files")
          .getEditable()) {
        attachments = new ArrayList<>();
        return attachments;
      }
      if (document != null)
        attachments = document.getFiles()
            .stream()
            .filter(dof -> ADD_FILE_TO_INVOICE.getName()
                .equals(dof.getActionName()))
            .collect(toList());
      else
        attachments = new ArrayList<>();
      attachments.addAll(addedFiles);
    }
    return attachments;
  }

  public String getFileSize(DocumentFile dof) {
    if (dof == null || dof.getFile() == null) {
      return "0 Byte";
    }
    return FileUtils.byteCountToDisplaySize(dof.getFile()
        .length());
  }

  public Structure getSelectedStructure() {
    return selectedstructure;
  }

  public boolean isLinkToAddressRefBillTo() {
    return linkToAddressRefBillTo;
  }

  public void setLinkToAddressRefBillTo(boolean linkToAddressRefBillTo) {
    this.linkToAddressRefBillTo = linkToAddressRefBillTo;
  }

  public boolean isLinkToAddressRefBuyer() {
    return linkToAddressRefBuyer;
  }

  public void setLinkToAddressRefBuyer(boolean linkToAddressRefBuyer) {
    this.linkToAddressRefBuyer = linkToAddressRefBuyer;
  }

  public boolean isLinkToAddressRefShipTo() {
    return linkToAddressRefShipTo;
  }

  public void setLinkToAddressRefShipTo(boolean linkToAddressRefShipTo) {
    this.linkToAddressRefShipTo = linkToAddressRefShipTo;
  }

  public boolean isLinkToAddressRefBillToEdit() {
    return linkToAddressRefBillToEdit;
  }

  public void setLinkToAddressRefBillToEdit(boolean linkToAddressRefBillToEdit) {
    this.linkToAddressRefBillToEdit = linkToAddressRefBillToEdit;
  }

  public boolean isLinkToAddressRefBuyerEdit() {
    return linkToAddressRefBuyerEdit;
  }

  public void setLinkToAddressRefBuyerEdit(boolean linkToAddressRefBuyerEdit) {
    this.linkToAddressRefBuyerEdit = linkToAddressRefBuyerEdit;
  }

  public boolean isLinkToAddressRefShipToEdit() {
    return linkToAddressRefShipToEdit;
  }

  public void setLinkToAddressRefShipToEdit(boolean linkToAddressRefShipToEdit) {
    this.linkToAddressRefShipToEdit = linkToAddressRefShipToEdit;
  }

  public boolean isChorusAvailable() {
    return chorusAvailable;
  }

  public void setChorusAvailable(boolean chorusAvailable) {
    this.chorusAvailable = chorusAvailable;
  }

  public Service getSelectedService() {
    return selectedService;
  }

  public void setSelectedService(Service selectedService) {
    this.selectedService = selectedService;
  }

  public boolean isUseOrderingPartyConfig() {
    return useOrderingPartyConfig;
  }

  public void setUseOrderingPartyConfig(boolean useOrderingPartyConfig) {
    this.useOrderingPartyConfig = useOrderingPartyConfig;
  }

  public boolean isCostAllocationNumberControl() {
    return costAllocationNumberControl;
  }

  public void setCostAllocationNumberControl(boolean costAllocationNumberControl) {
    this.costAllocationNumberControl = costAllocationNumberControl;
  }

  public String getRteSourceRefuse() {
    return rteSourceRefuse;
  }

  public void setRteSourceRefuse(String rteSourceRefuse) {
    this.rteSourceRefuse = rteSourceRefuse;
  }

  public String getRefuseComment() {
    return refuseComment;
  }

  public void setRefuseComment(String refuseComment) {
    this.refuseComment = refuseComment;
  }

  public RelatedInvoiceRefType getRelatedInvoiceRef() {
    if (relatedInvoiceRef == null) {
      InvoiceOCRReferenceType invoiceOCRReferenceType = new InvoiceOCRReferenceType();
      invoiceOCRReferenceType.setRefNum(new ComplexStringType());
      invoiceOCRReferenceType.setRefDate(new ComplexDateType());
      relatedInvoiceRef = new RelatedInvoiceRefType();
      relatedInvoiceRef.setInvoiceNumber(invoiceOCRReferenceType);
    }
    return relatedInvoiceRef;
  }

  public void setRelatedInvoiceRef(RelatedInvoiceRefType relatedInvoiceRef) {
    this.relatedInvoiceRef = relatedInvoiceRef;
  }

  public void initRelativeInvoiceRef() {
    relatedInvoiceRef = new RelatedInvoiceRefType();
    relatedInvoiceRef.setRelatedInvoiceType(new InvoiceTypeType());
    relatedInvoiceRef.getRelatedInvoiceType()
        .setInvoiceTypeCoded(COMMERCIAL_INVOICE);
    relatedInvoiceRef.setInvoiceNumber(new InvoiceOCRReferenceType());
    relatedInvoiceRef.getInvoiceNumber()
        .setRefNum(new ComplexStringType());
    relatedInvoiceRef.getInvoiceNumber()
        .setRefDate(new ComplexDateType());
  }

  public String getValueControlPaymentChoiceFromBO() {
    return valueControlPaymentChoiceFromBO;
  }

  public void setValueControlPaymentChoiceFromBO(String valueControlPaymentChoiceFromBO) {
    this.valueControlPaymentChoiceFromBO = valueControlPaymentChoiceFromBO;
  }

  public VATType getVATType() {
    return vatType;
  }

  public void setVATType(VATType vatType) {
    this.vatType = vatType;

  }

  public static List<InvoiceTypeCodeType> getReferencedTypes() {
    return referencedTypes;
  }

  public boolean isSelfBillingType() {
    return InvoiceXcblHelper.isSelfBillingType(oldInvoiceTypeValue);
  }

  public InvoiceTypeCodeType getInvoiceType() {
    return Optional.ofNullable(invoiceXcbl)
        .map(Invoice::getInvoiceHeader)
        .map(InvoiceHeaderType::getInvoiceType)
        .map(InvoiceTypeType::getInvoiceTypeCoded)
        .orElse(COMMERCIAL_INVOICE);
  }

  public InvoicePartyCodedType getPartyFromListOfPartyCoded(String partyType) {
    return getListOfPartyCoded()
        .stream()
        .filter(p -> partyType.equals(p.getPartyRoleCoded()))
        .findAny()
        .orElse(null);
  }

  public void invoiceTypeChanged(InvoiceTypeCodeType type) {

    if (referencedTypes.contains(type) && relatedInvoiceRef == null) {
      initRelativeInvoiceRef();
    }
    else {
      relatedInvoiceRef = null;
    }
    if (!editInvoice) {
      InvoicePartyType parties = getInvoice().getInvoiceHeader()
          .getInvoiceParty();
      User currentUser = getCurrentUser();
      // in case the user used the autocomplete to populate parties with information from address or partner scope, the information is saved in listOfPartyCoded
      InvoicePartyCodedType buyer = getPartyFromListOfPartyCoded("Buyer");
      InvoicePartyCodedType billTo = getPartyFromListOfPartyCoded("BillTo");
      InvoicePartyCodedType seller = getPartyFromListOfPartyCoded("Seller");
      boolean buyerAutocompleted = buyer != null && buyer.getPartyID()
          .getIdent() != null;
      boolean billToAutocompleted = billTo != null && billTo.getPartyID()
          .getIdent() != null;
      boolean sellerAutocompleted = seller != null && seller.getPartyID()
          .getIdent() != null;
      if (oldInvoiceTypeValue != null && InvoiceXcblHelper.isSelfBillingType(type) && !InvoiceXcblHelper.isSelfBillingType(
          oldInvoiceTypeValue)) {
        updatePartyWithInformationFromPartner(buyerAutocompleted ? buyer : parties.getBuyerParty(), getPartner());
        updatePartyWithInformationFromPartner(billToAutocompleted ? billTo : parties.getBillToParty(), getPartner());
        clearPartyValues(sellerAutocompleted ? seller : parties.getSellerParty());
        updateSupplierContactInfo(buyerAutocompleted ? buyer : parties.getBuyerParty(), currentUser);
        updateSupplierContactInfo(billToAutocompleted ? billTo : parties.getBillToParty(), currentUser);
        setSellerIban("");
        setSellerBic("");
        oldInvoiceTypeValue = type;
      }
      else if (oldInvoiceTypeValue != null && !InvoiceXcblHelper.isSelfBillingType(type) && InvoiceXcblHelper.isSelfBillingType(
          oldInvoiceTypeValue)) {
        updatePartyWithInformationFromPartner(sellerAutocompleted ? seller : parties.getSellerParty(), getPartner());
        clearPartyValues(buyerAutocompleted ? buyer : parties.getBuyerParty());
        clearPartyValues(billToAutocompleted ? billTo : parties.getBillToParty());
        Optional<CurrencyCodeType> currencyCode = getInvoiceCurrency(getInvoice());
        String currency = currencyCode.isPresent() ? currencyCode.get()
            .toString() : "";
        sellerIban = getBankAccountService().getIBAN(getPartner(), currency);
        sellerBic = getBankAccountService().getBIC(getPartner(), currency);
        updateLegalFormAndShareCapital(getInvoice(), getPartner());
        updateSupplierContactInfo(sellerAutocompleted ? seller : parties.getSellerParty(), currentUser);
        oldInvoiceTypeValue = type;
      }
    }
    else {
      oldInvoiceTypeValue = type;
    }
  }

  public BankAccountService getBankAccountService() {
    return bankAccountService;
  }

  public boolean isVatTypeExempt() {
    return vatType.EXEMPT.equals(vatType);
  }

  public String getSellerIban() {
    if (sellerIban == null)
      sellerIban = getIBAN(invoiceXcbl.getInvoiceHeader()
          .getInvoiceCurrency()
          .getCurrencyCoded()
          .toString());
    return sellerIban;
  }

  public void setSellerIban(String sellerIban) {
    this.sellerIban = sellerIban;
  }

  public String getSellerBic() {
    if (sellerBic == null)
      sellerBic = getBIC(invoiceXcbl.getInvoiceHeader()
          .getInvoiceCurrency()
          .getCurrencyCoded()
          .toString());
    return sellerBic;
  }

  public void setSellerBic(String sellerBic) {
    this.sellerBic = sellerBic;
  }

  public boolean isChangedCurrency() {
    return changedCurrency;
  }

  public void setChangedCurrency(boolean changedCurrency) {
    this.changedCurrency = changedCurrency;
  }

  public Map<String, String> getParafiscalTaxItems() {
    return parafiscalTaxItems;
  }

  public void setParafiscalTaxItems(Map<String, String> parafiscalTaxItems) {
    this.parafiscalTaxItems = parafiscalTaxItems;
  }

  public int getDefaultValueForMaxNumberOfDaysDelay() {
    return defaultValueForMaxNumberOfDaysDelay;
  }

  public void setDefaultValueForMaxNumberOfDaysDelay(int defaultValueForMaxNumberOfDaysDelay) {
    this.defaultValueForMaxNumberOfDaysDelay = defaultValueForMaxNumberOfDaysDelay;
  }

}
