package com.byzaneo.generix.xcbl.portal.task;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.ExecutorService;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.bean.ResultingFileAction;
import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.generix.rtemachine.repository.VariablesOperations;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.xcbl.portal.task.converter.DocumentStatusEntityConverter;
import com.byzaneo.generix.xcbl.ui.edition.*;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.generix.xtrade.task.*;
import com.byzaneo.generix.xtrade.util.ColumnValue;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.ui.handler.SessionHandler;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.shadow.Shadowable;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.DocumentStatusEntity;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.google.gson.annotations.JsonAdapter;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.component.selectmanycheckbox.SelectManyCheckbox;
import org.primefaces.component.selectoneradio.SelectOneRadio;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.event.*;
import javax.faces.model.SelectItem;
import javax.faces.validator.Validator;
import javax.persistence.PreRemove;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.*;
import java.util.stream.*;

import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.MessageHelper.warn;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.xtrade.api.DocumentStage.CORRECT;
import static com.byzaneo.xtrade.api.DocumentStage.UNDEFINED;
import static com.byzaneo.xtrade.util.FreeTextHelper.createFreeTextProperty;
import static com.byzaneo.xtrade.util.FreeTextHelper.getFreeTextIndexes;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceNumber;
import static java.util.Arrays.stream;
import static java.util.Optional.of;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;

@Task(name = "InvoiceCockpit", library = "portal/xcbl", version = "1.0.0")
@TaskViewModel(labelFamily = InvoiceCockpitTask.LABEL_FAMILY)
public class InvoiceCockpitTask extends ActionModuleClass<InvoiceIndex> implements Shadowable, InvoiceCategory {

  private List<String> selectedSecurityFields = new ArrayList<>();
  private List<SelectItem> availableSecurityFields;

  private static final long serialVersionUID = 4046950551719813412L;

  private final static Logger LOGGER = LoggerFactory.getLogger(InvoiceCockpitTask.class);

  public static final String LABEL_FAMILY = "gnxxcblinvlbls";

  public transient static final String[] DEFAULT_PARTY = { "BillTo", "Buyer", "Seller", "ShipTo" };

  private transient static final List<DocumentStage> UNCLICKABLE_STAGES = Arrays.asList(UNDEFINED, CORRECT, DocumentStage.ERROR);

  private final Integer TRIGGER_ACTION_MAX_INVOICES = 100;

  private boolean managePDFCache = false;

  protected Range<Integer> invoiceDocumentExportRange = new Range<>(0, 500);

  private boolean showOnlyWorkflow = false;

  @Autowired(required = false)
  @Qualifier(VariablesOperations.OPERATIONS_NAME)
  private transient VariablesOperations variablesOperations;

  // Status
  @JsonAdapter(DocumentStatusEntityConverter.class)
  private List<DocumentStatusEntity> statusesForInvoiceModification;

  @JsonAdapter(DocumentStatusEntityConverter.class)
  private List<DocumentStatusEntity> statusesForInvoiceOcrVerify;

  /**
   * Data descriptor
   */
  private transient BeanDescriptor dataModelDescriptor;

  private boolean countEnabled = false;

  private transient SessionHandler sessionHandler;

  private int detailPrecision = 2;

  private int footerPrecision = 2;

  private transient String floatColumnPattern;

  private transient List<Object> actions;

  private transient List<Object> allActions;

  private transient List<Object> actionsCreation;

  private String nextPortletPageCreation;

  private Long actionCreateId;

  @Getter
  @Setter
  private String validationProcessId;

  private transient Boolean isInvoicingEnabled = null;

  public InvoiceCockpitTask() {
    super();

  }

  public List<String> getSelectedSecurityFields() {
    return selectedSecurityFields;
  }

  public void setSelectedSecurityFields(List<String> selectedSecurityFields) {
    this.selectedSecurityFields = selectedSecurityFields;
  }


  public List<SelectItem> getAvailableSecurityFields() {
    return availableSecurityFields;
  }

  @PostConstruct
  public void init() {
    initAvailableSecurityFieldsList(portletFlowDirection);
    if(this.selectedSecurityFields != null && this.selectedSecurityFields.isEmpty()) {
      initSelectedSecurityFieldsList();
    }


    if (FacesContext.getCurrentInstance() != null) {
      this.sessionHandler = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
      Group group = this.sessionHandler.getPrimaryGroup();
      Partner partner = null;
      Company company = null;
      if (group instanceof Partner) {
        partner = (Partner) group;
        company = (Company) partner.getParent();
      }
      else if (group instanceof Company) {
        company = (Company) group;
      }

    }
    this.executorService = getBean(ExecutorService.class, ExecutorService.SERVICE_NAME);
  }

  private void initSelectedSecurityFieldsList() {
    this.selectedSecurityFields.add(FIELD_THIRD_PARTY);
    if("SENDING".equals(this.portletFlowDirection)) {
      this.selectedSecurityFields.add(FIELD_FROM);
    } else if("RECEIVING".equals(this.portletFlowDirection)) {
      this.selectedSecurityFields.add(FIELD_TO);
    } else {
      this.selectedSecurityFields.add(FIELD_FROM);
      this.selectedSecurityFields.add(FIELD_TO);
    }
  }


  /**
   * @see AbstractXcblDocumentTask#preRemove()
   */
  @Override
  @PreRemove
  public final void preRemove() {
    super.preRemove();
  }

  /* -- OVERRIDE -- */

  /**
   * @see AbstractXcblDocumentTask#getEDocument()
   */
  @Override
  public EDocument getEDocument() {
    return EDocument.Invoice;
  }

  public void resetAvailableSecurityFields(AjaxBehaviorEvent event) {
    String currentFlowDirection =(String) ((SelectOneRadio) event.getSource()).getValue();
    initAvailableSecurityFieldsList(currentFlowDirection);
    this.selectedSecurityFields.clear();
    initSelectedSecurityFieldsList();
  }

  public void onChangeCurrentFlowDirection(AjaxBehaviorEvent event) {
    String currentFlowDirection = (String) ((SelectOneRadio) event.getSource()).getValue();
    if ("SENDING".equals(currentFlowDirection)) {
      this.selectedSecurityFields.remove(FIELD_TO);
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_TO))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(true));
      ;
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_FROM))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));
      ;
    }
    else if ("RECEIVING".equals(this.portletFlowDirection)) {
      this.selectedSecurityFields.remove(FIELD_FROM);
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_FROM))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(true));
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_TO))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));

    }
    else {
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_FROM))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_TO))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));
    }
    FacesContext context = FacesContext.getCurrentInstance();
    UIComponent component = JSFHelper.findComponent("selectManyCheckbox");
    if (component != null) {
      Optional<Validator> validator = stream(((SelectManyCheckbox) component).getValidators()).findFirst();
      validator.ifPresent(value -> value.validate(context, (component), this.selectedSecurityFields));
    }
  }

  private void initAvailableSecurityFieldsList(String currentFlowDirection) {
    availableSecurityFields = List.of(
        new SelectItem(FIELD_FROM, MessageHelper.getMessage(LABEL_FAMILY + ".from", "From default", this.selectedLanguage), "", "RECEIVING".equals(
            currentFlowDirection)),
        new SelectItem(FIELD_TO, MessageHelper.getMessage(LABEL_FAMILY + ".to", "to default", this.selectedLanguage), "", "SENDING".equals(
            currentFlowDirection) ),
        new SelectItem(FIELD_THIRD_PARTY, MessageHelper.getMessage(LABEL_FAMILY + ".thirdParty", "thirdParty", this.selectedLanguage)),
        new SelectItem("buyerPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".buyerPartyID", "buyerPartyID", this.selectedLanguage)),
        new SelectItem("sellerAgentPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".sellerAgentPartyID", "sellerAgentPartyID", this.selectedLanguage)),
        new SelectItem("factorPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".factorPartyID", "factorPartyID", this.selectedLanguage)),
        new SelectItem("payerPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".payerPartyID", "payerPartyID", this.selectedLanguage)),
        new SelectItem("buyerAgentPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".buyerAgentPartyID", "buyerAgentPartyID", this.selectedLanguage)),
        new SelectItem("remitToPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".remitToPartyID", "remitToPartyID", this.selectedLanguage)));
  }

  public void setPortletFlowDirection(String portletFlowDirection) {
    this.portletFlowDirection = portletFlowDirection;
  }


  /**
   * @see AbstractXcblDocumentTask#importDocuments(List)
   */
  @Override
  public Logs importDocuments(List<InputStream> streams) {
    return streams.isEmpty() ? null : eDocumentService.importInvoices(company, partner, streams, detailPrecision, footerPrecision);
  }

  @Override
  protected void postLazyResponse(Pageable pageable, Map<String, Object> filters,
      Page<InvoiceIndex> page) {
    Map<Long, Integer> mapCount = documentService.getAttachFileCount(page.getContent(),
        ResultingFileAction.ADD_FILE_TO_INVOICE.getName(),
        ResultingFileAction.ADD_FILE_TO_INVOICE_FROM_WORKFLOW.getName());
    page.get()
        .forEach(i -> i.setAttachFileCount(mapCount.containsKey(i.getEntityId()) ? mapCount.get(i.getEntityId()) : 0));
  }

  /**
   * Gets the asynchronous limit for document export
   */
  @Override
  public long getAsynchronousProcessLimit() {
    return getAsynchronousDocumentExportRange().getMax();
  }

  /* -- LABELS -- */

  /**
   * @see AbstractTask#i18n(String)
   */
  @Override
  public String i18n(final String key) {
    return super.i18n(LABEL_FAMILY + "." + key);
  }

  @Override
  public ColumnValue getColumnValue(InvoiceIndex bean, PropertyDescriptor property) {
    if (property.getType()
        .equals(BigDecimal.class)) {
      property.setPattern(getColumnFloatPattern());
    }
    return super.getColumnValue(bean, property);
  }

  private String getColumnFloatPattern() {
    if (floatColumnPattern == null) {
      floatColumnPattern = "0." + StringUtils.repeat("0", footerPrecision);
    }
    return floatColumnPattern;
  }

  /* -- DESCRIPTOR -- */

  @Override
  public boolean isPropertyRemovable(PropertyDescriptor property) {
    // Allows free text removal
    return property.getName()
        .contains("freeText");
  }

  @Override
  public boolean isPropertyTypeEditable(PropertyDescriptor property) {
    return this.isPropertyRemovable(property);
  }

  @Override
  // TODO
  public void onAddProperty(ActionEvent event) {
    String freetext = this.property.getName();
    if (freetext.contains("freeText")) {
      this.property.setName("freeTexts[15]");
      this.property.setLabel(freetext);
      this.property.setPath("invoiceHeader.listOfNameValueSet.nameValueSet[0].listOfNameValuePair.nameValuePair[15].value");
    }
    super.onAddProperty(event);
  }

  @Override
  public void onAddFreeText(ActionEvent event) {
    final EDocument edoc = EDocument.valueOf(this.getIndexableType());

    if (edoc == null) {
      warn("xtdtsklbls.error_freetext_not_supported", this.getIndexableType());
      this.freeText = null;
      return;
    }

    if (getFreeTextIndexes(this.descriptor).anyMatch(i -> i == this.freeText.getIndex())) {
      error("xtdtsklbls.error_duplicate_freetext_number");
      return;
    }

    final PropertyDescriptor p = createFreeTextProperty(this.freeText.getIndex(), this.freeText.getLabel(), edoc.getHeaderPath());
    p.setType(this.freeText.getType());
    this.descriptor.addProperty(p);
    this.freeText = null;
  }


  /**
   * @see AbstractIndexableTask#createDescriptor()
   */
  @Override
  protected BeanDescriptor createDescriptor() {
    try {
      BeanDescriptor bd = beanService.fromClasspath("descriptors/invoice-columns.xml");

      if (this.dataModelDescriptor != null) {
        for (PropertyDescriptor pd : bd.getProperties()) {
          this.dataModelDescriptor.getProperties()
              .stream()
              .filter(p -> pd.getName()
                  .equals("indexable." + p.getName()) ||
                  pd.getName()
                      .equals(p.getName()))
              .findFirst()
              .ifPresent(property -> pd.setLabel(property.getLabel()));
        }
      }

      return bd;
    }
    catch (IOException ex) {
      TaskHelper.error(this, ex, "{0}", getRootCauseMessage(ex));
      return null;
    }
  }

  /* -- SECURITY -- */

  /**
   * @see AbstractXcblDocumentTask#getOrganizationByCode(String, String)
   */
  @Override
  public Group getOrganizationByCode(String code, String parentCode) {
    return securityService.getOrganizationByCode(code, parentCode);
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  @Override
  public void info(Object task, String messageKey) {
    TaskHelper.info(task, messageKey);
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  public void addMessage(Throwable t, Severity severity, String messageId, String forClientId, Object... args) {
    MessageHelper.addMessage(t, severity, messageId, forClientId, args);
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  public void error(String msgkey) {
    MessageHelper.error(msgkey);
  }

  public String getMessage(final String messageKey, final String defaultMessage, final Locale locale, final Object... args) {
    return MessageHelper.getMessage(messageKey, defaultMessage, locale, args);
  }

  public void onCompleteQuery(AjaxBehaviorEvent event) {
  }

  public boolean unclickableStage(InvoiceIndex invoice) {
    return invoice == null ? true : UNCLICKABLE_STAGES.contains(invoice.getStage());
  }

  /* -- UTILS -- */

  // - GETTERS AND SETTERS -

  /**
   * @see AbstractTask#getPathDescriptor()
   */
  @Override
  public String getPathDescriptor() {
    return pathDescriptor;
  }

  /**
   * @see AbstractTask#setPathDescriptor(String)
   */
  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  // -- OPTIMIZATION --

  /** @see AbstractXcblDocumentTask#isCountEnabled() */
  @Override
  public boolean isCountEnabled() {
    return this.countEnabled;
  }

  public void setCountEnabled(boolean countEnabled) {
    this.countEnabled = countEnabled;
  }

  public boolean isManagePDFCache() {
    return managePDFCache;
  }

  public void setManagePDFCache(boolean managePDFCache) {
    this.managePDFCache = managePDFCache;
  }

  public boolean isShowOnlyWorkflow() {
    return showOnlyWorkflow;
  }

  public void setShowOnlyWorkflow(boolean showOnlyWorkflow) {
    this.showOnlyWorkflow = showOnlyWorkflow;
  }

  /**
   * The selection will be changed by listeners: onSelectCheckbox onUnselectCheckbox onToggleSelect
   */
  @Override
  public void setMultiSelected(Indexable[] selected) {
  }

  public List<Object> getActions(Instance instance) {
    if (actions == null) {
      actions = this.getAllActions(instance)
          .stream()
          .filter(action -> !(action instanceof StandardInvoiceEditionTask && !((StandardInvoiceEditionTask) action).isEditInvoice()) &&
              !(action instanceof InvoiceComplianceTask) && !(action instanceof InvoiceControlTask) && !(action instanceof WorkflowMonitoringTask))
          .collect(Collectors.toList());
    }
    return actions;
  }

  public List<Object> getActionsCreation(Instance instance) {
    if (actionsCreation == null) {
      actionsCreation = this.getAllActions(instance)
          .stream()
          .filter(action -> !(action instanceof StandardInvoiceEditionTask && ((StandardInvoiceEditionTask) action).isEditInvoice()) &&
              !(action instanceof InvoiceComplianceTask) && !(action instanceof InvoiceControlTask) && !(action instanceof WorkflowMonitoringTask))
          .collect(Collectors.toList());
    }
    return actionsCreation;
  }

  public List<Object> getAllActions(Instance instance) {
    List<Class<? extends InvoiceEdition>> invoicingActions = this.getInvoiceActions();
    if (allActions == null) {
      allActions = InvoiceCategory.super.getAllActions(instance, taskService, invoicingActions);
    }
    return allActions;

  }

  public String getNextPortletPageCreation() {
    return nextPortletPageCreation;
  }

  public void setNextPortletPageCreation(String nextPortletPageCreation) {
    this.nextPortletPageCreation = nextPortletPageCreation;
  }

  public Long getActionCreateId() {
    return actionCreateId;
  }

  public void setActionCreateId(Long actionCreateId) {
    this.actionCreateId = actionCreateId;
    this.isInvoicingEnabled = null;
  }

  public List<DocumentStatusEntity> getStatusesForInvoiceModification() {
    // undo
    // if (statusesForInvoiceModification == null)
    // this.statusesForInvoiceModification = new ArrayList<DocumentStatusEntity>();
    return statusesForInvoiceModification;
  }

  public void setStatusesForInvoiceModification(List<DocumentStatusEntity> statusesForInvoiceModification) {
    this.statusesForInvoiceModification = statusesForInvoiceModification;
  }

  public List<DocumentStatusEntity> getStatusesForInvoiceOcrVerify() {
    if (statusesForInvoiceOcrVerify == null)
      statusesForInvoiceOcrVerify = new ArrayList<>();
    return statusesForInvoiceOcrVerify;
  }

  public void setStatusesForInvoiceOcrVerify(List<DocumentStatusEntity> statusesForInvoiceOcrVerify) {
    this.statusesForInvoiceOcrVerify = statusesForInvoiceOcrVerify;
  }

  public void setSecurityService(SecurityService securityService) {
    this.securityService = securityService;
  }

  @Override
  public String getFamilyLabel() {
    return LABEL_FAMILY;
  }

  @Override
  public String getLabelKey() {
    return "invoice_reference_type_INVOICE";
  }

  @Override
  public String getDefaultLabel() {
    return "i";
  }

  @Override
  public int getMaxTriggerActionNumber() {
    return this.TRIGGER_ACTION_MAX_INVOICES;
  }

  @Override
  protected Predicate<? super Indexable> getClassInstance() {
    return InvoiceIndex.class::isInstance;
  }

  @Override
  protected Function<? super InvoiceIndex, ? extends DocumentStatusEntityInterface> getStatus() {
    return InvoiceIndex::getStatus;
  }

  @Override
  protected Class<InvoiceIndex> getIndexableClass() {
    return InvoiceIndex.class;
  }

  @Override
  protected Function<? super Indexable, InvoiceIndex> castClass() {
    return InvoiceIndex.class::cast;
  }

  @Override
  protected Object getThisTask() {
    return InvoiceCockpitTask.this;
  }

  @Override
  protected void displayTriggerActionsMessage(TriggerActionWrapper triggerAction, boolean isGlobal, Indexable[] indexables) {
    TaskHelper.info(this, isGlobal ? "triggerActions_execute_info_global" : "triggerActions_execute_info",
        getTriggerActionName(of(triggerAction)), getInvoiceNumber(Stream.of(indexables)
            .findFirst()
            .orElse(null)));
  }

  @Override
  public Range<Integer> getDocumentExportRange() {
    return invoiceDocumentExportRange;
  }

  @Override
  public void setDocumentExportRange(Range<Integer> invoiceDocumentExportRange) {
    this.invoiceDocumentExportRange = invoiceDocumentExportRange;
  }

}
