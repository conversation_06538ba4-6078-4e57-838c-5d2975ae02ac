package com.byzaneo.generix.xcbl.portal.task;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.faces.event.CompleteEvent;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.bean.DocumentViewer;
import com.byzaneo.generix.edocument.exception.TemplateMissingException;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.ui.converter.BigDecimalConverterV2;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.generix.service.TransformService.BirtOutoutFileType;
import com.byzaneo.generix.xcbl.portal.task.util.*;
import com.byzaneo.generix.xtrade.task.AbstractIndexableTask;
import com.byzaneo.generix.xtrade.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document.AcquisitionType;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction;
import com.byzaneo.xtrade.service.DocumentTimelineService;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;
import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.component.selectoneradio.SelectOneRadio;
import org.primefaces.event.SelectEvent;
import org.primefaces.json.JSONObject;
import org.springframework.beans.factory.annotation.*;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.validator.ValidatorException;
import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;
import java.io.*;
import java.math.*;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.*;
import java.util.stream.*;

import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.sendFile;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.util.DocumentConsultStatusHelper.isOkForConsultStatusUpdate;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceNumber;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.COMMERCIAL_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.CREDIT_NOTE_GOODS_AND_SERVICES;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;

/**
 * Allows the display of early payments.
 */
@Task(name = "Payment", library = "portal/xcbl", version = "1.0.0")
@TaskViewModel(labelFamily = PaymentTask.LABEL_FAMILY_INVOICE)
public class PaymentTask extends ActionModuleClass<InvoiceIndex> {

  private static final long serialVersionUID = -7905710835609413323L;

  @Autowired
  @Qualifier(DocumentViewerService.SERVICE_NAME)
  private transient DocumentViewerService documentViewerService;

  @Autowired
  @Qualifier(DocumentTimelineService.SERVICE_NAME)
  private transient DocumentTimelineService documentTimelineService;

  @Autowired
  @Qualifier(InvoiceService.SERVICE_NAME)
  private transient InvoiceService invoiceService;

  public static final String LABEL_FAMILY_INVOICE = "gnxxcblinvlbls";

  public static final String LABEL_FAMILY = "gnxxcblpaymentlbls";

  private transient BeanDescriptor dataModelDescriptor;

  private boolean countEnabled = false;

  public BigDecimal defaultAnnualRate = BigDecimal.ZERO;
  private BigDecimalConverterV2 converter = new BigDecimalConverterV2();

  /* === ACTION MODULE Fields === */

  private final int TRIGGER_ACTION_MAX_PAYMENTS = 100;

  private int detailPrecision = 2;

  private int footerPrecision = 2;

  private transient PaymentData paymentData = new PaymentData(null);

  private transient int activeIndex;

  transient SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

  private transient List<DocumentTimeline> invoiceTimelines;

  private transient boolean showTimeline = false;

  private transient String daysLetter = getLabel("gnxxcblpaymentlbls", "payment_offering_an_early_payment_early_jpa_unit", "d", getLocale())
      .toLowerCase()
      .substring(0, 1);

  private static final int timelineCommentLimit = 100;

  private transient String floatColumnPattern;

  private transient boolean isBuyer = true;

  private transient String rejectComment;

  private static final List<String> NOT_SORTABLE_PROPERTIES = asList("daysPayedEarly", "earlyPaymentDiscountRate",
      "earlyPaymentAmountDiscount", "taxableValueInTaxAccountingCurrency");

  // used in order to know if the timeline comments should be displayed fully or truncate them to "timelineCommentLimit"
  private transient Map<Long, Boolean> timelineTruncatedComments;

  private transient List<PaymentData> globalSelection;

  private transient List<PaymentData> globalInnerSelection;

  private transient boolean acceptGlobalAllIndexesSelected = false;

  private transient String acceptGlobalIndexableId;

  @PostConstruct
  public void init() {
    this.executorService = getBean(ExecutorService.class, ExecutorService.SERVICE_NAME);
  }

  /**
   * @see AbstractIndexableTask#createDescriptor()
   */
  @Override
  protected BeanDescriptor createDescriptor() {
    try {
      BeanDescriptor bd = beanService.fromClasspath("descriptors/payment-columns.xml");

      if (this.dataModelDescriptor != null) {
        for (PropertyDescriptor pd : bd.getProperties()) {
          this.dataModelDescriptor.getProperties()
              .stream()
              .filter(p -> pd.getName()
                  .equals("indexable." + p.getName()) ||
                  pd.getName()
                      .equals(p.getName()))
              .findFirst()
              .ifPresent(property -> pd.setLabel(property.getLabel()));
        }
      }

      return bd;
    }
    catch (IOException ex) {
      TaskHelper.error(this, ex, "{0}", getRootCauseMessage(ex));
      return null;
    }
  }

  @Override
  protected Query resolveSearchQuery() {
    // base query
    final QueryBuilder qb = createBuilder(super.resolveSearchQuery());
    qb.and(Clauses.equal("_class", getIndexableType().getName()));
    qb.and(Clauses.isNotNull("paymentStatus"));
    qb.append(getQueryFromUserRoles(getCurrentUser()));
    return qb.query();
  }

  /* === Action Module === */

  @Override
  protected Predicate<? super Indexable> getClassInstance() {
    return InvoiceIndex.class::isInstance;
  }

  @Override
  protected Class<InvoiceIndex> getIndexableClass() {
    return InvoiceIndex.class;
  }

  @Override
  protected Function<? super InvoiceIndex, ? extends DocumentStatusEntityInterface> getStatus() {
    return InvoiceIndex::getStatus;
  }

  @Override
  protected Function<? super Indexable, InvoiceIndex> castClass() {
    return InvoiceIndex.class::cast;
  }

  @Override
  protected Object getThisTask() {
    return PaymentTask.this;
  }

  @Override
  public int getMaxTriggerActionNumber() {
    return this.TRIGGER_ACTION_MAX_PAYMENTS;
  }

  @Override
  protected void displayTriggerActionsMessage(TriggerActionWrapper triggerAction, boolean isGlobal, Indexable[] indexables) {
    TaskHelper.info(this, isGlobal ? "triggerActions_execute_info_global" : "triggerActions_execute_info",
        getTriggerActionName(of(triggerAction)), getInvoiceNumber(Stream.of(indexables)
            .findFirst()
            .orElse(null)));
  }

  // -- Import
  @Override
  public Logs importDocuments(List<InputStream> streams) {
    return streams.isEmpty() ? null : eDocumentService.importInvoices(company, partner, streams, detailPrecision, footerPrecision);
  }

  /* === Download === */

  public void onDownloadCsv(Instance instance) {
    onDownload(instance, this.documentService.getIndexable(this.viewer.getDocument()), BirtOutoutFileType.Excel);
  }

  private void onDownload(Instance instance, InvoiceIndex indexable, BirtOutoutFileType type) {
    try {
      this.download = view(instance, indexable, type)
          .flatMap(DocumentViewer::download)
          .orElse(null);
      getCurrentInstance()
          .addCallbackParam("download", this.download != null);

    }
    catch (TemplateMissingException tme) {
      TaskHelper.error(this, tme, LABEL_FAMILY_INVOICE + ".error_missing_template");
      log.error(tme.getMessage());
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error downloading document: %s", getRootCauseMessage(e));
    }
  }

  @Override
  public void onDownloadList(Instance instance) {
    if (dataModel != null && this.dataModel.getRowCount() == 0) {
      TaskHelper.warn(this, "gnxxcblorderlbls.error_export_no_records");
      return;
    }
    // CountgetAsynchronousProcessLimit
    int itemsCount = Long.valueOf(documentService.countIndexable(getIndexableType(), resolveSearchQuery()))
        .intValue();
    // Asynchronous limit for export list
    int asynchronousLimit = getAsynchronousListExportRange().getMax();
    // Process the export
    export(false, itemsCount, asynchronousLimit, getListExportFileName(), instance, FileType.EXCEL, BirtOutoutFileType.Excel);
    if (this.download != null) {
      getCurrentInstance().addCallbackParam("download", this.download != null && download.getFile() != null);
    }
    else {
      TaskHelper.error(this, null, "edctsklbls.unable_to_generate_file");
    }
  }

  public void onDownload() {
    try {
      sendFile(download.getFile(), download.getName(), true);
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error downloading file: %s", getRootCauseMessage(e));
    }
  }

  /* === View === */

  private Optional<DocumentViewer> view(Instance instance, InvoiceIndex indexable, BirtOutoutFileType type) {
    final Optional<DocumentViewer> view = this.documentViewerService.view(
        indexable, instance, this.partner,
        this.getCurrentUser()
            .getLocale(),
        this.viewerBaseName(), false, false, null, type);
    // document not found
    if (!view.isPresent()) {
      error("Document not found");
    }
    // performs straight validation to gets user's feed-backs...
    // to avoid unnecessary transformations, we could
    // change the document visualization workflow
    if (!view.map(DocumentViewer::isValid)
        .get()) {
      TaskHelper.error(this, null, LABEL_FAMILY_INVOICE + ".error_missing_template");
      return empty();
    }
    else {
      markDocumentsAsRead(indexable);
    }
    return view;
  }

  @Override
  public void onSelect(Instance instance, InvoiceIndex indexable, PropertyDescriptor property) {
    try {
      this.viewer = view(instance, indexable, BirtOutoutFileType.PDF).orElse(null);
      this.selected = null;
      this.allIndexesSelected = false;
      this.invoiceTimelines = this.getAllTimelinesForInvoice();
      if (this.invoiceTimelines.size() > 0) {
        this.showTimeline = true;
        this.timelineTruncatedComments = new HashMap<Long, Boolean>();
      }
    }
    catch (TemplateMissingException tme) {
      MessageHelper.error(tme, MessageHelper.getMessage(LABEL_FAMILY_INVOICE + ".error_missing_template",
          "There is no Birt template configured, please contact your administrator.", this.selectedLanguage));
      log.error(tme.getMessage());
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error viewing document: %s", getRootCauseMessage(e));
    }
  }

  @Override
  protected String viewerBaseName(InvoiceIndex indexable) {
    String baseName = invoiceService.resolveViewerBaseName(indexable, this.descriptor, PATTERN_DATE, LABEL_FAMILY_INVOICE)
        .orElse(super.viewerBaseName(indexable));

    while (StringUtils.endsWith(baseName, "-")) {
      baseName = StringUtils.removeEnd(baseName, "-");
    }
    return baseName;
  }

  private Function<Document, String> viewerBaseName() {
    return document -> of(document).filter(com.byzaneo.xtrade.bean.Document::isIndexed)
        .map(com.byzaneo.xtrade.bean.Document::<InvoiceIndex> getIndexValue)
        .map(this::viewerBaseName)
        .orElseGet(document::getReference);
  }

  private void markDocumentsAsRead(Indexable indexable) {
    documentService.markDocumentConsultStatusAsRead(indexable, isOkForConsultStatusUpdate(), currentSession().getUserId());
  }

  @Override
  public String getUserFriendlyMessageType() {
    return getLabel(LABEL_FAMILY_INVOICE, "invoice_reference_type_INVOICE", "invoice", getLocale()).toLowerCase();
  }

  /* === Multiselect === */

  @Override
  public void setMultiSelected(Indexable[] selected) {
  }

  /* === Timeline === */

  public List<DocumentTimeline> getAllTimelinesForInvoice() {
    if (viewer == null) {
      return emptyList();
    }
    return this.documentTimelineService.getAllTimelinesForDocument(viewer.getDocument()
        .getIndexValue()
        .getEntityId());
  }

  public boolean showTimeline() {
    return this.showTimeline && this.invoiceTimelines.size() > 0;
  }

  public void onShowHideTimeline() {
    this.showTimeline = !this.showTimeline;
  }

  public String getTimelineComment(Long timelineId, String comment) {
    if (comment.length() > timelineCommentLimit) {
      if (!this.timelineTruncatedComments.containsKey(timelineId)) {
        this.timelineTruncatedComments.put(timelineId, true);
      }
    }
    return this.timelineTruncatedComments.containsKey(timelineId) && this.timelineTruncatedComments.get(timelineId) == true
        ? comment.substring(0, timelineCommentLimit)
        : comment;
  }

  public void showFullTimelineComment(Long timelineId) {
    this.timelineTruncatedComments.put(timelineId, false);
  }

  /* === Payment === */

  public BigDecimal getEarlyPaymentValue(String propertyName) {
    InvoiceIndex invoiceIndex = (InvoiceIndex) this.getDataModel()
        .getRowData();
    if (InvoiceIndexer.EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE.equals(propertyName) &&
        invoiceIndex.getEarlyPaymentAnnualDiscountRate() == null) {
      return defaultAnnualRate.setScale(2, RoundingMode.HALF_DOWN);
    }
    // set earlyPaymentAnnualDiscountRate so as to be able to calculate the other values
    if (invoiceIndex.getEarlyPaymentAnnualDiscountRate() == null)
      invoiceIndex.setEarlyPaymentAnnualDiscountRate(defaultAnnualRate);
    return EarlyPaymentHelper.getEarlyPaymentValue(propertyName, invoiceIndex).setScale(2,RoundingMode.HALF_DOWN);
  }

  public BigDecimal getEarlyPaymentDiscountRate() {
    return EarlyPaymentHelper.getEarlyPaymentDiscountRate((InvoiceIndex) this.getDataModel()
        .getRowData());
  }

  public Integer getDaysPayedEarlyValue() {
    return EarlyPaymentHelper.getDaysPayedEarlyValue((InvoiceIndex) this.getDataModel()
        .getRowData());
  }

  /* === Overrides === */

  @Override
  public ColumnValue getColumnValue(InvoiceIndex bean, PropertyDescriptor property) {
    if (property.getType()
        .equals(BigDecimal.class)) {
      property.setPattern(getColumnFloatPattern());
    }
    return super.getColumnValue(bean, property);
  }

  private String getColumnFloatPattern() {
    if (floatColumnPattern == null) {
      floatColumnPattern = "0." + StringUtils.repeat("0", footerPrecision);
    }
    return floatColumnPattern;
  }

  @Override
  public boolean isSortable(PropertyDescriptor property) {
    return !NOT_SORTABLE_PROPERTIES.contains(property.getName());
  }

  @Override
  public QueryModel getSearchModel() {
    return super.getSearchModelAdvancedSearch();
  }

  @Override
  public Collection<String> onAutocompleteStart(final CompleteEvent event) {
    return eDocumentService.getFieldSuggestions(event.getFieldName(), event.getFieldValue(), getIndexableType(), this.descriptor,
        resolveBaseQuery());
  }

  @Override
  public void setBql(String bql) {
    setQuickSearchBql(bql);
  }

  public String getUsernameById(String userId) {
    return this.securityService.getUser(userId)
        .getFullname();
  }

  public void error(String msgkey) {
    MessageHelper.error(msgkey);
  }

  /**
   * Get status values
   *
   * @return map which contains values status
   */
  @Override
  public Map<Object, String> getValues(String label) {
    List<String> list;
    if (isOriginalStatusProperty(label)) {
      list = getAvailableStatus(getIndexableType());
      return getStatusesAsMapOfCodeObject(list);
    }
    else if (isOriginalStageProperty(label)) {
      return IndexableTaskHelper.toStageItems(sortStageLabel(asList(DocumentStage.values())), selectedLanguage);
    }
    else if (isOriginalConsultStatusProperty(label)) {
      list = sortConsultStatusLabel(asList(DocumentConsultStatus.values()));
    }
    else if (isOriginalPaymentStatusProperty(label)) {
      list = sortPaymentStatusLabel(asList(PaymentStatus.values()));
    }
    else if ("acquisition".equals(label)) {
      list = sortAcquisitionLabel(asList(AcquisitionType.values()));
    }
    else if (isArchiveStatusProperty(label)) {
      list = sortArchiveStatusLabel(asList(ArchiveStatus.values()));
    }
    else if ("invoiceTypeCoded".equals(label)) {
      List<InvoiceTypeCodeType> invoiceTypeCodesType = stream(InvoiceTypeCodeType.values())
          .filter(type -> type != COMMERCIAL_INVOICE && type != CREDIT_NOTE_GOODS_AND_SERVICES)
          .collect(toCollection(ArrayList::new));
      return Stream.of(asList(COMMERCIAL_INVOICE.name(), CREDIT_NOTE_GOODS_AND_SERVICES.name()),
          sortInvoiceTypeCodeTypeByLabel(invoiceTypeCodesType))
          .flatMap(List::stream)
          .collect(toMap(identity(), this::label, (o1, o2) -> o1, LinkedHashMap::new));
    }
    else if ("processingWay".equals(label)) {
      list = sortProcessingWay(asList(Document.ProcessingWay.values()));
    }
    else {
      return emptyMap();
    }
    return list.stream()
        .collect(toMap(o -> o,
            o -> getMessage(LABEL_CORE + "." + o, o, selectedLanguage)));
  }

  public String getMessage(final String messageKey, final String defaultMessage, final Locale locale, final Object... args) {
    return MessageHelper.getMessage(messageKey, defaultMessage, locale, args);
  }

  /**
   * methode used to compute data before showing the Early Payment Dialog by default it compute the Early Payment details using the current
   * date
   * 
   * @param indexable
   * @param tabIndex
   */
  public void onShowEarlyPaymentDialog(Indexable indexable, int tabIndex) {
    isBuyer = true;
    this.activeIndex = tabIndex;
    paymentData = new PaymentData(indexable);
    ((InvoiceIndex) indexable).setEarlyPaymentAnnualDiscountRate(defaultAnnualRate);
    fillPopinWithData(paymentData, LocalDate.now());
  }
  
  public boolean checkDueDateInPast(Indexable indexable) {
    return (((InvoiceIndex) indexable).getInvoiceDueDate().before(EarlyPaymentHelper.getCurrentDate0Hour()) ||
        ((InvoiceIndex) indexable).getInvoiceDueDate().equals(EarlyPaymentHelper.getCurrentDate0Hour()));
  }

  /**
   * Function to get the maximum and the minimum Dates to show in payment date calendar
   * @param paymentData
   * @param minDateCantPayBefore
   * @param invoiceDueDate
   */
  public void updatePaymentMaxAndMinDates(PaymentData paymentData, Date minDateCantPayBefore, Date invoiceDueDate) {
    Date currentDate = EarlyPaymentHelper.getCurrentDate0Hour();
    Period diff = Period.between(
        LocalDate.parse(format.format(currentDate))
            .withDayOfMonth(1),
        LocalDate.parse(format.format(invoiceDueDate))
            .withDayOfMonth(1));
    paymentData.setPaymentDateMinDate(minDateCantPayBefore);
    paymentData.setPaymentDateMaxDate(invoiceDueDate);
  }

  /**
   * init all data if used close the early payment popup
   */
  public void onCancel() {
    activeIndex = 0;
    paymentData = new PaymentData(null);
    globalSelection = null;
    globalInnerSelection = null;
    selected = null;
    rejectComment = "";
  }

  /**
   * used to call loadCalenderMetaData when Radio button value changed
   * 
   * @param event
   */
  public void onRadioChange(AjaxBehaviorEvent event) {
    SelectOneRadio paymentTypeRadio = (SelectOneRadio) event.getSource();

    EarlyPaymentHelper.PaymentType value = EarlyPaymentHelper.PaymentType.valueOf((String) paymentTypeRadio.getValue());
    loadCalenderMetaData(value);

  }

  /**
   * used to load metadata to show in the custom calendar
   * 
   * @param metadataType
   */
  public void loadCalenderMetaData(EarlyPaymentHelper.PaymentType metadataType) {
    if (globalSelection != null && !globalSelection.isEmpty()) {
      for (PaymentData paymentDataRow : globalSelection) {
        initMapTables(metadataType, paymentDataRow);
      }
    }
    else {
      initMapTables(metadataType, this.paymentData);
    }
  }

  private void initMapTables(EarlyPaymentHelper.PaymentType metadataType, PaymentData paymentDataRow) {
    if (metadataType.equals(EarlyPaymentHelper.PaymentType.AMOUNT)) {
      paymentDataRow.setItem(new JSONObject(paymentDataRow.getMapDatesAmount()));
    }
    else if (metadataType.equals(EarlyPaymentHelper.PaymentType.DISCOUNT)) {
      paymentDataRow.setItem(new JSONObject(paymentDataRow.getMapDatesRate()));
    }
    else /* dpe */ {
      paymentDataRow.setItem(new JSONObject(paymentDataRow.getMapDatesJpa()));
    }
  }

  /**
   * used to persist the Early payment details from dialog
   */
  public void saveEarlyPayment() throws Exception {
    if (paymentData == null)
    {
      log.error("Document could not be saved for indexable " + currentIndexable.getId());
      throw new ServiceException("Document could not be saved for indexabl " + currentIndexable.getId());
    }
    this.document = documentService.getDocument(paymentData.getIndexable());
    if (document == null) {
      log.error("Document couldn't be found for indexable " + currentIndexable.getId());
      throw new ServiceException("Document couldn't be found for indexable " + currentIndexable.getId());
    }
    ((InvoiceIndex) paymentData.getIndexable()).setPaymentStatus(PaymentStatus.EP_SUBMITTED);
    ((InvoiceIndex) paymentData.getIndexable()).setEarlyPaymentCannotPayBefore(paymentData.getJpa()
        .intValue());

    Invoice invoice = InvoiceTaskHelper.createInvoiceBasedOnXcbl(document);
    List<NameValuePairType> nameValuePairList = InvoiceIndexer.getEarlyPaymentNameValuePair(invoice)
        .getNameValuePair();

    {
      boolean statusExist = false;
      boolean jpaExists = false;
      boolean annualDiscountRateExists = false;
      for (int i = 0; i < nameValuePairList.size(); i++) {
        if (InvoiceIndexer.PAYMENT_STATUS.equals(nameValuePairList.get(i)
            .getName())) {
          statusExist = true;
          nameValuePairList.get(i)
              .setValue(PaymentStatus.EP_SUBMITTED.name());
        }
        if (InvoiceIndexer.EARLY_PAYMENT_CANNOT_PAY_BEFORE.equals(nameValuePairList.get(i)
            .getName())) {
          jpaExists = true;
          nameValuePairList.get(i)
              .setValue(paymentData.getJpa()
                  .toString());
        }
        if (InvoiceIndexer.EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE.equals(nameValuePairList.get(i)
            .getName())) {
          annualDiscountRateExists = true;
          nameValuePairList.get(i)
              .setValue(((InvoiceIndex) paymentData.getIndexable()).getEarlyPaymentAnnualDiscountRate().toString());
        }
      }
      if (!statusExist) {
        NameValuePairType paymentStatus = new NameValuePairType();
        paymentStatus.setName(InvoiceIndexer.PAYMENT_STATUS);
        paymentStatus.setValue(PaymentStatus.EP_SUBMITTED.name());
        nameValuePairList.add(paymentStatus);
      }
      if (!jpaExists) {
        NameValuePairType jpaTag = new NameValuePairType();
        jpaTag.setName(InvoiceIndexer.EARLY_PAYMENT_CANNOT_PAY_BEFORE);
        jpaTag.setValue(paymentData.getJpa()
            .toString());
        nameValuePairList.add(jpaTag);
      }
      if (!annualDiscountRateExists) {
        NameValuePairType annualDiscountRatePair = new NameValuePairType();
        annualDiscountRatePair.setName(InvoiceIndexer.EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE);
        annualDiscountRatePair.setValue(((InvoiceIndex) paymentData.getIndexable()).getEarlyPaymentAnnualDiscountRate().toString());
        nameValuePairList.add(annualDiscountRatePair);
      }
    }
    DocumentTimeline docTimeline = new DocumentTimeline(document, new Date(), TimelineAction.EP_SUBMITED, getCurrentUser().getId());
    docTimeline.setComment(((InvoiceIndex) paymentData.getIndexable()).getEarlyPaymentCannotPayBefore()
        .toString());
    docTimelineService.save(docTimeline);
    InvoiceXcblHelper.updateInvoiceDocument(document, invoice);
    documentService.saveIndexable(paymentData.getIndexable());
  }

  public void validateCheckbox(FacesContext context, UIComponent component, Object value) {
    if (value instanceof Boolean && ((Boolean) value).equals(Boolean.FALSE)) {
      FacesMessage message = new FacesMessage("You need to select the mandatory field");
      context.addMessage("LoginForm:staff", message);
      throw new ValidatorException(message);
    }
  }

  /**
   * methode executed when user select a date from the calender
   * 
   * @param event
   */
  public void onDateSelect(SelectEvent event) {
    acceptGlobalIndexableId=(String) FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("tskForm-paymentView-selectedIndexableId");

    PaymentData currentPaymentData = getSelectedPaymentData(acceptGlobalIndexableId);
    if (currentPaymentData == null)
      currentPaymentData = paymentData;

    if (event.getObject() != null && currentPaymentData != null) {
      Date selectedPaymentDate = (Date) event.getObject();
      LocalDate selectedPaymentDateToLocalDate = EarlyPaymentHelper.convertToLocalDate(selectedPaymentDate);
      Date invoiceDueDate = ((InvoiceIndex) currentPaymentData.getIndexable()).getInvoiceDueDate();
      LocalDate invoiceDueDateLocalDate = LocalDate.parse(format.format(invoiceDueDate));
      BigDecimal annualDiscountRate = ((InvoiceIndex) currentPaymentData.getIndexable()).getEarlyPaymentAnnualDiscountRate();
      long jpa = ChronoUnit.DAYS.between(selectedPaymentDateToLocalDate, invoiceDueDateLocalDate);
      currentPaymentData.setJpa(jpa);
      currentPaymentData.setEscomptePercentageExact(EarlyPaymentHelper.computeEscompte(annualDiscountRate, jpa));
      currentPaymentData.setEscomptePercentage(currentPaymentData.getEscomptePercentageExact().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN));
      currentPaymentData.setEscompteExact(currentPaymentData.getEscomptePercentageExact()
          .multiply(((InvoiceIndex) currentPaymentData.getIndexable()).getTaxableValue()));
      currentPaymentData.setEscompte(new BigDecimal(String.valueOf(currentPaymentData.getEscomptePercentageExact()
          .multiply(((InvoiceIndex) currentPaymentData.getIndexable()).getTaxableValue()))).setScale(2, RoundingMode.HALF_DOWN));
    }
    paymentData = currentPaymentData;
  }

  private PaymentData getSelectedPaymentData(String indexableId) {
    if (globalSelection == null || indexableId == null)
      return null;
    return globalSelection.stream()
        .filter(data -> data.getIndexable() != null && data.getIndexable()
            .getId()
            .equals(indexableId))
        .findFirst()
        .orElse(null);
  }

  /**
   * methode used to compute data before opening the seller dialog to accept or reject the early payment
   * 
   * @param indexable
   * @param tabIndex
   */
  public void onShowEarlyPaymentSellerDialog(Indexable indexable, int tabIndex) {
    this.activeIndex = tabIndex;
    paymentData = new PaymentData(indexable);
    LocalDate currentDate = LocalDate.now();
    calculatePaymentData(paymentData, currentDate);
    isBuyer = false;
    rejectComment = "";
  }

  private void calculatePaymentData(PaymentData paymentData, LocalDate currentDate) {
    Date invoiceDueDate = ((InvoiceIndex) paymentData.getIndexable()).getInvoiceDueDate();
    LocalDate invoiceDueDateLocalDate = LocalDate.parse(format.format(invoiceDueDate));

    Integer earlyPaymentCannotPayBefore = ((InvoiceIndex) paymentData.getIndexable()).getEarlyPaymentCannotPayBefore();
    LocalDate earlyPaymentCannotPayBeforeDate = invoiceDueDateLocalDate
        .minusDays(earlyPaymentCannotPayBefore != null ? earlyPaymentCannotPayBefore : 0);
    // if current date is > to earlyPaymentCannotPayBeforeDate (payment Date proposition) then put current as min date
    if (earlyPaymentCannotPayBeforeDate.isBefore(currentDate)) {
      earlyPaymentCannotPayBeforeDate = currentDate;
    }

    fillPopinWithData(paymentData, earlyPaymentCannotPayBeforeDate);
  }

  public void fillPopinWithData(PaymentData currentPaymentData, LocalDate earlyPaymentCannotPayBeforeDate) {
    InvoiceIndex invoiceIndex = (InvoiceIndex) currentPaymentData.getIndexable();
    Date invoiceDueDate = invoiceIndex.getInvoiceDueDate();
    LocalDate invoiceDueDateLocalDate = LocalDate.parse(format.format(invoiceDueDate));
    long jpa = ChronoUnit.DAYS.between(earlyPaymentCannotPayBeforeDate, invoiceDueDateLocalDate);
    BigDecimal annualDiscountRate = invoiceIndex.getEarlyPaymentAnnualDiscountRate();
    currentPaymentData.setJpa(jpa);
    currentPaymentData.setEscomptePercentageExact(EarlyPaymentHelper.computeEscompte(annualDiscountRate, jpa));
    currentPaymentData.setEscomptePercentage(EarlyPaymentHelper.computeEscompte(annualDiscountRate, jpa).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN));
    currentPaymentData.setEscompteExact(
        new BigDecimal(currentPaymentData.getEscomptePercentageExact().doubleValue() * invoiceIndex.getTaxableValue().doubleValue()));
    currentPaymentData.setEscompte(
        new BigDecimal(currentPaymentData.getEscomptePercentageExact().doubleValue() * invoiceIndex.getTaxableValue().doubleValue()).setScale(2, RoundingMode.HALF_DOWN));
    Date earlyPaymentCannotPayBeforeDateTodate = Date.from(
        earlyPaymentCannotPayBeforeDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    currentPaymentData.setSelectedPaymentDate(earlyPaymentCannotPayBeforeDateTodate);
    updatePaymentMaxAndMinDates(currentPaymentData, earlyPaymentCannotPayBeforeDateTodate, invoiceIndex.getInvoiceDueDate());
    for (LocalDate date = EarlyPaymentHelper.convertToLocalDate(currentPaymentData.getPaymentDateMinDate());
         date.isBefore(EarlyPaymentHelper.convertToLocalDate(currentPaymentData.getPaymentDateMaxDate())) ||
             date.isEqual(EarlyPaymentHelper.convertToLocalDate(currentPaymentData.getPaymentDateMaxDate()));
         date = date.plusDays(1)) {
      long jpaTemp = ChronoUnit.DAYS.between(date, invoiceDueDateLocalDate);
      BigDecimal escomptePercentageTemp = EarlyPaymentHelper.computeEscompte(annualDiscountRate, jpaTemp);
      BigDecimal escompteTemp = new BigDecimal(
          escomptePercentageTemp.doubleValue() * invoiceIndex.getTaxableValue().doubleValue()).setScale(2, RoundingMode.HALF_DOWN);
      escomptePercentageTemp = EarlyPaymentHelper.computeEscompte(annualDiscountRate, jpaTemp);
      String currentCurrency = CurrencyCodeAndSign.getSignByCode(invoiceIndex.getInvoiceCurrencyCoded());
      currentPaymentData.getMapDatesAmount().put(date.getDayOfMonth() + "-" + date.getMonth()
              .ordinal() + "-" + date.getYear(),
              converter.getValue(escompteTemp.toString(), 2, 1, currentCurrency));
      currentPaymentData.getMapDatesRate().put(date.getDayOfMonth() + "-" + date.getMonth()
              .ordinal() + "-" + date.getYear(),
          escomptePercentageTemp.multiply(BigDecimal.valueOf(100)).setScale(2,RoundingMode.HALF_DOWN) + "%");
      currentPaymentData.getMapDatesJpa().put(date.getDayOfMonth() + "-" + date.getMonth()
              .ordinal() + "-" + date.getYear(), jpaTemp + daysLetter);
    }
    loadCalenderMetaData(EarlyPaymentHelper.PaymentType.AMOUNT);

  }
  
  public void refreshPopinData(AjaxBehaviorEvent event) {
    fillPopinWithData(paymentData, LocalDate.now());
  }

  /**
   * used to persist the Early payment details from dialog in case of Accept proposition
   */
  public void onAcceptProposition() throws Exception {
    setGlobalInnerSelection(CollectionUtils.isNotEmpty(getGlobalInnerSelection()) ?
        getGlobalInnerSelection() :
        Arrays.asList(paymentData));
    boolean showError = false;
    for (PaymentData paymentData : getGlobalInnerSelection()) {
      try {
        acceptProposition(paymentData);
      }
      catch (Exception e) {
        log.error("An error occured while accepting invoice" + ((InvoiceIndex) paymentData.getIndexable()).getInvoiceNumber(), e);
        showError = true;
      }
    }
    if (showError) {
      message(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".error_general_accept");
    }
    globalSelection = null;
    globalInnerSelection = null;

  }

  private void acceptProposition(PaymentData paymentData)
      throws FileNotFoundException, IOException, JAXBException, XMLStreamException, Exception {
    this.paymentData = paymentData;
    InvoiceIndex currentIndexable = (InvoiceIndex) paymentData.getIndexable();
    this.document = documentService.getDocument(currentIndexable);
    currentIndexable.setPaymentStatus(PaymentStatus.EP_ACCEPTED);
    int jpa = paymentData.getJpa()
        .intValue();
    currentIndexable.setEarlyPaymentCannotPayBefore(jpa);
    currentIndexable.setDaysPayedEarly(jpa);
    currentIndexable.setEarlyPaymentDiscountRate(paymentData.getEscomptePercentage());
    currentIndexable.setEarlyPaymentAmountDiscount(paymentData.getEscompte());

    Invoice invoice = InvoiceTaskHelper.createInvoiceBasedOnXcbl(document);
    List<NameValuePairType> nameValuePairList = InvoiceIndexer.getEarlyPaymentNameValuePair(invoice)
        .getNameValuePair();

    setNameValuePairTypeWhenAccept(nameValuePairList);
    DocumentTimeline docTimeline = new DocumentTimeline(document, new Date(), TimelineAction.EP_ACCEPTED, getCurrentUser().getId());
    docTimeline.setComment(jpa + "-" + paymentData.getEscomptePercentage()
        .toPlainString() + "-" +
        paymentData.getEscompte()
            .toPlainString());
    docTimelineService.save(docTimeline);
    InvoiceXcblHelper.updateInvoiceDocument(document, invoice);
    documentService.saveIndexable(currentIndexable);
    this.paymentData = new PaymentData(null);
  }

  private void setNameValuePairTypeWhenAccept(List<NameValuePairType> nameValuePairList) {
    boolean statusExist = false;
    boolean jpaExists = false;
    boolean discoutRateExists = false;
    boolean amountDiscountExists = false;

    Long jpa = paymentData.getJpa();
    BigDecimal escomptePercentage = paymentData.getEscomptePercentage();
    BigDecimal escompte = paymentData.getEscompte();
    for (int i = 0; i < nameValuePairList.size(); i++) {
      if (InvoiceIndexer.PAYMENT_STATUS.equals(nameValuePairList.get(i)
          .getName())) {
        statusExist = true;
        nameValuePairList.get(i)
            .setValue(PaymentStatus.EP_ACCEPTED.name());
      }
      if (InvoiceIndexer.EARLY_PAYMENT_CANNOT_PAY_BEFORE.equals(nameValuePairList.get(i)
          .getName())) {
        jpaExists = true;
        nameValuePairList.get(i)
            .setValue(jpa.toString());
      }
      if (InvoiceIndexer.EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE.equals(nameValuePairList.get(i)
          .getName())) {
        discoutRateExists = true;
        nameValuePairList.get(i)
            .setValue(escomptePercentage.toPlainString());
      }
      if (InvoiceIndexer.EARLY_PAYMENT_AMOUNT_DISCOUNT.equals(nameValuePairList.get(i)
          .getName())) {
        amountDiscountExists = true;
        nameValuePairList.get(i)
            .setValue(escompte.toPlainString());
      }
    }
    if (!statusExist) {
      NameValuePairType paymentStatus = new NameValuePairType();
      paymentStatus.setName(InvoiceIndexer.PAYMENT_STATUS);
      paymentStatus.setValue(PaymentStatus.EP_ACCEPTED.name());
      nameValuePairList.add(paymentStatus);
    }
    if (!jpaExists) {
      NameValuePairType jpaTag = new NameValuePairType();
      jpaTag.setName(InvoiceIndexer.EARLY_PAYMENT_CANNOT_PAY_BEFORE);
      jpaTag.setValue(jpa.toString());
      nameValuePairList.add(jpaTag);
    }
    if (!discoutRateExists) {
      NameValuePairType escomptePercentageTag = new NameValuePairType();
      escomptePercentageTag.setName(InvoiceIndexer.EARLY_PAYMENT_ANNUAL_DISCOUNT_RATE);
      escomptePercentageTag.setValue(escomptePercentage.toPlainString());
      nameValuePairList.add(escomptePercentageTag);
    }
    if (!amountDiscountExists) {
      NameValuePairType escompteTag = new NameValuePairType();
      escompteTag.setName(InvoiceIndexer.EARLY_PAYMENT_AMOUNT_DISCOUNT);
      escompteTag.setValue(escompte.toPlainString());
      nameValuePairList.add(escompteTag);
    }
  }

  private void rejctPayment(Indexable currentIndexable, String comment, User currentUser)
      throws FileNotFoundException, IOException, JAXBException, XMLStreamException, Exception {
    Document currentDocument = documentService.getDocument(currentIndexable);
    if (currentDocument == null) {
      log.error("Document couldn't be found for indexable " + currentIndexable.getId());
      throw new ServiceException("Document couldn't be found for indexable " + currentIndexable.getId());
    }
    ((InvoiceIndex) currentIndexable).setPaymentStatus(PaymentStatus.EP_REFUSED);
    Invoice invoice = InvoiceTaskHelper.createInvoiceBasedOnXcbl(currentDocument);
    ListOfNameValuePairType earlyPaymentNameValuePair = InvoiceIndexer.getEarlyPaymentNameValuePair(invoice);
    List<NameValuePairType> nameValuePairList = earlyPaymentNameValuePair
        .getNameValuePair();
    setNameValuePairTypeWhenReject(nameValuePairList);
    DocumentTimeline docTimeline = new DocumentTimeline(currentDocument, new Date(), TimelineAction.EP_REFUSED, currentUser.getId());
    docTimeline.setComment(comment);
    docTimelineService.save(docTimeline);
    InvoiceXcblHelper.updateInvoiceDocument(currentDocument, invoice);
    documentService.saveIndexable(currentIndexable);
  }

  public void onGlobalPaymentAction() {
    if (!isItemSelected() && !this.allIndexesSelected) {
      message(FacesMessage.SEVERITY_WARN, LABEL_FAMILY + ".error_no_records");
      getCurrentInstance().addCallbackParam("canPerformAction", false);
      return;
    }
    LocalDate now = LocalDate.now();
    setGlobalSelection(stream(selected).filter(InvoiceIndex.class::isInstance)
        .map(InvoiceIndex.class::cast)
        .filter(invoice -> invoice.getPaymentStatus() == PaymentStatus.EP_SUBMITTED && !checkDueDateInPast(invoice))
        .map(invoice -> new PaymentData(invoice))
        .collect(Collectors.toList()));

    if (getGlobalSelection().size() == 0) {
      // message(FacesMessage.SEVERITY_WARN, LABEL_FAMILY + ".error_no_records_eligible");
      TaskHelper.warn(this, LABEL_FAMILY + ".error_no_records_eligible");
      getCurrentInstance().addCallbackParam("canPerformAction", false);
      return;
    }
    else {
      for (PaymentData data : globalSelection) {
        calculatePaymentData(data, now);
      }
    }
    if (selected.length > getGlobalSelection().size()) {
      getCurrentInstance().addCallbackParam("showIgnoredInfo", true);
      // message(FacesMessage.SEVERITY_WARN, LABEL_FAMILY + ".global_submitted_info");
      TaskHelper.warn(this, LABEL_FAMILY + ".global_submitted_info");
    }
    getCurrentInstance().addCallbackParam("canPerformAction", true);
    selected = null;
    isBuyer = true;
    rejectComment = "";

  }

  public void onConfirmRejectPayment() {
    setGlobalSelection(getGlobalSelection() != null ? getGlobalSelection() : Arrays.asList(paymentData));
    boolean showError = false;
    for (PaymentData paymentData : getGlobalSelection()) {
      try {
        if (rejectComment == null)
          rejectComment = "";

        rejctPayment(paymentData.getIndexable(), rejectComment, getCurrentUser());
      }
      catch (Exception e) {
        log.error("An error occured while rejecting invoice" + ((InvoiceIndex) paymentData.getIndexable()).getInvoiceNumber(), e);
        showError = true;
      }
    }
    if (showError) {
      message(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".error_general_reject");
    }
    globalSelection = null;
    rejectComment = "";
  }

  private void setNameValuePairTypeWhenReject(List<NameValuePairType> nameValuePairList) {
    boolean statusExist = false;
    for (int i = 0; i < nameValuePairList.size(); i++) {
      if (InvoiceIndexer.PAYMENT_STATUS.equals(nameValuePairList.get(i)
          .getName())) {
        statusExist = true;
        nameValuePairList.get(i)
            .setValue(PaymentStatus.EP_REFUSED.name());
      }
    }
    if (!statusExist) {
      NameValuePairType paymentStatus = new NameValuePairType();
      paymentStatus.setName(InvoiceIndexer.PAYMENT_STATUS);
      paymentStatus.setValue(PaymentStatus.EP_REFUSED.name());
      nameValuePairList.add(paymentStatus);
    }
  }

  public boolean hasAttachFiles(InvoiceIndex indexable) {
    return indexable.getAttachFileCount() != 0;
  }

  /* === Getters and Setters === */

  @Override
  public String getPathDescriptor() {
    return pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  @Override
  public boolean isCountEnabled() {
    return this.countEnabled;
  }

  public void setCountEnabled(boolean countEnabled) {
    this.countEnabled = countEnabled;
  }

  public BigDecimal getDefaultAnnualRate() {
    return defaultAnnualRate;
  }

  public void setDefaultAnnualRate(BigDecimal defaultAnnualRate) {
    this.defaultAnnualRate = defaultAnnualRate.setScale(2, RoundingMode.HALF_DOWN);
  }

  // -- timeline

  public List<DocumentTimeline> getInvoiceTimelines() {
    return invoiceTimelines;
  }

  public void setInvoiceTimelines(List<DocumentTimeline> invoiceTimelines) {
    this.invoiceTimelines = invoiceTimelines;
  }

  public boolean isShowTimeline() {
    return showTimeline;
  }

  public void setShowTimeline(boolean showTimeline) {
    this.showTimeline = showTimeline;
  }

  public Map<Long, Boolean> getTimelineTruncatedComments() {
    return timelineTruncatedComments;
  }

  public void setTimelineTruncatedComments(Map<Long, Boolean> timelineTruncatedComments) {
    this.timelineTruncatedComments = timelineTruncatedComments;
  }

  public int getActiveIndex() {
    return activeIndex;
  }

  public void setActiveIndex(int activeIndex) {
    this.activeIndex = activeIndex;
  }

  public SimpleDateFormat getFormat() {
    return format;
  }

  public void setFormat(SimpleDateFormat format) {
    this.format = format;
  }

  public String getDaysLetter() {
    return daysLetter;
  }

  public void setDaysLetter(String daysLetter) {
    this.daysLetter = daysLetter;
  }

  public boolean isBuyer() {
    return isBuyer;
  }

  public void setBuyer(boolean buyer) {
    isBuyer = buyer;
  }

  public String getRejectComment() {
    return rejectComment;
  }

  public void setRejectComment(String rejectComment) {
    this.rejectComment = rejectComment;
  }

  public List<PaymentData> getGlobalSelection() {
    return globalSelection;
  }

  public void setGlobalSelection(List<PaymentData> globalSelection) {
    this.globalSelection = globalSelection;
  }

  public PaymentData getPaymentData() {
    return paymentData;
  }

  public void setPaymentData(PaymentData paymentData) {
    this.paymentData = paymentData;
  }

  public List<PaymentData> getGlobalInnerSelection() {
    return globalInnerSelection;
  }

  public void setGlobalInnerSelection(List<PaymentData> globalInnerSelection) {
    this.globalInnerSelection = globalInnerSelection;
  }

  public boolean isAcceptGlobalAllIndexesSelected() {
    return acceptGlobalAllIndexesSelected;
  }

  public void setAcceptGlobalAllIndexesSelected(boolean acceptGlobalAllIndexesSelected) {
    this.acceptGlobalAllIndexesSelected = acceptGlobalAllIndexesSelected;
  }

  public String getAcceptGlobalIndexableId() {
    return acceptGlobalIndexableId;
  }

  public void setAcceptGlobalIndexableId(String acceptGlobalIndexableId) {
    this.acceptGlobalIndexableId = acceptGlobalIndexableId;
  }

}
