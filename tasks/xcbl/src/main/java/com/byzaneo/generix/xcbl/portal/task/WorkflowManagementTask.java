package com.byzaneo.generix.xcbl.portal.task;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.faces.event.*;
import com.byzaneo.faces.model.query.*;
import com.byzaneo.generix.api.TaskTransferListener;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.edocument.service.InvoiceServiceImpl.AbsenceType;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.SecurityService.Resource;
import com.byzaneo.generix.service.TransformService.Transform;
import com.byzaneo.generix.ui.ApplicationHandler.EDateFormat;
import com.byzaneo.generix.ui.SessionHandler;
import com.byzaneo.generix.xcbl.portal.models.*;
import com.byzaneo.generix.xcbl.portal.task.bean.*;
import com.byzaneo.generix.xcbl.portal.task.converter.WorkflowFunctionConverter;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.clause.*;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.api.TaskType;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.WorkflowStepUser.ExchangeType;
import com.byzaneo.xtrade.dao.*;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.util.WorkflowHelper;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.primefaces.component.api.UIColumn;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.selectonemenu.SelectOneMenu;
import org.primefaces.component.tabview.TabView;
import org.primefaces.context.RequestContext;
import org.primefaces.event.*;
import org.primefaces.model.LazyDataModel;
import org.quartz.SchedulerException;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;

import javax.annotation.*;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.event.*;
import javax.faces.model.SelectItem;
import javax.persistence.PreRemove;
import java.io.*;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.stream.*;

import static com.byzaneo.commons.bean.FileType.HTML;
import static com.byzaneo.commons.ui.FileResourceHandler.createResourceRequestPath;
import static com.byzaneo.commons.ui.util.EmptyPersistentDataModel.emptyDataModel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.ui.util.MessageHelper.addMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.info;
import static com.byzaneo.commons.util.FileHelper.deleteFile;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.faces.model.query.QueryModelBuilder.createQueryModel;
import static com.byzaneo.generix.service.TransformService.GLOBAL_TEMPLATES_OWNER;
import static com.byzaneo.generix.util.FormPageHelper.getAllFormPageTemplates;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.in;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.util.QueryHelper.toBql;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.security.spring.UserDetailsService.getAuthenticatedUser;
import static com.byzaneo.task.util.TaskHelper.error;
import static com.byzaneo.xtrade.api.Indexable.ENTITY_ID;
import static com.byzaneo.xtrade.ipm.ui.IpmAppHandler.resolveBeanDescriptor;
import static com.byzaneo.xtrade.ipm.ui.QueryHandler.completeDocumentQuery;
import static com.byzaneo.xtrade.util.WorkflowHelper.getWorkflowStepActionsAsMap;
import static com.byzaneo.xtrade.util.WorkflowHelper.getWorkflowStepUsersAsMap;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;

@Task(name = "WorkflowManagement", library = "portal/workflow", version = "1.0.0")
@TaskViewModel(labelFamily = WorkflowManagementTask.LABEL_FAMILY)
public class WorkflowManagementTask extends AbstractTask implements TaskTransferListener {

  private static final long serialVersionUID = 1L;

  private final static Logger LOGGER = LoggerFactory.getLogger(WorkflowManagementTask.class);

  public static final String LABEL_FAMILY = "gnxworkflowlbls";

  public enum ActorsSelectionType {
    BY_TYPE,
    BY_FUNCTION,
    BY_NAME
  }

  public enum WorkflowProcess {
    READ,
    NONE,
    WORKFLOW_CREATION,
    STEPS_CREATION,
    EDITION,
    DISABLE
  }

  private enum AbsenceEditMode {
    NONE,
    CREATION,
    EDIT
  }

  @Autowired
  @Qualifier(WorkflowDAO.DAO_NAME)
  private transient WorkflowDAO workflowDAO;

  @Autowired
  @Qualifier(WorkflowDocumentStatusDAO.DAO_NAME)
  private transient WorkflowDocumentStatusDAO workflowDocumentStatusDAO;

  private transient Workflow workflowInCreation;

  private transient WorkflowStep workflowStepInCreation;

  private transient int workflowInCreationStepNumber;

  private transient boolean workflowStepsConfiguration = false;

  private transient QueryModel queryModel;

  private transient boolean isBQL = false;

  private transient BeanService beanService;

  private transient Integer[] values = { 0, 1, 2, 3, 4, 5 };

  private transient WorkflowProcess workflowProcess = WorkflowProcess.NONE;

  private transient List<String> selectedGroup;

  protected transient SecurityService securityService;

  private static final String USER_WORKFLOW = "workflowSteps.workflowStepUsers.id.user";

  private static final String USER_OFF = "idUserOff";

  private static final String USER_BACKUP = "idUserBackup";

  private static final String USER_DELEGATOR = "idUserDelegator";

  private static final String USER_DELEGATED = "idUserDelegated";

  private transient LazyDataModel<Workflow> workflowDataModel;

  /**
   * data model for tab 2 absences and also tab 3 delegations. They have the same model class and we don't forget to initialize (set to
   * null) obTabChange so each time we'll have the correct data
   */
  private transient LazyDataModel<WorkflowUserBackup> userBackupAndDelegationDataModel;

  private transient LazyDataModel<WorkflowFunction> workflowFunctionDataModel;

  private transient SessionHandler sessionHandler;

  private transient List<User> autocompletedUserWorkflow = new ArrayList<User>();

  private transient List<User> autocompletedUserOff = new ArrayList<User>();

  private transient List<User> autocompletedUserBackup = new ArrayList<User>();

  // equivalent of "autocompletedUserOff" for absences
  private transient List<User> autocompletedUserDelegator = new ArrayList<User>();

  // equivalent of "autocompletedUserBackup" for absences
  private transient List<User> autocompletedUserDelegated = new ArrayList<User>();

  // READ and Edit an existing workflow
  private transient Workflow selectedWorkflow;

  private transient WorkflowStep selectedWorkflowStep;

  private transient int selectedWorkflowStepNumber;

  private transient boolean readingSteps = false;

  // Absence
  @Getter
  @Setter
  private transient Absence selectedAbsence;

  private transient WorkflowUserBackup wubAbsenceToBeUpdated;

  @Getter
  @Setter
  private transient AbsenceEditMode absenceEditMode = AbsenceEditMode.NONE;

  // Delegation
  @Getter
  @Setter
  private transient Absence selectedDelegation;

  private transient WorkflowUserBackup wubDelegationToBeUpdated;

  @Getter
  @Setter
  private transient AbsenceEditMode delegationEditMode = AbsenceEditMode.NONE;

  private transient List<Object> enrichActions;

  // Workflow Functions
  @Getter
  @Setter
  private transient List<User> functionUsers;

  @Getter
  @Setter
  private transient WorkflowFunction selectedWorkflowFunction;

  @Getter
  @Setter
  private transient User[] selectedFunctionUsers;

  @Getter
  @Setter
  private transient String workflowFunctionEntity;

  @Getter
  @Setter
  private transient String workflowFunctionUserEntity;

  @Getter
  @Setter
  private transient boolean notRenderFunctionUsers;

  @Getter
  @Setter
  private transient String workflowAbsenceEntity;

  @Getter
  @Setter
  private transient String workflowDelegationEntity;

  @Getter
  @Setter
  private transient int activeTabIndex = 0;

  @Getter
  @Setter
  private transient WorkflowFunction selectedStepFunction;

  @Getter
  @Setter
  private transient List<WorkflowFunction> functionsForSteps;

  @Getter
  @Setter
  private transient boolean confirmFunctionDeletion;

  @Autowired
  @Qualifier(WorkflowService.SERVICE_NAME)
  private transient WorkflowService workflowService;

  @Autowired
  @Qualifier(WorkflowUserBackupService.SERVICE_NAME)
  private transient WorkflowUserBackupService workflowUserBackupService;

  @Autowired
  @Qualifier(WorkflowFunctionService.SERVICE_NAME)
  private transient WorkflowFunctionService workflowFunctionService;

  @Autowired
  @Qualifier(WorkflowReminderSchedulerService.SERVICE_NAME)
  private transient WorkflowReminderSchedulerService wkfReminderSchedulerService;

  @Autowired
  @Qualifier(CustomActionService.SERVICE_NAME)
  private transient CustomActionService customActionService;

  @TaskTransient
  protected transient InstanceService instanceService;

  @Getter
  @Setter
  private transient String actorTypeSelectedValue;

  @Getter
  @Setter
  private transient String exchangeDirection;

  private String legalEntityValue;

  private Set<String> legalEntityValues;

  @Getter
  @Setter
  private transient boolean editMode;

  @Getter
  @Setter
  private transient boolean replaceWithANewVersionMode;
  private transient Workflow oldDuplicatedWorkflowForANewVersion;

  @Getter
  @Setter
  private transient Map<WorkflowStepId, List<WorkflowStepUser>> originalWorkflowSteps;

  private transient Map<WorkflowStepId, Set<ActionStepAssociation>> originalWorkflowStepActions;

  // Templates
  private transient List<Template> templateItems;

  @Getter
  @Setter
  private transient Template selectedTemplate;

  @Getter
  @Setter
  private transient String previewUrl;

  @Getter
  @Setter
  private transient String selectedReminderTemplate;

  @Getter
  @Setter
  private transient List<User> possibleUsers;

//@formatter:off
  /**
   * Possible cases: 
   * 1) Partner user no perimeter (only his Partner) -> we initialize the set in init() method, we need to populate it only once 
   * 2) Partner user having some perimeter (entities can change) -> we initialize the set in resolveSearchQuery() 
   * at every search query modification, of course only for the tab index 2 (absences Tab)
   * 3) Customer user having some perimeter (entities can change) -> we initialize the set in resolveSearchQuery()
   * 4) Client user without perimeter -> we initialize the set in resolveSearchQuery() (the autocomplete as before by "suggest" method which searches all users into the database wasn't good enough)
   * 
   * we use the field userOffAutocompleteProposals in onCompleteAbsentUser method, also in onAutocompleteStart related to advanced search
   * 
   * If the portal is shared (not dedicated) the customer users are removed from the list of possible choices
   */
//@formatter:on
  // no need for setter/getter
  private transient Set<User> userOffAutocompleteProposals = new TreeSet<User>();

  // Same thing as for absences/userOff
  private transient Set<User> delegatorAutocompleteProposals = new TreeSet<User>();

//@formatter:off
  /**
   * Client user without perimeter -> we initialize the set / autocomplete in resolveSearchQuery() not as before by "suggest" method
   * 
   * When the userOff is chosen we initialize the set of possibles backup users taking into account the selected user perimeter.
   * 
   * If the portal is shared (not dedicated) the customer users are removed from the list of possible choices
   */
//@formatter:on
  // no need for setter/getter
  private transient Set<User> userBackupAutocompleteProposals = new TreeSet<User>();

  // Same thing as for absences/userBackup
  private transient Set<User> delegatedAutocompleteProposals = new TreeSet<User>();

  private String activeAbsenceEntityCode = null;

  private String activeDelegationEntityCode = null;

  @Getter
  @Setter
  private transient List<SelectItem> legalEntities = new ArrayList<>();

  @Getter
  @Setter
  private List<CustomAction> customActions = new ArrayList<>();

  @Getter
  @Setter
  private transient Map<Long, Boolean> selectedActions = new HashMap<>();

  // custom to remove from database on save
  private transient List<CustomAction> customActionsRemoved = new ArrayList<>();

  @PostConstruct
  public void init() {
    this.sessionHandler = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
    this.beanService = getSpringBean(BeanService.class, BeanService.SERVICE_NAME);
    this.workflowProcess = WorkflowProcess.NONE;
    this.securityService = getSpringBean(SecurityService.class, SecurityService.SERVICE_NAME);
    this.instanceService = getBean(InstanceService.class, InstanceService.SERVICE_NAME);
    this.transformService = getBean(TransformService.class, TransformService.SERVICE_NAME);

    this.legalEntityValue = sessionHandler.isPartnerUser() ? sessionHandler.getPartner()
        .getCode() : null;
    this.legalEntityValues = (sessionHandler.isPartnerUser() || (!sessionHandler.isBackOfficeUser() && isClientWithPerimeter()))
        ? UserDetailsService.getUserScopeCodes(Objects.requireNonNull(getAuthenticatedUser()))
        : null;
    this.workflowFunctionEntity = sessionHandler.isPartnerUser() ? sessionHandler.getPartner()
        .getCode() : null;
    selectedAbsence = new Absence(AbsenceType.ABSENCE);
    selectedDelegation = new Absence(AbsenceType.DELEGATION);
    customActionService.setUsedCustomActions(customActions);

    // the next assignments make sense only if the current user is partner user or customer (company) user
    // for admin or backofice user we only need to set the WorkflowManagement portlet
    if (sessionHandler.isBackOfficeUser()) {
      return;
    }
    if (sessionHandler.isPartnerUser()) {
      this.workflowAbsenceEntity = sessionHandler.getPartner()
          .getCode();
    }
    else if (isClientWithPerimeter() && CollectionUtils.isNotEmpty(legalEntityValues) && this.workflowAbsenceEntity == null) {
      // try to get the code from the first item in dropdown, already computed
      this.workflowAbsenceEntity = legalEntityValues.stream()
          .findFirst()
          .get();
    }

    if (sessionHandler.isPartnerUser()) {
      this.workflowDelegationEntity = sessionHandler.getPartner()
          .getCode();
    }
    else if (isClientWithPerimeter() && CollectionUtils.isNotEmpty(legalEntityValues) && this.workflowDelegationEntity == null) {
      // try to get the code from the first item in dropdown, already computed
      this.workflowDelegationEntity = legalEntityValues.stream()
          .findFirst()
          .get();
    }

    if (!isPartnerWithPerimeterOrClient()) {
      // they are initialize only once, we don't have a drop-down to change
      activeAbsenceEntityCode = activeDelegationEntityCode = this.partnerCode;
    }
    else {
      // init values here because we must have something set for the first call of resolveQuesrySearch
      if (this.isClientWithoutPerimeter() && (workflowAbsenceEntity == null || "null".equals(workflowAbsenceEntity))) {
        // customer user without perimeter, "Entity" selected in dropdown
        activeAbsenceEntityCode = null;
        activeDelegationEntityCode = null;
      }
      else {
        activeAbsenceEntityCode = workflowAbsenceEntity;
        activeDelegationEntityCode = workflowDelegationEntity;
      }
    }
  }

  @PreRemove
  public void preRemove() {
    // delete custom actions from db
    customActionService.removeAll(customActions);
  }

  @Override
  public void beforePersist() {
    super.beforePersist();
    customActions = customActionService.updateCustomActions(customActions, customActionsRemoved);
  }

  @Override
  @PreDestroy
  public void reset() {
    super.reset();
    search = null;
    userQuery = null;
    userQueryQuickSearch = null;
    draggableRows = false;
  }

  @Override
  public void prepareTaskForExport() {

  }

  @Override
  public void prepareTaskForImport(Instance instance) {
    customActions.forEach(action -> action.setId(null));
    customActions = customActionService.storeAllCustomActions(customActions);
  }

  public LazyDataModel<Workflow> getWorkflowDataModel() {
    try {
      if (isWorkflow()) {
        return workflowDataModel == null ? workflowDataModel = resolveWorkflowDataModel() : workflowDataModel;
      }
      else {
        return emptyDataModel();
      }
    }
    catch (Exception e) {
      error(e, "xtdtsklbls.error_data_model", e.getMessage());
      return emptyDataModel();
    }
  }

  public LazyDataModel<Workflow> resolveWorkflowDataModel() {
    return new WorkflowManagementDataModel(workflowService, resolveSearchQuery());
  }

  public LazyDataModel<WorkflowUserBackup> getUserBackupAndDelegationDataModel() {
    try {
      if (isUserBackupConfiguration() || isDelegationConfiguration()) {
        return userBackupAndDelegationDataModel == null ? userBackupAndDelegationDataModel = resolveUserBackupAndDelegationDataModel()
            : userBackupAndDelegationDataModel;
      }
      else {
        return emptyDataModel();
      }
    }
    catch (Exception e) {
      error(e, "xtdtsklbls.error_data_model", e.getMessage());
      return emptyDataModel();
    }
  }

  public LazyDataModel<WorkflowUserBackup> resolveUserBackupAndDelegationDataModel() {
    return new WorkflowUserBackupDataModel(workflowUserBackupService, resolveSearchQuery());
  }

  @Override
  protected Query resolveSearchQuery() {
    QueryBuilder qb = QueryBuilder.createBuilder();
    addOwnersAndDescriptionPart(qb);
    addAuthorPart(qb);
    if (isUserBackupConfiguration()) {
      fillAutocompleteInfo(workflowAbsenceEntity, userOffAutocompleteProposals);
    }
    else if (isDelegationConfiguration()) {
      fillAutocompleteInfo(workflowDelegationEntity, delegatorAutocompleteProposals);
    }
    // search clauses
    if (advSearch) {
      final List<Clause> clauses = computeAdvSearchClauses();
      qb.and(clauses);
    }

    // search query
    qb.append(this.userQuery);
    qb.append(this.userQueryQuickSearch);
    String q = toBql(qb.query());

    // resolve property name for search
    q = StringUtils.replaceEach(q, new String[] { "[\'", "\']", "[", "]" }, new String[] { ".", "", ".", "" });

    q = replaceUsernameById(q);

    return isWorkflow() ? toQuery(q) : (q.isEmpty() ? createBuilder().query() : toQuery(q));
  }

  // for the autocomplete we only need owners = "...", in case an advance search was done and the user tries to perform another autocomplete
  // the resolveSearchQuery() will return something like owners = "..." AND name = "workflow1" AND...
  public Query resolveAutocompleteSearchQuery() {
    QueryBuilder qb = QueryBuilder.createBuilder();
    addOwnersAndDescriptionPart(qb);
    String q = toBql(qb.query());
    return toQuery(q);
  }

  private void addOwnersAndDescriptionPart(QueryBuilder qb) {
    if (isWorkflow() || isUserBackupConfiguration() || isDelegationConfiguration()) {
      qb.and(equal("owners", getCompanyCodeFromUser()));
    }
    if (isUserBackupConfiguration()) {
      Clause orClause = new OrClause(Clauses.isNull("description"), Clauses.equal("description", AbsenceType.ABSENCE.toString()));
      qb.and(orClause);
    }
    else if (isDelegationConfiguration()) {
      qb.and(equal("description", AbsenceType.DELEGATION.toString()));
    }
  }

  private void addAuthorPart(QueryBuilder qb) {
    if (isWorkflow()) {
      qb.or(equal("author", legalEntityValue), this.isPartnerUserConnected() ? equal("author", null) : null);
    }
    else if (isUserBackupConfiguration()) {
      if (!isPartnerWithPerimeterOrClient()) {
        qb.and(equal("author", legalEntityValue));
      }
      else {
        if (!this.isClientWithoutPerimeter() || !(workflowAbsenceEntity == null || "null".equals(workflowAbsenceEntity))) {
          qb.and(equal("author", workflowAbsenceEntity));
        }
      }
    }
    else if (isDelegationConfiguration()) {
      if (!isPartnerWithPerimeterOrClient()) {
        qb.and(equal("author", legalEntityValue));
      }
      else {
        if (!this.isClientWithoutPerimeter() || !(workflowDelegationEntity == null || "null".equals(workflowDelegationEntity))) {
          qb.and(equal("author", workflowDelegationEntity));
        }
      }
    }
  }

  private void fillAutocompleteInfo(String workflowEntity,
      Set<User> autocompleteProposals) {
    String activeEntityCode = "";
    if (!isPartnerWithPerimeterOrClient()) {
      activeEntityCode = legalEntityValue;
      if (this.isUserBackupConfiguration()) {
        this.activeAbsenceEntityCode = legalEntityValue;
      }
      else if (this.isDelegationConfiguration()) {
        this.activeAbsenceEntityCode = legalEntityValue;
      }
    }
    else {
      if (this.isClientWithoutPerimeter() && (workflowEntity == null || "null".equals(workflowEntity))) {
        // customer user without perimeter, "Entity" selected in dropdown
        activeEntityCode = null;
        if (this.isUserBackupConfiguration()) {
          this.activeAbsenceEntityCode = null;
        }
        else if (this.isDelegationConfiguration()) {
          this.activeDelegationEntityCode = null;
        }
      }
      else {
        activeEntityCode = workflowEntity;
        if (this.isUserBackupConfiguration()) {
          this.activeAbsenceEntityCode = workflowEntity;
        }
        else if (this.isDelegationConfiguration()) {
          this.activeDelegationEntityCode = workflowEntity;
        }
      }
    }
    autocompleteProposals.clear();
    List<User> possibleUsersOff = new ArrayList<User>();
    if (activeEntityCode == null) {
      // customer user without perimeter
      possibleUsersOff.addAll(securityService.getUsers(this.company, this.isDedicatedPortal(), true));
    }
    else {
      // partners user with or without perimeter, customer user with perimeter
      possibleUsersOff = securityService.getUsersFromPerimeter(asList(activeEntityCode));
      // if we have the same partner code ('002') in more than one environments ('ENV', 'ST2') we'll have users from both
      // but we need only from one so a filtering is needed
      possibleUsersOff = this.filterByCompany(possibleUsersOff, this.companyCode, true);
    }
    if (this.isDedicatedPortal()) {
      possibleUsersOff = this.removeCompanyUsers(possibleUsersOff);
    }
    autocompleteProposals.addAll(possibleUsersOff);
  }

  /**
   * We have a list of users (no matter how we obtained it) and we need to get only the users related to the specified company Note: The
   * users must me complete, including UserGroupAssociations. Lazy initializations don't work. We may choose to take only partners users or
   * partners + customers users I If we have the same partner code ('002') in more than one environments ('ENV', 'ST2') we'll have users
   * from both but we need only from one so a filtering is needed
   * 
   * @param initialUserList
   * @param companyCode
   * @param takeOnlyPartners
   * @return a list of users related to the specified company
   */
  private List<User> filterByCompany(List<User> initialUserList, String companyCode, boolean takeOnlyPartners) {
    List<User> result = new ArrayList<User>();
    for (User user : initialUserList) {
      boolean shouldPassToNextUser = false;
      for (UserGroupAssociation uga : user.getUserGroupAssociations()) {
        if (!takeOnlyPartners && uga.getId()
            .getGroup() instanceof Company) {
          Company cpy = (Company) uga.getId()
              .getGroup();
          if (Objects.equals(cpy.getCode(), companyCode)) {
            result.add(user);
            shouldPassToNextUser = true;
            break;
          }
        }
        else if (uga.getId()
            .getGroup() instanceof Partner) {
              Partner partner = (Partner) uga.getId()
                  .getGroup();
              // The code IS in the perimeter partners codes because otherwise it wouldn't be in user list
              // so the code is NOT an indication, we test the parent
              if (!(partner.getParent() instanceof Company)) {
                continue;
              }
              Company parent = (Company) partner.getParent();
              if (Objects.equals(parent.getCode(), companyCode)) {
                result.add(user);
                shouldPassToNextUser = true;
                break;
              }
            }
        else {
          continue;
        }
        if (shouldPassToNextUser) {
          break;
        }
      }

    }
    return result;
  }

  public String getCompanyCodeFromUser() {
    String companyCode = null;
    if (this.currentSession()
        .isCompanyUser()) {
      companyCode = this.currentSession()
          .getUser()
          .getPrimaryGroup()
          .getCode();
    }
    else {
      companyCode = this.currentSession()
          .getCompany()
          .getCode();
    }
    return companyCode;
  }

  public Map<Object, String> getValues(String label)  {
    return getEnumValuesForInvoice(label);
  }

  public String replaceUsernameById(String query) {
    if (isConcernedUserField(query)) {
      Map<String, String> userFieldValues = getUserFieldValues(query);
      Optional<String> id = null;

      if (!userFieldValues.isEmpty()) {
        for (Map.Entry<String, String> userFieldValue : userFieldValues.entrySet()) {
          String fieldName = userFieldValue.getKey();
          String fullname = userFieldValue.getValue()
              .replace("\"", "");

          id = getUserIdFromFullName(fieldName, fullname);
          if (id.isPresent()) {
            query = query.replace(fullname, id.get());
          }
        }
      }
    }
    return query;

  }

  private Optional<String> getUserIdFromFullName(String fieldName, String fullname) {
    List<User> autocompletedField = new ArrayList<>();

    switch (fieldName) {
    case USER_WORKFLOW:
      autocompletedField = autocompletedUserWorkflow;
      break;
    case USER_OFF:
      autocompletedField = autocompletedUserOff;
      break;
    case USER_BACKUP:
      autocompletedField = autocompletedUserBackup;
      break;
    case USER_DELEGATOR:
      autocompletedField = autocompletedUserDelegator;
      break;
    case USER_DELEGATED:
      autocompletedField = autocompletedUserDelegated;
      break;
    default:
    }

    return autocompletedField.stream()
        .filter(user -> fullname.equals(user.getFullname()))
        .map(user -> user.getId())
        .findFirst();
  }

  // Retourne une map associant le nom du champ que l'utilisateur a autocomplété avec sa valeur
  private Map<String, String> getUserFieldValues(String query) {
    String replaceQuery = query.replace("AND", "=");
    String[] queryElements = replaceQuery.split("[=]");
    Map<String, String> userFieldValues = new HashMap<>();

    for (int i = 0; i < queryElements.length; i += 2) {
      if (isConcernedUserField(queryElements[i].trim())) {
        if (i + 1 < queryElements.length && queryElements[i] != null && queryElements[i + 1] != null)
          userFieldValues.put(queryElements[i].trim(), queryElements[i + 1].trim());
      }
    }
    return userFieldValues;
  }

  @Override
  public void setBql(String bql) {
    if (bql != null && bql.contains("_text_")) {
      String val = bql.replace("\"_text_\" ~ ", "");
      List<String> matches = new ArrayList<>();
      if (isUserBackupConfiguration() || isDelegationConfiguration()) {
        String text = val.replace("\"", "");
        List<PropertyDescriptor> values = createDescriptor()
            .getProperties()
            .stream()
            .filter(p -> p.getType() == String.class && isUserPropertyDescriptor(p))
            .collect(toList());
        List<String> usersIds = workflowService.searchByFieldsTextOnUser(query, "fullname", text, 10)
            .stream()
            .filter(user -> user.getFullname()
                .contains(text))
            .map(user -> user.getId())
            .collect(Collectors.toList());
        if (!usersIds.isEmpty() && !text.equals("")) {
          for (String userId : usersIds) {
            matches.addAll(values.stream()
                .map(p -> p.getName() + " ~ " + val.replace(text, userId))
                .collect(toList()));
          }
        }
        else matches = values.stream()
            .map(p -> p.getName() + " ~ " + val)
            .collect(toList());
      }
      else {
        matches = createDescriptor()
            .getProperties()
            .stream()
            // for the moment quick search does not search on user name
            .filter(p -> p.getType() == String.class && !isUserPropertyDescriptor(p))
            .map(p -> p.getName() + " ~ " + val)
            .collect(toList());
      }
      bql = "";
      for (int i = 0; i < matches.size(); i++) {
        bql += matches.get(i);
        if (i < matches.size() - 1)
          bql += " OR ";
      }
    }
    super.setBql(bql);
  }

  private boolean isUserPropertyDescriptor(PropertyDescriptor p) {
    return p.getName()
        .contains("workflowStepUsers.id.user") ||
        p.getName()
            .contains(USER_BACKUP) ||
        p.getName()
            .contains(USER_OFF) ||
        p.getName()
            .contains(USER_DELEGATED) ||
        p.getName()
            .contains(USER_DELEGATOR);
  }

  public void onReorderWorkflows(ReorderEvent event) {
    // event.getFromIndex() and event.getToIndex() returns the index of the reorder elements from the list(if we have 20 elements in the
    // list and the page size is 15 and we reorder first and second element from the second page event.getToIndex() will be 15 and
    // event.getFromIndex() will be 16) so i have to get the elements on the page on which the reorder was done, the list will contain
    // maximum of page size elements.
    int fromIndex = event.getFromIndex();
    int toIndex = event.getToIndex();
    int pageSize = workflowDataModel.getPageSize();
    int start = pageSize * (fromIndex / pageSize);
    List<Workflow> list = new LinkedList<>(this.workflowDataModel.load(start, pageSize, null, null, null));
    List<Integer> reorderList = new ArrayList<>();
    reorderList.addAll(list.stream()
        .map(workflow -> workflow.getSortNumber())
        .collect(Collectors.toList()));
    // here I need to get the position of those 2 reorder elements from the list so as to put them in another list. Using the
    // reorderElements list I can swap the values of the 2 workflow's sorNumber(in case some workflows are deleted then the sortnumber will
    // not correspond to the event.getFromIndex() and event.getToIndex(), for example lets say that we have 30 workflows and we delete first
    // 5 of them, then the sortNumber for the existing workflows are from 5 to 29, and if i want to reorder first 2 workflows from the first
    // page then event.getFromIndex() will be 1 and event.getToIndex() will be 0)
    int fromIndexPositionInList = fromIndex % pageSize;
    int toIndexPositionInList = toIndex % pageSize;
    List<Workflow> reorderElements = new ArrayList<Workflow>();
    reorderElements.add(list.get(toIndexPositionInList));
    reorderElements.add(list.get(fromIndexPositionInList));
    int sortNumberToIndex = reorderElements.get(0)
        .getSortNumber();
    reorderElements.get(0)
        .setSortNumber(reorderElements.get(1)
            .getSortNumber());
    reorderElements.get(1)
        .setSortNumber(sortNumberToIndex);
    this.workflowService.storeAllWorkflows(reorderElements);
  }

  // onAutocompleteStart(), onCountAdvancedSearch(), onAdvancedSearchModel(), getSearchModel() are function needed for the advance search
  // component and will be called from search.xhtml
  public Collection<String> onAutocompleteStart(final CompleteEvent event) {
    if (isUserWorkflowField(event.getFieldName())) {
      autocompletedUserWorkflow = workflowService.searchByFieldsTextOnUser(query, "fullname", event.getFieldValue(), 10);
      return getAutoCompleteUserFullName(event, autocompletedUserWorkflow);
    }
    else if (isUserOffField(event.getFieldName())) {
      autocompletedUserOff = Lists.newArrayList(this.userOffAutocompleteProposals);
      return getAutoCompleteUserFullName(event, autocompletedUserOff);
    }
    else if (isUserBackupField(event.getFieldName())) {
      if (CollectionUtils.isEmpty(userBackupAutocompleteProposals)) {
        this.fillUserBackupAutocompleteProposals();
      }
      autocompletedUserBackup = Lists.newArrayList(this.userBackupAutocompleteProposals);
      return getAutoCompleteUserFullName(event, autocompletedUserBackup);
    }
    else if (isUserDelegatorField(event.getFieldName())) {
      autocompletedUserDelegator = Lists.newArrayList(this.delegatorAutocompleteProposals);
      return getAutoCompleteUserFullName(event, autocompletedUserDelegator);
    }
    else if (isUserDelegatedField(event.getFieldName())) {
      if (CollectionUtils.isEmpty(delegatedAutocompleteProposals)) {
        this.fillUserDelegatedAutocompleteProposals();
      }
      autocompletedUserDelegated = Lists.newArrayList(this.delegatedAutocompleteProposals);
      return getAutoCompleteUserFullName(event, autocompletedUserDelegated);
    }
    return workflowService.searchByFieldsText(resolveAutocompleteSearchQuery(), event.getFieldName(), event.getFieldValue(), 10);
  }

  public List<User> onCompleteBackupUser(String query) {
    return computeListOfSecondUser(query, getSelectedAbsence(), activeAbsenceEntityCode, userBackupAutocompleteProposals);
  }

  public List<User> onCompleteDelegatedUser(String query) {
    return computeListOfSecondUser(query, getSelectedDelegation(), activeDelegationEntityCode, delegatedAutocompleteProposals);
  }

  private List<User> computeListOfSecondUser(String query, Absence absent, String activeEntityCode, Set<User> autocompleteProposals) {
    List<User> result = Collections.emptyList();
    if (isClientWithoutPerimeter()) {
      result = this.securityService
          .suggest(absent.getUserOff()
              .getId(), this.getInstance(), query)
          .stream()
          .filter(u -> absent.getUserBackupList() == null || !absent.getUserBackupList()
              .contains(u))
          .collect(Collectors.toList());
      if (activeEntityCode != null) {
        // we have selected something, it is not the "Entity" word selected
        result = result.stream()
            .filter(user ->
            {
              String theCode = user.getPrimaryGroup()
                  .getCode();
              boolean isPartnerUserHavingTheGoodCode = PrincipalHelper.isPartnerUser(user) &&
                  Objects.equals(theCode, activeEntityCode);
              boolean isCompanyUserOnDedicatedPortal = this.isDedicatedPortal() && PrincipalHelper.isCompanyUser(user);
              return (isPartnerUserHavingTheGoodCode || isCompanyUserOnDedicatedPortal);
            })
            .collect(Collectors.toList());
      }
      // if the portal is shared (not dedicated) we remove company users, they don't belong to the absences management
      if (!this.isDedicatedPortal()) {
        result = this.removeCompanyUsers(result);
      }
    }
    else {
      // partner user without perimeter, partner user having perimeter, customer user having perimeter
      result = autocompleteProposals.stream()
          .filter(user ->
          {
            return StringUtils.startsWithIgnoreCase(user.getLogin(), query) ||
                StringUtils.startsWithIgnoreCase(user.getFirstname(), query) ||
                StringUtils.startsWithIgnoreCase(user.getLastname(), query) || StringUtils.containsIgnoreCase(user.getEmail(), query) ||
                StringUtils.containsIgnoreCase(user.getPrimaryGroup()
                    .getName(), query) ||
                StringUtils.startsWithIgnoreCase(user.getPrimaryGroup()
                    .getFullname(), query);
          })
          .filter(u -> absent.getUserBackupList() == null || !absent.getUserBackupList()
              .contains(u))
          .filter(u -> !Objects.equals(u, absent.getUserOff()))
          .collect(Collectors.toList());
    }
    return result;
  }

  private List<User> removeCompanyUsers(List<User> users) {

    if (CollectionUtils.isEmpty(users)) {
      return new ArrayList<User>();
    }

    // if we do a test like:
    // boolean isCompanyUser = PrincipalHelper.isCompanyUser(user);
    // we get this exception:
    // org.hibernate.LazyInitializationException: failed to lazily initialize a collection of role:
    // com.byzaneo.security.bean.User.userGroupAssociations, could not initialize proxy - no Session
    // so a query to the database must be done. We use the new list to test if the user is company user.
    List<String> userIds = users.stream()
        .map(User::getId)
        .collect(Collectors.toList());
    List<User> usersFromDB = securityService.getUsersByIds(userIds.toArray(new String[0]));
    List<User> noCompanyUserList = usersFromDB.stream()
        .filter(u -> !PrincipalHelper.isCompanyUser(u))
        .collect(Collectors.toList());
    return noCompanyUserList;
  }

  public List<User> onCompleteAbsentUser(String query) {
    return computeListOfFirstUser(query, activeAbsenceEntityCode, userOffAutocompleteProposals);
  }

  public List<User> onCompleteDelegatorUser(String query) {
    return computeListOfFirstUser(query, activeDelegationEntityCode, delegatorAutocompleteProposals);
  }

  private List<User> computeListOfFirstUser(String query, String activeEntityCode, Set<User> autocompleteProposals) {
    List<User> result = Collections.emptyList();
    if (isClientWithoutPerimeter()) {
      // client without perimeter => get the suggestion list the old way
      result = this.securityService
          .suggest(null, this.getInstance(), query)
          .stream()
          .collect(Collectors.toList());
      if (activeEntityCode != null) {
        // we have selected something, it is not the "Entity" word selected
        result = result.stream()
            .filter(user ->
            {
              String theCode = user.getPrimaryGroup()
                  .getCode();
              boolean isPartnerUserHavingTheGoodCode = PrincipalHelper.isPartnerUser(user) &&
                  Objects.equals(theCode, activeEntityCode);
              boolean isCompanyUserOnDedicatedPortal = this.isDedicatedPortal() && PrincipalHelper.isCompanyUser(user);
              return (isPartnerUserHavingTheGoodCode || isCompanyUserOnDedicatedPortal);
            })
            .collect(Collectors.toList());
      }
    }
    else {
      // partner user without perimeter, partner user having perimeter, customer user having perimeter
      result = autocompleteProposals.stream()
          .filter(user ->
          {
            return StringUtils.startsWithIgnoreCase(user.getLogin(), query) ||
                StringUtils.startsWithIgnoreCase(user.getFirstname(), query) ||
                StringUtils.startsWithIgnoreCase(user.getLastname(), query) || StringUtils.containsIgnoreCase(user.getEmail(), query) ||
                StringUtils.containsIgnoreCase(user.getPrimaryGroup()
                    .getName(), query) ||
                StringUtils.startsWithIgnoreCase(user.getPrimaryGroup()
                    .getFullname(), query);
          })
          .collect(Collectors.toList());
    }
    // if the portal is shared (not dedicated) we remove company users, they don't belong to the absences management
    if (!this.isDedicatedPortal()) {
      result = this.removeCompanyUsers(result);
    }
    return result;
  }

  public void onUserOff(SelectEvent event) {
    User selectedUserOff = (User) event.getObject();
    this.getSelectedAbsence()
        .setUserOff(selectedUserOff);
    this.getSelectedAbsence()
        .setUserBackupList(null);

    // autocomplete list for backup users
    this.fillUserBackupAutocompleteProposals();
  }

  public void onUserDelegator(SelectEvent event) {
    User selectedDelegator = (User) event.getObject();
    this.getSelectedDelegation()
        .setUserOff(selectedDelegator);
    this.getSelectedDelegation()
        .setUserBackupList(null);

    // autocomplete list for delegated users
    this.fillUserDelegatedAutocompleteProposals();
  }

  private void fillUserBackupAutocompleteProposals() {
    userBackupAutocompleteProposals.clear();

    List<User> possibleUsersBackup = new ArrayList<User>();
    if (activeAbsenceEntityCode == null) {
      // customer user without perimeter, "Entity" selected in dropdown
      Collection<User> tempUsers = securityService.getUsers(this.company, this.isDedicatedPortal(), true);
      possibleUsersBackup.addAll(tempUsers);
    }
    else {
      possibleUsersBackup = securityService.getUsersFromPerimeter(asList(activeAbsenceEntityCode));
      // if we have the same partner code ('002') in more than one environments ('ENV', 'ST2') we'll have users from both
      // but we need only from one so a filtering is needed
      possibleUsersBackup = this.filterByCompany(possibleUsersBackup, this.companyCode, true);
    }
    // if the portal is shared (not dedicated) we remove company users, they don't belong to the absences management
    if (!this.isDedicatedPortal()) {
      possibleUsersBackup = this.removeCompanyUsers(possibleUsersBackup);
    }
    userBackupAutocompleteProposals.addAll(possibleUsersBackup);
  }

  private void fillUserDelegatedAutocompleteProposals() {
    delegatedAutocompleteProposals.clear();

    List<User> possibleUsersDelegated = new ArrayList<User>();
    if (activeDelegationEntityCode == null) {
      // customer user without perimeter, "Entity" selected in dropdown
      Collection<User> tempUsers = securityService.getUsers(this.company, this.isDedicatedPortal(), true);
      possibleUsersDelegated.addAll(tempUsers);
    }
    else {
      possibleUsersDelegated = securityService.getUsersFromPerimeter(asList(activeDelegationEntityCode));
      // if we have the same partner code ('002') in more than one environments ('ENV', 'ST2') we'll have users from both
      // but we need only from one so a filtering is needed
      possibleUsersDelegated = this.filterByCompany(possibleUsersDelegated, this.companyCode, true);
    }
    // if the portal is shared (not dedicated) we remove company users, they don't belong to the absences management
    if (!this.isDedicatedPortal()) {
      possibleUsersDelegated = this.removeCompanyUsers(possibleUsersDelegated);
    }
    delegatedAutocompleteProposals.addAll(possibleUsersDelegated);
  }

  public void onUserSelect(SelectEvent event) {
    this.getSelectedAbsence()
        .getUserBackupList()
        .add((User) event.getObject());
  }

  public void onUserDelegatedSelect(SelectEvent event) {
    this.getSelectedDelegation()
        .getUserBackupList()
        .add((User) event.getObject());
  }

  public void onUserUnselect(UnselectEvent event) {
    this.getSelectedAbsence()
        .getUserBackupList()
        .remove((User) event.getObject());
  }

  public void onUserDelegatedUnselect(UnselectEvent event) {
    this.getSelectedDelegation()
        .getUserBackupList()
        .remove((User) event.getObject());
  }

  private List<String> getAutoCompleteUserFullName(final CompleteEvent event, List<User> autocompletedUser) {
    return autocompletedUser.stream()
        .map(user -> user.getFullname())
        .collect(Collectors.toList());
  }

  private boolean isUserWorkflowField(String field) {
    return field.contains(USER_WORKFLOW);
  }

  private boolean isUserOffField(String field) {
    return field.contains(USER_OFF);
  }

  private boolean isUserBackupField(String field) {
    return field.contains(USER_BACKUP);
  }

  private boolean isUserDelegatorField(String field) {
    return field.contains(USER_DELEGATOR);
  }

  private boolean isUserDelegatedField(String field) {
    return field.contains(USER_DELEGATED);
  }

  private boolean isConcernedUserField(String field) {
    return isUserWorkflowField(field) || isUserOffField(field) || isUserBackupField(field) || isUserDelegatorField(field) ||
        isUserDelegatedField(field);
  }

  public long onCountAdvancedSearch(CountEvent event) {
    QueryBuilder qb = createBuilder();
    addOwnersAndDescriptionPart(qb);
    addAuthorPart(qb);
    String query = event.getBql();
    query = replaceUsernameById(query);
    qb.append(query);
    return isWorkflow() ? workflowService.countWorkflows(qb.query()) : workflowUserBackupService.count(qb.query());
  }

  public void onAdvancedSearchModel() {
    // Search in database
    selected = null;
    workflowDataModel = null;
    userBackupAndDelegationDataModel = null;
    try {
      String dataTableId = "resultTable"; // default isWorkflow();
      if (isUserBackupConfiguration()) {
        dataTableId = "absenceDataTable";
      }
      else if (isDelegationConfiguration()) {
        dataTableId = "delegationDataTable";
      }
      DataTable dataTable = JSFHelper.resetDataTable("tskForm-workflowManagementView-workflowTabs-" + dataTableId);

      String query = this.userQuery != null ? this.userQuery.toString() : "";
      if (query.contains("ORDER BY")) {
        String[] queryList = query.split(" ");
        String sortColumn = queryList[queryList.length - 1];
        if (dataTable != null) {
          for (UIColumn column : dataTable.getColumns()) {
            if (sortColumn
                .equals(column.getField())) {
              dataTable.setSortColumn(column);
            }
          }
          dataTable.setSortOrder("ASCENDING");
        }
        // field is not recognized in order by
        userQuery = toQuery(query.substring(0, query.indexOf("ORDER BY")));

        if (isWorkflow()) workflowDataModel = getWorkflowDataModel();
        else if (isUserBackupConfiguration() || isDelegationConfiguration())
          userBackupAndDelegationDataModel = getUserBackupAndDelegationDataModel();
      }
    }
    catch (Exception e) {
      LOGGER.error("failed to retreive data", e);
      if (isWorkflow()) workflowDataModel = emptyDataModel();
      else if (isUserBackupConfiguration() || isDelegationConfiguration()) userBackupAndDelegationDataModel = emptyDataModel();
    }
    draggableRows = true;
  }

  public QueryModel getSearchModelAdvancedSearch() {
    return QueryModelBuilder.builder()
        .addBqlQueryReservedWord()
        .addBqlQueryFunctionName()
        .addQueryFieldBeanDescriptor(getSearch(), getSearchFieldPredicate(),
            null, null, getLocale(), getLocale(), true, true, true, null,
            getAutocompleteField())
        .build();
  }

  @Override
  public QueryModel getSearchModel() {
    return this.getSearchModelAdvancedSearch();
  }

  public void onAddAbsence() {
    absenceEditMode = AbsenceEditMode.CREATION;
  }

  public void onAddDelegation() {
    delegationEditMode = AbsenceEditMode.CREATION;
  }

  public void onSaveOrUpdateAbsence() {
    if (validateAbsence()) {
      switch (absenceEditMode) {
      case CREATION:
        saveAbsence();
        break;
      case EDIT:
        updateAbsence();
        break;
      default:
        break;
      }
    }
    absenceEditMode = AbsenceEditMode.NONE;
    initAbsenceDelegation(selectedAbsence, AbsenceType.ABSENCE);
  }

  public void onSaveOrUpdateDelegation() {
    if (validateDelegation()) {
      switch (delegationEditMode) {
      case CREATION:
        saveDelegation();
        break;
      case EDIT:
        updateDelegation();
        break;
      default:
        break;
      }
    }
    delegationEditMode = AbsenceEditMode.NONE;
    initAbsenceDelegation(selectedDelegation, AbsenceType.DELEGATION);
  }

  private boolean validateAbsence() {
    return !checkIfReplacementPersonIsAbsent();
  }

  private boolean checkIfReplacementPersonIsAbsent() {
    String idPersonAbsent = workflowUserBackupService.replacementPersonIsAbsent(selectedAbsence.getUserBackupList(),
        selectedAbsence.getBeginDate(), selectedAbsence.getEndDate(), AbsenceType.ABSENCE.toString());

    if (!idPersonAbsent.isEmpty()) {
      User userAbsent = selectedAbsence.getUserBackupList()
          .stream()
          .filter(abs -> idPersonAbsent.equals(abs.getId()))
          .findFirst()
          .orElse(new User());
      error(LABEL_FAMILY + ".replacement_person_is_absent", userAbsent.getFullname());
      return true;
    }

    return false;
  }

  private boolean validateDelegation() {
    return !checkIfDelegatedPersonIsAlreadyDelegated();
  }

  private boolean checkIfDelegatedPersonIsAlreadyDelegated() {
    String idPersonDelegated = workflowUserBackupService.replacementPersonIsAbsent(selectedDelegation.getUserBackupList(),
        selectedDelegation.getBeginDate(), selectedDelegation.getEndDate(), AbsenceType.DELEGATION.toString());

    if (!idPersonDelegated.isEmpty()) {
      User userDelegated = selectedDelegation.getUserBackupList()
          .stream()
          .filter(delegated -> idPersonDelegated.equals(delegated.getId()))
          .findFirst()
          .orElse(new User());
      error(LABEL_FAMILY + ".delegated_person_is_already_delegated", userDelegated.getFullname());
      return true;
    }

    return false;
  }

  private void saveAbsence() {
    List<WorkflowUserBackup> wub = convertToWorkflowUserBackupList(selectedAbsence, AbsenceType.ABSENCE, workflowAbsenceEntity);
    List<WorkflowUserBackup> wubSaved = workflowUserBackupService.save(wub);

    if (isNotEmpty(wubSaved)) {
      info(LABEL_FAMILY + ".add_absence_success");
    }
  }

  private void saveDelegation() {
    List<WorkflowUserBackup> wub = convertToWorkflowUserBackupList(selectedDelegation, AbsenceType.DELEGATION, workflowDelegationEntity);
    List<WorkflowUserBackup> wubSaved = workflowUserBackupService.save(wub);

    if (isNotEmpty(wubSaved)) {
      info(LABEL_FAMILY + ".add_delegation_success");
    }
  }

  private List<WorkflowUserBackup> convertToWorkflowUserBackupList(Absence selected, AbsenceType absenceType, String workflowEntity) {
    List<WorkflowUserBackup> wubList = new ArrayList<>();
    // setting the author and owners
    selected.setOwners(getCompanyCodeFromUser());
    if (!isPartnerWithPerimeterOrClient()) {
      // partner user with no scope
      selected.setAuthor(legalEntityValue);
    }
    else {
      // user with some scope, we can choose the entity from dropdown
      selected.setAuthor(workflowEntity);
    }
    for (User user : selected.getUserBackupList()) {
      wubList.add(new WorkflowUserBackup(selected.getUserOff()
          .getId(), selected.getBeginDate(), selected.getEndDate(), user.getId(), selected.getOwners(),
          selected.getAuthor(), absenceType.toString()));
    }
    return wubList;
  }

  private void updateAbsence() {
    wubAbsenceToBeUpdated.setEndDate(selectedAbsence.getEndDate());

    WorkflowUserBackup wubUpdated = workflowUserBackupService.save(wubAbsenceToBeUpdated);

    if (wubUpdated != null) {
      info(LABEL_FAMILY + ".modify_absence_success");
    }
  }

  private void updateDelegation() {
    wubDelegationToBeUpdated.setEndDate(selectedDelegation.getEndDate());

    WorkflowUserBackup wubUpdated = workflowUserBackupService.save(wubDelegationToBeUpdated);

    if (wubUpdated != null) {
      info(LABEL_FAMILY + ".modify_delegation_success");
    }
  }

  public void onModifyAbsence(WorkflowUserBackup wub) {
    absenceEditMode = AbsenceEditMode.EDIT;

    wubAbsenceToBeUpdated = workflowUserBackupService.getFromId(wub);

    selectedAbsence.setUserOff(securityService.getUser(wub.getIdUserOff()));
    selectedAbsence.setBeginDate(wub.getBeginDate());
    selectedAbsence.setEndDate(wub.getEndDate());
    selectedAbsence.setUserBackupList(asList(securityService.getUser(wub.getIdUserBackup())));
    selectedAbsence.setOwners(wub.getOwners());
    selectedAbsence.setAuthor(wub.getAuthor());
    selectedAbsence.setDescription(AbsenceType.ABSENCE);
  }

  public void onModifyDelegation(WorkflowUserBackup wub) {
    delegationEditMode = AbsenceEditMode.EDIT;

    wubDelegationToBeUpdated = workflowUserBackupService.getFromId(wub);

    selectedDelegation.setUserOff(securityService.getUser(wub.getIdUserOff()));
    selectedDelegation.setBeginDate(wub.getBeginDate());
    selectedDelegation.setEndDate(wub.getEndDate());
    selectedDelegation.setUserBackupList(asList(securityService.getUser(wub.getIdUserBackup())));
    selectedDelegation.setOwners(wub.getOwners());
    selectedDelegation.setAuthor(wub.getAuthor());
    selectedDelegation.setDescription(AbsenceType.DELEGATION);
  }

  public void onDeleteAbsence(WorkflowUserBackup wub) {
    workflowUserBackupService.remove(wub);
    info(LABEL_FAMILY + ".delete_absence_success");
  }

  public void onDeleteDelegation(WorkflowUserBackup wub) {
    workflowUserBackupService.remove(wub);
    info(LABEL_FAMILY + ".delete_delegation_success");
  }

  public void onCancelAbsence() {
    initAbsenceDelegation(selectedAbsence, AbsenceType.ABSENCE);
  }

  public void onCancelDelegation() {
    initAbsenceDelegation(selectedDelegation, AbsenceType.DELEGATION);
  }

  private void initAbsenceDelegation(Absence absenceDelegation, AbsenceType absenceType) {
    absenceDelegation.setUserBackupList(null);
    absenceDelegation.setBeginDate(null);
    absenceDelegation.setEndDate(null);
    absenceDelegation.setUserOff(null);
    absenceDelegation.setOwners(null);
    absenceDelegation.setAuthor(null);
    absenceDelegation.setDescription(absenceType);
  }

  @Override
  public BeanDescriptor getSearch() {
    if (this.search == null) {
      this.search = super.getSearch();
      this.search.getProperties()
          .stream()
          .filter(pd -> pd.getType() == Date.class)
          .forEach(pd -> pd.setValue(new DatePeriod()));

      // Mise à jour des labels dans la recherche avancée si on change de page (workflow ou userBackup)
      validate(search.getProperties(), getLabelPrefix(), sessionHandler.getInstance());
    }
    return search;
  }

  public WorkflowService getWorkflowService() {
    if (workflowService == null) {
      this.workflowService = getBean(WorkflowService.class, WorkflowService.SERVICE_NAME);
    }
    return workflowService;
  }

  public void setWorkflowService(WorkflowService workflowService) {
    this.workflowService = workflowService;
  }

  public void setDataModel(LazyDataModel<Workflow> dataModel) {
    this.workflowDataModel = dataModel;
  }

  public List<User> getAutocompletedUserWorkflow() {
    return autocompletedUserWorkflow;
  }

  public void setAutocompletedUserWorkflow(List<User> autocompletedUserWorkflow) {
    this.autocompletedUserWorkflow = autocompletedUserWorkflow;
  }

  public List<User> getAutocompletedUserOff() {
    return autocompletedUserOff;
  }

  public void setAutocompletedUserOff(List<User> autocompletedUserOff) {
    this.autocompletedUserOff = autocompletedUserOff;
  }

  public List<User> getAutocompletedUserBackup() {
    return autocompletedUserBackup;
  }

  public void setAutocompletedUserBackup(List<User> autocompletedUserBackup) {
    this.autocompletedUserBackup = autocompletedUserBackup;
  }

  @Override
  public int getFieldsNumber() {
    return 4;
  }

  @Override
  public Class<?> getIndexableType() {
    return null;
  }

  @Override
  protected BeanDescriptor createDescriptorStatus() {
    BeanDescriptor bean = new BeanDescriptor();
    return bean;
  }

  @Override
  protected BeanDescriptor createDescriptor() {
    if (getPathDescriptor() != null) {
      try {
        return beanService.fromClasspath(getPathDescriptor());
      }
      catch (IOException ex) {
        error(this, ex, getRootCauseMessage(ex));
        LOGGER.error("Failed to load bean descriptor {}", ex);
        return null;
      }
    }
    return null;
  }

  public void addCustomAction() {
    customActions.add(new CustomAction());
  }

  public void removeCustomAction(CustomAction customAction) {
    // check first if action is used
    if (!customAction.isUsed()) {
      customActionsRemoved.add(customAction);
      customActions.remove(customAction);
    }
    else
      error(this, null, "error_deleting_custom_actions");
  }

  private void updateUsedCustomActions() {
    List<CustomAction> actions = workflowStepInCreation.getCustomActions()
        .stream()
        .map(ActionStepAssociation::getAction)
        .collect(toList());
    selectedActions = customActions.stream()
        .collect(toMap(action -> action.getId(), action -> actions.contains(action)));
  }

  public List<String> getAllAvailableFormPageTemplates(String code) {
    return getAllFormPageTemplates(code);
  }

  /**
   * Path descriptor dynamique si on veut consulter les absences ou consulter les workflows
   */
  @Override
  public String getPathDescriptor() {
    switch (activeTabIndex) {
    case 0:
      // isWorkflow()
      return "descriptors/workflow-columns.xml";
    case 1:
      // nothing for functions by now
      break;
    case 2:
      // isUserBackupConfiguration() / absences
      return "descriptors/userbackupworkflow-columns.xml";
    case 3:
      // isDelegationConfiguration()
      return "descriptors/delegationworkflow-columns.xml";
    default:
    }
    return "";
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  public void onDisableWorkflow(Workflow workflow) {
    this.selectedWorkflow = workflow;
    if (workflow.isWorkflowDisabled()) {
      addMessage(null, FacesMessage.SEVERITY_WARN, "gnxworkflowlbls.workflow_disabled");
    }
    else {
      setWorkflowProcessInDisable(workflow);
      try {
        wkfReminderSchedulerService.deleteJob(workflow);
      }
      catch (SchedulerException e) {
        LOGGER.error("Cannot delete job for workflow: " + e);
      }
    }
  }

  public void onEnableWorkflow(Workflow workflow) {
    this.selectedWorkflow = workflow;
  }

  public void onCancelEnableWorkflow() {
    this.selectedWorkflow = null;
  }

  public void enableWorkflow() {
    this.selectedWorkflow.setEndDate(null);
    scheduleReminderForWorkflow(selectedWorkflow);
    onSaveDisabledWorkflow();
  }

  public void onDeleteWorkflow(Workflow workflow) {

    List<String> invoiceNumbers = getInvoiceNumbersUsingThisWorkflow(workflow);

    if (!invoiceNumbers.isEmpty()) {
      addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".delete_workflow_error");
      for (String invoiceNumber : invoiceNumbers) {
        addMessage(null, FacesMessage.SEVERITY_ERROR, String.format("%s N° %s", "INVOIC", invoiceNumber));
      }
    }
    else if (!isWorkflowDisabled(workflow)) {
      addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".delete_not_disabled_workflow");
    }
    else {
      deleteWorkflow(workflow);
      addMessage(FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".delete_workflow_info", workflow.getName());
    }

  }

  public boolean isWorkflowDisabled(Workflow workflow) {
    return workflow.isWorkflowDisabled();
  }

  private List<String> getInvoiceNumbersUsingThisWorkflow(Workflow workflow) {
    List<Long> documentsId = workflowDocumentStatusDAO.findByWorkflow(workflow)
        .stream()
        .map(wkfDocStatus -> wkfDocStatus.getDocument())
        .map(doc -> doc.getId())
        .collect(Collectors.toList());
    return documentService.searchIndexables(InvoiceIndex.class, searchInvoices(documentsId), null)
        .getContent()
        .stream()
        .map(invoiceIndex -> invoiceIndex.getInvoiceNumber())
        .collect(Collectors.toList());
  }

  private Query searchInvoices(List<Long> ids) {
    return QueryBuilder.createBuilder()
        .and(in(ENTITY_ID, ids))
        .query();
  }

  public boolean isWorkflowExpired(Workflow workflow) {
    return workflow.getEndDate()
        .compareTo(new Date()) < 0;
  }

  private void deleteWorkflow(Workflow workflow) {
    this.workflowService.deleteWorkflow(workflow);
  }

  public void onSaveDisabledWorkflow() {
    setModificationData(selectedWorkflow);
    this.workflowDAO.store(this.workflowDAO.merge(this.selectedWorkflow));
    this.selectedWorkflow = null;
    setWorkflowProcessNone();
  }

  private void setModificationData(Workflow wkf) {
    wkf.setLastModifiedBy(getCurrentUser().getFullname());
    wkf.setModificationDate(new Date());
  }

  public void onEditWorkflow(Workflow workflow) {
    List<String> invoiceNumbers = getInvoiceNumbersUsingThisWorkflow(workflow);
    int assignedDocs = invoiceNumbers.size();
    if (assignedDocs > 0) {
      if (assignedDocs == 1)
        addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".edit_workflow_error_one_document",
            String.format("%s N° %s",
                "INVOIC", invoiceNumbers.get(0)));
      else if (assignedDocs < 10)
        addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".edit_workflow_error", assignedDocs);
      else {
        addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".edit_workflow_error_ten_docs", assignedDocs);
        addMessage(FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".edit_workflow_error_limit");
      }
      if (assignedDocs > 1)
        for (int i = 0; i < (assignedDocs > 10 ? 10 : assignedDocs); i++) {
          addMessage(null, FacesMessage.SEVERITY_ERROR, String.format("%s N° %s", "INVOIC",
              invoiceNumbers.get(i)));
        }
      return;
    }

    workflowInCreation = workflow;
    originalWorkflowSteps = getWorkflowStepUsersAsMap(workflow);
    originalWorkflowStepActions = getWorkflowStepActionsAsMap(workflow);
    editMode = true;
    setWorkflowProcessAtWorkflowCreation();
  }

  public void onDuplicateAndEditWorkflow(Workflow workflow) {

    this.prepareToDuplicateWorkflow(workflow);
    workflowInCreation.setEndDate(Date.from(LocalDate.of(2020, 1, 1)
        .atStartOfDay(ZoneId.systemDefault())
        .toInstant()));

    int nextSortNumber = this.workflowService.getNextOrderNumberForCompanyId("sortNumber", this.getCompanyCodeFromUser());
    workflowInCreation.setSortNumber(nextSortNumber);
    workflowInCreation = this.workflowDAO.store(this.workflowDAO.merge(this.workflowInCreation));

    originalWorkflowSteps = getWorkflowStepUsersAsMap(workflowInCreation);
    originalWorkflowStepActions = getWorkflowStepActionsAsMap(workflowInCreation);

  }

  public void onReplaceWithANewVersion(Workflow workflow) {
    oldDuplicatedWorkflowForANewVersion = workflow;
    replaceWithANewVersionMode = true;

    this.prepareToDuplicateWorkflow(workflow);
  }

  private void prepareToDuplicateWorkflow(Workflow workflow) {
    // this is a new workflow, a copy of the selected one, so no need to test related invoices
    workflowInCreation = WorkflowHelper.duplicateWorkflow(workflow, this.getCurrentUser()
        .getFullname());

    // No need to duplicate workflowDocumentStatuses - after all this is a new workflow
    // Duplicate workflowSteps as new steps
    List<WorkflowStep> duplicatedSteps = WorkflowHelper.duplicateStepsForDuplicatedWorkflow(workflow, workflowInCreation);
    workflowInCreation.setWorkflowSteps(duplicatedSteps);

    editMode = true;
    setWorkflowProcessAtWorkflowCreation();
  }

  public void onAddWorkflow() {
    this.workflowInCreation = new Workflow();
    // if we un-check the "Active" checkbox the end date will be '01.01.2020' and not System time
    this.workflowInCreation.setInEditMode(false);
    this.workflowInCreation.setOwners(getCompanyCodeFromUser());
    if (this.legalEntityValue != null)
      populateAuthorField();
    setWorkflowProcessAtWorkflowCreation();
  }

  public void populateAuthorField() {
    if (sessionHandler.isPartnerUser()) {
      if (perimeter.size() > 1)
        this.workflowInCreation.setAuthor(legalEntityValue);
      else
        this.workflowInCreation.setAuthor(partnerCode);
    }
    else if (sessionHandler.isCompanyUser()) {
      this.workflowInCreation.setAuthor(legalEntityValue);
    }
  }

  public List<SelectItem> getLegalEntityValues() {
    if (company == null)
      return null;
    if (legalEntities.isEmpty()) {
      if (sessionHandler.isPartnerUser() && CollectionUtils.isNotEmpty(perimeter)) {
        for (String p : perimeter)
          legalEntities.add(new SelectItem(p, this.securityService.getPartner(p, company)
              .getFullnameOrName() + " / " + p));

      }
      else if (sessionHandler.isCompanyUser()) {
        Stream<Partner> partnersStream = (this.securityService.getPartners(company)).stream();

        if (CollectionUtils.isNotEmpty(perimeter)) {
          partnersStream = partnersStream.filter(p -> perimeter.contains(p.getCode()));
        }

        legalEntities = partnersStream
            .map(p -> new SelectItem(p.getCode(), p.getFullnameOrName() + " / " + p.getCode()))
            .collect(Collectors.toList());

        if (isDedicatedPortal() && sessionHandler.isCompanyUser()) {
          legalEntities.add(new SelectItem(company.getCode(), company
              .getFullnameOrName() + " / " + company.getCode()));
        }
      }
      Comparator<SelectItem> comparator = new Comparator<SelectItem>() {
        @Override
        public int compare(SelectItem s1, SelectItem s2) {
          return (s1.getLabel())
              .compareToIgnoreCase(s2.getLabel());
        }
      };
      Collections.sort(legalEntities, comparator);
    }
    return legalEntities;
  }

  public boolean isPartnerWithPerimeterOrClient() {
    resolveOrganizationCodes();
    return ((sessionHandler.isPartnerUser() && perimeter.size() > 1) || sessionHandler.isCompanyUser()) ? true : false;
  }

  public boolean isClientWithoutPerimeter() {
    resolveOrganizationCodes();
    return ((sessionHandler.isCompanyUser() && perimeter.size() == 0)) ? true : false;
  }

  public boolean isClientWithPerimeter() {
    resolveOrganizationCodes();
    return ((sessionHandler.isCompanyUser() && perimeter.size() > 0)) ? true : false;
  }

  public void onReadWorkflow(SelectEvent event) {
    setWorkflowProcessReading();
  }

  public void onReadStep() {
    this.readingSteps = true;
    this.selectedWorkflowStepNumber = 0;
    this.selectedWorkflowStep = getCurrentWorkflowStep();
    updateSelectedGroup();
  }

  public void onNextStep() {
    this.selectedWorkflowStepNumber++;
    this.selectedWorkflowStep = getCurrentWorkflowStep();
    updateSelectedGroup();
  }

  private WorkflowStep getCurrentWorkflowStep() {
    return this.selectedWorkflow.getWorkflowSteps()
        .get(this.selectedWorkflowStepNumber);
  }

  public void onPreviousStep() {
    if (this.selectedWorkflowStepNumber == 0) {
      this.readingSteps = false;
      return;
    }
    this.selectedWorkflowStepNumber--;
    this.selectedWorkflowStep = getCurrentWorkflowStep();
    updateUsedCustomActions();
    updateSelectedGroup();
  }

  public void updateSelectedGroup() {
    this.selectedGroup = this.selectedWorkflowStep.getWorkflowStepUsers()
        .stream()
        .map(step -> step.getId())
        .map(stepId -> stepId.getUser())
        .collect(Collectors.toList());
  }

  public List<String> getUsersForEachStep(List<WorkflowStepUser> stepUsers) {
    // this method is called from a ui:repeat from a dialog, but for some reason if we open a different dialog the ui:reapet seems to call
    // this method even though we do not need it so I did this check in order to know if we really are need to do this or not
    if (!isWorkflowProcessReading())
      return null;
    List<String> users = stepUsers.stream()
        .map(step -> step.getId())
        .map(stepId -> stepId.getUser())
        .collect(Collectors.toList());
    return getAllUsers().stream()
        .filter(user -> users.contains((user.getId())))
        .map(user -> user.getFullname())
        .collect(Collectors.toList());
  }

  public void onCancelEditMode() {
    this.readingSteps = false;
    this.selectedWorkflow = null;
    this.selectedWorkflowStep = null;
    this.selectedGroup = null;
    this.possibleUsers = null;
    setWorkflowProcessNone();
  }

  public void onStopReadWorkflow() {
    setWorkflowProcessNone();
  }

  public void onCancelWorkflowCreation(ActionEvent event) {
    onCancelWorkflowCreation();
  }

  public void onCancelWorkflowCreation() {
    this.workflowInCreation = null;
    this.workflowStepInCreation = null;
    this.queryModel = null;
    this.possibleUsers = null;
    setWorkflowProcessNone();
  }

  public void startStepConfiguration() {
    this.workflowStepsConfiguration = true;
    this.workflowInCreationStepNumber = 0;
    if (this.workflowInCreation.getWorkflowSteps()
        .size() > 0) {
      this.workflowStepInCreation = this.workflowInCreation.getWorkflowSteps()
          .get(this.workflowInCreationStepNumber);
      updateSelectedGroupOrExchangeDirectionForWorkflowInCreation();
    }
    else {
      resetWorkflowStepAndActors();
    }
    updateUsedCustomActions();
    setWorkflowProcessAtStepCreation();
  }

  public void updateSelectedGroupOrExchangeDirectionForWorkflowInCreation() {
    exchangeDirection = this.workflowStepInCreation.getExchangeDirection();
    selectedStepFunction = this.workflowStepInCreation.getFunction();
    if (exchangeDirection != null)
      actorTypeSelectedValue = ActorsSelectionType.BY_TYPE.toString();
    if (selectedStepFunction != null)
      actorTypeSelectedValue = ActorsSelectionType.BY_FUNCTION.toString();
    this.selectedGroup = this.workflowStepInCreation.getWorkflowStepUsers()
        .stream()
        .map(step -> step.getId())
        .map(stepId -> stepId.getUser())
        .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(selectedGroup) && exchangeDirection == null && selectedStepFunction == null)
      actorTypeSelectedValue = ActorsSelectionType.BY_NAME.toString();
  }

  public void onPreviousStepCreation() {
    this.workflowInCreationStepNumber--;
    if (this.workflowInCreationStepNumber < 0) {
      this.workflowStepsConfiguration = false;
      setWorkflowProcessAtWorkflowCreation();
    }
    else {
      this.workflowStepInCreation = this.workflowInCreation.getWorkflowSteps()
          .get(this.workflowInCreationStepNumber);
      updateSelectedGroupOrExchangeDirectionForWorkflowInCreation();
      updateUsedCustomActions();
    }
  }

  public void onCreateAnotherStep() {
    createWorkflowStep();
    this.workflowInCreationStepNumber++;
    if (this.workflowInCreation.getWorkflowSteps()
        .size() > this.workflowInCreationStepNumber) {
      this.workflowStepInCreation = this.workflowInCreation.getWorkflowSteps()
          .get(this.workflowInCreationStepNumber);
      updateSelectedGroupOrExchangeDirectionForWorkflowInCreation();
    }
    else {
      workflowStepInCreation = workflowStepInCreation == null ? new WorkflowStep()
          : new WorkflowStep(workflowStepInCreation.getTemplate(), workflowStepInCreation.getReminderTemplate(),
              workflowStepInCreation.getReminderFrequency(), workflowStepInCreation.getNbRemindBeforeNextStep(),
              workflowStepInCreation.getNbRemindBeforeWarning());
      resetActors();
    }
    updateUsedCustomActions();
  }

  public void resetWorkflowStepAndActors() {
    this.workflowStepInCreation = new WorkflowStep();
    resetActors();
  }

  public void resetActors() {
    this.selectedGroup = new ArrayList<>();
    this.exchangeDirection = null;
    this.selectedStepFunction = null;
  }

  public void onEndWorkflowCreation() {
    createWorkflowStep();
    resetActors();
    setModificationData(workflowInCreation);

    if (editMode) {
      try {
        if (replaceWithANewVersionMode) {
          workflowService.prepareAndStoreNewWorkflowVersion(oldDuplicatedWorkflowForANewVersion, workflowInCreation,
              this.getCompanyCodeFromUser(), workflowInCreationStepNumber);
          replaceWithANewVersionMode = false;
          oldDuplicatedWorkflowForANewVersion = null;
        }
        else {
          workflowService.updateWorkflow(workflowInCreation, originalWorkflowSteps, originalWorkflowStepActions,
              workflowInCreationStepNumber);
        }
        if (!workflowInCreation.isWorkflowDisabled()) {
          scheduleReminderForWorkflow(workflowInCreation);
        }
        originalWorkflowSteps = null;
        originalWorkflowStepActions = null;
        editMode = false;
      }
      catch (Exception e) {
        addMessage(null, FacesMessage.SEVERITY_ERROR, LABEL_FAMILY + ".update_workflow_error");
        LOGGER.error("Cannot update workflow: " + e);
        RequestContext.getCurrentInstance()
            .addCallbackParam("validationFailed", true);
      }
    }
    else {
      this.workflowInCreation
          .setSortNumber(this.workflowService.getNextOrderNumberForCompanyId("sortNumber", this.getCompanyCodeFromUser()));
      this.workflowDAO.store(this.workflowDAO.merge(this.workflowInCreation));
      scheduleReminderForWorkflow(workflowInCreation);
    }
    this.workflowInCreation = null;
    this.workflowStepInCreation = null;
    this.possibleUsers = null;
    setWorkflowProcessNone();
  }

  private void createWorkflowStep() {
    boolean updateMode = false;
    // case when new step added
    if (this.workflowStepInCreation.getId() == null) {
      WorkflowStepId workflowStepId = new WorkflowStepId(this.workflowInCreation.getId(), getStepNumber());

      this.workflowStepInCreation.setId(workflowStepId);
    }
    // case when previous then next was done in which the list of users might have changed
    else {
      this.workflowStepInCreation.getWorkflowStepUsers()
          .clear();
      updateMode = true;
    }

    if (ActorsSelectionType.BY_NAME.toString()
        .equals(actorTypeSelectedValue)) {
      List<WorkflowStepUser> users = createStepUsers();
      for (WorkflowStepUser user : users) {
        this.workflowStepInCreation.addWorkflowStepUser(user);
      }
      this.workflowStepInCreation.setExchangeDirection(null);
      this.workflowStepInCreation.setFunction(null);
    }
    // case when actorTypeSelectedValue== "type"
    else {
      if (ActorsSelectionType.BY_FUNCTION.toString()
          .equals(actorTypeSelectedValue)) {
        this.workflowStepInCreation.setFunction(selectedStepFunction);
        this.workflowStepInCreation.setExchangeDirection(null);
      }
      else {
        this.workflowStepInCreation.setExchangeDirection(exchangeDirection);
        this.workflowStepInCreation.setFunction(null);
      }
      this.workflowStepInCreation.setWorkflowStepUsers(new ArrayList<WorkflowStepUser>());
    }

    if (updateMode) {
      this.workflowInCreation.updateWorkflowStepAtIndex(this.workflowInCreationStepNumber, this.workflowStepInCreation);
    }
    else {
      this.workflowInCreation.addWorkflowStep(this.workflowStepInCreation);
    }

    workflowStepInCreation.getCustomActions()
        .clear();
    workflowStepInCreation.getCustomActions()
        .addAll(customActions.stream()
            .filter(action -> selectedActions.get(action.getId()))
            .map(action -> new ActionStepAssociation(action, workflowStepInCreation))
            .collect(toList()));
  }

  public List<ActorsSelectionType> getActorsSelectionType() {
    if (this.workflowInCreation.getAuthor() == null)
      return asList(ActorsSelectionType.values()).stream()
          .filter(type -> !type.equals(ActorsSelectionType.BY_NAME))
          .collect(toList());
    return asList(ActorsSelectionType.values());
  }

  private List<WorkflowStepUser> createStepUsers() {
    List<WorkflowStepUser> users = new ArrayList<>();

    for (String groupId : this.selectedGroup) {
      WorkflowStepUser stepUser = new WorkflowStepUser();

      WorkflowStepUserId stepUserId = new WorkflowStepUserId(this.workflowInCreation.getId(), getStepNumber(), groupId);
      stepUser.setId(stepUserId);

      users.add(stepUser);
    }
    return users;
  }

  public boolean isCustomActionsEmpty() {
    return CollectionUtils.isEmpty(this.customActions);
  }

  public String getConfirmEditMessage() {
    if (workflowInCreationStepNumber == 0)
      return getMessage(LABEL_FAMILY + ".edit_workflow_one_step", "Do you confirm the creation of this one step workflow?",
          selectedLanguage);
    return getMessage(LABEL_FAMILY + ".edit_workflow_multiple_steps", "Do you confirm the creation of this workflow of {0} steps?",
        selectedLanguage, workflowInCreationStepNumber + 1);
  }

  public List<Object> getEnrichActions(Instance instance) {
    if (enrichActions == null) {
      List<TaskType> taskTypes = taskService.getTypes();
      enrichActions = taskTypes.stream()
          .filter(taskType -> StandardInvoiceEditionTask.class.equals(taskType.getType()))
          .map(taskType -> taskService.getTasks(instance, taskType))
          .filter(Objects::nonNull)
          .flatMap(List::stream)
          .filter(action -> !(action instanceof StandardInvoiceEditionTask && !((StandardInvoiceEditionTask) action).isEditInvoice()))
          .collect(toList());
    }
    return enrichActions;
  }

  public int getStepNumber() {
    return this.workflowInCreationStepNumber + 1;
  }

  public List<String> getCollections() {
    return Arrays.asList(WorkflowMessageType.values())
        .stream()
        .map(collection -> JSFHelper.getLabel(LABEL_FAMILY, collection.getLabel(), collection.getLabel(), JSFHelper.getLocale()))
        .collect(Collectors.toList());
  }

  @Override
  public Collection<String> onCompleteQuery(CompleteEvent event) {
    return completeDocumentQuery(event);
  }

  public QueryModel getQueryModel() {
    if (queryModel == null) {
      BeanDescriptor bean = resolveBeanDescriptor(InvoiceIndex.class, Collections.singletonMap(InvoiceIndex.class, "gnxxcblinvlbls"));
      queryModel = createQueryModel(bean, null, null, null, null, null, true, false);
    }
    return queryModel;
  }

  public List<User> getAllUsers() {
    if (CollectionUtils.isEmpty(possibleUsers)) {
      boolean isPartner = legalEntityValue != null && !legalEntityValue.equals(getCompany().getCode());
      possibleUsers = securityService.getUsersFromPartnerAndPartnerInTheirScopeOrFromCompanyByPortalType(
          legalEntityValue != null ? securityService.getPartner(legalEntityValue, getCompany()) : partner, this.getCompany(), isPartner,
          this.isDedicatedPortal());
    }
    return possibleUsers;
  }

  public Workflow getWorkflowInCreation() {
    return workflowInCreation;
  }

  public void setWorkflowInCreation(Workflow workflowInCreation) {
    this.workflowInCreation = workflowInCreation;
  }

  public WorkflowStep getWorkflowStepInCreation() {
    return workflowStepInCreation;
  }

  public void setWorkflowStepInCreation(WorkflowStep workflowStepInCreation) {
    this.workflowStepInCreation = workflowStepInCreation;
  }

  public boolean isWorkflowStepsConfiguration() {
    return workflowStepsConfiguration;
  }

  public void setWorkflowStepsConfiguration(boolean workflowStepsConfiguration) {
    this.workflowStepsConfiguration = workflowStepsConfiguration;
  }

  public boolean getIsBQL() {
    return isBQL;
  }

  public void setIsBQL(boolean isBQL) {
    this.isBQL = isBQL;
  }

  public Integer[] getValues() {
    return values;
  }

  public String valueOf(Integer nb) {
    return Integer.toString(nb);
  }

  public String getTodayAsStringUsingUserPattern() {
    return new SimpleDateFormat(
        EDateFormat
            .findPatternBy(getAuthenticatedUser(),
                com.byzaneo.generix.ui.SessionHandler.DEFAULT_DATE_PATTERN)
            .getPattern()).format(new Date());
  }

  public boolean isWorkflowProcessReading() {
    return this.workflowProcess == WorkflowProcess.READ;
  }

  public void setWorkflowProcessReading() {
    this.workflowProcess = WorkflowProcess.READ;
  }

  public boolean isWorkflowProcessNone() {
    return this.workflowProcess == WorkflowProcess.NONE;
  }

  public void setWorkflowProcessNone() {
    this.workflowProcess = WorkflowProcess.NONE;
  }

  public boolean isWorkflowProcessAtWorkflowCreation() {
    return this.workflowProcess == WorkflowProcess.WORKFLOW_CREATION;
  }

  public void setWorkflowProcessAtWorkflowCreation() {
    this.workflowProcess = WorkflowProcess.WORKFLOW_CREATION;
  }

  public boolean isWorkflowProcessAtStepCreation() {
    return this.workflowProcess == WorkflowProcess.STEPS_CREATION;
  }

  public void setWorkflowProcessAtStepCreation() {
    this.workflowProcess = WorkflowProcess.STEPS_CREATION;
  }

  public boolean isWorkflowProcessInEdition() {
    return this.workflowProcess == WorkflowProcess.EDITION;
  }

  public void setWorkflowProcessInEdition() {
    this.workflowProcess = WorkflowProcess.EDITION;
  }

  public boolean isWorkflowProcessInDisable() {
    return this.workflowProcess == WorkflowProcess.DISABLE;
  }

  public void setWorkflowProcessInDisable(Workflow workflow) {
    this.selectedWorkflow.setEndDate(new Date());
    onSaveDisabledWorkflow();
  }

  public List<String> getSelectedGroup() {
    return selectedGroup;
  }

  public void setSelectedGroup(List<String> selectedGroup) {
    this.selectedGroup = selectedGroup;
  }

  public Workflow getSelectedWorkflow() {
    return selectedWorkflow;
  }

  public void setSelectedWorkflow(Workflow selectedWorkflow) {
    this.selectedWorkflow = selectedWorkflow;
  }

  public int getSelectedWorkflowStepNumber() {
    return selectedWorkflowStepNumber;
  }

  public void setSelectedWorkflowStepNumber(int selectedWorkflowStepNumber) {
    this.selectedWorkflowStepNumber = selectedWorkflowStepNumber;
  }

  public boolean isReadingSteps() {
    return readingSteps;
  }

  public void setReadingSteps(boolean readingSteps) {
    this.readingSteps = readingSteps;
  }

  public WorkflowStep getSelectedWorkflowStep() {
    return selectedWorkflowStep;
  }

  public void setSelectedWorkflowStep(WorkflowStep selectedWorkflowStep) {
    this.selectedWorkflowStep = selectedWorkflowStep;
  }

  public boolean isUserBackupConfiguration() {
    return this.activeTabIndex == 2;
  }

  public boolean isDelegationConfiguration() {
    return this.activeTabIndex == 3;
  }

  private boolean isWorkflow() {
    return this.activeTabIndex == 0;
  }

  public void setSelectedInstance(Instance instance) {
    sessionHandler.setSwitchInstance(instance);
  }

  public List<SelectItem> getComboActorsTypeMode() {
    return getActorsByTypeLabels(LABEL_FAMILY + ".", selectedLanguage);
  }

  /**
   * Based on the UserType values retrieve all the resource label values and map them as SelectItems
   *
   * @param labelPrefix
   * @param language
   * @return
   */
  public List<SelectItem> getActorsByTypeLabels(String labelPrefix, Locale language) {
    List<ExchangeType> types = asList(WorkflowStepUser.ExchangeType.values());
    if (!isDedicatedPortal()) {
      types = types.stream()
          .filter(e -> !e.equals(ExchangeType.OWNER))
          .collect(toList());
    }
    return types.stream()
        .map(t -> new SelectItem(t, getMessage(labelPrefix + t.name()
            .toLowerCase(), WordUtils.capitalize(
                t.name()
                    .toLowerCase()),
            language)))
        .collect(toCollection(ArrayList::new));
  }

  public String getLabel(String text) {
    return JSFHelper.getLabel(LABEL_FAMILY, text.toLowerCase(), text.toLowerCase(), getLocale());
  }

  // TEMPLATES

  public List<Template> getTemplateItems() {
    if (templateItems == null) {
      templateItems = getTemplates(getInstanceCode());
      templateItems.addAll(getTemplates(GLOBAL_TEMPLATES_OWNER));
    }
    return templateItems;
  }

  private List<Template> getTemplates(String owner) {
    return transformService.getTemplates(owner, Transform.ThymeLeaf, null)
        .stream()
        .filter(template -> template.getName()
            .toLowerCase()
            .startsWith("wkf"))
        .collect(Collectors.toList());
  }

  public void onPreviewTemplate(String template) {
    this.previewUrl = null;
    this.selectedTemplate = templateItems
        .stream()
        .filter(t -> t.getName()
            .equals(template))
        .findFirst()
        .orElse(null);
    if (selectedTemplate == null) {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      info(LABEL_FAMILY + ".no_workflow_template");
      return;
    }
    try {
      final FileType outputType = HTML;
      final File previewFile = File.createTempFile("previewTemplate", outputType.getExtension());
      deleteFile(previewFile);
      final User user = getAuthenticatedUser();
      try (final OutputStream output = new FileOutputStream(previewFile);) {
        this.transformService.transform(selectedTemplate, outputType, output, getLocale(), getInstance(), user.getPrimaryGroup(), user,
            null);
      }
      this.previewUrl = createResourceRequestPath(
          previewFile, "previewTemplate",
          outputType.getDefaultMime());
    }
    catch (Exception e) {
      error(e, "msglbls.error_templating_message", getRootCauseMessage(e));
    }
  }

  public void onChangeReminderTemplate(AjaxBehaviorEvent event) {
    this.selectedReminderTemplate = (String) ((SelectOneMenu) event.getSource()).getSubmittedValue();
  }

  private boolean hasStepsWithReminderTemplate(Workflow workflow) {
    return workflow.getWorkflowSteps()
        .stream()
        .anyMatch(step -> step.getReminderTemplate() != null);
  }

  private void scheduleReminderForWorkflow(Workflow workflow) {
    try {
      if (hasStepsWithReminderTemplate(workflow))
        wkfReminderSchedulerService.scheduleJob(workflow, getInstance());
    }
    catch (SchedulerException e) {
      LOGGER.error("Cannot schedule reminder for workflow: " + e);
    }
  }

  public void setLegalEntityValue(String legalEntityValue) {
    this.legalEntityValue = legalEntityValue;
  }

  public String getLegalEntityValue() {
    return legalEntityValue;
  }

  // WORKFLOW FUNCTIONS

  public LazyDataModel<WorkflowFunction> getWorkflowFunctionDataModel() {
    try {
      if (this.activeTabIndex == 1) {
        return workflowFunctionDataModel == null
            ? workflowFunctionDataModel = resolveWorkflowFunctionDataModel()
            : workflowFunctionDataModel;
      }
      else {
        return emptyDataModel();
      }
    }
    catch (Exception e) {
      error(e, "xtdtsklbls.error_data_model", e.getMessage());
      return emptyDataModel();
    }
  }

  public LazyDataModel<WorkflowFunction> resolveWorkflowFunctionDataModel() {
    return new WorkflowFunctionDataModel(workflowFunctionService, resolveWorkflowFunctionSearchQuery());
  }

  public void updateFunctionTable() {
    this.workflowFunctionDataModel = null;
  }

  public Query resolveWorkflowFunctionSearchQuery() {
    QueryBuilder qb = QueryBuilder.createBuilder();
    qb.and(equal("owners", getCompanyCodeFromUser()))
        .or(equal("author", workflowFunctionEntity), equal("author", null));
    return qb.query();
  }

  public void onAddFunction() {
    this.selectedWorkflowFunction = new WorkflowFunction();
    selectedWorkflowFunction.setOwners(getCompanyCodeFromUser());
    if (workflowFunctionEntity != null) {
      if (sessionHandler.isPartnerUser()) {
        this.selectedWorkflowFunction.setAuthor(perimeter.size() > 1 ? workflowFunctionEntity : partnerCode);
      }
      else if (sessionHandler.isCompanyUser()) {
        this.selectedWorkflowFunction.setAuthor(workflowFunctionEntity);
      }
    }
  }

  public void onEditFunction(WorkflowFunction function) {
    this.selectedWorkflowFunction = function;
  }

  public void onPopulateFunction(WorkflowFunction function) {
    this.workflowFunctionUserEntity = null;
    this.selectedWorkflowFunction = function;
    if (this.workflowFunctionEntity != null) {
      JSFHelper.resetDataTable("tskForm-workflowManagementView-workflowTabs-functionUsersForm-functionUserTable");
      updateSelectedFunctionUsers(selectedWorkflowFunction);
      this.functionUsers = getUsersByEntityCode(workflowFunctionEntity);
    }
    else
      this.notRenderFunctionUsers = true;
  }

  public void onSaveFunction() {
    this.workflowFunctionService.saveFunction(selectedWorkflowFunction);
  }

  public void onSaveFunctionActors() {
    if (notRenderFunctionUsers)
      return;
    String entityCode = workflowFunctionUserEntity != null ? workflowFunctionUserEntity : workflowFunctionEntity;
    List<WorkflowFunctionUser> functionUsers = selectedWorkflowFunction.getFunctionUsers();
    List<User> users = functionUsers.stream()
        .filter(u -> u.getAuthor()
            .equals(entityCode))
        .map(u -> u.getUser())
        .collect(toList());
    if (selectedFunctionUsers == null || selectedFunctionUsers.length < 1) {
      selectedFunctionUsers = users.toArray(new User[users.size()]);
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      FacesContext.getCurrentInstance()
          .addMessage("populateFunction",
              new FacesMessage(FacesMessage.SEVERITY_ERROR, getMessage(LABEL_FAMILY + ".delete_functionUsers_msg",
                  "A function cannot be empty. Please select at least one name", this.selectedLanguage), ""));
      return;
    }

    // create function users based on selected partners
    for (User user : Arrays.asList(this.selectedFunctionUsers)) {
      if (!users.contains(user)) {
        WorkflowFunctionUser workflowFunctionUser = new WorkflowFunctionUser(selectedWorkflowFunction, user, entityCode);
        functionUsers.add(workflowFunctionUser);
      }
    }
    // remove unselected function users
    for (int i = 0; i < functionUsers.size(); i++) {
      WorkflowFunctionUser user = functionUsers.get(i);
      if (!Arrays.asList(this.selectedFunctionUsers)
          .contains(user.getUser()) && user.getAuthor()
              .equals(entityCode)) {
        functionUsers.remove(user);
        user.setFunction(null);
        i--;
      }
    }
    this.workflowFunctionService.saveFunction(selectedWorkflowFunction);
    if (workflowFunctionUserEntity != null)
      this.notRenderFunctionUsers = true;
    info(LABEL_FAMILY + ".populate_function_msg");
    FacesContext.getCurrentInstance()
        .addMessage("populateFunction", new FacesMessage(FacesMessage.SEVERITY_INFO,
            getMessage(LABEL_FAMILY + ".populate_function_msg", "Actor(s) for function saved", this.selectedLanguage), ""));
  }

  public void onCancelCreationFunction() {
    this.selectedWorkflowFunction = null;
  }

  public void onCancelPopulateFunction() {
    this.selectedFunctionUsers = null;
    this.workflowFunctionUserEntity = null;
    this.notRenderFunctionUsers = false;
    this.functionUsers = null;
    this.selectedWorkflowFunction = null;
  }

  public boolean renderWorkflowTabs() {
    return sessionHandler.isGranted(Resource.Workflow_Management, Right.READ) ||
        sessionHandler.isGranted(Resource.Workflow_Functions, Right.READ) ||
        sessionHandler.isGranted(Resource.Workflow_Absences, Right.READ) ||
        sessionHandler.isGranted(Resource.Workflow_Delegations, Right.READ) ||
        sessionHandler.isGranted(Resource.Workflow_Monitoring, Right.READ);
  }

  private void updateSelectedFunctionUsers(WorkflowFunction workflowFunction) {
    List<User> users = workflowFunction.getFunctionUsers()
        .stream()
        .filter(f -> f.getAuthor()
            .equals(workflowFunctionUserEntity != null ? workflowFunctionUserEntity : workflowFunctionEntity))
        .map(u -> u.getUser())
        .collect(toList());
    selectedFunctionUsers = !users.isEmpty() ? users.toArray(size -> new User[users.size()]) : null;
  }

  public void onChangeFunctionUserEntity(AjaxBehaviorEvent event) {
    this.workflowFunctionUserEntity = (String) ((SelectOneMenu) event.getSource()).getSubmittedValue();
    this.selectedWorkflowFunction = this.workflowFunctionService.getWorkflowFunctionById(this.selectedWorkflowFunction.getId());
    if (!StringUtils.isEmpty(workflowFunctionUserEntity)) {
      this.notRenderFunctionUsers = false;
      updateSelectedFunctionUsers(selectedWorkflowFunction);
      this.functionUsers = getUsersByEntityCode(workflowFunctionUserEntity);
    }
    else
      notRenderFunctionUsers = true;
    JSFHelper.resetDataTable("tskForm-workflowManagementView-workflowTabs-functionUsersForm-functionUserTable");
  }

  public Group getPrimaryGroupOfUser(User user) {
    if (user.getPrimaryGroup() == null) {
      Group group = this.securityService.getPrimaryGroupOfUser(user)
          .orElse(null);
      if (group != null)
        return group;
    }
    return user.getPrimaryGroup();
  }

  private List<User> getUsersByEntityCode(String entityCode) {
    boolean isPartner = !entityCode.equals(getCompany().getCode());
    List<User> users = securityService.getUsersFromPartnerAndPartnerInTheirScopeOrFromCompanyByPortalType(
        securityService.getPartner(entityCode, getCompany()), getCompany(), isPartner, this.isDedicatedPortal());
    return users;
  }

  public int getFunctionActorsNb(WorkflowFunction function) {
    return (int) (function.getAuthor() == null && workflowFunctionEntity != null ? function.getFunctionUsers()
        .stream()
        .filter(fu -> this.workflowFunctionEntity.equals(fu.getAuthor()))
        .map(fu -> fu.getUser())
        .count()
        : function.getFunctionUsers()
            .size());
  }

  public void onTabChange(TabChangeEvent event) {
    TabView tv = (TabView) event.getComponent();
    this.activeTabIndex = tv.getActiveIndex();
    search = null;
    userQuery = null;
    userQueryQuickSearch = null;
    workflowDataModel = null;
    userBackupAndDelegationDataModel = null;
    workflowFunctionDataModel = null;
    functionsForSteps = null;
    descriptor = null;
    renderedSearchProperties = null;

  }

  public void onCloseAbsence(CloseEvent event) {
    initAbsenceDelegation(selectedAbsence, AbsenceType.ABSENCE);
  }

  public void onCloseDelegation(CloseEvent event) {
    initAbsenceDelegation(selectedDelegation, AbsenceType.DELEGATION);
  }

  public void onSelectCheckbox(SelectEvent event) {
    List<User> totalSelectedIndexes = new ArrayList<>(
        selectedFunctionUsers != null ? Arrays.asList(selectedFunctionUsers) : Arrays.asList(new User[0]));
    totalSelectedIndexes.add((User) event.getObject());
    selectedFunctionUsers = totalSelectedIndexes.toArray(new User[totalSelectedIndexes.size()]);
  }

  public void onUnselectCheckbox(UnselectEvent event) {
    List<User> totalSelectedIndexes = new ArrayList<>(
        selectedFunctionUsers != null ? Arrays.asList(selectedFunctionUsers) : Arrays.asList(new User[0]));
    totalSelectedIndexes.remove((User) event.getObject());
    selectedFunctionUsers = totalSelectedIndexes.toArray(new User[totalSelectedIndexes.size()]);
  }

  public User[] getMultiSelectedFunctionUsers() {
    return selectedFunctionUsers;
  }

  public void setMultiSelectedFunctionUsers(User[] selectedFunctionUsers) {
    this.selectedFunctionUsers = selectedFunctionUsers;
  }

  // STEP FUNCTIONS

  public List<WorkflowFunction> getWorkflowFunctionsForSteps() {
    if (this.activeTabIndex == 0 && CollectionUtils.isEmpty(this.functionsForSteps)) {
      this.functionsForSteps = legalEntityValues != null
          ? workflowFunctionService.getDedicatedAndGenericFunctions(legalEntityValues, workflowInCreation.getOwners())
          : workflowFunctionService.getGenericFunctions(workflowInCreation.getOwners());
    }
    return functionsForSteps;
  }

  public Converter getWorkflowFunctionConverter() {
    return new WorkflowFunctionConverter(this.getFunctionsForSteps());
  }

  // DELETE FUNCTIONS

  public void onDeleteFunction(WorkflowFunction function) {
    this.selectedWorkflowFunction = this.workflowFunctionService.getWorkflowFunctionById(function.getId());
    List<Workflow> workflows = this.workflowService.getWorkflowsByFunction(function);
    if (!workflows.isEmpty()) {
      if (workflows.size() == 1) {
        String nameAndDesc = workflows.get(0)
            .getName();
        addMessage(FacesMessage.SEVERITY_ERROR, getMessage(LABEL_FAMILY + ".delete_functionWorkflow_error",
            "It is not possible to delete this function because it is still used by the workflow " + nameAndDesc, this.selectedLanguage,
            nameAndDesc));
      }
      else {
        List<String> workflowNamesAndDesc = new ArrayList<>();
        for (int i = 0; i < (workflows.size() > 10 ? 10 : workflows.size()); i++) {
          workflowNamesAndDesc.add(workflows.get(i)
              .getName());
        }
        String namesAndDesc = workflowNamesAndDesc.stream()
            .collect(Collectors.joining(", "));
        addMessage(FacesMessage.SEVERITY_ERROR,
            getMessage(LABEL_FAMILY + ".delete_functionWorkflows_list_error",
                "It is not possible to delete this function because it is still used by the following workflows: " + namesAndDesc,
                this.selectedLanguage, namesAndDesc));
      }
    }
    else if (this.selectedWorkflowFunction.getFunctionUsers()
        .isEmpty()) {
          deleteFunction();
        }
    else {
      this.confirmFunctionDeletion = true;
      RequestContext.getCurrentInstance()
          .addCallbackParam("openDeletionConfirmationDlg", true);
    }
  }

  public String getConfirmFunctionDeletionMsg() {
    if (!confirmFunctionDeletion)
      return null;
    List<String> entityCodes = this.selectedWorkflowFunction.getFunctionUsers()
        .stream()
        .map(fu -> fu.getAuthor())
        .collect(Collectors.toList());
    if (!entityCodes.isEmpty() && company != null) {
      Map<String, String> groupsFullName = this.securityService.getPartnersFullNameByCodes(company, entityCodes);
      if (entityCodes.contains(company.getCode()))
        groupsFullName.put(company.getCode(), company.getFullnameOrName());
      List<String> groupsCodes = new ArrayList<>(groupsFullName.keySet());
      StringBuilder warningReport = new StringBuilder();
      if (groupsCodes.size() == 1) {
        String groupNameAndCode = groupsFullName.get(groupsCodes.get(0)) + "/" + groupsCodes.get(0);
        if (this.sessionHandler.isPartnerUser() || this.selectedWorkflowFunction.getAuthor() != null) {
          return getMessage(LABEL_FAMILY + ".delete_functionUsers_partner_warn",
              "Warning! This function is assigned to at least one entity. Do you confirm that you want to delete the function?",
              this.selectedLanguage);
        }
        else {
          warningReport.append(getMessage(LABEL_FAMILY + ".delete_functionUsers_customer_warn",
              "Warning! This function is assigned to entity " + groupNameAndCode, this.selectedLanguage, groupNameAndCode));
          warningReport.append("\n");
        }
      }
      else {
        warningReport.append(getMessage(LABEL_FAMILY + ".delete_functionUsers_list_customer_warn",
            "Warning! This function is assigned to one or more entities, including: ", this.selectedLanguage));
        warningReport.append("\n");
        int size = groupsCodes.size();
        for (int i = 0; i < (size > 10 ? 10 : size); i++) {
          String groupNameAndCode = groupsFullName.get(groupsCodes.get(i)) + "/" + groupsCodes.get(i);
          warningReport.append(groupNameAndCode + "\n");
        }
        if (size > 10) {
          warningReport.append("...");
          warningReport.append("\n");
        }
      }
      warningReport.append(getMessage(LABEL_FAMILY + ".delete_function_confirmation",
          "Do you confirm that you want to delete the function? ", this.selectedLanguage));
      return warningReport.toString();
    }
    return null;
  }

  public void deleteFunction() {
    this.workflowFunctionService.deleteFunction(selectedWorkflowFunction);
    addMessage(FacesMessage.SEVERITY_INFO, LABEL_FAMILY + ".delete_function_info", selectedWorkflowFunction.getName());
    this.confirmFunctionDeletion = false;
  }

  public void onCancelDeleteFunction() {
    this.selectedWorkflowFunction = null;
    this.confirmFunctionDeletion = false;
  }

}
