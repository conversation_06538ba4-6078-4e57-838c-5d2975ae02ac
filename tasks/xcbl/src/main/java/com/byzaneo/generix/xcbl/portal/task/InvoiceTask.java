package com.byzaneo.generix.xcbl.portal.task;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.faces.event.CompleteEvent;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.generix.api.DocumentCompoundType;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.bean.ReconciliationDifferences.Footer.Difference;
import com.byzaneo.generix.bean.ReconciliationDifferences.SpecificLines.Line;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.bean.xcbl.XcblPath;
import com.byzaneo.generix.edocument.exception.TemplateMissingException;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.generix.rtemachine.repository.VariablesOperations;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.bean.Tax;
import com.byzaneo.generix.service.repository.service.RepositoryService;
import com.byzaneo.generix.ui.*;
import com.byzaneo.generix.ui.event.data.UploadData;
import com.byzaneo.generix.xcbl.portal.task.bean.*;
import com.byzaneo.generix.xcbl.portal.task.converter.*;
import com.byzaneo.generix.xcbl.portal.task.util.*;
import com.byzaneo.generix.xcbl.ui.edition.*;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.generix.xtrade.task.*;
import com.byzaneo.generix.xtrade.util.*;
import com.byzaneo.portal.bean.Portal;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.ui.handler.SessionHandler;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.api.TaskType;
import com.byzaneo.task.shadow.Shadowable;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.Document.*;
import com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction;
import com.byzaneo.xtrade.ipm.bean.Deployment;
import com.byzaneo.xtrade.ipm.service.ProjectService;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import com.google.gson.JsonParseException;
import com.google.gson.annotations.JsonAdapter;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.component.selectmanycheckbox.SelectManyCheckbox;
import org.primefaces.component.selectoneradio.SelectOneRadio;
import org.primefaces.context.RequestContext;
import org.primefaces.event.*;
import org.primefaces.model.*;
import org.primefaces.model.menu.*;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;

import javax.annotation.*;
import javax.faces.application.FacesMessage;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.event.*;
import javax.faces.model.SelectItem;
import javax.faces.validator.Validator;
import javax.persistence.PreRemove;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;
import java.io.File;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.*;
import java.util.stream.*;

import static com.byzaneo.commons.bean.FileType.ARCHIVE;
import static com.byzaneo.commons.bean.FileType.ERROR;
import static com.byzaneo.commons.bean.FileType.EXCEL;
import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.bean.FileType.isType;
import static com.byzaneo.commons.ui.FileResourceHandler.createResourceRequestPath;
import static com.byzaneo.commons.ui.util.JSFHelper.getFacesContext;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.JSFHelper.getRequest;
import static com.byzaneo.commons.ui.util.JSFHelper.getRequestURL;
import static com.byzaneo.commons.ui.util.JSFHelper.sendFile;
import static com.byzaneo.commons.ui.util.MessageHelper.warn;
import static com.byzaneo.commons.util.BeanDescriptorHelper.getPropertyValue;
import static com.byzaneo.commons.util.FileHelper.deleteFile;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.api.Variable.portlet_action;
import static com.byzaneo.generix.api.Variable.portlet_cause;
import static com.byzaneo.generix.edocument.ui.DocumentViewerHandler.toURI;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.addAllowOrChargeLine;
import static com.byzaneo.generix.edocument.util.ApplicationResponseXcblHelper.getW3cDocFromDocument;
import static com.byzaneo.generix.edocument.util.ApplicationResponseXcblHelper.resolvePaths;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.USER_RECORD;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getXcblInvoiceFile;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initInvoiceWithASN;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initInvoiceWithOrderStandard;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.initNewInvoice;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Add;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Diagnostic;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Export;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Export_List;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Forced;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Import;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Join;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Print;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Refused_Manually;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_Remove;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_View_Attachment;
import static com.byzaneo.generix.service.SecurityService.Resource.Portlet_Invoice_View_History;
import static com.byzaneo.generix.service.SecurityService.Resource.Workflow_Monitoring;
import static com.byzaneo.generix.service.TransformService.BirtOutoutFileType;
import static com.byzaneo.generix.service.TransformService.GLOBAL_TEMPLATES_OWNER;
import static com.byzaneo.generix.service.TransformService.Transform;
import static com.byzaneo.generix.ui.ApplicationHandler.getApplicationHandler;
import static com.byzaneo.generix.ui.FileUploadDialogHandler.openFileUploadDialog;
import static com.byzaneo.generix.util.DocumentCompoundTypeHelper.fromJson;
import static com.byzaneo.generix.util.DocumentConsultStatusHelper.isOkForConsultStatusUpdate;
import static com.byzaneo.generix.util.FormPageHelper.createContext;
import static com.byzaneo.generix.util.FormPageHelper.getFormPageTemplateByName;
import static com.byzaneo.generix.util.ToolsHelper.matchesUserFilter;
import static com.byzaneo.generix.util.ToolsHelper.removeWorkflowData;
import static com.byzaneo.generix.util.ToolsHelper.updateWorkflowPropertiesOnInvoiceIndex;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.PREDICATE_APPRSP_DOF;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.PREDICATE_INVOICE_DOF;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.PREDICATE_INVOICE_XCBL;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getAttachedFiles;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.resolveAppRspDof;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.resolveBirtFile;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.toStageItems;
import static com.byzaneo.portal.util.PortalHelper.findFirstPageByPortletContentId;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.security.api.Right.CREATE;
import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static com.byzaneo.xtrade.api.DocumentStage.CORRECT;
import static com.byzaneo.xtrade.api.DocumentStage.UNDEFINED;
import static com.byzaneo.xtrade.api.DocumentStatus.REFUSED;
import static com.byzaneo.xtrade.api.DocumentStatus.REFUSED_MANUALLY;
import static com.byzaneo.xtrade.api.DocumentStatus.TO_CORRECT;
import static com.byzaneo.xtrade.api.DocumentType.DESADV;
import static com.byzaneo.xtrade.api.DocumentType.ORDERS;
import static com.byzaneo.xtrade.api.DocumentType.ORDRSP;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.ATTACHFILE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.COMMENT;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.CUSTOM_ACTION;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.END;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.FIX;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.LOCK;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.PAUSE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.REDIRECT;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.REFUSE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.TASK_CANCEL;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.UNLOCK;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.VALID;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.WRITE_ASYNC;
import static com.byzaneo.xtrade.util.DocumentHelper.getFirstPDFFile;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static com.byzaneo.xtrade.util.FreeTextHelper.createFreeTextProperty;
import static com.byzaneo.xtrade.util.FreeTextHelper.getFreeTextIndexes;
import static com.byzaneo.xtrade.util.ReconciliationHelper.isOnlyQuantityConditionChecked;
import static com.byzaneo.xtrade.util.ReconciliationHelper.isQuantityConditionChecked;
import static com.byzaneo.xtrade.util.WorkflowHelper.getWorkflowDocumentUsersIdsByStep;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceNumber;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.COMMERCIAL_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.CREDIT_NOTE_GOODS_AND_SERVICES;
import static java.lang.Long.valueOf;
import static java.lang.String.valueOf;
import static java.nio.charset.Charset.defaultCharset;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.lang.StringUtils.isBlank;
import static org.apache.commons.lang3.CharEncoding.UTF_8;
import static org.apache.commons.lang3.LocaleUtils.toLocale;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.springframework.util.Assert.notNull;

/**
 * Allows to display the Invoice Manager documents.
 *
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Byzaneo
 * @date Nov 21, 2014
 * @since 3.0 GNX-1726
 */
@Task(name = "Invoice", library = "portal/xcbl", version = "1.0.0")
@TaskViewModel(labelFamily = InvoiceTask.LABEL_FAMILY)
public class InvoiceTask extends ActionModuleClass<InvoiceIndex> implements Shadowable, InvoiceCategory {

  private List<String> selectedSecurityFields = new ArrayList<>();
  private List<SelectItem> availableSecurityFields;

  private static final long serialVersionUID = 2353126110174782960L;

  private final static Logger LOGGER = LoggerFactory.getLogger(InvoiceTask.class);

  public static final String LABEL_FAMILY = "gnxxcblinvlbls";

  public static final String IN_PROGRESS = "IN_PROGRESS";

  private static final int ZIP_SIZE_TRIGGER = 50 * 1024 * 1024;

  public transient static final String[] DEFAULT_PARTY = { "BillTo", "Buyer", "Seller", "ShipTo" };

  private transient static final List<DocumentStage> UNCLICKABLE_STAGES = Arrays.asList(UNDEFINED, CORRECT, DocumentStage.ERROR);

  public transient static final int MAX_FILE_SIZE_BYTES = Integer.MAX_VALUE;

  public transient static final int MAX_FILE_SIZE_MB = MAX_FILE_SIZE_BYTES / 1024 / 1024;

  private transient static final String TAX_FORMAT = "%.0" + TAX_PRECISION + "f";

  public transient static final String DBERRORACTION = "db errors";

  private final Integer TRIGGER_ACTION_MAX_INVOICES = 100;

  private Order orderLinked;

  private transient OrderResponse ordRspLinked;

  private boolean managePDFCache = false;

  protected Range<Integer> invoiceDocumentExportRange = new Range<>(0, 500);

  private boolean showOnlyWorkflow = false;

  private transient Invoice invoiceXcbl;

  private transient String comment;

  @Autowired
  @Qualifier(DocumentViewerService.SERVICE_NAME)
  private transient DocumentViewerService documentViewerService;

  @Autowired
  @Qualifier(ProjectService.SERVICE_NAME)
  protected transient ProjectService projectService;

  @Autowired
  @Qualifier(RepositoryService.SERVICE_NAME)
  private transient RepositoryService repositoryService;

  @Autowired
  @Qualifier(WorkflowDocumentStatusService.SERVICE_NAME)
  private transient WorkflowDocumentStatusService workflowDocumentStatusService;

  @Autowired
  @Qualifier(WorkflowUserBackupService.SERVICE_NAME)
  private transient WorkflowUserBackupService workflowUserBackupService;

  @Autowired
  @Qualifier(DocumentTimelineService.SERVICE_NAME)
  private transient DocumentTimelineService documentTimelineService;

  @Autowired
  @Qualifier(WorkflowNotificationService.SERVICE_NAME)
  private transient WorkflowNotificationService workflowNotificationService;

  @Autowired
  private transient OrderService orderService;

  @Autowired
  @Qualifier(WorkflowStepRedirectService.SERVICE_NAME)
  private transient WorkflowStepRedirectService workflowStepRedirectService;

  @Autowired
  @Qualifier(WorkflowBusinessService.SERVICE_NAME)
  private transient WorkflowBusinessService workflowBusinessService;

  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private transient ConfigurationService configService;

  @Autowired
  @Qualifier(ReconciliationService.SERVICE_NAME)
  private transient ReconciliationService reconciliationService;

  @Autowired
  @Qualifier(ReconciliationRelationService.SERVICE_NAME)
  private transient ReconciliationRelationService recRelationService;

  @Autowired
  @Qualifier(InvoiceService.SERVICE_NAME)
  private transient InvoiceService invoiceService;

  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private transient InstanceService instanceService;

  @Autowired(required = false)
  @Qualifier(DocumentErrorService.SERVICE_NAME)
  private transient DocumentErrorService documentErrorService;

  @Autowired
  @Qualifier(RossumService.SERVICE_NAME)
  private transient RossumService rossumService;

  @Autowired(required = false)
  @Qualifier(VariablesOperations.OPERATIONS_NAME)
  private transient VariablesOperations variablesOperations;

  private transient List<String> availableInvoiceTypeCoded;

  private transient boolean newInvoice;

  private transient boolean lockForMe;

  private transient int activeIndex;

  private transient List<InvoicePartyCodedType> listOfPartyCoded;

  private transient Map<String, Tax> listCodeTva;

  private transient Map<String, IndicatorCodeType> listOfTaxLine;

  private transient InvoiceItemDetailType product;

  private transient List<Report> reports;

  private transient XcblEditionBuilder.XcblEdition xcblEdition;

  private transient List<InvoiceDifferenceLine> listOfDifferences;

  private transient ReconciliationDifferences reconciliationDiferences;

  private transient boolean allDifferences;

  private transient List<XcblPath> pathesDiagnose;

  private transient InvoiceLifeCycle lifeCycle;

  // Status
  @JsonAdapter(DocumentStatusEntityConverter.class)
  private List<DocumentStatusEntity> statusesForInvoiceModification;

  @JsonAdapter(DocumentStatusEntityConverter.class)
  private List<DocumentStatusEntity> statusesForInvoiceOcrVerify;

  /*
   * Beware that it's highly likely that any field added after 'modeCorrectInv' will break the tests on InvoiceTaskTest. It is an issue with
   * PowerMock and Mockito, most probably on the Comparator that they use in PropertyAndSetterInjection. If the InvoiceTaskTest fails just
   * by adding new field in this class, change the position of those fields.
   */

  private transient boolean modeCorrectInv = false;

  private transient boolean modeDiagnoseInv = false;

  private transient boolean modeForceInv = false;

  /**
   * Data descriptor
   */
  private transient BeanDescriptor dataModelDescriptor;

  private transient boolean editInvoiceInError = false;

  private transient InvoiceIndex targetInvoiceForAttachedFile;

  private boolean countEnabled = false;

  private transient InvoicePartyCodedType selectedParty;

  private transient SessionHandler sessionHandler;

  private int detailPrecision = 2;

  private int footerPrecision = 2;

  private transient String floatColumnPattern;

  private String cause;

  private transient String queryFromUserRoles;

  // data
  protected transient List<ProcessReportExecution> executions;

  // Correction
  private DocumentFile dofInvoiceCorrection;

  private DocumentFile dofAppRsp;

  private transient List<Object> actions;

  private transient List<Object> invoiceComplianceAndControlActions;

  private transient List<Object> allActions;

  private transient List<Object> actionsCreation;

  private String nextPortletPageCreation;

  private Long actionCreateId;

  private transient Boolean isInvoicingEnabled = null;

  private transient List<DocumentTimeline> invoiceTimelines;

  private transient boolean showTimeline = false;

  private transient boolean showCommentBlock = false;

  private static final int timelineCommentLimit = 100;

  // used in order to know if the timeline comments should be displayed fully or truncate them to "timelineCommentLimit"
  private transient Map<Long, Boolean> timelineTruncatedComments;

  // this list contains current users and all users which have as backup the current user (
  private transient List<User> currentUserAndBackedupUsersByCurrentUser;

  // this list contains all users which are backup for the current user
  private transient List<String> allBackupUsersForCurrentUser;

  /**
   * we need to diferentiate ABSENCE/DELEGATION (null means ABSENCE) this list contains current user and all users ("delegators") which have
   * "delegated" the current user
   */
  private transient List<User> currentUserAndDelegatorUsersForCurrentUser;
  // this list contains all users which are "delegated" for the current user (current user = "delegator")
  private transient List<String> allDelegatedUsersForCurrentUser;

  private transient List<InvoiceIndex> reconciliationPreviousInvoices;

  private transient List<ReceptionIndex> reconciliationReceptions;

  private transient Map<Long, String> fileUriMap = new HashMap<>();

  /**
   * if the selected invoice has an attached workflow and the user that should do the workflow steps (the delegator user) has delegated his
   * tasks to another user (delegated user) then the delegated user is the current user and we need to know the delegator in order to write
   * his name into the tml_timeline table
   */
  private transient User delegatorUserForSelectedInvoice = null;

  private transient WorkflowStep workflowStep;

  private transient boolean showAttach;

  private transient String workflowRedirectPage;

  private transient WorkflowStepRedirect workflowStepRedirect;

  private transient WorkflowStepRedirect cancelWorkflowStepRedirect;

  private transient List<Object> enrichActions;

  private transient String redirectUserComment;

  private transient List<PropertyDescriptor> unrenderedColumns;

  private String nextPortletPage;

  private transient boolean hasWorkflowAttached;
  private transient DefaultMenuModel viewerMenuModel;

  private Long portalDetailId;

  @Getter
  private transient CustomAction[] stepCustomActions;
  @Getter
  private transient CustomAction customAction;

  @Getter
  @Setter
  private transient String invoiceCurrency;

  @Getter
  @Setter
  private transient ReconciliationRelation reconciliationRelation;

  @Getter
  @Setter
  // used to know all invoices associated to a workflow for which the current user is an actor
  private transient List<Long> workflowActionsOnInvoicesForUser = new ArrayList<Long>();

  private transient boolean hasAuthenticationChannelAccessTokenV1ForRossum;

  @Getter
  @Setter
  private transient Set<User> nextWkfStepUsers = new HashSet<>();

  @Getter
  @Setter
  private transient User[] selectedNextStepUsers;

  @Getter
  @Setter
  private transient boolean designateNextStepUsers;
  
  @Getter
  @Setter
  private transient WorkflowStep nextWorkflowStep;

  public InvoiceTask() {
    super();

  }

  public List<String> getSelectedSecurityFields() {
    return selectedSecurityFields;
  }

  public void setSelectedSecurityFields(List<String> selectedSecurityFields) {
    this.selectedSecurityFields = selectedSecurityFields;
  }


  public List<SelectItem> getAvailableSecurityFields() {
    return availableSecurityFields;
  }





  @PostConstruct
  public void init() {
    initAvailableSecurityFieldsList(portletFlowDirection);
    if(this.selectedSecurityFields != null && this.selectedSecurityFields.isEmpty()) {
      initSelectedSecurityFieldsList();
    }


    if (FacesContext.getCurrentInstance() != null) {
      this.sessionHandler = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
      Group group = this.sessionHandler.getPrimaryGroup();
      Partner partner = null;
      Company company = null;
      if (group instanceof Partner) {
        partner = (Partner) group;
        company = (Company) partner.getParent();
      }
      else if (group instanceof Company) {
        company = (Company) group;
      }

      if (variablesOperations != null) {
        this.hasAuthenticationChannelAccessTokenV1ForRossum = variablesOperations.hasAuthenticationChannelAccessTokenV1ForUse(
            AccessTokenV1Use.ROSSUM,
            partner, company);
      }
    }
    this.executorService = getBean(ExecutorService.class, ExecutorService.SERVICE_NAME);
  }

  private void initSelectedSecurityFieldsList() {
    this.selectedSecurityFields.add(FIELD_THIRD_PARTY);
    if("SENDING".equals(this.portletFlowDirection)) {
      this.selectedSecurityFields.add(FIELD_FROM);
    } else if("RECEIVING".equals(this.portletFlowDirection)) {
      this.selectedSecurityFields.add(FIELD_TO);
    } else {
      this.selectedSecurityFields.add(FIELD_FROM);
      this.selectedSecurityFields.add(FIELD_TO);
    }
  }

  /* -- LIFE CYCLE -- */

  /**
   * @see AbstractTask#reset()
   */
  @Override
  @PreDestroy
  public void reset() {
    super.reset();
    resetEdition();
  }

  /**
   * @see AbstractXcblDocumentTask#preRemove()
   */
  @Override
  @PreRemove
  public final void preRemove() {
    super.preRemove();
  }

  public void resetEdition() {
    birtFile = null;
    files = null;
    document = null;
    invoiceXcbl = null;
    editInvoiceInError = false;
    modeCorrectInv = false;
    modeDiagnoseInv = false;
    modeForceInv = false;
    xcblEdition = null;
    pathesDiagnose = null;
    dofInvoiceCorrection = null;
    dofAppRsp = null;
    reports = null;
    cause = null;
  }

  /* -- OVERRIDE -- */

  /**
   * @see AbstractXcblDocumentTask#getEDocument()
   */
  @Override
  public EDocument getEDocument() {
    return EDocument.Invoice;
  }

  public void resetAvailableSecurityFields(AjaxBehaviorEvent event) {
    String currentFlowDirection =(String) ((SelectOneRadio) event.getSource()).getValue();
    initAvailableSecurityFieldsList(currentFlowDirection);
    this.selectedSecurityFields.clear();
    initSelectedSecurityFieldsList();
  }

  public void onChangeCurrentFlowDirection(AjaxBehaviorEvent event) {
    String currentFlowDirection = (String) ((SelectOneRadio) event.getSource()).getValue();
    if ("SENDING".equals(currentFlowDirection)) {
      this.selectedSecurityFields.remove(FIELD_TO);
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_TO))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(true));
      ;
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_FROM))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));
      ;
    }
    else if ("RECEIVING".equals(this.portletFlowDirection)) {
      this.selectedSecurityFields.remove(FIELD_FROM);
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_FROM))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(true));
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_TO))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));

    }
    else {
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_FROM))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));
      availableSecurityFields.stream()
          .filter(selectItem -> selectItem.getValue()
              .equals(FIELD_TO))
          .findFirst()
          .ifPresent(selectItem -> selectItem.setDisabled(false));
    }
    FacesContext context = FacesContext.getCurrentInstance();
    UIComponent component = JSFHelper.findComponent("selectManyCheckbox");
    if (component != null) {
      Optional<Validator> validator = stream(((SelectManyCheckbox) component).getValidators()).findFirst();
      validator.ifPresent(value -> value.validate(context, (component), this.selectedSecurityFields));
    }
  }

  private void initAvailableSecurityFieldsList(String currentFlowDirection) {
    availableSecurityFields = List.of(
        new SelectItem(FIELD_FROM, MessageHelper.getMessage(LABEL_FAMILY + ".from", "From default", this.selectedLanguage), "", "RECEIVING".equals(
            currentFlowDirection)),
        new SelectItem(FIELD_TO, MessageHelper.getMessage(LABEL_FAMILY + ".to", "to default", this.selectedLanguage), "", "SENDING".equals(
            currentFlowDirection) ),
        new SelectItem(FIELD_THIRD_PARTY, MessageHelper.getMessage(LABEL_FAMILY + ".thirdParty", "thirdParty", this.selectedLanguage)),
        new SelectItem("buyerPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".buyerPartyID", "buyerPartyID", this.selectedLanguage)),
        new SelectItem("sellerAgentPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".sellerAgentPartyID", "sellerAgentPartyID", this.selectedLanguage)),
        new SelectItem("factorPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".factorPartyID", "factorPartyID", this.selectedLanguage)),
        new SelectItem("payerPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".payerPartyID", "payerPartyID", this.selectedLanguage)),
        new SelectItem("buyerAgentPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".buyerAgentPartyID", "buyerAgentPartyID", this.selectedLanguage)),
        new SelectItem("remitToPartyID", MessageHelper.getMessage(LABEL_FAMILY + ".remitToPartyID", "remitToPartyID", this.selectedLanguage)));
  }

  public void setPortletFlowDirection(String portletFlowDirection) {
    this.portletFlowDirection = portletFlowDirection;
  }


  /**
   * @see AbstractXcblDocumentTask#importDocuments(List)
   */
  @Override
  public Logs importDocuments(List<InputStream> streams) {
    return streams.isEmpty() ? null : eDocumentService.importInvoices(company, partner, streams, detailPrecision, footerPrecision);
  }

  @Override
  protected String viewerBaseName(InvoiceIndex indexable) {
    String baseName = invoiceService.resolveViewerBaseName(indexable, this.descriptor, PATTERN_DATE, LABEL_FAMILY)
        .orElse(super.viewerBaseName(indexable));

    while (StringUtils.endsWith(baseName, "-")) {
      baseName = StringUtils.removeEnd(baseName, "-");
    }
    return baseName;
  }

  /**
   * In case of async execution, the localised baseFileName must be initialised before and passed as argument to this method.
   */
  protected String viewerBaseName(InvoiceIndex invoice, String baseFileName) {
    return ofNullable(invoice)
        .map(i -> new StringJoiner("-").add(baseFileName)
            .add(ofNullable(this.descriptor.get("invoiceNumber")).map(p -> p.getDisplayValue(i))
                .orElse(i.getEntityRef()))
            .add(ofNullable(this.descriptor.get("invoiceIssueDate"))
                .map(p -> p.getDisplayValue(i, null, null, PATTERN_DATE, null))
                .orElse(""))
            .toString())
        .orElse(super.viewerBaseName(indexable));
  }

  private Function<Document, String> viewerBaseName() {
    return document -> of(document).filter(com.byzaneo.xtrade.bean.Document::isIndexed)
        .map(com.byzaneo.xtrade.bean.Document::<InvoiceIndex> getIndexValue)
        .map(this::viewerBaseName)
        .orElseGet(document::getReference);
  }

  @Override
  protected Function<Document, String> viewerBaseName(String baseFileName) {
    return document -> of(document)
        .filter(com.byzaneo.xtrade.bean.Document::isIndexed)
        .map(com.byzaneo.xtrade.bean.Document::<InvoiceIndex> getIndexValue)
        .map(doc -> viewerBaseName(doc, baseFileName))
        .orElseGet(document::getReference);
  }

  /* -- RESTITUTION -- */

  /**
   * @see AbstractTask#getUserFriendlyXcblMessageNumber()
   */
  @Override
  public Optional<String> getUserFriendlyXcblMessageNumber() {
    return getUserFriendlyXcblMessageNumber(selectedInBirtPanel);
  }

  /**
   * @see AbstractTask#getUserFriendlyXcblMessageNumber(Indexable)
   */
  @Override
  public Optional<String> getUserFriendlyXcblMessageNumber(Indexable indexable) {
    return ofNullable(indexable).map(inv -> ((Invoice) inv).getInvoiceHeader()
        .getInvoiceNumber()
        .getValue());
  }

  /**
   * @see AbstractTask#getUserFriendlyMessageType()
   */
  @Override
  public String getUserFriendlyMessageType() {
    return getLabel(LABEL_FAMILY, "invoice_reference_type_INVOICE", "invoice", getLocale()).toLowerCase();
  }

  public void showAttachFiles(Indexable indexable, int tabIndex) {
    notNull(indexable, "No invoice selected.");
    document = documentService.getDocument(indexable);

    if (document != null) {
      currentIndexable = indexable;
      files = getAttachedFiles(document);
      activeIndex = tabIndex;
      showAttach = true;
    }
    else {
      currentIndexable = null;
      files = emptyList();
    }
  }

  public String getAttachedPdfFile(DocumentFile documentFile) {
    if (documentFile == null) {
      return null;
    }
    try {
      return createResourceRequestPath(documentFile.getFile(), documentFile.getFile()
          .getName(),
          documentFile.getType()
              .getDefaultMime());
    }
    catch (Exception e) {
      TaskHelper.error(this, e, "Unable to get " + documentFile.getType() + " file", getRootCauseMessage(e));
      return null;
    }
  }

  public void showAllAttachedFiles(Indexable indexable) {
    notNull(indexable, "No invoice selected.");
    document = documentService.getDocument(indexable);

    if (document == null || (files = document.getFiles()).isEmpty()) {
      TaskHelper.error(this, null, "gnxxcblinvlbls.error_invoice_not_attachment");
      files = null;
      return;
    }

    addDBErrors();
  }

  public void showTaxOrginalAndErrorFiles(Indexable indexable, boolean isErrorTab) {
    notNull(indexable, "No invoice selected.");
    document = documentService.getDocument(indexable);
    List<DocumentFile> documentFiles = new ArrayList<>();

    if (document == null || (documentFiles = getDocumentFiles(document)).isEmpty()) {
      TaskHelper.error(this, null, "gnxxcblinvlbls.error_invoice_not_attachment");
      files = null;
      return;
    }
    if (document != null) {
      currentIndexable = indexable;
      if (isErrorTab) {
        files = documentFiles.stream()
            .filter(file -> isErrorFile(file))
            .collect(toList());
        addDBErrors();
      }
      else {
        files = documentFiles.stream()
            .filter(file -> isTaxOriginalFile(file))
            .collect(toList());
      }
      if (files.isEmpty()) {
        if (isErrorTab)
          TaskHelper.warn(this, "gnxxcblinvlbls.error_no_demat_errors");
        else
          TaskHelper.warn(this, "gnxxcblinvlbls.error_no_tax_original");
        files = null;
        currentIndexable = null;
        return;
      }
      else {
        RequestContext.getCurrentInstance()
            .update("tskForm-invoiceView-invViewPnl");

      }
    }
  }

  private void addDBErrors() {
    if (EDocumentErrorHelper.hasDocumentErrors(document))
      files.add(new DocumentFile(new File(DBERRORACTION), FileType.UNKNOWN, DBERRORACTION, null, document, null));
  }

  public boolean hasAttachFiles(InvoiceIndex indexable) {
    return indexable.getAttachFileCount() != 0;
  }

  @SuppressWarnings("unchecked")
  @Override
  protected Class<DocumentFile> getRowExpansionType() {
    return DocumentFile.class;
  }

  @SuppressWarnings("unchecked")
  @Override
  public List<DocumentFile> resolveRowExpansionData(DocumentService documentService, Indexable rowExpansionSelected) {
    Document doc = documentService.getDocument((Long) rowExpansionSelected.getEntityId());
    return getAttachedFiles(doc);
  }

  @Override
  protected void postLazyResponse(Pageable pageable, Map<String, Object> filters,
      Page<InvoiceIndex> page) {
    Map<Long, Integer> mapCount = documentService.getAttachFileCount(page.getContent(),
        ResultingFileAction.ADD_FILE_TO_INVOICE.getName(),
        ResultingFileAction.ADD_FILE_TO_INVOICE_FROM_WORKFLOW.getName());
    page.get()
        .forEach(i -> i.setAttachFileCount(mapCount.containsKey(i.getEntityId()) ? mapCount.get(i.getEntityId()) : 0));
  }

  public List<DocumentFile> getDocumentFiles(Document document) {
    return DocumentPdfHelper.getDocumentFiles(document);
  }

  public String getTabName(DocumentFile dof) {
    if (isTaxOriginalFile(dof) && !showAttach)
      return MessageHelper.getMessage(LABEL_FAMILY + ".tax_original", "Tax original", this.selectedLanguage);
    if (isErrorFile(dof) && !showAttach)
      return MessageHelper.getMessage(LABEL_FAMILY + ".demat_errors", "Dematerialization errors", this.selectedLanguage);
    if (DBERRORACTION.equals(dof.getActionName()) && FileType.UNKNOWN.equals(dof.getType()))
      return MessageHelper.getMessage(LABEL_FAMILY + ".demat_errors", "Dematerialization errors", this.selectedLanguage);
    return getReducedFileName(dof.getFile()
        .getName());
  }

  public static String getFileSize(DocumentFile dof) {
    return InvoiceTaskHelper.getFileSize(dof);
  }

  @Override
  public String getFileContent(DocumentFile dof) {
    if (dof == null || dof.getFile() == null) {
      return null;
    }

    String fileContent = null;
    if (isSizeOk(dof)) {
      try (FileInputStream fis = new FileInputStream(dof.getFile())) {
        fileContent = IOUtils.toString(fis, defaultCharset());
        return isErrorFile(dof) ? I18NHelper.getListLabelValuesFromJson(fileContent, getCurrentUser().getLocale()) : fileContent;
      }
      catch (IOException e) {
        log.error(e.getMessage());
        return null;
      }
      catch (JsonParseException e) {
        // the error file is not a json
        return fileContent;
      }
    }
    else {
      return com.byzaneo.commons.util.FileHelper.readBytesFromFile(dof.getFile(), 500 * 1024);
    }
  }

  public String getDocumentErrorsLabel(DocumentFile dof) {
    return EDocumentErrorHelper.getDocumentErrorLabels(dof.getDocument(), getCurrentUser().getLocale())
        .stream()
        .collect(Collectors.joining(System.lineSeparator()));
  }

  private boolean isErrorFile(DocumentFile dof) {
    return isType(dof.getFile(), ERROR);
  }

  private boolean isTaxOriginalFile(DocumentFile dof) {
    return InvoiceHelper.isTaxOriginalFile(dof);
  }

  @Override
  public void onSelect(Instance instance) {
    this.onSelect(instance, (InvoiceIndex) getSelected(), null);
  }

  @Override
  public void onSelect(Instance instance, InvoiceIndex indexable, PropertyDescriptor property) {
    try {
      this.viewer = view(instance, indexable, BirtOutoutFileType.PDF).orElse(null);
      this.selected = null;
      this.allIndexesSelected = false;
      Document doc = viewer.getDocument();
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.getCurrentWorkflowDocumentStatusDocId(doc.getId());
      this.hasWorkflowAttached = workflowDocumentStatusService.getCurrentWorkflowDocumentStatusDocId(viewer.getDocument()
          .getId()) != null;
      this.invoiceTimelines = this.getAllTimelinesForInvoice();
      if (this.invoiceTimelines.size() > 0) {
        this.showTimeline = true;
        this.timelineTruncatedComments = new HashMap<Long, Boolean>();
      }
      workflowStepRedirect = workflowStepRedirectService.getWorkflowStepRedirectByUserAndDocument(getCurrentUser().getId(),
          viewer.getDocument()
              .getId());
      cancelWorkflowStepRedirect = workflowStepRedirectService.getWorkflowStepRedirectByOriginalUserAndDocument(getCurrentUser().getId(),
          viewer.getDocument()
              .getId());
      initWorkflowRedirectParams();
      this.delegatorUserForSelectedInvoice = this.computeDelegatorUserForSelectedInvoice(wDocStatus);
    }
    catch (TemplateMissingException tme) {
      MessageHelper.error(tme, MessageHelper.getMessage(LABEL_FAMILY + ".error_missing_template",
          "There is no Birt template configured, please contact your administrator.", this.selectedLanguage));
      log.error(tme.getMessage());
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error viewing document: %s", getRootCauseMessage(e));
    }
  }

  private User computeDelegatorUserForSelectedInvoice(WorkflowDocumentStatus workflowDocumentStatus) {
    if (!this.hasWorkflowAttached) {
      return null;
    }
    if (CollectionUtils.isEmpty(currentUserAndDelegatorUsersForCurrentUser) || currentUserAndDelegatorUsersForCurrentUser.size() == 1) {
      // empty list of delegators or it contains only the current user => there are no delegators
      return null;
    }
    Workflow workflow = workflowDocumentStatus.getWorkflow();
    if (workflow == null) {
      return null;
    }
    Set<String> workflowUserCodes = new HashSet<String>();
    for (WorkflowStep workflowStep : workflow.getWorkflowSteps()) {
      workflowUserCodes
          .addAll(getWorkflowDocumentUsersIdsByStep(workflowDocumentStatus, workflowStep.getId()
              .getNumStep()));
    }
    if (workflowUserCodes.contains(this.getCurrentUser()
        .getId())) {
      // current user is already a workflow step user=>he is not delegated (or delegation is not important since he already had steps to
      // do)
      return null;
    }
    List<User> delegators = this.currentUserAndDelegatorUsersForCurrentUser.stream()
        .filter(u -> !Objects.equals(u.getId(), this.getCurrentUser()
            .getId()))
        .collect(Collectors.toList());

    // filter the workflow user codes by delegator codes. The first one found is the good one!
    for (User delegator : delegators) {
      Optional<String> wkfUserCode = workflowUserCodes.stream()
          .filter(wuc -> Objects.equals(wuc, delegator.getId()))
          .findFirst();
      if (wkfUserCode.isPresent()) {
        return securityService.getUser(wkfUserCode.get());
      }
    }
    return null;
  }

  private Optional<DocumentViewer> view(Instance instance, InvoiceIndex indexable, BirtOutoutFileType type) {
    final Optional<DocumentViewer> view = this.documentViewerService.view(
        indexable, instance, this.partner,
        this.getCurrentUser()
            .getLocale(),
        this.viewerBaseName(), false, false, null, type);
    // document not found
    if (!view.isPresent()) {
      error("Document not found");
    }
    // performs straight validation to gets user's feed-backs...
    // to avoid unnecessary transformations, we could
    // change the document visualization workflow
    if (!view.map(DocumentViewer::isValid)
        .get()) {
      TaskHelper.error(this, null, LABEL_FAMILY + ".error_missing_template");
      return empty();
    }
    else {
      markDocumentsAsRead(indexable);
    }
    return view;
  }

  private void markDocumentsAsRead(Indexable indexable) {
    documentService.markDocumentConsultStatusAsRead(indexable, isOkForConsultStatusUpdate(), currentSession().getUserId());
  }

  public void onDownloadPdf(Instance instance, InvoiceIndex indexable) {
    onDownload(instance, indexable, BirtOutoutFileType.PDF);
  }

  public void onDownloadCsv(Instance instance, InvoiceIndex indexable) {
    onDownload(instance, indexable, BirtOutoutFileType.Excel);
  }

  public void onDownloadCsv(Instance instance) {
    onDownload(instance, this.documentService.getIndexable(this.viewer.getDocument()), BirtOutoutFileType.Excel);
  }

  private void onDownload(Instance instance, InvoiceIndex indexable, BirtOutoutFileType type) {
    try {
      this.download = view(instance, indexable, type)
          .flatMap(DocumentViewer::download)
          .orElse(null);
      getCurrentInstance()
          .addCallbackParam("download", this.download != null);

    }
    catch (TemplateMissingException tme) {
      TaskHelper.error(this, tme, LABEL_FAMILY + ".error_missing_template");
      log.error(tme.getMessage());
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error downloading document: %s", getRootCauseMessage(e));
    }
  }

  public void onDownloadCsvs(Instance instance) {
    onDownloads(instance, BirtOutoutFileType.Excel, InvoiceIndex.class);
  }

  public void onDownloadPdfs(Instance instance) {
    onDownloads(instance, BirtOutoutFileType.PDF, InvoiceIndex.class);
  }

  public void onDownload() {
    try {
      sendFile(download.getFile(), download.getName(), true);
    }
    catch (Exception e) {
      MessageHelper.error(e, "Error downloading file: %s", getRootCauseMessage(e));
    }
  }

  @Override
  public String getViewerURI(InvoiceIndex indexable) {
    if (indexable.getViewerURI() != null) {
      return indexable.getViewerURI();
    }
    String uri = toURI(indexable,
        this.getInstance(),
        this.getCurrentUser()
            .getLocale(),
        this.viewerBaseName(indexable),
        false, false, null, BirtOutoutFileType.PDF);
    indexable.setViewerURI(uri);
    return uri;
  }

  @Override
  public void onDownloadList(Instance instance) {
    if (dataModel != null && this.dataModel.getRowCount() == 0) {
      TaskHelper.warn(this, "gnxxcblorderlbls.error_export_no_records");
      return;
    }
    // CountgetAsynchronousProcessLimit
    int itemsCount = Long.valueOf(documentService.countIndexable(getIndexableType(), resolveSearchQuery()))
        .intValue();
    // Asynchronous limit for export list
    int asynchronousLimit = getAsynchronousListExportRange().getMax();
    // Process the export
    export(false, itemsCount, asynchronousLimit, getListExportFileName(), instance, FileType.EXCEL, BirtOutoutFileType.Excel);
    if (this.download != null) {
      getCurrentInstance().addCallbackParam("download", this.download != null && download.getFile() != null);
    }
    else {
      TaskHelper.error(this, null, "edctsklbls.unable_to_generate_file");
    }
  }

  /**
   * Gets the asynchronous limit for document export
   */
  @Override
  public long getAsynchronousProcessLimit() {
    return getAsynchronousDocumentExportRange().getMax();
  }

  /* -- SEARCH -- */

  // LIFE CYCLE

  /**
   * @see AbstractIndexableTask#onResetAdvancedSearch()
   */
  @Override
  public void onResetAdvancedSearch() {
    if (this.dataModelDescriptor == null) {
      @SuppressWarnings("unchecked")
      XcblDocumentDataModel model = (XcblDocumentDataModel) this.getDataModel();
      this.dataModelDescriptor = model.getDataDescriptor();
    }
    super.onResetAdvancedSearch();
  }

  /* -- LABELS -- */

  /**
   * @see AbstractTask#i18n(String)
   */
  @Override
  public String i18n(final String key) {
    return super.i18n(LABEL_FAMILY + "." + key);
  }

  @Override
  public ColumnValue getColumnValue(InvoiceIndex bean, PropertyDescriptor property) {
    if (property.getType()
        .equals(BigDecimal.class)) {
      property.setPattern(getColumnFloatPattern());
    }
    return super.getColumnValue(bean, property);
  }

  @Override
  public String getLabelStatus(Object bean, PropertyDescriptor descriptor) {
    if (this.isArchiveStatusProperty(descriptor.getName())) {
      Object value = getPropertyValue(bean, descriptor, "");
      final String labelValue = value == null ? "" : value.toString();
      return getMessage(LABEL_FAMILY + "." + labelValue, labelValue, selectedLanguage);
    }
    return super.getLabelStatus(bean, descriptor);
  }

  private String getColumnFloatPattern() {
    if (floatColumnPattern == null) {
      floatColumnPattern = "0." + StringUtils.repeat("0", footerPrecision);
    }
    return floatColumnPattern;
  }

  /**
   * Get status values
   *
   * @return map which contains values status
   */
  @Override
  public Map<Object, String> getValues(String label) {
    return getValues(label, currentSession().getUser(), this.getInstanceCode(), getSessionUserLocale(), selectedLanguage);
  }

  @Override
  public Map<Object, String> getValues(String label, User user, String instanceCode, Locale locale, Locale selectedLanguage) {
    List<String> list;
    if (isOriginalStatusProperty(label)) {
      list = getAvailableStatus(getIndexableType(), user, locale, true);
      return getStatusesAsMapOfCodeObject(list, instanceCode, locale);
    }
    else if (isOriginalStageProperty(label)) {
      return toStageItems(sortStageLabel(asList(DocumentStage.values()), locale), selectedLanguage);
    }
    else if (isOriginalConsultStatusProperty(label)) {
      list = sortConsultStatusLabel(asList(DocumentConsultStatus.values()), locale);
    }
    else if (isOriginalPaymentStatusProperty(label)) {
      list = sortPaymentStatusLabel(asList(PaymentStatus.values()), locale);
    }
    else if ("acquisition".equals(label)) {
      list = sortAcquisitionLabel(asList(AcquisitionType.values()), locale);
    }
    else if (isArchiveStatusProperty(label)) {
      list = sortArchiveStatusLabel(asList(ArchiveStatus.values()), locale);
    }
    else if ("invoiceTypeCoded".equals(label)) {
      List<InvoiceTypeCodeType> invoiceTypeCodesType = stream(InvoiceTypeCodeType.values())
          .filter(type -> type != COMMERCIAL_INVOICE && type != CREDIT_NOTE_GOODS_AND_SERVICES)
          .collect(toCollection(ArrayList::new));
      return Stream.of(asList(COMMERCIAL_INVOICE.name(), CREDIT_NOTE_GOODS_AND_SERVICES.name()),
          sortInvoiceTypeCodeTypeByLabel(invoiceTypeCodesType, locale))
          .flatMap(List::stream)
          .collect(toMap(identity(), key -> getMessage(LABEL_FAMILY + "." + key, key, locale), (o1, o2) -> o1, LinkedHashMap::new));
    }
    else if ("processingWay".equals(label)) {
      list = sortProcessingWay(asList(ProcessingWay.values()), locale);
    }
    else {
      return emptyMap();
    }
    return list.stream()
        .collect(toMap(o -> o,
            o -> getMessage(LABEL_CORE + "." + o, o, selectedLanguage)));
  }

  /* -- DESCRIPTOR -- */

  @Override
  public boolean isPropertyRemovable(PropertyDescriptor property) {
    // Allows free text removal
    return property.getName()
        .contains("freeText");
  }

  @Override
  public boolean isPropertyTypeEditable(PropertyDescriptor property) {
    return this.isPropertyRemovable(property);
  }

  @Override
  public StreamedContent getStreamedContent(DocumentFile documentFile) throws IOException {
    if (documentFile == null || documentFile.getFile() == null) {
      return null;
    }
    if (DBERRORACTION.equals(documentFile.getFile()
        .getName())) {
      File file = documentErrorService.createFileFromDBErrors(documentFile.getDocument());
      return new DefaultStreamedContent(new FileInputStream(file), documentFile.getType()
          .getDefaultMime(),
          file.getName());
    }
    if (documentFile.getFile()
        .length() < ZIP_SIZE_TRIGGER) {
      return super.getStreamedContent(documentFile);
    }
    try (final ByteArrayOutputStream zipout = new ByteArrayOutputStream()) {
      FileHelper.zip(Arrays.asList(documentFile.getFile()), zipout);
      return new DefaultStreamedContent(
          new ByteArrayInputStream(zipout.toByteArray()),
          ARCHIVE.getDefaultMime(), FileHelper.getFileNameWithoutExt(documentFile.getFile()) + ARCHIVE.getExtension(), UTF_8);
    }
  }

  public StreamedContent getStreamedContentAllFiles() throws IOException {
    List<File> filesToZip = new ArrayList<>();
    String zipFileName = "export-files-invoice-".concat(new SimpleDateFormat("YYYYMMddHHmmss").format(Calendar.getInstance()
        .getTime()));
    for (DocumentFile documentFile : this.files) {
      if (documentFile != null && documentFile.getFile() != null) {
        if (DBERRORACTION.equals(documentFile.getFile()
            .getName())) {
          filesToZip.add(documentErrorService.createFileFromDBErrors(documentFile.getDocument()));
        }
        else
          filesToZip.add(documentFile.getFile());
      }
    }
    if (!filesToZip.isEmpty())
      try (final ByteArrayOutputStream zipout = new ByteArrayOutputStream()) {
        FileHelper.zip(filesToZip, zipout);
        return new DefaultStreamedContent(
            new ByteArrayInputStream(zipout.toByteArray()),
            ARCHIVE.getDefaultMime(), zipFileName + ARCHIVE.getExtension(), UTF_8);
      }
    return null;
  }

  @Override
  // TODO
  public void onAddProperty(ActionEvent event) {
    String freetext = this.property.getName();
    if (freetext.contains("freeText")) {
      // this.property.setAlias(String.format("invoiceHeader.listOfNameValueSet?.nameValueSet.?[setName=='FreeText'][0].listOfNameValuePair.nameValuePair.?[name=='%s'][0].value",
      // freetext));
      // this.property.setModelExpression(String.format("\"invoiceHeader.listOfNameValueSet.nameValueSet.listOfNameValuePair.nameValuePair\"
      // on (\"name = %s\", \"value = (0)\")", freetext));
      this.property.setName("freeTexts[15]");
      this.property.setLabel(freetext);
      this.property.setPath("invoiceHeader.listOfNameValueSet.nameValueSet[0].listOfNameValuePair.nameValuePair[15].value");
    }
    super.onAddProperty(event);
  }

  @Override
  public void onAddFreeText(ActionEvent event) {
    final EDocument edoc = EDocument.valueOf(this.getIndexableType());

    if (edoc == null) {
      warn("xtdtsklbls.error_freetext_not_supported", this.getIndexableType());
      this.freeText = null;
      return;
    }

    if (getFreeTextIndexes(this.descriptor).anyMatch(i -> i == this.freeText.getIndex())) {
      error("xtdtsklbls.error_duplicate_freetext_number");
      return;
    }

    final PropertyDescriptor p = createFreeTextProperty(this.freeText.getIndex(), this.freeText.getLabel(), edoc.getHeaderPath());
    p.setType(this.freeText.getType());
    this.descriptor.addProperty(p);
    this.freeText = null;
  }


  /**
   * @see AbstractIndexableTask#createDescriptor()
   */
  @Override
  protected BeanDescriptor createDescriptor() {
    try {
      BeanDescriptor bd = beanService.fromClasspath("descriptors/invoice-columns.xml");

      if (this.dataModelDescriptor != null) {
        for (PropertyDescriptor pd : bd.getProperties()) {
          this.dataModelDescriptor.getProperties()
              .stream()
              .filter(p -> pd.getName()
                  .equals("indexable." + p.getName()) ||
                  pd.getName()
                      .equals(p.getName()))
              .findFirst()
              .ifPresent(property -> pd.setLabel(property.getLabel()));
        }
      }

      return bd;
    }
    catch (IOException ex) {
      TaskHelper.error(this, ex, "{0}", getRootCauseMessage(ex));
      return null;
    }
  }

  /* -- SECURITY -- */

  /**
   * @see AbstractXcblDocumentTask#getOrganizationByCode(String, String)
   */
  @Override
  public Group getOrganizationByCode(String code, String parentCode) {
    return securityService.getOrganizationByCode(code, parentCode);
  }

  /**
   * @see AbstractXcblDocumentTask#canDeleteAddedFile(DocumentFile)
   */
  @Override
  public boolean canDeleteAddedFile(DocumentFile documentFile) {
    String connectedUserOrganizationCode = sessionHandler.getPrimaryGroup()
        .getCode();
    return documentFile.getActionName() != null && documentFile.getActionName()
        .equals(ResultingFileAction.ADD_FILE_TO_INVOICE.getName()) && documentFile.getDocument()
            .getOwners()
            .equals(connectedUserOrganizationCode);
  }

  public boolean enableAction(Indexable indexable) {
    return !checkNorm(indexable, USER_RECORD) || isSupportFileType(PDF) || isSupportFileType(EXCEL);
  }

  /* -- EVENT -- */

  // CORRECT
  public void onCorrectInvoice(final Instance instance, final InvoiceIndex invoice) {
    this.indexable = invoice;
    this.modeCorrectInv = true;
    correctInvoice(instance, true);
  }

  private boolean correctInvoice(final Instance instance, boolean init) {
    try {
      this.document = this.documentService.getDocument(this.indexable);
      setInvoiceXcbl(document, "correct_error");
      this.xcblEdition = XcblEditionBuilder.builder(this.document, PREDICATE_INVOICE_DOF, this.selectedLanguage)
          .paths(this.document, PREDICATE_APPRSP_DOF)
          .build();

      // No error
      if (this.xcblEdition.getPathes()
          .size() == 0) {
        if (init) {
          resetEdition();
          TaskHelper.error(this, null, "no_error_found");
        }
        return false;
      }

      // Create BirtFile
      if (instance != null) {
        this.viewer = view(instance, this.indexable, BirtOutoutFileType.PDF).orElse(null);
        /* this.birtFile = resolveBirtFile(this.document, this.birtFile); */
      }

      return this.xcblEdition.getPathes()
          .size() != 0;
    }
    catch (Exception e) {
      // if there was any issue reset the fields and return the appropriate error message
      MessageHelper.error(e, e.getMessage());
      resetEdition();
      return false;
    }
  }

  // TODO A voir
  // CORRECT (2e version gardée pour cause de compatibilité avec les tests fait
  // dans InvoiceTaskTest)
  public void onCorrectInvoice(final InvoiceIndex invoice) {
    this.document = this.documentService.getDocument(invoice); /* Xcbl40Test */
    setInvoiceXcbl(document, "correct_error");
    indexable = invoice;
    modeCorrectInv = true;
    correctInvoice(true);
  }

  private boolean correctInvoice(boolean init) {
    try {
      xcblEdition = XcblEditionBuilder.builder(this.document, PREDICATE_INVOICE_DOF, this.selectedLanguage)
          .paths(this.document, PREDICATE_APPRSP_DOF)
          .build();
      // No error
      if (xcblEdition.getPathes()
          .size() == 0) {
        if (init) {
          resetEdition();
          TaskHelper.error(this, null, "no_error_found");
        }
        return false;
      }
      // Create BirtFile
      this.birtFile = resolveBirtFile(this.document, this.birtFile);
      return xcblEdition.getPathes()
          .size() != 0;
    }
    catch (Exception e) {
      MessageHelper.error(e, e.getMessage());
      resetEdition();
      return false;
    }
  }

  @TaskEvent(name = "Correction", sourceMethod = "prepareDocumentForCorrection", topicDescriptor = "document-topic", callbackMethod = "callback")
  public void onSaveCorrectInvoice() {
    if (this.document == null) {
      resetEdition();
      TaskHelper.error(this, null, "process_error");
      return;
    }

    this.document = this.documentService.getDocument(Document.class, this.document.getId(), true);
    this.indexable = this.document.getIndexValue();
    // Manage process return
    try {
      assertThatProcessOk(getReports());
      // Remove the old XCBL File
      if (this.xcblEdition != null)
        deleteFile(this.xcblEdition.getOldPath()
            .toFile());
      // Remove the old Application Response
      if (this.dofAppRsp != null && !TO_CORRECT.equals(this.document.getStatusAsEnumValue()))
        this.documentService.removeFile(this.document, this.dofAppRsp);

      setInvoiceXcbl(this.document, null);
      if (this.delegatorUserForSelectedInvoice == null) {
        // no delegator
        documentTimelineService.saveActionInTimeline(this.document, getCurrentUser(), FIX, EMPTY, 0);
      }
      else {
        documentTimelineService.saveActionInTimeline(this.document, getCurrentUser(), FIX, EMPTY, 0, null,
            this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
      }

    }
    catch (Exception e) {
      restoreDocumentForCorrection();
      resetEdition();
      TaskHelper.error(this, e, e.getCause() != null ? e.getCause()
          .getMessage() : e.getMessage());
      return;
    }
    if (TO_CORRECT.equals(this.document.getStatusAsEnumValue())) {
      if (correctInvoice(null, false))
        info(this, "correct_field_error");
      else TaskHelper.error(this, null, "no_error_found");
    }
    else {
      info(this, "correct_validation");
      resetEdition();
    }
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  @Override
  public void info(Object task, String messageKey) {
    TaskHelper.info(task, messageKey);
  }

  private void restoreDocumentForCorrection() {
    if (this.xcblEdition != null) { // Get the old XCBL File
      this.document.getFiles()
          .stream()
          .filter(dof -> this.xcblEdition.getNewPath()
              .toFile()
              .equals(dof.getFile()))
          .findAny()
          .ifPresent(dof -> dof.setFile(xcblEdition.getOldPath()
              .toFile()));
      deleteFile(this.xcblEdition.getNewPath()
          .toFile());
    }
    // Get the old Application Response
    if (this.dofAppRsp != null)
      this.document.addFile(this.dofAppRsp);
    saveAndDetachCurrentDocument(document);
  }

  public Document prepareDocumentForCorrection() {
    this.document = this.documentService.getDocument(this.indexable);
    // Get the document file which contains the invoice xml file
    this.dofInvoiceCorrection = this.document.getFiles()
        .stream()
        .filter(dof -> this.xcblEdition.getOldPath()
            .toFile()
            .equals(dof.getFile()))
        .findAny()
        .orElse(null);

    if (this.dofInvoiceCorrection == null)
      throw new IllegalArgumentException("Document is not good");

    // Set the new invoice xml file
    this.xcblEdition.write();
    this.document.getFiles()
        .stream()
        .filter(dof -> this.dofInvoiceCorrection.getFile()
            .equals(dof.getFile()))
        .findAny()
        .ifPresent(dof -> dof.setFile(xcblEdition.getNewPath()
            .toFile()));

    // Get the Application Response and remove it from document
    this.dofAppRsp = resolveAppRspDof(this.document);
    if (dofAppRsp != null) {
      // Remove AppRsp from document
      this.document.getFiles()
          .removeIf(dof -> this.dofAppRsp.equals(dof));
      this.dofAppRsp.setId(null);
    }

    saveAndDetachCurrentDocument(document);

    return this.document;
  }

  // DIAGNOSE
  public void onDiagnoseInvoice(final Instance instance, final InvoiceIndex invoice) {
    this.selected = null;
    this.document = this.documentService.getDocument(invoice);
    setInvoiceXcbl(document, "diagnose_error");
    indexable = invoice;
    modeDiagnoseInv = true;
    diagnoseInvoice(instance, true);
  }

  private boolean diagnoseInvoice(final Instance instance, boolean init) {
    try {
      this.document = this.documentService.getDocument(this.indexable);
      this.pathesDiagnose = resolvePaths(getW3cDocFromDocument(
          this.document, PREDICATE_APPRSP_DOF), this.selectedLanguage, null);

      // Create BirtFile
      /* this.birtFile = resolveBirtFile(this.document, this.birtFile); */
      this.viewer = view(instance, this.indexable, BirtOutoutFileType.PDF).orElse(null);

      return this.pathesDiagnose.size() != 0;
    }
    catch (Exception e) {
      // if there was any issue reset the fields and return the appropriate error message
      MessageHelper.error(e, e.getMessage());
      resetEdition();
      return false;
    }
  }

  void setInvoiceXcbl(Document document, String errorMsg) {
    if (document == null) return;
    try {
      File invoiceFile = null;
      if (document.getFiles()
          .stream()
          .filter(PREDICATE_INVOICE_XCBL)
          .count() > 1) {
        error("Document invalid : official index is not unique");
      }
      else {
        invoiceFile = document.getFiles()
            .stream()
            .filter(PREDICATE_INVOICE_XCBL)
            .findFirst()
            .map(DocumentFile::getFile)
            .orElse(null);
      }
      invoiceXcbl = JAXBHelper.unmarshal(Invoice.class, invoiceFile);
      if (document.getIndexValue() != null && invoiceXcbl != null) {
        invoiceXcbl.setId(document.getIndexValue()
            .getId());
      }
    }
    catch (JAXBException | XMLStreamException | IOException e) {
      TaskHelper.error(this, e, errorMsg != null ? errorMsg : e.getMessage());
    }
  }

  // FORCE
  public void onForced(final Instance instance, final InvoiceIndex invoice) {
    this.document = this.documentService.getDocument(invoice); /* Xcbl40Test */
    setInvoiceXcbl(document, null);
    indexable = invoice;
    modeForceInv = true;
    // Create BirtFile
    /* this.birtFile = resolveBirtFile(this.document, this.birtFile); */
    this.viewer = view(instance, this.indexable, BirtOutoutFileType.PDF).orElse(null);
  }

  @TaskEvent(name = "Forcer", sourceMethod = "prepareDocumentForForce", topicDescriptor = "document-topic", callbackMethod = "callback", contextMethod = "onSaveForceInvoiceVariables")
  public void onSaveForceInvoice() {
    try {
      // Manage process return
      assertThatProcessOk(getReports());
      this.indexable = this.document.getIndexValue();
      TaskHelper.info(this, "force_validation", Optional.ofNullable(this.invoiceXcbl)
          .map(Invoice::getInvoiceHeader)
          .map(InvoiceHeaderType::getInvoiceNumber)
          .map(ComplexStringType::getValue)
          .orElse(""));
    }
    catch (Exception e) {
      TaskHelper.error(this, e, e.getMessage());
      return;
    }
    finally {
      resetEdition();
    }
  }

  public Map<String, Object> onSaveForceInvoiceVariables() {
    Map<String, Object> vars = new HashMap<>();
    vars.put(portlet_action.toString(), "Forcer");
    vars.put(portlet_cause.toString(), this.cause);
    return vars;
  }

  public void callback(Report report) {
    getReports().add(report);
  }

  @Override
  public void callbackTriggerAction(Report report) {

    logActionInDocumentTimeline();
    try {
      assertThatProcessOk(Collections.singletonList(report));
    }
    catch (Exception e) {
      log.error(e.getMessage());
      return;
    }
  }

  public void logActionInDocumentTimeline() {
    processedDocuments.forEach(doc -> {
      DocumentTimeline docTimeline = new DocumentTimeline();
      docTimeline.setComment(getRunActionName() + "\n" + getFormPageEnteredValues());
      docTimeline.setAction(TimelineAction.CUSTOM);
      docTimeline.setDocument(doc);
      docTimeline.setDate(new Date());
      docTimeline.setUser(getUserId());
      docTimelineService.save(docTimeline);
    });
  }

  public void onAddLineAllowOrCharge() {
    if (this.product.getInvoicePricingDetail()
        .getItemAllowancesOrCharges() == null) {
      this.product.getInvoicePricingDetail()
          .setItemAllowancesOrCharges(new ListOfInvoiceAllowOrChargeType());
    }
    InvoiceAllowOrChargeType allowOrChargeType = createInvoiceAllowOrCharge();

    // only one type(amount/percent) of allowance/charge is allowed
    allowOrChargeType.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(null);

    addAllowOrChargeLine(allowOrChargeType, this.allowOrChrgLineEdit, this.product);
    this.allowOrChrgLineEdit = true;
  }

  public void onRowEditLine(RowEditEvent event) {
    this.allowOrChrgLineEdit = false;
  }

  public void onReturn() {
    files = null;
  }

  public void onCancelUpload() {
    uploadedFile = null;
    comment = null;
    this.uploadedFileReadable = false;
    this.uploadedFilePDF = false;
  }

  /* -- STATUS -- */

  public void updateDocStatusToRefusedManually(final InvoiceIndex invoice) {
    this.document = this.documentService.getDocument(invoice);
    this.document.setStatusWithEnumValue(REFUSED_MANUALLY);
    this.documentService.saveDocument(this.document);
    invalidateCachedColumnValues(invoice.getId());
  }

  @Override
  protected void markDocumentsAsRead() {
    markDocumentsAsRead(download);
  }

  public boolean isInvoiceInError(final InvoiceIndex invoice) {
    return isEqualsStatusCode(ofNullable(invoice)
        .map(InvoiceIndex::getStatus)
        .orElse(new DocumentStatusEntity()), DocumentStatus.ERROR);
  }

  public boolean isStateInError(final InvoiceIndex invoice) {
    return ofNullable(invoice)
        .map(InvoiceIndex::getStatus)
        .filter(s -> isEqualsStatusCode(s, DocumentStatus.ERROR))
        .isPresent();
  }

  @Override
  public boolean isLegalStateInError(final InvoiceIndex invoice) {
    return ofNullable(invoice)
        .map(InvoiceIndex::getStage)
        .filter(DocumentStage.ERROR::equals)
        .isPresent();
  }

  @Override
  public boolean isCorrectable(final InvoiceIndex indexable) {
    return ofNullable(indexable).map(InvoiceIndex::getStatus)
        .filter(status -> isEqualsStatusCode(status, DocumentStatus.ERROR) || isEqualsStatusCode(status, TO_CORRECT))
        .isPresent();
  }

  @Override
  public boolean isStateIsCorrectable(final InvoiceIndex indexable) {
    return ofNullable(indexable).map(InvoiceIndex::getStatus)
        .filter(s -> isEqualsStatusCode(TO_CORRECT, s))
        .isPresent();
  }

  public boolean isModifiable(final InvoiceIndex indexable) {
    if (statusesForInvoiceModification == null)
      this.statusesForInvoiceModification = new ArrayList<DocumentStatusEntity>();
    return this.statusesForInvoiceModification.contains(indexable.getStatus());
  }

  @Deprecated
  /** use {@link com.byzaneo.generix.edocument.service.InvoiceService#getRossumIdFromXcblIfOCRVerifiable(InvoiceIndex, List)}
   */
  public String getRossumIdFromXcblIfOCRVerifiable(final InvoiceIndex indexable) {
    if (indexable == null || !AcquisitionType.OCR.equals(
        indexable.getAcquisition()) || !getStatusesForInvoiceOcrVerify().contains(indexable.getStatus()) || isNotBlank(
        indexable.getRossumId()))
      return null;
    try {
      return invoiceService.getRossumReferenceNumber(indexable);
    }
    catch (Exception e) {
      log.warn("Can't retrieve Rossum ID for invoice uuid " + indexable.getUuid() + " : " + e.getMessage(), e);
      return null;
    }
  }

  public boolean isOCRVerifiable(final InvoiceIndex indexable) {
    if (!rossumService.isRossumServiceActive() || !this.hasAuthenticationChannelAccessTokenV1ForRossum || indexable == null || !AcquisitionType.OCR.equals(
        indexable.getAcquisition()) || !getStatusesForInvoiceOcrVerify().contains(indexable.getStatus()))
      return false;
    try {
      return isNotBlank(indexable.getRossumId()) || isNotBlank(invoiceService.getRossumReferenceNumber(indexable));
    }
    catch (Exception e) {
      log.warn("Can't retrieve rossum reference number : " + e.getMessage(), e);
      return false;
    }
  }

  public void generateOCRVerificationUrl(Indexable indexable) {
    try {
      // find the first ROSSUM authentication channel with an active token, if it exists, and return the token
      // or else find the first ROSSUM authentication channel (without an active token) and request a new token
      String rossumKey = variablesOperations.getAccessTokenV1(AccessTokenV1Use.ROSSUM, getPartner(),
          getCompany());
      InvoiceIndex invoiceIndexable = (InvoiceIndex) indexable;
      String rossumReferenceNumber = isNotBlank(invoiceIndexable.getRossumId())
          ? invoiceIndexable.getRossumId()
          : invoiceService.getRossumReferenceNumber(invoiceIndexable);
      RequestContext.getCurrentInstance()
          .addCallbackParam("rossumUrl", rossumService.getRossumStudioUrl(rossumReferenceNumber, getCurrentPageUrl(), rossumKey));
    }
    catch (Exception e) {
      MessageHelper.error(e, "gnxxcblinvlbls.rossum_fail", e.getMessage());
    }
  }

  public String getCurrentPageUrl() {
    HttpServletRequest request = (HttpServletRequest) getFacesContext().getExternalContext()
        .getRequest();
    String taskUrl = getRequestURL(request);
    return taskUrl.substring(0, taskUrl.indexOf("ui/portlet/task.jsf")) + "close_tab.html";
  }

  public boolean isStateIsDiagnosable(final InvoiceIndex indexable) {
    return ofNullable(indexable).map(InvoiceIndex::getStatus)
        .filter(s -> isEqualsStatusCode(s, REFUSED))
        .isPresent();
  }

  public void refuseDocumentAndSaveInTimeline() {
    refuseDocumentAndSaveInTimeline(viewer.getDocument(), getWorkflowForSelectedInvoice(), workflowStepRedirect);
    refreshTimeline();
  }

  public void refuseDocumentAndSaveInTimeline(Document document, Workflow workflow, WorkflowStepRedirect workflowStRedirect) {
    InvoiceIndex invoiceIndex = documentService.getIndexable(document);
    this.document = documentService.getDocument(invoiceIndex);
    invoiceService.refuseDocumentAndSaveInTimeline(this.getCurrentUser(), this.delegatorUserForSelectedInvoice, document, workflow,
        workflowStRedirect, workflowDocumentStatusService, documentService, workflowStepRedirectService, documentTimelineService, comment);
    resetDataModel();
  }

  private void resetDataModel() {
    this.dataModel = null;
  }

  public void refuseWorkflowDocuments() {
    refuseWorkflowDocuments(null, getSessionUserLocale(), resolveSearchQuery(), allIndexesSelected, isItemSelected(),
        this.getCurrentUser(), this.delegatorUserForSelectedInvoice, getCurrentModelSort(), getCurrentModelOrder(), null);
  }

  public void refuseWorkflowDocuments(Indexable[] indexes, Locale locale, Query query, boolean allIndexesSelected, boolean isItemSelected,
      User user, User delegatorUserForSelectedInvoice, String sort, Sort.Direction order, Integer countSelected) {
    if (!isItemSelected && !allIndexesSelected) {
      TaskHelper.warn(this, "gnxxcblcomlbls.error_document_select_line");
    }
    else {
      boolean unauthorizedRefusal = false;
      boolean canNotRefuse = false;
      int refused = 0;
      boolean hasCustomRefuseAction = false;
      if (!allIndexesSelected) {
        // in case the document was validated already
        query = resolveSearchQuery();
        QueryBuilder qb = createBuilder(query);
        qb.and(Clauses.in("_entity_id", Arrays.stream(selected)
            .map(Indexable::getEntityId)
            .collect(Collectors.toList())));
        query = qb.query();
      }

      indexes = loadIndexables(query, sort, order);
      invoiceService.refuseWorkflowDocuments(false, indexes, locale, query, allIndexesSelected, isItemSelected, this.getCurrentUser(),
          this.delegatorUserForSelectedInvoice, sort, order, countSelected, comment);
      resetDataModel();
    }
  }

  private void resetSelectedAndDataModel() {
    this.selected = null;
    this.dataModel = null;
  }

  public void initWorkflowRedirectParams() {
    Long portletId = null;
    if (workflowStepRedirect == null) {
      getWorkflowStepForSelectedInvoice();
      if (workflowStep != null && workflowStep.getEnrich()) {
        Document document = viewer.getDocument();
        if (document != null) {
          Workflow workflow = getWorkflowForSelectedInvoice();
          WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
              workflow);
          portletId = workflow.getWorkflowSteps()
              .get(wDocStatus.getNumStep() - 1)
              .getPortletID();
        }
      }
    }
    else {
      if (workflowStepRedirect.getEnrich()) {
        portletId = workflowStepRedirect.getPortletID();
      }
    }
    if (portletId != null) {
      com.byzaneo.portal.bean.Page page = findFirstPageByPortletContentId(getPortalSessionHandler().getPortal(), portletId.toString());
      workflowRedirectPage = page.getId();
    }
  }

  public void stopNotification() {
    Document document = viewer.getDocument();
    if (document != null) {
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
          getWorkflowForSelectedInvoice());

      wDocStatus.setPauseNotif(true);
      workflowDocumentStatusService.save(wDocStatus);

      String comment = getMessage(LABEL_FAMILY + ".stopNotification_message", "Reminder notifications have been paused", selectedLanguage);

      if (this.delegatorUserForSelectedInvoice == null) {
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), PAUSE, comment, wDocStatus.getNumStep());
      }
      else {
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), PAUSE, comment, wDocStatus.getNumStep(), null,
            this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
      }
      refreshTimeline();

      MessageHelper.info("gnxxcblinvlbls.stop_notification_comment");

    }
    this.dataModel = null;
  }

  public void updateDocStatusToRefusedAction(final InvoiceIndex invoice, Workflow workflow) {
    this.document = this.documentService.getDocument(invoice);
    updateDocStatusToRefusedAction(documentService, this.document, workflow);
  }

  public void updateDocStatusToRefusedAction(DocumentService documentService, Document document, Workflow workflow) {
    if (workflow.getStatusRefused() != null)
      document.setStatus(workflow.getStatusRefused());
    // also remove invoiceWorkflowIndex from the refused document
    removeWorkflowData((InvoiceIndex) document.getIndexValue());
    documentService.saveDocument(document);
  }

  public void validateRefusedActionComment() {
    if (StringUtils.isEmpty(comment)) {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      MessageHelper.addMessage(
          FacesMessage.SEVERITY_ERROR,
          MessageHelper.getMessage(LABEL_FAMILY + ".refuse_message_mandatory", "The reason of the refusal is mandatory",
              this.selectedLanguage));
      return;
    }
    List<DocumentTimeline> docs = documentTimelineService.getAllTimelinesForDocument(viewer.getDocument()
        .getIndexValue()
        .getEntityId());
    for (DocumentTimeline doc : docs) {
      if (doc.getAction()
          .equals(REFUSE.toString())) {
        doc.setComment(comment);
        documentTimelineService.save(doc);
      }
    }
    this.comment = null;
    viewer = null;
    resetSelectedAndDataModel();
    this.workflowStep = null;
    this.workflowStepRedirect = null;
  }

  public boolean showCommentBlock() {
    return this.showCommentBlock;
  }

  public void showHideCommentBlock() {
    this.showCommentBlock = !this.showCommentBlock;
  }

  public String addNewInvoice(Portal portal) {
    com.byzaneo.portal.bean.Page page = findFirstPageByPortletContentId(portal,
        String.valueOf(TaskHelper.getDefinition(this)
            .getId()));

    if (page != null)
      return page.getId();
    warn("No Page defined for action : %s",
        getDefinition(this).getTitle()); // I18N
    return null;
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  public void addMessage(Throwable t, Severity severity, String messageId, String forClientId, Object... args) {
    MessageHelper.addMessage(t, severity, messageId, forClientId, args);
  }

  // For testing (cf. InvoiceIndexTaskITCase)
  public void error(String msgkey) {
    MessageHelper.error(msgkey);
  }

  public boolean isFromAsn() {
    return DESADV.name()
        .equals(document.getType());
  }

  public Invoice getInvoice() {
    try {
      resolveOrganizationCodes();
      if (invoiceXcbl == null) {
        String documentId = getPortalSessionHandler().getDocumentId();
        if (documentId != null && valueOf(documentId) >= 0) {
          document = documentService.getDocument(Document.class,
              valueOf(documentId), true);
          if (document != null) {
            if (ORDERS.name()
                .equals(document.getType())) {
              invoiceXcbl = initInvoiceWithOrderStandard(document.getIndexValue(), detailPrecision, footerPrecision);
            }
            else if (isFromAsn()) {

              getOrderAndOrdRSPLinkingToAsn(document);
              invoiceXcbl = initInvoiceWithASN(document.getIndexValue(), orderLinked, ordRspLinked);
            }
            lockForMe = true;
          }
        }
        else if (documentId != null && valueOf(documentId) == -2) {
          newInvoice = true;
          this.invoiceXcbl = new Invoice();
          initNewInvoice(this.invoiceXcbl, this.partner);
          lockForMe = true;
        }
      }
    }
    catch (Exception e) {
      TaskHelper.error(this, e, "error_in_conversion");
    }
    return invoiceXcbl;
  }

  public long getWorkflowDocumentId() {
    if (viewer != null) {
      return viewer.getDocument()
          .getId();
    }
    return 0;
  }

  private void getOrderAndOrdRSPLinkingToAsn(final Document document) {
    if (document.getParent() == null) return;

    if (ORDERS.name()
        .equals(document.getParent()
            .getType())) {
      orderLinked = orderService.convertToOrderType((OrderIndex) documentService.getIndexable(getDocument().getParent()), false);
      ordRspLinked = documentService.getIndexable(
          of(getDocument().getParent())
              .map(com.byzaneo.xtrade.bean.Document::getChildren)
              .map(Set::stream)
              .orElse(Stream.empty())
              .filter(doc -> ORDRSP.name()
                  .equals(doc.getType()))
              .findFirst()
              .orElse(null));
    }
    if (ORDRSP.name()
        .equals(document.getParent()
            .getType())) {
      ordRspLinked = documentService.getIndexable(getDocument().getParent());
      orderLinked = orderService.convertToOrderType((OrderIndex) documentService.getIndexable(getDocument().getParent()
          .getParent()), false);
    }
  }

  public List<InvoicePartyCodedType> getListOfPartyCoded() {
    if (invoiceXcbl != null && listOfPartyCoded == null) {
      listOfPartyCoded = InvoiceXcblHelper.getListOfPartyCoded(invoiceXcbl, DEFAULT_PARTY);
      for (InvoicePartyCodedType party : listOfPartyCoded) {
        if (party != null && party.getPartyID() != null) {
          InvoiceXcblHelper.updateInvoiceParty(party, getPartyInfo(InvoiceHelper.getIdentValue(party.getPartyID())));
        }
      }
    }
    return listOfPartyCoded;
  }

  public void setListOfPartyCoded(List<InvoicePartyCodedType> partyCodedList) {
    listOfPartyCoded = partyCodedList;
  }

  public List<ContactType> getContacts(InvoiceOCRPartyType party) {
    List<ContactType> contacts = new ArrayList<>();
    if (party != null && party.getOtherContacts() != null && !party.getOtherContacts()
        .getContact()
        .isEmpty()) {
      contacts.addAll(party.getOtherContacts()
          .getContact());
    }
    return contacts;
  }

  public Converter getContactConverter(InvoiceOCRPartyType party) {
    return new ContactConverter(getContacts(party));
  }

  public void onSelectContact(ValueChangeEvent e) {
    InvoiceContactType contact = (InvoiceContactType) e.getNewValue();
    if (selectedParty != null) {
      selectedParty.setPrimaryContact(contact);
    }
  }

  public boolean hasAddress(String partyRoleCoded) {
    return partyRoleCoded != null &&
        getListOfPartyCoded()
            .stream()
            .map(InvoicePartyCodedType::getPartyRoleCoded)
            .anyMatch(partyRoleCoded::equals);
  }

  public Map<String, Tax> getListCodeTva() {
    if (listCodeTva == null) {
      listCodeTva = new TreeMap<>();
      List<Tax> liste = repositoryService.findAllByGroup(Tax.class, this.partner);
      liste.forEach(tax -> listCodeTva.put(String.format(Locale.ROOT, TAX_FORMAT, tax.getPercent()), tax));
    }
    return listCodeTva;
  }

  public Map<String, IndicatorCodeType> getListOfTaxLines() {
    if (listOfTaxLine == null) {
      listOfTaxLine = new TreeMap<>();
      listOfTaxLine.put(getMessage(LABEL_FAMILY + ".ALLOWANCE", "Allowance", selectedLanguage), IndicatorCodeType.LINE_ITEM_ALLOWANCE);
      listOfTaxLine.put(getMessage(LABEL_FAMILY + ".CHARGE", "Charge", selectedLanguage), IndicatorCodeType.LINE_ITEM_CHARGE);
    }
    return listOfTaxLine;
  }

  public boolean isLineAllowanceOrChrgEmpty(InvoiceItemDetailType detail) {
    return detail != null && detail.getInvoicePricingDetail() != null && detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges() != null && !detail
            .getInvoicePricingDetail()
            .getItemAllowancesOrCharges()
            .getAllowOrCharge()
            .isEmpty();
  }

  public int getActiveIndex() {
    return activeIndex;
  }

  public void setActiveIndex(int activeIndex) {
    this.activeIndex = activeIndex;
  }

  public boolean getNewInvoice() {
    return newInvoice;
  }

  public void setNewInvoice(boolean newInvoice) {
    this.newInvoice = newInvoice;
  }

  public List<SelectItem> getListOfGlobalTaxItemsLine() {
    return getListOfTaxLines().values()
        .stream()
        .map(
            SelectItem::new)
        .collect(toCollection(ArrayList::new));
  }

  public List<SelectItem> getTaxItemsAllowances() {
    return getListCodeTva().values()
        .stream()
        .map(tva -> new SelectItem(new BigDecimal(tva.getPercent()), tva.getPercent() + "%"))
        .collect(toList());
  }

  public String getMessage(final String messageKey, final String defaultMessage, final Locale locale, final Object... args) {
    return MessageHelper.getMessage(messageKey, defaultMessage, locale, args);
  }

  /* -- CHECK METHODS -- */

  // TODO : Refacto -> InvoiceXcblHelper
  public boolean checkNorm(Indexable indexable, String norm) {
    if (indexable == null || isBlank(norm)) {
      return false;
    }
    InvoiceIndex invoice = (InvoiceIndex) (isSafeboxSearch() ? ((ArchivedInvoice) indexable).getInvoice() : indexable);
    DocumentCompoundType compoundType = fromJson(invoice.getType());
    return compoundType != null && norm.equals(compoundType.getNorm());
  }

  /* -- VIEW -- */

  // LISTENER

  /**
   * saves an invoice as a target & open upload popup
   */

  public void uploadInvoiceAttachedFile(Indexable doc) {
    saveInvoiceAsTarget(doc);
    FileUploadDialogConfiguration configuration = new FileUploadDialogConfiguration().setTitle("gnxxcblcomlbls.choose_file_to_attached")
        .setSizeLimit(MAX_FILE_SIZE_BYTES)
        .setEnableComment(true);
    openFileUploadDialog(configuration);
  }

  public void saveInvoiceAsTarget(Indexable doc) {
    targetInvoiceForAttachedFile = (InvoiceIndex) doc;
  }

  public Object attachUploadedFile(SelectEvent event) {
    if (event.getObject() instanceof UploadData) {
      try {
        UploadData uploadData = (UploadData) event.getObject();
        UploadedFile uploadedFile = uploadData.getUploadedFile();

        if (uploadedFile == null)
          error("gnxxcblcomlbls.attachment_error_no_file");

        else if (!getBaseName(uploadedFile.getFileName()).matches("[a-zA-Z_\\-.0-9]*"))
          error("gnxxcblcomlbls.attachment_error_incorrect_file_name");

        else if (uploadedFile.getSize() < MAX_FILE_SIZE_BYTES) {
          String comment = uploadData.getComment();

          this.eDocumentService.attachFileToInvoice(this.targetInvoiceForAttachedFile,
              new InMemoryFile(uploadedFile.getFileName(), uploadedFile.getContents()),
              comment, ResultingFileAction.ADD_FILE_TO_INVOICE.getName());
          MessageHelper.info("gnxxcblcomlbls.attachment_successfully_added");
        }
        else {
          LOGGER.warn("Filesize in byte exceeded the maximum of int",
              uploadedFile.getSize());
          MessageHelper.error("gnxxcblcomlbls.error_import_csv_file_too_large", MAX_FILE_SIZE_MB);
        }
      }
      catch (com.byzaneo.generix.edocument.exception.FileAlreadyExistsException e) {
        LOGGER.warn("cannot add the file to the invoice", e);
        error("edctsklbls.att_attachment_error_joining_filealreadyexists");
      }
      catch (final Exception e) {
        LOGGER.warn("cannot add the file to the invoice", e);
        MessageHelper.error("gnxxcblcomlbls.attachment_error_joining", e);
      }
      finally {
        this.targetInvoiceForAttachedFile = null;
      }
    }
    return null;
  }

  public void onConfirmConflictReadableFile() {
    Document targetInvoiceDocument = documentService.getDocument(this.targetInvoiceForAttachedFile);
    String uploadedFileName = this.uploadedFile.getFileName();
    setComment(this.uploadedFileCommentCopy);
    // attach the file with actionName as clientReadable
    this.saveAttachedFile();
    // update the other attached file actionName from clientReadable to addFileToInvoice
    this.eDocumentService.updateAttachedFilesReadableByAddFileToInvoice(targetInvoiceDocument, uploadedFileName);
  }

  public void onCancelConflictReadableFile() {
    // make uploaded file not clientReadable
    this.uploadedFileReadable = false;
    setComment(this.uploadedFileCommentCopy);
    // attach the file with actionName as addFileToInvoice
    this.saveAttachedFile();
  }

  public void onSaveAttachedFile() {
    Document targetInvoiceDocument = documentService.getDocument(this.targetInvoiceForAttachedFile);
    this.uploadedFileCommentCopy = this.comment;
    if (this.uploadedFileReadable && this.eDocumentService.getAttachedFilesReadable(targetInvoiceDocument) > 0) {
      getCurrentInstance().addCallbackParam("showConflictReadableFileModal", true);
    }
    else {
      this.saveAttachedFile();
    }
  }

  public void saveAttachedFile() {
    try {
      if (uploadedFile == null)
        error("gnxxcblcomlbls.attachment_error_no_file");

      else if (!getBaseName(uploadedFile.getFileName()).matches("[a-zA-Z_\\-.0-9]*"))
        error("gnxxcblcomlbls.attachment_error_incorrect_file_name");
      else if (!uploadedFile.getFileName()
          .contains(".") ||
          !AcceptedFilesExtension.getAcceptedFilesExtensions()
              .contains(uploadedFile.getFileName()
                  .substring(uploadedFile.getFileName()
                      .lastIndexOf("."))))
        MessageHelper.error("gnxxcblcomlbls.attachment_types_unaccepted", AcceptedFilesExtension.getAcceptedFilesExtensions()
            .stream()
            .map(type -> type.replace(".", ""))
            .collect(Collectors.joining(", ")));
      else if (uploadedFile.getSize() < MAX_FILE_SIZE_BYTES) {
        if (this.viewer != null) {
          this.eDocumentService.attachFileToInvoice(this.targetInvoiceForAttachedFile,
              new InMemoryFile(uploadedFile.getFileName(), uploadedFile.getContents()),
              getComment(), ResultingFileAction.ADD_FILE_TO_INVOICE_FROM_WORKFLOW.getName());
          if (this.delegatorUserForSelectedInvoice == null) {
            documentTimelineService.saveActionInTimeline(viewer.getDocument(), getCurrentUser(), ATTACHFILE, comment, -1);
          }
          else {
            documentTimelineService.saveActionInTimeline(viewer.getDocument(), getCurrentUser(), ATTACHFILE, comment, -1, null,
                this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
          }
          refreshTimeline();
        }
        else {
          this.eDocumentService.attachFileToInvoice(this.targetInvoiceForAttachedFile,
              new InMemoryFile(uploadedFile.getFileName(), uploadedFile.getContents()),
              getComment(),
              uploadedFileReadable ? ResultingFileAction.CLIENT_READABLE.getName() : ResultingFileAction.ADD_FILE_TO_INVOICE.getName());
          if (this.delegatorUserForSelectedInvoice == null) {
            documentTimelineService.saveActionInTimeline(documentService.getDocument(this.targetInvoiceForAttachedFile), getCurrentUser(),
                ATTACHFILE, comment, -1);
          }
          else {
            documentTimelineService.saveActionInTimeline(documentService.getDocument(this.targetInvoiceForAttachedFile), getCurrentUser(),
                ATTACHFILE, comment, -1, null, this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
          }
        }
        MessageHelper.info("gnxxcblcomlbls.attachment_successfully_added");
      }
      else {
        LOGGER.warn("Filesize in byte exceeded the maximum of int",
            uploadedFile.getSize());
        MessageHelper.error("gnxxcblcomlbls.error_import_csv_file_too_large", MAX_FILE_SIZE_MB);
      }
    }
    catch (com.byzaneo.generix.edocument.exception.FileAlreadyExistsException e) {
      LOGGER.warn("cannot add the file to the invoice", e);
      error("edctsklbls.att_attachment_error_joining_filealreadyexists");
    }
    catch (final Exception e) {
      LOGGER.warn("cannot add the file to the invoice", e);
      MessageHelper.error("gnxxcblcomlbls.attachment_error_joining", e);
    }
    finally {
      this.targetInvoiceForAttachedFile = null;
      uploadedFile = null;
      comment = null;
      this.uploadedFileReadable = false;
      this.uploadedFilePDF = false;
    }
  }

  public void saveDocumentToAttachFile() {
    if (viewer != null) {
      saveInvoiceAsTarget(this.documentService.getIndexable(viewer.getDocument()));
    }
  }

  public void onCompleteQuery(AjaxBehaviorEvent event) {
  }

  public List<DocumentTimeline> getAllTimelinesForInvoice() {
    if (viewer == null) {
      return emptyList();
    }
    return this.documentTimelineService.getAllTimelinesForDocument(viewer.getDocument()
        .getIndexValue()
        .getEntityId());
  }

  public boolean showTimeline() {
    return this.showTimeline && this.invoiceTimelines.size() > 0;
  }

  public void onShowHideTimeline() {
    this.showTimeline = !this.showTimeline;
  }

  public String getTimelineComment(Long timelineId, String comment) {
    if (comment.length() > timelineCommentLimit) {
      if (!this.timelineTruncatedComments.containsKey(timelineId)) {
        this.timelineTruncatedComments.put(timelineId, true);
      }
    }
    return this.timelineTruncatedComments.containsKey(timelineId) && this.timelineTruncatedComments.get(timelineId) == true
        ? comment.substring(0, timelineCommentLimit)
        : comment;
  }

  public void showFullTimelineComment(Long timelineId) {
    this.timelineTruncatedComments.put(timelineId, false);
  }

  public String getUsernameById(String userId) {
    return invoiceService.getDisplayNameForUser(userId);
  }

  public void lockDocumentAndSaveInTimeline() {
    Document document = viewer.getDocument();

    if (document != null) {
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
          getWorkflowForSelectedInvoice());
      if (wDocStatus != null && !wDocStatus.isLocked()) {
        workflowDocumentStatusService.lockDocument(wDocStatus, getCurrentUser());
        lockInvoiceWorkflowIndex(document, true);
        if (this.delegatorUserForSelectedInvoice == null) {
          documentTimelineService.saveActionInTimeline(document, getCurrentUser(), LOCK, EMPTY, wDocStatus.getNumStep());
        }
        else {
          documentTimelineService.saveActionInTimeline(document, getCurrentUser(), LOCK, EMPTY, wDocStatus.getNumStep(), null,
              this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
        }
        refreshTimeline();
      }
      else {
        MessageHelper.error("gnxxcblinvlbls.error_document_locked");
      }
    }
  }

  private void lockInvoiceWorkflowIndex(Document invoiceDocument, boolean lock) {
    InvoiceIndex invoiceIndex = invoiceDocument.getIndexValue();
    if (invoiceIndex == null) {
      return;
    }
    if (lock) {
      invoiceIndex.setWkfLockedById(getCurrentUser().getId());
      invoiceIndex.setWkfLockedBy(getCurrentUser().getFullname());
    }
    else {
      invoiceIndex.setWkfLockedById(null);
      invoiceIndex.setWkfLockedBy(null);
    }
    this.documentService.saveIndexable(invoiceIndex);
  }

  private void refreshTimeline() {
    invoiceTimelines = getAllTimelinesForInvoice();
  }

  public void saveCommentInTimeline() {
    if (this.delegatorUserForSelectedInvoice == null) {
      this.documentTimelineService.saveActionInTimeline(viewer.getDocument(), getCurrentUser(), COMMENT, comment, -1);
    }
    else {
      this.documentTimelineService.saveActionInTimeline(viewer.getDocument(), getCurrentUser(), COMMENT, comment, -1, null,
          this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
    }
    this.comment = null;
    refreshTimeline();
  }

  public void validateWorkflowDocument() {
    validateDocument(documentService.getIndexable(viewer.getDocument()), viewer.getDocument());
    refreshTimeline();
    resetSelectedAndDataModel();
    this.workflowStep = null;
    this.workflowStepRedirect = null;
    this.nextWorkflowStep = null;
    this.nextWkfStepUsers = null;
    this.selectedNextStepUsers = null;
    // reset viewer if standard action, for custom we need to reset at the end of the process
    if (customAction == null) {
      viewerMenuModel = null;
      viewer = null;
    }
  }

  public void validateWorkflowDocuments() {
    if (!isItemSelected() && !this.allIndexesSelected) {
      TaskHelper.warn(this, "gnxxcblcomlbls.error_document_select_line");
    }
    else {
      boolean unauthorizedValidation = false;
      User user = this.getCurrentUser();
      Query sq = resolveSearchQuery();

      if (!allIndexesSelected) {
        // in case the document was validated already
        QueryBuilder qb = createBuilder(sq);
        qb.and(Clauses.in("_entity_id", Arrays.stream(selected)
            .map(Indexable::getEntityId)
            .collect(Collectors.toList())));
        sq = qb.query();
      }
      Indexable[] indexes = loadIndexables(sq, getCurrentModelSort(), getCurrentModelOrder());

      for (Indexable index : indexes) {
        Document document = this.documentService.getDocument(index);
        WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
            workflowDocumentStatusService.getWorkflowForDocument(index.getEntityId()));
        if (wDocStatus != null && (isBlank(wDocStatus.getUserId()) || user.getId()
            .equals(wDocStatus.getUserId())))
          validateDocument(index, document);
        else
          unauthorizedValidation = true;
      }
      if (unauthorizedValidation || (!allIndexesSelected && indexes.length != selected.length))
        error("gnxxcblinvlbls.unauthorizedValidation");
      resetSelectedAndDataModel();
    }
  }

  private void validateDocument(Indexable indexable, Document document) {
    Workflow workflow = workflowDocumentStatusService.getWorkflowForDocument(indexable.getEntityId());
    WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
        workflow);
    if (wDocStatus == null) {
      log.error("Couldn't find the workflow status related to the document {}", document.getNumber());
      return;
    }
    if (workflowStepRedirect != null) {
      workflowStepRedirectService.removeWorkflowStepRedirect(workflowStepRedirect);
    }

    InvoiceIndex invoiceIndex = (InvoiceIndex) indexable;
    if (nextWorkflowStep != null) {
      if (designateNextStepUsers)
        updateWorkflowDocumentUsersAfterDesignation(wDocStatus);
      else 
        wDocStatus.setUserId(null);
      wDocStatus.setNumStep(wDocStatus.getNumStep() + 1);
      wDocStatus.setNbRemind(0);
      wDocStatus.setPauseNotif(false);
      wDocStatus.setModificationDate(new Date());
      // update InvoiceWorkflowIndex
      updateWorkflowPropertiesOnInvoiceIndex(invoiceIndex, wDocStatus, workflow, nextWorkflowStep, securityService, documentService);
      this.documentService.saveIndexable(invoiceIndex);

      wDocStatus.getWorkflowDocumentUsers()
          .removeIf(wdu -> wdu.getNumStep() < wDocStatus.getNumStep());
      workflowDocumentStatusService.save(wDocStatus);
      if (this.delegatorUserForSelectedInvoice == null) {
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), VALID, EMPTY, wDocStatus.getNumStep());
      }
      else {
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), VALID, EMPTY, wDocStatus.getNumStep(), null,
            this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
      }
      List<User> usersToBeNotified = workflowNotificationService.getUsersToBeNotifiedForStep(workflow, wDocStatus.getNumStep(), wDocStatus);
      Map<Document, Map<Locale, DocumentRecipients>> documentRecipiants = new HashMap<Document, Map<Locale, DocumentRecipients>>();
      if (usersToBeNotified != null) {
        Map<Locale, DocumentRecipients> recipients = new HashMap<Locale, DocumentRecipients>();
        documentRecipiants.put(document, recipients);
        usersToBeNotified
            .forEach(
                user ->
                {
                  workflowNotificationService.addRecipient(toLocale(this.getLanguage()), recipients, user,
                      DocumentRecipients::addToInternetAddress);
                });
        workflowNotificationService.notify(documentRecipiants, getInstance(), new HashMap<String, Object>(),
            nextWorkflowStep.getTemplate());
      }
    }
    else {
      if (workflow != null && workflow.getStatusEnd() != null)
        document.setStatus(workflow.getStatusEnd());
      // final of workflow
      removeWorkflowData(invoiceIndex);
      documentService.saveDocument(document);
      workflowDocumentStatusService.removeWorkflowDocumentStatus(wDocStatus);
      if (this.delegatorUserForSelectedInvoice == null) {
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), VALID, EMPTY, wDocStatus.getNumStep());
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), END, EMPTY, wDocStatus.getNumStep());
      }
      else {
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), VALID, EMPTY, wDocStatus.getNumStep(), null,
            this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
        documentTimelineService.saveActionInTimeline(document, getCurrentUser(), END, EMPTY, wDocStatus.getNumStep(), null,
            this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
      }
    }
  }

  public void onDesignateNextStepActors() {
    document = document != null ? document : ofNullable(viewer).map(DocumentViewer::getDocument)
        .orElse(null);
    if (document == null)
      return;
    WorkflowDocumentStatus wkfDocStatus = workflowDocumentStatusService.getCurrentWorkflowDocumentStatusDocId(document.getId());
    if (designateNextStepUsers) {
      nextWkfStepUsers = securityService.getAllUsersOfCurrentWorkflowStep(
          getWorkflowDocumentUsersIdsByStep(wkfDocStatus, wkfDocStatus.getNumStep() + 1))
          .stream()
          .filter(user -> matchesUserFilter(
              securityService.createQueryFromUserRoles(user), user.getBqlInvQuery(), document.getIndexValue()))
          .collect(Collectors.toSet());
      setMultiSelectedNextStepUsers(nextWkfStepUsers.toArray(new User[nextWkfStepUsers.size()]));
      addCallbackParam("openDesignateNextStepActorsDlg", true);
    }
    else if(customAction == null) 
      validateWorkflowDocument();
  }

  public void resetActorsDesignation() {
    getCurrentInstance().execute("PF('wNextStepActorsTable').clearFilters()");
  }

  public void validateDocumentAfterDesignationOfNextStepActors() {
    if (customAction != null) {
      if (!customAction.isConfirmation())
        addCustomActionToTimeline();
      else {
        addCallbackParam("invConfirm", true);
        return;
      }
    }
    validateWorkflowDocument();
    resetActorsDesignation();
  }
  
  private void updateWorkflowDocumentUsersAfterDesignation(WorkflowDocumentStatus wDocStatus) {
    int nextStep = wDocStatus.getNumStep() + 1;
    wDocStatus.getWorkflowDocumentUsers()
        .removeAll(wDocStatus.getWorkflowDocumentUsers()
            .stream()
            .filter(wdu -> wdu.getNumStep() == nextStep)
            .collect(
                Collectors.toList()));
    List<String> userNames = new ArrayList<>();
    if (selectedNextStepUsers.length == 1) {
      wDocStatus.setUserId(selectedNextStepUsers[0].getId());
    }
    else {
      wDocStatus.setUserId(null);
    }
    for (User selectedUser : selectedNextStepUsers) {
      wDocStatus.getWorkflowDocumentUsers()
          .add(new WorkflowDocumentUser(selectedUser.getId(), nextStep));
      userNames.add(selectedUser.getFullname());
    }
    documentTimelineService.saveActionInTimeline(document, getCurrentUser(), TimelineAction.ACTORS_DESIGNATION,
        String.join("; ", userNames), nextStep);
  }

  public void onSelectNextStepUsersCheckbox(SelectEvent event) {
    List<User> totalSelectedIndexes = new ArrayList<>(
        selectedNextStepUsers != null ? Arrays.asList(selectedNextStepUsers) : Arrays.asList(new User[0]));
    totalSelectedIndexes.add((User) event.getObject());
    selectedNextStepUsers = totalSelectedIndexes.toArray(new User[totalSelectedIndexes.size()]);
  }

  public void onUnselectNextStepUsersCheckbox(UnselectEvent event) {
    List<User> totalSelectedIndexes = new ArrayList<>(
        selectedNextStepUsers != null ? Arrays.asList(selectedNextStepUsers) : Arrays.asList(new User[0]));
    totalSelectedIndexes.remove((User) event.getObject());
    selectedNextStepUsers = totalSelectedIndexes.toArray(new User[totalSelectedIndexes.size()]);
  }

  public User[] getMultiSelectedNextStepUsers() {
    return selectedNextStepUsers;
  }

  public void setMultiSelectedNextStepUsers(User[] selectedNextStepUsers) {
    this.selectedNextStepUsers = selectedNextStepUsers;
  }

  public void unlockDocumentAndSaveInTimeline() {
    Document document = viewer.getDocument();

    if (document != null) {
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
          getWorkflowForSelectedInvoice());
      if (wDocStatus != null && wDocStatus.isLocked() && currentUserOrBackupedUserOrDelegatedUSerHasLockedInvoice(wDocStatus, false)) {
        workflowDocumentStatusService.unlockDocument(wDocStatus, getCurrentUser());
        lockInvoiceWorkflowIndex(document, false);
        if (this.delegatorUserForSelectedInvoice == null) {
          documentTimelineService.saveActionInTimeline(document, getCurrentUser(), UNLOCK, EMPTY, wDocStatus.getNumStep());
        }
        else {
          documentTimelineService.saveActionInTimeline(document, getCurrentUser(), UNLOCK, EMPTY, wDocStatus.getNumStep(), null,
              this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
        }
        refreshTimeline();
      }
      else {
        MessageHelper.error("gnxxcblinvlbls.error_document_locked");
      }
    }
  }

  public DefaultMenuModel getViewerMenuModel() {
    if (viewerMenuModel == null) {
      viewerMenuModel = new DefaultMenuModel();
      Document doc = viewer.getDocument();
      this.hasWorkflowAttached = workflowDocumentStatusService.getCurrentWorkflowDocumentStatusDocId(viewer.getDocument()
          .getId()) != null;
      if (hasWorkflowAttached && workflowActionsOnInvoicesForUser.contains(doc.getId())) {
        boolean currentUserHasLockedInvoice = currentUserOrBackupedUserHasLockedInvoice(true);
        if (!isLockedDisplayErrMsg())
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.lock_action", "", JSFHelper.getLocale()), false,
              "jQuery('.jqsactions').hide(); jQuery('.jqsLock').click();");

        if (isLocked())
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.unlock_action", "", JSFHelper.getLocale()),
              !currentUserOrBackupedUserHasLockedInvoice(false), "jQuery('.jqsactions').hide(); jQuery('.jqsUnlock').click();");

        addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.stopNotification_action", "", JSFHelper.getLocale()),
            !currentUserHasLockedInvoice, "jQuery('.jqsactions').hide(); jQuery('.jqsStopNotification').click();");

        ApplicationHandler applicationHandler = getApplicationHandler();
        WorkflowStep step = getWorkflowStepForSelectedInvoice();
        WorkflowStep nextWorkfowStep = getNextWorkflowStepForIndexable(doc.getIndexValue());
        designateNextStepUsers = ofNullable(step).map(WorkflowStep::getDesignateNextStepUsers)
            .orElse(false);
        stepCustomActions = step == null ? null
            : stream(step.getCustomActionsAsArray())
                .map(ActionStepAssociation::getAction)
                .toArray(CustomAction[]::new);
        if (stepCustomActions != null) {
          for (int index = 0; index < stepCustomActions.length; index++) {
            DefaultMenuItem customAction = new DefaultMenuItem(applicationHandler.label(stepCustomActions[index].getName()));
            customAction.setDisabled(!currentUserHasLockedInvoice);
            customAction.setCommand("#{cc.attrs.value.triggerCustomAction(cc.attrs.value.stepCustomActions[" + index + "])}");
            customAction.setImmediate(true);
            customAction.setGlobal(true);
            customAction.setProcess("@this");
            if (stepCustomActions[index].getFormPage() != null) {
              // set props for formpage
              customAction.setOnclick("jQuery('.jqsactions').hide();");
              customAction.setOncomplete("if (args.templateExists) {PF('wShowFormPage').show();}");
              customAction.setUpdate("@(.formPageView,.formPageHiddenButtons)");
            }
            else if (CustomAction.CustomActionType.REFUSAL.equals(stepCustomActions[index].getActionType()) ||
                stepCustomActions[index].isConfirmation()) {
              // set props for confirmation
              if(CustomAction.CustomActionType.VALIDATION.equals(stepCustomActions[index].getActionType()) && nextWorkfowStep != null && designateNextStepUsers) {
                customAction.setUpdate("@(.psDesignateNextStepActorsDialog)");
                customAction.setOncomplete(
                  "jQuery('.jqsactions').hide(); PF('wDesignateNextStepActorsDialog').show(); centerDialogToWindow($('.psDesignateNextStepActorsDialog'));");
              }
              else {
                customAction.setUpdate("@(.psCustomConfirmationDialog)");
                customAction.setOncomplete(
                  "jQuery('.jqsactions').hide(); PF('wCustomConfirmationDialog').show(); centerDialogToWindow($('.psCustomConfirmationDialog'));");
              }
            }
            else {
              // set props for process
              customAction.setOnclick("jQuery('.jqsactions').hide();");
              customAction.setProcess("@this");
              if (CustomAction.CustomActionType.VALIDATION.equals(stepCustomActions[index].getActionType())) {
                if(nextWorkfowStep != null && designateNextStepUsers) {
                  customAction.setUpdate("@(.psDesignateNextStepActorsDialog)");
                  customAction.setOncomplete(
                    "jQuery('.jqsactions').hide(); PF('wDesignateNextStepActorsDialog').show(); centerDialogToWindow($('.psDesignateNextStepActorsDialog'));");
                }
                else
                  customAction.setOncomplete("jQuery('.jqsValidateCA').click();");
              }
              else {
                customAction.setOncomplete("jQuery('.jqsRunProcess').click()");
                customAction.setUpdate("@(.psDocumentPnl)");
              }
            }
            viewerMenuModel.addElement(customAction);
          }

          // if at least one VALIDATION custom action is present in the workflow actions menu
          // don't display the default validation action
          if (stream(stepCustomActions).noneMatch(action -> action.getActionType()
              .equals(CustomAction.CustomActionType.VALIDATION))) {
            addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.validate_action", "", JSFHelper.getLocale()),
                !currentUserHasLockedInvoice, "jQuery('.jqsactions').hide(); jQuery('.jqsValidate').click();");
          }
        }

        if (cancelWorkflowStepRedirect != null)
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.cancel_assign_task_action", "", JSFHelper.getLocale()), false,
              "jQuery('.jqsactions').hide(); jQuery('.jqsCancelAssignTask').click();");

        if (workflowStepRedirect != null)
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.give_back_control_action", "", JSFHelper.getLocale()), false,
              "jQuery('.jqsactions').hide(); jQuery('.jqsGiveBackControl').click();");

        if (workflowStepRedirect != null ? workflowStepRedirect.getEnrich() : step.getEnrich())
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.addData_action", "", JSFHelper.getLocale()),
              !currentUserHasLockedInvoice, "jQuery('.jqsactions').hide(); jQuery('.jqsAddData').click();");

        if (workflowStepRedirect != null ? workflowStepRedirect.getRefuse() : step.getRefuse())
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.refuse_action", "", JSFHelper.getLocale()),
              !currentUserHasLockedInvoice, "jQuery('.jqsactions').hide(); jQuery('.jqsRefuse').click();");

        if (workflowStepRedirect == null && step.getRedirect())
          addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.transferTask_action", "", JSFHelper.getLocale()),
              !currentUserHasLockedInvoice, "jQuery('.jqsactions').hide(); jQuery('.jqsTransferTask').click();");
      }

      addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.comment_action", "", JSFHelper.getLocale()), false,
          "jQuery('.jqsactions').hide(); jQuery('.jqsComment').click();");

      addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.attachFile_action", "", JSFHelper.getLocale()), false,
          "jQuery('.jqsactions').hide(); jQuery('.jqsAttachFile').click();");

      if (showRelaunchReconciliationAction() && !hasReconciliationSuccess())
        addItemToMenu(viewerMenuModel, getMessage("gnxxcblinvlbls.relaunch_reconciliation_action", "", JSFHelper.getLocale()), false,
            "jQuery('.jqsactions').hide(); jQuery('.jqsRelaunchReconciliation').click();");

      viewerMenuModel.generateUniqueIds();
      fileUriMap = new HashMap<>();
    }
    return viewerMenuModel;
  }

  @Override
  public void onValidateFormPageTemplate() {
    if (customAction == null)
      super.onValidateFormPageTemplate();
    else {
      addCallbackParam("openDesignateNextStepActorsDlg",
            designateNextStepUsers && CustomAction.CustomActionType.VALIDATION.equals(customAction.getActionType()));
      addCallbackParam("invValidation",
          !customAction.isConfirmation() && CustomAction.CustomActionType.VALIDATION.equals(customAction.getActionType()));
      addCallbackParam("invConfirm",
          customAction.isConfirmation() || CustomAction.CustomActionType.REFUSAL.equals(customAction.getActionType()));
      addCallbackParam("invRunProcess",
          !customAction.isConfirmation() && CustomAction.CustomActionType.NEUTRAL.equals(customAction.getActionType()));

      if (!customAction.isConfirmation() && !CustomAction.CustomActionType.REFUSAL.equals(
          customAction.getActionType()) && !designateNextStepUsers)
        addCustomActionToTimeline();
    }
  }

  public void triggerCustomAction(CustomAction action) {
    customAction = action;
    document = viewer.getDocument();
    if(designateNextStepUsers && CustomAction.CustomActionType.VALIDATION.equals(customAction.getActionType()))
      onDesignateNextStepActors();
    if (action.getFormPage() != null) {
      // call method for formpage
      Template template = getFormPageTemplateByName(customAction.getFormPage(), getInstance());
      if (template != null) {
        generateFormPageTemplate(template);
        addCallbackParam("templateExists", true);
      }
      else {
        this.formPageTemplateUrl = null;
        addCallbackParam("templateExists", false);
      }
    }
    else if (!customAction.isConfirmation() && !CustomAction.CustomActionType.REFUSAL.equals(customAction.getActionType()) && !designateNextStepUsers)
      addCustomActionToTimeline();
  }

  public void addCustomActionToTimeline() {
    WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(viewer.getDocument(),
        getWorkflowForSelectedInvoice());
    if (this.delegatorUserForSelectedInvoice == null) {
      documentTimelineService.saveActionInTimeline(viewer.getDocument(), getCurrentUser(), CUSTOM_ACTION, customAction.getName(),
          wDocStatus.getNumStep());
    }
    else {
      documentTimelineService.saveActionInTimeline(viewer.getDocument(), getCurrentUser(), CUSTOM_ACTION, customAction.getName(),
          wDocStatus.getNumStep(), null, this.delegatorUserForSelectedInvoice.getFullnameOrLogin());
    }

    refreshTimeline();
  }

  public void onCustomActionProcess() {
    // gets the deployed process
    if (isNotBlank(customAction.getProcess())) {
      final Deployment deployment = this.projectService.getDeployment(customAction.getProcess());
      if (deployment == null) {
        TaskHelper.error(this, null, "error_process_not_deployed");
        return;
      }

      try {
        // get updated document
        Document doc = documentService.getDocument(document.getId());

        Map<String, Object> context;
        if (jsonFormPageValues != null) {
          context = createContext(jsonFormPageValues);
          jsonFormPageValues = null;
        }
        else {
          context = new HashMap<String, Object>();
        }
        context.put("user_id", getCurrentUser().getId());
        context.put("user_login", getCurrentUser().getName());
        context.put(DOCUMENTS.toString(), singletonList(doc));
        context.put(WRITE_ASYNC.toString(), true);
              try {
                checkIfDocumentIsLocked(doc);
                documentLockingService.addDocumentLock(doc);
                projectService.start(deployment, context);
              }
              catch (Exception e) {
                log.error("Error while calling process :", e);
              }
              finally {
                documentLockingService.unlockAllDocuments();
                refreshTimeline();
              }
      }
      catch (Exception e) {
        TaskHelper.error(this, e, "Error while executing process: %s", e.getMessage());
      }
    }
    // reset viewer if action is validate, other cases are taken care of
    if (CustomAction.CustomActionType.VALIDATION.equals(customAction.getActionType()))
      viewer = null;
    resetViewerMenuModel();
    refreshTimeline();
  }

  public void resetViewerMenuModel() {
    viewerMenuModel = null;
    customAction = null;
  }

  private void addItemToMenu(DefaultMenuModel model, String value, boolean disable, String onClick) {
    DefaultMenuItem item = new DefaultMenuItem(value);
    item.setDisabled(disable);
    item.setOnclick(onClick);
    model.addElement(item);
  }

  public boolean isLocked() {
    if (viewer != null) {
      Document document = viewer.getDocument();
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
          getWorkflowForSelectedInvoice());
      if (wDocStatus != null)
        return wDocStatus.isLocked();
    }

    return false;
  }

  public boolean isLockedDisplayErrMsg() {
    if (viewer != null) {
      Document document = viewer.getDocument();
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
          getWorkflowForSelectedInvoice());
      if (wDocStatus != null) {
        if (wDocStatus.isLocked()) {
          if (!currentUserOrBackupedUserOrDelegatedUSerHasLockedInvoice(wDocStatus, false)) {
            displayLockErrMsgOnce();
          }
          return wDocStatus.isLocked();
        }
      }
    }

    return false;
  }

  private void displayLockErrMsgOnce() {
    List<FacesMessage> messageList = FacesContext.getCurrentInstance()
        .getMessageList();
    boolean hasNotLockErrMsg = messageList.stream()
        .filter(m -> m.getSummary()
            .equals(getMessage("gnxxcblinvlbls.error_document_locked", "", null)))
        .count() == 0;

    if (hasNotLockErrMsg) MessageHelper.error("gnxxcblinvlbls.error_document_locked");
  }

  public boolean currentUserOrBackupedUserHasLockedInvoice(boolean onlyForCurrentUser) {
    if (viewer != null) {
      Document document = viewer.getDocument();
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
          getWorkflowForSelectedInvoice());
      if (wDocStatus != null)
        return currentUserOrBackupedUserOrDelegatedUSerHasLockedInvoice(wDocStatus, onlyForCurrentUser);
    }
    return false;
  }

  public boolean currentUserOrBackupedUserOrDelegatedUSerHasLockedInvoice(WorkflowDocumentStatus wDocStatus, boolean onlyForCurrentUser) {
    String userId = wDocStatus.getUserId();
    if (onlyForCurrentUser) {
      return getCurrentUser().getId()
          .equals(userId);
    }
    boolean isFromBackupUsers = currentUserAndBackedupUsersByCurrentUser.stream()
        .map(user -> user.getId())
        .collect(Collectors.toList())
        .contains(userId) || allBackupUsersForCurrentUser.contains(userId);
    boolean isFromDelegatedUsers = currentUserAndDelegatorUsersForCurrentUser.stream()
        .map(user -> user.getId())
        .collect(Collectors.toList())
        .contains(userId) || allDelegatedUsersForCurrentUser.contains(userId);
    return isFromBackupUsers || isFromDelegatedUsers;
  }

  public boolean unclickableStage(InvoiceIndex invoice) {
    return invoice == null ? true : UNCLICKABLE_STAGES.contains(invoice.getStage());
  }

  /* -- VIEW -- */
  // TODO : Use getMenuTree() from IntegrationISHandler to construct the menu
  // dynamically
  public boolean isDisabledActionsMenu(final InvoiceIndex indexable) {
    return !isCorrectable(indexable) && !(isStateIsDiagnosable(indexable) && this.currentSession()
        .isGranted(Portlet_Invoice_Diagnostic, CREATE)) && !(isStateIsCorrectable(indexable) &&
            this.currentSession()
                .isGranted(Portlet_Invoice_Refused_Manually, CREATE)) &&
        !(isStateIsCorrectable(indexable) && this.currentSession()
            .isGranted(Portlet_Invoice_Forced, CREATE)) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Print, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Export, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_View_Attachment, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Join, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Remove, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_View_History, CREATE) &&
        !this.currentSession()
            .isGranted(Workflow_Monitoring, CREATE) &&
        !(enableAction(indexable));
  }

  public boolean isDisabledGlobalActionsMenu() {
    return !this.currentSession()
        .isGranted(Portlet_Invoice_Add, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Import, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Print, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Export, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Export_List, CREATE) &&
        !this.currentSession()
            .isGranted(Workflow_Monitoring, CREATE) &&
        !this.currentSession()
            .isGranted(Portlet_Invoice_Remove, CREATE);
  }

  /* -- UTILS -- */

  // - GETTERS AND SETTERS -

  /**
   * @see AbstractTask#getPathDescriptor()
   */
  @Override
  public String getPathDescriptor() {
    return pathDescriptor;
  }

  /**
   * @see AbstractTask#setPathDescriptor(String)
   */
  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  public boolean isLockForMe() {
    return this.lockForMe;
  }

  public boolean isEditInvoiceInError() {
    return editInvoiceInError;
  }

  public void setEditInvoiceInError(boolean editInvoiceInError) {
    this.editInvoiceInError = editInvoiceInError;
  }

  public boolean isModeCorrectInv() {
    return modeCorrectInv;
  }

  public void setModeCorrectInv(boolean modeCorrectInv) {
    this.modeCorrectInv = modeCorrectInv;
  }

  public boolean isModeDiagnoseInv() {
    return modeDiagnoseInv;
  }

  public void setModeDiagnoseInv(boolean modeDiagnoseInv) {
    this.modeDiagnoseInv = modeDiagnoseInv;
  }

  public boolean isModeForceInv() {
    return modeForceInv;
  }

  public void setModeForceInv(boolean modeForceInv) {
    this.modeForceInv = modeForceInv;
  }

  public List<Report> getReports() {
    return reports == null ? reports = new ArrayList<>() : reports;
  }

  public void setReports(List<Report> reports) {
    this.reports = reports;
  }

  public String getCause() {
    return cause;
  }

  public void setCause(String cause) {
    this.cause = cause;
  }

  public XcblEditionBuilder.XcblEdition getXcblEdition() {
    return xcblEdition;
  }

  public List<XcblPath> getPathesDiagnose() {
    return pathesDiagnose;
  }

  public List<DocumentTimeline> getInvoiceTimelines() {
    return invoiceTimelines;
  }

  public void setInvoiceTimelines(List<DocumentTimeline> invoiceTimelines) {
    this.invoiceTimelines = invoiceTimelines;
  }

  public boolean isShowTimeline() {
    return showTimeline;
  }

  public void setShowTimeline(boolean showTimeline) {
    this.showTimeline = showTimeline;
  }

  public Map<Long, Boolean> getTimelineTruncatedComments() {
    return timelineTruncatedComments;
  }

  public void setTimelineTruncatedComments(Map<Long, Boolean> timelineTruncatedComments) {
    this.timelineTruncatedComments = timelineTruncatedComments;
  }

  public boolean getShowAttach() {
    return showAttach;
  }

  public void setShowAttach(boolean showAttach) {
    this.showAttach = showAttach;
  }

  public Invoice getXcblInvoice() {
    return this.invoiceXcbl;
  }

  public void setIndexable(Invoice indexable) {
    this.invoiceXcbl = indexable;
  }

  // -- OPTIMIZATION --

  /** @see AbstractXcblDocumentTask#isCountEnabled() */
  @Override
  public boolean isCountEnabled() {
    return this.countEnabled;
  }

  public void setCountEnabled(boolean countEnabled) {
    this.countEnabled = countEnabled;
  }

  public boolean isManagePDFCache() {
    return managePDFCache;
  }

  public void setManagePDFCache(boolean managePDFCache) {
    this.managePDFCache = managePDFCache;
  }

  public boolean isShowOnlyWorkflow() {
    return showOnlyWorkflow;
  }

  public void setShowOnlyWorkflow(boolean showOnlyWorkflow) {
    this.showOnlyWorkflow = showOnlyWorkflow;
  }

  public List<User> getCurrentUserAndBackedupUsersByCurrentUser() {
    return currentUserAndBackedupUsersByCurrentUser;
  }

  public void setCurrentUserAndBackedupUsersByCurrentUser(List<User> currentUserAndBackedupUsersByCurrentUser) {
    this.currentUserAndBackedupUsersByCurrentUser = currentUserAndBackedupUsersByCurrentUser;
  }

  public List<String> getAllBackupUsersForCurrentUser() {
    return allBackupUsersForCurrentUser;
  }

  public void setAllBackupUsersForCurrentUser(List<String> allBackupUsersForCurrentUser) {
    this.allBackupUsersForCurrentUser = allBackupUsersForCurrentUser;
  }

  public List<User> getCurrentUserAndDelegatorUsersForCurrentUser() {
    return currentUserAndDelegatorUsersForCurrentUser;
  }

  public void setCurrentUserAndDelegatorUsersForCurrentUser(List<User> currentUserAndDelegatorUsersForCurrentUser) {
    this.currentUserAndDelegatorUsersForCurrentUser = currentUserAndDelegatorUsersForCurrentUser;
  }

  public List<String> getAllDelegatedUsersForCurrentUser() {
    return allDelegatedUsersForCurrentUser;
  }

  public void setAllDelegatedUsersForCurrentUser(List<String> allDelegatedUsersForCurrentUser) {
    this.allDelegatedUsersForCurrentUser = allDelegatedUsersForCurrentUser;
  }

  public void setInvoiceService(InvoiceService invoiceService) {
    this.invoiceService = invoiceService;
  }

  /**
   * The selection will be changed by listeners: onSelectCheckbox onUnselectCheckbox onToggleSelect
   */
  @Override
  public void setMultiSelected(Indexable[] selected) {
  }

  @Override
  public void onRemoveSelected(ActionEvent event) {
    // TODO refactoring don't load the indexable before
    if (!allIndexesSelected && (this.getMultiSelected() == null || this.getMultiSelected().length == 0)) {
      error("xtdtsklbls.error_no_item_selected");
    }
    else {
      if (allIndexesSelected) {
        this.remove(loadIndexables(resolveSearchQuery(), getCurrentModelSort(), getCurrentModelOrder()));
      }
      else {
        this.remove(this.getMultiSelected());
      }
    }
  }

  private void remove(Indexable... indexables) {
    try {
      if (this.documentService.removeDocuments(indexables)) {
        resetSelectedAndDataModel();
      }
    }
    catch (Exception e) {
      TaskHelper.error(this, e, "Error while removing document: %s", e.getMessage());
    }
  }

  private Workflow getWorkflowForSelectedInvoice() {
    InvoiceIndex indexable = this.documentService.getIndexable(viewer.getDocument());
    return workflowDocumentStatusService.getWorkflowForDocument(indexable.getEntityId());
  }

  public WorkflowStep getWorkflowStepForSelectedInvoice() {
    if (workflowStep == null) {
      if (viewer != null) {
        InvoiceIndex indexable = this.documentService.getIndexable(viewer.getDocument());
        workflowStep = workflowDocumentStatusService.getWorkflowStepForDocument(indexable.getEntityId());
      }
      else {
        log.warn("Cannot retrieve the invoice because the viewer is null");
        workflowStep = new WorkflowStep();
      }
    }
    return workflowStep;
  }

  public WorkflowStep getNextWorkflowStepForIndexable(Indexable indexable) {
    if (nextWorkflowStep == null) {
      return invoiceService.getNextWorkflowStepForIndexable(indexable);
    }
    return nextWorkflowStep;
  }

  public void onTransferTask() {
    workflowStepRedirect = new WorkflowStepRedirect();
    workflowStepRedirect.setOriginalUser(getCurrentUser().getId());
    workflowStepRedirect.setDocument(document != null ? document : viewer.getDocument());
  }

  public List<Object> getEnrichActions(Instance instance) {
    if (enrichActions == null) {
      List<TaskType> taskTypes = taskService.getTypes();
      enrichActions = taskTypes.stream()
          .filter(taskType -> StandardInvoiceEditionTask.class.equals(taskType.getType()))
          .map(taskType -> taskService.getTasks(instance, taskType))
          .filter(Objects::nonNull)
          .flatMap(List::stream)
          .filter(action -> !(action instanceof StandardInvoiceEditionTask && !((StandardInvoiceEditionTask) action).isEditInvoice()))
          .collect(toList());
    }
    return enrichActions;
  }

  public List<User> getAllUsersWhoHaveAccessToCurrentInvoice() {
    // create a new user with the id of the current user and the scope contains TO and FROM of the selected invoice
    this.document = this.viewer.getDocument();
    List<Partner> partners = this.securityService.getGroupsByCodes(company.getId(), Arrays.asList(document.getFrom(), document.getTo()));
    User user = new User();
    user.setId(sessionHandler.getUser()
        .getId());
    partners.forEach(partner -> user.addGroup(partner, true));
    return this.securityService.suggest(user, this.getInstance(), this.isDedicatedPortal());
  }

  public void onSaveRedirectUser() {
    WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document, getWorkflowForSelectedInvoice());
    workflowBusinessService.addWorkflowStepRedirectAndUpdateWorkflowDocumentStatus(wDocStatus, workflowStepRedirect, document,
        getCurrentUser(), REDIRECT, securityService.getUser(workflowStepRedirect.getRedirectUser())
            .getFullname(),
        redirectUserComment);
    sendMail(Arrays.asList(securityService.getUser(workflowStepRedirect.getRedirectUser())), "action_assign_doc_mail");
    workflowStepRedirect = null;
    redirectUserComment = null;
    cancelWorkflowStepRedirect = workflowStepRedirectService.getWorkflowStepRedirectByOriginalUserAndDocument(getCurrentUser().getId(),
        document.getId());
    refreshTimeline();
  }

  public void giveBackControl() {
    removeWorkflowStepRedirectAndUpdateWorkflowDocumentStatus(workflowStepRedirect,
        securityService.getUser(workflowStepRedirect.getOriginalUser()));
    workflowStepRedirect = null;
    viewer = null;
    resetSelectedAndDataModel();
    this.workflowStep = null;
  }

  public void cancelAssignTask() {
    removeWorkflowStepRedirectAndUpdateWorkflowDocumentStatus(cancelWorkflowStepRedirect,
        securityService.getUser(cancelWorkflowStepRedirect.getRedirectUser()));
    cancelWorkflowStepRedirect = null;
    refreshTimeline();
  }

  private void removeWorkflowStepRedirectAndUpdateWorkflowDocumentStatus(WorkflowStepRedirect wkfStepRedirect, User user) {
    document = document == null ? viewer.getDocument() : document;
    WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document, getWorkflowForSelectedInvoice());
    workflowBusinessService.removeWorkflowStepRedirectAndUpdateWorkflowDocumentStatus(wDocStatus, wkfStepRedirect, document,
        getCurrentUser(), TASK_CANCEL, user.getFullname());
    sendMail(Arrays.asList(user), "action_cancel_doc_mail");
  }

  public void sendMail(List<User> users, String templateName) {
    Map<Document, Map<Locale, DocumentRecipients>> documentRecipiants = new HashMap<Document, Map<Locale, DocumentRecipients>>();
    HashMap<Locale, DocumentRecipients> recipients = new HashMap<Locale, DocumentRecipients>();
    documentRecipiants.put(document, recipients);
    users.forEach(user -> workflowNotificationService.addRecipient(user.getLocale(), recipients, user,
        (rec, addr) -> rec.addToInternetAddress(addr)));
    if (this.transformService == null)
      this.transformService = getBean(TransformService.class, TransformService.SERVICE_NAME);
    Template template = this.transformService.getTemplates(GLOBAL_TEMPLATES_OWNER, Transform.ThymeLeaf, null)
        .stream()
        .filter(temp -> temp.getName()
            .equals(templateName))
        .findFirst()
        .orElse(null);
    Instance instance = getInstance();
    Map<String, Object> variables = new HashMap<String, Object>();
    variables.put("user", users.get(0));
    variables.put("instance", instance);
    variables.put("fullContextUrl", getUrlForWorkflowNotification());
    workflowNotificationService.notify(documentRecipiants, instance, template, variables);
  }

  public String getUrlForWorkflowNotification() {
    StringBuilder contexctUrl = new StringBuilder();
    if (JSFHelper.getExternalContext() != null) {
      String serverName = Optional.ofNullable(getInstance())
          .map(com.byzaneo.generix.bean.Instance::getHost)
          .map(Host::getName)
          .orElse("");
      contexctUrl.append(getRequest().getScheme());
      contexctUrl.append("://");
      contexctUrl.append("".equals(serverName) ? getRequest().getServerName() : serverName);
      contexctUrl.append(":");
      contexctUrl.append(getRequest().getServerPort());
      contexctUrl.append(getRequest().getContextPath());
      return contexctUrl.toString();
    }
    else {
      return configService.getFullContextUrl();
    }
  }

  public void initializeBackupUsersLists(User currentUser) {
    currentUserAndBackedupUsersByCurrentUser = initializeAndReturnBackupUsersLists(currentUser);
  }

  public List<User> initializeAndReturnBackupUsersLists(User currentUser) {
    // populate currentUserAndBackedupUsersByCurrentUser and currentUserAndAllBackupUsersForCurrentUser so as not to make same calls to
    // the database just to know if Unlock button is disabled or not
    if (currentUserAndBackedupUsersByCurrentUser == null && allBackupUsersForCurrentUser == null) {
      List<String> usersBackedupByCurrentUser = this.workflowUserBackupService.getAllUsersBackedupByCurrentUser(currentUser.getId());
      currentUserAndBackedupUsersByCurrentUser = new ArrayList<User>();
      currentUserAndBackedupUsersByCurrentUser.add(currentUser);
      currentUserAndBackedupUsersByCurrentUser.addAll(usersBackedupByCurrentUser.stream()
          .map(userId -> securityService.getUser(userId))
          .collect(toList()));
      allBackupUsersForCurrentUser = this.workflowUserBackupService.getBackupUsersForCurrentUser(currentUser.getId());
    }
    return currentUserAndBackedupUsersByCurrentUser;
  }

  public void initializeDelegatedUsersLists(User currentUser) {
    currentUserAndDelegatorUsersForCurrentUser = initializeAndReturnDelegatedUsersLists(currentUser);
  }

  public List<User> initializeAndReturnDelegatedUsersLists(User currentUser) {
    if (currentUserAndDelegatorUsersForCurrentUser == null && allDelegatedUsersForCurrentUser == null) {
      List<String> usersDelegatedToCurrentUser = this.workflowUserBackupService.getAllUsersBackedupByCurrentUser(currentUser.getId(),
          InvoiceServiceImpl.AbsenceType.DELEGATION.toString());
      currentUserAndDelegatorUsersForCurrentUser = new ArrayList<User>();
      currentUserAndDelegatorUsersForCurrentUser.add(currentUser);
      currentUserAndDelegatorUsersForCurrentUser.addAll(usersDelegatedToCurrentUser.stream()
          .map(userId -> securityService.getUser(userId))
          .collect(Collectors.toList()));
      allDelegatedUsersForCurrentUser = this.workflowUserBackupService.getBackupUsersForCurrentUser(currentUser.getId(),
          InvoiceServiceImpl.AbsenceType.DELEGATION.toString());
    }
    return currentUserAndDelegatorUsersForCurrentUser;
  }

  @Override
  protected Query resolveSearchQuery() {
    // resolveSearchQuery(qb, user) method is used for angular REST API implementation also where we need to pass user as a parameter.
    // base query
    final QueryBuilder qb = createBuilder(super.resolveSearchQuery());
    return resolveSearchQuery(qb, getCurrentUser());
  }

  public Query resolveSearchQuery(QueryBuilder qb, User user) {
    initializeBackupUsersLists(user);
    initializeDelegatedUsersLists(user);
    Set<User> currentUserAndBackdupAndDelegatedUsers = new HashSet<>();
    currentUserAndBackdupAndDelegatedUsers.addAll(currentUserAndBackedupUsersByCurrentUser);
    currentUserAndBackdupAndDelegatedUsers.addAll(currentUserAndDelegatorUsersForCurrentUser);
    workflowActionsOnInvoicesForUser.clear();
    workflowActionsOnInvoicesForUser = Stream
        .concat(workflowDocumentStatusService.getDocumentsOfConcernedUsers(new ArrayList<>(currentUserAndBackdupAndDelegatedUsers))
                .stream(),
            workflowStepRedirectService.getInvoicesForRedirectedUser(user.getId())
                .stream())
        .collect(Collectors.toList());
    if (isShowOnlyWorkflow()) {
      qb.and(Clauses.in("_entity_id", workflowActionsOnInvoicesForUser));
    }
    qb.append(getQueryFromUserRoles(user));
    qb.append(user.getBqlInvQuery());
    return qb.query();
  }

  // test purpose
  protected Query getBaseQuery() {
    return resolveBaseQuery();
  }

  @Override
  public void setBql(String bql) {
    setQuickSearchBql(bql);
  }

  @Override
  public QueryModel getSearchModel() {
    return super.getSearchModelAdvancedSearch();
  }

  @Override
  public Collection<String> onAutocompleteStart(final CompleteEvent event) {
    return eDocumentService.getFieldSuggestions(event.getFieldName(), event.getFieldValue(), getIndexableType(), getDescriptor(),
        getBaseQuery());
  }

  public boolean hasDifferenceFile() {
    return InvoiceTaskHelper.getReconcilationDocumentFile(this.document != null ? this.document : this.viewer.getDocument()) != null;
  }

  public boolean hasReconciliationSuccess() {
    if (invoiceTimelines == null)
      return false;
    boolean has = invoiceTimelines
        .stream()
        .filter(t -> TimelineAction.RECONCILIATION_SUCCESS.toString()
            .equals(t.getAction()))
        .findFirst()
        .isPresent();
    return has;
  }

  public void onShowReconciliationDialog() {
    this.currentIndexable = this.document != null ? this.document.getIndexValue()
        : this.viewer.getDocument()
            .getIndexValue();
    this.invoiceCurrency = CurrencyCodeAndSign.getSignByCode(((InvoiceIndex) this.currentIndexable).getInvoiceCurrencyCoded());
    this.reconciliationRelation = recRelationService.getReconciliationRelationByInvoiceId(currentIndexable.getEntityId());
    File recDiff = InvoiceTaskHelper.getReconcilationDocumentFile(this.document != null ? this.document : this.viewer.getDocument())
        .getFile();
    try {
      this.reconciliationDiferences = JAXBHelper.unmarshal(ReconciliationDifferences.class, recDiff);
    }
    catch (IOException | JAXBException | XMLStreamException e) {
      TaskHelper.error(this, e, "Invalid reconcilation gap file");
    }
  }

  public List<ReceptionIndex> getReconciliationReceptions() {
    if (reconciliationRelation == null)
      return emptyList();
    reconciliationReceptions = reconciliationRelation.getReceptions()
        .stream()
        .map(r -> ((ReceptionIndex) documentService.getIndexable(r.getId()
            .getReception())))
        .collect(toList());
    return reconciliationReceptions;
  }

  public List<InvoiceIndex> getReconciliationPreviousInvoices() {
    if (reconciliationRelation == null)
      return emptyList();
    reconciliationPreviousInvoices = reconciliationRelation.getPreviousInvoices()
        .stream()
        .map(i -> ((InvoiceIndex) documentService.getIndexable(i.getId()
            .getPreviousInvoice())))
        .collect(toList());
    return reconciliationPreviousInvoices;
  }

  public boolean isCreditNote(InvoiceIndex invoice) {
    return ReconciliationServiceImpl.invoiceTypeCoded.contains(invoice.getInvoiceTypeCoded());
  }

  public void onLifeCycle(InvoiceIndex invoice) {
    indexable = invoice;
    lifeCycle = new InvoiceLifeCycle(this.documentService.getDocument(invoice));
    this.invoiceTimelines = this.documentTimelineService.getAllTimelinesForDocument(indexable
        .getEntityId());
    if (this.invoiceTimelines.size() > 0) {
      this.timelineTruncatedComments = new HashMap<Long, Boolean>();
    }
  }

  public void relaunchReconciliationAction(Instance instance) {
    InvoiceIndex invoice = this.documentService.getIndexable(viewer.getDocument());
    this.document = this.documentService.getDocument(invoice);
    List<Reconciliation> reconciliations = reconciliationService.getReconciliationOrdered(this.document.getOwners());
    Reconciliation matchedReconciliation = reconciliationService.findAppropriateReconciliation(this.document, invoice, reconciliations);
    if (matchedReconciliation == null) {
      MessageHelper.error("gnxxcblinvlbls.reconciliation_not_found");
      return;
    }
    List<ReceptionIndex> matchedReceptions = new ArrayList<>();
    OrderIndex matchedOrder = null;
    List<DocumentTimeline> docTimeline = new ArrayList<DocumentTimeline>();
    docTimeline.add(new DocumentTimeline(this.document, new Date(), TimelineAction.RECONCILIATION_RELAUNCH, getCurrentUser().getId()));

    if (matchedReconciliation.isOrderOnly() ||
        (matchedReconciliation.isOrderAndReception() && !isOnlyQuantityConditionChecked(matchedReconciliation))) {
      matchedOrder = reconciliationService.searchOriginalOrder(this.document, matchedReconciliation, invoice, docTimeline);
    }
    if (matchedReconciliation.isOrderAndReception() && isQuantityConditionChecked(matchedReconciliation)) {
      matchedReceptions = reconciliationService.searchCorrespondingReceptions(this.document, matchedReconciliation, invoice, docTimeline);
    }

    if ((matchedReceptions == null && !matchedReconciliation.isOrderOnly()) ||
        (matchedOrder == null && !isOnlyQuantityConditionChecked(matchedReconciliation))) {
      documentService.saveDocument(this.document);
      documentTimelineService.storeAll(docTimeline);
      refreshTimeline();
      return;
    }
    List<InvoiceIndex> previousInvoicesAndCreditNotes = reconciliationService.searchPreviousInvoicesAndCreditNotes(document,
        matchedReconciliation, invoice,
        docTimeline);

    DocumentFile recDocFile = InvoiceTaskHelper.getReconcilationDocumentFile(this.document);
    File invoiceFromWorkDir = getXcblInvoiceFile(this.document);
    File parentDir = invoiceFromWorkDir.getParentFile();
    File workDir = parentDir.getParentFile();

    if (recDocFile != null) {
      this.document.removeFile(recDocFile);
    }
    reconciliationService.compareInvoiceWithOrderAndReception(this.document, matchedOrder, matchedReceptions, matchedReconciliation,
        instance, invoice, docTimeline, null, workDir, previousInvoicesAndCreditNotes);
    documentService.saveDocument(this.document);

    Document order = documentService.getDocument(matchedOrder);
    ReconciliationRelation relation = recRelationService.getReconciliationRelationByInvoiceId(this.document.getId());
    if (relation == null) {
      relation = new ReconciliationRelation(document, documentService.getDocument(matchedOrder));
    }
    else {
      relation.setOrder(order);
    }
    reconciliationService.addReceptionsAndPreviousInvoicesToRecRelation(relation, matchedReceptions, previousInvoicesAndCreditNotes);
    recRelationService.save(relation);
    documentService.saveIndexable(matchedOrder);
    documentTimelineService.storeAll(docTimeline);
    refreshTimeline();
  }

  public boolean showRelaunchReconciliationAction() {
    if (invoiceTimelines == null)
      return false;
    boolean relaunchReconciliation = invoiceTimelines
        .stream()
        .filter(t -> (TimelineAction.RECONCILIATION_EMPTY_ORDER_NUMBER.toString()
            .equals(t.getAction()) ||
            TimelineAction.RECONCILIATION_ORDER_NOT_FOUND.toString()
                .equals(t.getAction()) ||
            TimelineAction.RECONCILIATION_EMPTY_RECEPTION_NUMBER.toString()
                .equals(t.getAction()) ||
            TimelineAction.RECONCILIATION_RECEPTION_NOT_FOUND.toString()
                .equals(t.getAction()) ||
            TimelineAction.RECONCILIATION_GAP_FOUND.toString()
                .equals(t.getAction())))
        .findFirst()
        .isPresent();
    return relaunchReconciliation;
  }

  public void closeReconciliationDialog() {
    this.currentIndexable = null;
    listOfDifferences = null;
    reconciliationDiferences = null;
  }

  @Override
  protected String[] getAutocompleteField() {

    return getRenderedSearchProperties().stream()
        .filter(p -> String.class.isAssignableFrom(p.getType()))
        .filter(PropertyDescriptor::getAutoComplete)
        .map(p -> p.getName())
        .toArray(String[]::new);

  }

  public List<Object> getActions(Instance instance) {
    if (actions == null) {
      actions = this.getAllActions(instance)
          .stream()
          .filter(action -> !(action instanceof StandardInvoiceEditionTask && !((StandardInvoiceEditionTask) action).isEditInvoice()) &&
              !(action instanceof InvoiceComplianceTask) && !(action instanceof InvoiceControlTask) && !(action instanceof WorkflowMonitoringTask))
          .collect(Collectors.toList());
    }
    return actions;
  }

  public Long getPortalDetailId() {
    return portalDetailId;
  }

  public void setPortalDetailId(Long portalDetailId) {
    this.portalDetailId = portalDetailId;
  }

  public List<Object> getActionsByTypes(Instance instance, List<Class<?>> types) {
    return getAllActions(instance)
        .stream()
        .filter(action -> types.stream()
            .anyMatch(type -> type.isInstance(action)))
        .collect(Collectors.toList());
  }

  public List<Object> getComplianceAndControlActions(Instance instance) {
    if (invoiceComplianceAndControlActions == null) {
      invoiceComplianceAndControlActions = InvoiceCategory.super.getActionsByTypes(instance,
          List.of(InvoiceComplianceTask.class, InvoiceControlTask.class));
    }
    return invoiceComplianceAndControlActions;
  }

  public List<Object> getActionsCreation(Instance instance) {
    if (actionsCreation == null) {
      actionsCreation = this.getAllActions(instance)
          .stream()
          .filter(action -> !(action instanceof StandardInvoiceEditionTask && ((StandardInvoiceEditionTask) action).isEditInvoice()) &&
              !(action instanceof InvoiceComplianceTask) && !(action instanceof InvoiceControlTask) && !(action instanceof WorkflowMonitoringTask))
          .collect(Collectors.toList());
    }
    return actionsCreation;
  }

  public String formatBigDecimal(BigDecimal value) {
    return InvoiceTaskHelper.formatBigDecimal(value, this.currentSession()
        .getLocale());
  }

  public List<Object> getAllActions(Instance instance) {
    List<Class<? extends InvoiceEdition>> invoicingActions = this.getInvoiceActions();
    if (allActions == null) {
      allActions = InvoiceCategory.super.getAllActions(instance, taskService, invoicingActions);
    }
    return allActions;

  }

  public List<InvoiceDifferenceLine> getInvoiceDifferenceLines() {
    if (CollectionUtils.isEmpty(this.listOfDifferences) && this.currentIndexable != null) {
      String invoiceItemCodeField = getInvoiceItemCodeField(this.getInstance());
      listOfDifferences = InvoiceTaskHelper.buildInvoiceDifferenceLines((InvoiceIndex) this.currentIndexable,
          this.reconciliationDiferences, invoiceItemCodeField);
    }
    if (!allDifferences && CollectionUtils.isNotEmpty(listOfDifferences))
      return listOfDifferences.stream()
          .filter(d -> d.isDifference())
          .collect(Collectors.toList());
    return listOfDifferences;
  }

  public String getInvoiceItemCodeField(Instance instance) {
    String invoiceItemCodeField = instanceService.getConfiguration(instance)
        .getReconciliationEngine()
        .getInvoiceItemCodeField();
    return invoiceItemCodeField;
  }

  public List<ReconciliationDifferences.SpecificFooter.Difference> getListOfSpecificFooterDifferences() {
    return Optional.ofNullable(reconciliationDiferences)
        .map(ReconciliationDifferences::getSpecificFooter)
        .map(ReconciliationDifferences.SpecificFooter::getDifference)
        .orElse(emptyList());
  }

  public List<Line> getListOfSpecificLines() {
    return Optional.ofNullable(reconciliationDiferences)
        .map(ReconciliationDifferences::getSpecificLines)
        .map(ReconciliationDifferences.SpecificLines::getLine)
        .orElse(emptyList());
  }

  public String getSpecificLinesCodeAndDescription(String lineNumber) {
    if (listOfDifferences != null) {
      InvoiceDifferenceLine differenceLine = listOfDifferences.stream()
          .filter(diff -> String.valueOf(diff.getLineNumber())
              .equals(lineNumber))
          .findFirst()
          .orElse(null);
      if (differenceLine != null) {
        return differenceLine.getLineNumber() + " " + differenceLine.getProductDescription() + " " +
            differenceLine.getProductCode();
      }
    }
    return "";
  }

  public boolean displaySpecificFooterReceptionColumn() {
    return getListOfSpecificFooterDifferences().stream()
        .anyMatch(diff -> diff.getReceptionValue() != null);
  }

  public boolean displaySpecificLinesReceptionColumn(List<ReconciliationDifferences.SpecificLines.Line.Difference> differences) {
    return differences.stream()
        .anyMatch(diff -> diff.getReceptionValue() != null);
  }

  public DifferenceDetail getDetailBasedType(InvoiceDifferenceLine differences, DifferenceType type) {
    if (differences == null || type == null)
      return new DifferenceDetail();
    return differences.getDifferences()
        .get(type);
  }

  public boolean isIntruderProduct(InvoiceDifferenceLine differences, DifferenceType type) {
    return differences.isIntruderProduct() && (DifferenceType.ORDERED.equals(type) || DifferenceType.RECEPTION.equals(type));
  }

  public Difference getFooterForInvoiceTaxableValue() {
    return reconciliationDiferences.getFooter()
        .getDifference()
        .stream()
        .filter(f -> "invoiceTaxableValue".equals(f.getField()))
        .findFirst()
        .orElse(new Difference());
  }

  public Difference getFooterForInvoiceTotal() {
    return reconciliationDiferences.getFooter()
        .getDifference()
        .stream()
        .filter(f -> "invoiceTotal".equals(f.getField()))
        .findFirst()
        .orElse(new Difference());
  }

  public BigDecimal getReceiptDifference(Difference difference) {
    return InvoiceTaskHelper.calculateDifferenceValue(difference.getInvoiceValue(),
        difference.getOrderValue() != null ? difference.getOrderValue() : difference.getReceptionValue());
  }

  public BigDecimal getReceiptDifferencePercentage(Difference difference) {
    return InvoiceTaskHelper.calculateDifferencePercentage(difference.getInvoiceValue(),
        difference.getOrderValue() != null ? difference.getOrderValue() : difference.getReceptionValue());
  }

  public boolean greaterThanTolerance(BigDecimal value, BigDecimal tolerance) {
    if (value != null && tolerance != null) {
      return value.abs()
          .compareTo(tolerance) == 1;
    }
    else return false;
  }

  public String goToNextPortletCreation(Portal portal) {
    if (nextPortletPageCreation == null) {
      com.byzaneo.portal.bean.Page page = findFirstPageByPortletContentId(portal, valueOf(actionCreateId));
      if (page == null) {

        warn("No Page defined for action : %s", taskService.getTaskDefinition(actionCreateId)
            .getTitle()); // I18N
        return null;
      }
      nextPortletPageCreation = page.getId();
    }
    return nextPortletPageCreation;
  }

  public String goToNextPortlet(Portal portal) {
    if (nextPortletPage == null) {
      Long actionID = getDefinition(this).getParent()
          .getId();
      com.byzaneo.portal.bean.Page page = findFirstPageByPortletContentId(portal, valueOf(actionID));
      if (page == null) {
        warn("No Page defined for action : %s", taskService.getTaskDefinition(actionID)
            .getTitle()); // I18N
        return null;
      }
      nextPortletPage = page.getId();
    }
    return nextPortletPage;
  }

  public BigDecimal getEarlyPaymentValue(String propertyName) {
    return EarlyPaymentHelper.getEarlyPaymentValue(propertyName, (InvoiceIndex) this.getDataModel()
        .getRowData());
  }

  public BigDecimal getEarlyPaymentDiscountRate() {
    return EarlyPaymentHelper.getEarlyPaymentDiscountRate((InvoiceIndex) this.getDataModel()
        .getRowData());
  }

  public Integer getDaysPayedEarlyValue() {
    return EarlyPaymentHelper.getDaysPayedEarlyValue((InvoiceIndex) this.getDataModel()
        .getRowData());
  }

  @Override
  public boolean isInvoicingEnabled() {
    if (isInvoicingEnabled != null) {
      return isInvoicingEnabled;
    }
    List<Class<? extends InvoiceEdition>> invoicingActions = this.getInvoiceActions();
    if (actionType == null && actionCreateId != null) {
      TaskType taskType = taskService.getTaskType(actionCreateId);
      // in case the BO user removes a standardInvoiceEdition portlet which was linked to an invoice and the BO user did not update the
      // invoice portlet the link it still present in the invoice portlet configuration and so actionCreationMod can be null
      if (taskType == null)
        isInvoicingEnabled = false;
      isInvoicingEnabled = invoicingActions.contains(taskType.getType());
    }
    else {
      isInvoicingEnabled = invoicingActions.contains(actionType.getType());
    }
    return isInvoicingEnabled;
  }

  public String getNextPortletPageCreation() {
    return nextPortletPageCreation;
  }

  public void setNextPortletPageCreation(String nextPortletPageCreation) {
    this.nextPortletPageCreation = nextPortletPageCreation;
  }

  public Long getActionCreateId() {
    return actionCreateId;
  }

  public void setActionCreateId(Long actionCreateId) {
    this.actionCreateId = actionCreateId;
    this.isInvoicingEnabled = null;
  }

  @Override
  public List<PropertyDescriptor> getUnrenderedColumns() {
    if (unrenderedColumns == null) {
      unrenderedColumns = this.descriptor.getProperties()
          .stream()
          .filter(property -> !isRendered(property))
          .collect(Collectors.toList());
    }
    return unrenderedColumns;
  }

  public List<DocumentStatusEntity> getStatusesForInvoiceModification() {
    // undo
    // if (statusesForInvoiceModification == null)
    // this.statusesForInvoiceModification = new ArrayList<DocumentStatusEntity>();
    return statusesForInvoiceModification;
  }

  public void setStatusesForInvoiceModification(List<DocumentStatusEntity> statusesForInvoiceModification) {
    this.statusesForInvoiceModification = statusesForInvoiceModification;
  }

  public List<DocumentStatusEntity> getStatusesForInvoiceOcrVerify() {
    if (statusesForInvoiceOcrVerify == null)
      statusesForInvoiceOcrVerify = new ArrayList<>();
    return statusesForInvoiceOcrVerify;
  }

  public void setStatusesForInvoiceOcrVerify(List<DocumentStatusEntity> statusesForInvoiceOcrVerify) {
    this.statusesForInvoiceOcrVerify = statusesForInvoiceOcrVerify;
  }

  // used for ITCase
  public void setWorkflowDocumentStatusService(WorkflowDocumentStatusService workflowDocumentStatusService) {
    this.workflowDocumentStatusService = workflowDocumentStatusService;
  }

  // used for ITCase
  public void setWorkflowStepRedirectService(WorkflowStepRedirectService workflowStepRedirectService) {
    this.workflowStepRedirectService = workflowStepRedirectService;
  }

  public void setSecurityService(SecurityService securityService) {
    this.securityService = securityService;
  }

  // needed for tests to be able to mock
  public void setActionModuleService(ActionModuleService actionModuleService) {
    this.actionModuleService = actionModuleService;
  }

  @Override
  public String getFamilyLabel() {
    return LABEL_FAMILY;
  }

  @Override
  public String getLabelKey() {
    return "invoice_reference_type_INVOICE";
  }

  @Override
  public String getDefaultLabel() {
    return "i";
  }

  @Override
  public int getMaxTriggerActionNumber() {
    return this.TRIGGER_ACTION_MAX_INVOICES;
  }

  @Override
  protected Predicate<? super Indexable> getClassInstance() {
    return InvoiceIndex.class::isInstance;
  }

  @Override
  protected Function<? super InvoiceIndex, ? extends DocumentStatusEntityInterface> getStatus() {
    return InvoiceIndex::getStatus;
  }

  @Override
  protected Class<InvoiceIndex> getIndexableClass() {
    return InvoiceIndex.class;
  }

  @Override
  protected Function<? super Indexable, InvoiceIndex> castClass() {
    return InvoiceIndex.class::cast;
  }

  @Override
  protected Object getThisTask() {
    return InvoiceTask.this;
  }

  @Override
  protected void displayTriggerActionsMessage(TriggerActionWrapper triggerAction, boolean isGlobal, Indexable[] indexables) {
    TaskHelper.info(this, isGlobal ? "triggerActions_execute_info_global" : "triggerActions_execute_info",
        getTriggerActionName(of(triggerAction)), getInvoiceNumber(Stream.of(indexables)
            .findFirst()
            .orElse(null)));
  }

  @Override
  public Range<Integer> getDocumentExportRange() {
    return invoiceDocumentExportRange;
  }

  @Override
  public void setDocumentExportRange(Range<Integer> invoiceDocumentExportRange) {
    this.invoiceDocumentExportRange = invoiceDocumentExportRange;
  }

  public String getRedirectUserComment() {
    return redirectUserComment;
  }

  public void setRedirectUserComment(String redirectUserComment) {
    this.redirectUserComment = redirectUserComment;
  }

  public boolean isShowCommentBlock() {
    return showCommentBlock;
  }

  public void setShowCommentBlock(boolean showCommentBlock) {
    this.showCommentBlock = showCommentBlock;
  }

  public String getComment() {
    return comment;
  }

  public void setComment(String comment) {
    this.comment = comment;
  }

  public WorkflowStep getWorkflowStep() {
    return workflowStep;
  }

  public void setWorkflowStep(WorkflowStep workflowStep) {
    this.workflowStep = workflowStep;
  }

  public WorkflowStepRedirect getWorkflowStepRedirect() {
    return workflowStepRedirect;
  }

  public void setWorkflowStepRedirect(WorkflowStepRedirect workflowStepRedirect) {
    this.workflowStepRedirect = workflowStepRedirect;
  }

  public WorkflowStepRedirect getCancelWorkflowStepRedirect() {
    return cancelWorkflowStepRedirect;
  }

  public void setCancelWorkflowStepRedirect(WorkflowStepRedirect cancelWorkflowStepRedirect) {
    this.cancelWorkflowStepRedirect = cancelWorkflowStepRedirect;
  }

  public String getWorkflowRedirectPage() {
    return workflowRedirectPage;
  }

  public void setWorkflowRedirectPage(String workflowRedirectPage) {
    this.workflowRedirectPage = workflowRedirectPage;
  }

  public void setDocumentTimelineService(DocumentTimelineService documentTimelineService) {
    this.documentTimelineService = documentTimelineService;
  }

  public ReconciliationDifferences getReconciliationDiferences() {
    return reconciliationDiferences;
  }

  public void setReconciliationDiferences(ReconciliationDifferences reconciliationDiferences) {
    this.reconciliationDiferences = reconciliationDiferences;
  }

  public boolean isAllDifferences() {
    return allDifferences;
  }

  public void setAllDifferences(boolean allDifferences) {
    this.allDifferences = allDifferences;
  }

  public boolean isHasWorkflowAttached() {
    return hasWorkflowAttached;
  }

  public void setHasWorkflowAttached(boolean hasWorkflowAttached) {
    this.hasWorkflowAttached = hasWorkflowAttached;
  }

  public InvoiceLifeCycle getLifeCycle() {
    return lifeCycle;
  }

  public String getInvoiceUri() {
    if (this.currentIndexable == null || !(this.currentIndexable instanceof InvoiceIndex))
      return null;

    Document document = this.documentService.getDocument(this.currentIndexable);
    if (document == null)
      return null;

    return getFileUri(document);
  }

  public String getOrderUri() {
    if (this.currentIndexable == null || !(this.currentIndexable instanceof InvoiceIndex))
      return null;

    Order order = orderService.getOrdersFromBuyerOrderNumberAndOwner(company.getCode(),
            ((InvoiceIndex) this.currentIndexable).getBuyerOrderNumber())
        .orElse(null);
    if (order == null)
      return null;

    Document document = this.documentService.getDocument(order.<Long> getEntityId());
    if (document == null)
      return null;

    return getFileUri(document);
  }

  public String getPreviousInvoiceUri(String invoiceNumber) {
    if (CollectionUtils.isEmpty(reconciliationPreviousInvoices) || StringUtils.isBlank(invoiceNumber))
      return null;

    InvoiceIndex previousInvoiceIndex = reconciliationPreviousInvoices.stream()
        .filter(invoice -> invoiceNumber.equals(invoice.getInvoiceNumber()))
        .findFirst()
        .orElse(null);

    if (previousInvoiceIndex == null)
      return null;

    Document document = this.documentService.getDocument(previousInvoiceIndex);
    if (document == null)
      return null;

    return getFileUri(document);
  }

  public String getReceptionUri(String receptionNumber) {
    if (CollectionUtils.isEmpty(reconciliationReceptions) || StringUtils.isBlank(receptionNumber))
      return null;

    ReceptionIndex receptionIndex = reconciliationReceptions.stream()
        .filter(reception -> receptionNumber.equals(reception.getReceptionNumber()))
        .findFirst()
        .orElse(null);

    if (receptionIndex == null)
      return null;

    Document document = this.documentService.getDocument(receptionIndex);
    if (document == null)
      return null;

    return getFileUri(document);
  }

  public String getFileUri(Document doc) {
    if (fileUriMap.containsKey(doc.getId())) {
      return fileUriMap.get(doc.getId());
    }
    String fileURI = computeFirstPdfPath(doc);
    fileUriMap.put(doc.getId(), fileURI);
    return fileURI;
  }

  private String computeFirstPdfPath(Document doc) {
    File file = null;
    if (isNotBlank(doc.getType()) && doc.getType()
        .contains("INVOIC"))
      file = EDocumentServiceHelper.getReadableFile(doc, PDF);
    if (file == null) {
      DocumentFile dof = getFirstPDFFile(doc);
      file = dof != null ? dof.getFile() : null;
    }
    return file == null ? "" : createResourceRequestPath(file, null, null);
  }
}
