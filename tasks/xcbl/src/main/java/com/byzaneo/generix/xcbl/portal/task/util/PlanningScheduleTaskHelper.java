package com.byzaneo.generix.xcbl.portal.task.util;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.Stream.Builder;

import org.apache.commons.lang3.StringUtils;

import com.byzaneo.generix.edocument.util.PlanningScheduleHelper;
import com.byzaneo.generix.xcbl.bean.PlanningSchedule;
import com.byzaneo.generix.xcbl.portal.task.PlanningScheduleTask;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.AbstractDateQuantityPair;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.DateQuantityCodedType;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.DayQuantityPair;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleAddressDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleDatesDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleDatesDialogDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleDetailDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleHeaderDatesDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleItemReferenceDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleLineDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleMonthHeaderDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.PlanningScheduleWeekHeaderDTO;
import com.byzaneo.generix.xcbl.portal.task.bean.planningschedule.WeekQuantityPair;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DateCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DateQualifierType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DimensionType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ItemIdentifiersType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LineItemNumType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfDateCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfDimensionType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfQuantityCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfReferenceCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LocationType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartNumType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartNumbersType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PurchaseOrderReferenceType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.QuantityCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.QuantityValueType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.CommitmentLevelCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ListOfLocationGroupedPlanningDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ListOfLocationPlanningItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ListOfScheduleDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.LocationGroupedPlanningDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.LocationPlanningItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.PlanningScheduleHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ScheduleBaseItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ScheduleDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ScheduleReferencesType;

public class PlanningScheduleTaskHelper {

  /**
   * Based on the given PlanningSchedule create a DTO object used for DELFOR detail display page
   * 
   * @param planningSchedule
   * @param weekStep used to specify how many weeks to be added in a 'month' period
   * @return
   */
  public static PlanningScheduleDetailDTO createPlanningScheduleDetail(PlanningSchedule planningSchedule, int weekStep) {

    // get ScheduleIssueDate null-safe
    Optional<PlanningSchedule> optionalSchedule = Optional.ofNullable(planningSchedule);
    Date issueDate = optionalSchedule.map(PlanningSchedule::getPlanningScheduleHeader)
        .map(PlanningScheduleHeaderType::getScheduleIssueDate)
        .orElse(new Date());

    PlanningScheduleHeaderDatesDTO headerDates = createHeaders(issueDate, weekStep);

    List<LocationGroupedPlanningDetailType> planningDetailList = optionalSchedule
        .map(PlanningSchedule::getListOfLocationGroupedPlanningDetail)
        .map(ListOfLocationGroupedPlanningDetailType::getLocationGroupedPlanningDetail)
        .orElse(new ArrayList<>());

    List<PlanningScheduleLineDTO> planningDetailItemList = planningDetailList.stream()
        .flatMap(planningDetail -> createItemDetailList(planningDetail, issueDate, weekStep).stream())
        .collect(Collectors.toList());

    // contains the table header and the lines
    PlanningScheduleDetailDTO result = new PlanningScheduleDetailDTO();
    result.setHeaderDates(headerDates);
    result.setList(planningDetailItemList);
    return result;
  }

  /**
   * @param dayQuantity
   * @return
   */
  public static DateCodedType createDateCodedType(Date date) {
    DateCodedType dateCodedType = new DateCodedType();
    DateQualifierType value = new DateQualifierType();
    value.setDateQualifierCoded(PlanningScheduleHelper.DELIVERY_DATE_TIME_REQUESTED);// TODO verify otherCodedType is not lost
    dateCodedType.setDateQualifier(value);
    dateCodedType.setDate(date);
    return dateCodedType;
  }

  /**
   * Creates the list of item details DTOs
   * 
   * @param planningDetail
   * @param issueDate the reference date. The schedule for the next weeks will start from this date forward
   * @param weekStep used to specify how many weeks to be added in a 'month' period
   * @return
   */
  private static List<PlanningScheduleLineDTO> createItemDetailList(LocationGroupedPlanningDetailType planningDetail, Date issueDate,
      int weekStep) {
    Optional<LocationGroupedPlanningDetailType> optionalPlanning = Optional.of(planningDetail);
    // create and address DTO
    PlanningScheduleAddressDTO addressDTO = optionalPlanning.map(LocationGroupedPlanningDetailType::getLocation)
        .map(LocationType::getNameAddress)
        .map(addr -> new PlanningScheduleAddressDTO(addr.getExternalAddressID(), addr.getName1(), addr.getStreet(), addr.getHouseNumber(),
            addr.getPostalCode(), addr.getCity()))
        .orElse(new PlanningScheduleAddressDTO());

    List<LocationPlanningItemDetailType> detailList = optionalPlanning
        .map(LocationGroupedPlanningDetailType::getListOfLocationPlanningItemDetail)
        .map(ListOfLocationPlanningItemDetailType::getLocationPlanningItemDetail)
        .orElse(new ArrayList<>());

    return detailList.stream()
        .map(detailType -> createSchedulePlanningLine(detailType, addressDTO, issueDate, weekStep))
        .collect(Collectors.toList());
  }

  /**
   * Creates an item line DTO
   * 
   * @param detailType
   * @param addressDTO
   * @param issueDate the reference date. The schedule for the next weeks will start from this date forward
   * @param weekStep used to specify how many weeks to be added in a 'month' period
   * @return
   */
  private static PlanningScheduleLineDTO createSchedulePlanningLine(LocationPlanningItemDetailType detailType,
      PlanningScheduleAddressDTO addressDTO, Date issueDate, int weekStep) {
    PlanningScheduleLineDTO planningScheduleLineDTO = new PlanningScheduleLineDTO();
    planningScheduleLineDTO.setDeliveryAddress(addressDTO);

    // BasePlanningDetail/ItemScheduleReference/PurchaseOrderReference/BuyerOrderNumber
    Optional<ScheduleBaseItemDetailType> baseItemDetails = Optional.of(detailType)
        .map(LocationPlanningItemDetailType::getBasePlanningDetail);

    planningScheduleLineDTO.setLineNumber(baseItemDetails.map(ScheduleBaseItemDetailType::getLineItemNum)
        .map(LineItemNumType::getBuyerLineItemNum)
        .orElse(0));

    planningScheduleLineDTO.setOrderNumber(baseItemDetails.map(ScheduleBaseItemDetailType::getItemScheduleReference)
        .map(ScheduleReferencesType::getPurchaseOrderReference)
        .map(PurchaseOrderReferenceType::getBuyerOrderNumber)
        .orElse(StringUtils.EMPTY));

    // BasePlanningDetail/ItemIdentifiers/core:PartNumbers/core:BuyerPartNumber/core:PartID
    planningScheduleLineDTO.setBuyerProductId(baseItemDetails.map(ScheduleBaseItemDetailType::getItemIdentifiers)
        .map(ItemIdentifiersType::getPartNumbers)
        .map(PartNumbersType::getBuyerPartNumber)
        .map(PartNumType::getPartID)
        .orElse(StringUtils.EMPTY));

    /// BasePlanningDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber/core:PartID
    planningScheduleLineDTO.setSellerProductId(baseItemDetails.map(ScheduleBaseItemDetailType::getItemIdentifiers)
        .map(ItemIdentifiersType::getPartNumbers)
        .map(PartNumbersType::getSellerPartNumber)
        .map(PartNumType::getPartID)
        .orElse(StringUtils.EMPTY));

    // BasePlanningDetail/ItemIdentifiers/core:ItemDescription
    planningScheduleLineDTO.setProductDescription(baseItemDetails
        .map(ScheduleBaseItemDetailType::getItemIdentifiers)
        .map(ItemIdentifiersType::getItemDescription)
        .orElse(StringUtils.EMPTY));

    List<PlanningScheduleItemReferenceDTO> itemReferences = createItemReferences(baseItemDetails, 3);
    // Collections.reverse(itemReferences);// in case the last 3 items should be in cronological order
    planningScheduleLineDTO.setItemReferences(itemReferences);

    LocalDate date = issueDate.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    int weekNumber = date.get(WeekFields.of(Locale.FRANCE)
        .weekOfWeekBasedYear()); /* use the FRENCH locale in order to start the week from Monday */

    // Initialise the schedule for the next 4 weeks and 4 months (it can actually be any number of weeks not just 4 depending on the
    // weekStep)
    List<PlanningScheduleDatesDTO<DayOfWeek>> weekDates = Stream.generate(() -> new PlanningScheduleDatesDTO<DayOfWeek>(createWeekDays()))
        .limit(4)
        .collect(Collectors.toList());
    List<PlanningScheduleDatesDTO<Integer>> monthDates = initializeMonthDatesCollection(weekStep, weekNumber);

    List<ScheduleDetailType> scheduleDates = Optional.of(detailType)
        .map(LocationPlanningItemDetailType::getListOfScheduleDetail)
        .map(ListOfScheduleDetailType::getScheduleDetail)
        .orElse(Collections.emptyList());

    computeDates(scheduleDates, weekStep, date, weekNumber, weekDates, monthDates);

    planningScheduleLineDTO.setMonthDates(monthDates);
    planningScheduleLineDTO.setWeekDates(weekDates);
    planningScheduleLineDTO.setStatus(baseItemDetails.map(ScheduleBaseItemDetailType::getItemReleaseStatusCodedOther)
        .orElse(PlanningScheduleTask.STATUS_OTHER));
    return planningScheduleLineDTO;
  }

  /**
   * @param baseItemDetails
   * @param numberOfItems
   * @return
   */
  private static List<PlanningScheduleItemReferenceDTO> createItemReferences(Optional<ScheduleBaseItemDetailType> baseItemDetails,
      int numberOfItems) {

    // obtain a list with the last received quantities
    // BasePlanningDetail/ListOfQuantityCodedType/core:QuantityValue (last 3 with QuantityQualifierCoded == )
    List<Double> lastQuantities = baseItemDetails.map(ScheduleBaseItemDetailType::getListOfQuantityCoded)
        .map(ListOfQuantityCodedType::getQuantityCoded)
        .orElse(new ArrayList<>())
        .stream()
        .filter(qty -> PlanningScheduleHelper.QUANTITY_RECEIVED.equals(qty.getQuantityQualifierCoded()))
        .map(QuantityCodedType::getQuantityValue)
        .map(QuantityValueType::getValue)
        .map(BigDecimal::doubleValue)
        .collect(Collectors.toList());

    // get the last numberOfItems elements of the list starting with the last
    lastQuantities = lastQuantities.subList(Math.max(lastQuantities.size() - numberOfItems, 0), lastQuantities.size());
    Collections.reverse(lastQuantities);

    // BasePlanningDetail/ItemScheduleReference/ OtherScheduleReferences/core:ReferenceCoded/core:PrimaryReference/RefNum
    // BasePlanningDetail/ItemScheduleReference/ OtherScheduleReferences/core:ReferenceCoded/core:PrimaryReference/RefDate
    List<ReferenceType> lastReferences = baseItemDetails.map(ScheduleBaseItemDetailType::getItemScheduleReference)
        .map(ScheduleReferencesType::getOtherScheduleReferences)
        .map(ListOfReferenceCodedType::getReferenceCoded)
        .orElse(new ArrayList<>())
        .stream()
        .filter(reference -> PlanningScheduleHelper.DESPATCH_ADVICE_NUMBER.equals(reference.getReferenceTypeCoded()))
        .map(ReferenceCodedType::getPrimaryReference)
        .collect(Collectors.toList());
    // get the last numberOfItems elements of the list starting with the last
    lastReferences = lastReferences.subList(Math.max(lastReferences.size() - numberOfItems, 0), lastReferences.size());
    Collections.reverse(lastReferences);

    List<PlanningScheduleItemReferenceDTO> itemReferences = new ArrayList<>();

    for (int index = 0; index < lastReferences.size(); index++) {
      double qty = index < lastQuantities.size() ? lastQuantities.get(index) : 0.0;
      itemReferences.add(createItemReference(lastReferences.get(index), qty));
    }
    return itemReferences;
  }

  /**
   * Computes the the schedule for the next 4 weeks and 4 months (it can actually be any number of weeks not just 4 depending on the
   * weekStep)
   * 
   * @param scheduleDates all the dates to be processed
   * @param weekStep used to specify how many weeks to be added in a 'month' period
   * @param date the reference date. The schedule for the next weeks will start from this date forward
   * @param weekNumber
   * @param weekDatesOutput
   * @param monthDatesOutput
   */
  private static void computeDates(List<ScheduleDetailType> scheduleDates, int weekStep, LocalDate date, int weekNumber,
      List<PlanningScheduleDatesDTO<DayOfWeek>> weekDatesOutput, List<PlanningScheduleDatesDTO<Integer>> monthDatesOutput) {

    // create an initial list of date/quantity pairs based on the values from scheduleDates
    List<DayQuantityPair> dateQuantityList = scheduleDates.stream()
        .flatMap(scheduleDate -> createDayQuantityPairs(scheduleDate, false).stream())
        .collect(Collectors.toList());

    // Add the date/quantity elements to the weekDatesOutput and monthDatesOutput lists to their corresponding location
    // if 2 or more dates coincide the quantity will be incremented
    for (DayQuantityPair dayQuantityPair : dateQuantityList) {
      // if the date is before the issue date+ 4 weeks
      // add it to the planningScheduleLineDTO.weekDates
      LocalDate curentDate = dayQuantityPair.getDate()
          .toInstant()
          .atZone(ZoneId.systemDefault())
          .toLocalDate();
      int currentWeekNumber = curentDate.get(WeekFields.of(Locale.FRANCE)
          .weekOfWeekBasedYear());

      if ((date.isBefore(curentDate) || date.isEqual(curentDate)) && date.plusWeeks(4)
          .isAfter(curentDate)) {
        weekDatesOutput.get(currentWeekNumber - weekNumber)
            .getDateQuantityList()
            .stream()
            .filter(day -> day.getId()
                .equals(curentDate.getDayOfWeek()))
            .findFirst()
            .ifPresent(day -> updateQuantities(day, dayQuantityPair.getQuantity(), dayQuantityPair.getLevel()));
        ;
      }
      // otherwise if the date is after the 4 weeks and before the step(nb of weeks) * 4
      else if (date.isBefore(curentDate) && date.plusWeeks(4 * (weekStep + 1))
          .isAfter(curentDate)) {
        int weekDiff = currentWeekNumber - weekNumber - 4;/* first week after the first 4 weeks */
        // there are 4 elements in the monthDatesOutput and we need to get one corresponding to our week number
        monthDatesOutput.get(weekDiff / weekStep)
            .getDateQuantityList()
            .stream()
            .filter(week -> week.getId() == currentWeekNumber)
            .findFirst()
            .ifPresent(week -> updateQuantities(week, dayQuantityPair.getQuantity(), dayQuantityPair.getLevel()));
        ;
      }
    }
  }

  private static List<PlanningScheduleDatesDTO<Integer>> initializeMonthDatesCollection(int step, int weekNumber) {
    Builder<PlanningScheduleDatesDTO<Integer>> builder = Stream.<PlanningScheduleDatesDTO<Integer>> builder();
    for (int index = 0; index < 4; index++) {
      int initialWeekNumber = weekNumber + 4 + (index * step);
      builder = builder.add(new PlanningScheduleDatesDTO<Integer>(createMonthWeeks(initialWeekNumber, step)));
    }
    List<PlanningScheduleDatesDTO<Integer>> monthDates = builder.build()
        .collect(Collectors.toList());
    return monthDates;
  }

  /**
   * @param day
   * @param quantity
   * @param commitmentLevelCodeType
   */
  private static void updateQuantities(AbstractDateQuantityPair<?> day, double quantity, CommitmentLevelCodeType commitmentLevelCodeType) {
    day.setLevel(commitmentLevelCodeType);
    day.setQuantity(day.getQuantity() + quantity);
  }

  public static List<AbstractDateQuantityPair<Integer>> createMonthWeeks(int initialWeekNumber, int step) {
    List<AbstractDateQuantityPair<Integer>> weekDays = new ArrayList<>();
    for (int index = 0; index < step; index++) {
      weekDays.add(new WeekQuantityPair(initialWeekNumber + index));
    }
    return weekDays;

  }

  public static List<AbstractDateQuantityPair<DayOfWeek>> createWeekDays() {
    List<AbstractDateQuantityPair<DayOfWeek>> weekDays = new ArrayList<>();
    weekDays.add(new DayQuantityPair(DayOfWeek.MONDAY));
    weekDays.add(new DayQuantityPair(DayOfWeek.TUESDAY));
    weekDays.add(new DayQuantityPair(DayOfWeek.WEDNESDAY));
    weekDays.add(new DayQuantityPair(DayOfWeek.THURSDAY));
    weekDays.add(new DayQuantityPair(DayOfWeek.FRIDAY));
    return weekDays;
  }

  public static PlanningScheduleDatesDialogDTO createDatesDialogDTO(LocationPlanningItemDetailType item, Date issueDate) {
    PlanningScheduleDatesDialogDTO datesDTO = new PlanningScheduleDatesDialogDTO();

    datesDTO.setIssueDate(issueDate);

    Optional<ScheduleBaseItemDetailType> baseItemDetails = Optional.of(item)
        .map(LocationPlanningItemDetailType::getBasePlanningDetail);

    datesDTO.setLineNumber(baseItemDetails.map(ScheduleBaseItemDetailType::getLineItemNum)
        .map(LineItemNumType::getBuyerLineItemNum)
        .orElse(0));

    datesDTO.setReference(baseItemDetails.map(ScheduleBaseItemDetailType::getItemIdentifiers)
        .map(ItemIdentifiersType::getPartNumbers)
        .map(PartNumbersType::getBuyerPartNumber)
        .map(PartNumType::getPartID)
        .orElse(StringUtils.EMPTY));

    List<DimensionType> dimensions = baseItemDetails.map(ScheduleBaseItemDetailType::getListOfDimension)
        .map(ListOfDimensionType::getDimension)
        .orElse(new ArrayList<>());

    for (int index = 0; index < dimensions.size(); index++) {
      DimensionType dimension = dimensions.get(index);
      if (index == 0) {
        datesDTO.setFirstUnitType(dimension.getDimensionCodedOther());
        datesDTO.setFirstQuantity(PlanningScheduleHelper.getDimensionQuantity(dimension)
            .doubleValue());
      }
      else if (index == 1) {
        datesDTO.setSecondUnitType(dimension.getDimensionCodedOther());
        datesDTO.setSecondQuantity(PlanningScheduleHelper.getDimensionQuantity(dimension)
            .doubleValue());
      }
    }

    List<ScheduleDetailType> scheduleDates = Optional.of(item)
        .map(LocationPlanningItemDetailType::getListOfScheduleDetail)
        .map(ListOfScheduleDetailType::getScheduleDetail)
        .orElse(Collections.emptyList());

    List<DateQuantityCodedType> dateQuantityList = new ArrayList<>();
    scheduleDates.stream()
        .forEach(detail -> {
          List<DateCodedType> dates = Optional.of(detail)
              .map(ScheduleDetailType::getScheduleDates)
              .map(ListOfDateCodedType::getDateCoded)
              .orElse(Collections.emptyList());

          List<QuantityCodedType> quantites = Optional.of(detail)
              .map(ScheduleDetailType::getScheduleQuantities)
              .map(ListOfQuantityCodedType::getQuantityCoded)
              .orElse(Collections.emptyList());

          for (int index = 0; index < dates.size(); index++) {
            dateQuantityList.add(new DateQuantityCodedType(dates.get(index), quantites.get(index)));
          }
        });

    datesDTO.setDateQuantityList(dateQuantityList);
    return datesDTO;
  }

  /**
   * Obtain a list of date,quantity pairs for the given scheduleDetail
   * 
   * @param scheduleDetail
   * @return
   */
  private static List<DayQuantityPair> createDayQuantityPairs(ScheduleDetailType scheduleDetail, boolean onlyFirm) {

    List<DateCodedType> scheduleDates = Optional.of(scheduleDetail)
        .map(ScheduleDetailType::getScheduleDates)
        .map(ListOfDateCodedType::getDateCoded)
        .orElse(Collections.emptyList());

    List<QuantityCodedType> quantites = Optional.of(scheduleDetail)
        .map(ScheduleDetailType::getScheduleQuantities)
        .map(ListOfQuantityCodedType::getQuantityCoded)
        .orElse(Collections.emptyList());

    List<DayQuantityPair> days = new ArrayList<>();
    if (scheduleDates.size() > quantites.size()) {
      return Collections.emptyList();
    }

    for (int index = 0; index < scheduleDates.size(); index++) {
      DateCodedType dateCodedType = scheduleDates.get(index);
      QuantityCodedType quantityCodedType = quantites.get(index);

      // add only the FIRM dates
      if (onlyFirm && !CommitmentLevelCodeType.FIRM.equals(scheduleDetail.getCommitmentLevelCoded())) {
        continue;
      }
      // only the dates qualified as 'DELIVERY_DATE_TIME_REQUESTED' will be taken into consideration
      if (dateCodedType.getDateQualifier() == null || dateCodedType.getDate() == null ||
          !PlanningScheduleHelper.DELIVERY_DATE_TIME_REQUESTED.equals(dateCodedType.getDateQualifier()
              .getDateQualifierCoded())) {
        continue;
      }
      // only the quantities qualified as 'QUANTITY_SCHEDULED' will be taken into consideration
      if (quantityCodedType.getQuantityQualifierCoded() == null ||
          !PlanningScheduleHelper.QUANTITY_SCHEDULED.equals(quantityCodedType.getQuantityQualifierCoded())) {
        continue;
      }
      Date date = dateCodedType.getDate();

      DayQuantityPair dayQuantity = new DayQuantityPair();
      dayQuantity.setDate(date);
      dayQuantity.setId(date.toInstant()
          .atZone(ZoneId.systemDefault())
          .toLocalDate()
          .getDayOfWeek());

      dayQuantity.setQuantity(Optional.of(quantityCodedType)
          .map(QuantityCodedType::getQuantityValue)
          .map(QuantityValueType::getValue)
          .map(BigDecimal::doubleValue)
          .orElse(0.0));
      dayQuantity.setLevel(scheduleDetail.getCommitmentLevelCoded());
      days.add(dayQuantity);
    }
    return days;
  }

  private static PlanningScheduleItemReferenceDTO createItemReference(ReferenceType refType, double quantity) {
    PlanningScheduleItemReferenceDTO planningScheduleItemReferenceDTO = new PlanningScheduleItemReferenceDTO();
    planningScheduleItemReferenceDTO.setRefDate(refType.getRefDate());
    planningScheduleItemReferenceDTO.setRefNum(refType.getRefNum());
    planningScheduleItemReferenceDTO.setQuantity(quantity);
    return planningScheduleItemReferenceDTO;
  }

  private static PlanningScheduleHeaderDatesDTO createHeaders(Date issueDate, int weekStep) {
    PlanningScheduleHeaderDatesDTO headerDates = new PlanningScheduleHeaderDatesDTO();

    // get the localDate representation of the ScheduleIssueDate
    LocalDate date = issueDate.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    for (int i = 0; i < 4; i++) {
      /* WEEK HEADER */
      LocalDate currentDate = date.plusWeeks(i);// increment weeks
      // get first day of the week
      /* use the FRENCH locale in order to start the week from Monday */
      LocalDate firstDayOfWeek = currentDate.with(WeekFields.of(Locale.FRANCE)
          .dayOfWeek(), 1);
      // get the week number in the year
      int weekNumber = currentDate.get(WeekFields.of(Locale.FRANCE)
          .weekOfWeekBasedYear());
      // add values
      headerDates.addWeekHeader(new PlanningScheduleWeekHeaderDTO(weekNumber, java.sql.Date.valueOf(firstDayOfWeek)));

      /* MONTH HEADER (actually weekStep weeks) */
      // get the first day of the next months (4 weeks)
      currentDate = date.with(WeekFields.of(Locale.FRANCE)
          .dayOfWeek(), 1);
      currentDate = currentDate.plusWeeks(4);
      currentDate = currentDate.plusWeeks(weekStep * i);

      // get start date and end date of that period
      Date dateStart = java.sql.Date.valueOf(currentDate);
      Date dateEnd = java.sql.Date.valueOf(currentDate.plusWeeks(weekStep)
          .minusDays(1));
      headerDates.addMonthHeader(new PlanningScheduleMonthHeaderDTO(dateStart, dateEnd));
    }
    return headerDates;
  }

  public static QuantityCodedType createQuantityCodedType(double quantity) {
    QuantityCodedType quantityType = new QuantityCodedType();
    quantityType.setQuantityQualifierCoded(PlanningScheduleHelper.QUANTITY_SCHEDULED);
    QuantityValueType value = new QuantityValueType();
    value.setValue(BigDecimal.valueOf(quantity));
    quantityType.setQuantityValue(value);
    return quantityType;
  }

}
