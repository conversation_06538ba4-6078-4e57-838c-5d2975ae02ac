package com.byzaneo.generix.xcbl.portal.task;

import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.util.PoiHelper.stream;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.buildMessageKey;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.util.QueryHelper.toBql;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.security.spring.UserDetailsService.getAuthenticatedUser;
import static com.byzaneo.task.util.TaskHelper.error;
import static org.apache.commons.lang3.StringUtils.capitalize;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;

import java.io.*;
import java.math.BigDecimal;
import java.text.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import javax.xml.datatype.XMLGregorianCalendar;

import org.apache.commons.lang3.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.model.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort.Direction;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.generix.service.repository.service.RepositoryService;
import com.byzaneo.generix.util.BeanDescriptorHelper.Restriction;
import com.byzaneo.generix.xcbl.api.HistoryExporter;
import com.byzaneo.generix.xcbl.bean.*;
import com.byzaneo.generix.xcbl.portal.task.util.HistoryTaskHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.Query.ContextKey;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.clause.*;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document_;
import com.byzaneo.xtrade.ui.*;
import com.byzaneo.xtrade.xcbl.bean.Contract;
import com.byzaneo.xtrade.xcbl.service.XcblService;
import com.lowagie.text.*;

@Task(name = "History", library = "portal/xcbl", version = "1.0.0")
@TaskViewModel(labelFamily = "gnxxcblhistlbls")
public class HistoryTask extends AbstractXcblDocumentTask<History> {

  private static final long serialVersionUID = 6497763043657510731L;

  protected static final String LABEL_HISTORY = "gnxxcblhistlbls";

  @Autowired
  @Qualifier(XcblService.SERVICE_NAME)
  private transient XcblService xcblService;

  @Autowired
  @Qualifier(com.byzaneo.generix.xcbl.service.XcblService.SERVICE_NAME)
  private transient com.byzaneo.generix.xcbl.service.XcblService xcblServiceGnx;

  @Autowired
  @Qualifier(RepositoryService.SERVICE_NAME)
  private transient RepositoryService repositoryService;

  private transient Map<String, List<Product>> products;

  private static final BigDecimal TON = new BigDecimal("1000");

  private transient BigDecimal totalTonnage;

  private int exportLimit = 1000;

  private transient Map<HistoryProductKey, HistoryProduct> historyProductList;

  /** @see com.byzaneo.generix.xcbl.portal.task.AbstractXcblDocumentTask#getEDocument() */
  @Override
  public EDocument getEDocument() {
    return null;
  }

  @Override
  protected void indexProperties(BeanDescriptor descriptor, boolean global) {
    if (log.isInfoEnabled()) {
      log.info("Start Creating indexes");
    }

    BeanDescriptor indexesDescriptor;
    try {
      indexesDescriptor = beanService.fromClasspath("descriptors/history-columns.xml");

      for (PropertyDescriptor prop : descriptor.getProperties()) {
        if (prop.getRendered() && !prop.getSortable()) {
          PropertyDescriptor indexesProperty = indexesDescriptor.get(prop.getName());
          indexesProperty.setSortable(true);

          xcblService.addFieldIndex(this.getIndexableType(), indexesProperty.getName(), indexesProperty.getLabel(), Direction.ASC);
        }
      }
    }
    catch (IOException ex) {
      error(this, ex, "{0}", getRootCauseMessage(ex));
    }
  }

  @Override
  public void onResetAdvancedSearch() {
    // total tonnage only changes when the query changes
    totalTonnage = null;
    super.onResetAdvancedSearch();
  }

  @Override
  public void onAdvancedSearchModel() {
    // total tonnage only changes when the query changes
    totalTonnage = null;
    super.onAdvancedSearchModel();
  }

  // use by old advance search (advSearch.xhtml)
  @Override
  public BeanDescriptor getSearch() {
    if (search == null && getDescriptor() != null) {
      search = getDescriptor().cloneSafely();
      List<PropertyDescriptor> properties = this.search.getProperties();
      for (ListIterator<PropertyDescriptor> it = properties.listIterator(); it.hasNext();) {
        PropertyDescriptor pd = it.next();
        if (pd.getType() == XMLGregorianCalendar.class || pd.getType() == Date.class) {
          CustomPropertyDescriptor cpd = new CustomPropertyDescriptor(pd);
          cpd.setDatePeriod(new DatePeriod());
          it.set(cpd);
        }
      }
      search.setProperties(properties);
    }
    return search;
  }

  /**
   * @return wether the selection column is rendered
   */
  public boolean isSelectionRendered(PropertyDescriptor col) {
    // since only partners can confirm an Order, only they are enabled
    // to select Orders bound to be confirmed
    return isPartnerUser();
  }

  @Override
  public Logs importDocuments(List<InputStream> is) {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isMultipleValues(PropertyDescriptor property) {

    if ("saleCondition".equals(property.getName()) || "status".equals(property.getName()) ||
        "productDescription".equals(property.getName()))
      return true;

    return false;
  }

  /**
   * Returns the description for a product from Product collection if it contains one, otherwise null
   * 
   * @param productName
   * @param to
   * @return
   */
  private String getProductDescriptionFromProducts(String productName, String to) {
    return getProducts().containsKey(to) ? getProducts().get(to)
        .stream()
        .filter(p -> StringUtils.equalsIgnoreCase(p.getProductName(), productName))
        .findFirst()
        .map(Product::getDescription)
        .filter(StringUtils::isNotEmpty)
        .orElse(null) : null;
  }

  @Override
  public List<String> getValues(PropertyDescriptor property) {
    resolveOrganizationCodes();
    if (isOriginalStatusProperty(property)) {
      return partner != null ? xcblServiceGnx.findDistinctStatusFromHistory(partner.getCode())
          : xcblServiceGnx.findDistinctStatusFromHistory();
    }
    else if ("productDescription".equals(property.getName())) {
      return getHistoryProductList().keySet()
          .stream()
          .map(HistoryProductKey::toString)
          .collect(Collectors.toList());
    }

    return null;
  }

  @Override
  public boolean containsValue(PropertyDescriptor property) {
    return true;
  }

  @Override
  public String getValueStyleClass(History indexable, PropertyDescriptor property, String environmentCode) {
    return "";
  }

  public Map<String, List<Product>> getProducts() {
    resolveOrganizationCodes();

    if (products == null) {
      List<Product> productList = null;
      if (partner != null) {
        productList = repositoryService.findAllByGroup(Product.class, partner);
      }
      else if (company != null) {
        productList = repositoryService.findAllByGroup(Product.class, company);
      }
      products = new HashMap<>();
      for (Product product : productList) {
        if (!products.containsKey(product.getTo()))
          products.put(product.getTo(), new LinkedList<Product>());
        products.get(product.getTo())
            .add(product);
      }
    }

    return products;
  }

  private Map<HistoryProductKey, HistoryProduct> getHistoryProductList() {
    if (historyProductList == null) {
      List<HistoryProduct> values = partner != null ? xcblServiceGnx.getAllHistoryProducts(partner)
          : xcblServiceGnx.getAllHistoryProducts(company);

      historyProductList = new HashMap<>();
      for (HistoryProduct historyProduct : values) {
        HistoryProductKey key = historyProduct.getKey();
        key.setProductDescription(getProductDescriptionFromProducts(key.getProductName(), historyProduct.getPartner()));

        if (historyProductList.containsKey(historyProduct.getKey())) {
          historyProductList.get(historyProduct.getKey())
              .addPartners(historyProduct.getPartners());
        }
        else {
          historyProductList.put(historyProduct.getKey(), historyProduct);
        }
      }
    }
    return historyProductList;
  }

  /**
   * Returns the product description from the HistoryProduct with the given key or else returns the product name
   * 
   * @param labelKey
   * @return
   */
  private String getProductDescriptionOrNameFromHistoryProduct(String labelKey) {
    HistoryProductKey key = HistoryProductKey.fromString(labelKey);
    return StringUtils.isNotEmpty(key.getProductDescription()) ? key.getProductDescription()
        : key.getProductName();
  }

  @Override
  public String label(String labelKey) {
    String ret;
    // Status Roquette Specifique
    if (EnumUtils.isValidEnum(DocumentStatus.class, labelKey)) {
      return getLabel(LABEL_HISTORY, labelKey, "", getLocale());
    }
    else {
      ret = super.label(labelKey);
      if (ret.equals(labelKey)) {
        // for label we need an empty string if it's null, otherwise it's going to use the labelKey
        return Optional.ofNullable(getProductDescriptionOrNameFromHistoryProduct(labelKey))
            .orElse("");
      }
    }

    return ret;
  }

  @Override
  public String label(Locale locale, String labelPrefix, String labelKey) {

    final String messageKey = buildMessageKey(labelPrefix, labelKey);
    if (messageKey.isEmpty()) {
      return "";
    }
    /* Status label */
    if (EnumUtils.isValidEnum(DocumentStatus.class, labelKey)) {
      return getLabel(LABEL_CORE, labelKey, "", getLocale());
    }
    return TaskHelper.label(this, locale, messageKey, capitalize(labelKey));
  }

  // TODO: Needs refactoring
  /**
   * Return product description if provided otherwise return its name
   *
   * @param currentHistory
   * @return
   */
  public String getProductDescriptionOrName(History currentHistory,
      String labelKey) {

    if (getProducts() == null)
      return "";

    String procuctReference = getProductReference(currentHistory);

    return products.containsKey(currentHistory.getTo()) ? products.get(currentHistory.getTo())
        .stream()
        .filter(p -> procuctReference.equals(p.getReference()))
        .findFirst()
        .map(p -> StringUtils.isNotEmpty(p.getDescription())
            ? p.getDescription()
            : p.getProductName())
        .orElse("") : "";
  }

  private String getProductReference(History currentHistory) {
    QueryBuilder contractQb = createBuilder();
    contractQb.and(equal(Document_.owners.getName(), currentHistory.getOwners()));
    contractQb.and(equal(Document_.to.getName(), currentHistory.getTo()));
    contractQb.and(equal("shippingScheduleHeader.scheduleID", currentHistory.getContractNumber()));
    Contract contract = this.documentService.searchIndexable(Contract.class, contractQb.query());
    return contract.getListOfMaterialGroupedShippingDetail()
        .getMaterialGroupedShippingDetail()
        .get(0)
        .getBaseShippingDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID();
  }

  public String getTotalTonnage() {
    DecimalFormat df = new DecimalFormat("#.00");

    return totalTonnage != null ? df.format(totalTonnage) : "0.00";
  }

  @Override
  protected LazyDataModel<History> resolveDataModel() {
    return new HistoryDataModel(resolveSearchQuery());
  }

  @Override
  public Query resolveSearchQuery() {
    QueryBuilder qbTest = createBuilder(this.resolveBaseQuery());
    List<Clause> clauseDescr = new ArrayList<Clause>();

    PropertyDescriptor propDescr = this.getSearch()
        .get("productDescription");
    String[] propDescrValues = (String[]) propDescr.getValue();
    if (propDescrValues != null) { // propDescrValues is NULL when not selecting values
      for (String key : propDescrValues) {
        // Each value in "propDescrValues" is a key with this format: {ProductName} + {_:_} + {ProductDecription}
        // the productDescriptor and list of partner identifiers are contained in the value of the key
        // When we build the query for productDescription we will have the productDescription = productName1 and _to in (list of partner
        // identifiers)

        HistoryProduct historyProduct = getHistoryProductList().get(HistoryProductKey.fromString(key));
        clauseDescr.add(new AndClause(Clauses.equal("productDescription", historyProduct.getKey()
            .getProductName()),
            Clauses.in("_to", historyProduct.getPartners())));

      }

      if (!clauseDescr.isEmpty()) {
        qbTest.or(clauseDescr);
      }
    }

    // We built and set the "productDescription" part of the query above using "propDescrValues"
    this.getSearch()
        .get("productDescription")
        .setValue(null);

    // The rest of the advance search clauses are computed here
    qbTest.and(computeAdvSearchClauses(this.getSearch()
        .getProperties()));

    // search query
    qbTest.append(this.userQuery);
    qbTest.append(this.userQueryQuickSearch);

    String q = toBql(qbTest.query());

    // resolve property name for search
    if (q.indexOf('[') > -1) {
      q = q.replace("[", ".");
      q = q.replace("]", "");
    }

    // search query
    return toQuery(q);
  }

  public final class HistoryDataModel extends AbstractIndexableDataModel<History> {
    private static final long serialVersionUID = -6729324800659701990L;

    public HistoryDataModel(Query query) {
      super(documentService, query, getSearch());
    }

    /** @see AbstractPersistentDataModel#load(PageRequest, Map) */
    @Override
    public Page<History> load(PageRequest pageable, Map<String, Object> filters) {
      // initializes query builder with the base query
      final QueryBuilder qb = createBuilder(query);
      // adds filtering
      this.addFilterClauses(qb, filters);
      // processes loading request

      // load context on query
      Query query = qb.query();
      query.getContext()
          .put(ContextKey.PARTNER_SCOPE, getUserCodes());
      Page<History> histories = this.service.searchIndexables(this.type, query, pageable);

      if (totalTonnage == null)
        totalTonnage = xcblServiceGnx.getTotalTonnage(query, TON);

      return histories;
    }
  }

  public void onDownloadPDF(Object document) {
    Document pdf = (Document) document;
    pdf.setPageSize(PageSize.A4.rotate());
    pdf.setMargins(1.0f, 1.0f, 1.0f, 1.0f);
    pdf.open();
  }

  public StreamedContent onDownloadCSV() {
    StreamedContent export = null;

    try (Workbook workbook = new XSSFWorkbook()) {

      BeanDescriptor beanDescriptor = beanService.getBeanDescriptorByName("gnxHistory")
          .cloneSafely();

      List<PropertyDescriptor> properties = beanDescriptor.getProperties();

      for (int i = 0; i < properties.size(); i++) {
        PropertyDescriptor property = properties.get(i);

        if (partner != null && (Restriction.Company.toString()
            .equals(property.getRenderRestrict()))) {
          properties.remove(i);
          i--;
        }
      }

      beanDescriptor.setProperties(properties);

      Map<String, List<Product>> products = getProducts();

      HistoryExporter exporter = new HistoryExporter(workbook, beanDescriptor);
      exporter.setHeader(getAuthenticatedUser().getLocale());

      Query q = this.resolveSearchQuery();
      q.getContext()
          .put(ContextKey.PARTNER_SCOPE, perimeter);
      Page<History> allHistories = documentService.searchIndexables(History.class, q, PageRequest.of(0, exportLimit));

      allHistories.getContent()
          .forEach(h -> exporter.addRows(HistoryTaskHelper.getHistory(products, h)));

      SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMdd-HHmm");
      export = new DefaultStreamedContent(stream(workbook), "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          String.format("export-list-order-%s.xlsx", sdf.format(new Date())));
    }
    catch (Exception e) {
      error(this, e, "{0}", getRootCauseMessage(e));
    }

    return export;
  }

  @Override
  public String getPathDescriptor() {
    if (pathDescriptor == null)
      pathDescriptor = "descriptors/history-columns.xml";
    return pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  // ACCESSORS
  public int getExportLimit() {
    return exportLimit;
  }

  public void setExportLimit(int exportLimit) {
    this.exportLimit = exportLimit;
  }

}