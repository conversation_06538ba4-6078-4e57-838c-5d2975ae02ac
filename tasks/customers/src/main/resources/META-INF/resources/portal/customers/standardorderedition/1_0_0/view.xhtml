<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
			  xmlns:f="http://xmlns.jcp.org/jsf/core"
			  xmlns:cc="http://xmlns.jcp.org/jsf/composite"
			  xmlns:p="http://primefaces.org/ui"
			  xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx/front"
			  xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
			  xmlns:gcst="http://xmlns.jcp.org/jsf/composite/components/customers"
			  xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
			  xmlns:fn="http://java.sun.com/jsp/jstl/functions"
			  xmlns:h="http://xmlns.jcp.org/jsf/html"
			  xmlns:stdord="http://xmlns.jcp.org/jsf/composite/portal/customers/standardorderedition/1_0_0">

	<cc:interface name="standardOrderEditionView">
		<cc:attribute name="value" required="true" type="com.byzaneo.generix.customers.portal.task.StandardOrderEditionTask" />
	</cc:interface>

	<cc:implementation>
		<script type="text/javascript">
			$( document ).ready(function() {
				changePortletTitle();
				processAddresses();
				processDeliveryDateHidden();
				processDeliveryDate();
			});

			function scrollToTop() {
				parent.document.body.scrollTop = parent.document.documentElement.scrollTop = 0;
			}

			var checkout = true;

			// prevents the user to exit the page to display a message to warn of unsaved data
			// (does not matter what message you put, a default browser message will be showed)
			window.onbeforeunload = function (evt) {
				if (!checkout) {return;}
				return '';
			}

			function setCheckout(value){
				checkout = value;
			}

			function stepOneCalendarCallback(date) {
				// primefaces js widget day is from [0 - 6] - [Sunday - Monday]
				var day = date.getDay() + 1;
				// populate Calendar#DAY_OF_WEEK values from bean
				if($('.deliverySchedule')) {
					var deliverySchedule = Array.from($('.deliverySchedule').html());
					for (var i = 0; i &lt; deliverySchedule.length; i++) {
						// If supported day, verify if is not an holyday
						if (day == deliverySchedule[i]) {
							var m = date.getMonth(), d = date.getDate(), y = date.getFullYear();
							if ($.inArray(d + '-' + (m) + '-' + y) != -1) {
								return [false, ''];
							}
							return [true, 'deliverySchedule',''];
						}
					}
				}
				return [false, ''];
			}

			var height = null;
			var setHeight = null;
			var indicator;
			var wizzard;
			// function to calculate the margin-top of the indicators based of the height of the wizzard and the position of the scrollbar
			function changeIndicatorsPosition(newPage){
				if(!newPage){
					var currentHeight = $(window).scrollTop();
					if(currentHeight === 0){
						var parent = window.parent.document;
						currentHeight = $(parent).scrollTop();
					}
					if(indicator &amp;&amp; wizzard &amp;&amp; setHeight >= 0){
						if(setHeight == 0){
							indicator.style.marginTop = 0 + "px";
						}
						else if(wizzard.offsetHeight > indicator.offsetHeight){
							var marginTop = wizzard.offsetHeight - indicator.offsetHeight;
							var descriptionLabelHeight = $('.task-description')[0];
							var addedHeight = 150;
							// need to decrease 15, the height of the portlet description field in case it is not rendered from BO
							if(!descriptionLabelHeight){
								addedHeight = addedHeight - 15;
							}
							if(marginTop + addedHeight > currentHeight){
								marginTop = marginTop - (marginTop-currentHeight) - addedHeight;
								if(0 > marginTop){
									marginTop = 0;
								}
								indicator.style.marginTop = marginTop + "px";
							}
						}
					}
					if(!(currentHeight === setHeight)){
						setHeight = currentHeight;
					}
				}
			}

			function changePortletTitle() {
				var portletTitle = $(".task-title")[0];
				var title = $(".stepTitle")[0];
				if(portletTitle &amp;&amp; title){
					portletTitle.innerText = title.innerText.toUpperCase();
				}
				setIndicators($('.stepProgressPanel')[0]);
				setWizzard($('.wizardStyleClass')[0]);
				changeIndicatorsPosition(true);
			}

			window.parent.addEventListener('scroll', function() {
				changeIndicatorsPosition(false);
			});

			function setWizzard(wizz){
				wizzard = wizz;
			}

			function setIndicators(ind){
				indicator = ind;
			}

			function removeBottomMessage() {
				$('#tskForm-taskMsgTop').css('display','none');
				$('#tskForm-taskMsgBot').css('display','none');
			}

		</script>
		<gnx:errorMessage rendered="#{!cc.attrs.value.isPartnerUserConnected()}" msg="#{standardorderlbls.error_create_order}" />
		<p:messages closable="true" autoUpdate="true" id="stOrderEditionErrorMsg" severity="error"/>
		<p:messages closable="true" autoUpdate="true" id="stOrderEditionWarnMsg" severity="warn"/>
		<p:messages closable="true" autoUpdate="true" id="stOrderEditionInfoMsg" severity="info"/>
		<h:outputText styleClass="d-none" value="#{cc.attrs.value.getOrderToDuplicate()}"></h:outputText>
		<h:outputText styleClass="d-none" value="#{cc.attrs.value.xcblDocument}"/>
		<p:outputPanel id="wizardSection" styleClass="wizardStyleClass" rendered="#{cc.attrs.value.isPartnerUserConnected()}">
			<stdord:standardOrderStepOne step="#{cc.attrs.value.wizard.currentSection}" value="#{cc.attrs.value}"/>
			<stdord:standardOrderStepTwo step="#{cc.attrs.value.wizard.currentSection}" value="#{cc.attrs.value}"/>
			<stdord:standardOrderStepThree step="#{cc.attrs.value.wizard.currentSection}" value="#{cc.attrs.value}" owner="#{cc.attrs.owner}"/>
			<stdord:standardOrderStepFour step="#{cc.attrs.value.wizard.currentSection}" value="#{cc.attrs.value}"/>

			<p:outputPanel styleClass="col-md-10">
				<p:outputPanel styleClass="floatRight">
					<p:commandButton value="#{cc.attrs.value.wizard.currentSection.previousTitle}"
									 rendered="#{not cc.attrs.value.wizard.firstSection and not cc.attrs.value.wizard.lastSection}"
									 actionListener="#{cc.attrs.value.wizard.onPreviousSection}"
									 immediate="true"
									 process="@this wizardSection"
									 update="wizardSection"
									 oncomplete="changePortletTitle();scrollToTop();"
									 global="false"
									 styleClass="btn btn-secondary"/>

					<p:commandButton value="#{cc.attrs.value.getMultilanguageLabelValue(cc.attrs.value.orderDraft.draftLabel)}"
									 rendered="#{cc.attrs.value.orderDraft.draft and not cc.attrs.value.wizard.lastSection}"
									 actionListener="#{cc.attrs.value.onSaveDraft()}"
									 process="@this"
									 update="wizardSection"
									 oncomplete="removeBottomMessage();"
									 global="false"
									 styleClass="btn btn-outline-primary"/>

					<p:commandButton value="#{cc.attrs.value.wizard.currentSection.nextTitle}"
									 rendered="#{cc.attrs.value.wizard.currentSection.name == 'StandardOrderEditionStepOne'}"
									 actionListener="#{cc.attrs.value.wizard.onNextSection}"
									 process="@this wizardSection"
									 update="wizardSection"
									 oncomplete="changePortletTitle();removeBottomMessage();scrollToTop();if(!args.validationFailed){showDraftMessages();}"
									 global="false"
									 styleClass="btn btn-primary"/>

					<p:commandButton value="#{cc.attrs.value.wizard.currentSection.nextTitle}"
									 rendered="#{cc.attrs.value.wizard.currentSection.name == 'StandardOrderEditionStepTwo'}"
									 actionListener="#{cc.attrs.value.wizard.onNextSection}"
									 process="@this wizardSection"
									 update="wizardSection"
									 oncomplete="changePortletTitle();removeBottomMessage();scrollToTop();"
									 global="false"
									 styleClass="btn btn-primary"/>

					<p:commandButton value="#{labels.confirm}"
									 rendered="#{cc.attrs.value.wizard.currentSection.name == 'StandardOrderEditionStepThree'}"
									 disabled="#{cc.attrs.value.stepTwo.listProducts.size() == 0}"
									 actionListener="#{cc.attrs.value.onValidateIndicators()}"
									 process="@this wizardSection"
									 update="wizardSection"
									 oncomplete="if(args.indicatorsValidation){PF('wIndicatorsErrorDlg').show();} else{callRTE();scrollToTop();}"
									 global="false"
									 styleClass="btn btn-primary"/>
				</p:outputPanel>
				  <p:outputPanel styleClass="floatLeft" rendered="#{cc.attrs.value.wizard.currentSection.name == 'StandardOrderEditionStepTwo' and cc.attrs.value.stepTwo.allowCsvUpload}">
				    <p:commandButton actionListener="#{cc.attrs.value.exportAsCSV()}"
									 oncomplete="if(args &amp;&amp; args.download){jQuery('.jqsDownload').click(); window.onbeforeunload = null};"
									 process="@this wizardSection"
									 update="@(.jqsDownload)"
									 global="false"
									 immediate="true"
									 title="#{standardorderlbls.export_csv_btn}"
                                     icon="fa fa-download"
									 styleClass="btn btn-outline-primary"/>
				    <p:commandButton value="#{standardorderlbls.upload_csv_btn}"
									 process="@this wizardSection"
									 update="@(.psUploadStOrderPnl)"
									 oncomplete="PF('standardUploadDialog').show();"
									 global="false"
									 immediate="true"
									 styleClass="btn btn-outline-primary"/> 
				   </p:outputPanel>
			</p:outputPanel>

			<p:remoteCommand name="callRTE" actionListener="#{cc.attrs.value.onConfirmOrder()}"
							 oncomplete="if(!args.validationFailed){PF('wConfirmOrderDlg').show();}else{removeBottomMessage();}" process="@this wizardSection" update="wizardSection"/>

			<p:dialog id="indicatorsErrorDlg"
					  modal="true"
					  closable="true"
					  closeOnEscape="true"
					  resizable="false"
					  styleClass="psDlgConfirmation responsive-dialog"
					  responsive="true"
					  width="500px"
					  onShow="if(typeof centerDialogToWindow === 'function') {centerDialogToWindow($('.psDlgConfirmation'));}"
					  header="#{cc.attrs.value.errorMessages.size() > 0 ? standardorderlbls.indicators_error_title : standardorderlbls.indicators_warn_title}"
					  widgetVar="wIndicatorsErrorDlg">
				<p:outputPanel styleClass="container-fluid">
					<p:dataList value="#{cc.attrs.value.errorMessages.size() > 0 ? cc.attrs.value.errorMessages : cc.attrs.value.warnMessages}" var="message">
						<p:outputLabel value="#{message}"/>
					</p:dataList>
				</p:outputPanel>
				<p:outputPanel styleClass="ui-dialog-footer text-right">
					<p:commandButton value="#{labels.cancel}"
									 styleClass="btn btn-secondary"
									 type="button">
						<p:ajax event="click" oncomplete="PF('wIndicatorsErrorDlg').hide();" global="false"/>
					</p:commandButton>
					<p:commandButton value="#{labels.OK}"
									 styleClass="btn btn-primary"
									 process="@this wizardSection"
									 rendered="#{cc.attrs.value.errorMessages.size() == 0}"
									 update="wizardSection"
									 oncomplete="callRTE();"
									 onclick="PF('wIndicatorsErrorDlg').hide();">
					</p:commandButton>
				</p:outputPanel>
			</p:dialog>

			<p:dialog id="confirmOrderDlg"
							 modal="true"
							 closable="true"
							 closeOnEscape="true"
							 resizable="false"
							 styleClass="psDlgConfirmation responsive-dialog"
							 responsive="true"
							 width="500px"
							 onShow="if(typeof centerDialogToWindow === 'function') {centerDialogToWindow($('.psDlgConfirmation'));}"
							 header="#{labels.confirm}"
							 widgetVar="wConfirmOrderDlg">
				<p:outputPanel styleClass="container-fluid">
					<p:outputLabel  value="#{cc.attrs.value.getConfirmOrderMessage()}"/>
				</p:outputPanel>
				<p:outputPanel styleClass="text-right responsive-buttons">
					<p:commandButton value="#{labels.no}"
									 styleClass="btn btn-secondary"
									 type="button">
						<p:ajax event="click" oncomplete="PF('wConfirmOrderDlg').hide();" global="false"/>
					</p:commandButton>
					<p:commandButton value="#{labels.yes}"
									 styleClass="btn btn-primary"
									 process="@this wizardSection"
									 actionListener="#{cc.attrs.value.onSaveOrder()}"
									 update="wizardSection"
									 oncomplete="changePortletTitle();scrollToTop();setCheckout(false);"
									 onclick="PF('wConfirmOrderDlg').hide();">
					</p:commandButton>
				</p:outputPanel>
			</p:dialog>
			<!-- UPLOAD CSV -->
			<p:outputPanel styleClass="psUploadStOrderPnl">
             <p:dialog modal="true"
                      closable="true"
                      closeOnEscape="true"
                      onShow="if(typeof changeDialogYPosition === 'function') {changeDialogYPosition('import-invoice', 'resultTable');}"
                      styleClass="responsive-dialog import-invoice"
                      header="#{standardorderlbls.upload_csv_dlg}"
                      widgetVar="standardUploadDialog"
                      id="standardUploadDialog"
                      responsive="true">
              <p:ajax event="close" immediate="true" listener="#{cc.attrs.value.onCancelUpload()}"/>
               <p:outputPanel styleClass="displayOnGenerixCustomerTemplate">
                  <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-12">
                      <p:outputPanel styleClass="form-group">
                         <p:fileUpload id="loadFile"
                                       fileUploadListener="#{cc.attrs.value.selectFileToUpload}"
                                       mode="advanced"
                                       process="@this"
                                       update="@this @(.custom-file) @(.import-button)"
                                       styleClass="jqsHiddenUpload"
                                       multiple="false"
                                       allowTypes="/.(csv)$/"
                                       auto="true"/>
                         <p:message for="loadFile"/>
                         <p:outputPanel class="custom-file">
                            <h:outputLabel for="file-input"
                                           value="#{cc.attrs.value.getFileNameForPlaceholder()}"
                                           styleClass="custom-file-label form-control-rounded lang-#{gnxSessionHandler.getLocale()}"/>
                              <p:inputText id="file-input"
                                           type="button"
                                           styleClass="custom-file-input"
                                           onclick="$(document.getElementsByClassName('jqsHiddenUpload')[0]).find('input').click();"/>
                        </p:outputPanel>
                    </p:outputPanel>
                   </p:outputPanel>
                  </p:outputPanel>
                  <p:outputPanel id="buttonsPanel"
                                   styleClass="ui-dialog-footer text-right responsive-buttons pull-right">
                        <p:commandButton value="#{labels.close}"
                                         actionListener="#{cc.attrs.value.onCancelUpload()}"
                                         onclick="PF('standardUploadDialog').hide();"
                                         process="@this"
                                         styleClass="btn-gnx btn-gnx-default btn btn-secondary"/>
                        <p:commandButton value="#{labels.import}"
                                         process="@this wizardSection"
                                         actionListener="#{cc.attrs.value.uploadCSVFile()}"
			                             disabled="#{cc.attrs.value.uploadedFile == null}"
			                             styleClass="import-button btn btn-primary"
			                             oncomplete="PF('standardUploadDialog').hide();removeBottomMessage();scrollToTop();"
			                             update="@(.resultTable) wizardSection"/> 
                 </p:outputPanel>
              </p:outputPanel>
             </p:dialog>
            </p:outputPanel>
            <!-- DOWNLOAD -->
            <p:commandLink id="download" ajax="false" value=""
                         actionListener="#{cc.attrs.value.onDownload()}"
                         styleClass="jqsDownload hidden"/>
		</p:outputPanel>
	</cc:implementation>
</ui:component>