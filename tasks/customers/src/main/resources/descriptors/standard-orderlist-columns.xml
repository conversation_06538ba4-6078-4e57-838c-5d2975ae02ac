<?xml version="1.0" encoding="UTF-8"?>
<descriptor name="StandardOrderList" sortBy="modificationDate"
	sortAsc="false" version="4">
	<properties>
		<property name="buyerOrderNumber"
			type="java.lang.String" rendered="true" render-restrict="None"
			readonly="false" editable="true" required="false" sortable="false">
			<label>buyerOrderNumber</label>
		</property>
		<property
			name="buyerIdentification"
			alias="buyerIdentification"
			type="java.lang.String" rendered="true" readonly="false"
			editable="true" required="false" sortable="false">
			<label>primaryReferenceRefNum</label>
			<model-expression>"buyerIdentification"</model-expression>
		</property>
		<property name="sellerOrderNumber"
			type="java.lang.String" rendered="true" readonly="false"
			editable="true" required="false" sortable="false">
			<label>sellerOrderNumber</label>
		</property>
		<property name="orderType"
			type="java.lang.String" rendered="true" readonly="false"
			editable="true" required="false" sortable="false">
			<label>orderTypeCodedOther</label>
		</property>
		<property name="issueDate"
			type="javax.xml.datatype.XMLGregorianCalendar" rendered="true"
			render-restrict="None" readonly="false" editable="true"
			required="false" sortable="false">
			<label>orderIssueDate</label>
		</property>
		<property
			name="orderRequestedDeliverByDate"
			type="javax.xml.datatype.XMLGregorianCalendar" rendered="true"
			render-restrict="None" readonly="false" editable="true"
			required="false" sortable="false">
			<label>requestedDeliverByDate</label>
		</property>
		<property name="orderPromiseDate"
			type="javax.xml.datatype.XMLGregorianCalendar" rendered="true"
			render-restrict="None" readonly="false" editable="true"
			required="false" sortable="false">
			<label>promiseDate</label>
		</property>
		<property name="buyerPartyID"
			path="orderHeader.orderParty.buyerParty.partyID.ident"
			type="java.lang.String" rendered="true" render-restrict="Partners"
			readonly="false" editable="true" required="false" sortable="false">
			<label>buyerIdent</label>
		</property>
		<property name="buyerPartyName"
			path="orderHeader.orderParty.buyerParty.nameAddress.name1"
			type="java.lang.String" rendered="true" render-restrict="Partners"
			readonly="false" editable="true" required="false" sortable="false">
			<label>buyerName1</label>
		</property>
		<property name="sellerPartyID"
			path="orderHeader.orderParty.sellerParty.partyID.ident"
			type="java.lang.String" rendered="true" render-restrict="Company"
			readonly="false" editable="true" required="false" sortable="false">
			<label>sellerIdent</label>
		</property>
		<property name="sellerPartyName"
			path="orderHeader.orderParty.sellerParty.nameAddress.name1"
			type="java.lang.String" rendered="true" render-restrict="Company"
			readonly="false" editable="true" required="false" sortable="false">
			<label>sellerName1</label>
		</property>
		<property name="shipToPartyID"
			path="orderHeader.orderParty.shipToParty.partyID.ident"
			type="java.lang.String" rendered="false" readonly="false" editable="true"
			required="false" sortable="false">
			<label>ident</label>
		</property>
		<property name="shipToPartyName"
			path="orderHeader.orderParty.shipToParty.nameAddress.name1"
			type="java.lang.String" rendered="true" render-restrict="None"
			readonly="false" editable="true" required="false" sortable="false">
			<label>shipToname1</label>
		</property>
		<property name="numberOfLines"
			path="orderSummary.numberOfLines"
			type="java.lang.Integer"
			rendered="false" readonly="false" editable="true" required="false"
			sortable="false">
			<label>numberOfLines</label>
		</property>
		<property name="orderSubTotal"
			path="orderSummary.orderSubTotal.monetaryAmount"
			type="java.math.BigDecimal" rendered="false" readonly="false"
			editable="true" required="false" sortable="false">
			<label>monetaryAmount</label>
		</property>
		<property name="orderTotal"
			path="orderSummary.orderTotal.monetaryAmount"
			type="java.math.BigDecimal"
			rendered="false" readonly="false" editable="true" required="false"
			sortable="false">
			<label>monetaryAmount</label>
		</property>
		<property name="orderCurrency"
			path="orderHeader.orderCurrency.currencyCoded"
			type="java.lang.Class" rendered="false" readonly="true" editable="false"
			required="false" sortable="false">
			<label>currencyCoded</label>
		</property>
		<property name="creationDate" path="creationDate" type="java.util.Date" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>creationDate</label>
		</property>
		<property name="modificationDate" type="java.util.Date" rendered="true" render-restrict="None" readonly="false" editable="false" required="false" sortable="true">
			<label>modificationDate</label>
		</property>
		<property name="reference" path="reference" type="java.lang.String" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>reference</label>
		</property>
		<property name="owners" path="owners" type="java.lang.String" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>owners</label>
		</property>
		<property name="status" path="status" type="com.byzaneo.xtrade.api.DocumentStatusEntityInterface"
			rendered="true" readonly="false" editable="true" required="false"
			sortable="true">
			<label>status</label>
			<options-expression>#{cc.attrs.taskBean.getValues('status')}</options-expression>
		</property>
		<property name="type" path="type" type="java.lang.String" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>type</label>
		</property>
		<property name="id" path="id" type="java.lang.String" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>id</label>
		</property>
		<property name="from" path="from"
			type="java.lang.String" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>from</label>
		</property>
		<property name="to" path="to"
			type="java.lang.String" rendered="false"
			readonly="false" editable="true" required="false" sortable="true">
			<label>to</label>
		</property>
		<property name="orderingParty" path="orderingParty" editable="false" render-restrict="None" 
			readonly="true" rendered="false" required="false" sortable="true" type="java.lang.String">
			<label>orderingParty</label>
		</property>
		<property editable="true" path="consultStatus" name="consultStatus"
			readonly="false" render-restrict="None" rendered="false" required="false"
			sortable="true" type="com.byzaneo.xtrade.api.DocumentConsultStatus"
			styleClass="iconColumn" style="width:15px">
			<label>consultStatus</label>
			<options-expression>#{cc.attrs.taskBean.getValues('consultStatus')}</options-expression>
		</property>
		<property name="buyerAddressEmail"
				  type="java.lang.String" rendered="true" readonly="false"
				  editable="true" required="false" sortable="false">
			<label>buyerAddressEmail</label>
		</property>
		<property name="mostRecentProcessedReceptionNumber"
				  type="java.lang.String" rendered="false" readonly="true"
				  editable="false" required="false" sortable="true">
			<label>mostRecentProcessedReceptionNumber</label>
		</property>
		<property name="mostRecentProcessedReceptionDate"
				  type="java.util.Date" rendered="false" readonly="true"
				  editable="false" required="false" sortable="true">
			<label>mostRecentProcessedReceptionDate</label>
		</property>
		<property name="receivedSubTotal"
				  type="java.math.BigDecimal" rendered="false" readonly="true"
				  editable="false" required="false" sortable="true">
			<label>receivedSubTotal</label>
		</property>
		<property name="mostRecentProcessedInvoiceNumber"
				  type="java.lang.String" rendered="false" readonly="true"
				  editable="false" required="false" sortable="true">
			<label>mostRecentProcessedInvoiceNumber</label>
		</property>
		<property name="mostRecentProcessedInvoiceDate"
				  type="java.util.Date" rendered="false" readonly="true"
				  editable="false" required="false" sortable="true">
			<label>mostRecentProcessedInvoiceDate</label>
		</property>
		<property name="invoicedSubTotal"
				  type="java.math.BigDecimal" rendered="false" readonly="true"
				  editable="false" required="false" sortable="true">
			<label>invoicedSubTotal</label>
		</property>
	</properties>
</descriptor>