cancel = Gel\u00F6scht
error_document_select_line = Es ist kein Dokument ausgew\u00E4hlt.
owners = Eigent\u00FCmer
orderHeader_orderReferences_accountCode_refDate = Kontodatum
orderDetail_description = Beschreibung
download_list = Liste exportieren
one_order_selected = Element ausgew\u00E4hlt
orderSummary_orderSubTotal_monetaryAmount = Teilbetrag
export_order = Bestellung exportieren
orderDetail_itemQuantityUnitOfMeasurement = Einheit
invoice_attachedFile_view = Dateien anzeigen
triggerActions_execute_info = Die Aktion \u201E{0}\u201C wird auf die Bestellung \u201E{1}\u201C angewendet.
orderingParty = Auftraggeber
splitButton_choice = Aktionen
invoicingReconciliationTrackingDocumentation = Informationen zum Konfigurieren der Zeitleiste sind in der Dokumentation enthalten.
orderSummary_listOfTaxSummary_taxSummary[0]_taxableAmount = Steuerpflichtiger Betrag
orderHeader_orderIssueDate = Bestelldatum
orderHeader_releaseNumber = Freigabe-Nr.
timeline_step_icon = Symbol
error_order_select_line = Es ist kein Dokument ausgew\u00E4hlt.
orderHeader_orderParty_billToParty_nameAddress_name1 = Rechnungsempf\u00E4nger
consultStatus = Beratungsstatus
legend_sent_partially = teilversendet
triggerActions_add = Hinzuf\u00FCgen
report_pdf = PDF-BIRT-Vorlage
orderHeader_orderParty_materialIssuer_partyID_ident = Materialaussteller-ID
all_orders_selected = Alle Elemente der Liste sind ausgew\u00E4hlt
error_export_no_records = Sie k\u00F6nnen keine leere Liste exportieren.
legend_cancel = gel\u00F6scht
order_details = Bestelldetails
orderDetail_invoicedQuantity = Fak. Menge
orderHeader_purpose_purposeCoded = Nutzung
info_confirm_order = Bestellbest\u00E4tigung Nr. {0} gesendet
timeline_step_status = Status
mark_document_as_unread_warn = Es kann nur eine Bestellung mit dem Status \u201EGelesen\u201C zur\u00FCckgesetzt werden. Es wurde keine Aktion ausgef\u00FChrt.
or_between = zwischen
legend_accepted_with_amendment = Best\u00E4tigt mit \u00C4nderung(en)
confirm_add_timeline = Sie haben noch nicht gespeicherte Daten. Wenn Sie eine neue Zeile hinzuf\u00FCgen, werden alle nicht gespeicherten \u00C4nderungen gel\u00F6scht. M\u00F6chten Sie trotzdem fortfahren?
refused = Abgelehnt
triggerActions_global = Global
addNewOrder = Hinzuf\u00FCgen
ordrsp_action_type = Best\u00E4tigungsmodus
standard_order_list_item_menu_duplicate = Duplizieren
orderHeader_orderReferences_contractReferences_contract[0]_systemID = Vertragssystem-ID
msgConfirm = Sie werden {0} Bestellung best\u00E4tigen.<br/>Klicken Sie auf \u201EBest\u00E4tigen\u201C oder \u201EAbbrechen\u201C, wenn Sie die Bestellung nicht best\u00E4tigen m\u00F6chten.
to = Lieferanten-ID
open = \u00D6ffnen
orderHeader_orderTermsOfDelivery[0]_location_locationIdentifier_locationDescription = Lieferort
shipToPartyName = Geliefert an
timeline_step_status_not_selected = Liste der zul\u00E4ssigen Status
select_all = Alles ausw\u00E4hlen
orderHeader_orderParty_remitToParty_nameAddress_name1 = Empf\u00E4nger
triggerActions_limitWarning = Es k\u00F6nnen maximal 100 Bestellungen f\u00FCr diese Aktion ausgew\u00E4hlt werden. Bitte w\u00E4hlen Sie maximal 100 Bestellungen aus.
triggerActions_complete_info = Die Aktion \u201E{0}\u201C wurde f\u00FCr die Bestellung \u201E{1}\u201C ausgef\u00FChrt.
download_pdf = Drucken
export_warning_more_than_threshold = Es wird empfohlen, nicht mehr als {0} Elemente zum Exportieren auszuw\u00E4hlen.
export = Liste exportieren
report_html = HTML-BIRT-Vorlage
cancel_orders_roles = Rollen
receivedSubTotal = Erhaltener Gesamtbetrag exkl. Steuern
or_and = und
sellerPartyID = Lieferanten-ID
shipToPartyID = Empf\u00E4nger-ID
orderHeader_orderReferences_contractReferences_contract[0]_validityDates_startDate = Datum Vertragsbeginn
orderSummary_orderTotal_currency_currencyCoded = W\u00E4hrung
timeline_title = Verfolgung
mostRecentProcessedReceptionDate = Empfangsdatum
cancel_orders_cancel = Abbrechen
recherchable = Filterbar
error_check_rptdesign = Die Felder rptdesign m\u00FCssen ausgef\u00FCllt werden.
orderSummary_allowOrChargeSummary_totalAllowOrCharge[0]_summaryAllowOrCharge_monetaryAmount = Gesamtkosten
cancel_orders = Abbrechen
orderDetail_productCode = Produktcode
orderHeader_orderParty_shipFromParty_partyID_ident = Versandort-ID
orderRequestedDeliverByDate = Lieferdatum
orderHeader_orderParty_soldToParty_partyID_ident = Empfangsort-ID
orderDetail_orderedQuantity = Bestellmenge
orderHeader_orderParty_buyerParty_partyID_ident = K\u00E4ufer-ID
pending = Fakturiert
error_order_export_birt_pdf_missing = Dem PDF-Format ist keine BIRT-Vorlage f\u00FCr die Bestellung zugeordnet. Bitte wenden Sie sich an den Support.
error_in_OrderToASN_conversion = Fehler bei der Umwandlung der Bestellung in das Versandavis.
legend_accepted = Best\u00E4tigt
triggerActions_actionName_placeholder = Name der Aktion
orderSummary_listOfTaxSummary_taxSummary[0]_taxAmount = Steuerbetrag
standard_order_list_item_menu_confirm_duplicate_message = M\u00F6chten Sie wirklich eine neue Bestellung aus der Bestellung {0} erstellen?
orderHeader_orderDates_requestedShipByDate = Versanddatum
issueDate = Bestelldatum
sent_partially = Teilweise versendet
state_select = Ausw\u00E4hlen
triggerActions_complete_info_multi = Die Aktion \u201E{0}\u201C wurde f\u00FCr alle ausgew\u00E4hlten Bestellungen ausgef\u00FChrt.
cancel_orders_success = Stornierung durchgef\u00FChrt
orderHeader_orderParty_shipFromParty_nameAddress_name1 = Versandort-ID
orderHeader_orderParty_shipToParty_nameAddress_name1 = Geliefert an
birt = BIRT
orderSummary_numberOfLines = Anzahl der Zeilen
error_order_confirm_config = F\u00FCr mindestens eine der ausgew\u00E4hlten Bestellungen ist diese Aktion nicht erlaubt.
triggerActions_errorInProgress = Die Aktion wurde f\u00FCr alle ausgew\u00E4hlten Bestellungen ausgef\u00FChrt.
unselect_all = Alles abw\u00E4hlen
or_empty = Leer
error_file_missing = Bestellung nicht vorhanden. Bitte wenden Sie sich an Ihren Administrator.
msgConfirmSelected = M\u00F6chten Sie die Auswahl der Bestellungen best\u00E4tigen?
orderHeader_orderParty_soldToParty_nameAddress_name1 = Empfangsort
acknowledgment = Best\u00E4tigen
buyerAddressEmail = Initiator
orderHeader_orderParty_billToParty_partyID_ident = Rechnungsempf\u00E4nger-ID
select = Ausw\u00E4hlen
orderHeader_orderParty_sellerParty_partyID_ident = Lieferanten-ID
invoicingReconciliationTracking = Verfolgung von Rechnungsstellung/Abgleich.
cancel_orders_enabled_any_role = Rollenpr\u00FCfung deaktivieren
desadv = Versenden
status_separator = /
none = Ungelesen
triggerActions_notAllDocumentsPresent = Es gibt Dokumente, die nicht verarbeitet werden k\u00F6nnen. Bitte wenden Sie sich an Ihren Administrator.
type = Typ
orderHeader_orderTermsOfDelivery[0]_location_locationIdentifier_locID_ident = Lieferort-ID
triggerActions_fieldset = Aktionen hinzuf\u00FCgen
triggerActions_multiStatusError = Die angeforderte Aktion ist f\u00FCr Bestellungen mit \u00B4dem Status \u201E{0}\u201C nicht zul\u00E4ssig. Die Aktion wurde f\u00FCr diese Bestellungen nicht gestartet.
bql_filter_missing = BQL-Filter: Ein Wert ist erforderlich.
action = Aktionen zur Bestellung
confirmTitle = Bestellungen best\u00E4tigen
error_order_status = Mindestens eine der Bestellungen wurde bereits bearbeitet.
legend_refused = abgelehnt
id = ID
mark_as_unread = Als ungelesen markieren
orderHeader_action_tooltip = Mehrfachauswahl
order_reference_type_ORDER = Bestellung
export_document_files = Dokumente exportieren
orderSummary_orderTotal_monetaryAmount = Gesamtbetrag
orderSummary_orderSubTotal_currency_currencyCoded = W\u00E4hrung
list_status = Statusliste
orderHeader_orderTermsOfDelivery[0]_location_locationQualifierCoded = Lieferort-Code
mostRecentProcessedInvoiceNumber = J\u00FCngste Rechnung
orderDetail_receivedQuantity = Bestellmenge
orderHeader_orderParty_shipToParty_partyID_ident = Empf\u00E4nger-ID
quick_search = Schnellsuche
buyerPartyID = K\u00E4ufer-ID
mark_documents_as_unread_warn = Einige Bestellungen haben nicht den Status \u201EGelesen\u201C. Es wurden keine Aktionen f\u00FCr diese Bestellungen durchgef\u00FChrt.
orderHeader_orderParty_warehouseParty_nameAddress_name1 = Lager
standard_order_edition = Portlet
error_missing_template = Keine BIRT-Vorlage konfiguriert, bitte wenden Sie sich an Ihren Administrator.
msgConfirm2 = Sie werden {0} Bestellungen best\u00E4tigen.<br/>Klicken Sie auf \u201EBest\u00E4tigen\u201C oder \u201EAbbrechen\u201C, wenn Sie die Bestellungen nicht best\u00E4tigen m\u00F6chten.
status = Status
acknowledgment_with_modification = Bearbeiten und best\u00E4tigen
legend_pending = fakturiert
timeline_step_add = Hinzuf\u00FCgen
timeline_action = Verfolgen
info_confirm_orders = {0} Bestellung(en) gesendet
info_confirm_action = Ihre Aktion wurde ber\u00FCcksichtigt
orderHeader_orderReferences_accountCode_refNum = Konto-Nr.
non_recherchable = Nicht filterbar
document_name = Bestellung
error_processing = Umwandlungsfehler
report_csv = CSV-BIRT-Vorlage
buyerOrderNumber = Bestell-Nr.
legend_sent = versendet
legend_read = gelesen
action_afterorder = Aktion nach der Bestellung
orderHeader_orderDates_requestedDeliverByDate = Lieferdatum
orderHeader_orderType_orderTypeCoded = Bestelltyp
accepted_with_amendment = Best\u00E4tigt mit \u00C4nderung(en)
orderHeader_orderParty_manufacturingParty_nameAddress_name1 = Hersteller
button_modifyAndConfirm = Bearbeiten und best\u00E4tigen
orderHeader_orderParty_remitToParty_partyID_ident = Empf\u00E4nger-ID
timeline_Fieldset = Chronologie
ALL = Alle
timeline_step_query = Filter [BQL]
no_template = Keine
orderHeader_orderParty_buyerParty_nameAddress_name1 = K\u00E4ufer-ID
no_rptDesign = Kein rptDesign vorhanden: Bitte wenden Sie sich an Ihren Administrator.
mostRecentProcessedInvoiceDate = Rechnungsdatum
orderHeader_orderNumber_sellerOrderNumber = Lieferantenbestellnummer
reference = Referenz
triggerActions_emptyAction = <Unbestimmt>
triggerActions_execute_info_global = Die Aktion \u201E{0}\u201C wird auf die ausgew\u00E4hlten Bestellungen angewendet.
orderHeader_orderNumber_buyerOrderNumber = Bestell-Nr.
from = K\u00E4ufer-ID
multiple_orders_selected = Elemente ausgew\u00E4hlt
orderHeader_orderCurrency_currencyCoded = W\u00E4hrung
read = Gelesen
orderHeader_orderParty_sellerParty_nameAddress_name1 = Lieferant
accepted = Best\u00E4tigt
invoicedSubTotal = Fakturierter Gesamtbetrag ohne USt
mostRecentProcessedReceptionNumber = J\u00FCngster Empfang
creationDate = Erstellungsdatum
sent = Versendet
orderSubTotal = Gesamtbetrag der Bestellung exkl. USt
order_portlet_selection = Bestell-Editor-Portlet
sequence = Verketten
buyerPartyName = K\u00E4ufer-ID
synchonous_limit_passed = {0} Bestellungen wurden bearbeitet.
cancel_orders_enabled_roles = F\u00FCr Rollen zugelassen
orderHeader_orderParty_manufacturingParty_partyID_ident = Hersteller-ID
triggerActions_errorInProgress_multi = Achtung: Achtung, es lief bereits eine Aktion f\u00FCr einige Bestellungen, diese wurden nicht bearbeitet.
triggerActions_generalError = Bei der Ausf\u00FChrung der Aktion ist ein Fehler aufgetreten. Bitte wenden Sie sich an Ihren Administrator.
send = Senden
button_cancel = Abbrechen
orderDetail_itemLineNumber = Nr.
standard_order_list_item_menu_confirm_duplicate_title = Duplizierung best\u00E4tigen
orderHeader_orderParty_warehouseParty_partyID_ident = Lager-ID
cancel_orders_enable = Aktivierung der Funktion
orderHeader_requestedResponse_requestedResponseCoded = Antwort erforderlich
download_csv = Exportieren
action_order_line = Aktion auf Bestellzeilen
button_confirm = Best\u00E4tigen
sellerPartyName = Lieferant
error_order_export_birt_xls_missing = Keine BIRT-Vorlage f\u00FCr den Excel-Typ der Bestellung zugeordnet. Bitte wenden Sie sich an den Support.
invoice_download_pdf = PDF-Bestellansicht
triggerActions_statusError = Die angeforderte Aktion ist f\u00FCr eine Bestellung mit dem Status \u201E{0}\u201C nicht zul\u00E4ssig.
orderHeader_orderReferences_contractReferences_contract[0]_validityDates_endDate = Datum Vertragsende
orderHeader_orderParty_materialIssuer_nameAddress_name1 = Materialaussteller
triggerActions_noneSelected = Liste der zul\u00E4ssigen Status
orderDetail = Bestelldetail
legend_none = ungelesen