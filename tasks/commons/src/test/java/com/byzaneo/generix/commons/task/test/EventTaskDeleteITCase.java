package com.byzaneo.generix.commons.task.test;

import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static com.byzaneo.task.util.TaskTypeHelper.getType;
import static com.ibm.icu.util.Calendar.getInstance;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.byzaneo.commons.test.OpenEntityMangerInTest;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.generix.commons.task.CalendarTask;
import com.byzaneo.generix.service.PortalService;
import com.byzaneo.portal.bean.Page;
import com.byzaneo.portal.bean.Portal;
import com.byzaneo.portal.bean.PortalDefinition;
import com.byzaneo.portal.bean.Portlet;
import com.byzaneo.portal.layout.SimpleLayout;
import com.byzaneo.portal.util.PortalHelper;
import com.byzaneo.task.service.TaskService;
import com.byzaneo.xtrade.config.HazelcastJobStore;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * Test portlet deletion upon task removal using Event system.
 *
 * <AUTHOR> Aïdoun <<EMAIL>>
 * @date Jul 30, 2013
 * @company Byzaneo
 * @see #{link {@link PortalService}.onEventTaskDelete()
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/META-INF/spring/gnx-test.beans.xml"}, loader = SystemPropertyContextLoader.class)
public class EventTaskDeleteITCase extends OpenEntityMangerInTest {

  @Autowired
  @Qualifier(TaskService.SERVICE_NAME)
  private TaskService taskService;

  @Autowired
  @Qualifier(com.byzaneo.generix.service.TaskService.SERVICE_NAME)
  private com.byzaneo.generix.service.TaskService gnxTaskService;

  @Autowired
  @Qualifier(PortalService.SERVICE_NAME)
  private PortalService gnxPortalService;

  @Autowired
  @Qualifier(com.byzaneo.portal.service.PortalService.SERVICE_NAME)
  private com.byzaneo.portal.service.PortalService portalService;

  private Portal portal;

  private CalendarTask task;

  private static TestHazelcastInstanceFactory testInstanceFactory = new TestHazelcastInstanceFactory();

  private static HazelcastInstance hazelcastInstance;

  @BeforeAll
  static void beforeAll(){
    // since process.node.register=true in gnx-test.properties we need to configure hazelcast as well, for the test to run properly
    System.setProperty("hazelcast.ignoreXxeProtectionFailures", "true");
    hazelcastInstance = testInstanceFactory.newHazelcastInstance();
    HazelcastJobStore.setHazelcastClient(hazelcastInstance);
  }

  @AfterAll
  static void afterAll(){
    Hazelcast.shutdownAll();
  }
  @Override
  @BeforeEach
  public void init() {
    super.init();

    // set up task
    this.task = taskService.<CalendarTask>newTask(getType(CalendarTask.class));
    getDefinition(task).setTitle("Test Title");
    getDefinition(task).setDescription("Test description");
    getDefinition(task).setOwner("Myself");
    this.task = taskService.saveTask(task);
    assertNotNull(task);

    // set up portal
    portal = this.gnxPortalService.save(createPortal());
    assertNotNull(portal);
  }

  @Override
  @AfterEach
  public void destroy() {
    super.destroy();
  }

  /**
   * Removes a Task and asserts that the portlets refering to it are deleted.
   */
  @Test
  @Transactional
  public void testEventOnTaskDelete() {
    // TODO finalize new event impl
    int portletsNumber = PortalHelper.getPortlets(portal)
        .size();
    System.out.println("Portal [" + portal.getName() + "] - portlets : [" + portletsNumber + "] ");
    Assert.isTrue(portletsNumber == 1);

    // should fire EventTaskDelete intercepted by PortalService
    gnxTaskService.remove(task);

    portletsNumber = PortalHelper.getPortlets(portal)
        .size();
    System.out.println("Portal [" + portal.getName() + "] - portlets : [" + portletsNumber + "] ");
    Assert.isTrue(portletsNumber == 0);
  }

  @AfterEach
  public void tearDown() throws Exception {
    this.portalService.remove(this.portal);
  }

  /*
   * M O C K
   */

  /*-- PORTAL --*/

  private final Portal createPortal() {
    Portal portal = new Portal();
    portal.setName("Portal" + getInstance().getTimeInMillis());
    portal.setOwner("Myself");
    portal.setStyle("font-size:14pt;");
    portal.setStyleClass("bold");
    portal.setDefinition(createPortalDefinition());
    return portal;
  }

  private final PortalDefinition createPortalDefinition() {
    PortalDefinition portal = new PortalDefinition();
    Page page = new Page();
    page.setName("Page");
    page.getLayouts()
        .add((createSimpleLayout()));
    portal.getPages()
        .add(page);
    return portal;
  }

  private final SimpleLayout createSimpleLayout() {
    SimpleLayout layout = new SimpleLayout();
    layout.setPortlet(createPortlet(0, 0));
    return layout;
  }

  private final Portlet createPortlet(int row, int col) {
    Portlet portlet = new Portlet();
    portlet.setName("Portlet-" + row + "-" + col);
    portlet.setContentId(getDefinition(task).getId()
        .toString());
    portlet.setContentType("task");
    portlet.setMode("view");
    return portlet;
  }
}
