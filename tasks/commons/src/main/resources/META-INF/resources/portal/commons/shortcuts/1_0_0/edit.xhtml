<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:b="http://byzaneo.com/ui"
              xmlns:c="http://java.sun.com/jsp/jstl/core">

    <!-- INTERFACE -->
    <cc:interface name="shortcutsEdit">
        <cc:attribute name="value" type="com.byzaneo.generix.commons.task.ShortcutsTask" required="true"/>
    </cc:interface>

    <!-- IMPLEMENATION -->
    <cc:implementation>
        <style type="text/css">
            .shortcutItemPanel {
                border: 1px solid #D4D4D4 !important;
                margin: 0 !important;
            }

            .ui-orderlist.ui-grid-responsive {
                padding: 0 15px !important;
            }

            .ui-orderlist .ui-orderlist-list {
                overflow: unset;
                height: unset;
                border: none;
            }

            .ui-orderlist .ui-orderlist-list li {
                background: #ffffff;
                color: unset;
            }

            .align-end {
                display: flex;
                justify-content: flex-end;
            }

            .my-1 {
                margin-top: 1rem;
                margin-bottom: 1rem;
            }


            .hint-text {
                font-size: 0.7em !important;
                color: #aaa;
                margin-bottom: 0;
                padding: 0.3em
            }
        </style>
        <p:fieldset legend="#{shortcutslbls.configuration}" toggleable="true">
            <p:outputPanel styleClass="col-md-12" id="displayOptions">
                <p:outputLabel styleClass="col-md-3 control-label label-padding smallTextSize noPadding left"
                               for="displayType" value="#{shortcutslbls.display}"/>
                <p:outputPanel styleClass="col-md-9 noPadding left">
                    <p:selectOneRadio id="displayType" value="#{cc.attrs.value.displayType}" layout="lineDirection">
                        <f:selectItems value="#{cc.attrs.value.layoutValues}" var="item" itemValue="#{item}"
                                       itemLabel="#{shortcutslbls[item]}"/>
                    </p:selectOneRadio>
                </p:outputPanel>
            </p:outputPanel>

            <p:outputPanel styleClass="psShortcutItems row">
                <!-- Add button -->
                <p:outputPanel styleClass="col-md-12 right" style="margin-top: 1rem; margin-bottom: 1rem;">
                    <p:menuButton value="#{labels.add}">
                        <c:forEach var="item" items="#{cc.attrs.value.itemsTypesValues}">
                            <p:menuitem value="#{shortcutslbls[item]}" action="#{cc.attrs.value.addItem(item)}"
                                        update="@(.psShortcutItems)"/>
                        </c:forEach>
                    </p:menuButton>
                </p:outputPanel>
                <!-- List of items -->
                <p:dataTable id="shortcutsItemsTable-2"
                             value="#{cc.attrs.value.shortcutsItems}"
                             var="item"
                             emptyMessage=""
                             styleClass="col-md-12 datatable-noborder datatable-noheader borderless-lines">
                    <p:column styleClass="bold noPadding">
                        <p:outputPanel id="item-#{item.id}">
                            <p:outputPanel styleClass="col-md-12 shortcutItemPanel form-group">
                                <p:outputPanel styleClass="row right">
                                    <p:outputPanel styleClass="closeButton">
                                        <p:commandButton icon="fa fa-times" style="color:#525252;"
                                                         process="@this"
                                                         actionListener="#{cc.attrs.value.removeItem(item)}"
                                                         update="@(.psShortcutItems)"/>
                                    </p:outputPanel>
                                </p:outputPanel>

                                <p:outputPanel style="display: flex; flex-wrap: wrap; gap: 9px">
                                    <!--  Icon  -->
                                    <p:outputPanel rendered="#{item.itemType == 'SHORTCUT'}"
                                                   style="display: flex; align-items: center; flex: 0 0 23%;">
                                        <p:outputPanel
                                                style="display: flex; align-items: center; flex: 0 0 20%; padding: 0;">
                                            <p:outputLabel for="fontAwesomeId" style="padding: 0;"
                                                           value="#{shortcutslbls.icon}"/>
                                        </p:outputPanel>
                                        <p:outputPanel
                                                style="display: flex; align-items: center; flex: 0 0 18%; padding: 0;">
                                            <p:inputText id="fontAwesomeId" value="#{item.icon}"
                                                         validator="xssValidator"
                                                         style="width: 145px; max-width: 145px; min-width: 145px"
                                                         required="true"
                                                         requiredMessage="#{shortcutslbls.item_icon_required}">
                                                <f:validateRegex pattern="^(fa(s|r|l|b|d)?|fa-[a-z]+) fa-[a-z-]*[a-z]$"/>
                                            </p:inputText>
                                            <h:outputText id="fontInfoId" style="margin-left:5px; font-size: 1.5em;"
                                                          class="fa fa-info-circle"/>
                                            <p:tooltip for="fontInfoId" hideDelay="1000">
                                                <h:outputLink value="https://fontawesome.com/v6/icons"
                                                              target="_blank">
                                                    <h:outputText value="#{doctsklbls.iconTooltip}"/>
                                                </h:outputLink>
                                            </p:tooltip>
                                            <p:watermark for="fontAwesomeId" value="#{labels.pageIconPlaceholder}"/>
                                        </p:outputPanel>
                                    </p:outputPanel>

                                    <!--  Label  -->
                                    <p:outputPanel style=" display: flex; align-items: center; flex: 0 0 18%;">
                                        <p:inputText id="itemShortcutLabel" value="#{item.label}" required="true"
                                                     validator="xssValidator"
                                                     style="align-items: center; display: flex; width: 170px; max-width: 170px; min-width: 170px"
                                                     requiredMessage="#{shortcutslbls.item_label_required}"
                                                     placeholder="#{shortcutslbls.item_label_placeholder}">
                                            <f:ajax event="blur" update="itemShortcutLabel"/>
                                        </p:inputText>
                                        <b:labelSet id="itemShortcutLabelLs" for="itemShortcutLabel"
                                                    locales="#{cc.attrs.locales}"
                                                    localeSelected="#{cc.attrs.locale}"
                                                    defaultLocale="#{cc.attrs.defaultLocale}" selector="false"/>
                                    </p:outputPanel>

                                    <!-- Target Type   -->
                                    <p:outputPanel rendered="#{item.itemType == 'SHORTCUT'}"
                                                   style=" display: flex; align-items: center; flex: 0 0 27%;">
                                        <p:outputPanel
                                                style="display: flex; align-items: center; flex: 0 0 17%; padding: 0;">
                                            <p:outputLabel for="shortcutTargetType" style="padding: 0;"
                                                           value="#{shortcutslbls.shortcutTargetType}"/>
                                        </p:outputPanel>
                                        <p:outputPanel
                                                style="display: flex; align-items: center; flex: 0 0 84%; padding: 0;">
                                            <p:selectOneRadio id="shortcutTargetType" value="#{item.shortcutTargetType}"
                                                              layout="custom">
                                                <f:selectItem itemValue="PORTLET" itemLabel="#{shortcutslbls.PORTLET}"/>
                                                <f:selectItem itemValue="POPUP" itemLabel="#{shortcutslbls.POPUP}"/>
                                                <p:ajax process="@this" update="shortcutTarget" global="false"/>
                                            </p:selectOneRadio>
                                            <h:panelGrid columns="4">
                                                <p:outputLabel for="opt1" style="font-size: small;"
                                                               value="#{shortcutslbls.PORTLET}"/>
                                                <p:radioButton id="opt1" for="shortcutTargetType" itemIndex="0"/>
                                                <p:outputLabel for="opt2" style="font-size: small;"
                                                               value="#{shortcutslbls.POPUP}"/>
                                                <p:radioButton id="opt2" for="shortcutTargetType" itemIndex="1"/>
                                            </h:panelGrid>
                                        </p:outputPanel>
                                    </p:outputPanel>

                                    <!-- Target   -->
                                    <p:outputPanel rendered="#{item.itemType == 'SHORTCUT'}"
                                                   style="display: flex; align-items: center; flex: 0 0 24%;">
                                        <p:outputPanel
                                                style="display: flex; align-items: center; flex: 0 0 19%; padding: 0;">
                                            <p:outputLabel for="shortcutTarget" style="padding: 0;"
                                                           value="#{shortcutslbls.target}"/>
                                        </p:outputPanel>
                                        <p:outputPanel id="shortcutTarget" style="display: flex; align-items: center; flex: 0 0 80%; padding: 0;">
                                            <p:outputPanel rendered="#{item.shortcutTargetType.portlet}">
                                                <p:selectOneMenu id="selectedPortletItem" value="#{item.target}"
                                                                 style="width: 170px; max-width: 170px; min-width: 170px"
                                                                 required="#{item.disabled eq 'false'}"
                                                                 requiredMessage="#{shortcutslbls.item_target_required}">
                                                    <f:selectItem itemLabel="#{labels.select}" itemValue=""/>
                                                    <f:selectItems
                                                            value="#{cc.attrs.value.getPortletsList(cc.attrs.owner)}"
                                                            var="option"
                                                            itemLabel="#{gnxHandler.label(option.title, gnxPortalISHandler.defaultLanguage)}"
                                                            itemValue="#{option.target}"/>
                                                    <p:ajax event="change" update="@none" global="false" listener="#{cc.attrs.value.onValueChange(item)}"/>
<!--                                                    <p:ajax event="change" process="@this" update="@none" global="false"/>-->
                                                </p:selectOneMenu>
                                            </p:outputPanel>
                                            <p:outputPanel rendered="#{item.shortcutTargetType.popup}">
                                                <p:selectOneMenu id="targetItem" value="#{item.target}"
                                                                 style="width: 170px; max-width: 170px; min-width: 170px"
                                                                 required="#{item.disabled eq 'false'}"
                                                                 requiredMessage="#{shortcutslbls.item_target_required}">
                                                    <f:selectItem itemLabel="#{labels.select}" itemValue=""/>
                                                    <f:selectItems
                                                            value="#{cc.attrs.value.getPopupsList(cc.attrs.owner)}"
                                                            var="option" itemLabel="#{option}"
                                                            itemValue="#{option.value}"/>
                                                    <p:ajax event="change" process="@this" update="@none"
                                                            global="false"/>
                                                </p:selectOneMenu>
                                            </p:outputPanel>

                                        </p:outputPanel>
                                    </p:outputPanel>

                                    <!-- Disable role control   -->
                                    <p:outputPanel rendered="#{item.itemType == 'SHORTCUT'}"
                                                   style="display: flex; align-items: center; flex: 0 0 24%;margin-top:11px">
                                        <p:selectBooleanCheckbox id="disableRoleControl"
                                                                 value="#{item.disableRoleControl}"
                                                                 itemLabel="#{shortcutslbls.disableRoleControl}"
                                                                 style="align-items: center; display: flex;">
                                            <p:ajax update="allowedForRoles disableRoleControl"/>
                                        </p:selectBooleanCheckbox>
                                    </p:outputPanel>

                                    <p:outputPanel rendered="#{item.itemType == 'SHORTCUT'}"
                                                   style="display: flex; align-items: center; flex: 0 0 40%;margin-top:16px">
                                        <p:outputPanel
                                                style="display: flex; align-self: self-end; flex: 0 0 40%; padding: 0;">
                                            <p:outputLabel for="allowedForRoles" style="padding: 0;"
                                                           value="#{shortcutslbls.allowedForRoles}"/>
                                        </p:outputPanel>
                                        <p:outputPanel
                                                style="display: flex; align-items: center; flex: 0 0 55%; padding: 0;">
                                            <p:selectCheckboxMenu id="allowedForRoles" value="#{item.rolesList}"
                                                                  style="width: 100%"
                                                                  multiple="true"
                                                                  emptyLabel="#{shortcutslbls.role}"
                                                                  label="#{shortcutslbls.role}"
                                                                  disabled="#{item.disableRoleControl eq 'true'}"
                                                                  validator="#{item.rolesListValidator}"
                                                                  required="item.disableRoleControl eq 'false'"
                                                                  requiredMessage="#{shortcutslbls.item_rolesList_required}">
                                                <f:selectItems value="#{cc.attrs.value.getRolesList(cc.attrs.owner)}"/>
                                                <p:ajax event="change" process="@this" update="@none" global="false"/>
                                            </p:selectCheckboxMenu>
                                        </p:outputPanel>
                                    </p:outputPanel>

                                    <!-- Disabled   -->
                                    <p:outputPanel rendered="#{item.itemType == 'SHORTCUT'}"
                                                   style="display: flex; align-items: center; flex: 0 0 15%; margin-top: 11px">
                                        <p:selectBooleanCheckbox id="disabled" value="#{item.disabled}"
                                                                 itemLabel="#{shortcutslbls.disabled}"
                                                                 style="align-items: center; display: flex;">
                                            <p:ajax update="shortcutTarget disabled"/>
                                        </p:selectBooleanCheckbox>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <!--      Hint       -->
                                <p:outputPanel styleClass="row right ">
                                    <p:outputLabel styleClass="hint-text" value="#{shortcutslbls[item.itemType]}"/>
<!--                                    <h:outputText style="margin: 0 5px; font-size: 1.5rem;color: #CCC;"-->
<!--                                                  class="fa fa-arrows-v fa-lg"/>-->
                                </p:outputPanel>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:column>
                </p:dataTable>


            </p:outputPanel>


        </p:fieldset>
    </cc:implementation>
</ui:component>