<p:panel xmlns="http://www.w3.org/1999/xhtml"
			  xmlns:b="http://byzaneo.com/ui"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:pe="http://primefaces.org/ui/extensions">

	<p:outputPanel rendered="#{cc.attrs.value.activeTabIndex == 1}">
		<b:query id="searchComponent1" widgetVar="wSearchComponent1" 
				value="#{cc.attrs.value.bqlRelation}"
				mode="quickSearchOnly"
				model="#{gnxCompanyHandler.queryModel}"
				required="false" disabled="false"
				textSearchLabel="#{conntsklbls.search_placeholder_connection}">
			<p:ajax event="enterKeyPressed" process="searchComponent1" update="@none" onstart="stopPropagation(event); search1();"/>
		</b:query>
		<p:remoteCommand name="search1"
        		actionListener="#{cc.attrs.value.onAdvancedSearchModel()}"
                process="@this @widgetVar(wSearchComponent1)"
                update="@(.datatable-hide-filters)"
                onstart="PF('wRelResultTable').filter();"/>
		<p:spacer />
		<p:dataTable id="relResultTable"
                         widgetVar="wRelResultTable"
                         value="#{cc.attrs.value.relationDataModel}"
                         rows="15"
                         lazy="true"
                         var="rel"
		        		 paginator="true"
                         scrollable="false"
                         sortBy="companyName"
                         paginatorPosition="bottom"
                         paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
		        		 currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
		        		 rowsPerPageTemplate="15,50,100,500"
                         styleClass="datatable-hide-filters resultTable"
                         resizableColumns="true"
                         emptyMessage="#{labels.no_records_found}">
			 <p:column headerText="#" style="width:60px;">
				<h:graphicImage value ="#{gnxGravatarHandler.getInitialImage(rel.companyName)}" style="height: 50px;"/>
	        </p:column>
			<p:column headerText="#{conntsklbls.company_name}" styleClass="relationColumn">
			<p:commandLink actionListener="#{cc.attrs.value.onSelectRelationConnection(rel)}" process="@this"
			oncomplete="PF('wAddARelationManually').show();"
            update="@(.relationImage)">
	        	<h:outputText value="#{rel.companyName}"/>
	        </p:commandLink>
	        </p:column>
			<p:column headerText="#{conntsklbls.siret}" styleClass="relationColumn">
	            <h:outputText value="#{rel.registeredName}"/>
	        </p:column>
			<p:column headerText="#{conntsklbls.siren}" styleClass="relationColumn">
	            <h:outputText value="#{rel.registration}"/>
	        </p:column>
			<p:column headerText="#{conntsklbls.vat}" styleClass="relationColumn"> 
	            <h:outputText value="#{rel.vat}"/>
	        </p:column>
			<p:column headerText="#{conntsklbls.channel}" styleClass="relationColumn">
	        	<h:outputText value="#{conntsklbls[rel.channel]}"/>
	        </p:column>
	        <p:column headerText="#{conntsklbls.group}" styleClass="relationColumn">
	        	<p:outputPanel styleClass="groupColumn" rendered="#{rel.relationGroup != null}">
	        		<h:outputText value="#{rel.relationGroup.name}" />
	        	</p:outputPanel>
	        	<p:commandButton value="#{conntsklbls.affiliate}" rendered="#{rel.relationGroup == null}"
						process="@this @(.psEditGroupDT)"
						update="@(.psDlgAsignGroup) @(.psEditGroupDT)"
	        			actionListener="#{cc.attrs.value.onSelectRelation(rel)}"
	        			oncomplete="PF('wGroupAsg').show();"
	        			style="margin-left: 12px;"
						styleClass="btn btn-primary"/>
	        </p:column>
	        <p:column headerText="" style="width: 85px;">
	        	<p:commandButton id="relationRemoveButton" styleClass="removeButton" actionListener="#{cc.attrs.value.onDeleteRelation(rel)}"
	        	 process="@this" update="relResultTable"
						icon="fa fa-times"  />
						<p:tooltip id="toolTipRemoveRel" for="relationRemoveButton" value="#{conntsklbls.delete_relation}"/>
	        </p:column>
		</p:dataTable>
		
		<p:dialog header="#{conntsklbls.affiliate_group_header}"
			onShow="if(typeof centerDialogToWindow === 'function') {centerDialogToWindow($('.psDlgAsignGroup'));}"
			styleClass="psDlgAsignGroup"
			modal="true"
			widgetVar="wGroupAsg"
			width="500"
			dynamic="true">
			<p:ajax event="close" immediate="true" listener="#{cc.attrs.value.onSelectRelation(null)}"/>
			<p:messages id="dlgMessagesAffiliateDlg" styleClass="psDlgConnMessages" closable="true"/>
			<p:outputPanel styleClass="GroupAsignPnl form-group col-sm-12">
		    	<p:outputLabel styleClass="col-sm-2 control-label" for="Groups" value="#{conntsklbls.group}" />
				   <p:outputPanel styleClass="col-sm-10">
					 <p:selectOneMenu id="Groups" value="#{cc.attrs.value.selectedRelation.relationGroup}" styleclass="fullWidth"
					 converter="#{cc.attrs.value.getRelationGroupConverter()}" required="true" requiredMessage="#{conntsklbls.group_req_affiliate}" >
						<f:selectItem itemValue="#{null}" itemLabel="" />
						<f:selectItems value="#{cc.attrs.value.getGroups()}" var="group" itemValue="#{group.name}" 
						       itemLabel="#{group.name}" />
					 </p:selectOneMenu>
				   </p:outputPanel>
			</p:outputPanel>
			<p:outputPanel id="buttonsPanel"
					styleClass="ui-dialog-footer text-right responsive-buttons pull-right">
			 <p:outputPanel styleClass="validationButtonsDlg">
				<p:commandButton value="#{labels.cancel}"
						actionListener="#{cc.attrs.value.onSelectRelation(null)}"
						onclick="PF('wGroupAsg').hide();"
						process="@this"
						styleClass="btn-gnx btn-gnx-default btn btn-secondary"/>
				<p:commandButton value="#{conntsklbls.affiliate}" styleClass="btn btn-primary"
					    action="#{cc.attrs.value.onSaveSelectedRelation()}"
			 		    process="@this Groups" 
			 			update=" @(.resultTable) @(.psDlgConnMessages)"
			   			oncomplete="{args.validationFailed ? PF('wGroupAsg').show() : PF('wGroupAsg').hide()}"/>				
			 </p:outputPanel>
			</p:outputPanel>
		</p:dialog>
	</p:outputPanel>
</p:panel>