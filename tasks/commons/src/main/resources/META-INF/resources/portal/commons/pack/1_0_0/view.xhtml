<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
			  xmlns:f="http://xmlns.jcp.org/jsf/core" 
			  xmlns:h="http://xmlns.jcp.org/jsf/html"
			  xmlns:cc="http://xmlns.jcp.org/jsf/composite" 
			  xmlns:p="http://primefaces.org/ui" 
			  xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
			  xmlns:gnx="http://webui.generix.com/jsf"
			  xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
			  xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
	<!-- INTERFACE -->
	<cc:interface name="packView"/>
	<!-- IMPLEMENTATION -->
	<cc:implementation>
	<script type="text/javascript">
	 function resolveMessages(args){
    	if(args.validationFailed===true){
    		$('#tskForm-taskMsgTop').css('display','none'); 
    		$('#tskForm-taskMsgBot').css('display','none');
    		PF('wDlgPack').show();
    	}else{
    		PF('wDlgPack').hide();
    	}
    }
	</script>
		<ui:param name="taskProperties" value="#{cc.attrs.value.getDescriptor().getProperties()}" />
		<ui:param name="taskBean" value="#{cc.attrs.value}" />
		#{cc.attrs.value.setLanguage(gnxSessionHandler.locale.toString())}
		<p:outputPanel styleClass="psPackViewPnl portletContentMargin" >
				<!-- logs -->
                <g:logs id="cPacksImportLogsCC"
                        title="#{labels.result}"
                        value="#{cc.attrs.value.logs}"
                        rendered="#{cc.attrs.value.logs != null}">
                    <f:facet name="actions">
                        <p:commandButton icon="ui-icon-seek-prev"
                                         value="#{labels.back}"
                                         immediate="true"
                                         styleClass="mls"
                                         process="@this"
                                         update="@(.psPackViewPnl)">
                            <f:setPropertyActionListener target="#{cc.attrs.value.logs}" value="#{null}"/>
                        </p:commandButton>
                    </f:facet>
                </g:logs>
				<p:outputPanel styleClass="psPackList" rendered="#{cc.attrs.value.editMode=='NONE' and cc.attrs.value.logs==null}">
					<g:indexActions>
	                  <f:facet name="menuActions">
	           			<p:splitButton id="splitButtonId" widgetVar="splitButtonId" style="display:none;" value="#{comtsklbls.splitButton_choice}" rendered="#{not cc.attrs.value.isPartnerUserConnected()}" 
	           						   styleClass="psSplitButton">
							<p:menuitem id="actionMenuAddPack" value="#{gnxpacklbls.actions_addPack}"
				            			actionListener="#{cc.attrs.value.onAddPack(cc.attrs.owner)}"
				            			rendered="#{not cc.attrs.value.isPartnerUserConnected() and gnxSessionHandler.isGranted('Portlet_Pack', 'CREATE')}"
				            			update="@(.psPackViewPnl) @(.ui-messages)" />
                            <p:menuitem id="actionMenuExportPack" value="#{gnxpacklbls.actions_exportPacks}" 
                            			ajax="false" 
                            			rendered="#{not cc.attrs.value.isPartnerUserConnected() and gnxSessionHandler.isGranted('Portlet_Pack', 'READ')}"
                            			disabled="#{cc.attrs.value.dataModel.rowCount == 0}">
                            	<p:fileDownload value="#{cc.attrs.value.onDownloadCSV(gnxSessionHandler.locale)}" />
                            </p:menuitem>
                            <p:menuitem id="actionMenuImportPacks" value="#{gnxpacklbls.actions_importPacks}" 
                                        onclick="PF('standardUploadDialog').show();"
                                        onstart="jQuery('.psActionsMenu').hide();"
                                        rendered="#{not cc.attrs.value.isPartnerUserConnected() and gnxSessionHandler.isGranted('Portlet_Pack', 'UPDATE')}"
                                        update="@(.psPackViewPnl)" />
	    				</p:splitButton>
					  </f:facet>
			  		</g:indexActions> 
                    
	 				<p:dataTable id="resultTable" 
	 							 widgetVar="wResultTable"
	 							 styleClass="psPackTable datatable-hide-filters" 
	 							 value="#{cc.attrs.value.dataModel}" 
	 							 var="doc"
			 					 selectionMode="single" 
			 					 selection="#{cc.attrs.value.selected}"
	 							 rows="10" 
	 							 lazy="true" 
	 							 paginator="true" 
	 							 paginatorPosition="bottom" 
	 							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								 currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}" 
								 rowsPerPageTemplate="10 20 50"
								 rowKey="#{doc.id}" 
								 rowIndexVar="rowIndex" 
								 scrollable="false"
								 resizableColumns="true"  
								 emptyMessage="#{labels.no_records_found}"
                             	 sortOrder="#{cc.attrs.value.sortAsc ? 'ascending' : 'descending'}"
                            	 sortBy="#{cc.attrs.value.sortBy}"
                            	 sortField="#{cc.attrs.value.sortBy}"
                            	 rendered="#{cc.attrs.value.logs==null}"
                                 reflow="true">
					    
                        <p:ajax event="rowSelect"
						        listener="#{cc.attrs.value.onModifyPack(doc)}"
						        update="@(.psPackViewPnl)" />
                        
                        <!-- * COLUMNS * -->
                        <!-- Actions column -->
                        <p:column styleClass="center actions-column" exportable="false" rendered="#{not cc.attrs.value.isPartnerUserConnected()}">
                            <f:facet name="header">
                                <p:outputPanel styleClass="psActions">
                                    <p:commandButton id="actions" styleClass="psActionsBtn globalActionsButton"
                                                     title="#{labels.actions}"
                                                     icon="fa fa-ellipsis-h" type="button"
                                                     disabled="#{(not gnxSessionHandler.isGranted('Portlet_Pack', 'CREATE'))
                                                                 and (not gnxSessionHandler.isGranted('Portlet_Pack', 'READ'))
                                                                 and (not gnxSessionHandler.isGranted('Portlet_Pack', 'UPDATE'))}"/>
                                    <p:tieredMenu styleClass="xcbltask-tieredmenu jqsactions"
                                                  overlay="true" trigger="actions"
                                                  my="left top" at="left bottom">
                                        <p:menuitem id="actionMenuAddPack" 
                                                    value="#{gnxpacklbls.actions_addPack}"
				            			            actionListener="#{cc.attrs.value.onAddPack(cc.attrs.owner)}"
				            			            oncomplete="PF('wDlgPack').show()"
                                                    onclick="jQuery('.jqsactions').hide();"
				            			            rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'CREATE')}"
				            			            process="@this" update="@(.dialog-content)"
				            			            disabled="#{cc.attrs.value.areAvailablePacksEmpty()}"/>
                                        <p:menuitem id="actionMenuExportPack"
                                                    value="#{gnxpacklbls.actions_exportPacks}" 
                                                    onclick="jQuery('.jqsactions').hide();"
                            			            ajax="false" 
                            			            rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'READ')}"
                            			            disabled="#{cc.attrs.value.dataModel.rowCount == 0}">
                            	                    <p:fileDownload value="#{cc.attrs.value.onDownloadCSV(gnxSessionHandler.locale)}" />
                                        </p:menuitem>
                                        <p:menuitem id="actionMenuImportPacks" 
                                                    value="#{gnxpacklbls.actions_importPacks}"
                                                    onclick="PF('standardUploadDialog').show(); jQuery('.jqsactions').hide();"
                                                    rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'UPDATE')}"
                                                    update="@(.psPackViewPnl)" />
                                    </p:tieredMenu>
                                </p:outputPanel>
                            </f:facet>
                            <p:commandButton id="actionPack" styleClass="individualActionsButton"
                                             title="#{gnxpacklbls.actions}"
                                             type="button"
                                             icon="fa fa-ellipsis-h"
                                             disabled="#{(not gnxSessionHandler.isGranted('Portlet_Pack', 'CREATE'))
                                                        and (not gnxSessionHandler.isGranted('Portlet_Pack', 'UPDATE'))
                                                        and (not gnxSessionHandler.isGranted('Portlet_Pack', 'DELETE'))}" />
                                            
                            <p:tieredMenu styleClass="psMenuActions xcbltask-tieredmenu"
                                          overlay="true"
                                          trigger="actionPack"
                                          my="left top"
                                          at="left bottom"
                                          style="width:290px;">
                                <p:menuitem value="#{gnxpacklbls.actions_addPackUnits}"
                                            actionListener="#{cc.attrs.value.onAddPackUnits(doc)}"
                                            oncomplete="PF('wDlgPack').show()"
                                            onstart="jQuery('.psMenuActions').hide();"
                                            process="@this"
                                            update="@(.dialog-content)"
                                            rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'CREATE')}"/>
                                <p:menuitem value="#{gnxpacklbls.actions_deletePackUnits}"
                                            actionListener="#{cc.attrs.value.onDeletePackUnits(doc)}"
                                            oncomplete="PF('wDlgPack').show()"
                                            onstart="jQuery('.psMenuActions').hide(); "
                                            process="@this"
                                            update="@(.dialog-content)"
                                            rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'DELETE')}"/>
                            	<p:menuitem value="#{gnxpacklbls.actions_editPack}"
	                                        actionListener="#{cc.attrs.value.onModifyPack(doc)}"
	                                        oncomplete="PF('wDlgPack').show()"
	                                        onstart="jQuery('.psMenuActions').hide();"
	                                        process="@this"
	                                        update="@(.dialog-content)"
	                                        rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'UPDATE')}"/>
	                            <p:menuitem value="#{gnxpacklbls.actions_deletePack}"
	                                        onstart="jQuery('.psMenuActions').hide()"
	                                        actionListener="#{cc.attrs.value.onDeletePack(doc)}"
	                                        process="@this"
	                                        disabled="#{not gnxSessionHandler.isGranted('Portlet_Pack', 'DELETE')}"
	                                        rendered="#{gnxSessionHandler.isGranted('Portlet_Pack', 'DELETE')}"
	                                        update="@(.psConfirmDelete)" ignoreAutoUpdate="true"
                           					oncomplete="PF('wConfirmDelete').show();"/>   
                            </p:tieredMenu>
                        </p:column>
                        <!-- Properties columns -->
						<gnx:pcolumn property="#{taskProperties.get(0)}"/>
						<gnx:pcolumn property="#{taskProperties.get(1)}"/>
						<gnx:pcolumn property="#{taskProperties.get(2)}"/>
						<gnx:pcolumn property="#{taskProperties.get(3)}"/>
						<gnx:pcolumn property="#{taskProperties.get(4)}"/>
						<gnx:pcolumn property="#{taskProperties.get(5)}"/>
						<gnx:pcolumn property="#{taskProperties.get(6)}"/>
						<gnx:pcolumn property="#{taskProperties.get(7)}"/>
	                   	
					</p:dataTable>
					<p:remoteCommand name="refreshMenu" 
									 update="@(.psSplitButton) refreshMenuCmd" 
									 action="#{cc.attrs.value.setFirstCall()}" 
									 autoRun="true" 
									 rendered="#{not cc.attrs.value.isFirstCall()}"
									 id="refreshMenuCmd"/>
				</p:outputPanel>
				 <!-- Confirm DIALOG DELETE PACK -->
                <p:confirmDialog id="confirmDelete" styleClass="psConfirmDelete" widgetVar="wConfirmDelete" header="#{gnxpacklbls.confirmationTitle}" severity="alert" closable="false">
					<f:facet name="message">
						<h:outputFormat value="#{gnxpacklbls.confirmPackDelete}" >
							<f:param value="#{cc.attrs.value.pack.name}"></f:param>
							<f:param value="#{cc.attrs.value.pack.partner.fullname}"></f:param>
						</h:outputFormat>
					</f:facet>
					 <p:outputPanel styleClass="right packConfirmDelete">
						<p:commandButton styleClass="btn-gnx btn-gnx_default btn btn-outline-secondary" 
										 value="#{labels.cancel}" 
										 process="@this" 
										 actionListener="#{taskBean.onCancelDelete()}" 
										 oncomplete="PF('wConfirmDelete').hide();" />
                        <p:commandButton styleClass="btn-gnx btn-color-company btn btn-primary" 
										 value="#{labels.ok}" 
										 process="@this"
										 update="@(.psPackViewPnl) @(.ui-messages)"
										 ignoreAutoUpdate="true" 
										 oncomplete="PF('wConfirmDelete').hide();" 
										 actionListener="#{taskBean.onConfirmDeletePack()}" />
					</p:outputPanel>
				</p:confirmDialog> 
				
				<p:outputPanel styleClass="dialog-content">
                  <ui:include src="edit-pack.xhtml"/>
                </p:outputPanel>

		</p:outputPanel>
		
			<p:dialog modal="true"
		          closable="true"
                  closeOnEscape="true"
                  header="#{gnxpacklbls.standard_dialog_header}"
                  widgetVar="standardUploadDialog"
                  id="standardUploadDialog"
                  responsive="true"
                  styleClass="responsive-dialog pack-import">
                <p:outputPanel styleClass="displayOnGenerixTemplate">
                  <p:fileUpload fileUploadListener="#{cc.attrs.value.onSelectFileToImport}"
                                mode="advanced"
                                dragDropSupport="true"
                                uploadLabel="#{gnxxcblcomlbls.standard_dialog_uploadButton}"
                                cancelLabel="#{gnxxcblcomlbls.standard_dialog_cancelAllButton}"
                                label="#{gnxxcblcomlbls.standard_dialog_browseButton}"
                                multiple="false"
                                oncomplete="jQuery('.uploadButton').click();"
                                allowTypes="/.(csv)$/"/>
                </p:outputPanel>
                <p:outputPanel styleClass="pack-import-dlg displayOnGenerixCustomerTemplate">
                  <p:outputPanel styleClass="row">
                    <p:outputPanel styleClass="col-md-12">
                      <p:outputPanel styleClass="form-group">
                         <p:fileUpload id="loadFile"
                                       fileUploadListener="#{cc.attrs.value.onSelectFileToImport}"
                                       mode="advanced"
                                       process="@this"
                                       update="@this @(.custom-file) @(.import-button)"
                                       styleClass="jqsHiddenUpload"
                                       multiple="false"
                                       allowTypes="/.(csv)$/"
                                       auto="true"/>
                         <p:message for="loadFile"/>
                         <p:outputPanel class="custom-file">
                            <h:outputLabel for="file-input"
                                           value="#{cc.attrs.value.getFileNameForPlaceholder()}"
                                           styleClass="custom-file-label form-control-rounded lang-#{gnxSessionHandler.getLocale()}"/>
							 <p:inputText id="file-input" validator="xssValidator"
                                           type="button"
                                           styleClass="custom-file-input"
                                           onclick="$(document.getElementsByClassName('jqsHiddenUpload')[0]).find('input').click();"/>
                        </p:outputPanel>
                    </p:outputPanel>
                   </p:outputPanel>
                  </p:outputPanel>
                  <p:outputPanel id="buttonsPanel"
                                   styleClass="ui-dialog-footer text-right responsive-buttons pull-right">
                        <p:commandButton value="#{labels.close}"
                                         onclick="PF('standardUploadDialog').hide();"
                                         process="@this"
                                         styleClass="btn-gnx btn-gnx-default btn btn-secondary"/>
                        <p:commandButton value="#{labels.import}"
                                         process="@this"
			                             disabled="#{cc.attrs.value.uploadedFile == null}"
			                             styleClass="import-button btn btn-primary"
			                             onclick="jQuery('.uploadButton').click();"
			                             oncomplete="PF('standardUploadDialog').hide();"
			                             update="resultTable"/> 
                 </p:outputPanel>
               </p:outputPanel>
            </p:dialog>
            <p:commandButton icon="fa fa-download"
                             value="#{labels.import}"
                             style="visibility:hidden;"
                             actionListener="#{cc.attrs.value.onImportPacks()}"
                             oncomplete="PF('standardUploadDialog').hide();"
                             styleClass="uploadButton"
                             process="@this"
                             update="@(.psPackViewPnl)"/> 

			<p:commandButton id="call-me-after-page" widgetVar="afterPageBtn" style="display:none;" update="@(.psSplitButton)"/>
			  
	</cc:implementation>
</ui:component>
