package com.byzaneo.generix.commons.task;

import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.generix.commons.task.bean.*;
import com.byzaneo.generix.commons.task.service.LibraryService;
import com.byzaneo.generix.commons.task.util.FileExtensionHelper.FileExtensionIconColor;
import com.byzaneo.generix.commons.task.util.LibraryCategoryKeyValuePair;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.exception.CannotSaveFileException;
import com.byzaneo.generix.service.SecurityService.Resource;
import com.byzaneo.generix.ui.ApplicationHandler.EDateFormat;
import com.byzaneo.generix.xtrade.task.AbstractIndexableTask;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.service.DocumentService;
import com.rits.cloning.Cloner;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.event.*;
import org.primefaces.model.UploadedFile;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;

import javax.annotation.PostConstruct;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.sendFile;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.info;
import static com.byzaneo.commons.util.AcceptedFilesExtension.getAcceptedFilesExtensions;
import static com.byzaneo.query.builder.Clauses.equal;
import static java.util.Optional.ofNullable;
import static org.apache.commons.io.FilenameUtils.removeExtension;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.primefaces.context.RequestContext.getCurrentInstance;

@Task(name = "Library", library = "portal/commons", version = "1.0.0")
@TaskViewModel(labelFamily = LibraryTask.LABEL_FAMILY)
public class LibraryTask extends AbstractIndexableTask<Library> {

  private static final long serialVersionUID = -6425184073834923652L;

  private final static Logger log = LoggerFactory.getLogger(LibraryTask.class);

  public static final String LABEL_FAMILY = "labels";

  private final long MAX_FILE_SIZE = 5242880L; // 50 Mb

  @Autowired
  @Qualifier(LibraryService.SERVICE_NAME)
  private transient LibraryService libraryService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private transient DocumentService documentService;

  private LibraryConfiguration libraryConfiguration;
  private transient String introductionText;
  private transient Library library;
  private transient String selectedCategory;
  private transient String selectedPartCategory;
  private transient UploadedFile uploadedFile;

  private transient List<Library> libraries = new ArrayList<>();
  private transient List<LibraryCategory> categories = new ArrayList<>();
  private transient List<LibraryCategory> categoriesByUserLanguage = new ArrayList<>();
  private transient List<LibraryCategoryKeyValuePair> categoryIdList = new ArrayList<>();
  private transient Locale selectedLanguage;
  private transient EditMode editMode = EditMode.NONE;
  private static final Cloner cloner = new Cloner();
  private transient LibraryCategory categoryToUpdate;
  private transient boolean isNewCategory;
  // -- LIFECYCLE --

  private enum EditMode {
    NONE,
    CREATION,
    EDIT
  }

  @PostConstruct
  public void init() {
    this.responsive = true;
    editMode = EditMode.NONE;
    if (!isBackOfficeUser()) {
      // resolves organization codes
      this.resolveOrganizationCodes();
      this.libraryConfiguration = this.libraryService.getConfiguration(this.company);
      setIntroductionTextBySelectedLanguage();
    }
  }

  // -- OVERRIDE --

  @Override
  public String getPathDescriptor() {
    return null;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    return; // cannot set it manually
  }

  @Override
  public Query resolveBaseQuery() {
    QueryBuilder qb = new QueryBuilder();
    qb.asc("category.order");
    qb.asc("sortNumber");
    return qb.query();
  }

  // -- EVENTS --

  public void onChangeCategory() {
    if (this.library == null ||
        StringUtils.isEmpty(getSelectedCategory())) {
      isNewCategory = false;
      return;
    }
    isNewCategory = this.getCategories()
        .stream()
        .noneMatch(cat -> cat.getName()
            .equals(getSelectedCategory()) &&
            cat.getLanguage()
                .equals(getSelectedLanguage().toString()));
  }

  public void onAddLibrary() {
    editMode = EditMode.CREATION;
    this.library = new Library();
    this.selectedCategory = null;
    isNewCategory = false;
    this.library.setCategory(new LibraryCategory());
  }

  public void onDeleteLibrary(Library library) {
    LibraryCategory categoryToBeDeleted = library.getCategory();
    this.documentService.removeIndexable(library);
    Document documentToRemove = this.documentService.getDocument(library.getDocumentID());
    this.documentService.removeDocument(documentToRemove);
    this.libraries.clear();
    deleteCategory(categoryToBeDeleted);
    info(LABEL_FAMILY + ".library_deleted_success");
  }

  public void onEditLibrary(Library library) {
    editMode = EditMode.EDIT;
    this.library = library;
    isNewCategory = false;
    this.selectedCategory = library.getCategory()
        .getName();
    categoryToUpdate = cloner.deepClone(library.getCategory());
  }

  /**
   * Delete category (also from database) when the last library in this category is deleted
   * 
   * @param category
   */
  public void deleteCategory(LibraryCategory category) {
    boolean deleteCategory = true;
    for (Library library : this.getLibraries()) {
      if (category.getName()
          .equals(library.getCategory()
              .getName()) &&
          category.getLanguage()
              .equals(library.getCategory()
                  .getLanguage())) {
        deleteCategory = false;
        break;
      }
    }
    if (deleteCategory)
      this.documentService.removeIndexable(category);
  }

  public void onDownload() {
    sendFile(this.download.getFile(), this.download.getName(), true);
  }

  public void onDownloadFile(Library library) {
    if (library == null) {
      return;
    }
    try {
      Document document = this.documentService.getDocument(library.getDocumentID());
      DocumentFile dof = document.getFirstDocumentFile();
      this.download = new DocumentFileViewer(removeExtension(library.getOriginalName()), dof.getType(), dof.getFile());
      getCurrentInstance().addCallbackParam("download", this.download != null);
    }
    catch (Exception e) {
      log.error("Failed to download file", e);
      error("comtsklbls.failed_to_download_file");
    }
  }

  public void onReorderEvent(ReorderEvent event) {

    List<LibraryCategoryKeyValuePair> list = new LinkedList<>(this.categoryIdList);

    int fromIndex = event.getFromIndex();
    int toIndex = event.getToIndex();

    // sort the list after each drag&drop
    if (toIndex >= fromIndex) {
      Collections.rotate(list.subList(fromIndex, toIndex + 1), -1);
    }
    else {
      Collections.rotate(list.subList(toIndex, fromIndex + 1), 1);
    }
    this.categoryIdList.clear();
    this.categoryIdList.addAll(list);

    // save libraries order after each drag&drop
    saveOrderLibraries();
  }

  public void onSave() {
    libraryConfiguration.getIntroductionTexts()
        .put(getSelectedLanguage(), introductionText);
    this.libraryService.saveConfiguration(this.company, this.libraryConfiguration);
    info(LABEL_FAMILY + ".save_success");
  }

  public void onSaveLibrary() {
    String message = editMode.equals(EditMode.CREATION) ? LABEL_FAMILY + ".library_created_success"
        : LABEL_FAMILY + ".library_edited_succes";
    this.library.getCategory()
        .setName(getSelectedCategory());
    this.library.setCategory(getExistingCategory(library.getCategory()));
    try {
      if (this.library == null ||
          StringUtils.isEmpty(this.library.getCategory()
              .getName())) {
        error(LABEL_FAMILY + ".category_field_empty");
        return;
      }
      if (!checkAttachedFile()) {
        return;
      }
      if (this.uploadedFile != null) {
        // if the attach was replaced, the old one has to be removed
        if (editMode.equals(EditMode.EDIT)) {
          Document document = this.documentService.getDocument(this.library.getDocumentID());
          document.removeFile(document.getFirstDocumentFile());
        }
        saveAttachedFile();
      }
      initLibrary(this.library);
      this.libraries.clear();
      this.library = this.documentService.saveIndexable(this.library);
      // in edit mode, if the category was changed the old one has to be removed
      if (editMode.equals(EditMode.EDIT)) {
        if (!categoryToUpdate.getName()
            .equals(this.library.getCategory()
                .getName()) ||
            !categoryToUpdate.getLanguage()
                .equals(this.library.getLanguage())) {
          deleteCategory(categoryToUpdate);
        }
      }

      info(message);
    }
    catch (Exception e) {
      log.error("Failed to save library", e);
      error("comtsklbls.failed_save_library");
    }
    finally {
      this.uploadedFile = null;
    }
  }

  public void onChangeSelectedLanguage() {
    setIntroductionTextBySelectedLanguage();
    selectedCategory = null;
  }
  // -- UTILS --

  public void attachUploadedFile(FileUploadEvent event) {
    this.uploadedFile = event.getFile();
    String filename = this.uploadedFile.getFileName();
    boolean notAcceptedExtention = filename.indexOf(".") >= 0
        ? !getAcceptedFilesExtensions().contains(filename.substring(filename.lastIndexOf(".")))
        : true;
    if (notAcceptedExtention || !filename.matches("[a-zA-Z_\\-.0-9]*")) {
      removeUploadFile();
      error(notAcceptedExtention ? "gnxxcblcomlbls.attachment_type_unaccepted" : "gnxxcblcomlbls.attachment_error_incorrect_file_name");
      this.uploadedFile = null;
    }
  }

  public void setIntroductionTextBySelectedLanguage() {
    this.introductionText = ofNullable(libraryConfiguration).map(LibraryConfiguration::getIntroductionTexts)
        .map(introTexts -> introTexts.get(getSelectedLanguage()))
        .orElse(new String(""));
  }

  private boolean checkAttachedFile() {
    if (this.uploadedFile == null && editMode.equals(EditMode.CREATION)) {
      error("gnxxcblcomlbls.attachment_error_no_file");
      return false;
    }
    return true;
  }

  private Library initLibrary(Library library) {
    library.setCreationDate(new Date());
    LibraryCategory libraryCategory = getExistingCategory(library.getCategory());

    library.setCategory(libraryCategory);
    library.setLanguage(getSelectedLanguage().toString());
    // search the biggest sortNumber for the library
    // the new library will be the next in order
    int libraryMaxSortNumber = this.getLibraries()
        .stream()
        .filter(lib -> library.getCategory()
            .getName()
            .equals(lib.getCategory()
                .getName()))
        .max(Comparator.comparing(Library::getSortNumber))
        .map(Library::getSortNumber)
        .orElse(0);
    libraryMaxSortNumber++;
    library.setSortNumber(libraryMaxSortNumber);

    return library;
  }

  public boolean isFileNew(Library library) {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.MONTH, -1);
    return library.getCreationDate()
        .after(cal.getTime());
  }

  public List<Library> getLibrariesByCategory() {
    if (this.getSelectedPartCategory() == null) {
      return Collections.emptyList();
    }
    return this.getLibraries()
        .stream()
        .filter(lib -> lib.getCategory()
            .getName()
            .equals(this.selectedPartCategory) && lib.getLanguage()
                .equals(getLocale().toString()))
        .sorted(Comparator.comparing(Library::getSortNumber))
        .collect(Collectors.toList());
  }

  public List<String> getCategoriesName() {
    return this.getCategories()
        .stream()
        .filter(cat -> cat.getLanguage()
            .equals(getSelectedLanguage().toString()))
        .map(LibraryCategory::getName)
        .collect(Collectors.toList());
  }

  private LibraryCategory getExistingCategory(LibraryCategory category) {
    return this.getCategoriesByUserLanguage()
        .stream()
        .filter(cat -> cat.getName()
            .equals(category.getName()) && cat.getLanguage()
                .equals(getSelectedLanguage().toString()))
        .findFirst()
        .orElseGet(() -> createNewCategory(category.getName()));
  }

  private LibraryCategory createNewCategory(String name) {
    LibraryCategory libraryCategory = new LibraryCategory();
    libraryCategory.setName(name);
    libraryCategory.setLanguage(getSelectedLanguage().toString());
    libraryCategory.setOrder(this.getCategories()
        .size() + 1);

    LibraryCategory indexable = this.documentService.saveIndexable(libraryCategory);

    this.categories.clear();
    this.categories.addAll(getCategories());

    return indexable;
  }

  public String getExtensionColor(String name) {
    FileExtensionIconColor color;

    try {
      color = FileExtensionIconColor.valueOf(getFileExtension(name));
    }
    catch (IllegalArgumentException e) {
      color = FileExtensionIconColor.valueOf("DEFAULT");
    }
    return color.toString();
  }

  public String getFileExtension(String name) {
    String ext = FilenameUtils.getExtension(name);
    if (isBlank(ext))
      ext = getMessage("labels.UNKNOWN", "Unknow", getLocale());
    return ext.toUpperCase();
  }

  public String getSelectedCategory() {
    return selectedCategory;
  }

  public String getSelectedPartCategory() {
    if (this.selectedPartCategory == null && !this.getCategoriesByUserLanguage()
        .isEmpty())
      this.selectedPartCategory = this.categoriesByUserLanguage.get(0)
          .getName();
    return selectedPartCategory;
  }

  public String getUploadedFilename() {
    if (editMode.equals(EditMode.EDIT) && uploadedFile == null) {
      Document document = this.documentService.getDocument(this.library.getDocumentID());
      return document.getFirstFile()
          .getName();

    }
    if (uploadedFile == null) {
      return getMessage(CarouselTask.LABEL_FAMILY + ".choose_file", "choose file", JSFHelper.getLocale());
    }

    return uploadedFile.getFileName();
  }

  private void saveAttachedFile() {
    try {
      String comment = this.library.getComment();

      // Create document and build the FileSystem path : xcbl/company.getCode()/record
      Document document = this.documentService
          .saveDocument(new Document("Category", company.getCode(), DocumentType.RECORD.toString(), "", "", DocumentStatus.NONE));

      // Copy the file in the file system and store informations in database
      this.eDocumentService.attachFileToDocument(document,
          new InMemoryFile(uploadedFile.getFileName(), uploadedFile.getContents()),
          comment);

      // Save reference of this document in the Category object
      this.library.setDocumentID(document.getId());
      this.library.setFileSize(this.uploadedFile.getSize() / 1024);
      this.library.setOriginalName(this.uploadedFile.getFileName());
    }
    catch (CannotSaveFileException e) {
      log.warn(String.format("Cannot save the Document %s", this.uploadedFile.getFileName()), e);
      error("gnxxcblcomlbls.attachment_error_joining", e);
    }
  }

  private void saveOrderLibraries() {
    // keep in a map the current sortNumber for every category
    Map<LibraryCategory, Integer> catIndexHelper = new LinkedHashMap<>();
    for (LibraryCategoryKeyValuePair categoryIdPair : this.categoryIdList) {
      LibraryCategory category = categoryIdPair.getKey();
      String id = categoryIdPair.getValue();

      Integer catIndex = catIndexHelper.get(category);
      if (catIndex == null) {
        catIndex = 1;
        catIndexHelper.put(category, catIndex);
      }
      else {
        catIndex++;
        catIndexHelper.put(category, catIndex);
      }

      // search the library with the given id and update sortNumber value
      final int sortNumber = catIndex;
      this.getLibraries()
          .stream()
          .filter(lib -> id.equals(lib.getId()))
          .findFirst()
          .ifPresent(libFound -> libFound.setSortNumber(sortNumber));
    }

    // set new order for categories (the order from LinkedHashMap catIndexHelper is used)
    List<LibraryCategory> orderedCategories = new ArrayList<>(catIndexHelper.keySet());
    // this.categories.forEach(cat -> cat.setOrder(orderedCategories.indexOf(cat.getName()) + 1));
    List<LibraryCategory> updatatedCategories = getCategories();
    updatatedCategories.stream()
        .forEach(cat ->
    {
          for (int i = 0; i < orderedCategories.size(); i++) {
            if (cat.getName()
                .equals(orderedCategories.get(i)
                    .getName()) &&
                cat.getLanguage()
                    .equals(orderedCategories.get(i)
                        .getLanguage())) {
              cat.setOrder(i + 1);
            }
          }
        });
    // save new category order in database
    documentService.saveIndexables(updatatedCategories);

    // update category order from library
    this.getLibraries()
        .forEach(lib -> lib.getCategory()
            .setOrder(orderedCategories.indexOf(lib.getCategory()
                .getName()) + 1));

    this.getLibraries()
        .forEach(lib ->
    {
          for (int i = 0; i < orderedCategories.size(); i++) {
            if (lib.getCategory()
                .getName()
                .equals(orderedCategories.get(i)
                    .getName()) &&
                lib.getLanguage()
                    .equals(orderedCategories.get(i)
                        .getLanguage())) {
              lib.getCategory()
                  .setOrder(i + 1);
            }
          }
        });

    // save new library order in database
    this.documentService.saveIndexables(getLibraries());
    this.libraries.clear();
  }

  private void removeUploadFile() {
    try {
      File temp = File.createTempFile("task-library", "toRemove");
      FileHelper.deleteQuietly(FileHelper.toFile(this.uploadedFile.getContents(), temp));
    }
    catch (IOException e) {
      log.error("Failed to clean temporary file", e);
    }
  }

  // -- GETTERS & SETTERS --

  public List<Library> getLibraries() {
    if (this.libraries.isEmpty()) {
      Query baseQuery = resolveBaseQuery();
      this.libraries.addAll(
          this.documentService
              .searchIndexables(Library.class, baseQuery, null)
              .getContent());

      this.categoryIdList.clear();
      this.categoryIdList.addAll(this.libraries.stream()
          .map(lib -> new LibraryCategoryKeyValuePair(lib.getCategory(), lib.getId()))
          .collect(Collectors.toList()));
    }

    return libraries;
  }

  public void setLibraries(List<Library> libraries) {
    this.libraries = libraries;
  }

  public List<LibraryCategory> getCategories() {
    this.categories.clear();

    Set<LibraryCategory> categoriesToSort = this.getLibraries()
        .stream()
        .map(Library::getCategory)
        .collect(Collectors.toSet());
    List<LibraryCategory> categoriesList = new ArrayList<>(categoriesToSort);
    Collections.sort(categoriesList, Comparator.comparing(LibraryCategory::getOrder));
    this.categories.addAll(categoriesList);

    return this.categories;
  }

  public List<LibraryCategory> getCategoriesByUserLanguage() {
    this.categoriesByUserLanguage.clear();
    List<LibraryCategory> categories = new ArrayList<LibraryCategory>();
    QueryBuilder qb = new QueryBuilder();
    qb.asc("order");
    qb.and(equal("language", getSelectedLanguage().toString()));
    categories.addAll(
        this.documentService
            .searchIndexables(LibraryCategory.class, qb.query(), null)
            .getContent());

    this.categoriesByUserLanguage.addAll(categories);

    return this.categoriesByUserLanguage;
  }

  public void setCategories(List<LibraryCategory> categories) {
    this.categories = categories;
  }

  public Library getLibrary() {
    return library;
  }

  public void setLibrary(Library library) {
    this.library = library;
  }

  public void setSelectedCategory(String selectedCategory) {
    this.selectedCategory = selectedCategory;
  }

  public void setSelectedPartCategory(String selectedPartCategory) {
    this.selectedPartCategory = selectedPartCategory;
  }

  public UploadedFile getUploadedFile() {
    return uploadedFile;
  }

  public LibraryConfiguration getLibraryConfiguration() {
    return libraryConfiguration;
  }

  public void setLibraryConfiguration(LibraryConfiguration libraryConfiguration) {
    this.libraryConfiguration = libraryConfiguration;
  }

  public String getIntroductionText() {
    return introductionText;
  }

  public void setIntroductionText(String introductionText) {
    this.introductionText = introductionText;
  }

  public void setUploadedFile(UploadedFile uploadedFile) {
    this.uploadedFile = uploadedFile;
  }

  public long getMaxFileSize() {
    return MAX_FILE_SIZE;
  }

  public String getMaxFileSizeInMo() {
    return MAX_FILE_SIZE / (1024 * 1024) + " Mo";
  }

  public boolean isNewCategory() {
    return isNewCategory;
  }

  public Locale getSelectedLanguage() {
    if (selectedLanguage == null) {
      selectedLanguage = this.currentSession()
          .isGranted(Resource.Portlet_Library, Right.EXECUTE) ? currentSession().getInstance()
              .getConfiguration()
              .getDefaultLanguage() : getLocale();
    }
    return selectedLanguage;
  }

  public void setSelectedLanguage(Locale selectedLanguage) {
    this.selectedLanguage = selectedLanguage;
  }

  public EditMode getEditMode() {
    return editMode;
  }

  public void setEditMode(EditMode editMode) {
    this.editMode = editMode;
  }

  public String getUploadedDate(Date creationDate) {
    User user = getCurrentUser();
    long hours = (new Date().getTime() - creationDate.getTime()) / 3600000;
    String msg = getMessage(LABEL_FAMILY + ".uploaded_since", "Uploaded on ", user.getLocale());
    if (hours < 1) {
      msg += getMessage(LABEL_FAMILY + ".one_hour_past", "less than an hour", user.getLocale());
    }
    else if (hours < 24) {
      msg += getMessage(LABEL_FAMILY + ".a_day_past", "less than a day", user.getLocale());
    }
    else {
      SimpleDateFormat format = new SimpleDateFormat(EDateFormat.findPatternBy(user, EDateFormat.dd_MM_yyyy)
          .getPattern());
      msg += getMessage(LABEL_FAMILY + ".by_date", format.format(creationDate),
          user.getLocale(), format.format(creationDate));
    }
    return msg;
  }
}
