package com.byzaneo.generix.commons.task.business;

import static com.byzaneo.xtrade.process.VariableHelper.createExpression;
import static com.byzaneo.xtrade.ui.convert.ProcessExpressionConverter.CONVERTER_ID;

import org.activiti.engine.delegate.*;
import org.apache.tools.ant.types.FileSet;

import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.process.task.AbstractTask;

/**
 * copy or move files from ${process_input_dir} to the specified place
 */
@Task(name = "output", library = "process/business/io", version = "1.0.0")
@TaskViewModel(labelFamily = "inputBusinessTaskLbls")
public class OutputTask extends AbstractTask {

  private static final long serialVersionUID = -4411925541900580309L;

  /** Destination (required) */
  @TaskProperty(required = true, label = "#{inputBusinessTaskLbls.output_to}")
  private Expression to;

  /**
   * File set used to filter processed files (see https://ant.apache.org/manual/Types/fileset.html) (default: "*")
   */
  @TaskProperty(type = FileSet.class, converter = CONVERTER_ID, label = "#{inputBusinessTaskLbls.input_fileSet}")
  private Expression fileSet;

  /**
   * Overwrite existing files even if the destination files are newer. (default: <code>false</code>)
   */
  @TaskProperty(type = Boolean.class, label = "#{inputBusinessTaskLbls.input_overwrite}")
  private Expression overwrite;

  /**
   * Ignore the directory structure of the source files, and copy all files into the directory specified by the {@link #to} (directory)
   * property. (default: <code>false</code>)
   */
  @TaskProperty(type = Boolean.class, label = "#{inputBusinessTaskLbls.input_flatten}")
  private Expression flatten;

  /**
   * Limit processed files (-1 means disabled) (default is -1)
   * 
   * @since 7.0
   */
  @TaskProperty(type = Integer.class, label = "#{inputBusinessTaskLbls.input_limit}")
  private Expression limit = new FixedValue(-1);

  // master/slaves file copy

  /**
   * Regular expression pattern for the master file (optional)
   * 
   * @since 7.0
   */
  @TaskProperty(group = "master_slaves", label = "#{inputBusinessTaskLbls.input_masterFilePattern}")
  private Expression masterFilePattern;

  /**
   * Regular expression pattern for the master file (required if {@link #masterFilePattern} is set)
   * 
   * @since 7.0
   */
  @TaskProperty(group = "master_slaves", label = "#{inputBusinessTaskLbls.input_slaveFilesFormat}")
  private Expression slaveFilesFormat;

  /** @see org.activiti.engine.delegate.JavaDelegate#execute(org.activiti.engine.delegate.DelegateExecution) */
  @Override
  public void executeTask(DelegateExecution execution) throws Exception {
    Expression move = new FixedValue(Boolean.FALSE);
    Expression polled = new FixedValue(Boolean.FALSE);
    InputOutputHelper.doCopy(execution,
        createExpression("${process_input_dir_string}"),
        to,
        fileSet,
        move,
        overwrite,
        flatten,
        polled,
        limit,
        masterFilePattern,
        slaveFilesFormat,
        new FixedValue(0), null);
  }

  /* -- ACCESSORS -- */

  public Expression getFrom() {
    return null;
  }

  public void setFrom(Expression from) {
    // backward compatibility
  }

  public Expression getTo() {
    return to;
  }

  public void setTo(Expression to) {
    this.to = to;
  }

  public Expression getFileSet() {
    return fileSet;
  }

  public void setFileSet(Expression fileSet) {
    this.fileSet = fileSet;
  }

  public Expression getOverwrite() {
    return overwrite;
  }

  public void setOverwrite(Expression overwrite) {
    this.overwrite = overwrite;
  }

  public Expression getFlatten() {
    return flatten;
  }

  public void setFlatten(Expression flatten) {
    this.flatten = flatten;
  }

  public Expression getLimit() {
    return limit;
  }

  public void setLimit(Expression limit) {
    this.limit = limit;
  }

  public Expression getMasterFilePattern() {
    return masterFilePattern;
  }

  public void setMasterFilePattern(Expression masterFilePattern) {
    this.masterFilePattern = masterFilePattern;
  }

  public Expression getSlaveFilesFormat() {
    return slaveFilesFormat;
  }

  public void setSlaveFilesFormat(Expression slaveFilesFormat) {
    this.slaveFilesFormat = slaveFilesFormat;
  }

}
