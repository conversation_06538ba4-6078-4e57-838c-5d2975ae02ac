package com.byzaneo.generix.commons.task.business;

import static com.byzaneo.commons.util.CollectionsHelper.newEntry;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OWNER;
import static com.byzaneo.xtrade.process.VariableHelper.addDeads;
import static com.byzaneo.xtrade.process.VariableHelper.addDocuments;
import static com.byzaneo.xtrade.process.VariableHelper.createIndex;
import static com.byzaneo.xtrade.process.VariableHelper.getDocuments;
import static com.byzaneo.xtrade.process.VariableHelper.newDocumentInstance;
import static com.byzaneo.xtrade.util.DocumentHelper.setOwners;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.*;
import java.text.*;
import java.util.*;

import org.activiti.engine.delegate.*;
import org.apache.commons.io.*;
import org.slf4j.Logger;
import org.springframework.util.ResourceUtils;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.exception.ProcessInterruptedException;
import com.byzaneo.commons.util.*;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.process.reprocess.Reprocess;
import com.byzaneo.xtrade.process.*;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.process.task.*;

/**
 * Business task that allow loading a dead document in the context in order to be able to re-process a BP. If the process in ran for the
 * first time no action will be taken
 * 
 * <AUTHOR> <<EMAIL>>
 */
@Task(name = "reprocessInput", library = "process/business/utils", version = "1.0.0")
@TaskViewModel(labelFamily = "comtsklbls")
public class ReprocessInputTask extends AbstractTask implements CancelableTask {

  private static final long serialVersionUID = 7759391671501239353L;

  private static final Logger log = getLogger(ReprocessInputTask.class);

  private static final String PROPERTIES_SUFFIX = "_properties";

  private static final String BACKUP_PROPERTIES_FILES_URI = "files[0].uri";

  private static final String BACKUP_PROPERTIES_INDEX_TYPE = "index.type";

  private static final List<String> BACKUP_PROPERTIES = Arrays.asList(BACKUP_PROPERTIES_INDEX_TYPE, BACKUP_PROPERTIES_FILES_URI, "type",
      "status", "from", "to", "subtype", "number", "issueDate", "files[0].actionName", "files[0].comment");

  @Override
  public void executeTask(DelegateExecution execution) throws Exception {

    Reprocess reprocess = (Reprocess) execution.getVariable(Reprocess.class.getSimpleName());
    if (reprocess == null) {
      backupProcessInputDir(execution, BACKUP_PROPERTIES);
      return;
    }

    log.info(String.format("Restarting new execution from %s", reprocess));

    File source = getSourceFile(reprocess);
    // get all the files excluding the properties file atached
    Collection<File> files = VariableHelper.getFileSet(execution, new FixedValue("{\"excludes\":\"*" + PROPERTIES_SUFFIX + "\"}"), source);
    String owner = (String) execution.getVariable(PROCESS_OWNER.var);

    List<Document> documents = files.stream()
        .filter(File::exists)
        .peek(file ->
    {
          if (Thread.currentThread()
              .isInterrupted()) {
            throw new ProcessInterruptedException("Canceled by user");
          }
        })
        .map(file ->
    {
          // create an entry with our file and the attached properties file
          File propFile = new File(reprocess.getProcessInputBackupDir(), getPropertiesFileName(file));
          Properties propertiesFile = loadPropertiesFromFile(propFile);
          return newEntry(file, propertiesFile);
        })
        .filter(entry -> entry.getValue() != null)/* only treat documents with properties file attached */
        .map(entry ->
    {
          // copy the reprocessed file to the destination
          String destinationPath = entry.getValue()
              .getProperty(BACKUP_PROPERTIES_FILES_URI);
          File newFile = copyFileToDestinationn(entry.getKey(), FilenameUtils.getFullPathNoEndSeparator(destinationPath));
          return newEntry(newFile, entry.getValue());
        })
        .map(entry -> createDocument(execution, entry.getKey(), owner, entry.getValue()))
        .filter(Objects::nonNull)
        .collect(toList());

    setOwners(documents, owner, false);
    // add the documents to the context
    addDocuments(execution, documents);

  }

  /**
   * Creates a new document based on the given file and the task properties obtained from an attached properties file using backupProperties
   * (list of properties). If the document creation fails it will be added to the execution dead list
   * 
   * @param execution
   * @param file
   * @param owner
   * @param properties
   * @return the new Document
   */
  private Document createDocument(DelegateExecution execution, File file, String owner, Properties properties) {

    Document doc = null;

    FileType type = FileType.getType(file);
    // assume that all XML processed files are XCBLs
    type = FileType.XML.equals(type) ? FileType.XCBL : type;
    DocumentFile documentFile = new DocumentFile(file, type, this.getClass()
        .getSimpleName(),
        execution.getId(), doc, "");
    try {

      doc = newDocumentInstance(execution, null);
      // - creates document -

      // sets reference
      if (StringHelper.isBlank(doc.getReference())) {
        doc.setReference(getReference(file));
      }
      log.debug("Document reference: {}", doc.getReference());

      // - creates document file -
      doc.addFile(documentFile);

      // get the attached properties file (if it exists)
      if (properties != null) {
        // and set the backup document properties
        setDocumentProperties(properties, doc, BACKUP_PROPERTIES);

        if (FileType.XCBL.equals(type)) {
          Expression indexClassName = new FixedValue(properties.getProperty(BACKUP_PROPERTIES_INDEX_TYPE));
          Expression indexFile = new FixedValue(Boolean.TRUE);
          if (createIndex(execution, doc, indexClassName, indexFile, null, file) != null) {
            return doc;
          }
          else {
            // indexing failed
            addDeads(execution, doc);
          }
        }
      }
    }
    catch (Exception e) {
      if (file != null) {
        if (doc == null) {
          doc = new Document(getReference(file), owner, null, null, null, DocumentStatus.ERROR);
        }
        if (!doc.hasFiles()) {
          doc.addFile(documentFile);
        }
        addDeads(execution, doc);
      }
      log.error("Error creating document from file: {} ({})", file, getRootCauseMessage(e));
    }
    return doc;
  }

  /**
   * Copies the fileDocuments of the documents in execution context into the INPUT_BACKUP_DIR
   * 
   * @param execution
   * @param propertiesDescription
   * @throws IOException
   */
  private static void backupProcessInputDir(DelegateExecution execution, List<String> propertiesDescription) throws IOException {

    File destDir = VariableHelper.getInputBackupDirectory(execution);
    for (Document document : getDocuments(execution)) {
      DocumentFile docFile = document.getFirstDocumentFile();
      if (docFile == null)
        continue;

      if (isNotEmpty(propertiesDescription)) {
        File file = new File(destDir, getPropertiesFileName(docFile.getFile()));
        writeDocumentProperties(file, document, propertiesDescription);
      }
      FileUtils.copyFileToDirectory(docFile.getFile(), destDir);
    }
  }

  /**
   * Copies a file to a directory preserving the file date and returns the new file
   * 
   * @param execution
   * @param path
   * @return
   */
  private File copyFileToDestinationn(File file, String path) {
    try {
      File destDir = ResourceUtils.getFile(path);
      FileUtils.copyFileToDirectory(file, destDir);
      return new File(destDir, file.getName());
    }
    catch (IOException ex) {
      log.error("The destination path is incorect. The dead or backup document path will be used");
      return file;
    }
  }

  private File getSourceFile(Reprocess reprocess) throws AssertionError {
    File source;
    switch (reprocess.getReprocessType()) {
    case DEAD_QUEUE:
      source = new File(reprocess.getProcessDeadQueue());
      break;
    case FULL:
      source = new File(reprocess.getProcessInputBackupDir());
      break;
    default:
      throw new AssertionError(reprocess.getReprocessType());
    }
    return source;
  }

  /**
   * Writes in the given file the all the document properties given in the propertiesDescription
   * 
   * @param file
   * @param document
   * @param propertiesDescription
   */
  private static void writeDocumentProperties(File file, Document document, List<String> propertiesDescription) {

    try (FileOutputStream fileOut = new FileOutputStream(file)) {
      Properties properties = new Properties();
      for (String propertyExpression : propertiesDescription) {
        Object property = ExpressionHelper.getProperty(document, propertyExpression);
        if (property == null)
          continue;

        if (String.class.isAssignableFrom(property.getClass()) || Enum.class.isAssignableFrom(property.getClass())) {
          properties.setProperty(propertyExpression, property.toString());
        }
        else if (Date.class.isAssignableFrom(property.getClass())) {
          // treat Date expression case
          properties.setProperty(propertyExpression, new SimpleDateFormat().format(property));
        }
        else if (Class.class.isAssignableFrom(property.getClass())) {
          // if the property is of type Class use the name
          properties.setProperty(propertyExpression, ((Class<?>) property).getName());
        }
      }

      properties.store(fileOut, "Document properties");
    }
    catch (IOException e) {
      log.error("Error writing document properties file: {} ({})", file, getRootCauseMessage(e));
    }
  }

  /**
   * @param file
   * @return
   */
  private static Properties loadPropertiesFromFile(File file) {
    try (FileInputStream fileIn = new FileInputStream(file)) {
      Properties properties = new Properties();
      properties.load(fileIn);
      return properties;
    }
    catch (IOException e) {
      log.error("Error reading document properties file: {} ({})", file, getRootCauseMessage(e));
      return null;
    }
  }

  private static void setDocumentProperties(Properties properties, Document document, List<String> propertiesDescription) {
    if (properties == null) {
      return;
    }
    for (String propertyExpression : propertiesDescription) {
      String property = properties.getProperty(propertyExpression);
      if (property != null) {
        Class<?> type = ExpressionHelper.getValueType(document, propertyExpression);
        if (String.class.equals(type) || Enum.class.isAssignableFrom(type)) {
          ExpressionHelper.setProperty(document, propertyExpression, property);
        }
        else if (Date.class.equals(type)) {
          try {
            ExpressionHelper.setProperty(document, propertyExpression, new SimpleDateFormat().parse(property));
          }
          catch (ParseException e) {
            log.error("Cannot set value '{}' to {}", property, propertyExpression);
          }
        }
      }
    }
  }

  private static String getPropertiesFileName(File file) {
    return getBaseName(file.getAbsolutePath()) + PROPERTIES_SUFFIX;
  }

  private String getReference(File source) {
    return getBaseName(source.getAbsolutePath());
  }
}
