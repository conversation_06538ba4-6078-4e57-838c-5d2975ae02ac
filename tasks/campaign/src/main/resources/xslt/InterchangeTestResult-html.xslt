<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:altova="http://www.altova.com" xmlns:altovaext="http://www.altova.com/xslt-extensions" xmlns:clitype="clitype" xmlns:fn="http://www.w3.org/2005/xpath-functions" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:ix="http://www.xbrl.org/2008/inlineXBRL" xmlns:java="java" xmlns:jxb="http://java.sun.com/xml/ns/jaxb" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:sps="http://www.altova.com/StyleVision/user-xpath-functions" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" exclude-result-prefixes="#all">
	<xsl:output version="1.0" method="xml" indent="no" encoding="UTF-8" omit-xml-declaration='yes'
	use-character-maps="spaces" doctype-public="-//W3C//DTD HTML 4.01 Transitional//EN" doctype-system="http://www.w3.org/TR/html4/loose.dtd"/>
	<xsl:character-map name="spaces">
		<xsl:output-character character="&#160;" string="&amp;nbsp;"/>
	</xsl:character-map>
<!-- 	<xsl:param name="altova:bGeneratingFromPxf" select="false()"/> -->
<!-- 	<xsl:param name="SV_OutputFormat" select="'HTML'"/> -->
<!-- 	<xsl:param name="SV_BaseOutputFileName" as="xs:string?"> -->
<!-- 		<xsl:sequence select="for $i in altovaext:get-base-output-uri(), $j in tokenize( $i, &apos;[/\\]&apos; )[last()] return replace( $j, &apos;\.[^\.\s#%;]*$&apos;, &apos;&apos; )" use-when="function-available(&apos;altovaext:get-base-output-uri&apos;)"/> -->
<!-- 	</xsl:param> -->
<!-- 	<xsl:param name="SV_GeneratedFileNamePrefix" select="if ( $SV_BaseOutputFileName ) then $SV_BaseOutputFileName else &apos;InterchangeTestResult&apos;" as="xs:string?"/> -->
	<xsl:variable name="XML" select="/"/>
<!-- 	<xsl:variable name="altova:nPxPerIn" select="96"/> -->
<!-- 	<xsl:import-schema schema-location="C:\dev\workspace\generix\tasks\gcm\src\main\resources\schemas\InterchangeTestResult.xsd"/> -->
<!-- 	<xsl:variable name="altova:CssImages" select="()"/> -->
<!-- 	<xsl:template match="/">
		<xsl:call-template name="altova:Root"/>
	</xsl:template>  -->
	<xsl:template match="/">
	<html>
		<link href="bootstrap-3.3.7-dist\css\bootstrap.min.css" rel="stylesheet" media="screen"/>
		<body>
				<div class="panel panel-primary">
					<div class="panel panel-heading">
					<h4>Résultat du test pour le partenaire <xsl:value-of select="test_result/test/partner"/></h4>
					</div>
			
				<div class="panel-body">
					<table class="table table-bordered table-hover">
						<tr>
								<th colspan="6">Informations enveloppe</th>
						</tr>
						<tr>
							<td>Date du message</td>
							<td><xsl:value-of select="test_result/test/date"/></td>
							<td>N° enveloppe</td>
							<td><xsl:value-of select="test_result/test/interchange/@id"/></td>
						</tr>
						<tr>
							<td>Emetteur message</td>
							<td><xsl:value-of select="test_result/test/interchange/sender"/></td>
							<td>Destinataire message</td>
							<td><xsl:value-of select="test_result/test/interchange/recipient"/></td>
						</tr>
					</table>
							<table class="table table-bordered table-hover">
						<tr>
								<th colspan="6">Informations message</th>
						</tr>
						<tr>
							<td>N° message</td>
							<td><xsl:value-of select="test_result/test/interchange/message/@id"/></td>
							<td>N° document</td>
							<td><xsl:value-of select="test_result/test/interchange/message/document_ref"/></td>
							<td>Nombre d'erreur</td>
								<xsl:choose>
										<xsl:when test="test_result/test/interchange/message/number_of_error = 0">
											<td><button type="button" disabled="disabled" class="btn btn-success"><xsl:value-of select="test_result/test/interchange/message/number_of_error"/></button></td>
										</xsl:when>
									<xsl:otherwise>
											<td><button type="button" disabled="disabled" class="btn btn-danger"><xsl:value-of select="test_result/test/interchange/message/number_of_error"/></button></td>
									</xsl:otherwise>
								</xsl:choose>
						</tr>
					</table>
					
					<xsl:choose>
						<xsl:when test="test_result/test/interchange/message/number_of_error != 0">
							<table class="table table-bordered table-hover">
								<tr>
									<th colspan="6">Erreurs</th>
								</tr>
								<xsl:for-each select="test_result/test/interchange/message/error">
									<tr>
										<td rowspan="3"><h4><span class="label label-primary">Ligne <xsl:value-of select="@line"/></span></h4><br/><xsl:value-of select="rules_number"/></td>
										<td>Type d'erreur </td>
										<td><xsl:value-of select="short_description"/></td>
									</tr>
									<tr>
										<td>Erreur</td>
										<td><xsl:value-of select="data"/></td>
									</tr>
									<tr>
										<td>Donnée </td>
										<td><xsl:value-of select="detail"/></td>
									</tr>
								</xsl:for-each>		
							</table>
						</xsl:when>
					</xsl:choose>	
				</div>
				<div class="panel-footer">
					Copyright Generix 2016-2017
				</div>
				</div>
		  </body>
	 </html>
</xsl:template>
<!-- 	<xsl:function name="altova:is-cell-empty" as="xs:boolean"> -->
<!-- 		<xsl:param name="altova:cell" as="element()"/> -->
<!-- 		<xsl:sequence select="altova:is-node-empty( $altova:cell )"/> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:function name="altova:is-node-empty" as="xs:boolean"> -->
<!-- 		<xsl:param name="altova:node" as="element()"/> -->
<!-- 		<xsl:sequence select="every $altova:child in $altova:node/child::node() satisfies ( ( boolean( $altova:child/self::text() ) and string-length( $altova:child ) = 0 ) or ( ( boolean( $altova:child/self::div ) or boolean( $altova:child/self::span ) or boolean( $altova:child/self::a ) ) and altova:is-node-empty( $altova:child ) ) )"/> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:function name="altova:col-span" as="xs:integer"> -->
<!-- 		<xsl:param name="altova:cell" as="element()"/> -->
<!-- 		<xsl:sequence select="if ( exists( $altova:cell/@colspan ) ) then xs:integer( $altova:cell/@colspan ) else 1"/> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:template match="@* | node()" mode="altova:generate-table"> -->
<!-- 		<xsl:param name="altova:generate-cols"/> -->
<!-- 		<xsl:copy> -->
<!-- 			<xsl:apply-templates select="@* | node()" mode="#current"> -->
<!-- 				<xsl:with-param name="altova:generate-cols" select="$altova:generate-cols"/> -->
<!-- 			</xsl:apply-templates> -->
<!-- 		</xsl:copy> -->
<!-- 	</xsl:template> -->
<!-- 	<xsl:template match="tbody" mode="altova:generate-table"> -->
<!-- 		<xsl:param name="altova:generate-cols"/> -->
<!-- 		<xsl:choose> -->
<!-- 			<xsl:when test="empty(tr)"> -->
<!-- 				<xsl:copy> -->
<!-- 					<tr> -->
<!-- 						<td/> -->
<!-- 					</tr> -->
<!-- 				</xsl:copy> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:otherwise> -->
<!-- 				<xsl:copy> -->
<!-- 					<xsl:apply-templates select="@* | node()" mode="#current"> -->
<!-- 						<xsl:with-param name="altova:generate-cols" select="$altova:generate-cols"/> -->
<!-- 					</xsl:apply-templates> -->
<!-- 				</xsl:copy> -->
<!-- 			</xsl:otherwise> -->
<!-- 		</xsl:choose> -->
<!-- 	</xsl:template> -->
<!-- 	<xsl:template match="th | td" mode="altova:generate-table"> -->
<!-- 		<xsl:choose> -->
<!-- 			<xsl:when test="altova:is-cell-empty( . )"> -->
<!-- 				<xsl:copy> -->
<!-- 					<xsl:apply-templates select="@*" mode="#current"/> -->
<!-- 					<xsl:text>&#160;</xsl:text> -->
<!-- 				</xsl:copy> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:otherwise> -->
<!-- 				<xsl:copy> -->
<!-- 					<xsl:apply-templates select="@* | node()" mode="#current"/> -->
<!-- 				</xsl:copy> -->
<!-- 			</xsl:otherwise> -->
<!-- 		</xsl:choose> -->
<!-- 	</xsl:template> -->
<!-- 	<xsl:function name="altova:GetChartYValuesForSingleSeries"> -->
<!-- 		<xsl:param name="seqCategoryLeafPos" as="node()*"/> -->
<!-- 		<xsl:param name="nodeSeriesLeafPos" as="node()"/> -->
<!-- 		<xsl:param name="bValuesInCategory" as="xs:boolean"/> -->
<!-- 		<xsl:for-each select="$seqCategoryLeafPos"> -->
<!-- 			<xsl:element name="altova:Value"> -->
<!-- 				<xsl:value-of select="altova:GetChartYValueForSingleSeriesPos($nodeSeriesLeafPos, ., $bValuesInCategory)"/> -->
<!-- 			</xsl:element> -->
<!-- 		</xsl:for-each> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:function name="altova:GetChartYValueForSingleSeriesPos"> -->
<!-- 		<xsl:param name="nodeSeriesLeafPos" as="node()"/> -->
<!-- 		<xsl:param name="nodeCategoryLeafPos" as="node()"/> -->
<!-- 		<xsl:param name="bValuesInCategory" as="xs:boolean"/> -->
<!-- 		<xsl:variable name="altova:seqCategoryContextIds" select="$nodeCategoryLeafPos/altova:Context/@altova:ContextId" as="xs:string*"/> -->
<!-- 		<xsl:variable name="altova:seqSeriesContextIds" select="$nodeSeriesLeafPos/altova:Context/@altova:ContextId" as="xs:string*"/> -->
<!-- 		<xsl:variable name="altova:sCommonContextId" select="for $i in $altova:seqCategoryContextIds return if (some $j in $altova:seqSeriesContextIds satisfies $i eq $j) then $i else ()" as="xs:string*"/> -->
<!-- 		<xsl:choose> -->
<!-- 			<xsl:when test="count($altova:sCommonContextId) gt 1"> -->
<!-- 				<xsl:message select="concat('Found several values instead of a single one (contexts: ', string-join($altova:sCommonContextId, ', '), ').')" terminate="yes"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="count($altova:sCommonContextId) lt 1"> -->
<!-- 				<xsl:message select="concat('XBRL Chart: Info: No value found for position labeled &quot;', $nodeCategoryLeafPos/@altova:sLabel, '&quot;')" terminate="no"/> -->
<!-- 				<xsl:sequence select="'altova:no-value'"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="$bValuesInCategory"> -->
<!-- 				<xsl:sequence select="xs:string($nodeCategoryLeafPos/altova:Context[@altova:ContextId eq $altova:sCommonContextId]/@altova:Value)"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:otherwise> -->
<!-- 				<xsl:sequence select="xs:string($nodeSeriesLeafPos/altova:Context[@altova:ContextId eq $altova:sCommonContextId]/@altova:Value)"/> -->
<!-- 			</xsl:otherwise> -->
<!-- 		</xsl:choose> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:function name="altova:GetChartLabelForPos" as="xs:string"> -->
<!-- 		<xsl:param name="nodeParam" as="node()"/> -->
<!-- 		<xsl:value-of select="string-join($nodeParam/ancestor-or-self::altova:Pos/@altova:sLabel, ' ')"/> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:function name="altova:convert-length-to-pixel" as="xs:decimal"> -->
<!-- 		<xsl:param name="altova:length"/> -->
<!-- 		<xsl:variable name="normLength" select="normalize-space($altova:length)"/> -->
<!-- 		<xsl:choose> -->
<!-- 			<xsl:when test="ends-with($normLength, 'px')"> -->
<!-- 				<xsl:value-of select="substring-before($normLength, 'px')"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'in')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'in')) * $altova:nPxPerIn"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'cm')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'cm')) * $altova:nPxPerIn div 2.54"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'mm')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'mm')) * $altova:nPxPerIn div 25.4"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'pt')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'pt')) * $altova:nPxPerIn div 72.0"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'pc')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'pc')) * $altova:nPxPerIn div 6.0"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:otherwise> -->
<!-- 				<xsl:value-of select="$normLength"/> -->
<!-- 			</xsl:otherwise> -->
<!-- 		</xsl:choose> -->
<!-- 	</xsl:function> -->
<!-- 	<xsl:function name="altova:convert-length-to-mm" as="xs:decimal"> -->
<!-- 		<xsl:param name="altova:length"/> -->
<!-- 		<xsl:variable name="normLength" select="normalize-space($altova:length)"/> -->
<!-- 		<xsl:choose> -->
<!-- 			<xsl:when test="ends-with($normLength, 'px')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'px')) div $altova:nPxPerIn * 25.4"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'in')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'in')) * 25.4"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'cm')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'cm')) * 10"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'mm')"> -->
<!-- 				<xsl:value-of select="substring-before($normLength, 'mm') "/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'pt')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'pt')) * 25.4 div 72.0"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:when test="ends-with($normLength, 'pc')"> -->
<!-- 				<xsl:value-of select="xs:decimal(substring-before($normLength, 'pc')) * 25.4 div 6.0"/> -->
<!-- 			</xsl:when> -->
<!-- 			<xsl:otherwise> -->
<!-- 				<xsl:value-of select="number($normLength) div $altova:nPxPerIn * 25.4"/> -->
<!-- 			</xsl:otherwise> -->
<!-- 		</xsl:choose> -->
<!-- 	</xsl:function> -->
</xsl:stylesheet>
