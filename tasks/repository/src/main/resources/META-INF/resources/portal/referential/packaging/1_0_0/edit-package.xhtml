<p:panel id="idPackageEdit"
         closable="true"
         visible="true"
         widgetVar="wPackageEditPnl"
         closeSpeed="0"
         style="margin-bottom: 10px; border: 0;"
         rendered="#{cc.attrs.value.indexablePackage != null}"
         styleClass="container-fluid psPackageEdit"
         xmlns="http://www.w3.org/1999/xhtml"
         xmlns:p="http://primefaces.org/ui"
         xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
         xmlns:h="http://xmlns.jcp.org/jsf/html"
         xmlns:f="http://xmlns.jcp.org/jsf/core"
>

    <ui:param name="package" value="#{cc.attrs.value.indexablePackage}"/>
    <ui:param name="taskBean" value="#{cc.attrs.value}"/>

    <p:focus/>

    <h:form id="packageForm">
        <p:outputPanel styleClass="panel panel-default">
            <!-- PANEL HEADER -->
            <!--<p:outputPanel styleClass="panel-heading"/>-->

			<p:messages autoUpdate="true" closable="true"/>

            <!-- PANEL BODY -->
            <p:outputPanel styleClass="panel-body">
                <p:outputPanel styleClass="container-fluid">
                    <p:outputPanel styleClass="row">
                        <p:outputPanel styleClass="col-md-12">
                            <p:panel columns="2" cellpadding="5" header="${gnxrefpacklbls.header_panel_package}">
                            	<p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="packageCode" value="${gnxrefpacklbls.code}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputText id="packageCode" required="true"
                                                 validator="xssValidator"
                                    			 disabled="#{taskBean.getOldPackageCode()!=null}"
                                                 value="${package.code}" styleClass="col-sm-3"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="designation" value="${gnxrefpacklbls.designation}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputText id="designation" required="false"
                                                 validator="xssValidator"
                                                 value="${package.designation}" styleClass="col-sm-3"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="PCB" value="${gnxrefpacklbls.pcb}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="PCB" required="true"
                                                 value="${package.pcb}" styleClass="col-sm-3"
                                                 decimalPlaces="0">
                                                 <f:validateDoubleRange minimum="1"/>
 									</p:inputNumber>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="SPCB" value="${gnxrefpacklbls.spcb}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="SPCB" required="false"
                                                 value="${package.spcb}" styleClass="col-sm-3"
                                                 decimalPlaces="0" minValue="0"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="height" value="${gnxrefpacklbls.height}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="height" required="false"
                                                 value="${package.height}" styleClass="col-sm-3"
                                                  minValue="0"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="length" value="${gnxrefpacklbls.length}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="length" required="false"
                                                 value="${package.length}" styleClass="col-sm-3"
                                                  minValue="0"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="width" value="${gnxrefpacklbls.width}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="width" required="false"
                                                 value="${package.width}" styleClass="col-sm-3"
                                                  minValue="0"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="weight" value="${gnxrefpacklbls.weight}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="weight" required="false"
                                                 value="${package.weight}" styleClass="col-sm-3"
                                                  minValue="0"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel for="expansionCoeff" value="${gnxrefpacklbls.expansionCoeff}"
                                                   styleClass="col-sm-3 control-label"/>
                                    <p:inputNumber id="expansionCoeff" required="false"
                                                 value="${package.expansionCoeff}" styleClass="col-sm-3"
                                                 decimalPlaces="0" symbol="%" symbolPosition="s"
                                                  maxValue="100" minValue="0"/>
                                </p:outputPanel>
                            </p:panel>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:outputPanel>
            </p:outputPanel>

            <!-- PANEL FOOTER -->
            <p:outputPanel styleClass="panel-footer">
                <p:outputPanel styleClass="right">
                    <ui:param name="previousPage"
                              value=""/>

                    <p:commandButton icon="ui-icon-cancel"
                                     value="#{labels.cancel}"
                                     styleClass="mls"
                                     process="@this"
                                     update="@(.packPsDocumentPnl)"
                                     actionListener="#{taskBean.onCancelPackage()}"
                                     oncomplete="#{previousPage}">
                    </p:commandButton>

                    <p:commandButton id="idCfmPackage"
                                     icon="fa fa-paper-plane-o fa-fw"
                                     value="#{labels.save}"
                                     process="@this @(.psPackageEdit)"
                                     update="@(.packPsDocumentPnl)"
                                     actionListener="#{taskBean.onSavePackage()}"
                                     onstart="return $('.ui-icon-check:visible').length==0;"
                                     onclick="$('.ui-icon-check:visible').click();"
                                     ignoreAutoUpdate="true"/>

                </p:outputPanel>
            </p:outputPanel>
        </p:outputPanel>
    </h:form>

    <script type="text/javascript">

    </script>
</p:panel>