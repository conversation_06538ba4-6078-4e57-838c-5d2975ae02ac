<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://java.sun.com/jsf/core"
              xmlns:h="http://java.sun.com/jsf/html"
              xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://java.sun.com/jsf/facelets"
              xmlns:c="http://java.sun.com/jsp/jstl/core"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:pe="http://primefaces.org/ui/extensions">
    <!-- INTERFACE -->
    <cc:interface name="dialogProduct">
        <cc:attribute name="taskBean" required="true"/>
    </cc:interface>
    <!-- IMPLEMENTATION -->
    <cc:implementation>
      <script type="text/javascript">
			function removeMessages() {
            	$('#tskForm-taskMsgTop').css('display','none'); 
        		$('#tskForm-taskMsgBot').css('display','none');
            }
		</script>
        <p:dialog id="dlgProduct"
                  modal="true"
                  closable="true"
                  closeOnEscape="true"
                  styleClass="psDlgProduct"
                  header="#{cc.attrs.taskBean.selectedProduct.id==null ? gnxxcblreflbls.pdt_product_new : gnxxcblreflbls.pdt_product_edit.concat(' ').concat(cc.attrs.taskBean.selectedProduct.productName)}"
                  widgetVar="wDlgProduct"
                  minHeight="40"
                  style="min-width:800px"
                  width="auto"
                  height="auto"
                  onShow="if(typeof changeDialogYPosition === 'function') {changeDialogYPosition('psDlgProduct', 'resultTable');} PF('wTabView').select(0)"
                  position="center center">
            <p:ajax event="close"
                    oncomplete="reset()"
                    immediate="true"/>
            <p:remoteCommand name="reset"
                             process="@this">
                <p:resetInput target="dlgProduct"/>
            </p:remoteCommand>
            <p:messages id="dlgMessages"
                        styleClass="psDlgMessages" closable="true"/>
            <p:tabView widgetVar="wTabView">
                <p:tab title="#{gnxxcblreflbls.pdt_product_tab_general}">
                    <h:panelGroup id="productTable"
                                  layout="block"
                                  styleClass="psProductTable"
                                  rendered="#{cc.attrs.taskBean.selectedProduct!=null}">
                        <h:outputText value="&lt;br/&gt;" escape="false"/>
                        
                        <p:outputPanel styleClass="col-sm-12">
								<p:outputPanel styleClass="row" style="margin-bottom: 18px;">
									<!-- Brand -->
									<p:outputPanel styleClass="ui-datagrid-column ui-grid-col-6">
										<h:outputLabel for="brand" styleClass="col-sm-6" 
												value="#{gnxxcblreflbls.brand}" />
										<p:inputText id="brand" styleClass="col-sm-6 form-control-sm form-control-rounded brandStyle"
											value="#{cc.attrs.taskBean.selectedProduct.from}" validator="xssValidator"/>
									</p:outputPanel>
									<!-- Family -->
									<p:outputPanel styleClass="ui-datagrid-column ui-grid-col-6">
			                            <h:outputLabel for="family" styleClass="col-sm-6" 
												value="#{gnxxcblreflbls.family}" />
										<p:inputText id="family" styleClass="col-sm-6 form-control-sm form-control-rounded familyStyle"
											value="#{cc.attrs.taskBean.selectedProduct.family}" validator="xssValidator"/>
									</p:outputPanel>
								</p:outputPanel>
						</p:outputPanel>
                        
                       	<p:dataGrid value="#{cc.attrs.taskBean.getRenderedSearchProperties('description', 'calculationNet', 'calculationGross', 'vat')}"
                                    var="property"
                                    columns="2"
                                    styleClass="productDataGrid"
                                    layout="grid">
                            <c:set var="isDate"
                                   value="#{property.type.simpleName == 'XMLGregorianCalendar' or property.type.simpleName == 'Date'}"/>
                            <c:set var="isNumber"
                                   value="#{property.type.simpleName == 'Double' or property.type.simpleName == 'Float'}"/>
                            <c:set var="isDisabled"
                                   value="#{not (property.editable and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits', 'ADMIN')))}"/>
                            <c:set var="isGrantedToModifyNetPrice"
                                   value="#{(property.name != 'calculationNet' or (property.name == 'calculationNet' and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == true)))}"/>
                            <c:set var="isGrantedToReadNetPrice"
                                   value="#{property.name == 'calculationNet' and ((gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == false) and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'READ') == true))}"/>

                            <p:outputLabel value="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}#{property.required?' *':''}"
                                           styleClass="col-sm-6"
                                           rendered="#{not ((not cc.attrs.taskBean.isMultipleValues(property))
                                           		and isNumber 
                                           		and property.name == 'calculationNet'
                                           		and ((gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == false) and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'READ') == false)))}"/>
                            <p:inputText value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                            	   	     validator="xssValidator"
                                         maxlength="255"
                                         label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                         styleClass="col-sm-6 form-control form-control-rounded form-control-sm"
                                         rendered="#{not cc.attrs.taskBean.isMultipleValues(property) and not (isDate or isNumber) and not (property.name == 'unitOfMeasurementType') and not (property.name == 'reference')}"
                                         style="margin-bottom: 15px;"
                                         disabled="#{isDisabled}"
                                         required="#{property.required}"/>
                            <p:inputText value="#{cc.attrs.taskBean.selectedProduct['reference']}"
                                         validator="xssValidator"
                                         maxlength="255"
                                         label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                         styleClass="col-sm-6 form-control form-control-rounded form-control-sm"
                                         rendered="#{property.name == 'reference'}"
                                         style="margin-bottom: 15px;"
                                         disabled="#{isDisabled}"
                                         required="#{property.required}">
                                    <f:attribute name="ctrlEan" value="#{cc.attrs.taskBean.ctrlEan}" />
									<f:validator validatorId="eanCodeValidator" />
                            </p:inputText>
                            <p:autoComplete id="unitOfMeasurement"
                                            rendered="#{cc.attrs.taskBean.isMultipleValues(property) and not (isDate or isNumber) and (property.name == 'unitOfMeasurementType')}"
                                            dropdown="true"
                                            title="#{labels.selection_or_enter}"
                                            value="#{cc.attrs.taskBean.selectedProduct['unitOfMeasurementType']}"
                                            required="#{property.required}"
                                            requiredMessage="#{gnxxcblinvlbls.unit_of_measurement} : #{gnxxcblcomlbls.value_required}"
                                            completeMethod="#{cc.attrs.taskBean.getUnitOfMeasureAsString()}"
                                            var="unit"
                                            itemValue="#{unit}"
                                            itemLabel="#{cc.attrs.taskBean.getUnitOfMeasureFromString(unit)}"
                                            styleClass="col-sm-6 #{property.name} input-autocomplete-resize product-logistics-uom">
                                <p:ajax event="itemSelect"
                                        process="@this" update="unitOfMeasurement" immediate="true" global="false"/>
                                <p:ajax event="query"
                                        process="@this" update="@this" immediate="true" global="false"/>
                            </p:autoComplete>

                            <!-- When user can only read net price and not modify it, net price will be replaced by gross price -->
                            <pe:inputNumber value="#{cc.attrs.taskBean.selectedProduct['calculationGross']}"
                                            label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                            styleClass="col-sm-6 "
                                            style="padding: 0;margin-bottom: 15px;"
                                            symbolPosition="s"
                                            decimalSeparator=","
                                            decimalPlaces="6"
                                            thousandSeparator=" "
                                            required="#{property.required}"
                                            rendered="#{(not cc.attrs.taskBean.isMultipleValues(property)) and isNumber and isGrantedToReadNetPrice}"
                                            disabled="true"/>

                            <ui:fragment
                                    rendered="#{not cc.attrs.taskBean.isMultipleValues(property) and isNumber and isGrantedToModifyNetPrice}">
                                <pe:inputNumber value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                                label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                                styleClass="col-sm-6"
                                                style="padding: 0;margin-bottom: 15px;"
                                                symbolPosition="s"
                                                decimalSeparator=","
                                                decimalPlaces="6"
                                                thousandSeparator=" "
                                                required="#{property.required}"
                                                rendered="#{property.typeName != 'PERCENT'}"
                                                disabled="#{isDisabled}"/>
                                <pe:inputNumber value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                                label="#{gnxxcblreflbls[property.name]}"
                                                styleClass="col-sm-6"
                                                style="padding: 0;margin-bottom: 15px;"
                                                symbol=" %" symbolPosition="s"
                                                decimalSeparator=","
                                                thousandSeparator=" "
                                                minValue="0.000"
                                                maxValue="100.000"
                                                required="#{property.required}"
                                                rendered="#{property.typeName == 'PERCENT'}"
                                                disabled="#{isDisabled}"/>
                            </ui:fragment>
							<p:autoComplete value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
											rendered="#{cc.attrs.taskBean.isMultipleValues(property) and property.name == 'to'}"
											completeMethod="#{gnxSecurityAppHandler.onCompletePartner}"
											converter="addressPartnerCodeConverter" var="partner"
											maxResults="5" minQueryLength="3" forceSelection="true"
											disabled="#{cc.attrs.readOnly}"
											validator="xssValidator" itemValue="#{partner.code}"
											itemLabel="#{gnxAddressHandler.getPartnerFullname(partner)}"
											styleClass="autocomplete-center w-100"
											inputStyleClass="w-100 address-input">
								<p:ajax event="itemSelect" process="@this" listener="#{cc.attrs.taskBean.onItemSelect}"
										update="#{cc.attrs.taskBean.getUpdate(property)}"/>
								<p:column styleClass="autocomplete-partner-column nowrap">
									<h:outputText value="#{partner.code}" />
								</p:column>
								<p:column
										styleClass="autocomplete-partner-fullname-column nowrap">
									<h:outputText value="#{partner.fullname}" />
								</p:column>
							</p:autoComplete>
                            <p:selectOneMenu label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                             styleClass="col-sm-6 #{property.name}"
                                             panelStyleClass="productPanel"
                                             value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                             rendered="#{cc.attrs.taskBean.isMultipleValues(property) and not (property.name == 'unitOfMeasurementType' or property.name == 'to')}"
                                             required="#{property.required}"
                                             disabled="#{isDisabled}"
                                             filter="true"
                                             filterMatchMode="contains">
                                <f:attribute name="property" value="#{property}" />
                                <p:ajax event="change"
                                        listener="#{cc.attrs.taskBean.onValueChangeListener}"
                                        process="@this"
                                        immediate="true"
                                        ignoreAutoUpdate="true"
                                        update="#{cc.attrs.taskBean.getUpdate(property)}"
                                        disabled="#{not cc.attrs.taskBean.isListenable(property)}"/>
                                <f:selectItem itemLabel="#{labels.selection}"
                                              itemValue=""/>
                                <f:selectItems value="#{cc.attrs.taskBean.getObjectValues(property)}"
                                               var="v"
                                               itemLabel="#{cc.attrs.taskBean.labelObject(v)}"
                                               itemValue="#{cc.attrs.taskBean.valueObject(v)}"
                                               itemDescription="#{cc.attrs.taskBean.descriptionObject(v)}"/>
                            </p:selectOneMenu>
                            <p:calendar label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                        styleClass="pDate col-sm-6 form-control form-control-rounded form-control-sm"
                                        style="padding:0;margin-bottom: 15px;background-color: #eee;"
                                        navigator="true"
                                        value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                        locale="#{gnxSessionHandler.locale}"
                                        timeZone="#{secSessionHandler.timeZone}"
                                        rendered="#{isDate}"
                                        required="#{property.required}"
                                        disabled="true"/>
                        </p:dataGrid>
                        
                        <h:panelGroup layout="block"
                                      style="clear: both;"/>
                        <!-- Champs sur une ligne -->
                        <p:dataGrid value="#{cc.attrs.taskBean.search['description']}"
                                    var="property"
                                    styleClass="productDataGrid"
                                    columns="1"
                                    layout="grid">
                            <p:outputLabel id="DESCTHIS" value="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}#{property.required?' *':''}"
                                           styleClass="col-sm-3 description-label" style="font-size: 14px !important;"/>
                            <p:inputText label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                         validator="xssValidator"
                                         maxlength="255"
                                         value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                         styleClass="col-sm-9 form-control form-control-rounded form-control-sm"
                                         disabled="#{not (property.editable and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Description', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Description', 'ADMIN')))}"
                                         required="#{property.required}"/>
                        </p:dataGrid>
                    </h:panelGroup>
                </p:tab>
                <p:tab title="#{gnxxcblreflbls.pdt_product_tab_allowance_charge}"
                       rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'READ') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'ADMIN')}">
                   
                   <!--  Price gross and net and Vat Code -->
                    <h:panelGroup layout="block"
                                  styleClass="psProductTable"
                                  rendered="#{cc.attrs.taskBean.selectedProduct!=null}">
                        <h:outputText value="&lt;br/&gt;" escape="false"/>
                        <p:dataGrid value="#{cc.attrs.taskBean.getRenderedSearchPropertiesByNames('calculationNet' , 'calculationGross' , 'vat')}"
                                    var="property"
                                    columns="2"
                                    styleClass="productDataGrid"
                                    layout="grid">
                            <c:set var="isDate"
                                   value="#{property.type.simpleName == 'XMLGregorianCalendar' or property.type.simpleName == 'Date'}"/>
                            <c:set var="isNumber"
                                   value="#{property.type.simpleName == 'Double' or property.type.simpleName == 'Float'}"/>
                            <c:set var="isDisabled"
                                   value="#{not (property.editable and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits', 'ADMIN')))}"/>
                            <c:set var="isGrantedToModifyNetPrice"
                                   value="#{(property.name != 'calculationNet' or (property.name == 'calculationNet' and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == true)))}"/>
                            <c:set var="isGrantedToReadNetPrice"
                                   value="#{property.name == 'calculationNet' and ((gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == false) and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'READ') == true))}"/>

                            <p:outputLabel value="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}#{property.required?' *':''}"
                                           styleClass="col-sm-4"
                                           rendered="#{not ((not cc.attrs.taskBean.isMultipleValues(property))
                                           		and isNumber 
                                           		and property.name == 'calculationNet'
                                           		and ((gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == false) and (gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'READ') == false)))}"/>
                            <!-- When user can only read net price and not modify it, net price will be replaced by gross price -->
                            <pe:inputNumber value="#{cc.attrs.taskBean.selectedProduct['calculationGross']}"
                                            label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                            styleClass="col-sm-5 form-control form-control-sm form-control-rounded "
                                            style="padding: 0;margin-bottom: 15px;"
                                            symbolPosition="s"
                                            decimalSeparator=","
                                            decimalPlaces="6"
                                            thousandSeparator=" "
                                            required="#{property.required}"
                                            rendered="#{(not cc.attrs.taskBean.isMultipleValues(property)) and isNumber and isGrantedToReadNetPrice}"
                                            disabled="true"/>

                            <ui:fragment
                                    rendered="#{not cc.attrs.taskBean.isMultipleValues(property) and isNumber and isGrantedToModifyNetPrice}">
                                <pe:inputNumber value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                                label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                                styleClass="col-sm-5 form-control form-control-sm form-control-rounded "
                                                style="padding: 0;margin-bottom: 15px;"
                                                symbolPosition="s"
                                                decimalSeparator=","
                                                decimalPlaces="6"
                                                thousandSeparator=" "
                                                required="#{property.required}"
                                                rendered="#{property.typeName != 'PERCENT'}"
                                                disabled="#{isDisabled}"/>
                                <pe:inputNumber value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                                label="#{gnxxcblreflbls[property.name]}"
                                                styleClass="col-sm-5 form-control form-control-sm form-control-rounded"
                                                style="padding: 0;margin-bottom: 15px;"
                                                symbol=" %" symbolPosition="s"
                                                decimalSeparator=","
                                                thousandSeparator=" "
                                                minValue="0.000"
                                                maxValue="100.000"
                                                required="#{property.required}"
                                                rendered="#{property.typeName == 'PERCENT'}"
                                                disabled="#{isDisabled}"/>
                            </ui:fragment>
                            <p:selectOneMenu label="#{gnxHandler.label(property.label, gnxSessionHandler.locale)}"
                                             styleClass="col-sm-5 #{property.name}"
                                             panelStyleClass="productPanel"
                                             value="#{cc.attrs.taskBean.selectedProduct[property.name]}"
                                             rendered="#{cc.attrs.taskBean.isMultipleValues(property) and not (property.name == 'unitOfMeasurementType')}"
                                             required="#{property.required}"
                                             disabled="#{isDisabled}"
                                             filter="true"
                                             filterMatchMode="contains">
                                <f:attribute name="property" value="#{property}" />
                                <p:ajax event="change"
                                        listener="#{cc.attrs.taskBean.onValueChangeListener}"
                                        process="@this"
                                        immediate="true"
                                        ignoreAutoUpdate="true"
                                        update="#{cc.attrs.taskBean.getUpdate(property)}"
                                        disabled="#{not cc.attrs.taskBean.isListenable(property)}"/>
                                <f:selectItem itemLabel="#{labels.selection}"
                                              itemValue=""/>
                                <f:selectItems value="#{cc.attrs.taskBean.getObjectValues(property)}"
                                               var="v"
                                               itemLabel="#{cc.attrs.taskBean.labelObject(v)}"
                                               itemValue="#{cc.attrs.taskBean.valueObject(v)}"
                                               itemDescription="#{cc.attrs.taskBean.descriptionObject(v)}"/>
                            </p:selectOneMenu>
                        </p:dataGrid>
                        <h:panelGroup layout="block"
                                      style="clear: both;"/>
                    </h:panelGroup>
        			<p:separator style="width:100%;height:10px" styleClass="allowance-separator"/>
        				<h:outputText value="#{gnxxcblreflbls.pdt_allowaces_and_charges}" 
       	 					style="font-size:1.5em !important;float:left"/>
       	 			<p:remoteCommand name="acrc" update="@(.psProductTableAllowanceOrCharge) @(.psActionBtnPnl) @(.psAddAllowanceBtn)"
       	 				 process="@this" global="false"/>
                    <p:dataTable id="productTableAllowanceOrCharge"
                                 styleClass="psProductTableAllowanceOrCharge"
                                 var="objectAttribute"
                                 value="#{cc.attrs.taskBean.getAllowanceOrCharge(cc.attrs.taskBean.selectedProduct.objectAttribute)}"
                                 editable="true"
                                 style="clear: both;"
                                 emptyMessage="#{labels.no_records_found}"
                                 resizableColumns="true">

                        <p:ajax event="rowEditInit" listener="#{cc.attrs.taskBean.onRowInit}"
                                update="@(.psActionBtnPnl) @(.psAddAllowanceBtn)"/>
                        <p:ajax event="rowEdit" update="@(.psDlgMessages)"
                                listener="#{cc.attrs.taskBean.onRowEdit}" oncomplete="if(!args.validationFailed) {acrc()} removeMessages();"/>
                        <p:ajax event="rowEditCancel"
                                listener="#{cc.attrs.taskBean.onRowEditCancel}" oncomplete="acrc()"/>
                        <!-- Actions column-->
						<p:column styleClass="center actions-column" exportable="false">
						    <f:facet name="header">
						  	  <p:outputPanel styleClass="psActions">
					            <p:commandButton id="actions"
								                title="#{labels.actions}"
								                icon="fa fa-ellipsis-h"
								                type="button"
								                styleClass="globalActionsButton"/>            
					            <p:tieredMenu styleClass="xcbltask-tieredmenu global-actions"  overlay="true" trigger="actions" my="left top" at="left bottom">
									 <p:menuitem value="#{comlbls.add}"
	                                         actionListener="#{cc.attrs.taskBean.onAddAllowanceOrChargeAttribute()}"
	                                         process="@this"
	                                         update="@(.psProductTableAllowanceOrCharge) @(.psActionBtnPnl) @(.psAddAllowanceBtn)"
	                                         ignoreAutoUpdate="true"
	                                         rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'ADMIN')}"
	                                         disabled="#{cc.attrs.taskBean.editLine}"
	                                         onstart="jQuery('.global-actions').hide();"
	                                         oncomplete="editLastDatatableRow('productTableAllowanceOrCharge')"
	                                         styleClass="mls btn-gnx btn-gnx-referential psAddAllowanceBtn"/>
					             </p:tieredMenu>
						       </p:outputPanel>
					          </f:facet>
					           <p:rowEditor style="float: left;"
                                            rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'ADMIN')}"/>
                           	   <p:commandButton
                                    actionListener="#{cc.attrs.taskBean.onRemoveAttribute(cc.attrs.taskBean.selectedProduct,objectAttribute)}"
                                    immediate="true"
                                    ignoreAutoUpdate="true"
                                    process="@this"
                                    onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
                                    update="productTableAllowanceOrCharge @(.psDlgMessages) @(.psActionBtnPnl)"
                                    icon="ui-icon ui-icon-trash"
                                    rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'DELETE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'ADMIN')}"
                                    style="background: transparent;box-shadow: none;border-color: transparent;width: 16px;height: 16px;">
                            </p:commandButton>
				        </p:column> 
                        <ui:decorate template="objectAttribute.xhtml">
                            <ui:param name="listType"
                                      value="#{cc.attrs.taskBean.allowanceOrChargeListType}"/>
                            <ui:param name="listCode"
                                      value="#{cc.attrs.taskBean.getListCode(cc.attrs.taskBean.selectedProduct)}"/>
                        </ui:decorate>
                    </p:dataTable>
                </p:tab>
                
                <p:tab title="#{gnxxcblreflbls.pdt_tab_logistics}"
                       rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Logistics_Info', 'READ')}">
						<p:outputPanel styleClass="logistics-content">
							<p:outputPanel styleClass="row form-group" style="padding-right: 15px;">
							
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<!-- Packaging -->
										<h:outputLabel for="packaging" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.packaging}" />
										<p:outputPanel styleClass="col-sm-4">
											<p:selectOneMenu id="packaging" value="#{cc.attrs.taskBean.selectedProduct.packaging}"
															styleClass="form-control form-control-sm form-control-rounded w-100">
				                                <f:selectItem />
				                                <f:selectItems value="#{cc.attrs.taskBean.getPackaging()}"
				                                               var="packaging"
				                                               itemLabel="#{packaging.getLabel(gnxSessionHandler.locale)}"
				                                               itemValue="#{packaging.listValue}"/>
				                            </p:selectOneMenu>
										</p:outputPanel>
										<!-- Qty per pack -->
			                            <h:outputLabel for="qtyPerPpack" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.qty_per_pack}" />
										<p:outputPanel styleClass="col-sm-4">
											<pe:inputNumber id="qtyPerPpack" value="#{cc.attrs.taskBean.selectedProduct.qtyPerPack}"
		                                           styleClass="form-control form-control-sm form-control-rounded"
		                                           decimalPlaces="0">
	                                           <f:validateLongRange minimum="1" />
                                           </pe:inputNumber>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<!-- Indic Special -->
										<h:outputLabel for="specialIndicator" styleClass="col-sm-2" 
												value="#{gnxxcblreflbls.special_indicator}" />
										<p:outputPanel styleClass="col-sm-4">
				                            <p:selectOneMenu id="specialIndicator" value="#{cc.attrs.taskBean.selectedProduct.specialIndicator}"
				                            				styleClass="form-control form-control-sm form-control-rounded w-100">
				                                <f:selectItem />
				                                <f:selectItems value="#{cc.attrs.taskBean.getSpecialIndicators()}"
				                                               var="indic"
				                                               itemLabel="#{indic.getLabel(gnxSessionHandler.locale)}"
				                                               itemValue="#{indic.listValue}"/>
				                            </p:selectOneMenu>
										</p:outputPanel>
										<!-- Status -->
			                            <h:outputLabel for="productStatus" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.status}" />
										<p:outputPanel styleClass="col-sm-4">
											<p:selectOneMenu id="productStatus" value="#{cc.attrs.taskBean.selectedProduct.productStatus}" 
															styleClass="form-control form-control-sm form-control-rounded w-100">
				                                <f:selectItem />
				                                <f:selectItems value="#{cc.attrs.taskBean.getStatus()}"
				                                               var="prodStatus"
				                                               itemLabel="#{prodStatus.getLabel(gnxSessionHandler.locale)}"
				                                               itemValue="#{prodStatus.listValue}"/>
				                            </p:selectOneMenu>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<!-- Multiple of order -->
										<h:outputLabel for="multipleOfOrder" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.multiple_of_order}" />
										<p:outputPanel styleClass="col-sm-4">
											<pe:inputNumber id="multipleOfOrder" value="#{cc.attrs.taskBean.selectedProduct.multipleOfOrder}"
		                                           styleClass="form-control form-control-sm form-control-rounded"
		                                           decimalPlaces="0">
	                                           <f:validateLongRange minimum="1" />
	                                           <p:ajax event="change" process="@this" immediate="true" />
                                           </pe:inputNumber>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<!-- Min order -->
										<h:outputLabel for="minimumOrder" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.minimum_order}" />
										<p:outputPanel styleClass="col-sm-4 minOrder">
												<p:spinner id="minimumOrder" value="#{cc.attrs.taskBean.selectedProduct.minimumOrder}"
					                                           stepFactor="#{cc.attrs.taskBean.getStepFactor()}" styleClass="form-control-sm form-control-rounded"
					                                           label="#{gnxxcblreflbls.minimum_order}"> 
				                                    <p:ajax event="change" process="@this" global="false" async="false" ignoreAutoUpdate="true" />
				                                    <f:attribute name="multiple" value="#{cc.attrs.taskBean.selectedProduct.multipleOfOrder}" />
	                                       			<f:attribute name="maxOrder" value="#{cc.attrs.taskBean.selectedProduct.maximumOrder}" />
	                                       			<f:attribute name="validateMaximum" value="false" />
	                                       			<f:attribute name="validateMinimum" value="true" />
													<f:validator validatorId="productLogisticsOrderValidator" for="minimumOrder" />    
	                                       			<f:validateLongRange minimum="1" />
				                               </p:spinner>
										</p:outputPanel>
										<!-- Max order -->
			                            <h:outputLabel for="maximumOrder" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.maximum_order}" />
										<p:outputPanel styleClass="col-sm-4 maxOrder">
												<p:spinner id="maximumOrder" value="#{cc.attrs.taskBean.selectedProduct.maximumOrder}"
					                                           stepFactor="#{cc.attrs.taskBean.getStepFactor()}" styleClass="form-control-sm form-control-rounded"
					                                        label="#{gnxxcblreflbls.maximum_order}"> 
 			                                    <p:ajax event="change" process="@this" global="false" async="false" ignoreAutoUpdate="true" />
	                                       		<f:attribute name="multiple" value="#{cc.attrs.taskBean.selectedProduct.multipleOfOrder}" />
	                                       		<f:attribute name="minOrder" value="#{cc.attrs.taskBean.selectedProduct.minimumOrder}" />
	                                       		<f:attribute name="validateMinimum" value="false" />
                                       			<f:attribute name="validateMaximum" value="true" />
                                           		<f:validator validatorId="productLogisticsOrderValidator" for="maximumOrder" />  
                                 				<f:validateLongRange minimum="1" />  
				                               </p:spinner>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<!-- Weight -->
										<h:outputLabel for="weight" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.weight}" />
										<p:outputPanel styleClass="col-sm-4">
											<pe:inputNumber id="weight" value="#{cc.attrs.taskBean.selectedProduct.weight}"
		                                           styleClass="form-control form-control-sm form-control-rounded"
		                                           minValue="0" />
										</p:outputPanel>
										<!-- Volume -->
			                            <h:outputLabel for="volume" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.volume}" />
										<p:outputPanel styleClass="col-sm-4">
											<pe:inputNumber id="volume" value="#{cc.attrs.taskBean.selectedProduct.volume}"
	                                           styleClass="form-control form-control-sm form-control-rounded"
	                                           decimalPlaces="5"
	                                           minValue="0" />
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<!-- Units/package -->
										<h:outputLabel for="unitsPerPackage" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.units_per_package}" />
										<p:outputPanel styleClass="col-sm-4">
												<pe:inputNumber id="unitsPerPackage" value="#{cc.attrs.taskBean.selectedProduct.unitsPerPackage}"
		                                           styleClass="form-control form-control-sm form-control-rounded"
												   decimalPlaces="0">
											   		<f:validateLongRange minimum="1" />
											   </pe:inputNumber>
										</p:outputPanel>
										<!-- Units/pallet --> 
			                            <h:outputLabel for="unitsPerPallet" styleClass="col-sm-2"
												value="#{gnxxcblreflbls.units_per_pallet}" />
										<p:outputPanel styleClass="col-sm-4">
												<pe:inputNumber id="unitsPerPallet" value="#{cc.attrs.taskBean.selectedProduct.unitsPerPallet}"
		                                           styleClass="form-control form-control-sm form-control-rounded"
												   decimalPlaces="0">
											   		<f:validateLongRange minimum="1" />
											   </pe:inputNumber>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
							</p:outputPanel>
							
						</p:outputPanel>
				</p:tab>
                <p:tab title="#{gnxxcblreflbls.pdt_tab_media_info}">
                       <p:outputPanel styleClass="media-content">
							<p:outputPanel styleClass="row form-group" style="padding-right: 15px;">
							
								<!-- Preview -->
								<p:outputPanel styleClass="col-sm-12 previewPanel">
									<p:outputPanel styleClass="form-group row">
										<p:outputPanel styleClass="col-sm-12 previewContainer">
											<p:outputPanel styleClass="graphicImageContainer col-sm-12"
											               rendered="#{cc.attrs.taskBean.isImage(cc.attrs.taskBean.selectedProduct.thumbnailURL)}">
													<p:outputPanel styleClass="col-sm-12">
													<h:graphicImage value="#{cc.attrs.taskBean.getImageUrl(cc.attrs.taskBean.selectedProduct.thumbnailURL)}" style="height: 50px;"
																styleClass="img-thumbnailURL" onmousemove="zoomInCustom(event, 'fullSizeImageUrl')" onmouseout="zoomOut()">
                                               		</h:graphicImage>
                                               		</p:outputPanel>
                                               		<p:outputPanel styleClass="col-sm-12">
                                               			<h:outputText id="magnifyingGlass" styleClass="fa fa-search-plus" />
													</p:outputPanel>
											</p:outputPanel>
											<p:outputPanel styleClass="imageNotFoundContainer" rendered="#{not cc.attrs.taskBean.isImage(cc.attrs.taskBean.selectedProduct.thumbnailURL)}">
												<h:outputText id="notFoundImg" 
															styleClass="far fa-file-image fa-3x" />
											</p:outputPanel>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
							
								<!-- Thumbnail URL -->
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<h:outputLabel for="thumbnailURL" styleClass="col-sm-2"
											value="#{gnxxcblreflbls.thumbnail_url}" />
										<p:outputPanel styleClass="col-sm-10">
											<p:inputText id="thumbnailURL" styleClass="form-control form-control-sm form-control-rounded"
												value="#{cc.attrs.taskBean.selectedProduct.thumbnailURL}" validator="xssValidator">
												<p:ajax event="blur" process="@this @(.previewPanel)" update="@(.previewPanel)" 
														immediate="true" global="false">
												</p:ajax>
											</p:inputText>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
								
								<!-- Image URL -->
								<p:outputPanel styleClass="col-sm-12">
									<p:outputPanel styleClass="form-group row">
										<h:outputLabel for="imageUrl" styleClass="col-sm-2"
											value="#{gnxxcblreflbls.image_url}" />
										<p:outputPanel styleClass="col-sm-10">
											<p:inputText id="imageUrl" styleClass="form-control form-control-sm form-control-rounded fullSizeImageUrl"
												validator="xssValidator"
												value="#{cc.attrs.taskBean.selectedProduct.imageURL}">
												<p:ajax event="change" process="@this" update="@(.fullImageUrl)" />
											</p:inputText>
										</p:outputPanel>
									</p:outputPanel>
								</p:outputPanel>
							</p:outputPanel>
						</p:outputPanel>
                </p:tab>				
                <p:tab title="#{gnxxcblreflbls.pdt_product_tab_other}"
                       rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'READ') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'ADMIN')}">
                    <p:remoteCommand name="rc" update="@(.psProductTableOther) @(.psActionBtnPnl) @(.psAddOtherBtn)"
       	 				 process="@this" global="false"/>
                    <p:dataTable id="productTableOther"
                                 styleClass="psProductTableOther"
                                 style="clear: both;"
                                 var="objectAttribute"
                                 value="#{cc.attrs.taskBean.getOtherAttribute(cc.attrs.taskBean.selectedProduct.objectAttribute)}"
                                 editable="true"
                                 emptyMessage="#{labels.no_records_found}"
                                 resizableColumns="true">

                        <p:ajax event="rowEditInit" listener="#{cc.attrs.taskBean.onRowInit}"
                                update="@(.psActionBtnPnl) @(.psAddOtherBtn)"/>
                        <p:ajax event="rowEdit" update="@(.psDlgMessages)" oncomplete="if(!args.validationFailed) {rc()} removeMessages();"
                                listener="#{cc.attrs.taskBean.onRowEdit}"/>
                        <p:ajax event="rowEditCancel" oncomplete="rc()"
                                listener="#{cc.attrs.taskBean.onRowEditCancel}"/>
						<!-- Action column -->
						<p:column styleClass="center actions-column" exportable="false">
						    <f:facet name="header">
						  	  <p:outputPanel styleClass="psActions">
					            <p:commandButton id="actions"
								                title="#{labels.actions}"
								                icon="fa fa-ellipsis-h"
								                type="button"
								                styleClass="globalActionsButton"/>            
					            <p:tieredMenu styleClass="xcbltask-tieredmenu global-actions"  overlay="true" trigger="actions" my="left top" at="left bottom">
	                                <p:menuitem value="#{comlbls.add}"
                                         actionListener="#{cc.attrs.taskBean.onAddOtherAttribute()}"
                                         process="@this"
                                         update="@(.psProductTableOther)"
                                         ignoreAutoUpdate="true"
                                         rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'ADMIN')}"
                                         disabled="#{cc.attrs.taskBean.editLine}"
                                         onstart="jQuery('.global-actions').hide();"
                                         oncomplete="editLastDatatableRow('productTableOther')"
                                         styleClass="mls btn-gnx btn-gnx-referential psAddOtherBtn"/>         
					            </p:tieredMenu>
						      </p:outputPanel>
					        </f:facet>
					        <p:rowEditor style="float: left;"
                                         rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'ADMIN')}"/>
                            <p:commandButton
                                    actionListener="#{cc.attrs.taskBean.onRemoveAttribute(cc.attrs.taskBean.selectedProduct, objectAttribute)}"
                                    immediate="true"
                                    ignoreAutoUpdate="true"
                                    process="@this"
                                    onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
                                    update="productTableOther @(.psDlgMessages) @(.psActionBtnPnl)"
                                    icon="ui-icon ui-icon-trash"
                                    style="background: transparent;box-shadow: none;border-color: transparent;width: 16px;height: 16px;"
                                    rendered="#{gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'DELETE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'ADMIN')}">
                            </p:commandButton>
				        </p:column>	
                        <ui:decorate template="objectAttribute.xhtml">
                            <ui:param name="type"
                                      value="other"/>
                            <ui:param name="listType"
                                      value="#{cc.attrs.taskBean.listType}"/>
                        </ui:decorate>
                    </p:dataTable>
                </p:tab>
            </p:tabView>
            <p:outputPanel layout="block" id="actionsBtnPnl"
                          styleClass="psActionBtnPnl pull-left right validationButtons">
              <p:commandButton value="#{labels.cancel}"
                                 onclick="PF('wDlgProduct').hide();"
                                 class="btn btn-secondary"
                                 disabled="#{cc.attrs.taskBean.editLine}"
                                 process="@this">
                    <p:resetInput target="@form"/>
                </p:commandButton>
                <p:commandButton styleClass="btn-gnx btn-color-company btn btn-primary"
                                 value="#{comlbls.save}"
                                 onclick="if (#{(gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Net_Price', 'CREATE') == false)}) recopyGrossPriceIntoNetPrice()"
                                 actionListener="#{cc.attrs.taskBean.onRowEditProduct(cc.attrs.taskBean.selectedProduct)}"
                                 process="@(.psDlgProduct)"
                                 update="@(.psDlgMessages) @(.datatable-hide-filters) -taskMessages"
                                 ignoreAutoUpdate="true"
                                 oncomplete="args.validationFailed ? PF('wDlgProduct').show() : PF('wDlgProduct').hide()"
                                 disabled="#{cc.attrs.taskBean.editLine}"
                                 rendered="#{(gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Description', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_General_Description', 'ADMIN'))
										  or
								 			(gnxSessionHandler.isGranted('Portlet_Referentiel_Produits', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits', 'ADMIN'))
								 		  or
								 			(gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Remise_Charge', 'ADMIN'))
								 		  or
								 			(gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'CREATE') or gnxSessionHandler.isGranted('Portlet_Referentiel_Produits_Other', 'ADMIN'))}"/>

                <!-- Copy the value of Gross Price into Net Price -->
                <p:remoteCommand name="recopyGrossPriceIntoNetPrice"
                                 actionListener="#{cc.attrs.taskBean.getSelectedProduct().setCalculationNet(cc.attrs.taskBean.getSelectedProduct().getCalculationGross())}"
                                 process="@this"/>


            </p:outputPanel>
        </p:dialog>
    </cc:implementation>
</ui:component>