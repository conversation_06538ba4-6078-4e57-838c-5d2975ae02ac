package com.byzaneo.generix.repository.ui.converter;

import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static java.util.Optional.of;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.generix.service.repository.service.RepositoryService;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jun 30, 2017
 * @since AIO-6417
 */
@FacesConverter(ProductConverter.CONVERTER_ID)
public class ProductConverter implements Converter {

  public static final String CONVERTER_ID = "gnxProductConverter";

  private final RepositoryService service;

  public ProductConverter() {
    this.service = getSpringBean(RepositoryService.class, RepositoryService.SERVICE_NAME);
  }

  /**
   * @see javax.faces.convert.Converter#getAsObject(javax.faces.context.FacesContext, javax.faces.component.UIComponent, java.lang.String)
   */
  @Override
  public Object getAsObject(FacesContext context, UIComponent component, String value) {
    return service
        .searchById(Product.class, value)
        .orElse(null);
  }

  /**
   * @see javax.faces.convert.Converter#getAsString(javax.faces.context.FacesContext, javax.faces.component.UIComponent, java.lang.Object)
   */
  @Override
  public String getAsString(FacesContext context, UIComponent component, Object value) {
    if (value == null)
      return null;

    if (value instanceof String)
      return (String) value;

    if (value instanceof Product)
      return of(value)
          .map(Product.class::cast)
          .map(Product::getId)
          .orElse(null);

    return null;
  }
}
