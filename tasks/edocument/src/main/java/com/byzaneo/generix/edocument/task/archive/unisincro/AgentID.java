//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.01.30 at 04:50:00 PM EET 
//

package com.byzaneo.generix.edocument.task.archive.unisincro;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.*;

/**
 * <p>
 * Java class for Agent_ID complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Agent_ID">
 *   &lt;simpleContent>
 *     &lt;extension base="&lt;http://www.w3.org/2001/XMLSchema>string">
 *       &lt;attribute name="scheme" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *             &lt;enumeration value="TaxCode"/>
 *             &lt;enumeration value="VATRegistrationNumber"/>
 *             &lt;enumeration value="NationalHealthCareAuthority"/>
 *             &lt;enumeration value="OtherScheme"/>
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="otherScheme" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/extension>
 *   &lt;/simpleContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Agent_ID", namespace = "", propOrder = {
    "value"
})
public class AgentID {

  @XmlValue
  protected String value;
  @XmlAttribute(name = "scheme", namespace = "", required = true)
  @XmlJavaTypeAdapter(CollapsedStringAdapter.class)
  protected String scheme;
  @XmlAttribute(name = "otherScheme", namespace = "")
  protected String otherScheme;

  /**
   * Gets the value of the value property.
   * 
   * @return possible object is {@link String }
   */
  public String getValue() {
    return value;
  }

  /**
   * Sets the value of the value property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setValue(String value) {
    this.value = value;
  }

  /**
   * Gets the value of the scheme property.
   * 
   * @return possible object is {@link String }
   */
  public String getScheme() {
    return scheme;
  }

  /**
   * Sets the value of the scheme property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setScheme(String value) {
    this.scheme = value;
  }

  /**
   * Gets the value of the otherScheme property.
   * 
   * @return possible object is {@link String }
   */
  public String getOtherScheme() {
    return otherScheme;
  }

  /**
   * Sets the value of the otherScheme property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setOtherScheme(String value) {
    this.otherScheme = value;
  }

}
