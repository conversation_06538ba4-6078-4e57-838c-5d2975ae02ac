package com.byzaneo.generix.edocument.task.portal;

import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.ui.util.EmptyPersistentDataModel.emptyDataModel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.util.BeanDescriptorHelper.getPropertyValue;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.faces.model.query.QueryModelBuilder.createQueryModel;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.security.bean.Group.PROPERTY_VIEW_GROUP_MAP_TYPE;
import static com.byzaneo.security.bean.Group_.freeViewConfiguration;
import static java.util.Collections.sort;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.primefaces.model.SortOrder.ASCENDING;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;

import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.xml.bind.*;

import com.byzaneo.xtrade.xcbl.util.CustomLocaleMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.*;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.component.api.UIColumn;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.model.LazyDataModel;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.faces.event.CountEvent;
import com.byzaneo.faces.model.query.*;
import com.byzaneo.generix.audit.bean.*;
import com.byzaneo.generix.audit.service.AuditService;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.edocument.task.bean.*;
import com.byzaneo.generix.edocument.task.models.DematPartnerFileDataModel;
import com.byzaneo.generix.edocument.util.CsvExportHelper;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.ui.SessionHandler;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.operator.Operator;
import com.byzaneo.security.bean.Exchange;
import com.byzaneo.security.bean.Exchange.Direction;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.bean.Document;

@Task(name = "DematPartnerFile", library = "portal/edocument", version = "1.0.0")
@TaskViewModel(labelFamily = DematPartnerFileTask.EDCTSKLBLS)
public class DematPartnerFileTask extends AbstractTask {
  private static final Logger log = getLogger(DematPartnerFileTask.class);

  static final String EDCTSKLBLS = "edctsklbls";

  private static final long serialVersionUID = 1L;

  private static final int ASYNCHRONOUS_PROCESS_LIMIT = 250;

  private static final String DEFAULT_TEMPLATE_URI = "/_global_/BIRT/exchanges/exchanges";

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  @Qualifier(TransformService.SERVICE_NAME)
  private transient TransformService transformService;

  private transient LazyDataModel<Exchange> dataModel;

  private transient final String DEFAULT_QUERY = "id.kind = INVOIC";

  private static final Comparator<Revision> TIMESTAMP_COMPARATOR = Comparator.comparing(Revision::getTimestamp);

  private transient AuditService auditService;

  private transient Exchange exchange;

  private transient List<Revision> revisions;

  // mapping for the export
  private transient Map<String, String> exportMapping = null;

  /*
   * No transient property
   */
  private String templateUri = DEFAULT_TEMPLATE_URI;

  private FileType output;

  private String legalEntityLabel;

  private Boolean showLegalEntityLabel = Boolean.TRUE;

  private Boolean collapseLegalEntityPnl = Boolean.TRUE;

  public DematPartnerFileTask() {
    super();
    asynchronousListExportRange = new Range<>(0, 5000);
    listExportRange = new Range<>(0, 100000);
  }

  @PostConstruct
  public void init() {
    try {
      this.auditService = getBean(AuditService.class, AuditService.SERVICE_NAME);
    } catch (Exception e) {
      this.auditService = null;
    }

    if (descriptor == null) {
      this.query = QueryBuilder.createBuilder(DEFAULT_QUERY)
          .query();
    }
  }

  @Override
  public BeanDescriptor createDescriptor() {
    try {
      if (beanService == null) {
        beanService = getBean(BeanService.class, BeanService.SERVICE_NAME);
      }
      return beanService.fromClasspath(getPathDescriptor());
    }
    catch (IOException e) {
      log.error(e.getMessage());
      return null;
    }
  }

  public boolean isRendered(Object o, PropertyDescriptor col) {
    return true;
  }

  public String getValueStyleClass(Object obj, PropertyDescriptor property) {
    return "";
  }

  @Override
  public QueryModel getSearchModel() {
    return QueryModelBuilder.builder()
        .addBqlQueryReservedWord()
        .addBqlQueryFunctionName()
        .addQueryFieldBeanDescriptor(getSearch(), getSearchFieldPredicate(),
            null, null, getLocale(), getLocale(), false, false, false, null)
        .build();
  }

  public void onAdvancedSearchModel() {
    // Search in database
    this.selected = null;
    this.dataModel = null;
    try {
      DataTable dataTable = JSFHelper.resetDataTable("tskForm-dematPartnerFileView-resultTable");
      String query = this.userQuery != null ? this.userQuery.toString() : "";
      if (query.contains("ORDER BY")) {

        String[] queryList = query.split(" ");
        String sortColumn = queryList[queryList.length - 1];
        if (dataTable != null) {
          for (UIColumn column : dataTable.getColumns()) {
            if (sortColumn
                .equals(column.getField())) {
              dataTable.setSortColumn(column);
            }
          }
          dataTable.setSortOrder("ASCENDING");
        }
        // field is not recognized in order by
        userQuery = toQuery(query.substring(0, query.indexOf("ORDER BY")));

        this.dataModel = getDataModel();
      }
    }
    catch (Exception e) {
      log.error("failed to retreive data", e);
      this.dataModel = emptyDataModel();
    }
  }

  public LazyDataModel<Exchange> getDataModel() {
    try {
      return this.dataModel == null ? this.dataModel = resolveDataModel() : this.dataModel;
    }
    catch (Exception e) {
      error(e, "xtdtsklbls.error_data_model", e.getMessage());
      return null;
    }
  }

  public long onCountAdvancedSearch(CountEvent event) {
    QueryBuilder qb = createBuilder(resolveBaseQuery());
    qb.append(event.getBql());
    return this.securityService.countExchange(qb.query());
  }

  public List<Exchange> getExchangesForEdition(int start, int end) {
    List<Exchange> exchanges = new ArrayList<>();
    String sortField = ((DematPartnerFileDataModel) this.dataModel).getSortField();
    org.primefaces.model.SortOrder sortOrder = ((DematPartnerFileDataModel) this.dataModel).getSortOrder();
    PageRequest pageable = isNotBlank(sortField)
        ? PageRequest.of(start, end, ASCENDING.equals(sortOrder) ? ASC : DESC, sortField)
        : PageRequest.of(start, end);
    Page<Exchange> page = securityService.searchExchange(QueryBuilder.createBuilder(resolveSearchQuery())
        .query(false), pageable);

    exchanges.addAll(page.getContent());
    return exchanges;
  }

  public LazyDataModel<Exchange> resolveDataModel() {
    return new DematPartnerFileDataModel(securityService, resolveSearchQuery());
  }

  public List<Revision> getRevisions() {
    if (CollectionUtils.isEmpty(revisions)) {
      revisions = new ArrayList<>();
      if (exchange != null) {
        QueryBuilder qbExchange = createBuilder("_entity_id = " + auditService.generateExchangeMongoUuid(exchange.getId()))
            .desc("timestamp");
        revisions.addAll(auditService.getRevisionsByQuery(qbExchange.query()));

        QueryBuilder qbLegalEntity = createBuilder("_entity_id = " + exchange.getId()
            .getLegalEntity()
            .getId()).desc("timestamp");
        revisions.addAll(auditService.getRevisionsByQuery(qbLegalEntity.query()));

        QueryBuilder qbLegalEntityLocation = createBuilder("_entity_id = " + exchange.getId()
            .getLegalEntity()
            .getLocation()
            .getId()).desc("timestamp");
        revisions.addAll(auditService.getRevisionsByQuery(qbLegalEntityLocation.query()));

        QueryBuilder qbDematPartner = createBuilder("_entity_id = " + exchange.getId()
            .getDematPartner()
            .getId()).desc("timestamp");
        revisions.addAll(auditService.getRevisionsByQuery(qbDematPartner.query()));

        QueryBuilder qbDematPartnerLocation = createBuilder("_entity_id = " + exchange.getId()
            .getDematPartner()
            .getLocation()
            .getId()).desc("timestamp");
        revisions.addAll(auditService.getRevisionsByQuery(qbDematPartnerLocation.query()));

        sort(revisions, TIMESTAMP_COMPARATOR.reversed());
      }
    }
    revisions.forEach(rev -> {
      rev.setItems(rev.getItems()
          .stream()
          .filter(item -> !item.getNewValue()
              .equals(item.getOldValue()))
          .collect(Collectors.toList()));
      setRevisionEntityInfo(rev, exchange);
    });
    return revisions;
  }

  private String getExchangeKey(String propertyKey) {
    if (exportMapping == null) {
      initExportMapping();
    }
    return exportMapping.get(propertyKey);
  }

  private void initExportMapping() {
    this.exportMapping = new HashMap<>();
    exportMapping.put("id.dematPartner.fullname", "dematPartnerName");
    exportMapping.put("id.dematPartner.code", "dematPartnerCode");
    exportMapping.put("id.dematPartner.registration", "dematPartnerSiren");
    exportMapping.put("id.dematPartner.duns", "dematPartnerDuns");
    exportMapping.put("id.dematPartner.vat", "dematPartnerVATNumber");
    exportMapping.put("id.dematPartner.location.address.streetName", "dematPartnerAddress");
    exportMapping.put("id.dematPartner.location.address.postalCode", "dematPartnerPostalCode");
    exportMapping.put("id.dematPartner.location.address.city", "dematPartnerCity");
    exportMapping.put("id.dematPartner.location.address.country", "dematPartnerCountry");
    exportMapping.put("id.direction", "direction");
    exportMapping.put("id.kind", "messageType");
    exportMapping.put("start", "startDate");
    exportMapping.put("end", "endDate");
    exportMapping.put("id.dematPartner.creation", "dematPartnerCreationDate");
    exportMapping.put("id.legalEntity.creation", "legalEntityCreationDate");
    exportMapping.put("id.legalEntity.fullname", "legalEntityName");
    exportMapping.put("id.legalEntity.code", "legalEntityCode");
    exportMapping.put("id.legalEntity.registration", "legalEntitySiren");
    exportMapping.put("id.legalEntity.duns", "legalEntityDuns");
    exportMapping.put("id.legalEntity.vat", "legalEntityVATNumber");
    exportMapping.put("id.legalEntity.location.address.streetName", "legalEntityAddress");
    exportMapping.put("id.legalEntity.location.address.postalCode", "legalEntityPostalCode");
    exportMapping.put("id.legalEntity.location.address.city", "legalEntityCity");
    exportMapping.put("id.legalEntity.location.address.country", "legalEntityCountry");
  }

  private ExchangeEditionBean buildExchangeEdition(Exchange exchange) {
    ExchangeEditionBean exchangeEditionBean = new ExchangeEditionBean();
    if (exchange != null) {
      exchangeEditionBean.setDematPartnerName(exchange.getId()
          .getDematPartner()
          .getFullname());
      exchangeEditionBean.setDematPartnerCode(exchange.getId()
          .getDematPartner()
          .getCode());
      exchangeEditionBean.setDematPartnerSiren(exchange.getId()
          .getDematPartner()
          .getRegistration());
      exchangeEditionBean.setDematPartnerDuns(exchange.getId()
          .getDematPartner()
          .getDuns());
      exchangeEditionBean.setDematPartnerVATNumber(exchange.getId()
          .getDematPartner()
          .getVat());
      exchangeEditionBean.setDematPartnerAddress(exchange.getId()
          .getDematPartner()
          .getLocation()
          .getAddress()
          .getStreetName());
      exchangeEditionBean.setDematPartnerPostalCode(exchange.getId()
          .getDematPartner()
          .getLocation()
          .getAddress()
          .getPostalCode());
      exchangeEditionBean.setDematPartnerCity(exchange.getId()
          .getDematPartner()
          .getLocation()
          .getAddress()
          .getCity());
      exchangeEditionBean.setDematPartnerCountry(CustomLocaleMapper.getDisplayCountry(exchange.getId()
              .getDematPartner()
              .getLocation()
              .getAddress()
              .getCountry(), selectedLanguage));
      exchangeEditionBean.setDirection(getMessage(LABEL_CORE + "." + exchange.getId()
          .getDirection()
          .toString(), exchange.getId()
              .getDirection()
              .toString(),
          selectedLanguage));
      exchangeEditionBean.setMessageType(exchange.getId()
          .getKind());
      exchangeEditionBean.setStartDate(exchange.getStart());
      exchangeEditionBean.setEndDate(exchange.getEnd());
      exchangeEditionBean.setDematPartnerCreationDate(exchange.getId()
          .getDematPartner()
          .getCreation());
      exchangeEditionBean.setLegalEntityCreationDate(exchange.getId()
          .getLegalEntity()
          .getCreation());
      exchangeEditionBean.setLegalEntityName(exchange.getId()
          .getLegalEntity()
          .getFullname());
      exchangeEditionBean.setLegalEntityCode(exchange.getId()
          .getLegalEntity()
          .getCode());
      exchangeEditionBean.setLegalEntitySiren(exchange.getId()
          .getLegalEntity()
          .getRegistration());
      exchangeEditionBean.setLegalEntityDuns(exchange.getId()
          .getLegalEntity()
          .getDuns());
      exchangeEditionBean.setLegalEntityVATNumber(exchange.getId()
          .getLegalEntity()
          .getVat());
      exchangeEditionBean.setLegalEntityAddress(exchange.getId()
          .getLegalEntity()
          .getLocation()
          .getAddress()
          .getStreetName());
      exchangeEditionBean.setLegalEntityPostalCode(exchange.getId()
          .getLegalEntity()
          .getLocation()
          .getAddress()
          .getPostalCode());
      exchangeEditionBean.setLegalEntityCity(exchange.getId()
          .getLegalEntity()
          .getLocation()
          .getAddress()
          .getCity());
      exchangeEditionBean.setLegalEntityCountry(CustomLocaleMapper.getDisplayCountry(exchange.getId()
              .getLegalEntity()
              .getLocation()
              .getAddress()
              .getCountry(), selectedLanguage));
    }
    return exchangeEditionBean;
  }

  private File generateExchangesEditionXmlFile(List<Exchange> exchanges, String exchangeFileName) {
    List<ExchangeEditionBean> exchangeEditionList = new ArrayList<>();
    exchanges.forEach(exchange -> exchangeEditionList.add(buildExchangeEdition(exchange)));
    File exchangesEditionXmlFile = new File(FileUtils.getTempDirectory(), exchangeFileName + ".xml");
    try {
      JAXBContext jaxbContext = JAXBContext.newInstance(ExchangeEditionWrapper.class);
      Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
      jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
      jaxbMarshaller.marshal(new ExchangeEditionWrapper(exchangeEditionList), exchangesEditionXmlFile);
    }
    catch (JAXBException e) {
      log.error("Impossible de générer un fichier xml correspondant pour les échanges : " + e.getMessage());
    }
    return exchangesEditionXmlFile;
  }

  @Override
  public File downloadFile(FileType fileType, Instance instance) {
    int itemsCount = Long.valueOf(securityService.countExchange(QueryBuilder.createBuilder(resolveSearchQuery())
        .query(false)))
        .intValue();
    int asynchronousLimit = (int) getAsynchronousProcessLimit();
    if (itemsCount == 0) {
      RequestContext.getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      this.facesContext = FacesContext.getCurrentInstance();
      FacesMessage fm = MessageHelper.toFacesMessage("edctsklbls.ddl_no_file_warning_message", FacesMessage.SEVERITY_WARN);
      this.facesContext.addMessage(null, fm);
      return null;
    }

    String exchangeFileName = "exchange-file-" + UUID.randomUUID()
        .toString();
    String sortField = ((DematPartnerFileDataModel) this.dataModel).getSortField();
    org.springframework.data.domain.Sort.Direction sortOrder = ASCENDING.equals(((DematPartnerFileDataModel) this.dataModel).getSortOrder())
        ? ASC
        : DESC;
    return startDownloadProcess(fileType, instance, itemsCount, asynchronousLimit, exchangeFileName, sortField, sortOrder);
  }

  @Override
  protected File processingDownloadFile(FileType fileType, int count, String downloadFileName, BeanDescriptor desc, Query searchQuery,
      String sortField, org.springframework.data.domain.Sort.Direction sortOrder, Locale userLocale, String userDateFormat,
      Instance instance, boolean isCompanyUser, List<Document> documentsToUpdate) throws ServiceException {
    File exchangesFileToDownload = null;
    int limitFileSize = fileType == PDF ? pdfLimitFileSize : csvLimitFileSize;
    int totalFileNumber = (count % limitFileSize) == 0 ? (count / limitFileSize) : (count / limitFileSize) + 1;
    StringBuilder fileName = new StringBuilder(downloadFileName);
    for (int i = 0; i < totalFileNumber; i++) {
      fileName = fileName.delete(fileName.length() - 3, fileName.length() - 1)
          .append("_")
          .append(i);
      final int size = i < totalFileNumber ? limitFileSize : count - i * limitFileSize;
      List<Exchange> exchanges = getExchangesForEdition(i, size);
      switch (fileType) {
      case PDF:
        File exchangeXmlFile = generateExchangesEditionXmlFile(exchanges, fileName.toString());
        exchangesFileToDownload = createPdfExchangeFileFromTemplate(fileType, exchanges, exchangeXmlFile);
        try {
          Files.delete(exchangeXmlFile.toPath());
        }
        catch (IOException e1) {
          log.warn("Impossible to delete echange xml file" + exchangeXmlFile.getName());
        }
        break;
      case CSV:
        try {
          exchangesFileToDownload = createCsvExchangeFile(fileType, exchanges, fileName.toString());
        }
        catch (IOException e) {
          throw new ServiceException(e);
        }
        break;
      default:
        break;
      }
    }
    return totalFileNumber == 1 ? exchangesFileToDownload : zipDownloadFolder(TEMP_DIR + downloadFileName + ".zip", getDownloadDirectory());
  }

  private File createCsvExchangeFile(FileType fileType, List<Exchange> exchanges, String exchangeFileName)
      throws IOException, FileNotFoundException {
    File exchangesFileToDownload;
    exchangesFileToDownload = new File(getDownloadDirectory(),
        FilenameUtils.getBaseName(exchangeFileName) + fileType.getExtension());
    BeanDescriptor descriptor = getDescriptor().cloneSafely();
    List<PropertyDescriptor> properties = descriptor.getProperties()
        .stream()
        .map(p -> {
          p.setName(getExchangeKey(p.getName()));
          return p;
        })
        .collect(Collectors.toList());
    descriptor.setProperties(properties);
    List<Object> exchangeEditionList = new ArrayList<>();
    exchanges.forEach(exchange -> exchangeEditionList.add(buildExchangeEdition(exchange)));
    CsvExportHelper.exportAsCSV(exchangeEditionList, descriptor, new FileOutputStream(exchangesFileToDownload),
        selectedLanguage, null);
    return exchangesFileToDownload;
  }

  private File createPdfExchangeFileFromTemplate(FileType fileType, List<Exchange> exchanges, File exchangeXmlFile) {
    File exchangesFile = new File(getDownloadDirectory(),
        FilenameUtils.getBaseName(exchangeXmlFile.getName()) + fileType.getExtension());
    Template template = null;
    if (StringUtils.isNotEmpty(templateUri)) {
      template = this.transformService.getTemplate(templateUri);
      Map<String, Object> config = new HashMap<>();
      config.put("uri", exchangeXmlFile.toURI()
          .toString());
      try (final OutputStream os = new FileOutputStream(exchangesFile)) {
        this.transformService.transform(
            template,
            fileType,
            os,
            selectedLanguage,
            null, null, null,
            config);
      }
      catch (ServiceException se) {
        throw se;
      }
      catch (IOException e) {
        throw new ServiceException(e);
      }
    }
    return exchangesFile;
  }

  private void setRevisionEntityInfo(Revision revision, Exchange exchange) {
    if (revision.getEntityClass()
        .getName()
        .endsWith("Partner") ||
        revision.getEntityClass()
            .getName()
            .endsWith("Company")) {
      if (revision.getEntityId()
          .toString()
          .equals(exchange.getId()
              .getDematPartner()
              .getId())) {
        revision.setEntityInfo("demat_partner");
      }
      else {
        revision.setEntityInfo("legal_entity");
      }
    }
    else if (revision.getEntityClass()
        .getName()
        .endsWith("Location")) {
          if (revision.getEntityId()
              .toString()
              .equals(exchange.getId()
                  .getDematPartner()
                  .getLocation()
                  .getId())) {
            revision.setEntityInfo("demat_partner");
          }
          else {
            revision.setEntityInfo("legal_entity");
          }
        }
  }

  public void onReturn() {
    exchange = null;
    revisions = null;
  }

  @Override
  public void setBql(String bql) {
    if (bql != null && bql.contains("_text_")) {
      String val = bql.replace("\"_text_\" ~ ", "");
      List<String> matches = this.getDescriptor()
          .getProperties()
          .stream()
          .filter(p -> p.getType() == String.class)
          .map(p -> p.getName() + " ~ " + val)
          .collect(toList());
      bql = "";
      for (int i = 0; i < matches.size(); i++) {
        bql += matches.get(i);
        if (i < matches.size() - 1)
          bql += " OR ";
      }
    }
    super.setBql(bql);
  }

  @Override
  public Query resolveBaseQuery() {

    this.resolveOrganizationCodes();
    QueryBuilder qb = QueryBuilder.createBuilder()
        .and(Clauses.clause("id.dematPartner.fullname", Operator.NOT_LIKE, "Removed"))
        .and(Clauses.equal("id.legalEntity.code", partner != null ? partner.getCode() : company.getCode()));

    if (partner == null) {
      qb = QueryBuilder.createBuilder()
          .or(qb.query()
              .getWhereClause(), Clauses.equal("id.legalEntity.parent.code", company.getCode()));
    }

    if (this.query != null) {
      qb.append(this.query);
    }

    if (getSortBy() != null) {
      if (isSortAsc()) {
        qb.asc(getSortBy());
      }
      else {
        qb.desc(getSortBy());
      }
    }

    return qb.query();
  }

  public Serializable printOldValue(Revision revision, RevisionItem item) {
    // null...
    if ("null".equals(item.getOldValue()))
      return "";

    // group's free view configuration...
    if (freeViewConfiguration.getName()
        .equals(item.getProperty())) {
      return printFreeViewConfiguration(item.getOldValue());
    }

    // default...
    return item.getOldValue();
  }

  public Serializable printNewValue(Revision revision, RevisionItem item) {
    // null...
    if ("null".equals(item.getNewValue()))
      return "";

    // group's free view configuration...
    if (freeViewConfiguration.getName()
        .equals(item.getProperty())) {
      return printFreeViewConfiguration(item.getNewValue());
    }

    // default...
    return item.getNewValue();
  }

  private Serializable printFreeViewConfiguration(Serializable value) {
    StringBuilder builder = new StringBuilder();
    if (value != null && isNotBlank(value.toString()))
      getGson().<Map<String, BeanDescriptor>> fromJson(value.toString(), PROPERTY_VIEW_GROUP_MAP_TYPE)
          .entrySet()
          .forEach(e -> {
            StringBuilder props = new StringBuilder();
            e.getValue()
                .getProperties()
                .forEach(p -> {
                  if (p.getRendered()) {
                    props.append("<li style=\"font-style:")
                        .append(p.getReadonly() ? "italic" : "normal")
                        .append("\">")
                        .append(p.getLabel())
                        .append(p.getRequired() ? "*" : "")
                        .append(" (")
                        .append(p.getName()
                            .substring(4))
                        .append(")</li>");
                  }
                });
            if (props.length() > 0)
              builder.append("<b>")
                  .append(e.getKey())
                  .append("</b><ul style=\"padding:0\">")
                  .append(props)
                  .append("</ul>");
          });

    return builder.toString();
  }

  @Override
  protected BeanDescriptor createDescriptorStatus() {
    BeanDescriptor bean = new BeanDescriptor();
    return bean;
  }

  @Override
  public String getPathDescriptor() {
    this.pathDescriptor = "descriptors/dematPartnerFile-columns.xml";
    return this.pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  @Override
  public Class<?> getIndexableType() {
    return null;
  }

  @Override
  public void reset() {
    super.reset();
    this.dataModel = null;
  }

  public QueryModel getExchangeQueryModel(Locale locale, Locale defaultLocale) {
    return createQueryModel(getDescriptor(), null, null, null, locale, defaultLocale, true, true, "type");
  }

  public Exchange getExchange() {
    return exchange;
  }

  public void setExchange(Exchange exchange) {
    this.exchange = exchange;
  }

  public Map<Object, String> getValues(String label) {
    if ("direction".equals(label)) {
      List<Direction> list = Arrays.asList(Direction.values());
      return list.stream()
          .collect(toMap(o -> o,
              o -> getMessage(LABEL_CORE + "." + o.toString(), o.toString(), selectedLanguage)));
    }
    else if ("country".equals(label)) {
      Map<Locale, String> countries = I18NHelper.getCountryNames(selectedLanguage, true, true);
      Map<Object, String> results = new HashMap<>();
      countries.forEach((k, v) -> results.put(new Locale("", k.getCountry()), v));
      return results;
    }
    return Collections.emptyMap();
  }

  @Override
  public String getLabelStatus(Object bean, PropertyDescriptor descriptor) {
    if (descriptor.getType()
        .equals(Direction.class)) {
      Object value = getPropertyValue(bean, descriptor, "");
      return getMessage(LABEL_CORE + "." + value.toString(), value.toString(), selectedLanguage);
    }
    return super.getLabelStatus(bean, descriptor);
  }

  @Override
  public BeanDescriptor getSearch() {
    if (this.search == null) {
      this.search = super.getSearch();
      this.search.getProperties()
          .stream()
          .filter(pd -> pd.getType() == Date.class)
          .forEach(pd -> pd.setValue(new DatePeriod()));
    }
    return this.search;
  }

  @Override
  public List<PropertyDescriptor> getDescriptorSortable(Instance instance) {
    List<PropertyDescriptor> properties = getDescriptor().getProperties();
    List<PropertyDescriptor> result = properties.stream()
        .filter(p -> p.getRendered() && ("start".equals(p.getName()) || "end".equals(p.getName())))
        .collect(Collectors.toList());
    validate(result, "", instance);
    return result;
  }

  public String getTemplateUri() {
    return templateUri;
  }

  public void setTemplateUri(String templateUri) {
    this.templateUri = templateUri;
  }

  public FileType getOutput() {
    return output;
  }

  public void setOutput(FileType output) {
    this.output = output;
  }

  @Override
  public long getAsynchronousProcessLimit() {
    return ASYNCHRONOUS_PROCESS_LIMIT;
  }

  public List<Map<String, String>> getAdditionalClauses() {
    return null;
  }

  public String getLegalEntityLabel() {
    try {
      if (StringUtils.isEmpty(legalEntityLabel)) {
        SessionHandler handler = JSFHelper.getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
        LabelSet labelSet = I18NHelper.createLabelSet(EDCTSKLBLS, handler.getLocale()
            .getLanguage(), null, handler.getAvailableLocales());
        labelSet.addLabel(MessageHelper.getMessage(EDCTSKLBLS + ".legal_entity", "Legal entity", Locale.FRENCH), Locale.FRENCH);
        labelSet.addLabel(MessageHelper.getMessage(EDCTSKLBLS + ".legal_entity", "Legal entity", Locale.ENGLISH), Locale.ENGLISH);
        this.legalEntityLabel = I18NHelper.toJsonLabelSet(labelSet);
      }
      return legalEntityLabel;
    }
    catch (Exception e) {
      log.error("Failed to get legal entity label", e);
      return "";
    }
  }

  public void setLegalEntityLabel(String legalEntityLabel) {
    this.legalEntityLabel = legalEntityLabel;
  }

  public Boolean getShowLegalEntityLabel() {
    return showLegalEntityLabel;
  }

  public void setShowLegalEntityLabel(Boolean showLegalEntityLabel) {
    this.showLegalEntityLabel = showLegalEntityLabel;
  }

  public Boolean getCollapseLegalEntityPnl() {
    return collapseLegalEntityPnl;
  }

  public void setCollapseLegalEntityPnl(Boolean collapseLegalEntityPnl) {
    this.collapseLegalEntityPnl = collapseLegalEntityPnl;
  }
}
