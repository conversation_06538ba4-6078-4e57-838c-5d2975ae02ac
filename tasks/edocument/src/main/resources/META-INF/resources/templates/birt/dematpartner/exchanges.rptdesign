<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.6.0.v201606072122</property>
    <list-property name="propertyBindings">
        <structure>
            <property name="name">FILELIST</property>
            <property name="id">5</property>
            <expression name="value" type="javascript">params["uri"].value</expression>
        </structure>
    </list-property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">120</property>
    <parameters>
        <scalar-parameter name="uri" id="359">
            <property name="hidden">true</property>
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">C:\temp\file.xml</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.datatools.enablement.oda.xml" name="XML Source" id="5">
            <property name="FILELIST">C:\temp\file.xml</property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="Data Set" id="360">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">dematPartnerName</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerName</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerSiren</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerSiren</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerDuns</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerDuns</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerVat</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerVat</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerAddress</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerAddress</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerPostalCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerPostalCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerCity</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerCity</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerCountry</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">dematPartnerCountry</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityName</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityName</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityAddress</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityAddress</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityPostalCode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityPostalCode</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityCity</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityCity</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityCountry</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityCountry</text-property>
                </structure>
                <structure>
                    <property name="columnName">direction</property>
                    <property name="alias">direction</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">direction</text-property>
                </structure>
                <structure>
                    <property name="columnName">type</property>
                    <property name="alias">type</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">type</text-property>
                </structure>
                <structure>
                    <property name="columnName">startDate</property>
                    <property name="alias">startDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">startDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">endDate</property>
                    <property name="alias">endDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">endDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">dematPartnerCreationDate</property>
                    <property name="alias">dematPartnerCreationDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">dematPartnerCreationDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityCreationDate</property>
                    <property name="alias">legalEntityCreationDate</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">legalEntityCreationDate</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntitySiren</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntitySiren</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityDuns</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityDuns</text-property>
                </structure>
                <structure>
                    <property name="columnName">legalEntityVat</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">legalEntityVat</text-property>
                </structure>
                <structure>
                    <property name="columnName">messageType</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">messageType</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">dematPartnerName</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">dematPartnerCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">dematPartnerSiren</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">dematPartnerDuns</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">dematPartnerVat</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">dematPartnerAddress</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">dematPartnerPostalCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">dematPartnerCity</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">dematPartnerCountry</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">legalEntityName</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">legalEntityCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">legalEntityAddress</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">legalEntityPostalCode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">legalEntityCity</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">legalEntityCountry</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">direction</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">type</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">startDate</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">endDate</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">dematPartnerCreationDate</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">legalEntityCreationDate</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">legalEntitySiren</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">legalEntityDuns</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">legalEntityVat</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">messageType</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">XML Source</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">dematPartnerName</property>
                    <property name="nativeName">dematPartnerName</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">dematPartnerCode</property>
                    <property name="nativeName">dematPartnerCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">dematPartnerSiren</property>
                    <property name="nativeName">dematPartnerSiren</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">dematPartnerDuns</property>
                    <property name="nativeName">dematPartnerDuns</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">dematPartnerVat</property>
                    <property name="nativeName">dematPartnerVat</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">dematPartnerAddress</property>
                    <property name="nativeName">dematPartnerAddress</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">dematPartnerPostalCode</property>
                    <property name="nativeName">dematPartnerPostalCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">dematPartnerCity</property>
                    <property name="nativeName">dematPartnerCity</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">dematPartnerCountry</property>
                    <property name="nativeName">dematPartnerCountry</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">legalEntityName</property>
                    <property name="nativeName">legalEntityName</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">legalEntityCode</property>
                    <property name="nativeName">legalEntityCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">legalEntityAddress</property>
                    <property name="nativeName">legalEntityAddress</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">legalEntityPostalCode</property>
                    <property name="nativeName">legalEntityPostalCode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">legalEntityCity</property>
                    <property name="nativeName">legalEntityCity</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">legalEntityCountry</property>
                    <property name="nativeName">legalEntityCountry</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">direction</property>
                    <property name="nativeName">direction</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">type</property>
                    <property name="nativeName">type</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">startDate</property>
                    <property name="nativeName">startDate</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">endDate</property>
                    <property name="nativeName">endDate</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">dematPartnerCreationDate</property>
                    <property name="nativeName">dematPartnerCreationDate</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">legalEntityCreationDate</property>
                    <property name="nativeName">legalEntityCreationDate</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">legalEntitySiren</property>
                    <property name="nativeName">legalEntitySiren</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">legalEntityDuns</property>
                    <property name="nativeName">legalEntityDuns</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">legalEntityVat</property>
                    <property name="nativeName">legalEntityVat</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">messageType</property>
                    <property name="nativeName">messageType</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/exchangeWrapper/exchangeEdition/]#:#{dematPartnerName;STRING;dematPartnerName},{dematPartnerCode;STRING;dematPartnerCode},{dematPartnerSiren;STRING;dematPartnerSiren},{dematPartnerDuns;STRING;dematPartnerDuns},{dematPartnerVat;STRING;dematPartnerVat},{dematPartnerAddress;STRING;dematPartnerAddress},{dematPartnerPostalCode;STRING;dematPartnerPostalCode},{dematPartnerCity;STRING;dematPartnerCity},{dematPartnerCountry;STRING;dematPartnerCountry},{legalEntityName;STRING;legalEntityName},{legalEntityCode;STRING;legalEntityCode},{legalEntityAddress;STRING;legalEntityAddress},{legalEntityPostalCode;STRING;legalEntityPostalCode},{legalEntityCity;STRING;legalEntityCity},{legalEntityCountry;STRING;legalEntityCountry},{direction;STRING;direction},{type;STRING;type},{startDate;STRING;startDate},{endDate;STRING;endDate},{dematPartnerCreationDate;STRING;dematPartnerCreationDate},{legalEntityCreationDate;STRING;legalEntityCreationDate},{legalEntitySiren;STRING;legalEntitySiren},{legalEntityDuns;STRING;legalEntityDuns},{legalEntityVat;STRING;legalEntityVat},{messageType;STRING;messageType}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>name</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>name</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>code</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>code</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>siren</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>siren</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>duns</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>duns</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>vat</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>vat</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>address</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>address</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>postalCode</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>postalCode</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>city</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>city</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>country</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>country</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>direction</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>direction</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>type</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>type</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>startDate</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>startDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>endDate</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>endDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>dematPartnerCreationDate</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>dematPartnerCreationDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>legalEntityCreationDate</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>legalEntityCreationDate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
    </data-sets>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <property name="footerHeight">0.5in</property>
            <page-header>
                <text id="1224">
                    <property name="contentType">html</property>
                    <text-property name="content"><![CDATA[<value-of>new Date()</value-of>]]></text-property>
                </text>
            </page-header>
            <page-footer>
                <grid id="1213">
                    <property name="marginLeft">545pt</property>
                    <property name="marginRight">0pt</property>
                    <property name="textAlign">center</property>
                    <property name="width">80%</property>
                    <column id="1214"/>
                    <column id="1215"/>
                    <column id="1216"/>
                    <row id="1217">
                        <cell id="1218">
                            <auto-text id="1219">
                                <property name="textAlign">left</property>
                                <property name="type">page-number</property>
                            </auto-text>
                        </cell>
                        <cell id="1220">
                            <text id="1221">
                                <property name="contentType">plain</property>
                                <text-property name="content"><![CDATA[/]]></text-property>
                            </text>
                        </cell>
                        <cell id="1222">
                            <auto-text id="1223">
                                <property name="type">total-page</property>
                            </auto-text>
                        </cell>
                    </row>
                </grid>
            </page-footer>
        </simple-master-page>
    </page-setup>
    <body>
        <table id="868">
            <property name="width">7.941666666666666in</property>
            <property name="dataSet">Data Set</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">dematPartnerName</property>
                    <text-property name="displayName">dematPartnerName</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerName"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerCode</property>
                    <text-property name="displayName">dematPartnerCode</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerCode"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerSiren</property>
                    <text-property name="displayName">dematPartnerSiren</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerSiren"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerDuns</property>
                    <text-property name="displayName">dematPartnerDuns</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerDuns"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerVat</property>
                    <text-property name="displayName">dematPartnerVat</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerVat"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerAddress</property>
                    <text-property name="displayName">dematPartnerAddress</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerAddress"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerPostalCode</property>
                    <text-property name="displayName">dematPartnerPostalCode</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerPostalCode"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerCity</property>
                    <text-property name="displayName">dematPartnerCity</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerCity"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerCountry</property>
                    <text-property name="displayName">dematPartnerCountry</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerCountry"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityName</property>
                    <text-property name="displayName">legalEntityName</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityName"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityCode</property>
                    <text-property name="displayName">legalEntityCode</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityCode"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityAddress</property>
                    <text-property name="displayName">legalEntityAddress</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityAddress"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityPostalCode</property>
                    <text-property name="displayName">legalEntityPostalCode</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityPostalCode"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityCity</property>
                    <text-property name="displayName">legalEntityCity</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityCity"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityCountry</property>
                    <text-property name="displayName">legalEntityCountry</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityCountry"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">direction</property>
                    <text-property name="displayName">direction</text-property>
                    <expression name="expression" type="javascript">dataSetRow["direction"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">type</property>
                    <text-property name="displayName">type</text-property>
                    <expression name="expression" type="javascript">dataSetRow["type"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">startDate</property>
                    <text-property name="displayName">startDate</text-property>
                    <expression name="expression" type="javascript">dataSetRow["startDate"]</expression>
                    <property name="dataType">date-time</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">endDate</property>
                    <text-property name="displayName">endDate</text-property>
                    <expression name="expression" type="javascript">dataSetRow["endDate"]</expression>
                    <property name="dataType">date-time</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">dematPartnerCreationDate</property>
                    <text-property name="displayName">dematPartnerCreationDate</text-property>
                    <expression name="expression" type="javascript">dataSetRow["dematPartnerCreationDate"]</expression>
                    <property name="dataType">date-time</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">legalEntityCreationDate</property>
                    <text-property name="displayName">legalEntityCreationDate</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityCreationDate"]</expression>
                    <property name="dataType">date</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">legalEntitySiren</property>
                    <text-property name="displayName">legalEntitySiren</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntitySiren"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityDuns</property>
                    <text-property name="displayName">legalEntityDuns</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityDuns"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">legalEntityVat</property>
                    <text-property name="displayName">legalEntityVat</text-property>
                    <expression name="expression" type="javascript">dataSetRow["legalEntityVat"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">messageType</property>
                    <text-property name="displayName">messageType</text-property>
                    <expression name="expression" type="javascript">dataSetRow["messageType"]</expression>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <column id="875">
                <property name="width">7.941666666666666in</property>
            </column>
            <header>
                <row id="869">
                    <cell id="870">
                        <label id="1078"/>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="1010">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <property name="borderTopStyle">solid</property>
                    <property name="borderTopWidth">medium</property>
                    <cell id="1011">
                        <label id="952">
                            <property name="backgroundColor">#C0C0C0</property>
                            <property name="fontWeight">bold</property>
                            <property name="fontStyle">normal</property>
                            <property name="color">#008000</property>
                            <property name="textUnderline">underline</property>
                            <property name="borderBottomStyle">solid</property>
                            <property name="borderBottomWidth">thin</property>
                            <property name="borderLeftStyle">none</property>
                            <property name="borderRightStyle">none</property>
                            <property name="borderTopStyle">none</property>
                            <text-property name="text">Entité juridique :</text-property>
                        </label>
                        <grid id="1016">
                            <column id="1017"/>
                            <column id="1018"/>
                            <column id="1019"/>
                            <column id="1020"/>
                            <column id="1021"/>
                            <row id="1022">
                                <cell id="1023">
                                    <label id="1034">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Nom :</text-property>
                                    </label>
                                </cell>
                                <cell id="1024">
                                    <label id="1035">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Code :</text-property>
                                    </label>
                                </cell>
                                <cell id="1025">
                                    <label id="1036">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">SIREN :</text-property>
                                    </label>
                                </cell>
                                <cell id="1026">
                                    <label id="1037">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Référence :</text-property>
                                    </label>
                                </cell>
                                <cell id="1027">
                                    <label id="1038">
                                        <property name="fontSize">9pt</property>
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">TVA intracommunautaire :</text-property>
                                    </label>
                                </cell>
                            </row>
                            <row id="1028">
                                <cell id="1029">
                                    <data id="1039">
                                        <property name="resultSetColumn">legalEntityName</property>
                                    </data>
                                </cell>
                                <cell id="1030">
                                    <data id="1040">
                                        <property name="resultSetColumn">legalEntityCode</property>
                                    </data>
                                </cell>
                                <cell id="1031">
                                    <property name="verticalAlign">top</property>
                                    <data id="1073">
                                        <property name="resultSetColumn">legalEntitySiren</property>
                                    </data>
                                </cell>
                                <cell id="1032">
                                    <data id="1074">
                                        <property name="resultSetColumn">legalEntityDuns</property>
                                    </data>
                                </cell>
                                <cell id="1033">
                                    <data id="1075">
                                        <property name="resultSetColumn">legalEntityVat</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                        <label id="1043"/>
                        <grid id="1045">
                            <property name="width">7.9in</property>
                            <column id="1046">
                                <property name="width">1.7833333333333334in</property>
                            </column>
                            <column id="1047"/>
                            <row id="1048">
                                <cell id="1049">
                                    <label id="1050">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Adresse :</text-property>
                                    </label>
                                </cell>
                                <cell id="1051">
                                    <data id="1069">
                                        <property name="resultSetColumn">legalEntityAddress</property>
                                    </data>
                                </cell>
                            </row>
                            <row id="1053">
                                <cell id="1054">
                                    <label id="1055">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Code Postal :</text-property>
                                    </label>
                                </cell>
                                <cell id="1056">
                                    <data id="1071">
                                        <property name="resultSetColumn">legalEntityPostalCode</property>
                                    </data>
                                </cell>
                            </row>
                            <row id="1058">
                                <cell id="1059">
                                    <label id="1060">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Ville : </text-property>
                                    </label>
                                </cell>
                                <cell id="1061">
                                    <data id="1070">
                                        <property name="resultSetColumn">legalEntityCity</property>
                                    </data>
                                </cell>
                            </row>
                            <row id="1063">
                                <cell id="1064">
                                    <label id="1065">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Pays :</text-property>
                                    </label>
                                </cell>
                                <cell id="1066">
                                    <data id="1072">
                                        <property name="resultSetColumn">legalEntityCountry</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                        <label id="1068"/>
                        <grid id="1179">
                            <column id="1180"/>
                            <row id="1181">
                                <cell id="1182">
                                    <label id="1185">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Date de création : </text-property>
                                    </label>
                                </cell>
                            </row>
                            <row id="1183">
                                <cell id="1184">
                                    <data id="1186">
                                        <structure name="dateTimeFormat">
                                            <property name="category">Custom</property>
                                            <property name="pattern">dd/MM/y HH:mm:ss</property>
                                        </structure>
                                        <property name="resultSetColumn">legalEntityCreationDate</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                        <label id="1187"/>
                        <label id="949">
                            <property name="backgroundColor">#C0C0C0</property>
                            <property name="fontWeight">bold</property>
                            <property name="color">#008000</property>
                            <property name="textUnderline">underline</property>
                            <property name="borderBottomStyle">solid</property>
                            <property name="borderBottomWidth">thin</property>
                            <property name="borderRightStyle">none</property>
                            <property name="textAlign">left</property>
                            <text-property name="text">Partenaire de dématérialisation :</text-property>
                        </label>
                    </cell>
                </row>
                <row id="871">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="872">
                        <grid id="896">
                            <column id="897"/>
                            <column id="898"/>
                            <column id="899"/>
                            <column id="900"/>
                            <column id="901"/>
                            <row id="902">
                                <cell id="903">
                                    <label id="914">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Nom :</text-property>
                                    </label>
                                </cell>
                                <cell id="904">
                                    <label id="915">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Code :</text-property>
                                    </label>
                                </cell>
                                <cell id="905">
                                    <label id="916">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">SIREN :</text-property>
                                    </label>
                                </cell>
                                <cell id="906">
                                    <label id="917">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Référence :</text-property>
                                    </label>
                                </cell>
                                <cell id="907">
                                    <label id="918">
                                        <property name="fontSize">9pt</property>
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">TVA intracommunautaire</text-property>
                                    </label>
                                </cell>
                            </row>
                            <row id="908">
                                <cell id="909">
                                    <data id="919">
                                        <property name="resultSetColumn">dematPartnerName</property>
                                    </data>
                                </cell>
                                <cell id="910">
                                    <data id="920">
                                        <property name="resultSetColumn">dematPartnerCode</property>
                                    </data>
                                </cell>
                                <cell id="911">
                                    <data id="921">
                                        <property name="resultSetColumn">dematPartnerSiren</property>
                                    </data>
                                </cell>
                                <cell id="912">
                                    <data id="922">
                                        <property name="resultSetColumn">dematPartnerDuns</property>
                                    </data>
                                </cell>
                                <cell id="913">
                                    <data id="923">
                                        <property name="resultSetColumn">dematPartnerVat</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                    </cell>
                </row>
                <row id="1004">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="1005">
                        <label id="1006"/>
                    </cell>
                </row>
                <row id="924">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="925">
                        <grid id="926">
                            <property name="width">7.9in</property>
                            <column id="927">
                                <property name="width">1.7833333333333334in</property>
                            </column>
                            <column id="928"/>
                            <row id="929">
                                <cell id="930">
                                    <label id="941">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Adresse :</text-property>
                                    </label>
                                </cell>
                                <cell id="931">
                                    <data id="945">
                                        <property name="resultSetColumn">dematPartnerAddress</property>
                                    </data>
                                </cell>
                            </row>
                            <row id="932">
                                <cell id="933">
                                    <label id="942">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Code Postal :</text-property>
                                    </label>
                                </cell>
                                <cell id="934">
                                    <data id="946">
                                        <property name="resultSetColumn">dematPartnerPostalCode</property>
                                    </data>
                                </cell>
                            </row>
                            <row id="935">
                                <cell id="936">
                                    <label id="943">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Ville : </text-property>
                                    </label>
                                </cell>
                                <cell id="937">
                                    <data id="947">
                                        <property name="resultSetColumn">dematPartnerCity</property>
                                    </data>
                                </cell>
                            </row>
                            <row id="938">
                                <cell id="939">
                                    <label id="944">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Pays :</text-property>
                                    </label>
                                </cell>
                                <cell id="940">
                                    <data id="948">
                                        <property name="resultSetColumn">dematPartnerCountry</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                    </cell>
                </row>
                <row id="1007">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="1008">
                        <label id="1009"/>
                    </cell>
                </row>
                <row id="1167">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="1168">
                        <grid id="1169">
                            <column id="1170"/>
                            <row id="1171">
                                <cell id="1172">
                                    <label id="1175">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Date de création : </text-property>
                                    </label>
                                </cell>
                            </row>
                            <row id="1173">
                                <cell id="1174">
                                    <data id="1177">
                                        <structure name="dateTimeFormat">
                                            <property name="category">Custom</property>
                                            <property name="pattern">dd/MM/y HH:mm:ss</property>
                                        </structure>
                                        <property name="resultSetColumn">dematPartnerCreationDate</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                        <label id="1188"/>
                    </cell>
                </row>
                <row id="950">
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="951">
                        <label id="1134">
                            <property name="backgroundColor">#C0C0C0</property>
                            <property name="fontWeight">bold</property>
                            <property name="color">#008000</property>
                            <property name="textUnderline">underline</property>
                            <property name="borderBottomStyle">solid</property>
                            <property name="borderBottomWidth">thin</property>
                            <property name="borderRightStyle">none</property>
                            <property name="textAlign">left</property>
                            <text-property name="text">Échange :</text-property>
                        </label>
                    </cell>
                </row>
                <row id="953">
                    <property name="borderBottomStyle">solid</property>
                    <property name="borderBottomWidth">medium</property>
                    <property name="borderLeftStyle">solid</property>
                    <property name="borderLeftWidth">medium</property>
                    <property name="borderRightStyle">solid</property>
                    <property name="borderRightWidth">medium</property>
                    <cell id="954">
                        <grid id="1137">
                            <column id="1138"/>
                            <column id="1139"/>
                            <column id="1140"/>
                            <column id="1141"/>
                            <row id="1142">
                                <cell id="1143">
                                    <label id="1152">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Direction :</text-property>
                                    </label>
                                </cell>
                                <cell id="1144">
                                    <label id="1153">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Type :</text-property>
                                    </label>
                                </cell>
                                <cell id="1145">
                                    <label id="1154">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Début :</text-property>
                                    </label>
                                </cell>
                                <cell id="1146">
                                    <label id="1155">
                                        <property name="fontWeight">bold</property>
                                        <text-property name="text">Fin :</text-property>
                                    </label>
                                </cell>
                            </row>
                            <row id="1147">
                                <cell id="1148">
                                    <data id="1163">
                                        <property name="resultSetColumn">direction</property>
                                    </data>
                                </cell>
                                <cell id="1149">
                                    <data id="1194">
                                        <property name="resultSetColumn">messageType</property>
                                    </data>
                                </cell>
                                <cell id="1150">
                                    <data id="1165">
                                        <structure name="dateTimeFormat">
                                            <property name="category">Custom</property>
                                            <property name="pattern">dd/MM/y HH:mm:ss</property>
                                        </structure>
                                        <property name="resultSetColumn">startDate</property>
                                    </data>
                                </cell>
                                <cell id="1151">
                                    <data id="1164">
                                        <structure name="dateTimeFormat">
                                            <property name="category">Custom</property>
                                            <property name="pattern">dd/MM/y HH:mm:ss</property>
                                        </structure>
                                        <property name="resultSetColumn">endDate</property>
                                    </data>
                                </cell>
                            </row>
                        </grid>
                        <label id="1193"/>
                    </cell>
                </row>
                <row id="1135">
                    <cell id="1136"/>
                </row>
            </detail>
            <footer>
                <row id="873">
                    <cell id="874">
                        <label id="1201"/>
                    </cell>
                </row>
            </footer>
        </table>
        <data id="1041"/>
        <data id="1042"/>
    </body>
</report>
