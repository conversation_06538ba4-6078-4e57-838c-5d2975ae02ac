<?xml version="1.0" encoding="UTF-8"?>
<descriptor name="CarrefourAdvanceShipmentNotice" sortBy="creationDate" sortAsc="true" version="4">
	<properties>
		<property name="archiveUid" type="java.lang.String" rendered="true" render-restrict="None" readonly="true" editable="false" required="false" sortable="true">
			<label>archiveUid</label>
		</property>
		<property name="safeBox" type="java.lang.String" rendered="false" render-restrict="None" readonly="true" editable="false" required="false" sortable="true">
			<label>safeBox</label>
		</property>
		<property name="creationDate" type="java.util.Date" rendered="false" readonly="false" editable="true" required="false" sortable="true">
			<label>creationDate</label>
		</property>
		<property name="indexable.asnHeader.asnParty.buyerParty.partyID.ident" type="java.lang.String" rendered="true" render-restrict="Partners" readonly="false" editable="true" required="false" sortable="false">
			<label>buyerIdent</label>
		</property>
		<property name="indexable.asnHeader.asnParty.buyerParty.nameAddress.name1" type="java.lang.String" rendered="true" render-restrict="Partners" readonly="false" editable="true" required="false" sortable="false">
			<label>buyerName</label>
		</property>
		<property name="indexable.asnHeader.asnParty.sellerParty.partyID.ident" type="java.lang.String" rendered="true" render-restrict="Company" readonly="false" editable="true" required="false" sortable="false">
			<label>sellerIdent</label>
		</property>
		<property name="indexable.asnHeader.asnParty.sellerParty.nameAddress.name1" type="java.lang.String" rendered="true" render-restrict="Company" readonly="false" editable="true" required="false" sortable="false">
			<label>sellerName</label>
		</property>
		<property name="indexable.asnHeader.asnParty.shipToParty.partyID.ident" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>shipToIdent</label>
		</property>
		<property name="indexable.asnHeader.asnParty.shipToParty.nameAddress.name1" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>shipToName</label>
		</property>
		<property name="indexable.asnHeader.asnParty.shipFromParty.partyID.ident" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>shipFromIdent</label>
		</property>
		<property name="indexable.asnHeader.asnParty.shipFromParty.nameAddress.name1" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>shipFromName</label>
		</property>
		<property name="indexable.asnHeader.asnNumber" type="java.lang.String" rendered="true" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>asnNumber</label>
		</property>
		<property name="indexable.asnHeader.asnIssueDate" type="java.util.Date" rendered="true" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>issueDate</label>
		</property>
		<property name="indexable.asnHeader.asnOrderNumber[0].buyerOrderNumber" type="java.lang.String" rendered="true" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>buyerOrderNumber</label>
		</property>
		<property name="indexable.asnHeader.asnOrderNumber[0].sellerOrderNumber" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>sellerOrderNumber</label>
		</property>
		<property name="indexable.asnHeader.asnPurpose.asnPurposeCoded" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>purpose</label>
		</property>
		<property name="indexable.asnHeader.asnType.asnTypeCoded" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>asnType</label>
		</property>
		<property name="indexable.asnHeader.asnStatus.asnStatusCoded" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>asnStatus</label>
		</property>
		<property name="indexable.asnHeader.asnCurrency.asnCurrencyStatusCoded" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>currencyStatus</label>
		</property>
		<property name="indexable.asnHeader.asnLanguage.asnLanguageCoded" type="java.lang.String" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>language</label>
		</property>
		<property name="indexable.asnHeader.asnDates.deliveryDate" type="java.util.Date" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>deliveryDate</label>
		</property>
		<property name="indexable.asnHeader.asnDates.shipDate" type="java.util.Date" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>shipDate</label>
		</property>
		<property name="indexable.asnHeader.asnDates.earliestDeliveryDate" type="java.util.Date" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>earliestDeliveryDate</label>
		</property>
		<property name="indexable.asnHeader.asnDates.latestDeliveryDate" type="java.util.Date" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>latestDeliveryDate</label>
		</property>
		<property name="indexable.asnHeader.shippingReferences.otherShippingReferences.referenceCoded[0].primaryReference.refNum" type="java.lang.String" rendered="true" readonly="true" editable="false" required="false" sortable="true">
			<label>packageNumber</label>
		</property>
		<property name="indexable.asnHeader.shippingReferences.otherShippingReferences.referenceCoded[1].primaryReference.refNum" type="java.lang.String" rendered="true" readonly="true" editable="false" required="false" sortable="true">
			<label>trackingNumber</label>
		</property>
		<property name="indexable.asnSummary.transportPackagingTotals.totalTransport" type="java.lang.Integer" rendered="false" render-restrict="None" readonly="false" editable="true" required="false" sortable="false">
			<label>totalTransport</label>
		</property>
		<property name="status" type="com.byzaneo.xtrade.api.DocumentStatusEntityInterface" rendered="true" render-restrict="None" readonly="false" editable="true" required="false" sortable="true">
			<label>status</label>
			<options-expression>#{cc.attrs.taskBean.getValues('status')}</options-expression>
		</property>
		<property name="from" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false" sortable="true">
			<label>from</label>
		</property>
		<property name="to" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false" sortable="true">
			<label>to</label>
		</property>
		<property name="owners" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false" sortable="true">
			<label>owners</label>
		</property>
		<property name="type" type="java.lang.String" rendered="false" readonly="true" editable="false" required="false" sortable="true">
			<label>type</label>
		</property>
		<property name="creationDate" type="java.util.Date" rendered="false" readonly="false" editable="true" required="false" sortable="true">
			<label>creationDate</label>
		</property>
	</properties>
</descriptor>