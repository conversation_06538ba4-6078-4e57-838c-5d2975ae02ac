package com.byzaneo.generix.signature.task;

import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.*;

import java.io.File;
import java.util.*;
import java.util.stream.*;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.edocument.util.EDocumentErrorHelper;
import com.byzaneo.generix.signature.GnxSignatureHandlerPkcs11;
import com.byzaneo.generix.signature.service.SignatureServiceImpl;
import com.byzaneo.generix.signature.util.SignatureHandlerFactory;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentErrorService;

import org.activiti.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import eu.europa.esig.dss.model.DSSException;
import eu.europa.esig.dss.simplereport.SimpleReport;

@ExtendWith(MockitoExtension.class)
 public class SignatureVerificationTaskTest {
  private final String pdf_path = "src/test/resources/pdf_signed.pdf";

  private final String xml_path = "src/test/resources/xml_signed.xml";

  private final String pdf_cert_path = "src/test/resources/pdf_cert.jks";

  private final String xml_cert_path = "src/test/resources/xml_cert.jks";

  private final String bad_cert_path = "src/test/resources/bad_cert.jks";

  private final String bad_key_path = "src/test/resources/key1.cer";

  private final String pdf_key_path = "src/test/resources/key2.cer";

  private final String xml_key_path = "src/test/resources/key3.cer";

  @Mock
  public DelegateExecution execution;

  @InjectMocks
  private SignatureVerificationTask instancePDF;

  @InjectMocks
  private SignatureVerificationTask instanceXML;

  @Mock
  private DocumentErrorService documentErrorService;

  @BeforeEach
  public void before() {
    Mockito.reset(execution);
    instancePDF.setCertificateVerifyingTaskManager(new CertificateVerifyingTaskManager());
    instancePDF.setSignatureService(new SignatureServiceImpl());
    instancePDF.setDocumentFileFilter("type = 'PDF'");
    instancePDF.setUseEmbeddedCertificate(true);
    instanceXML.setCertificateVerifyingTaskManager(new CertificateVerifyingTaskManager());
    instanceXML.setSignatureService(new SignatureServiceImpl());
    instanceXML.setDocumentFileFilter("type = 'XML'");
    instanceXML.setUseEmbeddedCertificate(true);
    EDocumentErrorHelper.setDocumentErrorService(documentErrorService);
  }

  @Test
  public void verifyDocumentWithBadCertificate() throws Exception {
    Document pdf_doc = initDocument(pdf_path, "PDF");
    Document xml_doc = initDocument(xml_path, "XML");
    Document key = initDocument(bad_cert_path, "JKS");
    // Crypted password
    key.getFirstDocumentFile()
        .setActionName("vA79CS7TmCQ4ljGSXupC5A==");
    Set<Document> certificates = new HashSet<>();
    certificates.add(key);

    Assertions.assertFalse(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, certificates)));
    Assertions.assertFalse(hasValideSignatureBeenFind(instanceXML.verifyDocument(xml_doc, certificates)));
    Assertions.assertTrue(isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.NONE));
    Assertions.assertTrue(isEqualsStatusCode(xml_doc.getStatus(), DocumentStatus.NONE));
  }

  @Test
  public void verifyDocumentWithGoodCertificate() throws Exception {
    Document pdf_doc = initDocument(pdf_path, "PDF");
    Document xml_doc = initDocument(xml_path, "XML");
    Document pdf_key = initDocument(pdf_cert_path, "JKS");
    Document xml_key = initDocument(xml_cert_path, "JKS");
    // Crypted password
    pdf_key.getFirstDocumentFile()
        .setActionName("shjz+eGo7g5mErH8vUb3CA==");
    xml_key.getFirstDocumentFile()
        .setActionName("shjz+eGo7g5mErH8vUb3CA==");
    Set<Document> certificates = new HashSet<>();
    certificates.add(pdf_key);
    certificates.add(xml_key);

    Assertions.assertTrue(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, certificates)));
    Assertions.assertTrue(hasValideSignatureBeenFind(instanceXML.verifyDocument(xml_doc, certificates)));
    Assertions.assertTrue(isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.ACCEPTED));
    Assertions.assertTrue(isEqualsStatusCode(xml_doc.getStatus(), DocumentStatus.ACCEPTED));
  }

  @Test
  public void verifyDocumentWithBadEmbeddedCertificate() throws Exception {
    Document pdf_doc = initDocument(pdf_path, "PDF");
    Document xml_doc = initDocument(xml_path, "XML");

    Assertions.assertEquals(0, instancePDF.verifyDocumentWithEmbeddedCertificate(new ArrayList<Document>(), pdf_doc, new File(bad_key_path)));
    Assertions.assertEquals(0, instanceXML.verifyDocumentWithEmbeddedCertificate(new ArrayList<Document>(), xml_doc, new File(bad_key_path)));
    Assertions.assertTrue(isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.REFUSED));
    Assertions.assertTrue(isEqualsStatusCode(xml_doc.getStatus(), DocumentStatus.REFUSED));
  }

  @Test
  public void verifyDocumentWithGoodEmbeddedCertificate() throws Exception {
    Document pdf_doc = initDocument(pdf_path, "PDF");
    Document xml_doc = initDocument(xml_path, "XML");

    Assertions.assertEquals(1, instancePDF.verifyDocumentWithEmbeddedCertificate(new ArrayList<Document>(), pdf_doc, new File(pdf_key_path)));
    Assertions.assertEquals(1, instanceXML.verifyDocumentWithEmbeddedCertificate(new ArrayList<Document>(), xml_doc, new File(xml_key_path)));
    Assertions.assertTrue(isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.ACCEPTED));
    Assertions.assertTrue(isEqualsStatusCode(xml_doc.getStatus(), DocumentStatus.ACCEPTED));
  }

  public Document initDocument(String path, String type) {
    DocumentFile docfile = new DocumentFile();
    docfile.setFile(new File(path));
    Document doc = new Document();
    doc.addFile(docfile);
    doc.setType(type);

    switch (type) {
    case "PDF":
      docfile.setType(FileType.PDF);
      break;
    case "PUB":
    case "JKS":
      docfile.setType(FileType.CER);
      break;
    case "XML":
      docfile.setType(FileType.XML);
      break;
    }

    return doc;
  }

  @Test
  public void verifyDocumentForCertificateTest() {
    File certificate = new File("src/test/resources", "e_78621_signed-all.cer");
    Document xml_doc = initDocument("src/test/resources/e_78621_signed.xml", "XML");

    Assertions.assertTrue(hasValideSignatureBeenFind(instanceXML.verifyDocumentForCertificate(xml_doc, certificate)));
  }

  @Test
  public void verifyDocumentWithRightKeyAndMultipleCertificats() throws Exception {
    Document pdf_doc = initDocument(pdf_path, "PDF");
    Document wrongKey = initDocument(bad_key_path, "PUB");
    Document rightKey = initDocument(pdf_key_path, "PUB");

    Set<Document> partnerCertificates = new HashSet<>();
    partnerCertificates.add(wrongKey);

    Set<Document> clientCertificates = new HashSet<>();
    clientCertificates.add(rightKey);

    Assertions.assertFalse(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, partnerCertificates)));
    Assertions.assertTrue(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, clientCertificates)));

    assert (isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.ACCEPTED));

    pdf_doc = initDocument(pdf_path, "PDF");
    partnerCertificates = new HashSet<>();
    partnerCertificates.add(rightKey);

    clientCertificates = new HashSet<>();
    clientCertificates.add(wrongKey);

    Assertions.assertTrue(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, partnerCertificates)));
    Assertions.assertFalse(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, clientCertificates)));
    assert (isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.ACCEPTED));

    pdf_doc = initDocument(pdf_path, "PDF");
    partnerCertificates = new HashSet<>();
    partnerCertificates.add(wrongKey);

    clientCertificates = new HashSet<>();
    clientCertificates.add(rightKey);
    clientCertificates.add(pdf_doc);
    clientCertificates.add(wrongKey);

    Assertions.assertFalse(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, partnerCertificates)));
    Assertions.assertTrue(hasValideSignatureBeenFind(instancePDF.verifyDocument(pdf_doc, clientCertificates)));
    assert (isEqualsStatusCode(pdf_doc.getStatus(), DocumentStatus.ACCEPTED));

  }

  /**
   * Jira 7696 On va déclencher une DSSException, ce qui va avoir pour effet de mettre les documents incorrect en dead et ne plus les
   * perdres à tout jamais
   */
  @SuppressWarnings("unchecked")
  @Test
  public void shouldCatchDSSExceptionAndReturnNull() throws Exception {
    CertificateVerifyingTaskManager certificateVerifyingTaskManagerMock = mock(CertificateVerifyingTaskManager.class);

    // La vérification va renvoyer null : c'est la valeur attendue si le document est incorrect
    SignatureVerificationTask signatureVerificationTaskMock = spy(SignatureVerificationTask.class);
    lenient().when(signatureVerificationTaskMock.verifyDocumentForCertificate(any(), any())).thenReturn(null);

    // Génération d'une d'exception pour se mettre dans les conditions de la jira
    GnxSignatureHandlerPkcs11 gnxSignatureHandlerPkcs11Mock = mock(GnxSignatureHandlerPkcs11.class);
    lenient().when(gnxSignatureHandlerPkcs11Mock.getTrustedCertificate()).thenThrow(DSSException.class);

    SignatureHandlerFactory signatureHandlerFactory = mock(SignatureHandlerFactory.class);
    lenient().when(signatureHandlerFactory.getSignatureHandler(any(), anyBoolean())).thenReturn(gnxSignatureHandlerPkcs11Mock);

    signatureVerificationTaskMock.setCertificateVerifyingTaskManager(certificateVerifyingTaskManagerMock);

    assertNull(signatureVerificationTaskMock.verifyDocument(new Document(),
        Stream.of(new Document())
            .collect(Collectors.toSet())));

    // 2 documents que l'on va vérifier : ils sont incorrects, on s'attends à les voir passer en deads
    Set<Document> docs = Stream.of(new Document(), new Document())
        .collect(Collectors.toSet());

    signatureVerificationTaskMock.verifyDocuments(
        docs,
        Stream.of(new Document())
            .collect(Collectors.toSet()),
        Stream.of(new Document())
            .collect(Collectors.toSet()),
        execution);

    verify(signatureVerificationTaskMock, times(2)).addDeads(any(), any());
  }

  private boolean hasValideSignatureBeenFind(SimpleReport simpleReport) {
    return simpleReport != null && simpleReport.getValidSignaturesCount() > 0;
  }

}
