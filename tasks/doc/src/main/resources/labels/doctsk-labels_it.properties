docchart_chartheight = Altezza
docchart_recnotnull = Destinatario non null
docchart_title_layout = Layout
docchart_tos = A
docchart_stepamount = Quantit\u00E0 step
docchart_lp_ne = Nord Est
docchart_lp_sw = Sud Ovest
docchart_step = Step
docchart_status = Stato
docchart_froms = Da
docchart_ownername = Proprietario
info_renaming_doc = Documento rinominato
warn_removing_locked_doc = Rimozione documento bloccato
docchart_lp_se = Sud Est
docchart_title_type = Tipo
docchart_lp_s = Sud
docchart_lp_w = Ovest
docchart_chartwidth = Larghezza
docchart_title_filters = Filtri
error_removing_doc = Errore durante la rimozione del documento
docchart_stepiteration = Iterazione step
docchart_title_period = Periodo
docchart_lp_e = Est
docchart_countfield = Campo conteggio
docchart_doctype = Tipo di documento
docchart_docfield = Campo del documento
docchart_chart = Tipo di grafico
docchart_tos_lower = a
info_removing_doc = Documento rimosso
error_renaming_disabled = Errore rinomina disattivata
docchart_legendposition = Posizione leggenda
docchart_lp_nw = Nord Ovest
docchart_lp_n = Nord

#Document counter
configuration = Configurazione
portlet_open_click = Portlet per aprire
legende = Leggenda
color = Colore
query = Ricerca
filter1 = Ricerca 1
filter2 = Ricerca 2
docPercentage = di tutti i documenti
message_no_data = Il grafico non contiene dati da visualizzare
month = Mese
week = Settimana
percentage_legend_default = di tutti i documenti
date_range_week = Settimana
date_range_year_tooltip = Se la casella non \u00E8 selezionata, il contatore registra i dati dal 1\u00B0 gennaio.\nSe la casella \u00E8 selezionata, il contatore registra i dati degli ultimi 365 giorni.
last_update = Ultimo aggiornamento
date_range_with_values = Da {0} a {1}
date_range_use = Autorizzare l'uso dell'intervallo di date
date_range_sliding = Periodo continuo
date_range = Intervallo di date
date_range_month_tooltip = Se la casella di controllo non \u00E8 selezionata, il contatore registra i dati dal 1\u00B0 del mese.\nSe la casella \u00E8 selezionata, il contatore registra i dati degli ultimi 30 giorni.
date_range_week_tooltip = Se la casella di controllo non \u00E8 selezionata, il contatore registra i dati da luned\u00EC.\nSe la casella \u00E8 selezionata, il contatore registra i dati degli ultimi 7 giorni.
date_range_introduction = \u00C8 possibile regolare la visualizzazione di questo contatore definendo un intervallo di date sul pulsante {0}.
date_range_year = Anno
date_range_month = Mese