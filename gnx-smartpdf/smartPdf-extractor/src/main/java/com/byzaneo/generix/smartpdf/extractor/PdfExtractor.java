/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.byzaneo.generix.smartpdf.extractor;

import java.awt.*;
import java.awt.geom.*;
import java.io.*;
import java.text.*;
import java.util.*;
import java.util.List;
import java.util.Map.Entry;
import java.util.regex.*;

import org.apache.commons.lang3.StringUtils;
import org.apache.fontbox.util.BoundingBox;
import org.apache.pdfbox.pdmodel.*;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.pdmodel.font.*;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDOutlineItem;
import org.apache.pdfbox.pdmodel.interactive.pagenavigation.PDThreadBead;
import org.apache.pdfbox.text.*;
import org.apache.pdfbox.util.IterativeMergeSort;
import org.slf4j.*;
import org.w3c.dom.Document;

import com.byzaneo.generix.smartpdf.DatabaseHelper;
import com.byzaneo.generix.smartpdf.extractor.exceptions.TemplateDoesNotHandleMultiPageDocumentsException;
import com.byzaneo.generix.smartpdf.helper.*;
import com.byzaneo.generix.smartpdf.model.*;
import com.byzaneo.generix.smartpdf.pdfbox.PdfBoxUtil;
import com.byzaneo.generix.smartpdf.pdfbox.model.*;

//TODO find a solution for that lib
// import com.sun.org.apache.xml.internal.serialize.XMLSerializer;

/**
 * This class will take a pdf document and strip out all of the text and ignore the formatting and such. Please note; it is up to clients of
 * this class to verify that a specific user has the correct permissions to extract text from the PDF document. The basic flow of this
 * process is that we get a document and use a series of processXXX() functions that work on smaller and smaller chunks of the page.
 * Eventually, we fully process each page and then print it.
 *
 * <AUTHOR> Litchfield
 */
public class PdfExtractor extends LegacyPDFStreamEngine {

  /**
   * wrapper of TextPosition that adds flags to track status as line start and paragraph start positions.
   * <p>
   * This is implemented as a wrapper since the TextPosition class doesn't provide complete access to its state fields to subclasses. Also,
   * conceptually TextPosition is immutable while these flags need to be set post-creation so it makes sense to put these flags in this
   * separate class.
   * </p>
   *
   * <AUTHOR>
   */
  private static final class PositionWrapper {

    private boolean isLineStart = false;

    private boolean isParagraphStart = false;

    private boolean isHangingIndent = false;

    private boolean isArticleStart = false;

    private TextPosition position = null;

    /**
     * Constructs a PositionWrapper around the specified TextPosition object.
     *
     * @param position the text position.
     */
    PositionWrapper(TextPosition position) {
      this.position = position;
    }

    /**
     * Returns the underlying TextPosition object.
     *
     * @return the text position
     */
    public TextPosition getTextPosition() {
      return position;
    }

    public boolean isArticleStart() {
      return isArticleStart;
    }

    public boolean isHangingIndent() {
      return isHangingIndent;
    }

    public boolean isLineStart() {
      return isLineStart;
    }

    public boolean isParagraphStart() {
      return isParagraphStart;
    }

    /**
     * Sets the isArticleStart() flag to true.
     */
    public void setArticleStart() {
      isArticleStart = true;
    }

    /**
     * Sets the isHangingIndent() flag to true.
     */
    public void setHangingIndent() {
      isHangingIndent = true;
    }

    /**
     * Sets the isLineStart() flag to true.
     */
    public void setLineStart() {
      isLineStart = true;
    }

    /**
     * sets the isParagraphStart() flag to true.
     */
    public void setParagraphStart() {
      isParagraphStart = true;
    }
  }

  /**
   * Internal class that maps strings to lists of {@link TextPosition} arrays. Note that the number of entries in that list may differ from
   * the number of characters in the string due to normalization.
   *
   * <AUTHOR> Dörfler
   */
  private static final class WordWithTextPositions {
    String text;

    List<TextPosition> textPositions;

    WordWithTextPositions(String word, List<TextPosition> positions) {
      text = word;
      textPositions = positions;
    }

    public String getText() {
      return text;
    }

    public List<TextPosition> getTextPositions() {
      return textPositions;
    }
  }

  private final static String VERSION = "2018-03-20-001";

  private static float defaultIndentThreshold = 2.0f;

  private static float defaultDropThreshold = 2.5f;

  private static final boolean useCustomQuickSort;

  // enable the ability to set the default indent/drop thresholds
  // with -D system properties:
  // pdftextstripper.indent
  // pdftextstripper.drop
  static {
    String strDrop = null, strIndent = null;
    try {
      String className = PdfExtractor.class.getSimpleName()
          .toLowerCase();
      String prop = className + ".indent";
      strIndent = System.getProperty(prop);
      prop = className + ".drop";
      strDrop = System.getProperty(prop);
    }
    catch (SecurityException e) {
      // PDFBOX-1946 when run in an applet
      // ignore and use default
    }
    if (strIndent != null && strIndent.length() > 0) {
      try {
        defaultIndentThreshold = Float.parseFloat(strIndent);
      }
      catch (NumberFormatException nfe) {
        // ignore and use default
      }
    }
    if (strDrop != null && strDrop.length() > 0) {
      try {
        defaultDropThreshold = Float.parseFloat(strDrop);
      }
      catch (NumberFormatException nfe) {
        // ignore and use default
      }
    }
  }
  static {
    // check if we need to use the custom quicksort algorithm as a
    // workaround to the PDFBOX-1512 transitivity issue of
    // TextPositionComparator:
    boolean is16orLess = false;
    try {
      String version = System.getProperty("java.specification.version");
      StringTokenizer st = new StringTokenizer(version, ".");
      int majorVersion = Integer.parseInt(st.nextToken());
      int minorVersion = 0;
      if (st.hasMoreTokens()) {
        minorVersion = Integer.parseInt(st.nextToken());
      }
      is16orLess = majorVersion == 1 && minorVersion <= 6;
    }
    catch (SecurityException x) {
      // when run in an applet ignore and use default
      // assume 1.7 or higher so that quicksort is used
    }
    catch (NumberFormatException nfe) {
      // should never happen, but if it does,
      // assume 1.7 or higher so that quicksort is used
    }
    useCustomQuickSort = !is16orLess;
  }

  private static final Logger log = LoggerFactory.getLogger(PdfExtractor.class);

  private static final float END_OF_LAST_TEXT_X_RESET_VALUE = -1;

  private static final float MAX_Y_FOR_LINE_RESET_VALUE = -Float.MAX_VALUE;

  private static final float EXPECTED_START_OF_NEXT_WORD_X_RESET_VALUE = -Float.MAX_VALUE;

  private static final float MAX_HEIGHT_FOR_LINE_RESET_VALUE = -1;

  private static final float MIN_Y_TOP_FOR_LINE_RESET_VALUE = Float.MAX_VALUE;

  private static final float LAST_WORD_SPACING_RESET_VALUE = -1;

  /**
   * a list of regular expressions that match commonly used list item formats, i.e. bullets, numbers, letters, Roman numerals, etc. Not
   * meant to be comprehensive.
   */
  private static final String[] LIST_ITEM_EXPRESSIONS = { "\\.", "\\d+\\.", "\\[\\d+\\]",
      "\\d+\\)", "[A-Z]\\.", "[a-z]\\.", "[A-Z]\\)", "[a-z]\\)", "[IVXL]+\\.",
      "[ivxl]+\\.", };

  private static Map<Character, Character> MIRRORING_CHAR_MAP = new HashMap<Character, Character>();

  static {

    String path = "org/apache/pdfbox/resources/text/BidiMirroring.txt";

    InputStream input = PdfExtractor.class.getClassLoader()
        .getResourceAsStream(path);

    try {

      parseBidiFile(input);

    }
    catch (IOException e) {

      log.warn("Could not parse BidiMirroring.txt, mirroring char map will be empty: " + e.getMessage());

    }
    finally {

      try {

        input.close();

      }
      catch (IOException e) {

        log.error("Could not close BidiMirroring.txt ", e);
      }
    }
  }

  private static final Set<Character> BLANK_CHARS = new HashSet<Character>(
      Arrays.asList(new Character[] { ' ' }));

  // private final static String TRANSCODING_FILENAME = "interchange.ini";

  public final static PdfExtractor getInstance() {

    if (log.isInfoEnabled()) {

      log.info("Version de PdfExtractor : {}", VERSION);

      Locale currentLocale = Locale.getDefault();

      log.info(
          "[displayLanguage={}][displayCountry()={}][language={}]" + "[country={}][user.country={}][user.language={}]",
          currentLocale.getDisplayLanguage(), currentLocale.getDisplayCountry(),
          currentLocale.getLanguage(), currentLocale.getCountry(),
          System.getProperty("user.country"), System.getProperty("user.language"));
    }

    try {

      return new PdfExtractor();

    }
    catch (IOException e) {

      log.error("Error on create instance of PdfExtractor : " + e.getMessage(), e);
      return null;
    }
  }

  private static List<String> getKeysInLine(Set<String> keys, LineItem lineItem) {

    if (keys == null || keys.isEmpty() || lineItem == null) {
      return null;
    }

    List<String> result = new ArrayList<String>();

    for (String key : keys) {

      if (lineItem.isTextContainsIgnoreCase(key) || lineItem.isCondensedTextContainsIgnoreCase(key)) {

        result.add(key);
      }
    }

    return result;
  }

  public static void main(String[] args) {

    if (args.length != 2) {

      usage();
      return;
    }

    if (log.isTraceEnabled()) {

      log.trace("Arguments : ");
      for (int i = 0; i < args.length; i++) {

        log.trace("\t" + args[i]);
      }
    }
    /*
     * GenericHelper genericHelper = new GenericHelper(); Properties properties = genericHelper.getProperties();
     *
     * if (properties == null) { log.error("Erreur à la lecture du fichier de propriétées"); return; }
     *
     * DatabaseHelper databaseHelper = new DatabaseHelper();
     *
     * DataSource dataSource = databaseHelper.getDataSource(properties);
     *
     * if (dataSource == null) { log.error("Erreur de connection à la base de données"); return; }
     *
     * TemplateHelper templateHelper = new TemplateHelper(); templateHelper.setDataSource(dataSource);
     *
     * MappingPDF template = templateHelper.getTemplateByName(args[0]);
     *
     * if (template == null) {
     *
     * log.error("Aucun modèle trouvé avec le nom [" + args[0] + "]"); return; }
     *
     * log.debug("Utilisation du modèle [id=" + template.getIdmapping() + "][nom=" + template.getNom() + "]");
     *
     * File file = new File(args[1]);
     *
     * if (!file.isFile()) {
     *
     * log.error("Le fichier [" + args[1] + "] n'existe pas ou n'est pas un fichier"); return; }
     *
     * log.debug("Extraction des données du fichier [" + args[1] + "]...");
     *
     * // testTemplateHelper(dataSource);
     *
     */

    PdfExtractor pdfExtractor = PdfExtractor.getInstance();

    if (args[0].indexOf('/') > -1 || args[0].indexOf('\\') > -1) {

      // Le premier argument contient probablement un chemin de fichier
      File file = new File(args[0]);

      if (!file.isFile()) {

        log.error("Le fichier de modèle {} n'existe pas ou n'est pas un fichier", args[0]);
        return;
      }

      file = new File(args[1]);

      if (!file.isFile()) {

        log.error("Le fichier PDF {} n'existe pas ou n'est pas un fichier", args[1]);
        return;
      }

      try {

        pdfExtractor.extractDatas(new FileInputStream(args[0]),
            new FileInputStream(args[1]));

      }
      catch (FileNotFoundException e) {

        // Ne devrait pas se produire, les chemins sont testés en amont
        log.error("Fichier non trouvé : {}", e.getMessage(), e);
      }

    }
    else {

      // On considère que c'est le nom du template qui est passé en paramètres
      pdfExtractor.extractDatas(args[0], args[1]);
    }

    List<Donnee> donnees = pdfExtractor.getDonnees();

    if (log.isTraceEnabled()) {

      log.trace("Liste des données...");

      if (donnees != null) {

        for (Donnee donnee : donnees) {

          log.trace("\t[code=" + donnee.getCode() + "][value=" + donnee.getValue() + "][type=" + donnee.getTypedonnee() + "]");
        }

      }
      else {

        log.trace("Aucune donnée extraite");
      }

      log.trace("...fin de la liste des données");
    }

    if (donnees == null) {

      log.warn("Aucune donnée extraite");
      return;
    }

    Document xmlDocument = pdfExtractor.getXmlDocumentOfExtractedDatas();

    // log.debug(xmlDocument.toString());

    /**
     * TODO find a solution for that lib /*OutputFormat outputFormat = new OutputFormat(xmlDocument); StringWriter stringWriter = new
     * StringWriter(); XMLSerializer xmlSerializer = new XMLSerializer(stringWriter, outputFormat); try {
     * xmlSerializer.serialize(xmlDocument); } catch (IOException e) { log.warn("Erreur a la serialisation XML : " + e.getMessage(), e); }
     * log.info(stringWriter.toString());
     */
  }

  @SuppressWarnings("unused")
  private static void mainTest(String[] args) {

    if (args.length != 1) {

      usageTest();
      return;
    }

    try {

      PDDocument pdDocument = null;

      try {

        pdDocument = PDDocument.load(new File(args[0]));

        PdfExtractor pdfExtractor = new PdfExtractor(pdDocument);
        pdfExtractor.setSortByPosition(true);

        // pdfExtractor.processDocument(pdDocument);

        // int numberOfPages = pdDocument.getNumberOfPages();
        //
        // for (int i = 0; i < numberOfPages; i++) {
        //
        // pdfTextStripper.extractFromPage(pdDocument.getPage(i), i);
        // }

        List<PageItem> pageItems = pdfExtractor.getPageItems();

        showPageItemsAndContent(pageItems);

        MapZone testMapZone = new MapZone("liBMontant");
        testMapZone
            .setSearchZone(new Rectangle(new Point(60, 10), new Dimension(100, 700)));
        testMapZone.setPatern("MONTANT H.T.");

        pdfExtractor.addMapZoneByStaticString(testMapZone);

        testMapZone = new MapZone("libBaseTVA");
        testMapZone.setSearchZone(60, 10, 100, 700);
        testMapZone.setPatern("BASE T.V.A.");

        pdfExtractor.addMapZoneByStaticString(testMapZone);

        testMapZone = new MapZone("AdresseLivraison");
        testMapZone.setSearchZone(25, 10, 70, 700);
        testMapZone.setPatern("Adresse de Livraison");

        pdfExtractor.addMapZoneByStaticString(testMapZone);

        for (Entry<String, List<MapZone>> entry : pdfExtractor.mapZonesByStaticString
            .entrySet()) {

          System.out.println("[staticLabel=" + entry.getKey() + "]");

          for (MapZone mapZone : entry.getValue()) {

            System.out.println("\t[mapZoneName=" + mapZone.getCode() + "]");
          }
        }

        testMethodeV2(pageItems, pdfExtractor.mapZonesByStaticString);

        // testMethodeV1(pageItems, mapZones);

      }
      finally {

        if (pdDocument != null) {
          pdDocument.close();
        }
      }

    }
    catch (IOException e) {

      e.printStackTrace();
    }
  }

  /**
   * iterates over the specified list of Patterns until it finds one that matches the specified string. Then returns the Pattern.
   * <p>
   * Order of the supplied list of patterns is important as most common patterns should come first. Patterns should be strict in general,
   * and all will be used with case sensitivity on.
   * </p>
   *
   * @param string the string to be searched
   * @param patterns list of patterns
   * @return matching pattern
   */
  protected static Pattern matchPattern(String string, List<Pattern> patterns) {
    for (Pattern p : patterns) {
      if (p.matcher(string)
          .matches()) {
        return p;
      }
    }
    return null;
  }

  /**
   * This method parses the bidi file provided as inputstream.
   *
   * @param inputStream - The bidi file as inputstream
   * @throws IOException if any line could not be read by the LineNumberReader
   */
  private static void parseBidiFile(InputStream inputStream) throws IOException {
    LineNumberReader rd = new LineNumberReader(new InputStreamReader(inputStream));

    do {
      String s = rd.readLine();
      if (s == null) {
        break;
      }

      int comment = s.indexOf('#'); // ignore comments
      if (comment != -1) {
        s = s.substring(0, comment);
      }

      if (s.length() < 2) {
        continue;
      }

      StringTokenizer st = new StringTokenizer(s, ";");
      int nFields = st.countTokens();
      Character[] fields = new Character[nFields];
      for (int i = 0; i < nFields; i++) {
        fields[i] = (char) Integer.parseInt(st.nextToken()
            .trim(), 16);
      }

      if (fields.length == 2) {
        // initialize the MIRRORING_CHAR_MAP
        MIRRORING_CHAR_MAP.put(fields[0], fields[1]);
      }

    }
    while (true);
  }

  private static void showPageItemsAndContent(List<PageItem> pageItems) {

    Rectangle rectangle;
    WordGroupItem trimmedWordGroupItem;

    for (PageItem pageItem : pageItems) {

      System.out.println("Page n°" + pageItem.getPageNumber());

      for (LineItem lineItem : pageItem.getLineItems()) {

        System.out.println("\tLigne n°" + lineItem.getLineNumber());

        for (WordGroupItem wordGroupItem : lineItem.getWordGroupItems()) {

          rectangle = wordGroupItem.getBounds();

          System.out.println("\t\tGroupe de mots [" + wordGroupItem.getText() + "][X=" + rectangle.x + "][Y=" + rectangle.y + "][height=" +
              rectangle.height + "][width=" + rectangle.width + "]");

          trimmedWordGroupItem = wordGroupItem.getTrimmedWordGroupItem();

          rectangle = trimmedWordGroupItem.getBounds();

          if (rectangle != null) {

            System.out.println(
                "\t\tGroupe de mots \"trimmed\" [" + trimmedWordGroupItem.getText() + "][X=" + rectangle.x + "][Y=" + rectangle.y +
                    "][height=" + rectangle.height + "][width=" + rectangle.width + "]");
          }
          else {

            System.out.println("\t\tGroupe de mots condensé [" + trimmedWordGroupItem.getText() + "]");
          }

          if (wordGroupItem.getWordItems() != null) {

            for (WordItem wordItem : wordGroupItem.getWordItems()) {

              rectangle = wordItem.getBounds();

              System.out.println("\t\t\t[" + wordItem.getText() + "][X=" + rectangle.x + "][Y=" + rectangle.y + "][height=" +
                  rectangle.height + "][width=" + rectangle.width + "]");
            }
          }
        }
      }
    }
  }

  private static void testMethodeV2(List<PageItem> pageItems,
      TreeMap<String, List<MapZone>> mapZonesByStaticLabel) {

    List<String> keysInLine;

    Set<String> staticLabels = mapZonesByStaticLabel.keySet();

    Rectangle foundBounds;

    // Parcours des pages
    for (PageItem pageItem : pageItems) {

      // Parcours des lignes
      for (LineItem lineItem : pageItem.getLineItems()) {

        // Recherche des libellés statiques dans la ligne
        keysInLine = getKeysInLine(staticLabels, lineItem);

        if (keysInLine == null || keysInLine.isEmpty()) {

          continue;
        }

        // Pour chaque libellé trouvé
        for (String staticLabel : keysInLine) {

          foundBounds = lineItem.getBoundsOfSubText(staticLabel);

          System.out.println("Bounds of [" + staticLabel + "][X=" + foundBounds.x + "][Y=" + foundBounds.y + "][width=" +
              foundBounds.width + "][height=" + foundBounds.height + "]");

          // Pour chaque mapZone associé au libellé
          for (MapZone mapZone : mapZonesByStaticLabel.get(staticLabel)) {

            if (mapZone.getSearchZone() != null && mapZone.getSearchZone()
                .contains(foundBounds)) {

              mapZone.setFoundZone(foundBounds);
              System.out.println(
                  "Correspond a la zone [name=" + mapZone.getCode() + "]");
            }
          }
        }
      }
    }

  }

  private static void usage() {

    System.err.println(
        "Usage: java " + PdfExtractor.class.getName() + " <nom_masque> <chemin_pdf>");
  }

  private static void usageTest() {

    System.err.println("Usage: java " + PdfExtractor.class.getName() + " <input-pdf>");
  }

  private String datasDefinitionsFilepath;

  // private static void testTemplateHelper(DataSource dataSource) {
  //
  // TemplateHelper templateHelper = new TemplateHelper();
  // templateHelper.setDataSource(dataSource);
  //
  // String templateName = "val1";
  //
  // PdfTemplate mappingPDF = templateHelper.getTemplateByName(templateName);
  //
  // if (mappingPDF == null) {
  //
  // log.error("Aucun mapping trouvé pour [nom=" + templateName + "]");
  // return;
  // }
  //
  // System.out.println("Mapping trouvé [id=" + mappingPDF.getIdmapping() + "][nom="
  // + mappingPDF.getNom() + "]");
  //
  // templateName = "val";
  //
  // List<PdfTemplate> mappingPDFList = templateHelper.getTemplatesByName(templateName);
  //
  // if (mappingPDFList == null || mappingPDFList.isEmpty()) {
  //
  // log.error("Aucun mapping trouvé avec un nom contenant [" + templateName + "]");
  // }
  //
  // System.out.println("Liste des templates dont le nom contient [" + templateName + "] :");
  //
  // for (PdfTemplate template : mappingPDFList) {
  //
  // System.out.println(
  // "\t[id=" + template.getIdmapping() + "][name=" + template.getNom() + "]");
  // }
  //
  // mappingPDFList = templateHelper.getAllTemplates();
  //
  // if (mappingPDFList == null || mappingPDFList.isEmpty()) {
  //
  // log.error("Aucun mapping trouvé");
  // }
  //
  // System.out.println("Liste de tous les templates :");
  //
  // for (PdfTemplate template : mappingPDFList) {
  //
  // System.out.println(
  // "\t[id=" + template.getIdmapping() + "][name=" + template.getNom() + "]");
  // }
  // }

  DonneeHelper donneeHelper;

  /**
   * MapZones correspondant à des libellés, référencées par le texte des libellés Prévoit qu'il y ait plusieurs Map pour un libellé donné
   * mais dans un premier temps, on considère qu'ils n'y a qu'une mapZone pour un texte de libellé (on peut avoir plusieurs mapZone avec des
   * libellés identiques si elles sont dans des zones de recherche différentes ou on des contraintes permettant de les distinguer)
   */
  private TreeMap<String, List<MapZone>> mapZonesByStaticString;

  private List<PageItem> pageItems;

  private PageItem currentPageItem;

  /**
   * The platform's line separator.
   */
  protected final String LINE_SEPARATOR = System.getProperty("line.separator");

  private String lineSeparator = LINE_SEPARATOR;

  private String wordSeparator = " ";

  private int currentPageNo = 0;

  private int startPage = 1;

  private int endPage = Integer.MAX_VALUE;

  private PDOutlineItem startBookmark = null;

  // 1-based bookmark pages
  private int startBookmarkPageNumber = -1;

  private int endBookmarkPageNumber = -1;

  private PDOutlineItem endBookmark = null;

  private boolean suppressDuplicateOverlappingText = true;

  private boolean shouldSeparateByBeads = true;

  private boolean sortByPosition = false;

  private float indentThreshold = defaultIndentThreshold;

  private float dropThreshold = defaultDropThreshold;

  // we will need to estimate where to add spaces, these are used to help
  // guess
  private float spacingTolerance = .5f;

  private float averageCharTolerance = .3f;

  private List<PDRectangle> beadRectangles = null;

  /**
   * The charactersByArticle is used to extract text by article divisions. For example a PDF that has two columns like a newspaper, we want
   * to extract the first column and then the second column. In this example the PDF would have 2 beads(or articles), one for each column.
   * The size of the charactersByArticle would be 5, because not all text on the screen will fall into one of the articles. The five
   * divisions are shown below Text before first article first article text text between first article and second article second article
   * text text after second article Most PDFs won't have any beads, so charactersByArticle will contain a single entry.
   */
  protected ArrayList<List<TextPosition>> charactersByArticle = new ArrayList<List<TextPosition>>();

  private final Map<String, TreeMap<Float, TreeSet<Float>>> characterListMapping = new HashMap<String, TreeMap<Float, TreeSet<Float>>>();

  protected PDDocument pdDocument;

  private List<Pattern> listOfPatterns = null;

  private int lineNumber = 0;

  private LineItem currentLineItem;

  private WordGroupItem currentWordGroupItem;

  private WordItem currentWordItem;

  private PdfTemplate mappingPDF;

  private InputStream pdfInputStream;

  public final double RATIO = (double) 72 / (double) 96;

  private List<Donnee> donnees;

  private Map<String, Rectangle> regionBoundsByName;

  Map<String, String> transcodingMonths;

  // /**
  // * Utilisé pour stocker les mapZone correspondant à des libellés trouvés
  // *
  // * (a ce stade, la position trouvée est normalement renseignée dans mapZone.foundZone)
  // *
  // * @deprecated use {@link PdfPagePattern#getLabelMapZonesByNames()} instead.
  // */
  // @Deprecated
  // private Map<String, MapZone> labelMapZonesByNames;
  //
  // /**
  // * @deprecated use {@link PdfPagePattern#getMapZonesByStaticLabel()} instead.
  // */
  // @Deprecated
  // private Map<String, List<MapZone>> mapZonesByStaticLabel;
  //
  // /**
  // * @deprecated use {@link PdfPagePattern#getMandatoryMapZones()} instead.
  // */
  // @Deprecated
  // private List<MapZone> mandatoryMapZones;

  // /**
  // * @deprecated use {@link PdfPagePattern#getRelativeMapZones()} instead.
  // */
  // @Deprecated
  // private List<MapZone> relativeMapZones;

  private TranscodingHelper transcodingHelper;

  // private Map<String, Map<String, String>> transcodingMap;

  // private Ini transcodingIni;

  private Map<String, Integer> datasTypesByCode;

  // Liste des codes
  private Map<String, Donnee> extractedDatasByCode;

  private Map<String, Map<String, String>> linesDatasCodesAndValues;

  private Map<Integer, List<Donnee>> linesDatas;

  private String transcodingFilepath = null;

  private PdfExtractor() throws IOException {

    // loadTranscodingValues();
  }

  /**
   * Instantiate a new PDFTextStripper object.
   *
   * @throws IOException If there is an error loading the properties.
   */
  @Deprecated
  private PdfExtractor(PDDocument pdDocument) throws IOException {

    this();

    this.pdDocument = pdDocument;
  }

  // private void addDonnee(Donnee donnee) {
  //
  // if (donnees == null) {
  //
  // donnees = new ArrayList<Donnee>();
  // }
  //
  // donnees.add(donnee);
  // }

  @Deprecated
  public PdfExtractor(PdfTemplate mappingPDF, InputStream pdfInputStream) throws IOException {

    this.mappingPDF = mappingPDF;
    this.pdfInputStream = pdfInputStream;
  }

  public boolean _extractDatas2(PdfPagePattern pdfPagePattern, PDPage pdPage,
      boolean suppressDuplicateOverlappingText) throws TemplateDoesNotHandleMultiPageDocumentsException.MandatoryDatasNotFoundException {

    if (pdfPagePattern == null) {

      log.error("pdfPagePattern null");
      return false;
    }

    if (pdPage == null) {

      log.error("pdfPage null");
      return false;
    }

    // PDFTextStripperByArea pdfTextStripperByArea;

    try {

      checkMandatoryDatas(pdfPagePattern, pdPage, suppressDuplicateOverlappingText);

    }
    catch (IOException e) {

      log.error("Exception d'entrée/sortie à la vérification des données obligatoires : " + e.getMessage(), e);
      return false;
    }

    return true;
  }

  private void addExtractedDatasByCode(Donnee donnee) {

    if (donnee == null) {

      log.warn("donnee is null");
      return;
    }

    if (StringUtils.isBlank(donnee.getCode())) {

      log.warn("data without code");
      return;
    }

    if (extractedDatasByCode == null) {

      extractedDatasByCode = new TreeMap<String, Donnee>();
    }

    extractedDatasByCode.put(donnee.getCode(), donnee);
  }

  /**
   * Alimente la map labelMapZonesByNames
   *
   * @deprecated use {@link PdfPagePattern#addLabelMapZoneByName(MapZone)} instead.
   * @param mapZone
   */
  // @Deprecated
  // private void addLabelMapZoneByName(MapZone mapZone) {
  //
  // if (labelMapZonesByNames == null) {
  //
  // labelMapZonesByNames = new TreeMap<String, MapZone>();
  // }
  //
  // labelMapZonesByNames.put(mapZone.getCode(), mapZone);
  // }

  @Deprecated
  private void addMapZoneByStaticString(MapZone mapZone) {

    if (mapZonesByStaticString == null) {

      mapZonesByStaticString = new TreeMap<String, List<MapZone>>();
    }

    String staticLabel = mapZone.getPattern();

    if (!mapZonesByStaticString.containsKey(staticLabel) || mapZonesByStaticString.get(staticLabel) == null) {

      List<MapZone> mapZonesList = new ArrayList<MapZone>();

      mapZonesByStaticString.put(staticLabel, mapZonesList);
    }

    mapZonesByStaticString.get(staticLabel)
        .add(mapZone);
  }

  private boolean addPageItem(PageItem pageItem) {

    if (pageItems == null) {

      pageItems = new ArrayList<PageItem>();
    }

    return pageItems.add(pageItem);
  }

  /**
   * Utilisé pour restituer les positions au niveau des objets Donnee
   *
   * @param name
   * @param bounds
   */
  private void addRegionBoundByName(String name, Rectangle bounds) {

    if (regionBoundsByName == null) {

      regionBoundsByName = new TreeMap<String, Rectangle>();
    }

    regionBoundsByName.put(name, bounds);
  }

  private void addRegions(PDFTextStripperByArea pdfTextStripperByArea, List<MapZone> mapZones,
      int orientation) {

    if (pdfTextStripperByArea == null) {

      log.error("pdfTextStripperByArea null");
      return;
    }

    if (mapZones == null) {

      log.error("mapZones null");
      return;
    }

    for (MapZone mapZone : mapZones) {

      // if (log.isTraceEnabled()) {
      //
      // Rectangle bounds = mapZone.getDefinedZone();
      //
      // log.trace("Ajout region " + mapZone.getName() + "...");
      // log.trace("coordonnées d'origine [x=" + bounds.x + "][y=" + bounds.y + "][width="
      // + bounds.width + "][height=" + bounds.height + "]");
      //
      // bounds = getRectangleWithRatio(bounds);
      //
      // log.trace("coordonnées avec ratio [x=" + bounds.x + "][y=" + bounds.y + "][width="
      // + bounds.width + "][height=" + bounds.height + "]");
      // }

      // gerer l'orientation
      if (orientation == PdfPagePattern.ORIENTATION_PORTRAIT) {// portrait

        // log.trace("Orientation portrait");

        String regionName = mapZone.getCode();
        Rectangle regionBounds = getRectangleWithRatio(mapZone.getDefinedZone());

        pdfTextStripperByArea.addRegion(regionName, regionBounds);

        addRegionBoundByName(regionName, regionBounds);

      }
      else if (orientation == PdfPagePattern.ORIENTATION_PAYSAGE) {// paysage

        // log.trace("Orientation paysage");

        // log.warn("Orientation paysage non géré");

        String regionName = mapZone.getCode();
        Rectangle regionBounds = getRectangleWithRatio(mapZone.getDefinedZone());

        pdfTextStripperByArea.addRegion(regionName, regionBounds);

        addRegionBoundByName(regionName, regionBounds);

      }
      else {

        log.error("orientation non attendue [" + orientation + "]");
      }
    }
  }

  /**
   * Lecture des MapZone de l'instance de PdfPagePattern et ajout des régions correspondantes (nom et coordonnées) à l'instance de
   * PDFTextStripperByArea
   *
   * @param pdfTextStripperByArea Instance de PDFTextStripperByArea auquel ajouté les régions
   * @param pdfPagePattern Instance de PdfPagePattern contenant les MapZone d'une page
   */
  // private void addRegions(PDFTextStripperByArea pdfTextStripperByArea,
  // PdfPagePattern pdfPagePattern) {
  //
  // if (pdfPagePattern == null) {
  //
  // log.warn("pdfPagePattern null");
  // return;
  // }
  //
  // for (int i = 0; i < pdfPagePattern.getMapZonesCount(); i++) {
  //
  // MapZone mapZone = pdfPagePattern.getMapZone(i);
  //
  // if (mapZone.isStaticLabel() || mapZone.isRelative()) {
  // continue;
  // }
  //
  // if (log.isTraceEnabled()) {
  //
  // Rectangle bounds = mapZone.getDefinedZone();
  //
  // log.trace("Ajout region " + mapZone.getCode() + "...");
  // log.trace("coordonnées d'origine [x=" + bounds.x + "][y=" + bounds.y + "][width="
  // + bounds.width + "][height=" + bounds.height + "]");
  //
  // bounds = getRectangleWithRatio(bounds);
  //
  // log.trace("coordonnées avec ratio [x=" + bounds.x + "][y=" + bounds.y + "][width="
  // + bounds.width + "][height=" + bounds.height + "]");
  // }
  //
  // // gerer l'orientation
  // if (pdfPagePattern.getOrientation() == PdfPagePattern.ORIENTATION_PORTRAIT) {
  //
  // log.trace("Orientation portrait");
  //
  // String regionName = mapZone.getCode();
  // Rectangle regionBounds = getRectangleWithRatio(mapZone.getDefinedZone());
  //
  // pdfTextStripperByArea.addRegion(regionName, regionBounds);
  //
  // addRegionBoundByName(regionName, regionBounds);
  //
  // } else if (pdfPagePattern.getOrientation() == PdfPagePattern.ORIENTATION_PAYSAGE) {
  //
  // log.warn("Orientation paysage non géré");
  //
  // } else {
  //
  // log.error("orientation inconnue:" + pdfPagePattern.getOrientation());
  // }
  // }
  // }
  //
  // private void addTranscoding(String fieldName, String originalValue, String replacementValue)
  // {
  //
  // if (transcodingMap == null) {
  //
  // transcodingMap = new HashMap<String, Map<String, String>>();
  // }
  //
  // if (transcodingMap.get(fieldName) == null) {
  //
  // transcodingMap.put(fieldName, new HashMap<String, String>());
  // }
  //
  // transcodingMap.get(fieldName).put(originalValue.toLowerCase(), replacementValue);
  // }

  private boolean areThoseParamsOkWithMapZones(PDPage pdPage, PageItem pageItem,
      PdfPagePattern pdfPagePattern) {

    boolean result = true;

    if (pdPage == null) {

      log.error("pdPage null");
      result = false;
    }

    if (pageItem == null) {

      log.error("pageItem null");
      result = false;
    }

    if (pdfPagePattern == null) {

      log.error("pdfPagePattern null");
      result = false;

    }
    else if (pdfPagePattern.getMapZones() == null || pdfPagePattern.getMapZones()
        .isEmpty()) {

          log.error("pdfPagePattern without defined zones");
          result = false;
        }

    return result;
  }

  /**
   * Test si les paramètres ne pas sont null et si les listes comportent au moins un enregistrement
   *
   * @param pdDocument
   * @param pageItems
   * @param pdfPagePatterns
   * @return Vrai si les conditions sont respectées, Faux sinon
   */
  private boolean areThosesParamsOk(PDDocument pdDocument, List<PageItem> pageItems,
      List<PdfPagePattern> pdfPagePatterns) {

    boolean result = true;

    if (pdDocument == null || pdDocument.getNumberOfPages() < 1) {

      log.warn(pdDocument == null ? "pdDocument null" : "pdDocument has no pages");
      result = false;

    }

    if (!areThosesParamsOk(pdDocument, pdfPagePatterns)) {

      result = false;
    }

    return result;
  }

  private boolean areThosesParamsOk(PDDocument pdDocument, List<PdfPagePattern> pdfPagePatterns) {

    boolean result = true;

    if (pdDocument == null || pdDocument.getNumberOfPages() < 1) {

      log.warn(pdDocument == null ? "Document is null" : "Document without any page");
      result = false;
    }

    if (pdfPagePatterns == null || pdfPagePatterns.isEmpty()) {

      log.warn(pdfPagePatterns == null ? "Template is null" : "Template without any page");
      result = false;
    }

    return result;
  }

  private void checkMandatoryDatas(PdfPagePattern pdfPagePattern, PDPage pdPage,
      boolean suppressDuplicateOverlappingText)
      throws TemplateDoesNotHandleMultiPageDocumentsException.MandatoryDatasNotFoundException, IOException {

    List<MapZone> mandatoryMapZones = pdfPagePattern.getMandatoryMapZones();

    PDFTextStripperByArea pdfTextStripperByArea;

    if (mandatoryMapZones != null) {

      pdfTextStripperByArea = getNewPDFTextStripperByArea();
      pdfTextStripperByArea
          .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
      log.trace("suppressDuplicateOverlappingText=" + (suppressDuplicateOverlappingText ? "true" : "false"));

      addRegions(pdfTextStripperByArea, mandatoryMapZones, pdfPagePattern.getOrientation());
      pdfTextStripperByArea.extractRegions(pdPage);

      String value;

      for (MapZone mandatoryMapZone : mandatoryMapZones) {

        value = pdfTextStripperByArea.getTextForRegion(mandatoryMapZone.getCode());

        if (mandatoryMapZone.isStaticLabel()) {

          // TODO: gerer des regex
          if (!value.equals(mandatoryMapZone.getPattern())) {

            throw new TemplateDoesNotHandleMultiPageDocumentsException.MandatoryDatasNotFoundException("La zone statique obligatoire [" +
                mandatoryMapZone.getCode() + "=" + value + "] n'a pas la valeur [" + mandatoryMapZone.getPattern() + "] attendue");
          }

        }
        else {

          // TODO: faire des tests plus poussés (pattern,...)
          if (StringUtils.isBlank(value)) {

            throw new TemplateDoesNotHandleMultiPageDocumentsException.MandatoryDatasNotFoundException(
                "La zone obligatoire [" + mandatoryMapZone.getCode() + "=" + value + "] n'a pas été trouvée");
          }
        }
      }
    }
  }

  /**
   * Used within {@link #normalize(List)} to create a single {@link WordWithTextPositions} entry.
   */
  private WordWithTextPositions createWord(String word, List<TextPosition> wordPositions) {
    return new WordWithTextPositions(normalizeWord(word), wordPositions);
  }

  /**
   * End a page. Default implementation is to do nothing. Subclasses may provide additional information.
   *
   * @param page The page we are about to process.
   * @throws IOException If there is any error writing to the stream.
   */
  protected void endPage(PDPage page) throws IOException {
    // default is to do nothing
  }

  /**
   * Extraction de données à partir d'un flux contenant le modèle en XMl et un flux sur le fichier PDF d'où extraire les données
   *
   * @param xmlInputStream Flux sur le modèle
   * @param pdfInputStream Flux sur le fichier PDF
   * @return
   */
  public boolean extractDatas(InputStream xmlInputStream, InputStream pdfInputStream) {

    if (xmlInputStream == null) {

      log.error("Flux de lecture du mapping null !");
      return false;
    }

    if (pdfInputStream == null) {

      log.error("Flux de lecture du fichier PDF null !");
      return false;
    }

    TemplateHelper templateHelper = new TemplateHelper();
    // List<PdfPagePattern> pdfPagePatterns = templateHelper
    // .getPdfPagePatternsFromXml(xmlInputStream);

    PdfTemplate pdfTemplate = templateHelper.getPdfTemplateFromXml(xmlInputStream);

    // if (pdfTemplate == null) {
    //
    // log.error("Template null");
    // return false;
    // }
    //
    // log.trace("pdfTemplate.isSuppressDuplicateOverlappingText="
    // + (pdfTemplate.isSuppressDuplicateOverlappingText() ? "true" : "false"));
    //
    // List<PdfPagePattern> pdfPagePatterns = pdfTemplate.getPdfPagePatterns();
    //
    // if (pdfPagePatterns == null || pdfPagePatterns.isEmpty()) {
    //
    // log.error("Template without defined pages !");
    // return false;
    // }

    return extractDatas(pdfTemplate, pdfInputStream,
        pdfTemplate.isSuppressDuplicateOverlappingText());
  }

  /**
   * Extraction des données à partir d'une instance de PdfTemplate et un d'un flux sur le fichier PDF d'où extraire les données
   *
   * @param template Modèle de document
   * @param pdfInputStream Flux sur le fichier PDF
   * @return Vrai si l'extraction s'est bien déroulée, Faux sinon
   */
  public boolean extractDatas(PdfTemplate template, InputStream pdfInputStream) {

    if (template == null) {

      log.error("Template is null");
      return false;
    }

    if (pdfInputStream == null) {

      log.error("Flux de lecture du fichier PDF null !");
      return false;
    }

    if (log.isDebugEnabled()) {

      log.debug("Utilisation du modele [id=" + template.getTemplateId() + "][nom=" + template.getName() + "]");

      log.debug("Suppression de texte duplique : " + (template.isSuppressDuplicateOverlappingText() ? "Oui" : "Non"));
    }

    TemplateHelper templateHelper = new TemplateHelper();

    PdfTemplate pdfTemplate = templateHelper
        .getPdfTemplateFromXml(template.getByteArrayTemplate());

    // List<PdfPagePattern> pdfPagePatterns = null;
    //
    // if (pdfTemplate != null) {
    //
    // pdfPagePatterns = pdfTemplate.getPdfPagePatterns();
    // }
    //
    // if (pdfPagePatterns == null || pdfPagePatterns.isEmpty()) {
    //
    // log.error("Mapping sans pages !");
    // return false;
    // }

    return extractDatas(pdfTemplate, pdfInputStream,
        template.isSuppressDuplicateOverlappingText());
  }

  /**
   * Extraction des données à partir d'une instance de PdfTemplate et un d'un flux sur le fichier PDF d'où extraire les données, avec la
   * possibilité de spécifier si un filtrage doit être effectué si des textes se superposent
   *
   * @param pdfTemplate Modèle de document
   * @param pdfInputStream Flux sur le fichier PDF
   * @param suppressDuplicateOverlappingText Option de filtrage des textes superposés
   * @return Vrai si l'extraction s'est bien déroulée, Faux sinon
   */
  public boolean extractDatas(PdfTemplate pdfTemplate, InputStream pdfInputStream,
      boolean suppressDuplicateOverlappingText) {

    try {

      if (pdfTemplate == null) {

        log.error("Template null");
        return false;
      }

      if (log.isDebugEnabled()) {

        log.debug("Utilisation du modele [id=" + pdfTemplate.getTemplateId() + "][nom=" + pdfTemplate.getName() + "]");

        log.debug("Suppression de texte duplique : " + (pdfTemplate.isSuppressDuplicateOverlappingText() ? "Oui" : "Non"));
      }

      List<PdfPagePattern> pdfPagePatterns = pdfTemplate.getPdfPagePatterns();

      if (pdfPagePatterns == null || pdfPagePatterns.isEmpty()) {

        log.error("Modele sans aucune page definie");
        return false;
      }

      // boolean multipagesTemplate = pdfPagePatterns.size() > 1;

      PDDocument pdDocument = null;

      try {

        try {

          pdDocument = PDDocument.load(pdfInputStream);

          if (pdDocument == null) {

            log.error("Echec au chargement du fichier PDF");
            return false;
          }

          int pageCount = pdDocument.getNumberOfPages();

          if (pageCount < 1) {

            log.warn("Document sans aucune page...");
            return false;
          }

          // Lecture du PDF et construction des objets nécessaires à la
          // recherche textuelle
          processDocument(pdDocument);

          // listStaticAndRelativeMapZones(pdfPagePatterns);

          // !-- NE PAS SUPPRIMER...
          // A réactiver si on veut s'arrêter dès qu'une donnée obligatoire n'est pas
          // présente
          // try {
          // log.trace("////////extractMandatoryDatas... //////////");
          // extractMandatoryDatas(pdDocument, pdfPagePatterns,
          // suppressDuplicateOverlappingText);
          // log.trace("////////fin extractMandatoryDatas //////////");
          // } catch (TemplateDoesNotHandleMultiPageDocumentsException e) {
          // // TODO Auto-generated catch block
          // e.printStackTrace();
          // }
          // ...NE PAS SUPPRIMER --!

          // log.trace("////////extractRelativeDatas 1... //////////");
          // extractRelativeDatas(pdDocument, pdfPagePatterns);
          // log.trace("////////fin extractRelativeDatas 1... //////////");

          log.trace("////////Extraction des Données Relatives... //////////");
          try {

            extractRelativeDatas(pdDocument, pageItems, pdfPagePatterns,
                suppressDuplicateOverlappingText);

          }
          catch (TemplateDoesNotHandleMultiPageDocumentsException e) {

            log.error("Le modele ne gere pas les documents comportants plusieurs pages",
                e);
          }
          log.trace("////////fin de l'extraction des Données Relatives//////////");

          log.trace("////////Extraction des données fixes...////////");
          try {

            extractFixedDatas(pdDocument, pageItems, pdfPagePatterns,
                suppressDuplicateOverlappingText);

          }
          catch (TemplateDoesNotHandleMultiPageDocumentsException e) {

            log.error("Le modele ne gere pas les documents comportants plusieurs pages",
                e);
          }
          log.trace("////////fin de l'extraction des données fixes////////");

          List<MapZone> linesMapZones = pdfTemplate.getLinesMapZones();

          if (linesMapZones != null && !linesMapZones.isEmpty()) {

            log.trace("////////Extraction des données de ligne articles...////////");
            extractLinesDatas(pdDocument, pageItems, linesMapZones,
                suppressDuplicateOverlappingText);
            log.trace(
                "////////fin de l'extraction des données de ligne articles////////");

          }
          else {

            log.trace("Aucune donnée de ligne définie");
          }

          Map<String, String> fixedCodesAndValues = pdfTemplate.getFixedCodesAndValues();

          if (fixedCodesAndValues != null && !fixedCodesAndValues.isEmpty()) {

            log.trace("////////Ajout/remplacement des données fixées...////////");

            Integer dateType;

            for (Entry<String, String> entry : fixedCodesAndValues.entrySet()) {

              Donnee donnee = new Donnee();

              dateType = getDataTypeForCode(entry.getKey());

              if (dateType != null) {

                donnee.setTypedonnee(dateType);

              }
              else {

                log.error(
                    "Pas de type trouvé pour le code [" + entry.getKey() + "]");
              }

              donnee.setCode(entry.getKey());
              if ((donnee.getCode()
                  .equalsIgnoreCase("SELLERGLN") ||
                  donnee.getCode()
                      .equalsIgnoreCase("SELLERINTRACOMNUMBER")) &&
                  entry.getValue()
                      .startsWith("*")) {

                donnee.setCorporateKey(true);
                donnee.setValue(
                    entry.getValue()
                        .substring(1, entry.getValue()
                            .length()));

              }
              else {

                donnee.setValue(entry.getValue());
              }

              donnee.setFixed(true);

              addExtractedDatasByCode(donnee);
            }

            log.trace("////////fin Ajout/remplacement des données fixées////////");
          }

        }
        finally {

          if (pdDocument != null) {
            pdDocument.close();
          }
        }

        return true;

      }
      catch (InvalidPasswordException e) {

        log.error("Mot de passe non valide : " + e.getMessage(), e);

      }
      catch (IOException e) {

        log.error("Erreur d'entrée/sortie : " + e.getMessage(), e);
      }

    }
    catch (Exception e) {

      log.error("Exception innatendue : " + e.getMessage(), e);

    }
    catch (Throwable t) {

      log.error("Exception innatendue : " + t.getMessage(), t);
    }

    return false;
  }

  /**
   * Extraction des données à partir d'un nom de template et d'un chemin de fichier
   *
   * @param templateName Nom du template à utiliser pour l'extraction
   * @param filepath Chemin du fichier d'où extraire les données
   * @return Vrai si l'extraction s'est bien déroulée, Faux sinon
   */
  public boolean extractDatas(String templateName, String filepath) {

    TemplateHelper templateHelper = new TemplateHelper();

    log.debug("Recherche du modele : " + templateName);

    // L'instance de PdfTemplate est ici alimenté à partir de la base de données
    // avec uniquement le nom du template et les flux PDF (exemple) et XML (modèle)
    PdfTemplate pdfTemplate = templateHelper.getTemplateByName(templateName);

    if (pdfTemplate != null) {

      log.debug("Utilisation du modele [id=" + pdfTemplate.getTemplateId() + "][name=" + pdfTemplate.getName() + "]");
    }

    File file = new File(filepath);

    if (!file.isFile()) {

      log.error("[" + filepath + "] n'existe pas ou n'est pas un fichier");
      return false;
    }

    log.debug("Extraction des donnees du fichier [" + filepath + "]...");

    templateHelper.readXmlTemplate(pdfTemplate);
    // PdfTemplate pdfTemplateFromXml = templateHelper
    // .getPdfTemplateFromXml(pdfTemplateFromDatabase.getByteArrayTemplate());

    List<PdfPagePattern> pdfPagePatterns = null;

    if (pdfTemplate != null) {

      pdfPagePatterns = pdfTemplate.getPdfPagePatterns();
    }

    if (pdfPagePatterns == null || pdfPagePatterns.isEmpty()) {

      log.error("Modèle PDF sans pages définies !");
      return false;
    }

    try {

      return extractDatas(pdfTemplate, new FileInputStream(filepath),
          pdfTemplate.isSuppressDuplicateOverlappingText());

    }
    catch (FileNotFoundException e) {

      log.error("Le fichier [" + filepath + "] n'existe pas : " + e.getMessage(), e);
    }

    return false;
  }

  private void extractFixedDatas(PDDocument pdDocument, List<PageItem> pageItems,
      List<PdfPagePattern> pdfPagePatterns, boolean suppressDuplicateOverlappingText)
      throws TemplateDoesNotHandleMultiPageDocumentsException {

    if (!areThosesParamsOk(pdDocument, pageItems, pdfPagePatterns)) {

      // Les erreurs sont remontées par areThosesParamsOk
      return;
    }

    int pdfDocumentPageCount = pdDocument.getNumberOfPages();
    int pageItemsCount = pageItems.size();

    boolean pdfDocumentHasOnlyOnePage = pdfDocumentPageCount < 2;
    boolean templateHandleMultiPageDocuments = pdfPagePatterns.size() > 1;

    if (pdfDocumentHasOnlyOnePage) {

      // Le document PDF ne comporte qu'une seule page

      // On extrait les données de l'unique page avec ce qui est défini pour la première page
      // du template
      extractFixedDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
          suppressDuplicateOverlappingText);

      if (templateHandleMultiPageDocuments) {

        // Si le template a été défini avec plusieurs pages, on fait une recherche dans
        // l'unique page du document avec ce qui a été définie pour la dernière page dans le
        // template
        PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);
        extractFixedDatas(pdDocument.getPage(pdfDocumentPageCount - 1),
            pageItems.get(pageItemsCount - 1), lastPdfPagePattern,
            suppressDuplicateOverlappingText);
      }

    }
    else {

      // Le document PDF comporte au moins deux pages
      if (!templateHandleMultiPageDocuments) {

        throw new TemplateDoesNotHandleMultiPageDocumentsException(
            "Document have " + pdDocument.getNumberOfPages() + " pages");
      }

      // On extrait respectivement les données de la première et dernière page du document
      // avec ce qui a été définie pour la première et la dernières page au niveau du template

      log.trace("Extraction des données de la première page...");
      extractFixedDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
          suppressDuplicateOverlappingText);

      PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);

      log.trace("Extraction des données de la dernière page...");
      extractFixedDatas(pdDocument.getPage(pdfDocumentPageCount - 1),
          pageItems.get(pageItemsCount - 1), lastPdfPagePattern,
          suppressDuplicateOverlappingText);
    }
  }

  private void extractFixedDatas(PDPage pdPage, PageItem pageItem, PdfPagePattern pdfPagePattern,
      boolean suppressDuplicateOverlappingText) {

    log.trace("Extraction des données fixes pour la page {}", pdfPagePattern.getPageNumber());

    if (!areThoseParamsOkWithMapZones(pdPage, pageItem, pdfPagePattern)) {

      return;
    }

    PDFTextStripperByArea pdfTextStripperByArea;

    try {

      pdfTextStripperByArea = getNewPDFTextStripperByArea();
      pdfTextStripperByArea
          .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
      log.trace("suppressDuplicateOverlappingText=" + (suppressDuplicateOverlappingText ? "true" : "false"));

      List<MapZone> mapZones = new ArrayList<MapZone>();

      for (MapZone mapZone : pdfPagePattern.getMapZones()) {

        if (mapZone.isStaticLabel() || mapZone.isRelative()) {

          continue;
        }

        mapZones.add(mapZone);
      }

      addRegions(pdfTextStripperByArea, mapZones, pdfPagePattern.getOrientation());
      pdfTextStripperByArea.extractRegions(pdPage);

      extractFromRegions(pdfTextStripperByArea.getRegions(), pdfTextStripperByArea,
          pdfPagePattern);

    }
    catch (IOException e) {

      log.error("Exception d'entrée/sortie : {}", e.getMessage(), e);
    }
  }

  /**
   * Extraction des informations d'une page de PDF en alimentant la liste des Donnee L'alimentation se fait en parcourant la liste des
   * regions et en recherchant les MapZone correspondantes au nom des regions dans le template de la page
   *
   * @param regions Liste des noms de regions où lire les données
   * @param pdfTextStripperByArea Instance de PDFTextStripperByArea pour la lecture des données
   * @param pdfPagePattern Instance de PdfPagePattern avec la liste des MapZone d'une page
   */
  private void extractFromRegions(List<String> regions,
      PDFTextStripperByArea pdfTextStripperByArea, PdfPagePattern pdfPagePattern) {

    extractFromRegions(regions, pdfTextStripperByArea, pdfPagePattern, null);
  }

  /**
   * Extraction des informations du PDF en alimentant la liste des Donnee L'alimentation se fait en parcourant la liste des regions et en
   * recherchant les mapZone correspondantes au nom des regions, d'abord dans le template de la première page, puis dans le template de la
   * dernière page si la MapZone n'y a pas été trouvée
   *
   * @param regions Liste des noms de regions où lire les données
   * @param pdfTextStripperByArea Instance de PDFTextStripperByArea pour la lecture des données
   * @param firstPdfPagePattern Instance de PdfPagePattern avec la liste des MapZone de la première page
   * @param lastPdfPagePattern Instance de PdfPagePattern avec la liste des MapZone de la dernière page
   */
  private void extractFromRegions(List<String> regions,
      PDFTextStripperByArea pdfTextStripperByArea, PdfPagePattern firstPdfPagePattern,
      PdfPagePattern lastPdfPagePattern) {

    String extractedText;
    MapZone mapZone;

    String stringPattern;
    String datePattern;
    String amountPattern;
    Pattern pattern;
    Matcher matcher;
    String transcodingText;

    for (int i = 0; i < regions.size(); i++) {

      datePattern = null;
      amountPattern = null;

      String regionName = regions.get(i);

      extractedText = pdfTextStripperByArea.getTextForRegion(regionName)
          .trim();

      log.trace("\tRegion [name={}][text={}][SuppressDuplicateOverlappingText={}]",
          regionName, extractedText,
          pdfTextStripperByArea.getSuppressDuplicateOverlappingText());

      mapZone = firstPdfPagePattern.getMapZone(regionName);

      // Si on ne trouve pas la mapZone dans le template de la première page, on la cherche
      // dans la dernière page
      if (mapZone == null && lastPdfPagePattern != null) {

        mapZone = lastPdfPagePattern.getMapZone(regionName);
      }

      if (mapZone == null) {

        log.warn("Pas de mapZone trouvé pour [name=" + regionName + "]");
        continue;
      }

      if (StringUtils.isBlank(mapZone.getCode())) {

        log.warn("MapZone sans code : {}", mapZone.toLogString());
        continue;
      }

      Integer mapZoneType = getDataTypeForCode(mapZone.getCode());

      if (mapZoneType == null) {

        log.warn("Pas de type trouvé pour le code [{}]", mapZone.getCode());

      }
      else {

        log.trace("Type trouvé pour le code {} : {}", mapZone.getCode(),
            Donnee.getStringType(mapZoneType));
      }

      if (mapZoneType != null && !mapZoneType.equals(mapZone.getType())) {

        log.warn("Donnée [code={}] renseigné au type {} dans le modèle au lieu du type {}",
            mapZone.getCode(), Donnee.getStringType(mapZone.getType()),
            Donnee.getStringType(mapZoneType));

        mapZone.setType(mapZoneType);
      }

      if (mapZone.isDeleteSpaces()) {

        // https://stackoverflow.com/questions/26123271/replace-spaces-in-string-with-regex-in-java#answer-26123870
        // http://www.fileformat.info/info/unicode/category/Zs/list.htm
        extractedText = extractedText.replaceAll("[\\p{Zs}\\s]+", "");
      }

      if (mapZone.isDeduplicate() && mapZone.getDuplicateOccurency() > 0) {

        if (getDonneeHelper() != null) {

          extractedText = getDonneeHelper().getDeDuplicateValue(extractedText,
              mapZone.getDuplicateOccurency());
        }
        else {

          System.err.println("getDonneeHelper() return null");
        }
      }

      // Test si une expression regulière est renseignée
      if ((stringPattern = mapZone.getPattern()) != null && stringPattern.startsWith("e_")) {

        stringPattern = stringPattern.substring(2, stringPattern.length());

        if (stringPattern.contains("_")) {

          if (mapZone.getType() == Donnee.TYPE_DATE) {

            datePattern = DonneeHelper.getSanitizedDatePattern(stringPattern.substring(
                stringPattern.lastIndexOf('_') + 1, stringPattern.length()));

            stringPattern = stringPattern.substring(0, stringPattern.lastIndexOf('_'));

          }
          else if (mapZone.getType() == Donnee.TYPE_REAL) {

            amountPattern = stringPattern.substring(stringPattern.lastIndexOf("_") + 1,
                stringPattern.length());

            stringPattern = stringPattern.substring(0, stringPattern.lastIndexOf('_'));
          }
        }

        // prefixer les expressions régulière avec (?s) pour gèrer plusieurs lignes
        // pattern = Pattern.compile(stringPattern, Pattern.MULTILINE);
        pattern = Pattern.compile(stringPattern);
        matcher = pattern.matcher(extractedText);

        if (matcher.matches()) {

          log.trace("[{}] est conforme au pattern [{}] : {} groupes trouvés",
              extractedText, stringPattern, matcher.groupCount());

          if (matcher.groupCount() < 2) {

            if (matcher.groupCount() > 0) {

              extractedText = matcher.group(1);

            }
            else {

              extractedText = matcher.group(0);
            }

          }
          else {

            StringBuilder stringBuilder = new StringBuilder();
            String string;

            for (int groupIndex = 1; groupIndex <= matcher.groupCount(); groupIndex++) {

              string = matcher.group(groupIndex);

              if (string != null) {
                stringBuilder.append(string);
              }
            }

            extractedText = stringBuilder.toString();
          }

          log.trace("Donnée extraite : [{}]", extractedText);

        }
        else {

          log.debug("[{}] non conforme au pattern [{}]", extractedText, stringPattern);
          extractedText = "";
        }
      }

      Donnee donnee = new Donnee();
      donnee.setCode(regionName);
      donnee.setTypedonnee(mapZone.getType());

      if (mapZone.getType() == Donnee.TYPE_DATE) {

        // datePattern a pu être alimenté avant si le pattern contenait une expression
        // régulière
        donnee.setPattern(datePattern != null ? datePattern
            : DonneeHelper.getSanitizedDatePattern(mapZone.getPattern()));

        if (donnee.getPattern()
            .contains("MMMM") && getTranscodingHelper() != null) {

          extractedText = getTranscodingHelper()
              .replaceMonths(extractedText.toLowerCase());
        }

      }
      else if (getTranscodingHelper() != null) {

        transcodingText = getTranscodingHelper().getTranscoding(regionName, extractedText);

        if (transcodingText != null) {

          extractedText = transcodingText;
        }

      }
      else {

        log.warn("TranscodingHelper null");
      }

      extractedText = extractedText.replaceAll("(\\r\\n|\\r|\\n)", " ");

      if (mapZone.getType() == Donnee.TYPE_REAL) {

        if (mapZone.isAbsoluteValueRequired()) {

          extractedText = extractedText.replaceAll("[^0-9\\,\\.]", "");

        }
        else {

          extractedText.replaceAll("[^0-9\\,\\.\\-]", "");
        }

        if (StringUtils.isNotBlank(amountPattern)) {

          donnee.setPattern(amountPattern);

        }
        else if (StringUtils.isNotBlank(mapZone.getPattern()) && !mapZone.getPattern()
            .startsWith("e_")) {

              donnee.setPattern(mapZone.getPattern());
            }

        if (StringUtils.isBlank(donnee.getPattern())) {

          donnee.setPattern(Donnee.DEFAULT_INPUT_AMOUNT_PATTERN);
        }
      }

      // Ajouté le 6 mars 2019 suite pbm facture leistritz
      // extractedText = StringEscapeUtils.escapeXml10(extractedText);

      donnee.setValue(extractedText);

      donnee.setPartnerKey(mapZone.isPartnerKey());
      donnee.setCorporateKey(mapZone.isCorporateKey());
      donnee.setPosition(getRegionBoundByName(regionName));

      // Indique si la mapZone a été trouvée
      mapZone.setFound(StringUtils.isNotBlank(donnee.getValue()));

      if (donnee.getTypedonnee() == Donnee.TYPE_DATE) {

        donnee.convertToDefaultDatePattern();

      }
      else if (donnee.getTypedonnee() == Donnee.TYPE_REAL) {

        donnee.convertToDefaultAmountPattern();
      }

      addExtractedDatasByCode(donnee);
    }
  }

  private void extractLinesDatas(PDDocument pdDocument, List<PageItem> pageItems,
      List<MapZone> linesMapZones, boolean suppressDuplicateOverlappingText) {

    extractLinesDatasFromPage(pdDocument.getPage(0), pageItems.get(0), linesMapZones,
        suppressDuplicateOverlappingText);
  }

  private void extractLinesDatasFromPage(PDPage pdPage, PageItem pageItem,
      List<MapZone> linesMapZones, boolean suppressDuplicateOverlappingText) {

    if (pageItem == null) {

      log.error("pageItem null");
      return;
    }

    if (linesMapZones == null || linesMapZones.isEmpty()) {

      log.error("lineMapZones null or empty");
      return;
    }

    PDFTextStripperByArea pdfTextStripperByArea;
    Map<String, MapZone> lineMapZonesByCode;

    try {

      MapZone keyMapZone = null;

      lineMapZonesByCode = new HashMap<String, MapZone>();

      for (MapZone mapZone : linesMapZones) {

        if (mapZone.isLineField()) {

          if (mapZone.isLineKeyField()) {

            keyMapZone = mapZone;
          }

          lineMapZonesByCode.put(mapZone.getCode(), mapZone);
        }
      }

      if (keyMapZone == null) {

        log.error("No line key field defined");
        return;
      }

      Rectangle searchZone = keyMapZone.getSearchZone();

      if (searchZone == null) {

        log.error("No search zone for mapZone [code={}]", keyMapZone.getCode());
        return;
      }

      pdfTextStripperByArea = getNewPDFTextStripperByArea();

      pdfTextStripperByArea
          .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
      log.trace("suppressDuplicateOverlappingText=" + (suppressDuplicateOverlappingText ? "true" : "false"));

      String lineKeyFieldRowRegionName = "lineKeyFieldRow";

      pdfTextStripperByArea.addRegion(lineKeyFieldRowRegionName,
          getRectangleWithRatio(searchZone));

      pdfTextStripperByArea.extractRegions(pdPage);

      List<String> regions = pdfTextStripperByArea.getRegions();
      String lineSeparator = pdfTextStripperByArea.getLineSeparator();

      if (log.isTraceEnabled()) {

        for (String regionName : regions) {

          log.trace("Region {} : [{}]", regionName,
              pdfTextStripperByArea.getTextForRegion(regionName));
        }
      }

      String extractedText = pdfTextStripperByArea
          .getTextForRegion(lineKeyFieldRowRegionName);

      StringTokenizer stringTokenizer = new StringTokenizer(extractedText, lineSeparator);

      List<String> keyFieldTexts = new ArrayList<String>();

      String keyFieldText;

      while (stringTokenizer.hasMoreTokens()) {

        keyFieldText = stringTokenizer.nextToken()
            .trim();

        if (StringUtils.isNotBlank(keyFieldText)) {

          keyFieldTexts.add(keyFieldText);
        }
      }

      if (log.isTraceEnabled()) {

        for (String keyField : keyFieldTexts) {

          log.trace("KeyField : [{}]", keyField);
        }
      }

      Map<String, Rectangle> foundZoneByFieldText = findKeyFields(pageItem, keyFieldTexts,
          keyMapZone.getDefinedZone());

      if (foundZoneByFieldText == null || foundZoneByFieldText.isEmpty()) {

        log.trace("Aucune ligne article trouvée");
        return;
      }

      log.trace("{} lignes articles trouvées", foundZoneByFieldText.size());

      Rectangle relativeBounds;

      Map<String, Map<String, Rectangle>> lineRegionBoundsByFoundKeyText = new LinkedHashMap<String, Map<String, Rectangle>>();

      Map<String, Rectangle> regionBoundsByFieldCode;

      Map<String, Integer> lineNumberByKeyText = new HashMap<String, Integer>();
      Integer lineNumber = 0;

      // Parcours des champs clé <=> parcours des lignes
      for (Entry<String, Rectangle> entry : foundZoneByFieldText.entrySet()) {

        log.trace("Ligne avec champs clé [{}] :", entry.getKey());

        lineNumberByKeyText.put(entry.getKey(), lineNumber);

        regionBoundsByFieldCode = new HashMap<String, Rectangle>();

        // Parcours des autres champs de lignes articles (non clé)
        for (MapZone mapZone : linesMapZones) {

          if (mapZone.isLineKeyField()) {

            continue;
          }

          log.trace("\tRecherche de la zone {}", mapZone.getCode());

          keyMapZone.setFoundZone(entry.getValue());

          relativeBounds = getRelativeBounds(keyMapZone, mapZone);

          regionBoundsByFieldCode.put(mapZone.getCode(), relativeBounds);

          log.trace("\t\tLimites relatives : [x={}][y={}][width={}][height={}]",
              relativeBounds.x, relativeBounds.y, relativeBounds.width,
              relativeBounds.height);
        }

        lineRegionBoundsByFoundKeyText.put(entry.getKey(), regionBoundsByFieldCode);

        lineNumber++;
      }

      pdfTextStripperByArea = getNewPDFTextStripperByArea();

      pdfTextStripperByArea
          .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
      log.trace("suppressDuplicateOverlappingText=" + (suppressDuplicateOverlappingText ? "true" : "false"));

      lineNumber = 0;
      Map<Integer, Map<String, Rectangle>> foundBoundsByCodeAndLineNumber = new TreeMap<Integer, Map<String, Rectangle>>();
      Map<String, Rectangle> foundBoundByCode;

      for (Entry<String, Map<String, Rectangle>> entry : lineRegionBoundsByFoundKeyText
          .entrySet()) {

        foundBoundByCode = new HashMap<String, Rectangle>();
        foundBoundsByCodeAndLineNumber.put(lineNumber, foundBoundByCode);

        log.trace("Ajout des regions pour la ligne {}", entry.getKey());

        for (Entry<String, Rectangle> entry2 : entry.getValue()
            .entrySet()) {

          log.trace("\tajout de {}={}", entry.getKey() + "__" + entry2.getKey(),
              entry2.getValue());
          pdfTextStripperByArea.addRegion(entry.getKey() + "__" + entry2.getKey(),
              entry2.getValue());

          foundBoundByCode.put(entry2.getKey(), entry2.getValue());
        }

        lineNumber++;
      }

      pdfTextStripperByArea.extractRegions(pdPage);

      regions = pdfTextStripperByArea.getRegions();

      linesDatasCodesAndValues = new LinkedHashMap<String, Map<String, String>>();
      Map<Integer, Map<String, String>> codesAndValuesByLineNumber = new TreeMap<Integer, Map<String, String>>();

      int indexOfSeparator;
      String lineKeyText;
      String fieldCode;
      String fieldValue;
      Map<String, String> lineFieldValueByCode;

      List<Donnee> datas;

      linesDatas = new TreeMap<Integer, List<Donnee>>();

      for (String regionName : regions) {

        indexOfSeparator = regionName.indexOf("__");
        lineKeyText = regionName.substring(0, indexOfSeparator);
        fieldCode = regionName.substring(indexOfSeparator + 2, regionName.length());
        fieldValue = pdfTextStripperByArea.getTextForRegion(regionName)
            .trim();

        if (!linesDatasCodesAndValues.containsKey(lineKeyText)) {

          lineFieldValueByCode = new HashMap<String, String>();
          lineFieldValueByCode.put(keyMapZone.getCode(), lineKeyText);

          log.trace("Clé extraite : [{}={}]", keyMapZone.getCode(), lineKeyText);

          linesDatasCodesAndValues.put(lineKeyText, lineFieldValueByCode);
        }

        lineNumber = lineNumberByKeyText.get(lineKeyText);

        if (lineNumber != null) {

          if (!linesDatas.containsKey(lineNumber)) {

            linesDatas.put(lineNumber, new ArrayList<Donnee>());
          }

          Donnee donnee = new Donnee();
          donnee.setCode(fieldCode);
          donnee.setValue(fieldValue);
          donnee.setTypedonnee(Donnee.getTypeOfLineFieldCode(fieldCode));

          linesDatas.get(lineNumber)
              .add(donnee);
        }

        linesDatasCodesAndValues.get(lineKeyText)
            .put(fieldCode, fieldValue);

        log.trace("Region extraite : [{}={}]", regionName, fieldValue);
      }

      for (Entry<String, Map<String, String>> resultEntry : linesDatasCodesAndValues
          .entrySet()) {

        log.trace("Ligne {}", resultEntry.getKey());

        lineNumber = lineNumberByKeyText.get(resultEntry);

        for (Entry<String, String> fieldEntry : resultEntry.getValue()
            .entrySet()) {

          log.trace("\t{}={}", fieldEntry.getKey(), fieldEntry.getValue());
        }
      }

      for (Entry<Integer, List<Donnee>> lineEntry : linesDatas.entrySet()) {

        log.trace("Ligne {}", lineEntry.getKey());

        for (Donnee donnee : lineEntry.getValue()) {

          log.trace("\t[code={}][type={}][value={}]", donnee.getCode(),
              donnee.getTypedonnee(), donnee.getValue());
        }
      }

    }
    catch (IOException e) {

      log.error("IO Exception : {}", e.getMessage(), e);
    }
  }

  private void extractMandatoryDatas(PDDocument pdDocument, List<PdfPagePattern> pdfPagePatterns,
      boolean suppressDuplicateOverlappingText)
      throws TemplateDoesNotHandleMultiPageDocumentsException {

    if (!areThosesParamsOk(pdDocument, pageItems, pdfPagePatterns)) {

      // Les erreurs sont remontées par areThosesParamsOk
      return;
    }

    int pdfDocumentPageCount = pdDocument.getNumberOfPages();
    int pageItemsCount = pageItems.size();

    boolean pdfDocumentHasOnlyOnePage = pdfDocumentPageCount < 2;
    boolean templateHandleMultiPageDocuments = pdfPagePatterns.size() > 1;

    if (pdfDocumentHasOnlyOnePage) {

      // Le document PDF ne comporte qu'une seule page

      // On extrait les données de l'unique page avec ce qui est défini pour la première page
      // du template
      extractMandatoryDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
          suppressDuplicateOverlappingText);

      if (templateHandleMultiPageDocuments) {

        // Si le template a été défini avec plusieurs pages, on fait une recherche dans
        // l'unique page du document avec ce qui a été définie pour la dernière page dans le
        // template
        PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);
        extractMandatoryDatas(pdDocument.getPage(pdfDocumentPageCount - 1),
            pageItems.get(pageItemsCount - 1), lastPdfPagePattern,
            suppressDuplicateOverlappingText);
      }

    }
    else {

      // Le document PDF comporte au moins deux pages
      if (!templateHandleMultiPageDocuments) {

        throw new TemplateDoesNotHandleMultiPageDocumentsException(
            "Document have " + pdDocument.getNumberOfPages() + " pages");
      }

      // On extrait respectivement les données de la première et dernière page du document
      // avec ce qui a été définie pour la première et la dernières page au niveau du template

      extractMandatoryDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
          suppressDuplicateOverlappingText);

      PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);
      extractMandatoryDatas(pdDocument.getPage(pdfDocumentPageCount - 1),
          pageItems.get(pageItemsCount - 1), lastPdfPagePattern,
          suppressDuplicateOverlappingText);
    }
  }

  private void extractMandatoryDatas(PDPage pdPage, PageItem pageItem,
      PdfPagePattern pdfPagePattern, boolean suppressDuplicateOverlappingText) {

    log.trace("Extraction des données obligatoires pour la page {}",
        pdfPagePattern.getPageNumber());

    if (!areThoseParamsOkWithMapZones(pdPage, pageItem, pdfPagePattern)) {

      return;
    }

    PDFTextStripperByArea pdfTextStripperByArea;

    try {

      pdfTextStripperByArea = getNewPDFTextStripperByArea();
      pdfTextStripperByArea
          .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
      log.trace("suppressDuplicateOverlappingText=" + (suppressDuplicateOverlappingText ? "true" : "false"));

      List<MapZone> mapZones = new ArrayList<MapZone>();

      for (MapZone mapZone : pdfPagePattern.getMapZones()) {

        if (mapZone.isObligatoire()) {

          mapZones.add(mapZone);
        }
      }

      addRegions(pdfTextStripperByArea, mapZones, pdfPagePattern.getOrientation());
      pdfTextStripperByArea.extractRegions(pdPage);

      // extractFromRegions(pdfTextStripperByArea.getRegions(), pdfTextStripperByArea,
      // pdfPagePattern);

    }
    catch (IOException e) {

      log.error("Exception d'entrée/sortie : {}", e.getMessage(), e);
    }
  }

  private void extractRelativeDatas(PDDocument pdDocument, List<PageItem> pageItems,
      List<PdfPagePattern> pdfPagePatterns, boolean suppressDuplicateOverlappingText)
      throws TemplateDoesNotHandleMultiPageDocumentsException {

    if (!areThosesParamsOk(pdDocument, pageItems, pdfPagePatterns)) {

      // Les erreurs sont remontées par areThosesParamsOk
      return;
    }

    int pdfDocumentPageCount = pdDocument.getNumberOfPages();
    int pageItemsCount = pageItems.size();

    boolean pdfDocumentHasOnlyOnePage = pdfDocumentPageCount < 2;
    boolean templateHandleMultiPageDocuments = pdfPagePatterns.size() > 1;

    PdfPagePattern firstPdfPagePattern = pdfPagePatterns.get(0);
    PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);

    if (log.isTraceEnabled()) {

      if (firstPdfPagePattern != null) {

        log.trace("Orientation de la première page : " + getPdfPageOrientation(firstPdfPagePattern));
      }

      if (lastPdfPagePattern != null) {

        log.trace("Orientation de la dernière page : " + getPdfPageOrientation(lastPdfPagePattern));
      }
    }

    if (pdfDocumentHasOnlyOnePage) {

      // Le document PDF ne comporte qu'une seule page

      // On extrait les données de l'unique page avec ce qui est défini pour la première page
      // du template
      extractRelativeDatas(pdDocument.getPage(0), pageItems.get(0), firstPdfPagePattern,
          suppressDuplicateOverlappingText);

      if (templateHandleMultiPageDocuments) {

        // Si le template a été défini avec plusieurs pages, on fait, en plus, une recherche
        // dans l'unique page du document avec ce qui a été définie pour la dernière page
        // dans le template
        extractRelativeDatas(pdDocument.getPage(pdfDocumentPageCount - 1),
            pageItems.get(pageItemsCount - 1), lastPdfPagePattern,
            suppressDuplicateOverlappingText);
      }

    }
    else {

      // Le document PDF comporte au moins deux pages
      if (!templateHandleMultiPageDocuments) {

        throw new TemplateDoesNotHandleMultiPageDocumentsException(
            "Document have " + pdDocument.getNumberOfPages() + " pages");
      }

      // On extrait respectivement les données de la première et dernière page du document
      // avec ce qui a été définie pour la première et la dernières page au niveau du template

      extractRelativeDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
          suppressDuplicateOverlappingText);

      extractRelativeDatas(pdDocument.getPage(pdfDocumentPageCount - 1),
          pageItems.get(pageItemsCount - 1), lastPdfPagePattern,
          suppressDuplicateOverlappingText);

      log.trace("Recherche de données de pieds de page non trouvées...");
      List<MapZone> unfoundMultipageFooterMapZones = getUnfoundMultipageMapZones(
          lastPdfPagePattern);

      if (unfoundMultipageFooterMapZones != null && !unfoundMultipageFooterMapZones.isEmpty()) {

        log.trace("des données de pieds de page non trouvées existent...");
        PdfPagePattern newPdfPagePattern = lastPdfPagePattern
            .getCloneWithOnlyUnfoundMapZones();

        extractRelativeDatas(pdDocument.getPage(pdfDocumentPageCount - 2),
            pageItems.get(pageItemsCount - 2), newPdfPagePattern,
            suppressDuplicateOverlappingText);
      }

      log.trace("Recherche de données d'en-tête non trouvées...");
      List<MapZone> unfoundMultipageHeaderMapZones = getUnfoundMultipageMapZones(
          firstPdfPagePattern);

      if (unfoundMultipageHeaderMapZones != null && !unfoundMultipageHeaderMapZones.isEmpty()) {

        log.trace("des données d'en-tête non trouvées existent...");
      }
    }
  }

  private void extractRelativeDatas(PDPage pdPage, PageItem pageItem,
      PdfPagePattern pdfPagePattern, boolean suppressDuplicateOverlappingText) {

    if (pageItem == null) {

      log.error("pageItem null");
      return;
    }

    if (pdfPagePattern == null) {

      log.error("pdfPagePattern null");
      return;
    }

    log.trace("Extraction des données relatives pour la page {}",
        pdfPagePattern.getPageNumber());

    // recherche des libellées sur la page
    findLabelMapZone(pageItem, pdfPagePattern);

    Map<String, MapZone> labelMapZonesByNames = pdfPagePattern.getLabelMapZonesByNames();

    if (labelMapZonesByNames == null) {

      log.trace("aucune zone de label statique défini pour la page {}",
          pdfPagePattern.getPageNumber());
      return;
    }

    List<MapZone> relativeMapZones = pdfPagePattern.getRelativeMapZones();

    if (relativeMapZones == null) {

      log.error("relativeMapZones null pour le template de la page {} ({})",
          pdfPagePattern.getPageNumber(), pdfPagePattern.toString());
      return;
    }

    try {

      PDFTextStripperByArea pdfTextStripperByArea = getNewPDFTextStripperByArea();
      pdfTextStripperByArea
          .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
      log.trace("suppressDuplicateOverlappingText=" + (suppressDuplicateOverlappingText ? "true" : "false"));

      MapZone referenceMapZone;

      // recherche des mapZone liées aux libellés
      for (MapZone relativeMapZone : relativeMapZones) {

        referenceMapZone = relativeMapZone.getReferenceMapZone();

        if (referenceMapZone != null) {

          String referenceName = referenceMapZone.getCode();
          String relativeName = relativeMapZone.getCode();

          if (labelMapZonesByNames.containsKey(referenceName)) {

            Rectangle relativeFindBounds = getRelativeBounds(
                labelMapZonesByNames.get(referenceName), relativeMapZone);

            // /!\ ici on renseigne un rectangle déjà mis au ratio
            relativeMapZone.setFoundZone(relativeFindBounds);

            pdfTextStripperByArea.addRegion(relativeName, relativeFindBounds);

            // TODO: n'a pas d'impact car on ne devrait pas avoir deux zones avec le
            // même nom dans un template, mais devrait peut-être être ajouté dans une
            // liste au niveau pdfPagePattern au lieu d'être au niveau du PdfExtractor
            addRegionBoundByName(relativeName, relativeFindBounds);

            log.trace("Zone relative trouvée : {}", relativeMapZone.toLogString());

          }
          else {

            log.warn("Pas de mapZone libellé trouvé pour [name={}]",
                referenceMapZone.getCode());

            log.debug("Liste des libellés trouvés :");
            for (Entry<String, MapZone> entry : labelMapZonesByNames.entrySet()) {

              log.debug("\t" + entry.getKey());
            }
          }
        }
      }

      pdfTextStripperByArea.extractRegions(pdPage);

      List<String> regions = pdfTextStripperByArea.getRegions();

      log.trace("Extraction regions des zones relatives...");
      extractFromRegions(regions, pdfTextStripperByArea, pdfPagePattern);
      log.trace("...fin extraction regions des zones relatives");

    }
    catch (IOException e) {

      log.error("Exception d'entrée/sortie : {}", e.getMessage(), e);
    }
  }

  private void fillBeadRectangles(PDPage page) {
    beadRectangles = new ArrayList<PDRectangle>();

    log.trace(page.getThreadBeads()
        .size() + " beads");

    for (PDThreadBead bead : page.getThreadBeads()) {
      if (bead == null) {
        // can't skip, because of null entry handling in
        // processTextPosition()
        beadRectangles.add(null);
        continue;
      }

      PDRectangle beadRectangle = bead.getRectangle();

      // bead rectangle is in PDF coordinates (y=0 is bottom),
      // glyphs are in image coordinates (y=0 is top),
      // so we must flip
      PDRectangle mediaBox = page.getMediaBox();
      float upperRightY = mediaBox.getUpperRightY() - beadRectangle.getLowerLeftY();
      float lowerLeftY = mediaBox.getUpperRightY() - beadRectangle.getUpperRightY();
      beadRectangle.setLowerLeftY(lowerLeftY);
      beadRectangle.setUpperRightY(upperRightY);

      PdfBoxUtil.logPDRectangle(beadRectangle, log, "Bead Rectangle");

      // adjust for cropbox
      PDRectangle cropBox = page.getCropBox();

      PdfBoxUtil.logPDRectangle(beadRectangle, log, "CropBox");

      if (cropBox.getLowerLeftX() != 0 || cropBox.getLowerLeftY() != 0) {
        beadRectangle
            .setLowerLeftX(beadRectangle.getLowerLeftX() - cropBox.getLowerLeftX());
        beadRectangle
            .setLowerLeftY(beadRectangle.getLowerLeftY() - cropBox.getLowerLeftY());
        beadRectangle
            .setUpperRightX(beadRectangle.getUpperRightX() - cropBox.getLowerLeftX());
        beadRectangle
            .setUpperRightY(beadRectangle.getUpperRightY() - cropBox.getLowerLeftY());
      }

      beadRectangles.add(beadRectangle);
    }
  }

  /**
   * @param pageItem Page ou rechercher les lignes
   * @param keyFieldTexts Textes a recherche
   * @return
   */
  private Map<String, Rectangle> findKeyFields(PageItem pageItem, List<String> keyFieldTexts,
      Rectangle definedBounds) {

    List<String> keysInLine;
    Rectangle foundBounds;

    Map<String, Rectangle> foundZoneByFieldText = new LinkedHashMap<String, Rectangle>();

    // Parcours des lignes
    for (LineItem lineItem : pageItem.getLineItems()) {

      // Recherche des champs clés dans la ligne
      keysInLine = getKeysInLine(new HashSet<String>(keyFieldTexts), lineItem);

      if (keysInLine == null || keysInLine.isEmpty()) {

        continue;
      }

      // Même si plusieurs itération du champs sont trouvées, on prend la première
      String foundKey = keysInLine.get(0);

      foundBounds = lineItem.getBoundsOfSubText(foundKey);

      if (foundBounds == null) {

        log.warn("Limites non trouvés pour [key=" + foundKey + "] dans [lineItem=" + lineItem.getText() + "]");
        continue;
      }

      log.trace("Defined Bounds of \t[" + foundKey + "][X=" + definedBounds.x + "][Y=" + definedBounds.y + "][width=" +
          definedBounds.width + "][height=" + definedBounds.height + "]");

      log.trace("Raw Bounds of \t[" + foundKey + "][X=" + foundBounds.x + "][Y=" + foundBounds.y + "][width=" + foundBounds.width +
          "][height=" + foundBounds.height + "]");

      foundBounds.x = definedBounds.x;
      foundBounds.width = definedBounds.width;

      log.trace("Correct Bounds of \t[" + foundKey + "][X=" + foundBounds.x + "][Y=" + foundBounds.y + "][width=" + foundBounds.width +
          "][height=" + foundBounds.height + "]");

      foundZoneByFieldText.put(foundKey, foundBounds);
    }

    return foundZoneByFieldText;
  }

  /**
   * Recherche la liste des libellés statiques et les renseigne dans la liste labelMapZoneByName du template de page (accessible via
   * {@link PdfPagePattern#getLabelMapZonesByNames()})
   *
   * @param pageItem Page dans laquelle rechercher les libellés
   * @param pdfPagePattern Template de page a utiliser
   */
  private void findLabelMapZone(PageItem pageItem, PdfPagePattern pdfPagePattern) {

    if (pageItem == null) {

      log.error("pageItem null");
      return;
    }

    if (pdfPagePattern == null) {

      log.error("pdfPagePattern null");
      return;
    }

    if (pageItem.getLineItems() == null) {

      log.debug("Aucune ligne de texte sur la page");
      return;
    }

    Map<String, List<MapZone>> mapZonesByStaticLabel = pdfPagePattern
        .getMapZonesByStaticLabel();

    if (mapZonesByStaticLabel == null) {

      return;
    }

    List<String> keysInLine;
    Set<String> staticLabels = mapZonesByStaticLabel.keySet();

    Rectangle foundBounds;

    int i = 1;

    // Parcours des lignes
    for (LineItem lineItem : pageItem.getLineItems()) {

      log.trace("Ligne " + StringUtils.leftPad(Integer.toString(i++), 4) + ">" + lineItem.getText());

      // Recherche des libellés statiques dans la ligne
      keysInLine = getKeysInLine(staticLabels, lineItem);

      if (keysInLine == null || keysInLine.isEmpty()) {

        continue;
      }

      // Pour chaque libellé trouvé
      for (String staticLabel : keysInLine) {

        foundBounds = lineItem.getBoundsOfSubText(staticLabel);

        if (foundBounds == null) {

          log.warn("Limites non trouvés pour [staticLabel=" + staticLabel + "] dans [lineItem=" + lineItem.getText() + "]");
          continue;
        }

        log.trace("Bounds of [" + staticLabel + "][X=" + foundBounds.x + "][Y=" + foundBounds.y + "][width=" + foundBounds.width +
            "][height=" + foundBounds.height + "]");

        boolean found = false;

        // Pour chaque mapZone associé au libellé
        for (MapZone mapZone : mapZonesByStaticLabel.get(staticLabel)) {

          if (mapZone.getSearchZone() != null && getRectangleWithRatio(mapZone.getSearchZone())
              .contains(foundBounds)) {

            found = true;
            mapZone.setFoundZone(foundBounds);

            pdfPagePattern.addLabelMapZoneByName(mapZone);

            log.trace("Libellé trouvé page {} : {} ", pageItem.getPageNumber(),
                mapZone.toLogString());
          }
        }

        if (log.isTraceEnabled() && !found) {

          Rectangle rectangleWithRatio;

          for (MapZone mapZone : mapZonesByStaticLabel.get(staticLabel)) {

            log.trace("Aucune des zones suivantes ne correspond a ces limites");
            log.trace("\t[Code=" + mapZone.getCode() + "][Label=" + mapZone.getLabel() + "]");

            if (mapZone.getSearchZone() != null) {

              rectangleWithRatio = getRectangleWithRatio(mapZone.getSearchZone());

              log.trace("\t[X=" + rectangleWithRatio.x + "][Y=" + rectangleWithRatio.y + "][W=" + rectangleWithRatio.width + "][H=" +
                  rectangleWithRatio.height + "]");
            }
          }
        }
      }
    }
  }

  /**
   * Get the current character width-based tolerance value that is being used to estimate where spaces in text should be added. Note that
   * the default value for this has been determined from trial and error.
   *
   * @return The current tolerance / scaling factor
   */
  public float getAverageCharTolerance() {
    return averageCharTolerance;
  }

  // private void extractMandoryDatas(PDDocument pdDocument, List<PageItem> pageItems,
  // List<PdfPagePattern> pdfPagePatterns, boolean suppressDuplicateOverlappingText)
  // throws TemplateDoesNotHandleMultiPageDocumentsException {
  //
  // if (!areThosesParamsOk(pdDocument, pageItems, pdfPagePatterns)) {
  //
  // // Les erreurs sont remontées par areThosesParamsOk
  // return;
  // }
  //
  // boolean pdfDocumentHasOnlyOnePage = pdDocument.getNumberOfPages() < 2;
  // boolean templateHandleMultiPageDocuments = pdfPagePatterns.size() > 1;
  //
  // if (pdfDocumentHasOnlyOnePage) {
  //
  // // Le document PDF ne comporte qu'une seule page
  //
  // // On extrait les données de l'unique page avec ce qui est défini pour la première page
  // // du template
  // extractMandatoryDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
  // suppressDuplicateOverlappingText);
  //
  // if (templateHandleMultiPageDocuments) {
  //
  // // Si le template a été défini avec plusieurs pages, on fait une recherche dans
  // // l'unique page du document avec ce qui a été définie pour la dernière page dans le
  // // template
  // PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);
  // extractMandatoryDatas(pdDocument.getPage(0), pageItems.get(0), lastPdfPagePattern,
  // suppressDuplicateOverlappingText);
  // }
  //
  // } else {
  //
  // // Le document PDF comporte au moins deux pages
  // if (!templateHandleMultiPageDocuments) {
  //
  // throw new TemplateDoesNotHandleMultiPageDocumentsException(
  // "Document have " + pdDocument.getNumberOfPages() + " pages");
  // }
  //
  // // On extrait respectivement les données de la première et dernière page du document
  // // avec ce qui a été définie pour la première et la dernières page au niveau du template
  //
  // extractMandatoryDatas(pdDocument.getPage(0), pageItems.get(0), pdfPagePatterns.get(0),
  // suppressDuplicateOverlappingText);
  //
  // PdfPagePattern lastPdfPagePattern = pdfPagePatterns.get(pdfPagePatterns.size() - 1);
  // extractMandatoryDatas(pdDocument.getPage(0), pageItems.get(0), lastPdfPagePattern,
  // suppressDuplicateOverlappingText);
  // }
  // }

  /**
   * Character strings are grouped by articles. It is quite common that there will only be a single article. This returns a List that
   * contains List objects, the inner lists will contain TextPosition objects.
   *
   * @return A double List of TextPositions for all text strings on the page.
   */
  protected List<List<TextPosition>> getCharactersByArticle() {
    return charactersByArticle;
  }

  // private void extractRelativeDatas(PDDocument pdDocument, List<PdfPagePattern>
  // pdfPagePatterns,
  // boolean suppressDuplicateOverlappingText) {
  //
  // if (relativeMapZones == null || relativeMapZones.isEmpty()) {
  //
  // log.warn("Aucune mapZone relative");
  // return;
  // }
  //
  // log.trace("{} mapZones relatives", relativeMapZones.size());
  //
  // setSortByPosition(true);
  //
  // try {
  //
  // PDFTextStripperByArea pdfTextStripperByArea = getNewPDFTextStripperByArea();
  // pdfTextStripperByArea
  // .setSuppressDuplicateOverlappingText(suppressDuplicateOverlappingText);
  // log.trace("suppressDuplicateOverlappingText="
  // + (suppressDuplicateOverlappingText ? "true" : "false"));
  //
  // // Lecture du PDF et construction des objets nécessaires à la
  // // recherche textuelle
  // // processDocument(pdDocument);
  //
  // // recherche des libellées
  // findLabelMapZone(pageItems, pdfPagePatterns);
  //
  // // recherche des mapZone liées aux libellés
  // for (MapZone relativeMapZone : relativeMapZones) {
  //
  // if (relativeMapZone.getReferenceMapZone() != null) {
  //
  // String referenceName = relativeMapZone.getReferenceMapZone().getCode();
  // String relativeName = relativeMapZone.getCode();
  //
  // if (labelMapZonesByNames.containsKey(referenceName)) {
  //
  // Rectangle relativeFindBounds = getRelativeBounds(
  // labelMapZonesByNames.get(referenceName), relativeMapZone);
  //
  // // /!\ ici on renseigne un rectangle déjà mis au ratio
  // relativeMapZone.setFoundZone(relativeFindBounds);
  //
  // pdfTextStripperByArea.addRegion(relativeName, relativeFindBounds);
  //
  // addRegionBoundByName(relativeName, relativeFindBounds);
  //
  // log.trace("Zone relative trouvée : " + relativeMapZone.toLogString());
  //
  // } else {
  //
  // log.warn("Pas de mapZone libelle trouvé pour [name="
  // + relativeMapZone.getReferenceMapZone().getCode() + "]");
  //
  // for (Entry<String, MapZone> entry : labelMapZonesByNames.entrySet()) {
  //
  // log.warn("\t" + entry.getKey());
  // }
  // }
  //
  // }
  // }
  //
  // pdfTextStripperByArea.extractRegions(pdDocument.getPage(0));
  //
  // List<String> regions = pdfTextStripperByArea.getRegions();
  //
  // log.trace("Extraction regions des zones relatives...");
  // extractFromRegions(regions, pdfTextStripperByArea, pdfPagePatterns.get(0));
  // log.trace("...fin extraction regions des zones relatives");
  //
  // } catch (IOException e) {
  //
  // log.error("Erreur à l'analyse du PDF : " + e.getMessage(), e);
  // }
  // }

  /**
   * Get the current page number that is being processed.
   *
   * @return A 1 based number representing the current page.
   */
  protected int getCurrentPageNo() {
    return currentPageNo;
  }

  private Integer getDataTypeForCode(String code) {

    if (datasTypesByCode == null || datasTypesByCode.isEmpty()) {

      loadDatasTypesByCodes();

      if (datasTypesByCode == null || datasTypesByCode.isEmpty()) {

        return null;
      }
    }

    return datasTypesByCode.get(code);
  }

  // private void findLabelMapZone(List<PageItem> pageItems, List<PdfPagePattern> pdfPagePatterns)
  // {
  //
  // List<String> keysInLine;
  //
  // Set<String> staticLabels = mapZonesByStaticLabel.keySet();
  //
  // Rectangle foundBounds;
  //
  // // Parcours des pages
  // for (PageItem pageItem : pageItems) {
  //
  // // Parcours des lignes
  // for (LineItem lineItem : pageItem.getLineItems()) {
  //
  // // Recherche des libellés statiques dans la ligne
  // keysInLine = getKeysInLine(staticLabels, lineItem);
  //
  // if (keysInLine == null || keysInLine.isEmpty()) {
  //
  // continue;
  // }
  //
  // // Pour chaque libellé trouvé
  // for (String staticLabel : keysInLine) {
  //
  // foundBounds = lineItem.getBoundsOfSubText(staticLabel);
  //
  // if (foundBounds == null) {
  //
  // log.warn("Limites non trouvés pour [staticLabel=" + staticLabel
  // + "] dans [lineItem=" + lineItem.getText() + "]");
  // continue;
  // }
  //
  // // log.trace("Bounds of [" + staticLabel + "][X=" + foundBounds.x + "][Y="
  // // + foundBounds.y + "][width=" + foundBounds.width + "][height="
  // // + foundBounds.height + "]");
  //
  // // Pour chaque mapZone associé au libellé
  // for (MapZone mapZone : mapZonesByStaticLabel.get(staticLabel)) {
  //
  // if (mapZone.getSearchZone() != null
  // && getRectangleWithRatio(mapZone.getSearchZone())
  // .contains(foundBounds)) {
  //
  // mapZone.setFoundZone(foundBounds);
  //
  // addLabelMapZoneByName(mapZone);
  //
  // log.trace("Libellé trouvé : " + mapZone.toLogString());
  // }
  // }
  // }
  // }
  // }
  // }

  private Donnee getDonnee(String regionName, MapZone mapZone, String extractedText) {

    String stringPattern;
    String datePattern = null;
    String amountPattern = null;
    String transcodingText;

    Pattern pattern;
    Matcher matcher;

    Integer mapZoneType = getDataTypeForCode(mapZone.getCode());

    log.trace("Type trouvé pour le code {} : {}", mapZone.getCode(),
        Donnee.getStringType(mapZoneType));

    if (mapZoneType != null && !mapZoneType.equals(mapZone.getType())) {

      log.warn("Donnée [code={}] renseigné au type {} dans le modèle au lieu du type {}",
          mapZone.getCode(), Donnee.getStringType(mapZone.getType()),
          Donnee.getStringType(mapZoneType));

      mapZone.setType(mapZoneType);
    }

    if (mapZone.isDeleteSpaces()) {

      // https://stackoverflow.com/questions/26123271/replace-spaces-in-string-with-regex-in-java#answer-26123870
      // http://www.fileformat.info/info/unicode/category/Zs/list.htm
      extractedText = extractedText.replaceAll("[\\p{Zs}\\s]+", "");
    }

    // Test si une expression regulière est renseignée
    if ((stringPattern = mapZone.getPattern()) != null && stringPattern.startsWith("e_")) {

      stringPattern = stringPattern.substring(2, stringPattern.length());

      if (stringPattern.contains("_")) {

        if (mapZone.getType() == Donnee.TYPE_DATE) {

          datePattern = DonneeHelper.getSanitizedDatePattern(stringPattern
              .substring(stringPattern.indexOf('_') + 1, stringPattern.length()));

          stringPattern = stringPattern.substring(0, stringPattern.indexOf('_'));

        }
        else if (mapZone.getType() == Donnee.TYPE_REAL) {

          amountPattern = stringPattern.substring(stringPattern.indexOf("_") + 1,
              stringPattern.length());

          stringPattern = stringPattern.substring(0, stringPattern.indexOf('_'));
        }
      }

      // prefixer les expressions régulière avec (?s) pour gèrer plusieurs lignes
      // pattern = Pattern.compile(stringPattern, Pattern.MULTILINE);
      pattern = Pattern.compile(stringPattern);
      matcher = pattern.matcher(extractedText);

      if (matcher.matches()) {

        log.trace("[{}] est conforme au pattern [{}] : {} groupes trouvés", extractedText,
            stringPattern, matcher.groupCount());

        if (matcher.groupCount() < 2) {

          if (matcher.groupCount() > 0) {

            extractedText = matcher.group(1);

          }
          else {

            extractedText = matcher.group(0);
          }

        }
        else {

          StringBuilder stringBuilder = new StringBuilder();

          for (int groupIndex = 1; groupIndex <= matcher.groupCount(); groupIndex++) {

            stringBuilder.append(matcher.group(groupIndex));
          }

          extractedText = stringBuilder.toString();
        }

        log.trace("Donnée extraite : [{}]", extractedText);

      }
      else {

        log.debug("[{}] non conforme au pattern [{}]", extractedText, stringPattern);
        extractedText = "";
      }
    }

    Donnee donnee = new Donnee();
    donnee.setCode(regionName);
    donnee.setTypedonnee(mapZone.getType());

    if (mapZone.getType() == Donnee.TYPE_DATE) {

      // datePattern a pu être alimenté avant si le pattern contenait une expression
      // régulière
      donnee.setPattern(datePattern != null ? datePattern
          : DonneeHelper.getSanitizedDatePattern(mapZone.getPattern()));

      if (donnee.getPattern()
          .contains("MMMM") && getTranscodingHelper() != null) {

        extractedText = getTranscodingHelper().replaceMonths(extractedText.toLowerCase());
      }

    }
    else if (getTranscodingHelper() != null) {

      transcodingText = getTranscodingHelper().getTranscoding(regionName, extractedText);

      if (transcodingText != null) {

        extractedText = transcodingText;
      }

    }
    else {

      log.warn("TranscodingHelper null");
    }

    extractedText = extractedText.replaceAll("(\\r\\n|\\r|\\n)", " ");

    if (mapZone.getType() == Donnee.TYPE_REAL) {

      if (mapZone.isAbsoluteValueRequired()) {

        extractedText = extractedText.replaceAll("[^0-9\\,\\.]", "");

      }
      else {

        extractedText.replaceAll("[^0-9\\,\\.\\-]", "");
      }

      if (StringUtils.isNotBlank(amountPattern)) {

        donnee.setPattern(amountPattern);

      }
      else if (StringUtils.isNotBlank(mapZone.getPattern()) && !mapZone.getPattern()
          .startsWith("e_")) {

            donnee.setPattern(mapZone.getPattern());

          }

      if (StringUtils.isBlank(donnee.getPattern())) {

        donnee.setPattern(Donnee.DEFAULT_INPUT_AMOUNT_PATTERN);
      }
    }

    donnee.setValue(extractedText);

    donnee.setPartnerKey(mapZone.isPartnerKey());
    donnee.setCorporateKey(mapZone.isCorporateKey());
    donnee.setPosition(getRegionBoundByName(regionName));

    // Indique si la mapZone a été trouvée
    mapZone.setFound(StringUtils.isNotBlank(donnee.getValue()));

    if (donnee.getTypedonnee() == Donnee.TYPE_DATE) {

      donnee.convertToDefaultDatePattern();

    }
    else if (donnee.getTypedonnee() == Donnee.TYPE_REAL) {

      donnee.convertToDefaultAmountPattern();
    }

    return donnee;
  }

  private DonneeHelper getDonneeHelper() {

    if (donneeHelper == null) {

      donneeHelper = new DonneeHelper();
    }

    return donneeHelper;
  }

  public List<Donnee> getDonnees() {

    if (extractedDatasByCode == null) {

      return null;
    }

    return new ArrayList<Donnee>(extractedDatasByCode.values());
  }

  /**
   * the minimum whitespace, as a multiple of the max height of the current characters beyond which the current line start is considered to
   * be a paragraph start.
   *
   * @return the character height multiple for max allowed whitespace between lines in the same paragraph.
   */
  public float getDropThreshold() {
    return dropThreshold;
  }

  /**
   * Get the bookmark where text extraction should end, inclusive. Default is null.
   *
   * @return The ending bookmark.
   */
  public PDOutlineItem getEndBookmark() {
    return endBookmark;
  }

  /**
   * This will get the last page that will be extracted. This is inclusive, for example if a 5 page PDF an endPage value of 5 would extract
   * the entire document, an end page of 2 would extract pages 1 and 2. This defaults to Integer.MAX_VALUE such that all pages of the pdf
   * will be extracted.
   *
   * @return Value of property endPage.
   */
  public int getEndPage() {
    return endPage;
  }

  /**
   * returns the multiple of whitespace character widths for the current text which the current line start can be indented from the previous
   * line start beyond which the current line start is considered to be a paragraph start.
   *
   * @return the number of whitespace character widths to use when detecting paragraph indents.
   */
  public float getIndentThreshold() {
    return indentThreshold;
  }

  public Map<String, Map<String, String>> getLinesDatas() {

    return linesDatasCodesAndValues;
  }

  /**
   * This will get the line separator.
   *
   * @return The desired line separator string.
   */
  public String getLineSeparator() {
    return lineSeparator;
  }

  /**
   * returns a list of regular expression Patterns representing different common list item formats. For example numbered items of form:
   * <ol>
   * <li>some text</li>
   * <li>more text</li>
   * </ol>
   * or
   * <ul>
   * <li>some text</li>
   * <li>more text</li>
   * </ul>
   * etc., all begin with some character pattern. The pattern "\\d+\." (matches "1.", "2.", ...) or "\[\\d+\]" (matches "[1]", "[2]", ...).
   * <p>
   * This method returns a list of such regular expression Patterns.
   *
   * @return a list of Pattern objects.
   */
  protected List<Pattern> getListItemPatterns() {
    if (listOfPatterns == null) {
      listOfPatterns = new ArrayList<Pattern>();
      for (String expression : LIST_ITEM_EXPRESSIONS) {
        Pattern p = Pattern.compile(expression);
        listOfPatterns.add(p);
      }
    }
    return listOfPatterns;
  }

  private PDFTextStripperByArea getNewPDFTextStripperByArea() throws IOException {

    PDFTextStripperByArea pdfTextStripperByArea = new PDFTextStripperByArea();
    pdfTextStripperByArea.setAverageCharTolerance(0.5f);
    pdfTextStripperByArea.setSortByPosition(true);
    // pdfTextStripperByArea.setSuppressDuplicateOverlappingText(false);

    return pdfTextStripperByArea;
  }

  public List<PageItem> getPageItems() {
    return pageItems;
  }

  private String getPdfPageOrientation(PdfPagePattern pdfPagePattern) {

    switch (pdfPagePattern.getOrientation()) {
    case PdfPagePattern.ORIENTATION_PAYSAGE:

      return "Paysage";
    case PdfPagePattern.ORIENTATION_PORTRAIT:

      return "Portrait";

    default:
      return "Inconnu (" + pdfPagePattern.getOrientation() + ")";
    }
  }

  private Rectangle getRectangleWithRatio(Rectangle rectangle) {

    int x = (int) (rectangle.x * RATIO);
    int y = (int) (rectangle.y * RATIO);
    int width = (int) (rectangle.width * RATIO);
    int height = (int) (rectangle.height * RATIO);

    return new Rectangle(x, y, width, height);
  }

  private Rectangle getRegionBoundByName(String name) {

    if (regionBoundsByName == null) {

      return null;
    }

    return regionBoundsByName.get(name);
  }

  /**
   * <p>
   * Retourne les limites de la mapZone relative, à partir des coordonnées d'un rectangle ou a été trouvé la mapZone de référence<br/>
   * <strong>Contrairement à getBoundsFrom(Rectangle reference), la propriété 'foundBounds' de la mapZone de référence doit avoir été
   * renseignée</strong>
   * </p>
   * <p>
   * Prend en compte le ratio via getRectangleWithRatio()
   * </p>
   * <p>
   * Ex : si la mapZone relative 'Rel' est définie comme étant un rectangle de 100x15px, 10px sous la mapZone de référence 'Ref' et 5px à
   * droite et que 'Ref.foundBounds' indique qu'elle a été trouvé en x=5 et y=10, on retournera un rectangle de 100x15 situé aux coordonnées
   * x=10 et y=20
   * </p>
   *
   * @return Limites de la mapZone relative
   */
  public Rectangle getRelativeBounds(MapZone referenceMapZone, MapZone relativeMapZone) {

    if (relativeMapZone.getDefinedZone() == null) {

      log.warn("mapZone relative sans zone définie");
      return null;
    }

    if (referenceMapZone.getFoundBounds() == null) {

      log.warn("foundZone du rectangle de reference null :");
      log.warn(referenceMapZone.toLogString());
      return null;
    }

    Rectangle relativeDefinedBounds = getRectangleWithRatio(relativeMapZone.getDefinedZone());
    Rectangle referenceDefinedBounds = getRectangleWithRatio(referenceMapZone.getDefinedZone());

    // /!\ La zone trouvée est déjà au ratio
    Rectangle referenceFindBounds = referenceMapZone.getFoundBounds();

    Rectangle relativeBounds = new Rectangle();

    relativeBounds
        .setSize(new Dimension(relativeDefinedBounds.width, relativeDefinedBounds.height));

    int newX = referenceFindBounds.x + (relativeDefinedBounds.x - referenceDefinedBounds.x);
    int newY = referenceFindBounds.y + (relativeDefinedBounds.y - referenceDefinedBounds.y);

    relativeBounds.setLocation(newX, newY);

    relativeMapZone.setFoundZone(relativeBounds);

    return relativeBounds;
  }

  /**
   * This will tell if the text stripper should separate by beads.
   *
   * @return If the text will be grouped by beads.
   */
  public boolean getSeparateByBeads() {
    return shouldSeparateByBeads;
  }

  /**
   * This will tell if the text stripper should sort the text tokens before writing to the stream.
   *
   * @return true If the text tokens will be sorted before being written.
   */
  public boolean getSortByPosition() {
    return sortByPosition;
  }

  /**
   * Get the current space width-based tolerance value that is being used to estimate where spaces in text should be added. Note that the
   * default value for this has been determined from trial and error.
   *
   * @return The current tolerance / scaling factor
   */
  public float getSpacingTolerance() {
    return spacingTolerance;
  }

  /**
   * Get the bookmark where text extraction should start, inclusive. Default is null.
   *
   * @return The starting bookmark.
   */
  public PDOutlineItem getStartBookmark() {
    return startBookmark;
  }

  /**
   * This is the page that the text extraction will start on. The pages start at page 1. For example in a 5 page PDF document, if the start
   * page is 1 then all pages will be extracted. If the start page is 4 then pages 4 and 5 will be extracted. The default value is 1.
   *
   * @return Value of property startPage.
   */
  public int getStartPage() {
    return startPage;
  }

  /**
   * @return Returns the suppressDuplicateOverlappingText.
   */
  public boolean getSuppressDuplicateOverlappingText() {
    return suppressDuplicateOverlappingText;
  }

  private TranscodingHelper getTranscodingHelper() {

    if (transcodingHelper == null) {

      transcodingHelper = TranscodingHelper.getInstance(transcodingFilepath);
    }

    return transcodingHelper;
  }

  private List<MapZone> getUnfoundMultipageMapZones(PdfPagePattern pdfPagePattern) {

    if (pdfPagePattern == null) {

      log.warn("pdfPagePattern null");
      return null;
    }

    List<MapZone> mapZones = pdfPagePattern.getMultipageMapZones();

    if (mapZones == null) {

      return null;
    }

    List<MapZone> unfoundMapZones = new ArrayList<MapZone>();

    for (MapZone mapZone : mapZones) {

      if (!mapZone.isFound()) {

        unfoundMapZones.add(mapZone);
      }
    }

    return unfoundMapZones;
  }

  /**
   * This will get the word separator.
   *
   * @return The desired word separator string.
   */
  public String getWordSeparator() {
    return wordSeparator;
  }

  public Document getXmlDocumentOfExtractedDatas() {

    return getDonneeHelper().getXmlDocument(getDonnees(), getLinesDatas());
  }

  // private Map<String, String> getTranscodingMonths() {
  //
  // if (transcodingMonths == null && getTranscodingHelper() != null) {
  //
  // transcodingMonths = getTranscodingHelper().getTranscodingMonths();
  // }
  //
  // return transcodingMonths;
  // }

  // private String getTranscoding(String fieldName, String originalValue) {
  //
  // if (transcodingIni == null) {
  //
  // return null;
  // }
  //
  // if (transcodingIni.get(fieldName) == null) {
  //
  // return null;
  // }
  //
  // return transcodingIni.get(fieldName).get(originalValue.toLowerCase());
  // }

  public String getXmlStringOfExtractedDatas() {

    return getDonneeHelper().getXmlString(getDonnees(), getLinesDatas());
  }

  /**
   * Handles the LTR and RTL direction of the given words. The whole implementation stands and falls with the given word. If the word is a
   * full line, the results will be the best. If the word contains of single words or characters, the order of the characters in a word or
   * words in a line may wrong, due to RTL and LTR marks and characters! Based on http://www.nesterovsky-bros.com/weblog/2013/07/28/
   * VisualToLogicalConversionInJava.aspx
   *
   * @param word The word that shall be processed
   * @return new word with the correct direction of the containing characters
   */
  private String handleDirection(String word) {
    Bidi bidi = new Bidi(word, Bidi.DIRECTION_DEFAULT_LEFT_TO_RIGHT);

    // if there is pure LTR text no need to process further
    if (!bidi.isMixed() && bidi.getBaseLevel() == Bidi.DIRECTION_LEFT_TO_RIGHT) {
      return word;
    }

    // collect individual bidi information
    int runCount = bidi.getRunCount();
    byte[] levels = new byte[runCount];
    Integer[] runs = new Integer[runCount];

    for (int i = 0; i < runCount; i++) {
      levels[i] = (byte) bidi.getRunLevel(i);
      runs[i] = i;
    }

    // reorder individual parts based on their levels
    Bidi.reorderVisually(levels, 0, runs, 0, runCount);

    // collect the parts based on the direction within the run
    StringBuilder result = new StringBuilder();

    for (int i = 0; i < runCount; i++) {
      int index = runs[i];
      int start = bidi.getRunStart(index);
      int end = bidi.getRunLimit(index);

      int level = levels[index];

      if ((level & 1) != 0) {
        while (--end >= start) {
          char character = word.charAt(end);
          if (Character.isMirrored(word.codePointAt(end))) {
            if (MIRRORING_CHAR_MAP.containsKey(character)) {
              result.append(MIRRORING_CHAR_MAP.get(character));
            }
            else {
              result.append(character);
            }
          }
          else {
            result.append(character);
          }
        }
      }
      else {
        result.append(word, start, end);
      }
    }

    return result.toString();
  }

  /**
   * handles the line separator for a new line given the specified current and previous TextPositions.
   *
   * @param current the current text position
   * @param lastPosition the previous text position
   * @param lastLineStartPosition the last text position that followed a line separator.
   * @param maxHeightForLine max height for positions since lastLineStartPosition
   * @return start position of the last line
   * @throws IOException if something went wrong
   */
  private PositionWrapper handleLineSeparation(PositionWrapper current,
      PositionWrapper lastPosition, PositionWrapper lastLineStartPosition,
      float maxHeightForLine) throws IOException {
    current.setLineStart();
    isParagraphSeparation(current, lastPosition, lastLineStartPosition, maxHeightForLine);
    lastLineStartPosition = current;
    if (current.isParagraphStart()) {
      if (lastPosition.isArticleStart()) {
        if (lastPosition.isLineStart()) {
          // writeLineSeparator();
        }
      }
      else {
        // writeLineSeparator();
      }
    }
    else {
      // writeLineSeparator();
    }
    return lastLineStartPosition;
  }

  /**
   * tests the relationship between the last text position, the current text position and the last text position that followed a line
   * separator to decide if the gap represents a paragraph separation. This should <i>only</i> be called for consecutive text positions that
   * first pass the line separation test.
   * <p>
   * This base implementation tests to see if the lastLineStartPosition is null OR if the current vertical position has dropped below the
   * last text vertical position by at least 2.5 times the current text height OR if the current horizontal position is indented by at least
   * 2 times the current width of a space character.
   * </p>
   * <p>
   * This also attempts to identify text that is indented under a hanging indent.
   * </p>
   * <p>
   * This method sets the isParagraphStart and isHangingIndent flags on the current position object.
   * </p>
   *
   * @param position the current text position. This may have its isParagraphStart or isHangingIndent flags set upon return.
   * @param lastPosition the previous text position (should not be null).
   * @param lastLineStartPosition the last text position that followed a line separator, or null.
   * @param maxHeightForLine max height for text positions since lasLineStartPosition.
   */
  private void isParagraphSeparation(PositionWrapper position, PositionWrapper lastPosition,
      PositionWrapper lastLineStartPosition, float maxHeightForLine) {
    boolean result = false;
    if (lastLineStartPosition == null) {
      result = true;
    }
    else {
      float yGap = Math.abs(position.getTextPosition()
          .getYDirAdj() -
          lastPosition.getTextPosition()
              .getYDirAdj());
      float newYVal = multiplyFloat(getDropThreshold(), maxHeightForLine);
      // do we need to flip this for rtl?
      float xGap = position.getTextPosition()
          .getXDirAdj() -
          lastLineStartPosition.getTextPosition()
              .getXDirAdj();
      float newXVal = multiplyFloat(getIndentThreshold(),
          position.getTextPosition()
              .getWidthOfSpace());
      float positionWidth = multiplyFloat(0.25f, position.getTextPosition()
          .getWidth());

      if (yGap > newYVal) {
        result = true;
      }
      else if (xGap > newXVal) {
        // text is indented, but try to screen for hanging indent
        if (!lastLineStartPosition.isParagraphStart()) {
          result = true;
        }
        else {
          position.setHangingIndent();
        }
      }
      else if (xGap < -position.getTextPosition()
          .getWidthOfSpace()) {
            // text is left of previous line. Was it a hanging indent?
            if (!lastLineStartPosition.isParagraphStart()) {
              result = true;
            }
          }
      else if (Math.abs(xGap) < positionWidth) {
        // current horizontal position is within 1/4 a char of the last
        // linestart. We'll treat them as lined up.
        if (lastLineStartPosition.isHangingIndent()) {
          position.setHangingIndent();
        }
        else if (lastLineStartPosition.isParagraphStart()) {
          // check to see if the previous line looks like
          // any of a number of standard list item formats
          Pattern liPattern = matchListItemPattern(lastLineStartPosition);
          if (liPattern != null) {
            Pattern currentPattern = matchListItemPattern(position);
            if (liPattern == currentPattern) {
              result = true;
            }
          }
        }
      }
    }

    if (result) {

      position.setParagraphStart();
    }
  }

  private void loadDatasTypesByCodes() {

    datasTypesByCode = null;

    if (StringUtils.isBlank(datasDefinitionsFilepath)) {

      log.info("Pas de chemin renseigné pour les liste des données," + " recherche dans le Classpath Java...");

      datasDefinitionsFilepath = DatabaseHelper.GenericHelper
          .getFilePathFromClassPath(Donnee.DEFAULT_DATAS_DEFINTIONS_FILENAME);

      if (StringUtils.isBlank(datasDefinitionsFilepath)) {

        log.info("Pas de chemin trouvé pour la liste des données dans le Classpath Java," + " recherche dans le fichier jar...");

        InputStream datasDefinitionsFileInputStream = getClass()
            .getResourceAsStream("/" + Donnee.DEFAULT_DATAS_DEFINTIONS_FILENAME);

        if (datasDefinitionsFileInputStream == null) {

          log.warn("Aucun fichier de description des données trouvé",
              Donnee.DEFAULT_DATAS_DEFINTIONS_FILENAME);

          return;
        }

        log.info(
            "Utilisation du fichier de définition des données " + "présent dans le fichier jar (/{})",
            Donnee.DEFAULT_DATAS_DEFINTIONS_FILENAME);

        datasTypesByCode = DonneeHelper
            .getDatasTypesByCodes(datasDefinitionsFileInputStream);

        if (datasTypesByCode == null) {
          // Des erreurs sont déjà remontées par DonneeHelper.getDatasTypesByCodes
          log.error("Erreur à la lecture du fichier de définition des données");
        }

        return;

      }
      else {

        log.info("Fichier de définition des données utilisé : [{}]",
            datasDefinitionsFilepath);
      }

    }
    else {

      log.info("Fichier de définition des données utilisé : [{}]", datasDefinitionsFilepath);
    }

    datasTypesByCode = DonneeHelper.getDatasTypesByCodes(datasDefinitionsFilepath);
  }

  /**
   * returns the list item Pattern object that matches the text at the specified PositionWrapper or null if the text does not match such a
   * pattern. The list of Patterns tested against is given by the {@link #getListItemPatterns()} method. To add to the list, simply override
   * that method (if sub-classing) or explicitly supply your own list using {@link #setListItemPatterns(List)}.
   *
   * @param pw position
   * @return the matching pattern
   */
  private Pattern matchListItemPattern(PositionWrapper pw) {

    TextPosition tp = pw.getTextPosition();
    String txt = tp.getUnicode();

    return matchPattern(txt, getListItemPatterns());
  }

  // private boolean hasTranscodificationBeenModified() {
  //
  // return false;
  // }

  private float multiplyFloat(float value1, float value2) {
    // multiply 2 floats and truncate the resulting value to 3 decimal
    // places
    // to avoid wrong results when comparing with another float
    return Math.round(value1 * value2 * 1000) / 1000f;
  }

  /**
   * Alimente la Map mapZonesByStaticLabel et la liste relativeMapZones
   *
   * @param pdfPagePatterns
   */
  // private void listStaticAndRelativeMapZones(List<PdfPagePattern> pdfPagePatterns) {
  //
  // mapZonesByStaticLabel = new TreeMap<String, List<MapZone>>();
  // relativeMapZones = new ArrayList<MapZone>();
  // mandatoryMapZones = new ArrayList<MapZone>();
  //
  // String staticText;
  //
  // for (PdfPagePattern pdfPagePattern : pdfPagePatterns) {
  //
  // if (log.isTraceEnabled()) {
  //
  // log.trace("Liste des zones relative du template de la page {} ({})",
  // pdfPagePattern.getPageNumber(), pdfPagePattern.toString());
  //
  // if (pdfPagePattern.getRelativeMapZones() == null) {
  //
  // log.trace("Aucune zone relative au niveau du template de la page");
  //
  // } else {
  //
  // log.trace("{} zones relatives au niveau du template de la page",
  // pdfPagePattern.getRelativeMapZones().size());
  // }
  // }
  //
  // for (MapZone mapZone : pdfPagePattern.getMapZones()) {
  //
  // staticText = mapZone.getPattern();
  //
  // if (mapZone.isStaticLabel()) {
  //
  // if (StringUtils.isNotBlank(staticText)) {
  //
  // if (!mapZonesByStaticLabel.containsKey(staticText)
  // || mapZonesByStaticLabel.get(staticText) == null) {
  //
  // mapZonesByStaticLabel.put(staticText, new ArrayList<MapZone>());
  // }
  //
  // mapZonesByStaticLabel.get(staticText).add(mapZone);
  //
  // } else {
  //
  // log.warn("MapZone [name=" + mapZone.getCode()
  // + "] déclarée comme statique mais [pattern=" + mapZone.getPattern()
  // + "]");
  // }
  // }
  //
  // if (mapZone.isRelative()) {
  //
  // if (mapZone.getReferenceMapZone() != null) {
  //
  // relativeMapZones.add(mapZone);
  //
  // } else {
  //
  // log.warn("MapZone [name=" + mapZone.getCode() + "] déclarée comme relative "
  // + "mais sans mapZone de référence");
  // }
  // }
  //
  // if (mapZone.isObligatoire()) {
  //
  // mandatoryMapZones.add(mapZone);
  // }
  // }
  // }
  // }

  // private boolean loadTranscodingValues() {
  //
  // String transcodingFilePath = GenericHelper.getFilePathFromClassPath(TRANSCODING_FILENAME);
  //
  // if (transcodingFilePath == null) {
  //
  // log.warn("Fichier d'interchange [" + TRANSCODING_FILENAME
  // + "] non trouvé dans le classPath, " + "recherche dans le fichier jar");
  //
  // InputStream transcodingFileInputStream = getClass()
  // .getResourceAsStream("/" + TRANSCODING_FILENAME);
  //
  // try {
  //
  // transcodingIni = new Ini(transcodingFileInputStream);
  //
  // log.info("Utilisation du fichier d'interchange " + TRANSCODING_FILENAME
  // + " présent dans le fichier jar");
  //
  // return true;
  //
  // } catch (InvalidFileFormatException e) {
  //
  // log.error("Erreur à la lecture du fichier ini [{}] dans le jar : {}",
  // TRANSCODING_FILENAME, e.getMessage(), e);
  //
  // } catch (IOException e) {
  //
  // log.error("Erreur à la lecture du fichier ini [{}] dans le jar : {}",
  // TRANSCODING_FILENAME, e.getMessage(), e);
  // }
  //
  // log.error("Fichier d'interchange [" + TRANSCODING_FILENAME
  // + "] non trouvé dans le classPath et dans le jar");
  //
  // return false;
  // }
  //
  // File file = new File(transcodingFilePath);
  //
  // if (!file.isFile()) {
  //
  // log.error("Le fichier d'interchange [{}] n'existe pas ou n'est pas un fichier",
  // transcodingFilePath);
  // return false;
  // }
  //
  // log.info("Fichier d'interchange lu : [" + transcodingFilePath + "]");
  //
  // try {
  //
  // transcodingIni = new Ini(file);
  //
  // Section monthSection = transcodingIni.get("MONTH");
  //
  // transcodingMonths = new HashMap<String, String>();
  //
  // if (monthSection != null) {
  //
  // for (String key : monthSection.keySet()) {
  //
  // transcodingMonths.put(key, monthSection.get(key));
  // }
  // }
  //
  // return true;
  //
  // } catch (InvalidFileFormatException e) {
  //
  // log.error("Erreur à la lecture du fichier ini [{}] : {}", transcodingFilePath,
  // e.getMessage(), e);
  //
  // } catch (IOException e) {
  //
  // log.error("Erreur à la lecture du fichier ini [{}] : {}", transcodingFilePath,
  // e.getMessage(), e);
  // }
  //
  // return false;
  // }

  /**
   * Normalize the given list of TextPositions.
   *
   * @param line list of TextPositions
   * @return a list of strings, one string for every word
   */
  private List<WordWithTextPositions> normalize(List<LineItem> line) {

    List<WordWithTextPositions> normalized = new LinkedList<WordWithTextPositions>();
    StringBuilder lineBuilder = new StringBuilder();
    List<TextPosition> wordPositions = new ArrayList<TextPosition>();

    for (LineItem item : line) {
      lineBuilder = normalizeAdd(normalized, lineBuilder, wordPositions, item);
    }

    if (lineBuilder.length() > 0) {
      normalized.add(createWord(lineBuilder.toString(), wordPositions));
    }

    return normalized;
  }

  /**
   * Used within {@link #normalize(List)} to handle a {@link TextPosition}.
   *
   * @return The StringBuilder that must be used when calling this method.
   */
  private StringBuilder normalizeAdd(List<WordWithTextPositions> normalized,
      StringBuilder lineBuilder, List<TextPosition> wordPositions, LineItem item) {

    if (item.isWordSeparator()) {

      normalized.add(
          createWord(lineBuilder.toString(), new ArrayList<TextPosition>(wordPositions)));
      lineBuilder = new StringBuilder();
      wordPositions.clear();

    }
    else {

      TextPosition text = item.getTextPosition();
      lineBuilder.append(text.getUnicode());
      wordPositions.add(text);
    }

    return lineBuilder;
  }

  /**
   * Normalize certain Unicode characters. For example, convert the single "fi" ligature to "f" and "i". Also normalises Arabic and Hebrew
   * presentation forms.
   *
   * @param word Word to normalize
   * @return Normalized word
   */
  private String normalizeWord(String word) {

    StringBuilder builder = null;
    int p = 0;
    int q = 0;
    int strLength = word.length();

    for (; q < strLength; q++) {
      // We only normalize if the codepoint is in a given range.
      // Otherwise, NFKC converts too many things that would cause
      // confusion. For example, it converts the micro symbol in
      // extended Latin to the value in the Greek script. We normalize
      // the Unicode Alphabetic and Arabic A&B Presentation forms.
      char c = word.charAt(q);
      if (0xFB00 <= c && c <= 0xFDFF || 0xFE70 <= c && c <= 0xFEFF) {
        if (builder == null) {
          builder = new StringBuilder(strLength * 2);
        }
        builder.append(word.substring(p, q));
        // Some fonts map U+FDF2 differently than the Unicode spec.
        // They add an extra U+0627 character to compensate.
        // This removes the extra character for those fonts.
        if (c == 0xFDF2 && q > 0 && (word.charAt(q - 1) == 0x0627 || word.charAt(q - 1) == 0xFE8D)) {
          builder.append("\u0644\u0644\u0647");
        }
        else {
          // Trim because some decompositions have an extra space,
          // such as U+FC5E
          builder.append(Normalizer
              .normalize(word.substring(q, q + 1), Normalizer.Form.NFKC)
              .trim());
        }
        p = q + 1;
      }
    }
    if (builder == null) {
      return handleDirection(word);
    }
    else {
      builder.append(word.substring(p, q));
      return handleDirection(builder.toString());
    }
  }

  private boolean overlap(float y1, float height1, float y2, float height2) {

    return within(y1, y2, .1f) || y2 <= y1 && y2 >= y1 - height1 || y1 <= y2 && y1 >= y2 - height2;
  }

  /**
   * @param pdDocument The location to put the text.
   * @throws IOException If the doc is in an invalid state.
   */
  public void processDocument(PDDocument pdDocument) throws IOException {

    resetEngine();
    this.pdDocument = pdDocument;

    processPages(pdDocument.getPages());
  }

  /**
   * Write a list of string containing a whole line of a document.
   *
   * @param line a list with the words of the given line
   * @throws IOException if something went wrong
   */
  private void processLine(List<WordWithTextPositions> line) throws IOException {

    // LOG.trace("Ligne avec " + line.size() + " groupes de mots");

    currentLineItem = new LineItem();
    currentLineItem.setLineNumber(lineNumber);
    currentLineItem.setPageItem(currentPageItem);

    int numberOfStrings = line.size();

    int characterCount = 0;

    StringBuilder text = new StringBuilder();
    List<TextPosition> textPositions = new ArrayList<TextPosition>();

    for (int i = 0; i < numberOfStrings; i++) {

      WordWithTextPositions word = line.get(i);

      if (StringUtils.isBlank(word.getText())) {
        continue;
      }

      text.append(word.getText());
      textPositions.addAll(word.getTextPositions());

      characterCount += word.getText()
          .trim()
          .length();

      // LOG.trace("\t[" + word.getText() + "][size="
      // + word.getText().length() + "][textPositions="
      // + word.textPositions.size() + "]");

      // int idx = 0;
      //
      // for (TextPosition textPosition : word.getTextPositions()) {
      //
      // LOG.trace("\t\t[" + word.getText().charAt(idx) + "][height="
      // + textPosition.getHeight() + "][width="
      // + textPosition.getWidth() + "][X="
      // + textPosition.getX() + "][Y=" + textPosition.getY()
      // + "][XScale=" + textPosition.getXScale() + "][YScale="
      // + textPosition.getYScale() + "][EndX="
      // + textPosition.getEndX() + "][EndY="
      // + textPosition.getEndY() + "]");
      //
      // idx++;
      // }

      // TODO : a reactiver en cas de besoins
      // processWord(word.getText(), word.getTextPositions());

      if (i < numberOfStrings - 1) {
        // writeWordSeparator();
      }
    }

    currentLineItem.setText(text.toString());
    currentLineItem.setTextPositions(textPositions);

    if (characterCount > 0) {

      currentPageItem.addLineItem(currentLineItem);
      lineNumber++;
    }
  }

  /**
   * This will process the contents of a page.
   *
   * @param pdPage The page to process.
   * @throws IOException If there is an error processing the page.
   */
  @Override
  public void processPage(PDPage pdPage) throws IOException {

    log.trace("Traitement de la page " + currentPageNo);

    lineNumber = 0;

    currentPageItem = new PageItem(pdPage);
    currentPageItem.setPageNumber(currentPageNo);

    addPageItem(currentPageItem);

    if (currentPageNo >= startPage && currentPageNo <= endPage &&
        (startBookmarkPageNumber == -1 || currentPageNo >= startBookmarkPageNumber) &&
        (endBookmarkPageNumber == -1 || currentPageNo <= endBookmarkPageNumber)) {

      int numberOfArticleSections = 1;
      if (shouldSeparateByBeads) {
        // log.trace("shouldSeparateByBeads");
        fillBeadRectangles(pdPage);
        numberOfArticleSections += beadRectangles.size() * 2;
      }

      // log.trace("numberOfArticleSections=" + numberOfArticleSections);
      // log.trace("charactersByArticle.size()=originalSize=" + charactersByArticle.size());

      int originalSize = charactersByArticle.size();
      charactersByArticle.ensureCapacity(numberOfArticleSections);
      int lastIndex = Math.max(numberOfArticleSections, originalSize);

      for (int i = 0; i < lastIndex; i++) {

        if (i < originalSize) {

          charactersByArticle.get(i)
              .clear();

        }
        else {

          if (numberOfArticleSections < originalSize) {

            charactersByArticle.remove(i);

          }
          else {

            charactersByArticle.add(new ArrayList<TextPosition>());
          }
        }
      }

      // log.trace("charactersByArticle.size()=" + charactersByArticle.size());

      characterListMapping.clear();
      super.processPage(pdPage);
      processPageStep2();
      endPage(pdPage);
    }
  }

  /**
   * This will process all of the pages and the text that is in them.
   *
   * @param pages The pages object in the document.
   * @throws IOException If there is an error parsing the text.
   */
  protected void processPages(PDPageTree pages) throws IOException {

    PDPage startBookmarkPage = startBookmark == null ? null
        : startBookmark.findDestinationPage(pdDocument);

    if (startBookmarkPage != null) {
      startBookmarkPageNumber = pages.indexOf(startBookmarkPage) + 1;
    }
    else {
      // -1 = undefined
      startBookmarkPageNumber = -1;
    }

    PDPage endBookmarkPage = endBookmark == null ? null
        : endBookmark.findDestinationPage(pdDocument);
    if (endBookmarkPage != null) {
      endBookmarkPageNumber = pages.indexOf(endBookmarkPage) + 1;
    }
    else {
      // -1 = undefined
      endBookmarkPageNumber = -1;
    }

    if (startBookmarkPageNumber == -1 && startBookmark != null && endBookmarkPageNumber == -1 && endBookmark != null &&
        startBookmark.getCOSObject() == endBookmark.getCOSObject()) {
      // this is a special case where both the start and end bookmark
      // are the same but point to nothing. In this case
      // we will not extract any text.
      startBookmarkPageNumber = 0;
      endBookmarkPageNumber = 0;
    }

    for (PDPage page : pages) {
      currentPageNo++;
      if (page.hasContents()) {
        processPage(page);
      }
    }
  }

  /**
   * This will print the text of the processed page to "output". It will estimate, based on the coordinates of the text, where newlines and
   * word spacings should be placed. The text will be sorted only if that feature was enabled.
   *
   * @throws IOException If there is an error writing the text.
   */
  protected void processPageStep2() throws IOException {

    float maxYForLine = MAX_Y_FOR_LINE_RESET_VALUE;
    float minYTopForLine = MIN_Y_TOP_FOR_LINE_RESET_VALUE;
    float endOfLastTextX = END_OF_LAST_TEXT_X_RESET_VALUE;
    float lastWordSpacing = LAST_WORD_SPACING_RESET_VALUE;
    float maxHeightForLine = MAX_HEIGHT_FOR_LINE_RESET_VALUE;
    PositionWrapper lastPosition = null;
    PositionWrapper lastLineStartPosition = null;

    boolean startOfPage = true; // flag to indicate start of page
    boolean startOfArticle;

    // LOG.trace("charactersByArticle=" + charactersByArticle);

    for (List<TextPosition> textList : charactersByArticle) {

      // LOG.trace("textList=" + textList);

      if (getSortByPosition()) {

        TextPositionComparator comparator = new TextPositionComparator();

        // because the TextPositionComparator is not transitive, but
        // JDK7+ enforces transitivity on comparators, we need to use
        // a custom quicksort implementation (which is slower,
        // unfortunately).
        if (useCustomQuickSort) {
          IterativeMergeSort.sort(textList, comparator);
        }
        else {
          Collections.sort(textList, comparator);
        }
      }

      log.trace("startArticle");
      startOfArticle = true;

      // Now cycle through to print the text.
      // We queue up a line at a time before we print so that we can
      // convert
      // the line from presentation form to logical form (if needed).
      List<LineItem> line = new ArrayList<LineItem>();

      Iterator<TextPosition> textIter = textList.iterator();
      // PDF files don't always store spaces. We will need to guess where
      // we should add spaces based on the distances between
      // TextPositions.
      // Historically, this was done based on the size of the space
      // character provided by the font. In general, this
      // worked but there were cases where it did not work. Calculating
      // the average character width and using that as a metric works
      // better in some cases but fails in some cases where the spacing
      // worked. So we use both. NOTE: Adobe reader also fails on some of
      // these examples.

      // Keeps track of the previous average character width
      float previousAveCharWidth = -1;
      while (textIter.hasNext()) {
        TextPosition position = textIter.next();
        PositionWrapper current = new PositionWrapper(position);
        String characterValue = position.getUnicode();

        // Resets the average character width when we see a change in
        // font or a change in the font size
        if (lastPosition != null && (position.getFont() != lastPosition.getTextPosition()
            .getFont() ||
            position.getFontSize() != lastPosition.getTextPosition()
                .getFontSize())) {
          previousAveCharWidth = -1;
        }

        float positionX;
        float positionY;
        float positionWidth;
        float positionHeight;

        // If we are sorting, then we need to use the text direction
        // adjusted coordinates, because they were used in the sorting.
        if (getSortByPosition()) {
          positionX = position.getXDirAdj();
          positionY = position.getYDirAdj();
          positionWidth = position.getWidthDirAdj();
          positionHeight = position.getHeightDir();
        }
        else {
          positionX = position.getX();
          positionY = position.getY();
          positionWidth = position.getWidth();
          positionHeight = position.getHeight();
        }

        // The current amount of characters in a word
        int wordCharCount = position.getIndividualWidths().length;

        // Estimate the expected width of the space based on the
        // space character with some margin.
        float wordSpacing = position.getWidthOfSpace();
        float deltaSpace;
        if (wordSpacing == 0 || Float.isNaN(wordSpacing)) {
          deltaSpace = Float.MAX_VALUE;
        }
        else {
          if (lastWordSpacing < 0) {
            deltaSpace = wordSpacing * getSpacingTolerance();
          }
          else {
            deltaSpace = (wordSpacing + lastWordSpacing) / 2f * getSpacingTolerance();
          }
        }

        // Estimate the expected width of the space based on the average
        // character width with some margin. This calculation does not
        // make a true average (average of averages) but we found that
        // it gave the best results after numerous experiments.
        // Based on experiments we also found that .3 worked well.
        float averageCharWidth;
        if (previousAveCharWidth < 0) {
          averageCharWidth = positionWidth / wordCharCount;
        }
        else {
          averageCharWidth = (previousAveCharWidth + positionWidth / wordCharCount) / 2f;
        }
        float deltaCharWidth = averageCharWidth * getAverageCharTolerance();

        // Compares the values obtained by the average method and the
        // wordSpacing method and picks the smaller number.
        float expectedStartOfNextWordX = EXPECTED_START_OF_NEXT_WORD_X_RESET_VALUE;
        if (endOfLastTextX != END_OF_LAST_TEXT_X_RESET_VALUE) {
          if (deltaCharWidth > deltaSpace) {
            expectedStartOfNextWordX = endOfLastTextX + deltaSpace;
          }
          else {
            expectedStartOfNextWordX = endOfLastTextX + deltaCharWidth;
          }
        }

        if (lastPosition != null) {
          if (startOfArticle) {
            lastPosition.setArticleStart();
            startOfArticle = false;
          }
          // RDD - Here we determine whether this text object is on
          // the current line. We use the lastBaselineFontSize to
          // handle the superscript case, and the size of the current
          // font to handle the subscript case.
          // Text must overlap with the last rendered baseline text by
          // at least a small amount in order to be considered as
          // being on the same line.

          // XXX BC: In theory, this check should really check if the
          // next char is in full range seen in this line. This is
          // what I tried to do with minYTopForLine, but this caused a
          // lot of regression test failures. So, I'm leaving it be
          // for now
          if (!overlap(positionY, positionHeight, maxYForLine, maxHeightForLine)) {

            processLine(normalize(line));

            line.clear();
            lastLineStartPosition = handleLineSeparation(current, lastPosition,
                lastLineStartPosition, maxHeightForLine);
            expectedStartOfNextWordX = EXPECTED_START_OF_NEXT_WORD_X_RESET_VALUE;
            maxYForLine = MAX_Y_FOR_LINE_RESET_VALUE;
            maxHeightForLine = MAX_HEIGHT_FOR_LINE_RESET_VALUE;
            minYTopForLine = MIN_Y_TOP_FOR_LINE_RESET_VALUE;
          }
          // test if our TextPosition starts after a new word would be
          // expected to start
          if (expectedStartOfNextWordX != EXPECTED_START_OF_NEXT_WORD_X_RESET_VALUE && expectedStartOfNextWordX < positionX &&
              // only bother adding a space if the last character
              // was not a space
              lastPosition.getTextPosition()
                  .getUnicode() != null &&
              !lastPosition.getTextPosition()
                  .getUnicode()
                  .endsWith(" ")) {
            line.add(LineItem.getWordSeparator());
          }
        }
        if (positionY >= maxYForLine) {
          maxYForLine = positionY;
        }
        // RDD - endX is what PDF considers to be the x coordinate of
        // the end position of the text. We use it in computing our
        // metrics below.
        endOfLastTextX = positionX + positionWidth;

        // add it to the list
        if (characterValue != null) {
          if (startOfPage && lastPosition == null) {
            // RTL?
          }
          line.add(new LineItem(position));
        }
        maxHeightForLine = Math.max(maxHeightForLine, positionHeight);
        minYTopForLine = Math.min(minYTopForLine, positionY - positionHeight);
        lastPosition = current;
        if (startOfPage) {
          lastPosition.setParagraphStart();
          lastPosition.setLineStart();
          lastLineStartPosition = lastPosition;
          startOfPage = false;
        }
        lastWordSpacing = wordSpacing;
        previousAveCharWidth = averageCharWidth;
      }
      // print the final line
      if (line.size() > 0) {

        // log.trace("Ligne : " + normalize(line));
        processLine(normalize(line));
      }
    }
  }

  /**
   * This will process a TextPosition object and add the text to the list of characters on a page. It takes care of overlapping text.
   *
   * @param text The text to process.
   */
  @Override
  protected void processTextPosition(TextPosition text) {
    boolean showCharacter = true;
    if (suppressDuplicateOverlappingText) {
      showCharacter = false;
      String textCharacter = text.getUnicode();
      float textX = text.getX();
      float textY = text.getY();
      TreeMap<Float, TreeSet<Float>> sameTextCharacters = characterListMapping
          .get(textCharacter);
      if (sameTextCharacters == null) {
        sameTextCharacters = new TreeMap<Float, TreeSet<Float>>();
        characterListMapping.put(textCharacter, sameTextCharacters);
      }
      // RDD - Here we compute the value that represents the end of the
      // rendered
      // text. This value is used to determine whether subsequent text
      // rendered
      // on the same line overwrites the current text.
      //
      // We subtract any positive padding to handle cases where extreme
      // amounts
      // of padding are applied, then backed off (not sure why this is
      // done, but there
      // are cases where the padding is on the order of 10x the character
      // width, and
      // the TJ just backs up to compensate after each character). Also,
      // we subtract
      // an amount to allow for kerning (a percentage of the width of the
      // last
      // character).
      boolean suppressCharacter = false;
      float tolerance = text.getWidth() / textCharacter.length() / 3.0f;

      SortedMap<Float, TreeSet<Float>> xMatches = sameTextCharacters.subMap(textX - tolerance,
          textX + tolerance);
      for (TreeSet<Float> xMatch : xMatches.values()) {
        SortedSet<Float> yMatches = xMatch.subSet(textY - tolerance, textY + tolerance);
        if (!yMatches.isEmpty()) {
          suppressCharacter = true;
          break;
        }
      }
      if (!suppressCharacter) {
        TreeSet<Float> ySet = sameTextCharacters.get(textX);
        if (ySet == null) {
          ySet = new TreeSet<Float>();
          sameTextCharacters.put(textX, ySet);
        }
        ySet.add(textY);
        showCharacter = true;
      }
    }
    if (showCharacter) {
      // if we are showing the character then we need to determine which
      // article it belongs to
      int foundArticleDivisionIndex = -1;
      int notFoundButFirstLeftAndAboveArticleDivisionIndex = -1;
      int notFoundButFirstLeftArticleDivisionIndex = -1;
      int notFoundButFirstAboveArticleDivisionIndex = -1;
      float x = text.getX();
      float y = text.getY();
      if (shouldSeparateByBeads) {
        for (int i = 0; i < beadRectangles.size() && foundArticleDivisionIndex == -1; i++) {
          PDRectangle rect = beadRectangles.get(i);
          if (rect != null) {
            if (rect.contains(x, y)) {
              foundArticleDivisionIndex = i * 2 + 1;
            }
            else if ((x < rect.getLowerLeftX() || y < rect.getUpperRightY()) && notFoundButFirstLeftAndAboveArticleDivisionIndex == -1) {
              notFoundButFirstLeftAndAboveArticleDivisionIndex = i * 2;
            }
            else if (x < rect.getLowerLeftX() && notFoundButFirstLeftArticleDivisionIndex == -1) {
              notFoundButFirstLeftArticleDivisionIndex = i * 2;
            }
            else if (y < rect.getUpperRightY() && notFoundButFirstAboveArticleDivisionIndex == -1) {
              notFoundButFirstAboveArticleDivisionIndex = i * 2;
            }
          }
          else {
            foundArticleDivisionIndex = 0;
          }
        }
      }
      else {
        foundArticleDivisionIndex = 0;
      }
      int articleDivisionIndex;
      if (foundArticleDivisionIndex != -1) {
        articleDivisionIndex = foundArticleDivisionIndex;
      }
      else if (notFoundButFirstLeftAndAboveArticleDivisionIndex != -1) {
        articleDivisionIndex = notFoundButFirstLeftAndAboveArticleDivisionIndex;
      }
      else if (notFoundButFirstLeftArticleDivisionIndex != -1) {
        articleDivisionIndex = notFoundButFirstLeftArticleDivisionIndex;
      }
      else if (notFoundButFirstAboveArticleDivisionIndex != -1) {
        articleDivisionIndex = notFoundButFirstAboveArticleDivisionIndex;
      }
      else {
        articleDivisionIndex = charactersByArticle.size() - 1;
      }

      List<TextPosition> textList = charactersByArticle.get(articleDivisionIndex);

      // In the wild, some PDF encoded documents put diacritics (accents
      // on
      // top of characters) into a separate Tj element. When displaying
      // them
      // graphically, the two chunks get overlayed. With text output
      // though,
      // we need to do the overlay. This code recombines the diacritic
      // with
      // its associated character if the two are consecutive.
      if (textList.isEmpty()) {
        textList.add(text);
      }
      else {
        // test if we overlap the previous entry.
        // Note that we are making an assumption that we need to only
        // look back
        // one TextPosition to find what we are overlapping.
        // This may not always be true. */
        TextPosition previousTextPosition = textList.get(textList.size() - 1);
        if (text.isDiacritic() && previousTextPosition.contains(text)) {
          previousTextPosition.mergeDiacritic(text);
        }
        // If the previous TextPosition was the diacritic, merge it into
        // this
        // one and remove it from the list.
        else if (previousTextPosition.isDiacritic() && text.contains(previousTextPosition)) {
          text.mergeDiacritic(previousTextPosition);
          textList.remove(textList.size() - 1);
          textList.add(text);
        }
        else {
          textList.add(text);
        }
      }
    }
  }

  /**
   * Write a Java string to the output stream.
   *
   * @param text The text to write to the stream.
   * @throws IOException If there is an error when writing the text.
   */
  protected void processWord(String text) throws IOException {
    // output.write(text);
  }

  /**
   * Write a Java string to the output stream. The default implementation will ignore the <code>textPositions</code> and just calls
   * {@link #processWord(String)}.
   *
   * @param text The text to write to the stream.
   * @param textPositions The TextPositions belonging to the text.
   * @throws IOException If there is an error when writing the text.
   */
  protected void processWord(String text, List<TextPosition> textPositions) throws IOException {

    if (StringUtils.isBlank(text)) {

      return;
    }

    currentWordGroupItem = new WordGroupItem();
    currentWordGroupItem.setText(text);
    currentWordGroupItem.setTextPositions(textPositions);
    currentWordGroupItem.setPageItem(currentPageItem);

    currentLineItem.addWordGroupItem(currentWordGroupItem);

    StringBuilder wordBuilder = null;
    int currentPosition = 0;
    List<TextPosition> currentWordTextPositions = null;

    boolean searchNewWord = true;

    while (currentPosition < text.length()) {

      if (BLANK_CHARS.contains(text.charAt(currentPosition))) {

        if (wordBuilder != null && currentWordItem != null) {

          currentWordItem.setText(wordBuilder.toString());
          currentWordItem.setTextPositions(currentWordTextPositions);
        }

        searchNewWord = true;

        currentPosition++;
        continue;
      }

      if (searchNewWord) {

        searchNewWord = false;

        currentWordItem = new WordItem();
        currentWordItem.setPageItem(currentPageItem);
        currentWordGroupItem.addWordItem(currentWordItem);

        wordBuilder = new StringBuilder();
        currentWordTextPositions = new ArrayList<TextPosition>();
      }

      wordBuilder.append(text.charAt(currentPosition));
      currentWordTextPositions.add(textPositions.get(currentPosition));

      currentPosition++;
    }

    if (wordBuilder != null && currentWordItem != null) {

      currentWordItem.setText(wordBuilder.toString());
      currentWordItem.setTextPositions(currentWordTextPositions);
    }

    int i = 0;
    int textPositionsCount = textPositions.size();
    Rectangle wordBoundsRectangle = null;

    for (TextPosition textPosition : textPositions) {

      // LOG.trace("String[" + textPosition.getXDirAdj() + ","
      // + textPosition.getYDirAdj() + " fs="
      // + textPosition.getFontSize() + " xscale="
      // + textPosition.getXScale() + " height="
      // + textPosition.getHeightDir() + " space="
      // + textPosition.getWidthOfSpace() + " width="
      // + textPosition.getWidthDirAdj() + "]"
      // + textPosition.getUnicode());

      // glyph space -> user space
      // note: text.getTextMatrix() is *not* the Text Matrix, it's the
      // Text Rendering Matrix
      AffineTransform at = textPosition.getTextMatrix()
          .createAffineTransform();

      // En rouge :
      // Affiche les rectangle avec la "hauteur" (pas la vrai hauteur,
      // mais utilisé pour l'heuristique de l'extraction de texte, ce qui
      // correspond à la moitié de la hauteur
      // du rectangle limitant (bounding box) et commence a y=0
      Rectangle2D.Float rect = new Rectangle2D.Float(0, 0,
          textPosition.getWidthDirAdj() / textPosition.getTextMatrix()
              .getScalingFactorX(),
          textPosition.getHeightDir() / textPosition.getTextMatrix()
              .getScalingFactorY());
      Shape s = at.createTransformedShape(rect);
      s = currentPageItem.getYFlipAffineTransform()
          .createTransformedShape(s);
      s = currentPageItem.getRotateAffineTransform()
          .createTransformedShape(s);

      // En bleu :
      // affiche le rectangle avec les limites verticales réelles, basées
      // sur les valeurs y de la boite des limites de la font
      // Habituellement, la hauteur est identique a ce que l'on voit
      // lorsque l'on double-clic sur du texte dans Adobre Reader
      PDFont font = textPosition.getFont();
      BoundingBox bbox = font.getBoundingBox();

      // advance width, bbox height (glyph space)
      float xadvance = font.getWidth(textPosition.getCharacterCodes()[0]);
      // todo: should iterate all chars
      rect = new Rectangle2D.Float(0, bbox.getLowerLeftY(), xadvance, bbox.getHeight());

      if (font instanceof PDType3Font) {
        // bbox and font matrix are unscaled
        at.concatenate(font.getFontMatrix()
            .createAffineTransform());
      }
      else {
        // bbox and font matrix are already scaled to 1000
        at.scale(1 / 1000f, 1 / 1000f);
      }
      s = at.createTransformedShape(rect);
      s = currentPageItem.getYFlipAffineTransform()
          .createTransformedShape(s);
      s = currentPageItem.getRotateAffineTransform()
          .createTransformedShape(s);

      Rectangle rectangle = s.getBounds();

      if (i == 0) {

        wordBoundsRectangle = new Rectangle(rectangle.getLocation());
      }

      if ((i + 1) == textPositionsCount) {

        int leftX = wordBoundsRectangle.x;
        int rightX = rectangle.x + rectangle.width;

        wordBoundsRectangle.setSize(rightX - leftX, rectangle.height);
        currentWordGroupItem.setBounds(wordBoundsRectangle);
      }

      i++;

      // PdfBoxUtil.logRectangle(rectangle, LOG, "[" + textPosition.getUnicode() + "]");
    }
  }

  private void resetEngine() {

    currentPageNo = 0;
    pdDocument = null;

    if (charactersByArticle != null) {
      charactersByArticle.clear();
    }

    if (characterListMapping != null) {
      characterListMapping.clear();
    }
  }

  /**
   * Set the character width-based tolerance value that is used to estimate where spaces in text should be added. Note that the default
   * value for this has been determined from trial and error. Setting this value larger will reduce the number of spaces added.
   *
   * @param averageCharToleranceValue average tolerance / scaling factor to use
   */
  public void setAverageCharTolerance(float averageCharToleranceValue) {
    averageCharTolerance = averageCharToleranceValue;
  }

  public void setDataDefinitionsFilepath(String filepath) {

    datasDefinitionsFilepath = filepath;

    loadDatasTypesByCodes();
  }

  /**
   * sets the minimum whitespace, as a multiple of the max height of the current characters beyond which the current line start is
   * considered to be a paragraph start. The default value is 2.5.
   *
   * @param dropThresholdValue the character height multiple for max allowed whitespace between lines in the same paragraph.
   */
  public void setDropThreshold(float dropThresholdValue) {
    dropThreshold = dropThresholdValue;
  }

  /**
   * Set the bookmark where the text extraction should stop.
   *
   * @param aEndBookmark The ending bookmark.
   */
  public void setEndBookmark(PDOutlineItem aEndBookmark) {
    endBookmark = aEndBookmark;
  }

  /**
   * This will set the last page to be extracted by this class.
   *
   * @param endPageValue New value of 1-based endPage property.
   */
  public void setEndPage(int endPageValue) {
    endPage = endPageValue;
  }

  /**
   * sets the multiple of whitespace character widths for the current text which the current line start can be indented from the previous
   * line start beyond which the current line start is considered to be a paragraph start. The default value is 2.0.
   *
   * @param indentThresholdValue the number of whitespace character widths to use when detecting paragraph indents.
   */
  public void setIndentThreshold(float indentThresholdValue) {
    indentThreshold = indentThresholdValue;
  }

  /**
   * Set the desired line separator for output text. The line.separator system property is used if the line separator preference is not set
   * explicitly using this method.
   *
   * @param separator The desired line separator string.
   */
  public void setLineSeparator(String separator) {
    lineSeparator = separator;
  }

  /**
   * use to supply a different set of regular expression patterns for matching list item starts.
   *
   * @param patterns list of patterns
   */
  protected void setListItemPatterns(List<Pattern> patterns) {
    listOfPatterns = patterns;
  }

  /**
   * Set if the text stripper should group the text output by a list of beads. The default value is true!
   *
   * @param aShouldSeparateByBeads The new grouping of beads.
   */
  public void setShouldSeparateByBeads(boolean aShouldSeparateByBeads) {
    shouldSeparateByBeads = aShouldSeparateByBeads;
  }

  /**
   * The order of the text tokens in a PDF file may not be in the same as they appear visually on the screen. For example, a PDF writer may
   * write out all text by font, so all bold or larger text, then make a second pass and write out the normal text.<br>
   * The default is to <b>not</b> sort by position.<br>
   * <br>
   * A PDF writer could choose to write each character in a different order. By default PDFBox does <b>not</b> sort the text tokens before
   * processing them due to performance reasons.
   *
   * @param newSortByPosition Tell PDFBox to sort the text positions.
   */
  public void setSortByPosition(boolean newSortByPosition) {
    sortByPosition = newSortByPosition;
  }

  /**
   * Set the space width-based tolerance value that is used to estimate where spaces in text should be added. Note that the default value
   * for this has been determined from trial and error. Setting this value larger will reduce the number of spaces added.
   *
   * @param spacingToleranceValue tolerance / scaling factor to use
   */
  public void setSpacingTolerance(float spacingToleranceValue) {
    spacingTolerance = spacingToleranceValue;
  }

  /**
   * Set the bookmark where text extraction should start, inclusive.
   *
   * @param aStartBookmark The starting bookmark.
   */
  public void setStartBookmark(PDOutlineItem aStartBookmark) {
    startBookmark = aStartBookmark;
  }

  /**
   * This will set the first page to be extracted by this class.
   *
   * @param startPageValue New value of 1-based startPage property.
   */
  public void setStartPage(int startPageValue) {
    startPage = startPageValue;
  }

  /**
   * By default the text stripper will attempt to remove text that overlapps each other. Word paints the same character several times in
   * order to make it look bold. By setting this to false all text will be extracted, which means that certain sections will be duplicated,
   * but better performance will be noticed.
   *
   * @param suppressDuplicateOverlappingTextValue The suppressDuplicateOverlappingText to set.
   */
  public void setSuppressDuplicateOverlappingText(boolean suppressDuplicateOverlappingTextValue) {
    suppressDuplicateOverlappingText = suppressDuplicateOverlappingTextValue;
  }

  public void setTranscodingFilepath(String filepath) {

    transcodingHelper = null;

    transcodingFilepath = filepath;
  }

  /**
   * Set the desired word separator for output text. The PDFBox text extraction algorithm will output a space character if there is enough
   * space between two words. By default a space character is used. If you need and accurate count of characters that are found in a PDF
   * document then you might want to set the word separator to the empty string.
   *
   * @param separator The desired page separator string.
   */
  public void setWordSeparator(String separator) {
    wordSeparator = separator;
  }

  /**
   * This will determine of two floating point numbers are within a specified variance.
   *
   * @param first The first number to compare to.
   * @param second The second number to compare to.
   * @param variance The allowed variance.
   */
  private boolean within(float first, float second, float variance) {
    return second < first + variance && second > first - variance;
  }
}
