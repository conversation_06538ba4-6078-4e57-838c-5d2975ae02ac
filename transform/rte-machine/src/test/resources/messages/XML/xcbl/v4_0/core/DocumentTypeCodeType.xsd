<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="DocumentTypeCodeType">
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfAnalysis">
                <xsd:annotation>
                    <xsd:documentation>Certificate providing the values of an analysis.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfConformity">
                <xsd:annotation>
                    <xsd:documentation>Certificate certifying the conformity to predefined definitions.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfQuality">
                <xsd:annotation>
                    <xsd:documentation>Certificate certifying the quality of goods, services etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestReport">
                <xsd:annotation>
                    <xsd:documentation>Report providing the results of a test session.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductPerformanceReport">
                <xsd:annotation>
                    <xsd:documentation>Report specifying the performance values of products.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductSpecificationReport">
                <xsd:annotation>
                    <xsd:documentation>Report providing specification values of products.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessDataReport">
                <xsd:annotation>
                    <xsd:documentation>Reports on events during production process.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstSampleTestReport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceSalesCatalogue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyInformation">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing basic data concerning a party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederalLabelApproval">
                <xsd:annotation>
                    <xsd:documentation>A pre-approved document relating to federal label approval requirements.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MillCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate certifying a specific quality of agricultural products.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostReceipt">
                <xsd:annotation>
                    <xsd:documentation>Document/message which evidences the transport of goods by post (e.g. mail, parcel, etc.).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeightCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate certifying the weight of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeightList">
                <xsd:annotation>
                    <xsd:documentation>Document/message specifying the weight of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Certificate">
                <xsd:annotation>
                    <xsd:documentation>Document by means of which the documentary credit applicant specifies the conditions for the certificate and by whom the certificate is to be issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CombinedCertificateOfValueAndOrigin">
                <xsd:annotation>
                    <xsd:documentation>Document identifying goods in which the issuing authority expressly certifies that the goods originate in a specific country or part of, or group of countries. It also states the price and/or cost of the goods with the purpose of determining the customs origin.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MovementCertificateATr1">
                <xsd:annotation>
                    <xsd:documentation>Specific form of transit declaration issued by the exporter (movement certificate).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfQuantity">
                <xsd:annotation>
                    <xsd:documentation>Certificate certifying the quantity of goods, services etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QualityDataMessage">
                <xsd:annotation>
                    <xsd:documentation>Usage of QALITY-message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Query">
                <xsd:annotation>
                    <xsd:documentation>Request information based on defined criteria.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponseToQuery">
                <xsd:annotation>
                    <xsd:documentation>Self-explanatory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusInformation">
                <xsd:annotation>
                    <xsd:documentation>Information regarding the status of a related message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Restow">
                <xsd:annotation>
                    <xsd:documentation>Message/document identifying containers that have been unloaded and then reloaded onto the same means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerDischargeList">
                <xsd:annotation>
                    <xsd:documentation>Message/document itemising containers to be discharged from vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorporateSuperannuationContributionsAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing contributions advice used for corporate superannuation schemes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndustrySuperannuationContributionsAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing contributions advice used for superannuation schemes which are industry wide.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorporateSuperannuationMemberMaintenanceMessage">
                <xsd:annotation>
                    <xsd:documentation>Member maintenance message used for corporate superannuation schemes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndustrySuperannuationMemberMaintenanceMessage">
                <xsd:annotation>
                    <xsd:documentation>Member maintenance message used for industry wide superannuation schemes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LifeInsurancePayrollDeductionsAdvice">
                <xsd:annotation>
                    <xsd:documentation>Payroll deductions advice used in the life insurance industry.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnderbondRequest">
                <xsd:annotation>
                    <xsd:documentation>A Message/document requesting to move cargo from one Customs control point to another.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnderbondApproval">
                <xsd:annotation>
                    <xsd:documentation>A message/document issuing Customs approval to move cargo from one Customs control point to another.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfSealingOfExportMeatLockers">
                <xsd:annotation>
                    <xsd:documentation>Document / message issued by the authority in the exporting country evidencing the sealing of export meat lockers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoStatus">
                <xsd:annotation>
                    <xsd:documentation>Message identifying the status of cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryReport">
                <xsd:annotation>
                    <xsd:documentation>A message specifying information relating to held inventories.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IdentityCard">
                <xsd:annotation>
                    <xsd:documentation>Official document to identify a person.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponseToATradeStatisticsMessage">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which the competent national authorities provide a declarant with an acceptance or a rejection about a received declaration for European statistical purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VaccinationCertificate">
                <xsd:annotation>
                    <xsd:documentation>Official document proving immunisation against certain diseases.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Passport">
                <xsd:annotation>
                    <xsd:documentation>An official document giving permission to travel in foreign countries.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrivingLicenceNational">
                <xsd:annotation>
                    <xsd:documentation>An official document giving permission to drive a car in a given country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrivingLicenceInternational">
                <xsd:annotation>
                    <xsd:documentation>An official document giving a native of one country permission to drive a vehicle in certain other countries.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreePass">
                <xsd:annotation>
                    <xsd:documentation>A document giving free access to a service.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SeasonTicket">
                <xsd:annotation>
                    <xsd:documentation>A document giving access to a service for a determined period of time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportStatusReport">
                <xsd:annotation>
                    <xsd:documentation>A message to report the transport status and/or change in the transport status (i.e. event) between agreed parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportStatusRequest">
                <xsd:annotation>
                    <xsd:documentation>A message to request a transport status report (e.g. through the International multimodal status report message IFSTA).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankingStatus">
                <xsd:annotation>
                    <xsd:documentation>A banking status document and/or message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Extra-CommunityTradeStatisticalDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a declarant provides information about extra-Community trade of goods required by the body responsible for the collection of trade statistics. Trade by a country in the European Union with a country outside the European Union.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WrittenInstructionsInConformanceWithAdrArticleNumber">
                <xsd:annotation>
                    <xsd:documentation>Written instructions relating to dangerous goods and defined in the European Agreement of Dangerous Transport by Road known as ADR (Accord europeen relatif au transport international des marchandises Dangereuses par Route).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamageCertification">
                <xsd:annotation>
                    <xsd:documentation>Official certification that damages to the goods to be transported have been discovered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValidatedPricedTender">
                <xsd:annotation>
                    <xsd:documentation>A validated priced tender.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceSalesCatalogueResponse">
                <xsd:annotation>
                    <xsd:documentation>A document providing a response to a previously sent price/sales catalogue.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceNegotiationResult">
                <xsd:annotation>
                    <xsd:documentation>A document providing the result of price negotiations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SafetyAndHazardDataSheet">
                <xsd:annotation>
                    <xsd:documentation>Document or message to supply advice on a dangerous or hazardous material to industrial customers so as to enable them to take measures to protect their employees and the environment from any potential harmful effects from these material.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalStatementOfAnAccount">
                <xsd:annotation>
                    <xsd:documentation>A statement of an account containing the booked items as in the ledger of the account servicing financial institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListingStatementOfAnAccount">
                <xsd:annotation>
                    <xsd:documentation>A statement from the account servicing financial institution containing items pending to be booked.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosingStatementOfAnAccount">
                <xsd:annotation>
                    <xsd:documentation>Last statement of a period containing the interest calculation and the final balance of the last entry date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentOn-HireReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the movement of containers or other items of transport equipment to record physical movement activity and establish the beginning of a rental period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentOff-HireReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the movement of containers or other items of transport equipment to record physical movement activity and establish the end of a rental period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Treatment-NilOutturn">
                <xsd:annotation>
                    <xsd:documentation>No shortage, surplus or damaged outturn resulting from container vessel unpacking.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Treatment-Time-UpUnderbond">
                <xsd:annotation>
                    <xsd:documentation>Movement type indicator: goods are moved under customs control for warehousing due to being time-up.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Treatment-UnderbondBySea">
                <xsd:annotation>
                    <xsd:documentation>Movement type indicator: goods are to move by sea under customs control to a customs office where formalities will be completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Treatment-PersonalEffect">
                <xsd:annotation>
                    <xsd:documentation>Cargo consists of personal effects.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Treatment-Timber">
                <xsd:annotation>
                    <xsd:documentation>Cargo consists of timber.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminaryCreditAssessment">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued either by a factor to indicate his preliminary credit assessment on a buyer, or by a seller to request a factor's preliminary credit assessment on a buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditCover">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued either by a factor to give a credit cover on a buyer, or by a seller to request a factor's credit cover.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentAccount">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a factor to indicate the money movements of a seller's or another factor's account with him.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialDispute">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a party (usually the buyer) to indicate that one or more invoices or one or more credit notes are disputed for payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Chargeback">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a factor to a seller or to another factor to indicate that the rest of the amounts of one or more invoices uncollectable from buyers are charged back to clear the invoice(s) off the ledger.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reassignment">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a factor to a seller or to another factor to reassign an invoice or credit note previously assigned to him.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollateralAccount">
                <xsd:annotation>
                    <xsd:documentation>Document message issued by a factor to indicate the movements of invoices, credit notes and payments of a seller's account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForPayment">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a creditor to a debtor to request payment of one or more invoices past due.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnshipPermit">
                <xsd:annotation>
                    <xsd:documentation>A message or document issuing permission to unship cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatisticalDefinitions">
                <xsd:annotation>
                    <xsd:documentation>Transmission of one or more statistical definitions.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatisticalData">
                <xsd:annotation>
                    <xsd:documentation>Transmission of one or more items of data or data sets.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForStatisticalData">
                <xsd:annotation>
                    <xsd:documentation>Request for one or more items or data sets of statistical data.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Call-OffDelivery">
                <xsd:annotation>
                    <xsd:documentation>Document/message to provide split quantities and delivery dates referring to a previous delivery instruction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsignmentStatusReport">
                <xsd:annotation>
                    <xsd:documentation>Message covers information about the consignment status.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryMovementAdvice">
                <xsd:annotation>
                    <xsd:documentation>Advice of inventory movements.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryStatusAdvice">
                <xsd:annotation>
                    <xsd:documentation>Advice of stock on hand.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebitNoteRelatedToGoodsOrServices">
                <xsd:annotation>
                    <xsd:documentation>Debit information related to a transaction for goods or services to the relevant party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditNoteRelatedToGoodsOrServices">
                <xsd:annotation>
                    <xsd:documentation>Document message used to provide credit information related to a transaction for goods or services to the relevant party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeteredServicesInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message claiming payment for the supply of metered services (e.g., gas, electricity, etc.) supplied to a fixed meter whose consumption is measured over a period of time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditNoteRelatedToFinancialAdjustments">
                <xsd:annotation>
                    <xsd:documentation>Document message for providing credit information related to financial adjustments to the relevant party, e.g., bonuses.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebitNoteRelatedToFinancialAdjustments">
                <xsd:annotation>
                    <xsd:documentation>Document/message for providing debit information related to financial adjustments to the relevant party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsManifest">
                <xsd:annotation>
                    <xsd:documentation>Message/document identifying a customs manifest. The document itemises a list of cargo prepared by shipping companies from bills of landing and presented to customs for formal report of cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VesselUnpackReport">
                <xsd:annotation>
                    <xsd:documentation>A document code to indicate that the message being transmitted identifies all short and surplus cargoes off-loaded from a vessel at a specified discharging port.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralCargoSummaryManifestReport">
                <xsd:annotation>
                    <xsd:documentation>A document code to indicate that the message being transmitted is summary manifest information for general cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsignmentUnpackReport">
                <xsd:annotation>
                    <xsd:documentation>A document code to indicate that the message being transmitted is a consignment unpack report only.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeatAndMeatBy-ProductsSanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that meat or meat by- products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeatFoodProductsSanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that meat food products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PoultrySanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that poultry products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HorsemeatSanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that horsemeat products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CasingSanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that casing products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PharmaceuticalSanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that pharmaceutical products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InedibleSanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by the competent authority in the exporting country evidencing that inedible products comply with the requirements set by the importing country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImpendingArrival">
                <xsd:annotation>
                    <xsd:documentation>Notification of impending arrival details for vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeansOfTransportAdvice">
                <xsd:annotation>
                    <xsd:documentation>Message reporting the means of transport used to carry goods or cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalInformation">
                <xsd:annotation>
                    <xsd:documentation>Message reporting the arrival details of goods or cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoReleaseNotification">
                <xsd:annotation>
                    <xsd:documentation>Message/document sent by the cargo handler indicating that the cargo has moved from a Customs controlled premise.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExciseCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate asserting that the goods have been submitted to the excise authorities before departure from the exporting country or before delivery in case of import traffic.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationDocument">
                <xsd:annotation>
                    <xsd:documentation>An official document providing registration details.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxNotification">
                <xsd:annotation>
                    <xsd:documentation>Used to specify that the message is a tax notification.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentDirectInterchangeReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the movement of containers or other items of transport equipment being exchanged, establishing relevant rental periods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentImpendingArrivalAdvice">
                <xsd:annotation>
                    <xsd:documentation>Advice that containers or other items of transport equipment may be expected to be delivered to a certain location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued within an enterprise to initiate the purchase of articles, materials or services required for the production or manufacture of goods to be offered for sale or otherwise supplied to customers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentDamageReport">
                <xsd:annotation>
                    <xsd:documentation>Report of damaged items of transport equipment that have been returned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentMaintenanceAndRepairWorkEstimateAdvice">
                <xsd:annotation>
                    <xsd:documentation>Advice providing estimates of transport equipment maintenance and repair costs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentEmptyReleaseInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to release an item of empty transport equipment to a specified party or parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportMovementGateInReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the inward movement of cargo, containers or other items of transport equipment which have been delivered to a facility by an inland carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturingInstructions">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued within an enterprise to initiate the manufacture of goods to be offered for sale.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportMovementGateOutReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the outward movement of cargo, containers or other items of transport equipment (either full or empty) which have been picked up by an inland carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentUnpackingInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to unpack specified cargo from specified containers or other items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentUnpackingReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the completion of unpacking specified containers or other items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentPick-UpAvailabilityRequest">
                <xsd:annotation>
                    <xsd:documentation>Request for confirmation that an item of transport equipment will be available for collection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentPick-UpAvailabilityConfirmation">
                <xsd:annotation>
                    <xsd:documentation>Confirmation that an item of transport equipment is available for collection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentPick-UpReport">
                <xsd:annotation>
                    <xsd:documentation>Report that an item of transport equipment has been collected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentShiftReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the movement of containers or other items of transport within a facility.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportDischargeInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to unload specified cargo, containers or transport equipment from a means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportDischargeReport">
                <xsd:annotation>
                    <xsd:documentation>Report on cargo, containers or transport equipment unloaded from a particular means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StoresRequisition">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued within an enterprise ordering the taking out of stock of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportLoadingInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to load cargo, containers or transport equipment onto a means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportLoadingReport">
                <xsd:annotation>
                    <xsd:documentation>Report on completion of loading cargo, containers or other transport equipment onto a means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentMaintenanceAndRepairWork">
                <xsd:annotation>
                    <xsd:documentation>authorisation Authorisation to have transport equipment repaired or to have maintenance performed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportDepartureReport">
                <xsd:annotation>
                    <xsd:documentation>Report of the departure of a means of transport from a particular facility.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEmptyEquipmentAdvice">
                <xsd:annotation>
                    <xsd:documentation>Advice that an item or items of empty transport equipment are available for return.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentAcceptanceOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to accept items of transport equipment which are to be delivered by an inland carrier (rail, road or barge) to a specified facility.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentSpecialServiceInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to perform a specified service or services on an item or items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentStockReport">
                <xsd:annotation>
                    <xsd:documentation>Report on the number of items of transport equipment stored at one or more locations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportCargoReleaseOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to release cargo or items of transport equipment to a specified party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoicingDataSheet">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued within an enterprise containing data about goods sold, to be used as the basis for the preparation of an invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentPackingInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to pack cargo into a container or other item of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsClearanceNotice">
                <xsd:annotation>
                    <xsd:documentation>Notification of customs clearance of cargo or items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDocumentsExpirationNotice">
                <xsd:annotation>
                    <xsd:documentation>Notice specifying expiration of Customs documents relating to cargo or items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentOn-HireRequest">
                <xsd:annotation>
                    <xsd:documentation>Request for transport equipment to be made available for hire.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentOn-HireOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to release empty items of transport equipment for on-hire to a lessee, and authorising collection by or on behalf of a specified party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentOff-HireRequest">
                <xsd:annotation>
                    <xsd:documentation>Request to terminate the lease on an item of transport equipment at a specified time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentSurveyOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to perform a survey on specified items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentSurveyOrderResponse">
                <xsd:annotation>
                    <xsd:documentation>Response to an order to conduct a survey of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentSurveyReport">
                <xsd:annotation>
                    <xsd:documentation>Survey report of specified items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackingInstructions">
                <xsd:annotation>
                    <xsd:documentation>Document/message within an enterprise giving instructions on how goods are to be packed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvisingItemsToBeBookedToAFinancialAccount">
                <xsd:annotation>
                    <xsd:documentation>A document and/or message advising of items which have to be booked to a financial account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentMaintenanceAndRepairWorkEstimateOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to draw up an estimate of the costs of maintenance or repair of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentMaintenanceAndRepairNotice">
                <xsd:annotation>
                    <xsd:documentation>Report of transport equipment which has been repaired or has had maintenance performed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyContainerDispositionOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to make available empty containers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoVesselDischargeOrder">
                <xsd:annotation>
                    <xsd:documentation>Order that the containers or cargo specified are to be discharged from a vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoVesselLoadingOrder">
                <xsd:annotation>
                    <xsd:documentation>Order that specified cargo, containers or groups of containers are to be loaded in or on a vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MultidropOrder">
                <xsd:annotation>
                    <xsd:documentation>One purchase order that contains the orders of two or more vendors and the associated delivery points for each.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BailmentContract">
                <xsd:annotation>
                    <xsd:documentation>A document authorizing the bailing of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BasicAgreement">
                <xsd:annotation>
                    <xsd:documentation>A document indicating an agreement containing basic terms and conditions applicable to future contracts between two parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternalTransportOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message giving instructions about the transport of goods within an enterprise.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Grant">
                <xsd:annotation>
                    <xsd:documentation>A document indicating the granting of funds.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndefiniteDeliveryIndefiniteQuantityContract">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a contract calling for the indefinite deliveries of indefinite quantities of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndefiniteDeliveryDefiniteQuantityContract">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a contract calling for indefinite deliveries of definite quantities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequirementsContract">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a requirements contract that authorizes the filling of all purchase requirements during a specified contract period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaskOrder">
                <xsd:annotation>
                    <xsd:documentation>A document indicating an order that tasks a contractor to perform a specified function.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MakeOrBuyPlan">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a plan that identifies which items will be made and which items will be bought.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubcontractorPlan">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a plan that identifies the manufacturer's subcontracting strategy for a specific contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostDataSummary">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a summary of cost data.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertifiedCostAndPriceData">
                <xsd:annotation>
                    <xsd:documentation>A document indicating cost and price data whose accuracy has been certified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WageDetermination">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a determination of the wages to be paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractFundsStatusReportCfsr">
                <xsd:annotation>
                    <xsd:documentation>A report to provide the status of funds applicable to the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertifiedInspectionAndTestResults">
                <xsd:annotation>
                    <xsd:documentation>A certification as to the accuracy of inspection and test results.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialInspectionAndReceivingReport">
                <xsd:annotation>
                    <xsd:documentation>A report that is both an inspection report for materials and a receiving document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchasingSpecification">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a specification used to purchase an item.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentOrPerformanceBond">
                <xsd:annotation>
                    <xsd:documentation>A document indicating a bond that guarantees the payment of monies or a performance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractSecurityClassificationSpecification">
                <xsd:annotation>
                    <xsd:documentation>A document that indicates the specification contains the security and classification requirements for a contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturingSpecification">
                <xsd:annotation>
                    <xsd:documentation>A document indicating the specification of how an item is to be manufactured.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyAmericaCertificateOfCompliance">
                <xsd:annotation>
                    <xsd:documentation>A document certifying that more than 50 percent of the cost of an item is attributed to US origin.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerOff-HireNotice">
                <xsd:annotation>
                    <xsd:documentation>Notice to return leased containers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoAcceptanceOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to accept cargo to be delivered by a carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pick-UpNotice">
                <xsd:annotation>
                    <xsd:documentation>Notice specifying the pick-up of released cargo or containers from a certain address.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorisationToPlanAndSuggestOrders">
                <xsd:annotation>
                    <xsd:documentation>Document or message that authorises receiver to plan orders, based on information in this message, and send these orders as suggestions to the sender.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorisationToPlanAndShipOrders">
                <xsd:annotation>
                    <xsd:documentation>Document or message that authorises receiver to plan and ship orders based on information in this message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Drawing">
                <xsd:annotation>
                    <xsd:documentation>The document or message is a drawing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostPerformanceReportCprFormat2">
                <xsd:annotation>
                    <xsd:documentation>A report identifying the cost performance on a contract at specified levels of the work breakdown structure (format 2 - organizational categories).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostScheduleStatusReportCssr">
                <xsd:annotation>
                    <xsd:documentation>A report providing the status of the cost and schedule applicable to a contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostPerformanceReportCprFormat1">
                <xsd:annotation>
                    <xsd:documentation>A report identifying the cost performance on a contract including the current month's values at specified levels of the work breakdown structure (format 1 - work breakdown structure).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostPerformanceReportCprFormat3">
                <xsd:annotation>
                    <xsd:documentation>A report identifying the cost performance on a contract that summarizes changes to a contract over a given reporting period with beginning and ending values (format 3 - baseline).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostPerformanceReportCprFormat4">
                <xsd:annotation>
                    <xsd:documentation>A report identifying the cost performance on a contract including forecasts of labour requirements for the remaining portion of the contract (format 4 - staffing).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostPerformanceReportCprFormat5">
                <xsd:annotation>
                    <xsd:documentation>A report identifying the cost performance on a contract that summarizes cost or schedule variances (format 5 - explanations and problem analysis).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProgressiveDischargeReport">
                <xsd:annotation>
                    <xsd:documentation>Document or message progressively issued by the container terminal operator in charge of discharging a vessel identifying containers that have been discharged from a specific vessel at that point in time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BalanceConfirmation">
                <xsd:annotation>
                    <xsd:documentation>Confirmation of a balance at an entry date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerStrippingOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to unload goods from a container.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerStuffingOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to stuff specified goods or consignments in a container.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConveyanceDeclarationArrival">
                <xsd:annotation>
                    <xsd:documentation>Declaration to the public authority upon arrival of the conveyance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConveyanceDeclarationDeparture">
                <xsd:annotation>
                    <xsd:documentation>Declaration to the public authority upon departure of the conveyance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConveyanceDeclarationCombined">
                <xsd:annotation>
                    <xsd:documentation>Combined declaration of arrival and departure to the public authority.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectRecoveryPlan">
                <xsd:annotation>
                    <xsd:documentation>A project plan for recovery after a delay or problem resolution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectProductionPlan">
                <xsd:annotation>
                    <xsd:documentation>A project plan for the production of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatisticalAndOtherAdministrativeInternalDocuments">
                <xsd:annotation>
                    <xsd:documentation>Documents/messages issued within an enterprise for the for the purpose of collection of production and other internal statistics, and for other administration purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectMasterSchedule">
                <xsd:annotation>
                    <xsd:documentation>A high level, all encompassing master schedule of activities to complete a project.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PricedAlternateTenderBillOfQuantity">
                <xsd:annotation>
                    <xsd:documentation>A priced tender based upon an alternate specification.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedPricedBillOfQuantity">
                <xsd:annotation>
                    <xsd:documentation>An estimate based upon a detailed, quantity based specification (bill of quantity).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DraftBillOfQuantity">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing a draft bill of quantity, issued in an unpriced form.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditCollectionInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction for the collection of the documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForAnAmendmentOfADocumentaryCredit">
                <xsd:annotation>
                    <xsd:documentation>Request for an amendment of a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAmendmentInformation">
                <xsd:annotation>
                    <xsd:documentation>Documentary credit amendment information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviceOfAnAmendmentOfADocumentaryCredit">
                <xsd:annotation>
                    <xsd:documentation>Advice of an amendment of a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponseToAnAmendmentOfADocumentaryCredit">
                <xsd:annotation>
                    <xsd:documentation>Response to an amendment of a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditIssuanceInformation">
                <xsd:annotation>
                    <xsd:documentation>Provides information on documentary credit issuance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DirectPaymentValuationRequest">
                <xsd:annotation>
                    <xsd:documentation>Request to establish a direct payment valuation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DirectPaymentValuation">
                <xsd:annotation>
                    <xsd:documentation>Document/message addressed, for instance, by a general contractor to the owner, in order that a direct payment be made to a subcontractor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProvisionalPaymentValuation">
                <xsd:annotation>
                    <xsd:documentation>Document/message establishing a provisional payment valuation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentValuation">
                <xsd:annotation>
                    <xsd:documentation>Document/message establishing the financial elements of a situation of works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuantityValuation">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing a confirmed assessment, by quantity, of the completed work for a construction contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuantityValuationRequest">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing an initial assessment, by quantity, of the completed work for a construction contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractBillOfQuantities-Boq">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing a formal specification identifying quantities and prices that are the basis of a contract for a construction project. BOQ means: Bill of quantity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnpricedBillOfQuantity">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing a detailed, quantity based specification, issued in an unpriced form to invite tender prices.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PricedTenderBoq">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing a detailed, quantity based specification, updated with prices to form a tender submission for a construction contract. BOQ means: Bill of quantity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Enquiry">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a party interested in the purchase of goods specified therein and indicating particular, desirable conditions regarding delivery terms, etc., addressed to a prospective supplier with a view to obtaining an offer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterimApplicationForPayment">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing a provisional assessment in support of a request for payment for completed work for a construction contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgreementToPay">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which the debtor expresses the intention to pay.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForFinancialCancellation">
                <xsd:annotation>
                    <xsd:documentation>The message is a request for financial cancellation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-AuthorisedDirectDebits">
                <xsd:annotation>
                    <xsd:documentation>The message contains pre-authorised direct debit(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LetterOfIntent">
                <xsd:annotation>
                    <xsd:documentation>Document/message by means of which a buyer informs a seller that the buyer intends to enter into contractual negotiations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApprovedUnpricedBillOfQuantity">
                <xsd:annotation>
                    <xsd:documentation>Document/message providing an approved detailed, quantity based specification (bill of quantity), in an</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentValuationForUnscheduledItems">
                <xsd:annotation>
                    <xsd:documentation>A payment valuation for unscheduled items.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalPaymentRequestBasedOnCompletionOfWork">
                <xsd:annotation>
                    <xsd:documentation>The final payment request of a series of payment requests submitted upon completion of all the work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentRequestForCompletedUnits">
                <xsd:annotation>
                    <xsd:documentation>A request for payment for completed units.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Order">
                <xsd:annotation>
                    <xsd:documentation>Document/message by means of which a buyer initiates a transaction with a seller involving the supply of goods or services as specified, according to conditions set out in an offer, or otherwise known to the buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BlanketOrder">
                <xsd:annotation>
                    <xsd:documentation>Usage of document/message for general order purposes with later split into quantities and delivery dates and maybe delivery locations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpotOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message ordering the remainder of a production's batch.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message for goods in leasing contracts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RushOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message for urgent ordering.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message to order repair of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CallOffOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message to provide split quantities and delivery dates referring to a previous blanket order.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsignmentOrder">
                <xsd:annotation>
                    <xsd:documentation>Order to deliver goods into stock with agreement on payment when goods are sold out of this stock.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SampleOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message to order samples.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SwapOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message informing buyer or seller of the replacement of goods previously ordered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOrderChangeRequest">
                <xsd:annotation>
                    <xsd:documentation>Change to an purchase order already sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOrderResponse">
                <xsd:annotation>
                    <xsd:documentation>Response to an purchase order already received.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HireOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message for hiring human resources or renting goods or equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SparePartsOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message to order spare parts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CampaignPriceSalesCatalogue">
                <xsd:annotation>
                    <xsd:documentation>A price/sales catalogue containing special prices which are valid only for a specified period or under specified conditions.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerList">
                <xsd:annotation>
                    <xsd:documentation>Document or message issued by party identifying the containers for which they are responsible.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryForecast">
                <xsd:annotation>
                    <xsd:documentation>A message which enables the transmission of delivery or product forecasting requirements.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrossDockingServicesOrder">
                <xsd:annotation>
                    <xsd:documentation>A document or message to order cross docking services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-Pre-AuthorisedDirectDebits">
                <xsd:annotation>
                    <xsd:documentation>The message contains non-pre-authorised direct debit(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RejectedDirectDebits">
                <xsd:annotation>
                    <xsd:documentation>The message contains rejected direct debit(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryInstructions">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a buyer giving instructions regarding the details of the delivery of goods ordered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliverySchedule">
                <xsd:annotation>
                    <xsd:documentation>Usage of DELFOR-message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryJust-In-Time">
                <xsd:annotation>
                    <xsd:documentation>Usage of DELJIT-message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-AuthorisedDirectDebitRequests">
                <xsd:annotation>
                    <xsd:documentation>The message contains pre-authorised direct debit request(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-Pre-AuthorisedDirectDebitRequests">
                <xsd:annotation>
                    <xsd:documentation>The message contains non-pre-authorised direct debit request(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryRelease">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a buyer releasing the despatch of goods after receipt of the Ready for despatch advice from the seller.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementOfALetterOfCredit">
                <xsd:annotation>
                    <xsd:documentation>Settlement of a letter of credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankToBankFundsTransfer">
                <xsd:annotation>
                    <xsd:documentation>The message is a bank to bank funds transfer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerPaymentOrders">
                <xsd:annotation>
                    <xsd:documentation>The message contains customer payment order(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LowValuePaymentOrders">
                <xsd:annotation>
                    <xsd:documentation>The message contains low value payment order(s) only.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrewListDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Declaration regarding crew members aboard the conveyance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Inquiry">
                <xsd:annotation>
                    <xsd:documentation>This is a request for information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponseToPreviousBankingStatusMessage">
                <xsd:annotation>
                    <xsd:documentation>A response to a previously sent banking status message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectMasterPlan">
                <xsd:annotation>
                    <xsd:documentation>A high level, all encompassing master plan to complete a project.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectPlan">
                <xsd:annotation>
                    <xsd:documentation>A plan for project work to be completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectSchedule">
                <xsd:annotation>
                    <xsd:documentation>A schedule of project activities to be completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectPlanningAvailableResources">
                <xsd:annotation>
                    <xsd:documentation>Available resources for project planning purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectPlanningCalendar">
                <xsd:annotation>
                    <xsd:documentation>Work calendar information for project planning purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StandingOrder">
                <xsd:annotation>
                    <xsd:documentation>An order to supply fixed quantities of products at fixed regular intervals.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoMovementEventLog">
                <xsd:annotation>
                    <xsd:documentation>A document detailing times and dates of events pertaining to a cargo movement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoAnalysisVoyageReport">
                <xsd:annotation>
                    <xsd:documentation>An analysis of the cargo for a voyage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SelfBilledCreditNote">
                <xsd:annotation>
                    <xsd:documentation>A document which indicates that the customer is claiming credit in a self billing environment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedCreditNote-GoodsAndServices">
                <xsd:annotation>
                    <xsd:documentation>Credit note for goods and services that covers multiple transactions involving more than one invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryAdjustmentStatusReport">
                <xsd:annotation>
                    <xsd:documentation>A message detailing statuses related to the adjustment of inventory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentMovementInstruction">
                <xsd:annotation>
                    <xsd:documentation>Instruction to perform one or more different movements of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentMovementReport">
                <xsd:annotation>
                    <xsd:documentation>Report on one or more different movements of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentStatusChangeReport">
                <xsd:annotation>
                    <xsd:documentation>Report on one or more changes of status associated with an item or items of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FumigationCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate attesting that fumigation has been performed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WineCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate attesting to the quality, origin or appellation of wine.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WoolHealthCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate attesting that wool is free from specified risks to human or animal health.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryNote">
                <xsd:annotation>
                    <xsd:documentation>Paper document attached to a consignment informing the receiving party about contents of this consignment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackingList">
                <xsd:annotation>
                    <xsd:documentation>Document/message specifying the distribution of goods in individual packages (in trade environment the despatch advice message is used for the packing list).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewCodeRequest">
                <xsd:annotation>
                    <xsd:documentation>Requesting a new code.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CodeChangeRequest">
                <xsd:annotation>
                    <xsd:documentation>Request a change to an existing code.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SimpleDataElementRequest">
                <xsd:annotation>
                    <xsd:documentation>Requesting a new simple data element.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SimpleDataElementChangeRequest">
                <xsd:annotation>
                    <xsd:documentation>Request a change to an existing simple data element.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompositeDataElementRequest">
                <xsd:annotation>
                    <xsd:documentation>Requesting a new composite data element.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompositeDataElementChangeRequest">
                <xsd:annotation>
                    <xsd:documentation>Request a change to an existing composite data element.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SegmentRequest">
                <xsd:annotation>
                    <xsd:documentation>Request a new segment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SegmentChangeRequest">
                <xsd:annotation>
                    <xsd:documentation>Requesting a change to an existing segment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewMessageRequest">
                <xsd:annotation>
                    <xsd:documentation>Request for a new message (NMR).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessageInDevelopmentRequest">
                <xsd:annotation>
                    <xsd:documentation>Requesting a Message in Development (MiD).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ModificationOfExistingMessage">
                <xsd:annotation>
                    <xsd:documentation>Requesting a change to an existing message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrackingNumberAssignmentReport">
                <xsd:annotation>
                    <xsd:documentation>Report of assigned tracking numbers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UserDirectoryDefinition">
                <xsd:annotation>
                    <xsd:documentation>Document/message defining the contents of a user directory set or parts thereof.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnitedNationsStandardMessageRequest">
                <xsd:annotation>
                    <xsd:documentation>Requesting a United Nations Standard Message (UNSM).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceDirectoryDefinition">
                <xsd:annotation>
                    <xsd:documentation>Document/message defining the contents of a service directory set or parts thereof.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusReport">
                <xsd:annotation>
                    <xsd:documentation>Message covers information about the status.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KanbanSchedule">
                <xsd:annotation>
                    <xsd:documentation>Message to describe a Kanban schedule.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductDataMessage">
                <xsd:annotation>
                    <xsd:documentation>A message to submit master data, a set of data that is rarely changed, to identify and describe products a supplier offers to their (potential) customer or buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AClaimForPartsAndOrLabourCharges">
                <xsd:annotation>
                    <xsd:documentation>A claim for parts and/or labour charges incurred .</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryScheduleResponse">
                <xsd:annotation>
                    <xsd:documentation>A message providing a response to a previously transmitted delivery schedule.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionRequest">
                <xsd:annotation>
                    <xsd:documentation>A message requesting a party to inspect items.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionReport">
                <xsd:annotation>
                    <xsd:documentation>A message informing a party of the results of an inspection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationAcknowledgementAndErrorReport">
                <xsd:annotation>
                    <xsd:documentation>A message used by an application to acknowledge reception of a message and/or to report any errors.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceVariationInvoice">
                <xsd:annotation>
                    <xsd:documentation>An invoice which requests payment for the difference in price between an original invoice and the result of the application of a price variation formula.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditNoteForPriceVariation">
                <xsd:annotation>
                    <xsd:documentation>A credit note which is issued against a price variation invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstructionToCollect">
                <xsd:annotation>
                    <xsd:documentation>A message instructing a party to collect goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DangerousGoodsList">
                <xsd:annotation>
                    <xsd:documentation>Listing of all details of dangerous goods carried.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationRenewal">
                <xsd:annotation>
                    <xsd:documentation>Code specifying the continued validity of previously submitted registration information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationChange">
                <xsd:annotation>
                    <xsd:documentation>Code specifying the modification of previously submitted registration information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponseToRegistration">
                <xsd:annotation>
                    <xsd:documentation>Code specifying a response to an occurrence of a registration message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImplementationGuideline">
                <xsd:annotation>
                    <xsd:documentation>A document specifying the criterion and format for exchanging information in an electronic data interchange syntax.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForTransfer">
                <xsd:annotation>
                    <xsd:documentation>Document/message is a request for transfer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostPerformanceReport">
                <xsd:annotation>
                    <xsd:documentation>A report to convey cost performance data for a project or contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationErrorAndAcknowledgement">
                <xsd:annotation>
                    <xsd:documentation>A message to inform a message issuer that a previously sent message has been received by the addressee's application, or that a previously sent message has been rejected by the addressee's application.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CashPoolFinancialStatement">
                <xsd:annotation>
                    <xsd:documentation>A financial statement for a cash pool.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SequencedDeliverySchedule">
                <xsd:annotation>
                    <xsd:documentation>Message to describe a sequence of product delivery.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DelcredereCreditNote">
                <xsd:annotation>
                    <xsd:documentation>A credit note sent to the party paying on behalf of a number of buyers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OfferQuotation">
                <xsd:annotation>
                    <xsd:documentation>Document/message which, with a view to concluding a contract, sets out the conditions under which the goods are offered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForQuote">
                <xsd:annotation>
                    <xsd:documentation>Document/message requesting a quote on specified goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcknowledgementMessage">
                <xsd:annotation>
                    <xsd:documentation>Message providing acknowledgement information at the business application level concerning the processing of a message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationErrorMessage">
                <xsd:annotation>
                    <xsd:documentation>Message indicating that a message was rejected due to errors encountered at the application level.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoMovementVoyageSummary">
                <xsd:annotation>
                    <xsd:documentation>A consolidated voyage summary which contains the information in a certificate of analysis, a voyage analysis and a cargo movement time log for a voyage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contract">
                <xsd:annotation>
                    <xsd:documentation>Document/message evidencing an agreement between the seller and the buyer for the supply of goods or services; its effects are equivalent to those of an order followed by an acknowledgement of order.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForUsageOfBerthOrMooringFacilities">
                <xsd:annotation>
                    <xsd:documentation>Document to apply for usage of berth or mooring facilities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForDesignationOfBerthingPlaces">
                <xsd:annotation>
                    <xsd:documentation>Document to apply for designation of berthing places.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForShiftingFromTheDesignatedPlaceInPort">
                <xsd:annotation>
                    <xsd:documentation>Document to apply for shifting from the designated place in port.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplementaryDocumentForApplicationForCargoOperationOfDangerousGoods">
                <xsd:annotation>
                    <xsd:documentation>Supplementary document to apply for cargo operation of dangerous goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcknowledgementOfOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message acknowledging an undertaking to fulfil an order and confirming conditions or acceptance of conditions.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplementaryDocumentForApplicationForTransportOfDangerousGoods">
                <xsd:annotation>
                    <xsd:documentation>Supplementary document to apply for transport of dangerous goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OpticalCharacterReadingOcrPayment">
                <xsd:annotation>
                    <xsd:documentation>Payment effected by an Optical Character Reading (OCR) document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminarySalesReport">
                <xsd:annotation>
                    <xsd:documentation>Preliminary sales report sent before all the information is available.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEmergencyCard">
                <xsd:annotation>
                    <xsd:documentation>Official document specifying, for a given dangerous goods item, information such as nature of hazard, protective devices, actions to be taken in case of accident, spillage or fire and first aid to be given.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProformaInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message serving as a preliminary invoice, containing - on the whole - the same information as the final invoice, but not actually claiming payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartialInvoice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatingInstructions">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NameProductPlate">
                <xsd:annotation>
                    <xsd:documentation>Plates on goods identifying and describing an article.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-InsuranceCedingBordereau">
                <xsd:annotation>
                    <xsd:documentation>The document or message contains a bordereau describing co-insurance ceding information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForDeliveryInstructions">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a supplier requesting instructions from the buyer regarding the details of the delivery of goods ordered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialInvoiceWhichIncludesAPackingList">
                <xsd:annotation>
                    <xsd:documentation>Commercial transaction (invoice) will include a packing list.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeData">
                <xsd:annotation>
                    <xsd:documentation>Document/message is for trade data.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeclarationForCargoExamination">
                <xsd:annotation>
                    <xsd:documentation>Declaration provided to customs for cargo examination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeclarationForCargoExaminationAlternate">
                <xsd:annotation>
                    <xsd:documentation>Alternate declaration provided to customs for cargo examination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookingRequest">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a supplier to a carrier requesting space to be reserved for a specified consignment, indicating desirable conveyance, despatch time, etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsCrewAndConveyance">
                <xsd:annotation>
                    <xsd:documentation>Document/message contains information regarding the crew list and conveyance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsSummaryDeclarationWithCommercialDetailAlternate">
                <xsd:annotation>
                    <xsd:documentation>Alternate Customs declaration summary with commercial transaction details.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemsBookedToAFinancialAccountReport">
                <xsd:annotation>
                    <xsd:documentation>A message reporting items which have been booked to a financial account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportOfTransactionsWhichNeedFurtherInformationFromTheReceiver">
                <xsd:annotation>
                    <xsd:documentation>A message reporting transactions which need further information from the receiver.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingInstructions">
                <xsd:annotation>
                    <xsd:documentation>Document/message advising details of cargo and exporter's requirements for its physical movement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippersLetterOfInstructionsAir">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a consignor in which he gives details of a consignment of goods that enables an airline or its agent to prepare an air waybill.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportOfTransactionsForInformationOnly">
                <xsd:annotation>
                    <xsd:documentation>A message reporting transactions for information only.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CartageOrderLocalTransport">
                <xsd:annotation>
                    <xsd:documentation>Document/message giving instructions regarding local transport of goods, e.g. from the premises of an enterprise to those of a carrier undertaking further transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EdiAssociatedObjectAdministrationMessage">
                <xsd:annotation>
                    <xsd:documentation>A message giving additional information about the exchange of an EDI associated object.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReadyForDespatchAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a supplier informing a buyer that goods ordered are ready for despatch.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SummarySalesReport">
                <xsd:annotation>
                    <xsd:documentation>Sales report containing summaries for several earlier sent sales reports.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderStatusEnquiry">
                <xsd:annotation>
                    <xsd:documentation>A message enquiring the status of previously sent orders.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderStatusReport">
                <xsd:annotation>
                    <xsd:documentation>A message reporting the status of previously sent orders.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclarationRegardingTheInwardAndOutwardMovementOfVessel">
                <xsd:annotation>
                    <xsd:documentation>Document to declare inward and outward movement of a vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a supplier initiating the despatch of goods to a buyer (consignee).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message by means of which the seller or consignor informs the consignee about the despatch of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotificationOfUsageOfBerthOrMooringFacilities">
                <xsd:annotation>
                    <xsd:documentation>Document to notify usage of berth or mooring facilities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForVesselsEnteringIntoPortAreaInNight-Time">
                <xsd:annotation>
                    <xsd:documentation>Document to apply for vessel's entering into port area in night-time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotificationOfEmergencyShiftingFromTheDesignatedPlaceInPort">
                <xsd:annotation>
                    <xsd:documentation>Document to notify shifting from designated place in port once secured at the designated place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsSummaryDeclarationWithoutCommercialDetailAlternate">
                <xsd:annotation>
                    <xsd:documentation>Alternate Customs declaration summary without any commercial transaction details.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerformanceBond">
                <xsd:annotation>
                    <xsd:documentation>A document that guarantees performance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentBond">
                <xsd:annotation>
                    <xsd:documentation>A document that guarantees the payment of monies.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviceOfDistributionOfDocuments">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which the party responsible for the issue of a set of trade documents specifies the various recipients of originals and copies of these documents, with an indication of the number of copies distributed to each of them.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message claiming payment for goods or services supplied under conditions agreed between seller and buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message for providing credit information to the relevant party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommissionNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a seller specifies the amount of commission, the percentage of the invoice amount, or some other basis for the calculation of the commission to which a sales agent is entitled.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebitNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message for providing debit information to the relevant party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorrectedInvoice">
                <xsd:annotation>
                    <xsd:documentation>Commercial invoice that includes revised information differing from an earlier submission of the same invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedInvoice">
                <xsd:annotation>
                    <xsd:documentation>Commercial invoice that covers multiple transactions involving more than one vendor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrepaymentInvoice">
                <xsd:annotation>
                    <xsd:documentation>An invoice to pay amounts for goods and services in advance; these amounts will be subtracted from the final invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HireInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message for invoicing the hiring of human resources or renting goods or equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxInvoice">
                <xsd:annotation>
                    <xsd:documentation>An invoice for tax purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Self-BilledInvoice">
                <xsd:annotation>
                    <xsd:documentation>An invoice the invoicee is producing instead of the seller.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DelcredereInvoice">
                <xsd:annotation>
                    <xsd:documentation>An invoice sent to the party paying for a number of buyers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FactoredInvoice">
                <xsd:annotation>
                    <xsd:documentation>Invoice assigned to a third party for collection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseInvoice">
                <xsd:annotation>
                    <xsd:documentation>Usage of INVOIC-message for goods in leasing contracts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsignmentInvoice">
                <xsd:annotation>
                    <xsd:documentation>Commercial invoice that covers a transaction other than one involving a sale.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FactoredCreditNote">
                <xsd:annotation>
                    <xsd:documentation>Credit note related to assigned invoice(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialAccountSummaryResponse">
                <xsd:annotation>
                    <xsd:documentation>A document providing a response to a previously sent commercial account summary message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrossDockingDespatchAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document by means of which the supplier or consignor informs the buyer, consignee or the distribution centre about the despatch of goods for cross docking.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransshipmentDespatchAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document by means of which the supplier or consignor informs the buyer, consignee or the distribution centre about the despatch of goods for transshipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExceptionalOrder">
                <xsd:annotation>
                    <xsd:documentation>An order which falls outside the framework of an agreement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransshipmentOrder">
                <xsd:annotation>
                    <xsd:documentation>An order requesting the supply of products packed according to the final delivery point which will be moved across a dock in a distribution centre without further handling.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrossDockingOrder">
                <xsd:annotation>
                    <xsd:documentation>An order requesting the supply of products which will be de-consolidated in the distribution centre and re- consolidated according to final delivery location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeansOfTransportationAvailabilityInformation">
                <xsd:annotation>
                    <xsd:documentation>Information giving the various availabilities of a means of transportation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeansOfTransportationScheduleInformation">
                <xsd:annotation>
                    <xsd:documentation>Information giving the various schedules of a means of transportation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentDeliveryNotice">
                <xsd:annotation>
                    <xsd:documentation>Notification regarding the delivery of transport equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstructionsForBankTransfer">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing instructions from a customer to his bank to pay an amount in a specified currency to a nominated party in another country by a method either specified (e.g. teletransmission, air mail) or left to the discretion of the bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForBankersDraft">
                <xsd:annotation>
                    <xsd:documentation>Application by a customer to his bank to issue a banker's draft stating the amount and currency of the draft, the name of the payee and the place and country of payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectionPaymentAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank advises that a collection has been paid, giving details and methods of funds disposal.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditPaymentAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank advises payment under a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAcceptanceAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank advises acceptance under a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditNegotiationAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank advises negotiation under a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForBankersGuarantee">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a customer requests his bank to issue a guarantee in favour of a nominated party in another country, stating the amount and currency and the specific conditions of the guarantee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankersGuarantee">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a bank undertakes to pay out a limited amount of money to a designated party, on conditions stated therein (other than those laid down in the Uniform Customs Practice).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditLetterOfIndemnity">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a beneficiary of a documentary credit accepts responsibility for non-compliance with the terms and conditions of the credit, and undertakes to refund the money received under the credit, with interest and charges accrued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreadviceOfACredit">
                <xsd:annotation>
                    <xsd:documentation>Preadvice indicating a credit to happen in the future.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectionOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank is instructed (or requested) to handle financial and/or commercial documents in order to obtain acceptance and/or payment, or to deliver documents on such other terms and conditions as may be specified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentsPresentationForm">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a draft or similar instrument and/or commercial documents are presented to a bank for acceptance, discounting, negotiation, payment or collection, whether or not against a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing information needed to initiate the payment. It may cover the financial settlement for one or more commercial trade transactions. A payment order is an instruction to the ordered bank to arrange for the payment of one specified amount to the beneficiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtendedPaymentOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing information needed to initiate the payment. It may cover the financial settlement for several commercial trade transactions, which it is possible to specify in a special payments detail part. It is an instruction to the ordered bank to arrange for the payment of one specified amount to the beneficiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MultiplePaymentOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing a payment order to debit one or more accounts and to credit one or more beneficiaries.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message sent by an account servicing institution to one of its account owners, to inform the account owner of an entry which has been or will be credited to its account for a specified amount on the date indicated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtendedCreditAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message sent by an account servicing institution to one of its account owners, to inform the account owner of an entry that has been or will be credited to its account for a specified amount on the date indicated. It provides extended commercial information concerning the relevant remittance advice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebitAdvice">
                <xsd:annotation>
                    <xsd:documentation>Advice on a debit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReversalOfDebit">
                <xsd:annotation>
                    <xsd:documentation>Reversal of debit accounting entry by bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReversalOfCredit">
                <xsd:annotation>
                    <xsd:documentation>Reversal of credit accounting entry by bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditApplication">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank is requested to issue a documentary credit on the conditions specified therein.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCredit">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a bank states that it has issued a documentary credit under which the beneficiary is to obtain payment, acceptance or negotiation on compliance with certain terms and conditions and against presentation of stipulated documents and such drafts as may be specified. The credit may or may not be confirmed by another bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditNotification">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by an advising bank in order to transmit a documentary credit to a beneficiary, or to another advising bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditTransferAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank advises that (part of) a documentary credit is being or has been transferred in favour of a second beneficiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAmendmentNotification">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank advises that the terms and conditions of a documentary credit have been amended.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAmendment">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a bank notifies a beneficiary of the details of an amendment to the terms and conditions of a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RemittanceAdvice">
                <xsd:annotation>
                    <xsd:documentation>Document/message advising of the remittance of payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankersDraft">
                <xsd:annotation>
                    <xsd:documentation>Draft drawn in favour of a third party either by one bank on another bank, or by a branch of a bank on its head office (or vice versa) or upon another branch of the same bank. In either case, the draft should comply with the specifications laid down for cheques in the country in which it is to be payable.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfExchange">
                <xsd:annotation>
                    <xsd:documentation>Document/message, issued and signed in conformity with the applicable legislation, which contains an unconditional order whereby the drawer directs the drawee to pay a definite sum of money to the payee or to his order, on demand or at a definite time, against the surrender of the document itself.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromissoryNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message, issued and signed in conformity with the applicable legislation, which contains an unconditional promise whereby the maker undertakes to pay a definite sum of money to the payee or to his order, on demand or at a definite time, against the surrender of the document itself.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialStatementOfAccount">
                <xsd:annotation>
                    <xsd:documentation>Statement giving the status of a financial account.  Note:  1.  This code value will be removed effective with directory D.00B.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatementOfAccountMessage">
                <xsd:annotation>
                    <xsd:documentation>Usage of STATAC-message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued to the insured certifying that insurance has been effected and that a policy has been issued. Such a certificate for a particular cargo is primarily used when good are insured under the terms of a floating or an open policy; at the request of the insured it can be exchanged for a policy.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsurancePolicy">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the insurer evidencing an agreement to insure and containing the conditions of the agreement concluded whereby the insurer undertakes for a specific fee to indemnify the insured for the losses arising out of the perils and accidents specified in the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceDeclarationSheetBordereau">
                <xsd:annotation>
                    <xsd:documentation>A document/message used when an insured reports to his insurer details of individual shipments which are covered by an insurance contract - an open cover or a floating policy - between the parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsurersInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by an insurer specifying the cost of an insurance which has been effected and claiming payment therefore.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoverNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by an insurer (insurance broker, agent, etc.) to notify the insured that his insurance have been carried out.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardingInstructions">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued to a freight forwarder, giving instructions regarding the action to be taken by the forwarder for the forwarding of goods described therein.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersAdviceToImportAgent">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a freight forwarder in an exporting country advising his counterpart in an importing country about the forwarding of goods described therein.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersAdviceToExporter">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a freight forwarder informing an exporter of the action taken in fulfillment of instructions received.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersInvoice">
                <xsd:annotation>
                    <xsd:documentation>Invoice issued by a freight forwarder specifying services rendered and costs incurred and claiming payment therefore.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersCertificateOfReceipt">
                <xsd:annotation>
                    <xsd:documentation>Non-negotiable document issued by a forwarder to certify that he has assumed control of a specified consignment, with irrevocable instructions to send it to the consignee indicated in the document or to hold it at his disposal. E.g. FIATA-FCR.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message provided by the shipper or his agent to the carrier, multimodal transport operator, terminal or other receiving authority, giving information about export consignments offered for transport, and providing for the necessary receipts and declarations of liability. (Sometimes a multipurpose cargo handling document also fulfilling the functions of document 632, 633, 650 and 655).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersWarehouseReceipt">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a forwarder acting as Warehouse Keeper acknowledging receipt of goods placed in a warehouse, and stating or referring to the conditions which govern the warehousing and the release of goods. The document contains detailed provisions regarding the rights of holders-by-endorsement, transfer of ownership, etc. E.g. FIATA-FWR.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsReceipt">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a port, warehouse/shed, or terminal operator acknowledging receipt of goods specified therein on conditions stated or referred to in the document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PortChargesDocuments">
                <xsd:annotation>
                    <xsd:documentation>Documents/messages specifying services rendered, storage and handling costs, demurrage and other charges due to the owner of goods described therein.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarehouseWarrant">
                <xsd:annotation>
                    <xsd:documentation>Negotiable receipt document, issued by a Warehouse Keeper to a person placing goods in a warehouse and conferring title to the goods stored.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a party entitled to authorize the release of goods specified therein to a named consignee, to be retained by the custodian of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HandlingOrder">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a cargo handling organization (port administration, terminal operator, etc.) for the removal or other handling of goods under their care.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GatePass">
                <xsd:annotation>
                    <xsd:documentation>Document/message authorizing goods specified therein to be brought out of a fenced-in port or terminal area.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Waybill">
                <xsd:annotation>
                    <xsd:documentation>Non-negotiable document evidencing the contract for the transport of cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UniversalMultipurposeTransportDocument">
                <xsd:annotation>
                    <xsd:documentation>Document/message evidencing a contract of carriage covering the movement of goods by any mode of transport, or combination of modes, for national as well as international transport, under any applicable international convention or national law and under the conditions of carriage of any carrier or transport operator undertaking or arranging the transport referred to in the document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsReceiptCarriage">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a carrier or a carrier's agent, acknowledging receipt for carriage of goods specified therein on conditions stated or referred to in the document, enabling the carrier to issue a transport document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HouseWaybill">
                <xsd:annotation>
                    <xsd:documentation>The document made out by an agent/consolidator which evidences the contract between the shipper and the agent/consolidator for the arrangement of carriage of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MasterBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>A bill of lading issued by the master of a vessel (in actuality the owner or charterer of the vessel). It could cover a number of house bills.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Negotiable document/message which evidences a contract of carriage by sea and the taking over or loading of goods by carrier, and by which carrier undertakes to deliver goods against surrender of the document. A provision in the document that goods are to be delivered to the order of a named person, or to order, or to bearer, constitutes such an undertaking.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLadingOriginal">
                <xsd:annotation>
                    <xsd:documentation>The original of the bill of lading issued by a transport company. When issued by the maritime industry it could signify ownership of the cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLadingCopy">
                <xsd:annotation>
                    <xsd:documentation>A copy of the bill of lading issued by a transport company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyContainerBill">
                <xsd:annotation>
                    <xsd:documentation>Bill of lading indicating an empty container.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TankerBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Document which evidences a transport of liquid bulk cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SeaWaybill">
                <xsd:annotation>
                    <xsd:documentation>Non-negotiable document which evidences a contract for the carriage of goods by sea and the taking over of the goods by the carrier, and by which the carrier undertakes to deliver the goods to the consignee named in the document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InlandWaterwayBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Negotiable transport document made out to a named person, to order or to bearer, signed by the carrier and handed to the sender after receipt of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-NegotiableMaritimeTransportDocumentGeneric">
                <xsd:annotation>
                    <xsd:documentation>Non-negotiable document which evidences a contract for the carriage of goods by sea and the taking over or loading of the goods by the carrier, and by which the carrier undertakes to deliver the goods to the consignee named in the document. E.g. Sea waybill. Remark: Synonymous with "straight" or "non-negotiable Bill of lading" used in certain countries, e.g. Canada.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MatesReceipt">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a ship's officer to acknowledge that a specified consignment has been received on board a vessel, and the apparent condition of the goods; enabling the carrier to issue a Bill of lading.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HouseBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>The bill of lading issued not by the carrier but by the freight forwarder/consolidator known by the carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LetterOfIndemnityForNon-SurrenderOfBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a commercial party or a bank of an insurance company accepting responsibility to the beneficiary of the indemnity in accordance with the terms thereof.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Non-negotiable document issued by a freight forwarder evidencing a contract for the carriage of goods by sea and the taking over or loading of the goods by the freight forwarder, and by which the freight forwarder undertakes to deliver the goods to the consignee named in the document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RailConsignmentNoteGenericTerm">
                <xsd:annotation>
                    <xsd:documentation>Transport document constituting a contract for the carriage of goods between the sender and the carrier (the railway). For international rail traffic, this document must conform to the model prescribed by the international conventions concerning carriage of goods by rail, e.g. CIM Convention, SMGS Convention.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoadList-Smgs">
                <xsd:annotation>
                    <xsd:documentation>Accounting document, one copy of which is drawn up for each consignment note; it accompanies the consignment over the whole route and is a rail transport document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EscortOfficialRecognition">
                <xsd:annotation>
                    <xsd:documentation>Document/message which gives right to the owner to exert all functions normally transferred to a guard in a train by which an escorted consignment is transported.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RechargingDocument">
                <xsd:annotation>
                    <xsd:documentation>Fictitious transport document regarding a previous transport, enabling a carrier's agent to give to another carrier's agent (in a different country) the possibility to collect charges relating to the original transport (rail environment).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoadConsignmentNote">
                <xsd:annotation>
                    <xsd:documentation>Transport document/message which evidences a contract between a carrier and a sender for the carriage of goods by road (generic term). Remark: For international road traffic, this document must contain at least the particulars prescribed by the convention on the contract for the international carriage of goods by road (CMR).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirWaybill">
                <xsd:annotation>
                    <xsd:documentation>Document/message made out by or on behalf of the shipper which evidences the contract between the shipper and carrier(s) for carriage of goods over routes of the carrier(s) and which is identified by the airline prefix issuing the document plus a serial (IATA).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MasterAirWaybill">
                <xsd:annotation>
                    <xsd:documentation>Document/message made out by or on behalf of the agent/consolidator which evidences the contract between the agent/consolidator and carrier(s) for carriage of goods over routes of the carrier(s) for a consignment consisting of goods originated by more than one shipper (IATA).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubstituteAirWaybill">
                <xsd:annotation>
                    <xsd:documentation>A temporary air waybill which contains only limited information because of the absence of the original.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrewsEffectsDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Declaration to Customs regarding the personal effects of crew members aboard the conveyance; equivalent to IMO FAL 4.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PassengerList">
                <xsd:annotation>
                    <xsd:documentation>Declaration to Customs regarding passengers aboard the conveyance; equivalent to IMO FAL 6.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryNoticeRailTransport">
                <xsd:annotation>
                    <xsd:documentation>Document/message created by the consignor or by the departure station, joined to the transport or sent to the consignee, giving the possibility to the consignee or the arrival station to attest the delivery of the goods. The document must be returned to the consignor or to the departure station.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchNotePostParcels">
                <xsd:annotation>
                    <xsd:documentation>Document/message which, according to Article 106 of the "Agreement concerning Postal Parcels" under the UPU convention, is to accompany post parcels.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MultimodalCombinedTransportDocumentGeneric">
                <xsd:annotation>
                    <xsd:documentation>A transport document used when more than one mode of transportation is involved in the movement of cargo. It is a contract of carriage and receipt of the cargo for a multimodal transport. It indicates the place where the responsible transport company in the move takes responsibility for the cargo, the place where the responsibility of this transport company in the move ends and the conveyances involved.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThroughBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Bill of lading which evidences a contract of carriage from one place to another in separate stages of which at least one stage is a sea transit, and by which the issuing carrier accepts responsibility for the carriage as set forth in the through bill of lading.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardersCertificateOfTransport">
                <xsd:annotation>
                    <xsd:documentation>Negotiable document/message issued by a forwarder to certify that he has taken charge of a specified consignment for despatch and delivery in accordance with the consignor's instructions, as indicated in the document, and that he accepts responsibility for delivery of the goods to the holder of the document through the intermediary of a delivery agent of his choice. E.g. FIATA-FCT.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CombinedTransportDocumentGeneric">
                <xsd:annotation>
                    <xsd:documentation>Negotiable or non-negotiable document evidencing a contract for the performance and/or procurement of performance of combined transport of goods and bearing on its face either the heading "Negotiable combined transport document issued subject to Uniform Rules for a Combined Transport Document (ICC Brochure No. 298)" or the heading "Non-negotiable Combined Transport Document issued subject to Uniform Rules for a Combined Transport Document (ICC Brochure No. 298)".</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MultimodalTransportDocumentGeneric">
                <xsd:annotation>
                    <xsd:documentation>Document/message which evidences a multimodal transport contract, the taking in charge of the goods by the multimodal transport operator, and an undertaking by him to deliver the goods in accordance with the terms of the contract. (International Convention on Multimodal Transport of Goods).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CombinedTransportBillOfLadingMultimodalBillOfLading">
                <xsd:annotation>
                    <xsd:documentation>Document which evidences a multimodal transport contract, the taking in charge of the goods by the multimodal transport operator, and an undertaking by him to deliver the goods in accordance with the terms of the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookingConfirmation">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a carrier to confirm that space has been reserved for a consignment in means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CallingForwardNotice">
                <xsd:annotation>
                    <xsd:documentation>Instructions for release or delivery of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a transport operation specifying freight costs and charges incurred for a transport operation and stating conditions of payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalNoticeGoods">
                <xsd:annotation>
                    <xsd:documentation>Notification from the carrier to the consignee in writing, by telephone or by any other means (express letter, message, telegram, etc.) informing him that a consignment addressed to him is being or will shortly be held at his disposal at a specified point in the place of destination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoticeOfCircumstancesPreventingDeliveryGoods">
                <xsd:annotation>
                    <xsd:documentation>Request made by the carrier to the sender, or, as the case may be, the consignee, for instructions as to the disposal of the consignment when circumstances prevent delivery and the return of the goods has not been requested by the consignor in the transport document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoticeOfCircumstancesPreventingTransportGoods">
                <xsd:annotation>
                    <xsd:documentation>Request made by the carrier to the sender, or, the consignee as the case may be, for instructions as to the disposal of the goods when circumstances prevent transport before departure or en route, after acceptance of the consignment concerned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryNoticeGoods">
                <xsd:annotation>
                    <xsd:documentation>Notification in writing, sent by the carrier to the sender, to inform him at his request of the actual date of delivery of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoManifest">
                <xsd:annotation>
                    <xsd:documentation>Listing of goods comprising the cargo carried in a means of transport or in a transport-unit. The cargo manifest gives the commercial particulars of the goods, such as transport document numbers, consignors, consignees, shipping marks, number and kind of packages and descriptions and quantities of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightManifest">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing the same information as a cargo manifest, and additional details on freight amounts, charges, etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bordereau">
                <xsd:annotation>
                    <xsd:documentation>Document/message used in road transport, listing the cargo carried on a road vehicle, often referring to appended copies of Road consignment note.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerManifestUnitPackingList">
                <xsd:annotation>
                    <xsd:documentation>Document/message specifying the contents of particular freight containers or other transport units, prepared by the party responsible for their loading into the container or unit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargesNote">
                <xsd:annotation>
                    <xsd:documentation>Document used by the rail organization to indicate freight charges or additional charges in each case where the departure station is not able to calculate the charges for the total voyage (e.g. tariff not yet updated, part of voyage not covered by the tariff). This document must be considered as joined to the transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviceOfCollection">
                <xsd:annotation>
                    <xsd:documentation>Document that is joined to the transport or sent by separate means, giving to the departure rail organization the proof that the cash-on delivery amount has been encashed by the arrival rail organization before reimbursement of the consignor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SafetyOfShipCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document certifying a ship's safety to a specified date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SafetyOfRadioCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document certifying the safety of a ship's radio facilities to a specified date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SafetyOfEquipmentCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document certifying the safety of a ship's equipment to a specified date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CivilLiabilityForOilCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document declaring a ship owner's liability for oil propelling or carried on a vessel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoadlineDocument">
                <xsd:annotation>
                    <xsd:documentation>Document specifying the limit of a ship's legal submersion under various conditions.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeratDocument">
                <xsd:annotation>
                    <xsd:documentation>Document certifying that a ship is free of rats, valid to a specified date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaritimeDeclarationOfHealth">
                <xsd:annotation>
                    <xsd:documentation>Document certifying the health condition on board a vessel, valid to a specified date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfRegistry">
                <xsd:annotation>
                    <xsd:documentation>Official certificate stating the vessel's registry.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipsStoresDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Declaration to Customs regarding the contents of the ship's stores (equivalent to IMO FAL 3) i.e. goods intended for consumption by passengers/crew on board vessels, aircraft or trains, whether or not sold or landed; goods necessary for operation/maintenance of conveyance, including fuel/lubricants, excluding spare parts/equipment (IMO).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportLicenceApplicationFor">
                <xsd:annotation>
                    <xsd:documentation>Application for a permit issued by a government authority permitting exportation of a specified commodity subject to specified conditions as quantity, country of destination, etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportLicence">
                <xsd:annotation>
                    <xsd:documentation>Permit issued by a government authority permitting exportation of a specified commodity subject to specified conditions as quantity, country of destination, etc. Synonym: Embargo permit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExchangeControlDeclarationExport">
                <xsd:annotation>
                    <xsd:documentation>Document/message completed by an exporter/seller as a means whereby the competent body may control that the amount of foreign exchange accrued from a trade transaction is repatriated in accordance with the conditions of payment and exchange control regulations in force.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchNoteModelT">
                <xsd:annotation>
                    <xsd:documentation>European community transit declaration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchNoteModelT1">
                <xsd:annotation>
                    <xsd:documentation>Transit declaration for goods circulating under internal community transit procedures (between European Union (EU) countries).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchNoteModelT2">
                <xsd:annotation>
                    <xsd:documentation>Ascertainment that the declared goods were originally produced in an European Union (EU) country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlDocumentT5">
                <xsd:annotation>
                    <xsd:documentation>Control document (export declaration) used particularly in case of re-sending without use with only VAT collection, refusal, unconformity with contract etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Re-SendingConsignmentNote">
                <xsd:annotation>
                    <xsd:documentation>Rail consignment note prepared by the consignor for the facilitation of an eventual return to the origin of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchNoteModelT2L">
                <xsd:annotation>
                    <xsd:documentation>Ascertainment that the declared goods were originally produced in an European Union (EU) country. May only be used for goods that are loaded on one single means of transport in one single departure point for one single delivery point.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsDeclarationForExportation">
                <xsd:annotation>
                    <xsd:documentation>Document/message by which goods are declared for export Customs clearance, conforming to the layout key set out at Appendix I to Annex C.1 concerning outright exportation to the Kyoto convention (CCC). Within a Customs union, "for despatch" may have the same meaning as "for exportation".</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoDeclarationDeparture">
                <xsd:annotation>
                    <xsd:documentation>Generic term, sometimes referred to as Freight declaration, applied to the documents providing the particulars required by the Customs concerning the cargo (freight) carried by commercial means of transport (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForGoodsControlCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message submitted to a competent body by party requesting a Goods control certificate to be issued in accordance with national or international standards, or conforming to legislation in the importing country, or as specified in the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsControlCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a competent body evidencing the quality of the goods described therein, in accordance with national or international standards, or conforming to legislation in the importing country, or as specified in the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForPhytosanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message submitted to a competent body by party requesting a Phytosanitary certificate to be issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhytosanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the competent body in the exporting country evidencing that plants, fruit, or vegetables are free from disease and fit for consumption and giving details on fumigation or other treatment to which they may have been subjected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SanitaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the competent authority in the exporting country evidencing that alimentary and animal products, including dead animals, are fit for human consumption, and giving details, when relevant, of controls undertaken.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VeterinaryCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the competent authority in the exporting country evidencing that live animals or birds are not infested or infected with disease, and giving details regarding their provenance, and of vaccinations and other treatment to which they have been subjected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForInspectionCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message submitted to a competent body by a party requesting an Inspection certificate to be issued in accordance with national or international standards, or conforming to legislation in the country in which it is required, or as specified in the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a competent body evidencing that the goods described therein have been inspected in accordance with national or international standards, in conformity with legislation in the country in which the inspection is required, or as specified in the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfOriginApplicationFor">
                <xsd:annotation>
                    <xsd:documentation>Document/message submitted to a competent body by an interested party requesting a Certificate of origin to be issued in accordance with relevant criteria, and on the basis of evidence of the origin of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfOrigin">
                <xsd:annotation>
                    <xsd:documentation>Document/message identifying goods, in which the authority or body authorized to issue it certifies expressly that the goods to which the certificate relates originate in a specific country. The word "country" may include a group of countries, a region or a part of a country. This certificate may also include a declaration by the manufacturer, producer, supplier, exporter or other competent person.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclarationOfOrigin">
                <xsd:annotation>
                    <xsd:documentation>Appropriate statement as to the origin of the goods, made in connection with their exportation by the manufacturer, producer, supplier, exporter or other competent person on the Commercial invoice or any other document relating to the goods (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegionalAppellationCertificate">
                <xsd:annotation>
                    <xsd:documentation>Certificate drawn up in accordance with the rules laid down by an authority or approved body, certifying that the goods described therein qualify for a designation specific to the given region (e.g. champagne, port wine, Parmesan cheese).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreferenceCertificateOfOrigin">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfOriginFormGsp">
                <xsd:annotation>
                    <xsd:documentation>Specific form of certificate of origin for goods qualifying for preferential treatment under the generalized system of preferences (includes a combined declaration of origin and certificate, form A).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsularInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message to be prepared by an exporter in his country and presented to a diplomatic representation of the importing country for endorsement and subsequently to be presented by the importer in connection with the import of the goods described therein.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DangerousGoodsDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by a consignor in accordance with applicable conventions or regulations, describing hazardous goods or materials for transport purposes, and stating that the latter have been packed and labelled in accordance with the provisions of the relevant conventions or regulations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatisticalDocumentExport">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which an exporter provides information about exported goods required by the body responsible for the collection of international trade statistics.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntrastatDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a declarant provides information about goods required by the body responsible for the collection of trade statistics.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryVerificationCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby an official authority (Customs or governmental) certifies that goods have been delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImportLicenceApplicationFor">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which an interested party applies to the competent body for authorization to import either a limited quantity of articles subject to import restrictions, or an unlimited quantity of such articles during a limited period, and specifies the kind of articles, their origin and value, etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImportLicence">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the competent body in accordance with import regulations in force, by which authorization is granted to a named party to import either a limited quantity of designated articles or an unlimited quantity of such articles during a limited period, under conditions specified in the document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeclarationWithoutCommercialDetail">
                <xsd:annotation>
                    <xsd:documentation>CUSDEC transmission that does not include data from the commercial detail section of the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeclarationWithCommercialAndItemDetail">
                <xsd:annotation>
                    <xsd:documentation>CUSDEC transmission that includes data from both the commercial detail and item detail sections of the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeclarationWithoutItemDetail">
                <xsd:annotation>
                    <xsd:documentation>CUSDEC transmission that does not include data from the item detail section of the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RelatedDocument">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiptCustoms">
                <xsd:annotation>
                    <xsd:documentation>Receipt for Customs duty/tax/fee paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForExchangeAllocation">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby an importer/buyer requests the competent body to allocate an amount of foreign exchange to be transferred to an exporter/seller in payment for goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignExchangePermit">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the competent body authorizing an importer/buyer to transfer an amount of foreign exchange to an exporter/seller in payment for goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExchangeControlDeclarationImport">
                <xsd:annotation>
                    <xsd:documentation>Document/message completed by an importer/buyer as a means for the competent body to control that a trade transaction for which foreign exchange has been allocated has been executed and that money has been transferred in accordance with the conditions of payment and the exchange control regulations in force.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsDeclarationForImportation">
                <xsd:annotation>
                    <xsd:documentation>Document/message by which goods are declared for import Customs clearance [sister entry of 830].</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsDeclarationForHomeUse">
                <xsd:annotation>
                    <xsd:documentation>Document/message by which goods are declared for import Customs clearance according to Annex B.1 (concerning clearance for home use) to the Kyoto convention (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsImmediateReleaseDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by an importer notifying Customs that goods have been removed from an importing means of transport to the importer's premises under a Customs- approved arrangement for immediate release, or requesting authorization to do so.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeliveryNote">
                <xsd:annotation>
                    <xsd:documentation>Document/message whereby a Customs authority releases goods under its control to be placed at the disposal of the party concerned. Synonym: Customs release note.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoDeclarationArrival">
                <xsd:annotation>
                    <xsd:documentation>Generic term, sometimes referred to as Freight declaration, applied to the documents providing the particulars required by the Customs concerning the cargo (freight) carried by commercial means of transport (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValueDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which a declarant (importer) states the invoice or other price (e.g. selling price, price of identical goods), and specifies costs for freight, insurance and packing, etc., terms of delivery and payment, any relationship with the trading partner, etc., for the purpose of determining the Customs value of goods imported.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsInvoice">
                <xsd:annotation>
                    <xsd:documentation>Document/message required by the Customs in an importing country in which an exporter states the invoice or other price (e.g. selling price, price of identical goods), and specifies costs for freight, insurance and packing, etc., terms of delivery and payment, for the purpose of determining the Customs value in the importing country of goods consigned to that country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDeclarationPostParcels">
                <xsd:annotation>
                    <xsd:documentation>Document/message which, according to Article 106 of the "Agreement concerning Postal Parcels" under the UPU Convention, must accompany post parcels and in which the contents of such parcels are specified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxDeclarationValueAddedTax">
                <xsd:annotation>
                    <xsd:documentation>Document/message in which an importer states the pertinent information required by the competent body for assessment of value-added tax.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxDeclarationGeneral">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing a general tax declaration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxDemand">
                <xsd:annotation>
                    <xsd:documentation>Document/message containing the demand of tax.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmbargoPermit">
                <xsd:annotation>
                    <xsd:documentation>Document/message giving the permission to export specified goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsDeclarationForCustomsTransit">
                <xsd:annotation>
                    <xsd:documentation>Document/message by which the sender declares goods for Customs transit according to Annex E.1 (concerning Customs transit) to the Kyoto convention (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TifForm">
                <xsd:annotation>
                    <xsd:documentation>International Customs transit document by which the sender declares goods for carriage by rail in accordance with the provisions of the 1952 International Convention to facilitate the crossing of frontiers for goods carried by rail (TIF Convention of UIC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TirCarnet">
                <xsd:annotation>
                    <xsd:documentation>International Customs document (International Transit by Road), issued by a guaranteeing association approved by the Customs authorities, under the cover of which goods are carried, in most cases under Customs seal, in road vehicles and/or containers in compliance with the requirements of the Customs TIR Convention of the International Transport of Goods under cover of TIR Carnets (UN/ECE).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EcCarnet">
                <xsd:annotation>
                    <xsd:documentation>EC customs transit document issued by EC customs authorities for transit and/or temporary user of goods within the EC.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Eur1CertificateOfOrigin">
                <xsd:annotation>
                    <xsd:documentation>Customs certificate used in preferential goods interchanges between EC countries and EC external countries.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AtaCarnet">
                <xsd:annotation>
                    <xsd:documentation>International Customs document (Admission Temporaire / Temporary Admission) which, issued under the terms of the ATA Convention (1961), incorporates an internationally valid guarantee and may be used, in lieu of national Customs documents and as security for import duties and taxes, to cover the temporary admission of goods and, where appropriate, the transit of goods. If accepted for controlling the temporary export and reimport of goods, international guarantee does not apply (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SingleAdministrativeDocument">
                <xsd:annotation>
                    <xsd:documentation>A set of documents, replacing the various (national) forms for Customs declaration within the EC, implemented on 01-01-1988.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralResponseCustoms">
                <xsd:annotation>
                    <xsd:documentation>General response message to permit the transfer of data from Customs to the transmitter of the previous message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentResponseCustoms">
                <xsd:annotation>
                    <xsd:documentation>Document response message to permit the transfer of data from Customs to the transmitter of the previous message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ErrorResponseCustoms">
                <xsd:annotation>
                    <xsd:documentation>Error response message to permit the transfer of data from Customs to the transmitter of the previous message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackageResponseCustoms">
                <xsd:annotation>
                    <xsd:documentation>Package response message to permit the transfer of data from Customs to the transmitter of the previous message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxCalculationConfirmationResponseCustoms">
                <xsd:annotation>
                    <xsd:documentation>Tax calculation/confirmation response message to permit the transfer of data from Customs to the transmitter of the previous message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuotaPriorAllocationCertificate">
                <xsd:annotation>
                    <xsd:documentation>Document/message issued by the competent body for prior allocation of a quota.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndUseAuthorization">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GovernmentContract">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatisticalDocumentImport">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationForDocumentaryCredit">
                <xsd:annotation>
                    <xsd:documentation>Message with application for opening of a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousCustomsDocumentMessage">
                <xsd:annotation>
                    <xsd:documentation>Indication of the previous Customs document/message concerning the same transaction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
