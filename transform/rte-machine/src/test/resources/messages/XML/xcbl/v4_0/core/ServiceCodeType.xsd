<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="ServiceCodeType">
        <xsd:annotation>
            <xsd:documentation>This code indicates the special service required. This code list is derived from X12 1300 (Service, promotion, allowance or charge code)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="About">
                <xsd:annotation>
                    <xsd:documentation>To be construed as allowing a difference not exceeding 10 % more or 10 % less than the amount which it refers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AbsoluteMinimumCharge">
                <xsd:annotation>
                    <xsd:documentation>Absolute minimum charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccessCharge-Federal">
                <xsd:annotation>
                    <xsd:documentation>Access charge - federal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccessCharges">
                <xsd:annotation>
                    <xsd:documentation>Access charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccessCharge-State">
                <xsd:annotation>
                    <xsd:documentation>Access charge - state</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountNumberCorrectionCharge">
                <xsd:annotation>
                    <xsd:documentation>Account number correction charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcidBattery">
                <xsd:annotation>
                    <xsd:documentation>Acid (battery)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcknowledgmentofDeliveryFeeAOD">
                <xsd:annotation>
                    <xsd:documentation>Acknowledgment of Delivery Fee (AOD)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActivationOfCarnet">
                <xsd:annotation>
                    <xsd:documentation>Activation of carnet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalCommercialInvoices">
                <xsd:annotation>
                    <xsd:documentation>Additional commercial invoices</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalCopiesOfFreightBill">
                <xsd:annotation>
                    <xsd:documentation>Additional copies of freight bill</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalMaterial">
                <xsd:annotation>
                    <xsd:documentation>Additional material</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalTariffClassifications">
                <xsd:annotation>
                    <xsd:documentation>Additional tariff classifications</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AddOn-Destination">
                <xsd:annotation>
                    <xsd:documentation>Add on - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AddOn-Origin">
                <xsd:annotation>
                    <xsd:documentation>Add on - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AddressCorrection">
                <xsd:annotation>
                    <xsd:documentation>Address correction</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AddToMakeMarketValue">
                <xsd:annotation>
                    <xsd:documentation>Add to make market value</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Adjustment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentForMaximumChargesBilling">
                <xsd:annotation>
                    <xsd:documentation>Adjustment for maximum charges billing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentForMinimumAverageTimeRequirementBilling">
                <xsd:annotation>
                    <xsd:documentation>Adjustment for minimum average time requirement billing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Adjustments">
                <xsd:annotation>
                    <xsd:documentation>Adjustments</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Administrative">
                <xsd:annotation>
                    <xsd:documentation>Administrative</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdValorem">
                <xsd:annotation>
                    <xsd:documentation>Ad valorem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceChargesHandling">
                <xsd:annotation>
                    <xsd:documentation>Advance charges handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceDestinationAmount">
                <xsd:annotation>
                    <xsd:documentation>Advance destination amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceDestinationFee">
                <xsd:annotation>
                    <xsd:documentation>Advance destination fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceFee">
                <xsd:annotation>
                    <xsd:documentation>Advance fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceLadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Advance lading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceOriginAmount">
                <xsd:annotation>
                    <xsd:documentation>Advance origin amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvanceOriginFee">
                <xsd:annotation>
                    <xsd:documentation>Advance origin fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Advances">
                <xsd:annotation>
                    <xsd:documentation>Advances</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvertisingAllowance">
                <xsd:annotation>
                    <xsd:documentation>Advertising allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Affidavit">
                <xsd:annotation>
                    <xsd:documentation>Affidavit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgentDisbursement-Destination">
                <xsd:annotation>
                    <xsd:documentation>Agent disbursement - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgentDisbursement-Origin">
                <xsd:annotation>
                    <xsd:documentation>Agent disbursement - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AircraftOnGround">
                <xsd:annotation>
                    <xsd:documentation>Aircraft on ground (AOG)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirExportCertificate">
                <xsd:annotation>
                    <xsd:documentation>Air export certificate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirExpressCharge">
                <xsd:annotation>
                    <xsd:documentation>Air express charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirlineOpeningFee">
                <xsd:annotation>
                    <xsd:documentation>Airline opening fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirportTerminalHandlingCharge">
                <xsd:annotation>
                    <xsd:documentation>Airport terminal handling charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirTransportationCharge">
                <xsd:annotation>
                    <xsd:documentation>Air transportation charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholicBeverageReportCharge">
                <xsd:annotation>
                    <xsd:documentation>Alcoholic beverage report charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlleghenyCountyPADeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>Allegheny county, PA delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Allowance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowanceLineItems">
                <xsd:annotation>
                    <xsd:documentation>Allowance is related to all line items in a message as a default allowance. It may be overridden per line item.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowanceMessage">
                <xsd:annotation>
                    <xsd:documentation>Allowance is related to the entire message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowancePerCallOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowanceAdvance">
                <xsd:annotation>
                    <xsd:documentation>Allowance advance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowanceForConsignmentMerchandise">
                <xsd:annotation>
                    <xsd:documentation>Allowance for consignment merchandise</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowanceNon-Performance">
                <xsd:annotation>
                    <xsd:documentation>Allowance Non-performance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowanceRequest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Alterations">
                <xsd:annotation>
                    <xsd:documentation>Alterations</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AmendingExportDocumentation">
                <xsd:annotation>
                    <xsd:documentation>Amending export documentation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnealHeatSteelorGlassTreatment">
                <xsd:annotation>
                    <xsd:documentation>Anneal/Heat (steel or glass treatment)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnodizingCharge">
                <xsd:annotation>
                    <xsd:documentation>Anodizing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Anti-dumpingDuty">
                <xsd:annotation>
                    <xsd:documentation>Anti-dumping duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppointmentNotification">
                <xsd:annotation>
                    <xsd:documentation>Appointment (notification)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArbitraryInAdditionToThroughRatesandCharges">
                <xsd:annotation>
                    <xsd:documentation>Arbitrary (In addition to through rates and charges)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArtWork">
                <xsd:annotation>
                    <xsd:documentation>Art work</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Assembly">
                <xsd:annotation>
                    <xsd:documentation>Assembly</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssistAmount">
                <xsd:annotation>
                    <xsd:documentation>Assist amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttachmentsToBillOfLadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Attachments to Bill of Lading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BadDebt">
                <xsd:annotation>
                    <xsd:documentation>Bad debt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankingDrafts">
                <xsd:annotation>
                    <xsd:documentation>Banking drafts</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BargeFreightAllKindsService">
                <xsd:annotation>
                    <xsd:documentation>Barge freight all kinds service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BaseCharge">
                <xsd:annotation>
                    <xsd:documentation>Base charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BasicReorderAllowance">
                <xsd:annotation>
                    <xsd:documentation>Basic reorder allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeamingCharge">
                <xsd:annotation>
                    <xsd:documentation>Beaming charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeddingFeedingDisinfecting">
                <xsd:annotation>
                    <xsd:documentation>Bedding/feeding/disinfecting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeefFee">
                <xsd:annotation>
                    <xsd:documentation>Beef fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeyondCharge">
                <xsd:annotation>
                    <xsd:documentation>Beyond charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeyondFreightCharges">
                <xsd:annotation>
                    <xsd:documentation>Beyond freight charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillAndHold">
                <xsd:annotation>
                    <xsd:documentation>Bill and hold</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BilledDemand">
                <xsd:annotation>
                    <xsd:documentation>Billed demand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLadingAttendancy">
                <xsd:annotation>
                    <xsd:documentation>Bill of Lading attendancy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Bill of Lading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BlackLungTax">
                <xsd:annotation>
                    <xsd:documentation>Black lung tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BlockingAndBracingCharge">
                <xsd:annotation>
                    <xsd:documentation>Blocking and bracing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BlowerCharge">
                <xsd:annotation>
                    <xsd:documentation>Blower charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BobtailCharges">
                <xsd:annotation>
                    <xsd:documentation>Bobtail charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BondAmount">
                <xsd:annotation>
                    <xsd:documentation>Bond amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BondCharge">
                <xsd:annotation>
                    <xsd:documentation>Bond charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BopSheet">
                <xsd:annotation>
                    <xsd:documentation>Bop sheet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BordeauxArbitraries">
                <xsd:annotation>
                    <xsd:documentation>Bordeaux arbitraries</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Both-Flat">
                <xsd:annotation>
                    <xsd:documentation>Both-flat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BreakbulkServices">
                <xsd:annotation>
                    <xsd:documentation>Breakbulk services</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BreakBulkSurfaceCharge">
                <xsd:annotation>
                    <xsd:documentation>Break bulk surface charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BridgeToll">
                <xsd:annotation>
                    <xsd:documentation>Bridge toll</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokenLot">
                <xsd:annotation>
                    <xsd:documentation>Broken lot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokenPackageCharge">
                <xsd:annotation>
                    <xsd:documentation>Broken package charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Brokerage">
                <xsd:annotation>
                    <xsd:documentation>Brokerage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokerageOrDuty">
                <xsd:annotation>
                    <xsd:documentation>Brokerage or duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BunkerAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Bunker adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BunkerAdjustment-20FootContainer">
                <xsd:annotation>
                    <xsd:documentation>Bunker adjustment - 20 foot container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BunkerAdjustment-40FootContainer">
                <xsd:annotation>
                    <xsd:documentation>Bunker adjustment - 40 foot container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BunkerSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Bunker surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BurdenOverheadOrAllowanceForIndirectCosts">
                <xsd:annotation>
                    <xsd:documentation>Burden, overhead, or allowance for indirect costs</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Burning">
                <xsd:annotation>
                    <xsd:documentation>Burning</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyerHandCarry">
                <xsd:annotation>
                    <xsd:documentation>Buyer hand carry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersCarAllowance">
                <xsd:annotation>
                    <xsd:documentation>Buyers car allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CablePressurization">
                <xsd:annotation>
                    <xsd:documentation>Cable pressurization</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CablesSendingOf">
                <xsd:annotation>
                    <xsd:documentation>Cables (sending of)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CallTag">
                <xsd:annotation>
                    <xsd:documentation>Call tag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CampArbitrary">
                <xsd:annotation>
                    <xsd:documentation>Camp arbitrary</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadaGreatLakesAdditionals">
                <xsd:annotation>
                    <xsd:documentation>Canada great lakes additionals</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadianCQCustomsClearance">
                <xsd:annotation>
                    <xsd:documentation>Canadian C.Q.customs clearance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadianCurrencyExchange">
                <xsd:annotation>
                    <xsd:documentation>Canadian currency exchange</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadianImportTerminationFee">
                <xsd:annotation>
                    <xsd:documentation>Canadian import termination fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadianReconsignmentFee">
                <xsd:annotation>
                    <xsd:documentation>Canadian reconsignment fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadianRemanifestFee">
                <xsd:annotation>
                    <xsd:documentation>Canadian remanifest fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancellationCharge">
                <xsd:annotation>
                    <xsd:documentation>Cancellation charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelledOrderHeavyDutyFlatcar">
                <xsd:annotation>
                    <xsd:documentation>Cancelled order, heavy duty flatcar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Capping">
                <xsd:annotation>
                    <xsd:documentation>Capping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CaptureAdditionalData">
                <xsd:annotation>
                    <xsd:documentation>Capture additional data</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoTaxes">
                <xsd:annotation>
                    <xsd:documentation>Cargo taxes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarLoading">
                <xsd:annotation>
                    <xsd:documentation>Car loading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarRental">
                <xsd:annotation>
                    <xsd:documentation>Car rental</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Carrier">
                <xsd:annotation>
                    <xsd:documentation>Carrier</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarrierCreditAllowance">
                <xsd:annotation>
                    <xsd:documentation>Carrier credit allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarrierDebitAllowance">
                <xsd:annotation>
                    <xsd:documentation>Carrier debit allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarrierNotificationCharge">
                <xsd:annotation>
                    <xsd:documentation>Carrier notification charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Cartage">
                <xsd:annotation>
                    <xsd:documentation>Cartage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CartageCharge">
                <xsd:annotation>
                    <xsd:documentation>Cartage charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CatalogingServices">
                <xsd:annotation>
                    <xsd:documentation>Cataloging services</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CentralBuy">
                <xsd:annotation>
                    <xsd:documentation>Central buy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CentsOff">
                <xsd:annotation>
                    <xsd:documentation>Cents off</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfConformance">
                <xsd:annotation>
                    <xsd:documentation>Certificate of conformance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfOrigin">
                <xsd:annotation>
                    <xsd:documentation>Certificate of origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateOfRegistration">
                <xsd:annotation>
                    <xsd:documentation>Certificate of registration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Certification">
                <xsd:annotation>
                    <xsd:documentation>Certification</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChainAndBinders">
                <xsd:annotation>
                    <xsd:documentation>Chain and binders</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChamberOfCommerceServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Chamber of commerce service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChangeOfAirbill-ServiceFee">
                <xsd:annotation>
                    <xsd:documentation>Change of airbill - service fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Charge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargeLineItems">
                <xsd:annotation>
                    <xsd:documentation>Charge is related to all line items in a message as a default charge. It may be overridden per line item.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargeMessage">
                <xsd:annotation>
                    <xsd:documentation>Charge is related to the entire message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargePerCallOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargesForwardAdvanceCharge">
                <xsd:annotation>
                    <xsd:documentation>Charges forward/advance charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CharterServices">
                <xsd:annotation>
                    <xsd:documentation>Charter services</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChassisTransfer">
                <xsd:annotation>
                    <xsd:documentation>Chassis transfer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChemicalMillingCharge">
                <xsd:annotation>
                    <xsd:documentation>Chemical milling charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChicagoLoopCharge">
                <xsd:annotation>
                    <xsd:documentation>Chicago loop charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CigaretteStamping">
                <xsd:annotation>
                    <xsd:documentation>Cigarette stamping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CityDelivery">
                <xsd:annotation>
                    <xsd:documentation>City delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CityMaintenanceFee">
                <xsd:annotation>
                    <xsd:documentation>City maintenance fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CityPick-up">
                <xsd:annotation>
                    <xsd:documentation>City pick-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CityTerminalCharge">
                <xsd:annotation>
                    <xsd:documentation>City terminal charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CleaningCharge">
                <xsd:annotation>
                    <xsd:documentation>Cleaning charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosingSealing">
                <xsd:annotation>
                    <xsd:documentation>Closing and sealing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoatingDipRustproofEDP">
                <xsd:annotation>
                    <xsd:documentation>Coating (dip, rustproof, EDP)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CODAmount">
                <xsd:annotation>
                    <xsd:documentation>Cod amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CODCharges">
                <xsd:annotation>
                    <xsd:documentation>Cod charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectOnDeliveryAlterationCharge">
                <xsd:annotation>
                    <xsd:documentation>Collect on delivery alteration charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectOnDeliveryDeletionCharge">
                <xsd:annotation>
                    <xsd:documentation>Collect on delivery deletion charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Collect surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-manufacturingDiscount">
                <xsd:annotation>
                    <xsd:documentation>Co-manufacturing discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Combination">
                <xsd:annotation>
                    <xsd:documentation>Combination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CombinationPerformanceAndNon-performance">
                <xsd:annotation>
                    <xsd:documentation>Combination performance and non-performance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CombineAllSameDayShipment">
                <xsd:annotation>
                    <xsd:documentation>Combine all same day shipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommissionAmount">
                <xsd:annotation>
                    <xsd:documentation>Commission amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommunicationsCharges">
                <xsd:annotation>
                    <xsd:documentation>Communications charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompetitiveAllowance">
                <xsd:annotation>
                    <xsd:documentation>Competitive allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompetitiveCarAllowance">
                <xsd:annotation>
                    <xsd:documentation>Competitive car allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompetitivePrice">
                <xsd:annotation>
                    <xsd:documentation>Competitive price</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompressorCharge">
                <xsd:annotation>
                    <xsd:documentation>Compressor charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConcessionCredit">
                <xsd:annotation>
                    <xsd:documentation>Concession credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConcessionMoney">
                <xsd:annotation>
                    <xsd:documentation>Concession money</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CongestionSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Congestion surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConnectCharge">
                <xsd:annotation>
                    <xsd:documentation>Connect charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConservationResearchFee">
                <xsd:annotation>
                    <xsd:documentation>Conservation research fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsigneeUnload">
                <xsd:annotation>
                    <xsd:documentation>Consignee unload</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consolidation">
                <xsd:annotation>
                    <xsd:documentation>Consolidation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstantSurveillanceService">
                <xsd:annotation>
                    <xsd:documentation>Constant surveillance service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstantSurveillanceService-Armed">
                <xsd:annotation>
                    <xsd:documentation>Constant surveillance service - armed</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsularizationFee">
                <xsd:annotation>
                    <xsd:documentation>Consularization fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsularLegalizationService">
                <xsd:annotation>
                    <xsd:documentation>Consular legalization service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsultingService">
                <xsd:annotation>
                    <xsd:documentation>Consulting service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumerServiceProviderCharge">
                <xsd:annotation>
                    <xsd:documentation>Consumer service provider charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerAllowance">
                <xsd:annotation>
                    <xsd:documentation>Container allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerDeposits">
                <xsd:annotation>
                    <xsd:documentation>Container deposits</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerDestuffing">
                <xsd:annotation>
                    <xsd:documentation>Container destuffing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerDiscount">
                <xsd:annotation>
                    <xsd:documentation>Container discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerDiversion">
                <xsd:annotation>
                    <xsd:documentation>Container diversion</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerLease">
                <xsd:annotation>
                    <xsd:documentation>Container lease</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerLeasing">
                <xsd:annotation>
                    <xsd:documentation>Container leasing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerLossDamage">
                <xsd:annotation>
                    <xsd:documentation>Container loss/damage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerServiceChargeUKEUR">
                <xsd:annotation>
                    <xsd:documentation>Container service charge UK/EUR</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerServiceChargeUSACanada">
                <xsd:annotation>
                    <xsd:documentation>Container service charge USA/Canada</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerStuffing">
                <xsd:annotation>
                    <xsd:documentation>Container stuffing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerTrailerAllowance">
                <xsd:annotation>
                    <xsd:documentation>Container/trailer allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContinuousMileage">
                <xsd:annotation>
                    <xsd:documentation>Continuous mileage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractAllowance">
                <xsd:annotation>
                    <xsd:documentation>Contract allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractEscalation">
                <xsd:annotation>
                    <xsd:documentation>Contract escalation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Contract service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlledAtmosphere">
                <xsd:annotation>
                    <xsd:documentation>Controlled atmosphere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Converting">
                <xsd:annotation>
                    <xsd:documentation>Converting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-opCredit">
                <xsd:annotation>
                    <xsd:documentation>Co-op credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CooperativeAdvertisingMerchandisingAllowancePerformance">
                <xsd:annotation>
                    <xsd:documentation>Cooperative advertising/merchandising allowance (performance)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Copying">
                <xsd:annotation>
                    <xsd:documentation>Copying</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CopyOfBillOfLadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Copy of Bill of Lading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CopyOfDeliveryReceiptCharge">
                <xsd:annotation>
                    <xsd:documentation>Copy of delivery receipt charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoreCharge">
                <xsd:annotation>
                    <xsd:documentation>Core charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostRecoveryAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Cost recovery/adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CostRecoveryFactor">
                <xsd:annotation>
                    <xsd:documentation>Cost recovery factor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CottonFee">
                <xsd:annotation>
                    <xsd:documentation>Cotton fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CountAndRecount">
                <xsd:annotation>
                    <xsd:documentation>Count and recount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CountervailingDuty">
                <xsd:annotation>
                    <xsd:documentation>Countervailing duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CouponReimbursement">
                <xsd:annotation>
                    <xsd:documentation>Coupon reimbursement</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Crafting">
                <xsd:annotation>
                    <xsd:documentation>Crafting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Crating">
                <xsd:annotation>
                    <xsd:documentation>Crating</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Credit">
                <xsd:annotation>
                    <xsd:documentation>Credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrencyAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Currency adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrencyAdjustment-20FootContainer">
                <xsd:annotation>
                    <xsd:documentation>Currency adjustment - 20 foot container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrencyAdjustment-40FootContainer">
                <xsd:annotation>
                    <xsd:documentation>Currency adjustment - 40 foot container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrencyAdjustment-BreakBulk">
                <xsd:annotation>
                    <xsd:documentation>Currency adjustment - break bulk</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrencyAdjustmentFactor">
                <xsd:annotation>
                    <xsd:documentation>Currency adjustment factor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrencyDiscount">
                <xsd:annotation>
                    <xsd:documentation>Currency discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerAccountIdentification">
                <xsd:annotation>
                    <xsd:documentation>Customer account identification</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerEquipmentAllowance">
                <xsd:annotation>
                    <xsd:documentation>Customer equipment allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsBrokerFee">
                <xsd:annotation>
                    <xsd:documentation>Customs broker fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsCharge">
                <xsd:annotation>
                    <xsd:documentation>Customs charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsDuty">
                <xsd:annotation>
                    <xsd:documentation>Customs duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsEntry">
                <xsd:annotation>
                    <xsd:documentation>Customs entry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsExam">
                <xsd:annotation>
                    <xsd:documentation>Customs exam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsFees-ContainerLevel">
                <xsd:annotation>
                    <xsd:documentation>Customs fees - container level</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsFees-LiftLevel">
                <xsd:annotation>
                    <xsd:documentation>Customs fees - lift level</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsFormalities">
                <xsd:annotation>
                    <xsd:documentation>Customs formalities</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsInvoice">
                <xsd:annotation>
                    <xsd:documentation>Customs invoice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsInvoice-AdditionalPage">
                <xsd:annotation>
                    <xsd:documentation>Customs invoice - additional page</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsPenalty">
                <xsd:annotation>
                    <xsd:documentation>Customs penalty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Cut">
                <xsd:annotation>
                    <xsd:documentation>Cut</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CutAndParallel">
                <xsd:annotation>
                    <xsd:documentation>Cut and parallel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CuttingCharge">
                <xsd:annotation>
                    <xsd:documentation>Cutting charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamagedMerchandise">
                <xsd:annotation>
                    <xsd:documentation>Damaged merchandise</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamageToCarrierEquipment">
                <xsd:annotation>
                    <xsd:documentation>Damage to carrier equipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamageToCarrierVessel">
                <xsd:annotation>
                    <xsd:documentation>Damage to carrier vessel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DamageToGovernmentEquipment">
                <xsd:annotation>
                    <xsd:documentation>Damage to government equipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DataDrawingCharge">
                <xsd:annotation>
                    <xsd:documentation>Data/drawing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeadheadMileageCharge">
                <xsd:annotation>
                    <xsd:documentation>Deadhead mileage charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeafAndDisabledSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Deaf and disabled surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclaredValueForCarriage">
                <xsd:annotation>
                    <xsd:documentation>Declared value for carriage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclaredValueForCustoms">
                <xsd:annotation>
                    <xsd:documentation>Declared value for customs</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclaredValueForInsurance">
                <xsd:annotation>
                    <xsd:documentation>Declared value for insurance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeductToMakeMarketValue">
                <xsd:annotation>
                    <xsd:documentation>Deduct to make market value</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DefectiveAllowance">
                <xsd:annotation>
                    <xsd:documentation>Defective allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeficitFreight">
                <xsd:annotation>
                    <xsd:documentation>Deficit freight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="De-Installation">
                <xsd:annotation>
                    <xsd:documentation>De-installation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DelayFurnishingDestinationWeights">
                <xsd:annotation>
                    <xsd:documentation>Delay furnishing destination weights</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Delivery">
                <xsd:annotation>
                    <xsd:documentation>Delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliverySurcharge">
                <xsd:annotation>
                    <xsd:documentation>Delivery surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DemandCharge">
                <xsd:annotation>
                    <xsd:documentation>Demand charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Demurrage">
                <xsd:annotation>
                    <xsd:documentation>Demurrage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Demurrage-AverageAgreement">
                <xsd:annotation>
                    <xsd:documentation>Demurrage - average agreement</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Demurrage-Special">
                <xsd:annotation>
                    <xsd:documentation>Demurrage - special</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Deposit">
                <xsd:annotation>
                    <xsd:documentation>Deposit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepositCharges">
                <xsd:annotation>
                    <xsd:documentation>Deposit charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepositInLieuOfOrder">
                <xsd:annotation>
                    <xsd:documentation>Deposit in lieu of order</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Deramping">
                <xsd:annotation>
                    <xsd:documentation>Deramping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DerrickCharge">
                <xsd:annotation>
                    <xsd:documentation>Derrick charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DesignatedSupplierInspection">
                <xsd:annotation>
                    <xsd:documentation>Designated supplier inspection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DestinationCharge">
                <xsd:annotation>
                    <xsd:documentation>Destination charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DetentionLoading">
                <xsd:annotation>
                    <xsd:documentation>Detention loading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DetentionOfPowerUnits">
                <xsd:annotation>
                    <xsd:documentation>Detention of power units</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DetentionOfTrailers">
                <xsd:annotation>
                    <xsd:documentation>Detention of trailers</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Detention-SpecialTypeFlatCar">
                <xsd:annotation>
                    <xsd:documentation>Detention - special type flat car</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DetentionUnloading">
                <xsd:annotation>
                    <xsd:documentation>Detention unloading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeterminedFreight">
                <xsd:annotation>
                    <xsd:documentation>Determined freight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DevelopmentCharge">
                <xsd:annotation>
                    <xsd:documentation>Development charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DieServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Die service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DirectProductHandling">
                <xsd:annotation>
                    <xsd:documentation>Direct product handling (DPC)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Disbursement">
                <xsd:annotation>
                    <xsd:documentation>Disbursement</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisconnectCharge">
                <xsd:annotation>
                    <xsd:documentation>Disconnect charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount">
                <xsd:annotation>
                    <xsd:documentation>Discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount-DropBoxConvenienceCtr">
                <xsd:annotation>
                    <xsd:documentation>Discount - drop box/convenience ctr.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount-Incentive">
                <xsd:annotation>
                    <xsd:documentation>Discount - incentive</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount-MultipleShipment">
                <xsd:annotation>
                    <xsd:documentation>Discount - multiple shipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount-ServiceOptionDelivery">
                <xsd:annotation>
                    <xsd:documentation>Discount - service option (delivery)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount-ServiceOptionPickup">
                <xsd:annotation>
                    <xsd:documentation>Discount - service option (pickup)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discount-Special">
                <xsd:annotation>
                    <xsd:documentation>Discount - special</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisplayAllowance">
                <xsd:annotation>
                    <xsd:documentation>Display allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributionFee">
                <xsd:annotation>
                    <xsd:documentation>Distribution fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributionService">
                <xsd:annotation>
                    <xsd:documentation>Distribution service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributorDiscountAllowance">
                <xsd:annotation>
                    <xsd:documentation>Distributor discount/allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DiversionAndReconsignment">
                <xsd:annotation>
                    <xsd:documentation>Diversion and reconsignment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DiversionCharge">
                <xsd:annotation>
                    <xsd:documentation>Diversion charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DiversionToAirCharge">
                <xsd:annotation>
                    <xsd:documentation>Diversion to air charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dockage-BoatDetention">
                <xsd:annotation>
                    <xsd:documentation>Dockage - boat detention</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentationCharge">
                <xsd:annotation>
                    <xsd:documentation>Documentation charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentHandling">
                <xsd:annotation>
                    <xsd:documentation>Document handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Door-To-Door">
                <xsd:annotation>
                    <xsd:documentation>Door-to-Door</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DowelPinCharge">
                <xsd:annotation>
                    <xsd:documentation>Dowel pin charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrayagAtPortOfDebarkation">
                <xsd:annotation>
                    <xsd:documentation>Drayage at port of debarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Drayage">
                <xsd:annotation>
                    <xsd:documentation>Drayage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrayageatPortOfDebarkationRateZone">
                <xsd:annotation>
                    <xsd:documentation>Drayage at port of debarkation (rate zone)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrayageAtPortOfEmbarkation">
                <xsd:annotation>
                    <xsd:documentation>Drayage at port of embarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrayageatPortOfEmbarkationRateZone">
                <xsd:annotation>
                    <xsd:documentation>Drayage at port of embarkation (rate zone)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrayageLineHaul">
                <xsd:annotation>
                    <xsd:documentation>Drayage/line haul</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DriverAssistedUnloading">
                <xsd:annotation>
                    <xsd:documentation>Driver assisted unloading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DriversWages">
                <xsd:annotation>
                    <xsd:documentation>Driver's wages</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DropDock">
                <xsd:annotation>
                    <xsd:documentation>Drop dock</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DropYard">
                <xsd:annotation>
                    <xsd:documentation>Drop yard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrumCost">
                <xsd:annotation>
                    <xsd:documentation>Drum cost</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrumDeposit">
                <xsd:annotation>
                    <xsd:documentation>Drum deposit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrumUpCharge">
                <xsd:annotation>
                    <xsd:documentation>Drum up charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DryerCharge">
                <xsd:annotation>
                    <xsd:documentation>Dryer charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DryIce">
                <xsd:annotation>
                    <xsd:documentation>Dry ice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dunnage">
                <xsd:annotation>
                    <xsd:documentation>Dunnage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DutyCharge">
                <xsd:annotation>
                    <xsd:documentation>Duty charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DutyDrawback">
                <xsd:annotation>
                    <xsd:documentation>Duty drawback</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarlyBuyAllowance">
                <xsd:annotation>
                    <xsd:documentation>Early buy allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarlyPaymentAllowance">
                <xsd:annotation>
                    <xsd:documentation>Early payment allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarlyShipAllowance">
                <xsd:annotation>
                    <xsd:documentation>Early ship allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyPortCharge">
                <xsd:annotation>
                    <xsd:documentation>Emergency port charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyService">
                <xsd:annotation>
                    <xsd:documentation>Emergency service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencySurcharge">
                <xsd:annotation>
                    <xsd:documentation>Emergency surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyWeighingCharge">
                <xsd:annotation>
                    <xsd:documentation>Empty weighing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Enclosure">
                <xsd:annotation>
                    <xsd:documentation>Enclosure</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndorsementFee">
                <xsd:annotation>
                    <xsd:documentation>Endorsement fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Energycharge">
                <xsd:annotation>
                    <xsd:documentation>Energy charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EnergySurchargeFuelAdjustmentFactor">
                <xsd:annotation>
                    <xsd:documentation>Energy surcharge (fuel adjustment factor)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EngineeringCharge">
                <xsd:annotation>
                    <xsd:documentation>Engineering charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EngineeringSupplies">
                <xsd:annotation>
                    <xsd:documentation>Engineering supplies</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engraving">
                <xsd:annotation>
                    <xsd:documentation>Engraving</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EnteredValue">
                <xsd:annotation>
                    <xsd:documentation>Entered value</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EnvironmentalProtectionService">
                <xsd:annotation>
                    <xsd:documentation>Environmental protection service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentManufacturerRestorationAudit">
                <xsd:annotation>
                    <xsd:documentation>Equipment manufacturer restoration audit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Escalation">
                <xsd:annotation>
                    <xsd:documentation>Escalation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EscortService">
                <xsd:annotation>
                    <xsd:documentation>Escort service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Escrow">
                <xsd:annotation>
                    <xsd:documentation>Escrow</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Eur1PresentationFee">
                <xsd:annotation>
                    <xsd:documentation>Eur1 presentation fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EuropeanPortCharges">
                <xsd:annotation>
                    <xsd:documentation>European port charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Exact">
                <xsd:annotation>
                    <xsd:documentation>Indicates that this is the exact amount.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExcessiveValueCharge">
                <xsd:annotation>
                    <xsd:documentation>Excessive value charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExcessMileageCharge">
                <xsd:annotation>
                    <xsd:documentation>Excess mileage charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExcessPeriods">
                <xsd:annotation>
                    <xsd:documentation>Excess periods</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExcessValueFee">
                <xsd:annotation>
                    <xsd:documentation>Excess value fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExcessWeight">
                <xsd:annotation>
                    <xsd:documentation>Excess weight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExchangeAccessCredit">
                <xsd:annotation>
                    <xsd:documentation>Exchange access credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExclusiveUse">
                <xsd:annotation>
                    <xsd:documentation>Exclusive use</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExclusiveUseOfEquipment">
                <xsd:annotation>
                    <xsd:documentation>Exclusive use of equipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExhibitionDeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>Exhibition delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExhibitionPickupCharge">
                <xsd:annotation>
                    <xsd:documentation>Exhibition pickup charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpandedService">
                <xsd:annotation>
                    <xsd:documentation>Expanded service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditedOneDayConsularService">
                <xsd:annotation>
                    <xsd:documentation>Expedited one day consular service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditedServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Expedited service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditedShipments">
                <xsd:annotation>
                    <xsd:documentation>Expedited shipments</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditingFee">
                <xsd:annotation>
                    <xsd:documentation>Expediting fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditingPremium">
                <xsd:annotation>
                    <xsd:documentation>Expediting premium</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportCustomsClearance">
                <xsd:annotation>
                    <xsd:documentation>Export customs clearance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportDeclarations-Automated">
                <xsd:annotation>
                    <xsd:documentation>Export declarations - automated</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportDeclarations-USShippers">
                <xsd:annotation>
                    <xsd:documentation>Export declarations - U.S. shippers</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportImportCharge">
                <xsd:annotation>
                    <xsd:documentation>Export/import charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportLicenseApplication">
                <xsd:annotation>
                    <xsd:documentation>Export license application</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportShippingCharge">
                <xsd:annotation>
                    <xsd:documentation>Export shipping charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtraCopiesandMailings">
                <xsd:annotation>
                    <xsd:documentation>Extra copies and mailings</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtraLaborHelperService">
                <xsd:annotation>
                    <xsd:documentation>Extra labor (helper service)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtraLength">
                <xsd:annotation>
                    <xsd:documentation>Extra length</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtraLengthSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Extra length surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtraService-Counter-To-Counter">
                <xsd:annotation>
                    <xsd:documentation>Extra Service - counter-to-counter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FabricationCharge">
                <xsd:annotation>
                    <xsd:documentation>Fabrication charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FacsimileCharges">
                <xsd:annotation>
                    <xsd:documentation>Facsimile charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FacsimileCharges-AdditionalPages">
                <xsd:annotation>
                    <xsd:documentation>Facsimile charges - additional pages</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FailedLampPanelCharge">
                <xsd:annotation>
                    <xsd:documentation>Failed lamp panel charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FaxPre-alert">
                <xsd:annotation>
                    <xsd:documentation>Fax pre-alert</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederalTransferSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Federal transfer surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fee">
                <xsd:annotation>
                    <xsd:documentation>Fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinanceCharge">
                <xsd:annotation>
                    <xsd:documentation>Finance charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstArticleCharge">
                <xsd:annotation>
                    <xsd:documentation>First article charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstFlightOut">
                <xsd:annotation>
                    <xsd:documentation>First flight out</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FlatrackSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Flatrack surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FlatRate">
                <xsd:annotation>
                    <xsd:documentation>Flat rate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FloorStockProtection">
                <xsd:annotation>
                    <xsd:documentation>Floor stock protection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FoodAndLodging">
                <xsd:annotation>
                    <xsd:documentation>Food and Lodging</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignCustomsDuty">
                <xsd:annotation>
                    <xsd:documentation>Foreign customs duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignMilitarySalesRental">
                <xsd:annotation>
                    <xsd:documentation>Foreign military sales (FMS) rental</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignMilitarySalesSpecialCharge">
                <xsd:annotation>
                    <xsd:documentation>Foreign military sales (FMS) special charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignOfficeAdvance">
                <xsd:annotation>
                    <xsd:documentation>Foreign office advance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardCoupons">
                <xsd:annotation>
                    <xsd:documentation>Forward coupons</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardingAgentCommission">
                <xsd:annotation>
                    <xsd:documentation>Forwarding agent commission</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForwardingCharge">
                <xsd:annotation>
                    <xsd:documentation>Forwarding charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FranchiseFee">
                <xsd:annotation>
                    <xsd:documentation>Franchise fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreeDomicileShipmentProcessing">
                <xsd:annotation>
                    <xsd:documentation>Free domicile shipment processing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreeGoods">
                <xsd:annotation>
                    <xsd:documentation>Free goods</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Freight">
                <xsd:annotation>
                    <xsd:documentation>Freight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightBasedOnDollarMinimum">
                <xsd:annotation>
                    <xsd:documentation>Freight based on dollar minimum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightChargesToBorder">
                <xsd:annotation>
                    <xsd:documentation>Freight charges to border</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightChargesToDestination">
                <xsd:annotation>
                    <xsd:documentation>Freight charges to destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightEqualization">
                <xsd:annotation>
                    <xsd:documentation>Freight equalization</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightInternational">
                <xsd:annotation>
                    <xsd:documentation>Freight, international</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightInternationalNon-USDollars">
                <xsd:annotation>
                    <xsd:documentation>Freight, international, non-U.S. dollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightInternationalUSDollars">
                <xsd:annotation>
                    <xsd:documentation>Freight, international, U.S. dollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightPassthrough">
                <xsd:annotation>
                    <xsd:documentation>Freight passthrough</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Freight surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreshnessLeakerAllowance">
                <xsd:annotation>
                    <xsd:documentation>Freshness/leaker allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FuelCharge">
                <xsd:annotation>
                    <xsd:documentation>Fuel charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FuelSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Fuel surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FullService">
                <xsd:annotation>
                    <xsd:documentation>Full service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FullTruckloadAllowance">
                <xsd:annotation>
                    <xsd:documentation>Full truckload allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fumigation">
                <xsd:annotation>
                    <xsd:documentation>Fumigation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GarmentDistrict">
                <xsd:annotation>
                    <xsd:documentation>Garment district</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GarmentSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Garment surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GasPressure">
                <xsd:annotation>
                    <xsd:documentation>Gas pressure</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GatewayFee">
                <xsd:annotation>
                    <xsd:documentation>Gateway fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Glaze">
                <xsd:annotation>
                    <xsd:documentation>Glaze</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoldFactor">
                <xsd:annotation>
                    <xsd:documentation>Gold factor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsAndServicesCharge">
                <xsd:annotation>
                    <xsd:documentation>Goods and services charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsAndServicesCreditAllowance">
                <xsd:annotation>
                    <xsd:documentation>Goods and services credit allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsAndServicesTaxCharge">
                <xsd:annotation>
                    <xsd:documentation>Goods and services tax charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GovernmentInspection">
                <xsd:annotation>
                    <xsd:documentation>Government inspection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GovernmentWarehouseFee-Destination">
                <xsd:annotation>
                    <xsd:documentation>Government warehouse fee - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GovernmentWarehouseFee-Origin">
                <xsd:annotation>
                    <xsd:documentation>Government warehouse fee - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrainDoors">
                <xsd:annotation>
                    <xsd:documentation>Grain doors</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrainFlowCharge">
                <xsd:annotation>
                    <xsd:documentation>Grain flow charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Gratuity">
                <xsd:annotation>
                    <xsd:documentation>Gratuity</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Grinding">
                <xsd:annotation>
                    <xsd:documentation>Grinding</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrossReceiptsSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Gross receipts surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupageDiscount">
                <xsd:annotation>
                    <xsd:documentation>Groupage discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupedItems">
                <xsd:annotation>
                    <xsd:documentation>Grouped items</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GuaranteedInspectionTechnicalService">
                <xsd:annotation>
                    <xsd:documentation>Guaranteed inspection technical service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GulfPortDeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>Gulf port delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Handling">
                <xsd:annotation>
                    <xsd:documentation>Handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HandlingChargesOnDistributionFreightForwardedBeyond">
                <xsd:annotation>
                    <xsd:documentation>Handling charges on distribution freight forwarded beyond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HandlingFreightAtPositionsNotImmediatelyAdjacentToVehicleCharge">
                <xsd:annotation>
                    <xsd:documentation>Handling freight at positions not immediately adjacent to vehicle charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HarborMaintenanceFee">
                <xsd:annotation>
                    <xsd:documentation>Harbor maintenance fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HarborMaintenanceReport">
                <xsd:annotation>
                    <xsd:documentation>Harbor maintenance report</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HaulingAndHoisting">
                <xsd:annotation>
                    <xsd:documentation>Hauling and hoisting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HaulingAndHoistingToBeDirectBilled">
                <xsd:annotation>
                    <xsd:documentation>Hauling and hoisting to be direct billed</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousCargoCharge">
                <xsd:annotation>
                    <xsd:documentation>Hazardous cargo charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousCargoOnDeck">
                <xsd:annotation>
                    <xsd:documentation>Hazardous cargo on deck</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousMaterialsHandlingFee-Domestic">
                <xsd:annotation>
                    <xsd:documentation>Hazardous materials handling fee - domestic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousMaterialsHandlingFee-International">
                <xsd:annotation>
                    <xsd:documentation>Hazardous materials handling fee - international</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousStorage">
                <xsd:annotation>
                    <xsd:documentation>Hazardous storage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HeatInTransitCharges">
                <xsd:annotation>
                    <xsd:documentation>Heat in transit charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HeatTreatCharge">
                <xsd:annotation>
                    <xsd:documentation>Heat treat charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HeavyDutyFlatCarCharge">
                <xsd:annotation>
                    <xsd:documentation>Heavy duty flat car charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HeavyLift">
                <xsd:annotation>
                    <xsd:documentation>Heavy lift</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HighSecurityRedIn-bondSealCharge">
                <xsd:annotation>
                    <xsd:documentation>High security red in-bond seal charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HighwayInterchange">
                <xsd:annotation>
                    <xsd:documentation>Highway interchange</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HointinsAndHauling">
                <xsd:annotation>
                    <xsd:documentation>Hointins and hauling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HoldingCharge">
                <xsd:annotation>
                    <xsd:documentation>Holding charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeLineFreightCharge">
                <xsd:annotation>
                    <xsd:documentation>Home line freight charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HoneyFee">
                <xsd:annotation>
                    <xsd:documentation>Honey fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Hook-upcharge">
                <xsd:annotation>
                    <xsd:documentation>Hook-up charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HoseCharge">
                <xsd:annotation>
                    <xsd:documentation>Hose charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HoseChargeSpecial">
                <xsd:annotation>
                    <xsd:documentation>Hose charge special</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HouseholdGoodsPick-upOrDelivery">
                <xsd:annotation>
                    <xsd:documentation>Household goods pick-up or delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IATAAirbillPreparation">
                <xsd:annotation>
                    <xsd:documentation>IATA airbill preparation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IATAFee">
                <xsd:annotation>
                    <xsd:documentation>IATA fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Identification">
                <xsd:annotation>
                    <xsd:documentation>Identification</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImportServiceFee">
                <xsd:annotation>
                    <xsd:documentation>Import service fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImproperDocumentation">
                <xsd:annotation>
                    <xsd:documentation>Improper documentation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InboundFreightCharges">
                <xsd:annotation>
                    <xsd:documentation>Inbound freight charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncomeFreightManufacturingToShippingPoint">
                <xsd:annotation>
                    <xsd:documentation>Income Freight (manufacturing to shipping point)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorrectBillingAccountCharge">
                <xsd:annotation>
                    <xsd:documentation>Incorrect billing account charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndustryPriceAllowance">
                <xsd:annotation>
                    <xsd:documentation>Industry price allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialLicenseFee">
                <xsd:annotation>
                    <xsd:documentation>Initial license fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InlandTransportation">
                <xsd:annotation>
                    <xsd:documentation>Inland transportation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsideCableConnectors">
                <xsd:annotation>
                    <xsd:documentation>Inside cable connectors</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsideDelivery">
                <xsd:annotation>
                    <xsd:documentation>Inside delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsidePick-up">
                <xsd:annotation>
                    <xsd:documentation>Inside Pick-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectAtDestination">
                <xsd:annotation>
                    <xsd:documentation>Inspect at Destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectAtOrigin">
                <xsd:annotation>
                    <xsd:documentation>Inspect at Origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Inspection">
                <xsd:annotation>
                    <xsd:documentation>Inspection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Installation">
                <xsd:annotation>
                    <xsd:documentation>Installation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstallationAndTraining">
                <xsd:annotation>
                    <xsd:documentation>Installation and Training</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstallationWarranty">
                <xsd:annotation>
                    <xsd:documentation>Installation and Warranty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsulatedTankCharge">
                <xsd:annotation>
                    <xsd:documentation>Insulated tank charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Insurance">
                <xsd:annotation>
                    <xsd:documentation>Insurance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceFee">
                <xsd:annotation>
                    <xsd:documentation>Insurance fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsurancePlacementCostCharge">
                <xsd:annotation>
                    <xsd:documentation>Insurance placement cost charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsurancePremium">
                <xsd:annotation>
                    <xsd:documentation>Insurance premium</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceProvidedByLessee">
                <xsd:annotation>
                    <xsd:documentation>Insurance Provided by Lessee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceProvidedByLessor">
                <xsd:annotation>
                    <xsd:documentation>Insurance Provided by Lessor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Insurance surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterdivisionProfit">
                <xsd:annotation>
                    <xsd:documentation>Interdivision profit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestAmount">
                <xsd:annotation>
                    <xsd:documentation>Interest amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestOnRefund">
                <xsd:annotation>
                    <xsd:documentation>Interest on refund</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestOnSecurityDeposit">
                <xsd:annotation>
                    <xsd:documentation>Interest on security deposit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterimUsePermittedAtSpecialRate">
                <xsd:annotation>
                    <xsd:documentation>Interim use permitted at special rate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalAirTransportAssociationCommission">
                <xsd:annotation>
                    <xsd:documentation>International air transport association (IATA) commission</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalAirTransportAssociationMarkup">
                <xsd:annotation>
                    <xsd:documentation>International air transport association (IATA) markup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalCourier">
                <xsd:annotation>
                    <xsd:documentation>International courier</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalDoor-to-DoorHandlingFee">
                <xsd:annotation>
                    <xsd:documentation>International door-to-door handling fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterplantCharge">
                <xsd:annotation>
                    <xsd:documentation>Interplant charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterstateHighwayToll">
                <xsd:annotation>
                    <xsd:documentation>Interstate/highway toll</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InTransitPriceProtection">
                <xsd:annotation>
                    <xsd:documentation>In transit price protection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Intra-plantCharge">
                <xsd:annotation>
                    <xsd:documentation>Intra-plant charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceAdditionalAmount">
                <xsd:annotation>
                    <xsd:documentation>Invoice additional amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Invoice adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceAt-CostAmount">
                <xsd:annotation>
                    <xsd:documentation>Invoice at-cost amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceDeliveryTermsAmount">
                <xsd:annotation>
                    <xsd:documentation>Invoice delivery terms amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceNo-ChargeAmount">
                <xsd:annotation>
                    <xsd:documentation>Invoice no-charge amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceServices">
                <xsd:annotation>
                    <xsd:documentation>Invoice services</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceWithGoods">
                <xsd:annotation>
                    <xsd:documentation>Invoice with Goods</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IrishArbitraries">
                <xsd:annotation>
                    <xsd:documentation>Irish arbitraries</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IslandDeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>Island delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IslandPick-UpCharge">
                <xsd:annotation>
                    <xsd:documentation>Island pick-up charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItalianReleaseCharge">
                <xsd:annotation>
                    <xsd:documentation>Italian release charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemPercentage">
                <xsd:annotation>
                    <xsd:documentation>Item percentage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Item-Unit">
                <xsd:annotation>
                    <xsd:documentation>Item-unit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KeepFromFreezingPercentDifferential">
                <xsd:annotation>
                    <xsd:documentation>Keep from freezing percent differential</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Koshering">
                <xsd:annotation>
                    <xsd:documentation>Koshering</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LabelAllowance">
                <xsd:annotation>
                    <xsd:documentation>Label allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Labeling">
                <xsd:annotation>
                    <xsd:documentation>Labeling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborCharges">
                <xsd:annotation>
                    <xsd:documentation>Labor charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborCostOfRemoval">
                <xsd:annotation>
                    <xsd:documentation>Labor cost of removal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborModify">
                <xsd:annotation>
                    <xsd:documentation>Labor, modify</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborNoTroubleFound">
                <xsd:annotation>
                    <xsd:documentation>Labor, no trouble found</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborOvertime">
                <xsd:annotation>
                    <xsd:documentation>Labor, overtime</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborPremiumOvertime">
                <xsd:annotation>
                    <xsd:documentation>Labor, premium overtime</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborRepairAndReturnOrders">
                <xsd:annotation>
                    <xsd:documentation>Labor (repair and return orders)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborService">
                <xsd:annotation>
                    <xsd:documentation>Labor service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborStraight-time">
                <xsd:annotation>
                    <xsd:documentation>Labor, straight-time</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborTestAndCalibrate">
                <xsd:annotation>
                    <xsd:documentation>Labor, test and calibrate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LadingAdjustmentCharge">
                <xsd:annotation>
                    <xsd:documentation>Lading adjustment charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LandCurrencyAdjustmentFactor-20FootContainer">
                <xsd:annotation>
                    <xsd:documentation>Land currency adjustment factor - 20 foot container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LandCurrencyAdjustmentFactor-40FootContainer">
                <xsd:annotation>
                    <xsd:documentation>Land currency adjustment factor - 40 foot container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Lashing">
                <xsd:annotation>
                    <xsd:documentation>Lashing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LateOrderCharge">
                <xsd:annotation>
                    <xsd:documentation>Late order charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatePaymentCharge">
                <xsd:annotation>
                    <xsd:documentation>Late payment charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LayoutDesign">
                <xsd:annotation>
                    <xsd:documentation>Layout/design</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LayoverCharges">
                <xsd:annotation>
                    <xsd:documentation>Layover charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeadFactor">
                <xsd:annotation>
                    <xsd:documentation>Lead factor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeakingUndergroundStorageTax">
                <xsd:annotation>
                    <xsd:documentation>Leaking underground storage tax (LUST)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseShortfallConsideration">
                <xsd:annotation>
                    <xsd:documentation>Lease shortfall consideration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LessThanContainer">
                <xsd:annotation>
                    <xsd:documentation>Less than container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LessThanTruckloadCharge">
                <xsd:annotation>
                    <xsd:documentation>Less than truckload (LTL) charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LetterOfCreditProcessing">
                <xsd:annotation>
                    <xsd:documentation>Letter of credit processing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LicenseAndTitle">
                <xsd:annotation>
                    <xsd:documentation>License and title</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LifelineSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Lifeline surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiftGateTruckOrForkliftServiceAtPick-upDelivery">
                <xsd:annotation>
                    <xsd:documentation>Lift gate (Truck) or forklift service at pick-up/delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LimeFee">
                <xsd:annotation>
                    <xsd:documentation>Lime fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LineItemAllowance">
                <xsd:annotation>
                    <xsd:documentation>Allowance is related to a line item. It can override a default allowance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LineItemCharge">
                <xsd:annotation>
                    <xsd:documentation>Charge is related to a line item. It can override a default charge.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LinehaulFromPortOfDebarkation">
                <xsd:annotation>
                    <xsd:documentation>Linehaul from port of debarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LinehaulPercentDifferential">
                <xsd:annotation>
                    <xsd:documentation>Linehaul percent differential</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LinehaulToPortOfEmbarkation">
                <xsd:annotation>
                    <xsd:documentation>Linehaul to port of embarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LinerTermsAtPortOfDebarkation">
                <xsd:annotation>
                    <xsd:documentation>Liner Terms at port of debarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LinerTermsAtPortOfEmbarkation">
                <xsd:annotation>
                    <xsd:documentation>Liner Terms at port of embarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidatedDamages">
                <xsd:annotation>
                    <xsd:documentation>Liquidated damages</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidationAnti-DumpingDuty">
                <xsd:annotation>
                    <xsd:documentation>Liquidation anti-dumping duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidationCountervailingDuty">
                <xsd:annotation>
                    <xsd:documentation>Liquidation countervailing duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidationTaxAmount">
                <xsd:annotation>
                    <xsd:documentation>Liquidation tax amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidationTotalDueUSCustomsService">
                <xsd:annotation>
                    <xsd:documentation>Liquidation total due U.S. customs service (USCS)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidationTotalFees">
                <xsd:annotation>
                    <xsd:documentation>Liquidation total fees</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Loading">
                <xsd:annotation>
                    <xsd:documentation>Loading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoadingLaborCharges">
                <xsd:annotation>
                    <xsd:documentation>Loading (labor charges)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoadWeighingCharge">
                <xsd:annotation>
                    <xsd:documentation>Load weighing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanFee">
                <xsd:annotation>
                    <xsd:documentation>Loan fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocalDeliveryDrayage">
                <xsd:annotation>
                    <xsd:documentation>Local delivery/drayage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocomotiveDelayedInSwitchingService">
                <xsd:annotation>
                    <xsd:documentation>Locomotive delayed in switching service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocomotiveUnderOwnPower">
                <xsd:annotation>
                    <xsd:documentation>Locomotive under own power</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LotCharge">
                <xsd:annotation>
                    <xsd:documentation>Lot charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LotteryChargeBack">
                <xsd:annotation>
                    <xsd:documentation>Lottery charge back</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LotteryCrossRedeemed">
                <xsd:annotation>
                    <xsd:documentation>Lottery cross redeemed</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Low-TierLotteryCashingBonus">
                <xsd:annotation>
                    <xsd:documentation>Low-tier lottery cashing bonus</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Low-TierLotteryWinners">
                <xsd:annotation>
                    <xsd:documentation>Low-tier lottery winners</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LumpSum">
                <xsd:annotation>
                    <xsd:documentation>Lump sum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MachiningCharge">
                <xsd:annotation>
                    <xsd:documentation>Machining charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailFee">
                <xsd:annotation>
                    <xsd:documentation>Mail fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mailing-PostageCost">
                <xsd:annotation>
                    <xsd:documentation>Mailing - postage cost</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mailing-ServiceFee">
                <xsd:annotation>
                    <xsd:documentation>Mailing - service fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailInvoice">
                <xsd:annotation>
                    <xsd:documentation>Mail invoice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailInvoiceToEachLocation">
                <xsd:annotation>
                    <xsd:documentation>Mail invoice to each location</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManifestCharge">
                <xsd:annotation>
                    <xsd:documentation>Manifest charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Manufacturing">
                <xsd:annotation>
                    <xsd:documentation>Manufacturing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarketDevelopmentFunds">
                <xsd:annotation>
                    <xsd:documentation>Market development funds</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarkingOrTaggingCharge">
                <xsd:annotation>
                    <xsd:documentation>Marking or tagging charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarriageRule">
                <xsd:annotation>
                    <xsd:documentation>Marriage rule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Maximum">
                <xsd:annotation>
                    <xsd:documentation>Highest possible value   maximum     not exceeding   up to.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MemoReturnableContainer">
                <xsd:annotation>
                    <xsd:documentation>Memo returnable container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MerchandiseProcessingFee">
                <xsd:annotation>
                    <xsd:documentation>Merchandise processing fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessageCharge">
                <xsd:annotation>
                    <xsd:documentation>Message charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessageRateAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Message rate adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessengerService">
                <xsd:annotation>
                    <xsd:documentation>Messenger service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MetalsSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Metals surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeterCharge">
                <xsd:annotation>
                    <xsd:documentation>Meter charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mid-TierLotteryCashingBonus">
                <xsd:annotation>
                    <xsd:documentation>Mid-tier lottery cashing bonus</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mid-TierLotteryWinners">
                <xsd:annotation>
                    <xsd:documentation>Mid-tier lottery winners</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MileageFeeForRepairAndReturn">
                <xsd:annotation>
                    <xsd:documentation>Mileage fee (for repair and return)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MileageOrTravel">
                <xsd:annotation>
                    <xsd:documentation>Mileage or travel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Minus-Amount">
                <xsd:annotation>
                    <xsd:documentation>The lesser value expressed in amount.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Minus-Percentage">
                <xsd:annotation>
                    <xsd:documentation>The lesser value expressed in percentage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ModifiedAtmosphere">
                <xsd:annotation>
                    <xsd:documentation>Modified atmosphere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MonthlyRental">
                <xsd:annotation>
                    <xsd:documentation>Monthly rental</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MountDemount">
                <xsd:annotation>
                    <xsd:documentation>Mount/demount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mounting">
                <xsd:annotation>
                    <xsd:documentation>Mounting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MunicipalSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Municipal surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MushroomFee">
                <xsd:annotation>
                    <xsd:documentation>Mushroom fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewDiscount">
                <xsd:annotation>
                    <xsd:documentation>New discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewDistributionAllowance">
                <xsd:annotation>
                    <xsd:documentation>New distribution allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewItemAllowance">
                <xsd:annotation>
                    <xsd:documentation>New item allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewStoreAllowance">
                <xsd:annotation>
                    <xsd:documentation>New store allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewStoreDiscount">
                <xsd:annotation>
                    <xsd:documentation>New store discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewWarehouse">
                <xsd:annotation>
                    <xsd:documentation>New warehouse</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewWarehouseDiscount">
                <xsd:annotation>
                    <xsd:documentation>New warehouse discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewYorkDeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>New York delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewYorkPick-upCharge">
                <xsd:annotation>
                    <xsd:documentation>New York pick-up charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NHDWharfage">
                <xsd:annotation>
                    <xsd:documentation>N.H.D. wharfage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoAllowance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoAllowanceOrCharge">
                <xsd:annotation>
                    <xsd:documentation>No increases or reduction in price (list or stated) are included.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoCharge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-DutiableCharges">
                <xsd:annotation>
                    <xsd:documentation>Non-dutiable charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NonGeneratedFreight">
                <xsd:annotation>
                    <xsd:documentation>Non generated freight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-returnableContainers">
                <xsd:annotation>
                    <xsd:documentation>Non-returnable containers</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoReturnCreditAllowance">
                <xsd:annotation>
                    <xsd:documentation>No return credit allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NormalPumpCharge">
                <xsd:annotation>
                    <xsd:documentation>Normal pump charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotarizedAffidavit">
                <xsd:annotation>
                    <xsd:documentation>Notarized affidavit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotifyConsignee">
                <xsd:annotation>
                    <xsd:documentation>Notify consignee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotifyConsigneeBeforeDelivery">
                <xsd:annotation>
                    <xsd:documentation>Notify consignee before delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NozzleCharge">
                <xsd:annotation>
                    <xsd:documentation>Nozzle charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OceanCharges-Hazardous">
                <xsd:annotation>
                    <xsd:documentation>Ocean charges - hazardous</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OceanFreight">
                <xsd:annotation>
                    <xsd:documentation>Ocean freight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OfficeSupplies">
                <xsd:annotation>
                    <xsd:documentation>Office supplies</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Offshore-AlaskaHawaii">
                <xsd:annotation>
                    <xsd:documentation>Offshore - Alaska/Hawaii</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnCarriage">
                <xsd:annotation>
                    <xsd:documentation>On carriage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnDeckBreakBulkDifferential">
                <xsd:annotation>
                    <xsd:documentation>On deck break bulk differential</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="One-DayService">
                <xsd:annotation>
                    <xsd:documentation>One - day service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OneTimeEngineeringCharge">
                <xsd:annotation>
                    <xsd:documentation>One time engineering charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="One-TimeLicenseFee">
                <xsd:annotation>
                    <xsd:documentation>One-time license fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="One-Time-OnlyCharge">
                <xsd:annotation>
                    <xsd:documentation>One-time-only charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnetimeTooling">
                <xsd:annotation>
                    <xsd:documentation>Onetime tooling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnHandService">
                <xsd:annotation>
                    <xsd:documentation>On hand service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatorCredit">
                <xsd:annotation>
                    <xsd:documentation>Operator credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptionalCharge">
                <xsd:annotation>
                    <xsd:documentation>Optional charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptionalSoftwareSupportForOperationalSupportSystems">
                <xsd:annotation>
                    <xsd:documentation>Optional software support for operational support systems</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptionalSoftwareSupportForSwitchingSystems">
                <xsd:annotation>
                    <xsd:documentation>Optional software support for switching systems</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptionChargeColorFabricOfficeFurniture">
                <xsd:annotation>
                    <xsd:documentation>Option charge (color fabric office furniture)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Order-Flat">
                <xsd:annotation>
                    <xsd:documentation>Order-flat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderNotifyCharge">
                <xsd:annotation>
                    <xsd:documentation>Order notify charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherAccessorialServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Other accessorial service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherAdvances">
                <xsd:annotation>
                    <xsd:documentation>Other advances</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherExportCharges">
                <xsd:annotation>
                    <xsd:documentation>Other export charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherGovernmentAgencyDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Other government agency declaration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherGovernmentAgencyExam">
                <xsd:annotation>
                    <xsd:documentation>Other government agency exam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherImportCharge">
                <xsd:annotation>
                    <xsd:documentation>Other import charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherMiscellaneousEarningOrAdditive">
                <xsd:annotation>
                    <xsd:documentation>Other miscellaneous earning or additive</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherSeeRelatedDescription">
                <xsd:annotation>
                    <xsd:documentation>Other (See related description)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutOfRouteMiles">
                <xsd:annotation>
                    <xsd:documentation>Out of route miles</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutOfZonePick-upOrDelivery">
                <xsd:annotation>
                    <xsd:documentation>Out of zone pick-up or delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutsideCableConnectors">
                <xsd:annotation>
                    <xsd:documentation>Outside cable connectors</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverDimension">
                <xsd:annotation>
                    <xsd:documentation>Over dimension</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverHeightContainer">
                <xsd:annotation>
                    <xsd:documentation>Over height container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverrunCharge">
                <xsd:annotation>
                    <xsd:documentation>Overrun charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OversizedPremium">
                <xsd:annotation>
                    <xsd:documentation>Oversized premium</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OvertimeLoading">
                <xsd:annotation>
                    <xsd:documentation>Overtime loading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverWidthContainer">
                <xsd:annotation>
                    <xsd:documentation>Over width container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Packaging">
                <xsd:annotation>
                    <xsd:documentation>Packaging</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackagingService">
                <xsd:annotation>
                    <xsd:documentation>Packaging service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackingCratingAndHandlingCharge">
                <xsd:annotation>
                    <xsd:documentation>Packing, crating, and handling charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackingCratingHandlingAndTransportationCharge">
                <xsd:annotation>
                    <xsd:documentation>Packing, crating, handling, and transportation charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackInvoiceWithShipment">
                <xsd:annotation>
                    <xsd:documentation>Pack invoice with shipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaintingPrimerOrFinish">
                <xsd:annotation>
                    <xsd:documentation>Painting (primer or finish)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pallet">
                <xsd:annotation>
                    <xsd:documentation>Pallet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PalletExchangeCharge">
                <xsd:annotation>
                    <xsd:documentation>Pallet exchange charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Palletizing">
                <xsd:annotation>
                    <xsd:documentation>Palletizing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Paralleling">
                <xsd:annotation>
                    <xsd:documentation>Paralleling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParishCountySalesTaxOnly">
                <xsd:annotation>
                    <xsd:documentation>Parish/county sales tax (only)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PassingShippersExportEntry">
                <xsd:annotation>
                    <xsd:documentation>Passing shippers export entry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Payment">
                <xsd:annotation>
                    <xsd:documentation>Payment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollAdditivesOvertimeLabor">
                <xsd:annotation>
                    <xsd:documentation>Payroll additives, overtime labor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollAdditivesStraightTimeLabor">
                <xsd:annotation>
                    <xsd:documentation>Payroll additives, straight time labor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollTaxes">
                <xsd:annotation>
                    <xsd:documentation>Payroll taxes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PecanFee">
                <xsd:annotation>
                    <xsd:documentation>Pecan fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PenaltyCharge">
                <xsd:annotation>
                    <xsd:documentation>Penalty charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PercentDifferential-LessThanContainer">
                <xsd:annotation>
                    <xsd:documentation>Percent differential - less than container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PercentOfProduct">
                <xsd:annotation>
                    <xsd:documentation>Percent of product</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PercentOfShipmentValueThatIsReturnable">
                <xsd:annotation>
                    <xsd:documentation>Percent of shipment value that is returnable</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PercentOfShippedQuantityThatIsReturnable">
                <xsd:annotation>
                    <xsd:documentation>Percent of shipped quantity that is returnable</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerformanceAllowance">
                <xsd:annotation>
                    <xsd:documentation>Performance allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerformanceAward">
                <xsd:annotation>
                    <xsd:documentation>Performance award</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerItemCharge">
                <xsd:annotation>
                    <xsd:documentation>Per item charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PermitCharge">
                <xsd:annotation>
                    <xsd:documentation>Permit charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PermitsBondsEscortAttendant">
                <xsd:annotation>
                    <xsd:documentation>Permits bonds escort attendant</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerOrderCharge">
                <xsd:annotation>
                    <xsd:documentation>Per order charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerPoundCharge">
                <xsd:annotation>
                    <xsd:documentation>Per pound charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonalPropertyMember">
                <xsd:annotation>
                    <xsd:documentation>Personal property, member</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonalPropertySpouse">
                <xsd:annotation>
                    <xsd:documentation>Personal property, spouse</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhosphatizingSteelTreatment">
                <xsd:annotation>
                    <xsd:documentation>Phosphatizing (steel treatment)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PickleAndOil">
                <xsd:annotation>
                    <xsd:documentation>Pickle and oil</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PickUp">
                <xsd:annotation>
                    <xsd:documentation>Pick/up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pick-upAndDelivery">
                <xsd:annotation>
                    <xsd:documentation>Pick-up and delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pickup-OutOfArea">
                <xsd:annotation>
                    <xsd:documentation>Pickup - out of area</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PickupSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Pickup surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PierChargesOtherThanWharfage">
                <xsd:annotation>
                    <xsd:documentation>Pier charges other than wharfage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PierCharges-Wharfage">
                <xsd:annotation>
                    <xsd:documentation>Pier charges - wharfage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PierPick-upAndOrDelivery">
                <xsd:annotation>
                    <xsd:documentation>Pier Pick-up and/or delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PierUnloading">
                <xsd:annotation>
                    <xsd:documentation>Pier unloading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PilotInspection">
                <xsd:annotation>
                    <xsd:documentation>Pilot inspection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlacementAndOrRemovalCharge">
                <xsd:annotation>
                    <xsd:documentation>Placement and/or removal charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Plating">
                <xsd:annotation>
                    <xsd:documentation>Plating</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Plus-Amount">
                <xsd:annotation>
                    <xsd:documentation>The greater value expressed in amount.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Plus-Percentage">
                <xsd:annotation>
                    <xsd:documentation>The greater value expressed in percentage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlusOrMinus-Amount">
                <xsd:annotation>
                    <xsd:documentation>The greater/lesser value expressed in amount.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlusOrMinus-Percentage">
                <xsd:annotation>
                    <xsd:documentation>The greater/lesser value expressed in percentage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PoleLashingEquipmentSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Pole lashing equipment (PLE) surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PoleWood-ServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Pole, wood-service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PorkFee">
                <xsd:annotation>
                    <xsd:documentation>Pork fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PortChanges">
                <xsd:annotation>
                    <xsd:documentation>Port changes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PortHandlingAndUnloading">
                <xsd:annotation>
                    <xsd:documentation>Port handling and unloading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PositioningAtOrigin">
                <xsd:annotation>
                    <xsd:documentation>Positioning at origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Postage">
                <xsd:annotation>
                    <xsd:documentation>Postage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostDamagedHandling">
                <xsd:annotation>
                    <xsd:documentation>Post damaged handling (PDC)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PotatoFee">
                <xsd:annotation>
                    <xsd:documentation>Potato fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PowerFactorAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Power factor adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-carriage">
                <xsd:annotation>
                    <xsd:documentation>Pre-carriage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-carriageExcess">
                <xsd:annotation>
                    <xsd:documentation>Pre-carriage excess</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreciousMetalContent">
                <xsd:annotation>
                    <xsd:documentation>Precious metal content</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreloadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Preloading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrelodgeCharge">
                <xsd:annotation>
                    <xsd:documentation>Prelodge charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiseUse">
                <xsd:annotation>
                    <xsd:documentation>Premise use</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiumCharge">
                <xsd:annotation>
                    <xsd:documentation>Premium charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiumTransportation">
                <xsd:annotation>
                    <xsd:documentation>Premium transportation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrepaidUsageAllowance">
                <xsd:annotation>
                    <xsd:documentation>Prepaid usage allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Preparation">
                <xsd:annotation>
                    <xsd:documentation>Preparation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationAndDelivery">
                <xsd:annotation>
                    <xsd:documentation>Preparation and delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationOfAirWaybill-Origin">
                <xsd:annotation>
                    <xsd:documentation>Preparation of air waybill - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationOfCanadianCustomsInvoice">
                <xsd:annotation>
                    <xsd:documentation>Preparation of Canadian customs invoice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationOfCommercialInvoice">
                <xsd:annotation>
                    <xsd:documentation>Preparation of commercial invoice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationOfExportEntry">
                <xsd:annotation>
                    <xsd:documentation>Preparation of export entry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationOfInsuranceCertificate">
                <xsd:annotation>
                    <xsd:documentation>Preparation of insurance certificate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationOfUSExportDocumentation">
                <xsd:annotation>
                    <xsd:documentation>Preparation of U.S. export documentation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-PositionedInventoryService">
                <xsd:annotation>
                    <xsd:documentation>Pre-positioned inventory service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousBilling">
                <xsd:annotation>
                    <xsd:documentation>Previous billing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceAdjustmentPercent">
                <xsd:annotation>
                    <xsd:documentation>Price adjustment percent (PCT)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceAndMarketingAllowance">
                <xsd:annotation>
                    <xsd:documentation>Price and marketing allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceDeviation">
                <xsd:annotation>
                    <xsd:documentation>Price deviation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorBalance">
                <xsd:annotation>
                    <xsd:documentation>Prior balance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorBillingAmount">
                <xsd:annotation>
                    <xsd:documentation>Prior billing amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorDeliveryOfBillCharge">
                <xsd:annotation>
                    <xsd:documentation>Prior delivery of bill charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorityService">
                <xsd:annotation>
                    <xsd:documentation>Priority service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorMonthCredit">
                <xsd:annotation>
                    <xsd:documentation>Prior month credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivateOwnedVehicleProcessing">
                <xsd:annotation>
                    <xsd:documentation>Private owned vehicle processing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Processing">
                <xsd:annotation>
                    <xsd:documentation>Processing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessingCharge">
                <xsd:annotation>
                    <xsd:documentation>Processing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessInTransitPrivilege">
                <xsd:annotation>
                    <xsd:documentation>Process in transit privilege</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcurementCharge">
                <xsd:annotation>
                    <xsd:documentation>Procurement charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProfessionalFees">
                <xsd:annotation>
                    <xsd:documentation>Professional fees</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProformaInvoice">
                <xsd:annotation>
                    <xsd:documentation>Proforma invoice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProgressPaymentRequirement">
                <xsd:annotation>
                    <xsd:documentation>Progress payment requirement</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionalAllowance">
                <xsd:annotation>
                    <xsd:documentation>Promotional allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionalDiscount">
                <xsd:annotation>
                    <xsd:documentation>Promotional discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProofComposition">
                <xsd:annotation>
                    <xsd:documentation>Proof and composition</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProofOfDelivery">
                <xsd:annotation>
                    <xsd:documentation>Proof of delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProtectiveServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Protective service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProtectiveService-Cold">
                <xsd:annotation>
                    <xsd:documentation>Protective service - cold</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProtectiveService-Heat">
                <xsd:annotation>
                    <xsd:documentation>Protective service - heat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProvideHouseholdIdentifier">
                <xsd:annotation>
                    <xsd:documentation>Provide household identifier</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProvideNameAndAddress">
                <xsd:annotation>
                    <xsd:documentation>Provide name and address</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PullingEyes">
                <xsd:annotation>
                    <xsd:documentation>Pulling eyes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PumpAirCharge">
                <xsd:annotation>
                    <xsd:documentation>Pump air charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PumpCharge">
                <xsd:annotation>
                    <xsd:documentation>Pump charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOption">
                <xsd:annotation>
                    <xsd:documentation>Purchase option</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuantityDiscount">
                <xsd:annotation>
                    <xsd:documentation>Quantity discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuantitySurcharge">
                <xsd:annotation>
                    <xsd:documentation>Quantity surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RailheadHandling">
                <xsd:annotation>
                    <xsd:documentation>Railhead handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Ramping">
                <xsd:annotation>
                    <xsd:documentation>Ramping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RateCode">
                <xsd:annotation>
                    <xsd:documentation>Rate code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RateReductionBond">
                <xsd:annotation>
                    <xsd:documentation>Rate reduction bond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Rebate">
                <xsd:annotation>
                    <xsd:documentation>Rebate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Re-BillCharge">
                <xsd:annotation>
                    <xsd:documentation>Re-bill charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RebilledDrayage-Destination">
                <xsd:annotation>
                    <xsd:documentation>Rebilled drayage - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RebilledDrayage-Origin">
                <xsd:annotation>
                    <xsd:documentation>Rebilled drayage - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Receiving">
                <xsd:annotation>
                    <xsd:documentation>Receiving</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecipientAddressCorrection">
                <xsd:annotation>
                    <xsd:documentation>Recipient address correction</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReclamationCenterHandlingChute">
                <xsd:annotation>
                    <xsd:documentation>Reclamation center handling (chute)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReclamationFederal">
                <xsd:annotation>
                    <xsd:documentation>Reclamation, federal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReclamationSharedResponsibility">
                <xsd:annotation>
                    <xsd:documentation>Reclamation shared responsibility (SRS)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReclamationState">
                <xsd:annotation>
                    <xsd:documentation>Reclamation, state</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reconciliation">
                <xsd:annotation>
                    <xsd:documentation>Reconciliation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reconnectcharge">
                <xsd:annotation>
                    <xsd:documentation>Reconnect charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReconsignConsigneeCharge">
                <xsd:annotation>
                    <xsd:documentation>Reconsign consignee charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReconsignDeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>Reconsign delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReconsignmentCharge">
                <xsd:annotation>
                    <xsd:documentation>Reconsignment charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecooperingAtOwnersOrShippersExpense">
                <xsd:annotation>
                    <xsd:documentation>Recoopering (at owner's or shipper's expense)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecordFiling">
                <xsd:annotation>
                    <xsd:documentation>Record/filing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Recovery">
                <xsd:annotation>
                    <xsd:documentation>Recovery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecoveryFee">
                <xsd:annotation>
                    <xsd:documentation>Recovery fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecratingRecoopering-Destination">
                <xsd:annotation>
                    <xsd:documentation>Recrating/recoopering - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecratingRecoopering-Origin">
                <xsd:annotation>
                    <xsd:documentation>Recrating/recoopering - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecurringHardwareMaintenanceCharge">
                <xsd:annotation>
                    <xsd:documentation>Recurring hardware maintenance charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecurringLicenseFee">
                <xsd:annotation>
                    <xsd:documentation>Recurring license fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecurringSoftwareMaintenanceCharge">
                <xsd:annotation>
                    <xsd:documentation>Recurring software maintenance charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Redelivery">
                <xsd:annotation>
                    <xsd:documentation>Redelivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RedistributionAllowance">
                <xsd:annotation>
                    <xsd:documentation>Redistribution allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReductionPrepalletizedCargo">
                <xsd:annotation>
                    <xsd:documentation>Reduction prepalletized cargo</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReeferCargoPercentDifferential">
                <xsd:annotation>
                    <xsd:documentation>Reefer cargo percent differential</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReeferMaintenance">
                <xsd:annotation>
                    <xsd:documentation>Reefer maintenance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reel">
                <xsd:annotation>
                    <xsd:documentation>Reel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReelCable">
                <xsd:annotation>
                    <xsd:documentation>Reel cable</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReelDeposit">
                <xsd:annotation>
                    <xsd:documentation>Reel deposit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Refrigeration">
                <xsd:annotation>
                    <xsd:documentation>Refrigeration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RefrigerationMechanicalDetention">
                <xsd:annotation>
                    <xsd:documentation>Refrigeration/mechanical detention</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Refund">
                <xsd:annotation>
                    <xsd:documentation>Refund</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RefurbishingCharge">
                <xsd:annotation>
                    <xsd:documentation>Refurbishing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Regain">
                <xsd:annotation>
                    <xsd:documentation>Regain</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationOfExportForReentry">
                <xsd:annotation>
                    <xsd:documentation>Registration of export for reentry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationOfExportShipments">
                <xsd:annotation>
                    <xsd:documentation>Registration of export shipments</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegulatoryFee">
                <xsd:annotation>
                    <xsd:documentation>Regulatory fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegulatoryRequiredRefund">
                <xsd:annotation>
                    <xsd:documentation>Regulatory required refund</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReliabilityCharge">
                <xsd:annotation>
                    <xsd:documentation>Reliability charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RelinquishmentCharge">
                <xsd:annotation>
                    <xsd:documentation>Relinquishment charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReliquidationAnti-DumpingDuty">
                <xsd:annotation>
                    <xsd:documentation>Reliquidation anti-dumping duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReliquidationCountervailingDuty">
                <xsd:annotation>
                    <xsd:documentation>Reliquidation countervailing duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReliquidationTaxAmount">
                <xsd:annotation>
                    <xsd:documentation>Reliquidation tax amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReliquidationTotalDueUSCustomsService">
                <xsd:annotation>
                    <xsd:documentation>Reliquidation total due u.s. customs service (USCS)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReliquidationTotalFees">
                <xsd:annotation>
                    <xsd:documentation>Reliquidation total fees</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentalCharge">
                <xsd:annotation>
                    <xsd:documentation>Rental charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentalDeduction">
                <xsd:annotation>
                    <xsd:documentation>Rental deduction</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentsAndLeases">
                <xsd:annotation>
                    <xsd:documentation>Rents and Leases</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepackCharge">
                <xsd:annotation>
                    <xsd:documentation>Repack charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Repair">
                <xsd:annotation>
                    <xsd:documentation>Repair</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairAtBuyersExpenseCharge">
                <xsd:annotation>
                    <xsd:documentation>Repair at buyers expense charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairAtCustomerExpenseCharge">
                <xsd:annotation>
                    <xsd:documentation>Repair at customer expense charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairAtGovernmentExpenseCharge">
                <xsd:annotation>
                    <xsd:documentation>Repair at government expense charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Repickup">
                <xsd:annotation>
                    <xsd:documentation>Repickup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestViaCanada">
                <xsd:annotation>
                    <xsd:documentation>Request via Canada</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResearchDevelopmentFee">
                <xsd:annotation>
                    <xsd:documentation>Research and development fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResellersDiscount">
                <xsd:annotation>
                    <xsd:documentation>Resellers discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidentialDelivery">
                <xsd:annotation>
                    <xsd:documentation>Residential delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidentialPick-up">
                <xsd:annotation>
                    <xsd:documentation>Residential Pick-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Respotting">
                <xsd:annotation>
                    <xsd:documentation>Respotting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RestockingCharge">
                <xsd:annotation>
                    <xsd:documentation>Restocking charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RestrictedArticleFee">
                <xsd:annotation>
                    <xsd:documentation>Restricted article fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Retainer">
                <xsd:annotation>
                    <xsd:documentation>Retainer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnableContainer">
                <xsd:annotation>
                    <xsd:documentation>Returnable container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnCargoCharge">
                <xsd:annotation>
                    <xsd:documentation>Return cargo charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnedLoad">
                <xsd:annotation>
                    <xsd:documentation>Returned load</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Rework">
                <xsd:annotation>
                    <xsd:documentation>Rework</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RidingAttendantCharge">
                <xsd:annotation>
                    <xsd:documentation>Riding attendant charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RockyMountainBureau583Item1100ArbitraryCharge">
                <xsd:annotation>
                    <xsd:documentation>Rocky mountain bureau 583 item 1100 arbitrary charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RollOutAdjustment">
                <xsd:annotation>
                    <xsd:documentation>Roll out adjustment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RollRebate">
                <xsd:annotation>
                    <xsd:documentation>Roll rebate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Royalties">
                <xsd:annotation>
                    <xsd:documentation>Royalties</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Salvage">
                <xsd:annotation>
                    <xsd:documentation>Salvage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Same-DayService">
                <xsd:annotation>
                    <xsd:documentation>Same - day service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SaturdayDelivery">
                <xsd:annotation>
                    <xsd:documentation>Saturday delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SaturdayPick-Up">
                <xsd:annotation>
                    <xsd:documentation>Saturday pick-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SaturdayPick-upOrDeliveryCharge">
                <xsd:annotation>
                    <xsd:documentation>Saturday pick-up or delivery charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScaleCharge">
                <xsd:annotation>
                    <xsd:documentation>Scale charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScaleChargeUnloading">
                <xsd:annotation>
                    <xsd:documentation>Scale charge unloading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScrapAllowance">
                <xsd:annotation>
                    <xsd:documentation>Scrap allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecuritySignatureService">
                <xsd:annotation>
                    <xsd:documentation>Security signature service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SegregatingSorting">
                <xsd:annotation>
                    <xsd:documentation>Segregating (sorting)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SelectCharge">
                <xsd:annotation>
                    <xsd:documentation>Select charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SelfUnloader">
                <xsd:annotation>
                    <xsd:documentation>Self unloader</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellerHandCarry">
                <xsd:annotation>
                    <xsd:documentation>Seller hand carry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceAssistanceProgramSurcharge">
                <xsd:annotation>
                    <xsd:documentation>Service assistance program surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceChargeWithCashDiscount">
                <xsd:annotation>
                    <xsd:documentation>Service charge (with cash discount)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceUpgrade">
                <xsd:annotation>
                    <xsd:documentation>Service upgrade</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Set-up">
                <xsd:annotation>
                    <xsd:documentation>Set-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shearing">
                <xsd:annotation>
                    <xsd:documentation>Shearing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SheepFee">
                <xsd:annotation>
                    <xsd:documentation>Sheep fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperLoad">
                <xsd:annotation>
                    <xsd:documentation>Shipper load</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperLoadAndCount">
                <xsd:annotation>
                    <xsd:documentation>Shipper load and count</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperLoadCarrierCount">
                <xsd:annotation>
                    <xsd:documentation>Shipper load carrier count</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperLoadConsigneeUnload">
                <xsd:annotation>
                    <xsd:documentation>Shipper load consignee unload</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shipping">
                <xsd:annotation>
                    <xsd:documentation>Shipping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingAndHandling">
                <xsd:annotation>
                    <xsd:documentation>Shipping and handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipsidePickup">
                <xsd:annotation>
                    <xsd:documentation>Shipside pickup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipToStockQualityAudit">
                <xsd:annotation>
                    <xsd:documentation>Ship to stock quality audit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shotblasting">
                <xsd:annotation>
                    <xsd:documentation>Shotblasting</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShrinkageAllowance">
                <xsd:annotation>
                    <xsd:documentation>Shrinkage allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShrinkAllowance">
                <xsd:annotation>
                    <xsd:documentation>Shrink allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shrink-WrapCharge">
                <xsd:annotation>
                    <xsd:documentation>Shrink-wrap charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SingleFactorOriginationDestination">
                <xsd:annotation>
                    <xsd:documentation>Single factor origination/destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SingleFactorOriginationPortOfDebarkation">
                <xsd:annotation>
                    <xsd:documentation>Single factor origination/port of debarkation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SingleFactorPortOfEmbarkationDestination">
                <xsd:annotation>
                    <xsd:documentation>Single factor port of embarkation/destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SingleInvoiceAllowance">
                <xsd:annotation>
                    <xsd:documentation>Single invoice allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SinglePick-up">
                <xsd:annotation>
                    <xsd:documentation>Single pick-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SingleShipmentFee">
                <xsd:annotation>
                    <xsd:documentation>Single shipment fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sleeving">
                <xsd:annotation>
                    <xsd:documentation>Sleeving</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlipSheetRail">
                <xsd:annotation>
                    <xsd:documentation>Slip sheet, rail</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlipSheetTruck">
                <xsd:annotation>
                    <xsd:documentation>Slip sheet, truck</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlipSheetUnloadingAllowance">
                <xsd:annotation>
                    <xsd:documentation>Slip sheet unloading allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlottingAllowance">
                <xsd:annotation>
                    <xsd:documentation>Slotting allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SmallOrderCharge">
                <xsd:annotation>
                    <xsd:documentation>Small order charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoftwareSupportService">
                <xsd:annotation>
                    <xsd:documentation>Software support service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SourceInspection">
                <xsd:annotation>
                    <xsd:documentation>Source inspection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialAllowance">
                <xsd:annotation>
                    <xsd:documentation>Special allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialBuy">
                <xsd:annotation>
                    <xsd:documentation>Special buy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialCircusTrains">
                <xsd:annotation>
                    <xsd:documentation>Special circus trains</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialCredit">
                <xsd:annotation>
                    <xsd:documentation>Special credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialDelivery">
                <xsd:annotation>
                    <xsd:documentation>Special delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialDetentionCharge">
                <xsd:annotation>
                    <xsd:documentation>Special detention charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialEquipmentCharge">
                <xsd:annotation>
                    <xsd:documentation>Special equipment charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialFinishCharge">
                <xsd:annotation>
                    <xsd:documentation>Special finish charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialFreightSupplements">
                <xsd:annotation>
                    <xsd:documentation>Special freight supplements</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialHandling">
                <xsd:annotation>
                    <xsd:documentation>Special handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialMileageMovements">
                <xsd:annotation>
                    <xsd:documentation>Special mileage movements</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialPackaging">
                <xsd:annotation>
                    <xsd:documentation>Special packaging</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialPermits">
                <xsd:annotation>
                    <xsd:documentation>Special permits</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialPickup">
                <xsd:annotation>
                    <xsd:documentation>Special pickup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialPumpCharge">
                <xsd:annotation>
                    <xsd:documentation>Special pump charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialSealCharge">
                <xsd:annotation>
                    <xsd:documentation>Special seal charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialServices">
                <xsd:annotation>
                    <xsd:documentation>Special services</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialTestEquipmentCharge">
                <xsd:annotation>
                    <xsd:documentation>Special test equipment charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialToolingCharge">
                <xsd:annotation>
                    <xsd:documentation>Special tooling charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialToolingreworkcharge">
                <xsd:annotation>
                    <xsd:documentation>Special Tooling rework charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialTrainMovement">
                <xsd:annotation>
                    <xsd:documentation>Special train movement</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialUse">
                <xsd:annotation>
                    <xsd:documentation>Special use</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialVehicleRent">
                <xsd:annotation>
                    <xsd:documentation>Special vehicle rent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecificationReview">
                <xsd:annotation>
                    <xsd:documentation>Specification review</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecificDuty">
                <xsd:annotation>
                    <xsd:documentation>Specific duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SplitDelivery">
                <xsd:annotation>
                    <xsd:documentation>Split delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SplitPick-up">
                <xsd:annotation>
                    <xsd:documentation>Split pick-up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SplitPick-UpAtPierCharge">
                <xsd:annotation>
                    <xsd:documentation>Split pick-up at pier charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpoolCharge">
                <xsd:annotation>
                    <xsd:documentation>Spool charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpottingOfTrailer">
                <xsd:annotation>
                    <xsd:documentation>Spotting of trailer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpreaderCharge">
                <xsd:annotation>
                    <xsd:documentation>Spreader charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StampFee">
                <xsd:annotation>
                    <xsd:documentation>Stamp fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stamping">
                <xsd:annotation>
                    <xsd:documentation>Stamping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StandbyCharge">
                <xsd:annotation>
                    <xsd:documentation>Standby charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateMetropolitanTransitAuthoritySurcharge">
                <xsd:annotation>
                    <xsd:documentation>State/metropolitan transit authority surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateMotorFuel">
                <xsd:annotation>
                    <xsd:documentation>State motor fuel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateSalesCharge">
                <xsd:annotation>
                    <xsd:documentation>State sales charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateSurcharge">
                <xsd:annotation>
                    <xsd:documentation>State surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SteamingCharge">
                <xsd:annotation>
                    <xsd:documentation>Steaming charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StencilingCharge">
                <xsd:annotation>
                    <xsd:documentation>Stenciling charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stopcharge">
                <xsd:annotation>
                    <xsd:documentation>Stopcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stop-offAtDestination">
                <xsd:annotation>
                    <xsd:documentation>Stop-off at destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stop-offAtOrigination">
                <xsd:annotation>
                    <xsd:documentation>Stop-off at origination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stop-offAtPierCharge">
                <xsd:annotation>
                    <xsd:documentation>Stop-off at pier charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stop-offCharge">
                <xsd:annotation>
                    <xsd:documentation>Stop-off charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StoppingInTransit">
                <xsd:annotation>
                    <xsd:documentation>Stopping in transit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Storage">
                <xsd:annotation>
                    <xsd:documentation>Storage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StorageInTransit">
                <xsd:annotation>
                    <xsd:documentation>Storage in transit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StraighteningCharge">
                <xsd:annotation>
                    <xsd:documentation>Straightening charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Strapping">
                <xsd:annotation>
                    <xsd:documentation>Strapping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StreetLampsCharge">
                <xsd:annotation>
                    <xsd:documentation>Street lamps charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StrippingSortingAndConsolidation">
                <xsd:annotation>
                    <xsd:documentation>Stripping, sorting, and consolidation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stuffing">
                <xsd:annotation>
                    <xsd:documentation>Stuffing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StuffingCharge">
                <xsd:annotation>
                    <xsd:documentation>Stuffing charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubjectToCooperativeAdvertisingAllowance">
                <xsd:annotation>
                    <xsd:documentation>Subject to cooperative advertising allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubjectToTaxOnResale">
                <xsd:annotation>
                    <xsd:documentation>Subject to tax on resale</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SufferanceWarehouseChargeExportOrImport">
                <xsd:annotation>
                    <xsd:documentation>Sufferance warehouse charge (export or import)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SugarFee">
                <xsd:annotation>
                    <xsd:documentation>Sugar fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SumOfAddsAndDeductsToMakeMarketValue">
                <xsd:annotation>
                    <xsd:documentation>Sum of adds and deducts to make market value</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SundayOrHolidayPick-upOrDelivery">
                <xsd:annotation>
                    <xsd:documentation>Sunday or holiday pick-up or delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuperBagCharge">
                <xsd:annotation>
                    <xsd:documentation>Super bag charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupervisorCharge">
                <xsd:annotation>
                    <xsd:documentation>Supervisor charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplementalDuty">
                <xsd:annotation>
                    <xsd:documentation>Supplemental duty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplementalItems">
                <xsd:annotation>
                    <xsd:documentation>Supplemental items</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Surcharge">
                <xsd:annotation>
                    <xsd:documentation>Surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuretyBond">
                <xsd:annotation>
                    <xsd:documentation>Surety bond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Swell">
                <xsd:annotation>
                    <xsd:documentation>Swell</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SwitchCharge">
                <xsd:annotation>
                    <xsd:documentation>Switch charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SwitchingCharge">
                <xsd:annotation>
                    <xsd:documentation>Switching charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TankCarAllowance">
                <xsd:annotation>
                    <xsd:documentation>Tank car allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TankRental">
                <xsd:annotation>
                    <xsd:documentation>Tank rental</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tarping">
                <xsd:annotation>
                    <xsd:documentation>Tarping</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax">
                <xsd:annotation>
                    <xsd:documentation>Tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-AirportTaxDestination">
                <xsd:annotation>
                    <xsd:documentation>Tax - airport tax, destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-AirportTaxOrigin">
                <xsd:annotation>
                    <xsd:documentation>Tax - airport tax, origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-BeverageTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - beverage tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-CitySalesTaxOnly">
                <xsd:annotation>
                    <xsd:documentation>Tax - city sales tax (only)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxCredit">
                <xsd:annotation>
                    <xsd:documentation>Tax credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-ExciseTax-Destination">
                <xsd:annotation>
                    <xsd:documentation>Tax - excise tax - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-ExciseTax-Origin">
                <xsd:annotation>
                    <xsd:documentation>Tax - excise tax - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-FederalExciseTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - Federal excise tax, FET</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-FederalExciseTaxOnTires">
                <xsd:annotation>
                    <xsd:documentation>Tax - Federal excise tax, FET, on Tires</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-Governmental">
                <xsd:annotation>
                    <xsd:documentation>Tax - governmental</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-HandlingChargeTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - handling charge tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxLiability-Amortized">
                <xsd:annotation>
                    <xsd:documentation>Tax liability - amortized</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxLiability-OneTime">
                <xsd:annotation>
                    <xsd:documentation>Tax liability - one time</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-LocalSalesTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - local sales tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-LocalTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - local tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-MetropolitanTransitTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - metropolitan transit tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxOnMiscellaneousCharges">
                <xsd:annotation>
                    <xsd:documentation>Tax on miscellaneous charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxOnTransportation">
                <xsd:annotation>
                    <xsd:documentation>Tax on transportation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-RegulatoryTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - regulatory tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-SalesandUse">
                <xsd:annotation>
                    <xsd:documentation>Tax - sales and use</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-SalesTaxStateAndLocal">
                <xsd:annotation>
                    <xsd:documentation>Tax - sales tax (state and local)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-StateHazardousSubstance">
                <xsd:annotation>
                    <xsd:documentation>Tax - state hazardous substance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-StateTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - state tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-SuperFundExciseTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - super fund excise tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-UseTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - use tax</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax-ValueAddedTax">
                <xsd:annotation>
                    <xsd:documentation>Tax - value added tax (VAT)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TeaFee">
                <xsd:annotation>
                    <xsd:documentation>Tea fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TechnologyExchange">
                <xsd:annotation>
                    <xsd:documentation>Technology exchange</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TelegramChargeback">
                <xsd:annotation>
                    <xsd:documentation>Telegram chargeback</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TelephoneCharge">
                <xsd:annotation>
                    <xsd:documentation>Telephone charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Telephone-Destination">
                <xsd:annotation>
                    <xsd:documentation>Telephone - destination</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Telephone-Origin">
                <xsd:annotation>
                    <xsd:documentation>Telephone - origin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TemperatureProtection">
                <xsd:annotation>
                    <xsd:documentation>Temperature protection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TemporaryAllowance">
                <xsd:annotation>
                    <xsd:documentation>Temporary allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TemporaryVoluntaryAllowance">
                <xsd:annotation>
                    <xsd:documentation>Temporary voluntary allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TenderedAsTruckload">
                <xsd:annotation>
                    <xsd:documentation>Tendered as truckload</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalCharge">
                <xsd:annotation>
                    <xsd:documentation>Terminal charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalDifferential">
                <xsd:annotation>
                    <xsd:documentation>Terminal differential</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalHandlingCharges">
                <xsd:annotation>
                    <xsd:documentation>Terminal handling charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalServiceFee">
                <xsd:annotation>
                    <xsd:documentation>Terminal service fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TermsAllowance">
                <xsd:annotation>
                    <xsd:documentation>Terms allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Testing">
                <xsd:annotation>
                    <xsd:documentation>Testing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestingServicesCharge">
                <xsd:annotation>
                    <xsd:documentation>Testing services charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestQualificationCharge">
                <xsd:annotation>
                    <xsd:documentation>Test/qualification charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyAllowance">
                <xsd:annotation>
                    <xsd:documentation>Third party allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyPallets">
                <xsd:annotation>
                    <xsd:documentation>Third party pallets</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThroughputAllowance">
                <xsd:annotation>
                    <xsd:documentation>Throughput allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThroughputContainerCharge">
                <xsd:annotation>
                    <xsd:documentation>Throughput container charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThruwayCharge">
                <xsd:annotation>
                    <xsd:documentation>Thruway charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TicketingService">
                <xsd:annotation>
                    <xsd:documentation>Ticketing service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TobaccoProductsReportCharge">
                <xsd:annotation>
                    <xsd:documentation>Tobacco products report charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TOFCServiceCharge">
                <xsd:annotation>
                    <xsd:documentation>Tofc service charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolCharge">
                <xsd:annotation>
                    <xsd:documentation>Tool charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tooling">
                <xsd:annotation>
                    <xsd:documentation>Tooling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolingReworkCharge">
                <xsd:annotation>
                    <xsd:documentation>Tooling rework charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolsForPrinting">
                <xsd:annotation>
                    <xsd:documentation>Tools for printing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalOther">
                <xsd:annotation>
                    <xsd:documentation>The total for items other than those primarily reported upon in the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalAssessorialCharges">
                <xsd:annotation>
                    <xsd:documentation>Total assessorial charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalDueUSCustomsService">
                <xsd:annotation>
                    <xsd:documentation>Total due U.S. customs service (USCS)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalFees">
                <xsd:annotation>
                    <xsd:documentation>Total fees</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalInvoiceAmount">
                <xsd:annotation>
                    <xsd:documentation>Total invoice amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalInvoiceAmountNon-USDollars">
                <xsd:annotation>
                    <xsd:documentation>Total invoice amount, non-U.S. dollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalInvoiceAmountUSDollars">
                <xsd:annotation>
                    <xsd:documentation>Total invoice amount, U.S. dollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalMaterialInvoiceAmount">
                <xsd:annotation>
                    <xsd:documentation>Total material invoice amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TracingInboundViaOtherCarriers">
                <xsd:annotation>
                    <xsd:documentation>Tracing inbound via other carriers</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TracingServiceFee">
                <xsd:annotation>
                    <xsd:documentation>Tracing service fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrackStorage">
                <xsd:annotation>
                    <xsd:documentation>Track storage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeDiscount">
                <xsd:annotation>
                    <xsd:documentation>Trade discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeIn">
                <xsd:annotation>
                    <xsd:documentation>Trade in</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrailerRentalCharge">
                <xsd:annotation>
                    <xsd:documentation>Trailer rental charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferCharge">
                <xsd:annotation>
                    <xsd:documentation>Transfer charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferOfLadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Transfer of lading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferredCharges">
                <xsd:annotation>
                    <xsd:documentation>Transferred charges</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transit">
                <xsd:annotation>
                    <xsd:documentation>Transit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransmissionCharge">
                <xsd:annotation>
                    <xsd:documentation>Transmission charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportationAndSetup">
                <xsd:annotation>
                    <xsd:documentation>Transportation and setup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportationChargeMinimumRate">
                <xsd:annotation>
                    <xsd:documentation>Transportation charge (minimum rate)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportationDirectBilling">
                <xsd:annotation>
                    <xsd:documentation>Transportation direct billing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportationThirdPartyBilling">
                <xsd:annotation>
                    <xsd:documentation>Transportation third party billing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportationVendorProvided">
                <xsd:annotation>
                    <xsd:documentation>Transportation vendor provided</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrimmingCharge">
                <xsd:annotation>
                    <xsd:documentation>Trimming charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TruckDetention">
                <xsd:annotation>
                    <xsd:documentation>Truck detention</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TruckloadDiscount">
                <xsd:annotation>
                    <xsd:documentation>Truckload discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TurningCharge">
                <xsd:annotation>
                    <xsd:documentation>Turning charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Two-DayService">
                <xsd:annotation>
                    <xsd:documentation>Two - day service</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TwoDoorPickUp">
                <xsd:annotation>
                    <xsd:documentation>Two door pick up</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnabsorbedSwitching">
                <xsd:annotation>
                    <xsd:documentation>Unabsorbed switching</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Unitized">
                <xsd:annotation>
                    <xsd:documentation>Unitized</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Unloading">
                <xsd:annotation>
                    <xsd:documentation>Unloading</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnloadingLaborCharges">
                <xsd:annotation>
                    <xsd:documentation>Unloading (labor charges)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnloadingReloadingCharge">
                <xsd:annotation>
                    <xsd:documentation>Unloading/reloading charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnsaleableMerchandiseAllowance">
                <xsd:annotation>
                    <xsd:documentation>Unsaleable merchandise allowance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnscheduledFee">
                <xsd:annotation>
                    <xsd:documentation>Unscheduled fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UpCharge">
                <xsd:annotation>
                    <xsd:documentation>Up charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UsagePlanDetailCharge">
                <xsd:annotation>
                    <xsd:documentation>Usage plan detail charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="USCustomsServiceFlatAssistAmount">
                <xsd:annotation>
                    <xsd:documentation>U.S. customs service (USCS) flat assist amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="USCustomsServiceMaximumAssistAmount">
                <xsd:annotation>
                    <xsd:documentation>U.S. customs service (USCS) maximum assist amount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="USDAInspectedStampingCertification">
                <xsd:annotation>
                    <xsd:documentation>USDA inspected, stamping certification</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UseChargeToolingPersonnel">
                <xsd:annotation>
                    <xsd:documentation>Use charge tooling/personnel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Use-SpecialTypeFlatCar">
                <xsd:annotation>
                    <xsd:documentation>Use - special type flat car</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="USVehicles">
                <xsd:annotation>
                    <xsd:documentation>U.S. vehicles</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValuationFee">
                <xsd:annotation>
                    <xsd:documentation>Valuation fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VanCleaning">
                <xsd:annotation>
                    <xsd:documentation>Van cleaning</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VehicleOrderedButNotUsed">
                <xsd:annotation>
                    <xsd:documentation>Vehicle ordered but not used</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VehiclePrepChargeCourtesyDelivery">
                <xsd:annotation>
                    <xsd:documentation>Vehicle prep charge (courtesy delivery)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VehicleRoadCharge">
                <xsd:annotation>
                    <xsd:documentation>Vehicle road charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VendorFreight">
                <xsd:annotation>
                    <xsd:documentation>Vendor freight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VentingInstructions">
                <xsd:annotation>
                    <xsd:documentation>Venting instructions</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VirginIslandTransferCharge">
                <xsd:annotation>
                    <xsd:documentation>Virgin island transfer charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VolumeDiscount">
                <xsd:annotation>
                    <xsd:documentation>Volume discount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VoluntaryContributionCharge">
                <xsd:annotation>
                    <xsd:documentation>Voluntary contribution charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaitingTime">
                <xsd:annotation>
                    <xsd:documentation>Waiting time</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Warehouse">
                <xsd:annotation>
                    <xsd:documentation>Warehouse</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Warehousing">
                <xsd:annotation>
                    <xsd:documentation>Warehousing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Warranties">
                <xsd:annotation>
                    <xsd:documentation>Warranties</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarRiskCrewInsurance">
                <xsd:annotation>
                    <xsd:documentation>War risk crew insurance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarRiskInsurance">
                <xsd:annotation>
                    <xsd:documentation>War risk insurance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarRiskSurcharge">
                <xsd:annotation>
                    <xsd:documentation>War risk surcharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WastedFutileTrip">
                <xsd:annotation>
                    <xsd:documentation>Wasted/futile trip</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WatermelonFee">
                <xsd:annotation>
                    <xsd:documentation>Watermelon fee</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaybillAndInvoiceDistribution">
                <xsd:annotation>
                    <xsd:documentation>Waybill and invoice distribution</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeatherProtection">
                <xsd:annotation>
                    <xsd:documentation>Weather protection</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeightVerificationCharge">
                <xsd:annotation>
                    <xsd:documentation>Weight verification charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Wharfage-Breakbulk">
                <xsd:annotation>
                    <xsd:documentation>Wharfage - breakbulk</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WharfageCharge">
                <xsd:annotation>
                    <xsd:documentation>Wharfage charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Wharfage-Container">
                <xsd:annotation>
                    <xsd:documentation>Wharfage - container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WharfageHandling">
                <xsd:annotation>
                    <xsd:documentation>Wharfage and Handling</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WideAreaTelephoneServiceUsageCredit">
                <xsd:annotation>
                    <xsd:documentation>Wide area telephone service (WATS) usage credit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WillCallCharge">
                <xsd:annotation>
                    <xsd:documentation>Will call charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WrittenProofOfDelivery">
                <xsd:annotation>
                    <xsd:documentation>Written proof of delivery</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="X-rayCharge">
                <xsd:annotation>
                    <xsd:documentation>X-ray charge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
