<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="PartyRoleCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the party role of the person relating to the package. This code list is derived from EDIFACT 3035 (party function code qualifier) and X12 98 (entity identifier code).</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcceptanceLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcceptingParty">
                <xsd:annotation>
                    <xsd:documentation>(3352) Party accepting goods, products, services etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Accountant">
                <xsd:annotation>
                    <xsd:documentation>Identifies the accountant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountCoordinator">
                <xsd:annotation>
                    <xsd:documentation>An individual with coordination responsibilities for a specific account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingStation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOf">
                <xsd:annotation>
                    <xsd:documentation>Party account is assigned to.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOfDestinationParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOf-OriginParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOwner">
                <xsd:annotation>
                    <xsd:documentation>Identifies the owner of the account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOwnersServicingBankOnTheReceivingSide">
                <xsd:annotation>
                    <xsd:documentation>Identifies the financial institution on the receiving side which services the account owner's bank account(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOwnersServicingBankOnTheSendingSide">
                <xsd:annotation>
                    <xsd:documentation>Identifies the financial institution on the sending side which services the account owner's bank account(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountServicingFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Identifies the financial institution servicing the account(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountsPayableOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcknowledgementRecipient">
                <xsd:annotation>
                    <xsd:documentation>Party to whom acknowledgement should be sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Acronym">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActionParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualPurchasersCustomer">
                <xsd:annotation>
                    <xsd:documentation>Party the purchaser within the actual message is selling the ordered goods or services to.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualReferredByOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualReferredToOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcuteCareHospital">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalDebtor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalDeliveryAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdditionalPickUpAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Adjuster">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdministrationCentre">
                <xsd:annotation>
                    <xsd:documentation>Identification of an administration centre.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Administrator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdmittingHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation>Health care provider that admitted the patient.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdmittingServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdmittingSurgeon">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdultDayCareProgramFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Advertiser">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvertisingGroup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviseThroughBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the financial institution through which the advising bank is to advise.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvisingBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the financial institution used by the issuing bank to advise the documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Advisor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the advisor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Affiant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Affiliate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AffiliatedCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the affiliated company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AffiliatedParty">
                <xsd:annotation>
                    <xsd:documentation>A party attached or connected to another party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Agency">
                <xsd:annotation>
                    <xsd:documentation>The business or establishment of an agent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgencyGrantingSecurityClearance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgentOrRepresentative">
                <xsd:annotation>
                    <xsd:documentation>(3196) Party authorized to act on behalf of another party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgentsBank">
                <xsd:annotation>
                    <xsd:documentation>Bank of the agent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AIDSOrARCUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirAmbulanceServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirCargoCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirForceFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Airline">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirportAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholBeverageDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholismAndOtherChemicalDependencyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholOrDrugAbuseOrDependencyInpatientUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlcoholOrDrugAbuseOrDependencyOutpatientServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Alias">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlienAffiliate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllegedDebtor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Alliance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlliedHealthProfessional">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllotmentCustomer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowableRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlternateReturnAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlternativeAddressee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlternativeManufacturer">
                <xsd:annotation>
                    <xsd:documentation>Party identification of an alternative manufacturer for a product.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlzheimersDiagnosticOrAssessmentServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AmendedName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AmericanIndian-OwnedBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AmicusCuriae">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnesthesiologyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AngioplastyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnimalBeingInvestigated">
                <xsd:annotation>
                    <xsd:documentation>Animal being investigated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnimalSource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Annuitant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnuitantPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AntennaSite">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Appellant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Appellee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Applicant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantComaker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantForJob">
                <xsd:annotation>
                    <xsd:documentation>A person who applied for a job.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantsBank">
                <xsd:annotation>
                    <xsd:documentation>Financial institution which is requested to issue the documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantsCosigner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantsEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantsReference">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppraisalCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppraiserName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApprovedConsignor">
                <xsd:annotation>
                    <xsd:documentation>Person or company approved by the relevant authority in the country to pack and export specific goods under Customs supervision.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApprovedImporter">
                <xsd:annotation>
                    <xsd:documentation>Person or company which is authorised by the relevant Customs authority to import goods without payment all taxes or specific taxes at the point of entry into the country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Approver">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Arbitrator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Architect">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Architect-Designer">
                <xsd:annotation>
                    <xsd:documentation>Designer of the construction project.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AreaOfOperation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArmedServicesLocationDesignation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArmyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrestingOfficer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArthritisTreatmentCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssetAccountHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Assignee">
                <xsd:annotation>
                    <xsd:documentation>Identifies the assignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Assigner">
                <xsd:annotation>
                    <xsd:documentation>Identifies the entity who assigns.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Assignor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the assignor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssistantSurgeon">
                <xsd:annotation>
                    <xsd:documentation>Physician assisting in surgery.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssistantUSTrustee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssociateGeneralAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Association">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttendingPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Attorney">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is an attorney.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttorneyForDefendantPrivate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttorneyForDefendantPublic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttorneyForPlaintiff">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttorneyOfRecord">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Auctioneer">
                <xsd:annotation>
                    <xsd:documentation>Identifies the auctioneer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuditOffice">
                <xsd:annotation>
                    <xsd:documentation>The office responsible for conducting audits.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Auditor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthenticatingParty">
                <xsd:annotation>
                    <xsd:documentation>Party which certifies that a document is authentic.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Author">
                <xsd:annotation>
                    <xsd:documentation>Identifies the author.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorityForShipment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizationAssignedBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedAcceptingOfficial">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedExporter">
                <xsd:annotation>
                    <xsd:documentation>Exporter authorized/approved by Customs for special Customs procedures e.g. simplified procedure.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedImporter">
                <xsd:annotation>
                    <xsd:documentation>Importer authorized/approved by Customs for special Customs procedures e.g. simplified procedure.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedMarketer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedOfficial">
                <xsd:annotation>
                    <xsd:documentation>Employee of a company or firm authorized to act on behalf of that company or firm e.g. to make a Customs declaration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizedTrader">
                <xsd:annotation>
                    <xsd:documentation>Trader authorized/approved by Customs for special transit procedures e.g. simplified procedure.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizingOfficial">
                <xsd:annotation>
                    <xsd:documentation>Party that has delegated the authority to take a certain action on behalf of a company or agency.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AutomatedDataProcessingPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AutomatedUnderwritingSystem">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AvailableWithBank">
                <xsd:annotation>
                    <xsd:documentation>Financial institution with whom the documentary credit is available.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AvailabilityShipToParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bailiff">
                <xsd:annotation>
                    <xsd:documentation>Identifies the bailiff.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BailmentWarehouse">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BailPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BakeryDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bank">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Banker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankruptcyReferee">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is a referee in a bankruptcy case.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankruptcyTrustee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BaseJurisdiction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BasePeriodEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Battery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeneficialOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Beneficiary">
                <xsd:annotation>
                    <xsd:documentation>The ultimate recipient of the funds. Normally the account owner who is reimbursed by the payor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeneficiarysBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the account servicer for the beneficiary or the payee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bidder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BidOpeningLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillAndShipTo">
                <xsd:annotation>
                    <xsd:documentation>Party receiving goods and relevant invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BilledFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillingProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillingService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLadingRecipient">
                <xsd:annotation>
                    <xsd:documentation>Party to receive B/L.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bill-ToPartyForDiversionCharges">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BirthingRoomOrLDRPRoom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BirthName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Birthplace">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BloodBank">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BoardOfRealtors">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BobtailServicePoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bondsman">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookingAgent">
                <xsd:annotation>
                    <xsd:documentation>Party acting as a booking office for transport and forwarding services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookingOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bookkeeper">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BorderCrossing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Borrower">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BorrowersEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bottler">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Brakeman">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Branch">
                <xsd:annotation>
                    <xsd:documentation>Identifies the branch.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrandName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BreakBulkBerthOperator">
                <xsd:annotation>
                    <xsd:documentation>Party who offers facilities for berthing of vessels, handling and storage of break bulk cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BreakBulkPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Broadcaster">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Broker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokerDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokerOpinionOrAnalysisRequester">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokerOrSalesOffice">
                <xsd:annotation>
                    <xsd:documentation>Party acting in the name of the seller as broker or as sales office.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BrokersAssistant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuilderName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuildingInspectorate">
                <xsd:annotation>
                    <xsd:documentation>Party controlling the conformity of works to legal and regulation rules.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuildingSiteEngineer">
                <xsd:annotation>
                    <xsd:documentation>Party at the building site responsible for engineering matters for that particular site.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuildingSiteForwarder">
                <xsd:annotation>
                    <xsd:documentation>Party at the building site responsible for forwarding the received goods on that particular site.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuildingSitePurchaser">
                <xsd:annotation>
                    <xsd:documentation>Party at the building site responsible for the purchasing of goods and services for that particular site.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BureauOfLandManagementPropertyUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BurnCareUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Business">
                <xsd:annotation>
                    <xsd:documentation>Identifies the business.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessArea">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessAssociate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessPartner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Buyer">
                <xsd:annotation>
                    <xsd:documentation>Party to whom merchandise and/or service is sold.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyerAsOfficiallyRegistered">
                <xsd:annotation>
                    <xsd:documentation>Buying party as officially registered with government.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersAgentOrRepresentative">
                <xsd:annotation>
                    <xsd:documentation>Third party who arranged the purchase of merchandise on behalf of the actual buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersBank">
                <xsd:annotation>
                    <xsd:documentation>[3420] Bank employed by the buyer to make payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersCorporateOffice">
                <xsd:annotation>
                    <xsd:documentation>The identification of the buyer's corporate office.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Financial institution designated by buyer to make payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersParentCompany">
                <xsd:annotation>
                    <xsd:documentation>Parent company, e.g. holding company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersQualityReviewBoard">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyingGroup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyingParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CanadianCustomsBroker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Candidate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CandyAndConfectionsDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CardiacCatherizationLaboratory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CardiacIntensiveCareUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CardiacRehabilitationProgramFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CardiacRehabilitationServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarnetIssuer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Carrier">
                <xsd:annotation>
                    <xsd:documentation>(3126) Party undertaking or arranging transport of goods between named points.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CarriersAgent">
                <xsd:annotation>
                    <xsd:documentation>Party authorized to act for or on behalf of carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CaseManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CaseOfNeedParty">
                <xsd:annotation>
                    <xsd:documentation>Party to be approached in case of difficulty.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CashPoolLevelAccountServicingFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Identification of a financial institution servicing the level account of a cash pool.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CashPoolSub-AccountServicingFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Identification of a financial institution servicing the sub-account of a cash pool.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CashPoolTopAccountServicingFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Identification of a financial institution servicing the top account of a cash pool.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CedingCompany">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CentralBankOrRegulatoryAuthority">
                <xsd:annotation>
                    <xsd:documentation>Identifies central bank or regulatory authority which must be informed of certain aspects of a message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CentralCatalogueParty">
                <xsd:annotation>
                    <xsd:documentation>Party controlling a central catalogue.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertifiedAccountant">
                <xsd:annotation>
                    <xsd:documentation>Code identifying the party as a certified accountant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertifyingParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Chairperson">
                <xsd:annotation>
                    <xsd:documentation>Identifies the chairperson.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChamberOfCommerce">
                <xsd:annotation>
                    <xsd:documentation>Name of the Chamber of Commerce of the town where the company is registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChangedAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargesPayerAtDestination">
                <xsd:annotation>
                    <xsd:documentation>Party, other than the ordering party, which has to pay the charges concerning the destination operations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Charity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CharterSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChassisProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CheckingParty">
                <xsd:annotation>
                    <xsd:documentation>Party or contact designated on behalf of carrier or his agent to establish the actual figures for quantities, weight, volume and/or (cube) measurements of goods or containers which are to appear in the transport contract and on which charges will be based.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChequeDrawnBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the bank on which the cheque should be drawn, as instructed by the ordering customer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChequeOrder">
                <xsd:annotation>
                    <xsd:documentation>Party to which the cheque will be ordered, when different from the beneficiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChiefDeputyClerkOfCourt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Child">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensChronicDiseaseFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensDayCareCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensEyeEarNoseAndThroatFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensGeneralFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensHospitalUnitOfAnInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensOrthopedicFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensOtherSpecialtyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensPsychiatricFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensRehabilitationFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildrensTuberculosisAndOtherRespiratoryDiseasesFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Chiropractor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChronicDiseaseFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChronicObstructivePulmonaryDiseaseServiceFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChurchOperatedFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CigarettesAndTobaccoDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CircuitLocationIdentifier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="City">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimAdministrator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Claimant">
                <xsd:annotation>
                    <xsd:documentation>Party who claims goods or insurance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimantAuthorizedRepresentative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClearingHouse">
                <xsd:annotation>
                    <xsd:documentation>Institution through which funds will be paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Clerk">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClerkOfCourt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Clinic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosedDoorPharmacy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosedSale">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoatingOrPaintSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Borrower">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Contractor">
                <xsd:annotation>
                    <xsd:documentation>Member of a grouping of co-contractors.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Counsel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Debtor">
                <xsd:annotation>
                    <xsd:documentation>Identifies an entity as a joint or mutual debtor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Defendant">
                <xsd:annotation>
                    <xsd:documentation>Identifies the co-defendant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Driver">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Investigator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollateralAssignee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectingBank">
                <xsd:annotation>
                    <xsd:documentation>Any bank, other than the remitting bank, involved in processing the collection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectionPrincipal">
                <xsd:annotation>
                    <xsd:documentation>The party entrusting the handling of a collection to a bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialInsurer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommissionableAgent">
                <xsd:annotation>
                    <xsd:documentation>IATA cargo agent entitled to commission.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommissionProcessor">
                <xsd:annotation>
                    <xsd:documentation>Party who provides extra treatment to goods on commission base.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommunityAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommunityHealthPromotionFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyAssignedWell">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyInterviewerWorksFor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyInWhichInterestHeld">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyMergedWith">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Company-OwnedOilField">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyWhichHoldsFinancialInterest">
                <xsd:annotation>
                    <xsd:documentation>Identifies a company which holds any financial stake in an undertaking or organization.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComparableProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComparablePropertyListing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComparableRentals">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompetingProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompetitivePropertyListing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompilationAgency">
                <xsd:annotation>
                    <xsd:documentation>The agency responsible for the compilation of information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Complainant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComplainantsAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComponentManufacturer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComprehensiveGeriatricAssessmentFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConcurrentEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Conductor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConductorPilot">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Conduit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConnectingCarrier">
                <xsd:annotation>
                    <xsd:documentation>Owner or operator of a transportation conveyance to which goods in a given transaction will be transferred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Connection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consignee">
                <xsd:annotation>
                    <xsd:documentation>(3132) Party to which goods are consigned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsigneeCourierTransferStation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsigneeOfVessel">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsigneesAgent">
                <xsd:annotation>
                    <xsd:documentation>Party authorized to act on behalf of the consignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsigneeToBeSpecified">
                <xsd:annotation>
                    <xsd:documentation>The party to be identified at a later time as the consignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consignee-ToReceiveLargeParcelsAndFreight">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consignee-ToReceiveMailAndSmallParcels">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consignor">
                <xsd:annotation>
                    <xsd:documentation>(3336) Party which, by contract with a carrier, consigns or sends goods with the carrier, or has them conveyed by him. Synonym: shipper, sender.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsignorCourierTransferStation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsignorsRepresentative">
                <xsd:annotation>
                    <xsd:documentation>Party authorised to represent the consignor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedDebtor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consolidator">
                <xsd:annotation>
                    <xsd:documentation>Party consolidating various consignments, payments etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstructionContractor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstructionFirm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsultantsOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsultingPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumerCreditAccountCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumerReferralContact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumerServiceProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsumerServiceProviderCustomer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactBank1">
                <xsd:annotation>
                    <xsd:documentation>Identifies an additional bank which must be informed of certain aspects of the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactBank2">
                <xsd:annotation>
                    <xsd:documentation>Identifies an additional bank which must be informed of certain aspects of the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactParty">
                <xsd:annotation>
                    <xsd:documentation>Party to contact.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactParty1">
                <xsd:annotation>
                    <xsd:documentation>First party to contact.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactParty2">
                <xsd:annotation>
                    <xsd:documentation>Second party to contact.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactParty3">
                <xsd:annotation>
                    <xsd:documentation>Third party to contact.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactParty4">
                <xsd:annotation>
                    <xsd:documentation>Fourth party to contact.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContactPerson">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerLocationParty">
                <xsd:annotation>
                    <xsd:documentation>Party from whose premises container will be or has been despatched.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerOperatorOrLessee">
                <xsd:annotation>
                    <xsd:documentation>Party to whom the possession of specified property (e.g. container) has been conveyed for a period of time in return for rental payments.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerReturnCompany">
                <xsd:annotation>
                    <xsd:documentation>The company to which containers have to be returned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerSurveyAgent">
                <xsd:annotation>
                    <xsd:documentation>The container survey agency that will survey the containers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContingentAnnuitant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContingentBeneficiary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContingentOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractAdministrationOffice">
                <xsd:annotation>
                    <xsd:documentation>The office responsible for the administration of a contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractedServiceProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractingOfficerRepresentative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contractor-General">
                <xsd:annotation>
                    <xsd:documentation>Single contractor for the whole construction project, working by his own or with subcontractors.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractOrCognizantSecurityOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contractor-Lead">
                <xsd:annotation>
                    <xsd:documentation>Leader representing a grouping of co-contractors.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contractor-Main">
                <xsd:annotation>
                    <xsd:documentation>Firm or grouping of co-contractors which has been awarded the contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractualDeliveryMeter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractualReceiptMeter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControllerQuality">
                <xsd:annotation>
                    <xsd:documentation>Party controlling the quality of goods and workmanship for the project.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Converter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConvoyingParty">
                <xsd:annotation>
                    <xsd:documentation>Party designated to escort the transported goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Occupant">
                <xsd:annotation>
                    <xsd:documentation>The party is a co-occupant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Operative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CooperativeBuyingGroup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Coordinator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Owner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Participant">
                <xsd:annotation>
                    <xsd:documentation>Identifies another party who participates in an activity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Co-Producer">
                <xsd:annotation>
                    <xsd:documentation>A code used to identify a party who participates in production.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CopyMessageTo">
                <xsd:annotation>
                    <xsd:documentation>Party that is to receive a copy of a message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorporateOffice">
                <xsd:annotation>
                    <xsd:documentation>Identification of the Head Office within a company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Corporation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorporationFor-ProfitFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorrectedAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorrectedInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorrectedName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Cosigner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CosignersEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CouncilOfGovernments">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CounterClaimant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Country">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CountryOfDestination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CountryOfExport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CountryOfOrigin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="County">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Court">
                <xsd:annotation>
                    <xsd:documentation>Identifies the court.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CourtAppointedAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditGrantor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Creditor">
                <xsd:annotation>
                    <xsd:documentation>Party to whom payment is due.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditorsAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditRatingAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditReportingAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrewMember">
                <xsd:annotation>
                    <xsd:documentation>A person manning a means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrimeVictim">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrossClaimant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Cross-TownSwitch">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CTScannerUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentEmployer-Primary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentEmployer-Secondary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentReceiver">
                <xsd:annotation>
                    <xsd:documentation>Current receiver of the goods in a multi-step transportation process (indirect flow) involving at least one grouping centre.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentSender">
                <xsd:annotation>
                    <xsd:documentation>Current sender of the goods in a multi-step transportation process (indirect flow) involving at least one grouping centre.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustodialParent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Custodian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Customer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerFOrWhomSameOrSimilarWOrkWasPerFormed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerIdentificationFileCustomerIdentifier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Customs">
                <xsd:annotation>
                    <xsd:documentation>Identification of customs authority relevant to the transaction or shipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsBroker">
                <xsd:annotation>
                    <xsd:documentation>Agent or representative or a professional Customs clearing agent who deals directly with Customs on behalf of the importer or exporter (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsOfficeOfClearance">
                <xsd:annotation>
                    <xsd:documentation>Identifies the office where customs clearance procedures take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DairyDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DataProcessingServiceBureau">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Debtor">
                <xsd:annotation>
                    <xsd:documentation>Party from whom payment is due.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebtorCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebtorIndividual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Debtor-In-Possession">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebtorsAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebtPaymentAuthorisationParty">
                <xsd:annotation>
                    <xsd:documentation>A party which authorises the payment of a debt.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DecisionMaker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Declarant">
                <xsd:annotation>
                    <xsd:documentation>(3140) Party who makes a declaration to an official body or - where legally permitted - in whose name, or on whose behalf, a declaration to an official body is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclarantsAgentOrRepresentative">
                <xsd:annotation>
                    <xsd:documentation>Any natural or legal person who makes a declaration to an official body on behalf of another natural or legal person, where legally permitted (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Deconsolidator">
                <xsd:annotation>
                    <xsd:documentation>Party that splits up a large consignment composed of separate consignments of goods. The smaller consignments of goods were grouped together into that large consignment for carriage as a larger unit in order to obtain a reduced rate.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DefaultOrForeclosureSpecialist">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Defendant">
                <xsd:annotation>
                    <xsd:documentation>Identifies the defendant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DelicatessenDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryParty">
                <xsd:annotation>
                    <xsd:documentation>(3144) Party to which goods should be delivered, if not identical with consignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryZone">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dentist">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartmentOfJusticeFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dependent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DependentInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DependentName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Depositor">
                <xsd:annotation>
                    <xsd:documentation>Party depositing goods, financial payments or documents.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DesignatedHazardousWasteFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DesignEngineering">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Designer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchChargePayer">
                <xsd:annotation>
                    <xsd:documentation>Party, other than the ordering party, which has to pay the charges concerning the despatch operations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchParty">
                <xsd:annotation>
                    <xsd:documentation>(3282) Party where goods are collected or taken over by the carrier (i.e. if other than consignor).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DestinationCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DestinationDrayman">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DestinationIntermodalRamp">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DestinationMailFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DestinationTerminal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DiagnosticRadioisotopeFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DialysisCenters">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DifferentPremiseAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Director">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisabledIndividual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisbursingOfficer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DischargePoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisciplinaryContact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Dispatcher">
                <xsd:annotation>
                    <xsd:documentation>An individual responsible for sending something to a destination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Distiller">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributionRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Distributor">
                <xsd:annotation>
                    <xsd:documentation>Party distributing goods, financial payments or documents.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributorBranch">
                <xsd:annotation>
                    <xsd:documentation>The affiliate of a retailer or distributor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="District">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistrictAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistrictJustice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Division">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoctorOfOptometry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoctorOfOsteopathy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAccountParty">
                <xsd:annotation>
                    <xsd:documentation>Party which is responsible for the payment settlement of the documentary credit with the applicant's bank/issuing bank, if different from the documentary credit applicant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAccountPartysBank">
                <xsd:annotation>
                    <xsd:documentation>Bank of the documentary credit account party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditApplicant">
                <xsd:annotation>
                    <xsd:documentation>Party at whose request the applicant's bank/issuing bank is to issue a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditBeneficiary">
                <xsd:annotation>
                    <xsd:documentation>Party in whose favour the documentary credit is to be issued and the party that must comply with the credit's terms and conditions.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditBeneficiarysBank">
                <xsd:annotation>
                    <xsd:documentation>Financial institution with which the beneficiary of the documentary credit maintains an account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditReimbursingBank">
                <xsd:annotation>
                    <xsd:documentation>Self-explanatory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditSecondBeneficiary">
                <xsd:annotation>
                    <xsd:documentation>Party to whom the documentary credit can be transferred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentCustodian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentOrMessageIssuerOrSender">
                <xsd:annotation>
                    <xsd:documentation>Issuer of a document and/or sender of a message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentRecipient">
                <xsd:annotation>
                    <xsd:documentation>(1370) Party which should receive a specified document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoingBusinessAs">
                <xsd:annotation>
                    <xsd:documentation>The name under which business is conducted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DomesticFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Domestic party acting as financial institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DownstreamFirstSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DownstreamMeterLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DownstreamParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Drawee">
                <xsd:annotation>
                    <xsd:documentation>Party on whom drafts must be drawn.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrillingContractor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Driver">
                <xsd:annotation>
                    <xsd:documentation>Person who drives a means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Drop-OffLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DryGroceryDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DurableMedicalEquipmentSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EconomicallyDisadvantagedIndividual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EducationalInstitution">
                <xsd:annotation>
                    <xsd:documentation>Identifies a university, college or school.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElectronicDataInterchangeCoordinatorPointAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElectronicFiler">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElectronicRegistrationUtility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElectronicReturnOriginator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ElementarySchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EligiblePartyToTheContract">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EligiblePartyToTheRate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyDepartmentSocialWorkServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyMedicalTechnician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergencyResponseUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmergingSmallBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Employee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployeeName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Employer">
                <xsd:annotation>
                    <xsd:documentation>Self-explanatory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyContainerResponsibleParty">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for the empty container.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyContainerReturnAgent">
                <xsd:annotation>
                    <xsd:documentation>Party, designated by owner of containers, responsible for their collection as agreed between the owner and customer/ consignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyEquipmentDespatchParty">
                <xsd:annotation>
                    <xsd:documentation>Party from whose premises empty equipment will be or has been despatched.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmptyEquipmentReturnParty">
                <xsd:annotation>
                    <xsd:documentation>Party to whose premises empty equipment will be or has been returned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndItemManufacturer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndUser">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EnergyInformationAdministration-OwnedOilField">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engineer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engineer-Construction">
                <xsd:annotation>
                    <xsd:documentation>Party representing the contractor to advise and supervise engineering aspects of the works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engineer-Consultant">
                <xsd:annotation>
                    <xsd:documentation>Party providing professional engineering services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engineering">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EngineerMeasurement">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for quantity measurements.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EngineerPilot">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Engineer-ResidentConstruction">
                <xsd:annotation>
                    <xsd:documentation>Party commissioned by the owner to advise and supervise engineering aspects of the works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Enroller">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntityHoldingTheInformation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntityInWhichAFinancialInterestIsHeld">
                <xsd:annotation>
                    <xsd:documentation>Business in which a financial interest is held.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntityProvidingTheService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntityResponsibleForFollow-Up">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntryPointTechnicalAssessmentGroup">
                <xsd:annotation>
                    <xsd:documentation>Party acts as an entry point for technical assessment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentDrop-OffParty">
                <xsd:annotation>
                    <xsd:documentation>The party which drops off equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentOwner">
                <xsd:annotation>
                    <xsd:documentation>Owner of equipment (container, etc.).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentRepairParty">
                <xsd:annotation>
                    <xsd:documentation>A party making repairs to equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstablishedLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Estimator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EventLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Examiner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Exchanger">
                <xsd:annotation>
                    <xsd:documentation>Party exchanging currencies or goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExciseParty">
                <xsd:annotation>
                    <xsd:documentation>Party to whom excise must be paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExcludedParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExecutiveCommitteeMember">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExecutorOfEstate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Exhibitor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpertWitness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Exporter">
                <xsd:annotation>
                    <xsd:documentation>(3030) Party who makes - or on whose behalf a Customs clearing agent or other authorized person makes - an export declaration. This may include a manufacturer, seller or other person. Within a Customs union, consignor may have the same meaning as exporter.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Ex-Spouse">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtendedCareFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExtracorporealShock-WaveLithotripterUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EyeEarNoseAndThroatFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Facility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Factor">
                <xsd:annotation>
                    <xsd:documentation>Company offering a financial service whereby a firm sells or transfers title to its accounts receivable to the factoring company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Factor-Correspondent">
                <xsd:annotation>
                    <xsd:documentation>Factoring company engaged by another factoring company to assist the letter with the services provided to the clients (sellers).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FamilyMember">
                <xsd:annotation>
                    <xsd:documentation>Identifies a family member.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Father">
                <xsd:annotation>
                    <xsd:documentation>Person is a father.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederalFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederalGovernment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederallyCharteredFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederalStateCountyOrCityFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FellowshipInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Felon">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Field">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FieldorBranchOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FilerName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FilingAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FilingLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FilingOffice">
                <xsd:annotation>
                    <xsd:documentation>Identifies the filing office.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalCustomerOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalMaintenanceOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalMaintenancePeriodDesignatingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalMessageRecipient">
                <xsd:annotation>
                    <xsd:documentation>To identify the final recipient of the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalReferredToOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalScheduledDestination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Party acting as financial institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialPlanner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialStatementRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Fireman">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstAppraiser">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstBreakTerminal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstContact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FitnessCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FloodCertifier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FloodDeterminationProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FloralDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FoodServiceOperator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeclosingLender">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignDisclosureInformationOffice">
                <xsd:annotation>
                    <xsd:documentation>The office that reviews sensitive information for foreign disclosure.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignLanguageSynonym">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignRegistrationLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForeignSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Formation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FormerAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FormerlyDoingBusinessAs">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FormerlyKnownAs">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FormerPresident">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FormerRegisteredAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FormerResidence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForProfitHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Forwarder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Foundation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Founder">
                <xsd:annotation>
                    <xsd:documentation>Identifies the founder.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Franchisee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Franchisor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreeonBoardPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightForwarder">
                <xsd:annotation>
                    <xsd:documentation>Party arranging forwarding of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightManagementFacilitator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightOrChargesPayer">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for the payment of freight.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightPayerOnBehalfOfTheConsignee">
                <xsd:annotation>
                    <xsd:documentation>Freight payer is a third party acting on behalf of the consignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightPayerOnBehalfOfTheConsignor">
                <xsd:annotation>
                    <xsd:documentation>Freight payer is a third party acting on behalf of the consignor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FrozenDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FutureAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GainingInventoryManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GaragedLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Garnishee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GasPlant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GasTransactionEndingPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GasTransactionPoint1">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GasTransactionPoint2">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GasTransactionStartingPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GatewayProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralInpatientCareForAIDSOrARCFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralMedicalAndSurgicalFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneralMerchandiseDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeneticCounselingOrScreeningServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeriatricAcuteCareUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeriatricClinics">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GiftRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsCollectionParty">
                <xsd:annotation>
                    <xsd:documentation>Party that will collect or has collected the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsCustodian">
                <xsd:annotation>
                    <xsd:documentation>(3024) Party responsible for the keeping of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsOwner">
                <xsd:annotation>
                    <xsd:documentation>The party which owns the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsReleaser">
                <xsd:annotation>
                    <xsd:documentation>(3026) Party entitled to authorize release of goods from custodian.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Government">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Government-CombinedControl">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Government-Federal-Military">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Government-Federal-Other">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Government-Federal-Veterans">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GovernmentFurnishedPropertyFOBPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GovernmentLoanAgencySponsorOrAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Government-Local">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Grandparent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Grantor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroundAmbulanceServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Group">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupAffiliate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupAffiliation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupingCentre">
                <xsd:annotation>
                    <xsd:documentation>A party in charge of groupage, including degroupage and regroupage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupPractice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupPurchasingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GuaranteeAgency">
                <xsd:annotation>
                    <xsd:documentation>Identifies the guarantee agency.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Guardian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousMaterialOffice">
                <xsd:annotation>
                    <xsd:documentation>The office responsible for providing information regarding hazardous material.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HazardousWasteGenerator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HeadOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HealthAndBeautyDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HealthcareProfessionalShortageAreaFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HealthMaintenanceOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HealthMiscellaneous">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HealthSciencesLibrary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HemodialysisFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HighSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HisopathologyLaboratory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HispanicServiceInstitute">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HistoricallyBlackCollegeOrUniversity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeHealthAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeHealthCare">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeHealthCorporation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeHealthServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HomeStateName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Hospice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Hospital">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HospitalAuxiliaryUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HospitalDistrictOrAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HospitalUnitOfAnInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HospitalUnitWithinAnInstitutionForTheMentallyRetarded">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HostileWitness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImportedFromLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Importer">
                <xsd:annotation>
                    <xsd:documentation>(3020) Party who makes - or on whose behalf a Customs clearing agent or other authorized person makes - an import declaration. This may include a person who has possession of the goods or to whom the goods are consigned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InCareOfPartyNo1">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InCareOfPartyNo2">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporatedBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporatedLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporationJurisdiction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporationStatePlaceOfBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporationStatePrincipalOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndependentAdjuster">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndependentLab">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndependentPhysiciansAssociation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Individual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndividualFor-ProfitFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndustryAssociation">
                <xsd:annotation>
                    <xsd:documentation>Identifies the industry association.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndustryBureau">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IneligibleParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InformationDisseminationAgency">
                <xsd:annotation>
                    <xsd:documentation>The agency responsible for the dissemination of information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InformationMaintenanceAgency">
                <xsd:annotation>
                    <xsd:documentation>The agency responsible for the maintenance of information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InformationProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InformationReferenceAgency">
                <xsd:annotation>
                    <xsd:documentation>The agency responsible for the reference of information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InformationSource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialMedicalProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialSubscriber">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Initiating">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InjectionPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="In-PatientPharmacy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InquiringParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InquiryAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="In-ServiceSource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionAddress">
                <xsd:annotation>
                    <xsd:documentation>Specifies the address for an inspection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionAndAcceptanceLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionAndAcceptanceParty">
                <xsd:annotation>
                    <xsd:documentation>The party who will perform inspection and acceptance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionLeader">
                <xsd:annotation>
                    <xsd:documentation>An individual responsible for an inspection team.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Inspector">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstallationOnSite">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstalledAt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstitutionalProvider">
                <xsd:annotation>
                    <xsd:documentation>The institution providing the service.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstitutionForMentalRetardation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="In-StoreBakeryDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceBeneficiary">
                <xsd:annotation>
                    <xsd:documentation>Party which benefits from insurance coverage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceClaimAdjuster">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceCompany">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceInstitute">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Insured">
                <xsd:annotation>
                    <xsd:documentation>Party which is the object of an insurance contract.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuredLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuredOrSubscriber">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Insurer">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntegratedMaterialManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntellectualPropertyOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestedParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterimFundingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterimTrustee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterlineServiceCommitmentCustomer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Intermediary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntermediaryBank1">
                <xsd:annotation>
                    <xsd:documentation>A financial institution between the ordered bank and the beneficiary's bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntermediaryBank2">
                <xsd:annotation>
                    <xsd:documentation>A financial institution between the ordered bank and the beneficiary's bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntermediaryOrBroker">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntermediateCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntermediateConsignee">
                <xsd:annotation>
                    <xsd:documentation>The intermediate consignee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IntermediateLevelParentCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies an intermediate parent company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalFreightForwarder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternshipEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Interpreter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Interviewee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Interviewer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Inventor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryController">
                <xsd:annotation>
                    <xsd:documentation>To specifically identify the party in charge of inventory control.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryControlPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryReportingParty">
                <xsd:annotation>
                    <xsd:documentation>Party reporting inventory information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Investigator">
                <xsd:annotation>
                    <xsd:documentation>A party who conducts investigations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvestmentAdvisor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Investor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Invoicee">
                <xsd:annotation>
                    <xsd:documentation>(3006) Party to whom an invoice is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoicingDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvolvedParty">
                <xsd:annotation>
                    <xsd:documentation>Party which is involved in an activity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IrrevocableBeneficiary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Issuer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IssuerOfDebitOrCreditMemo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IssuerOfInvoice">
                <xsd:annotation>
                    <xsd:documentation>(3028) Party issuing an invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IssuerOfWaybill">
                <xsd:annotation>
                    <xsd:documentation>Party issuing the contract (waybill) for carriage.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IssuingBank">
                <xsd:annotation>
                    <xsd:documentation>Financial institution which issues the documentary credit, if the applicant's bank is not acting as the issuing bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemShipTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemShipFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointAnnuitant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointAnnuitantOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointDebtor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointDebtorAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointOwner">
                <xsd:annotation>
                    <xsd:documentation>Identifies the joint owner.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointOwnerAnnuitant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointOwnerAnnuitantPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointOwnerJointAnnuitant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointOwnerJointAnnuitantPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointOwnerPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JointVenture">
                <xsd:annotation>
                    <xsd:documentation>Identifies the joint venture.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Judge">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is a judge.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JuniorHighSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Jurisdiction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JuvenileDefendant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JuvenileOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JuvenileVictim">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JuvenileWitness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KeyPerson">
                <xsd:annotation>
                    <xsd:documentation>Identifies the key person.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KidneyDialysisUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Labeler">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Laboratory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LaborSurplusAreaFirm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Landlord">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LargeBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastBreakTerminal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastKnownSourceOfSupply">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LawEnforcementAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LawFirm">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is a law firm.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalReceiver">
                <xsd:annotation>
                    <xsd:documentation>The party responsible for a receivership.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalRepresentative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalTitleHolder">
                <xsd:annotation>
                    <xsd:documentation>Identifies the legal title holder.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Lender">
                <xsd:annotation>
                    <xsd:documentation>Party lending goods or equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LendingInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Lessee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LesseesSurveyor">
                <xsd:annotation>
                    <xsd:documentation>Surveyor hired by the lessee of the item.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Lessor">
                <xsd:annotation>
                    <xsd:documentation>The party is a lessor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiabilityHolder">
                <xsd:annotation>
                    <xsd:documentation>Identifies the liability holder.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Licensee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LienHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LimitedLiabilityCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LimitedLiabilityPartnership">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LimitedPartner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Liquidator">
                <xsd:annotation>
                    <xsd:documentation>The party responsible for settling or paying a debt.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListingCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListingOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListMailer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiterallyTranslatedName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoadingParty">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for the loading when other than carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanApplicant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanBroker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanCorrespondent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanHolderForRealEstateAsset">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanOriginator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanServicer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocalChain">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocalGovernmentSponsor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the local government sponsor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocationOfGoods">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocationOfGoodsForCustomsExaminationBeforeClearance">
                <xsd:annotation>
                    <xsd:documentation>SE.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocationOfLoadExchange">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocationOfPrincipalAssets">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocationOfSpotForStorage">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LongTermCareFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Long-TermDisabilityCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LosingInventoryManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LossPayee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Lot">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MagistrateJudge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MagneticResonanceImagingFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaidenName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mainline">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaintenanceOrganizationUsedForEstimate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MammographyScreeningCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagedCare">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagedCareOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagementControlActivity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagementNon-Officer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Manager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagingGeneralAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturerOfGoods">
                <xsd:annotation>
                    <xsd:documentation>Party who manufactures the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturersRepresentative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Manufacturing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManufacturingPlant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarineSurveyor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarkerOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MarketTimer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mark-ForParty">
                <xsd:annotation>
                    <xsd:documentation>The party within an organization for whom the material is marked to be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MasterAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MasterGeneralAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MasterProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Material">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialChangeNoticeAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialDispositionAuthorizationLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialIssuer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialSafetyDataSheetRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeansOfTransportCharterer">
                <xsd:annotation>
                    <xsd:documentation>Charterer of the means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeatDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalDoctor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalInsuranceCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalNurse">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalSurgicalOrOtherIntensiveCareUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalTreatmentExecutant">
                <xsd:annotation>
                    <xsd:documentation>The party who executes a medical treatment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalTreatmentPrescriber">
                <xsd:annotation>
                    <xsd:documentation>The party who prescribes a medical treatment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeetingLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MegavoltageRadiationTherapyUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Member">
                <xsd:annotation>
                    <xsd:documentation>Member of a group (e.g. of a group of persons or a service scheme).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MerchantBanker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MergedCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the company involved in a merger.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MergedCompanyWithRetainedIdentity">
                <xsd:annotation>
                    <xsd:documentation>Company whose identity has been retained from a merger.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MergedName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessageFrom">
                <xsd:annotation>
                    <xsd:documentation>Party where the message comes from.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessageRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MessageTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeteringLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MexicanCustomsBroker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MiddleSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MigrantHealthClinic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MilitaryBranch">
                <xsd:annotation>
                    <xsd:documentation>Identifies the branch of the military.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MilitaryFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MinimumRoyaltyPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Minister">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MinorityInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Minority-OwnedBusinessLarge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Minority-OwnedBusinessSmall">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MiscellaneousHealthCareFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MonitoringParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MortgageCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the mortgage company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MortgageInsurer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MortgageLiabilityAccountHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostRecentEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mother">
                <xsd:annotation>
                    <xsd:documentation>Person is a mother.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MotorCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MotorVehicleReportProviderCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MultipleListingService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Municipality">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NameChangedTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NameNotToBeConfusedWith">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NameonCreditCard">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NameUnderWhichFiled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NameVariationSynonym">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NationalChain">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NationalOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NativeHawaiianOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NavyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NeglectedOrDelinquentInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NegotiatingBank">
                <xsd:annotation>
                    <xsd:documentation>Financial institution to whom a negotiable documentary credit is directed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Neighbor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Neighborhood">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NeonatalIntensiveCareUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NetworkName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewAssignee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewDebtor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewSecuredParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewServiceProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Newspaper">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NewSupplySource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextDestination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextScheduledDestination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Nickname">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoHeading">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NominatedBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the nominated bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NominatedFreightCompany">
                <xsd:annotation>
                    <xsd:documentation>Party nominated to act as transport company or carrier for the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-DomesticFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Non-domestic party acting as financial institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NonemploymentIncomeSource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-HealthCareMiscellaneousFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-Insured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-InvasiveCardiacAssessmentServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-MortgageLiabilityAccountHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-OccupantCo-Borrower">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ProductionSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ProfitHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NonprofitInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-RailroadEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NonRegisteredBusinessName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-RegisteredInvestmentAdvisor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ResidentBeneficiary">
                <xsd:annotation>
                    <xsd:documentation>The ultimate non-resident recipient of the funds. Normally the account owner who is reimbursed by the payor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ResidentCreditor">
                <xsd:annotation>
                    <xsd:documentation>Non-resident party receiving the payment or against whom a liability exists.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ResidentDebtor">
                <xsd:annotation>
                    <xsd:documentation>Non-resident party who makes the payment or against whom a claim exists.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ResidentGroupCompanyWithWhomFinancialAccountIs">
                <xsd:annotation>
                    <xsd:documentation>held Identifies the non-resident group company with whom the financial account is held.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ResidentInstructingParty">
                <xsd:annotation>
                    <xsd:documentation>Identifies the non-resident party originating the instruction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-ResidentThirdPartyCompanyWithWhomFinancial">
                <xsd:annotation>
                    <xsd:documentation>account is held Identifies the non-resident third party company with whom the financial account is held.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Non-TemporaryStorageFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Notary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotaryPublic">
                <xsd:annotation>
                    <xsd:documentation>Identifies the notary public.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoteCo-Signer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotForProfitHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotifyParty">
                <xsd:annotation>
                    <xsd:documentation>(3180) Party to be notified of arrival of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotifyPartyForShippersOrder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotifyPartyNo1">
                <xsd:annotation>
                    <xsd:documentation>The first party which is to be notified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotifyPartyNo2">
                <xsd:annotation>
                    <xsd:documentation>The second party which is to be notified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NursingHome">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NursingHomeChain">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ObservationRoomServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ObstetricsAndGynecologyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ObstetricsUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Occupant">
                <xsd:annotation>
                    <xsd:documentation>The party is an occupant.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OccupationalHealthServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OccupationalTherapyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OceanTariffConference">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OfficeManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Officer">
                <xsd:annotation>
                    <xsd:documentation>Identifies the officer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OffsetOperator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OilAndGasCustodian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OilAndGasRegulatoryAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OldAssignee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OldDebtor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OldSecuredParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnBehalfOf">
                <xsd:annotation>
                    <xsd:documentation>Party on behalf of which an action is executed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="On-CallProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OncologyCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Open-HeartSurgeryFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatingPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Operator-CommunicationChannel">
                <xsd:annotation>
                    <xsd:documentation>Operator of a communication channel.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Operator-EssentialServices">
                <xsd:annotation>
                    <xsd:documentation>Operator of essential services e.g. water, sewerage system, power.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatorOfPropertyOrEquipment">
                <xsd:annotation>
                    <xsd:documentation>The party which operates property or a unit of equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatorOfPropertyOrUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatorOfTheSourceTransferPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperatorOfTheTransferPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderedBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the account servicer for the ordering customer or payor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderedBy">
                <xsd:annotation>
                    <xsd:documentation>Party who issued an order.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderingBank">
                <xsd:annotation>
                    <xsd:documentation>Bank which instructed the sender to act on the transaction(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderingCustomer">
                <xsd:annotation>
                    <xsd:documentation>Identifies the originator of the instruction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderingParty">
                <xsd:annotation>
                    <xsd:documentation>To be used only if ordering party and buyer are not identical.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderingPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderOfShippersOrders-Transportation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderOfTheShipperParty">
                <xsd:annotation>
                    <xsd:documentation>The owner of goods under consignment which are moving under a negotiable transport document and will only be released upon receipt of the original transport document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderWriter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Organization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrganizationCompletingConfigurationChange">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrganizationGroup">
                <xsd:annotation>
                    <xsd:documentation>Identifies the organization group.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrganizationHavingSourceControl">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrganizedOutpatientServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrganizedSocialWorkServiceFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Organizer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrganOrTissueTransplantUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalClaimant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalIntermodalRamp">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalJurisdiction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalShipper">
                <xsd:annotation>
                    <xsd:documentation>The original supplier of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginatingBank">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginatingCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginatorOfArticleNumber">
                <xsd:annotation>
                    <xsd:documentation>A code identifying the party which created a specific article number.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginDrayman">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginMailFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginSublocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginTerminal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrthopedicFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrthopedicSurgeryFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherDepartments">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherDisadvantagedBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherIndividualDisabilityCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherNot-For-ProfitFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherPersonOrEntityAssociatedWithStudent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherRelatedParty">
                <xsd:annotation>
                    <xsd:documentation>Identifies an entity as an unspecified but related party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherSpecialtyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OtherUnlistedTypeOfOrganizationalEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OuterContinentalShelfAreaLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Out-Of-StatePrincipalOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutpatientSocialWorkServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutpatientSurgeryServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutpatientSurgicenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutsideInspectionAgency">
                <xsd:annotation>
                    <xsd:documentation>Third party inspecting goods or equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutsideProcessor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutsideTestAgency">
                <xsd:annotation>
                    <xsd:documentation>Third party testing goods, equipment or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverrideInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Owner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerAnnuitant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerAnnuitantPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerJointAnnuitantPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfEquipment">
                <xsd:annotation>
                    <xsd:documentation>Party who owns equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfMeansOfTransport">
                <xsd:annotation>
                    <xsd:documentation>(3126) Party owning the means of transport. No synonym of carrier = CA.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfOperation">
                <xsd:annotation>
                    <xsd:documentation>Owner of the operation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfProperty">
                <xsd:annotation>
                    <xsd:documentation>Party owning a property.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfPropertyOrUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfResidence">
                <xsd:annotation>
                    <xsd:documentation>Identifies the owner of a residence.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfVehicle">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOfVessel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerOrLessorsSurveyor">
                <xsd:annotation>
                    <xsd:documentation>Surveyor hired by the owner or lessor of the item.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnerPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnersAgent">
                <xsd:annotation>
                    <xsd:documentation>Person acting on delegation of powers of the owner.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnersRepresentative">
                <xsd:annotation>
                    <xsd:documentation>Person commissioned by the owner to represent him in certain circumstances.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwningInventoryControlPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OxygenTherapyFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Packer">
                <xsd:annotation>
                    <xsd:documentation>Party or person who has undertaken or will undertake packing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Paramedic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParamedicalCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Parent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParentCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the parent company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Participant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParticipatingArea">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParticipatingLaboratory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Partner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Partnership">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartnershipFor-ProfitFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartRemanufacturer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartsDistributor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartSource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAccordingToDocumentaryCreditTransaction">
                <xsd:annotation>
                    <xsd:documentation>Party related to documentary credit transaction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAssigningAStatus">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for assigning a status.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAtFinalPlaceOfPositioning">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party at the final place of positioning.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAtPick-UpLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAuthorizedtoDefinitizeContractAction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAuthorizedToMakeDefiniteAContractAction">
                <xsd:annotation>
                    <xsd:documentation>Party who has the authority to make definite a contract action.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAuthorizingDisposition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyAuthorizingReleaseOfSecurityInformation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyDataResponsibleParty">
                <xsd:annotation>
                    <xsd:documentation>The party responsible for all party data.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyDeclaringTheValueAddedTax">
                <xsd:annotation>
                    <xsd:documentation>A code to identify the party who is responsible for declaring the Value Added Tax (VAT) on the sale of goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyDesignatedToExecuteRe-Icing">
                <xsd:annotation>
                    <xsd:documentation>Party designated to execute re-icing, selected in the official list of mandatories competent for this kind of operation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyDesignatedToExecuteSanitaryProcedures">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyDesignatedToProvideLivingAnimalCare">
                <xsd:annotation>
                    <xsd:documentation>Party responsible to take care of transported living animals.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyExecutingAndVerifying">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyFillingShippersOrder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyForWhomItemIsUltimatelyIntended">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyFromWhomCustomsDocumentsAreToBePickedUp">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party from whom customs documents are to be picked up.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyFromWhomNon-CustomsDocumentsAreToBePickedUp">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party from whom non-customs documents are to be picked up.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyFulfillingAllOperations">
                <xsd:annotation>
                    <xsd:documentation>Code indicating the fact that the party identified carries out all operations within that company's activities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyInPossession">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyIssuingMutuallyAgreedCodes">
                <xsd:annotation>
                    <xsd:documentation>The party which has issued all mutually agreed codes used in the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyMaintainingTheCodesUsedInTheMessage">
                <xsd:annotation>
                    <xsd:documentation>The party which maintains the codes used in the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyMaintainingTheIdentifiersUsedInTheMessage">
                <xsd:annotation>
                    <xsd:documentation>The party which maintains the identifiers used in the message.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyMandatedToLiquidateAnEnterprise">
                <xsd:annotation>
                    <xsd:documentation>A code to identify the party who has been legally mandated to sell off an enterprise.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyManufacturedFor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party for whom manufacturing of goods is done.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyNotToBeConfusedWith">
                <xsd:annotation>
                    <xsd:documentation>Identifies a party not to be confused with another party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyPassingTheTransaction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyPerformingCertification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyPerformingCount">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyPerformingInspection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyPerformingTask">
                <xsd:annotation>
                    <xsd:documentation>An entity responsible for performing a task to be undertaken.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyPerformingVerification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyProvidingFinancing">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party providing the financing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyProvidingGovernmentFurnishedProperty">
                <xsd:annotation>
                    <xsd:documentation>The party responsible for providing government furnished property.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyRecoveringTheValueAddedTax">
                <xsd:annotation>
                    <xsd:documentation>A code to identify the party who is eligible to recover the Value Added Tax (VAT) on the sale of goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyRepresented">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party represented.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyResponsibleForDisinfection">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for performing disinfection operations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyResponsibleForFinancialSettlement">
                <xsd:annotation>
                    <xsd:documentation>(3450) Party responsible for either the transfer or repatriation of the funds relating to a transaction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyResponsibleForRefueling">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for performing refueling operations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartySubmittingQuote">
                <xsd:annotation>
                    <xsd:documentation>The party stating the price of something to be purchased.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyThatReceivedDisclosureStatement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyThatRemitsPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoAppearBefore">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToApproveSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Partytobeadvised-WrittenOrders">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToBeBilled">
                <xsd:annotation>
                    <xsd:documentation>Party to be billed in accordance with AAR Accounting rule 11.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToBeBilledForOtherThanFreight">
                <xsd:annotation>
                    <xsd:documentation>Party receiving invoice excluding freight costs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoContract">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToFilePersonalPropertyTax">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoLease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoPerformPackaging">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToPrintSomeDocument">
                <xsd:annotation>
                    <xsd:documentation>The party that is to print a specific document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoProvideDiscount">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveAllDocuments">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveBillOfLading">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCertificateOfCompliance">
                <xsd:annotation>
                    <xsd:documentation>Party acting for or on behalf of seller in matters concerning compliance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCertificateOfConformance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCertifiedInspectionReport">
                <xsd:annotation>
                    <xsd:documentation>Party (at buyer) to receive certified inspection report.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCertifiedTestResults">
                <xsd:annotation>
                    <xsd:documentation>Party to whom the certified test results should be sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCommercialInvoiceRemittance">
                <xsd:annotation>
                    <xsd:documentation>Party to whom payment for a commercial invoice or bill should be remitted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveContractSecurityClassificationSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCopy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCorrespondence">
                <xsd:annotation>
                    <xsd:documentation>Second party designated by a first party to receive certain correspondence in lieu of it being mailed directly to this first party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveCredit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveCustomsDocuments">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party to whom customs documents are to be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveElectronicMemoOfInvoice">
                <xsd:annotation>
                    <xsd:documentation>Party being informed about invoice issue (via EDI).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveEquipment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveFreightBill">
                <xsd:annotation>
                    <xsd:documentation>Party to whom the freight bill should be sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveInspectionReport">
                <xsd:annotation>
                    <xsd:documentation>Party to whom the inspection report should be sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveInvitationToOffer">
                <xsd:annotation>
                    <xsd:documentation>An entity to receive an invitation to offer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveInvoiceFOrGoodsOrServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveInvoiceForLeasePayments">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveLimitationsOfHeavyElementsReport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveMaterialRelease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveNon-CustomsDocuments">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party to whom non-customs documents are to be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveOrderToSupply">
                <xsd:annotation>
                    <xsd:documentation>Party designated by the registering party to receive a binding direction to supply something.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceivePackingCratingAndHandlingCredit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceivePaperMemoOfInvoice">
                <xsd:annotation>
                    <xsd:documentation>Party being informed about invoice issue (via paper).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveProposal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceivePurchaseOrder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveReports">
                <xsd:annotation>
                    <xsd:documentation>The party to whom reports are to be submitted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveScaleTicket">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveShippingManifest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveShippingNotice">
                <xsd:annotation>
                    <xsd:documentation>The party is to be the recipient of the shipping notice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveSolicitation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveStatementOfAccount">
                <xsd:annotation>
                    <xsd:documentation>Party to whom the statement of account should be sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveStatementOfFeesDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveStatus">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveTechnicalDocumentation">
                <xsd:annotation>
                    <xsd:documentation>Party to whom technical documentation should be sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToReceiveTestReport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoReceiveTransportationCredit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoWhomAcknowledgmentShouldBeSent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyToWhomDocumentsAreToBePresented">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartytoWhomProtestSubmitted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyWaivingRequirements">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyWhichDeliversConsignmentsToTheTerminal">
                <xsd:annotation>
                    <xsd:documentation>Party which delivers consignments to a terminal.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyWhichPicksUpConsignmentsFromTheTerminal">
                <xsd:annotation>
                    <xsd:documentation>Party which picks up consignments from a terminal.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyWhoSignedTheDeliveryReceipt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyWithKnowledgeOfAffairsOfTheCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartyWithPowerToVoteSecurities">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Passenger">
                <xsd:annotation>
                    <xsd:documentation>A person conveyed by a means of transport, other than the crew.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Patient">
                <xsd:annotation>
                    <xsd:documentation>A person receiving or registered to receive medical treatment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PatientCompanion">
                <xsd:annotation>
                    <xsd:documentation>Person accompanying the patient.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PatientEducationUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PatientFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PatientRepresentativeServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Payee">
                <xsd:annotation>
                    <xsd:documentation>Identifies the credit party when other than the beneficiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayeesCompanyNameOrId">
                <xsd:annotation>
                    <xsd:documentation>Receiving company name/ID (ACH transfers).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Payer">
                <xsd:annotation>
                    <xsd:documentation>(3308) Party initiating payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayerOfLastResort">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayersCompanyNameOrId">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayersFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayingBank">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayingFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Financial institution designated to make payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Payor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the debit party when other than the ordering customer (for banking purposes).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pay-ToProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pedestrian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PediatricAcuteInpatientUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PerformedAt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PermitHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonForWhoseBenefitPropertywasSeized">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonnelOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonOnClaim">
                <xsd:annotation>
                    <xsd:documentation>To identify the person who is the subject of the claim.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonOrOtherEntityLegallyResponsibleForAChild">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonOrOtherEntityLegallyResponsibleForAndWithWhomAChildResides">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PersonOrOtherEntityWithWhomAChildResides">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PetitioningCreditor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the petitioning creditor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PetitioningCreditorsAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pharmacist">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pharmacy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PharmacyDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicalTherapyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Physician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicianClinicOrGroupPractice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PhysicianHospitalOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Physician-Specialists">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PickUpAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PierName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pipeline">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PipelineSegment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PipelineSegmentBoundary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlaceOfBottling">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlaceOfBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlaceOfDistilling">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlaceOfOccurrence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlaceOfPerformanceCognizantSecurityOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Plaintiff">
                <xsd:annotation>
                    <xsd:documentation>Identifies the plaintiff.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanAdministrator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanningOrMaintenanceOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanningCommission">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanningScheduleOrMaterialReleaseIssuer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanSponsor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Plant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlantClearanceOfficer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Podiatrist">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PointOfService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicyHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicyWritingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PollingSite">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PortAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostalMailingAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostsecondaryEducationRecipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostsecondaryEducationSender">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Post-SecondaryInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PotentialSourceOfSupply">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PowerOfAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreferredProviderOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-KDayCare">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-kindergartenToGrade12Recipient">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-kindergartenToGrade12Sender">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminaryCustomerOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminaryInspectionLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminaryMaintenanceOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminaryMaintenancePeriodDesignatingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreliminaryReferredToOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Premises">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Preparer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentingBank">
                <xsd:annotation>
                    <xsd:documentation>The bank which presents documents to the drawee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="President">
                <xsd:annotation>
                    <xsd:documentation>Identifies the president.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousBusinessPartner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousCreditGrantor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousDebtHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousDistributor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousEmployer">
                <xsd:annotation>
                    <xsd:documentation>Previous employer of a person(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousGroup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousMember">
                <xsd:annotation>
                    <xsd:documentation>Previous member of a group of persons or a service scheme.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousName">
                <xsd:annotation>
                    <xsd:documentation>Name of an entity used before the current name.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousNameOfFirm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousNeighbor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousOccupant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousOwner">
                <xsd:annotation>
                    <xsd:documentation>Identifies the previous owner.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousResidence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousStation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousTitleCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousTradeUnion">
                <xsd:annotation>
                    <xsd:documentation>Employee organisation who previously represented an employee .</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceAdministration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryBeneficiary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryCareProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceCheckShipTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryControlPointLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation>Health care provider that has primary responsibility for patient.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryInsuredOrSubscriber">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryInterexchangeCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryInternationalTelecomCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryIntra-LATACarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryInventoryControlActivity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryPayer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryResidence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimarySupportOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimarySurgeon">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryTaxpayer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimeContractor">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for the whole project if other than the buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Principal">
                <xsd:annotation>
                    <xsd:documentation>(3340) Party accepting liability for goods held or moving (e.g. transit) under a Customs authorization and - when applicable - a guarantee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrincipalBorrower">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrincipalExecutiveOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrincipalPerson">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Printer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorBaseJurisdiction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorIncorrectInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorInsuranceCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorTitleEvidenceHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivatePre-KEducation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivateSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProBonoAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Processor">
                <xsd:annotation>
                    <xsd:documentation>Party or person who has or will apply a process.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcurementResponsibilityForOrder">
                <xsd:annotation>
                    <xsd:documentation>A code used to identify the organization which is responsible for the procurement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProduceDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Producer">
                <xsd:annotation>
                    <xsd:documentation>Party or person who has produced the produce.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Production">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductionManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductPositionHolder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductServicesAndRepairsCentre">
                <xsd:annotation>
                    <xsd:documentation>A centre which services and repairs products.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProfessionalOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Project">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for a project, e.g. a construction project.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectCoordinationOffice">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for technical coordination of works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectManagementOffice">
                <xsd:annotation>
                    <xsd:documentation>Party commissioned by the owner to follow through the execution of all works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Property">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PropertyOrBuildingManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProposedInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Proposer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProSeCounsel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrototypeSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Provider">
                <xsd:annotation>
                    <xsd:documentation>Identifies the provider.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricChildOrAdolescentServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricConsultation-LiaisonServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricEducationServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricEmergencyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricGeriatricServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricHealthFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricInpatientUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricOutpatientServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PsychiatricPartialHospitalizationProgram">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Psychiatrist">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Psychologist">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicAlternativeSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicHealthServiceFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicHealthServiceIndianServiceFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicorPrivateOrganizationForTheDisabled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicPre-KEducation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Publisher">
                <xsd:annotation>
                    <xsd:documentation>Identifies the publisher.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pumper">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchasedCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the purchased company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchasersDepartmentBuyer">
                <xsd:annotation>
                    <xsd:documentation>Purchasing department of buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseServiceProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QualifyingOfficer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QualityControl">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuotationRequestingParty">
                <xsd:annotation>
                    <xsd:documentation>Party sending a request for a quotation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RadioactiveImplantsUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RadioControlStationLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RadioStation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RailCarrier">
                <xsd:annotation>
                    <xsd:documentation>A carrier moving cargo, including containers, via rail.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Railroad">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RatingOrganization">
                <xsd:annotation>
                    <xsd:documentation>Identifies an organization responsible for assigning a classification or rating.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiptMeterLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiptZone">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivedFrom">
                <xsd:annotation>
                    <xsd:documentation>Name of a person or department which actually delivers the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Receiver">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiverManager">
                <xsd:annotation>
                    <xsd:documentation>Manager of a business which is in receivership status and which will not be liquidated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiverOfCheque">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party which is to receive the actual cheque, when different from the receiver of funds.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiverOfFunds">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party that receives the funds.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiversCorrespondentBank">
                <xsd:annotation>
                    <xsd:documentation>The branch of the receiver, or another financial institution, at which the funds will be made available to the receiver.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiverSite">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiversSub-Entity">
                <xsd:annotation>
                    <xsd:documentation>Identifies a sub-entity within the receiver's organization.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingBank">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingFacilityScheduler">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Financial institution designated to receive payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingPointForCustomerSamples">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivingSub-Location">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecipientOfCivilorLegalLiabilityPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReclamationCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Record-KeepingAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecoveryRoom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecreationalTherapyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Referee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reference">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferencesEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferralProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferringHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation>Health care provider that referred patient to current provider of services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferringProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Refinery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RefineryOperator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RefusalParty">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party responsible for a refusal.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Region">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegionalOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteredBusinessName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteredInvestmentAdvisor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteredOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteredOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteredPrincipal">
                <xsd:annotation>
                    <xsd:documentation>An individual who is registered as a principal for an entity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteringNonparentParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteringParentParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteringParty">
                <xsd:annotation>
                    <xsd:documentation>Party performing the registration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Registrar">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationTribunal">
                <xsd:annotation>
                    <xsd:documentation>Name of the tribunal where the company is registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegularDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegulatoryStateDistrict">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegulatoryStateEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RehabilitationFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RehabilitationInpatientUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RehabilitationOutpatientServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReimbursingBank">
                <xsd:annotation>
                    <xsd:documentation>Identifies the financial institution through which the reimbursement is to be effected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsuranceIntermediaryOrBroker">
                <xsd:annotation>
                    <xsd:documentation>Intermediary party between ceding company and reinsurance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reinsurer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Relative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseDrayman">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseToParty">
                <xsd:annotation>
                    <xsd:documentation>Party to which the goods or container(s) is (are) to be released.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RemittingBank">
                <xsd:annotation>
                    <xsd:documentation>The bank to which the principal has entrusted the handling of a collection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RemitTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RenderingProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Renter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentPayor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairingOutlet">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairOrRefurbishLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReplacingManufacturer">
                <xsd:annotation>
                    <xsd:documentation>A code used to identify a party who replaces the previous party for the manufacture of an article.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reporter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportingCarrier">
                <xsd:annotation>
                    <xsd:documentation>Party who makes the cargo report to Customs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportingCarriersNominatedAgentOrRepresentative">
                <xsd:annotation>
                    <xsd:documentation>(Customs) Agent who formally makes a cargo report to Customs on behalf of the carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportingLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportingOfficer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Representative">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReproductiveHealthServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedLender">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Requestor">
                <xsd:annotation>
                    <xsd:documentation>The party requesting an action.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResaleCustomer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResaleDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResearchInstitute">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reservoir">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidenceOrDomicile">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidencyInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidentBeneficiary">
                <xsd:annotation>
                    <xsd:documentation>The ultimate resident recipient of the funds. Normally the account owner who is reimbursed by the payor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidentInstructingParty">
                <xsd:annotation>
                    <xsd:documentation>Identifies the resident party originating the instruction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RespiratoryTherapyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RespiteCareFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponsibleExhibitor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponsibleGovernmentAgency">
                <xsd:annotation>
                    <xsd:documentation>Identifies the responsible government agency.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponsibleInstallationDestination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponsibleInstallationOrigin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResponsibleParty">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party that can be called to account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RetailAccount">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RetailPharmacy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Retrocessionaire">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnedTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoadCarrier">
                <xsd:annotation>
                    <xsd:documentation>A road carrier moving cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoutingParty">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for the selection of the carrier(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoutingPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoyaltyOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RuralHealthClinic">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalesAssistant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalesOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Salesperson">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalesResponsibility">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalvageCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SamplesToBeReturnedTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SamplingLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="School-BasedServiceProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SchoolDistrict">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SCorporation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScreeningPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SealingCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondaryInventoryControlActivity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondaryLocationAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondaryPayer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondaryTaxpayer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondaryWarranter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondContact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondContactOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondHome">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondLevelParentCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Secretariat">
                <xsd:annotation>
                    <xsd:documentation>Party is a secretariat.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Section8AProgramParticipantFirm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecuredParty">
                <xsd:annotation>
                    <xsd:documentation>Identifies a party that is guaranteed against loss.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecuredPartyCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecuredPartyIndividual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SelfInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Seller">
                <xsd:annotation>
                    <xsd:documentation>(3346) Party selling merchandise to a buyer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellerAsOfficiallyRegistered">
                <xsd:annotation>
                    <xsd:documentation>Selling party as officially registered with government.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellersAgentOrRepresentative">
                <xsd:annotation>
                    <xsd:documentation>(3254) Party representing the seller for the purpose of the trade transaction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellersFinancialInstitution">
                <xsd:annotation>
                    <xsd:documentation>Financial institution designated by seller to receive payment. RDFI (ACH transfers).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellingOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellingParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SenderOfFunds">
                <xsd:annotation>
                    <xsd:documentation>Identifies the party that sends the funds.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SendersCorrespondentBank">
                <xsd:annotation>
                    <xsd:documentation>The account, or branch of the sender, or another financial institution, through which the sender will reimburse the receiver.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SeniorMembershipProgram">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceBureau">
                <xsd:annotation>
                    <xsd:documentation>Party carrying out service bureau processing work, (e.g. a payroll bureau).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicePerformanceSite">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicePerformer">
                <xsd:annotation>
                    <xsd:documentation>The party who is performing a service.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceRequester">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicingBroker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicingDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SharedService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shareholder">
                <xsd:annotation>
                    <xsd:documentation>Identifies a shareholder.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShelteredWorkshop">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipFrom">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party from where goods will be or have been shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shipper">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippersAssociation">
                <xsd:annotation>
                    <xsd:documentation>An association of shippers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingLineService">
                <xsd:annotation>
                    <xsd:documentation>Identifies the shipping line service organization.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingScheduleIssuer">
                <xsd:annotation>
                    <xsd:documentation>The party which issues a shipping schedule.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipTo">
                <xsd:annotation>
                    <xsd:documentation>Identification of the party to where goods will be or have been shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShowingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sibling">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Signatory">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is a signatory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Signer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SignificantOther">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SinglePhotonEmissionComputerizedTomographyUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SiteOperator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SkilledNursingFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SkilledNursingorOtherLong-TermCareUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SkilledNursingServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Slaughterer">
                <xsd:annotation>
                    <xsd:documentation>Party or person who has undertaken or will undertake a slaughter.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlotCharterParty">
                <xsd:annotation>
                    <xsd:documentation>An identification code of a participant or user that books slots (space) on a ship, more likely on a long term basis on a series of sailings. He pays for the space whether he uses it or not.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SmallBaseStationLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SmallBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SmallControlStationLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SmallDisadvantagedBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SociallyDisadvantagedBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SociallyDisadvantagedIndividual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SociallyInsuredPerson">
                <xsd:annotation>
                    <xsd:documentation>A person who is registered in a social security scheme.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SocialSecuritiesCollectorsOffice">
                <xsd:annotation>
                    <xsd:documentation>Party collecting social securities premiums.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SocietyOfPropertyInformationCompilersAndAnalysts">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoldTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoldToAndShipTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoldToIfDifferentFromBillTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoleProprietor">
                <xsd:annotation>
                    <xsd:documentation>Identifies the sole proprietor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Source">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SourceMeterLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SourceOfInformation">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is the source of information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SourcePartyOfInformation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialAccount">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecialCounsel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecializedMobileRadioServiceLicensee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecializedOutpatientProgramForAIDSOrARC">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpecifiedRepository">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpeechTherapyServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SportsMedicineClinicOrServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Spouse">
                <xsd:annotation>
                    <xsd:documentation>Person is a spouse.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpouseInsured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpudContractor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="State">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateCharteredFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateDivision">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SteamshipCompany">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stockholder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StorageArea">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StorageFacilityatDestination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StorageFacilityAtOrigin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Store">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StoreClass">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StoreGroup">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StoreKeeper">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StoreManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Student">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StudySubmitter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sub-Account">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Subcontractor">
                <xsd:annotation>
                    <xsd:documentation>Firm carrying out a part of the works for a contractor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubcontractOrCognizantSecurityOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Subcontractor-Nominated">
                <xsd:annotation>
                    <xsd:documentation>Subcontractor authorized by the owner after having been proposed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubcontractorWithDirectPayment">
                <xsd:annotation>
                    <xsd:documentation>Subcontractor benefiting from direct payments.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sub-Entity">
                <xsd:annotation>
                    <xsd:documentation>A part into which an entity has been divided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Subgroup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubjectOfInquiry">
                <xsd:annotation>
                    <xsd:documentation>The party is the subject of an inquiry.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubjectProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Submitter">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is a submitter.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubmitterOfSample">
                <xsd:annotation>
                    <xsd:documentation>An entity responsible for the submission of a sample.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sub-Office">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Subscriber">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubscribersEmployer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubscribersSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubsequentOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sub-Servicer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Subsidiary">
                <xsd:annotation>
                    <xsd:documentation>Identifies the subsidiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubsidiaryDivision">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubstituteSupplier">
                <xsd:annotation>
                    <xsd:documentation>Party which may be in a position to supply products or services should the main usual supplier be unable to do so.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuccessfulJobApplicant">
                <xsd:annotation>
                    <xsd:documentation>Person who has been chosen for a job.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupervisingHealthCareProvider">
                <xsd:annotation>
                    <xsd:documentation>Health care provider that supervised the rendering of a service.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupervisingPhysician">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Supervisor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupervisoryAppraiserName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Supplier">
                <xsd:annotation>
                    <xsd:documentation>Party who supplies goods and/or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplierOrManufacturer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuppliersCorporateOffice">
                <xsd:annotation>
                    <xsd:documentation>The identification of the supplier's corporate office.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplierWorkTeam">
                <xsd:annotation>
                    <xsd:documentation>The supplier's team responsible for performing the work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplySource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupportingPersonnel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupportParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Surety">
                <xsd:annotation>
                    <xsd:documentation>Natural or legal person (generally a bank or insurance company) who accepts responsibility in due legal form for the financial consequences of non-fulfillment of another's obligations to the Customs (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuretyForAdditions">
                <xsd:annotation>
                    <xsd:documentation>Natural of legal person (generally a bank of insurance company) who accepts responsibility in due legal form for the financial guarantee to Customs of the payment of additional duties or fees that become due against a particular shipment, which have not previously been covered by surety.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuretyForAntidumpingOrCountervailingDuty">
                <xsd:annotation>
                    <xsd:documentation>Natural or legal person that has been contracted by the importer to guarantee to Customs the payment of antidumping and/or countervailing duties that become due against a particular shipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceDischargeAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceManagementEntity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Surgeon">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurplusDealer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Surveyor-PofessionalQuantity">
                <xsd:annotation>
                    <xsd:documentation>Party responsible to the owner for the quantification and valuation of the works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurveyorQuantity">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for the quantification and valuation of the works on behalf of the contractor.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurveyorTopographical">
                <xsd:annotation>
                    <xsd:documentation>Party responsible for topographical measurements.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="System">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TankFarm">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TankFarmOwner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TariffIssuer">
                <xsd:annotation>
                    <xsd:documentation>The issuer of a tariff, e.g. a freight tariff.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxAddress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxCollectorsOffice">
                <xsd:annotation>
                    <xsd:documentation>Party collecting taxes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxExemptOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Team">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TechnicalOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TelephoneAnsweringServiceBureau">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tenant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TenantManager">
                <xsd:annotation>
                    <xsd:documentation>A code to identify the party who rents the rights to use the goodwill and facilities of an enterprise.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tenderer">
                <xsd:annotation>
                    <xsd:documentation>Firm answering an invitation to tender.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Terminal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalOperator">
                <xsd:annotation>
                    <xsd:documentation>A party which handles the loading and unloading of marine vessels.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TertiaryPayer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestingLaboratory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestingService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestSponsor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TheaterCircuit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Theatre">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TherapeuticRadioisotopeFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdParty">
                <xsd:annotation>
                    <xsd:documentation>Another party besides the two principals.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyAdministrator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyInvestmentAdvisor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyMarketer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyRepricingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyRepricingPreferredProviderOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyReviewingOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ThirdPartyReviewingPreferredProviderOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Titleholder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TitleInsuranceServicesProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TitleUnderwriter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tooling">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolingDesign">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolingFabrication">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolSource">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TopParentCompanyInSameCountry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TowingAgency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tradename">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeNameSynonym">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeUnion">
                <xsd:annotation>
                    <xsd:documentation>Organisation representing employees.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrainingInstitution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferringParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferTo">
                <xsd:annotation>
                    <xsd:documentation>The party which is the recipient of a transfer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransitAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransitChargePayer">
                <xsd:annotation>
                    <xsd:documentation>Party, other than the ordering party, which has to pay the charges concerning the transit operations.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransitFreightForwarder">
                <xsd:annotation>
                    <xsd:documentation>Freight forwarder to whom transit consignments are addressed, and from whom they are to be on-forwarded.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransitPrincipal">
                <xsd:annotation>
                    <xsd:documentation>Natural or legal person responsible for the satisfactory performance of a Customs transit operation. Source: CCC.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransitPrincipalsAgentOrRepresentative">
                <xsd:annotation>
                    <xsd:documentation>Agent acting on behalf of the transit principal (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transmitter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportationOffice">
                <xsd:annotation>
                    <xsd:documentation>The office that provides transportation information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransporterOfHazardousWaste">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransshipmentParty">
                <xsd:annotation>
                    <xsd:documentation>A party responsible for transshipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TraumaCenter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TreatmentFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TribalGovernment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Trust">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Trustee">
                <xsd:annotation>
                    <xsd:documentation>To specify that the party is a trustee.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrusteesAttorney">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrustOfficer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrustOrEstate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TuberculosisAndOTherRespiratoryDiseasesFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UltimateConsignee">
                <xsd:annotation>
                    <xsd:documentation>Party who has been designated on the invoice or packing list as the final recipient of the stated merchandise.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UltimateCustomer">
                <xsd:annotation>
                    <xsd:documentation>The final recipient of goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UltimateParentCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the ultimate parent company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UltimateSameCountryParentCompany">
                <xsd:annotation>
                    <xsd:documentation>Identifies the highest level parent company in the same country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UltrasoundUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnassignedAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Underwriter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnexpectedHandlingParty">
                <xsd:annotation>
                    <xsd:documentation>Party authorized (during a voyage) to apply unexpected handling procedures or party having applied these procedures.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Union">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnitedStatesCustomsBroker">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnitedStatesOverseasSecurityAdministrationOffice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnitProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="University-CollegeOrSchool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnloadingParty">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnserviceableMaterialConsignee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UpstreamMeterLocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UpstreamParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="USTrustee">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UtilizationManagementOrganization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VacationHome">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValidatingParty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValueAddedNetworkProvider">
                <xsd:annotation>
                    <xsd:documentation>A party that provides telecommunications interconnectivity services in an electronic data interchange environment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Vehicle">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VehicleCustodian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VehicleSalvageAssignment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Vendor">
                <xsd:annotation>
                    <xsd:documentation>Party vending goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VesselMaster">
                <xsd:annotation>
                    <xsd:documentation>Master of the conveyance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VesselName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Veteran-OwnedBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VeteransAdministrationFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VeteransAdministrationLoanAuthorizedSupplier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VeteransAdministrationLoanGuarantyAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Veterinarian">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VeterinaryHospital">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Victim">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VideoDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VolunteerServicesDepartment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Voter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WageEarner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Warehouse">
                <xsd:annotation>
                    <xsd:documentation>The name of the warehouse where product is held.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarehouseDepositor">
                <xsd:annotation>
                    <xsd:documentation>(3004) Party depositing goods in a warehouse.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarehouseKeeper">
                <xsd:annotation>
                    <xsd:documentation>(3022) Party taking responsibility for goods entered into a warehouse.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Warranter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaterAmbulanceServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaterSystem">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeightingParty">
                <xsd:annotation>
                    <xsd:documentation>Party designated (legally accepted) to ascertain the weight.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellCasingDepthAuthority">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellPadConstructionContractor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Wholesaler">
                <xsd:annotation>
                    <xsd:documentation>Seller of articles, often in large quantities, to be retailed by others.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Wholly-OwnedSubsidiary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Winner">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WithdrawalPoint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Witness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WitnessForDefendant">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WitnessForPlaintiff">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WitnessForProsecution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Woman-OwnedBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Woman-OwnedBusinessLarge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Woman-OwnedBusinessSmall">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WomensHealthCenterOrServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorksiteHealthPromotionFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorksManager">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorkTeam">
                <xsd:annotation>
                    <xsd:documentation>Team responsible for performing work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WritingAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="X-RayRadiationTherapyUnit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
