<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="UOMCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the unit of measurement. This code list is derived from UNECE Rec 20 and X12 355 (Unit or Basis for measurement code)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1">
                <xsd:annotation>
                    <xsd:documentation>ActualPounds</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4">
                <xsd:annotation>
                    <xsd:documentation>SmallSpray</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5">
                <xsd:annotation>
                    <xsd:documentation>Lift</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="8">
                <xsd:annotation>
                    <xsd:documentation>HeatLot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="10">
                <xsd:annotation>
                    <xsd:documentation>Group</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="11">
                <xsd:annotation>
                    <xsd:documentation>Outfit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="13">
                <xsd:annotation>
                    <xsd:documentation>Ration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="14">
                <xsd:annotation>
                    <xsd:documentation>Shot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="15">
                <xsd:annotation>
                    <xsd:documentation>Stick</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="16">
                <xsd:annotation>
                    <xsd:documentation>115KilogramDrum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="17">
                <xsd:annotation>
                    <xsd:documentation>100PoundDrum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="18">
                <xsd:annotation>
                    <xsd:documentation>55GallonDrum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="19">
                <xsd:annotation>
                    <xsd:documentation>TankTruck</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="20">
                <xsd:annotation>
                    <xsd:documentation>20FootContainer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="21">
                <xsd:annotation>
                    <xsd:documentation>40FootContainer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="22">
                <xsd:annotation>
                    <xsd:documentation>DeciliterPerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="23">
                <xsd:annotation>
                    <xsd:documentation>GramsperCubicCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="24">
                <xsd:annotation>
                    <xsd:documentation>TheoreticalPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="25">
                <xsd:annotation>
                    <xsd:documentation>GramsperSquareCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="26">
                <xsd:annotation>
                    <xsd:documentation>ActualTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="27">
                <xsd:annotation>
                    <xsd:documentation>TheoreticalTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="28">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerSquareMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="29">
                <xsd:annotation>
                    <xsd:documentation>PoundPerThousandSquareFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="30">
                <xsd:annotation>
                    <xsd:documentation>HorsePowerDayPerAirDryMetricTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="31">
                <xsd:annotation>
                    <xsd:documentation>CatchWeight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="32">
                <xsd:annotation>
                    <xsd:documentation>KilogramPerAirDryMetricTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="33">
                <xsd:annotation>
                    <xsd:documentation>KilopascalSquareMetersPerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="34">
                <xsd:annotation>
                    <xsd:documentation>KilopascalsPerMillimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="35">
                <xsd:annotation>
                    <xsd:documentation>MillilitersPerSquareCentimeterSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="36">
                <xsd:annotation>
                    <xsd:documentation>CubicFeetPerMinutePerSquareFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="37">
                <xsd:annotation>
                    <xsd:documentation>OuncePerSquareFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="38">
                <xsd:annotation>
                    <xsd:documentation>OuncesPerSquareFootPer0.01Inch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="40">
                <xsd:annotation>
                    <xsd:documentation>MilliliterPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="41">
                <xsd:annotation>
                    <xsd:documentation>MilliliterPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="43">
                <xsd:annotation>
                    <xsd:documentation>SuperBulkBag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="44">
                <xsd:annotation>
                    <xsd:documentation>500KilogramBulkBag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="45">
                <xsd:annotation>
                    <xsd:documentation>300KilogramBulkBag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="46">
                <xsd:annotation>
                    <xsd:documentation>FiftyLbBulkBag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="47">
                <xsd:annotation>
                    <xsd:documentation>50PoundBag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="48">
                <xsd:annotation>
                    <xsd:documentation>BulkCarLoad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="50">
                <xsd:annotation>
                    <xsd:documentation>ActualKilograms</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="51">
                <xsd:annotation>
                    <xsd:documentation>ActualTonnes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="53">
                <xsd:annotation>
                    <xsd:documentation>TheoreticalKilograms</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="54">
                <xsd:annotation>
                    <xsd:documentation>TheoreticalTonne</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="56">
                <xsd:annotation>
                    <xsd:documentation>Sitas</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="57">
                <xsd:annotation>
                    <xsd:documentation>Mesh</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="58">
                <xsd:annotation>
                    <xsd:documentation>NetKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="59">
                <xsd:annotation>
                    <xsd:documentation>PartsPerMillion</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="60">
                <xsd:annotation>
                    <xsd:documentation>PercentWeight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="61">
                <xsd:annotation>
                    <xsd:documentation>PartsPerBillion</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="62">
                <xsd:annotation>
                    <xsd:documentation>PercentPer1000Hours</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="63">
                <xsd:annotation>
                    <xsd:documentation>FailureRateInTime</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="64">
                <xsd:annotation>
                    <xsd:documentation>PoundPerSquareInch-Gauge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="66">
                <xsd:annotation>
                    <xsd:documentation>Oersted</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="69">
                <xsd:annotation>
                    <xsd:documentation>TestSpecificScale</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="71">
                <xsd:annotation>
                    <xsd:documentation>VoltAmperePerPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="72">
                <xsd:annotation>
                    <xsd:documentation>WattPerPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="73">
                <xsd:annotation>
                    <xsd:documentation>AmpereTumPerCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="74">
                <xsd:annotation>
                    <xsd:documentation>MilliPascals</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="76">
                <xsd:annotation>
                    <xsd:documentation>Gauss</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="77">
                <xsd:annotation>
                    <xsd:documentation>Mil</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="78">
                <xsd:annotation>
                    <xsd:documentation>Kilogauss</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="80">
                <xsd:annotation>
                    <xsd:documentation>PoundsPerSquareInchAbsolute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="81">
                <xsd:annotation>
                    <xsd:documentation>Henry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="84">
                <xsd:annotation>
                    <xsd:documentation>KilopoundPerSquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="85">
                <xsd:annotation>
                    <xsd:documentation>FootPounds</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="86">
                <xsd:annotation>
                    <xsd:documentation>Joules</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="87">
                <xsd:annotation>
                    <xsd:documentation>PoundPerCubicFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="89">
                <xsd:annotation>
                    <xsd:documentation>Poise</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="90">
                <xsd:annotation>
                    <xsd:documentation>SayboldUniversalSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="91">
                <xsd:annotation>
                    <xsd:documentation>Stokes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="92">
                <xsd:annotation>
                    <xsd:documentation>CaloriePerCubicCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="93">
                <xsd:annotation>
                    <xsd:documentation>CaloriePerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="94">
                <xsd:annotation>
                    <xsd:documentation>CurlUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="95">
                <xsd:annotation>
                    <xsd:documentation>TwentyThousandGallon-USTankcar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="96">
                <xsd:annotation>
                    <xsd:documentation>TenThousandGallon-USTankcar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="97">
                <xsd:annotation>
                    <xsd:documentation>10KilogramDrum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="98">
                <xsd:annotation>
                    <xsd:documentation>15KilogramDrum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1A">
                <xsd:annotation>
                    <xsd:documentation>CarMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1B">
                <xsd:annotation>
                    <xsd:documentation>CarCount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1C">
                <xsd:annotation>
                    <xsd:documentation>LocomotiveCount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1D">
                <xsd:annotation>
                    <xsd:documentation>CabooseCount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1E">
                <xsd:annotation>
                    <xsd:documentation>EmptyCar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1F">
                <xsd:annotation>
                    <xsd:documentation>TrainMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1G">
                <xsd:annotation>
                    <xsd:documentation>FuelUsage-Gallons</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1H">
                <xsd:annotation>
                    <xsd:documentation>CabooseMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1I">
                <xsd:annotation>
                    <xsd:documentation>FixedRate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1J">
                <xsd:annotation>
                    <xsd:documentation>TonMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1K">
                <xsd:annotation>
                    <xsd:documentation>LocomotiveMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1L">
                <xsd:annotation>
                    <xsd:documentation>TotalCarCount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1M">
                <xsd:annotation>
                    <xsd:documentation>TotalCarMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1X">
                <xsd:annotation>
                    <xsd:documentation>QuarterMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2A">
                <xsd:annotation>
                    <xsd:documentation>RadiansPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2B">
                <xsd:annotation>
                    <xsd:documentation>RadianPerSecondSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2C">
                <xsd:annotation>
                    <xsd:documentation>Roentgen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2F">
                <xsd:annotation>
                    <xsd:documentation>VoltsPerMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2G">
                <xsd:annotation>
                    <xsd:documentation>Volts-AlternatingCurrent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2H">
                <xsd:annotation>
                    <xsd:documentation>Volts-DirectCurrent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2I">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnitPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2J">
                <xsd:annotation>
                    <xsd:documentation>CubicCentimetersPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2K">
                <xsd:annotation>
                    <xsd:documentation>CubicFeetPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2L">
                <xsd:annotation>
                    <xsd:documentation>CubicFeetPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2M">
                <xsd:annotation>
                    <xsd:documentation>CentimetersPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2N">
                <xsd:annotation>
                    <xsd:documentation>Decibel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2P">
                <xsd:annotation>
                    <xsd:documentation>Kilobyte</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2Q">
                <xsd:annotation>
                    <xsd:documentation>Kilobecquerel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2R">
                <xsd:annotation>
                    <xsd:documentation>Kilocurie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2U">
                <xsd:annotation>
                    <xsd:documentation>Megagram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2V">
                <xsd:annotation>
                    <xsd:documentation>MegagramPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2W">
                <xsd:annotation>
                    <xsd:documentation>Bin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2X">
                <xsd:annotation>
                    <xsd:documentation>MetersPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2Y">
                <xsd:annotation>
                    <xsd:documentation>Milliroentgen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="2Z">
                <xsd:annotation>
                    <xsd:documentation>Millivolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3B">
                <xsd:annotation>
                    <xsd:documentation>Megajoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3C">
                <xsd:annotation>
                    <xsd:documentation>Manmonth</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3E">
                <xsd:annotation>
                    <xsd:documentation>PoundPerPoundOfProduct</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3F">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerLiterOfProduct</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3G">
                <xsd:annotation>
                    <xsd:documentation>PoundPerPieceOfProduct</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3H">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerKilogramOfProduct</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3I">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerPieceOfProduct</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4A">
                <xsd:annotation>
                    <xsd:documentation>Bobbin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4B">
                <xsd:annotation>
                    <xsd:documentation>Cap</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4C">
                <xsd:annotation>
                    <xsd:documentation>Centistokes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4E">
                <xsd:annotation>
                    <xsd:documentation>20-Pack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4G">
                <xsd:annotation>
                    <xsd:documentation>Microliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4H">
                <xsd:annotation>
                    <xsd:documentation>Micrometer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4I">
                <xsd:annotation>
                    <xsd:documentation>MetersPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4J">
                <xsd:annotation>
                    <xsd:documentation>MetersPerSecondPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4K">
                <xsd:annotation>
                    <xsd:documentation>Milliamperes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4L">
                <xsd:annotation>
                    <xsd:documentation>Megabyte</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4M">
                <xsd:annotation>
                    <xsd:documentation>MilligramPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4N">
                <xsd:annotation>
                    <xsd:documentation>Megabecquerel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4O">
                <xsd:annotation>
                    <xsd:documentation>Microfarad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4P">
                <xsd:annotation>
                    <xsd:documentation>NewtonsPerMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4Q">
                <xsd:annotation>
                    <xsd:documentation>OunceInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4R">
                <xsd:annotation>
                    <xsd:documentation>OunceFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4T">
                <xsd:annotation>
                    <xsd:documentation>Picofarad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4U">
                <xsd:annotation>
                    <xsd:documentation>PoundPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4W">
                <xsd:annotation>
                    <xsd:documentation>TonPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="4X">
                <xsd:annotation>
                    <xsd:documentation>KiloliterPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5A">
                <xsd:annotation>
                    <xsd:documentation>BarrelsPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5B">
                <xsd:annotation>
                    <xsd:documentation>Batch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5C">
                <xsd:annotation>
                    <xsd:documentation>Gallon-US-PerThousand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5E">
                <xsd:annotation>
                    <xsd:documentation>MmscfPerDay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5F">
                <xsd:annotation>
                    <xsd:documentation>PoundsPerThousand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5G">
                <xsd:annotation>
                    <xsd:documentation>Pump</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5H">
                <xsd:annotation>
                    <xsd:documentation>Stage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5I">
                <xsd:annotation>
                    <xsd:documentation>StandardCubicFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5J">
                <xsd:annotation>
                    <xsd:documentation>HydraulicHorsePower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5K">
                <xsd:annotation>
                    <xsd:documentation>CountPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5P">
                <xsd:annotation>
                    <xsd:documentation>SeismicLevel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="5Q">
                <xsd:annotation>
                    <xsd:documentation>SeismicLine</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A1">
                <xsd:annotation>
                    <xsd:documentation>15DegreesCCalorie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A10">
                <xsd:annotation>
                    <xsd:documentation>AmpereSquareMetrePerJouleSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A11">
                <xsd:annotation>
                    <xsd:documentation>Angstrom</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A12">
                <xsd:annotation>
                    <xsd:documentation>AstronomicalUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A13">
                <xsd:annotation>
                    <xsd:documentation>Attojoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A14">
                <xsd:annotation>
                    <xsd:documentation>Barn</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A15">
                <xsd:annotation>
                    <xsd:documentation>BarnPerElectronVolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A16">
                <xsd:annotation>
                    <xsd:documentation>BarnPerSteradianElectronVolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A17">
                <xsd:annotation>
                    <xsd:documentation>BarnPerSterdian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A18">
                <xsd:annotation>
                    <xsd:documentation>BecquerelPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A19">
                <xsd:annotation>
                    <xsd:documentation>BecquerelPerMetreCubed</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A2">
                <xsd:annotation>
                    <xsd:documentation>AmperePerCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A20">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnitPerSecondSquareFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A22">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnitPerSecondFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A23">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnitPerHourSquareFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A24">
                <xsd:annotation>
                    <xsd:documentation>CandelaPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A25">
                <xsd:annotation>
                    <xsd:documentation>ChevalVapeur</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A26">
                <xsd:annotation>
                    <xsd:documentation>CoulombMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A27">
                <xsd:annotation>
                    <xsd:documentation>CoulombMetreSquaredPerVolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A28">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerCubicCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A29">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A3">
                <xsd:annotation>
                    <xsd:documentation>AmperePerMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A30">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerCubicMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A31">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerKilogramSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A32">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A33">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerSquareCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A34">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A35">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerSquareMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A36">
                <xsd:annotation>
                    <xsd:documentation>CubicCentimetrePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A37">
                <xsd:annotation>
                    <xsd:documentation>CubicDecimetrePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A38">
                <xsd:annotation>
                    <xsd:documentation>CubicMetrePerCoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A39">
                <xsd:annotation>
                    <xsd:documentation>CubicMetrePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A4">
                <xsd:annotation>
                    <xsd:documentation>AmperePerSquareCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A40">
                <xsd:annotation>
                    <xsd:documentation>CubicMetrePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A41">
                <xsd:annotation>
                    <xsd:documentation>AmperePerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A42">
                <xsd:annotation>
                    <xsd:documentation>CuriePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A43">
                <xsd:annotation>
                    <xsd:documentation>DeadweightTonnage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A44">
                <xsd:annotation>
                    <xsd:documentation>Decalitre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A45">
                <xsd:annotation>
                    <xsd:documentation>Decametre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A47">
                <xsd:annotation>
                    <xsd:documentation>Decitex</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A48">
                <xsd:annotation>
                    <xsd:documentation>DegreeRankin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A49">
                <xsd:annotation>
                    <xsd:documentation>Denier</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A5">
                <xsd:annotation>
                    <xsd:documentation>AmpereSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A50">
                <xsd:annotation>
                    <xsd:documentation>DynSecondPerCubicCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A51">
                <xsd:annotation>
                    <xsd:documentation>DyneSecondPerCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A52">
                <xsd:annotation>
                    <xsd:documentation>DyneSecondPerCentimetreToTheFifth</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A53">
                <xsd:annotation>
                    <xsd:documentation>ElectronVolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A54">
                <xsd:annotation>
                    <xsd:documentation>ElectronvoltPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A55">
                <xsd:annotation>
                    <xsd:documentation>ElectronvoltSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A56">
                <xsd:annotation>
                    <xsd:documentation>ElectronvoltSquareMetrePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A57">
                <xsd:annotation>
                    <xsd:documentation>Erg</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A58">
                <xsd:annotation>
                    <xsd:documentation>ErgPerCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A6">
                <xsd:annotation>
                    <xsd:documentation>AmperePerSquareMetreKelvinSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A60">
                <xsd:annotation>
                    <xsd:documentation>ErgPerCubicCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A61">
                <xsd:annotation>
                    <xsd:documentation>ErgPerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A62">
                <xsd:annotation>
                    <xsd:documentation>ErgPerGramSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A63">
                <xsd:annotation>
                    <xsd:documentation>ErgPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A64">
                <xsd:annotation>
                    <xsd:documentation>ErgPerSecondSquareCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A65">
                <xsd:annotation>
                    <xsd:documentation>ErgPerSquareCentimetreSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A66">
                <xsd:annotation>
                    <xsd:documentation>ErgSquareCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A67">
                <xsd:annotation>
                    <xsd:documentation>ErgSquareCentimetrePerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A68">
                <xsd:annotation>
                    <xsd:documentation>Exajoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A69">
                <xsd:annotation>
                    <xsd:documentation>FaradPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A7">
                <xsd:annotation>
                    <xsd:documentation>AmperePerSquareMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A70">
                <xsd:annotation>
                    <xsd:documentation>Femtojoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A71">
                <xsd:annotation>
                    <xsd:documentation>Femtometre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A73">
                <xsd:annotation>
                    <xsd:documentation>FootPerSecondSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A74">
                <xsd:annotation>
                    <xsd:documentation>FootPound-ForcePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A75">
                <xsd:annotation>
                    <xsd:documentation>FreightTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A76">
                <xsd:annotation>
                    <xsd:documentation>Gallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A77">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfDisplacement</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A78">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfElecticCurrent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A79">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfElectricCharge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A8">
                <xsd:annotation>
                    <xsd:documentation>AmpereSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A80">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfElectricFieldStrength</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A81">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfElectricPolarization</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A82">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfElectricPotential</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A83">
                <xsd:annotation>
                    <xsd:documentation>GaussianCgsUnitOfMagnetization</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A84">
                <xsd:annotation>
                    <xsd:documentation>GigacoulombPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A85">
                <xsd:annotation>
                    <xsd:documentation>Gigaelectronvolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A86">
                <xsd:annotation>
                    <xsd:documentation>Gigahertz</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A87">
                <xsd:annotation>
                    <xsd:documentation>Gigaohm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A88">
                <xsd:annotation>
                    <xsd:documentation>GigaohmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A89">
                <xsd:annotation>
                    <xsd:documentation>Gigapascal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A9">
                <xsd:annotation>
                    <xsd:documentation>Rate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A90">
                <xsd:annotation>
                    <xsd:documentation>Gigawatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A91">
                <xsd:annotation>
                    <xsd:documentation>Grade</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A93">
                <xsd:annotation>
                    <xsd:documentation>GramPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A94">
                <xsd:annotation>
                    <xsd:documentation>GramPerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A95">
                <xsd:annotation>
                    <xsd:documentation>Gray</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A96">
                <xsd:annotation>
                    <xsd:documentation>GrayPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A97">
                <xsd:annotation>
                    <xsd:documentation>Hectopascal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="A98">
                <xsd:annotation>
                    <xsd:documentation>HenryPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AA">
                <xsd:annotation>
                    <xsd:documentation>Ball</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AB">
                <xsd:annotation>
                    <xsd:documentation>BulkPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ACR">
                <xsd:annotation>
                    <xsd:documentation>Acre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AD">
                <xsd:annotation>
                    <xsd:documentation>Byte</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AE">
                <xsd:annotation>
                    <xsd:documentation>AmperePerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AH">
                <xsd:annotation>
                    <xsd:documentation>AdditionalMinutes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AI">
                <xsd:annotation>
                    <xsd:documentation>AverageMinutesPerCall</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AJ">
                <xsd:annotation>
                    <xsd:documentation>Cop</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AK">
                <xsd:annotation>
                    <xsd:documentation>Fathom</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AL">
                <xsd:annotation>
                    <xsd:documentation>AccessLine</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AM">
                <xsd:annotation>
                    <xsd:documentation>Ampoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AMH">
                <xsd:annotation>
                    <xsd:documentation>AmpereHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AMP">
                <xsd:annotation>
                    <xsd:documentation>Ampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AN">
                <xsd:annotation>
                    <xsd:documentation>MinutesOrMessages</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ANN">
                <xsd:annotation>
                    <xsd:documentation>Years</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AP">
                <xsd:annotation>
                    <xsd:documentation>AluminiumPoundOnly</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="APZ">
                <xsd:annotation>
                    <xsd:documentation>TroyOunce</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AQ">
                <xsd:annotation>
                    <xsd:documentation>Anti-HemophilicFactorUnits</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AR">
                <xsd:annotation>
                    <xsd:documentation>Suppository</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ARE">
                <xsd:annotation>
                    <xsd:documentation>Are</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AS">
                <xsd:annotation>
                    <xsd:documentation>Assortment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ASM">
                <xsd:annotation>
                    <xsd:documentation>AlcoholicStrengthByMass</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ASU">
                <xsd:annotation>
                    <xsd:documentation>AlcoholicStrengthByVolume</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AT">
                <xsd:annotation>
                    <xsd:documentation>Atmosphere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ATM">
                <xsd:annotation>
                    <xsd:documentation>StandardAtmosphere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ATT">
                <xsd:annotation>
                    <xsd:documentation>TechnicalAtmosphere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AU">
                <xsd:annotation>
                    <xsd:documentation>OcularInsertSystem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AV">
                <xsd:annotation>
                    <xsd:documentation>Capsule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AW">
                <xsd:annotation>
                    <xsd:documentation>PowderFilledVial</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AX">
                <xsd:annotation>
                    <xsd:documentation>Twenty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AY">
                <xsd:annotation>
                    <xsd:documentation>Assembly</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AZ">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnitPerPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B0">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnitsPerCubicFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B1">
                <xsd:annotation>
                    <xsd:documentation>BarrelsPerDay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B11">
                <xsd:annotation>
                    <xsd:documentation>JoulePerKilogramKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B12">
                <xsd:annotation>
                    <xsd:documentation>JoulePerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B13">
                <xsd:annotation>
                    <xsd:documentation>JoulePerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B14">
                <xsd:annotation>
                    <xsd:documentation>JoulePerMetreToTheFourthPower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B15">
                <xsd:annotation>
                    <xsd:documentation>JoulePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B16">
                <xsd:annotation>
                    <xsd:documentation>JoulePerMoleKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B18">
                <xsd:annotation>
                    <xsd:documentation>JouleSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B2">
                <xsd:annotation>
                    <xsd:documentation>Bunk</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B20">
                <xsd:annotation>
                    <xsd:documentation>JouleSquareMetrePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B21">
                <xsd:annotation>
                    <xsd:documentation>KelvinPerWatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B22">
                <xsd:annotation>
                    <xsd:documentation>Kiloampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B23">
                <xsd:annotation>
                    <xsd:documentation>KiloamperePerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B24">
                <xsd:annotation>
                    <xsd:documentation>KiloamperePerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B25">
                <xsd:annotation>
                    <xsd:documentation>KilobecquerelPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B26">
                <xsd:annotation>
                    <xsd:documentation>Kilocoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B27">
                <xsd:annotation>
                    <xsd:documentation>KilocoulombPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B28">
                <xsd:annotation>
                    <xsd:documentation>KilocoulombPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B29">
                <xsd:annotation>
                    <xsd:documentation>Kiloelectronvolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B3">
                <xsd:annotation>
                    <xsd:documentation>BattingPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B31">
                <xsd:annotation>
                    <xsd:documentation>KilogramMetrePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B32">
                <xsd:annotation>
                    <xsd:documentation>KilogramMetreSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B33">
                <xsd:annotation>
                    <xsd:documentation>KilogramMetreSquaredPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B34">
                <xsd:annotation>
                    <xsd:documentation>KilogramPerCubicDecimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B35">
                <xsd:annotation>
                    <xsd:documentation>KilogramPerLitre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B36">
                <xsd:annotation>
                    <xsd:documentation>ThermochemicalCaloriePerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B37">
                <xsd:annotation>
                    <xsd:documentation>Kilogram-Force</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B38">
                <xsd:annotation>
                    <xsd:documentation>Kilogram-ForceMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B39">
                <xsd:annotation>
                    <xsd:documentation>Kilogram-ForceMetrePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B4">
                <xsd:annotation>
                    <xsd:documentation>Barrel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B40">
                <xsd:annotation>
                    <xsd:documentation>Kilogram-ForcePerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B41">
                <xsd:annotation>
                    <xsd:documentation>KilojoulePerKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B42">
                <xsd:annotation>
                    <xsd:documentation>KilojoulePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B43">
                <xsd:annotation>
                    <xsd:documentation>KilojoulePerKilogramKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B44">
                <xsd:annotation>
                    <xsd:documentation>KilojoulePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B45">
                <xsd:annotation>
                    <xsd:documentation>Kilomole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B46">
                <xsd:annotation>
                    <xsd:documentation>KilomolePerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B47">
                <xsd:annotation>
                    <xsd:documentation>Kilonewton</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B48">
                <xsd:annotation>
                    <xsd:documentation>KilonewtonMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B49">
                <xsd:annotation>
                    <xsd:documentation>Kiloohm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B5">
                <xsd:annotation>
                    <xsd:documentation>Billet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B50">
                <xsd:annotation>
                    <xsd:documentation>KiloohmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B51">
                <xsd:annotation>
                    <xsd:documentation>Kilopond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B52">
                <xsd:annotation>
                    <xsd:documentation>Kilosecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B53">
                <xsd:annotation>
                    <xsd:documentation>Kilosiemens</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B54">
                <xsd:annotation>
                    <xsd:documentation>KilosiemensPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B55">
                <xsd:annotation>
                    <xsd:documentation>KilovoltPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B56">
                <xsd:annotation>
                    <xsd:documentation>KiloweberPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B57">
                <xsd:annotation>
                    <xsd:documentation>LightYear</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B58">
                <xsd:annotation>
                    <xsd:documentation>LitrePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B59">
                <xsd:annotation>
                    <xsd:documentation>LumenHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B6">
                <xsd:annotation>
                    <xsd:documentation>Bun</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B60">
                <xsd:annotation>
                    <xsd:documentation>LumenPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B61">
                <xsd:annotation>
                    <xsd:documentation>LumenPerWatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B62">
                <xsd:annotation>
                    <xsd:documentation>LumenSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B63">
                <xsd:annotation>
                    <xsd:documentation>LuxHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B64">
                <xsd:annotation>
                    <xsd:documentation>LuxSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B65">
                <xsd:annotation>
                    <xsd:documentation>Maxwell</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B66">
                <xsd:annotation>
                    <xsd:documentation>MegaamperePerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B67">
                <xsd:annotation>
                    <xsd:documentation>MegabecquerelPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B69">
                <xsd:annotation>
                    <xsd:documentation>MegacoulombPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B7">
                <xsd:annotation>
                    <xsd:documentation>Cycle</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B70">
                <xsd:annotation>
                    <xsd:documentation>MegacoulombPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B71">
                <xsd:annotation>
                    <xsd:documentation>Megaelectronvolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B72">
                <xsd:annotation>
                    <xsd:documentation>MegagramPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B73">
                <xsd:annotation>
                    <xsd:documentation>Meganewton</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B74">
                <xsd:annotation>
                    <xsd:documentation>MeganewtonMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B75">
                <xsd:annotation>
                    <xsd:documentation>Megaohm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B76">
                <xsd:annotation>
                    <xsd:documentation>MegaohmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B77">
                <xsd:annotation>
                    <xsd:documentation>MegasiemensPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B78">
                <xsd:annotation>
                    <xsd:documentation>Megavolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B79">
                <xsd:annotation>
                    <xsd:documentation>MegavoltPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B8">
                <xsd:annotation>
                    <xsd:documentation>JoulePerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B81">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalMetreSquaredReciprocalSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B83">
                <xsd:annotation>
                    <xsd:documentation>MetreToTheFourthPower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B84">
                <xsd:annotation>
                    <xsd:documentation>Microampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B85">
                <xsd:annotation>
                    <xsd:documentation>Microbar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B86">
                <xsd:annotation>
                    <xsd:documentation>Microcoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B87">
                <xsd:annotation>
                    <xsd:documentation>MicrocoulombPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B88">
                <xsd:annotation>
                    <xsd:documentation>MicrocoulombPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B89">
                <xsd:annotation>
                    <xsd:documentation>MicrofaradPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B9">
                <xsd:annotation>
                    <xsd:documentation>Batt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B90">
                <xsd:annotation>
                    <xsd:documentation>Microhenry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B91">
                <xsd:annotation>
                    <xsd:documentation>MicrohenryPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B92">
                <xsd:annotation>
                    <xsd:documentation>Micronewton</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B93">
                <xsd:annotation>
                    <xsd:documentation>MicronewtonMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B94">
                <xsd:annotation>
                    <xsd:documentation>Microohm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B95">
                <xsd:annotation>
                    <xsd:documentation>MicroohmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B96">
                <xsd:annotation>
                    <xsd:documentation>Micropascal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B97">
                <xsd:annotation>
                    <xsd:documentation>Microradian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B98">
                <xsd:annotation>
                    <xsd:documentation>Microsecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="B99">
                <xsd:annotation>
                    <xsd:documentation>Microsiemens</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BAR">
                <xsd:annotation>
                    <xsd:documentation>Bar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BB">
                <xsd:annotation>
                    <xsd:documentation>BaseBox</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BD">
                <xsd:annotation>
                    <xsd:documentation>Board</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BE">
                <xsd:annotation>
                    <xsd:documentation>Bundle</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BFT">
                <xsd:annotation>
                    <xsd:documentation>BoardFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BG">
                <xsd:annotation>
                    <xsd:documentation>Bag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BH">
                <xsd:annotation>
                    <xsd:documentation>Brush</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BHP">
                <xsd:annotation>
                    <xsd:documentation>BrakeHorsePower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BIL">
                <xsd:annotation>
                    <xsd:documentation>Billion-Eur</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BJ">
                <xsd:annotation>
                    <xsd:documentation>Bucket</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BK">
                <xsd:annotation>
                    <xsd:documentation>Basket</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BL">
                <xsd:annotation>
                    <xsd:documentation>Bale</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BLD">
                <xsd:annotation>
                    <xsd:documentation>DryBarrel-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BLL">
                <xsd:annotation>
                    <xsd:documentation>Barrel-USPetroleumEtc</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BO">
                <xsd:annotation>
                    <xsd:documentation>Bottle</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BP">
                <xsd:annotation>
                    <xsd:documentation>100BoardFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BQL">
                <xsd:annotation>
                    <xsd:documentation>Becquerel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BT">
                <xsd:annotation>
                    <xsd:documentation>Bolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BTU">
                <xsd:annotation>
                    <xsd:documentation>BritishThermalUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BU">
                <xsd:annotation>
                    <xsd:documentation>Bushel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BUA">
                <xsd:annotation>
                    <xsd:documentation>Bushel-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BUI">
                <xsd:annotation>
                    <xsd:documentation>Bushel-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BW">
                <xsd:annotation>
                    <xsd:documentation>BaseWeight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BX">
                <xsd:annotation>
                    <xsd:documentation>Box</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BZ">
                <xsd:annotation>
                    <xsd:documentation>MillionBTUs</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C0">
                <xsd:annotation>
                    <xsd:documentation>Call</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C1">
                <xsd:annotation>
                    <xsd:documentation>CompositeProductPounds-TotalWeight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C10">
                <xsd:annotation>
                    <xsd:documentation>Millifarad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C11">
                <xsd:annotation>
                    <xsd:documentation>Milligal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C12">
                <xsd:annotation>
                    <xsd:documentation>MilligramPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C13">
                <xsd:annotation>
                    <xsd:documentation>Milligray</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C14">
                <xsd:annotation>
                    <xsd:documentation>Millihenry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C15">
                <xsd:annotation>
                    <xsd:documentation>Millijoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C16">
                <xsd:annotation>
                    <xsd:documentation>MillimetrePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C17">
                <xsd:annotation>
                    <xsd:documentation>MillimetreSquaredPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C18">
                <xsd:annotation>
                    <xsd:documentation>Millimole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C19">
                <xsd:annotation>
                    <xsd:documentation>MolePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C2">
                <xsd:annotation>
                    <xsd:documentation>Carset</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C20">
                <xsd:annotation>
                    <xsd:documentation>Millinewton</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C22">
                <xsd:annotation>
                    <xsd:documentation>MillinewtonPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C23">
                <xsd:annotation>
                    <xsd:documentation>MilliohmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C24">
                <xsd:annotation>
                    <xsd:documentation>MillipascalSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C25">
                <xsd:annotation>
                    <xsd:documentation>Milliradian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C26">
                <xsd:annotation>
                    <xsd:documentation>Millisecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C27">
                <xsd:annotation>
                    <xsd:documentation>Millisiemens</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C28">
                <xsd:annotation>
                    <xsd:documentation>Millisievert</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C29">
                <xsd:annotation>
                    <xsd:documentation>Millitesla</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C3">
                <xsd:annotation>
                    <xsd:documentation>MicrovoltPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C30">
                <xsd:annotation>
                    <xsd:documentation>MillivoltPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C31">
                <xsd:annotation>
                    <xsd:documentation>Milliwatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C32">
                <xsd:annotation>
                    <xsd:documentation>MilliwattPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C33">
                <xsd:annotation>
                    <xsd:documentation>Milliweber</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C34">
                <xsd:annotation>
                    <xsd:documentation>Mole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C35">
                <xsd:annotation>
                    <xsd:documentation>MolePerCubicDecimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C36">
                <xsd:annotation>
                    <xsd:documentation>MolePerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C38">
                <xsd:annotation>
                    <xsd:documentation>MolePerLitre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C39">
                <xsd:annotation>
                    <xsd:documentation>Nanoampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C4">
                <xsd:annotation>
                    <xsd:documentation>Carload</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C40">
                <xsd:annotation>
                    <xsd:documentation>Nanocoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C41">
                <xsd:annotation>
                    <xsd:documentation>Nanofarad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C42">
                <xsd:annotation>
                    <xsd:documentation>NanofaradPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C43">
                <xsd:annotation>
                    <xsd:documentation>Nanohenry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C44">
                <xsd:annotation>
                    <xsd:documentation>NanohenryPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C45">
                <xsd:annotation>
                    <xsd:documentation>Nanometre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C46">
                <xsd:annotation>
                    <xsd:documentation>NanoohmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C47">
                <xsd:annotation>
                    <xsd:documentation>Nanosecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C48">
                <xsd:annotation>
                    <xsd:documentation>Nanotesla</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C49">
                <xsd:annotation>
                    <xsd:documentation>Nanowatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C5">
                <xsd:annotation>
                    <xsd:documentation>Cost</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C50">
                <xsd:annotation>
                    <xsd:documentation>Neper</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C51">
                <xsd:annotation>
                    <xsd:documentation>NeperPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C52">
                <xsd:annotation>
                    <xsd:documentation>Picometre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C53">
                <xsd:annotation>
                    <xsd:documentation>NewtonMetreSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C54">
                <xsd:annotation>
                    <xsd:documentation>NewtonMetreSquaredKilogramSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C55">
                <xsd:annotation>
                    <xsd:documentation>NewtonPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C56">
                <xsd:annotation>
                    <xsd:documentation>NewtonPerSquareMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C57">
                <xsd:annotation>
                    <xsd:documentation>NewtonSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C58">
                <xsd:annotation>
                    <xsd:documentation>NewtonSecondPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C59">
                <xsd:annotation>
                    <xsd:documentation>Octave</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C6">
                <xsd:annotation>
                    <xsd:documentation>Cell</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C60">
                <xsd:annotation>
                    <xsd:documentation>OhmCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C61">
                <xsd:annotation>
                    <xsd:documentation>OhmMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C62">
                <xsd:annotation>
                    <xsd:documentation>One, Piece, Unit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C63">
                <xsd:annotation>
                    <xsd:documentation>Parsec</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C64">
                <xsd:annotation>
                    <xsd:documentation>PascalPerKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C65">
                <xsd:annotation>
                    <xsd:documentation>PascalSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C66">
                <xsd:annotation>
                    <xsd:documentation>PascalSecondPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C67">
                <xsd:annotation>
                    <xsd:documentation>PascalSecondPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C68">
                <xsd:annotation>
                    <xsd:documentation>Petajoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C69">
                <xsd:annotation>
                    <xsd:documentation>Phon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C7">
                <xsd:annotation>
                    <xsd:documentation>Centipoise</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C70">
                <xsd:annotation>
                    <xsd:documentation>Picoampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C71">
                <xsd:annotation>
                    <xsd:documentation>Picocoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C72">
                <xsd:annotation>
                    <xsd:documentation>PicofaradPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C73">
                <xsd:annotation>
                    <xsd:documentation>Picohenry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C75">
                <xsd:annotation>
                    <xsd:documentation>Picowatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C76">
                <xsd:annotation>
                    <xsd:documentation>PicowattPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C77">
                <xsd:annotation>
                    <xsd:documentation>PoundGage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C78">
                <xsd:annotation>
                    <xsd:documentation>Pound-Force</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C8">
                <xsd:annotation>
                    <xsd:documentation>MillicoulombPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C80">
                <xsd:annotation>
                    <xsd:documentation>Rad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C81">
                <xsd:annotation>
                    <xsd:documentation>Radian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C82">
                <xsd:annotation>
                    <xsd:documentation>RadianMeterSquaredPerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C83">
                <xsd:annotation>
                    <xsd:documentation>RadianMetreSquaredPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C84">
                <xsd:annotation>
                    <xsd:documentation>RadianPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C85">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalAngstrom</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C86">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C87">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalCubicMetrePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C88">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalElectronVoltPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C89">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalHenry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C9">
                <xsd:annotation>
                    <xsd:documentation>CoilGroup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C90">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalJoulePerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C91">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalKelvinOrKelvinToThePower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C92">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C93">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C94">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C95">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C96">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalPascalOrPascalToThePowerMinusOne</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C97">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C98">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalSecondPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="C99">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalSecondPerMetreSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CA">
                <xsd:annotation>
                    <xsd:documentation>Can</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CCT">
                <xsd:annotation>
                    <xsd:documentation>CarryingCapacityInMetricTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CD">
                <xsd:annotation>
                    <xsd:documentation>Carat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CDL">
                <xsd:annotation>
                    <xsd:documentation>Candela</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CE">
                <xsd:annotation>
                    <xsd:documentation>Centigrade</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CEL">
                <xsd:annotation>
                    <xsd:documentation>DegreeCelsius</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CEN">
                <xsd:annotation>
                    <xsd:documentation>Hundred</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CG">
                <xsd:annotation>
                    <xsd:documentation>Card</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CGM">
                <xsd:annotation>
                    <xsd:documentation>Centigram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CH">
                <xsd:annotation>
                    <xsd:documentation>Container</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CJ">
                <xsd:annotation>
                    <xsd:documentation>Cone</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CK">
                <xsd:annotation>
                    <xsd:documentation>Connector</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CKG">
                <xsd:annotation>
                    <xsd:documentation>CoulombPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CL">
                <xsd:annotation>
                    <xsd:documentation>Coil</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CLF">
                <xsd:annotation>
                    <xsd:documentation>HundredLeave</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CLT">
                <xsd:annotation>
                    <xsd:documentation>Centiliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CMK">
                <xsd:annotation>
                    <xsd:documentation>SquareCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CMQ">
                <xsd:annotation>
                    <xsd:documentation>CubicCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CMT">
                <xsd:annotation>
                    <xsd:documentation>Centimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CNP">
                <xsd:annotation>
                    <xsd:documentation>100-Pack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CNT">
                <xsd:annotation>
                    <xsd:documentation>Cental-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CO">
                <xsd:annotation>
                    <xsd:documentation>Carboy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="COU">
                <xsd:annotation>
                    <xsd:documentation>Coulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CQ">
                <xsd:annotation>
                    <xsd:documentation>Cartridge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CR">
                <xsd:annotation>
                    <xsd:documentation>Crate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CS">
                <xsd:annotation>
                    <xsd:documentation>Case</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CT">
                <xsd:annotation>
                    <xsd:documentation>Carton</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CTM">
                <xsd:annotation>
                    <xsd:documentation>MetricCarat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CU">
                <xsd:annotation>
                    <xsd:documentation>Cup</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CUR">
                <xsd:annotation>
                    <xsd:documentation>Curie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CV">
                <xsd:annotation>
                    <xsd:documentation>Cover</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CWA">
                <xsd:annotation>
                    <xsd:documentation>HundredPounds-CWT/HundredWeight-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CWI">
                <xsd:annotation>
                    <xsd:documentation>HundredWeight-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CY">
                <xsd:annotation>
                    <xsd:documentation>Cylinder</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CZ">
                <xsd:annotation>
                    <xsd:documentation>Combo</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D1">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalSecondPerSteradian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D10">
                <xsd:annotation>
                    <xsd:documentation>SiemensPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D12">
                <xsd:annotation>
                    <xsd:documentation>SiemensSquareMetrePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D13">
                <xsd:annotation>
                    <xsd:documentation>Sievert</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D14">
                <xsd:annotation>
                    <xsd:documentation>ThousandLinearYards</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D16">
                <xsd:annotation>
                    <xsd:documentation>SquareCentimetrePerErg</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D17">
                <xsd:annotation>
                    <xsd:documentation>SquareCentimetrePerSteradianErg</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D18">
                <xsd:annotation>
                    <xsd:documentation>MetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D19">
                <xsd:annotation>
                    <xsd:documentation>SquareMetreKelvinPerWatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D2">
                <xsd:annotation>
                    <xsd:documentation>ReciprocalSecondPerSteradianMetreSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D20">
                <xsd:annotation>
                    <xsd:documentation>SquareMetrePerJoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D21">
                <xsd:annotation>
                    <xsd:documentation>SquareMetrePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D22">
                <xsd:annotation>
                    <xsd:documentation>SquareMetrePerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D23">
                <xsd:annotation>
                    <xsd:documentation>PenGrams-Protein</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D24">
                <xsd:annotation>
                    <xsd:documentation>SquareMetrePerSteradian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D25">
                <xsd:annotation>
                    <xsd:documentation>SquareMetrePerSteradianJoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D26">
                <xsd:annotation>
                    <xsd:documentation>SquareMetrePerVoltSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D27">
                <xsd:annotation>
                    <xsd:documentation>Steradian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D28">
                <xsd:annotation>
                    <xsd:documentation>Syphon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D29">
                <xsd:annotation>
                    <xsd:documentation>Terahertz</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D30">
                <xsd:annotation>
                    <xsd:documentation>Terajoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D31">
                <xsd:annotation>
                    <xsd:documentation>Terawatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D33">
                <xsd:annotation>
                    <xsd:documentation>Tesla</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D34">
                <xsd:annotation>
                    <xsd:documentation>Tex</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D35">
                <xsd:annotation>
                    <xsd:documentation>ThermochemicalCalorie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D37">
                <xsd:annotation>
                    <xsd:documentation>ThermochemicalCaloriePerGramKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D38">
                <xsd:annotation>
                    <xsd:documentation>ThermochemicalCaloriePerSecondCentimetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D39">
                <xsd:annotation>
                    <xsd:documentation>ThermochemicalCaloriePerSecondSquareCentimetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D40">
                <xsd:annotation>
                    <xsd:documentation>ThousandLitre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D41">
                <xsd:annotation>
                    <xsd:documentation>TonnePerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D42">
                <xsd:annotation>
                    <xsd:documentation>TropicalYear</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D43">
                <xsd:annotation>
                    <xsd:documentation>UnifiedAtomicMassUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D44">
                <xsd:annotation>
                    <xsd:documentation>Var</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D45">
                <xsd:annotation>
                    <xsd:documentation>VoltSquaredPerKelvinSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D46">
                <xsd:annotation>
                    <xsd:documentation>Volt-Ampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D47">
                <xsd:annotation>
                    <xsd:documentation>VoltPerCentimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D48">
                <xsd:annotation>
                    <xsd:documentation>VoltPerKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D49">
                <xsd:annotation>
                    <xsd:documentation>MillivoltPerKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D5">
                <xsd:annotation>
                    <xsd:documentation>KilogramPerSquareCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D50">
                <xsd:annotation>
                    <xsd:documentation>VoltPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D51">
                <xsd:annotation>
                    <xsd:documentation>VoltPerMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D52">
                <xsd:annotation>
                    <xsd:documentation>WattPerKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D53">
                <xsd:annotation>
                    <xsd:documentation>WattPerMetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D54">
                <xsd:annotation>
                    <xsd:documentation>WattPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D55">
                <xsd:annotation>
                    <xsd:documentation>WattPerSquareMetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D56">
                <xsd:annotation>
                    <xsd:documentation>WattPerSquareMetreKelvinToTheFourthPower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D57">
                <xsd:annotation>
                    <xsd:documentation>WattPerSteradian</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D58">
                <xsd:annotation>
                    <xsd:documentation>WattPerSteradianSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D59">
                <xsd:annotation>
                    <xsd:documentation>WeberPerMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D6">
                <xsd:annotation>
                    <xsd:documentation>RontgenPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D60">
                <xsd:annotation>
                    <xsd:documentation>WeberPerMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D63">
                <xsd:annotation>
                    <xsd:documentation>Book</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D64">
                <xsd:annotation>
                    <xsd:documentation>Block</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D65">
                <xsd:annotation>
                    <xsd:documentation>Round</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D66">
                <xsd:annotation>
                    <xsd:documentation>Cassette</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D67">
                <xsd:annotation>
                    <xsd:documentation>DollarPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D69">
                <xsd:annotation>
                    <xsd:documentation>InchToTheFourthPower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D7">
                <xsd:annotation>
                    <xsd:documentation>Sandwich</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D70">
                <xsd:annotation>
                    <xsd:documentation>InternationalTableCalorie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D71">
                <xsd:annotation>
                    <xsd:documentation>InternationalTableCaloriePerSecondCentimetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D72">
                <xsd:annotation>
                    <xsd:documentation>InternationalTableCaloriePerSecondSquareCentimetreKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D73">
                <xsd:annotation>
                    <xsd:documentation>JouleSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D74">
                <xsd:annotation>
                    <xsd:documentation>KilogramPerMole</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D75">
                <xsd:annotation>
                    <xsd:documentation>InternationalTableCaloriePerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D76">
                <xsd:annotation>
                    <xsd:documentation>InternationalTableCaloriePerGramKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D77">
                <xsd:annotation>
                    <xsd:documentation>Megacoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D79">
                <xsd:annotation>
                    <xsd:documentation>Beam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D8">
                <xsd:annotation>
                    <xsd:documentation>DraizeScore</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D80">
                <xsd:annotation>
                    <xsd:documentation>Microwatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D81">
                <xsd:annotation>
                    <xsd:documentation>Microtesla</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D82">
                <xsd:annotation>
                    <xsd:documentation>Microvolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D83">
                <xsd:annotation>
                    <xsd:documentation>MillinewtonMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D85">
                <xsd:annotation>
                    <xsd:documentation>MicrowattPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D86">
                <xsd:annotation>
                    <xsd:documentation>Millicoulomb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D87">
                <xsd:annotation>
                    <xsd:documentation>MillimolePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D88">
                <xsd:annotation>
                    <xsd:documentation>MillicoulombPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D89">
                <xsd:annotation>
                    <xsd:documentation>MillicoulombPerSquareMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D9">
                <xsd:annotation>
                    <xsd:documentation>DynePerSquareCentemeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D90">
                <xsd:annotation>
                    <xsd:documentation>CubicMeters-Net</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D91">
                <xsd:annotation>
                    <xsd:documentation>Rem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D92">
                <xsd:annotation>
                    <xsd:documentation>Band</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D93">
                <xsd:annotation>
                    <xsd:documentation>SecondPerCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D94">
                <xsd:annotation>
                    <xsd:documentation>SecondPerRadianCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D95">
                <xsd:annotation>
                    <xsd:documentation>JoulePerGram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D96">
                <xsd:annotation>
                    <xsd:documentation>PoundGross</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D97">
                <xsd:annotation>
                    <xsd:documentation>PalletPerUnitLoad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D98">
                <xsd:annotation>
                    <xsd:documentation>MassPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="D99">
                <xsd:annotation>
                    <xsd:documentation>Sleeve</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DAA">
                <xsd:annotation>
                    <xsd:documentation>Decare</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DAD">
                <xsd:annotation>
                    <xsd:documentation>TenDay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DAY">
                <xsd:annotation>
                    <xsd:documentation>Day</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DB">
                <xsd:annotation>
                    <xsd:documentation>DryPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DC">
                <xsd:annotation>
                    <xsd:documentation>Disk</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DD">
                <xsd:annotation>
                    <xsd:documentation>Degree</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DE">
                <xsd:annotation>
                    <xsd:documentation>Deal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DEC">
                <xsd:annotation>
                    <xsd:documentation>Decade</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DF">
                <xsd:annotation>
                    <xsd:documentation>Dram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DG">
                <xsd:annotation>
                    <xsd:documentation>Decigram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DH">
                <xsd:annotation>
                    <xsd:documentation>Miles</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DI">
                <xsd:annotation>
                    <xsd:documentation>Dispenser</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DJ">
                <xsd:annotation>
                    <xsd:documentation>Decagram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DLT">
                <xsd:annotation>
                    <xsd:documentation>Deciliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DMK">
                <xsd:annotation>
                    <xsd:documentation>SquareDecimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DMQ">
                <xsd:annotation>
                    <xsd:documentation>CubicDecimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DMT">
                <xsd:annotation>
                    <xsd:documentation>Decimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DN">
                <xsd:annotation>
                    <xsd:documentation>DeciNewton-Meter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DO">
                <xsd:annotation>
                    <xsd:documentation>Dollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DPC">
                <xsd:annotation>
                    <xsd:documentation>DozenPiece</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DPR">
                <xsd:annotation>
                    <xsd:documentation>DozenPair</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DPT">
                <xsd:annotation>
                    <xsd:documentation>DisplacementTonnage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DQ">
                <xsd:annotation>
                    <xsd:documentation>DataRecord</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DR">
                <xsd:annotation>
                    <xsd:documentation>Drum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DRA">
                <xsd:annotation>
                    <xsd:documentation>Dram-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DRI">
                <xsd:annotation>
                    <xsd:documentation>Dram-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DRL">
                <xsd:annotation>
                    <xsd:documentation>DozenRoll</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DRM">
                <xsd:annotation>
                    <xsd:documentation>Drachm-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DS">
                <xsd:annotation>
                    <xsd:documentation>Display</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DT">
                <xsd:annotation>
                    <xsd:documentation>DryTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DTN">
                <xsd:annotation>
                    <xsd:documentation>Decitonne</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DU">
                <xsd:annotation>
                    <xsd:documentation>Dyne</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DWT">
                <xsd:annotation>
                    <xsd:documentation>PennyWeight</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DX">
                <xsd:annotation>
                    <xsd:documentation>DynesPerCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DY">
                <xsd:annotation>
                    <xsd:documentation>DirectoryBook</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DZN">
                <xsd:annotation>
                    <xsd:documentation>Dozen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DZP">
                <xsd:annotation>
                    <xsd:documentation>DozenPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="E2">
                <xsd:annotation>
                    <xsd:documentation>Belt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="E3">
                <xsd:annotation>
                    <xsd:documentation>Trailer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="E4">
                <xsd:annotation>
                    <xsd:documentation>GrossKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="E5">
                <xsd:annotation>
                    <xsd:documentation>MetricLongTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="E9">
                <xsd:annotation>
                    <xsd:documentation>English</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EA">
                <xsd:annotation>
                    <xsd:documentation>Each</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EB">
                <xsd:annotation>
                    <xsd:documentation>ElectronicMailBox</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EC">
                <xsd:annotation>
                    <xsd:documentation>EachPerMonth</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EP">
                <xsd:annotation>
                    <xsd:documentation>ElevenPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EQ">
                <xsd:annotation>
                    <xsd:documentation>EquivalentGallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EV">
                <xsd:annotation>
                    <xsd:documentation>Envelope</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EX">
                <xsd:annotation>
                    <xsd:documentation>Feet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EZ">
                <xsd:annotation>
                    <xsd:documentation>FeetAndDecimal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="F1">
                <xsd:annotation>
                    <xsd:documentation>ThousandCubicFeetPerDay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="F9">
                <xsd:annotation>
                    <xsd:documentation>FibersPerCubicCentimeterOfAir</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FAH">
                <xsd:annotation>
                    <xsd:documentation>DegreeFahrenheit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FAR">
                <xsd:annotation>
                    <xsd:documentation>Farad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FB">
                <xsd:annotation>
                    <xsd:documentation>Field</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FC">
                <xsd:annotation>
                    <xsd:documentation>1000CubicFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FD">
                <xsd:annotation>
                    <xsd:documentation>MillionParticlePerCubicFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FE">
                <xsd:annotation>
                    <xsd:documentation>TrackFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FF">
                <xsd:annotation>
                    <xsd:documentation>HundredCubicMeters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FG">
                <xsd:annotation>
                    <xsd:documentation>TransdermalPatch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FH">
                <xsd:annotation>
                    <xsd:documentation>Micromolar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FL">
                <xsd:annotation>
                    <xsd:documentation>FlakeTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FM">
                <xsd:annotation>
                    <xsd:documentation>MillionCubicFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FO">
                <xsd:annotation>
                    <xsd:documentation>FluidOunce</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FOT">
                <xsd:annotation>
                    <xsd:documentation>Foot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FP">
                <xsd:annotation>
                    <xsd:documentation>PoundPerSquareFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FR">
                <xsd:annotation>
                    <xsd:documentation>FootPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FS">
                <xsd:annotation>
                    <xsd:documentation>FootPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FTK">
                <xsd:annotation>
                    <xsd:documentation>SquareFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FTQ">
                <xsd:annotation>
                    <xsd:documentation>CubicFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FZ">
                <xsd:annotation>
                    <xsd:documentation>FluidOunce-Imperial</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="G2">
                <xsd:annotation>
                    <xsd:documentation>USGallonsPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="G3">
                <xsd:annotation>
                    <xsd:documentation>ImperialGallonsPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="G5">
                <xsd:annotation>
                    <xsd:documentation>Gill-Imperial</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="G7">
                <xsd:annotation>
                    <xsd:documentation>MicroficheSheet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GB">
                <xsd:annotation>
                    <xsd:documentation>Gallon-US-PerDay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GBQ">
                <xsd:annotation>
                    <xsd:documentation>Gigabecquerel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GC">
                <xsd:annotation>
                    <xsd:documentation>GramPer100Gram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GD">
                <xsd:annotation>
                    <xsd:documentation>GrossBarrel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GE">
                <xsd:annotation>
                    <xsd:documentation>PoundPerGallon-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GF">
                <xsd:annotation>
                    <xsd:documentation>GramsPer100Centimeters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GFI">
                <xsd:annotation>
                    <xsd:documentation>GramOfFissileIsotope</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GG">
                <xsd:annotation>
                    <xsd:documentation>GreatGross-DozenGross</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GGR">
                <xsd:annotation>
                    <xsd:documentation>GreatGross</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GH">
                <xsd:annotation>
                    <xsd:documentation>HalfGallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GI">
                <xsd:annotation>
                    <xsd:documentation>ImperialGallons</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GIA">
                <xsd:annotation>
                    <xsd:documentation>Gill-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GII">
                <xsd:annotation>
                    <xsd:documentation>Gill-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GJ">
                <xsd:annotation>
                    <xsd:documentation>GramsPerMilliliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GK">
                <xsd:annotation>
                    <xsd:documentation>GramsPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GL">
                <xsd:annotation>
                    <xsd:documentation>GramsPerLiter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GLD">
                <xsd:annotation>
                    <xsd:documentation>DryGallon-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GLI">
                <xsd:annotation>
                    <xsd:documentation>Gallon-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GLL">
                <xsd:annotation>
                    <xsd:documentation>Gallon-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GM">
                <xsd:annotation>
                    <xsd:documentation>GramsPerSquareMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GN">
                <xsd:annotation>
                    <xsd:documentation>GrossGallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GO">
                <xsd:annotation>
                    <xsd:documentation>MilligramsPerSquareMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GP">
                <xsd:annotation>
                    <xsd:documentation>MilligramsPerCubicMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GQ">
                <xsd:annotation>
                    <xsd:documentation>MicrogramPerCubicMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GRM">
                <xsd:annotation>
                    <xsd:documentation>Gram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GRN">
                <xsd:annotation>
                    <xsd:documentation>Grain</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GRO">
                <xsd:annotation>
                    <xsd:documentation>Gross</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GRT">
                <xsd:annotation>
                    <xsd:documentation>GrossRegisterTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GT">
                <xsd:annotation>
                    <xsd:documentation>GrossTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GV">
                <xsd:annotation>
                    <xsd:documentation>Gigajoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GW">
                <xsd:annotation>
                    <xsd:documentation>GallonsPerThousandCubicFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GWH">
                <xsd:annotation>
                    <xsd:documentation>GigawattHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GY">
                <xsd:annotation>
                    <xsd:documentation>GrossYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GZ">
                <xsd:annotation>
                    <xsd:documentation>GageSystem</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="H1">
                <xsd:annotation>
                    <xsd:documentation>HalfPage-Electronic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="H2">
                <xsd:annotation>
                    <xsd:documentation>HalfLiter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HA">
                <xsd:annotation>
                    <xsd:documentation>Hank</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HAR">
                <xsd:annotation>
                    <xsd:documentation>Hectare</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HBA">
                <xsd:annotation>
                    <xsd:documentation>Hectobar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HBX">
                <xsd:annotation>
                    <xsd:documentation>HundredBoxes</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HC">
                <xsd:annotation>
                    <xsd:documentation>HundredCount</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HD">
                <xsd:annotation>
                    <xsd:documentation>HalfDozen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HE">
                <xsd:annotation>
                    <xsd:documentation>HundredthOfACarat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HF">
                <xsd:annotation>
                    <xsd:documentation>HundredFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HGM">
                <xsd:annotation>
                    <xsd:documentation>Hectogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HH">
                <xsd:annotation>
                    <xsd:documentation>HundredCubicFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HI">
                <xsd:annotation>
                    <xsd:documentation>HundredSheet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HIU">
                <xsd:annotation>
                    <xsd:documentation>HundredInternationalUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HJ">
                <xsd:annotation>
                    <xsd:documentation>Horsepower</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HK">
                <xsd:annotation>
                    <xsd:documentation>HundredKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HL">
                <xsd:annotation>
                    <xsd:documentation>HundredFeet-Linear</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HLT">
                <xsd:annotation>
                    <xsd:documentation>Hectoliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HM">
                <xsd:annotation>
                    <xsd:documentation>MilesPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HMQ">
                <xsd:annotation>
                    <xsd:documentation>MillionCubicMetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HMT">
                <xsd:annotation>
                    <xsd:documentation>Hectometer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HN">
                <xsd:annotation>
                    <xsd:documentation>MillimetersOfMercury</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HO">
                <xsd:annotation>
                    <xsd:documentation>HundredTroyOunce</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HP">
                <xsd:annotation>
                    <xsd:documentation>MillimeterOfWater</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HPA">
                <xsd:annotation>
                    <xsd:documentation>HectolitreOfPureAlcohol</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HS">
                <xsd:annotation>
                    <xsd:documentation>HundredSquareFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HT">
                <xsd:annotation>
                    <xsd:documentation>HalfHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HTZ">
                <xsd:annotation>
                    <xsd:documentation>Hertz</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HUR">
                <xsd:annotation>
                    <xsd:documentation>Hours</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HV">
                <xsd:annotation>
                    <xsd:documentation>HundredWeight-Short</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HW">
                <xsd:annotation>
                    <xsd:documentation>HundredWeight-Long</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HY">
                <xsd:annotation>
                    <xsd:documentation>HundredYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IA">
                <xsd:annotation>
                    <xsd:documentation>InchPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IB">
                <xsd:annotation>
                    <xsd:documentation>InchesPerSecond-VibrationVelocity</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IC">
                <xsd:annotation>
                    <xsd:documentation>CountPerInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IE">
                <xsd:annotation>
                    <xsd:documentation>Person</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IF">
                <xsd:annotation>
                    <xsd:documentation>InchesOfWater</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="II">
                <xsd:annotation>
                    <xsd:documentation>Column-Inches</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IL">
                <xsd:annotation>
                    <xsd:documentation>InchesPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IM">
                <xsd:annotation>
                    <xsd:documentation>Impression</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INH">
                <xsd:annotation>
                    <xsd:documentation>Inch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INK">
                <xsd:annotation>
                    <xsd:documentation>SquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="INQ">
                <xsd:annotation>
                    <xsd:documentation>CubicInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IP">
                <xsd:annotation>
                    <xsd:documentation>InsurancePolicy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IT">
                <xsd:annotation>
                    <xsd:documentation>CountsPerCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IU">
                <xsd:annotation>
                    <xsd:documentation>InchPerSecond-LinearSpeed</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IV">
                <xsd:annotation>
                    <xsd:documentation>InchPerSecondSquared-Acceleration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IW">
                <xsd:annotation>
                    <xsd:documentation>InchesPerSecondPerSecond-VibrationAcceleration</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="J2">
                <xsd:annotation>
                    <xsd:documentation>JoulePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JB">
                <xsd:annotation>
                    <xsd:documentation>Jumbo</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JE">
                <xsd:annotation>
                    <xsd:documentation>JoulePerKelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JG">
                <xsd:annotation>
                    <xsd:documentation>Jug</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JK">
                <xsd:annotation>
                    <xsd:documentation>MegajoulePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JM">
                <xsd:annotation>
                    <xsd:documentation>MegajoulePerCubicMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JO">
                <xsd:annotation>
                    <xsd:documentation>Joint</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JOU">
                <xsd:annotation>
                    <xsd:documentation>Joule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JR">
                <xsd:annotation>
                    <xsd:documentation>Jar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="K1">
                <xsd:annotation>
                    <xsd:documentation>KilowattDemand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="K2">
                <xsd:annotation>
                    <xsd:documentation>KilovoltAmpereReactiveDemand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="K3">
                <xsd:annotation>
                    <xsd:documentation>KilovoltAmpereReactiveHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="K5">
                <xsd:annotation>
                    <xsd:documentation>KilovoltAmpere-Reactive</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="K6">
                <xsd:annotation>
                    <xsd:documentation>Kiloliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KA">
                <xsd:annotation>
                    <xsd:documentation>Cake</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KB">
                <xsd:annotation>
                    <xsd:documentation>Kilocharacter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KBA">
                <xsd:annotation>
                    <xsd:documentation>Kilobar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KD">
                <xsd:annotation>
                    <xsd:documentation>KilogramsDecimal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KEL">
                <xsd:annotation>
                    <xsd:documentation>Kelvin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KF">
                <xsd:annotation>
                    <xsd:documentation>Kilopacket</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KG">
                <xsd:annotation>
                    <xsd:documentation>Keg</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KGM">
                <xsd:annotation>
                    <xsd:documentation>Kilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KGS">
                <xsd:annotation>
                    <xsd:documentation>KilogramPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KHZ">
                <xsd:annotation>
                    <xsd:documentation>Kilohertz</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KI">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerMillimeterWidth</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KJ">
                <xsd:annotation>
                    <xsd:documentation>Kilosegment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KJO">
                <xsd:annotation>
                    <xsd:documentation>Kilojoule</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KK">
                <xsd:annotation>
                    <xsd:documentation>100Kilograms</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KL">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KMH">
                <xsd:annotation>
                    <xsd:documentation>KilometersPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KMK">
                <xsd:annotation>
                    <xsd:documentation>SquareKilometre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KMQ">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerCubicMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KNI">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfNitrogen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KNS">
                <xsd:annotation>
                    <xsd:documentation>KilogramNamedSubstance</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KNT">
                <xsd:annotation>
                    <xsd:documentation>Knot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KO">
                <xsd:annotation>
                    <xsd:documentation>MillequivalenceCausticPotashPerGramOfProduct</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPA">
                <xsd:annotation>
                    <xsd:documentation>Kilopascal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPH">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfPotassiumHydroxideOrCausticPotash</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPO">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfPotassiumOxide</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KPP">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfPhosphorusPentoxideOrPhosphoricAnhydride</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KR">
                <xsd:annotation>
                    <xsd:documentation>Kiloroentgen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KS">
                <xsd:annotation>
                    <xsd:documentation>1000PoundsPerSquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KSD">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfSubstance90%Dry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KSH">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfSodiumHydroxideOrCausticSoda</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KT">
                <xsd:annotation>
                    <xsd:documentation>Kit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KMT">
                <xsd:annotation>
                    <xsd:documentation>Kilometre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KTN">
                <xsd:annotation>
                    <xsd:documentation>Kilotonne</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KUR">
                <xsd:annotation>
                    <xsd:documentation>KilogramOfUranium</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KVA">
                <xsd:annotation>
                    <xsd:documentation>Kilovolt-Ampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KVR">
                <xsd:annotation>
                    <xsd:documentation>Kilovar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KVT">
                <xsd:annotation>
                    <xsd:documentation>Kilovolt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KW">
                <xsd:annotation>
                    <xsd:documentation>KilogramsPerMillimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KWH">
                <xsd:annotation>
                    <xsd:documentation>KilowattHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KWT">
                <xsd:annotation>
                    <xsd:documentation>Kilowatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KX">
                <xsd:annotation>
                    <xsd:documentation>MillilitersPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="L2">
                <xsd:annotation>
                    <xsd:documentation>LitersPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LA">
                <xsd:annotation>
                    <xsd:documentation>PoundPerCubicInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LBR">
                <xsd:annotation>
                    <xsd:documentation>Pound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LBT">
                <xsd:annotation>
                    <xsd:documentation>TroyPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LC">
                <xsd:annotation>
                    <xsd:documentation>LinearCentimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LD">
                <xsd:annotation>
                    <xsd:documentation>LitersPerDay  UN/ECE - Litre Per Day</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LE">
                <xsd:annotation>
                    <xsd:documentation>Lite</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LEF">
                <xsd:annotation>
                    <xsd:documentation>Leaf</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LF">
                <xsd:annotation>
                    <xsd:documentation>LinearFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LG">
                <xsd:annotation>
                    <xsd:documentation>LongTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LH">
                <xsd:annotation>
                    <xsd:documentation>LabourHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LI">
                <xsd:annotation>
                    <xsd:documentation>LinearInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LJ">
                <xsd:annotation>
                    <xsd:documentation>LargeSpray</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LK">
                <xsd:annotation>
                    <xsd:documentation>Link</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LM">
                <xsd:annotation>
                    <xsd:documentation>LinearMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LN">
                <xsd:annotation>
                    <xsd:documentation>Length</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LO">
                <xsd:annotation>
                    <xsd:documentation>Lot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LP">
                <xsd:annotation>
                    <xsd:documentation>LiquidPounds</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LPA">
                <xsd:annotation>
                    <xsd:documentation>LitreOfPureAlcohol</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LR">
                <xsd:annotation>
                    <xsd:documentation>Layer</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LS">
                <xsd:annotation>
                    <xsd:documentation>LumpSum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LTN">
                <xsd:annotation>
                    <xsd:documentation>Ton-UKOrLongton-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LTR">
                <xsd:annotation>
                    <xsd:documentation>Liter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LUM">
                <xsd:annotation>
                    <xsd:documentation>Lumen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LUX">
                <xsd:annotation>
                    <xsd:documentation>Lux</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LX">
                <xsd:annotation>
                    <xsd:documentation>LinearYardsPerPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LY">
                <xsd:annotation>
                    <xsd:documentation>LinearYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M0">
                <xsd:annotation>
                    <xsd:documentation>MagneticTape</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M1">
                <xsd:annotation>
                    <xsd:documentation>MilligramsPerLiter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M2">
                <xsd:annotation>
                    <xsd:documentation>Millimeter-Actual</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M4">
                <xsd:annotation>
                    <xsd:documentation>MonetaryValue</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M5">
                <xsd:annotation>
                    <xsd:documentation>Microcurie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M7">
                <xsd:annotation>
                    <xsd:documentation>MicroInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="M9">
                <xsd:annotation>
                    <xsd:documentation>MillionBritishThermalUnitsPerOneThousandCubicFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MA">
                <xsd:annotation>
                    <xsd:documentation>MachinePerUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MAL">
                <xsd:annotation>
                    <xsd:documentation>MegaLitre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MAM">
                <xsd:annotation>
                    <xsd:documentation>Megametre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MAW">
                <xsd:annotation>
                    <xsd:documentation>Megawatt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MB">
                <xsd:annotation>
                    <xsd:documentation>Millimeter-Nominal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MBE">
                <xsd:annotation>
                    <xsd:documentation>ThousandStandardBrickEquivalent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MBF">
                <xsd:annotation>
                    <xsd:documentation>ThousandBoardFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MBR">
                <xsd:annotation>
                    <xsd:documentation>Millibar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MC">
                <xsd:annotation>
                    <xsd:documentation>Microgram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MCU">
                <xsd:annotation>
                    <xsd:documentation>Millicurie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MD">
                <xsd:annotation>
                    <xsd:documentation>AirDryMetricTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MF">
                <xsd:annotation>
                    <xsd:documentation>MilligramPerSquareFootPerSide</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MGM">
                <xsd:annotation>
                    <xsd:documentation>Milligram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MH">
                <xsd:annotation>
                    <xsd:documentation>Microns</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MHZ">
                <xsd:annotation>
                    <xsd:documentation>Megahertz</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MI">
                <xsd:annotation>
                    <xsd:documentation>Metric</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MIK">
                <xsd:annotation>
                    <xsd:documentation>SquareMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MIL">
                <xsd:annotation>
                    <xsd:documentation>Thousand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MIN">
                <xsd:annotation>
                    <xsd:documentation>Minute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MIO">
                <xsd:annotation>
                    <xsd:documentation>Million</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MIU">
                <xsd:annotation>
                    <xsd:documentation>MillionInternationalUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MK">
                <xsd:annotation>
                    <xsd:documentation>MilligramsPerSquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLD">
                <xsd:annotation>
                    <xsd:documentation>BillionsOfDollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MLT">
                <xsd:annotation>
                    <xsd:documentation>Milliliter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MMK">
                <xsd:annotation>
                    <xsd:documentation>SquareMillimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MMQ">
                <xsd:annotation>
                    <xsd:documentation>CubicMillimetre</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MMT">
                <xsd:annotation>
                    <xsd:documentation>Millimeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MON">
                <xsd:annotation>
                    <xsd:documentation>Months</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MPA">
                <xsd:annotation>
                    <xsd:documentation>Megapascal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MQ">
                <xsd:annotation>
                    <xsd:documentation>1000Meters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MQH">
                <xsd:annotation>
                    <xsd:documentation>CubicMeterPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MQS">
                <xsd:annotation>
                    <xsd:documentation>CubicMetrePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MSK">
                <xsd:annotation>
                    <xsd:documentation>MetrePerSecondSquared</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MT">
                <xsd:annotation>
                    <xsd:documentation>Mat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MTK">
                <xsd:annotation>
                    <xsd:documentation>SquareMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MTQ">
                <xsd:annotation>
                    <xsd:documentation>CubicMeter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MTR">
                <xsd:annotation>
                    <xsd:documentation>Meter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MTS">
                <xsd:annotation>
                    <xsd:documentation>MetrePerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MV">
                <xsd:annotation>
                    <xsd:documentation>NumberOfMults</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MVA">
                <xsd:annotation>
                    <xsd:documentation>Megavolt-Ampere</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MW">
                <xsd:annotation>
                    <xsd:documentation>MetricTonKilograms</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MWH">
                <xsd:annotation>
                    <xsd:documentation>MegawattHourOr1000KwH</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MX">
                <xsd:annotation>
                    <xsd:documentation>Mixed</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MY">
                <xsd:annotation>
                    <xsd:documentation>Millimeter-Average</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MZ">
                <xsd:annotation>
                    <xsd:documentation>Millimeter-Minimum</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="N1">
                <xsd:annotation>
                    <xsd:documentation>PenCalories</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="N2">
                <xsd:annotation>
                    <xsd:documentation>NumberOfLines</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="N3">
                <xsd:annotation>
                    <xsd:documentation>PrintPoint</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NA">
                <xsd:annotation>
                    <xsd:documentation>MilligramPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NAR">
                <xsd:annotation>
                    <xsd:documentation>NumberOfArticles</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NB">
                <xsd:annotation>
                    <xsd:documentation>Barge</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NBB">
                <xsd:annotation>
                    <xsd:documentation>NumberOfBobbins</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NC">
                <xsd:annotation>
                    <xsd:documentation>Car</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NCL">
                <xsd:annotation>
                    <xsd:documentation>NumberOfCells</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ND">
                <xsd:annotation>
                    <xsd:documentation>NetBarrel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NE">
                <xsd:annotation>
                    <xsd:documentation>NetLiters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NEW">
                <xsd:annotation>
                    <xsd:documentation>Newton</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NF">
                <xsd:annotation>
                    <xsd:documentation>Messages</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NG">
                <xsd:annotation>
                    <xsd:documentation>NetGallons</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NH">
                <xsd:annotation>
                    <xsd:documentation>MessageHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NI">
                <xsd:annotation>
                    <xsd:documentation>NetImperialGallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NIU">
                <xsd:annotation>
                    <xsd:documentation>NumberOfInternationalUnits</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NJ">
                <xsd:annotation>
                    <xsd:documentation>NumberOfScreens</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NL">
                <xsd:annotation>
                    <xsd:documentation>Load</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NMI">
                <xsd:annotation>
                    <xsd:documentation>NauticalMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NMP">
                <xsd:annotation>
                    <xsd:documentation>NumberOfPacks</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NN">
                <xsd:annotation>
                    <xsd:documentation>Train</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NPL">
                <xsd:annotation>
                    <xsd:documentation>NumberOfParcels</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NPR">
                <xsd:annotation>
                    <xsd:documentation>NumberOfPairs</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NPT">
                <xsd:annotation>
                    <xsd:documentation>NumberOfParts</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NQ">
                <xsd:annotation>
                    <xsd:documentation>Mho</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NR">
                <xsd:annotation>
                    <xsd:documentation>Micromho</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NRL">
                <xsd:annotation>
                    <xsd:documentation>NumberOfRolls</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NT">
                <xsd:annotation>
                    <xsd:documentation>NetTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NTT">
                <xsd:annotation>
                    <xsd:documentation>NetRegisterTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NU">
                <xsd:annotation>
                    <xsd:documentation>Newton-Meter</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NV">
                <xsd:annotation>
                    <xsd:documentation>Vehicle</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NX">
                <xsd:annotation>
                    <xsd:documentation>PartsPerThousand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NY">
                <xsd:annotation>
                    <xsd:documentation>PoundPerAirDryMetricTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OA">
                <xsd:annotation>
                    <xsd:documentation>Panel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OHM">
                <xsd:annotation>
                    <xsd:documentation>Ohm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ON">
                <xsd:annotation>
                    <xsd:documentation>OuncePerSquareYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ONZ">
                <xsd:annotation>
                    <xsd:documentation>Ounce</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OP">
                <xsd:annotation>
                    <xsd:documentation>TwoPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OT">
                <xsd:annotation>
                    <xsd:documentation>OvertimeHours</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OZ">
                <xsd:annotation>
                    <xsd:documentation>OunceAv</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OZA">
                <xsd:annotation>
                    <xsd:documentation>FluidOunce-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OZI">
                <xsd:annotation>
                    <xsd:documentation>FluidOunce-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P0">
                <xsd:annotation>
                    <xsd:documentation>Page-Electronic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P1">
                <xsd:annotation>
                    <xsd:documentation>Percent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P2">
                <xsd:annotation>
                    <xsd:documentation>PoundPerFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P3">
                <xsd:annotation>
                    <xsd:documentation>ThreePack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P4">
                <xsd:annotation>
                    <xsd:documentation>FourPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P5">
                <xsd:annotation>
                    <xsd:documentation>FivePack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P6">
                <xsd:annotation>
                    <xsd:documentation>SixPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P7">
                <xsd:annotation>
                    <xsd:documentation>SevenPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P8">
                <xsd:annotation>
                    <xsd:documentation>EightPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="P9">
                <xsd:annotation>
                    <xsd:documentation>NinePack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PA">
                <xsd:annotation>
                    <xsd:documentation>Packet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PAL">
                <xsd:annotation>
                    <xsd:documentation>Pascal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PB">
                <xsd:annotation>
                    <xsd:documentation>PairInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PD">
                <xsd:annotation>
                    <xsd:documentation>Pad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PE">
                <xsd:annotation>
                    <xsd:documentation>PoundEquivalent</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PF">
                <xsd:annotation>
                    <xsd:documentation>Pallet-Lift</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PG">
                <xsd:annotation>
                    <xsd:documentation>Plate</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PGL">
                <xsd:annotation>
                    <xsd:documentation>ProofGallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PI">
                <xsd:annotation>
                    <xsd:documentation>Pitch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PK">
                <xsd:annotation>
                    <xsd:documentation>Package</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PL">
                <xsd:annotation>
                    <xsd:documentation>Pail</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PM">
                <xsd:annotation>
                    <xsd:documentation>PoundPercentage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PN">
                <xsd:annotation>
                    <xsd:documentation>PoundNet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PO">
                <xsd:annotation>
                    <xsd:documentation>PoundPerInchOfLength</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PQ">
                <xsd:annotation>
                    <xsd:documentation>PagePerInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PR">
                <xsd:annotation>
                    <xsd:documentation>Pair</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PS">
                <xsd:annotation>
                    <xsd:documentation>PoundsPerSquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PT">
                <xsd:annotation>
                    <xsd:documentation>Pint-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PTD">
                <xsd:annotation>
                    <xsd:documentation>DryPint-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PTI">
                <xsd:annotation>
                    <xsd:documentation>Pint-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PTL">
                <xsd:annotation>
                    <xsd:documentation>LiquidPint-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PU">
                <xsd:annotation>
                    <xsd:documentation>Tray</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PV">
                <xsd:annotation>
                    <xsd:documentation>HalfPint</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PW">
                <xsd:annotation>
                    <xsd:documentation>PoundPerInchOfWidth</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PY">
                <xsd:annotation>
                    <xsd:documentation>PeckDry-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PZ">
                <xsd:annotation>
                    <xsd:documentation>PeckDry-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q1">
                <xsd:annotation>
                    <xsd:documentation>Quarter-Time</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q2">
                <xsd:annotation>
                    <xsd:documentation>PintUSDry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q3">
                <xsd:annotation>
                    <xsd:documentation>Meal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q4">
                <xsd:annotation>
                    <xsd:documentation>Fifty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q5">
                <xsd:annotation>
                    <xsd:documentation>Twenty-Five</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q6">
                <xsd:annotation>
                    <xsd:documentation>Thirty-Six</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Q7">
                <xsd:annotation>
                    <xsd:documentation>Twenty-Four</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QA">
                <xsd:annotation>
                    <xsd:documentation>Page-Facsimile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QAN">
                <xsd:annotation>
                    <xsd:documentation>Quarter-OfAYear</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QB">
                <xsd:annotation>
                    <xsd:documentation>Page-Hardcopy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QD">
                <xsd:annotation>
                    <xsd:documentation>QuarterDozen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QH">
                <xsd:annotation>
                    <xsd:documentation>QuarterHours</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QK">
                <xsd:annotation>
                    <xsd:documentation>QuarterKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QR">
                <xsd:annotation>
                    <xsd:documentation>Quire</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QS">
                <xsd:annotation>
                    <xsd:documentation>Quart</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QT">
                <xsd:annotation>
                    <xsd:documentation>Quart-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QTD">
                <xsd:annotation>
                    <xsd:documentation>DryQuart-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QTI">
                <xsd:annotation>
                    <xsd:documentation>Quart-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QTL">
                <xsd:annotation>
                    <xsd:documentation>LiquidQuart-US</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QTR">
                <xsd:annotation>
                    <xsd:documentation>Quarter-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="R1">
                <xsd:annotation>
                    <xsd:documentation>Pica</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="R4">
                <xsd:annotation>
                    <xsd:documentation>Calorie</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="R5">
                <xsd:annotation>
                    <xsd:documentation>ThousandsOfDollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="R6">
                <xsd:annotation>
                    <xsd:documentation>MillionsOfDollars</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="R9">
                <xsd:annotation>
                    <xsd:documentation>ThousandCubicMeters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RA">
                <xsd:annotation>
                    <xsd:documentation>Rack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RD">
                <xsd:annotation>
                    <xsd:documentation>Rod</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RG">
                <xsd:annotation>
                    <xsd:documentation>Ring</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RH">
                <xsd:annotation>
                    <xsd:documentation>RunningOrOperatingHours</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RK">
                <xsd:annotation>
                    <xsd:documentation>RollMetricMeasure</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RL">
                <xsd:annotation>
                    <xsd:documentation>Reel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RM">
                <xsd:annotation>
                    <xsd:documentation>Ream</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RN">
                <xsd:annotation>
                    <xsd:documentation>ReamMetricMeasure</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RO">
                <xsd:annotation>
                    <xsd:documentation>Roll</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RP">
                <xsd:annotation>
                    <xsd:documentation>PoundPerReam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RPM">
                <xsd:annotation>
                    <xsd:documentation>RevolutionsPerMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RPS">
                <xsd:annotation>
                    <xsd:documentation>RevolutionsPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RS">
                <xsd:annotation>
                    <xsd:documentation>Reset</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RT">
                <xsd:annotation>
                    <xsd:documentation>RevenueTonMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RU">
                <xsd:annotation>
                    <xsd:documentation>Run</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S3">
                <xsd:annotation>
                    <xsd:documentation>FootSquaredPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S4">
                <xsd:annotation>
                    <xsd:documentation>SquareMetersPerSecond</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S5">
                <xsd:annotation>
                    <xsd:documentation>SixtyFourthsOfAnInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S6">
                <xsd:annotation>
                    <xsd:documentation>Session</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S7">
                <xsd:annotation>
                    <xsd:documentation>StorageUnits</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="S8">
                <xsd:annotation>
                    <xsd:documentation>StandardAdvertisingUnit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SA">
                <xsd:annotation>
                    <xsd:documentation>Sack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SAN">
                <xsd:annotation>
                    <xsd:documentation>HalfYearOr6Months</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SCO">
                <xsd:annotation>
                    <xsd:documentation>Score</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SCR">
                <xsd:annotation>
                    <xsd:documentation>Scruple</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SD">
                <xsd:annotation>
                    <xsd:documentation>SolidPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SE">
                <xsd:annotation>
                    <xsd:documentation>Section</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SEC">
                <xsd:annotation>
                    <xsd:documentation>Second</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SET">
                <xsd:annotation>
                    <xsd:documentation>Set</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SG">
                <xsd:annotation>
                    <xsd:documentation>Segment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SHT">
                <xsd:annotation>
                    <xsd:documentation>ShippingTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SIE">
                <xsd:annotation>
                    <xsd:documentation>Siemens</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SK">
                <xsd:annotation>
                    <xsd:documentation>SplitTanktruck</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SL">
                <xsd:annotation>
                    <xsd:documentation>Slipsheet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SMI">
                <xsd:annotation>
                    <xsd:documentation>StatuteMile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SN">
                <xsd:annotation>
                    <xsd:documentation>SquareRod</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SO">
                <xsd:annotation>
                    <xsd:documentation>Spool</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SP">
                <xsd:annotation>
                    <xsd:documentation>ShelfPackage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SQ">
                <xsd:annotation>
                    <xsd:documentation>Square</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SR">
                <xsd:annotation>
                    <xsd:documentation>Strip</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SS">
                <xsd:annotation>
                    <xsd:documentation>SheetMetricMeasure</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SST">
                <xsd:annotation>
                    <xsd:documentation>ShortStandard-7200Matches</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ST">
                <xsd:annotation>
                    <xsd:documentation>Sheet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="STI">
                <xsd:annotation>
                    <xsd:documentation>Stone-UK</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="STN">
                <xsd:annotation>
                    <xsd:documentation>NetTon-2000Lb</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SV">
                <xsd:annotation>
                    <xsd:documentation>Skid</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SW">
                <xsd:annotation>
                    <xsd:documentation>Skein</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SX">
                <xsd:annotation>
                    <xsd:documentation>Shipment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T0">
                <xsd:annotation>
                    <xsd:documentation>TelecommunicationLineInService</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T1">
                <xsd:annotation>
                    <xsd:documentation>ThousandPoundGross</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T2">
                <xsd:annotation>
                    <xsd:documentation>ThousandthsOfAnInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T3">
                <xsd:annotation>
                    <xsd:documentation>ThousandPiece</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T4">
                <xsd:annotation>
                    <xsd:documentation>ThousandBag</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T5">
                <xsd:annotation>
                    <xsd:documentation>ThousandCasing</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T6">
                <xsd:annotation>
                    <xsd:documentation>ThousandGallons</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T7">
                <xsd:annotation>
                    <xsd:documentation>ThousandImpression</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T8">
                <xsd:annotation>
                    <xsd:documentation>ThousandLinearInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="T9">
                <xsd:annotation>
                    <xsd:documentation>ThousandKilowattHours</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TA">
                <xsd:annotation>
                    <xsd:documentation>TenthCubicFoot</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TAH">
                <xsd:annotation>
                    <xsd:documentation>KiloampereHourOrThousandAmpereHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TC">
                <xsd:annotation>
                    <xsd:documentation>Truckload</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TD">
                <xsd:annotation>
                    <xsd:documentation>Therm</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TE">
                <xsd:annotation>
                    <xsd:documentation>Tote</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TF">
                <xsd:annotation>
                    <xsd:documentation>TenSquareYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TI">
                <xsd:annotation>
                    <xsd:documentation>ThousandSquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TJ">
                <xsd:annotation>
                    <xsd:documentation>ThousandSquareCentimeters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TK">
                <xsd:annotation>
                    <xsd:documentation>Tank-Rectangular</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TL">
                <xsd:annotation>
                    <xsd:documentation>ThousandFeet-Linear</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TM">
                <xsd:annotation>
                    <xsd:documentation>ThousandFeet-Board</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TN">
                <xsd:annotation>
                    <xsd:documentation>Tin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TNE">
                <xsd:annotation>
                    <xsd:documentation>MetricTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TP">
                <xsd:annotation>
                    <xsd:documentation>TenPack</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TPR">
                <xsd:annotation>
                    <xsd:documentation>TenPair</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TQ">
                <xsd:annotation>
                    <xsd:documentation>ThousandFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TQD">
                <xsd:annotation>
                    <xsd:documentation>ThousandCubicMetrePerDay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TR">
                <xsd:annotation>
                    <xsd:documentation>TenSquareFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TRL">
                <xsd:annotation>
                    <xsd:documentation>Trillion-EUR</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TS">
                <xsd:annotation>
                    <xsd:documentation>ThousandSquareFeet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TSD">
                <xsd:annotation>
                    <xsd:documentation>TonneOfSubstance90%Dry</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TSH">
                <xsd:annotation>
                    <xsd:documentation>TonOfSteamPerHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TT">
                <xsd:annotation>
                    <xsd:documentation>ThousandLinearMeters</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TU">
                <xsd:annotation>
                    <xsd:documentation>Tube</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TV">
                <xsd:annotation>
                    <xsd:documentation>ThousandKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TW">
                <xsd:annotation>
                    <xsd:documentation>ThousandSheets</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TY">
                <xsd:annotation>
                    <xsd:documentation>Tank-Cylindrical</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="U1">
                <xsd:annotation>
                    <xsd:documentation>Treatment</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="U2">
                <xsd:annotation>
                    <xsd:documentation>Tablet</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="U3">
                <xsd:annotation>
                    <xsd:documentation>Ten</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="U5">
                <xsd:annotation>
                    <xsd:documentation>TwoHundredFifty</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UA">
                <xsd:annotation>
                    <xsd:documentation>Torr</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UB">
                <xsd:annotation>
                    <xsd:documentation>TelecommunicationLineInServiceAverage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UC">
                <xsd:annotation>
                    <xsd:documentation>TelecommunicationPort</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UD">
                <xsd:annotation>
                    <xsd:documentation>TenthMinute</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UE">
                <xsd:annotation>
                    <xsd:documentation>TenthHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UF">
                <xsd:annotation>
                    <xsd:documentation>UsagePerTelecommunicationLineAverage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UH">
                <xsd:annotation>
                    <xsd:documentation>TenThousandYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UL">
                <xsd:annotation>
                    <xsd:documentation>Unitless</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UM">
                <xsd:annotation>
                    <xsd:documentation>MillionUnits</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VA">
                <xsd:annotation>
                    <xsd:documentation>VoltAmperePerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VC">
                <xsd:annotation>
                    <xsd:documentation>FiveHundred</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VI">
                <xsd:annotation>
                    <xsd:documentation>Vial</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VLT">
                <xsd:annotation>
                    <xsd:documentation>Volt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VQ">
                <xsd:annotation>
                    <xsd:documentation>Bulk</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VS">
                <xsd:annotation>
                    <xsd:documentation>Visit</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VT">
                <xsd:annotation>
                    <xsd:documentation>Voltage</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="W2">
                <xsd:annotation>
                    <xsd:documentation>WetKilo</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="W4">
                <xsd:annotation>
                    <xsd:documentation>TwoWeek</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WA">
                <xsd:annotation>
                    <xsd:documentation>WattPerKilogram</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WB">
                <xsd:annotation>
                    <xsd:documentation>WetPound</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WCD">
                <xsd:annotation>
                    <xsd:documentation>Cord</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WE">
                <xsd:annotation>
                    <xsd:documentation>WetTon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WEB">
                <xsd:annotation>
                    <xsd:documentation>Weber</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WEE">
                <xsd:annotation>
                    <xsd:documentation>Week</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WG">
                <xsd:annotation>
                    <xsd:documentation>WineGallon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WH">
                <xsd:annotation>
                    <xsd:documentation>Wheel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WHR">
                <xsd:annotation>
                    <xsd:documentation>WattHour</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WI">
                <xsd:annotation>
                    <xsd:documentation>WeightPerSquareInch</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WM">
                <xsd:annotation>
                    <xsd:documentation>WorkingMonth</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WR">
                <xsd:annotation>
                    <xsd:documentation>Wrap</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WSD">
                <xsd:annotation>
                    <xsd:documentation>Standard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WTT">
                <xsd:annotation>
                    <xsd:documentation>Watt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WW">
                <xsd:annotation>
                    <xsd:documentation>MillilitersOfWater</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="X1">
                <xsd:annotation>
                    <xsd:documentation>Chains-LandSurvey</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YDK">
                <xsd:annotation>
                    <xsd:documentation>SquareYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YDQ">
                <xsd:annotation>
                    <xsd:documentation>CubicYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YL">
                <xsd:annotation>
                    <xsd:documentation>100LinealYards</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YRD">
                <xsd:annotation>
                    <xsd:documentation>Yard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YT">
                <xsd:annotation>
                    <xsd:documentation>TenYard</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z1">
                <xsd:annotation>
                    <xsd:documentation>LiftVan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z2">
                <xsd:annotation>
                    <xsd:documentation>Chest</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z3">
                <xsd:annotation>
                    <xsd:documentation>Cask</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z4">
                <xsd:annotation>
                    <xsd:documentation>Hogshead</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z5">
                <xsd:annotation>
                    <xsd:documentation>Lug</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z6">
                <xsd:annotation>
                    <xsd:documentation>ConferencePoints</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Z8">
                <xsd:annotation>
                    <xsd:documentation>NewspageAgateLine</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ZP">
                <xsd:annotation>
                    <xsd:documentation>Page</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
