<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="DateQualifierCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the function of a date, time or period. This code list is derived from EDIFACT 2005 (Date or time or period function code qualifier) and X12 374 (Date/Time qualifier)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="36030">
                <xsd:annotation>
                    <xsd:documentation>Calculation is based on year of 360 days, month of 30 days.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="36530">
                <xsd:annotation>
                    <xsd:documentation>Calculation is based on year of 365 days, month of 30 days.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="365630">
                <xsd:annotation>
                    <xsd:documentation>Calculation is based on year of 365-6 days, month of 30 days.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3602831">
                <xsd:annotation>
                    <xsd:documentation>Calculation is based on year of 360 days, month of 28-31 days.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="3652831">
                <xsd:annotation>
                    <xsd:documentation>Calculation is based on year of 365 days, month of 28-31 days.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="36562831">
                <xsd:annotation>
                    <xsd:documentation>Calculation is based on year of 365-6 days, month of 28- 31 days.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1stScheduleDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="1stScheduleShip">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Acceleration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcceptanceDateOfDocument">
                <xsd:annotation>
                    <xsd:documentation>The date on which a document was accepted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcceptanceDateOfGoodsDeclarationCustoms">
                <xsd:annotation>
                    <xsd:documentation>Date on which a Goods declaration is accepted by Customs in accordance with Customs legislation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcceptanceDateTimeOfGoods">
                <xsd:annotation>
                    <xsd:documentation>Date on which the goods are taken over by the carrier at the place of acceptance (CMR 4).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccidentBenefitAgeLimit">
                <xsd:annotation>
                    <xsd:documentation>To identify the age to which benefits are provided to the insured in the event of an accident.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccidentBenefitPeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify the period of time for which benefits are provided in the event of an accident.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccidentDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time when an accident occurred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccidentInsuranceEliminationPeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify the period of time the insured must be disabled in the event of an accident for benefits to be payable by the ceding company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccidentLifetimeBenefitQualificationAge">
                <xsd:annotation>
                    <xsd:documentation>To identify the qualification age for lifetime benefits provided to the insured in the event of an accident.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountantsOpinionDate">
                <xsd:annotation>
                    <xsd:documentation>Date of an accountant's opinion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountClosedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when account was closed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingMethodologyChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date when accounting methodology was changed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingPeriod">
                <xsd:annotation>
                    <xsd:documentation>Self-explanatory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingPeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingTransactionDate">
                <xsd:annotation>
                    <xsd:documentation>Date to which an accounting transaction refers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingValueDate">
                <xsd:annotation>
                    <xsd:documentation>Date against which the entry has to be legally allocated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountingYear">
                <xsd:annotation>
                    <xsd:documentation>Year considered for accounting of the treaty or portion of the treaty.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountOpened">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AccountsReceivable-StatementDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Acknowledgment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcquisitionDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActionDate">
                <xsd:annotation>
                    <xsd:documentation>Date of action.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActionEndDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActivityReportingDate">
                <xsd:annotation>
                    <xsd:documentation>The date applicable to the activity being reported.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActivityPeriodDateRange">
                <xsd:annotation>
                    <xsd:documentation>A specific date range associated with an activity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Actual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualArchive">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>[2106] Date (and time) of arrival of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualComplete">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>(2280) Date (and time) of departure of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DischargeDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the specified goods or transport equipment has or have been discharged from the means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualPortOfEntry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualRelease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ActualReturnToWork">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateActual">
                <xsd:annotation>
                    <xsd:documentation>Actual date of start.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcuteManifestationDate">
                <xsd:annotation>
                    <xsd:documentation>The date the symptoms manifested themselves in an acute form.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AcuteManifestationOfAChronicCondition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Added">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AddressPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Adjournment">
                <xsd:annotation>
                    <xsd:documentation>The period of time over which an adjournment is in effect.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustedAge">
                <xsd:annotation>
                    <xsd:documentation>The adjusted age used for purposes of calculation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustedContestability">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustedDeathBenefit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustedEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustedHire">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustedStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentProcessed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentPromised">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdjustmentToBillDated">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdministratorOrderedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when an administrator is ordered for a company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdmissionDateAndOrTimeExpected">
                <xsd:annotation>
                    <xsd:documentation>Expected date and/or time of admission.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdoptionDateActual">
                <xsd:annotation>
                    <xsd:documentation>Actual date when adoption occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviseAfterDateTime">
                <xsd:annotation>
                    <xsd:documentation>The information must be advised after the date/time indicated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviseBeforeDateTime">
                <xsd:annotation>
                    <xsd:documentation>The information must be advised before the date/time indicated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviseCompletedDateTime">
                <xsd:annotation>
                    <xsd:documentation>The advise has been completed at the date indicated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdviseOnDateTime">
                <xsd:annotation>
                    <xsd:documentation>The information must be advised on the date/time indicated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AdvisingDate">
                <xsd:annotation>
                    <xsd:documentation>Date of advice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Age">
                <xsd:annotation>
                    <xsd:documentation>The length of time that a person or thing has existed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AgreementToPayDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the debtor agreed to pay.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AiredDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllotmentPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowableEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AllowedRenewalDurationPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time a company can renew its duration period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlternateBasePeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlternateProblemResolution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AlternativeDueDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnalysisMonthEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnesthesiaAdministration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Anniversary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnualReportDelinquentOnDate">
                <xsd:annotation>
                    <xsd:documentation>Date when annual report was considered delinquent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnualReportDueDate">
                <xsd:annotation>
                    <xsd:documentation>Date when annual report is due.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnualReportFilingDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the annual report was filed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnualReportMailingDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the annual report was mailed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Annuitization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnnuityCommencementDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AnticipatedClosing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppliancePlacement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicantSigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Application">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationEntry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationLoggedDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApplicationStatus">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppointmentEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppointmentExpiryDate">
                <xsd:annotation>
                    <xsd:documentation>Date when an appointment will expire.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AppraisalOrdered">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApprovalOffer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ApprovalDate">
                <xsd:annotation>
                    <xsd:documentation>Date of approval.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Arrested">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeAtInitialPort">
                <xsd:annotation>
                    <xsd:documentation>Date/time that the conveyance arrives at the initial port in the country of destination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>Date/time of earliest arrival of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeOfTransportLeadTime">
                <xsd:annotation>
                    <xsd:documentation>Lead time is referenced to the date a transport will arrive or has arrived.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeScheduled">
                <xsd:annotation>
                    <xsd:documentation>Date (and time) of scheduled arrival of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Date (and time) of ultimate arrival of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalInCountry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArterialBloodGasTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssemblyStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Asset">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssetAndLiabilityScheduleDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the asset and liability schedule.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssetDocumentationExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssignedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when assigned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AssignmentRecorded">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AttendanceDateAndOrTimeAndOrPeriod">
                <xsd:annotation>
                    <xsd:documentation>Date and or time and or period of attendance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AudienceDeficiencyPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuditPeriodAfterSplitDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuditPeriodPriorToSplitDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthenticationDateTimeOfDocument">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the document is signed or otherwise authenticated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthenticationValidationDateTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AuthorizationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when an authorization was given.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AutomaticPremiumLoan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AvailabilityDueDate">
                <xsd:annotation>
                    <xsd:documentation>Date when ordered items should be available at a specified location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Availability">
                <xsd:annotation>
                    <xsd:documentation>Date/time when received item is available.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AverageDeliveryDelay">
                <xsd:annotation>
                    <xsd:documentation>The average delay between deliveries.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AveragePaymentPeriod">
                <xsd:annotation>
                    <xsd:documentation>The average period of time over which money has been paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Award">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BackOnMarket">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BackOrderDeliveryDateTimePeriod">
                <xsd:annotation>
                    <xsd:documentation>The date/time/period during which the delivery of a back order will take, or has taken, place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BalanceDateTimePeriod">
                <xsd:annotation>
                    <xsd:documentation>The date/time/period of a balance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BalanceSheetDateLatest">
                <xsd:annotation>
                    <xsd:documentation>Date of the latest balance sheet.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Base">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Baseline">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BaselineComplete">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BaselineStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeforeInventoryIsReplenishedBasedOnStockCheckLeadTime">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeginningLayDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeginTherapy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BeneficiarysBanksDueDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which funds should be made available to the beneficiary's bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Benefit">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitAdjustmentEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitAdjustmentPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitAdjustmentStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitApplicationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitChangeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a benefit provided by a service provider is changed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitCreditEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitCreditPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitCreditStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitDebitEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitDebitStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitPaymentsTerminationDate">
                <xsd:annotation>
                    <xsd:documentation>To identify the date on which benefit payments have ceased.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time for which benefits are provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitReleaseAuthorizationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a benefit is authorized for release.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitsPaid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitTransferEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitTransferPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitTransferStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitTypeGrossWeeklyAmountEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitTypeNetWeeklyAmountEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of benefit ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BenefitPeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of benefit begins.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BestBeforeDate">
                <xsd:annotation>
                    <xsd:documentation>The best before date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BestTimeToCall">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BiannualTerminalInspectionDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which a biannual inspection of a terminal has taken or will take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BidEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BidOpenDateBidsWillBeOpened">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BidPriceException">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bill">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BilledThrough">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BillOfLadingDate">
                <xsd:annotation>
                    <xsd:documentation>Date as specified on the bill of lading.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BirthCertificate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BirthDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a person was born.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BlanksValueDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which the funds are at the disposal of the receiving bank.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Blueprint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BoardOfDirectorsIncompleteAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BoardOfDirectorsNotAuthorizedAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>As of this date the board of directors is not authorized.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BoardOfDirectorsNotCompleteAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>As of this date the board of directors is not fully filled.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BoardOfDirectorsRegistrationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the board of directors was registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookEntryDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookingDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date at which the booking was made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BritishThermalUnitTestPerformed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BudgetLineApplicationDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which something has been applied to a budget line.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessControlChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a new authority took control.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessOpenedDate">
                <xsd:annotation>
                    <xsd:documentation>Date opened for business.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BusinessTerminationDate">
                <xsd:annotation>
                    <xsd:documentation>Date the business terminates.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersDock">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BuyersLocalTime">
                <xsd:annotation>
                    <xsd:documentation>Time at the buyer's location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CalculationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CalendarAnniversary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CalendarYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelAfter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelAfterExCountry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelAfterExFactory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelIfNotDeliveredByThisDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelIfNotPublishedByThisDate">
                <xsd:annotation>
                    <xsd:documentation>Cancel if not published by this date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancelIfNotShippedByThisDate">
                <xsd:annotation>
                    <xsd:documentation>Cancel the order if goods not shipped by this date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancellationDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which a document or message has been cancelled.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CancellationDateTimeLatest">
                <xsd:annotation>
                    <xsd:documentation>The latest date/time on which cancellation of the payment order may be requested.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CapitalizationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of capitalization.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CapitationPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CapitationPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoBookingConfirmedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which the cargo booking has been accepted by the carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CargoReleaseDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Ultimate date/time at which goods or equipment should be released.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CaseConverted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CaseReopened">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CashCallDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which a cash call was made for a loss suffered and covered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CategoryChangeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a change of category is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CeasedOperations">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificateEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Certification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificationOfWeightDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which the carrier proceeds to the weighting of the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificationPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CertificationRevision">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChangedAccountingDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChangedDate">
                <xsd:annotation>
                    <xsd:documentation>Date change takes place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargeBackDateTime">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargePeriod">
                <xsd:annotation>
                    <xsd:documentation>Period a specified charge is valid for.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargePeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>The charge period's last date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChargePeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>The charge period's first date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChartOfAccountPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period covered by the chart of account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Check">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CheckedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when checked.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildbirthDateAndOrTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Actual date and or time of childbirth.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ChildbirthDateEstimated">
                <xsd:annotation>
                    <xsd:documentation>Estimated date of childbirth.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Christening">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Citation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CitizenshipChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date of citizenship change.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimAdministratorKnowledgeOfTheInjury">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimAdministratorNotifiedOfEmployeeLegalRepresentation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimIncurredDate">
                <xsd:annotation>
                    <xsd:documentation>The date that the claim was incurred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimMadeDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which a claim was made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimNotificationReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimsMade">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimStatementPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClaimStatementPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Clause">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClearanceDateCustoms">
                <xsd:annotation>
                    <xsd:documentation>(3080) Date on which Customs formalities necessary to allow goods to be exported, to enter home use, or to be placed under another Customs procedure has been accomplished (CCC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClinicalExaminationDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time of clinical examination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Closed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosedUntilDate">
                <xsd:annotation>
                    <xsd:documentation>Date when again open.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosingDateOfFirstBalanceSheet">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosingDateTime">
                <xsd:annotation>
                    <xsd:documentation>Final date for delivering cargo to a liner ship.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ClosureDateTimePeriod">
                <xsd:annotation>
                    <xsd:documentation>Date/time/period when an enterprise is closed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollateralDependent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollateralDependentBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollateralDependentEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Collection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectionDateTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>The transport order may be issued before the goods are ready for picking up. This date/time indicates from when on the carrier can have access to the consignment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CollectionDateTimeLatest">
                <xsd:annotation>
                    <xsd:documentation>In relation with the arrangements agreed between buyer and seller or between sender and main transport it may be necessary to specify the latest collection date/time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommencedPumpingBallastDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the intake of materials to be carried to improve the trim and the stability of the means of transport, was commenced.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommencedTankCleaningDateTime">
                <xsd:annotation>
                    <xsd:documentation>The date/and or time tank cleaning was started.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommercialRating">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompanyTermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ComparisonPeriod">
                <xsd:annotation>
                    <xsd:documentation>The time period covered in a comparison.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompilationDateAndTime">
                <xsd:annotation>
                    <xsd:documentation>Date and time of the compilation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Complaint">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletedPumpingBallastDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which the intake of materials, to be carried to improve the trim and the stability of the means of transport, was completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletedTankCleaningDateTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time tank cleaning was completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletionDate">
                <xsd:annotation>
                    <xsd:documentation>Date of completion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletionDateOrTimeNotAfter">
                <xsd:annotation>
                    <xsd:documentation>The not after date or time for completion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletionDateOrTimeNotBefore">
                <xsd:annotation>
                    <xsd:documentation>The not before date or time for completion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletionDateOrTimeScheduled">
                <xsd:annotation>
                    <xsd:documentation>The scheduled date or time for completion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Compliance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConditionInitialTreatmentDate">
                <xsd:annotation>
                    <xsd:documentation>Date when initially treated for this condition.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Confinement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConfinementEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConfinementStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConfirmationDateLeadTime">
                <xsd:annotation>
                    <xsd:documentation>Lead time is referenced to the date of confirmation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConfirmedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time which has been confirmed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Connection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consolidated">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedOmnibusBudgetReconciliationActCOBRA">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedOmnibusBudgetReconciliationActCOBRABegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedOmnibusBudgetReconciliationActCOBRAEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidatedOmnibusBudgetReconciliationActCOBRAQualifyingEvent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConsolidationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when consolidation occurred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstructionDateActual">
                <xsd:annotation>
                    <xsd:documentation>Date of actual construction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstructionStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerSafetyConventionCscInspectionDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which container equipment is to be or has been inspected as per the Container Safety Convention (CSC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerSafetyConventionCscPlateExpirationDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the validity of a Container Safety Convention (CSC) plate expires.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerStrippingDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the container stripping should be completed at the latest.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerStuffingDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the container stuffing should be completed at the latest.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contestability">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Contingency">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContingencyEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Continuance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContinuationOfPayStartDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContinuingEducationRequirement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContinuousCoverageDates">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContinuousPassiveMotionCPM">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractAuditDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractAwardDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the contract is awarded to a tenderer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractBindingDate">
                <xsd:annotation>
                    <xsd:documentation>The date from which a contract becomes binding on the contracting parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractCompletionDate">
                <xsd:annotation>
                    <xsd:documentation>The date a contract is completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractCostsThrough">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractDate">
                <xsd:annotation>
                    <xsd:documentation>[2326] Date when a Contract is agreed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractDefinition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a contract becomes valid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractExpiryDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a contract expires.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractMailed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractPerformanceDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractPerformanceStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractRe-Open">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractualDeliveryDate">
                <xsd:annotation>
                    <xsd:documentation>The date of delivery contractually agreed between parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractualStartDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which activities stated in the contract must start.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContributionPeriodEndDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a contribution period ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContributionPeriodStartDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a contribution period commences.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContributionsCeasingDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when contributions cease.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControlPlan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ControvertDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConversionPrivilegeEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConversionToRepayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConversionIntoHoldingCompanyDate">
                <xsd:annotation>
                    <xsd:documentation>Date business was converted into a holding company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConvertedToElectronicDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConveyanceRegistrationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a vessel, vehicle or other means of transport was registered by a competent authority.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConvictedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when convicted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoordinationOfBenefits">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoordinationOfBenefitsBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoordinationOfBenefitsEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CorrectedDateOfBirth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Correction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CosignerSigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CourtDismissalDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which a court refused further hearing of a case.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CourtEvent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CourtNotice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CourtRegistrationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of registration in the court.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoverageDuration">
                <xsd:annotation>
                    <xsd:documentation>The period coverage has been in force.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoverageIssueDate">
                <xsd:annotation>
                    <xsd:documentation>Date from which the anniversary coverage is measured.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CoveredIncomePeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify the period over which covered income is measured.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditAdvice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditDocumentationExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditedService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditedServiceBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditedServiceEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CreditorsRequestedValueDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the creditor requests to be credited.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Crime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CriminalSentenceDuration">
                <xsd:annotation>
                    <xsd:documentation>The period of time over which a criminal sentence applies.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CrossborderDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which goods are transferred across a country border.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CumulativeQuantityEndDate">
                <xsd:annotation>
                    <xsd:documentation>Last Date for accumulation of delivery quantities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CumulativeQuantityStartDate">
                <xsd:annotation>
                    <xsd:documentation>First Date for accumulation of delivery quantities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CureTime">
                <xsd:annotation>
                    <xsd:documentation>Specifies the length of time that an article was or should be cured.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Current">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentAuthorityControlStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when current authority took control.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentDisabilityPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentDisabilityPeriodLastDayWorked">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentDisabilityPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentIncomePeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify the period over which current income is measured.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentLegalStructureEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation>Date when current legal structure became effective.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentList">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentMonthEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentNameEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation>Date when current name became effective.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentReportDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the current report.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentScheduleDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurrentScheduleShip">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurtailmentDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CurtailmentDatefromAdviceOfPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerContractEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerContractExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerOrder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerValueDate">
                <xsd:annotation>
                    <xsd:documentation>Date at which funds are taken into account for interest calculation (in debit or credit).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsCargoRelease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsClearance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomsEntryDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Cycle">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CycleBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CycleEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CycleToDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateAccountMatures">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateAcquired">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateAndOrTimeOfHandlingEstimated">
                <xsd:annotation>
                    <xsd:documentation>The date and or time when the handling action is estimated to take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateAsAt">
                <xsd:annotation>
                    <xsd:documentation>Date related to a given context.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateAssignmentFiledForRecord">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BankruptcyFiledDate">
                <xsd:annotation>
                    <xsd:documentation>Date when bankruptcy was filed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateBecameAware">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateClaimPaid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateCourseOfOrthodonticsTreatmentBeganOrIsExpectedToBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateDeedFiledForRecord">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateDefaultWasCured">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateDocumentationOrPaperworkOrBothWasSent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FiledDate">
                <xsd:annotation>
                    <xsd:documentation>Date when filed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateForeclosureHeld">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateForeclosureProceedingsInstituted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateForeclosureSaleScheduled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateForTheLastUpdate">
                <xsd:annotation>
                    <xsd:documentation>Date for the last update.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateHazardInsurancePolicyCancelled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateHazardInsurancePolicyRefused">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateMaterialUsageSuspended">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAbandonment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAcquisitionOfTitle">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAdvance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAppraisal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAssignmentApproval">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfAssignmentRejection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfClaim">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfClosing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfDamage">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeceaseDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a person died.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfDeedInLieu">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfDivorce">
                <xsd:annotation>
                    <xsd:documentation>Date when two married persons are officially divorced.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfExtensionToConvey">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfFirmCommitment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfFirstMortgagePayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfFirstRegistration">
                <xsd:annotation>
                    <xsd:documentation>Date of first registration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfFirstUncuredDefault">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfForeclosureNotice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLastContact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLastDraw">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLastInstallmentReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLastPaymentReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLastUpdate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLoan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLocalOfficeApprovalOfConveyanceOfDamagedRealEstateProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLocalOfficeApprovalOfConveyanceOfOccupied">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfLocalOfficeCertificationOfConveyanceOfDamagedRealEstate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfMaximumMedicalImprovement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfNoticeOfProbableIneligibilityForAssignment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfNoticeOfReferralForAssignment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfNoticeToConvey">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfOperation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfOrderLeadTime">
                <xsd:annotation>
                    <xsd:documentation>Lead time is referenced to the date of order.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfPossession">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfProductFailure">
                <xsd:annotation>
                    <xsd:documentation>The date the product failed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfPropertyInspection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseFromBankruptcyDate">
                <xsd:annotation>
                    <xsd:documentation>Date when an entity is released from bankruptcy status.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfRepairOrService">
                <xsd:annotation>
                    <xsd:documentation>The date of a repair or service.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfSeizure">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfSeparation">
                <xsd:annotation>
                    <xsd:documentation>Date of marital separation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfSourceDocument">
                <xsd:annotation>
                    <xsd:documentation>The date of the source document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfStandard">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOnWhichAssetsJudgedInsufficientToPayCreditors">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOutstandingLoanBalanceReported">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackagingDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which the packaging of a product took place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DatePaid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DatePlacedInDisfavour">
                <xsd:annotation>
                    <xsd:documentation>Date when placed in a disfavoured category or status.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DatePossessoryActionInitiated">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DatePracticeCeased">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DatePracticeEstablished">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DatePropertySold">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateReadyForCollection">
                <xsd:annotation>
                    <xsd:documentation>A date on which an object is ready for collection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateRedemptionPeriodEnds">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateReferredToAttorneyForForeclosure">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which something was asked for.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateThroughWhichPropertyTaxesHaveBeenPaid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateTimeOfDiscountTermination">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the deduction from an amount comes to an end.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateTimeOfInterestDue">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the interest has to be paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateTimeOfLatestAccountsFilingAtPublicRegistry">
                <xsd:annotation>
                    <xsd:documentation>The latest date/time when financial accounts were filed at public registry.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateTotalAmountOfDelinquencyReported">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateVoluntaryConveyanceAccepted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateWhenContainerEquipmentBecomesDomestic">
                <xsd:annotation>
                    <xsd:documentation>The date on which foreign-built container equipment has entered into the commerce of another country and has become domestic equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DaylightOverdraftTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DaysOfOperation">
                <xsd:annotation>
                    <xsd:documentation>Week days of operation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeathDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time of death.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebitAdvice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebitValueDateRequested">
                <xsd:annotation>
                    <xsd:documentation>Date on which the account owner wants the debit value to his account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DebtIncurred">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DecisionDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Declaration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclarationPresentationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when item has been or has to be declared/presented.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeclarationReferencePeriod">
                <xsd:annotation>
                    <xsd:documentation>Reference period of a set of items reported on the same declaration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Deductible">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeedNotAvailableAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>Date when deed was not available.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Deferral">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeferredDistribution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeferredPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeferredPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time for which an activity has been postponed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DefinitionOfDisabilityDuration">
                <xsd:annotation>
                    <xsd:documentation>To identify the period for which the definition of disability applies.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DelayedBillingNotDelayedPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeletionDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which deletion occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Delivered">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliverNotBeforeAndNotAfterDates">
                <xsd:annotation>
                    <xsd:documentation>Deliver not before and not after a specific date range.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryAppointmentDateAndTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTime1StSchedule">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which goods or consignment are delivered at their destination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeCurrentSchedule">
                <xsd:annotation>
                    <xsd:documentation>Delivery Date deriving from actual schedule.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeDeferred">
                <xsd:annotation>
                    <xsd:documentation>New date and time of delivery calculated on basis of a consignee's requirement (chargeable).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>Date identifying a point in time before which the goods shall not be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeExpected">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which goods are expected to be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeFirst">
                <xsd:annotation>
                    <xsd:documentation>First possible date/time for delivery.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeLast">
                <xsd:annotation>
                    <xsd:documentation>Date when the last delivery should be or has been accomplished.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeLatest">
                <xsd:annotation>
                    <xsd:documentation>Date identifying a point of time after which goods shall not or will not be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimePromisedFor">
                <xsd:annotation>
                    <xsd:documentation>[2138] Date by which, or period within which, the merchandise should be delivered to the buyer, as agreed between the seller and the buyer (generic term).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimePromisedForAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Delivery might take place earliest at given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimePromisedForPriorToAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Delivery might take place latest at given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeRequested">
                <xsd:annotation>
                    <xsd:documentation>Date on which buyer requests goods to be delivered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeScheduledFor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryOrderIssued">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryRating">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryRequested">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryTicket">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DenialEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DenialRescission">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartmentAdmissionDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time of admission to a department.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartmentDischargeDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time of discharge from a department.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartmentOfLaborWageDeterminationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTime">
                <xsd:annotation>
                    <xsd:documentation>[2280] Date (and time) of departure of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>Date/time of earliest departure of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTimeFromLastPortOfCall">
                <xsd:annotation>
                    <xsd:documentation>Date/time that conveyance departed from the last foreign port of call.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTimeScheduled">
                <xsd:annotation>
                    <xsd:documentation>Date (and time) of scheduled departure of means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which a means of transport has to depart ultimately.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureFromSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepositDateTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>(2170) Date/time on which the goods are or are expected to be despatched or shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DespatchNoteDate">
                <xsd:annotation>
                    <xsd:documentation>[2218] Date when a Despatch Note is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DetrimentalInformationReceiptDate">
                <xsd:annotation>
                    <xsd:documentation>Date when detrimental information was received.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DirectInterchangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date the item was directly interchanged.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Disability">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisabilityWaitPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time the insured must be disabled before reinsurance coverage becomes effective.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisbursementDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DischargeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when goods should, might or have been discharged from the means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DischargeAndLoadingCompletedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when all discharge and loading operations on the transport means have been completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discharge-Planned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Discovered">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DiscrepancyDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which a discrepancy has been found.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DisembarkationDateAndTime">
                <xsd:annotation>
                    <xsd:documentation>Date and time at which crew and/or passengers disembark.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DismissalDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Disposal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Disposition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributorEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DistributorTermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DividendApplied">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditAmendmentDate">
                <xsd:annotation>
                    <xsd:documentation>Date of amendment of a documentary credit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditExpiryDateTime">
                <xsd:annotation>
                    <xsd:documentation>The latest date/time for presentation of the documents to the bank where the credit expires.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditIssueDate">
                <xsd:annotation>
                    <xsd:documentation>The date the documentary credit has been issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentaryCreditPresentationPeriod">
                <xsd:annotation>
                    <xsd:documentation>The specification of the period of time, expressed in number of days, after the date of issuance of the transport document(s) within which the documents must be presented.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentMessageDateTime">
                <xsd:annotation>
                    <xsd:documentation>(2006) Date/time when a document/message is issued. This may include authentication.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentReceivedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the document was actually received.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DocumentRequestedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the document is requested by a party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoNotDeliverAfter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoNotDeliverBefore">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoNotShipBeforeExCountry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DoNotShipBeforeExFactory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Draft">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DrawingRevisionDate">
                <xsd:annotation>
                    <xsd:documentation>Date the drawing revision has been allocated to a design.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DropActionDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DroppedToLessThanHalfTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DueDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DueDateLastCompleteInstallmentPaid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DueDateOfFirstPaymentToPrincipalAndInterest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DueToCustomer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DuplicateBill">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Duration">
                <xsd:annotation>
                    <xsd:documentation>Duration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarliestFilingPeriod">
                <xsd:annotation>
                    <xsd:documentation>Earliest period for which a filing is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarliestSaleDate">
                <xsd:annotation>
                    <xsd:documentation>The earliest date on which the product may be made available for sale.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarlyFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarlyStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarlyWithdrawal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EarningsEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Education">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EducationBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EducationEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date education is completed at an educational institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EducationStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date education begins at an educational institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EffectiveDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date and/or time at which specified event or document becomes effective.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EffectiveDateOfChange">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Election">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Eligibility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EligibilityBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EligibilityEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmbarkationDateAndTime">
                <xsd:annotation>
                    <xsd:documentation>Date and time at which crew and/or passengers board.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployedInThisPosition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployedInThisProfession">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployeeEffectiveDateOfCoverage">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployeeLaidOff">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployeeTemporaryLaidOffPeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>The start date of a period in which employees were temporarily placed out of work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployeeTemporaryLaidOffPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>The ending date of a period in which employees were temporarily placed out of work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployeeWagesCeased">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployerKnowledgeOfTheInjury">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmployerReportedInjuryToClaimAdministrator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmploymentPositionStartDate">
                <xsd:annotation>
                    <xsd:documentation>The start date of employment in a particular position.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmploymentEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmploymentOrHire">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmploymentPositionStartDateEstimated">
                <xsd:annotation>
                    <xsd:documentation>Estimated start date of employment in a particular position.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EmploymentProfessionStartDate">
                <xsd:annotation>
                    <xsd:documentation>Start date of employment in a particular profession.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which a period (from - to) ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndAvailabilityDate">
                <xsd:annotation>
                    <xsd:documentation>The end date of availability.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndDateOfSupport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndDateScheduled">
                <xsd:annotation>
                    <xsd:documentation>Date when activity is scheduled to end.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndingLayDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndOfGrace">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndOfLastFiscalYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndorsedCertificateIssueDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which a certificate, endorsed by signature or other agreed means, is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EndorsementDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EngineeringChangeLevelDate">
                <xsd:annotation>
                    <xsd:documentation>Date the engineering level of goods is changed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EngineeringDataList">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Enrollment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EnrollmentSignatureDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntryDate">
                <xsd:annotation>
                    <xsd:documentation>Date of entry.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EntryDateEstimatedCustoms">
                <xsd:annotation>
                    <xsd:documentation>Date on which the official date of Customs entry is anticipated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentCollectionOrPickUpDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the equipment was actually collected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentCollectionOrPickUpDateTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment can be picked up at the earliest.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentCollectionOrPickUpDateTimePlanned">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment can be picked up, either full or empty, according to a planning.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentLogEntry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentPositioningDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment was actually positioned (delivered).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentPositioningDateTimeEstimated">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment is estimated to be positioned (delivered).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentPositioningDateTimeRequested">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment is requested to be positioned (delivered).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentPositioningDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment should be positioned (delivered) at the latest.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentPreTripDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which equipment is pre-tripped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentReconditioningDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Actual date/time of the reconditioning of a piece of equipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentRepairReadyDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Ultimate date/time on which a piece of equipment must be</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentReturnPeriodExpected">
                <xsd:annotation>
                    <xsd:documentation>Period until which equipment is expected to be hired.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EquipmentStockCheckDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which equipment has been ascertained as being in stock.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EscalationDate">
                <xsd:annotation>
                    <xsd:documentation>Value date of indexes appearing as numerators in an escalation formula.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EscalationStartDate">
                <xsd:annotation>
                    <xsd:documentation>Value date of the indexes appearing as denominators in an escalation formula.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstablishedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when an entity was established or created.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimateComment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Estimated">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedAcceptanceDate">
                <xsd:annotation>
                    <xsd:documentation>To estimate the date of acceptance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArrivalDateTimeEstimated">
                <xsd:annotation>
                    <xsd:documentation>(2348) Date/time when carrier estimates that a means of transport should arrive at the port of discharge or place of destination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CompletionDateOrTimeEstimated">
                <xsd:annotation>
                    <xsd:documentation>The estimated date or time for completion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedCompletion-FirstPriorMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedCompletion-SecondPriorMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedCompletion-ThirdPriorMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConstructionDateEstimated">
                <xsd:annotation>
                    <xsd:documentation>Estimated date of construction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfBirthEstimated">
                <xsd:annotation>
                    <xsd:documentation>The estimated date of birth.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeEstimated">
                <xsd:annotation>
                    <xsd:documentation>Date and/or time when the shipper of the goods expects delivery will take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DepartureDateTimeEstimated">
                <xsd:annotation>
                    <xsd:documentation>Date/time when carrier estimates that a means of transport should depart at the place of departure.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedEscrowClosing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedImmigrationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedPointOfArrival">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedPointOfDischarge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedPortOfEntry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateEstimated">
                <xsd:annotation>
                    <xsd:documentation>Date of estimated start.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedStart-FirstPriorMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedStart-SecondPriorMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatedStart-ThirdPriorMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EstimatePreparation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EvaluationDate">
                <xsd:annotation>
                    <xsd:documentation>Date evaluation takes place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EventDate">
                <xsd:annotation>
                    <xsd:documentation>A date specifying an event.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Examination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExecutionDate">
                <xsd:annotation>
                    <xsd:documentation>The date when ordered bank initiated the transaction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExecutionDateTimeRequested">
                <xsd:annotation>
                    <xsd:documentation>The date/time on which the ordered bank is requested to initiate the payment order, as specified by the originator (e.g. the date of the debit).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExitPlantDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpectedContainerHireFromDateTime">
                <xsd:annotation>
                    <xsd:documentation>Estimated date and time when the containers are expected to go on-hire.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpectedFundingDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpectedProblemResolutionDate">
                <xsd:annotation>
                    <xsd:documentation>Date when problem is expected to be resolved.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpectedReply">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpectedValueDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the funds are expected to be at the disposal of the beneficiary.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditedOn">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Expiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationDateOfExtensionToAssign">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationDateOfExtensiontoForeclose">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationDateTimeOfCustomsDocument">
                <xsd:annotation>
                    <xsd:documentation>Date on which validity of a customs document expires.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationDateToSubmitTitleEvidence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationOfExtensionToConvey">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationOfExtensionToSubmitFiscalData">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpirationOfForeclosureTimeframe">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpiryDate">
                <xsd:annotation>
                    <xsd:documentation>Date of expiry of the validity of a referenced document, price information or any other referenced data element with a limited validity period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpiryDateOfExportLicence">
                <xsd:annotation>
                    <xsd:documentation>[2078] Date of expiry of the validity of an Export Licence.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpiryDateOfImportLicence">
                <xsd:annotation>
                    <xsd:documentation>[2272] Date of expiry of the validity of an Import Licence.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Explantation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when imported vessel/merchandise last left the country of export for the country of import.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExportationDateForTextiles">
                <xsd:annotation>
                    <xsd:documentation>Date when imported textiles last left the country of origin for the country of importation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Exposure">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExposureSourcePeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Extended">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Extract">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FacsimileDueBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Failed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FailureModeEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FeasibilitySignOff">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FederalHighwayAdministrationFhwaInspectionDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which container equipment is to be or has been inspected in accordance with the requirements of the U.S. Federal Highway Administration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FeeCapitalization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FieldFailure">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FieldTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FileGenerationDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and, or time of file generation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FilingPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalIssue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalReconciliationValueEstimateAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinalScheduledPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialCoveragePeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time for which financial coverage applies.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialInformationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the financial information.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinancialInformationSubmissionDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when financial information is submitted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinishDateOrTimeMandatory">
                <xsd:annotation>
                    <xsd:documentation>The mandatory date or time for finishing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinishDateOrTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>The earliest date or time for finishing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FinishDateOrTimeLatest">
                <xsd:annotation>
                    <xsd:documentation>The latest date or time for finishing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstContact">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstDateOfOrdering">
                <xsd:annotation>
                    <xsd:documentation>The first date on which ordering may take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstInterestBearingDate">
                <xsd:annotation>
                    <xsd:documentation>The first date from which interest is borne.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstInterestPaymentDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstInvolvement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstIssue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstMarketed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstPaymentChange">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstPaymentDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstProduced">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstPublishedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when material was first published.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstRateAdjustment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstSaleDateAndOrTimeAndOrPeriod">
                <xsd:annotation>
                    <xsd:documentation>The first date, and/or time, and/or period a product was sold.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstSubmission">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FirstVisitorConsultation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FiscalAnniversary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FlatExtraEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FlatExtraPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period for charging the additional extra.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForecastPeriod">
                <xsd:annotation>
                    <xsd:documentation>A period for which a forecast applies.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForecastPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>The ending date of a forecast period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ForecastPeriodStart">
                <xsd:annotation>
                    <xsd:documentation>The beginning of a forecast period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Formation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FreightBillDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when freight bill is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfAwardToCompletion">
                <xsd:annotation>
                    <xsd:documentation>Lead time to determine the completion date of an effort based on the date an award is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfAwardToDelivery">
                <xsd:annotation>
                    <xsd:documentation>Lead time to determine the delivery date based on the date an award is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfAwardToEarliestDelivery">
                <xsd:annotation>
                    <xsd:documentation>Lead time to determine the earliest date a delivery can be made based on the date an award is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfAwardToLatestDelivery">
                <xsd:annotation>
                    <xsd:documentation>Lead time to determine the latest date a delivery can be made based on the date an award is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfNoticeToProceedToCommencementOf">
                <xsd:annotation>
                    <xsd:documentation>performance Period of time from notice to proceed until performance commencement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfNoticeToProceedToCompletion">
                <xsd:annotation>
                    <xsd:documentation>Period of time from date of notice to proceed until completion.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfOrderReceiptToDelivery">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfOrderReceiptToSampleReady">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfOrderReceiptToShipment">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfReceiptOfItemToApproval">
                <xsd:annotation>
                    <xsd:documentation>Lead time to determine the date an item will be approved based on the date the item was received.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfReceiptOfToolingAidsToSampleReady">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfSampleApprovalToFirstProductShipment">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromDateOfToolingAuthorizationToSampleReady">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FromLastBookedOrderToDelivery">
                <xsd:annotation>
                    <xsd:documentation>Lead time is the defined timespan.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FumigationDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>The date/or time on which fumigation is to occur or has taken place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FunctionalTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FundsAppropriation-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FundsAppropriation-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FuturePeriod">
                <xsd:annotation>
                    <xsd:documentation>Period in the future.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GallonsPerMinuteTestPerformed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsCollectionOrPickUpDateTimePlanned">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which goods can be picked up, according to a planning.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsPositioningDateTimeExpected">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which goods are expected to be positioned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsPositioningDateTimePlanned">
                <xsd:annotation>
                    <xsd:documentation>The date/time on which the goods will be positioned according to a planning.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GoodsReceiptDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time upon which the goods were received by a given party.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GraduationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when graduation occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupContractEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GroupContractExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GuaranteeDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a guarantee is placed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GuaranteePeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GuarantorReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HandOverDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which hand over (i.e. the transfer of responsibility for an object or activity such as documentation, system etc. from one party to another) actually takes place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HandOverDateTimePlanned">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which hand over (i.e. the transfer of responsibility for an object or activity such as documentation, system etc. from one party to another) is planned to take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HighCapitalYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HighFabricationReleaseAuthorization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HighRawMaterialAuthorization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HiredFromDate">
                <xsd:annotation>
                    <xsd:documentation>Date from which an item has been or will be hired.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HiredUntilDate">
                <xsd:annotation>
                    <xsd:documentation>Date until which an item has been or will be hired.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HoldAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HoldAsStockAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Holiday">
                <xsd:annotation>
                    <xsd:documentation>A date or period that is a break from work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HolidayFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HolidayStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Homestead">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HorizonEndDate">
                <xsd:annotation>
                    <xsd:documentation>The last date of a period forming a horizon.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HorizonPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period forming a (planning) horizon.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HorizonStartDate">
                <xsd:annotation>
                    <xsd:documentation>The first date of a period forming a horizon.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HosesConnectedDateTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time hoses were connected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HosesDisconnectedDateTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time hoses were disconnected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HospitalAdmissionDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time of admission to a hospital.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HospitalDischargeDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time of discharge from a hospital.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="HydrotestDate">
                <xsd:annotation>
                    <xsd:documentation>The date equipment has been hydrotested.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Illness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IllnessBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IllnessRecoveryDateExpected">
                <xsd:annotation>
                    <xsd:documentation>Date when a person is expected to recover from illness.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImmigrationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Implantation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImplementationDateTimePeriod">
                <xsd:annotation>
                    <xsd:documentation>A date/time/period within which an implementation is to take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Import">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImportationDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which goods are imported, as determined by the governing Customs administration.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ImportLicenceDate">
                <xsd:annotation>
                    <xsd:documentation>[2292] Date when Import Licence is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InactivityEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when inactivity ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Inactivity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InboundMovementAuthorizationDate">
                <xsd:annotation>
                    <xsd:documentation>Inland movement authorization date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Incident">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Income">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncomeDocumentationExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncomeIncreasePeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncomePeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time in which income is earned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of incorporation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncorporationDissolution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IncumbencyPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IndividualEducationPlanIEP">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="In-force">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InformationReleaseAuthorizationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the information was authorized to be released.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialClaim">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialCompletion">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialContactDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialDisabilityPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialDisabilityPeriodLastDayWorked">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialDisabilityPeriodReturnToWork">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialDisabilityPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialFeeDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialFinancialAccountsFiledDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the initial financial accounts were filed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlacementDateInitial">
                <xsd:annotation>
                    <xsd:documentation>Date of initial placement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialPremium">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialPremiumEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialRequestDate">
                <xsd:annotation>
                    <xsd:documentation>Date of an initial request.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InitialTreatment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InjuryBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InjuryEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InjuryOrIllness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InlandShip">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InquiryDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InServiceSurveyDate">
                <xsd:annotation>
                    <xsd:documentation>Date of survey of equipment while in use.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsolvencyDischargeGrantedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when insolvency discharge was granted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InspectionDate">
                <xsd:annotation>
                    <xsd:documentation>Date of inspection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstallationDateTimePeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstallmentDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstalmentPaymentDueDate">
                <xsd:annotation>
                    <xsd:documentation>Self-explanatory.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InstructionsOriginalExecutionDate">
                <xsd:annotation>
                    <xsd:documentation>Original execution date for the instruction.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsufficientAssetsJudgementDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which assets were judged to be insufficient.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InsuranceCard">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestCapitalization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestPaidThrough">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestPaidTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestPeriod">
                <xsd:annotation>
                    <xsd:documentation>Number of days used for the calculation of interests.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterestRateValidityPeriod">
                <xsd:annotation>
                    <xsd:documentation>Validity period of the interest rate.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalAssessmentApprovalForPublicationDate">
                <xsd:annotation>
                    <xsd:documentation>Date the Data Maintenance Request (DMR) was approved for publication after completing international review.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InternationalReviewCycleStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date the international review cycle starts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterruptionEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterruptionStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterviewedDate">
                <xsd:annotation>
                    <xsd:documentation>Date of an interview.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InterviewerSigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Inventory">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InventoryReportDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which a inventory report is made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvestigationEndDate">
                <xsd:annotation>
                    <xsd:documentation>The date when an investigation ended.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvestigationStartDate">
                <xsd:annotation>
                    <xsd:documentation>The date when an investigation began.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvestmentNumberAllocationDate">
                <xsd:annotation>
                    <xsd:documentation>The date that an investment number was allocated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvitationToTenderDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the invitation to tender has been made available to relevant parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceDateTime">
                <xsd:annotation>
                    <xsd:documentation>[2376] Date when a Commercial Invoice is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoiceDateRequired">
                <xsd:annotation>
                    <xsd:documentation>Date required for invoice issue.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoicePeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoicePeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InvoicingPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period for which an invoice is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IrregularInterestPaymentDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="IssueDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a document/message has been or will be issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemContractEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ItemContractExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JoinedEmployerDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a person joins an employer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JoinedFundDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a person joins a fund.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="JudgementDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which a decision from a court of law was rendered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Judgment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KeyEventCalendarYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="KeyEventFiscalYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LabourWageDeterminationDate">
                <xsd:annotation>
                    <xsd:documentation>The date a labour wage is determined.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastActivityDate">
                <xsd:annotation>
                    <xsd:documentation>Date of last activity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastAnnualReportDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the last annual report.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastCapitalChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date of last capital change.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastCertification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastChange">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastCheckForBalanceSheetUpdateDate">
                <xsd:annotation>
                    <xsd:documentation>Date balance sheet was last checked to determine if update had taken place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastCivilianFlight">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastDateForAGovernmentAgencyToFileAClaim">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastDateOfAccountsFiledAtPublicRegister">
                <xsd:annotation>
                    <xsd:documentation>Date on which accounts were last filed at the public register.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastDateOfOrdering">
                <xsd:annotation>
                    <xsd:documentation>The last date on which ordering may take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastDateToFileAClaim">
                <xsd:annotation>
                    <xsd:documentation>Date after which no claim can be filed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastDateToObject">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastEpisode">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastFlight">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastFollow-up">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastInsuranceMedical">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastInterestBearingDate">
                <xsd:annotation>
                    <xsd:documentation>The last date from which interest is borne.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastItemDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastLogged">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastMarketed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastMeal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastMenstrualCycleStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the last menstrual cycle started.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastMenstrualPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastMilitaryFlight">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastNotification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastOffHireDate">
                <xsd:annotation>
                    <xsd:documentation>Date the item was last returned from hire.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastOnHireDate">
                <xsd:annotation>
                    <xsd:documentation>Date the item was last placed on hire.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPaidInstallmentDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPaymentMade">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPhysical">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPremiumEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPremiumPaidDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastProduction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPublication">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastPublishedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when material was last published.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastSalaryIncrease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastSaleDateAndOrTimeAndOrPeriod">
                <xsd:annotation>
                    <xsd:documentation>The last date, and/or time, and/or period a product was sold.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastScreening">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastSeenDate">
                <xsd:annotation>
                    <xsd:documentation>The date when last seen.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastVisitDate">
                <xsd:annotation>
                    <xsd:documentation>Date of last visit.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LastXRayDate">
                <xsd:annotation>
                    <xsd:documentation>The date the last X-ray was taken.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LateFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestAbsence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LateStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestBalanceSheet">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestCheckInTime">
                <xsd:annotation>
                    <xsd:documentation>Latest time of check-in.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestDeliveryDateatPier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestDeliveryDateAtRailRamp">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestFilingPeriod">
                <xsd:annotation>
                    <xsd:documentation>Latest period for which a filing may be made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestMaterialSafetyDataSheetDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestMeterReadingDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the latest reading of a meter took place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestOnBoardCarrierDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestPriceAdjustmentDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the latest price adjustment took place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestReceivingDateCutoffDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LatestVisitorConsultation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LayTimeFirstDay">
                <xsd:annotation>
                    <xsd:documentation>First of a number of days allowed in a charter party of the loading and discharging of cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LayTimeLastDay">
                <xsd:annotation>
                    <xsd:documentation>Last of a number of days allowed in a charter party for the loading and discharging of cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeadTime">
                <xsd:annotation>
                    <xsd:documentation>Time required between order entry till earliest goods delivery.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseCommencement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeasePeriod">
                <xsd:annotation>
                    <xsd:documentation>The period associated with a lease.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseTermEndDate">
                <xsd:annotation>
                    <xsd:documentation>End date of the lease term.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeaseTermStartDate">
                <xsd:annotation>
                    <xsd:documentation>Start date of the lease term.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LeftEmployerDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a person leaves an employer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalActionClosedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the legal action was closed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalActionStarted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalSettlementTermsMetDate">
                <xsd:annotation>
                    <xsd:documentation>Date when terms specified in the legal settlement were met.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LegalStructureChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date when legal structure was changed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LenderCreditCheck">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LetterOfAgreementDate">
                <xsd:annotation>
                    <xsd:documentation>Date of a letter of agreement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LetterOfLiabilityDate">
                <xsd:annotation>
                    <xsd:documentation>Date of a letter of liability.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="License">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LicenseEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LicenseExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LicenseRenewal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LicenseRequested">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Lien">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LiquidationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of liquidation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListingReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ListPriceChange">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoadedonVessel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoadingDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the specified goods or transport equipment has or have been loaded in or on the means of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Loan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanPurchase">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LoanServicingTransfer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LocationExceptionOrderNumberAssigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Loss">
                <xsd:annotation>
                    <xsd:documentation>Date, time, period on which a referenced loss occurred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LotNumberExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LowestActivityPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period of lowest activity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LowPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailingDate">
                <xsd:annotation>
                    <xsd:documentation>Date when an item may be mailed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MailReceiptDate">
                <xsd:annotation>
                    <xsd:documentation>Date mail was received.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Maintenance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaintenanceComment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaintenanceEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MakegoodCommercialDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagementAvailableDate">
                <xsd:annotation>
                    <xsd:documentation>Date when management is available.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManagerNotRegisteredAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>As of this date the manager is not registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MandatoryOrTargetFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MandatoryOrTargetStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ManifestShipNoticeDate">
                <xsd:annotation>
                    <xsd:documentation>The date of issuance of a manifest or ship notice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Manufacture">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Map">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DateOfMarriage">
                <xsd:annotation>
                    <xsd:documentation>Date when two persons are married.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MasterLeaseAgreement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialChangeNotice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialClassification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaterialSpecification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Maturity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaturityDate">
                <xsd:annotation>
                    <xsd:documentation>Date at which maturity occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaximumCreditGrantedDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the highest credit was granted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeatAgeingPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period of time between slaughter and delivery during which meat is ageing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Medicaid">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicaidBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicaidEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalCertificate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalInformationSignature">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicalInformationSystem">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Medicare">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicareBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicareEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartACoverageEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartAEligibilityBeginDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartAEligibilityEndDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartATerminationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartBCoverageEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartBEligibilityBeginDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartBEligibilityEndDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MedicarePartBTerminationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Medication">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeetingDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the meeting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Merger">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Message">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MeterReading">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MidpointOfPerformance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MinimumRequiredDistribution">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MinimumShelfLifeRemainingAtTimeOfDespatchPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period indicating the minimum shelf life remaining for a product at the time of leaving the supplier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MinimumShelfLifeRemainingAtTimeOfReceipt">
                <xsd:annotation>
                    <xsd:documentation>The minimum shelf life remaining at the time of receipt.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MonthEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MonthToDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MooringDateAndTime">
                <xsd:annotation>
                    <xsd:documentation>Date and time of mooring.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MortgageeOfficialSignatureDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MortgageeReportedCurtailmentDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MortgageNoteDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostLikelyEarlyFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostLikelyEarlyStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostLikelyLateFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostLikelyLateStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostRecentHemoglobinOrHematocritOrBoth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostRecentRenewal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostRecentRevisionorInitialVersion">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MostRecentSerumCreatine">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MovedFromLocationDate">
                <xsd:annotation>
                    <xsd:documentation>The date an entity moved from a location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NegotiatedExtensionDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NegotiatedFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NegotiatedStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NegotiationsStartDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which negotiations started.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NetCreditServiceDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NetWorthDate">
                <xsd:annotation>
                    <xsd:documentation>Date of net worth.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextActivity">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextAnniversary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextAnnualMeeting">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextInstallmentDueDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextInterestChangeDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextPayIncrease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextPaymentDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NextReviewDate">
                <xsd:annotation>
                    <xsd:documentation>Date of next review.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NonWorking">
                <xsd:annotation>
                    <xsd:documentation>To specify a non working date or period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoProductionScheduleEstablishedAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoPromiseAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoShippingScheduleEstablishedAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotAfter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotBefore">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Note">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Notice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotificationDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time of notification.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotificationOfDeath">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotificationTimeLimit">
                <xsd:annotation>
                    <xsd:documentation>The time limit which has been set for a notification to take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Notified">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotRegistered">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NotRegisteredAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>Date when not yet registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NursingHome">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NursingHomeFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NursingHomeTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Occupancy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Occurrence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OccurrenceSpan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OccurrenceSpanFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OccurrenceSpanTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OfferExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OffHireSurveyDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the equipment was surveyed at the end of the current leasing period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OffHold">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OfficialRailCarInterchangeEitherActualorAgreedUpon">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Off-Market">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Offset">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OffsetBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OffsetEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OffsetFromCoordinatedUniversalTimeUtc">
                <xsd:annotation>
                    <xsd:documentation>Number of hour's offset from Coordinated Universal Time (UTC).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OldestUnpaidInstallment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="On">
                <xsd:annotation>
                    <xsd:documentation>Fixed maturity day for deferred payment or time draft(s).</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnBoardDate">
                <xsd:annotation>
                    <xsd:documentation>The date goods have been loaded on board of a conveyance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnHireSurveyDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the equipment was surveyed at the beginning of the current leasing period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnHold">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnsetOfCurrentSymptomsOrIllness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OnsetOfSimilarSymptomsOrIllness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OpenHouse">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OpeningDate">
                <xsd:annotation>
                    <xsd:documentation>Date of opening.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OperationsCeasedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when operations ceased.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Opinion">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptimisticEarlyFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptimisticEarlyStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptimisticLateFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OptimisticLateStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Option">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Order">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderAmendmentBindingDate">
                <xsd:annotation>
                    <xsd:documentation>The date when an order amendment becomes binding for both parties.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderCompletionDateTimeUltimate">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the order should be completed at the latest.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderDateTime">
                <xsd:annotation>
                    <xsd:documentation>[2010] Date when an order is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderReceivedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the purchase order is received by the seller.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrderToProceedDate">
                <xsd:annotation>
                    <xsd:documentation>Issue date of an instruction to start work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Original">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalDueDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalDuration">
                <xsd:annotation>
                    <xsd:documentation>Original length of time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalEarlyFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalEarlyStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalEstimateDate">
                <xsd:annotation>
                    <xsd:documentation>The date of the original estimate.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalIssueAge">
                <xsd:annotation>
                    <xsd:documentation>The original issue age.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalList">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginallyScheduledShip">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalMaturityDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalNameChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the original name was changed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalPostingDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the entry was originally posted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OriginalReceipt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrthodonticTreatmentPeriodRemaining">
                <xsd:annotation>
                    <xsd:documentation>The period of time that the orthodontic treatment has remaining.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OrthodonticTreatmentPeriodTotal">
                <xsd:annotation>
                    <xsd:documentation>The period of orthodontic treatment from beginning to end.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Out-Of-Pocket">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OutsideAuditorsReport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Overhauled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverrideDateForSettlement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OverTargetBaselineMonth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OvertimeEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of overtime ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OvertimeStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of overtime begins.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnershipChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date when ownership changes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OwnershipPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OxygenSaturationTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OxygenTherapy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OxygenTherapyEvaluation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OxygenTherapyFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OxygenTherapyTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackingEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which packing completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PackingStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which packing commenced.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaidInFull">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaidThroughDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Paid-ThroughDateForAccruedSickPay">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaidThroughDateForMinimumPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Paid-ThroughDateForSalaryContinuation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaidThroughDateForTotalPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Paid-ThroughDateForVacationPay">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaidToDate">
                <xsd:annotation>
                    <xsd:documentation>Date to which payments have been paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Paid-up">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaperworkMailed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartialAnnuitization">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParticipationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of participation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PartTimeWorkingChangeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when the proportion of part-time work changes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PastDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PatientFirstVisitForCondition">
                <xsd:annotation>
                    <xsd:documentation>The date of the first visit by a patient to a healthcare provider for this condition.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentCommencement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentDate">
                <xsd:annotation>
                    <xsd:documentation>[2034] Date on which an amount due is made available to the creditor, in accordance with the terms of payment.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentDateTimeDeferred">
                <xsd:annotation>
                    <xsd:documentation>Date/time when instalments are due.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentDueDate">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which funds should be made available.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentInitiated">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentInstructionDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which a payment instruction was given.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentIssue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentPeriod">
                <xsd:annotation>
                    <xsd:documentation>A period of time in which a payment has been or will be made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentPeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PaymentValuationPresentationDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the payment valuation is presented.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollDeductionDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time of a monetary deduction made from the salary of a person on a payroll.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollPeriodBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PayrollPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeakActivityPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period of peak activity.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeerReviewOrganizationPROApprovedStay">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeerReviewOrganizationPROApprovedStayFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeerReviewOrganizationPROApprovedStayTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Penalty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PenaltyBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PenaltyEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PendingArchive">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PercolationTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodAnEventIsLateDueToCustomer">
                <xsd:annotation>
                    <xsd:documentation>The period of time an event is late due to the actions of a customer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodAssigned">
                <xsd:annotation>
                    <xsd:documentation>The period assigned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodBetweenChanges">
                <xsd:annotation>
                    <xsd:documentation>The period of time between changes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodCoveredBySourceDocuments">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodExtended">
                <xsd:annotation>
                    <xsd:documentation>Number of time units added to the original end date/time/period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodicInspectionDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which a periodic inspection has to take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodOfCareEndDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time when a period of care ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodOfCareStartDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time when a period of care starts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodOfIllnessEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of illness ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodOfIllnessStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of illness began.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodOverdue">
                <xsd:annotation>
                    <xsd:documentation>The period by which an event is overdue.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PeriodWorkedForTheCompany">
                <xsd:annotation>
                    <xsd:documentation>Period of time that was worked for the company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PermitToAdmitDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which permission was granted to move merchandise into a bonded warehouse or free trade zone.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PermitYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PessimisticEarlyFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PessimisticEarlyStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PessimisticLateFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PessimisticLateStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PickUpCollectionDateTimeOfCargo">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which the cargo is picked up.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PickUpDateTimeOfEquipment">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which the equipment is picked up.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Placement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlacementDate">
                <xsd:annotation>
                    <xsd:documentation>The date something is placed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlacementDatePrevious">
                <xsd:annotation>
                    <xsd:documentation>The date something was previously placed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlacementPeriodExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Plan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanAnniversary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanEntry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlannedDurationOfWorks">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlannedRelease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanOfTreatmentPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanParticipationSuspension">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanPeriodElection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanPeriodElectionBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanPeriodElectionEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PlanTermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PluggedAndAbandoned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicyEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicyEffectiveOnOrAfter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicyEffectiveOnOrBefore">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicyExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PolicySurrender">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PoolSettlement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PositioningDateTimeOfEquipment">
                <xsd:annotation>
                    <xsd:documentation>Date/time when equipment is positioned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PositioningDateTimeOfGoods">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time the goods have to be or have been positioned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostingDate">
                <xsd:annotation>
                    <xsd:documentation>The date when an entry is posted to an account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PostmarkDateTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Post-Reclassification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Post-ReclassificationEndOfSecondYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Post-ReclassificationFirstReportCard">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Post-ReclassificationFirstSemi-annual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Post-ReclassificationSecondSemi-annual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrearrangedDealMatch">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-AwardSurvey">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrecedingPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period preceding current period.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pre-certificationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreforeclosureAcceptanceDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreforeclosureSaleClosingDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PregnancyBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PregnancyDurationActual">
                <xsd:annotation>
                    <xsd:documentation>Actual duration of pregnancy.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PregnancyEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiumChangePeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify the period of the premium change.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiumPaidToDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiumPaidToDateBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PremiumPaidToDateEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreparationDateTimeOfDocument">
                <xsd:annotation>
                    <xsd:documentation>Date and/or time that the document was prepared.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Prescription">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrescriptionDispensingDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time when a prescription was dispensed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrescriptionFill">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrescriptionFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrescriptionIssueDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time when a prescription was issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrescriptionTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentationDateLatest">
                <xsd:annotation>
                    <xsd:documentation>Latest date for presentation of a document.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentationDateOfGoodsDeclarationCustoms">
                <xsd:annotation>
                    <xsd:documentation>[2032] Date on which a Goods declaration is presented or lodged with Customs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentationToBankruptcyReceiversDate">
                <xsd:annotation>
                    <xsd:documentation>Date when presented to the bankruptcy receivers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentControl">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentedToReceivers">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentLegalStructure">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentlyResiding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentmentDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentName">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PresentResidenceStartDate">
                <xsd:annotation>
                    <xsd:documentation>The beginning date of residence at present location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousBookingDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time at which the previous booking was made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousCourtEvent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousCurrentAccountDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the previous current account.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousEmployment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousEmploymentEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousEmploymentStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousInvoiceDate">
                <xsd:annotation>
                    <xsd:documentation>Indicates the date which was allocated to a previous invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviouslyReportedDateOfBirth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviouslyResided">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousMeterReadingDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the previous reading of a meter took place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousMonthEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousReportDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the previous report.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousResidence">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousResidenceEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousResidenceStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PreviousTerminationDate">
                <xsd:annotation>
                    <xsd:documentation>Identifies the date of the previous termination.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceAdjustmentDate">
                <xsd:annotation>
                    <xsd:documentation>Value date of indexes appearing as numerators in a price adjustment formula.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceAdjustmentLimitDate">
                <xsd:annotation>
                    <xsd:documentation>Limit value date of indexes used as numerators in a price adjustment formula.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceAdjustmentStartDate">
                <xsd:annotation>
                    <xsd:documentation>Value date of the indexes appearing as denominators in a price adjustment formula.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceBaseDateTime">
                <xsd:annotation>
                    <xsd:documentation>Base date/time of prices.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriceProtection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryCareProvider">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryCareProviderBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrimaryCareProviderEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Printed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorContractAnniversary">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorContractIssue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorHospitalizationDatesRelatedToCurrentServices">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorIncorrectDateOfBirth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorNotice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PriorPlacement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivilegeDetailsVerificationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when privilege details were verified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessingDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time of processing.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessingDatePeriod">
                <xsd:annotation>
                    <xsd:documentation>Date/period a specific process happened/will happen.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessingEndDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/Time when a specific process ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProcessingStartDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/Time when a specific process starts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductAvailabilityDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductHeldUntil">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Production">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductionDateNoScheduleEstablishedAsOf">
                <xsd:annotation>
                    <xsd:documentation>Date as of there is no valid production schedule.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductionInspectionDate">
                <xsd:annotation>
                    <xsd:documentation>Date of production inspection.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductionManufactureDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which goods are produced.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProductLifespanAtTimeOfProduction">
                <xsd:annotation>
                    <xsd:documentation>The total lifespan of a product at the time of its production.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProfitPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period over which profit was earned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProgrammedCalendarYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProgrammedFiscalYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProgressPayment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectedActionEndDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectedForeclosureSaleDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProjectOverTargetBaselineDate">
                <xsd:annotation>
                    <xsd:documentation>The date an over target baseline was implemented for a project.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForDeliveryAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForDeliveryPriorToAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForDeliveryWeekOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForShipment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForShipmentAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForShipmentPriortoAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromisedForShipmentWeekOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionDatePeriod">
                <xsd:annotation>
                    <xsd:documentation>Date/period relevant for specific promotion activities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionEndDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when promotion activities end.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionFloorStockProtect-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionFloorStockProtect-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionInvoicePerformance-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionInvoicePerformance-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionOrder-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionOrder-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionPerformance-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionPerformance-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionRequestedDelivery-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionRequestedDelivery-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionShip-End">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionShip-Start">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PromotionStartDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when promotion activities begin.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Property">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PropertyAcquired">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PropertyBuilt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrototypeTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ProviderSignatureDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the provider signed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PublicationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchasedDate">
                <xsd:annotation>
                    <xsd:documentation>Date of purchase.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOrder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOrderLatestPossibleChangeDate">
                <xsd:annotation>
                    <xsd:documentation>Date identifying a point of time after which a purchase order cannot be changed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PurchaseOrderReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PutIntoService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QualityRating">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuarterEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuotaDate">
                <xsd:annotation>
                    <xsd:documentation>Date that the quota applies to.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Quote">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuoteToBeReceivedBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RateAdjustment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RateDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RateOfExchangeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the exchange rate was fixed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RatePriceDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which a rate/price is determined.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReachedTotalDepth">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReadyForInspection">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RealEstateProperty">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RealEstateTaxYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Rebuilt">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Recapture">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivablesCollectionPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time over which receivable accounts are collected.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Received">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiveAtLockboxDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which a financial institution, serving as collection agency for a company located in another part of the country, collects an amount of money on behalf of that company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivedDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time of receipt.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivedInTheMail">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceiverAppointed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivershipPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period of time a receivership lasts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReceivershipResultDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the result of the receivership occurs.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reclassification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReclassificationExitDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Recompletion">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecordedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when recorded.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Recovery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecoveryFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RecoveryStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Re-entry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Re-evaluationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReExportationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of re-exportation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferenceDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the reference was issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferencedItemCreationDate">
                <xsd:annotation>
                    <xsd:documentation>Creation date of referenced item.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferralDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferredFrom">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReferredTo">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Refinance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RefusalPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period within which a refusal can be made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Registered">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegisteredContractorNumberAssignmentDateOriginal">
                <xsd:annotation>
                    <xsd:documentation>Date when a registered contractor number was originally assigned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationDateOfPreviousCustomsDeclaration">
                <xsd:annotation>
                    <xsd:documentation>Registration date of the Customs declaration for the previous Customs procedure either in the same or another country.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RegistrationOfBoardOfDirectors">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Rehire">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReimbursementClaimIssueDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>Date and or time when a reimbursement claim is issued.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReImportationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of re-importation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinstatementDate">
                <xsd:annotation>
                    <xsd:documentation>Identifies the date of reinstatement.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsuranceAccountingPeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify a reinsurance account period via start and end dates.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsuranceCoverageDuration">
                <xsd:annotation>
                    <xsd:documentation>The period for which reinsurance coverage has been in force.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsuranceCurrentAccountPeriod">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsuranceEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsuranceIssueAge">
                <xsd:annotation>
                    <xsd:documentation>The actual or equivalent age at time of issue.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsurancePaidUpDate">
                <xsd:annotation>
                    <xsd:documentation>The date up to which the reinsurance has been paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReinsurancePaymentFrequency">
                <xsd:annotation>
                    <xsd:documentation>The frequency of payments of reinsurance premiums.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Reissue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Rejected">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time assigned to identify the release of a set of rules, conditions, conventions, productions, etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseDateCustoms">
                <xsd:annotation>
                    <xsd:documentation>Date on which Customs releases merchandise to the carrier or importer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseDateOfCustomer">
                <xsd:annotation>
                    <xsd:documentation>Date the customer authorised the goods' release.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseDateOfSupplier">
                <xsd:annotation>
                    <xsd:documentation>Date when the supplier released goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleasedReturnToWork">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleasedToVessel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseFrequency">
                <xsd:annotation>
                    <xsd:documentation>Frequency of a release.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReleaseOfInformation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Remittance">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RemodelingCompleted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Removed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RenewalDate">
                <xsd:annotation>
                    <xsd:documentation>Date of renewal.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentalPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RentSurvey">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepairTurnaroundTime">
                <xsd:annotation>
                    <xsd:documentation>Provides the period of time necessary to turnaround a given repair.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepaymentBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RepaymentScheduleSent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Replaced">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Replacement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReplacementEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReplyDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Report">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when reported.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportEndDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportingCycleDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period covered by the report.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReportStartDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Requalification">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Request">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedContract">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedDepartureDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeRequestedForAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Delivery is requested to happen after or on given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DeliveryDateTimeRequestedForPriorToAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Delivery is requested to happen prior to or including the given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedForDeliveryWeekOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipmentDateTimeRequestedForAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Shipment should happen earliest at given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipmentDateTimeRequestedForPriorToAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Shipment should take place latest at given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedForShipmentWeekOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedOffer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedPick-up">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedSettlement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedShip">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestedStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequestForQuotation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequiredBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RequiredDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReservationOfFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidentLicenseEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResidentLicenseExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResignationDate">
                <xsd:annotation>
                    <xsd:documentation>Date of resignation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Resigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ResolvedDate">
                <xsd:annotation>
                    <xsd:documentation>Date when resolved.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RestDay">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RestFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RestStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Resubmission">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RetentionReleaseDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the retention is released.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RetentionReleaseDateTimePartial">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the retention is partially released.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Retermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Retirement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RetransmissionTimeStamp">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnedDate">
                <xsd:annotation>
                    <xsd:documentation>Date return takes place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnedToCustomer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnToWorkDate">
                <xsd:annotation>
                    <xsd:documentation>Date of return to work.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReturnToWorkPartTime">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReviewDate">
                <xsd:annotation>
                    <xsd:documentation>Date the item was or will be reviewed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ReviewPeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RevisedAdjustedEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RevisedAdjustedStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RevisedEstimateDate">
                <xsd:annotation>
                    <xsd:documentation>The date the estimate was revised.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Revision">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Revocation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Revoked">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RigArrival">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoadFundTaxExpiryDate">
                <xsd:annotation>
                    <xsd:documentation>The date of expiry of the road fund tax.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sailing">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalaryChangeEffectiveDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a change in salary becomes effective.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalaryDeferral">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SaleableStockDemandCoverPeriodExpected">
                <xsd:annotation>
                    <xsd:documentation>A period of time when saleable stocks are expected to cover demand for a product.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalesDateAndOrTimeAndOrPeriod">
                <xsd:annotation>
                    <xsd:documentation>The date, and or time, and or period on which a sale took place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SalesPeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SampleAvailableDate">
                <xsd:annotation>
                    <xsd:documentation>Date as of a sample will be available seller defined.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SampleCollected">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SampleRequiredDate">
                <xsd:annotation>
                    <xsd:documentation>Date as of a sample has to be available customer defined.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SamplesToBeReturnedBy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sanction">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SatisfactionDate">
                <xsd:annotation>
                    <xsd:documentation>Date when satisfaction was obtained.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Satisfied">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Schedule">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledFinish">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledForDeliveryOnOrAfter">
                <xsd:annotation>
                    <xsd:documentation>Scheduled for delivery on or after the specified date, and or time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledForDeliveryOnOrBefore">
                <xsd:annotation>
                    <xsd:documentation>Scheduled for delivery on or before specified date and or time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledForDeliveryWeekOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledForShipmentAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledForShipmentPriorToAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledForShipmentWeekOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledInterchangeDelivery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduledStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ScheduleRelease">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SchoolRefund">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Seal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondAdmissionDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecondDischargeDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecurityRateAdjustment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SecuritySharePriceAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>Date of the security share price.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Selected">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SelectionPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of selection ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SelectionPeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of selection begins.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellByDate">
                <xsd:annotation>
                    <xsd:documentation>The date by which a product should be sold.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SellersLocalTime">
                <xsd:annotation>
                    <xsd:documentation>Time at the seller's location.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SemiannualEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SendingRoadTimeStamp">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SepticApproval">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Service">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceCompletionDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation>Actual date/time on which the service was completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceInterruption">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicePeriodEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicePeriodStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServiceRequested">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ServicerSignatureDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Setoff">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Settled">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementDate">
                <xsd:annotation>
                    <xsd:documentation>Date for settlement of financial transaction e.g. foreign exchange securities.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementDateAsSpecifiedByTheOriginator">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementDateFromInterlineSettlementSystemOnly">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementDatePlanned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementDueDate">
                <xsd:annotation>
                    <xsd:documentation>More generic than 'payment due date' and therefore more apt for reinsurance/insurance business.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SettlementStatus">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SharePrice">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShelfLifeExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipDateOriginallyScheduled">
                <xsd:annotation>
                    <xsd:documentation>The date on which the shipment of goods was originally scheduled.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipDuringDate">
                <xsd:annotation>
                    <xsd:documentation>The date identifying the period during or in which the goods should be shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipmentDateTimePromisedFor">
                <xsd:annotation>
                    <xsd:documentation>Shipment might happen at given date/time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipmentDateTimePromisedForPriorToAndIncluding">
                <xsd:annotation>
                    <xsd:documentation>Shipment might take place latest at given date.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipmentDateTimeRequested">
                <xsd:annotation>
                    <xsd:documentation>Date on which goods should be shipped or despatched by the supplier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipNotLaterThanDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time by which the goods should have been shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipNotBeforeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Goods should not be shipped before given date/time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipNotBeforeAndNotAfterDateTime">
                <xsd:annotation>
                    <xsd:documentation>Shipment(s) of goods is/are to be made not before the first specified date/time and not after the second specified date/time.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipOnOrAboutDate">
                <xsd:annotation>
                    <xsd:documentation>Date on or about which goods should be shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shipped">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingDateAndOrTimeCurrentSchedule">
                <xsd:annotation>
                    <xsd:documentation>Shipping date and/or time as currently scheduled.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShippingDateNoScheduleEstablishedAsOf">
                <xsd:annotation>
                    <xsd:documentation>As at this date no valid shipping schedule has been established.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipWeekOfDate">
                <xsd:annotation>
                    <xsd:documentation>Date identifying the week during which goods should be shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShutIn">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SicknessBenefitAgeLimit">
                <xsd:annotation>
                    <xsd:documentation>To identify the age to which benefits are provided to the insured in the event of sickness.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SicknessBenefitPeriod">
                <xsd:annotation>
                    <xsd:documentation>To identify the period of time for which benefits are provided in the event of sickness.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SicknessInsuranceEliminationPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period of time the insured must be disabled in the event of sickness for benefits to be payable by the ceding company.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SicknessLifetimeBenefitQualificationAge">
                <xsd:annotation>
                    <xsd:documentation>To identify the qualification age for lifetime benefits provided to the insured in the event of sickness.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SidetrackedWellbore">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SignatureDate">
                <xsd:annotation>
                    <xsd:documentation>Date of signature.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SignatureReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SimilarIllnessOnsetDate">
                <xsd:annotation>
                    <xsd:documentation>The date of the onset of an illness similar to the illness currently being treated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SimpleInterestDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlaughteringEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which slaughtering completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SlaughteringStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which slaughtering commenced.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SocialSecurityClaimsVerificationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when social security claims were verified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Sold">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoldBookPublication">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SoleDirectorshipRegistrationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when sole directorship was registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Solicitation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SourceDocumentCaptureDate">
                <xsd:annotation>
                    <xsd:documentation>Date source document data is entered into a business application.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpacingOrderUnitAssigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SponsoredDependent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SponsoredDependentBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SponsoredDependentEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SpouseRetirement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Spud">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which a period starts.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeActual">
                <xsd:annotation>
                    <xsd:documentation>The actual date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeEarliest">
                <xsd:annotation>
                    <xsd:documentation>The earliest date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeEstimated">
                <xsd:annotation>
                    <xsd:documentation>The estimated date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeLatest">
                <xsd:annotation>
                    <xsd:documentation>The latest date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeMandatory">
                <xsd:annotation>
                    <xsd:documentation>The mandatory date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeNotAfter">
                <xsd:annotation>
                    <xsd:documentation>The not after date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeNotBefore">
                <xsd:annotation>
                    <xsd:documentation>The not before date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateOrTimeScheduled">
                <xsd:annotation>
                    <xsd:documentation>The scheduled date or time for starting.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartDateTimePlanned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartedDoingBusiness">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartNoEarlierThan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StartNoLaterThan">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Statement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateResidencyDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StateTermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatisticalTimeSeriesDate">
                <xsd:annotation>
                    <xsd:documentation>Date for statistical time series purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Status">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusAfterAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusAssignmentDate">
                <xsd:annotation>
                    <xsd:documentation>Date a status was assigned.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusChangeDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time when a status changes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusCommercial">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusOutsideProcessor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StatusPriorAndIncluding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StopDistributionDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which distribution is to stop.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StopWorkAsOfGivenDate">
                <xsd:annotation>
                    <xsd:documentation>Date work stopped or will stop.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Stored">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StudentSigned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubcontractorPeriodOfHire">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubmissionDate">
                <xsd:annotation>
                    <xsd:documentation>The date of a submission.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Submittal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubsequentInterestPaymentDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SubsequentSubmission">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupersededDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time being overlaid by a date given elsewhere.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplementalIssue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuppliersAverageCreditPeriod">
                <xsd:annotation>
                    <xsd:documentation>The average period of time that credit is extended by suppliers.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurfaceCasingAuthorityApproval">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Surgery">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Surrender">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SurveyYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuspendedDate">
                <xsd:annotation>
                    <xsd:documentation>Date of suspension.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuspensionEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SuspensionPeriod">
                <xsd:annotation>
                    <xsd:documentation>The period for which something is suspended.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SwornAndSubscribed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SymptomsOnsetPatientAlleged">
                <xsd:annotation>
                    <xsd:documentation>Date and/or time of onset of symptoms according to the patient.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SystemSurvey">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SystemTest">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TableRatingEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TailCoverageBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TailCoverageEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TanksAcceptedDateTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time the tanks are to be or have been accepted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TanksInspectedDateTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time the tanks are to be or have been inspected.  Note: 1. This period is not the same as "reinsurance current account period".</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tax">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period a tax rate/tax amount etc. is applicable.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a tax period ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxPeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a tax period begins.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxPointDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which tax is due or calculated.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TaxYear">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TechnicalRating">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TelexDate">
                <xsd:annotation>
                    <xsd:documentation>Date identifying when a telex message was sent.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TemporaryProsthesisDate">
                <xsd:annotation>
                    <xsd:documentation>The date a temporary prosthetic device was provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TenderSubmissionDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time on which the tender was submitted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminatedDate">
                <xsd:annotation>
                    <xsd:documentation>Date termination takes place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Termination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminationOfFacility">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TermsDiscountDueDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date by which payment should be made if discount terms are to apply.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TermsMet">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TermsNetDueDate">
                <xsd:annotation>
                    <xsd:documentation>Date by which payment must be made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestCompletionDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a test has been completed.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestPerformed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestPeriodEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TestStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a test has been started.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TexasLineTermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Therapy">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TimeEmployeeBeganWork">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TimeInUS">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TimeLimit">
                <xsd:annotation>
                    <xsd:documentation>The time limit in which an event must take place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TimeNowDate">
                <xsd:annotation>
                    <xsd:documentation>A time now date used for planning and scheduling purposes.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TimePeriod">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TitleTransfer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ToolingRequiredDate">
                <xsd:annotation>
                    <xsd:documentation>Date as of a tool has to be available customer defined.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TotalShelfLifePeriod">
                <xsd:annotation>
                    <xsd:documentation>A period indicating the total shelf life of a product.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Tour">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TradeStyleRegistrationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when trade style was registered.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrainingBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrainingEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransactionCreationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransferNoteAcceptanceDateAndTime">
                <xsd:annotation>
                    <xsd:documentation>Date and time when a transfer note (transfer document for transport exclusively using containers as equipment) is recognised as being valid by the carrier.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transferred">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TranshipmentDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date and time of the transfer of the goods from one means of transport to another.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transition">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransitTimeLimits">
                <xsd:annotation>
                    <xsd:documentation>Description to be provided.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransmissionDateTimeOfDocument">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Transmitted">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentStrippingDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time on which the stripping of a transport equipment is to or has taken place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TransportEquipmentStuffingDateAndOrTime">
                <xsd:annotation>
                    <xsd:documentation>The date and/or time on which the stuffing of transport equipment is to or has taken place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Travel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Treatment">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TreatmentEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TreatmentStart">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrialBalancePeriod">
                <xsd:annotation>
                    <xsd:documentation>Period covered by the trial balance.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrialSet">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrialStartDateActual">
                <xsd:annotation>
                    <xsd:documentation>Date when the trial actually started.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TrialStartDateScheduled">
                <xsd:annotation>
                    <xsd:documentation>Date when a trial is scheduled to begin.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Trust">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnderwritingYear">
                <xsd:annotation>
                    <xsd:documentation>Year in which the treaty was commenced.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="UnloadedDateAndTime">
                <xsd:annotation>
                    <xsd:documentation>To report the date and time that an unloading action occurred.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValidationDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValidityPeriod">
                <xsd:annotation>
                    <xsd:documentation>Dates (from/to)/period referenced documents are valid.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValidityStartDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValidUntil">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValuationDateTime">
                <xsd:annotation>
                    <xsd:documentation>Date/time of valuation.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValuationDateCustoms">
                <xsd:annotation>
                    <xsd:documentation>Date when Customs valuation was made.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValueAddedTaxClaimsVerificationDate">
                <xsd:annotation>
                    <xsd:documentation>Date when the Value Added Tax (VAT) claims were verified.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValueDate">
                <xsd:annotation>
                    <xsd:documentation>Date on which the funds are at the disposal of the beneficiary or cease to be at the disposal of the ordering customer.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ValueDateOfIndex">
                <xsd:annotation>
                    <xsd:documentation>Date of validity of index values.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VerbalReportNeeded">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VerificationReceived">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VerificationSent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Verified">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Version">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VesselRegistry">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VestingService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VestingServiceBegin">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VestingServiceEnd">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VoluntaryTermination">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="VoucherDateOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WageEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WagesEarned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WagePeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of wage ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WagePeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of wage begins.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaitingTime">
                <xsd:annotation>
                    <xsd:documentation>The period of time between the moment at which one wants an activity to begin and the moment at which this activity can actually begin.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WarrantyExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Waybill">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WeekEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Weighed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WellLogRun">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WholesaleContractEffective">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WholesaleContractExpiration">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WillAdviseAsOf">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WithdrawnDate">
                <xsd:annotation>
                    <xsd:documentation>The date when something was retracted.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WithheldDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorkEffectiveStartDate">
                <xsd:annotation>
                    <xsd:documentation>The date on which work will effectively start.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorkingPeriodEndDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of work ends.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorkingPeriodStartDate">
                <xsd:annotation>
                    <xsd:documentation>Date when a period of work begins.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorkPeriod">
                <xsd:annotation>
                    <xsd:documentation>Period of execution of works.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorksCompletionDateTimeActual">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorksCompletionDateTimePlanned">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WorstTimeToCall">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YearBeginning">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YearDue">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YearEnding">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YearOfOccurrence">
                <xsd:annotation>
                    <xsd:documentation>Year in which a specific event (e.g. a loss) took place.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="YearToDate">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
