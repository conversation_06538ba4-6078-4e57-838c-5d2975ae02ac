<?xml version="1.0" encoding="UTF-8"?>
<!--
  Library:           OASIS Universal Business Language (UBL) 2.1 OS
                     http://docs.oasis-open.org/ubl/os-UBL-2.1/
  Release Date:      04 November 2013
  Module:            xsd/common/UBL-CommonBasicComponents-2.1.xsd
  Generated on:      2013-10-31 17:17z
  Copyright (c) OASIS Open 2013. All Rights Reserved.
-->
<xsd:schema xmlns="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"
            xmlns:udt="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            elementFormDefault="qualified"
            attributeFormDefault="unqualified"
            version="2.1">
   <!-- ===== Imports ===== -->
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"
               schemaLocation="UBL-QualifiedDataTypes-2.1.xsd"/>
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"
               schemaLocation="UBL-UnqualifiedDataTypes-2.1.xsd"/>
   <!-- ===== Element Declarations ===== -->
   <xsd:element name="AcceptedIndicator" type="AcceptedIndicatorType"/>
   <xsd:element name="AcceptedVariantsDescription"
                type="AcceptedVariantsDescriptionType"/>
   <xsd:element name="AccountFormatCode" type="AccountFormatCodeType"/>
   <xsd:element name="AccountID" type="AccountIDType"/>
   <xsd:element name="AccountTypeCode" type="AccountTypeCodeType"/>
   <xsd:element name="AccountingCost" type="AccountingCostType"/>
   <xsd:element name="AccountingCostCode" type="AccountingCostCodeType"/>
   <xsd:element name="ActionCode" type="ActionCodeType"/>
   <xsd:element name="ActivityType" type="ActivityTypeType"/>
   <xsd:element name="ActivityTypeCode" type="ActivityTypeCodeType"/>
   <xsd:element name="ActualDeliveryDate" type="ActualDeliveryDateType"/>
   <xsd:element name="ActualDeliveryTime" type="ActualDeliveryTimeType"/>
   <xsd:element name="ActualDespatchDate" type="ActualDespatchDateType"/>
   <xsd:element name="ActualDespatchTime" type="ActualDespatchTimeType"/>
   <xsd:element name="ActualPickupDate" type="ActualPickupDateType"/>
   <xsd:element name="ActualPickupTime" type="ActualPickupTimeType"/>
   <xsd:element name="ActualTemperatureReductionQuantity"
                type="ActualTemperatureReductionQuantityType"/>
   <xsd:element name="AdValoremIndicator" type="AdValoremIndicatorType"/>
   <xsd:element name="AdditionalAccountID" type="AdditionalAccountIDType"/>
   <xsd:element name="AdditionalConditions" type="AdditionalConditionsType"/>
   <xsd:element name="AdditionalInformation" type="AdditionalInformationType"/>
   <xsd:element name="AdditionalStreetName" type="AdditionalStreetNameType"/>
   <xsd:element name="AddressFormatCode" type="AddressFormatCodeType"/>
   <xsd:element name="AddressTypeCode" type="AddressTypeCodeType"/>
   <xsd:element name="AdjustmentReasonCode" type="AdjustmentReasonCodeType"/>
   <xsd:element name="AdmissionCode" type="AdmissionCodeType"/>
   <xsd:element name="AdvertisementAmount" type="AdvertisementAmountType"/>
   <xsd:element name="AgencyID" type="AgencyIDType"/>
   <xsd:element name="AgencyName" type="AgencyNameType"/>
   <xsd:element name="AirFlowPercent" type="AirFlowPercentType"/>
   <xsd:element name="AircraftID" type="AircraftIDType"/>
   <xsd:element name="AliasName" type="AliasNameType"/>
   <xsd:element name="AllowanceChargeReason" type="AllowanceChargeReasonType"/>
   <xsd:element name="AllowanceChargeReasonCode" type="AllowanceChargeReasonCodeType"/>
   <xsd:element name="AllowanceTotalAmount" type="AllowanceTotalAmountType"/>
   <xsd:element name="AltitudeMeasure" type="AltitudeMeasureType"/>
   <xsd:element name="Amount" type="AmountType"/>
   <xsd:element name="AmountRate" type="AmountRateType"/>
   <xsd:element name="AnimalFoodApprovedIndicator"
                type="AnimalFoodApprovedIndicatorType"/>
   <xsd:element name="AnimalFoodIndicator" type="AnimalFoodIndicatorType"/>
   <xsd:element name="AnnualAverageAmount" type="AnnualAverageAmountType"/>
   <xsd:element name="ApplicationStatusCode" type="ApplicationStatusCodeType"/>
   <xsd:element name="ApprovalDate" type="ApprovalDateType"/>
   <xsd:element name="ApprovalStatus" type="ApprovalStatusType"/>
   <xsd:element name="AttributeID" type="AttributeIDType"/>
   <xsd:element name="AuctionConstraintIndicator" type="AuctionConstraintIndicatorType"/>
   <xsd:element name="AuctionURI" type="AuctionURIType"/>
   <xsd:element name="AvailabilityDate" type="AvailabilityDateType"/>
   <xsd:element name="AvailabilityStatusCode" type="AvailabilityStatusCodeType"/>
   <xsd:element name="AverageAmount" type="AverageAmountType"/>
   <xsd:element name="AverageSubsequentContractAmount"
                type="AverageSubsequentContractAmountType"/>
   <xsd:element name="AwardDate" type="AwardDateType"/>
   <xsd:element name="AwardTime" type="AwardTimeType"/>
   <xsd:element name="AwardingCriterionDescription"
                type="AwardingCriterionDescriptionType"/>
   <xsd:element name="AwardingCriterionID" type="AwardingCriterionIDType"/>
   <xsd:element name="AwardingCriterionTypeCode" type="AwardingCriterionTypeCodeType"/>
   <xsd:element name="AwardingMethodTypeCode" type="AwardingMethodTypeCodeType"/>
   <xsd:element name="BackOrderAllowedIndicator" type="BackOrderAllowedIndicatorType"/>
   <xsd:element name="BackorderQuantity" type="BackorderQuantityType"/>
   <xsd:element name="BackorderReason" type="BackorderReasonType"/>
   <xsd:element name="BalanceAmount" type="BalanceAmountType"/>
   <xsd:element name="BalanceBroughtForwardIndicator"
                type="BalanceBroughtForwardIndicatorType"/>
   <xsd:element name="BarcodeSymbologyID" type="BarcodeSymbologyIDType"/>
   <xsd:element name="BaseAmount" type="BaseAmountType"/>
   <xsd:element name="BaseQuantity" type="BaseQuantityType"/>
   <xsd:element name="BaseUnitMeasure" type="BaseUnitMeasureType"/>
   <xsd:element name="BasedOnConsensusIndicator" type="BasedOnConsensusIndicatorType"/>
   <xsd:element name="BasicConsumedQuantity" type="BasicConsumedQuantityType"/>
   <xsd:element name="BatchQuantity" type="BatchQuantityType"/>
   <xsd:element name="BestBeforeDate" type="BestBeforeDateType"/>
   <xsd:element name="BindingOnBuyerIndicator" type="BindingOnBuyerIndicatorType"/>
   <xsd:element name="BirthDate" type="BirthDateType"/>
   <xsd:element name="BirthplaceName" type="BirthplaceNameType"/>
   <xsd:element name="BlockName" type="BlockNameType"/>
   <xsd:element name="BrandName" type="BrandNameType"/>
   <xsd:element name="BrokerAssignedID" type="BrokerAssignedIDType"/>
   <xsd:element name="BudgetYearNumeric" type="BudgetYearNumericType"/>
   <xsd:element name="BuildingName" type="BuildingNameType"/>
   <xsd:element name="BuildingNumber" type="BuildingNumberType"/>
   <xsd:element name="BulkCargoIndicator" type="BulkCargoIndicatorType"/>
   <xsd:element name="BusinessClassificationEvidenceID"
                type="BusinessClassificationEvidenceIDType"/>
   <xsd:element name="BusinessIdentityEvidenceID" type="BusinessIdentityEvidenceIDType"/>
   <xsd:element name="BuyerEventID" type="BuyerEventIDType"/>
   <xsd:element name="BuyerProfileURI" type="BuyerProfileURIType"/>
   <xsd:element name="BuyerReference" type="BuyerReferenceType"/>
   <xsd:element name="CV2ID" type="CV2IDType"/>
   <xsd:element name="CalculationExpression" type="CalculationExpressionType"/>
   <xsd:element name="CalculationExpressionCode" type="CalculationExpressionCodeType"/>
   <xsd:element name="CalculationMethodCode" type="CalculationMethodCodeType"/>
   <xsd:element name="CalculationRate" type="CalculationRateType"/>
   <xsd:element name="CalculationSequenceNumeric" type="CalculationSequenceNumericType"/>
   <xsd:element name="CallBaseAmount" type="CallBaseAmountType"/>
   <xsd:element name="CallDate" type="CallDateType"/>
   <xsd:element name="CallExtensionAmount" type="CallExtensionAmountType"/>
   <xsd:element name="CallTime" type="CallTimeType"/>
   <xsd:element name="CancellationNote" type="CancellationNoteType"/>
   <xsd:element name="CandidateReductionConstraintIndicator"
                type="CandidateReductionConstraintIndicatorType"/>
   <xsd:element name="CandidateStatement" type="CandidateStatementType"/>
   <xsd:element name="CanonicalizationMethod" type="CanonicalizationMethodType"/>
   <xsd:element name="CapabilityTypeCode" type="CapabilityTypeCodeType"/>
   <xsd:element name="CardChipCode" type="CardChipCodeType"/>
   <xsd:element name="CardTypeCode" type="CardTypeCodeType"/>
   <xsd:element name="CargoTypeCode" type="CargoTypeCodeType"/>
   <xsd:element name="CarrierAssignedID" type="CarrierAssignedIDType"/>
   <xsd:element name="CarrierServiceInstructions" type="CarrierServiceInstructionsType"/>
   <xsd:element name="CatalogueIndicator" type="CatalogueIndicatorType"/>
   <xsd:element name="CategoryName" type="CategoryNameType"/>
   <xsd:element name="CertificateType" type="CertificateTypeType"/>
   <xsd:element name="CertificateTypeCode" type="CertificateTypeCodeType"/>
   <xsd:element name="ChangeConditions" type="ChangeConditionsType"/>
   <xsd:element name="Channel" type="ChannelType"/>
   <xsd:element name="ChannelCode" type="ChannelCodeType"/>
   <xsd:element name="CharacterSetCode" type="CharacterSetCodeType"/>
   <xsd:element name="Characteristics" type="CharacteristicsType"/>
   <xsd:element name="ChargeIndicator" type="ChargeIndicatorType"/>
   <xsd:element name="ChargeTotalAmount" type="ChargeTotalAmountType"/>
   <xsd:element name="ChargeableQuantity" type="ChargeableQuantityType"/>
   <xsd:element name="ChargeableWeightMeasure" type="ChargeableWeightMeasureType"/>
   <xsd:element name="ChildConsignmentQuantity" type="ChildConsignmentQuantityType"/>
   <xsd:element name="ChipApplicationID" type="ChipApplicationIDType"/>
   <xsd:element name="CityName" type="CityNameType"/>
   <xsd:element name="CitySubdivisionName" type="CitySubdivisionNameType"/>
   <xsd:element name="CodeValue" type="CodeValueType"/>
   <xsd:element name="CollaborationPriorityCode" type="CollaborationPriorityCodeType"/>
   <xsd:element name="Comment" type="CommentType"/>
   <xsd:element name="CommodityCode" type="CommodityCodeType"/>
   <xsd:element name="CompanyID" type="CompanyIDType"/>
   <xsd:element name="CompanyLegalForm" type="CompanyLegalFormType"/>
   <xsd:element name="CompanyLegalFormCode" type="CompanyLegalFormCodeType"/>
   <xsd:element name="CompanyLiquidationStatusCode"
                type="CompanyLiquidationStatusCodeType"/>
   <xsd:element name="ComparedValueMeasure" type="ComparedValueMeasureType"/>
   <xsd:element name="ComparisonDataCode" type="ComparisonDataCodeType"/>
   <xsd:element name="ComparisonDataSourceCode" type="ComparisonDataSourceCodeType"/>
   <xsd:element name="ComparisonForecastIssueDate"
                type="ComparisonForecastIssueDateType"/>
   <xsd:element name="ComparisonForecastIssueTime"
                type="ComparisonForecastIssueTimeType"/>
   <xsd:element name="CompletionIndicator" type="CompletionIndicatorType"/>
   <xsd:element name="Condition" type="ConditionType"/>
   <xsd:element name="ConditionCode" type="ConditionCodeType"/>
   <xsd:element name="Conditions" type="ConditionsType"/>
   <xsd:element name="ConditionsDescription" type="ConditionsDescriptionType"/>
   <xsd:element name="ConsigneeAssignedID" type="ConsigneeAssignedIDType"/>
   <xsd:element name="ConsignmentQuantity" type="ConsignmentQuantityType"/>
   <xsd:element name="ConsignorAssignedID" type="ConsignorAssignedIDType"/>
   <xsd:element name="ConsolidatableIndicator" type="ConsolidatableIndicatorType"/>
   <xsd:element name="ConstitutionCode" type="ConstitutionCodeType"/>
   <xsd:element name="ConsumerIncentiveTacticTypeCode"
                type="ConsumerIncentiveTacticTypeCodeType"/>
   <xsd:element name="ConsumerUnitQuantity" type="ConsumerUnitQuantityType"/>
   <xsd:element name="ConsumersEnergyLevel" type="ConsumersEnergyLevelType"/>
   <xsd:element name="ConsumersEnergyLevelCode" type="ConsumersEnergyLevelCodeType"/>
   <xsd:element name="ConsumptionEnergyQuantity" type="ConsumptionEnergyQuantityType"/>
   <xsd:element name="ConsumptionID" type="ConsumptionIDType"/>
   <xsd:element name="ConsumptionLevel" type="ConsumptionLevelType"/>
   <xsd:element name="ConsumptionLevelCode" type="ConsumptionLevelCodeType"/>
   <xsd:element name="ConsumptionReportID" type="ConsumptionReportIDType"/>
   <xsd:element name="ConsumptionType" type="ConsumptionTypeType"/>
   <xsd:element name="ConsumptionTypeCode" type="ConsumptionTypeCodeType"/>
   <xsd:element name="ConsumptionWaterQuantity" type="ConsumptionWaterQuantityType"/>
   <xsd:element name="ContainerizedIndicator" type="ContainerizedIndicatorType"/>
   <xsd:element name="Content" type="ContentType"/>
   <xsd:element name="ContentUnitQuantity" type="ContentUnitQuantityType"/>
   <xsd:element name="ContractFolderID" type="ContractFolderIDType"/>
   <xsd:element name="ContractName" type="ContractNameType"/>
   <xsd:element name="ContractSubdivision" type="ContractSubdivisionType"/>
   <xsd:element name="ContractType" type="ContractTypeType"/>
   <xsd:element name="ContractTypeCode" type="ContractTypeCodeType"/>
   <xsd:element name="ContractedCarrierAssignedID"
                type="ContractedCarrierAssignedIDType"/>
   <xsd:element name="ContractingSystemCode" type="ContractingSystemCodeType"/>
   <xsd:element name="CoordinateSystemCode" type="CoordinateSystemCodeType"/>
   <xsd:element name="CopyIndicator" type="CopyIndicatorType"/>
   <xsd:element name="CorporateRegistrationTypeCode"
                type="CorporateRegistrationTypeCodeType"/>
   <xsd:element name="CorporateStockAmount" type="CorporateStockAmountType"/>
   <xsd:element name="CorrectionAmount" type="CorrectionAmountType"/>
   <xsd:element name="CorrectionType" type="CorrectionTypeType"/>
   <xsd:element name="CorrectionTypeCode" type="CorrectionTypeCodeType"/>
   <xsd:element name="CorrectionUnitAmount" type="CorrectionUnitAmountType"/>
   <xsd:element name="CountrySubentity" type="CountrySubentityType"/>
   <xsd:element name="CountrySubentityCode" type="CountrySubentityCodeType"/>
   <xsd:element name="CreditLineAmount" type="CreditLineAmountType"/>
   <xsd:element name="CreditNoteTypeCode" type="CreditNoteTypeCodeType"/>
   <xsd:element name="CreditedQuantity" type="CreditedQuantityType"/>
   <xsd:element name="CrewQuantity" type="CrewQuantityType"/>
   <xsd:element name="CurrencyCode" type="CurrencyCodeType"/>
   <xsd:element name="CurrentChargeType" type="CurrentChargeTypeType"/>
   <xsd:element name="CurrentChargeTypeCode" type="CurrentChargeTypeCodeType"/>
   <xsd:element name="CustomerAssignedAccountID" type="CustomerAssignedAccountIDType"/>
   <xsd:element name="CustomerReference" type="CustomerReferenceType"/>
   <xsd:element name="CustomizationID" type="CustomizationIDType"/>
   <xsd:element name="CustomsClearanceServiceInstructions"
                type="CustomsClearanceServiceInstructionsType"/>
   <xsd:element name="CustomsImportClassifiedIndicator"
                type="CustomsImportClassifiedIndicatorType"/>
   <xsd:element name="CustomsStatusCode" type="CustomsStatusCodeType"/>
   <xsd:element name="CustomsTariffQuantity" type="CustomsTariffQuantityType"/>
   <xsd:element name="DamageRemarks" type="DamageRemarksType"/>
   <xsd:element name="DangerousGoodsApprovedIndicator"
                type="DangerousGoodsApprovedIndicatorType"/>
   <xsd:element name="DataSendingCapability" type="DataSendingCapabilityType"/>
   <xsd:element name="DataSourceCode" type="DataSourceCodeType"/>
   <xsd:element name="Date" type="DateType"/>
   <xsd:element name="DebitLineAmount" type="DebitLineAmountType"/>
   <xsd:element name="DebitedQuantity" type="DebitedQuantityType"/>
   <xsd:element name="DeclarationTypeCode" type="DeclarationTypeCodeType"/>
   <xsd:element name="DeclaredCarriageValueAmount"
                type="DeclaredCarriageValueAmountType"/>
   <xsd:element name="DeclaredCustomsValueAmount" type="DeclaredCustomsValueAmountType"/>
   <xsd:element name="DeclaredForCarriageValueAmount"
                type="DeclaredForCarriageValueAmountType"/>
   <xsd:element name="DeclaredStatisticsValueAmount"
                type="DeclaredStatisticsValueAmountType"/>
   <xsd:element name="DeliveredQuantity" type="DeliveredQuantityType"/>
   <xsd:element name="DeliveryInstructions" type="DeliveryInstructionsType"/>
   <xsd:element name="DemurrageInstructions" type="DemurrageInstructionsType"/>
   <xsd:element name="Department" type="DepartmentType"/>
   <xsd:element name="Description" type="DescriptionType"/>
   <xsd:element name="DescriptionCode" type="DescriptionCodeType"/>
   <xsd:element name="DespatchAdviceTypeCode" type="DespatchAdviceTypeCodeType"/>
   <xsd:element name="DifferenceTemperatureReductionQuantity"
                type="DifferenceTemperatureReductionQuantityType"/>
   <xsd:element name="DirectionCode" type="DirectionCodeType"/>
   <xsd:element name="DisplayTacticTypeCode" type="DisplayTacticTypeCodeType"/>
   <xsd:element name="DispositionCode" type="DispositionCodeType"/>
   <xsd:element name="District" type="DistrictType"/>
   <xsd:element name="DocumentCurrencyCode" type="DocumentCurrencyCodeType"/>
   <xsd:element name="DocumentDescription" type="DocumentDescriptionType"/>
   <xsd:element name="DocumentHash" type="DocumentHashType"/>
   <xsd:element name="DocumentID" type="DocumentIDType"/>
   <xsd:element name="DocumentStatusCode" type="DocumentStatusCodeType"/>
   <xsd:element name="DocumentStatusReasonCode" type="DocumentStatusReasonCodeType"/>
   <xsd:element name="DocumentStatusReasonDescription"
                type="DocumentStatusReasonDescriptionType"/>
   <xsd:element name="DocumentType" type="DocumentTypeType"/>
   <xsd:element name="DocumentTypeCode" type="DocumentTypeCodeType"/>
   <xsd:element name="DocumentationFeeAmount" type="DocumentationFeeAmountType"/>
   <xsd:element name="DueDate" type="DueDateType"/>
   <xsd:element name="DurationMeasure" type="DurationMeasureType"/>
   <xsd:element name="Duty" type="DutyType"/>
   <xsd:element name="DutyCode" type="DutyCodeType"/>
   <xsd:element name="EarliestPickupDate" type="EarliestPickupDateType"/>
   <xsd:element name="EarliestPickupTime" type="EarliestPickupTimeType"/>
   <xsd:element name="EconomicOperatorRegistryURI"
                type="EconomicOperatorRegistryURIType"/>
   <xsd:element name="EffectiveDate" type="EffectiveDateType"/>
   <xsd:element name="EffectiveTime" type="EffectiveTimeType"/>
   <xsd:element name="ElectronicDeviceDescription"
                type="ElectronicDeviceDescriptionType"/>
   <xsd:element name="ElectronicMail" type="ElectronicMailType"/>
   <xsd:element name="EmbeddedDocumentBinaryObject"
                type="EmbeddedDocumentBinaryObjectType"/>
   <xsd:element name="EmergencyProceduresCode" type="EmergencyProceduresCodeType"/>
   <xsd:element name="EmployeeQuantity" type="EmployeeQuantityType"/>
   <xsd:element name="EncodingCode" type="EncodingCodeType"/>
   <xsd:element name="EndDate" type="EndDateType"/>
   <xsd:element name="EndTime" type="EndTimeType"/>
   <xsd:element name="EndpointID" type="EndpointIDType"/>
   <xsd:element name="EnvironmentalEmissionTypeCode"
                type="EnvironmentalEmissionTypeCodeType"/>
   <xsd:element name="EstimatedAmount" type="EstimatedAmountType"/>
   <xsd:element name="EstimatedConsumedQuantity" type="EstimatedConsumedQuantityType"/>
   <xsd:element name="EstimatedDeliveryDate" type="EstimatedDeliveryDateType"/>
   <xsd:element name="EstimatedDeliveryTime" type="EstimatedDeliveryTimeType"/>
   <xsd:element name="EstimatedDespatchDate" type="EstimatedDespatchDateType"/>
   <xsd:element name="EstimatedDespatchTime" type="EstimatedDespatchTimeType"/>
   <xsd:element name="EstimatedOverallContractAmount"
                type="EstimatedOverallContractAmountType"/>
   <xsd:element name="EstimatedOverallContractQuantity"
                type="EstimatedOverallContractQuantityType"/>
   <xsd:element name="EvaluationCriterionTypeCode"
                type="EvaluationCriterionTypeCodeType"/>
   <xsd:element name="EvidenceTypeCode" type="EvidenceTypeCodeType"/>
   <xsd:element name="ExceptionResolutionCode" type="ExceptionResolutionCodeType"/>
   <xsd:element name="ExceptionStatusCode" type="ExceptionStatusCodeType"/>
   <xsd:element name="ExchangeMarketID" type="ExchangeMarketIDType"/>
   <xsd:element name="ExclusionReason" type="ExclusionReasonType"/>
   <xsd:element name="ExecutionRequirementCode" type="ExecutionRequirementCodeType"/>
   <xsd:element name="ExemptionReason" type="ExemptionReasonType"/>
   <xsd:element name="ExemptionReasonCode" type="ExemptionReasonCodeType"/>
   <xsd:element name="ExpectedOperatorQuantity" type="ExpectedOperatorQuantityType"/>
   <xsd:element name="ExpectedQuantity" type="ExpectedQuantityType"/>
   <xsd:element name="ExpenseCode" type="ExpenseCodeType"/>
   <xsd:element name="ExpiryDate" type="ExpiryDateType"/>
   <xsd:element name="ExpiryTime" type="ExpiryTimeType"/>
   <xsd:element name="Expression" type="ExpressionType"/>
   <xsd:element name="ExpressionCode" type="ExpressionCodeType"/>
   <xsd:element name="ExtendedID" type="ExtendedIDType"/>
   <xsd:element name="Extension" type="ExtensionType"/>
   <xsd:element name="FaceValueAmount" type="FaceValueAmountType"/>
   <xsd:element name="FamilyName" type="FamilyNameType"/>
   <xsd:element name="FeatureTacticTypeCode" type="FeatureTacticTypeCodeType"/>
   <xsd:element name="FeeAmount" type="FeeAmountType"/>
   <xsd:element name="FeeDescription" type="FeeDescriptionType"/>
   <xsd:element name="FileName" type="FileNameType"/>
   <xsd:element name="FinancingInstrumentCode" type="FinancingInstrumentCodeType"/>
   <xsd:element name="FirstName" type="FirstNameType"/>
   <xsd:element name="FirstShipmentAvailibilityDate"
                type="FirstShipmentAvailibilityDateType"/>
   <xsd:element name="Floor" type="FloorType"/>
   <xsd:element name="FollowupContractIndicator" type="FollowupContractIndicatorType"/>
   <xsd:element name="ForecastPurposeCode" type="ForecastPurposeCodeType"/>
   <xsd:element name="ForecastTypeCode" type="ForecastTypeCodeType"/>
   <xsd:element name="FormatCode" type="FormatCodeType"/>
   <xsd:element name="ForwarderServiceInstructions"
                type="ForwarderServiceInstructionsType"/>
   <xsd:element name="FreeOfChargeIndicator" type="FreeOfChargeIndicatorType"/>
   <xsd:element name="FreeOnBoardValueAmount" type="FreeOnBoardValueAmountType"/>
   <xsd:element name="FreightForwarderAssignedID" type="FreightForwarderAssignedIDType"/>
   <xsd:element name="FreightRateClassCode" type="FreightRateClassCodeType"/>
   <xsd:element name="Frequency" type="FrequencyType"/>
   <xsd:element name="FrozenDocumentIndicator" type="FrozenDocumentIndicatorType"/>
   <xsd:element name="FrozenPeriodDaysNumeric" type="FrozenPeriodDaysNumericType"/>
   <xsd:element name="FullnessIndicationCode" type="FullnessIndicationCodeType"/>
   <xsd:element name="FullyPaidSharesIndicator" type="FullyPaidSharesIndicatorType"/>
   <xsd:element name="FundingProgram" type="FundingProgramType"/>
   <xsd:element name="FundingProgramCode" type="FundingProgramCodeType"/>
   <xsd:element name="GasPressureQuantity" type="GasPressureQuantityType"/>
   <xsd:element name="GenderCode" type="GenderCodeType"/>
   <xsd:element name="GeneralCargoIndicator" type="GeneralCargoIndicatorType"/>
   <xsd:element name="GovernmentAgreementConstraintIndicator"
                type="GovernmentAgreementConstraintIndicatorType"/>
   <xsd:element name="GrossTonnageMeasure" type="GrossTonnageMeasureType"/>
   <xsd:element name="GrossVolumeMeasure" type="GrossVolumeMeasureType"/>
   <xsd:element name="GrossWeightMeasure" type="GrossWeightMeasureType"/>
   <xsd:element name="GuaranteeTypeCode" type="GuaranteeTypeCodeType"/>
   <xsd:element name="GuaranteedDespatchDate" type="GuaranteedDespatchDateType"/>
   <xsd:element name="GuaranteedDespatchTime" type="GuaranteedDespatchTimeType"/>
   <xsd:element name="HandlingCode" type="HandlingCodeType"/>
   <xsd:element name="HandlingInstructions" type="HandlingInstructionsType"/>
   <xsd:element name="HashAlgorithmMethod" type="HashAlgorithmMethodType"/>
   <xsd:element name="HaulageInstructions" type="HaulageInstructionsType"/>
   <xsd:element name="HazardClassID" type="HazardClassIDType"/>
   <xsd:element name="HazardousCategoryCode" type="HazardousCategoryCodeType"/>
   <xsd:element name="HazardousRegulationCode" type="HazardousRegulationCodeType"/>
   <xsd:element name="HazardousRiskIndicator" type="HazardousRiskIndicatorType"/>
   <xsd:element name="HeatingType" type="HeatingTypeType"/>
   <xsd:element name="HeatingTypeCode" type="HeatingTypeCodeType"/>
   <xsd:element name="HigherTenderAmount" type="HigherTenderAmountType"/>
   <xsd:element name="HolderName" type="HolderNameType"/>
   <xsd:element name="HumanFoodApprovedIndicator" type="HumanFoodApprovedIndicatorType"/>
   <xsd:element name="HumanFoodIndicator" type="HumanFoodIndicatorType"/>
   <xsd:element name="HumidityPercent" type="HumidityPercentType"/>
   <xsd:element name="ID" type="IDType"/>
   <xsd:element name="IdentificationCode" type="IdentificationCodeType"/>
   <xsd:element name="IdentificationID" type="IdentificationIDType"/>
   <xsd:element name="ImmobilizationCertificateID"
                type="ImmobilizationCertificateIDType"/>
   <xsd:element name="ImportanceCode" type="ImportanceCodeType"/>
   <xsd:element name="IndicationIndicator" type="IndicationIndicatorType"/>
   <xsd:element name="IndustryClassificationCode" type="IndustryClassificationCodeType"/>
   <xsd:element name="Information" type="InformationType"/>
   <xsd:element name="InformationURI" type="InformationURIType"/>
   <xsd:element name="InhalationToxicityZoneCode" type="InhalationToxicityZoneCodeType"/>
   <xsd:element name="InhouseMail" type="InhouseMailType"/>
   <xsd:element name="InspectionMethodCode" type="InspectionMethodCodeType"/>
   <xsd:element name="InstallmentDueDate" type="InstallmentDueDateType"/>
   <xsd:element name="InstructionID" type="InstructionIDType"/>
   <xsd:element name="InstructionNote" type="InstructionNoteType"/>
   <xsd:element name="Instructions" type="InstructionsType"/>
   <xsd:element name="InsurancePremiumAmount" type="InsurancePremiumAmountType"/>
   <xsd:element name="InsuranceValueAmount" type="InsuranceValueAmountType"/>
   <xsd:element name="InventoryValueAmount" type="InventoryValueAmountType"/>
   <xsd:element name="InvoiceTypeCode" type="InvoiceTypeCodeType"/>
   <xsd:element name="InvoicedQuantity" type="InvoicedQuantityType"/>
   <xsd:element name="InvoicingPartyReference" type="InvoicingPartyReferenceType"/>
   <xsd:element name="IssueDate" type="IssueDateType"/>
   <xsd:element name="IssueNumberID" type="IssueNumberIDType"/>
   <xsd:element name="IssueTime" type="IssueTimeType"/>
   <xsd:element name="IssuerID" type="IssuerIDType"/>
   <xsd:element name="ItemClassificationCode" type="ItemClassificationCodeType"/>
   <xsd:element name="ItemUpdateRequestIndicator" type="ItemUpdateRequestIndicatorType"/>
   <xsd:element name="JobTitle" type="JobTitleType"/>
   <xsd:element name="JourneyID" type="JourneyIDType"/>
   <xsd:element name="Justification" type="JustificationType"/>
   <xsd:element name="JustificationDescription" type="JustificationDescriptionType"/>
   <xsd:element name="Keyword" type="KeywordType"/>
   <xsd:element name="LanguageID" type="LanguageIDType"/>
   <xsd:element name="LastRevisionDate" type="LastRevisionDateType"/>
   <xsd:element name="LastRevisionTime" type="LastRevisionTimeType"/>
   <xsd:element name="LatestDeliveryDate" type="LatestDeliveryDateType"/>
   <xsd:element name="LatestDeliveryTime" type="LatestDeliveryTimeType"/>
   <xsd:element name="LatestMeterQuantity" type="LatestMeterQuantityType"/>
   <xsd:element name="LatestMeterReadingDate" type="LatestMeterReadingDateType"/>
   <xsd:element name="LatestMeterReadingMethod" type="LatestMeterReadingMethodType"/>
   <xsd:element name="LatestMeterReadingMethodCode"
                type="LatestMeterReadingMethodCodeType"/>
   <xsd:element name="LatestPickupDate" type="LatestPickupDateType"/>
   <xsd:element name="LatestPickupTime" type="LatestPickupTimeType"/>
   <xsd:element name="LatestProposalAcceptanceDate"
                type="LatestProposalAcceptanceDateType"/>
   <xsd:element name="LatestSecurityClearanceDate"
                type="LatestSecurityClearanceDateType"/>
   <xsd:element name="LatitudeDegreesMeasure" type="LatitudeDegreesMeasureType"/>
   <xsd:element name="LatitudeDirectionCode" type="LatitudeDirectionCodeType"/>
   <xsd:element name="LatitudeMinutesMeasure" type="LatitudeMinutesMeasureType"/>
   <xsd:element name="LeadTimeMeasure" type="LeadTimeMeasureType"/>
   <xsd:element name="LegalReference" type="LegalReferenceType"/>
   <xsd:element name="LegalStatusIndicator" type="LegalStatusIndicatorType"/>
   <xsd:element name="LiabilityAmount" type="LiabilityAmountType"/>
   <xsd:element name="LicensePlateID" type="LicensePlateIDType"/>
   <xsd:element name="LifeCycleStatusCode" type="LifeCycleStatusCodeType"/>
   <xsd:element name="LimitationDescription" type="LimitationDescriptionType"/>
   <xsd:element name="Line" type="LineType"/>
   <xsd:element name="LineCountNumeric" type="LineCountNumericType"/>
   <xsd:element name="LineExtensionAmount" type="LineExtensionAmountType"/>
   <xsd:element name="LineID" type="LineIDType"/>
   <xsd:element name="LineNumberNumeric" type="LineNumberNumericType"/>
   <xsd:element name="LineStatusCode" type="LineStatusCodeType"/>
   <xsd:element name="ListValue" type="ListValueType"/>
   <xsd:element name="LivestockIndicator" type="LivestockIndicatorType"/>
   <xsd:element name="LoadingLengthMeasure" type="LoadingLengthMeasureType"/>
   <xsd:element name="LoadingSequenceID" type="LoadingSequenceIDType"/>
   <xsd:element name="LocaleCode" type="LocaleCodeType"/>
   <xsd:element name="Location" type="LocationType"/>
   <xsd:element name="LocationID" type="LocationIDType"/>
   <xsd:element name="LocationTypeCode" type="LocationTypeCodeType"/>
   <xsd:element name="Login" type="LoginType"/>
   <xsd:element name="LogoReferenceID" type="LogoReferenceIDType"/>
   <xsd:element name="LongitudeDegreesMeasure" type="LongitudeDegreesMeasureType"/>
   <xsd:element name="LongitudeDirectionCode" type="LongitudeDirectionCodeType"/>
   <xsd:element name="LongitudeMinutesMeasure" type="LongitudeMinutesMeasureType"/>
   <xsd:element name="LossRisk" type="LossRiskType"/>
   <xsd:element name="LossRiskResponsibilityCode" type="LossRiskResponsibilityCodeType"/>
   <xsd:element name="LotNumberID" type="LotNumberIDType"/>
   <xsd:element name="LowTendersDescription" type="LowTendersDescriptionType"/>
   <xsd:element name="LowerOrangeHazardPlacardID" type="LowerOrangeHazardPlacardIDType"/>
   <xsd:element name="LowerTenderAmount" type="LowerTenderAmountType"/>
   <xsd:element name="MandateTypeCode" type="MandateTypeCodeType"/>
   <xsd:element name="ManufactureDate" type="ManufactureDateType"/>
   <xsd:element name="ManufactureTime" type="ManufactureTimeType"/>
   <xsd:element name="MarkAttention" type="MarkAttentionType"/>
   <xsd:element name="MarkAttentionIndicator" type="MarkAttentionIndicatorType"/>
   <xsd:element name="MarkCare" type="MarkCareType"/>
   <xsd:element name="MarkCareIndicator" type="MarkCareIndicatorType"/>
   <xsd:element name="MarketValueAmount" type="MarketValueAmountType"/>
   <xsd:element name="MarkingID" type="MarkingIDType"/>
   <xsd:element name="MathematicOperatorCode" type="MathematicOperatorCodeType"/>
   <xsd:element name="MaximumAdvertisementAmount" type="MaximumAdvertisementAmountType"/>
   <xsd:element name="MaximumAmount" type="MaximumAmountType"/>
   <xsd:element name="MaximumBackorderQuantity" type="MaximumBackorderQuantityType"/>
   <xsd:element name="MaximumCopiesNumeric" type="MaximumCopiesNumericType"/>
   <xsd:element name="MaximumMeasure" type="MaximumMeasureType"/>
   <xsd:element name="MaximumNumberNumeric" type="MaximumNumberNumericType"/>
   <xsd:element name="MaximumOperatorQuantity" type="MaximumOperatorQuantityType"/>
   <xsd:element name="MaximumOrderQuantity" type="MaximumOrderQuantityType"/>
   <xsd:element name="MaximumPaidAmount" type="MaximumPaidAmountType"/>
   <xsd:element name="MaximumPaymentInstructionsNumeric"
                type="MaximumPaymentInstructionsNumericType"/>
   <xsd:element name="MaximumPercent" type="MaximumPercentType"/>
   <xsd:element name="MaximumQuantity" type="MaximumQuantityType"/>
   <xsd:element name="MaximumValue" type="MaximumValueType"/>
   <xsd:element name="MaximumVariantQuantity" type="MaximumVariantQuantityType"/>
   <xsd:element name="Measure" type="MeasureType"/>
   <xsd:element name="MedicalFirstAidGuideCode" type="MedicalFirstAidGuideCodeType"/>
   <xsd:element name="MeterConstant" type="MeterConstantType"/>
   <xsd:element name="MeterConstantCode" type="MeterConstantCodeType"/>
   <xsd:element name="MeterName" type="MeterNameType"/>
   <xsd:element name="MeterNumber" type="MeterNumberType"/>
   <xsd:element name="MeterReadingComments" type="MeterReadingCommentsType"/>
   <xsd:element name="MeterReadingType" type="MeterReadingTypeType"/>
   <xsd:element name="MeterReadingTypeCode" type="MeterReadingTypeCodeType"/>
   <xsd:element name="MiddleName" type="MiddleNameType"/>
   <xsd:element name="MimeCode" type="MimeCodeType"/>
   <xsd:element name="MinimumAmount" type="MinimumAmountType"/>
   <xsd:element name="MinimumBackorderQuantity" type="MinimumBackorderQuantityType"/>
   <xsd:element name="MinimumImprovementBid" type="MinimumImprovementBidType"/>
   <xsd:element name="MinimumInventoryQuantity" type="MinimumInventoryQuantityType"/>
   <xsd:element name="MinimumMeasure" type="MinimumMeasureType"/>
   <xsd:element name="MinimumNumberNumeric" type="MinimumNumberNumericType"/>
   <xsd:element name="MinimumOrderQuantity" type="MinimumOrderQuantityType"/>
   <xsd:element name="MinimumPercent" type="MinimumPercentType"/>
   <xsd:element name="MinimumQuantity" type="MinimumQuantityType"/>
   <xsd:element name="MinimumValue" type="MinimumValueType"/>
   <xsd:element name="MiscellaneousEventTypeCode" type="MiscellaneousEventTypeCodeType"/>
   <xsd:element name="ModelName" type="ModelNameType"/>
   <xsd:element name="MonetaryScope" type="MonetaryScopeType"/>
   <xsd:element name="MovieTitle" type="MovieTitleType"/>
   <xsd:element name="MultipleOrderQuantity" type="MultipleOrderQuantityType"/>
   <xsd:element name="MultiplierFactorNumeric" type="MultiplierFactorNumericType"/>
   <xsd:element name="Name" type="NameType"/>
   <xsd:element name="NameCode" type="NameCodeType"/>
   <xsd:element name="NameSuffix" type="NameSuffixType"/>
   <xsd:element name="NationalityID" type="NationalityIDType"/>
   <xsd:element name="NatureCode" type="NatureCodeType"/>
   <xsd:element name="NegotiationDescription" type="NegotiationDescriptionType"/>
   <xsd:element name="NetNetWeightMeasure" type="NetNetWeightMeasureType"/>
   <xsd:element name="NetTonnageMeasure" type="NetTonnageMeasureType"/>
   <xsd:element name="NetVolumeMeasure" type="NetVolumeMeasureType"/>
   <xsd:element name="NetWeightMeasure" type="NetWeightMeasureType"/>
   <xsd:element name="NetworkID" type="NetworkIDType"/>
   <xsd:element name="NominationDate" type="NominationDateType"/>
   <xsd:element name="NominationTime" type="NominationTimeType"/>
   <xsd:element name="NormalTemperatureReductionQuantity"
                type="NormalTemperatureReductionQuantityType"/>
   <xsd:element name="Note" type="NoteType"/>
   <xsd:element name="NotificationTypeCode" type="NotificationTypeCodeType"/>
   <xsd:element name="OccurrenceDate" type="OccurrenceDateType"/>
   <xsd:element name="OccurrenceTime" type="OccurrenceTimeType"/>
   <xsd:element name="OnCarriageIndicator" type="OnCarriageIndicatorType"/>
   <xsd:element name="OneTimeChargeType" type="OneTimeChargeTypeType"/>
   <xsd:element name="OneTimeChargeTypeCode" type="OneTimeChargeTypeCodeType"/>
   <xsd:element name="OntologyURI" type="OntologyURIType"/>
   <xsd:element name="OpenTenderID" type="OpenTenderIDType"/>
   <xsd:element name="OperatingYearsQuantity" type="OperatingYearsQuantityType"/>
   <xsd:element name="OptionalLineItemIndicator" type="OptionalLineItemIndicatorType"/>
   <xsd:element name="OptionsDescription" type="OptionsDescriptionType"/>
   <xsd:element name="OrderIntervalDaysNumeric" type="OrderIntervalDaysNumericType"/>
   <xsd:element name="OrderQuantityIncrementNumeric"
                type="OrderQuantityIncrementNumericType"/>
   <xsd:element name="OrderResponseCode" type="OrderResponseCodeType"/>
   <xsd:element name="OrderTypeCode" type="OrderTypeCodeType"/>
   <xsd:element name="OrderableIndicator" type="OrderableIndicatorType"/>
   <xsd:element name="OrderableUnit" type="OrderableUnitType"/>
   <xsd:element name="OrderableUnitFactorRate" type="OrderableUnitFactorRateType"/>
   <xsd:element name="OrganizationDepartment" type="OrganizationDepartmentType"/>
   <xsd:element name="OriginalContractingSystemID"
                type="OriginalContractingSystemIDType"/>
   <xsd:element name="OriginalJobID" type="OriginalJobIDType"/>
   <xsd:element name="OtherConditionsIndicator" type="OtherConditionsIndicatorType"/>
   <xsd:element name="OtherInstruction" type="OtherInstructionType"/>
   <xsd:element name="OtherName" type="OtherNameType"/>
   <xsd:element name="OutstandingQuantity" type="OutstandingQuantityType"/>
   <xsd:element name="OutstandingReason" type="OutstandingReasonType"/>
   <xsd:element name="OversupplyQuantity" type="OversupplyQuantityType"/>
   <xsd:element name="OwnerTypeCode" type="OwnerTypeCodeType"/>
   <xsd:element name="PackLevelCode" type="PackLevelCodeType"/>
   <xsd:element name="PackQuantity" type="PackQuantityType"/>
   <xsd:element name="PackSizeNumeric" type="PackSizeNumericType"/>
   <xsd:element name="PackageLevelCode" type="PackageLevelCodeType"/>
   <xsd:element name="PackagingTypeCode" type="PackagingTypeCodeType"/>
   <xsd:element name="PackingCriteriaCode" type="PackingCriteriaCodeType"/>
   <xsd:element name="PackingMaterial" type="PackingMaterialType"/>
   <xsd:element name="PaidAmount" type="PaidAmountType"/>
   <xsd:element name="PaidDate" type="PaidDateType"/>
   <xsd:element name="PaidTime" type="PaidTimeType"/>
   <xsd:element name="ParentDocumentID" type="ParentDocumentIDType"/>
   <xsd:element name="ParentDocumentLineReferenceID"
                type="ParentDocumentLineReferenceIDType"/>
   <xsd:element name="ParentDocumentTypeCode" type="ParentDocumentTypeCodeType"/>
   <xsd:element name="ParentDocumentVersionID" type="ParentDocumentVersionIDType"/>
   <xsd:element name="PartPresentationCode" type="PartPresentationCodeType"/>
   <xsd:element name="PartecipationPercent" type="PartecipationPercentType"/>
   <xsd:element name="PartialDeliveryIndicator" type="PartialDeliveryIndicatorType"/>
   <xsd:element name="ParticipationPercent" type="ParticipationPercentType"/>
   <xsd:element name="PartyCapacityAmount" type="PartyCapacityAmountType"/>
   <xsd:element name="PartyType" type="PartyTypeType"/>
   <xsd:element name="PartyTypeCode" type="PartyTypeCodeType"/>
   <xsd:element name="PassengerQuantity" type="PassengerQuantityType"/>
   <xsd:element name="Password" type="PasswordType"/>
   <xsd:element name="PayPerView" type="PayPerViewType"/>
   <xsd:element name="PayableAlternativeAmount" type="PayableAlternativeAmountType"/>
   <xsd:element name="PayableAmount" type="PayableAmountType"/>
   <xsd:element name="PayableRoundingAmount" type="PayableRoundingAmountType"/>
   <xsd:element name="PayerReference" type="PayerReferenceType"/>
   <xsd:element name="PaymentAlternativeCurrencyCode"
                type="PaymentAlternativeCurrencyCodeType"/>
   <xsd:element name="PaymentChannelCode" type="PaymentChannelCodeType"/>
   <xsd:element name="PaymentCurrencyCode" type="PaymentCurrencyCodeType"/>
   <xsd:element name="PaymentDescription" type="PaymentDescriptionType"/>
   <xsd:element name="PaymentDueDate" type="PaymentDueDateType"/>
   <xsd:element name="PaymentFrequencyCode" type="PaymentFrequencyCodeType"/>
   <xsd:element name="PaymentID" type="PaymentIDType"/>
   <xsd:element name="PaymentMeansCode" type="PaymentMeansCodeType"/>
   <xsd:element name="PaymentMeansID" type="PaymentMeansIDType"/>
   <xsd:element name="PaymentNote" type="PaymentNoteType"/>
   <xsd:element name="PaymentOrderReference" type="PaymentOrderReferenceType"/>
   <xsd:element name="PaymentPercent" type="PaymentPercentType"/>
   <xsd:element name="PaymentPurposeCode" type="PaymentPurposeCodeType"/>
   <xsd:element name="PaymentTermsDetailsURI" type="PaymentTermsDetailsURIType"/>
   <xsd:element name="PenaltyAmount" type="PenaltyAmountType"/>
   <xsd:element name="PenaltySurchargePercent" type="PenaltySurchargePercentType"/>
   <xsd:element name="PerUnitAmount" type="PerUnitAmountType"/>
   <xsd:element name="Percent" type="PercentType"/>
   <xsd:element name="PerformanceMetricTypeCode" type="PerformanceMetricTypeCodeType"/>
   <xsd:element name="PerformanceValueQuantity" type="PerformanceValueQuantityType"/>
   <xsd:element name="PerformingCarrierAssignedID"
                type="PerformingCarrierAssignedIDType"/>
   <xsd:element name="PersonalSituation" type="PersonalSituationType"/>
   <xsd:element name="PhoneNumber" type="PhoneNumberType"/>
   <xsd:element name="PlacardEndorsement" type="PlacardEndorsementType"/>
   <xsd:element name="PlacardNotation" type="PlacardNotationType"/>
   <xsd:element name="PlannedDate" type="PlannedDateType"/>
   <xsd:element name="PlotIdentification" type="PlotIdentificationType"/>
   <xsd:element name="PositionCode" type="PositionCodeType"/>
   <xsd:element name="PostEventNotificationDurationMeasure"
                type="PostEventNotificationDurationMeasureType"/>
   <xsd:element name="PostalZone" type="PostalZoneType"/>
   <xsd:element name="Postbox" type="PostboxType"/>
   <xsd:element name="PowerIndicator" type="PowerIndicatorType"/>
   <xsd:element name="PreCarriageIndicator" type="PreCarriageIndicatorType"/>
   <xsd:element name="PreEventNotificationDurationMeasure"
                type="PreEventNotificationDurationMeasureType"/>
   <xsd:element name="PreferenceCriterionCode" type="PreferenceCriterionCodeType"/>
   <xsd:element name="PrepaidAmount" type="PrepaidAmountType"/>
   <xsd:element name="PrepaidIndicator" type="PrepaidIndicatorType"/>
   <xsd:element name="PrepaidPaymentReferenceID" type="PrepaidPaymentReferenceIDType"/>
   <xsd:element name="PreviousCancellationReasonCode"
                type="PreviousCancellationReasonCodeType"/>
   <xsd:element name="PreviousJobID" type="PreviousJobIDType"/>
   <xsd:element name="PreviousMeterQuantity" type="PreviousMeterQuantityType"/>
   <xsd:element name="PreviousMeterReadingDate" type="PreviousMeterReadingDateType"/>
   <xsd:element name="PreviousMeterReadingMethod" type="PreviousMeterReadingMethodType"/>
   <xsd:element name="PreviousMeterReadingMethodCode"
                type="PreviousMeterReadingMethodCodeType"/>
   <xsd:element name="PreviousVersionID" type="PreviousVersionIDType"/>
   <xsd:element name="PriceAmount" type="PriceAmountType"/>
   <xsd:element name="PriceChangeReason" type="PriceChangeReasonType"/>
   <xsd:element name="PriceEvaluationCode" type="PriceEvaluationCodeType"/>
   <xsd:element name="PriceRevisionFormulaDescription"
                type="PriceRevisionFormulaDescriptionType"/>
   <xsd:element name="PriceType" type="PriceTypeType"/>
   <xsd:element name="PriceTypeCode" type="PriceTypeCodeType"/>
   <xsd:element name="PricingCurrencyCode" type="PricingCurrencyCodeType"/>
   <xsd:element name="PricingUpdateRequestIndicator"
                type="PricingUpdateRequestIndicatorType"/>
   <xsd:element name="PrimaryAccountNumberID" type="PrimaryAccountNumberIDType"/>
   <xsd:element name="PrintQualifier" type="PrintQualifierType"/>
   <xsd:element name="Priority" type="PriorityType"/>
   <xsd:element name="PrivacyCode" type="PrivacyCodeType"/>
   <xsd:element name="PrizeDescription" type="PrizeDescriptionType"/>
   <xsd:element name="PrizeIndicator" type="PrizeIndicatorType"/>
   <xsd:element name="ProcedureCode" type="ProcedureCodeType"/>
   <xsd:element name="ProcessDescription" type="ProcessDescriptionType"/>
   <xsd:element name="ProcessReason" type="ProcessReasonType"/>
   <xsd:element name="ProcessReasonCode" type="ProcessReasonCodeType"/>
   <xsd:element name="ProcurementSubTypeCode" type="ProcurementSubTypeCodeType"/>
   <xsd:element name="ProcurementTypeCode" type="ProcurementTypeCodeType"/>
   <xsd:element name="ProductTraceID" type="ProductTraceIDType"/>
   <xsd:element name="ProfileExecutionID" type="ProfileExecutionIDType"/>
   <xsd:element name="ProfileID" type="ProfileIDType"/>
   <xsd:element name="ProfileStatusCode" type="ProfileStatusCodeType"/>
   <xsd:element name="ProgressPercent" type="ProgressPercentType"/>
   <xsd:element name="PromotionalEventTypeCode" type="PromotionalEventTypeCodeType"/>
   <xsd:element name="ProviderTypeCode" type="ProviderTypeCodeType"/>
   <xsd:element name="PublishAwardIndicator" type="PublishAwardIndicatorType"/>
   <xsd:element name="Purpose" type="PurposeType"/>
   <xsd:element name="PurposeCode" type="PurposeCodeType"/>
   <xsd:element name="QualityControlCode" type="QualityControlCodeType"/>
   <xsd:element name="Quantity" type="QuantityType"/>
   <xsd:element name="QuantityDiscrepancyCode" type="QuantityDiscrepancyCodeType"/>
   <xsd:element name="RadioCallSignID" type="RadioCallSignIDType"/>
   <xsd:element name="RailCarID" type="RailCarIDType"/>
   <xsd:element name="Rank" type="RankType"/>
   <xsd:element name="Rate" type="RateType"/>
   <xsd:element name="ReceiptAdviceTypeCode" type="ReceiptAdviceTypeCodeType"/>
   <xsd:element name="ReceivedDate" type="ReceivedDateType"/>
   <xsd:element name="ReceivedElectronicTenderQuantity"
                type="ReceivedElectronicTenderQuantityType"/>
   <xsd:element name="ReceivedForeignTenderQuantity"
                type="ReceivedForeignTenderQuantityType"/>
   <xsd:element name="ReceivedQuantity" type="ReceivedQuantityType"/>
   <xsd:element name="ReceivedTenderQuantity" type="ReceivedTenderQuantityType"/>
   <xsd:element name="Reference" type="ReferenceType"/>
   <xsd:element name="ReferenceDate" type="ReferenceDateType"/>
   <xsd:element name="ReferenceEventCode" type="ReferenceEventCodeType"/>
   <xsd:element name="ReferenceID" type="ReferenceIDType"/>
   <xsd:element name="ReferenceTime" type="ReferenceTimeType"/>
   <xsd:element name="ReferencedConsignmentID" type="ReferencedConsignmentIDType"/>
   <xsd:element name="RefrigeratedIndicator" type="RefrigeratedIndicatorType"/>
   <xsd:element name="RefrigerationOnIndicator" type="RefrigerationOnIndicatorType"/>
   <xsd:element name="Region" type="RegionType"/>
   <xsd:element name="RegisteredDate" type="RegisteredDateType"/>
   <xsd:element name="RegisteredTime" type="RegisteredTimeType"/>
   <xsd:element name="RegistrationDate" type="RegistrationDateType"/>
   <xsd:element name="RegistrationExpirationDate" type="RegistrationExpirationDateType"/>
   <xsd:element name="RegistrationID" type="RegistrationIDType"/>
   <xsd:element name="RegistrationName" type="RegistrationNameType"/>
   <xsd:element name="RegistrationNationality" type="RegistrationNationalityType"/>
   <xsd:element name="RegistrationNationalityID" type="RegistrationNationalityIDType"/>
   <xsd:element name="RegulatoryDomain" type="RegulatoryDomainType"/>
   <xsd:element name="RejectActionCode" type="RejectActionCodeType"/>
   <xsd:element name="RejectReason" type="RejectReasonType"/>
   <xsd:element name="RejectReasonCode" type="RejectReasonCodeType"/>
   <xsd:element name="RejectedQuantity" type="RejectedQuantityType"/>
   <xsd:element name="RejectionNote" type="RejectionNoteType"/>
   <xsd:element name="ReleaseID" type="ReleaseIDType"/>
   <xsd:element name="ReliabilityPercent" type="ReliabilityPercentType"/>
   <xsd:element name="Remarks" type="RemarksType"/>
   <xsd:element name="ReminderSequenceNumeric" type="ReminderSequenceNumericType"/>
   <xsd:element name="ReminderTypeCode" type="ReminderTypeCodeType"/>
   <xsd:element name="ReplenishmentOwnerDescription"
                type="ReplenishmentOwnerDescriptionType"/>
   <xsd:element name="RequestForQuotationLineID" type="RequestForQuotationLineIDType"/>
   <xsd:element name="RequestedDeliveryDate" type="RequestedDeliveryDateType"/>
   <xsd:element name="RequestedDespatchDate" type="RequestedDespatchDateType"/>
   <xsd:element name="RequestedDespatchTime" type="RequestedDespatchTimeType"/>
   <xsd:element name="RequestedInvoiceCurrencyCode"
                type="RequestedInvoiceCurrencyCodeType"/>
   <xsd:element name="RequestedPublicationDate" type="RequestedPublicationDateType"/>
   <xsd:element name="RequiredCurriculaIndicator" type="RequiredCurriculaIndicatorType"/>
   <xsd:element name="RequiredCustomsID" type="RequiredCustomsIDType"/>
   <xsd:element name="RequiredDeliveryDate" type="RequiredDeliveryDateType"/>
   <xsd:element name="RequiredDeliveryTime" type="RequiredDeliveryTimeType"/>
   <xsd:element name="RequiredFeeAmount" type="RequiredFeeAmountType"/>
   <xsd:element name="ResidenceType" type="ResidenceTypeType"/>
   <xsd:element name="ResidenceTypeCode" type="ResidenceTypeCodeType"/>
   <xsd:element name="ResidentOccupantsNumeric" type="ResidentOccupantsNumericType"/>
   <xsd:element name="Resolution" type="ResolutionType"/>
   <xsd:element name="ResolutionCode" type="ResolutionCodeType"/>
   <xsd:element name="ResolutionDate" type="ResolutionDateType"/>
   <xsd:element name="ResolutionTime" type="ResolutionTimeType"/>
   <xsd:element name="ResponseCode" type="ResponseCodeType"/>
   <xsd:element name="ResponseDate" type="ResponseDateType"/>
   <xsd:element name="ResponseTime" type="ResponseTimeType"/>
   <xsd:element name="RetailEventName" type="RetailEventNameType"/>
   <xsd:element name="RetailEventStatusCode" type="RetailEventStatusCodeType"/>
   <xsd:element name="ReturnabilityIndicator" type="ReturnabilityIndicatorType"/>
   <xsd:element name="ReturnableMaterialIndicator"
                type="ReturnableMaterialIndicatorType"/>
   <xsd:element name="ReturnableQuantity" type="ReturnableQuantityType"/>
   <xsd:element name="RevisedForecastLineID" type="RevisedForecastLineIDType"/>
   <xsd:element name="RevisionDate" type="RevisionDateType"/>
   <xsd:element name="RevisionStatusCode" type="RevisionStatusCodeType"/>
   <xsd:element name="RevisionTime" type="RevisionTimeType"/>
   <xsd:element name="RoamingPartnerName" type="RoamingPartnerNameType"/>
   <xsd:element name="RoleCode" type="RoleCodeType"/>
   <xsd:element name="RoleDescription" type="RoleDescriptionType"/>
   <xsd:element name="Room" type="RoomType"/>
   <xsd:element name="RoundingAmount" type="RoundingAmountType"/>
   <xsd:element name="SalesOrderID" type="SalesOrderIDType"/>
   <xsd:element name="SalesOrderLineID" type="SalesOrderLineIDType"/>
   <xsd:element name="SchemeURI" type="SchemeURIType"/>
   <xsd:element name="SealIssuerTypeCode" type="SealIssuerTypeCodeType"/>
   <xsd:element name="SealStatusCode" type="SealStatusCodeType"/>
   <xsd:element name="SealingPartyType" type="SealingPartyTypeType"/>
   <xsd:element name="SecurityClassificationCode" type="SecurityClassificationCodeType"/>
   <xsd:element name="SecurityID" type="SecurityIDType"/>
   <xsd:element name="SellerEventID" type="SellerEventIDType"/>
   <xsd:element name="SequenceID" type="SequenceIDType"/>
   <xsd:element name="SequenceNumberID" type="SequenceNumberIDType"/>
   <xsd:element name="SequenceNumeric" type="SequenceNumericType"/>
   <xsd:element name="SerialID" type="SerialIDType"/>
   <xsd:element name="ServiceInformationPreferenceCode"
                type="ServiceInformationPreferenceCodeType"/>
   <xsd:element name="ServiceName" type="ServiceNameType"/>
   <xsd:element name="ServiceNumberCalled" type="ServiceNumberCalledType"/>
   <xsd:element name="ServiceType" type="ServiceTypeType"/>
   <xsd:element name="ServiceTypeCode" type="ServiceTypeCodeType"/>
   <xsd:element name="SettlementDiscountAmount" type="SettlementDiscountAmountType"/>
   <xsd:element name="SettlementDiscountPercent" type="SettlementDiscountPercentType"/>
   <xsd:element name="SharesNumberQuantity" type="SharesNumberQuantityType"/>
   <xsd:element name="ShippingMarks" type="ShippingMarksType"/>
   <xsd:element name="ShippingOrderID" type="ShippingOrderIDType"/>
   <xsd:element name="ShippingPriorityLevelCode" type="ShippingPriorityLevelCodeType"/>
   <xsd:element name="ShipsRequirements" type="ShipsRequirementsType"/>
   <xsd:element name="ShortQuantity" type="ShortQuantityType"/>
   <xsd:element name="ShortageActionCode" type="ShortageActionCodeType"/>
   <xsd:element name="SignatureID" type="SignatureIDType"/>
   <xsd:element name="SignatureMethod" type="SignatureMethodType"/>
   <xsd:element name="SizeTypeCode" type="SizeTypeCodeType"/>
   <xsd:element name="SoleProprietorshipIndicator"
                type="SoleProprietorshipIndicatorType"/>
   <xsd:element name="SourceCurrencyBaseRate" type="SourceCurrencyBaseRateType"/>
   <xsd:element name="SourceCurrencyCode" type="SourceCurrencyCodeType"/>
   <xsd:element name="SourceForecastIssueDate" type="SourceForecastIssueDateType"/>
   <xsd:element name="SourceForecastIssueTime" type="SourceForecastIssueTimeType"/>
   <xsd:element name="SourceValueMeasure" type="SourceValueMeasureType"/>
   <xsd:element name="SpecialInstructions" type="SpecialInstructionsType"/>
   <xsd:element name="SpecialSecurityIndicator" type="SpecialSecurityIndicatorType"/>
   <xsd:element name="SpecialServiceInstructions" type="SpecialServiceInstructionsType"/>
   <xsd:element name="SpecialTerms" type="SpecialTermsType"/>
   <xsd:element name="SpecialTransportRequirements"
                type="SpecialTransportRequirementsType"/>
   <xsd:element name="SpecificationID" type="SpecificationIDType"/>
   <xsd:element name="SpecificationTypeCode" type="SpecificationTypeCodeType"/>
   <xsd:element name="SplitConsignmentIndicator" type="SplitConsignmentIndicatorType"/>
   <xsd:element name="StartDate" type="StartDateType"/>
   <xsd:element name="StartTime" type="StartTimeType"/>
   <xsd:element name="StatementTypeCode" type="StatementTypeCodeType"/>
   <xsd:element name="StatusAvailableIndicator" type="StatusAvailableIndicatorType"/>
   <xsd:element name="StatusCode" type="StatusCodeType"/>
   <xsd:element name="StatusReason" type="StatusReasonType"/>
   <xsd:element name="StatusReasonCode" type="StatusReasonCodeType"/>
   <xsd:element name="StreetName" type="StreetNameType"/>
   <xsd:element name="SubcontractingConditionsCode"
                type="SubcontractingConditionsCodeType"/>
   <xsd:element name="SubmissionDate" type="SubmissionDateType"/>
   <xsd:element name="SubmissionDueDate" type="SubmissionDueDateType"/>
   <xsd:element name="SubmissionMethodCode" type="SubmissionMethodCodeType"/>
   <xsd:element name="SubscriberID" type="SubscriberIDType"/>
   <xsd:element name="SubscriberType" type="SubscriberTypeType"/>
   <xsd:element name="SubscriberTypeCode" type="SubscriberTypeCodeType"/>
   <xsd:element name="SubstitutionStatusCode" type="SubstitutionStatusCodeType"/>
   <xsd:element name="SuccessiveSequenceID" type="SuccessiveSequenceIDType"/>
   <xsd:element name="SummaryDescription" type="SummaryDescriptionType"/>
   <xsd:element name="SupplierAssignedAccountID" type="SupplierAssignedAccountIDType"/>
   <xsd:element name="SupplyChainActivityTypeCode"
                type="SupplyChainActivityTypeCodeType"/>
   <xsd:element name="TareWeightMeasure" type="TareWeightMeasureType"/>
   <xsd:element name="TargetCurrencyBaseRate" type="TargetCurrencyBaseRateType"/>
   <xsd:element name="TargetCurrencyCode" type="TargetCurrencyCodeType"/>
   <xsd:element name="TargetInventoryQuantity" type="TargetInventoryQuantityType"/>
   <xsd:element name="TargetServicePercent" type="TargetServicePercentType"/>
   <xsd:element name="TariffClassCode" type="TariffClassCodeType"/>
   <xsd:element name="TariffCode" type="TariffCodeType"/>
   <xsd:element name="TariffDescription" type="TariffDescriptionType"/>
   <xsd:element name="TaxAmount" type="TaxAmountType"/>
   <xsd:element name="TaxCurrencyCode" type="TaxCurrencyCodeType"/>
   <xsd:element name="TaxEnergyAmount" type="TaxEnergyAmountType"/>
   <xsd:element name="TaxEnergyBalanceAmount" type="TaxEnergyBalanceAmountType"/>
   <xsd:element name="TaxEnergyOnAccountAmount" type="TaxEnergyOnAccountAmountType"/>
   <xsd:element name="TaxEvidenceIndicator" type="TaxEvidenceIndicatorType"/>
   <xsd:element name="TaxExclusiveAmount" type="TaxExclusiveAmountType"/>
   <xsd:element name="TaxExemptionReason" type="TaxExemptionReasonType"/>
   <xsd:element name="TaxExemptionReasonCode" type="TaxExemptionReasonCodeType"/>
   <xsd:element name="TaxIncludedIndicator" type="TaxIncludedIndicatorType"/>
   <xsd:element name="TaxInclusiveAmount" type="TaxInclusiveAmountType"/>
   <xsd:element name="TaxLevelCode" type="TaxLevelCodeType"/>
   <xsd:element name="TaxPointDate" type="TaxPointDateType"/>
   <xsd:element name="TaxTypeCode" type="TaxTypeCodeType"/>
   <xsd:element name="TaxableAmount" type="TaxableAmountType"/>
   <xsd:element name="TechnicalCommitteeDescription"
                type="TechnicalCommitteeDescriptionType"/>
   <xsd:element name="TechnicalName" type="TechnicalNameType"/>
   <xsd:element name="TelecommunicationsServiceCall"
                type="TelecommunicationsServiceCallType"/>
   <xsd:element name="TelecommunicationsServiceCallCode"
                type="TelecommunicationsServiceCallCodeType"/>
   <xsd:element name="TelecommunicationsServiceCategory"
                type="TelecommunicationsServiceCategoryType"/>
   <xsd:element name="TelecommunicationsServiceCategoryCode"
                type="TelecommunicationsServiceCategoryCodeType"/>
   <xsd:element name="TelecommunicationsSupplyType"
                type="TelecommunicationsSupplyTypeType"/>
   <xsd:element name="TelecommunicationsSupplyTypeCode"
                type="TelecommunicationsSupplyTypeCodeType"/>
   <xsd:element name="Telefax" type="TelefaxType"/>
   <xsd:element name="Telephone" type="TelephoneType"/>
   <xsd:element name="TenderEnvelopeID" type="TenderEnvelopeIDType"/>
   <xsd:element name="TenderEnvelopeTypeCode" type="TenderEnvelopeTypeCodeType"/>
   <xsd:element name="TenderResultCode" type="TenderResultCodeType"/>
   <xsd:element name="TenderTypeCode" type="TenderTypeCodeType"/>
   <xsd:element name="TendererRequirementTypeCode"
                type="TendererRequirementTypeCodeType"/>
   <xsd:element name="TendererRoleCode" type="TendererRoleCodeType"/>
   <xsd:element name="TestMethod" type="TestMethodType"/>
   <xsd:element name="Text" type="TextType"/>
   <xsd:element name="ThirdPartyPayerIndicator" type="ThirdPartyPayerIndicatorType"/>
   <xsd:element name="ThresholdAmount" type="ThresholdAmountType"/>
   <xsd:element name="ThresholdQuantity" type="ThresholdQuantityType"/>
   <xsd:element name="ThresholdValueComparisonCode"
                type="ThresholdValueComparisonCodeType"/>
   <xsd:element name="TierRange" type="TierRangeType"/>
   <xsd:element name="TierRatePercent" type="TierRatePercentType"/>
   <xsd:element name="TimeAmount" type="TimeAmountType"/>
   <xsd:element name="TimeDeltaDaysQuantity" type="TimeDeltaDaysQuantityType"/>
   <xsd:element name="TimeFrequencyCode" type="TimeFrequencyCodeType"/>
   <xsd:element name="TimezoneOffset" type="TimezoneOffsetType"/>
   <xsd:element name="TimingComplaint" type="TimingComplaintType"/>
   <xsd:element name="TimingComplaintCode" type="TimingComplaintCodeType"/>
   <xsd:element name="Title" type="TitleType"/>
   <xsd:element name="ToOrderIndicator" type="ToOrderIndicatorType"/>
   <xsd:element name="TotalAmount" type="TotalAmountType"/>
   <xsd:element name="TotalBalanceAmount" type="TotalBalanceAmountType"/>
   <xsd:element name="TotalConsumedQuantity" type="TotalConsumedQuantityType"/>
   <xsd:element name="TotalCreditAmount" type="TotalCreditAmountType"/>
   <xsd:element name="TotalDebitAmount" type="TotalDebitAmountType"/>
   <xsd:element name="TotalDeliveredQuantity" type="TotalDeliveredQuantityType"/>
   <xsd:element name="TotalGoodsItemQuantity" type="TotalGoodsItemQuantityType"/>
   <xsd:element name="TotalInvoiceAmount" type="TotalInvoiceAmountType"/>
   <xsd:element name="TotalMeteredQuantity" type="TotalMeteredQuantityType"/>
   <xsd:element name="TotalPackageQuantity" type="TotalPackageQuantityType"/>
   <xsd:element name="TotalPackagesQuantity" type="TotalPackagesQuantityType"/>
   <xsd:element name="TotalPaymentAmount" type="TotalPaymentAmountType"/>
   <xsd:element name="TotalTaskAmount" type="TotalTaskAmountType"/>
   <xsd:element name="TotalTaxAmount" type="TotalTaxAmountType"/>
   <xsd:element name="TotalTransportHandlingUnitQuantity"
                type="TotalTransportHandlingUnitQuantityType"/>
   <xsd:element name="TraceID" type="TraceIDType"/>
   <xsd:element name="TrackingDeviceCode" type="TrackingDeviceCodeType"/>
   <xsd:element name="TrackingID" type="TrackingIDType"/>
   <xsd:element name="TradeItemPackingLabelingTypeCode"
                type="TradeItemPackingLabelingTypeCodeType"/>
   <xsd:element name="TradeServiceCode" type="TradeServiceCodeType"/>
   <xsd:element name="TradingRestrictions" type="TradingRestrictionsType"/>
   <xsd:element name="TrainID" type="TrainIDType"/>
   <xsd:element name="TransactionCurrencyTaxAmount"
                type="TransactionCurrencyTaxAmountType"/>
   <xsd:element name="TransitDirectionCode" type="TransitDirectionCodeType"/>
   <xsd:element name="TransportAuthorizationCode" type="TransportAuthorizationCodeType"/>
   <xsd:element name="TransportEmergencyCardCode" type="TransportEmergencyCardCodeType"/>
   <xsd:element name="TransportEquipmentTypeCode" type="TransportEquipmentTypeCodeType"/>
   <xsd:element name="TransportEventTypeCode" type="TransportEventTypeCodeType"/>
   <xsd:element name="TransportExecutionPlanReferenceID"
                type="TransportExecutionPlanReferenceIDType"/>
   <xsd:element name="TransportExecutionStatusCode"
                type="TransportExecutionStatusCodeType"/>
   <xsd:element name="TransportHandlingUnitTypeCode"
                type="TransportHandlingUnitTypeCodeType"/>
   <xsd:element name="TransportMeansTypeCode" type="TransportMeansTypeCodeType"/>
   <xsd:element name="TransportModeCode" type="TransportModeCodeType"/>
   <xsd:element name="TransportServiceCode" type="TransportServiceCodeType"/>
   <xsd:element name="TransportServiceProviderRemarks"
                type="TransportServiceProviderRemarksType"/>
   <xsd:element name="TransportServiceProviderSpecialTerms"
                type="TransportServiceProviderSpecialTermsType"/>
   <xsd:element name="TransportUserRemarks" type="TransportUserRemarksType"/>
   <xsd:element name="TransportUserSpecialTerms" type="TransportUserSpecialTermsType"/>
   <xsd:element name="TransportationServiceDescription"
                type="TransportationServiceDescriptionType"/>
   <xsd:element name="TransportationServiceDetailsURI"
                type="TransportationServiceDetailsURIType"/>
   <xsd:element name="TransportationStatusTypeCode"
                type="TransportationStatusTypeCodeType"/>
   <xsd:element name="TypeCode" type="TypeCodeType"/>
   <xsd:element name="UBLVersionID" type="UBLVersionIDType"/>
   <xsd:element name="UNDGCode" type="UNDGCodeType"/>
   <xsd:element name="URI" type="URIType"/>
   <xsd:element name="UUID" type="UUIDType"/>
   <xsd:element name="UnknownPriceIndicator" type="UnknownPriceIndicatorType"/>
   <xsd:element name="UpperOrangeHazardPlacardID" type="UpperOrangeHazardPlacardIDType"/>
   <xsd:element name="UrgencyCode" type="UrgencyCodeType"/>
   <xsd:element name="UtilityStatementTypeCode" type="UtilityStatementTypeCodeType"/>
   <xsd:element name="ValidateProcess" type="ValidateProcessType"/>
   <xsd:element name="ValidateTool" type="ValidateToolType"/>
   <xsd:element name="ValidateToolVersion" type="ValidateToolVersionType"/>
   <xsd:element name="ValidationDate" type="ValidationDateType"/>
   <xsd:element name="ValidationResultCode" type="ValidationResultCodeType"/>
   <xsd:element name="ValidationTime" type="ValidationTimeType"/>
   <xsd:element name="ValidatorID" type="ValidatorIDType"/>
   <xsd:element name="ValidityStartDate" type="ValidityStartDateType"/>
   <xsd:element name="Value" type="ValueType"/>
   <xsd:element name="ValueAmount" type="ValueAmountType"/>
   <xsd:element name="ValueMeasure" type="ValueMeasureType"/>
   <xsd:element name="ValueQualifier" type="ValueQualifierType"/>
   <xsd:element name="ValueQuantity" type="ValueQuantityType"/>
   <xsd:element name="VarianceQuantity" type="VarianceQuantityType"/>
   <xsd:element name="VariantConstraintIndicator" type="VariantConstraintIndicatorType"/>
   <xsd:element name="VariantID" type="VariantIDType"/>
   <xsd:element name="VersionID" type="VersionIDType"/>
   <xsd:element name="VesselID" type="VesselIDType"/>
   <xsd:element name="VesselName" type="VesselNameType"/>
   <xsd:element name="WarrantyInformation" type="WarrantyInformationType"/>
   <xsd:element name="WebsiteURI" type="WebsiteURIType"/>
   <xsd:element name="WeekDayCode" type="WeekDayCodeType"/>
   <xsd:element name="Weight" type="WeightType"/>
   <xsd:element name="WeightNumeric" type="WeightNumericType"/>
   <xsd:element name="WeightingAlgorithmCode" type="WeightingAlgorithmCodeType"/>
   <xsd:element name="WorkPhase" type="WorkPhaseType"/>
   <xsd:element name="WorkPhaseCode" type="WorkPhaseCodeType"/>
   <xsd:element name="XPath" type="XPathType"/>
   <!-- ===== Type Definitions ===== -->
   <!-- ===== Basic Business Information Entity Type Definitions ===== -->
   <xsd:complexType name="AcceptedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AcceptedVariantsDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AccountFormatCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AccountIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AccountTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AccountingCostCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AccountingCostType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActivityTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActivityTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualDeliveryDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualDeliveryTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualDespatchDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualDespatchTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualPickupDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualPickupTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ActualTemperatureReductionQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdValoremIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdditionalAccountIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdditionalConditionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdditionalInformationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdditionalStreetNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AddressFormatCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AddressTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdjustmentReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdmissionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AdvertisementAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AgencyIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AgencyNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AirFlowPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AircraftIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AliasNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AllowanceChargeReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AllowanceChargeReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AllowanceTotalAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AltitudeMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AmountRateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:RateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AnimalFoodApprovedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AnimalFoodIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AnnualAverageAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ApplicationStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ApprovalDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ApprovalStatusType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AttributeIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AuctionConstraintIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AuctionURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AvailabilityDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AvailabilityStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AverageAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AverageSubsequentContractAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AwardDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AwardTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AwardingCriterionDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AwardingCriterionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AwardingCriterionTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="AwardingMethodTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BackOrderAllowedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BackorderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BackorderReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BalanceAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BalanceBroughtForwardIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BarcodeSymbologyIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BaseAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BaseQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BaseUnitMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BasedOnConsensusIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BasicConsumedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BatchQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BestBeforeDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BindingOnBuyerIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BirthDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BirthplaceNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BlockNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BrandNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BrokerAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BudgetYearNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BuildingNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BuildingNumberType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BulkCargoIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BusinessClassificationEvidenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BusinessIdentityEvidenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BuyerEventIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BuyerProfileURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="BuyerReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CV2IDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CalculationExpressionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CalculationExpressionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CalculationMethodCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CalculationRateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:RateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CalculationSequenceNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CallBaseAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CallDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CallExtensionAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CallTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CancellationNoteType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CandidateReductionConstraintIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CandidateStatementType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CanonicalizationMethodType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CapabilityTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CardChipCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CardTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CargoTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CarrierAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CarrierServiceInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CatalogueIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CategoryNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CertificateTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CertificateTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChangeConditionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChannelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChannelType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CharacterSetCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CharacteristicsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChargeIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChargeTotalAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChargeableQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChargeableWeightMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChildConsignmentQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ChipApplicationIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CityNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CitySubdivisionNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CodeValueType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CollaborationPriorityCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CommentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CommodityCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CompanyIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CompanyLegalFormCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CompanyLegalFormType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CompanyLiquidationStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ComparedValueMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ComparisonDataCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ComparisonDataSourceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ComparisonForecastIssueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ComparisonForecastIssueTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CompletionIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConditionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConditionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConditionsDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConditionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsigneeAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsignmentQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsignorAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsolidatableIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConstitutionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumerIncentiveTacticTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumerUnitQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumersEnergyLevelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumersEnergyLevelType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionEnergyQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionLevelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionLevelType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionReportIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ConsumptionWaterQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContainerizedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContentUnitQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractFolderIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractSubdivisionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractedCarrierAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ContractingSystemCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CoordinateSystemCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CopyIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CorporateRegistrationTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CorporateStockAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CorrectionAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CorrectionTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CorrectionTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CorrectionUnitAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CountrySubentityCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CountrySubentityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CreditLineAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CreditNoteTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CreditedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CrewQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CurrentChargeTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CurrentChargeTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomerAssignedAccountIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomerReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomizationIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomsClearanceServiceInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomsImportClassifiedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomsStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="CustomsTariffQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DamageRemarksType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DangerousGoodsApprovedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DataSendingCapabilityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DataSourceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DebitLineAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DebitedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeclarationTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeclaredCarriageValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeclaredCustomsValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeclaredForCarriageValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeclaredStatisticsValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeliveredQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DeliveryInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DemurrageInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DepartmentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DescriptionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DespatchAdviceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DifferenceTemperatureReductionQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DirectionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DisplayTacticTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DispositionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DistrictType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentHashType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentStatusReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentStatusReasonDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DocumentationFeeAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DurationMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DutyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="DutyType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EarliestPickupDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EarliestPickupTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EconomicOperatorRegistryURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EffectiveDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EffectiveTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ElectronicDeviceDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ElectronicMailType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EmbeddedDocumentBinaryObjectType">
      <xsd:simpleContent>
         <xsd:extension base="udt:BinaryObjectType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EmergencyProceduresCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EmployeeQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EncodingCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EndDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EndTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EndpointIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EnvironmentalEmissionTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedConsumedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedDeliveryDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedDeliveryTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedDespatchDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedDespatchTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedOverallContractAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EstimatedOverallContractQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EvaluationCriterionTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="EvidenceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExceptionResolutionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExceptionStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExchangeMarketIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExclusionReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExecutionRequirementCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExemptionReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExemptionReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpectedOperatorQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpectedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpenseCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpiryDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpiryTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpressionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExpressionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExtendedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ExtensionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FaceValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FamilyNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FeatureTacticTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FeeAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FeeDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FileNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FinancingInstrumentCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FirstNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FirstShipmentAvailibilityDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FloorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FollowupContractIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ForecastPurposeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ForecastTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FormatCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ForwarderServiceInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FreeOfChargeIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FreeOnBoardValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FreightForwarderAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FreightRateClassCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FrequencyType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FrozenDocumentIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FrozenPeriodDaysNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FullnessIndicationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FullyPaidSharesIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FundingProgramCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="FundingProgramType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GasPressureQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GenderCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GeneralCargoIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GovernmentAgreementConstraintIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GrossTonnageMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GrossVolumeMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GrossWeightMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GuaranteeTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GuaranteedDespatchDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="GuaranteedDespatchTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HandlingCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HandlingInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HashAlgorithmMethodType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HaulageInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HazardClassIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HazardousCategoryCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HazardousRegulationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HazardousRiskIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HeatingTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HeatingTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HigherTenderAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HolderNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HumanFoodApprovedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HumanFoodIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="HumidityPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IdentificationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IdentificationIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ImmobilizationCertificateIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ImportanceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IndicationIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IndustryClassificationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InformationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InformationURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InhalationToxicityZoneCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InhouseMailType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InspectionMethodCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InstallmentDueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InstructionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InstructionNoteType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InsurancePremiumAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InsuranceValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InventoryValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InvoiceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InvoicedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="InvoicingPartyReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IssueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IssueNumberIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IssueTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="IssuerIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ItemClassificationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ItemUpdateRequestIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="JobTitleType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="JourneyIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="JustificationDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="JustificationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="KeywordType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LanguageIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LastRevisionDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LastRevisionTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestDeliveryDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestDeliveryTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestMeterQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestMeterReadingDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestMeterReadingMethodCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestMeterReadingMethodType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestPickupDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestPickupTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestProposalAcceptanceDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatestSecurityClearanceDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatitudeDegreesMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatitudeDirectionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LatitudeMinutesMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LeadTimeMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LegalReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LegalStatusIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LiabilityAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LicensePlateIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LifeCycleStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LimitationDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LineCountNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LineExtensionAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LineIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LineNumberNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LineStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LineType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ListValueType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LivestockIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LoadingLengthMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LoadingSequenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LocaleCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LocationIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LocationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LocationTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LoginType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LogoReferenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LongitudeDegreesMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LongitudeDirectionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LongitudeMinutesMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LossRiskResponsibilityCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LossRiskType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LotNumberIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LowTendersDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LowerOrangeHazardPlacardIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="LowerTenderAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MandateTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ManufactureDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ManufactureTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MarkAttentionIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MarkAttentionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MarkCareIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MarkCareType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MarketValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MarkingIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MathematicOperatorCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumAdvertisementAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumBackorderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumCopiesNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumNumberNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumOperatorQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumOrderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumPaidAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumPaymentInstructionsNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumValueType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MaximumVariantQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MedicalFirstAidGuideCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterConstantCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterConstantType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterNumberType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterReadingCommentsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterReadingTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MeterReadingTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MiddleNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MimeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumBackorderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumImprovementBidType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumInventoryQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumNumberNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumOrderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MinimumValueType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MiscellaneousEventTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ModelNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MonetaryScopeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MovieTitleType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MultipleOrderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="MultiplierFactorNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NameCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NameSuffixType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NationalityIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NatureCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NegotiationDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NetNetWeightMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NetTonnageMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NetVolumeMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NetWeightMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NetworkIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NominationDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NominationTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NormalTemperatureReductionQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NoteType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="NotificationTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OccurrenceDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OccurrenceTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OnCarriageIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OneTimeChargeTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OneTimeChargeTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OntologyURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OpenTenderIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OperatingYearsQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OptionalLineItemIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OptionsDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderIntervalDaysNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderQuantityIncrementNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderResponseCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderableIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderableUnitFactorRateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:RateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrderableUnitType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OrganizationDepartmentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OriginalContractingSystemIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OriginalJobIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OtherConditionsIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OtherInstructionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OtherNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OutstandingQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OutstandingReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OversupplyQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="OwnerTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackLevelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackSizeNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackageLevelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackagingTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackingCriteriaCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PackingMaterialType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaidAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaidDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaidTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ParentDocumentIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ParentDocumentLineReferenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ParentDocumentTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ParentDocumentVersionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PartPresentationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PartecipationPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PartialDeliveryIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ParticipationPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PartyCapacityAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PartyTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PartyTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PassengerQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PasswordType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PayPerViewType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PayableAlternativeAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PayableAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PayableRoundingAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PayerReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentAlternativeCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentChannelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentDueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentFrequencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentMeansCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentMeansIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentNoteType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentOrderReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentPurposeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PaymentTermsDetailsURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PenaltyAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PenaltySurchargePercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PerUnitAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PerformanceMetricTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PerformanceValueQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PerformingCarrierAssignedIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PersonalSituationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PhoneNumberType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PlacardEndorsementType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PlacardNotationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PlannedDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PlotIdentificationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PositionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PostEventNotificationDurationMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PostalZoneType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PostboxType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PowerIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreCarriageIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreEventNotificationDurationMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreferenceCriterionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrepaidAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrepaidIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrepaidPaymentReferenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousCancellationReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousJobIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousMeterQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousMeterReadingDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousMeterReadingMethodCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousMeterReadingMethodType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PreviousVersionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriceAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriceChangeReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriceEvaluationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriceRevisionFormulaDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriceTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PricingCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PricingUpdateRequestIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrimaryAccountNumberIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrintQualifierType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PriorityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrivacyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrizeDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PrizeIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProcedureCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProcessDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProcessReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProcessReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProcurementSubTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProcurementTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProductTraceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProfileExecutionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProfileIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProfileStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProgressPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PromotionalEventTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ProviderTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PublishAwardIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PurposeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="PurposeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="QualityControlCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="QuantityDiscrepancyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="QuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RadioCallSignIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RailCarIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RankType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:RateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReceiptAdviceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReceivedDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReceivedElectronicTenderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReceivedForeignTenderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReceivedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReceivedTenderQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReferenceDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReferenceEventCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReferenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReferenceTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReferenceType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReferencedConsignmentIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RefrigeratedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RefrigerationOnIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegisteredDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegisteredTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegistrationDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegistrationExpirationDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegistrationIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegistrationNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegistrationNationalityIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegistrationNationalityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RegulatoryDomainType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RejectActionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RejectReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RejectReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RejectedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RejectionNoteType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReleaseIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReliabilityPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RemarksType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReminderSequenceNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReminderTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReplenishmentOwnerDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequestForQuotationLineIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequestedDeliveryDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequestedDespatchDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequestedDespatchTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequestedInvoiceCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequestedPublicationDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequiredCurriculaIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequiredCustomsIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequiredDeliveryDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequiredDeliveryTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RequiredFeeAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResidenceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResidenceTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResidentOccupantsNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResolutionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResolutionDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResolutionTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResolutionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResponseCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResponseDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ResponseTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RetailEventNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RetailEventStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReturnabilityIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReturnableMaterialIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ReturnableQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RevisedForecastLineIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RevisionDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RevisionStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RevisionTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RoamingPartnerNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RoleCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RoleDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RoomType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="RoundingAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SalesOrderIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SalesOrderLineIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SchemeURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SealIssuerTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SealStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SealingPartyTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SecurityClassificationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SecurityIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SellerEventIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SequenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SequenceNumberIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SequenceNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SerialIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ServiceInformationPreferenceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ServiceNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ServiceNumberCalledType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ServiceTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ServiceTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SettlementDiscountAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SettlementDiscountPercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SharesNumberQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ShippingMarksType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ShippingOrderIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ShippingPriorityLevelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ShipsRequirementsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ShortQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ShortageActionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SignatureIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SignatureMethodType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SizeTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SoleProprietorshipIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SourceCurrencyBaseRateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:RateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SourceCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SourceForecastIssueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SourceForecastIssueTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SourceValueMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecialInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecialSecurityIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecialServiceInstructionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecialTermsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecialTransportRequirementsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecificationIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SpecificationTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SplitConsignmentIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StartDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StartTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StatementTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StatusAvailableIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StatusReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StatusReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="StreetNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubcontractingConditionsCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubmissionDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubmissionDueDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubmissionMethodCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubscriberIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubscriberTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubscriberTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SubstitutionStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SuccessiveSequenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SummaryDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SupplierAssignedAccountIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="SupplyChainActivityTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TareWeightMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TargetCurrencyBaseRateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:RateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TargetCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TargetInventoryQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TargetServicePercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TariffClassCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TariffCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TariffDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxCurrencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxEnergyAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxEnergyBalanceAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxEnergyOnAccountAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxEvidenceIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxExclusiveAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxExemptionReasonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxExemptionReasonType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxIncludedIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxInclusiveAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxLevelCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxPointDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TaxableAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TechnicalCommitteeDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TechnicalNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelecommunicationsServiceCallCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelecommunicationsServiceCallType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelecommunicationsServiceCategoryCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelecommunicationsServiceCategoryType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelecommunicationsSupplyTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelecommunicationsSupplyTypeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelefaxType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TelephoneType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TenderEnvelopeIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TenderEnvelopeTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TenderResultCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TenderTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TendererRequirementTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TendererRoleCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TestMethodType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TextType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ThirdPartyPayerIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ThresholdAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ThresholdQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ThresholdValueComparisonCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TierRangeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TierRatePercentType">
      <xsd:simpleContent>
         <xsd:extension base="udt:PercentType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TimeAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TimeDeltaDaysQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TimeFrequencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TimezoneOffsetType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TimingComplaintCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TimingComplaintType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TitleType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ToOrderIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalBalanceAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalConsumedQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalCreditAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalDebitAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalDeliveredQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalGoodsItemQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalInvoiceAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalMeteredQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalPackageQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalPackagesQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalPaymentAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalTaskAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalTaxAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TotalTransportHandlingUnitQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TraceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TrackingDeviceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TrackingIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TradeItemPackingLabelingTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TradeServiceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TradingRestrictionsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TrainIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransactionCurrencyTaxAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransitDirectionCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportAuthorizationCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportEmergencyCardCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportEquipmentTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportEventTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportExecutionPlanReferenceIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportExecutionStatusCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportHandlingUnitTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportMeansTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportModeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportServiceCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportServiceProviderRemarksType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportServiceProviderSpecialTermsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportUserRemarksType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportUserSpecialTermsType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportationServiceDescriptionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportationServiceDetailsURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TransportationStatusTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="TypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UBLVersionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UNDGCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="URIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UUIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UnknownPriceIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UpperOrangeHazardPlacardIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UrgencyCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="UtilityStatementTypeCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidateProcessType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidateToolType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidateToolVersionType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidationDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidationResultCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidationTimeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TimeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidatorIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValidityStartDateType">
      <xsd:simpleContent>
         <xsd:extension base="udt:DateType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValueAmountType">
      <xsd:simpleContent>
         <xsd:extension base="udt:AmountType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValueMeasureType">
      <xsd:simpleContent>
         <xsd:extension base="udt:MeasureType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValueQualifierType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValueQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="ValueType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="VarianceQuantityType">
      <xsd:simpleContent>
         <xsd:extension base="udt:QuantityType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="VariantConstraintIndicatorType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IndicatorType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="VariantIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="VersionIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="VesselIDType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="VesselNameType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NameType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WarrantyInformationType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WebsiteURIType">
      <xsd:simpleContent>
         <xsd:extension base="udt:IdentifierType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WeekDayCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WeightNumericType">
      <xsd:simpleContent>
         <xsd:extension base="udt:NumericType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WeightType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WeightingAlgorithmCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WorkPhaseCodeType">
      <xsd:simpleContent>
         <xsd:extension base="udt:CodeType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="WorkPhaseType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
   <xsd:complexType name="XPathType">
      <xsd:simpleContent>
         <xsd:extension base="udt:TextType"/>
      </xsd:simpleContent>
   </xsd:complexType>
</xsd:schema>
<!-- ===== Copyright Notice ===== --><!--
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.    
-->