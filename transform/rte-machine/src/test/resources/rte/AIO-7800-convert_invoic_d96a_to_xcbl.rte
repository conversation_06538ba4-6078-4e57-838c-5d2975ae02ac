%@(#)  convert_invoic_d96a_to_xcbl.rte                         modif:19/06/2018
!==============================================================================
% Fichier       :  convert_invoic_d96a_to_xcbl.rte             version 5.0
!------------------------------------------------------------------------------
% Description   : Conversion D96A vers XCBL
!------------------------------------------------------------------------------
% Projet        : All in one
!------------------------------------------------------------------------------
% Auteur        : GENERIX
!==============================================================================
! 14/11/2014  RMA    Creation
! 20/01/2015  EBE    Modification seg IMD, seg RFF, seg TAX
! 22/01/2015  EBE    Modification seg TAX
! 13/03/2015  EBE    Validation du message
! 19/06/2018  XR     Conversion de script compilé à script interprété
!==============================================================================
#define PROG_NAME "convert_invoic_d96a_to_xcbl"
#define PROG_DATE "19/06/2018"
#define PROG_VER "5.0"
#define PROG_INFO build(PROG_NAME," du ",PROG_DATE," v", PROG_VER)
#define NOW time("now","%d.%m.%Y %H:%M:%S")

!
! Définition du message en entrée
!
message "UN-EDIFACT/96/draft/A/invoic_interchange.msg" receiving
!
! Définition du message en sortie
!
schema "XML/xcbl/v4_0/financial/v1_0/financial.xsd"  validating, building
/*
schema "xcbl40/schema/invoice/financial.xsd" validating, building
message "xcbl40/dtd/Invoice.dtd" building
*/

!
! Section d'initialisation
!
begin
   
	PROCESS_ERRONEOUS := TRUE
	pXML.REMOVEWHITESPACES := "1"
	pXML.SKIPENCODING := "1"
	bfVariablesInit()
endbegin

!
! Date et heure de préparation du document
! Ce segment permet d'envelopper l'interchange et d'identifier le destinataire et l'émetteur du message.
! Le principe du segment UNB est le même qu'une enveloppe physique contenant une ou plusieurs lettres/documents et détaillant l'adresse à laquelle l'enveloppe a été envoyée et arrivera
!

nodein UNB
	bfLOG("Converting segment", "UNB")
	nLINE++

	if(nUNB = 1) then
		bfERROR ("99InternalError0012","Expected one UNB, get more than one", "UNB", nLINE)
		bfEXIT(12)
	endif

	nodeout SInvoice gGInvoice
		eAxmlns:core := "rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd"
		eAxmlns := "rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"
	   	! eAxmlns:xsi := "http://www.w3.org/2001/XMLSchema-instance"
		! eAxsi:schemaLocation := "rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"
	endnodeout

    tPreparationDateTimeOfDocument := tfConvertUNBDateXML(build(eS004.0017,eS004.0019),"UNB_eS004.0017")

	!Date de dématérialisation
	nodeout Score:DateQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded, gGcore:DateQualifier
		eEcore:DateQualifierCoded := "DocumentReceivedDateTime"
	endnodeout
	nodeout Score:Date gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded
		! Ne marche pas pour TU eEcore:Date := time("now","%Y-%m-%dT%H:%M:%S") !YYMMDDHHMM
		eEcore:Date := tPreparationDateTimeOfDocument
	endnodeout

	!PreparationDateTimeOfDocument
	nodeout Score:DateQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded, gGcore:DateQualifier
		eEcore:DateQualifierCoded := "PreparationDateTimeOfDocument"
	endnodeout

	nodeout Score:Date gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded
		eEcore:Date := tPreparationDateTimeOfDocument
	endnodeout

	nUNB++
endnodein

!
! Ce segment est utilisé pour débuter, spécifier et identifier un message.
!
nodein UNH
	bfLOG("Converting segment", "UNH")
	nLINE++

	if(eS009.0065 <> "INVOIC") then
		bfERROR ("99InternalError0010","Incorrect EDIFACT message", "UNH", nLINE)
		bfEXIT(10)
	endif

	if(nUNB = 0) then
		bfERROR ("99InternalError0011","UNB required in the interchange", "UNH", nLINE)
		bfEXIT(11)
	endif

	nUNH++
	if(nUNH > 1) then
		bfERROR ("99InternalError0009","Too much UNH", "UNH", nLINE)
		bfEXIT(9)
	endif

	nodeout Score:LanguageCoded gGInvoice, gGInvoiceHeader, gGInvoiceLanguage
		eEcore:LanguageCoded := "en"
	endnodeout

endnodein

!
! Type du document
! Numéro du document
! Ce segment est utilisé pour identifier le message facture et en préciser le numéro et la fonction.
!
nodein BGM
	bfLOG("Converting segment", "BGM")
	nLINE++

	bBGM := TRUE

	switch eC002.1001
		case "380" :
		case "389" :
			tTypeDocument := "CommercialInvoice"
		case "381" :
		case "261" :
			tTypeDocument := "CreditNoteGoodsAndServices"
		case "382" :
			tTypeDocument := "CommissionNote"
		case "384" :
			tTypeDocument := "CorrectedInvoice"
		case "82" :
			tTypeDocument := "MeteredServicesInvoice"
		case "83" :
			tTypeDocument := "CreditNoteFinancialAdjustment"
		case "84" :
			tTypeDocument := "DebitNoteFinancialAdjustment"
		case "130" :
			tTypeDocument := "InvoicingDataSheet"
		case "325" :
			tTypeDocument := "ProformaInvoice"
		case "80" :
			tTypeDocument := "DebitNoteGoodsAndServices"
		case "385" :
			tTypeDocument := "ConsolidatedInvoice"
		case "386" :
			tTypeDocument := "PrepaymentInvoice"
		case "390" :
			tTypeDocument := "DelcredereInvoice"
		case "393" :
			tTypeDocument := "FactoredInvoice"
		default:
			tTypeDocument := "Other"
			bfWARNING("Warn0001", "", build("Warning eC002.1001=", eC002.1001, " not managed"), "BGM", nLINE)
	endswitch

	nodeout SInvoiceTypeCoded gGInvoice,gGInvoiceHeader,gGInvoiceType
		eEInvoiceTypeCoded :=  tTypeDocument
	endnodeout
	if(tTypeDocument = "Other") then
		nodeout SInvoiceTypeCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceType
			if (eC002.1001 = EMPTY) then
				eEInvoiceTypeCodedOther :=  " "
			else
				eEInvoiceTypeCodedOther :=  eC002.1001
			endif
		endnodeout
	endif

	nodeout SInvoiceNumber gGInvoice,gGInvoiceHeader,gGInvoiceNumber
		if (e1004 = EMPTY) then
			bfERROR ("XCBL_ERROR_BGM_1004_EMPTY","e1004 in BGM is empty", "BGM", nLINE)
			eEInvoiceNumber := " "
		else
			eEInvoiceNumber := e1004
		endif
	endnodeout

	if(eC002.1001 = "389" or eC002.1001 = "261") then
		nodeout Score:NoteID gGInvoice,gGInvoiceHeader,gGListOfStructuredNote, gGcore:StructuredNote
			eEcore:NoteID := "SelfBillFlag"
		endnodeout
		nodeout Score:AgencyCoded gGInvoice,gGInvoiceHeader,gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice,gGInvoiceHeader,gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
			eEcore:AgencyCodedOther := "InternalCode"
		endnodeout
	endif

	switch (e1225)
		case "9":
			nodeout SInvoicePurposeCoded gGInvoice, gGInvoiceHeader, gGInvoicePurpose
				eEInvoicePurposeCoded := "Original"
			endnodeout
		case "7":
			nodeout SInvoicePurposeCoded gGInvoice, gGInvoiceHeader, gGInvoicePurpose
				eEInvoicePurposeCoded := "Duplicate"
			endnodeout
		case "43":
			nodeout SInvoicePurposeCoded gGInvoice, gGInvoiceHeader, gGInvoicePurpose
				eEInvoicePurposeCoded := "Other"
			endnodeout
			nodeout SInvoicePurposeCodedOther gGInvoice, gGInvoiceHeader, gGInvoicePurpose
				eEInvoicePurposeCodedOther := "ExtraTransmission"
			endnodeout
		default:
			nodeout SInvoicePurposeCoded gGInvoice, gGInvoiceHeader, gGInvoicePurpose
				eEInvoicePurposeCoded := "Original"
			endnodeout
			bfERROR ("XCBL_ERROR_BGM1225","Unexpected e1225 in BGM.",tSegment, nLINE)
	endswitch
endnodein

!
! Date du document
! Date d'operation
! Date de référence
! Ce segment est utilisé pour préciser la date ou la période de la facture. Il peut également être utilisé pour préciser la date à laquelle les marchandises ont été expédiées, livrées ou enlevées.
!
nodein DTM
	nLINE++
	bfLOG("Converting segment", "DTM")
	switch eC507.2005
		case "11" : ! Despatch date and or time
			nodeout SActualShipDate gGInvoice, gGInvoiceHeader, gGInvoiceDates
				eEActualShipDate := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+11(",eC507.2380,":",eC507.2379,")"))
			endnodeout
		case "137" : ! Document/message date/time
			nNBInvoiceIssueDate ++
			bInvoiceIssueDate := TRUE
			tDateFacture := eC507.2380
			if (nNBInvoiceIssueDate > 1) then
				bfERROR ("XCBL_ERROR_DTM137_MORE_THAN_ONE","More than one DTM+137.",tSegment, nLINE)
			else
				nodeout SInvoiceIssueDate gGInvoice, gGInvoiceHeader
					eEInvoiceIssueDate := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+137(",eC507.2380,":",eC507.2379,")"))
				endnodeout
			endif
		case "263" : ! 	Invoicing period format :718 = CCYYMMDD-CCYYMMDD:
			if (eC507.2379 = "718") then
				if (length(eC507.2380)=17) then
					tDate1:=substr(eC507.2380,1,8)
					tDate2:=substr(eC507.2380,10,8)
				else
					if(length(eC507.2380)=16) then
						tDate1:=substr(eC507.2380,1,8)
						tDate2:=substr(eC507.2380,9,8)
					else
						tDate1 := "19700101"
						tDate2 := "19700101"
						bfERROR ("XCBL_ERROR_PERIOD_NOT_WELLFORMATTED","Period not well formated. Forced the two dates to 1970-01-01T00:00:00",tSegment, nLINE)
					endif
				endif
			else
				tDate1 := "19700101"
				tDate2 := "19700101"
				bfERROR ("XCBL_ERROR_FORMAT_NOT_MANAGED","Format not managed. Forced period to 1970-01-01T00:00:00",tSegment, nLINE)
			endif
			nodeout Score:StartDate gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGInvoicingPeriod
				eEcore:StartDate := tfConvertDateXML(tDate1, eC507.2379, build("DTM+263(",tDate1,":",eC507.2379,")"))
			endnodeout
			nodeout Score:EndDate gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGInvoicingPeriod
				eEcore:EndDate := tfConvertDateXML(tDate2, eC507.2379, build("DTM+263(",tDate2,":",eC507.2379,")"))
			endnodeout
		case "200" :
			nodeout Score:Date gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded
				eEcore:Date := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+200(",eC507.2380,":",eC507.2379,")"))
			endnodeout
			nodeout Score:DateQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded, gGcore:DateQualifier
				eEcore:DateQualifierCoded := "Collection"
			endnodeout
		case "35" :
			nodeout SActualDeliveryDate gGInvoice, gGInvoiceHeader, gGInvoiceDates
				eEActualDeliveryDate := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+35(",eC507.2380,":",eC507.2379,")"))
			endnodeout
		default:
			bfWARNING("Warn0001", "", build("Warning eC507.2005=", eC507.2005, " not managed"), "DTM", nLINE)
	endswitch
endnodein

nodein PAI
	bfLOG("Converting segment","PAI")
	nLINE++
	tPaymentMeanCoded := ""
	switch eC534.4461
		case "20" : ! Cheque
			tPaymentMeanCoded := "Cheque"
		case "42" : ! PaymentToBankAccount
			tPaymentMeanCoded := "PaymentToBankAccount"
		case "60" : ! PromissoryNote
			tPaymentMeanCoded := "PromissoryNote"
		case "70" : ! BillDrawnByTheCreditorOnTheDebtor
			tPaymentMeanCoded := "BillDrawnByTheCreditorOnTheDebtor"
		default:
			tPaymentMeanCoded := ""
	endswitch
		if(tPaymentMeanCoded <> EMPTY) then
		nodeout Score:PaymentMeanCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod
			eEcore:PaymentMeanCoded := tPaymentMeanCoded
		endnodeout
	endif
endnodein

!
! Conditions d'escompte (AAB)
! Informations libre d'entete (AAI)
! Conditions de pénalité (PMD)
! Mentions sur le regime legal du document (SIN)
! Informations libres d'entete (SUR, AAI)
! Indemnite forfaitaire de penalite (PMT)
! Forme juridique et captial social vendeur (REG)
!
nodein FTX
	bfLOG("Converting segment", build("FTX qualifiant: ", e4451))
	nLINE++

	switch e4451
		case "SIN" : ! Special instructions
			tFTXSIN := build(tFTXSIN," ",eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
			! Generé dans le prochain segment TAX
		case "AAB" : ! Terms of payments
			nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
				eEcore:PaymentTermCoded := "Discount"
			endnodeout
			nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
				eEcore:PaymentTermDescription := build(eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
			endnodeout
		case "SUR" : ! Supplier Remarks
			nodeout Score:TextTypeCoded gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:TextTypeCoded := "Other"
			endnodeout
			nodeout Score:TextTypeCodedOther gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:TextTypeCodedOther := "SupplierRemarks"
			endnodeout
			nodeout Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:GeneralNote :=  build(eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
			endnodeout
		case "AAI" : ! General information
			switch eC108.4440.1
				case "01" :
					nodeout Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
						eEcore:GeneralNote :=  "GoodsRelatedInvoice"
					endnodeout
					nodeout Score:NoteID gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
						eEcore:NoteID :=  "01"
					endnodeout
					nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
						eEcore:AgencyCoded :=  "Other"
					endnodeout
					nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
						eEcore:AgencyCodedOther :=  "GS1France"
					endnodeout
				case "02" :
					nodeout Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
						eEcore:GeneralNote :=  "ServicesRelatedInvoice"
					endnodeout
					nodeout Score:NoteID gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
						eEcore:NoteID :=  "02"
					endnodeout
					nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
						eEcore:AgencyCoded :=  "Other"
					endnodeout
					nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
						eEcore:AgencyCodedOther :=  "GS1France"
					endnodeout
				default:
					nodeout Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
						eEcore:GeneralNote :=  build(eC108.4440.1,eC108.4440.2, eC108.4440.3, eC108.4440.4,eC108.4440.5)
					endnodeout
			endswitch
			nodeout Score:TextTypeCoded gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:TextTypeCoded := "Other"
			endnodeout
			nodeout Score:TextTypeCodedOther gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:TextTypeCodedOther := "GeneralInformation"
			endnodeout
		case "PMD" : ! Payment detail/remittance information
			!AIO-6584
			tPMD := build(tPMD," ",eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
		case "PMT": ! Payment information
			!AIO-6584
			bTestPMT := TRUE
			nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
				eEcore:PaymentTermCoded := "LatePayment"
			endnodeout
			nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
				eEcore:PaymentTermDescription := build(eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
			endnodeout
		case "REG" : ! Regulatory information
			nNBFtxReg++
			if (nNBFtxReg > 1) then
				bfERROR ("XCBL_ERROR_FTX_REG_MORE_THAN_ONE","More than one FTX+REG.",tSegment, nLINE)
			else
				tFTXREGC10844401:=eC108.4440.1
				tFTXREGC10844402:=eC108.4440.2
				tFTXREGC10844403:=eC108.4440.3
			endif
		default:
			nodeout Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:GeneralNote := build(eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
			endnodeout
			nodeout Score:TextTypeCoded gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:TextTypeCoded := "Other"
			endnodeout
			nodeout Score:TextTypeCodedOther gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:TextTypeCodedOther := e4451
			endnodeout
	endswitch
endnodein

!
! Ce segment est utilisé pour spécifier les références relatives à l'intégralité de la facture, par exemple, des numéros de commande, de bon de livraison ou d'avis d'expédition.
! Document de référence
!
nodein RFF g1
	!AIO-6584
	if not bTestPMT then
		if index(tPMD,"40") <> 0 or index(toupper(tPMD),"QUARANTE") <> 0 then
			nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
				eEcore:PaymentTermCoded := "LatePayment"
			endnodeout
			nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
				eEcore:PaymentTermDescription := "."!tPMD
			endnodeout
		endif
		bTestPMT := TRUE
	endif

	bfLOG("Converting segment", "RFF g1")
	nLINE++
	tRFF1153 := eC506.1153
	switch (eC506.1153)
		case "DQ" : ! Delivery note number
			tBonDeLivraison := eC506.1154
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGASNNumber
				eEcore:RefNum := eC506.1154
			endnodeout
		case "ON" : ! Order number (purchase)
			nodeout Score:BuyerOrderNumber gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGPurchaseOrderReference
				eEcore:BuyerOrderNumber := eC506.1154
			endnodeout
		case "BC" : ! Buyer's contract number
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ContractID
				eEcore:Ident := eC506.1154
			endnodeout
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ContractID, gGcore:Agency
				eEcore:AgencyCoded := "AssignedByBuyerOrBuyersAgent"
			endnodeout
		case "CT" : ! Contract number
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ContractID
				eEcore:Ident := eC506.1154
			endnodeout
		case "IV" : ! 	Invoice number
			nodeout SInvoiceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGListOfRelatedInvoiceRef, gGRelatedInvoiceRef, gGRelatedInvoiceType
				eEInvoiceTypeCoded := "CommercialInvoice"
			endnodeout
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGListOfRelatedInvoiceRef, gGRelatedInvoiceRef, gGInvoiceNumber
				eEcore:RefNum := eC506.1154
			endnodeout
		case "DL" :! Debit note number
			tNoteDeDebit := eC506.1154
			nodeout SInvoiceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGListOfRelatedInvoiceRef, gGRelatedInvoiceRef, gGRelatedInvoiceType
				eEInvoiceTypeCoded := "CreditInvoice"
			endnodeout
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGListOfRelatedInvoiceRef, gGRelatedInvoiceRef, gGInvoiceNumber
				eEcore:RefNum := eC506.1154
			endnodeout
		case "AAK" :
			tRFF1153AAK := eC506.1154
		case "PL" :
			tRFF1153PL := eC506.1154
		case "ADE" :
			tRFF1153ADE := eC506.1154
		case "04G" :
			nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
				eEcore:ReferenceTypeCoded := "ReturnedGoodsAuthorizationNumber"
			endnodeout
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
				eEcore:RefNum := eC506.1154
			endnodeout
		case "CR" :
			if (eC506.1154 <> EMPTY) then
				nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
					eEcore:ReferenceTypeCoded := "CustomerReferenceNumber"
				endnodeout
				nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
					eEcore:RefNum := eC506.1154
				endnodeout
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g1", nLINE)
	endswitch
endnodein

!
! Date de reference
! Ce segment est utilisé pour indiquer les dates du document ou du message identifié dans le segment RFF précédent.
!
nodein DTM g1
	bfLOG("Converting segment", "DTM g1")
	nLINE++

	if eC507.2005="171" then
		switch tRFF1153
			case "DQ" : ! Delivery note number
				nodeout Score:RefDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGASNNumber
					eEcore:RefDate := tfConvertDateXML(eC507.2380, eC507.2379, build("RFF+DQ_DTM+171(g1)(",eC507.2380,":",eC507.2379,")"))
				endnodeout
			case "ON" : ! Order number (purchase)
				nodeout Score:PurchaseOrderDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGPurchaseOrderReference
					eEcore:PurchaseOrderDate := tfConvertDateXML(eC507.2380, eC507.2379, build("RFF+ON_DTM+171(g1)(",eC507.2380,":",eC507.2379,")"))
				endnodeout
			case "BC" : ! Buyer's contract number
				nodeout Score:StartDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ValidityDates
					eEcore:StartDate := tfConvertDateXML(eC507.2380, eC507.2379, build("RFF+BC_DTM+171(g1)(",eC507.2380,":",eC507.2379,")"))
				endnodeout
			case "IV" :! Invoicee
				nodeout Score:RefDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGListOfRelatedInvoiceRef, gGRelatedInvoiceRef, gGInvoiceNumber
					eEcore:RefDate := tfConvertDateXML(eC507.2380, eC507.2379, build("RFF+IV_DTM+171(g1)(",eC507.2380,":",eC507.2379,")"))
				endnodeout
			case "DL" :
				nodeout Score:RefDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGListOfRelatedInvoiceRef, gGRelatedInvoiceRef, gGInvoiceNumber
					eEcore:RefDate := tfConvertDateXML(eC507.2380 , eC507.2379, build("RFF+DL_DTM+171(g1)(",eC507.2380,":",eC507.2379,")"))
				endnodeout
			case "04G" :
				nodeout Score:RefDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
					eEcore:RefDate := tfConvertDateXML(eC507.2380 , eC507.2379, build("RFF+04G_DTM+171(g1)(",eC507.2380,":",eC507.2379,")"))
				endnodeout
		endswitch
	endif
	if (eC507.2380 <> EMPTY and (tRFF1153AAK <> EMPTY or tRFF1153PL <> EMPTY or tRFF1153ADE <> EMPTY)) then
		if (tRFF1153AAK <> EMPTY) then
			nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
				eEcore:ReferenceTypeCoded := "DespatchAdviceNumber"
			endnodeout
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
				eEcore:RefNum := tRFF1153AAK
			endnodeout
			tRFF1153AAK := EMPTY
		endif
		if (tRFF1153PL <> EMPTY) then
			nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
				eEcore:ReferenceTypeCoded := "PriceListNumber"
			endnodeout
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
				eEcore:RefNum := tRFF1153PL
			endnodeout
			tRFF1153PL := EMPTY
		endif
		if (tRFF1153ADE <> EMPTY) then
			nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
				eEcore:ReferenceTypeCoded := "AccountNumber"
			endnodeout
			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
				eEcore:RefNum := tRFF1153ADE
			endnodeout
			tRFF1153ADE := EMPTY
		endif
		nodeout Score:RefDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefDate := tfConvertDateXML(eC507.2380 , eC507.2379, build("DTM for RFF+AAK(",eC507.2380,":",eC507.2379,")"))
		endnodeout
	endif
endnodein

!
! Ce segment permet d'identifier les partenaires impliqués dans la facture. Les intervenants et les données qui leur sont associées dans la facture et l'avoir
! Nom et Addresse Seller
! Nom et Addresse HeadOffice Seller
! Nom et Addresse Representant fiscal
!
nodein NAD g2

	nLINE++
	nNAD++
	bfLOG("Converting segment", build("NAD g2 qualifiant:", e3035))
	tNAD3035 := e3035

	bfCompleteFTX()
	bfCompleteRFF()

	switch tNAD3035
		case "IV" : ! Invoicee
			nNbNADIV++
			if(nNbNADIV > 1) then
				bfERROR ("XCBL_ERROR_NAD_IV_MORE_THAN_ONE","More than one segment NAD+IV", "NAD+IV", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident :=" "
						bfERROR ("XCBL_ERROR_NAD_IV_IDENT_EMPTY","eC082.3039 in NAD+IV is empty", "NAD+IV", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout

				if (eC080.3036.1 = EMPTY) then
					bIVName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					bIVNameAddress := TRUE
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						bIVNameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						bIVNameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						bIVNameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						bIVNameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						bIVNameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
						bIVNameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bIVNameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bIVNameAddress) then
					if(bIVName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_IV_NAME_EMPTY","eC080.3036.1 in NAD+IV is empty", "NAD+IV", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "SE" : ! Seller
			nNbNADSE++
			if(nNbNADSE > 1) then
				bfERROR ("XCBL_ERROR_NAD_SE_MORE_THAN_ONE","More than one segment NAD+SE", "NAD+SE", nLINE)
			else
				bNADSE := TRUE
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident := " "
						bfERROR ("XCBL_ERROR_NAD_SE_IDENT_EMPTY","eC082.3039 in NAD+SE is empty", "NAD+SE", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout
				if (tFTXREGC10844402 <> EMPTY) then
					nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
						eEcore:AgencyCoded := "AssignedByNationalTradeAgency"
					endnodeout
					nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
						eEcore:CodeListIdentifierCoded := "BusinessLegalStructureType"
					endnodeout
					nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
						eEcore:Ident := tFTXREGC10844402
					endnodeout
				endif
				if (tFTXREGC10844403 <> EMPTY) then
					nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
						eEcore:AgencyCoded := "CEC"
					endnodeout
					nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
						eEcore:CodeListIdentifierCoded := "Other"
					endnodeout
					nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
						eEcore:CodeListIdentifierCodedOther := "LegalCapital"
					endnodeout
					nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
						eEcore:Ident := tFTXREGC10844403
					endnodeout
				endif

				if (eC080.3036.1 = EMPTY) then
					bSEName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
					eEcore:Name1 := eC080.3036.1
					endnodeout
				endif

				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
						bSENameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bSENameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bSENameAddress) then
					if(bSEName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_SE_NAME_EMPTY","eC080.3036.1 in NAD+SE is empty", "NAD+SE", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
				if (tFTXREGC10844401 <> EMPTY) then
					nodeout Score:RegisteredName gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation
						eEcore:RegisteredName := tFTXREGC10844401
					endnodeout
				endif
			endif
		case "BY" : ! Buyer
			nNbNADBY++
			if(nNbNADBY > 1) then
				bfERROR ("XCBL_ERROR_NAD_BY_MORE_THAN_ONE","More than one segment NAD+BY", "NAD+BY", nLINE)
			else
				bNADBY := TRUE
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident := " "
						bfERROR ("XCBL_ERROR_NAD_BY_IDENT_EMPTY","eC082.3039 in NAD+BY is empty", "NAD+BY", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout
				if (eC080.3036.1 = EMPTY) then
					bBYName1Empty := TRUE
					else
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
							eEcore:Name1 := eC080.3036.1
						endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
						bBYNameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bBYNameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bBYNameAddress) then
					if(bBYName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_BY_NAME_EMPTY","eC080.3036.1 in NAD+BY is empty", "NAD+BY", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "CO" : ! Corporate office
			nNbNADCO++
			if(nNbNADCO > 1) then
				bfERROR ("XCBL_ERROR_NAD_CO_MORE_THAN_ONE","More than one segment NAD+CO", "NAD+CO", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
					if(eC082.3039 = "") then
						eC082.3039 := "N/A"
					endif
					eEcore:Ident := eC082.3039
				endnodeout
				nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
					eEcore:PartyRoleCoded := "HeadOffice"
				endnodeout
				if (eC080.3036.1 = EMPTY) then
					bCOName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bCONameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bCONameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bCONameAddress) then
					if(bCOName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_CO_NAME_EMPTY","eC080.3036.1 in NAD+CO is empty", "NAD+CO", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "LC" : !  Tax Representant
			nNbNADLC++
			if(nNbNADLC > 1) then
				bfERROR ("XCBL_ERROR_NAD_LC_MORE_THAN_ONE","More than one segment NAD+LC", "NAD+LC", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
					if(eC082.3039 = "") then
						eC082.3039 := "N/A"
					endif
					eEcore:Ident := eC082.3039
				endnodeout
				nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
					eEcore:PartyRoleCoded := "DeclarantsAgentOrRepresentative"
				endnodeout
				if (eC080.3036.1 = EMPTY) then
					bLCName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bLCNameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bLCNameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bLCNameAddress) then
					if(bLCName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_LC_NAME_EMPTY","eC080.3036.1 in NAD+LC is empty", "NAD+LC", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "DP" : ! Delivery party
			nNbNADDP++
			if(nNbNADDP > 1) then
				bfERROR ("XCBL_ERROR_NAD_DP_MORE_THAN_ONE","More than one segment NAD+DP", "NAD+DP", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident := " "
						bfERROR ("XCBL_ERROR_NAD_DP_IDENT_EMPTY","eC082.3039 in NAD+DP is empty", "NAD+DP", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout
				if (eC080.3036.1 = EMPTY) then
					bDPName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
						bDPNameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bDPNameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bDPNameAddress) then
					if(bDPName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_DP_NAME_EMPTY","eC080.3036.1 in NAD+DP is empty", "NAD+DP", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "RE" : ! Party to receive commercial invoice remittance
			nNbNADRE++
			if(nNbNADRE > 1) then
				bfERROR ("XCBL_ERROR_NAD_RE_MORE_THAN_ONE","More than one segment NAD+RE", "NAD+RE", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident := " "
						bfERROR ("XCBL_ERROR_NAD_RE_IDENT_EMPTY","eC082.3039 in NAD+RE is empty", "NAD+RE", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout
				nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
					eEcore:PartyRoleCoded := "PartytoReceiveInvoiceFOrGoodsOrServices"
				endnodeout

				if (eC080.3036.1 = EMPTY) then
					bREName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bRENameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bRENameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bRENameAddress) then
					if(bREName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_RE_NAME_EMPTY","eC080.3036.1 in NAD+RE is empty","NAD+RE", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "DL" : ! Factor
			nNbNADDL++
			if(nNbNADDL > 1) then
				bfERROR ("XCBL_ERROR_NAD_DL_MORE_THAN_ONE","More than one segment NAD+DL", "NAD+DL", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident := " "
						bfERROR ("XCBL_ERROR_NAD_DL_IDENT_EMPTY","eC082.3039 in NAD+DL is empty", "NAD+DL", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout
				nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
					eEcore:PartyRoleCoded := "Factor"
				endnodeout
				if (eC080.3036.1 = EMPTY) then
						bDLName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bDLNameAddress := TRUE
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bDLNameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bDLNameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bDLNameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bDLNameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
						bDLNameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
							bDLNameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bDLNameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bDLNameAddress) then
					if(bDLName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_DL_NAME_EMPTY","eC080.3036.1 in NAD+DL is empty", "NAD+DL", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		case "SF" : ! ShipFromParty
			nNbNADSF++
			if(nNbNADSF > 1) then
				bfERROR ("XCBL_ERROR_NAD_SF_MORE_THAN_ONE","More than one segment NAD+SF", "NAD+SF", nLINE)
			else
				nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:PartyID
					if (eC082.3039 = EMPTY) then
						eEcore:Ident :=" "
						bfERROR ("XCBL_ERROR_NAD_SF_IDENT_EMPTY","eC082.3039 in NAD+SF is empty", "NAD+SF", nLINE)
					else
						eEcore:Ident := eC082.3039
					endif
				endnodeout

				if (eC080.3036.1 = EMPTY) then
					bSFName1Empty := TRUE
				else
					nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						eEcore:Name1 := eC080.3036.1
					endnodeout
				endif
				if (eC080.3036.2 <> EMPTY) then
					bSFNameAddress := TRUE
					nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						eEcore:Name2 := eC080.3036.2
					endnodeout
				endif
				if (build(eC080.3036.3,eC080.3036.4,eC080.3036.5) <> EMPTY) then
					nodeout Score:Name3 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						bSFNameAddress := TRUE
						eEcore:Name3 := build(eC080.3036.3,eC080.3036.4,eC080.3036.5)
					endnodeout
				endif
				if (eC059.3042.1 <> EMPTY) then
					nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						bSFNameAddress := TRUE
						eEcore:Street := eC059.3042.1
					endnodeout
				endif
				if (eC059.3042.2 <> EMPTY) then
					nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						bSFNameAddress := TRUE
						eEcore:StreetSupplement1 := eC059.3042.2
					endnodeout
				endif
				if (peel(build(eC059.3042.3, " ", eC059.3042.4), " ") <> EMPTY) then
					nodeout Score:StreetSupplement2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						bSFNameAddress := TRUE
						eEcore:StreetSupplement2 := peel(build(eC059.3042.3, " ", eC059.3042.4), " ")
					endnodeout
				endif
				if (e3164 <> EMPTY) then
					nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						bSFNameAddress := TRUE
						eEcore:City := e3164
					endnodeout
				endif
				if (e3251 <> EMPTY) then
					nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
						bSFNameAddress := TRUE
						eEcore:PostalCode := e3251
					endnodeout
				endif
				if (e3207 <> EMPTY) then
					bSFNameAddress := TRUE
					if(taCountry[e3207] = "OK") then
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := e3207
						endnodeout
					else
						nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCoded := "Other"
						endnodeout
						nodeout Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country
							eEcore:CountryCodedOther := e3207
						endnodeout
					endif
				endif
				if (bSFNameAddress) then
					if(bSFName1Empty) then
						nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:NameAddress
							bfERROR ("XCBL_ERROR_NAD_SF_NAME_EMPTY","eC080.3036.1 in NAD+SF is empty", "NAD+SF", nLINE)
							eEcore:Name1 := " "
						endnodeout
					endif
				endif
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning e3035=", e3035, " not managed"), "NAD g2", nLINE)
	   endswitch
endnodein

!
! Ce segment est utilisé pour indiquer les numéros de références du partenaire identifié dans le segment NAD précédent.
! Numero d'enregistrement
! Reference gouvernementale
!
nodein RFF g2,g3
	nLINE++
	switch (tNAD3035)
		case "SE" :! Seller
			if (nNbNADSE = 1) then
				switch eC506.1153
					case "XA" : ! Company/place registration number
						nRFFXASE++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_SE_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+SE is empty", "RFF+XA", nLINE)
						else
							if (nRFFXASE > 1) then
								bfERROR ("XCBL_ERROR_RFF_XA_SE_MORE_THAN_ONE","More than one RFF+XA for NAD+SE", "RFF+XA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "Other"
								endnodeout
								nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCodedOther := "RCS-RCM"
								endnodeout
								nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyDescription := "French Trade and Companies Register"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "GN" : ! Government reference number
						nRFFGNSE++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+SE is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNSE > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_SE_MORE_THAN_ONE","More than one RFF+GN for NAD+SE", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" : ! VAT registration number
						nRFFVASE++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+SE is empty", "RFF+VA", nLINE)
						else
							if (nRFFVASE > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_SE_MORE_THAN_ONE","More than one RFF+VA for NAD+SE", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "BY" :! Buyer
			if (nNbNADBY = 1) then
				switch eC506.1153
					case "XA" : ! Company/place registration number
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_BY_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+BY is empty", "RFF+XA", nLINE)
						else
							nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCoded := "Other"
							endnodeout
							nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCodedOther := "RCS-RCM"
							endnodeout
							nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyDescription := "French Trade and Companies Register"
							endnodeout
							nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier
								eEcore:Ident := eC506.1154
							endnodeout
						endif
					case "GN" : ! Government reference number
						nRFFGNBY++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_BY_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+BY is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNBY > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_BY_MORE_THAN_ONE","More than one RFF+GN for NAD+BY", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" : ! VAT registration number
						nRFFVABY++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+BY is empty", "RFF+VA", nLINE)
						else
							if (nRFFVABY > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_BY_MORE_THAN_ONE","More than one RFF+VA for NAD+BY", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "IV":! Invoicee
			if (nNbNADIV = 1) then
				switch eC506.1153
					case "XA" :! Company/place registration number
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_IV_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+IV is empty", "RFF+XA", nLINE)
						else
							nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCoded := "Other"
							endnodeout
							nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCodedOther := "RCS-RCM"
							endnodeout
							nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyDescription := "French Trade and Companies Register"
							endnodeout
							nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier
								eEcore:Ident := eC506.1154
							endnodeout
						endif
					case "GN" :! Government reference number
						nRFFGNIV++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+IV is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNIV > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_IV_MORE_THAN_ONE","More than one RFF+GN for NAD+IV", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" :! VAT registration number
						nRFFVAIV++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+IV is empty", "RFF+VA", nLINE)
						else
							if (nRFFVAIV > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_IV_MORE_THAN_ONE","More than one RFF+VA for NAD+IV", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "CO" : ! Corporate office
			if (nNbNADCO = 1) then
				switch eC506.1153
					case "XA" :! Company/place registration number
						nRFFXACO++
						tRFFXACO := eC506.1154
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_CO_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+CO is empty", "RFF+XA", nLINE)
						else
							if (nRFFXACO > 1) then
								bfERROR ("XCBL_ERROR_RFF_XA_CO_MORE_THAN_ONE","More than one RFF+XA for NAD+CO", "RFF+XA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "Other"
								endnodeout
								nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCodedOther := "RCS-RCM"
								endnodeout
								nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyDescription := "French Trade and Companies Register"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "GN" :! Government reference number
						nRFFGNCO++
						tRFFGNCO := eC506.1154
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+CO is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNCO > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_CO_MORE_THAN_ONE","More than one RFF+GN for NAD+CO", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" :! VAT registration number
						nRFFVACO++
						tRFFVACO := eC506.1154
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+CO is empty", "RFF+VA", nLINE)
						else
							if (nRFFVACO > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_CO_MORE_THAN_ONE","More than one RFF+VA for NAD+CO", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "LC" : !  Tax Representant
			if (nNbNADLC = 1) then
				switch eC506.1153
					case "XA" :! Company/place registration number
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_LC_IDENT_EMPTY","eC506.1154 in RFF+LC for NAD+LC is empty", "RFF+LC", nLINE)
						else
							nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCoded := "Other"
							endnodeout
							nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCodedOther := "RCS-RCM"
							endnodeout
							nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyDescription := "French Trade and Companies Register"
							endnodeout
							nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
								eEcore:Ident := eC506.1154
							endnodeout
						endif
					case "GN" :! Government reference number
						nRFFGNLC++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+LC is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNLC > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_LC_MORE_THAN_ONE","More than one RFF+GN for NAD+LC", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" :! VAT registration number
						nRFFVALC++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+LC is empty", "RFF+VA", nLINE)
						else
							if (nRFFVALC > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_LC_MORE_THAN_ONE","More than one RFF+VA for NAD+LC", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "DP" :! Delivery Party
			if (nNbNADDP = 1) then
				switch eC506.1153
					case "XA" : ! Company/place registration number
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_DP_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+DP is empty", "RFF+XA", nLINE)
						else
							nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCoded := "Other"
							endnodeout
							nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCodedOther := "RCS-RCM"
							endnodeout
							nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyDescription := "French Trade and Companies Register"
							endnodeout
							nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier
								eEcore:Ident := eC506.1154
							endnodeout
						endif
					case "GN" : ! Government reference number
						nRFFGNDP++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_DP_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+DP is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNDP > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_DP_MORE_THAN_ONE","More than one RFF+GN for NAD+DP", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" : ! VAT registration number
						nRFFVADP++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_DP_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+DP is empty", "RFF+VA", nLINE)
						else
							if (nRFFVADP > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_DP_MORE_THAN_ONE","More than one RFF+VA for NAD+DP", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "RE" : ! Party to receive commercial invoice remittance
			if (nNbNADRE = 1) then
				switch eC506.1153
					case "XA" :! Company/place registration number
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_RE_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+RE is empty", "RFF+XA", nLINE)
						else
							nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCoded := "Other"
							endnodeout
							nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCodedOther := "RCS-RCM"
							endnodeout
							nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyDescription := "French Trade and Companies Register"
							endnodeout
							nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
								eEcore:Ident := eC506.1154
							endnodeout
						endif
					case "GN" :! Government reference number
						nRFFGNRE++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_RE_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+RE is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNRE > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_RE_MORE_THAN_ONE","More than one RFF+GN for NAD+RE", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" :! VAT registration number
						nRFFVARE++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_RE_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+RE is empty", "RFF+VA", nLINE)
						else
							if (nRFFVARE > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_RE_MORE_THAN_ONE","More than one RFF+VA for NAD+RE", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		case "DL" : ! Factor
			if (nNbNADDL = 1) then
				switch eC506.1153
					case "XA" :! Company/place registration number
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_XA_DL_IDENT_EMPTY","eC506.1154 in RFF+XA for NAD+DL is empty", "RFF+XA", nLINE)
						else
							nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCoded := "Other"
							endnodeout
							nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyCodedOther := "RCS-RCM"
							endnodeout
							nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
								eEcore:AgencyDescription := "French Trade and Companies Register"
							endnodeout
							nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
								eEcore:Ident := eC506.1154
							endnodeout
						endif
					case "GN" :! Government reference number
						nRFFGNDL++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_GN_DL_IDENT_EMPTY","eC506.1154 in RFF+GN for NAD+DL is empty", "RFF+GN", nLINE)
						else
							if (nRFFGNDL > 1) then
								bfERROR ("XCBL_ERROR_RFF_GN_DL_MORE_THAN_ONE","More than one RFF+GN for NAD+DL", "RFF+GN", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:AgencyCoded := "FR-INSEE"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "Other"
								endnodeout
								nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
									eEcore:CodeListIdentifierCodedOther := "SIREN"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					case "VA" :! VAT registration number
						nRFFVADL++
						if (eC506.1154 = EMPTY) then
							bfERROR ("XCBL_ERROR_RFF_VA_DL_IDENT_EMPTY","eC506.1154 in RFF+VA for NAD+DL is empty", "RFF+VA", nLINE)
						else
							if (nRFFVADL > 1) then
								bfERROR ("XCBL_ERROR_RFF_VA_DL_MORE_THAN_ONE","More than one RFF+VA for NAD+DL", "RFF+VA", nLINE)
							else
								nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:AgencyCoded := "CEC"
								endnodeout
								nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
									eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
								endnodeout
								nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
									eEcore:Ident := eC506.1154
								endnodeout
							endif
						endif
					default:
						bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g2,g3", nLINE)
				endswitch
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning e3035=", tNAD3035, " not managed"), "NAD g2", nLINE)
	endswitch
endnodein

nodein CTA g2,g5
	nLINE++
	if (tNAD3035 = "SF") then
		if (e3139 = "DL") then
			nodeout Score:ContactName gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact
				eEcore:ContactName := eC056.3412
			endnodeout
			nodeout Score:ContactFunctionCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ContactFunction
				eEcore:ContactFunctionCoded := "DeliveryContact"
			endnodeout
		endif
	endif
endnodein

nodein COM g2,g5
	nLINE++
	if (tNAD3035 = "SF") then
		switch eC076.3155
			case "FX":
				nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberValue := eC076.3148
				endnodeout
				nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberTypeCoded := "FaxNumber"
				endnodeout
			case "XF":
				nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberValue := eC076.3148
				endnodeout
				nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberTypeCoded := "Other"
				endnodeout
				nodeout Score:ContactNumberTypeCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberTypeCodedOther := "X400"
				endnodeout
			case "TL":
				nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberValue := eC076.3148
				endnodeout
				nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberTypeCoded := "TelexNumber"
				endnodeout
			case "TE":
				nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberValue := eC076.3148
				endnodeout
				nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberTypeCoded := "TelephoneNumber"
				endnodeout
			case "EM":
				nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberValue := eC076.3148
				endnodeout
				nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipFromParty, gGcore:OtherContacts, gGcore:Contact, gGcore:ListOfContactNumber, gGcore:ContactNumber
					eEcore:ContactNumberTypeCoded := "EmailAddress"
				endnodeout
		endswitch
	endif
endnodein

!
! Mentions sur le regime legal du document
!
nodein TAX g6

	nLINE++
	bfLOG("Converting segment", build("TAX g6 qualifiant: ", e5283))

	switch(e5283)
		case "7": 	! Tax
			switch(eC241.5153)
				case "VAT" :	! Value added tax
					switch(e5305)
						case "E": ! Exempt from tax
							if(bFTXSIN = FALSE) then
								bfERROR("XCBL_ERROR_TAX_7_VAT_E_NO_FTXSIN", "TAX+7+VAT++++E without FTX+SIN","TAX(g6)",nLINE)
							endif
						case "AE":
							nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxTypeCoded := "ValueAddedTax"
							endnodeout
							nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
							endnodeout
							nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxCategoryCoded := "Other"
							endnodeout
							nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxCategoryCodedOther := "NotApplicable"
							endnodeout
							nodeout Score:TaxTreatmentCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxTreatmentCoded := "Other"
							endnodeout
							nodeout Score:TaxTreatmentCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxTreatmentCodedOther := "ReverseCharge"
							endnodeout
						default:
							nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxTypeCoded := "ValueAddedTax"
							endnodeout
							nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
							endnodeout
							nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxCategoryCoded := "Other"
							endnodeout
							nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxCategoryCodedOther := "NotApplicable"
							endnodeout
							nodeout Score:TaxTreatmentCoded gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxTreatmentCoded := "Other"
							endnodeout
							nodeout Score:TaxTreatmentCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
								eEcore:TaxTreatmentCodedOther := eC533.5289
							endnodeout
					endswitch
				default:
					nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGTaxReference
						eEcore:TaxTypeCoded := "ValueAddedTax"
					endnodeout
					nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGTaxReference
						eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
					endnodeout
					nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGTaxReference
						eEcore:TaxCategoryCoded := "Other"
					endnodeout
					nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
						eEcore:TaxCategoryCodedOther := "NotApplicable"
					endnodeout
					nodeout Score:TaxTreatmentCoded gGInvoice, gGInvoiceHeader, gGTaxReference
						eEcore:TaxTreatmentCoded := "Other"
					endnodeout
					nodeout Score:TaxTreatmentCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
						eEcore:TaxTreatmentCodedOther := eC533.5289
					endnodeout
			endswitch
		default:
			nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGTaxReference
				eEcore:TaxTypeCoded := "ValueAddedTax"
			endnodeout
			nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGTaxReference
				eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
			endnodeout
			nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGTaxReference
				eEcore:TaxCategoryCoded := "Other"
			endnodeout
			nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
            eEcore:TaxCategoryCodedOther := "NotApplicable"
			endnodeout
			nodeout Score:TaxTreatmentCoded gGInvoice, gGInvoiceHeader, gGTaxReference
				eEcore:TaxTreatmentCoded := "NetInvoiceLevel"
			endnodeout
	endswitch
endnodein

!
! Code de la devise de facturation
! Code de la devise de paiement de la TVA
!
nodein CUX g7
	nLINE++
	bfLOG("Converting segment", "CUX g7")
	nNbCUX++
	tReferenceCurrency := ""
	tTargetCurrency := ""
	if (nNbCUX > 1) then
		bfERROR("XCBL_ERROR_CUX_MORE_THAN_ONE", "More than one segment CUX (g7)","CUX(g7)",nLINE)
	else
		if ( eC504.1.6343 = "4") then
			if(eC504.1.6345 = EMPTY) then
				tInvoiceCurrency := "EUR"
			else
				tInvoiceCurrency := eC504.1.6345
			endif
			if (taDevises[tInvoiceCurrency] = "OK") then
				nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceCurrency
					eEcore:CurrencyCoded := tInvoiceCurrency
				endnodeout
			else
				nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceCurrency
					eEcore:CurrencyCoded := "Other"
				endnodeout
				nodeout Score:CurrencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceCurrency
					eEcore:CurrencyCodedOther := tInvoiceCurrency
				endnodeout
			endif
		endif
		if (eC504.2.6343 = "10E" and eC504.2.6345 <> EMPTY) then
			if(taDevises[eC504.2.6345] = "OK") then
				nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGTaxAccountingCurrency
					eEcore:CurrencyCoded := eC504.2.6345
				endnodeout
			else
				nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGTaxAccountingCurrency
					eEcore:CurrencyCoded := "Other"
				endnodeout
				nodeout Score:CurrencyCodedOther gGInvoice, gGInvoiceHeader, gGTaxAccountingCurrency
					eEcore:CurrencyCodedOther := eC504.2.6345
				endnodeout
			endif
		endif
	endif

	if (eC504.1.6347 = "2" and eC504.1.6343 = "4") then
		tReferenceCurrency := eC504.1.6345
	endif
	if (eC504.2.6347 = "3" and eC504.2.6343 = "10E") then
		tTargetCurrency := eC504.2.6345
	endif

endnodein

nodein DTM g7
	nLINE++
	bfLOG("Converting segment", "DTM g7")
	if (eC507.2005 = "134" and eC507.2380 <> EMPTY) then
		if (tReferenceCurrency <> EMPTY and tTargetCurrency <> EMPTY) then
			nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGListOfRateOfExchangeDetail, gGcore:RateOfExchangeDetail, gGcore:ReferenceCurrency
				eEcore:CurrencyCoded := tReferenceCurrency
			endnodeout
			nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGListOfRateOfExchangeDetail, gGcore:RateOfExchangeDetail, gGcore:TargetCurrency
				eEcore:CurrencyCoded := tTargetCurrency
			endnodeout
			nodeout Score:RateOfExchange gGInvoice, gGInvoiceHeader, gGListOfRateOfExchangeDetail, gGcore:RateOfExchangeDetail
				eEcore:RateOfExchange := "-0"
			endnodeout
			nodeout Score:IndicativeIndicator gGInvoice, gGInvoiceHeader, gGListOfRateOfExchangeDetail, gGcore:RateOfExchangeDetail
				eEcore:IndicativeIndicator := "0"
			endnodeout
			nodeout Score:DateOfRateOfExchange gGInvoice, gGInvoiceHeader, gGListOfRateOfExchangeDetail, gGcore:RateOfExchangeDetail
				eEcore:DateOfRateOfExchange := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+137 (g7)(",eC507.2380,":",eC507.2379,")"))
			endnodeout
		endif
	endif

endnodein

!
! Conditions d'escompte
! Ce segment est utilisé pour indiquer la date d'échéance de la facture, les conditions d'escomptes conditionnels et de pénalités applicables à la facture.
!
nodein PAT g8
	nLINE++
	bfLOG("Converting segment", "PAT g8")
	bPaymentTermCodedPenalty := FALSE
	bPaymentTermCodedDiscount := FALSE
	bPCDPaymentTermDesc := FALSE
	tPAT4279 := e4279
	switch(e4279)
		case "22": 	! Discount
			if (eC112.2475 = EMPTY) then
				bShouldMapDTM12 := TRUE
			else
				tC1122152 := eC112.2152
				if (eC112.2152 = EMPTY) then
					tC1122152 := "0"
				endif
				nodeout Score:PaymentTermCoded gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
					eEcore:PaymentTermCoded := "Discount"
				endnodeout
				bPaymentTermCodedDiscount := TRUE
				nodeout Score:PaymentTermValue gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
					eEcore:PaymentTermValue := tC1122152
				endnodeout
				switch(eC112.2475)
					case "5" : ! Date of invoice
						nodeout Score:PaymentTermCodedOther gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
							eEcore:PaymentTermCodedOther := "AfterInvoiceDate"
						endnodeout
						if(eC112.2009 = "3" and eC112.2151 = "D") then !  After reference (3)  Day (D)
							if number(eC112.2152) > 0 then
								nodeout Score:DiscountDueDate gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm, gGcore:DiscountInformation
									nDateEscompte := number(time(tDateFacture,"%a")) + (number(eC112.2152)*86400)+720 ! Calcul de la date facture + j
									tDateEscompte := build(time(build(nDateEscompte),"%Y%m%d"))
									eEcore:DiscountDueDate := tfConvertDateXML(tDateEscompte, "102", build("PAT+22(",tDateEscompte,":101)"))
								endnodeout
							endif
						endif
					case "67" :
						nodeout Score:PaymentTermCodedOther gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
							eEcore:PaymentTermCodedOther := "AfterExpectedDeliveryDate"
						endnodeout
					default:
						bfWARNING("Warn0001", "", build("Warning eC112.2475=", eC112.2475, " not managed"), "PAT+22 g8", nLINE)
				endswitch
			endif
		case "20":
			if (eC112.2475 <> EMPTY) then
				tC1122152 := eC112.2152
				if (eC112.2152 = EMPTY) then
					tC1122152 := "0"
				endif
				nodeout Score:PaymentTermCoded gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
					eEcore:PaymentTermCoded := "PenaltyTerms"
				endnodeout
				bPaymentTermCodedPenalty := TRUE
				nodeout Score:PaymentTermValue gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
					eEcore:PaymentTermValue := tC1122152
				endnodeout
				switch(eC112.2475)
					case "5" : ! Date of invoice
						nodeout Score:PaymentTermCodedOther gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
							eEcore:PaymentTermCodedOther := "AfterInvoiceDate"
						endnodeout
					case "67" :
						nodeout Score:PaymentTermCodedOther gGInvoice,gGInvoiceHeader,gGInvoicePaymentInstructions,gGcore:PaymentTerms,gGcore:PaymentTerm
							eEcore:PaymentTermCodedOther := "AfterExpectedDeliveryDate"
						endnodeout
					default:
						bfWARNING("Warn0001", "", build("Warning eC112.2475=", eC112.2475, " not managed"), "PAT+20 g8", nLINE)
				endswitch
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning e4279=", e4279, " not managed"), "PAT g8", nLINE)
	endswitch
endnodein

!
! Date d'echeance de reglement
! Ce segment permet de préciser la date d'échéance de la facture (obligatoire). Il permet également d¿indiquer, le cas échéant, la date limite d'application de l'escompte.
!
nodein DTM g8
	bfLOG("Converting segment", build("DTM g8 qualifiant: ", eC507.2005))
	nLINE++
	switch(eC507.2005)
		case "13" : ! Terms net due date
			nDTM13++
			if(nDTM13 = 1) then
				nodeout SInvoiceDueDate gGInvoice, gGInvoiceHeader, gGInvoiceDates
					bInvoiceDueDate := TRUE
					tInvoiceDueDate := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+13(g8)(",eC507.2380,":",eC507.2379,")"))
					eEInvoiceDueDate := tInvoiceDueDate
				endnodeout
			endif
		case "12" :
			if (bShouldMapDTM12) then
				tDTM12 :=  tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+12(g8)(",eC507.2380,":",eC507.2379,")"))
				bShouldMapDTM12 := FALSE
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC507.2005=", eC507.2005, " not managed"), "DTM g8", nLINE)
	endswitch
endnodein

!
!
! Ce segment est utilisé pour spécifier les pourcentages qui seront déduits ou ajoutés au montant de la facture si celle-ci est payée avant ou après l'échéance.
! Une seule occurrence de ce segment sera utilisée par segment PAT.
!
nodein PCD g8
	nLINE++
	bfLOG("Converting segment", build("PCD g8 qualifiant ", eC501.5245))
	tSeg5015249 := "KO"
	if (eC501.5249 = "13") then
		tSeg5015249 := "OK"
	endif
	if (eC501.5249 = EMPTY) then
		tSeg5015249 := "OK"
	endif
	switch(eC501.5245)
		case "12" : ! ! Discount percentage
			if(tSeg5015249 = "OK") then ! Invoice Value
				if(tPAT4279 = "22") then ! Discount
					if(not bPaymentTermCodedDiscount) then
						nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
							eEcore:PaymentTermCoded := "Discount"
						endnodeout
						bPaymentTermCodedDiscount := TRUE
					endif
					nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
						eEcore:PaymentTermDescription := eC501.5482
						bPCDPaymentTermDesc := TRUE
					endnodeout

					if (tDTM12 <> EMPTY) then
						nodeout Score:DiscountDueDate gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm, gGcore:DiscountInformation
							eEcore:DiscountDueDate := tDTM12
						endnodeout
					endif
				endif
			endif
		case "15" :   ! Penalty percentage
			if(tSeg5015249 = "OK") then ! Invoice Value
				if(tPAT4279 = "20") then ! Penality terms
					if (not bPaymentTermCodedPenalty) then
						nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
							eEcore:PaymentTermCoded := "PenaltyTerms"
						endnodeout
						bPaymentTermCodedPenalty := TRUE
					endif
					nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
						eEcore:PaymentTermDescription := eC501.5482
						bPCDPaymentTermDesc := TRUE
					endnodeout
				endif

			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC501.5245=", eC501.5245, " not managed"), "PCD g8", nLINE)
	endswitch
endnodein

!
!
! Ce segment permet de spécifier les montants monétaires de la facture qui seront déduits si le "facturé" paie avant l'échéance. Une seule occurrence de ce segment sera utilisée par segment PAT.
!
nodein MOA g8
	nLINE++
	bfLOG("Converting segment", "MOA g8")

	switch(eC516.5025)
		case "21" : ! Cash Discount
			if(tPAT4279 = "22") then ! Discount
				if (not bPCDPaymentTermDesc) then
					nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
						eEcore:PaymentTermDescription := "Amount"
					endnodeout
				endif
				if(not bPaymentTermCodedDiscount) then
					nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
						eEcore:PaymentTermCoded := "Discount"
					endnodeout
					bPaymentTermCodedDiscount := TRUE
				endif
				if(eC516.5004 <> EMPTY) then
					nodeout Score:MonetaryAmount gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm, gGcore:DiscountInformation, gGcore:DiscountAmount
						eEcore:MonetaryAmount := eC516.5004
					endnodeout
				endif
		      endif
		case "01G" :
			if (tPAT4279 = "22") then
				nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
					eEcore:PaymentTermCoded := "Discount"
				endnodeout
				nodeout Score:PaymentTermCodedOther gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
					eEcore:PaymentTermCodedOther := build("MonetaryAmountWithoutRegulatoryTaxesAsBasis",":",eC516.5004)
				endnodeout
			endif
		case "02G" :
			if (tPAT4279 = "22") then
				nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
					eEcore:PaymentTermCoded := "Discount"
				endnodeout
				nodeout Score:PaymentTermCodedOther gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
					eEcore:PaymentTermCodedOther := build("MonetaryAmountWithRegulatoryTaxesAsBasis",":",eC516.5004)
				endnodeout
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC516.5025=", eC516.5025, " not managed"), "MOA g8", nLINE)
		endswitch
endnodein

nodein TOD g12
	nLINE++
	if (e4055 <> EMPTY) then
		nodeout Score:TermsOfDeliveryFunctionCoded gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery
			switch (e4055)
				case "6" :
					eEcore:TermsOfDeliveryFunctionCoded  := "DeliveryCondition"
				case "3" :
					eEcore:TermsOfDeliveryFunctionCoded  := "PriceandDespatchCondition"
				case "5" :
					eEcore:TermsOfDeliveryFunctionCoded  := "TransportCondition"
				default :
					bTOD4055Other := TRUE
					eEcore:TermsOfDeliveryFunctionCoded  := "Other"
			endswitch
		endnodeout
		if(bTOD4055Other) then
			nodeout Score:TermsOfDeliveryFunctionCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery
				eEcore:TermsOfDeliveryFunctionCodedOther  := "Unknown"
			endnodeout
			bTOD4055Other := FALSE
		endif
		nodeout Score:TransportTermsCoded gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery
			switch ( eC100.4053)
				case "CPT" :
					eEcore:TransportTermsCoded  := "CarriagePaidTo"
				case "CIP" :
					eEcore:TransportTermsCoded  := "Carriage-InsurancePaidTo"
				case "DDP" :
					eEcore:TransportTermsCoded  := "DeliveredDutyPaid"
				case "EXW" :
					eEcore:TransportTermsCoded  := "Ex-Works"
				case "DAP" :
					bTOD1004053DAP := TRUE
					eEcore:TransportTermsCoded  := "Other"
				default :
					bTOD1004053Other := TRUE
					eEcore:TransportTermsCoded  := "Other"
			endswitch
		endnodeout
		if (bTOD1004053DAP)then
			nodeout Score:TransportTermsCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery
				eEcore:TransportTermsCodedOther  := "DeliveredAtPlace"
			endnodeout
			bTOD1004053DAP := FALSE
		endif
		if (bTOD1004053Other) then
			nodeout Score:TransportTermsCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery
				eEcore:TransportTermsCodedOther  := "Unknown"
			endnodeout
			bTOD1004053Other :=FALSE
		endif
		nodeout Score:ShipmentMethodOfPaymentCoded gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery
			switch ( e4215)
				case "CC" :
					eEcore:ShipmentMethodOfPaymentCoded  := "Collect"
				case "PP" :
					eEcore:ShipmentMethodOfPaymentCoded  := "Prepaid-BySeller"
				case "PU" :
					eEcore:ShipmentMethodOfPaymentCoded  := "Pickup"
				default :
					eEcore:ShipmentMethodOfPaymentCoded  := "NotSpecified"
			endswitch
		endnodeout
	else
		bfERROR("XCBL_ERROR_TOD_4055", build("Error e4055 empty"), "TOD g12", nLINE)
	endif
endnodein

nodein LOC g12
	nLINE++
	if (eC517.3225 <> EMPTY) then
		nodeout Score:LocationQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location
			eEcore:LocationQualifierCoded := "PlaceOfDelivery"
		endnodeout
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:LocationIdentifier, gGcore:LocID
			eEcore:Ident := eC517.3225
		endnodeout
	endif
endnodein

!
! Remise et charge globale
! Ce segment est utilisé pour indiquer les réductions ou charges portant sur toutes les lignes de la facture, par exemple, les frais de transport.
! Il est également utilisé pour transmettre des informations sur les taxes ou assimilées
!
nodein ALC g15

	nLINE++
	bfResetALCg15Value()
	bfLOG("Converting segment", build("ALC g15 qualif ", eC214.7161, " description ", build(eC214.7160.1,eC214.7160.2)))
	switch (e5463)
		case "A" :	! Allowance
			if(e4471 = "2") then
				tALCHeaderTypeReductionCharge := "Allowance"
				tALCHeaderMethodOfHandling := "AllowanceToBeIssuedByVendor"
			else
				if(e4471 = "1") then
					tALCHeaderTypeReductionCharge := "Allowance"
					tALCHeaderMethodOfHandling := "OffInvoice"
				else
					bfERROR("XCBL_ERROR_ALC_A_REGLMT_CODE", build("Error e4471=", e4471, " should be 1 or 2"), "ALC+A g15", nLINE)
				endif
			endif
		case "C" :	! Charge
			if(e4471 = "2") then
				tALCHeaderTypeReductionCharge := "Charge"
				tALCHeaderMethodOfHandling := "ChargeToBePaidByCustomer"
			else
				if(e4471 = "1") then
					tALCHeaderTypeReductionCharge := "Charge"
					tALCHeaderMethodOfHandling := "OffInvoice"
				else
					bfERROR("XCBL_ERROR_ALC_C_REGLMT_CODE", build("Error e4471=", e4471, " should be 1 or 2"), "ALC+C g15", nLINE)
				endif
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning e5463=", e5463, " not managed"), "ALC g15", nLINE)
	endswitch

	switch (eC214.7161) ! Type of Allowance or Charge
		case "AA" :
			tALCHeaderService := "AdvertisingAllowance"
			tALCHeaderDescriptionSup := "Remise pour publicité"
		case "AAB" : ! Returned goods charges
			tALCHeaderService := "ReturnedLoad"
			tALCHeaderDescriptionSup := "Frais de marchandises retournées"
		case "ABL" : ! Packaging surcharge
			tALCHeaderService := "Packaging"
			tALCHeaderDescriptionSup := "Supplément emballage"
		case "ADR" : ! Other services
			tALCHeaderDescriptionSup := "Autres services"
			if(e5463 = "A") then
				tALCHeaderService := "GoodsAndServicesCreditAllowance"
			else
				tALCHeaderService := "GoodsAndServicesCharge"
			endif
		case "ADS" :
			tALCHeaderService := "ContainerAllowance"
			tALCHeaderDescriptionSup := "Commande d'une palette complète"
		case "ADT" :
			tALCHeaderService := "PickUp"
			tALCHeaderDescriptionSup := "Enlèvement"
		case "ASS" :
			tALCHeaderService := "GroupageDiscount"
			tALCHeaderDescriptionSup := "Remise d'assortiment"
		case "CAC" :
			tALCHeaderService := "ServiceChargeWithCashDiscount"
			tALCHeaderDescriptionSup := "Remise pour paiement comptant"
		case "FC" : ! Freight charge
			tALCHeaderService := "Freight"
			tALCHeaderDescriptionSup := "Frais de transport"
		case "FI" : ! Finance charge
			tALCHeaderService := "FinanceCharge"
			tALCHeaderDescriptionSup := "Frais financiers"
		case "LA" : ! Labeling
			tALCHeaderService := "Labeling"
			tALCHeaderDescriptionSup := "Etiquetage"
		case "PAD" :
			tALCHeaderService := "PromotionalAllowance"
			tALCHeaderDescriptionSup := "Remise promotionnelle"
		case "QD" :
			tALCHeaderService := "QuantityDiscount"
			tALCHeaderDescriptionSup := "Remise quantitative"
		case "RAA" :
			tALCHeaderService := "Rebate"
			tALCHeaderDescriptionSup := "Ristourne"
		case "TAE" :
			tALCHeaderService := "FullTruckloadAllowance"
			tALCHeaderDescriptionSup := "Remise pour camion complet"
		case "TD" :
			tALCHeaderService := "TradeDiscount"
			tALCHeaderDescriptionSup := "Remise commerciale"
		case "TX" : ! Tax
			tALCHeaderDescriptionSup := "Taxe"
			if (tALCHeaderTypeReductionCharge = "Charge") then
				tALCHeaderTypeReductionCharge := "Service"
				tALCHeaderService := "Tax-RegulatoryTax"
			else
				tALCHeaderService := "Tax"
			endif
		case "X14" :
			tALCHeaderService := "DisplayAllowance"
			tALCHeaderDescriptionSup := "Emplacement préférentiel pour la vente"
		case "01G" :
			tALCHeaderService := "GroupageDiscount"
			tALCHeaderDescriptionSup := "Remise baisse durable des prix à la consommation"
		default:
			tALCHeaderService := "Other"
	endswitch

	tALCHeaderCalculationIndex := e1227
	if(tALCHeaderDescriptionSup <> EMPTY) then
		if (build(eC214.7160.1,eC214.7160.2) <> EMPTY) then
			tALCHeaderDescription      := build(eC214.7160.1,eC214.7160.2," (",tALCHeaderDescriptionSup,")")
		endif
	else
		tALCHeaderDescription      := build(eC214.7160.1,eC214.7160.2)
	endif
    tALCHeaderRefID            := eC552.1230
	if (tALCHeaderDescription = EMPTY) then
		bfERROR("XCBL_ERROR_ALC_DESC_MISSING", "ALC description missing (eC214.7160.1,eC214.7160.2).","ALC(g15)",nLINE)
		tALCHeaderDescription := " "
	endif

endnodein

!
! Pourcentage de la remise ou charge globale pourcentage
! Ce segment est utilisé pour spécifier les pourcentages de réduction ou charge pour le groupe ALC.
!
nodein PCD g15,g18

	nLINE++
	bfLOG("Converting segment", "PCD g15,g18")
	nPCDg15++
	if (nPCDg15 > 1) then
		bfERROR("XCBL_ERROR_PCD_MORE_THAN_ONE", "More than one PCD g15 found after ALC segment","PCD(g15,g18)",nLINE)
	else
		if (eC501.5245 <> "1" and eC501.5245 <> "2" ) then
			bfERROR("XCBL_ERROR_PCD_QUALIFIER_NOT_EXPECTED", "PCD qualifier is not expected after ALC segment. Should be 1 or 2.","PCD(g15,g18)",nLINE)
		else
			if (tALCHeaderTypeReductionCharge = "Allowance" and eC501.5245 = "2") then
				bfERROR ("XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_A","PCD+2 can not follow ALC+A","PCD(g15,g18)", nLINE)
			else

				if((tALCHeaderTypeReductionCharge = "Charge" or tALCHeaderTypeReductionCharge = "Service") and eC501.5245 = "1") then
					bfERROR ("XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_C","PCD+1 can not follow ALC+C","PCD(g15,g18)", nLINE)
				else
					if (tALCHeaderTypeReductionCharge <> EMPTY) then
						bPCDWithALCg15 := TRUE

						nodeout Score:IndicatorCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge
								eEcore:IndicatorCoded  := tALCHeaderTypeReductionCharge
						endnodeout

                        if(tALCHeaderRefID <> EMPTY) then
                            nodeout Score:RefID gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
                                eEcore:RefID := tALCHeaderRefID
                            endnodeout
                        endif

						nodeout Score:ListOfDescription gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
							eEcore:ListOfDescription := tALCHeaderDescription
						endnodeout

						nodeout Score:ServiceCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
							eEcore:ServiceCoded := tALCHeaderService
						endnodeout

						if (tALCHeaderCalculationIndex <> EMPTY) then
							nodeout Score:AllowOrChargeTreatmentCoded gGInvoice, gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge,gGcore:AllowOrChargeTreatment
								eEcore:AllowOrChargeTreatmentCoded := "Other"
							endnodeout

							nodeout Score:AllowOrChargeTreatmentCodedOther gGInvoice, gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge,gGcore:AllowOrChargeTreatment
								eEcore:AllowOrChargeTreatmentCodedOther := taCalculationSequence[tALCHeaderCalculationIndex]
							endnodeout
						endif

						nodeout Score:MethodOfHandlingCoded gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
							eEcore:MethodOfHandlingCoded := tALCHeaderMethodOfHandling
						endnodeout

						nodeout Score:Percent gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge
							if (eC501.5482 = EMPTY) then
								bfERROR ("XCBL_ERROR_PCD_g15_EMPTY","eC501.5482 in PCD g15 seg is empty","PCD(g15,g18)", nLINE)
								eEcore:Percent := "-0"
							else
								eEcore:Percent := eC501.5482
							endif
						endnodeout

						nodeout Score:PercentQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentQualifier
							eEcore:PercentQualifierCoded := "Other"
						endnodeout
						nodeout Score:PercentQualifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentQualifier
							eEcore:PercentQualifierCodedOther := "HeaderAllowanceorCharge"
						endnodeout

					endif
				endif
			endif
		endif
	endif

endnodein

!
! Montant de la remise ou charge globale
! Ce segment est utilisé pour spécifier les montants monétaires des réductions ou des charges et les bases de calcul pour le groupe ALC.
!
nodein MOA g15,g19
	nLINE++
	bfLOG("Converting segment", "MOA g15,g19")

	if(eC516.5025 = "8") then
		nMOAg15++
	endif

	if (nMOAg15 > 1) then
		bfERROR("XCBL_ERROR_MOA_MORE_THAN_ONE", "More than one MOA g15 found after ALC segment(Qualifier : 8)","MOA(g15,g19)",nLINE)
	else
		if (eC516.5025 = "8") then
			bMOAWithALCg15 := TRUE
			if (tALCHeaderTypeReductionCharge <> EMPTY) then
				if (bPCDWithALCg15) then

					nodeout Score:MonetaryAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentageMonetaryValue
						if (eC516.5004 = EMPTY) then
							eEcore:MonetaryAmount := "-0"
							bfERROR ("XCBL_ERROR_MOA_g15_EMPTY","eC516.5004 in MOA g15 seg is empty","MOA(g15,g19)", nLINE)
						else
							eEcore:MonetaryAmount := eC516.5004
						endif
					endnodeout

				else
					nodeout Score:IndicatorCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge
						eEcore:IndicatorCoded  := tALCHeaderTypeReductionCharge
					endnodeout

                    if(tALCHeaderRefID <> EMPTY) then
                        nodeout Score:RefID gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
                            eEcore:RefID := tALCHeaderRefID
                        endnodeout
                    endif

					nodeout Score:ListOfDescription gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
						eEcore:ListOfDescription := tALCHeaderDescription
					endnodeout

					nodeout Score:ServiceCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
							eEcore:ServiceCoded := tALCHeaderService
					endnodeout

					nodeout Score:AllowOrChargeTreatmentCodedOther gGInvoice, gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge,gGcore:AllowOrChargeTreatment
						eEcore:AllowOrChargeTreatmentCodedOther := taCalculationSequence[tALCHeaderCalculationIndex]
					endnodeout

					nodeout Score:AllowOrChargeTreatmentCoded gGInvoice, gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge,gGcore:AllowOrChargeTreatment
						eEcore:AllowOrChargeTreatmentCoded := "Other"
					endnodeout

					nodeout Score:MethodOfHandlingCoded gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
						eEcore:MethodOfHandlingCoded := tALCHeaderMethodOfHandling
					endnodeout

					nodeout Score:MonetaryAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:MonetaryValue
						if (eC516.5004 = EMPTY) then
							bfERROR ("XCBL_ERROR_MOA_g15_EMPTY","eC516.5004 in MOA g15 seg is empty","MOA(g15,g19)", nLINE)
							eEcore:MonetaryAmount := "-0"
						else
							eEcore:MonetaryAmount  := eC516.5004
						endif
					endnodeout
				endif
			endif
		endif
	endif

	if (eC516.5025 = "01G" or eC516.5025 = "02G") then
		nodeout Score:BasisCoded gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
			eEcore:BasisCoded := "Other"
		endnodeout
		if(eC516.5025 = "02G") then
			nodeout Score:BasisCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
				eEcore:BasisCodedOther := "MonetaryAmountWithRegulatoryTaxes"
			endnodeout
		else
			nodeout Score:BasisCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
				eEcore:BasisCodedOther := "MonetaryAmountWithoutRegulatoryTaxes"
			endnodeout
		endif
		nodeout Score:MonetaryLimitValue gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge, gGcore:BasisMonetaryRange, gGcore:MonetaryLimit
			eEcore:MonetaryLimitValue := eC516.5004
		endnodeout
	endif

	if (eC516.5025 = "25") then
		nodeout Score:BasisCoded gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
			eEcore:BasisCoded := "MonetaryAmount"
		endnodeout
		nodeout Score:MonetaryLimitValue gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge, gGcore:BasisMonetaryRange, gGcore:MonetaryLimit
			eEcore:MonetaryLimitValue := eC516.5004
		endnodeout
	endif
endnodein

!
! Taux de TVA appliqué à la charge ou la remise globale
! Le segment TAX indique si les réductions et charges spécifiées dans le groupe 15 sont soumises à taxe ou à droit.
!
nodein TAX g15,g21
	nLINE++
	bfLOG("Converting segment", "TAX g15,g21")
	switch(eC241.5153)
		case "VAT":  ! Value Added Tax
			if (tALCHeaderTypeReductionCharge <> EMPTY) then
				nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxTypeCoded := "ValueAddedTax"
				endnodeout

				nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxFunctionQualifierCoded := "Tax"
				endnodeout

				!Catégorie de la TVA appliquée
				nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxCategoryCoded := "Other"
				endnodeout
				nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxCategoryCodedOther := eC243.5278
				endnodeout

				!Taux de TVA appliqué
				nodeout Score:TaxPercent gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxPercent := eC243.5278
				endnodeout

				!Montant de taxe pour respecter la norme XCBL
				nodeout Score:TaxAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxAmount := "0"
				endnodeout

				nodeout Score:TaxableAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxableAmount := e5286
				endnodeout
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC241.5153=", eC241.5153, " not managed"), "TAX g15,g21", nLINE)
	endswitch
endnodein

!
! Numero de ligne
! Numero de produit
! Le segment LIN débute la section de détail du message Facture. Celle-ci est formée d'un groupe de segments répétitif, qui commence toujours par le segment LIN.
!
nodein LIN g25
	nLINE++

	bfForceCompleteLine()
	bMOA204WithALC := FALSE
	bNewLine := TRUE
	bLIN := TRUE
	bfCheckSegmentNadBy()
	bfCheckSegmentNadSe()
	bfCheckSegmentDtm()
	bfCheckSegmentBgm()
	bfCheckSegmentAlcg15()
	bfCheckSegmentAlcg25()
	tItemDescription := ""

	bfLOG("Converting segment", "LIN g25")
	if(eC212.7140 = "") then
		eC212.7140 := "N/A"
	endif
	nodeout Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
		eEcore:ProductIdentifier := eC212.7140
	endnodeout
	nodeout Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
		eEcore:ProductIdentifierQualifierCoded := "GTIN"
	endnodeout
	nodeout Score:SellerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum
		eEcore:SellerLineItemNum := e1082
	endnodeout
	nodeout Score:BuyerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum
		eEcore:BuyerLineItemNum := e1082
	endnodeout
	!AIO-6560
	if(eC829.1082 <> EMPTY) then
		nodeout SParentItemNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail
			eEParentItemNumber := eC829.1082
			eALineItemNumTypeCoded := "Seller"
		endnodeout
	endif
	if(e7083 <> EMPTY) then
		nodeout Score:GeneralNote gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfStructuredNote, gGcore:StructuredNote
			eEcore:GeneralNote := e7083
		endnodeout
		nodeout Score:NoteID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfStructuredNote, gGcore:StructuredNote
			eEcore:NoteID := "7083"
		endnodeout
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfStructuredNote, gGcore:StructuredNote, gGcore:Agency
			eEcore:AgencyCoded := "EAN"
		endnodeout
	endif
endnodein

!
! Code produit acheteur
! Code produit vendeur
! Code produit promotionnel
! Ce segment est utilisé pour - transmettre, dans des cas exceptionnels, le code de l¿article facturé si aucun code n'a été transmis dans le segment LIN (DE 7140) avec le code 5 en DE 4347 ;
!   - indiquer une identification supplémentaire au GTIN porté dans le LIN avec le code 1 en DE 4347, par exemple une variante promotionnelle
!   - ou indiquer une identification de substitution de l'article telle que le numéro d'article de l'acheteur ou du vendeur avec le code 1 en DE 4347.
!
nodein PIA g25
	nLINE++
	bfLOG("Converting segment", "PIA g25")
	switch(e4347)
		case "5" : ! Product identification
			switch eC212.1.7143
				case "IN" : ! Buyer's item number
					nodeout Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:BuyerPartNumber
						eEcore:PartID := eC212.1.7140
					endnodeout
				case "SA" : ! Supplier's article number
					nodeout Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:SellerPartNumber
						eEcore:PartID := eC212.1.7140
					endnodeout
				case "PV" : ! Promotional variant number
					nodeout Score:ProductIdentifierExt gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
						eEcore:ProductIdentifierExt := eC212.1.7140
					endnodeout
				default:
					bfWARNING("Warn0001", "", build("Warning e4347=", e4347, " not managed"), "PIA g25", nLINE)
			endswitch
		case "1" :
			switch eC212.1.7143
				case "SN" :
					nodeout Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail,\
					gGItemIdentifiers, gGcore:PartNumbers, gGcore:OtherItemIdentifiers, gGcore:ProductIdentifierCoded
						eEcore:ProductIdentifierQualifierCoded := "SerialNumber"
					endnodeout
					nodeout Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail,\
					gGItemIdentifiers, gGcore:PartNumbers, gGcore:OtherItemIdentifiers, gGcore:ProductIdentifierCoded
						eEcore:ProductIdentifier := eC212.1.7140
					endnodeout
				case "PV" :
					nodeout Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail,\
					gGItemIdentifiers, gGcore:PartNumbers, gGcore:OtherItemIdentifiers, gGcore:ProductIdentifierCoded
						eEcore:ProductIdentifierQualifierCoded := "PromotionalVariantNumber"
					endnodeout
					nodeout Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, \
					gGItemIdentifiers, gGcore:PartNumbers, gGcore:OtherItemIdentifiers, gGcore:ProductIdentifierCoded
						eEcore:ProductIdentifier := eC212.1.7140
					endnodeout
			endswitch
	endswitch
endnodein

!
! Libéllé du bien ou du service
! Ce segment est utilisé pour décrire la ligne article. La description sous forme de libellé de l'objet facturé est légalement obligatoire.
!
nodein IMD g25

	nLINE++
	bfLOG("Converting segment", build("IMD g25 qualifiant: ", e7077))
	switch(e7077)
		case "E":
			tItemDescription := peel(build(tItemDescription, " ", eC273.7008.1, eC273.7008.2), " ")
		!AIO-6560
		case "C":
			if(e7081 = "TPE") then
				if(eC273.7009 = "03" or eC273.7009 = "12" or eC273.7009 = "SG" or eC273.7009 = "DU" or eC273.7009 = "CSG") then
					bLineItemTypeCoded := TRUE
					nodeout Score:LineItemTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCoded := "ComponentGroup"
					endnodeout
					switch (eC273.7009)
						case "03":
							nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
								eEcore:LineItemTypeCodedOther := "BoxOfAssortedConsumerUnits"
							endnodeout
						case "12":
							nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
								eEcore:LineItemTypeCodedOther := "ComplexStandardDespatchUnit"
							endnodeout
						case "SG":
							nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
								eEcore:LineItemTypeCodedOther := "StandardGroupOfProducts"
							endnodeout
						case "DU":
							nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
								eEcore:LineItemTypeCodedOther := "DespatchUnit"
							endnodeout
						case "CSG":
							nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
								eEcore:LineItemTypeCodedOther := "FullDisplayStand"
							endnodeout
					endswitch
				endif
				if(eC273.7009 = "VQ") then
					bLineItemTypeCoded := TRUE
					nodeout Score:LineItemTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCoded := "Other"
					endnodeout
					nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCodedOther := "VariableQuantityProduct"
					endnodeout
				endif
				if (eC273.7009 = "CTO") then
					nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCodedOther := "CutToOrder"
					endnodeout
				endif
				if (eC273.7009 = "CU") then
					nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCodedOther := "ConsumerUnit"
					endnodeout
				endif
				if (eC273.7009 = "RC") then
					nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCodedOther := "ReturnableContainer"
					endnodeout
				endif
				if (eC273.7009 = "SER") then
					nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCodedOther := "Service"
					endnodeout
				endif
				if (eC273.7009 = "11") then
					nodeout Score:LineItemTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
						eEcore:LineItemTypeCodedOther := "Bulk"
					endnodeout
				endif
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning e7077=", e7077, " not managed"), "IMD g25", nLINE)
	endswitch
endnodein

nodein MEA g25
	nLINE++
	bfLOG("Converting segment", build("MEA g25"))

	nodeout Score:MeasurementValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfDimension, gGcore:Dimension, gGcore:Measurement
		eEcore:MeasurementValue := eC174.6314
	endnodeout
	nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfDimension, gGcore:Dimension, gGcore:Measurement, gGcore:UnitOfMeasurement
		switch eC174.6411
			case "LTR" :
				eEcore:UOMCoded := "LTR"
			case "MTQ":
				eEcore:UOMCoded := "MTQ"
			case "KGM" :
				eEcore:UOMCoded := "KGM"
			case "MTR":
				eEcore:UOMCoded := "MTR"
			case "PCE" :
				eEcore:UOMCoded := "EA"
			case "TNE":
				eEcore:UOMCoded := "TNE"
			default:
				bMEA1746411Other :=TRUE
				eEcore:UOMCoded := "Other"
		endswitch
	endnodeout
	if(bMEA1746411Other) then
		nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfDimension, gGcore:Dimension, gGcore:Measurement, gGcore:UnitOfMeasurement
			eEcore:UOMCodedOther := "Unknown"
		endnodeout
		bMEA1746411Other := FALSE
	endif
	nodeout Score:DimensionCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfDimension, gGcore:Dimension
		switch eC502.6313
			case "AAC" :
				eEcore:DimensionCoded := "TotalNetWeight"
			case "AAD":
				eEcore:DimensionCoded := "TotalGrossWeight"
			case "AAW" :
				eEcore:DimensionCoded := "GrossVolume"
			case "ACV":
				eEcore:DimensionCoded := "LoadingMeters"
			case "NPP" :
				bMEA5026313NPP := TRUE
				eEcore:DimensionCoded := "Other"
			default:
				bMEA5026313Other :=TRUE
				eEcore:DimensionCoded := "Other"
		endswitch
	endnodeout
	if(bMEA5026313NPP) then
		nodeout Score:DimensionCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfDimension, gGcore:Dimension
			eEcore:DimensionCodedOther := "NumberOfPalletPlaces"
		endnodeout
		bMEA5026313NPP := FALSE
	endif
	if(bMEA5026313Other) then
		nodeout Score:DimensionCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfDimension, gGcore:Dimension
			eEcore:DimensionCodedOther := "Unknown"
		endnodeout
		bMEA5026313Other := FALSE
	endif
endnodein

!
! Quantité facturé
! Ce segment est utilisé pour spécifier la quantité totale facturée pour la ligne. La quantité facturée est légalement obligatoire.
!
nodein QTY g25
	nLINE++
	bfLOG("Converting segment", build("QTY g25 qualifiant: ", eC186.6063))

	switch eC186.6063
		case "47" : ! Invoiced quantity
			if (bInvoicedQuantity) then
				bfERROR ("XCBL_ERROR_QTY47_g25_MORE_THAN_ONE","More than one segment QTY+47 (g25)","QTY+47(g25)", nLINE)
			else
				bInvoicedQuantity := TRUE
				nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity
					if(eC186.6060 = EMPTY) then
						bfERROR ("XCBL_ERROR_QTY47_g25_EMPTY","eC186.6060 in QTY g25 seg is empty","QTY+47(g25)", nLINE)
						eEcore:QuantityValue := "-0"
					else
						eEcore:QuantityValue := eC186.6060
					endif
				endnodeout
				nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCoded := "Other"
				endnodeout
				nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCodedOther := eC186.6411
				endnodeout
			endif
		!AIO-6560
		case "52" : ! Qauntity per box
			nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
				if(eC186.6060 = EMPTY) then
					bfERROR ("XCBL_ERROR_QTY52_g25_EMPTY","eC186.6060 in QTY g25 seg is empty","QTY+52(g25)", nLINE)
					eEcore:QuantityValue := "-0"
				else
					eEcore:QuantityValue := eC186.6060
				endif
			endnodeout
			nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded, gGcore:UnitOfMeasurement
				eEcore:UOMCoded := "Other"
			endnodeout
			nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded, gGcore:UnitOfMeasurement
				eEcore:UOMCodedOther := eC186.6411
			endnodeout
			nodeout Score:QuantityQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
				eEcore:QuantityQualifierCoded := "NumberOfUnits"
			endnodeout
		case "192" : ! Quantité gratuite
			if(eC186.6060 <> EMPTY and eC186.6411 <> EMPTY) then
				nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
					eEcore:QuantityValue := eC186.6060
				endnodeout
				nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded, gGcore:UnitOfMeasurement
					eEcore:UOMCoded := "Other"
				endnodeout
				nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded, gGcore:UnitOfMeasurement
					eEcore:UOMCodedOther := eC186.6411
				endnodeout
				nodeout Score:QuantityQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
					eEcore:QuantityQualifierCoded := "FreeGoodsQuantity"
				endnodeout

			endif
		case "61" : ! Quantité gratuite
			if(eC186.6060 <> EMPTY and eC186.6411 <> EMPTY) then
				nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGExceptionQuantities, gGReturnedQuantity
					eEcore:QuantityValue := eC186.6060
				endnodeout
				nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGExceptionQuantities, gGReturnedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCoded := "Other"
				endnodeout
				nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGExceptionQuantities, gGReturnedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCodedOther := eC186.6411
				endnodeout
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC186.6063=", eC186.6063, " not managed"), "QTY g25", nLINE)
	endswitch
endnodein

nodein DTM g25
	nLINE++
	bfLOG("Converting segment", "DTM g25")

	if(eC507.2005 = "35") then
		nodeout SInvoiceDueDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates
			eEInvoiceDueDate := tInvoiceDueDate
		endnodeout
		nodeout SActualDeliveryDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates
			eEActualDeliveryDate := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+35(",eC507.2380,":",eC507.2379,")"))
		endnodeout
	endif
	if(eC507.2005 = "200") then
		nodeout SInvoiceDueDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates
			eEInvoiceDueDate := tInvoiceDueDate
		endnodeout
		nodeout Score:Date gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates, gGListOfOtherInvoiceDates, gGcore:DateCoded
			eEcore:Date := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+200(",eC507.2380,":",eC507.2379,")"))
		endnodeout
		nodeout Score:DateQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates, gGListOfOtherInvoiceDates, gGcore:DateCoded, gGcore:DateQualifier
			eEcore:DateQualifierCoded := "PickUpCollectionDateTimeOfCargo"
		endnodeout
	endif
	if(eC507.2005 = "11") then
		nodeout SInvoiceDueDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates
			eEInvoiceDueDate := tInvoiceDueDate
		endnodeout
		nodeout SActualShipDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates
			eEActualShipDate := tfConvertDateXML(eC507.2380, eC507.2379, build("DTM+11(",eC507.2380,":",eC507.2379,")"))
		endnodeout
	endif

endnodein

!
! Prix unitaire net HT de la ligne
! Ce segment spécifie l'information sur le prix liée à la ligne article. Légalement, le prix unitaire net HT doit obligatoirement être mentionné.
!
nodein PRI g25,g28
	nLINE++
	bfLOG("Converting segment", "PRI g25,g28")
	switch eC509.5125
		case "AAA" : ! Calculation net
			if ( eC509.5118 <> EMPTY) then
				bPriceCalculationNet := TRUE
				nodeout Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
					eEcore:PriceTypeCoded := "CalculationNet"
				endnodeout
				nodeout Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
					eEcore:UnitPriceValue := eC509.5118
				endnodeout
				if (eC509.5284 <> EMPTY and eC509.6411 <> EMPTY) then
					nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity
						eEcore:QuantityValue := eC509.5284
					endnodeout
					nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity, gGcore:UnitOfMeasurement
						eEcore:UOMCoded := "Other"
					endnodeout
					nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity, gGcore:UnitOfMeasurement
						eEcore:UOMCodedOther := eC509.6411
					endnodeout
				endif
			endif
		case "AAB" : ! Calculation gross
			if ( eC509.5118 <> EMPTY) then
				bPriceCalculationGross := TRUE
				nodeout Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
					eEcore:PriceTypeCoded := "CalculationGross"
				endnodeout
				nodeout Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
					eEcore:UnitPriceValue := eC509.5118
				endnodeout
				if (eC509.5284 <> EMPTY and eC509.6411 <> EMPTY) then
					nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity
						eEcore:QuantityValue := eC509.5284

					endnodeout
					nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity, gGcore:UnitOfMeasurement
						eEcore:UOMCoded := "Other"
					endnodeout
					nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity, gGcore:UnitOfMeasurement
						eEcore:UOMCodedOther := eC509.6411
					endnodeout
				endif
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC509.5125=", eC509.5125, " not managed"), "PRI g25,g28", nLINE)
	endswitch
endnodein

nodein RFF g25,g29
	bfLOG("Converting segment", build("RFF g25,g29 qualif ", eC506.1153))
	nLINE++
	if(eC506.1153 = "ON" and eC506.1154<> EMPTY)then
		nodeout Score:BuyerOrderNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGPurchaseOrderReference
			eEcore:BuyerOrderNumber := eC506.1154
		endnodeout
		nodeout Score:PurchaseOrderLineItemNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGPurchaseOrderReference
			eEcore:PurchaseOrderLineItemNumber := eC506.1156
		endnodeout
		nodeout Score:ReleaseNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGPurchaseOrderReference
			eEcore:ReleaseNumber := eC506.4000
		endnodeout
	endif
	if(eC506.1153 = "CR" and eC506.1154<> EMPTY)then
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "CustomerReferenceNumber"
		endnodeout
		nodeout Score:RefNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := eC506.1154
		endnodeout
	endif

endnodein

nodein PAC g25,g30
	bfLOG("Converting segment", build("PAC g25,g30"))
	nLINE++
	tQuantityQualifierCodedOther := ""
	if (e7224 <> EMPTY) then
		nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
			eEcore:QuantityValue := e7224
		endnodeout
		if (eC202.7065 <> EMPTY) then
			nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded, gGcore:UnitOfMeasurement
				eEcore:UOMCoded := eC202.7065
			endnodeout
		else
			nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded, gGcore:UnitOfMeasurement
				eEcore:UOMCoded := "S7"
			endnodeout
		endif
		nodeout Score:QuantityQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
			eEcore:QuantityQualifierCoded := "ChargeableNumberOfUnits"
		endnodeout
		switch (eC531.7073)
			case "1" :
				tQuantityQualifierCodedOther := "PackagingCostPaidBySupplier"
			case "2" :
				tQuantityQualifierCodedOther := "PackagingCostPaidByRecipient"
			case "3" :
				tQuantityQualifierCodedOther := "PackagingCostNotCharged"

		endswitch
		if (tQuantityQualifierCodedOther <> EMPTY) then
			nodeout Score:QuantityQualifierCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGListOfQuantityCoded, gGcore:QuantityCoded
				eEcore:QuantityQualifierCodedOther := tQuantityQualifierCodedOther
			endnodeout
		endif
	endif

endnodein

!
! Taux de TVA de la ligne
! Ce segment est utilisé pour préciser les détails de taxation (TVA) de la ligne facturée. Le segment TAX est obligatoire et ne sera pas répété pour une ligne de facturation donnée.
!
nodein TAX g25,g33
	bfLOG("Converting segment", build("TAX g25,g33 qualif ", e5283))
	nLINE++

	switch(eC241.5153)
		case "VAT":  ! Value Added Tax
			nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
				eEcore:TaxTypeCoded :=  "ValueAddedTax"
			endnodeout
			if(tLINFTXSIN = EMPTY) then
				nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
					eEcore:TaxFunctionQualifierCoded :=  "Tax"
				endnodeout
				nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
					eEcore:TaxCategoryCoded :=  "StandardRate"
				endnodeout
			else
				nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
					eEcore:TaxFunctionQualifierCoded :=  "TaxRelatedInformation"
				endnodeout
				nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
					eEcore:TaxCategoryCoded :=  "ExemptFromTax"
				endnodeout
				nodeout Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
					eEcore:ReasonTaxExemptCoded := "Other"
				endnodeout
				nodeout Score:ReasonTaxExemptCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
					eEcore:ReasonTaxExemptCodedOther := tLINFTXSIN
				endnodeout
				tLINFTXSIN := ""
			endif
			nodeout Score:TaxPercent gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
				eEcore:TaxPercent :=  eC243.5278
			endnodeout
			nodeout Score:TaxAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
				eEcore:TaxAmount :=  "0"
			endnodeout
		default:
			bfWARNING("Warn0001", "", build("Warning eC241.5153=", eC241.5153, " not managed"), "TAX g25,g33", nLINE)
	endswitch
endnodein

!
! Libellé de la charge ou de la remise
! Ce segment est utilisé pour spécifier les réductions et charges qui s'appliquent uniquement sur la ligne facturée. Ce segment est obligatoire pour indiquer les rabais, remises ou ristournes
! dont le principe est acquis et le montant chiffrable au moment de la vente, ainsi que les charges afférentes aux articles.
!
nodein ALC g25,g38

	nLINE++
	bfLOG("Converting segment", build("ALC g25,g38 qualif ", eC214.7161, " description ", build(eC214.7160.1,eC214.7160.2)))

	bfResetALCg25Value()

	switch (e5463)
		case "A" :	! Allowance
			if(e4471 = "2") then
				tALCTypeReductionCharge := "LineItemAllowance"
				tALCMethodOfHandling := "AllowanceToBeIssuedByVendor"
			else
				if(e4471 = "1") then
					tALCTypeReductionCharge := "LineItemAllowance"
					tALCMethodOfHandling := "OffInvoice"
				else
					bfERROR("XCBL_ERROR_LINE_ALC_A_REGLMT_CODE", build("Error e4471=", e4471, " should be 1 or 2"), "ALC+A g38", nLINE)
				endif
			endif
		case "C" :	! Charge
			if(e4471 = "2") then
				tALCTypeReductionCharge := "LineItemCharge"
				tALCMethodOfHandling := "ChargeToBePaidByCustomer"
			else
				if(e4471 = "1") then
					tALCTypeReductionCharge := "LineItemCharge"
					tALCMethodOfHandling := "OffInvoice"
				else
					bfERROR("XCBL_ERROR_LINE_ALC_C_REGLMT_CODE", build("Error e4471=", e4471, " should be 1 or 2"), "ALC+C g38", nLINE)
				endif
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning e5463=", e5463, " not managed"), "ALC g25,g38", nLINE)
	endswitch

	switch (eC214.7161) ! Type of Allowance or Charge
		case "AA" :
			tALCService := "AdvertisingAllowance"
			tALCDescriptionSup := "Remise pour publicité"
		case "AAB" : ! Returned goods charges
			tALCService := "ReturnedLoad"
			tALCDescriptionSup := "Frais de marchandises retournées"
		case "ABL" : ! Packaging surcharge
			tALCService := "Packaging"
			tALCDescriptionSup := "Supplément emballage"
		case "ADR" : ! Other services
			tALCDescriptionSup := "Autres services"
			if(e5463 = "A") then
				tALCService := "GoodsAndServicesCreditAllowance"
			else
				tALCService := "GoodsAndServicesCharge"
			endif
		case "ADS" :
			tALCService := "ContainerAllowance"
			tALCDescriptionSup := "Commande d'une palette complète"
		case "ADT" :
			tALCService := "PickUp"
			tALCDescriptionSup := "Enlèvement"
		case "ASS" :
			tALCService := "GroupageDiscount"
			tALCDescriptionSup := "Remise d'assortiment"
		case "CAC" :
			tALCService := "ServiceChargeWithCashDiscount"
			tALCDescriptionSup := "Remise pour paiement comptant"
		case "FC" : ! Freight charge
			tALCService := "Freight"
			tALCDescriptionSup := "Frais de transport"
		case "FI" : ! Finance charge
			tALCService := "FinanceCharge"
			tALCDescriptionSup := "Frais financiers"
		case "LA" : ! Labelling
			tALCService := "Labelling"
			tALCDescriptionSup := "Etiquetage"
		case "PAD" :
			tALCService := "PromotionalAllowance"
			tALCDescriptionSup := "Remise promotionnelle"
		case "QD" :
			tALCService := "QuantityDiscount"
			tALCDescriptionSup := "Remise quantitative"
		case "RAA" :
			tALCService := "Rebate"
			tALCDescriptionSup := "Ristourne"
		case "TAE" :
			tALCService := "FullTruckloadAllowance"
			tALCDescriptionSup := "Remise pour camion complet"
		case "TD" :
			tALCService := "TradeDiscount"
			tALCDescriptionSup := "Remise commerciale"
		case "TX" : ! Tax
			tALCDescriptionSup := "Taxe"
			if (tALCTypeReductionCharge = "LineItemCharge") then
				tALCTypeReductionCharge := "Service"
				tALCService := "Tax-RegulatoryTax"
			else
				tALCService := "Tax"
			endif
		case "X14" :
			tALCService := "DisplayAllowance"
			tALCDescriptionSup := "Emplacement préférentiel pour la vente"
		case "01G" :
			tALCService := "GroupageDiscount"
			tALCDescriptionSup := "Remise baisse durable des prix à la consommation"
		default:
			tALCService := "Other"
	endswitch
	tALCCalculationIndex := e1227
	tALCRefID := eC552.1230
	tALCDescription := build(eC214.7160.1,eC214.7160.2)
	if(tALCDescriptionSup <> EMPTY) then
		if(build(eC214.7160.1,eC214.7160.2) <> EMPTY) then
			tALCDescription      := build(eC214.7160.1,eC214.7160.2," (",tALCDescriptionSup,")")
		endif
	else
		tALCDescription      := build(eC214.7160.1,eC214.7160.2)
	endif
	if (tALCDescription = EMPTY) then
		bfERROR("XCBL_ERROR_LINE_ALC_DESC_MISSING", "ALC(g25) description missing (eC214.7160.1,eC214.7160.2).","ALC(g25)",nLINE)
		tALCDescription := " "
	endif

endnodein

!
! Pourcentage de la remise ou de la charge
! Le segment PCD à l'intérieur du groupe de segments ALC est utilisé pour spécifier les pourcentages de réductions ou charges pour la ligne facturée.
! Si la réduction ou la charge est exprimée en pourcentage, l'indication du pourcentage est obligatoire.
!
nodein PCD g25,g38,g40

	nLINE++

	bfLOG("Converting segment", "PCD g25,g38,g40")
	nPCDg25++
	if (nPCDg25 > 1) then
		bfERROR("XCBL_ERROR_LINE_PCD_MORE_THAN_ONE", "More than one PCD g25 found after ALC segment","PCD(g25,g38,g40)",nLINE)
	else
		if (eC501.5245 <> "1" and eC501.5245 <> "2" ) then
			bfERROR("XCBL_ERROR_LINE_PCD_QUALIFIER_NOT_EXPECTED", "PCD(g25) qualifier is not expected after ALC segment. Should be 1 or 2.","PCD(g25,g38,g40)",nLINE)
		else
			if (tALCTypeReductionCharge = "LineItemAllowance" and eC501.5245 = "2") then
				bfERROR ("XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_A","PCD+2 (g25) can not follow ALC+A","PCD(g25,g38,g40)", nLINE)
			else

				if((tALCTypeReductionCharge = "LineItemCharge" or tALCTypeReductionCharge = "Service") and eC501.5245 = "1") then
					bfERROR ("XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_C","PCD+1 (g25) can not follow ALC+C","PCD(g25,g38,g40)", nLINE)
				else
					if (tALCTypeReductionCharge <> EMPTY) then
						bPCDWithALCg25 := TRUE

						nodeout Score:IndicatorCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
							eEcore:IndicatorCoded  := tALCTypeReductionCharge
						endnodeout

						nodeout Score:MethodOfHandlingCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
							eEcore:MethodOfHandlingCoded := tALCMethodOfHandling
						endnodeout

						nodeout Score:ServiceCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
							gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
							eEcore:ServiceCoded := tALCService
						endnodeout

						if(tALCRefID <> EMPTY) then
							nodeout Score:RefID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
								eEcore:RefID := tALCRefID
							endnodeout
						endif

						nodeout Score:ListOfDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
							gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
							eEcore:ListOfDescription := tALCDescription
						endnodeout

						nodeout Score:Percent gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge
							if (eC501.5482 = EMPTY) then
								bfERROR ("XCBL_ERROR_LINE_PCD_g25_EMPTY","eC501.5482 in PCD g25 seg is empty","PCD(g25,g38,g40)", nLINE)
								eEcore:Percent := "-0"
							else
								eEcore:Percent := eC501.5482
							endif
						endnodeout

						nodeout Score:PercentQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentQualifier
							eEcore:PercentQualifierCoded := "BasePriceAmount"
						endnodeout

						if (tALCCalculationIndex <> EMPTY) then
							nodeout Score:AllowOrChargeTreatmentCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowOrChargeTreatment
								eEcore:AllowOrChargeTreatmentCoded := "Other"
							endnodeout

							nodeout Score:AllowOrChargeTreatmentCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowOrChargeTreatment
								eEcore:AllowOrChargeTreatmentCodedOther := taCalculationSequence[tALCCalculationIndex]
							endnodeout
						endif
					endif
				endif
			endif
		endif
	endif
endnodein

nodein FTX g25
	nLINE++
	bfLOG("Converting segment", build("FTX g25 qualifiant:", e4451))
	if (e4451 = "SIN") then
		tLINFTXSIN := build(eC108.4440.1,eC108.4440.2, eC108.4440.3,eC108.4440.4,eC108.4440.5)
	endif

endnodein

nodein MOA g25,g26
	nLINE++
	bfLOG("Converting segment", build("MOA g25,g26 qualifiant:", eC516.5025, " value:",eC516.5004))
	if (eC516.5025 = "203" and eC516.5004 <> EMPTY) then
		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:LineItemTotal
			eEcore:MonetaryAmount  := eC516.5004
		endnodeout
	endif
endnodein

!
! Montant de la remise ou de la charge
! Le segment MOA à l'intérieur du groupe de segments ALC est utilisé pour spécifier les montants monétaires de réductions ou de charges de la ligne facturée et les bases de calcul.
! Les montants de réductions ou de charges indiqués à la ligne sont toujours des valeurs unitaires. Si la réduction ou la charge est exprimée en montant, l'indication du montant est obligatoire.
!
nodein MOA g25,g38,g41
	nLINE++
	bfLOG("Converting segment", build("MOA g25,g38,g41 qualifiant:", eC516.5025, " value:",eC516.5004))

	if(eC516.5025 = "204" or eC516.5025 = "23") then
		nMOAg25++
	endif

	if (nMOAg25 > 1) then
		bfERROR("XCBL_ERROR_LINE_MOA_MORE_THAN_ONE", "More than one MOA g25 found after ALC segment (qualifier 204 or 23) ","MOA(g25,g38,g41)",nLINE)
	else
		if (eC516.5025 = "204" or eC516.5025 = "23") then
			if (tALCTypeReductionCharge = "LineItemAllowance" and eC516.5025 = "23") then
				bfERROR ("XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_A","MOA+23 (g25) can not follow ALC+A","MOA(g25,g38,g41)", nLINE)
			else
				if((tALCTypeReductionCharge = "LineItemCharge" or tALCTypeReductionCharge = "Service") and eC516.5025 = "204") then
					bfERROR ("XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_C","MOA+204 (g25) can not follow ALC+C","MOA(g25,g38,g41)", nLINE)
				else
					bMOAWithALCg25 := TRUE
					if (tALCTypeReductionCharge <> EMPTY) then
						if (bPCDWithALCg25) then

							nodeout Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, \
								gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentageMonetaryValue
								if (eC516.5004 = EMPTY) then
									eEcore:MonetaryAmount := "-0"
									bfERROR ("XCBL_ERROR_LINE_MOA_g25_EMPTY","eC516.5004 in MOA g25 seg is empty","MOA(g25,g38,g41)", nLINE)
								else
									eEcore:MonetaryAmount := eC516.5004
								endif
							endnodeout

						else
							nodeout Score:IndicatorCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
								eEcore:IndicatorCoded  := tALCTypeReductionCharge
							endnodeout

							nodeout Score:MethodOfHandlingCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
								eEcore:MethodOfHandlingCoded := tALCMethodOfHandling
							endnodeout

							nodeout Score:ServiceCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
								eEcore:ServiceCoded := tALCService
							endnodeout


							if(tALCRefID <> EMPTY) then
								nodeout Score:RefID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
									gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
									eEcore:RefID := tALCRefID
								endnodeout
							endif

							nodeout Score:ListOfDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
								eEcore:ListOfDescription := tALCDescription
							endnodeout

							nodeout Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
								gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:MonetaryValue
								if (eC516.5004 = EMPTY) then
									bfERROR ("XCBL_ERROR_LINE_MOA_g25_EMPTY","eC516.5004 in MOA g25,g38,g41 seg is empty","MOA(g25,g38,g41)", nLINE)
									eEcore:MonetaryAmount := "-0"
								else
									eEcore:MonetaryAmount  := eC516.5004
								endif
							endnodeout

							if (tALCCalculationIndex <> EMPTY) then
								nodeout Score:AllowOrChargeTreatmentCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
									gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowOrChargeTreatment
									eEcore:AllowOrChargeTreatmentCoded := "Other"
								endnodeout

								nodeout Score:AllowOrChargeTreatmentCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
									gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowOrChargeTreatment
									eEcore:AllowOrChargeTreatmentCodedOther := taCalculationSequence[tALCCalculationIndex]
								endnodeout
							endif
						endif
					endif
				endif
			endif
		endif
	endif

	if (eC516.5025 = "02G" or eC516.5025 = "01G") then
		nodeout Score:BasisCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
			gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
			eEcore:BasisCoded := "Other"
		endnodeout
		if (eC516.5025 = "02G") then
			nodeout Score:BasisCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
				gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
				eEcore:BasisCodedOther := "MonetaryAmountWithRegulatoryTaxes"
			endnodeout
		else
			nodeout Score:BasisCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
				gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
				eEcore:BasisCodedOther := "MonetaryAmountWithoutRegulatoryTaxes"
			endnodeout
		endif
		nodeout Score:MonetaryLimitValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
			gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:BasisMonetaryRange, gGcore:MonetaryLimit
			eEcore:MonetaryLimitValue := eC516.5004
		endnodeout
	endif
	if(eC516.5025 = "25") then
		nodeout Score:BasisCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
			gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
			eEcore:BasisCoded := "MonetaryAmount"
		endnodeout
		nodeout Score:MonetaryLimitValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
			gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:BasisMonetaryRange, gGcore:MonetaryLimit
			eEcore:MonetaryLimitValue := eC516.5004
		endnodeout
	endif
endnodein

!
! Taux de TVA de la taxe parafiscale à la ligne
! Ce segment est utilisé pour identifier les taxes ou les droits qui s'appliquent sur les réductions ou charges spécifiées dans SG38.
! Le segment TAX n'est utilisé, de façon unique, que lorsque le taux de TVA de la taxe ou assimilée, de la réduction ou de la charge est différent de celui de l'article facturé,
! notamment lorsque la taxe ou assimilées n'est pas soumise à TVA (taux = 0). Les montants des charges à la ligne doivent être intégrés dans la ventilation par taux de TVA en pied de facture.
!
nodein TAX g25,g38,g43
	nLINE++
	bfLOG("Converting segment", build("TAX g25,g38,g43 qualif ", e5283))

	switch(eC241.5153)
		case "VAT":  ! Value Added Tax
			if (tALCTypeReductionCharge <> EMPTY) then
				nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
						gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxTypeCoded :=  "ValueAddedTax"
				endnodeout

				nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
						gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxFunctionQualifierCoded := "Tax"
				endnodeout

				!Catégorie de la TVA appliquée
				nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
						gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxCategoryCoded := "Other"
				endnodeout
				nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
						gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxCategoryCodedOther := eC243.5278
				endnodeout

				!Taux de TVA appliqué
				nodeout Score:TaxPercent gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
						gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxPercent := eC243.5278
				endnodeout

				!Montant de taxe pour respecter la norme XCBL
				nodeout Score:TaxAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
						gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
					eEcore:TaxAmount := "0"
				endnodeout
			endif
		default:
			bfWARNING("Warn0001", "", build("Warning eC241.5153=", eC241.5153, " not managed"), "TAX g25,g38,g43", nLINE)
	endswitch
endnodein

nodein UNS

	nLINE++
	bfCheckSegmentNadBy()
	bfCheckSegmentNadSe()
	bfCheckSegmentDtm()
	bfCheckSegmentBgm()
	bfCheckSegmentAlcg15()
endnodein

!
! Montant taxable
! Montant total TTC
! Montant de taxe total en euros.
! Cette occurrence du segment MOA est utilisée pour les acomptes versés ou les montants monétaires totaux du message. Le segment MOA est obligatoire.
!
!
nodein MOA g48

	nLINE++

	bfLOG("Converting segment", build("MOA g48 qualifiant: ", eC516.5025))
	switch eC516.5025
		case "9" : ! Prepaid amount
			if (eC516.5004 = EMPTY) then
				bfERROR ("XCBL_ERROR_MOA9_g48_EMPTY","eC516.5004 in MOA+9 g48 seg is empty","MOA+9(g48)", nLINE)
				tMOA9 := "-0"
			else
				tMOA9 := eC516.5004
			endif
			nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTotalAmountPayable
				eEcore:MonetaryAmount :=  tMOA9
			endnodeout
		case "113" : ! Prepaid amount
			bMOA113 := TRUE
			if (eC516.5004 = EMPTY) then
				bfERROR ("XCBL_ERROR_MOA113_g48_EMPTY","eC516.5004 in MOA g48 seg is empty","MOA+113(g48)", nLINE)
				tMOA113 := "-0"
			else
				tMOA113 := eC516.5004
			endif
			nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment, gGcore:PaymentAmount, gGcore:InvoiceCurrencyAmt
				eEcore:MonetaryAmount := tMOA113
			endnodeout
			nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment, gGcore:PaymentAmount, gGcore:ForeignCurrencyPayment
				eEcore:MonetaryAmount := tMOA113
			endnodeout
		case "124" : ! Total amount
			if (eC516.6345 = "EUR") then
				tMOA124g48 := eC516.5004
				nMOA124g48Matching++
			else
				if (eC516.6345 = "") then
					tMOA124g48 := eC516.5004
					nMOA124g48Matching++
				endif
			endif
		case "125" : ! Tax amount
			if (eC516.6345 <> EMPTY) then
				if (tInvoiceCurrency = eC516.6345) then
					tMOA125g48 := eC516.5004
					nMOA125g48Matching++
				endif
			else
				tMOA125g48 := eC516.5004
				nMOA125g48Matching++
			endif
		case "128" : ! Total amount
			if (eC516.6345 <> EMPTY) then
				if (tInvoiceCurrency = eC516.6345) then
					tMOA128g48 := eC516.5004
					nMOA128g48Matching++
				endif
			else
				tMOA128g48 := eC516.5004
				nMOA128g48Matching++
			endif
		case "35E" :
			nodeout Score:SetName gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet
				eEcore:SetName := "InvoiceExtraTotals"
			endnodeout
			nodeout Score:Name gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
				eEcore:Name := "DepositTotalAmount"
			endnodeout
			nodeout Score:Value gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
				eEcore:Value := eC516.5004
			endnodeout
		default:
			bfWARNING("Warn0001", "", build("Warning eC516.5025=", eC516.5025, " not managed"), "MOA g48", nLINE)
	endswitch

	bfCheckSegmentLine() ! Dans le cas de facture de remise de fin d'année on n'a pas de ligne. Dans Xcbl la ligne est obligatoire
	bfCheckSegmentAlcg25()
	bfForceCompleteLine() ! Si la ligne n'a pas de quantité définie, on force la valeur à -0, idem pour l'élément Price
endnodein

! Numéro de l'accompte
! Ce segment permet d'exprimer les références de l'acompte.
! Légalement, ce segment est obligatoire et doit donc être transmis si un acompte a été perçu (indication du montant de l'acompte dans le segment MOA précédent avec DE 5025 = 113).
! Il n'y a pas de traitement effectué sur ce segment.
!
nodein RFF g48,g49
	nLINE++
	bfLOG("Converting segment", "RFF g48,g49")
	switch(eC506.1153)
		case "PQ": ! Payment Reference
			bRFFPQ := TRUE
			if (eC506.1154 = EMPTY) then
				bfERROR ("XCBL_ERROR_RFFPQ_g48_EMPTY","eC506.1154 in RFF+PQ is empty","RFF+PQ(g48)", nLINE)
				tRFFPQ := " "
			else
				tRFFPQ := eC506.1154
			endif
			nodeout Score:OtherPaymentInfo gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment
				eEcore:OtherPaymentInfo := tRFFPQ
			endnodeout
			nodeout Score:PaymentMeanCoded gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment, gGcore:PaymentMean
				eEcore:PaymentMeanCoded := "Other"
			endnodeout
			nodeout Score:PaymentCodedMeanOther gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment, gGcore:PaymentMean
				eEcore:PaymentCodedMeanOther := "Unknow"
			endnodeout
		default:
			bfWARNING("Warn0001", "", build("Warning eC506.1153=", eC506.1153, " not managed"), "RFF g48,g49", nLINE)
	endswitch
endnodein

!
! Date de l'accompte
! Ce segment est utilisé pour dater les références spécifiées dans le segment RFF précédent.
! Légalement, si un acompte a été perçu (indication du montant de l'acompte dans le segment MOA précédent avec DE 5025 = 113), le segment DTM est obligatoire pour indiquer la date de l'acompte.
!
nodein DTM g48,g49

	bfLOG("Converting segment", "DTM g48,g49")
	nLINE++

	switch(eC507.2005)
		case "171":
			bDTM171 := TRUE
			nodeout Score:PaymentDate gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment
				eEcore:PaymentDate := tfConvertDateXML(eC507.2380, eC507.2379,  build("DTM+171(g48)(",eC507.2380,":",eC507.2379,")"))
			endnodeout
		default:
			bfWARNING("Warn0001", "", build("Warning eC507.2005=", eC507.2005, " not managed"), "DTM g48,g49", nLINE)
	endswitch
endnodein


!
! Taux de TVA ventilé
! Montant taxable en euros selon le taux ventilé
! Ce groupe (TAX,MOA) est utilisé pour fournir les montants totaux de TVA. Il comporte autant d¿occurrences que de taux de TVA dans la facture (y compris le taux = 0).
!
nodein TAX g50
	bfLOG("Converting segment", "TAX g50")
	nLINE++

	bfCheckMOA124g50()

	tMOA124g50 := ""
	nMOA124g50Matching := 0

	tTAX5153 := eC241.5153
	switch(eC241.5153)
		case "VAT":  ! Value Added Tax
			switch(e5305)
				case "A": ! Mixed tax rate
					tTypeTVA := "MixedTaxRate"
				case "B": ! Transfered(VAT)
					tTypeTVA := "Transferred"
				case "C": ! Duty paid by supplier
					tTypeTVA := "Transferred"
				case "E": ! Exempt from tax
					tTypeTVA := "ExemptFromTax"
				case "G": ! Free export item, tax not charged
					tTypeTVA := "FreeExportItemTaxNotCharged"
				case "H": ! Higher rate
					tTypeTVA := "HigherRate"
				case "O": ! Services outside scope of tax
					tTypeTVA := "ServicesOutsideScopeOfTax"
				case "S": ! Standard rate
					tTypeTVA := "StandardRate"
				case "Z": ! Zero rated goods
					tTypeTVA := "ZeroRatedGoods"
				default:
					tTypeTVA := "Other"
			endswitch
			nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
				eEcore:TaxCategoryCoded := tTypeTVA
			endnodeout
			nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
				eEcore:TaxTypeCoded := "Total"
			endnodeout
			nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
				eEcore:TaxFunctionQualifierCoded := "Tax"
			endnodeout
			nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
				eEcore:TaxCategoryCodedOther := eC243.5278
			endnodeout
			nodeout Score:TaxableAmount gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
				eEcore:TaxableAmount := e5286
			endnodeout
		default:
			bfWARNING("Warn0001", "", build("Warning eC241.5153=", eC241.5153, " not managed"), "TAX g50", nLINE)
	endswitch
endnodein


!
! Montant taxable en euros selon le taux ventilé
! Ce segment est utilisé pour spécifier les montants de TVA dus, pour le taux de TVA spécifié dans le segment TAX précédent.
! Légalement, le segment MOA sous TAX sera obligatoirement présent pour indiquer le montant de la TVA.
!
nodein MOA g50
	nLINE++
	bfLOG("Converting segment", "MOA g50")
	if tTAX5153="VAT" then
		switch(eC516.5025)
			case "124":
				if (eC516.6345 = "EUR") then
					tMOA124g50 := eC516.5004
					nMOA124g50Matching++
				else
					if (eC516.6345 = "") then
						tMOA124g50 := eC516.5004
						nMOA124g50Matching++
					endif
				endif
			default:
				bfWARNING("Warn0001", "", build("Warning eC516.5025=", eC516.5025, " not managed"), "MOA g50", nLINE)
		endswitch
	endif
endnodein

nodein ALC g51
	nLINE++
	bfLOG("Converting segment", "ALC g51")

	if(e5463 = "C" and eC214.7161 = "TX") then
			tALCg51C5521230 := eC552.1230
			tALCg51C2147160 := eC214.7160.1
	endif

endnodein

nodein MOA g51
	nLINE++
	bfLOG("Converting segment", "MOA g51")

	if (eC516.5025 = "8" and tALCg51C5521230 <> EMPTY and tALCg51C2147160 <> EMPTY and eC516.5004 <> EMPTY) then
		nodeout Score:AllowOrChargeIndicatorCoded gGInvoice, gGInvoiceSummary, gGAllowOrChargeSummary, gGcore:TotalAllowOrCharge
			eEcore:AllowOrChargeIndicatorCoded := "Charge"
		endnodeout
		nodeout Score:RefID gGInvoice, gGInvoiceSummary, gGAllowOrChargeSummary, gGcore:TotalAllowOrCharge, gGcore:AllowanceOrChargeDescription
			eEcore:RefID := tALCg51C5521230
		endnodeout
		nodeout Score:ListOfDescription gGInvoice, gGInvoiceSummary, gGAllowOrChargeSummary, gGcore:TotalAllowOrCharge, gGcore:AllowanceOrChargeDescription
			eEcore:ListOfDescription := tALCg51C2147160
		endnodeout
		nodeout Score:ServiceCoded gGInvoice, gGInvoiceSummary, gGAllowOrChargeSummary, gGcore:TotalAllowOrCharge, gGcore:AllowanceOrChargeDescription
			eEcore:ServiceCoded := "Tax-RegulatoryTax"
		endnodeout
		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGAllowOrChargeSummary, gGcore:TotalAllowOrCharge, gGcore:SummaryAllowOrCharge
			eEcore:MonetaryAmount := eC516.5004
		endnodeout
	endif


endnodein

nodein UNT
	nLINE++
	bfLOG("Converting segment", "UNT")
	bfCheckSegmentLine()
	bfCheckMOA124g50()
	bfCheckMOA124g48()
	bfCheckMOA125g48()
	bfCheckMOA128g48()
	bfCheckMOA113g48()
endnodein

nodein UNZ
	! validation du message à la fin du traitement pour tout de même générer le xcbl
	if not valid(MESSAGE_IN) then
		edierrorlist(MESSAGE_IN)
	endif
endnodein

!
! segment par défaut
!
default
	nLINE++
enddefault


!
! Section de fin des traitements
!
end
	if(nUNB = 0 and nUNH = 0) then
		bfERROR ("99InternalError0008","EDIFACT Invoice D96A Interchange is requiered","", nLINE)
		bfEXIT(8)
	endif
	print(MESSAGE_OUT)
	bfEXIT(0)
endend

!
! Convertion de la date EDIFACT en date XCBL
!
function tfConvertDateXML(tDateEDI, tFormatDate, tSegment)

	tFormatDate_ := tFormatDate
	if tDateEDI=EMPTY then
		if (index(tSegment,"DTM+137") > 0) then
			bfERROR ("XCBL_ERROR_INVOIC_DATE_EMPTY","Empty invoic date. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
		else
		bfERROR ("XCBL_ERROR_DATE_EMPTY","Empty date. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
		endif
		return "1970-01-01T00:00:00"
	endif

	if(tFormatDate_ = EMPTY) then
		if (index(tSegment,"DTM+137") > 0) then
			bfERROR ("XCBL_ERROR_INVOIC_DATE_FORMAT_EMPTY","Empty invoics date format. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
		else
		bfERROR ("XCBL_ERROR_DATE_FORMAT_EMPTY","Empty date format. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
		endif

		return "1970-01-01T00:00:00"
	endif
	bfLOG("Converting date", build(tDateEDI, ",format:",tFormatDate_))
	switch(tFormatDate_)
		case "101" : ! YYMMDD
			if (length(tDateEDI) <> 6) then
				if (index(tSegment,"DTM+137") > 0) then
					bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				else
				bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				endif
				return "1970-01-01T00:00:00"
			endif
			tYear := substr(tDateEDI,1,2)
			tMonth := substr(tDateEDI,3,2)
			tDay := substr(tDateEDI,5,2)
			if ( bfIsNUMBER(tYear) and bfIsNUMBER(tMonth) and bfIsNUMBER(tDay)) then
				tDateTest := build("20",tYear,tMonth,tDay)
				if (bfIsValidDate(tDateTest) = TRUE) then
					return build("20", tYear, "-", tMonth, "-", tDay, "T00:00:00")
				else
					if (index(tSegment,"DTM+137") > 0) then
						bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					else
					bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					endif
					return "1970-01-01T00:00:00"
				endif
			else
				if (index(tSegment,"DTM+137") > 0) then
					bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED","Invoic date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				else
				bfERROR ("XCBL_ERROR_DATE_NOT_WELLFORMATED","Date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				endif
				return "1970-01-01T00:00:00"
			endif
		case "102" : ! CCYYMMDD
		case "718" :
			if (length(tDateEDI) <> 8) then
				if (index(tSegment,"DTM+137") > 0) then
					bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				else
				bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				endif
				return "1970-01-01T00:00:00"
			endif
			tYear := substr(tDateEDI,1,4)
			tMonth := substr(tDateEDI,5,2)
			tDay := substr(tDateEDI,7,2)

			if ( bfIsNUMBER(tYear) and bfIsNUMBER(tMonth) and bfIsNUMBER(tDay) ) then
				tDateTest := build(tYear,tMonth,tDay)
				 if (bfIsValidDate(tDateTest ) = TRUE) then
					return build(tYear, "-", tMonth, "-", tDay, "T00:00:00")
				else
					if (index(tSegment,"DTM+137") > 0) then
						bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					else
					bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					endif
					return "1970-01-01T00:00:00"
				endif
			else
				if (index(tSegment,"DTM+137") > 0) then
					bfERROR("XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED","Date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				else
					bfERROR("XCBL_ERROR_DATE_NOT_WELLFORMATED","Date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				endif
				return "1970-01-01T00:00:00"
			endif
		case "201" : ! YYMMDDHHMM
			if (length(tDateEDI) <> 10) then
				if (index(tSegment,"DTM+137") > 0) then
					bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				else
				bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				endif
				return "1970-01-01T00:00:00"
			endif
			tYear := substr(tDateEDI,1,2)
			tMonth := substr(tDateEDI,3,2)
			tDay := substr(tDateEDI,5,2)
			tHour := substr(tDateEDI,7,2)
			tMinute := substr(tDateEDI,9,2)
			if ( bfIsNUMBER(tYear) and bfIsNUMBER(tMonth) and bfIsNUMBER(tDay) and bfIsNUMBER(tHour) and bfIsNUMBER(tMinute)) then
				tDateTest := build("20",tYear,tMonth,tDay)
				if (bfIsValidDate(tDateTest) = TRUE) then
					nHour := number(tHour)
					nMinute := number(tMinute)
					if( nHour >= 0 and nHour < 24) then
						if( nMinute >= 0 and nMinute < 60) then
							return build("20", tYear, "-", tMonth, "-", tDay, "T", tHour, ":", tMinute, ":00")
						endif
					endif
					if (index(tSegment,"DTM+137") > 0) then
						bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					else
					bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					endif
					return "1970-01-01T00:00:00"
				else
					if (index(tSegment,"DTM+137") > 0) then
						bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_VALID","Invoic date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					else
					bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					endif
					return "1970-01-01T00:00:00"
				endif
			else
				if (index(tSegment,"DTM+137") > 0) then
					bfERROR ("XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED","Invoic date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				else
				bfERROR ("XCBL_ERROR_DATE_NOT_WELLFORMATED","Date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				endif
				return "1970-01-01T00:00:00"
			endif
		default:
			if (index(tSegment,"DTM+137") > 0) then
				bfERROR ("XCBL_ERROR_INVOIC_FORMAT_NOT_MANAGED","Invoice date : Format not managed. Forced to 1970-01-01T00:00:00",tSegment, nLINE)
			else
			bfERROR ("XCBL_ERROR_FORMAT_NOT_MANAGED","Format not managed. Forced to 1970-01-01T00:00:00",tSegment, nLINE)
			endif
			return "1970-01-01T00:00:00"
	endswitch
endfunction

!Conversion de date spécifique à l'UNB (YYMMDDHHMM ou CCYYMMDDHHMM)
function tfConvertUNBDateXML(tDateEDI, tSegment)

	if tDateEDI=EMPTY then
		bfERROR ("XCBL_ERROR_DATE_EMPTY","Empty date. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
		return "1970-01-01T00:00:00"
	endif
	if length(tDateEDI) = 10 then
		bfLOG("Converting date", build(tDateEDI, ",format:","YYMMDDHHMM"))
		tYear := substr(tDateEDI,1,2)
		tMonth := substr(tDateEDI,3,2)
		tDay := substr(tDateEDI,5,2)
		tHour := substr(tDateEDI,7,2)
		tMinute := substr(tDateEDI,9,2)
		if ( bfIsNUMBER(tYear) and bfIsNUMBER(tMonth) and bfIsNUMBER(tDay) and bfIsNUMBER(tHour) and bfIsNUMBER(tMinute)) then
			tDateTest := build("20",tYear,tMonth,tDay)
			if (bfIsValidDate(tDateTest) = TRUE) then
				nHour := number(tHour)
				nMinute := number(tMinute)
				if( nHour >= 0 and nHour < 24) then
					if( nMinute >= 0 and nMinute < 60) then
						return build("20", tYear, "-", tMonth, "-", tDay, "T", tHour, ":", tMinute, ":00")
					endif
				endif
				bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				return "1970-01-01T00:00:00"
			else
				bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				return "1970-01-01T00:00:00"
			endif
		else
			bfERROR ("XCBL_ERROR_DATE_NOT_WELLFORMATED","Date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
			return "1970-01-01T00:00:00"
		endif
	else
		if length(tDateEDI) = 12 then
			bfLOG("Converting date", build(tDateEDI, ",format:","CCYYMMDDHHMM"))
			tYear := substr(tDateEDI,1,4)
			tMonth := substr(tDateEDI,5,2)
			tDay := substr(tDateEDI,7,2)
			tHour := substr(tDateEDI,9,2)
			tMinute := substr(tDateEDI,11,2)
			if ( bfIsNUMBER(tYear) and bfIsNUMBER(tMonth) and bfIsNUMBER(tDay) and bfIsNUMBER(tHour) and bfIsNUMBER(tMinute)) then
				tDateTest := build(tYear,tMonth,tDay)
				if (bfIsValidDate(tDateTest) = TRUE) then
					nHour := number(tHour)
					nMinute := number(tMinute)
					if( nHour >= 0 and nHour < 24) then
						if( nMinute >= 0 and nMinute < 60) then
							return build(tYear, "-", tMonth, "-", tDay, "T", tHour, ":", tMinute, ":00")
						endif
					endif
					bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					return "1970-01-01T00:00:00"
				else
					bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
					return "1970-01-01T00:00:00"
				endif
			else
				bfERROR ("XCBL_ERROR_DATE_NOT_WELLFORMATED","Date is not correctly formatted. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
				return "1970-01-01T00:00:00"
			endif
		else
			bfERROR ("XCBL_ERROR_DATE_NOT_VALID","Date is not valid. Forced to 1970-01-01T00:00:00", tSegment, nLINE)
			return "1970-01-01T00:00:00"
		endif
	endif
endfunction

!
! Initialisation des tableaux
!
function bfVariablesInit()

	nLINE := 0
	nUNH  := 0
	nUNB  := 0
	nNAD  := 0
	bLIN := FALSE
	bNewLine := FALSE
	bMOA204WithALC := FALSE
	bInvoicedQuantity := FALSE
	bPriceCalculationNet := FALSE
	bPriceCalculationGross := FALSE
	bNADBY := FALSE
	bNADSE := FALSE
	bInvoiceDueDate := FALSE
	tInvoiceDueDate := ""
	bInvoiceIssueDate := FALSE
	bBGM := FALSE
	bTestPMT := FALSE
	tInvoiceCurrency := ""
	nNBInvoiceIssueDate:=0
	nNBFtxReg := 0

	bIVName1Empty := FALSE
	bIVNameAddress := FALSE
	bBYName1Empty := FALSE
	bBYNameAddress := FALSE
	bSEName1Empty := FALSE
	bSENameAddress := FALSE
	bCOName1Empty := FALSE
	bCONameAddress := FALSE
	bLCName1Empty := FALSE
	bLCNameAddress := FALSE
	bDPName1Empty := FALSE
	bDPNameAddress := FALSE
	bREName1Empty := FALSE
	bRENameAddress := FALSE
	bDLName1Empty := FALSE
	bDLNameAddress := FALSE
	nNbNADIV := 0
	nNbNADSE := 0
	nNbNADBY := 0
	nNbNADCO := 0
	nNbNADLC := 0
	nNbNADDP := 0
	nNbNADRE := 0
	nNbNADDL := 0
	nRFFGNSE := 0
	nRFFVASE := 0
	nRFFXASE := 0
	nRFFGNBY := 0
	nRFFVABY := 0
	nRFFGNIV := 0
	nRFFVAIV := 0
	nRFFGNCO := 0
	nRFFXACO := 0
	nRFFVACO := 0
	tRFFVACO := ""
	tRFFXACO := ""
	tRFFGNCO := ""
	nRFFGNLC := 0
	nRFFVALC := 0
	nRFFGNDP := 0
	nRFFVADP := 0
	nRFFGNRE := 0
	nRFFVARE := 0
	nRFFGNDL := 0
	nRFFVADL := 0
	nDTM13 := 0
	tMOA124g48 := ""
	nMOA124g48Matching := 0
	tMOA125g48 := ""
	nMOA125g48Matching := 0
	tMOA128g48 := ""
	nMOA128g48Matching := 0
	tMOA128g50 := ""
	nMOA124g50Matching := 0

	tMOA113 := ""
	bMOA113 := FALSE
	tRFFPQ := ""
	bRFFPQ := FALSE
	bDTM171 := FALSE

	bfResetALCg15Value()
	bfResetALCg25Value()
	tFTXSIN := ""
	bFTXSIN := FALSE

	tFTXREGC10844401 := ""
	tFTXREGC10844402 := ""
	tFTXREGC10844403 := ""

	tALCg51C5521230 := ""
	tALCg51C2147160 := ""

	tPMD := ""

	remove(taCalculationSequence)
	taCalculationSequence["1"] := "FirstStepOfCalculation"
	taCalculationSequence["2"] := "SecondStepOfCalculation"
	taCalculationSequence["3"] := "ThirdStepOfCalculation"
	taCalculationSequence["4"] := "FourthStepOfCalculation"
	taCalculationSequence["5"] := "FifthStepOfCalculation"
	taCalculationSequence["6"] := "SixthStepOfCalculation"
	taCalculationSequence["7"] := "SeventhStepOfCalculation"
	taCalculationSequence["8"] := "EighthStepOfCalculation"
	taCalculationSequence["9"] := "NinthStepOfCalculation"

	bfTableInit()

endfunction

function bfResetALCg15Value()
	tALCHeaderTypeReductionCharge := ""
	tALCHeaderMethodOfHandling := ""
	tALCHeaderService := ""
	tALCHeaderCalculationIndex := ""
	tALCHeaderDescription := ""
	tALCHeaderDescriptionSup := ""
    tALCHeaderRefID := ""
	bPCDWithALCg15 := FALSE
	bMOAWithALCg15 := FALSE
	nMOAg15 := 0
	nPCDg15 := 0
endfunction

function bfResetALCg25Value()
	tALCTypeReductionCharge := ""
	tALCService := ""
	tALCCalculationIndex := ""
	tALCRefID := ""
	tALCDescription := ""
	tALCDescriptionSup := ""
	bPCDWithALCg25 := FALSE
	bMOAWithALCg25 := FALSE
	nMOAg25 := 0
	nPCDg25 := 0
endfunction
!
! Check que le segment LINE est présent.
! Si ce n'est pas le cas, la fonction génére un tag InvoiceDetail fake
!

function bfCheckSegmentLine() ! Dans le cas de facture de remise de fin d'année on n'a pas de ligne. Dans Xcbl la ligne est obligatoire

	if (bLIN = TRUE) then
		return TRUE
	endif
	bLIN := TRUE

	nodeout Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
		eEcore:ProductIdentifier := "N/A"
	endnodeout
	nodeout Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
		eEcore:ProductIdentifierQualifierCoded := "GTIN"
	endnodeout
	nodeout Score:SellerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum
		eEcore:SellerLineItemNum := "1"
	endnodeout
	nodeout Score:BuyerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum
		eEcore:BuyerLineItemNum := "1"
	endnodeout
	nodeout Score:ItemDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers
		eEcore:ItemDescription := "Fake Line"
	endnodeout
	nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity
		eEcore:QuantityValue := "0"
	endnodeout
	nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
		eEcore:UOMCoded := "Other"
	endnodeout
	nodeout Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
		eEcore:PriceTypeCoded := "CalculationNet"
	endnodeout
	nodeout Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
		eEcore:UnitPriceValue := "0"
	endnodeout

	nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		eEcore:TaxTypeCoded :=  "ValueAddedTax"
	endnodeout

	nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
	endnodeout

	!Catégorie de la TVA appliquée
	nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		eEcore:TaxCategoryCoded := "ExemptFromTax"
	endnodeout

	nodeout Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		eEcore:ReasonTaxExemptCoded := "Other"
	endnodeout
	nodeout Score:ReasonTaxExemptCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		eEcore:ReasonTaxExemptCodedOther := "FakeExemption"
	endnodeout

	!Montant de taxe pour respecter la norme XCBL
	nodeout Score:TaxAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		eEcore:TaxAmount := "0"
	endnodeout

	return(TRUE)

endfunction

function bfForceCompleteLine()
! Dans xcbl, la quantité pour une ligne est obligatoire. On force la quantité quand celle-ci n'a pas été renseignée auparavant.
! De même on force l'élement price si il n'y en a aucun
! De même on force le TypeOfAllowanceOrCharge/MonetaryAmount/MonetaryValue
	!AIO-6560
	if(bNewLine and not bLineItemTypeCoded) then
		nodeout Score:LineItemTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemType
			eEcore:LineItemTypeCoded := "Item"
		endnodeout
	endif
	if (bNewLine and not bInvoicedQuantity) then
		bfERROR ("XCBL_ERROR_QTY47_MISSING","QTY+47 in g25 is missing","QTY(g25)", nLINE)
		nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity
			eEcore:QuantityValue := "-0"
		endnodeout
		nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
			eEcore:UOMCoded := "Other"
		endnodeout
	endif
	if (bNewLine and not bPriceCalculationGross and not bPriceCalculationNet) then
		bfERROR ("XCBL_ERROR_PRI_MISSING","PRI+AAA and PRI+AAB in g25 are missing","QTY(g25)", nLINE)
		nodeout Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
			eEcore:PriceTypeCoded := "CalculationNet"
		endnodeout
		nodeout Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
			eEcore:UnitPriceValue := "-0"
		endnodeout
	endif

	bLineItemTypeCoded := FALSE
	bInvoicedQuantity := FALSE
	bNewLine := FALSE
	bPriceCalculationGross :=FALSE
	bPriceCalculationNet := FALSE
	if (tLINFTXSIN <> EMPTY) then
		nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
			eEcore:TaxTypeCoded :=  "ValueAddedTax"
		endnodeout

		nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
			eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
		endnodeout

		!Catégorie de la TVA appliquée
		nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
			eEcore:TaxCategoryCoded := "ExemptFromTax"
		endnodeout

		nodeout Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
			eEcore:ReasonTaxExemptCoded := "Other"
		endnodeout
		nodeout Score:ReasonTaxExemptCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
			eEcore:ReasonTaxExemptCodedOther := tLINFTXSIN
		endnodeout

		!Montant de taxe pour respecter la norme XCBL
		nodeout Score:TaxAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
			eEcore:TaxAmount := "0"
		endnodeout
		tLINFTXSIN := ""
	endif

	if(tItemDescription <> EMPTY) then
		nodeout Score:ItemDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers
			eEcore:ItemDescription := tItemDescription
		endnodeout
		tItemDescription:=""
	endif
endfunction


function bfCheckSegmentNadBy() ! Si le segment NAD+BY n'est pas présent on génére avec le contenu du segment NAD+IV

	if(bNADBY = TRUE) then
		return TRUE
	endif
	bNADBY := TRUE

	nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyID
		eEcore:Ident := " "
	endnodeout
endfunction

function bfCheckSegmentNadSe() ! Si le segment NAD+SE n'est pas présent on génére avec un contenu vide

	if (nRFFVASE = 0 and tRFFVACO <> EMPTY) then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
			eEcore:AgencyCoded := "CEC"
		endnodeout
		nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
			eEcore:CodeListIdentifierCoded := "ValueAddedTaxIdentification"
		endnodeout
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
			eEcore:Ident := tRFFVACO
		endnodeout
		nRFFVASE++
	endif
	if (nRFFXASE = 0 and tRFFXACO <> EMPTY) then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "RCS-RCM"
		endnodeout
		nodeout Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyDescription := "French Trade and Companies Register"
		endnodeout
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
			eEcore:Ident := tRFFXACO
		endnodeout
		nRFFXASE++
	endif
	if (nRFFGNSE = 0 and tRFFGNCO <> EMPTY) then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "FR-INSEE"
		endnodeout
		nodeout Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:CodeListIdentifierCoded := "Other"
		endnodeout
		nodeout Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:CodeListIdentifierCodedOther := "SIREN"
		endnodeout
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
			eEcore:Ident := tRFFGNCO
		endnodeout
		nRFFGNSE++
	endif
	if(bNADSE = FALSE) then
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyID
			bfERROR ("XCBL_ERROR_NAD_SE_MISSING","NAD+SE is missing","NAD", nLINE)
			eEcore:Ident := " "
		endnodeout
		bNADSE := TRUE
	endif
endfunction

function bfCheckSegmentDtm() ! Si les segments DTM+13(g8) (InvoiceDueDate) et DTM+137 (InvoiceIssueDate) ne sont pas présents, on génére InvoiceDueDate et InvoiceIssueDate avec 01/01/1970

	if(not bInvoiceDueDate ) then
		bfERROR ("XCBL_ERROR_DTM_13_g8_MISSING","DTM+13 (g8) is missing","DTM(g8)", nLINE)
		bInvoiceDueDate := TRUE
		nodeout SInvoiceDueDate gGInvoice, gGInvoiceHeader, gGInvoiceDates
			tInvoiceDueDate := "1970-01-01T00:00:00"
			eEInvoiceDueDate := tInvoiceDueDate
		endnodeout
	endif


	if (not bInvoiceIssueDate) then
		bfERROR ("XCBL_ERROR_DTM_137_MISSING","DTM+137 is missing","DTM", nLINE)
		bInvoiceIssueDate := TRUE
		nodeout SInvoiceIssueDate gGInvoice, gGInvoiceHeader
			eEInvoiceIssueDate := "1970-01-01T00:00:00"
		endnodeout
	endif
endfunction

function bfCheckSegmentAlcg15() !
	if (not bPCDWithALCg15 and not bMOAWithALCg15 and tALCHeaderTypeReductionCharge <> EMPTY) then
		bfERROR ("XCBL_ERROR_ALC_g15_PCD_MOA_EMPTY","MOA+8(g15) or PCD+1/2(g15) are missing","ALC(g15)", nLINE)
		bPCDWithALCg15 := TRUE
		bMOAWithALCg15 := TRUE
		nodeout Score:IndicatorCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge
			eEcore:IndicatorCoded  := tALCHeaderTypeReductionCharge
		endnodeout

		nodeout Score:MethodOfHandlingCoded gGInvoice,gGInvoiceHeader,gGInvoiceAllowancesOrCharges,gGcore:AllowOrCharge
			if (tALCHeaderMethodOfHandling = EMPTY) then
				tALCHeaderMethodOfHandling := " "
			endif
			eEcore:MethodOfHandlingCoded := tALCHeaderMethodOfHandling
		endnodeout

        if(tALCHeaderRefID <> EMPTY) then
            nodeout Score:RefID gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
                eEcore:RefID := tALCHeaderRefID
            endnodeout
        endif

		nodeout Score:ListOfDescription gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
			if (tALCHeaderDescription = EMPTY) then
				tALCHeaderDescription := " "
			endif
			eEcore:ListOfDescription := tALCHeaderDescription
		endnodeout

		nodeout Score:ServiceCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
			eEcore:ServiceCoded := tALCHeaderService
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:MonetaryValue
			eEcore:MonetaryAmount  := "-0"
		endnodeout

	endif
endfunction

function bfCheckSegmentAlcg25() !
	if (not bPCDWithALCg25 and not bMOAWithALCg25 and tALCTypeReductionCharge <> EMPTY) then
		bfERROR ("XCBL_ERROR_LINE_ALC_g25_PCD_MOA_EMPTY","MOA+204(g25) or MOA+23(g25) or PCD+1/2(g25) are missing","ALC(g25)", nLINE)
		bPCDWithALCg25 := TRUE
		nodeout Score:IndicatorCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
			eEcore:IndicatorCoded  := tALCTypeReductionCharge
		endnodeout

		nodeout Score:MethodOfHandlingCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
			eEcore:MethodOfHandlingCoded := "Other"
		endnodeout

		nodeout Score:ListOfDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
			gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
			if (tALCDescription = EMPTY) then
				tALCDescription := " "
			endif
			eEcore:ListOfDescription := tALCDescription
		endnodeout

		nodeout Score:ServiceCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
			gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
			eEcore:ServiceCoded := tALCService
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, \
				gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:MonetaryValue
			eEcore:MonetaryAmount  := "-0"
		endnodeout
	endif
endfunction


function bfCheckSegmentBgm()
	!InvoiceNumber
	if (not bBGM) then
		bfERROR ("XCBL_ERROR_BGM_MISSING","No segment BGM","BGM", nLINE)

		!InvoiceNumber
		bInvoiceNumber := TRUE
		nodeout SInvoiceNumber gGInvoice,gGInvoiceHeader,gGInvoiceNumber
			eEInvoiceNumber := " "
		endnodeout

		!InvoiceType
		bInvoiceType := TRUE
		nodeout SInvoiceTypeCoded gGInvoice,gGInvoiceHeader,gGInvoiceType
			eEInvoiceTypeCoded :=  "Other"
		endnodeout
		nodeout SInvoiceTypeCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceType
			eEInvoiceTypeCodedOther :=  " "
		endnodeout

		nodeout SInvoicePurposeCoded gGInvoice, gGInvoiceHeader, gGInvoicePurpose
			eEInvoicePurposeCoded := "Original"
		endnodeout
		bBGM := TRUE
	endif
endfunction


function bfCheckMOA124g48()
	if (nMOA124g48Matching > 1) then
		bfERROR("XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE", "More than one MOA+124 g48","MOA(g48)",nLINE)
	endif
	if (tMOA124g48 = EMPTY) then
		bfERROR("XCBL_ERROR_MOA_124_g48_EMPTY", "MOA+124 (g48) is empty","MOA(g48)",nLINE)
		tMOA124g48 := "-0"
	endif
	nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTotalTaxAmount
		eEcore:MonetaryAmount :=  tMOA124g48
	endnodeout
endfunction

function bfCheckMOA125g48()
	if (nMOA125g48Matching > 1) then
		bfERROR("XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE", "More than one MOA+125 g48","MOA(g48)",nLINE)
	endif
	if (tMOA125g48 = EMPTY) then
		bfERROR("XCBL_ERROR_MOA_125_g48_EMPTY", "MOA+125 (g48) is empty","MOA(g48)",nLINE)
		tMOA125g48 := "-0"
	endif
	nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTaxableValue
		eEcore:MonetaryAmount :=  tMOA125g48
	endnodeout
endfunction

function bfCheckMOA128g48()
	if (nMOA128g48Matching > 1) then
		bfERROR("XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE", "More than one MOA+128 g48","MOA(g48)",nLINE)
	endif
	if (nMOA128g48Matching = 0 or tMOA128g48 = EMPTY) then
		bfERROR("XCBL_ERROR_MOA_128_g48_EMPTY", "MOA+128 (g48) is empty","MOA(g48)",nLINE)
		tMOA128g48 := "-0"
	endif
	nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGInvoiceSubTotal
		eEcore:MonetaryAmount :=  tMOA128g48
	endnodeout
	nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGInvoiceTotal
		eEcore:MonetaryAmount :=  tMOA128g48
	endnodeout
endfunction

function bfCheckMOA124g50()
	if (tTAX5153="VAT") then
		if (nMOA124g50Matching > 1) then
			bfERROR("XCBL_ERROR_MOA_124_g50_MORE_THAN_ONE", "More than one MOA+124 g50","MOA(g50)",nLINE)
		endif
		if (tMOA124g50 = EMPTY) then
			bfERROR("XCBL_ERROR_MOA_124_g50_EMPTY", "MOA+124 (g50) is empty","MOA(g50)",nLINE)
			tMOA124g50 := "-0"
		endif
		nodeout Score:TaxAmount gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxAmount := tMOA124g50
		endnodeout
		nodeout Score:TaxAmountInTaxAccountingCurrency gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxAmountInTaxAccountingCurrency := tMOA124g50
		endnodeout
	endif
endfunction

function bfCheckMOA113g48()
	if (bMOA113 and not bRFFPQ) then
		bfERROR ("XCBL_ERROR_RFFPQ_MISSING","segment RFF+PQ is missing after MOA+113(g48)","RFF+PQ(g48)", nLINE)
		nodeout Score:OtherPaymentInfo gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment
			eEcore:OtherPaymentInfo := " "
		endnodeout
		nodeout Score:PaymentMeanCoded gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment, gGcore:PaymentMean
			eEcore:PaymentMeanCoded := "Other"
		endnodeout
		nodeout Score:PaymentCodedMeanOther gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment, gGcore:PaymentMean
			eEcore:PaymentCodedMeanOther := "Unknow"
		endnodeout
		bRFFPQ := TRUE
	endif
	if (bMOA113 and not bDTM171) then
		bfERROR ("XCBL_ERROR_DTM113_MISSING","segment DTM+171 is missing after MOA+113(g48)","DTM+171", nLINE)
		nodeout Score:PaymentDate gGInvoice, gGInvoiceSummary, gGListOfActualPayment, gGcore:ActualPayment
			eEcore:PaymentDate := "1970-01-01T00:00:00"
		endnodeout
		bDTM171 := TRUE
	endif
endfunction

function bfCompleteFTX()
	if (tPMD <> EMPTY) then
		nodeout Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
			eEcore:PaymentTermCoded := "PenaltyTerms"
		endnodeout
		nodeout Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
			eEcore:PaymentTermDescription := tPMD
		endnodeout
		tPMD:=""
	endif
	if (tFTXSIN <> EMPTY) then
		bFTXSIN := TRUE
		nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxTypeCoded := "ValueAddedTax"
		endnodeout
		nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
		endnodeout
		nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxCategoryCoded := "ExemptFromTax"
		endnodeout
		nodeout Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:ReasonTaxExemptCoded := "Other"
		endnodeout
		nodeout Score:ReasonTaxExemptCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:ReasonTaxExemptCodedOther := tFTXSIN
		endnodeout
		nodeout Score:TaxTreatmentCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxTreatmentCoded := "NoTaxApplies"
		endnodeout
		tFTXSIN := ""
	endif
endfunction



function bfCompleteRFF()
	if (tRFF1153AAK <> EMPTY) then
		!RFF without DTM
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "DespatchAdviceNumber"
		endnodeout
		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := tRFF1153AAK
		endnodeout
		tRFF1153AAK := ""
	endif
	if (tRFF1153PL <> EMPTY) then
		!RFF without DTM
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "PriceListNumber"
		endnodeout
		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := tRFF1153PL
		endnodeout
		tRFF1153PL := ""
	endif
	if (tRFF1153ADE <> EMPTY) then
		!RFF without DTM
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "AccountNumber"
		endnodeout
		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := tRFF1153ADE
		endnodeout
		tRFF1153ADE := ""
	endif
endfunction

function bfTableInit()

	remove(taDevises)
	taDevises["AFA"] := "OK"
	taDevises["DZD"] := "OK"
	taDevises["ADF"] := "OK"
	taDevises["ADP"] := "OK"
	taDevises["ARP"] := "OK"
	taDevises["ARS"] := "OK"
	taDevises["ARA"] := "OK"
	taDevises["AMD"] := "OK"
	taDevises["AWF"] := "OK"
	taDevises["AUD"] := "OK"
	taDevises["ATS"] := "OK"
	taDevises["AZM"] := "OK"
	taDevises["BSD"] := "OK"
	taDevises["BHD"] := "OK"
	taDevises["BDT"] := "OK"
	taDevises["BBD"] := "OK"
	taDevises["BYB"] := "OK"
	taDevises["BEF"] := "OK"
	taDevises["BZD"] := "OK"
	taDevises["BMD"] := "OK"
	taDevises["BTN"] := "OK"
	taDevises["BOB"] := "OK"
	taDevises["BAK"] := "OK"
	taDevises["BWP"] := "OK"
	taDevises["BRL"] := "OK"
	taDevises["BND"] := "OK"
	taDevises["BGL"] := "OK"
	taDevises["BIF"] := "OK"
	taDevises["KHR"] := "OK"
	taDevises["CAD"] := "OK"
	taDevises["CVE"] := "OK"
	taDevises["KYD"] := "OK"
	taDevises["XOF"] := "OK"
	taDevises["XAF"] := "OK"
	taDevises["XPF"] := "OK"
	taDevises["CLP"] := "OK"
	taDevises["CLF"] := "OK"
	taDevises["CNY"] := "OK"
	taDevises["COP"] := "OK"
	taDevises["KMF"] := "OK"
	taDevises["CRC"] := "OK"
	taDevises["HRK"] := "OK"
	taDevises["CUP"] := "OK"
	taDevises["CYP"] := "OK"
	taDevises["CZK"] := "OK"
	taDevises["DKK"] := "OK"
	taDevises["DEM"] := "OK"
	taDevises["BAD"] := "OK"
	taDevises["DJF"] := "OK"
	taDevises["DOP"] := "OK"
	taDevises["XCD"] := "OK"
	taDevises["ECS"] := "OK"
	taDevises["EGP"] := "OK"
	taDevises["SVC"] := "OK"
	taDevises["ERN"] := "OK"
	taDevises["EEK"] := "OK"
	taDevises["ETB"] := "OK"
	taDevises["EUR"] := "OK"
	taDevises["XEU"] := "OK"
	taDevises["FKP"] := "OK"
	taDevises["FJD"] := "OK"
	taDevises["ZAL"] := "OK"
	taDevises["FIM"] := "OK"
	taDevises["FRF"] := "OK"
	taDevises["GMD"] := "OK"
	taDevises["GEL"] := "OK"
	taDevises["GHC"] := "OK"
	taDevises["GIP"] := "OK"
	taDevises["XAU"] := "OK"
	taDevises["GRD"] := "OK"
	taDevises["GTQ"] := "OK"
	taDevises["GNF"] := "OK"
	taDevises["GWP"] := "OK"
	taDevises["GYD"] := "OK"
	taDevises["HTG"] := "OK"
	taDevises["HNL"] := "OK"
	taDevises["HKD"] := "OK"
	taDevises["HUF"] := "OK"
	taDevises["ISK"] := "OK"
	taDevises["INR"] := "OK"
	taDevises["IDR"] := "OK"
	taDevises["IRA"] := "OK"
	taDevises["IRR"] := "OK"
	taDevises["IQD"] := "OK"
	taDevises["IEP"] := "OK"
	taDevises["ILS"] := "OK"
	taDevises["ITL"] := "OK"
	taDevises["JMD"] := "OK"
	taDevises["JPY"] := "OK"
	taDevises["JOD"] := "OK"
	taDevises["KZT"] := "OK"
	taDevises["KES"] := "OK"
	taDevises["KWD"] := "OK"
	taDevises["AOR"] := "OK"
	taDevises["KGS"] := "OK"
	taDevises["LAK"] := "OK"
	taDevises["LVL"] := "OK"
	taDevises["LBP"] := "OK"
	taDevises["ALL"] := "OK"
	taDevises["LSL"] := "OK"
	taDevises["LRD"] := "OK"
	taDevises["LYD"] := "OK"
	taDevises["LTL"] := "OK"
	taDevises["LUF"] := "OK"
	taDevises["MOP"] := "OK"
	taDevises["MKD"] := "OK"
	taDevises["MGF"] := "OK"
	taDevises["MWK"] := "OK"
	taDevises["MYR"] := "OK"
	taDevises["MVR"] := "OK"
	taDevises["MTL"] := "OK"
	taDevises["MRO"] := "OK"
	taDevises["MUR"] := "OK"
	taDevises["MXN"] := "OK"
	taDevises["MDL"] := "OK"
	taDevises["MNT"] := "OK"
	taDevises["MAD"] := "OK"
	taDevises["MZM"] := "OK"
	taDevises["BOV"] := "OK"
	taDevises["MMK"] := "OK"
	taDevises["NAD"] := "OK"
	taDevises["NPR"] := "OK"
	taDevises["ANG"] := "OK"
	taDevises["AWG"] := "OK"
	taDevises["NLG"] := "OK"
	taDevises["AON"] := "OK"
	taDevises["TWD"] := "OK"
	taDevises["ZRN"] := "OK"
	taDevises["NZD"] := "OK"
	taDevises["NIO"] := "OK"
	taDevises["NGN"] := "OK"
	taDevises["KPW"] := "OK"
	taDevises["NOK"] := "OK"
	taDevises["PKR"] := "OK"
	taDevises["PAB"] := "OK"
	taDevises["PGK"] := "OK"
	taDevises["PYG"] := "OK"
	taDevises["PEN"] := "OK"
	taDevises["PHP"] := "OK"
	taDevises["PLN"] := "OK"
	taDevises["PLZ"] := "OK"
	taDevises["PTE"] := "OK"
	taDevises["GBP"] := "OK"
	taDevises["QAR"] := "OK"
	taDevises["OMR"] := "OK"
	taDevises["ROL"] := "OK"
	taDevises["RUR"] := "OK"
	taDevises["RWF"] := "OK"
	taDevises["WST"] := "OK"
	taDevises["STD"] := "OK"
	taDevises["SAR"] := "OK"
	taDevises["SBL"] := "OK"
	taDevises["SCR"] := "OK"
	taDevises["SLL"] := "OK"
	taDevises["SGD"] := "OK"
	taDevises["SKK"] := "OK"
	taDevises["SIT"] := "OK"
	taDevises["SBD"] := "OK"
	taDevises["SOS"] := "OK"
	taDevises["ZAR"] := "OK"
	taDevises["KRW"] := "OK"
	taDevises["ESP"] := "OK"
	taDevises["LKR"] := "OK"
	taDevises["SHP"] := "OK"
	taDevises["SDA"] := "OK"
	taDevises["SDD"] := "OK"
	taDevises["SDP"] := "OK"
	taDevises["SRG"] := "OK"
	taDevises["SZL"] := "OK"
	taDevises["SEK"] := "OK"
	taDevises["CHF"] := "OK"
	taDevises["SYP"] := "OK"
	taDevises["TJR"] := "OK"
	taDevises["TZS"] := "OK"
	taDevises["THB"] := "OK"
	taDevises["TPE"] := "OK"
	taDevises["TOP"] := "OK"
	taDevises["TTD"] := "OK"
	taDevises["TND"] := "OK"
	taDevises["TRL"] := "OK"
	taDevises["TMM"] := "OK"
	taDevises["USD"] := "OK"
	taDevises["AED"] := "OK"
	taDevises["UGX"] := "OK"
	taDevises["UAH"] := "OK"
	taDevises["ECV"] := "OK"
	taDevises["UYU"] := "OK"
	taDevises["USN"] := "OK"
	taDevises["UZS"] := "OK"
	taDevises["VUV"] := "OK"
	taDevises["VEB"] := "OK"
	taDevises["VND"] := "OK"
	taDevises["YER"] := "OK"
	taDevises["YUN"] := "OK"
	taDevises["ZMK"] := "OK"
	taDevises["ZWD"] := "OK"
	taDevises["AFN"] := "OK";
    taDevises["AOA"] := "OK";
    taDevises["AZN"] := "OK";
    taDevises["BYR"] := "OK";
    taDevises["BAM"] := "OK";
    taDevises["BGN"] := "OK";
    taDevises["COU"] := "OK";
    taDevises["CDF"] := "OK";
    taDevises["CUC"] := "OK";
    taDevises["XDR"] := "OK";
    taDevises["GHS"] := "OK";
    taDevises["MGA"] := "OK";
    taDevises["MXV"] := "OK";
    taDevises["MZN"] := "OK";
    taDevises["XUA"] := "OK";
    taDevises["RON"] := "OK";
    taDevises["RUB"] := "OK";
    taDevises["RSD"] := "OK";
    taDevises["XSU"] := "OK";
    taDevises["SDG"] := "OK";
    taDevises["SSP"] := "OK";
    taDevises["CHE"] := "OK";
    taDevises["CHW"] := "OK";
    taDevises["SRD"] := "OK";
    taDevises["TJS"] := "OK";
    taDevises["TMT"] := "OK";
    taDevises["TRY"] := "OK";
    taDevises["UYI"] := "OK";
    taDevises["VEF"] := "OK";
    taDevises["ZMW"] := "OK";
    taDevises["ZWL"] := "OK";

	remove(taCountry)
	taCountry["AE"] := "OK"
	taCountry["AF"] := "OK"
	taCountry["AG"] := "OK"
	taCountry["AI"] := "OK"
	taCountry["AL"] := "OK"
	taCountry["AM"] := "OK"
	taCountry["AN"] := "OK"
	taCountry["AO"] := "OK"
	taCountry["AQ"] := "OK"
	taCountry["AR"] := "OK"
	taCountry["AS"] := "OK"
	taCountry["AT"] := "OK"
	taCountry["AU"] := "OK"
	taCountry["AW"] := "OK"
	taCountry["AZ"] := "OK"
	taCountry["BA"] := "OK"
	taCountry["BB"] := "OK"
	taCountry["BD"] := "OK"
	taCountry["BE"] := "OK"
	taCountry["BF"] := "OK"
	taCountry["BG"] := "OK"
	taCountry["BH"] := "OK"
	taCountry["BI"] := "OK"
	taCountry["BJ"] := "OK"
	taCountry["BM"] := "OK"
	taCountry["BN"] := "OK"
	taCountry["BO"] := "OK"
	taCountry["BR"] := "OK"
	taCountry["BS"] := "OK"
	taCountry["BT"] := "OK"
	taCountry["BU"] := "OK"
	taCountry["BW"] := "OK"
	taCountry["BY"] := "OK"
	taCountry["BZ"] := "OK"
	taCountry["CA"] := "OK"
	taCountry["CC"] := "OK"
	taCountry["CD"] := "OK"
	taCountry["CF"] := "OK"
	taCountry["CG"] := "OK"
	taCountry["CH"] := "OK"
	taCountry["CI"] := "OK"
	taCountry["CK"] := "OK"
	taCountry["CL"] := "OK"
	taCountry["CM"] := "OK"
	taCountry["CN"] := "OK"
	taCountry["CO"] := "OK"
	taCountry["CR"] := "OK"
	taCountry["CS"] := "OK"
	taCountry["CU"] := "OK"
	taCountry["CV"] := "OK"
	taCountry["CX"] := "OK"
	taCountry["CY"] := "OK"
	taCountry["CZ"] := "OK"
	taCountry["DE"] := "OK"
	taCountry["DJ"] := "OK"
	taCountry["DK"] := "OK"
	taCountry["DM"] := "OK"
	taCountry["DO"] := "OK"
	taCountry["DZ"] := "OK"
	taCountry["EC"] := "OK"
	taCountry["EE"] := "OK"
	taCountry["EG"] := "OK"
	taCountry["EH"] := "OK"
	taCountry["ER"] := "OK"
	taCountry["ES"] := "OK"
	taCountry["ET"] := "OK"
	taCountry["FI"] := "OK"
	taCountry["FJ"] := "OK"
	taCountry["FK"] := "OK"
	taCountry["FM"] := "OK"
	taCountry["FO"] := "OK"
	taCountry["FR"] := "OK"
	taCountry["GA"] := "OK"
	taCountry["GB"] := "OK"
	taCountry["GD"] := "OK"
	taCountry["GE"] := "OK"
	taCountry["GF"] := "OK"
	taCountry["GH"] := "OK"
	taCountry["GI"] := "OK"
	taCountry["GL"] := "OK"
	taCountry["GM"] := "OK"
	taCountry["GN"] := "OK"
	taCountry["GP"] := "OK"
	taCountry["GQ"] := "OK"
	taCountry["GR"] := "OK"
	taCountry["GS"] := "OK"
	taCountry["GT"] := "OK"
	taCountry["GU"] := "OK"
	taCountry["GW"] := "OK"
	taCountry["GY"] := "OK"
	taCountry["HK"] := "OK"
	taCountry["HN"] := "OK"
	taCountry["HR"] := "OK"
	taCountry["HT"] := "OK"
	taCountry["HU"] := "OK"
	taCountry["ID"] := "OK"
	taCountry["IE"] := "OK"
	taCountry["IL"] := "OK"
	taCountry["IN"] := "OK"
	taCountry["IO"] := "OK"
	taCountry["IQ"] := "OK"
	taCountry["IR"] := "OK"
	taCountry["IS"] := "OK"
	taCountry["IT"] := "OK"
	taCountry["JM"] := "OK"
	taCountry["JO"] := "OK"
	taCountry["JP"] := "OK"
	taCountry["KE"] := "OK"
	taCountry["KG"] := "OK"
	taCountry["KH"] := "OK"
	taCountry["KI"] := "OK"
	taCountry["KM"] := "OK"
	taCountry["KN"] := "OK"
	taCountry["KP"] := "OK"
	taCountry["KR"] := "OK"
	taCountry["KW"] := "OK"
	taCountry["KY"] := "OK"
	taCountry["KZ"] := "OK"
	taCountry["LA"] := "OK"
	taCountry["LB"] := "OK"
	taCountry["LC"] := "OK"
	taCountry["LI"] := "OK"
	taCountry["LK"] := "OK"
	taCountry["LR"] := "OK"
	taCountry["LS"] := "OK"
	taCountry["LT"] := "OK"
	taCountry["LU"] := "OK"
	taCountry["LV"] := "OK"
	taCountry["LY"] := "OK"
	taCountry["MA"] := "OK"
	taCountry["MC"] := "OK"
	taCountry["MD"] := "OK"
	taCountry["MG"] := "OK"
	taCountry["MH"] := "OK"
	taCountry["MK"] := "OK"
	taCountry["ML"] := "OK"
	taCountry["MM"] := "OK"
	taCountry["MN"] := "OK"
	taCountry["MO"] := "OK"
	taCountry["MP"] := "OK"
	taCountry["MQ"] := "OK"
	taCountry["MR"] := "OK"
	taCountry["MS"] := "OK"
	taCountry["MT"] := "OK"
	taCountry["MU"] := "OK"
	taCountry["MV"] := "OK"
	taCountry["MW"] := "OK"
	taCountry["MX"] := "OK"
	taCountry["MY"] := "OK"
	taCountry["MZ"] := "OK"
	taCountry["NA"] := "OK"
	taCountry["NC"] := "OK"
	taCountry["NE"] := "OK"
	taCountry["NF"] := "OK"
	taCountry["NG"] := "OK"
	taCountry["NI"] := "OK"
	taCountry["NL"] := "OK"
	taCountry["NO"] := "OK"
	taCountry["NP"] := "OK"
	taCountry["NR"] := "OK"
	taCountry["NU"] := "OK"
	taCountry["NZ"] := "OK"
	taCountry["OM"] := "OK"
	taCountry["PA"] := "OK"
	taCountry["PE"] := "OK"
	taCountry["PF"] := "OK"
	taCountry["PG"] := "OK"
	taCountry["PH"] := "OK"
	taCountry["PK"] := "OK"
	taCountry["PL"] := "OK"
	taCountry["PM"] := "OK"
	taCountry["PN"] := "OK"
	taCountry["PR"] := "OK"
	taCountry["PT"] := "OK"
	taCountry["PW"] := "OK"
	taCountry["PY"] := "OK"
	taCountry["QA"] := "OK"
	taCountry["RE"] := "OK"
	taCountry["RO"] := "OK"
	taCountry["RU"] := "OK"
	taCountry["RW"] := "OK"
	taCountry["SA"] := "OK"
	taCountry["SB"] := "OK"
	taCountry["SC"] := "OK"
	taCountry["SD"] := "OK"
	taCountry["SE"] := "OK"
	taCountry["SG"] := "OK"
	taCountry["SH"] := "OK"
	taCountry["SI"] := "OK"
	taCountry["SJ"] := "OK"
	taCountry["SK"] := "OK"
	taCountry["SL"] := "OK"
	taCountry["SM"] := "OK"
	taCountry["SN"] := "OK"
	taCountry["SO"] := "OK"
	taCountry["SR"] := "OK"
	taCountry["ST"] := "OK"
	taCountry["SV"] := "OK"
	taCountry["SY"] := "OK"
	taCountry["SZ"] := "OK"
	taCountry["TC"] := "OK"
	taCountry["TD"] := "OK"
	taCountry["TF"] := "OK"
	taCountry["TG"] := "OK"
	taCountry["TH"] := "OK"
	taCountry["TJ"] := "OK"
	taCountry["TM"] := "OK"
	taCountry["TN"] := "OK"
	taCountry["TO"] := "OK"
	taCountry["TP"] := "OK"
	taCountry["TR"] := "OK"
	taCountry["TT"] := "OK"
	taCountry["TV"] := "OK"
	taCountry["TW"] := "OK"
	taCountry["TZ"] := "OK"
	taCountry["UA"] := "OK"
	taCountry["UG"] := "OK"
	taCountry["UM"] := "OK"
	taCountry["US"] := "OK"
	taCountry["UY"] := "OK"
	taCountry["UZ"] := "OK"
	taCountry["VA"] := "OK"
	taCountry["VC"] := "OK"
	taCountry["VE"] := "OK"
	taCountry["VG"] := "OK"
	taCountry["VI"] := "OK"
	taCountry["VN"] := "OK"
	taCountry["VU"] := "OK"
	taCountry["WF"] := "OK"
	taCountry["WS"] := "OK"
	taCountry["YE"] := "OK"
	taCountry["YT"] := "OK"
	taCountry["YU"] := "OK"
	taCountry["ZA"] := "OK"
	taCountry["ZM"] := "OK"
	taCountry["ZW"] := "OK"
	taCountry["AD"] := "OK"
    taCountry["AX"] := "OK"
    taCountry["BL"] := "OK"
    taCountry["BQ"] := "OK"
    taCountry["BV"] := "OK"
    taCountry["CW"] := "OK"
    taCountry["GG"] := "OK"
    taCountry["HM"] := "OK"
    taCountry["IM"] := "OK"
    taCountry["JE"] := "OK"
    taCountry["ME"] := "OK"
    taCountry["MF"] := "OK"
    taCountry["PS"] := "OK"
    taCountry["RS"] := "OK"
    taCountry["SS"] := "OK"
    taCountry["SX"] := "OK"
    taCountry["TK"] := "OK"
    taCountry["TL"] := "OK"
    taCountry["XI"] := "OK"
    taCountry["XU"] := "OK"

endfunction

!--------------------------------------------------------------------------
! Nom         : bfLOG
! Arguments   : VARIABLE
! Retour      :
! Description : LOG une trace dans la sortie erreur
!--------------------------------------------------------------------------
function bfLOG(tLOG_label,tLOG_value)

!   En attente de correction du bug AIO-7799
!	if(LOGLEVEL=2) then
!		log("* ",tLOG_label:50.50," : ",tLOG_value,NL)
!	endif
endfunction

!--------------------------------------------------------------------------
! Nom         : bfERROR
! Arguments   : VARIABLE
! Retour      :
! Description : trace l'erreur dans le fichier de trace
!--------------------------------------------------------------------------
function bfERROR(tREG_NO, tDescription, tSegmentName, nLineNumber)

    tREG_NO_ := tREG_NO
    tDescription_ := tDescription
    tSegmentName_ := tSegmentName
    nLineNumber_ := nLineNumber

    if tREG_NO_ <> EMPTY then
		nERROR_COUNT++
		tMsg := build(tREG_NO_ ,";", tSegmentName_, ";L", nLineNumber_, ";", tDescription_)
		log(tMsg, NL)
    endif
endfunction

!--------------------------------------------------------------------------
! Nom         : bfEND
! Arguments   : aucun
! Retour      : aucun
! Description : affiche un message de fin de programme
!--------------------------------------------------------------------------
function bfEND()

!   En attente de correction du bug AIO-7799
!	if(LOGLEVEL = 2) then
!		bfLOG(build("--- ",PROG_INFO," ends."),NOW)
!	endif
endfunction

!--------------------------------------------------------------------------
! Nom         : bfEXIT
! Arguments   : aucun
! Retour      : aucun
! Description : gestion de la sortie "brutale" des programmes
!--------------------------------------------------------------------------
function bfEXIT(nEXIT_code)
	bfEND()
	exit(nEXIT_code)
endfunction

!--------------------------------------------------------------------------
! Nom         : bfWARNING
! Arguments   : VARIABLE
! Retour      :
! Description : trace l'erreur dans le fichier de trace
!--------------------------------------------------------------------------
function bfWARNING(tREG_NO, tERROR_TYPE, tComplement, tSegmentName, nLineNumber)

    tREG_NO_     := tREG_NO
    tLang_ 	 := tLangWBA
    tERROR_TYPE_ := tERROR_TYPE
    tComplement_ := tComplement
    tSegmentName_ := tSegmentName
    nLineNumber_ := nLineNumber

    if tREG_NO_ <> EMPTY then

		nERROR_COUNT++
		tLog_ := tType_

		tMsg := ""

		tMsg := build(tMsg,tREG_NO_ ,";")

		if tSegmentName_ <> EMPTY then
			tMsg := build(tMsg,tSegmentName_,";")
		endif

		if nLineNumber_ <> 0 then
			tMsg := build(tMsg,"L",nLineNumber_,";")
		endif

		if tComplement_ <> EMPTY then
			tMsg := build(tMsg,tComplement_," ")
		endif

		tMsg := build(tMsg, tType_)

		log("= ", tMsg, NL)
    endif
endfunction

!--------------------------------------------------------------------------
! Nom         : bfIsNUMBER
! Arguments   : tText_
! Retour      : boolean
! Description : vérifie si la donnée est numérique
!--------------------------------------------------------------------------
function bfIsNUMBER(tText)

    tText_ := build(tText)

    if valid(NUMERIC, tText_ ) = TRUE then
		bNumber_ := TRUE
    else
        bNumber_ := FALSE
    endif
    return bNumber_
endfunction

!--------------------------------------------------------------------------
! Nom         : bfIsValidDate
! Arguments   : tText
! Retour      : boolean
! Description : vérifie si la donnée est une date correcte
!--------------------------------------------------------------------------
function bfIsValidDate(tText)

    tText_ := build(tText)

    if valid(DATE, tText_ ) = TRUE then
		!on tente de convertir avec en timestamp et de reconvertir pour vérifier que les dates sont bien égales.
		!tDate1_ := tText
		!tDate2_ := time(tDate1_, "%a") ! timestamp
		!tDate2_ := time(tDate2_, "%Y%m%d")
		!if (tDate1_ = tDate2_) then
		!	return TRUE
		!else
		!	return FALSE
		!endif
		tYear_ := substr(tText_,1,4)
		tMonth_ := substr(tText_,5,2)
		tDay_ := substr(tText_,7,2)
		bfLOG("Date validation",build("Year=",tYear_,",Month=",tMonth_,",Day=",tDay_))
		switch (tMonth_)
			case "01":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=01",",Day=",number(tDay_)))
					return FALSE
				endif
			case "02":
				bfLOG("Date validation",build("Month=02",",Day=",number(tDay_)))
				if(number(tDay_) > 29) then
					return FALSE
				endif
				if (number(tDay_) = 29) then
					bBissextile := FALSE
					if(index(build(number(tYear_)/4),".") = 0) then
						if(index(build(number(tYear_)/400),".") = 0 ) then
							bBissextile := TRUE
						else
							if(index(build(number(tYear_)/100),".") = 0) then
								bBissextile := FALSE
							else
								bBissextile := TRUE
							endif
						endif
					endif
					if(bBissextile = FALSE) then
						return FALSE
					endif
				endif
			case "03":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=03",",Day=",number(tDay_)))
					return FALSE
				endif
			case "04":
				if(number(tDay_) > 30) then
					bfLOG("Date validation",build("Month=04",",Day=",number(tDay_)))
					return FALSE
				endif
			case "05":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=05",",Day=",number(tDay_)))
					return FALSE
				endif
			case "06":
				if(number(tDay_) > 30) then
					bfLOG("Date validation",build("Month=06",",Day=",number(tDay_)))
					return FALSE
				endif
			case "07":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=07",",Day=",number(tDay_)))
					return FALSE
				endif
			case "08":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=08",",Day=",number(tDay_)))
					return FALSE
				endif
			case "09":
				if(number(tDay_) > 30) then
					bfLOG("Date validation",build("Month=09",",Day=",number(tDay_)))
					return FALSE
				endif
			case "10":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=10",",Day=",number(tDay_)))
					return FALSE
				endif
			case "11":
				if(number(tDay_) > 30) then
					bfLOG("Date validation",build("Month=11",",Day=",number(tDay_)))
					return FALSE
				endif
			case "12":
				if(number(tDay_) > 31) then
					bfLOG("Date validation",build("Month=12",",Day=",number(tDay_)))
					return FALSE
				endif
			default:
				return FALSE
		endswitch
    else
        return FALSE
    endif
    return TRUE
endfunction

