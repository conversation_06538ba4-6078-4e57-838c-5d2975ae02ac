	!%@(#)  EDF_UBL_TO_XCBL.rte                                 	   modif:13/11/2017
	!==============================================================================
	!  13/11/2018  MVI  1.0 Creation
	!  29/08/2019  LHA  Première adaptation du RTE EDF_UBL_TO_XCBL de MVI pour le projet EURINV
	!  03/12/2019  LHA  V2.0
	! ==============================================================================
	! Definition du message en entree
	schema "UBL/V2.1/maindoc/UBL-Invoice-2.1.xsd" validating,receiving
	! Definition du message en sortie
	schema "XML/xcbl/v4_0/financial/v1_0/financial.xsd" validating, building

	#include "generix_fct_V1_3.inc"  

	begin
		! Load transcodification file
		tFile := build(sHOME, "/tabledetransco.txt")
		load(tFile, taTransco)

		log("Début du traitement",NL)
		nLin := 0
		nTax := 0
		nTaxTotal := 0
		nTaxSubTotal := 0
		nHeaderNotes := 0
		nAddDocRef := 0
		nLinAll := 0
		nLinNotes := 0
		nSuppParty := 0
		nSuppTaxScheme := 0
		nCustParty := 0
		nCustTaxScheme := 0

		
	endbegin

	!============================================

	nodein Scbc:CustomizationID gGInvoice
		taENTETE["CustomizationID"] := eEcbc:CustomizationID
	endnodein

	nodein Scbc:ProfileID gGInvoice
		taENTETE["ProfileID"] := eEcbc:ProfileID
	endnodein

	nodein Scbc:ID gGInvoice     ! M 1/1 
		taENTETE["ID"] := eEcbc:ID   ! M  
		tNumberInvoic := eEcbc:ID   ! M  
	endnodein

	nodein Scbc:IssueDate gGInvoice     ! M 1/1 
		taENTETE["IssueDate"] := eEcbc:IssueDate      ! M  
		tDateInvoice := eEcbc:IssueDate   ! M  
	endnodein

	nodein Scbc:DueDate gGInvoice
		taENTETE["DueDate"] := eEcbc:DueDate
	endnodein

	nodein Scbc:InvoiceTypeCode gGInvoice     ! C 1/1 
		taENTETE["InvoiceTypeCode"] := eEcbc:InvoiceTypeCode   ! C  
	endnodein

	!Can be several times!
	nodein Scbc:Note gGInvoice
		nHeaderNotes++
		taHeaderNotes[build("Note", nHeaderNotes)] := eEcbc:Note   ! C  
	endnodein

	nodein Scbc:TaxPointDate gGInvoice
		taENTETE["TaxPointDate"] := eEcbc:TaxPointDate   ! C  
	endnodein

	nodein Scbc:DocumentCurrencyCode gGInvoice     ! C 1/1 
		taENTETE["DocumentCurrencyCode"] := eEcbc:DocumentCurrencyCode   ! C  
		tCurrency := eEcbc:DocumentCurrencyCode   ! C  
	endnodein

	nodein Scbc:TaxCurrencyCode gGInvoice
		taENTETE["TaxCurrencyCode"] := eEcbc:TaxCurrencyCode
	endnodein

	nodein Scbc:AccountingCost gGInvoice
		taENTETE["AccountingCost"] := eEcbc:AccountingCost
	endnodein

	nodein Scbc:BuyerReference gGInvoice
		taENTETE["BuyerReference"] := eEcbc:BuyerReference
	endnodein

	nodein Scbc:StartDate gGInvoice, gGcac:InvoicePeriod
		taENTETE["StartDate"] := eEcbc:StartDate
	endnodein

	nodein Scbc:EndDate gGInvoice, gGcac:InvoicePeriod
		taENTETE["EndDate"] := eEcbc:EndDate
	endnodein

	nodein Scbc:DescriptionCode gGInvoice, gGcac:InvoicePeriod
		taENTETE["DescriptionCode"] := eEcbc:DescriptionCode
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:OrderReference     ! M 1/1 
		taENTETE["OrderReference_DocumentCurrencyCode"] := eEcbc:ID   ! M  
	endnodein

	nodein Scbc:SalesOrderID gGInvoice, gGcac:OrderReference
		taENTETE["SalesOrderID"] := eEcbc:SalesOrderID
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:BillingReference,gGcac:InvoiceDocumentReference     ! M 1/1 
		taENTETE["BillingReferenceID"] := eEcbc:ID   ! M  
	endnodein

	nodein Scbc:IssueDate gGInvoice, gGcac:BillingReference, gGcac:InvoiceDocumentReference
		taENTETE["BillingReferenceIssueDate"] := eEcbc:IssueDate
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:DespatchDocumentReference
		taENTETE["ASN_ID"] := eEcbc:ID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:ReceiptDocumentReference
		taENTETE["ReceiptDocumentReference"] := eEcbc:ID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:OriginatorDocumentReference
		taENTETE["OriginatorDocumentReference"] := eEcbc:ID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:ContractDocumentReference
		taENTETE["Contract_ID"] := eEcbc:ID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AdditionalDocumentReference
		nAddDocRef++
		taAdditionalDocumentReference[build("ID", nAddDocRef)] := eEcbc:ID
		taAdditionalDocumentReference[build("IDSchemeID", nAddDocRef)] := eAschemeID
	endnodein

	nodein Scbc:DocumentDescription gGInvoice, gGcac:AdditionalDocumentReference
		taAdditionalDocumentReference[build("DocumentDescription", nAddDocRef)] := eEcbc:DocumentDescription
	endnodein
		
	nodein Scbc:URI gGInvoice, gGcac:AdditionalDocumentReference, gGcac:Attachment, gGcac:ExternalReference
		taAdditionalDocumentReference[build("URI", nAddDocRef)] := eEcbc:URI
	endnodein

	nodein Scbc:EmbeddedDocumentBinaryObject gGInvoice, gGcac:AdditionalDocumentReference, gGcac:Attachment
		taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObject", nAddDocRef)] := eEcbc:EmbeddedDocumentBinaryObject
		taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObjectFileName", nAddDocRef)] := eAfilename
		taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObjectmimeCode", nAddDocRef)] := eAmimeCode
	endnodein
		
	nodein Scbc:DocumentTypeCode gGInvoice, gGcac:AdditionalDocumentReference
		taAdditionalDocumentReference[build("DocumentTypeCode", nAddDocRef)] := eEcbc:DocumentTypeCode
	endnodein
		
	nodein Scbc:ID gGInvoice, gGcac:ProjectReference
		taENTETE["ProjectReference"] := eEcbc:ID
	endnodein











	! Supplier - Seller


	!Utilisé comme identifiant du vendeur
	nodein Scbc:RegistrationName gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["SUPP_Name"] := eEcbc:RegistrationName
	endnodein

	nodein Scbc:EndpointID gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party
		taENTETE["SUPP_EndpointID"] := eEcbc:EndpointID
		taENTETE["SUPP_EndpointID_SchemeID"] := eAschemeID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyIdentification
		nSuppParty++
		taSuppParty[build("SUPP_ID_PartyIdentification", nSuppParty)] := eEcbc:ID
		taSuppParty[build("SUPP_ID_PartyIdentification_SchemeID", nSuppParty)] := eAschemeID
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyName
		taENTETE["SUPP_Party_Identification_Name"] := eEcbc:Name
	endnodein

	nodein Scbc:StreetName gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["SUPP_StreetName"] := eEcbc:StreetName   ! C  
	endnodein

	nodein Scbc:AdditionalStreetName gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["SUPP_AdditionalStreetName"] := eEcbc:AdditionalStreetName   ! C  
	endnodein

	nodein Scbc:CityName gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["SUPP_CityName"] := eEcbc:CityName   ! C  
	endnodein

	nodein Scbc:PostalZone gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["SUPP_PostalZone"] := eEcbc:PostalZone   ! C  
	endnodein

	nodein Scbc:CountrySubentity gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PostalAddress
		taENTETE["SUPP_Country_Subentity"] := eEcbc:CountrySubentity
	endnodein

	nodein Scbc:Line gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PostalAddress, gGcac:AddressLine
		taENTETE["SUPP_AdressLine"] := eEcbc:Line
	endnodein

	nodein Scbc:IdentificationCode gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PostalAddress, gGcac:Country
		taENTETE["SUPP_IdentificationCode"] := eEcbc:IdentificationCode
	endnodein

	nodein Scbc:RegistrationName gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["SUPP_PartyLegalEntity_RegistrationName"] := eEcbc:RegistrationName
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["SUPP_PartyLegalEntity_CompanyID"] := eEcbc:CompanyID
	endnodein

	nodein Scbc:CompanyLegalForm gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["SUPP_PartyLegalEntity_CompanyLegalForm"] := eEcbc:CompanyLegalForm
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:Contact
		taENTETE["SUPP_Party_Contact_Name"] := eEcbc:Name
	endnodein

	nodein Scbc:Telephone gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:Contact
		taENTETE["SUPP_Party_Contact_Telephone"] := eEcbc:Telephone
	endnodein

	nodein Scbc:ElectronicMail gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:Contact
		taENTETE["SUPP_Party_Contact_ElectronicMail"] := eEcbc:ElectronicMail
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyTaxScheme
		nSuppTaxScheme++
		taSUPPTaxScheme[build("SUPP_PartyTaxScheme_CompanyID", nSuppTaxScheme)] := eEcbc:CompanyID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyTaxScheme, gGcac:TaxScheme
		taSUPPTaxScheme[build("SUPP_PartyTaxScheme_ID", nSuppTaxScheme)] := eEcbc:ID
	endnodein

	nodein Scbc:CustomerAssignedAccountID gGInvoice,gGcac:AccountingSupplierParty     ! C 1/1 
		taENTETE["SUPP_CustomerAssignedAccountID"]:= eEcbc:CustomerAssignedAccountID
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:AccountingSupplierParty, gGcac:Party, gGcac:PartyIdentification
		taENTETE["SUPP_PartyLegalEntity"] := eEcbc:ID
	endnodein

	nodein Scbc:CompanyID gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PartyTaxScheme     ! C 1/1 
		taENTETE["SUPP_CompanyID"] := eEcbc:CompanyID   ! C  
		tSellerId := eEcbc:CompanyID   ! C  
	endnodein

	nodein Scbc:CompanyID gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PartyLegalEntity     ! C 1/1 
		taENTETE["SUPP_PartyLegalEntity_CompanyID"] := eEcbc:CompanyID   ! C  
	endnodein

	nodein Scbc:IdentificationCode gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PartyLegalEntity,gGcac:RegistrationAddress,gGcac:Country     ! C 1/1 
		taENTETE["SUPP_IdentificationCode"] :=  eEcbc:IdentificationCode   ! C  
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:AccountingSupplierParty,gGcac:Party,gGcac:PartyLegalEntity,gGcac:CorporateRegistrationScheme     ! C 1/1 
		taENTETE["SUPP_ID"] :=  eEcbc:ID   ! C  
	endnodein





	! Buyer - Customer


	!Utilisé comme identifiant de l'acheteur
	nodein Scbc:RegistrationName gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["CUST_Name"] := eEcbc:RegistrationName
	endnodein

	nodein Scbc:EndpointID gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party
		taENTETE["CUST_EndpointID"] := eEcbc:EndpointID
		taENTETE["CUST_EndpointID_SchemeID"] := eAschemeID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyIdentification
		nCustParty++
		taCustParty[build("CUST_ID_PartyIdentification", nCustParty)] := eEcbc:ID
		taCustParty[build("CUST_ID_PartyIdentification_SchemeID", nCustParty)] := eAschemeID
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyName
		taENTETE["CUST_Party_Identification_Name"] := eEcbc:Name
	endnodein

	nodein Scbc:StreetName gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["CUST_StreetName"] := eEcbc:StreetName   ! C  
	endnodein

	nodein Scbc:AdditionalStreetName gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["CUST_AdditionalStreetName"] := eEcbc:AdditionalStreetName   ! C  
	endnodein

	nodein Scbc:CityName gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["CUST_CityName"] := eEcbc:CityName   ! C  
	endnodein

	nodein Scbc:PostalZone gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PostalAddress     ! C 1/1 
		taENTETE["CUST_PostalZone"] := eEcbc:PostalZone   ! C  
	endnodein

	nodein Scbc:CountrySubentity gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PostalAddress
		taENTETE["CUST_Country_Subentity"] := eEcbc:CountrySubentity
	endnodein

	nodein Scbc:Line gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PostalAddress, gGcac:AddressLine
		taENTETE["CUST_AdressLine"] := eEcbc:Line
	endnodein

	nodein Scbc:IdentificationCode gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PostalAddress, gGcac:Country
		taENTETE["CUST_IdentificationCode"] := eEcbc:IdentificationCode
	endnodein

	nodein Scbc:RegistrationName gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["CUST_PartyLegalEntity_RegistrationName"] := eEcbc:RegistrationName
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["CUST_PartyLegalEntity_CompanyID"] := eEcbc:CompanyID
	endnodein

	nodein Scbc:CompanyLegalForm gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyLegalEntity
		taENTETE["CUST_PartyLegalEntity_CompanyLegalForm"] := eEcbc:CompanyLegalForm
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:Contact
		taENTETE["CUST_Party_Contact_Name"] := eEcbc:Name
	endnodein

	nodein Scbc:Telephone gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:Contact
		taENTETE["CUST_Party_Contact_Telephone"] := eEcbc:Telephone
	endnodein

	nodein Scbc:ElectronicMail gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:Contact
		taENTETE["CUST_Party_Contact_ElectronicMail"] := eEcbc:ElectronicMail
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyTaxScheme
		nCustTaxScheme++
		taCUSTTaxScheme[build("CUST_PartyTaxScheme_CompanyID", nCustTaxScheme)] := eEcbc:CompanyID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyTaxScheme, gGcac:TaxScheme
		taCUSTTaxScheme[build("CUST_PartyTaxScheme_ID", nCustTaxScheme)] := eEcbc:ID
	endnodein

	nodein Scbc:CustomerAssignedAccountID gGInvoice,gGcac:AccountingCustomerParty     ! C 1/1 
		taENTETE["CUST_CustomerAssignedAccountID"]:= eEcbc:CustomerAssignedAccountID
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:AccountingCustomerParty, gGcac:Party, gGcac:PartyIdentification
		taENTETE["CUST_PartyLegalEntity"] := eEcbc:ID
	endnodein

	nodein Scbc:CompanyID gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PartyTaxScheme     ! C 1/1 
		taENTETE["CUST_CompanyID"] := eEcbc:CompanyID   ! C  
		tSellerId := eEcbc:CompanyID   ! C  
	endnodein

	nodein Scbc:CompanyID gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PartyLegalEntity     ! C 1/1 
		taENTETE["CUST_PartyLegalEntity_CompanyID"] := eEcbc:CompanyID   ! C  
	endnodein

	nodein Scbc:IdentificationCode gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PartyLegalEntity,gGcac:RegistrationAddress,gGcac:Country     ! C 1/1 
		taENTETE["CUST_IdentificationCode"] :=  eEcbc:IdentificationCode   ! C  
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:AccountingCustomerParty,gGcac:Party,gGcac:PartyLegalEntity,gGcac:CorporateRegistrationScheme     ! C 1/1 
		taENTETE["CUST_ID"] :=  eEcbc:ID   ! C  
	endnodein




	!Payee party
	nodein Scbc:ID gGInvoice, gGcac:PayeeParty, gGcac:PartyIdentification
		taENTETE["PayeePartyIdentificationID"] := eEcbc:ID
		taENTETE["PayeePartyIdentificationIDschemeID"] := eAschemeID
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:PayeeParty, gGcac:PartyName
		taENTETE["PayeePartyName"] := eEcbc:Name
	endnodein

	nodein Scbc:CompanyID gGInvoice, gGcac:PayeeParty, gGcac:PartyLegalEntity
		taENTETE["PayeeCompanyID"] := eEcbc:CompanyID
	endnodein






	! Tax Representative party

	nodein Scbc:Name gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PartyName
		taENTETE["TaxRepresentativeName"] := eEcbc:Name
	endnodein

	nodein Scbc:StreetName gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress
		taENTETE["TaxRepresentativeStreetName"] := eEcbc:StreetName
	endnodein
	 
	nodein Scbc:AdditionalStreetName gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress
		taENTETE["TaxRepresentativeAdditionalStreetName"] := eEcbc:AdditionalStreetName
	endnodein

	nodein Scbc:CityName gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress
		taENTETE["TaxRepresentativeCityName"] := eEcbc:CityName
	endnodein

	nodein Scbc:PostalZone gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress
		taENTETE["TaxRepresentativePostalZone"] := eEcbc:PostalZone
	endnodein

	nodein Scbc:CountrySubentity gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress
		taENTETE["TaxRepresentativeCountrySubentity"] := eEcbc:CountrySubentity
	endnodein

	nodein Scbc:Line gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress, gGcac:AddressLine
		taENTETE["TaxRepresentativeAdressLine"] := eEcbc:Line
	endnodein

	nodein Scbc:IdentificationCode gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress, gGcac:Country
		taENTETE["TaxRepresentaticeCountry"] := eEcbc:IdentificationCode
	endnodein
/*
	nodein Scbc:IdentificationCode gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PostalAddress, gGcac:Country
		taENTETE["TaxRepresentativeCompanyID"] := eEcbc:IdentificationCode
	endnodein
*/

	nodein Scbc:CompanyID gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PartyTaxScheme
		taENTETE["TaxRepresentativeCompanyID"] := eEcbc:CompanyID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:TaxRepresentativeParty, gGcac:PartyTaxScheme, gGcac:TaxScheme
		taENTETE["TaxRepresentativeTaxSchemeID"] := eEcbc:ID
	endnodein


! Delivery

	nodein Scbc:ActualDeliveryDate gGInvoice, gGcac:Delivery
		taENTETE["ActualDeliveryDate"] := eEcbc:ActualDeliveryDate
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation
		taENTETE["DeliveryLocationID"] := eEcbc:ID
		taENTETE["DeliveryLocationIDschemeID"] := eAschemeID
	endnodein
	
	nodein Scbc:StreetName gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address
		taENTETE["DeliveryLocationStreetName"] := eEcbc:StreetName
	endnodein

	nodein Scbc:AdditionalStreetName gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address
		taENTETE["DeliveryLocationAdditionalStreetName"] := eEcbc:AdditionalStreetName
	endnodein

	nodein Scbc:CityName gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address
		taENTETE["DeliveryLocationCityName"] := eEcbc:CityName
	endnodein

	nodein Scbc:PostalZone gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address
		taENTETE["DeliveryLocationPostalZone"] := eEcbc:PostalZone
	endnodein

	nodein Scbc:CountrySubentity gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address
		taENTETE["DeliveryLocationCountrySubentity"] := eEcbc:CountrySubentity
	endnodein

	nodein Scbc:Line gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address, gGcac:AddressLine
		taENTETE["DeliveryLocationAddressLine"] := eEcbc:Line
	endnodein

	nodein Scbc:IdentificationCode gGInvoice, gGcac:Delivery, gGcac:DeliveryLocation, gGcac:Address, gGcac:Country
		taENTETE["DeliveryLocationCountry"] := eEcbc:IdentificationCode
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:Delivery, gGcac:DeliveryParty, gGcac:PartyName
		taENTETE["DeliveryPartyName"] := eEcbc:Name
	endnodein


	! Payment, can have a few
	nodein Scbc:PaymentMeansCode gGInvoice, gGcac:PaymentMeans     ! M 1/1 
		taENTETE["PaymentMeansCode"] :=  eEcbc:PaymentMeansCode   ! M  
		taENTETE["PaymentMeansCodeName"] := eAname
	endnodein

	nodein Scbc:PaymentID gGInvoice, gGcac:PaymentMeans
		taENTETE["PaymentID"] := eEcbc:PaymentID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:PaymentMeans, gGcac:PayeeFinancialAccount
		taENTETE["PayeeFinancialAccount"] := eEcbc:ID
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:PaymentMeans, gGcac:PayeeFinancialAccount
		taENTETE["PayeeFinancialAccountName"] := eEcbc:Name
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:PaymentMeans, gGcac:PayeeFinancialAccount, gGcac:FinancialInstitutionBranch
		taENTETE["FinancialInstitutionBranch"] := eEcbc:ID
	endnodein

	nodein Scbc:Note gGInvoice, gGcac:PaymentTerms
		taENTETE["PaymentTermsNote"] := eEcbc:Note
	endnodein





	nodein Scbc:Name gGInvoice, gGcac:PaymentMeans, gGcac:PayeeFinancialAccount
		taENTETE["PayeeFinancialAccountName"] := eEcbc:Name
	endnodein





	nodein Scbc:PaymentDueDate gGInvoice,gGcac:PaymentMeans     ! C 1/1 
		taENTETE["PaymentDueDate"] := eEcbc:PaymentDueDate   ! C  
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:PaymentMeans,gGcac:PayeeFinancialAccount     ! C 1/1 
		taENTETE["PayeeFinancialAccount_ID"] := eEcbc:ID   ! C  
	endnodein

	nodein Scbc:Name gGInvoice,gGcac:PaymentMeans,gGcac:PayeeFinancialAccount     ! C 1/1 
		taENTETE["PayeeFinancialAccount_Name"] := eEcbc:Name   ! C  
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:PaymentMeans,gGcac:PayeeFinancialAccount,gGcac:FinancialInstitutionBranch     ! C 1/1 
		taENTETE["FinancialInstitutionBranch_ID"] := eEcbc:ID   ! C  
	endnodein

	nodein Scbc:Note gGInvoice,gGcac:PaymentTerms     ! C 1/unbound 
		taENTETE["PaymentTerms_Note"] := eEcbc:Note   ! C  
	endnodein


! Global Allowances and Charges - Not VAT

	nodein Scbc:ChargeIndicator gGInvoice,gGcac:AllowanceCharge     ! M 1/1 
		taENTETE["ChargeIndicator"] := eEcbc:ChargeIndicator   ! M  
	endnodein

	nodein Scbc:AllowanceChargeReasonCode gGInvoice,gGcac:AllowanceCharge     ! C 1/1 
		taENTETE["AllowanceChargeReasonCode"] := eEcbc:AllowanceChargeReasonCode   ! C  
	endnodein

	nodein Scbc:AllowanceChargeReason gGInvoice,gGcac:AllowanceCharge     ! C 1/1 
		taENTETE["AllowanceChargeReason"] := eEcbc:AllowanceChargeReason   ! C  
	endnodein

	nodein Scbc:MultiplierFactorNumeric gGInvoice,gGcac:AllowanceCharge     ! C 1/1 
		taENTETE["AllowanceChargeMultiplierFactorNumeric"] := eEcbc:MultiplierFactorNumeric   ! C  
	endnodein

	nodein Scbc:Amount gGInvoice,gGcac:AllowanceCharge     ! M 1/1 
		taENTETE["AllowanceChargeAmount"] := eEcbc:Amount   ! M  
		taENTETE["AllowanceChargeAmountcurrencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:BaseAmount gGInvoice,gGcac:AllowanceCharge     ! M 1/1 
		taENTETE["AllowanceChargeBaseAmount"] := eEcbc:BaseAmount   ! M  
		taENTETE["AllowanceChargeBaseAmountcurrencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AllowanceCharge, gGcac:TaxCategory
		taENTETE["AllowanceChargeTaxCategoryID"] := eEcbc:ID
	endnodein

	nodein Scbc:Percent gGInvoice, gGcac:AllowanceCharge, gGcac:TaxCategory
		taENTETE["AllowanceChargeTaxCategoryPercent"] := eEcbc:Percent
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:AllowanceCharge, gGcac:TaxCategory, gGcac:TaxScheme
		taENTETE["AllowanceChargeTaxSchemeID"] := eEcbc:ID
	endnodein

 ! Tax Total


	nodein Scbc:TaxAmount gGInvoice, gGcac:TaxTotal
		nTaxTotal++
		taTaxTotal[build("TaxAmount", nTaxTotal)] := eEcbc:TaxAmount
	endnodein	

	nodein Scbc:TaxableAmount gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal
		nTaxSubTotal++
		taTaxSubTotal[build("TaxableAmount", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:TaxableAmount
		taTaxSubTotal[build("TaxableAmount_Currency", nTaxTotal, "_", nTaxSubTotal)] := eAcurrencyID
	endnodein

	nodein Scbc:TaxAmount gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal
		taTaxSubTotal[build("TaxAmount", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:TaxableAmount
		taTaxSubTotal[build("TaxAmount_Currency", nTaxTotal, "_", nTaxSubTotal)] := eAcurrencyID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal, gGcac:TaxCategory
		taTaxSubTotal[build("TaxCategoryID", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:ID
	endnodein

	nodein Scbc:Percent gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal, gGcac:TaxCategory
		taTaxSubTotal[build("TaxCategoryPercent", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:Percent
	endnodein

	nodein Scbc:TaxExemptionReasonCode gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal, gGcac:TaxCategory
		taTaxSubTotal[build("TaxCategoryTaxExemptionReasonCode", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:TaxExemptionReasonCode
	endnodein

	nodein Scbc:TaxExemptionReason gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal, gGcac:TaxCategory
		taTaxSubTotal[build("TaxCategoryTaxExemptionReason", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:TaxExemptionReason
		tReasonTaxExemption := eEcbc:TaxExemptionReason
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal, gGcac:TaxCategory, gGcac:TaxScheme
		taTaxSubTotal[build("TaxSchemeID", nTaxTotal, "_", nTaxSubTotal)] := eEcbc:ID
	endnodein	




	nodein Scbc:TaxableAmount gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal
		nTax++
		taTaxes[build("TaxableAmount", nTax)] := eEcbc:TaxableAmount
	endnodein

	nodein Scbc:TaxAmount gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal
		taTaxes[build("TaxAmount", nTax)] := eEcbcTaxAmount
	endnodein

	nodein Scbc:Percent gGInvoice, gGcac:TaxTotal, gGcac:TaxSubtotal, gGcac:TaxCategory
		taTaxes[build("Percent", nTax)] := eEcbc:Percent
	endnodein



	nodein Scbc:LineExtensionAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["LineExtensionAmount"] := eEcbc:LineExtensionAmount   ! C  
		taPIED["LineExtensionAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:TaxExclusiveAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["TaxExclusiveAmount"] := eEcbc:TaxExclusiveAmount   ! C  
		taPIED["TaxExclusiveAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:TaxInclusiveAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["TaxInclusiveAmount"] := eEcbc:TaxInclusiveAmount   ! C  
		taPIED["TaxInclusiveAmount_currencyID"] := eAcurrencyID
	endnodein


	nodein Scbc:AllowanceTotalAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["AllowanceTotalAmount"] := eEcbc:AllowanceTotalAmount   ! C  
		taPIED["AllowanceTotalAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:ChargeTotalAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["ChargeTotalAmount"] := eEcbc:ChargeTotalAmount   ! C  
		taPIED["ChargeTotalAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:PrepaidAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["PrepaidAmount"] := eEcbc:PrepaidAmount   ! C  
		taPIED["PrepaidAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:PayableRoundingAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["PayableRoundingAmount"] := eEcbc:PayableRoundingAmount   ! C  
		taPIED["PayableRoundingAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:PayableAmount gGInvoice,gGcac:LegalMonetaryTotal     ! M 1/1 
		taPIED["PayableAmount"] := eEcbc:PayableAmount   ! M  
		tAmount := eEcbc:PayableAmount   ! M  
		taPIED["PayableAmount_currencyID"] := eAcurrencyID
	endnodein

	nodein Scbc:PrepaidAmount gGInvoice,gGcac:LegalMonetaryTotal     ! C 1/1 
		taPIED["PrepaidAmount"] := eEcbc:PrepaidAmount   ! C  
		taPIED["PrepaidAmount_currencyID"] := eAcurrencyID
	endnodein


	! Lignes
	nodein Scbc:ID gGInvoice,gGcac:InvoiceLine
		nLin++
		taLIGNE[build("ID", nLin)] := eEcbc:ID  
	endnodein

	nodein Scbc:Note gGInvoice,gGcac:InvoiceLine
		nLinNotes++
		taLIGNENOTES[build("Note", nLin,"_", nLinNotes)] := eEcbc:Note  
	endnodein

	nodein Scbc:InvoicedQuantity gGInvoice,gGcac:InvoiceLine      
		taLIGNE[build("InvoicedQuantity", nLin)] := eEcbc:InvoicedQuantity 
		taLIGNE[build("unitCodeInvoicedQuantity", nLin)] := eAunitCode 
	endnodein

	nodein Scbc:LineExtensionAmount gGInvoice,gGcac:InvoiceLine 
		taLIGNE[build("LineExtensionAmount", nLin)] := eEcbc:LineExtensionAmount 
		taLIGNE[build("currencyIDLineExtensionAmount", nLin)] := eAcurrencyID 
	endnodein

	nodein Scbc:AccountingCost gGInvoice, gGcac:InvoiceLine
		taLIGNE[build("AccountingCost", nLin)] := eEcbc:AccountingCost
	endnodein

	nodein Scbc:StartDate gGInvoice, gGcac:InvoiceLine, gGcac:InvoicePeriod
		taLIGNE[build("StartDate", nLin)] := eEcbc:StartDate
	endnodein

	nodein Scbc:EndDate gGInvoice, gGcac:InvoiceLine, gGcac:InvoicePeriod
		taLIGNE[build("EndDate", nLin)] := eEcbc:EndDate
	endnodein

	nodein Scbc:LineID gGInvoice, gGcac:InvoiceLine, gGcac:OrderLineReference
		taLIGNE[build("OrderLineReferenceID", nLin)] := eEcbc:LineID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:InvoiceLine, gGcac:DocumentReference
		taLIGNE[build("DocumentReferenceID", nLin)] := eEcbc:ID
		taLIGNE[build("schemeIDDocumentReferenceID", nLin)] := eAschemeID
	endnodein

	nodein Scbc:DocumentTypeCode gGInvoice, gGcac:InvoiceLine, gGcac:DocumentReference
		taLIGNE[build("DocumentReferenceDocumentTypeCode", nLin)] := eEcbc:DocumentTypeCode
	endnodein




	! Remises et charges de ligne
	nodein Scbc:ChargeIndicator gGInvoice, gGcac:InvoiceLine, gGcac:AllowanceCharge
		nLinAll++
		taDETAILALLOWANCES[build("ChargeIndicator", nLin,"_", nLinAll)] := eEcbc:ChargeIndicator
	endnodein

	nodein Scbc:AllowanceChargeReasonCode gGInvoice, gGcac:InvoiceLine, gGcac:AllowanceCharge
		taDETAILALLOWANCES[build("ReasonCode", nLin,"_", nLinAll)] := eEcbc:AllowanceChargeReasonCode
	endnodein

	nodein Scbc:AllowanceChargeReason gGInvoice, gGcac:InvoiceLine, gGcac:AllowanceCharge
		taDETAILALLOWANCES[build("Reason", nLin,"_", nLinAll)] := eEcbc:AllowanceChargeReason
	endnodein

	nodein Scbc:MultiplierFactorNumeric gGInvoice, gGcac:InvoiceLine, gGcac:AllowanceCharge
		taDETAILALLOWANCES[build("MultiplifierFactorNumeric", nLin,"_", nLinAll)] := eEcbc:MultiplifierFactorNumeric
	endnodein

	nodein Scbc:Amount gGInvoice, gGcac:InvoiceLine, gGcac:AllowanceCharge
		taDETAILALLOWANCES[build("LineChargeAmount", nLin, "_", nLinAll)] := eEcbc:Amount
		taDETAILALLOWANCES[build("LineChargeAmountCurr", nLin, "_", nLinAll)] := eAcurrencyID
	endnodein

	nodein Scbc:BaseAmount gGInvoice, gGcac:InvoiceLine, gGcac:AllowanceCharge
		taDETAILALLOWANCES[build("LineChargeBaseAmount", nLin, "_", nLinAll)] := eEcbc:BaseAmount
		taDETAILALLOWANCES[build("LineChargeBaseAmountCurr", nLin, "_", nLinAll)] := eAcurrencyID
	endnodein

	!nodein Scbc:TrackingID gGInvoice,gGcac:InvoiceLine,gGcac:Delivery 
	!	taLIGNE[build("TrackingID", nLin)] := eEcbc:TrackingID
	!endnodein





	!Item
	nodein Scbc:Description gGInvoice,gGcac:InvoiceLine,gGcac:Item
		taLIGNE[build("Description", nLin)] := eEcbc:Description
	endnodein

	nodein Scbc:Name gGInvoice,gGcac:InvoiceLine,gGcac:Item     ! C 1/1 
		taLIGNE[build("Name", nLin)] := eEcbc:Name   ! C  
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:BuyersItemIdentification
		taLIGNE[build("BuyersItemIdentification", nLin)] := eEcbc:ID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:SellersItemIdentification
		taLIGNE[build("SellersItemIdentification", nLin)] := eEcbc:ID
	endnodein

	nodein Scbc:ID gGInvoice,gGcac:InvoiceLine,gGcac:Item,gGcac:StandardItemIdentification     ! M 1/1 
		taLIGNE[build("StandardItemIdentification_ID", nLin)] := eEcbc:ID
		taLIGNE[build("schemeIDStandardItemIdentification_ID", nLin)] := eAschemeID
	endnodein

	nodein Scbc:IdentificationCode gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:OriginCountry
		taLIGNE[build("OriginCountryCode", nLin)] := eEcbc:IdentificationCode
	endnodein

	nodein Scbc:ItemClassificationCode gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:CommodityClassification
		taLIGNE[build("ItemClassificationCode", nLin)] := eEcbc:ItemClassificationCode
		taLIGNE[build("listIDItemClassificationCode", nLin)] := eAlistID
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:ClassifiedTaxCategory
		taLIGNE[build("ClassifiedTaxCategory_ID", nLin)] := eEcbc:ID
	endnodein

	nodein Scbc:Percent gGInvoice,gGcac:InvoiceLine,gGcac:Item,gGcac:ClassifiedTaxCategory
		taLIGNE[build("Percent", nLin)] := eEcbc:Percent
	endnodein

	nodein Scbc:ID gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:ClassifiedTaxCategory, gGcac:TaxScheme
		taLIGNE[build("TaxSchemeID", nLin)] := eEcbc:ID
	endnodein

	nodein Scbc:Name gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:AdditionalItemProperty
		taLIGNE[build("AdditionalItemPropertyName", nLin)] := eEcbc:Name
	endnodein

	nodein Scbc:Value gGInvoice, gGcac:InvoiceLine, gGcac:Item, gGcac:AdditionalItemProperty
		taLIGNE[build("AdditionalItemPropertyValue", nLin)] := eEcbc:Value
	endnodein


	!Price
	nodein Scbc:PriceAmount gGInvoice,gGcac:InvoiceLine,gGcac:Price
		taLIGNE[build("PriceAmount", nLin)] := eEcbc:PriceAmount
		taLIGNE[build("currencyIDPriceAmount", nLin)] := eAcurrencyID
	endnodein

	nodein Scbc:BaseQuantity gGInvoice,gGcac:InvoiceLine,gGcac:Price
		taLIGNE[build("BaseQuantity", nLin)] := eEcbc:BaseQuantity
		taLIGNE[build("unitCodeBaseQuantity", nLin)] := eAunitCode
	endnodein

	nodein Scbc:ChargeIndicator gGInvoice, gGcac:InvoiceLine, gGcac:Price, gGcac:AllowanceCharge
		taLIGNE[build("ChargeIndicatorAllowanceChargePrice", nLin)] := eEcbc:ChargeIndicator
	endnodein

	nodein Scbc:Amount gGInvoice, gGcac:InvoiceLine, gGcac:Price, gGcac:AllowanceCharge
		taLIGNE[build("AmountAllowanceChargePrice", nLin)] := eEcbc:Amount
		taLIGNE[build("currencyIDAmountAllowanceChargePrice",  nLin)] := eAcurrencyID
	endnodein

	nodein Scbc:BaseAmount gGInvoice, gGcac:InvoiceLine, gGcac:Price, gGcac:AllowanceCharge
		taLIGNE[build("BaseAmountAllowanceChargePrice", nLin)] := eEcbc:BaseAmount
		taLIGNE[build("currencyIDBaseAmountAllowanceChargePrice",  nLin)] := eAcurrencyID
	endnodein
















	!===========================================================

	end

	!LOG("Contenu du tableau taENTETE", "")
	!log(taENTETE)
	!LOG("Contenu du tableau taPIED", "")
	!log(taPIED)
	!LOG("Contenu du tableau taLIGNE", "")
	!log(taLIGNE)
	!log("Contenu du tableau taTaxes", "")
	!log(taTaxes)
	!log(taDETAILALLOWANCES)

	log(taAdditionalDocumentReference)


	log(taSUPPTaxScheme)


	bfMessage()

	endend

	!===========================================================


	function bfMessage()
/*
		nodeout Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyID     ! M 1/1 
		eEcore:Ident  := replace(replace(taENTETE["SUPP_Name"]," ", "_"),".","_") ! M  
		endnodeout


		nodeout Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:PartyID     ! M 1/1 
		eEcore:Ident  := replace(replace(taENTETE["CUST_Name"]," ", "_"),".","_") ! M  
		endnodeout
*/

		nodeout SInvoice gGInvoice     ! M 1/1 
		eAxmlns:core := "rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd"
		eAxmlns := "rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"
		endnodeout

		nodeout SInvoiceNumber gGInvoice,gGInvoiceHeader,gGInvoiceNumber     ! M 1/1 
		eEInvoiceNumber  := tNumberInvoic ! M  
		endnodeout

		nodeout SInvoiceIssueDate gGInvoice,gGInvoiceHeader     ! M 1/1 
		eEInvoiceIssueDate  := build(tDateInvoice,"T00:00:00") ! M  
		endnodeout
		
		if taENTETE["TaxPointDate"] <> EMPTY then
		nodeout STaxPointDate gGInvoice, gGInvoiceHeader  
		eETaxPointDate  := build(taENTETE["TaxPointDate"],"T00:00:00")
		endnodeout
		endif

		nodeout Score:BuyerOrderNumber gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGPurchaseOrderReference     ! M 1/1 
			eEcore:BuyerOrderNumber := taENTETE["OrderReference_DocumentCurrencyCode"]  
		endnodeout

		nodeout Score:SellerOrderNumber gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGPurchaseOrderReference
			eEcore:SellerOrderNumber := taENTETE["SalesOrderID"]
		endnodeout


		nodeout Score:PurchaseOrderDate gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGPurchaseOrderReference     ! C 1/1 
		eEcore:PurchaseOrderDate  := tDateOrder ! C  
		endnodeout

		nodeout Score:RefNum gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGASNNumber     ! M 1/1 
		eEcore:RefNum := taENTETE["ASN_ID"]  
		endnodeout

		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ContractID
			eEcore:Ident := taENTETE["Contract_ID"]
		endnodeout
	
		if taENTETE["BillingReferenceID"] <> EMPTY then	
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "Other"
		endnodeout
		nodeout Score:ReferenceTypeCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCodedOther := "BillingReference"
		endnodeout
		endif
		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := taENTETE["BillingReferenceID"]
		endnodeout
		
		nodeout Score:RefDate gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefDate := taENTETE["BillingReferenceIssueDate"]
		endnodeout

		if taENTETE["ReceiptDocumentReference"] <> EMPTY then
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "Other"
		endnodeout

		nodeout Score:ReferenceTypeCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCodedOther := "ReceiptReference"
		endnodeout
		endif

		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := taENTETE["ReceiptDocumentReference"]
		endnodeout

		if taENTETE["OriginatorDocumentReference"] <> EMPTY then
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "Other"
		endnodeout

		nodeout Score:ReferenceTypeCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCodedOther := "OriginatorReference"
		endnodeout
		endif
		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := taENTETE["OriginatorDocumentReference"]
		endnodeout
		
		if taENTETE["ProjectReference"] <> EMPTY then
		nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceTypeCoded := "ProjectNumber"
		endnodeout
		
		nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := taENTETE["ProjectReference"]
		endnodeout
		endif

!Accounting cost
		if taENTETE["AccountingCost"] <> EMPTY or taENTETE["BuyerReference"] <> EMPTY then
		nodeout Score:SetName gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet
			eEcore:SetName := "EURINV"
		endnodeout
		endif

		if taENTETE["AccountingCost"] <> EMPTY then
		nodeout Score:Name gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
			eEcore:Name := "AccountingCost"
		endnodeout

		
		nodeout Score:Value gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
			eEcore:Value := taENTETE["AccountingCost"]
		endnodeout
		endif
!Buyer Reference

		if taENTETE["BuyerReference"] <> EMPTY then
		nodeout Score:Name gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
			eEcore:Name := "BuyerReference"
		endnodeout
		
		nodeout Score:Value gGInvoice, gGInvoiceHeader, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
			eEcore:Value := taENTETE["BuyerReference"]
		endnodeout
		endif




		nAddDocRef_temp := 0

		while ( nAddDocRef_temp < nAddDocRef) do
			nAddDocRef_temp++
			 
			nodeout Score:AttachmentLocation gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentLocation := taAdditionalDocumentReference[build("ID", nAddDocRef_temp)]
			endnodeout

			if taAdditionalDocumentReference[build("IDSchemeID", nAddDocRef_temp)] = "ABZ" then	
			nodeout Score:AttachmentPurpose gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentPurpose := "Vehicle licence number"
			endnodeout 
			else
			nodeout Score:AttachmentPurpose gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentPurpose := taAdditionalDocumentReference[build("IDSchemeID", nAddDocRef_temp)]
			endnodeout 
			endif
	
			nodeout Score:AttachmentDescription gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentDescription := taAdditionalDocumentReference[build("DocumentDescription", nAddDocRef_temp)]
			endnodeout

			nodeout Score:AttachmentTitle gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentTitle := taAdditionalDocumentReference[build("URI", nAddDocRef_temp)]
			endnodeout

			nodeout Score:FileName gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:FileName := taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObjectFileName", nAddDocRef_temp)]
			endnodeout

			nodeout Score:MIMEType gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:MIMEType := taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObjectmimeCode", nAddDocRef_temp)]
			endnodeout

			if taAdditionalDocumentReference[build("DocumentTypeCode", nAddDocRef_temp)] = "130" then
			nodeout Score:AttachmentActionCoded gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentActionCoded := "Other"
			endnodeout

			nodeout Score:AttachmentActionCodedOther gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment
				eEcore:AttachmentActionCodedOther := "Invoice"
			endnodeout
			endif

			if taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObject", nAddDocRef_temp)] <> EMPTY then			
			nodeout Score:LanguageCoded gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment, gGcore:Language
				eEcore:LanguageCoded := "Other"
			endnodeout
! Plus de place dans une autre balise
			nodeout Score:LanguageCodedOther gGInvoice, gGInvoiceHeader, gGListOfAttachment, gGcore:Attachment, gGcore:Language
				eEcore:LanguageCodedOther := taAdditionalDocumentReference[build("EmbeddedDocumentBinaryObject", nAddDocRef_temp)]
			endnodeout
			endif
			
		endwhile


		nodeout SInvoicePurposeCoded gGInvoice,gGInvoiceHeader,gGInvoicePurpose     ! M 1/1 
		eEInvoicePurposeCoded  := "Original" ! M  
		endnodeout


		nodeout SInvoiceTypeCoded gGInvoice,gGInvoiceHeader,gGInvoiceType     ! M 1/1 
			switch (taENTETE["InvoiceTypeCode"])
							case "380":
									eEInvoiceTypeCoded  := "CommercialInvoice"
							case "389":
									eEInvoiceTypeCoded  := "CommercialInvoice"
							case "381":
									eEInvoiceTypeCoded  := "CreditNoteGoodsAndServices"
							case "261":
									eEInvoiceTypeCoded  := "CreditNoteGoodsAndServices"
							case "262":
									eEInvoiceTypeCoded  := "CommissionNote"
							case "384":
									eEInvoiceTypeCoded  := "CorrectedInvoice"
							case "82":
									eEInvoiceTypeCoded  := "MeteredServicesInvoice"
							case "83":
									eEInvoiceTypeCoded  := "CreditNoteFinancialAdjustment"
							case "84":
									eEInvoiceTypeCoded  := "DebitNoteFinancialAdjustment"
							case "130":
									eEInvoiceTypeCoded  := "InvoicingDataSheet"
							case "80":
									eEInvoiceTypeCoded  := "DebitNoteGoodsAndServices"
							case "385":
									eEInvoiceTypeCoded  := "ConsolidatedInvoice"
							case "386":
									eEInvoiceTypeCoded  := "PrepaymentInvoice"
							case "390":
									eEInvoiceTypeCoded  := "DelcredereInvoice"
							case "393":
									eEInvoiceTypeCoded  := "FactoredInvoice"
						default:
					eEInvoiceTypeCoded  := "N/A"
			endswitch
		endnodeout

		nodeout SInvoiceTypeCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceType     ! C 1/1 
				eEInvoiceTypeCodedOther := taENTETE["InvoiceTypeCode"]		
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice,gGInvoiceHeader,gGInvoiceCurrency     ! M 1/1 
		eEcore:CurrencyCoded := tCurrency  ! M  
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice,gGInvoiceHeader,gGTaxAccountingCurrency     ! M 1/1 
		eEcore:CurrencyCoded := taENTETE["TaxCurrencyCode"]  
		endnodeout

		nodeout Score:LanguageCoded gGInvoice,gGInvoiceHeader,gGInvoiceLanguage     ! M 1/1 
		eEcore:LanguageCoded  := tOrderedBy !  UTILISE POUR RECUPERER LE ORDERBY  
		endnodeout
		


		if tReasonTaxExemption <> EMPTY then
		nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxTypeCoded := "Other"
		endnodeout
		
		nodeout Score:TaxTypeCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxTypeCodedOther := "Other"
		endnodeout

		nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxFunctionQualifierCoded := "TaxRelatedInformation"
		endnodeout

		
		nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxCategoryCoded := "Other"
		endnodeout

		nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:TaxCategoryCodedOther := "Other"
		endnodeout

		nodeout Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:ReasonTaxExemptCoded := "Other"
		endnodeout

		nodeout Score:ReasonTaxExemptCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
			eEcore:ReasonTaxExemptCodedOther := tReasonTaxExemption
		endnodeout
		log(tReasonTaxExemption)
		endif

		if taENTETE["DueDate"] <> EMPTY then
		nodeout SInvoiceDueDate gGInvoice,gGInvoiceHeader,gGInvoiceDates     ! M 1/1 
			eEInvoiceDueDate := build(taENTETE["DueDate"],"T00:00:00")
		endnodeout
		endif

		if taENTETE["ActualDeliveryDate"] <> EMPTY then
		nodeout SActualDeliveryDate gGInvoice, gGInvoiceHeader, gGInvoiceDates
			eEActualDeliveryDate := build(taENTETE["ActualDeliveryDate"],"T00:00:00")
		endnodeout
		endif



		nodeout SActualShipDate gGInvoice,gGInvoiceHeader,gGInvoiceDates     ! C 1/1 
		eEActualShipDate  := tDateASN ! C  
		endnodeout
		
		if taENTETE["StartDate"] <> EMPTY then
		nodeout Score:StartDate gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGInvoicingPeriod 
			eEcore:StartDate := build(taENTETE["StartDate"],"T00:00:00")
		endnodeout
		endif

		if taENTETE["EndDate"] <> EMPTY then
		nodeout Score:EndDate gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGInvoicingPeriod 
			eEcore:EndDate := build(taENTETE["EndDate"],"T00:00:00")
		endnodeout
		endif
		
		nodeout Score:Date gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded
			!eEDate := time("%Y-%m-%dT%H:%M:%S")
			eEDate := build(taENTETE["EndDate"],"T00:00:00")
		endnodeout
		nodeout Score:DateQualifierCoded gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded, gGcore:DateQualifier
			eEcore:DateQualifierCoded := "DocumentReceivedDateTime"
		endnodeout
		nodeout Score:Date gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded
			!eEDate := time("%Y-%m-%dT%H:%M:%S")
			eEDate := build(taENTETE["EndDate"],"T00:00:00")
		endnodeout
		nodeout Score:DateQualifierCoded gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded, gGcore:DateQualifier
			eEcore:DateQualifierCoded := "PreparationDateTimeOfDocument"
		endnodeout

		if taENTETE["TaxPointDate"] <> EMPTY then
		nodeout Score:Date gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded
			eEcore:Date := build(taENTETE["TaxPointDate"],"T00:00:00")
		endnodeout
		
		nodeout Score:DateQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceDates, gGListOfOtherInvoiceDates, gGcore:DateCoded, gGcore:DateQualifier
			eEcore:DateQualifierCoded := "TaxPointDate"
		endnodeout
		endif





		nodeout Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:PartyID     ! M 1/1 
		eEcore:Ident  := replace(replace(taENTETE["CUST_Name"]," ", "_"),".","_") ! M  
		endnodeout

		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier
			eEcore:Ident := taENTETE["CUST_EndpointID"]
		endnodeout

		if taENTETE["CUST_EndpointID_SchemeID"] = "0196" then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "Kennitala"
		endnodeout
		else
				nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "N/A"
		endnodeout
		endif

		if taENTETE["CUST_EndpointID_SchemeID"] = "0002" then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "SIRENE"
		endnodeout
		else
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "N/A"
		endnodeout
		endif

		nCustParty_temp := 0
		while (nCustParty_temp < nCustParty) do
			nCustParty_temp++
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier
				eEcore:Ident := taCustParty[build("CUST_ID_PartyIdentification", nCustParty_temp)]
			endnodeout

			if taCustParty[build("CUST_ID_PartyIdentification_SchemeID", nCustParty_temp)] = "0002" then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "SIRENE"
			endnodeout

			endif
			
			if taCustParty[build("CUST_ID_PartyIdentification_SchemeID", nCustParty_temp)] = "0035" then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "BP"
			endnodeout
			endif

			if taCustParty[build("CUST_ID_PartyIdentification_SchemeID", nCustParty_temp)] = "SEPA" then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "SEPA"
			endnodeout
			endif
		endwhile

			nCustTaxScheme_temp := 0
			while (nCustTaxScheme > nCustTaxScheme_temp) do 
			nCustTaxScheme_temp++

			if taCUSTTaxScheme[build("CUST_PartyTaxScheme_ID", nCustTaxScheme_temp)] = "VAT" then

			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
				eEcore:Ident := taCUSTTaxScheme[build("CUST_PartyTaxScheme_CompanyID", nCustTaxScheme_temp)]
			endnodeout

			else
			
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier
				eEcore:Ident := "notVAT"
			endnodeout

			if taCUSTTaxScheme[build("CUST_PartyTaxScheme_ID", nCustTaxScheme_temp)] <> EMPTY then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency	
				eEcore:AgencyCoded := "Other"
			endnodeout
			endif

			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency	
				eEcore:AgencyCodedOther := taCUSTTaxScheme[build("CUST_PartyTaxScheme_ID", nCustTaxScheme_temp)]
			endnodeout

			endif

			endwhile
			

	! Sales party name
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier
			eEcore:Ident := taENTETE["CUST_Party_Identification_Name"]
		endnodeout
		if taENTETE["CUST_Party_Identification_Name"] <> EMPTY then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "PartyNameIdentification"
		endnodeout
		endif


		
		nodeout Score:Name1 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! M 1/1 
			eEcore:Name1 := taENTETE["CUST_Name"]
		endnodeout

		nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
			eEcore:Name2 := taENTETE["CUST_AdressLine"]
		endnodeout

		nodeout Score:Street gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! C 1/1 
			eEcore:Street := taENTETE["CUST_StreetName"]
		endnodeout

		nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
			eEcore:StreetSupplement1 := taENTETE["CUST_AdditionalStreetName"]
		endnodeout

		nodeout Score:PostalCode gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! C 1/1 
		eEcore:PostalCode  := taENTETE["CUST_PostalZone"]
		endnodeout

		nodeout Score:City gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! C 1/1 
		eEcore:City  := taENTETE["CUST_CityName"]
		endnodeout

		nodeout Score:District gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
			eEcore:District := taENTETE["CUST_Country_Subentity"]
		endnodeout

		nodeout Score:CountryCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress,gGcore:Country     ! M 1/1 
		eEcore:CountryCoded  := taENTETE["CUST_IdentificationCode"]
		endnodeout


		nodeout Score:ContactName gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PrimaryContact
			eEcore:ContactName := taENTETE["CUST_Party_Contact_Name"]
		endnodeout
		
			nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberValue := taENTETE["CUST_Party_Contact_Telephone"]
			endnodeout
			if taENTETE["CUST_Party_Contact_Telephone"] <> EMPTY then
			nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberTypeCoded := "TelephoneNumber"
			endnodeout
			endif	

			nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberValue := taENTETE["CUST_Party_Contact_ElectronicMail"]
			endnodeout
			
			if taENTETE["CUST_Party_Contact_ElectronicMail"] <> EMPTY then
			nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberTypeCoded := "EmailAddress"
			endnodeout
			endif

			nodeout Score:RegisteredName gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation
				eEcore:RegisteredName := taENTETE["CUST_PartyLegalEntity_RegistrationName"]
			endnodeout

			nodeout Score:CompanyRegistrationNumber gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation
				eEcore:CompanyRegistrationNumber := taENTETE["CUST_PartyLegalEntity_CompanyID"]
			endnodeout

			nodeout Score:RegisteredOffice gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation
				eEcore:RegisteredOffice := taENTETE["CUST_PartyLegalEntity_CompanyLegalForm"]
			endnodeout



		nodeout Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyID     ! M 1/1 
		eEcore:Ident  := replace(replace(taENTETE["SUPP_Name"]," ", "_"),".","_") ! M  
		endnodeout

		
/*
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
			eEcore:Ident := taENTETE["SUPP_EndpointID"]
		endnodeout
*/

		if taENTETE["SUPP_EndpointID_SchemeID"] = "0196" then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "Kennitala"
		endnodeout
		endif

		if taENTETE["SUPP_EndpointID_SchemeID"] = "0088" then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "EAN"
		endnodeout
		endif
		
		if taENTETE["SUPP_EndpointID_SchemeID"] = "0009" then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "N/A"
		endnodeout
		endif
		
		if taENTETE["SUPP_EndpointID_SchemeID"] = "0007" then
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "N/A"
		endnodeout
		endif
		
		nSuppParty_temp := 0
		while (nSuppParty_temp < nSuppParty) do
			nSuppParty_temp++
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
				eEcore:Ident := taSuppParty[build("SUPP_ID_PartyIdentification", nSuppParty_temp)]
			endnodeout
			
			if taSuppParty[build("SUPP_ID_PartyIdentification_SchemeID", nSuppParty)] = "0088" then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "EAN"
			endnodeout
			else 
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "Identifier"
			endnodeout
			endif

			if taSuppParty[build("SUPP_ID_PartyIdentification_SchemeID", nSuppParty_temp)] = "0035" then
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout

			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "BP"
			endnodeout
			else
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "N/A"
			endnodeout			
			endif

			if taSuppParty[build("SUPP_ID_PartyIdentification_SchemeID", nSuppParty_temp)] = "SEPA" then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
				eEcore:AgencyCodedOther := "SEPA"
			endnodeout
		else
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout
		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "N/A"
		endnodeout
			endif
		endwhile

			nSuppTaxScheme_temp := 0
			while (nSuppTaxScheme > nSuppTaxScheme_temp) do 
			nSuppTaxScheme_temp++

			if taSUPPTaxScheme[build("SUPP_PartyTaxScheme_ID", nSuppTaxScheme_temp)] ="VAT" then
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
				eEcore:Ident := taSUPPTaxScheme[build("SUPP_PartyTaxScheme_CompanyID", nSuppTaxScheme_temp)]
			endnodeout
			else	
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
				eEcore:Ident := taSUPPTaxScheme[build("SUPP_PartyTaxScheme_CompanyID", nSuppTaxScheme_temp)]
			endnodeout

			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency	
				eEcore:AgencyCoded := "Other"
			endnodeout
			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency	
				if taSUPPTaxScheme[build("SUPP_PartyTaxScheme_ID", nSuppTaxScheme_temp)] = "!=VAT" then
				eEcore:AgencyCodedOther := "notVAT"
				else
				eEcore:AgencyCodedOther := taSUPPTaxScheme[build("SUPP_PartyTaxScheme_ID", nSuppTaxScheme_temp)]
				endif
			endnodeout
			endif
			endwhile
			

	! Sales party name
	 	if taENTETE["SUPP_Party_Identification_Name"] <> EMPTY then	
		nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
			eEcore:Ident := taENTETE["SUPP_Party_Identification_Name"]
		endnodeout
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout

		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
			eEcore:AgencyCodedOther := "PartyNameIdentification"
		endnodeout
		endif


		
		nodeout Score:Name1 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:NameAddress     ! M 1/1 
			eEcore:Name1 := taENTETE["SUPP_Name"]
		endnodeout

		nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
			eEcore:Name2 := taENTETE["SUPP_AdressLine"]
		endnodeout

		nodeout Score:Street gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:NameAddress     ! C 1/1 
			eEcore:Street := taENTETE["SUPP_StreetName"]
		endnodeout

		nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
			eEcore:StreetSupplement1 := taENTETE["SUPP_AdditionalStreetName"]
		endnodeout

		nodeout Score:PostalCode gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:NameAddress     ! C 1/1 
		eEcore:PostalCode  := taENTETE["SUPP_PostalZone"]
		endnodeout

		nodeout Score:City gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:NameAddress     ! C 1/1 
		eEcore:City  := taENTETE["SUPP_CityName"]
		endnodeout

		nodeout Score:District gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
			eEcore:District := taENTETE["SUPP_Country_Subentity"]
		endnodeout

		nodeout Score:CountryCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:NameAddress,gGcore:Country     ! M 1/1 
		eEcore:CountryCoded  := taENTETE["SUPP_IdentificationCode"]
		endnodeout



		nodeout Score:ContactName gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PrimaryContact
			eEcore:ContactName := taENTETE["SUPP_Party_Contact_Name"]
		endnodeout
		
			nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberValue := taENTETE["SUPP_Party_Contact_Telephone"]
			endnodeout

			if taENTETE["SUPP_Party_Contact_Telephone"] <> EMPTY then
			nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberTypeCoded := "TelephoneNumber"
			endnodeout
			endif
			

			nodeout Score:ContactNumberValue gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberValue := taENTETE["SUPP_Party_Contact_ElectronicMail"]
			endnodeout

			if taENTETE["SUPP_Party_Contact_ElectronicMail"] <> EMPTY then
			nodeout Score:ContactNumberTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PrimaryContact, gGcore:ListOfContactNumber, gGcore:ContactNumber
				eEcore:ContactNumberTypeCoded := "EmailAddress"
			endnodeout
			endif



! Payee Party
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
			if taENTETE["PayeePartyIdentificationIDschemeID"] = "0035" then
				eEcore:Ident := build("BP: ",taENTETE["PayeePartyIdentificationID"])
			else
				eEcore:Ident := taENTETE["PayeePartyIdentificationID"]
			endif
			endnodeout

			if taENTETE["PayeePartyIdentificationIDschemeID"] <> EMPTY then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			endif

			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID, gGcore:Agency
				eEcore:AgencyCodedOther := taENTETE["PayeePartyIdentificationIDschemeID"]
			endnodeout

			nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Name1 := taENTETE["PayeePartyName"]
			endnodeout

			if taENTETE["PayeeCompanyID"] <> EMPTY then
			nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
				eEcore:PartyRoleCoded := "Beneficiary"
			endnodeout
			endif

			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
				eEcore:Ident := taENTETE["PayeeCompanyID"]
			endnodeout



! Tax representative party CompanyID

			if taENTETE["TaxRepresentativeTaxSchemeID"] <> EMPTY then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			endif

			
			if taENTETE["TaxRepresentativeTaxSchemeID"] <> EMPTY then
			nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
				eEcore:PartyRoleCoded := "TaxAuthority"
			endnodeout
			endif



			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID, gGcore:Agency
				eEcore:AgencyCodedOther := taENTETE["TaxRepresentativeTaxSchemeID"]
			endnodeout

			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
				eEcore:Ident := taENTETE["TaxRepresentativeCompanyID"]
			endnodeout

			if taENTETE["TaxRepresentativeName"] <> EMPTY then
			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
				eEcore:Ident := "TaxAuthority"
			endnodeout
			endif

			nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Name1 := taENTETE["TaxRepresentativeName"]
			endnodeout		

			nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Street := taENTETE["TaxRepresentativeStreetName"]
			endnodeout		

			nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:StreetSupplement1 := taENTETE["TaxRepresentativeAdditionalStreetName"]
			endnodeout		

			nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:City := taENTETE["TaxRepresentativeCityName"]
			endnodeout		

			nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:PostalCode := taENTETE["TaxRepresentativePostalZone"]
			endnodeout		

			nodeout Score:District gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:District := taENTETE["TaxRepresentativeCountrySubentity"]
			endnodeout		

			nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Name2 := taENTETE["TaxRepresentativeAdressLine"]
			endnodeout		

			nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
				eEcore:CountryCoded := taENTETE["TaxRepresentaticeCountry"]
			endnodeout


			nodeout Score:RegisteredName gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation
				eEcore:RegisteredName := taENTETE["SUPP_PartyLegalEntity_RegistrationName"]
			endnodeout

! Delivery

	
			if taENTETE["DeliveryLocationIDschemeID"] <> EMPTY then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			endif

			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID, gGcore:Agency
				eEcore:AgencyCodedOther := taENTETE["DeliveryLocationIDschemeID"]
			endnodeout

			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:PartyID
				if taENTETE["DeliveryLocationIDschemeID"] = "0035" then
					eEcore:Ident := build("BP: ",taENTETE["DeliveryLocationID"])
				else
					eEcore:Ident := taENTETE["DeliveryLocationID"]
				endif
			endnodeout
			
			nodeout Score:PartyRoleCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded
				eEcore:PartyRoleCoded := "Delivery"
			endnodeout

			nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Name1 := taENTETE["DeliveryPartyName"]
			endnodeout

			nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Name2 := taENTETE["DeliveryLocationAddressLine"]
			endnodeout

			nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:Street := taENTETE["DeliveryLocationStreetName"]
			endnodeout
			
			nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:StreetSupplement1 := taENTETE["DeliveryLocationAdditionalStreetName"]
			endnodeout
			
			nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:PostalCode := taENTETE["DeliveryLocationPostalZone"]
			endnodeout

			nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:City := taENTETE["DeliveryLocationCityName"]
			endnodeout

			nodeout Score:District gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress
				eEcore:District := taENTETE["DeliveryLocationCountrySubentity"]
			endnodeout

			nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:NameAddress, gGcore:Country
				eEcore:CountryCoded := taENTETE["DeliveryLocationCountry"]
			endnodeout


			nodeout Score:CompanyRegistrationNumber gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation
				eEcore:CompanyRegistrationNumber := taENTETE["SUPP_PartyLegalEntity_CompanyID"]
			endnodeout

			nodeout Score:RegisteredOffice gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation
				eEcore:RegisteredOffice := taENTETE["SUPP_PartyLegalEntity_CompanyLegalForm"]
			endnodeout

			if taENTETE["DeliveryLocationIDschemeID"] <> EMPTY then
			nodeout Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:LocationIdentifier, gGcore:LocID, gGcore:Agency
				eEcore:AgencyCoded := "Other"
			endnodeout
			endif

			nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:LocationIdentifier, gGcore:LocID, gGcore:Agency
				if taENTETE["DeliveryLocationIDschemeID"] = "0035" then
					eEcore:AgencyCodedOther := "BP"
				else
					eEcore:AgencyCodedOther := taENTETE["DeliveryLocationIDschemeID"]
				endif
			endnodeout

			nodeout Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:LocationIdentifier, gGcore:LocID
				eEcore:Ident := taENTETE["DeliveryLocationID"]
			endnodeout 

			nodeout Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:Street := taENTETE["DeliveryLocationStreetName"]
			endnodeout

			nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:StreetSupplement1 := taENTETE["DeliveryLocationAdditionalStreetName"]
			endnodeout

			nodeout Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:StreetSupplement1 := taENTETE["DeliveryLocationAdditionalStreetName"]
			endnodeout

			nodeout Score:City gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:City := taENTETE["DeliveryLocationCityName"]
			endnodeout

			nodeout Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:PostalCode := taENTETE["DeliveryLocationPostalZone"]
			endnodeout

			nodeout Score:District gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:District := taENTETE["DeliveryLocationCountrySubentity"]
			endnodeout

			nodeout Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress, gGcore:Country
				eEcore:CountryCoded := taENTETE["DeliveryLocationCountry"]
			endnodeout

			nodeout Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:Name1 := taENTETE["DeliveryPartyName"]
			endnodeout

			nodeout Score:Name2 gGInvoice, gGInvoiceHeader, gGInvoiceTermsOfDelivery, gGcore:Location, gGcore:NameAddress
				eEcore:Name2 := taENTETE["DeliveryLocationAddressLine"]
			endnodeout





			if taENTETE["ChargeIndicator"] <> EMPTY then
			nodeout Score:IndicatorCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge
				if taENTETE["ChargeIndicator"] = "true" then
					eEcore:IndicatorCoded := "Charge"
				else	
					eEcore:IndicatorCoded := "Allowance"
				endif
			endnodeout

			nodeout Score:MethodOfHandlingCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge
				eEcore:MethodOfHandlingCoded := "Other"
			endnodeout

			nodeout Score:MethodOfHandlingCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge
				eEcore:MethodOfHandlingCodedOther := "N/A"
			endnodeout

			if taENTETE["AllowanceChargeReasonCode"] = "DL" then
			nodeout Score:ServiceCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
				eEcore:ServiceCoded := "Delivery"
			endnodeout
			else
			nodeout Score:ServiceCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
				eEcore:ServiceCoded := "Other"
			endnodeout
			nodeout Score:ServiceCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
					eEcore:ServiceCodedOther := taENTETE["AllowanceChargeReasonCode"]
			endnodeout
			endif

			nodeout Score:ListOfDescription gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
				eEcore:ListOfDescription := taENTETE["AllowanceChargeReason"]
			endnodeout

			nodeout Score:PercentQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentQualifier
				eEcore:PercentQualifierCoded := "Other"
			endnodeout

			nodeout Score:PercentQualifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentQualifier
				eEcore:PercentQualifierCodedOther := "Other"
			endnodeout

			nodeout Score:Percent gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge
				eEcore:Percent := taENTETE["AllowanceChargeMultiplierFactorNumeric"]
			endnodeout

			nodeout Score:MonetaryAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentageMonetaryValue
				eEcore:MonetaryAmount := taENTETE["AllowanceChargeAmount"]
			endnodeout
			
			nodeout Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:PercentageAllowanceOrCharge, gGcore:PercentageMonetaryValue, gGcore:Currency
				eEcore:CurrencyCoded := taENTETE["AllowanceChargeAmountcurrencyID"] 
			endnodeout

			nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxTypeCoded := "ValueAddedTax"
			endnodeout

			nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxFunctionQualifierCoded := "Other"
			endnodeout

			nodeout Score:TaxFunctionQualifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxFunctionQualifierCodedOther := "N/A"
			endnodeout

			nodeout Score:TaxPercent gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxPercent := taENTETE["AllowanceChargeTaxCategoryPercent"]
			endnodeout

			nodeout Score:TaxableAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxableAmount := taENTETE["AllowanceChargeBaseAmount"]
			endnodeout




			if taENTETE["AllowanceChargeTaxSchemeID"] <> EMPTY then

			nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxTypeCoded := "Other"
			endnodeout

			nodeout Score:TaxTypeCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxTypeCodedOther := "N/A"
			endnodeout

			nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxFunctionQualifierCoded := "Other"
			endnodeout

			nodeout Score:TaxFunctionQualifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxFunctionQualifierCodedOther := "N/A"
			endnodeout

			nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxCategoryCoded := "Other"
			endnodeout

			nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				if taENTETE["AllowanceChargeTaxSchemeID"] = "VAT" then
					eEcore:TaxCategoryCodedOther := "StandardRate"
				else 
					eEcore:TaxCategoryCodedOther := "N/A"
				endif
			endnodeout

			if taENTETE["AllowanceChargeTaxCategoryID"] = "E" then
			nodeout Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:ReasonTaxExemptCoded := "Yes-TaxExempt"
			endnodeout
			endif

			nodeout Score:TaxPercent gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				eEcore:TaxPercent := taENTETE["AllowanceChargeTaxCategoryPercent"]
			endnodeout

			nodeout Score:TaxAmount gGInvoice, gGInvoiceHeader, gGInvoiceAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:Tax
				if taENTETE["AllowanceChargeTaxCategoryPercent"] = "0" then
					eEcore:TaxAmount := "0"
				endif
			endnodeout 

				endif	
				endif


			nodeout Score:PaymentTermsNote gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms
					eEcore:PaymentTermsNote := taENTETE["PaymentTermsNote"]
			endnodeout

			nodeout Score:PaymentMeanCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod
				if taENTETE["PaymentMeansCode"] = "30" then
					eEcore:PaymentMeanCoded := "CreditTransfer"
				else
					eEcore:PaymentMeanCoded := taENTETE["PaymentMeansCode"]
				endif
			endnodeout

			nodeout Score:PaymentMeanCodedOther gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod
					eEcore:PaymentMeanCodedOther := taENTETE["PaymentMeansCodeName"]
			endnodeout

			nodeout Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:PaymentMeanReference
				eEcore:RefNum := taENTETE["PaymentID"]
			endnodeout

			nodeout Score:AccountID gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:FITransfer, gGcore:ToFITransfer, gGcore:AccountDetail
				eEcore:AccountID := taENTETE["PayeeFinancialAccount"]
			endnodeout
			
			if taENTETE["PayeeFinancialAccountName"] <> EMPTY then
			nodeout Score:AccountTypeCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:FITransfer, gGcore:ToFITransfer, gGcore:AccountDetail
				eEcore:AccountTypeCoded := "Other"
			endnodeout

			nodeout Score:AccountTypeCodedOther gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:FITransfer, gGcore:ToFITransfer, gGcore:AccountDetail
				eEcore:AccountTypeCodedOther := "Payee"
			endnodeout
			endif

			nodeout Score:AccountName1 gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:FITransfer, gGcore:ToFITransfer, gGcore:AccountDetail
				eEcore:AccountName1 := taENTETE["PayeeFinancialAccountName"]
			endnodeout

			nodeout Score:FinancialInstitutionID gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:FITransfer, gGcore:ToFITransfer, gGcore:FinancialInstitution
				eEcore:FinancialInstitutionID := taENTETE["FinancialInstitutionBranch"]
			endnodeout
			
			if taENTETE["FinancialInstitutionBranch"] <> EMPTY then
			nodeout Score:FinancialInstitutionName gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentMethod, gGcore:FITransfer, gGcore:ToFITransfer, gGcore:FinancialInstitution
				eEcore:FinancialInstitutionName := "N/A"
			endnodeout
			endif


		nHeaderNotes_temp := 0
		while ( nHeaderNotes_temp < nHeaderNotes) do
			nHeaderNotes_temp++

			nodeout Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
				eEcore:GeneralNote := taHeaderNotes[build("Note", nHeaderNotes_temp)]
			endnodeout
		endwhile



		! Lignes
		
			nLin_temp := 0
			while (nLin_temp < nLin) do
			nLin_temp++
			
					nodeout Score:BuyerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum
					eEcore:BuyerLineItemNum := taLIGNE[build("ID", nLin_temp)]
					endnodeout

			nLinNotes_temp := 0
			while (nLinNotes_temp < nLinNotes) do
				nLinNotes_temp++
				nodeout Score:GeneralNote gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfStructuredNote, gGcore:StructuredNote
					eEcore:GeneralNote := taLIGNENOTES[build("Note", nLin_temp,"_", nLinNotes_temp)]
				endnodeout
			endwhile

			nodeout Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
					eEcore:PriceTypeCoded := "CalculationNet"
			endnodeout
			
			if taLIGNE[build("BaseQuantity", nLin_temp)] <> EMPTY then 
			nodeout Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
					eEcore:UnitPriceValue := build(number(taLIGNE[build("PriceAmount", nLin_temp)]) * number(taLIGNE[build("BaseQuantity", nLin_temp)]))
			endnodeout
			endif


			nodeout Score:CurrencyCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice, gGcore:Currency
				if taLIGNE[build("currencyIDPriceAmount", nLin_temp)] = "H87" then
				eEcore:CurrencyCoded := "PCE"
				else
				eEcore:CurrencyCoded := taLIGNE[build("currencyIDPriceAmount", nLin_temp)]
				endif

			endnodeout


		if taLIGNE[build("StartDate", nLin_temp)] <> EMPTY then
		nodeout Score:StartDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates, gGInvoicingPeriod
			eEcore:StartDate := build(taLIGNE[build("StartDate", nLin_temp)],"T00:00:00")	
		endnodeout
		endif

		if taLIGNE[build("EndDate", nLin_temp)] <> EMPTY then
		nodeout Score:EndDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates, gGInvoicingPeriod
			eEcore:EndDate := build(taLIGNE[build("EndDate", nLin_temp)],"T00:00:00")	
		endnodeout
		endif


		if taLIGNE[build("OrderLineReferenceID", nLin_temp)] <> EMPTY then
		nodeout Score:BuyerOrderNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGPurchaseOrderReference
			eEcore:BuyerOrderNumber := "N/A"
		endnodeout
		endif

		nodeout Score:PurchaseOrderLineItemNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGPurchaseOrderReference
			eEcore:PurchaseOrderLineItemNumber := taLIGNE[build("OrderLineReferenceID", nLin_temp)]
		endnodeout



		switch (taLIGNE[build("schemeIDDocumentReferenceID", nLin_temp)])
			case "ABZ": 
				nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
					eEcore:ReferenceTypeCoded := "LicensePlateNumber"
				endnodeout
			default: 
				nodeout Score:ReferenceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
					eEcore:ReferenceTypeCoded := "Other"
				endnodeout
			
				nodeout Score:ReferenceTypeCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
					eEcore:ReferenceTypeCodedOther := taLIGNE[build("schemeIDDocumentReferenceID", nLin_temp)]
				endnodeout
		endswitch

		nodeout Score:RefNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded, gGcore:PrimaryReference
			eEcore:RefNum := taLIGNE[build("DocumentReferenceID", nLin_temp)]
		endnodeout

		nodeout Score:ReferenceDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGOtherInvoiceReferences, gGcore:ReferenceCoded
			eEcore:ReferenceDescription := taLIGNE[build("DocumentReferenceDocumentTypeCode", nLin_temp)]
		endnodeout

		nodeout Score:ItemDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers
			eEcore:ItemDescription := taLIGNE[build("Description", nLin_temp)]
		endnodeout







		nodeout SLineItemNote gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail
			eELineItemNote := taLIGNE[build("Name", nLin_temp)]
		endnodeout

		
		switch (taLIGNE[build("AdditionalItemPropertyName", nLin_temp)])

		case "Colour" :

		nodeout Score:ItemCharacteristicCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:ListOfItemCharacteristic, gGcore:ItemCharacteristic
			eEcore:ItemCharacteristicCoded := "Color"
		endnodeout
		
		nodeout Score:ItemCharacteristicValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:ListOfItemCharacteristic, gGcore:ItemCharacteristic
			eEcore:ItemCharacteristicValue := taLIGNE[build("AdditionalItemPropertyValue", nLin_temp)]
		endnodeout
		nodeout Score:Ident gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:ListOfItemCharacteristic, gGcore:ItemCharacteristic, gGcore:Identifier
			eEcore:Ident := taLIGNE[build("AdditionalItemPropertyValue", nLin_temp)]
		endnodeout


		default:
		if taLIGNE[build("AdditionalItemPropertyName", nLin_temp)] <> EMPTY then
		nodeout Score:ItemCharacteristicCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:ListOfItemCharacteristic, gGcore:ItemCharacteristic
			eEcore:ItemCharacteristicCoded := "Other"
		endnodeout
		endif

		nodeout Score:ItemCharacteristicCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:ListOfItemCharacteristic, gGcore:ItemCharacteristic
			eEcore:ItemCharacteristicCodedOther := taLIGNE[build("AdditionalItemPropertyName", nLin_temp)]
		endnodeout

		nodeout Score:ItemCharacteristicValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:ListOfItemCharacteristic, gGcore:ItemCharacteristic
			eEcore:ItemCharacteristicValue := taLIGNE[build("AdditionalItemPropertyValue", nLin_temp)]
		endnodeout

		endswitch


		nodeout Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:BuyerPartNumber
					eEcore:PartID := taLIGNE[build("BuyersItemIdentification", nLin_temp)]
		endnodeout

		nodeout Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:SellerPartNumber
					eEcore:PartID := taLIGNE[build("SellersItemIdentification", nLin_temp)]
		endnodeout

		switch (taLIGNE[build("schemeIDStandardItemIdentification_ID", nLin_temp)])
		case "0160" :
		nodeout Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:OtherItemIdentifiers, gGcore:ProductIdentifierCoded
			eEcore:ProductIdentifierQualifierCoded := "GTIN"
		endnodeout
		
		nodeout Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:OtherItemIdentifiers, gGcore:ProductIdentifierCoded
			eEcore:ProductIdentifier := taLIGNE[build("StandardItemIdentification_ID", nLin_temp)]
		endnodeout
		endswitch


		switch (taLIGNE[build("listIDItemClassificationCode", nLin_temp)])
		case "MP" :
		nodeout Score:AgencyCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:CommodityCode, gGcore:Agency
			eEcore:AgencyCoded := "Other"
		endnodeout

		nodeout Score:AgencyCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:CommodityCode, gGcore:Agency
			eEcore:AgencyCodedOther := "Product/service identification number"
		endnodeout
		
		nodeout Score:Ident gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:CommodityCode
			eEcore:Ident := taLIGNE[build("ItemClassificationCode", nLin_temp)]
		endnodeout
		endswitch


			nodeout Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity
				eEcore:QuantityValue := taLIGNE[build("InvoicedQuantity", nLin_temp)]
			endnodeout 

			nodeout Score:UOMCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCoded := "Other"
			endnodeout

			if taLIGNE[build("unitCodeInvoicedQuantity", nLin_temp)] = "H87" then	
			nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCodedOther := "PCE"
			endnodeout
			else
			nodeout Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
					eEcore:UOMCodedOther := taLIGNE[build("unitCodeInvoicedQuantity", nLin_temp)]
			endnodeout
			endif


			nodeout Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
				eEcore:PriceTypeCoded := "CalculationGross"
			endnodeout

			nodeout Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
				eEcore:UnitPriceValue := taLIGNE[build("PriceAmount", nLin_temp)]
			endnodeout

			nodeout Score:CurrencyCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice, gGcore:Currency
				eEcore:CurrencyCoded := taLIGNE[build("currencyIDPriceAmount", nLin_temp)]
			endnodeout

			nodeout Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:LineItemTotal
				eEcore:MonetaryAmount := taLIGNE[build("LineExtensionAmount", nLin_temp)]
			endnodeout

			nodeout Score:CurrencyCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:LineItemTotal, gGcore:Currency
				eEcore:CurrencyCoded := taLIGNE[build("currencyIDLineExtensionAmount", nLin_temp)]
			endnodeout	

			nodeout Score:TaxPercent gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
				eEcore:TaxPercent := taLIGNE[build("Percent", nLin_temp)]
			endnodeout

			nodeout Score:CountryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGCountryOfOrigin
				eEcore:CountryCoded := taLIGNE[build("OriginCountryCode", nLin_temp)]
			endnodeout


			if taLIGNE[build("AccountingCost", nLin_temp)] <> EMPTY then
			nodeout Score:SetName gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfNameValueSet, gGcore:NameValueSet
				eEcore:SetName := "EURINV"
			endnodeout
			endif

			if taLIGNE[build("AccountingCost", nLin_temp)] <> EMPTY then
			nodeout Score:Name gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
				eEcore:Name := "AccountingCost"
			endnodeout
			endif

			nodeout Score:Value gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGListOfNameValueSet, gGcore:NameValueSet, gGcore:ListOfNameValuePair, gGcore:NameValuePair
				eEcore:Value := taLIGNE[build("AccountingCost", nLin_temp)]
			endnodeout






			nLinAll_temp := 0

				while (nLinAll_temp < nLinAll) do
			
					nLinAll_temp++

				nodeout Score:IndicatorCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge
				switch  (taDETAILALLOWANCES[build("ChargeIndicator", nLin_temp,"_", nLinAll_temp)])
					case "false" :
						eEcore:IndicatorCoded := "Allowance"
					case "true" :
						eEcore:IndicatorCoded := "Charge"
				endswitch
				endnodeout

				nodeout Score:ServiceCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
				switch (taDETAILALLOWANCES[build("ReasonCode", nLin_temp,"_", nLinAll_temp)])
					case "100" :
						eEcore:ServiceCoded := "Rebate"
				endswitch			
				endnodeout

				nodeout Score:ListOfDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:AllowanceOrChargeDescription
					eEcore:ListOfDescription := taDETAILALLOWANCES[build("Reason", nLin_temp,"_", nLinAll_temp)]
				endnodeout

				nodeout Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:MonetaryValue
					eEcore:MonetaryAmount := taDETAILALLOWANCES[build("LineChargeAmount", nLin_temp,"_", nLinAll_temp)]
				endnodeout

				nodeout Score:CurrencyCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ItemAllowancesOrCharges, gGcore:AllowOrCharge, gGcore:TypeOfAllowanceOrCharge, gGcore:MonetaryValue, gGcore:Currency
					eEcore:CurrencyCoded := taDETAILALLOWANCES[build("LineChargeAmountCurr", nLin_temp, "_", nLinAll_temp)]
				endnodeout

			endwhile


		endwhile

		! Summary
		
		nodeout Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGInvoiceTotal     ! M 1/1 
			eEcore:MonetaryAmount  := taPIED["TaxInclusiveAmount"] !tAmount ! M  
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGInvoiceTotal, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["TaxInclusiveAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGInvoiceSubTotal     ! M 1/1 
			eEcore:MonetaryAmount  := taPIED["LineExtensionAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGInvoiceSubTotal, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["LineExtensionAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTaxableValue     ! M 1/1 
			eEcore:MonetaryAmount  := taPIED["TaxExclusiveAmount"] !tAmountAllCh ! M  
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTaxableValue, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["TaxExclusiveAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGChargeTotal
			eEcore:MonetaryAmount := taPIED["ChargeTotalAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGChargeTotal, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["ChargeTotalAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGAllowanceTotal
			eEcore:MonetaryAmount := taPIED["AllowanceTotalAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGAllowanceTotal, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["AllowanceTotalAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGPrepaidAmount
			eEcore:MonetaryAmount := taPIED["PrepaidAmountAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGPrepaidAmount, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["PrepaidAmountAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTotalDiscount
			eEcore:MonetaryAmount := taPIED["PayableRoundingAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTotalDiscount, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["PayableRoundingAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTotalAmountPayable
			eEcore:MonetaryAmount := taPIED["PayableAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTotalAmountPayable, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["PayableAmount_currencyID"]
		endnodeout

		nodeout Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGPrepaidAmount
			eEcore:MonetaryAmount := taPIED["PrepaidAmount"]
		endnodeout

		nodeout Score:CurrencyCoded gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGPrepaidAmount, gGcore:Currency
			eEcore:CurrencyCoded := taPIED["PrepaidAmount_currencyID"]
		endnodeout


		nodeout Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTotalTaxAmount     ! M 1/1 
			eEcore:MonetaryAmount  := build(number(taPIED["TaxInclusiveAmount"]) - number(taPIED["TaxExclusiveAmount"]))  
		endnodeout

		nTaxSubTotal_temp := 0
		nTaxTotal_temp := 0
			while (nTaxTotal_temp < nTaxTotal) do
			nTaxTotal_temp++
			while (nTaxSubTotal_temp < nTaxSubTotal) do	
			nTaxSubTotal_temp++

		nodeout Score:TaxTypeCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxTypeCoded := "Other"
		endnodeout

		nodeout Score:TaxTypeCodedOther gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxTypeCodedOther := taTaxSubTotal[build("TaxCategoryPercent", nTaxTotal_temp, "_", nTaxSubTotal_temp)]
		endnodeout

		if taTaxSubTotal[build("TaxableAmount_Currency", nTaxTotal_temp, "_", nTaxSubTotal_temp)] = tCurrency then
		nodeout Score:TaxableAmount gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxableAmount := taTaxSubTotal[build("TaxableAmount", nTaxTotal_temp, "_", nTaxSubTotal_temp)]
		endnodeout
		else
		nodeout Score:TaxableAmountInTaxAccountingCurrency gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxableAmountInTaxAccountingCurrency := taTaxSubTotal[build("TaxableAmount", nTaxTotal_temp, "_", nTaxSubTotal_temp)]
		endnodeout
		endif	
	
		nodeout Score:TaxAmountInTaxAccountingCurrency gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxAmountInTaxAccountingCurrency := taTaxSubTotal[build("TaxAmount", nTaxTotal_temp, "_", nTaxSubTotal_temp)]
		endnodeout

		nodeout Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxFunctionQualifierCoded := "Other"
		endnodeout

		nodeout Score:TaxFunctionQualifierCodedOther gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxFunctionQualifierCodedOther := taTaxSubTotal[build("TaxCategoryTaxExemptionReason", nTaxTotal_temp, "_", nTaxSubTotal_temp)]
		endnodeout

		nodeout Score:TaxCategoryCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxCategoryCoded := "Other"
		endnodeout

		if taTaxSubTotal[build("TaxCategoryID", nTaxTotal_temp, "_", nTaxSubTotal_temp)] = "O" then

		nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxCategoryCodedOther := "0"
		endnodeout
		else
		nodeout Score:TaxCategoryCodedOther gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
			eEcore:TaxCategoryCodedOther := taTaxSubTotal[build("TaxCategoryPercent", nTaxTotal_temp, "_", nTaxSubTotal_temp)]
		endnodeout
		endif

			endwhile
			endwhile
			
		print(MESSAGE)
		log("Fin du traitement")
	endfunction



