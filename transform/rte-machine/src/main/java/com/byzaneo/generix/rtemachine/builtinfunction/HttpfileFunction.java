package com.byzaneo.generix.rtemachine.builtinfunction;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.http.HttpHeaders.CONTENT_TYPE;

import java.io.File;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.springframework.http.MediaType;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.exception.RteException;

public class HttpfileFunction extends RestFunction {
  @Override
  protected Object processConnection(RteRuntime runtime, HttpResponse response) throws Exception {
    if(response != null) {
      Object processOutputDir = runtime.getParamValuesProperties().get("PROCESS_OUTPUT_DIR");
      String fileName = "";
  
      if (processOutputDir != null) {
        fileName = processOutputDir.toString();
      }
  
      if (processOutputDir == null || StringUtils.isEmpty(fileName)) {
        throw new RteException("The PROCESS_OUTPUT_DIR variable could not be obtained");
      }
  
      String contDisp = getContentDisposition(response);
  
      if (contDisp != null && contDisp.indexOf("=") != -1) {
        fileName = fileName + File.separator + contDisp.split("=")[1];
      } else if (response.getHeaders(CONTENT_TYPE) != null && !"html".equals(MediaType.valueOf(getContentType(response)).getSubtype())) {
        fileName = fileName + File.separator + "response" + FileType.valueOf(MediaType.valueOf(getContentType(response))).getExtension();
      }
      else {
        throw new RteException("The file name could not be obtain from the http response");
      }
      
      FileUtils.copyInputStreamToFile(response.getEntity().getContent(), new File(fileName));
      return fileName.replace("\\", "\\\\");
    }
    
    return EMPTY;
  }
  
  private String getContentType(HttpResponse response) {
    return getHeader(response, CONTENT_TYPE);
  }
  
  private String getContentDisposition(HttpResponse response) {
    return getHeader(response, "Content-Disposition");
  }
  
  private String getHeader(HttpResponse response, String name) {
    if (response.getHeaders(name).length > 0) {
      return response.getHeaders(name)[0].getValue();
    }
    return EMPTY;
  }
}
