package com.byzaneo.generix.transform.edi.process;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.api.Variable.instance_company_identifier;
import static com.byzaneo.generix.util.DocumentCompoundTypeHelper.createDocumentCompoundType;
import static com.byzaneo.xtrade.api.DocumentType.INTERCHANGE_EDIFACT;
import static com.byzaneo.xtrade.process.Variable.PROCESS_INPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OWNER;
import static com.byzaneo.xtrade.process.Variable.REPORT_DOCUMENT;
import static com.byzaneo.xtrade.process.VariableHelper.getBoolean;
import static com.byzaneo.xtrade.process.VariableHelper.getFile;
import static com.byzaneo.xtrade.process.VariableHelper.getVariable;
import static com.byzaneo.xtrade.process.VariableHelper.isCheckIntegrity;
import static com.byzaneo.xtrade.process.VariableHelper.newDocumentInstancePopulated;
import static java.lang.Boolean.TRUE;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.springframework.util.Assert.isTrue;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.Charset;
import java.util.*;

import org.activiti.engine.delegate.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.*;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.exception.ProcessInterruptedException;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.Indexable;
import com.byzaneo.xtrade.process.reprocess.Reprocess;
import com.byzaneo.xtrade.process.Variable;
import com.byzaneo.xtrade.process.VariableHelper;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.process.task.*;
import com.byzaneo.xtrade.service.IntegrityService;
import com.influe.shared.*;

@Task(name = "EdifactSplitter", library = "process/business/edi", version = "1.0.0")
@TaskViewModel(labelFamily = "edifactsplitterlbls")
public class EdifactSplitterTask extends AbstractTask implements CancelableTask {

  /**
   * Engine configuration
   */
  @TaskProperty(group = "charset", label = "#{edifactsplitterlbls.inputCharset}")
  protected Expression inputCharset;
  /**
   * Engine configuration
   */
  @TaskProperty(group = "charset", label = "#{edifactsplitterlbls.outputCharset}")
  protected Expression outputCharset;

  public static final String VARIABLE_OWNER = "instance_company_identifier";

  public enum ContextValue {
    CONTEXT("${context}");

    private final String name;

    ContextValue(String name) {
      this.name = name;
    }

    @Override
    public String toString() {
      return name;
    }
  }

  private static final String TAX_ORIGINAL = "taxOriginal";

  private static final long serialVersionUID = 6310615013028064384L;
  private static final Logger log = LoggerFactory.getLogger(EdifactSplitterTask.class);

  /** source directory which contains the EDIFACT message to split. */
  @TaskProperty(label = "#{edifactsplitterlbls.folder_in}", required = true, labelPrefixed = false)
  private Expression source;
  /** FileFilter used to retrieve the EDI files in the <code>sourceDirectory</code>. (Default: "*.*") */
  @TaskProperty(label = "#{edifactsplitterlbls.filter}", labelPrefixed = false)
  private Expression filter;
  /** Destination directory. If it's null, a directory named "splitted" will be append to the source directory. */
  @TaskProperty(label = "#{edifactsplitterlbls.folder_out}", required = true, labelPrefixed = false)
  private Expression destination;

  @TaskProperty(label = "#{edifactsplitterlbls.taxOriginal}", type = Boolean.class)
  protected Expression taxOriginal = new FixedValue(Boolean.TRUE);

  private transient IntegrityService integrityService;

  public EdifactSplitterTask() {
    integrityService = getBean(IntegrityService.class, IntegrityService.SERVICE_NAME);
  }

  @Override
  public void executeTask(DelegateExecution execution) throws Exception {
    log.debug("Start EdifactSplitter");
    try {
      /* gestion des documents en entree */
      List<DocumentFile> filesToSplit = selectFiles(execution);

      boolean contextOut = ContextValue.CONTEXT.toString()
          .equals(destination.getExpressionText());
      File destDir = makeDestDir(execution, contextOut);

      log.info("Files to split: {}", filesToSplit.size());

      for (DocumentFile fileToSplit : filesToSplit) {

        if (Thread.currentThread()
            .isInterrupted()) {
          throw new ProcessInterruptedException("Canceled by user");
        }
        log.info("Split {}", fileToSplit.getFile()
            .getAbsolutePath());

        NormeFile reader;

        if (inputCharset != null && StringUtils.isNotEmpty(inputCharset.getExpressionText())) {
          log.info("Reading input file in {} charset.", inputCharset.getExpressionText());
          reader = new NormeFile(new InputStreamReader(new FileInputStream(fileToSplit.getFile()
              .getAbsolutePath()),
              Charset.forName(inputCharset.getExpressionText())));
        }
        else {
          reader = new NormeFile(fileToSplit.getFile()
              .getAbsolutePath());
        }
        try {
          reader.openNormeFile();
          reader.getNormeFile();
          String norme = reader.getNorme();

          if (reader.NLF_EDIFACT.equals(norme)) {
            split(execution, reader, destDir, contextOut, fileToSplit);
          }
          else {
            documentInError(execution, fileToSplit, "Norm is not Edifact");
          }
        }
        catch (Exception e) {
          documentInError(execution, fileToSplit, getRootCauseMessage(e));
        }
        finally {
          try {
            reader.closeNormeFile();
          }
          catch (Exception e) {
          }
          log.debug("End split {}", fileToSplit);
        }
      }
    }
    catch (

    Exception e) {
      log.error("Error : {}", getRootCauseMessage(e), e);
      throw e;
    }
    finally {
      log.debug("End EdifactSplitter");
    }
  }

  private void documentInError(DelegateExecution execution, DocumentFile fileToSplit, String message)
      throws IllegalAccessException, InvocationTargetException {
    Document docInterchange = fileToSplit.getDocument();
    if (docInterchange == null) {
      docInterchange = createDocument(execution, fileToSplit.getFile(), INTERCHANGE_EDIFACT.toString(), DocumentStatus.PENDING,
          FileType.EDI, 0);
    }
    docInterchange.setStatusWithEnumValue(DocumentStatus.ERROR);
    docInterchange.setError(message);
    VariableHelper.addDeads(execution, true, docInterchange);
    if (isCheckIntegrity(execution)) {
      integrityService.updateIntegrity(Arrays.asList(docInterchange), true);
    }
    log.error("Error : {} cause = {}", docInterchange.getFirstFile(), message);
  }

  private List<DocumentFile> selectFiles(DelegateExecution execution) {
    List<DocumentFile> filesToSplit = new ArrayList<DocumentFile>();
    if (ContextValue.CONTEXT.toString()
        .equals(source.getExpressionText())) {
      log.debug("Src Dir = {}", ContextValue.CONTEXT);

      List<Document> listDoc = VariableHelper.getDocuments(execution);

      for (Document document : listDoc) {

        // mise à jour du doc_type (cas GCN/Rest Trigger)
        if (document.getType() == null) {
          document.setType(INTERCHANGE_EDIFACT);
        }

        // mise à jour du file_type (cas GCN/Rest Trigger)
        DocumentFile dof = document.getFirstDocumentFile();
        dof.setType(FileType.EDI);
        File metaDofFile = dof != null ? dof.getFile() : null;
        if (metaDofFile == null || !metaDofFile.isFile()) {

          log.warn("Error : Ignored document : {}", document);
          VariableHelper.addDeads(execution, document);
          continue;
        }
        // déplacement du fichier dans l'input_dir du process et backup dans input_backup
        File inputFile = new File((File) getVariable(execution, PROCESS_INPUT_DIR), metaDofFile.getName());
        File inputBackupFile = new File(VariableHelper.getInputBackupDirectory(execution), metaDofFile.getName());
        try {
          FileUtils.moveFile(metaDofFile, inputFile);
          FileUtils.copyFile(inputFile, inputBackupFile);
          dof.setFile(inputFile);
        }
        catch (IOException e) {
          log.warn("Impossible to move input file in PROCESS_INPUT_DIR or PROCESS_INPUT_BACKUP_DIR, continuing without moving file",
              e.getMessage());
        }
        // Mise à jour de l'action name le cas échéant
        if (getBoolean(execution, taxOriginal, TRUE)) {
          log.debug("Setting input file {} as tax original.", inputFile.getName());
          dof.setActionName(TAX_ORIGINAL);
        }

        document.setStatusWithEnumValue(DocumentStatus.PENDING);
        filesToSplit.add(dof);
      }
    }
    else {
      /* gestion par repertoire */
      File sourceDir = getFile(execution, source);

      log.debug("Src Dir = {}", sourceDir);
      String filterValue = VariableHelper.getString(execution, filter, "*");
      Collection<File> files = FileHelper.fileSet(sourceDir, filterValue, null, false, null, null);

      /* Supress File empty */
      files.removeIf(file -> file.length() == 0);

      for (File file : files) {
        DocumentFile dof = new DocumentFile();
        dof.setFile(file);
        dof.setType(FileType.EDI);
        dof.setActionName(this.getClass()
            .getSimpleName());
        dof.setProcessInstanceId(execution.getId());
        filesToSplit.add(dof);

      }
    }
    return filesToSplit;
  }

  private File makeDestDir(DelegateExecution execution, boolean contextOut) throws Exception {
    File destDir = contextOut
        ? VariableHelper.getVariable(execution, Variable.PROCESS_WORK_DIR)
        : getFile(execution, destination);
    if (destDir == null) {
      throw new Exception("Invalid path forDestDir");
    }
    log.debug("Dest Dir = {}", destDir);
    destDir.mkdirs();
    return destDir;
  }

  @SuppressWarnings("squid:S2259")
  private void split(DelegateExecution execution, NormeFile reader, File destDir, boolean contextOut, DocumentFile fileToSplit)
      throws Exception {
    final String carSeg = reader.getDelimiteur() + System.getProperty("line.separator");
    String segUna = null;
    int nbInterchange = 0;
    Document docInterchange = null;

    InterchangeEdifact interchange = null;
    InterchangeEdifact.Message message = null;
    /* liste des fichiers en sortie */
    final List<File> listFileOut = new ArrayList<>();
    final List<Document> docsMessage = new ArrayList<>();
    int sizeSign = -1;

    while (reader.readLine() != reader.NFR_EOF) {
      try {
        LineProperties lineP = reader.getCurrentLine();
        Istring lineIs = new Istring(lineP.lineValue);
        lineIs.deleteSpaceLeft();
        String line = lineIs.toString();
        log.debug("LINE=" + line);
        if (sizeSign >= 0) {
          int cutIndex = line.indexOf("UNP+" + sizeSign);
          if (cutIndex > -1 && cutIndex < line.length()) {
            line = line.substring(cutIndex);
          }
          else {
            log.error("Can't extract certificate object");
            throw new Exception("Can't extract certificate object");
          }
          sizeSign = -1;
        }

        if (line == null) {
          break;
        }
        if (line.startsWith("UNA")) {
          segUna = line;
        }
        else if (line.startsWith("UNB")) {
          if (interchange != null) {
            throw new Exception("UNZ not found for UNB=" + line);
          }
          nbInterchange++;
          String nInterchange = reader.getDataAtIndexNormeFile(line, 5, 1);
          if (nInterchange == null) {
            log.error("Error : Interchange Number not found");
            throw new Exception("Interchange Number not found");
          }
          interchange = new InterchangeEdifact(nInterchange, segUna, line, carSeg, destDir, fileToSplit.getFile()
              .getName(), nbInterchange);

          if (fileToSplit.getDocument() != null & nbInterchange < 2) {
            // Cas où le document est déjà créé en amont (Rest Trigger par exemple.)
            // Dans ce cas, on ne recréé pas le document on le met à jour.
            // /!\ Si le fichier d'entrée contient plusieurs interchanges, on doit créer un document pour le deuxième interchange
            docInterchange = updateDocument(execution, fileToSplit.getDocument(), fileToSplit.getFile(), INTERCHANGE_EDIFACT.toString(),
                DocumentStatus.PENDING, nbInterchange);

            final String companyIdentifier = execution.getVariable(instance_company_identifier.toString(), String.class);
            isTrue(isNotBlank(companyIdentifier), "The process variable 'instance_company_identifier' is missing");
            docInterchange.setOwners(companyIdentifier);

            docInterchange.setReference(computeInterchangeReference(fileToSplit.getFile(), nbInterchange));

            docInterchange.setType(INTERCHANGE_EDIFACT.toString());
            docInterchange.setStatusWithEnumValue(DocumentStatus.PENDING);
          }
          else {
            docInterchange = createDocument(execution, fileToSplit.getFile(), INTERCHANGE_EDIFACT.toString(), DocumentStatus.PENDING,
                FileType.EDI, nbInterchange);
          }
          if (isCheckIntegrity(execution)) {
            integrityService.updateIntegrity(Arrays.asList(docInterchange), false);
          }
          VariableHelper.addDocuments(execution, docInterchange);

          String from = reader.getDataAtIndexNormeFile(line, 2, 1);
          String to = reader.getDataAtIndexNormeFile(line, 3, 1);

          docInterchange.setFrom(from);
          docInterchange.setTo(to);
        }
        else if (line.startsWith("LIN")) {
          if (message != null) {
            message.writeLine(line);
          }
        }
        else if (line.startsWith("UNH")) {
          if (message != null || interchange == null) {
            throw new Exception("UNT not found for UNH=" + line);
          }

          String typeEdifact = reader.getDataAtIndexNormeFile(line, 2, 1);
          String versionEdifact = reader.getDataAtIndexNormeFile(line, 2, 2);
          String releaseEdifact = reader.getDataAtIndexNormeFile(line, 2, 3);

          if (typeEdifact == null || typeEdifact.isEmpty()) {
            throw new Exception("Type Edifact is missing");
          }
          if (versionEdifact == null || versionEdifact.isEmpty()) {
            throw new Exception("Version Edifact is missing");
          }
          if (releaseEdifact == null || releaseEdifact.isEmpty()) {
            throw new Exception("Release Edifact is missing");
          }

          String documentType = createDocumentCompoundType(typeEdifact, versionEdifact + releaseEdifact);
          log.debug("Type = {}", documentType);

          if (outputCharset != null && StringUtils.isNotEmpty(outputCharset.getExpressionText())) {
            log.info("Writing output file in {} charset.", outputCharset.getExpressionText());
            message = interchange.newMessage(documentType, outputCharset.getExpressionText());
          }
          else {
            message = interchange.newMessage(documentType);
          }
          listFileOut.add(message.getFile());
          message.writeHeader(line);
        }
        else if (line.startsWith("UNT")) {
          if (message == null) {
            log.error("Error : Invalid Structure message");
            throw new Exception("Invalid Structure message");
          }
          message.writeTail(line);
          message.close();

          /* Create document Message */
          Document messageDoc = createDocumentMessage(execution, docsMessage, message, docInterchange);
          String processName = VariableHelper.getProcessDefinition(execution)
              .getName();
          if (isCheckIntegrity(execution)) {
            Reprocess reprocess = (Reprocess) execution.getVariable(Reprocess.class.getSimpleName());
            String reportId = ofNullable(execution.getVariable(REPORT_DOCUMENT.toString())).filter(Document.class::isInstance)
                .map(Document.class::cast)
                .map(Document::getIndexValue)
                .map(Indexable.class::cast)
                .map(Indexable::getId)
                .orElse(null);
            integrityService.createEntry(messageDoc, destDir.getPath(), processName, getVariable(execution, PROCESS_OWNER), reportId,
                reprocess != null ? true : false);
          }
          message = null;
        }
        else if (line.startsWith("UNZ")) {
          if (interchange == null || message != null) {
            log.error("Error : Invalid Structure Interchange or Message");
            throw new Exception("Invalid Structure Interchange");
          }
          interchange = null;
        }
        else {
          // on ignore les segments de signature et les groupes fonctionnels
          if (message == null && (!line.startsWith("UNO") && !line.startsWith("UNP")) &&
              (!line.startsWith("UNG") && !line.startsWith("UNE"))) {
            log.error("Error : Segment withOut Message or interchange line={}", line);
            throw new Exception("Segment withOut Message or interchange line=" + line);
          }
          if (line.startsWith("UNO")) {
            String sizeSignStr = reader.getDataAtIndexNormeFile(line, 5, 1);
            try {
              sizeSign = Integer.parseInt(sizeSignStr);
            }
            catch (NumberFormatException e) {
              sizeSign = -1;
              log.error("Error : Segment withOut Message or interchange line={}", line);
              throw new Exception("Segment withOut Message or interchange line=" + line);
            }
          }
          if (!line.startsWith("UNO") && !line.startsWith("UNP") && !line.startsWith("USH") && !line.startsWith("USA") &&
              !line.startsWith("USC") && !line.startsWith("UST") && !line.startsWith("USR") && !line.startsWith("UNG") &&
              !line.startsWith("UNE")) {
            message.writeSegment(line);
          }
          else {
            log.info("Ignoring segment line={}", (line.length() >= 3) ? line.substring(0, 3) : line);
          }
        }
      }
      catch (Exception e) {
        if (message != null) {
          message.close();
        }
        listFileOut.forEach(File::delete);
        log.info("Error : Segment withOut Message");
        throw e;
      }
    } /* while on Segment */

    if (message != null || interchange != null) {
      if (message != null) {
        message.close();
      }
      listFileOut.forEach(File::delete);
      log.error("Error : Invalid Struture Message");
      throw new Exception("Error : Invalid Structure Message");
    }
    if (docsMessage.isEmpty()) {
      throw new Exception("Error : no message found in interchange");
    }
    if (contextOut) {
      VariableHelper.addDocuments(execution, docsMessage);
    }
  }

  private Document createDocumentMessage(DelegateExecution execution, List<Document> docsMessage,
      InterchangeEdifact.Message message, Document docInterchange) throws IllegalAccessException, InvocationTargetException {

    /* Creation doc fils message */
    File fileResult = message.getFile();
    Document doc = createDocument(execution, fileResult, message.getDocumentType(), DocumentStatus.PENDING, FileType.EDIUNIT, 0);
    doc.setStage(DocumentStage.UNDEFINED);

    doc.setLinCount(message.getLinCount());
    log.debug("add Doc in Context: {}", doc);

    docInterchange.addChild(doc);
    docsMessage.add(doc);

    log.debug("Write out file : {} for fileIn = {}", fileResult, docInterchange.getFirstFile());
    return doc;
  }

  private Document createDocument(DelegateExecution execution, File documentFile, String documentType, DocumentStatus documentStatus,
      FileType dofType, int nbInterchange) throws IllegalAccessException, InvocationTargetException {
    org.activiti.engine.delegate.Expression documentClassName = new FixedValue(Document.class.getName());
    Document doc = newDocumentInstancePopulated(execution, documentClassName, null, documentFile);
    doc = updateDocument(execution, doc, documentFile, documentType, documentStatus, nbInterchange);

    // Mise à jour de l'action name le cas échéant
    if (getBoolean(execution, taxOriginal, TRUE) && documentType == INTERCHANGE_EDIFACT.toString()) {
      log.debug("Setting input file {} as tax original.", documentFile.getName());
      doc.addFile(new DocumentFile(documentFile, dofType,
          TAX_ORIGINAL,
          execution.getId(), doc,
          null));
    }
    else {
      doc.addFile(new DocumentFile(documentFile, dofType,
          this.getClass()
              .getSimpleName(),
          execution.getId(), doc,
          null));
    }
    return doc;
  }

  private Document updateDocument(DelegateExecution execution, Document doc, File documentFile, String documentType,
      DocumentStatus documentStatus, int nbInterchange) {
    final String companyIdentifier = execution.getVariable(instance_company_identifier.toString(), String.class);
    isTrue(isNotBlank(companyIdentifier), "The process variable 'instance_company_identifier' is missing");
    doc.setOwners(companyIdentifier);

    doc.setReference(computeInterchangeReference(documentFile, nbInterchange));

    doc.setType(documentType);
    doc.setStatusWithEnumValue(documentStatus);
    return doc;
  }

  private String computeInterchangeReference(File file, int nbInterchange) {
    if (nbInterchange > 0) {
      StringBuilder interchangeReference = new StringBuilder();
      interchangeReference.append(FileHelper.getFileNameWithoutExt(file));
      interchangeReference.append("_")
          .append(nbInterchange);
      interchangeReference.append(FileHelper.getFileExt(file));
      return interchangeReference.toString();
    }
    else {
      return file.getName();
    }
  }

  public Expression getSource() {
    return source;
  }

  public void setSource(Expression source) {
    this.source = source;
  }

  public Expression getFilter() {
    return filter;
  }

  public void setFilter(Expression filter) {
    this.filter = filter;
  }

  public Expression getDestination() {
    return destination;
  }

  public void setDestination(Expression destination) {
    this.destination = destination;
  }

  public Expression getTaxOriginal() {
    return taxOriginal;
  }

  public void setTaxOriginal(Expression taxOriginal) {
    this.taxOriginal = taxOriginal;
  }

  public Expression getInputCharset() {
    return inputCharset;
  }

  public void setInputCharset(Expression inputCharset) {
    this.inputCharset = inputCharset;
  }

  public Expression getOutputCharset() {
    return outputCharset;
  }

  public void setOutputCharset(Expression outputCharset) {
    this.outputCharset = outputCharset;
  }

}
