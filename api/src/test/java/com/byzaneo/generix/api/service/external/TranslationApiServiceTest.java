/*
package com.byzaneo.generix.api.service.external;

import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import com.byzaneo.generix.api.dto.translation.ImportError;
import com.byzaneo.generix.api.service.external.delegators.TranslationServiceDelegator;
import com.byzaneo.generix.api.service.external.impl.TranslationApiServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class TranslationApiServiceTest {

    @Mock
    private TranslationServiceDelegator translationServiceDelegator;

    @InjectMocks
    private TranslationApiServiceImpl translationApiService;

    private MockHttpServletRequest request;
    private UUID requestId;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        requestId = UUID.randomUUID();
    }

    @Test
    void testDownloadDictionary_Frontend_Success() {
        // JDK 17: Using var and text blocks for test data
        var csvContent = """
            module;key;fr;en;de
            common;hello;bonjour;hello;hallo
            """;
        var csvBytes = csvContent.getBytes();
        var mockResponse = Response.ok(csvBytes).build();
        
        when(translationServiceDelegator.downloadDictionary(eq(request), eq(requestId), eq("frontend"), eq("de")))
            .thenReturn(mockResponse);

        // When
        var response = translationApiService.downloadDictionary(request, requestId, "frontend", "de");

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
    }

    @Test
    void testDownloadDictionary_Errors_Success() {
        // JDK 17: Using var and text blocks
        var csvContent = """
            module;key;fr;en
            errors;ERR001;Erreur;Error
            """;
        var csvBytes = csvContent.getBytes();
        var mockResponse = Response.ok(csvBytes).build();
        
        when(translationServiceDelegator.downloadDictionary(eq(request), eq(requestId), eq("errors"), isNull()))
            .thenReturn(mockResponse);

        // When
        var response = translationApiService.downloadDictionary(request, requestId, "errors", null);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
    }

    @Test
    void testImportDictionary_Frontend_Success() {
        // JDK 17: Using var and text blocks
        var csvContent = """
            module;key;de;es
            common;hello;hallo;hola
            """;
        var inputStream = new ByteArrayInputStream(csvContent.getBytes());
        var mockResponse = Response.ok().build();
        
        when(translationServiceDelegator.importDictionary(eq(request), eq(requestId), eq("frontend"), eq("de"), any(InputStream.class)))
            .thenReturn(mockResponse);

        // When
        var response = translationApiService.importDictionary(request, requestId, "frontend", "de", inputStream);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
    }

    @Test
    void testImportDictionary_Errors_Success() {
        // JDK 17: Using var and text blocks
        var csvContent = """
            module;key;it
            errors;ERR001;Errore
            """;
        var inputStream = new ByteArrayInputStream(csvContent.getBytes());
        var importResponse = DictionaryImportResponse.builder()
            .totalImported(1)
            .errors(List.of())
            .build();
        var mockResponse = Response.ok(importResponse).build();

        when(translationServiceDelegator.importDictionary(eq(request), eq(requestId), eq("errors"), eq("it"), any(InputStream.class)))
            .thenReturn(mockResponse);

        // When
        var response = translationApiService.importDictionary(request, requestId, "errors", "it", inputStream);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
    }

    @Test
    void testImportDictionary_WithErrors() {
        // JDK 17: Test error handling
        var csvContent = """
            module;key;de
            invalid;test;hallo
            """;
        var inputStream = new ByteArrayInputStream(csvContent.getBytes());
        var errors = List.of(
            ImportError.builder()
                .lineNumber(2)
                .errorMessage("Unknown module: invalid")
                .build()
        );
        var importResponse = DictionaryImportResponse.builder()
            .totalImported(0)
            .errors(errors)
            .build();
        var mockResponse = Response.ok(importResponse).build();

        when(translationServiceDelegator.importDictionary(eq(request), eq(requestId), eq("frontend"), eq("de"), any(InputStream.class), any()))
            .thenReturn(mockResponse);

        // When
        var response = translationApiService.importDictionary(request, requestId, "frontend", "de", inputStream, null);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        var responseEntity = (DictionaryImportResponse) response.getEntity();
        assertEquals(0, responseEntity.getTotalImported());
        assertEquals(1, responseEntity.getErrors().size());
        assertEquals("Unknown module: invalid", responseEntity.getErrors().get(0).getErrorMessage());
    }

    @Test
    void testDownloadDictionary_AllLanguages() {
        // JDK 17: Test without language filter
        var csvContent = """
            module;key;fr;en;de;es
            common;hello;bonjour;hello;hallo;hola
            """;
        var csvBytes = csvContent.getBytes();
        var mockResponse = Response.ok(csvBytes).build();

        when(translationServiceDelegator.downloadDictionary(eq(request), eq(requestId), eq("frontend"), isNull()))
            .thenReturn(mockResponse);

        // When
        var response = translationApiService.downloadDictionary(request, requestId, "frontend", null);

        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());

        // Verify delegator was called with null language
        verify(translationServiceDelegator).downloadDictionary(request, requestId, "frontend", null);
    }

    @Test
    void testServiceDelegation() {
        // JDK 17: Verify that service properly delegates to delegator
        var inputStream = new ByteArrayInputStream("test".getBytes());
        var mockResponse = Response.ok().build();

        when(translationServiceDelegator.downloadDictionary(any(), any(), any(), any()))
            .thenReturn(mockResponse);
        when(translationServiceDelegator.importDictionary(any(), any(), any(), any(), any(), any()))
            .thenReturn(mockResponse);

        // Test download delegation
        translationApiService.downloadDictionary(request, requestId, "frontend", "en");
        verify(translationServiceDelegator).downloadDictionary(request, requestId, "frontend", "en");

        // Test import delegation
        translationApiService.importDictionary(request, requestId, "errors", "fr", inputStream, null);
        verify(translationServiceDelegator).importDictionary(request, requestId, "errors", "fr", inputStream, null);
    }
}
*/
