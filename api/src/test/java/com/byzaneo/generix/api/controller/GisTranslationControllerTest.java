/*
package com.byzaneo.generix.api.controller;

import com.byzaneo.generix.api.bean.TranslationImportResponseDTO;
import com.byzaneo.generix.api.bean.ImportErrorDTO;
import com.byzaneo.generix.api.dto.translation.DictionaryType;
import com.byzaneo.generix.api.service.external.TranslationApiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;

import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GisTranslationControllerTest {

    @Mock
    private TranslationApiService translationApiService;

    @InjectMocks
    private GisTranslationController controller;

    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
    }

    @Test
    void testDownloadDictionary_Frontend_Success() {
        // JDK 17: Using var and text blocks for test data
        var csvContent = """
            module;key;fr;en;de
            test;hello;bonjour;hello;hallo
            """;
        var csvBytes = csvContent.getBytes();
        var mockResponse = Response.ok(csvBytes).build();

        when(translationApiService.downloadDictionary(eq(request), any(UUID.class), eq("frontend"), eq("de")))
            .thenReturn(mockResponse);

        // When
        var response = controller.downloadDictionary(request, DictionaryType.frontend, "de");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getHeaders().containsKey("Content-Disposition"));
        assertTrue(response.getHeaders().getFirst("Content-Disposition").contains("frontend_translations.csv"));
    }

    @Test
    void testDownloadDictionary_Errors_Success() {
        // JDK 17: Using var and text blocks
        var csvContent = """
            module;key;fr;en;es
            errors;ERR001;Erreur;Error;Error
            """;
        var csvBytes = csvContent.getBytes();
        var mockResponse = Response.ok(csvBytes).build();

        when(translationApiService.downloadDictionary(eq(request), any(UUID.class), eq("errors"), eq(null)))
            .thenReturn(mockResponse);

        // When
        var response = controller.downloadDictionary(request, DictionaryType.errors, null);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getHeaders().containsKey("Content-Disposition"));
        assertTrue(response.getHeaders().getFirst("Content-Disposition").contains("error_translations.csv"));
    }

    @Test
    void testUploadDictionary_Success() throws IOException {
        // JDK 17: Using var and text blocks
        var csvContent = """
            module;key;de;es
            test;hello;hallo;hola
            """;
        var file = new MockMultipartFile(
            "file", "translations.csv", "text/csv", csvContent.getBytes());

        var expectedResponse = TranslationImportResponseDTO.builder()
            .totalImported(2)
            .errors(List.of())
            .build();
        var mockResponse = Response.ok(expectedResponse).build();

        when(translationApiService.importDictionary(eq(request), any(UUID.class), eq("frontend"), eq("de"), any()))
            .thenReturn(mockResponse);

        // When
        var response = controller.uploadDictionary(request, DictionaryType.frontend, "de", file);

        // Then
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().getTotalImported());
        assertTrue(response.getBody().getErrors().isEmpty());
    }

    @Test
    void testUploadDictionary_WithErrors() throws IOException {
        // JDK 17: Using var, text blocks, and List.of()
        var csvContent = """
            module;key;de;es
            invalid;unknown;hallo;hola
            """;
        var file = new MockMultipartFile(
            "file", "translations.csv", "text/csv", csvContent.getBytes());

        var errors = List.of(
            ImportErrorDTO.builder()
                .lineNumber(2)
                .errorMessage("Unknown module: invalid")
                .build()
        );

        var expectedResponse = TranslationImportResponseDTO.builder()
            .totalImported(0)
            .errors(errors)
            .build();
        var mockResponse = Response.ok(expectedResponse).build();

        when(translationApiService.importDictionary(eq(request), any(UUID.class), eq("frontend"), eq(null), any()))
            .thenReturn(mockResponse);

        // When
        var response = controller.uploadDictionary(request, DictionaryType.frontend, null, file);

        // Then
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getTotalImported());
        assertEquals(1, response.getBody().getErrors().size());
        assertEquals("Unknown module: invalid", response.getBody().getErrors().get(0).getErrorMessage());
    }

    @Test
    void testLanguageParameterParsing() {
        // JDK 17: Using var and text blocks for cleaner test data
        var csvContent = """
            module;key;fr;en
            test;hello;bonjour;hello
            """;
        var csvBytes = csvContent.getBytes();
        var mockResponse = Response.ok(csvBytes).build();

        when(translationApiService.downloadDictionary(eq(request), any(UUID.class), eq("frontend"), eq("fr")))
            .thenReturn(mockResponse);

        // When
        var response = controller.downloadDictionary(request, DictionaryType.frontend, "fr");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}
*/
