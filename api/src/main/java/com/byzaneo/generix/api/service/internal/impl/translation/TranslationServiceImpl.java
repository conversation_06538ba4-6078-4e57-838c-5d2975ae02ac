/**
 * 
 */
package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.angular.bean.*;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.util.RestServiceHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.generix.xtrade.util.ExcelExportHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentErrorTranslationService;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.core.Response;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.bean.FileType.getType;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.LocaleUtils.toLocale;

/**
 * <AUTHOR> GHOZZI <<EMAIL>>
 * @company Generix group
 * @date 17 april 2025
 */
@Service(TranslationService.SERVICE_NAME)
public class TranslationServiceImpl implements TranslationService {

  private static final String FILE_NAME = "translations.csv";

  private final I18NService i18NService;
  private final I18nTranslationDAO i18nTranslationDAO;
  private final DocumentErrorTranslationService documentErrorTranslationService;

  private static final String CODE = "code";
  private static final String MODULE_NAME = "module_name";
  private static final String DELIMITER = ";";
  private static final Set<String> ALLOWED_LANG_CODES = Set.of(
      "ab", "aa", "af", "ak", "sq", "am", "ar", "an", "hy", "as", "av", "ae", "ay", "az",
      "bm", "ba", "eu", "be", "bn", "bi", "bs", "br", "bg", "my", "ca", "ch", "ce", "ny",
      "zh", "cu", "cv", "kw", "co", "cr", "hr", "cs", "da", "dv", "nl", "dz", "en", "eo",
      "et", "ee", "fo", "fj", "fi", "fr", "fy", "ff", "gd", "gl", "lg", "ka", "de", "el",
      "kl", "gn", "gu", "ht", "ha", "he", "hz", "hi", "ho", "hu", "is", "io", "ig", "id",
      "ia", "ie", "iu", "ik", "ga", "it", "ja", "jv", "kn", "kr", "ks", "kk", "km", "ki",
      "rw", "ky", "kv", "kg", "ko", "kj", "ku", "lo", "la", "lv", "li", "ln", "lt", "lu",
      "lb", "mk", "mg", "ms", "ml", "mt", "gv", "mi", "mr", "mh", "mn", "na", "nv", "nd",
      "nr", "ng", "ne", "no", "nb", "nn", "ii", "oc", "oj", "or", "om", "os", "pi", "ps",
      "fa", "pl", "pt", "pa", "qu", "ro", "rm", "rn", "ru", "se", "sm", "sg", "sa", "sc",
      "sr", "sn", "sd", "si", "sk", "sl", "so", "st", "es", "su", "sw", "ss", "sv", "tl",
      "ty", "tg", "ta", "tt", "te", "th", "bo", "ti", "to", "ts", "tn", "tr", "tk", "tw",
      "ug", "uk", "ur", "uz", "ve", "vi", "vo", "wa", "cy", "wo", "xh", "yi", "yo", "za", "zu"
  );
  public TranslationServiceImpl(I18NService i18NService, I18nTranslationDAO i18nTranslationDAO,
      DocumentErrorTranslationService documentErrorTranslationService) {
    this.i18NService = i18NService;
    this.i18nTranslationDAO = i18nTranslationDAO;
    this.documentErrorTranslationService = documentErrorTranslationService;
  }

  @Transactional
  @Override
  public Response exportCSVTranslation(File tmpFile){
    List<I18NTranslationDto> translations = i18NService.getTranslations();
    List<Locale> locales = i18NService.getLocales();
    ExcelExportHelper.exportAsCsvGeneric(createExportedData(translations, locales), null, tmpFile, null, null, FILE_NAME, null, generateHeaders(locales));
    return ok(tmpFile, getType(tmpFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + tmpFile.getName()).build();
  }

  /**
   * Method to export data
   * @param translations list of translation lines
   * @param availableLocales list of language to export
   * @return list of line to export in csv file
   */
  @Transactional
  public List<String> createExportedData(List<I18NTranslationDto> translations, List<Locale> availableLocales) {
    Map<String, Map<Locale, String>> translationMap = new LinkedHashMap<>();
    for (I18NTranslationDto translation : translations) {
      String code = translation.getCode();
      I18NModule module = i18NService.findI18NModuleById(translation.getI18NModuleId());
      Locale locale = translation.getLocale();
      String valueToUse = translation.getNewValue()!= null ? translation.getNewValue() : translation.getDefaultValue();
      translationMap
              .computeIfAbsent(code, k -> new HashMap<>())
              .put(locale, valueToUse);
    }
    List<String> exportedData = new ArrayList<>();
    for (String code : translationMap.keySet()) {
      Map<Locale, String> localeMap = translationMap.get(code);
      Query q = new Query(new AndClause(Clauses.equal("code", code)));
      String moduleName = i18nTranslationDAO.search(q).get(0).getI18NModule().getName();
      StringBuilder row = new StringBuilder(moduleName !=null ? moduleName : "");
      row.append(DELIMITER).append(code);
      for (Locale locale : availableLocales) {
        String value = localeMap.getOrDefault(locale, "");
        row.append(DELIMITER).append(value);
      }
      exportedData.add(row.toString());
    }
    return exportedData;
  }

  /**
   * list of headers in file
   * @param availableLocales list of language available
   * @return liste of header for the file to export
   */
  private static String[] generateHeaders(List<Locale> availableLocales) {
    // Create headers array
    String[] headers = new String[availableLocales.size() + 2]; // +2 for the "code" header
    headers[1] = CODE; // First header is "code"
    headers[0] = MODULE_NAME; // second header is "module_id"
    int index = 2;
    for (Locale locale : availableLocales) {
      headers[index++] = locale.toString(); // Add each locale as a header
    }
    return headers;
  }

  @Transactional
  @Override
  public Response importTranslationsData(User user, String locale, Attachment file) {
    Locale messageLocale = new Locale(locale);
    List<List<String>> records = new ArrayList<>();
    Set<String> errorLines = new HashSet<>();
    List<String> successLines = new ArrayList<>();
    String errorLabel = i18NService.findByCodeAndLocale("error_label", messageLocale, user.getFullname());
    try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getDataHandler().getInputStream()))) {
      String line;
      while ((line = br.readLine()) != null) {
        records.add(Arrays.asList(line.split(DELIMITER)));
      }
      Set<String> languages = Arrays.stream(Locale.getISOLanguages())
              .map(Locale::new)
              .map(Locale::getDisplayLanguage)
              .collect(Collectors.toCollection(TreeSet::new));
      for (int recodsLine=1; recodsLine < records.size(); recodsLine++) {
        List<String> record = records.get(recodsLine);
        updateTranslations(records, languages, record, user, locale, errorLines, errorLabel, recodsLine);
      }
      String messageSuccess = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_file_successfully", "", new Locale(locale), records.size() - errorLines.size());
      successLines.add(messageSuccess);
      return listResultMessages(errorLines, successLines, messageSuccess);
    } catch (FileNotFoundException e) {
      throw new RuntimeException(e);
    } catch (IOException e) {
      throw new RuntimeException("Error reading the file: " + e.getMessage(), e);
    }
  }

  /**
   * Exports a CSV file containing error code translations for one or more specified languages.
   *
   * <p>This method retrieves translations from the data source, organizes them by error code and language,
   * and returns the result as a downloadable CSV file. If the provided list of language codes is null or empty,
   * the method automatically includes all available languages present in the data.</p>
   *
   * <p>The resulting CSV will use semicolon (;) as a separator and contain one row per error code.
   * Each row includes the error code followed by its translations in the specified languages.</p>
   *
   * <p>If no translation exists for a given language/errorCode pair, the corresponding cell in the CSV will be left empty.</p>
   *
   * @param requestedLang a list of ISO 639-1 language codes (e.g., "fr", "en", "de").
   *                       If null or empty, all available languages will be included in the export.
   *                       Only valid language codes should be passed; otherwise, validation should be handled externally.
   * @return a JAX-RS {@link Response} containing the CSV file as a byte stream, with headers configured
   *         to trigger a file download named <code>error_translations.csv</code>.
   */
  @Override
  public Response exportTranslationsWithLang(String requestedLang) {

    List<DocumentErrorTranslation> translations = documentErrorTranslationService.getErrorTranslationByLang(requestedLang);

    Map<String, Map<String, String>> errorCodeToLangMap = new TreeMap<>();
    Set<String> allLanguages = new TreeSet<>();

    for (DocumentErrorTranslation translation : translations) {
      String errorCode = translation.getId().getErrorCode();
      String lang = translation.getId().getLanguageCode();
      String label = translation.getErrorLabel();

      allLanguages.add(lang);

      errorCodeToLangMap
          .computeIfAbsent(errorCode, k -> new HashMap<>())
          .put(lang, label);
    }


    List<String> exportLangs = new ArrayList<>();

    if (requestedLang != null && !requestedLang.isBlank()) {
      exportLangs.add(requestedLang.trim());
    } else {
      exportLangs.addAll(allLanguages);
    }

    // Sort languages
    exportLangs.sort(String::compareTo);

    // Build CSV
    StringBuilder sb = new StringBuilder();
    sb.append("key");
    for (String lang : exportLangs) {
      sb.append(";").append(lang);
    }
    sb.append("\n");

    for (Map.Entry<String, Map<String, String>> entry : errorCodeToLangMap.entrySet()) {
      String errorCode = entry.getKey();
      Map<String, String> langMap = entry.getValue();

      sb.append(errorCode);
      for (String lang : exportLangs) {
        String translation = Optional.ofNullable(langMap.get(lang)).orElse("");
        sb.append(";").append(escapeCsv(translation));
      }
      sb.append("\n");
    }

    byte[] csvBytes = sb.toString().getBytes(StandardCharsets.UTF_8);
    return Response.ok(csvBytes, "application/octet-stream")
        .header("Content-Disposition", "attachment; filename=error_translations.csv")
        .header("Content-Length", csvBytes.length)
        .build();
  }

  @Override
  @Transactional
  public Response importTranslationsWithLang(String requestedLang, InputStream uploadedInputStream) {
    UUID requestId = UUID.randomUUID();
    List<ImportErrorDTO> errors = new ArrayList<>();
    int successCount = 0;

    try {
      LineIterator it = org.apache.commons.io.IOUtils.lineIterator(uploadedInputStream, "UTF-8");

      if (!it.hasNext()) {
        return RestServiceHelper.getResponseOnError(
            Response.Status.BAD_REQUEST.toString(),
            "CSV file is empty",
            Response.Status.BAD_REQUEST,
            requestId
        );
      }

      String headerLine = it.nextLine();
      String[] headers = StringUtils.splitPreserveAllTokens(headerLine, ';');

      if (!"key".equalsIgnoreCase(headers[0].trim())) {
        return RestServiceHelper.getResponseOnError(
            Response.Status.BAD_REQUEST.toString(),
            "CSV must start with a 'key' column followed by language codes",
            Response.Status.BAD_REQUEST,
            requestId
        );
      }

      Set<String> normalizedRequestedLangs = (requestedLang == null || requestedLang.isBlank())
          ? null
          : (!requestedLang.equalsIgnoreCase("fr") && !requestedLang.equalsIgnoreCase("en"))
              ? Set.of(requestedLang.toLowerCase())
              : Collections.emptySet();


      Map<String, Integer> langColumnIndex = new HashMap<>();
      for (int i = 1; i < headers.length; i++) {
        String langCode = headers[i].trim().toLowerCase();
        if (!ALLOWED_LANG_CODES.contains(langCode)) continue;
        if (langCode.equals("fr") || langCode.equals("en")) continue;
        if (normalizedRequestedLangs == null || normalizedRequestedLangs.contains(langCode)) {
          langColumnIndex.put(langCode, i);
        }
      }

      if (langColumnIndex.isEmpty()) {
        return RestServiceHelper.getResponseOnError(
            Response.Status.BAD_REQUEST.toString(),
            "No supported language columns found for import",
            Response.Status.BAD_REQUEST,
            requestId
        );
      }

      int lineNumber = 1;
      while (it.hasNext()) {
        lineNumber++;
        String line = it.nextLine().trim();
        if (line.isEmpty()) continue;

        String[] row = StringUtils.splitPreserveAllTokens(line, ';');

        try {
          String key = row[0].trim();
          if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("Missing key at line " + lineNumber);
          }

          DocumentErrorTranslation referenceTranslation = null;
          if (documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "fr")) != null) {
            referenceTranslation = documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "fr"));
          } else if (documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "en")) != null) {
            referenceTranslation = documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "en"));
          }

          if (referenceTranslation == null) {
            throw new IllegalArgumentException("Unknown key: '" + key + "' at line " + lineNumber);
          }

          boolean standardError = referenceTranslation.isStandardError();

          for (Map.Entry<String, Integer> entry : langColumnIndex.entrySet()) {
            String langCode = entry.getKey();

            if (!ALLOWED_LANG_CODES.contains(langCode)) {
              continue;
            }

            int columnIndex = entry.getValue();
            if (columnIndex >= row.length) continue;

            String label = row[columnIndex].trim();
            if (StringUtils.isBlank(label)) continue;

            DocumentErrorTranslation entity = new DocumentErrorTranslation(
                key,
                langCode,
                label,
                standardError
            );
            documentErrorTranslationService.saveErrorTranslation(entity);
            successCount++;
          }

        } catch (Exception e) {
          errors.add(new ImportErrorDTO(lineNumber, e.getMessage()));
        }
      }

      TranslationImportResponseDTO response = new TranslationImportResponseDTO(successCount, errors);
      return Response.ok(response).build();

    } finally {
      try {
        uploadedInputStream.close();
      } catch (IOException ignored) {}
    }
  }







  /**
   * Escapes a string value for safe inclusion in a CSV field using semicolon (;) as a delimiter.
   *
   * <p>If the input string contains special characters such as semicolon (;), double quote ("), newline (\n),
   * or carriage return (\r), the method wraps the string in double quotes and escapes any existing
   * double quotes by doubling them (i.e., " becomes "").</p>
   *
   * <p>This ensures the output conforms to standard CSV formatting rules.</p>
   *
   * @param value the input string to escape for CSV output
   * @return the escaped string, safely formatted for CSV inclusion
   */
  private String escapeCsv(String value) {
    if (value.contains(";") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
      value = value.replace("\"", "\"\"");
      return "\"" + value + "\"";
    }
    return value;
  }


  /**
   * Method to update line in DB
   * @param resultTranslationsList list of retrived translation line
   * @param record list of language in line
   * @param user connected user
   * @param position poisition of language
   */
  private void updateRow(List<I18NTranslation> resultTranslationsList, List<String> record, User user, int position){
    I18NTranslation i18NTranslation = resultTranslationsList.get(0);
    i18NTranslation.setNewValue(record.get(position));
    i18NTranslation.setNewValueChangedAt(new Date());
    i18NTranslation.setNewValueChangedBy(user);
    i18nTranslationDAO.merge(i18NTranslation);
  }

  /**
   * Method to sort messages error messages
   * @param errorSet set of error message
   * @return list of error messages sorted
   */
  private static List<String> sortErrorList(Set<String> errorSet) {
    List<String> errorList = new ArrayList<>(errorSet);
    Collections.sort(errorList);
    return errorList;
  }

  /**
   * Method to return result listMessages
   * @param errorLines list of errors
   * @param successLines list of succes updated line
   * @param messageSuccess message of succeded update
   * @return response on method
   */
  private Response listResultMessages(Set<String> errorLines, List<String> successLines, String messageSuccess){
    if (!errorLines.isEmpty() && successLines.isEmpty()) {
      List<String> sortedErrorList =  sortErrorList(errorLines);
      return Response.status(Response.Status.NOT_ACCEPTABLE)
              .entity(String.join("\n", sortedErrorList))
              .build();
    } else if (!errorLines.isEmpty()) {
      List<String> sortedErrorList =  sortErrorList(errorLines);
      String responseMessage = String.join("\n", successLines) + "\n" +
              String.join("\n", sortedErrorList);
      return Response.status(Response.Status.NOT_ACCEPTABLE)
              .entity(responseMessage)
              .build();
    } else {
      return Response.ok(messageSuccess).build();
    }
  }

  /**
   * Method to iterate list of translation lines in CSV imported file
   * @param records traslation line
   * @param languages locale
   * @param record list of locale to translate
   * @param user user connected
   * @param locale local ofcurrent user
   * @param errorLines set of errors
   * @param errorLabel error label
   * @param recodsLine list of translations line
   */
  private void updateTranslations(List<List<String>> records, Set<String> languages, List<String> record, User user, String locale, Set<String> errorLines, String errorLabel, int recodsLine){
    // start from the 3th position in the list to not take module_name and code
    for (int recordPosition=2; recordPosition < record.size(); recordPosition++) {
      if(!record.get(recordPosition).isEmpty()) {
        String localeFromRecord = records.get(0).get(recordPosition);
        String currentLocale = toLocale(localeFromRecord).getDisplayLanguage();
        List<String> locales = languages.stream().filter(language -> language.equals(currentLocale)).toList();
        if (locales.isEmpty()) {
          String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_locale_inconnue", "", new Locale(locale), recordPosition + 1, currentLocale);
          errorLines.add(errorLabel + localeInconnue);
          break;
        } else {
          if (!record.get(recordPosition).isEmpty()) {
            // search elements with locale and code
            Query q = new Query(new AndClause(Clauses.equal("locale", localeFromRecord), Clauses.equal("code", record.get(1))));
            List<I18NTranslation> resultTranslationsList = i18nTranslationDAO.search(q);
            if (!resultTranslationsList.isEmpty()) {
              I18NTranslation i18NTranslation = new I18NTranslation();
              Optional<I18NModule> i18nModule = i18NService.findI18NModuleByName(record.get(0));
              if (i18nModule.isPresent()) {
                i18NTranslation.setI18NModule(i18nModule.get());
              } else {
                String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_ligne_ignoree", "", new Locale(locale), recodsLine, currentLocale);
                errorLines.add(errorLabel + localeInconnue);
                break;
              }
              // update data in i18n_translation table
              updateRow(resultTranslationsList, record, user, recordPosition);
            } else {
              String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_ligne_ignoree", "", new Locale(locale), recodsLine, record.get(0), record.get(1));
              errorLines.add(errorLabel + localeInconnue);
              break;
            }
          }
        }
      }
    }
  }

}
