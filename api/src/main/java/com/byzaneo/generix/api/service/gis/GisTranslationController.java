/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.api.service.gis;

import com.byzaneo.generix.api.service.gis.dto.TranslationImportReportDto;
import io.swagger.annotations.*;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * GIS Translation Service API Controller
 * Implements the B2B GIS Translation Service API for managing frontend and error translations.
 * 
 * <AUTHOR> Assistant
 */
@Api(tags = "GIS Translation Service", description = "Operations for managing translation dictionaries")
@Path("/v1/translations")
public interface GisTranslationController {

    /**
     * Export translation dictionary as CSV file
     * 
     * @param request HTTP servlet request
     * @param dictType Dictionary type (frontend or errors)
     * @param lang Optional language filter (comma-separated)
     * @return CSV file with translations
     */
    @GET
    @Path("/{dictType}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(
        value = "Retrieve the translation dictionary through a download of a CSV file",
        notes = "Export translation data as CSV file. If lang parameter is provided, only specified languages are exported."
    )
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "File exported successfully"),
        @ApiResponse(code = 400, message = "Bad Request - Invalid input or parameters"),
        @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
        @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server"),
        @ApiResponse(code = 503, message = "Service Unavailable")
    })
    Response downloadDictionary(
        @Context HttpServletRequest request,
        @ApiParam(value = "Dictionary type", required = true, allowableValues = "frontend,errors")
        @PathParam("dictType") @NotNull String dictType,
        @ApiParam(value = "Language filter (comma-separated ISO 639-1 codes)", required = false)
        @QueryParam("lang") String lang
    );

    /**
     * Import translation dictionary from CSV file
     * 
     * @param request HTTP servlet request
     * @param dictType Dictionary type (frontend or errors)
     * @param lang Optional language filter for import (comma-separated)
     * @param file CSV file to import
     * @return Import report with success/error details
     */
    @POST
    @Path("/{dictType}")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
        value = "Create or update translations through the upload of a dictionary file",
        notes = "Import translation data from CSV file. If lang parameter is provided, only specified languages are imported."
    )
    @ApiResponses(value = {
        @ApiResponse(code = 201, message = "File imported successfully", response = TranslationImportReportDto.class),
        @ApiResponse(code = 400, message = "Bad Request - Invalid input or parameters"),
        @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
        @ApiResponse(code = 409, message = "Conflict - Data conflict during import"),
        @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server"),
        @ApiResponse(code = 503, message = "Service Unavailable")
    })
    Response uploadDictionary(
        @Context HttpServletRequest request,
        @ApiParam(value = "Dictionary type", required = true, allowableValues = "frontend,errors")
        @PathParam("dictType") @NotNull String dictType,
        @ApiParam(value = "Language filter for import (comma-separated ISO 639-1 codes)", required = false)
        @QueryParam("lang") String lang,
        @ApiParam(value = "CSV file to import", required = true)
        @Multipart(value = "file") Attachment file
    );
}
