/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.api.service;

import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import com.byzaneo.generix.api.dto.translation.DictionaryType;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Service interface for GIS translation operations
 */
public interface GisTranslationService {
    
    /**
     * Export translations as CSV file
     *
     * @param dictType Dictionary type (errors or frontend)
     * @param language Single language code to export (null for all)
     * @return CSV file as Resource
     */
    Resource exportTranslations(DictionaryType dictType, String language);

    /**
     * Import translations from CSV file
     *
     * @param dictType Dictionary type (errors or frontend)
     * @param language Single language code to import (null for all)
     * @param file CSV file to import
     * @return Import response with success count and errors
     */
    DictionaryImportResponse importTranslations(DictionaryType dictType, String language, MultipartFile file);
}
