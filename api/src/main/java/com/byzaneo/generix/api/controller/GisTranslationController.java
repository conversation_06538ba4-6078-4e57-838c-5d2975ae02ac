package com.byzaneo.generix.api.controller;

import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import com.byzaneo.generix.api.dto.translation.DictionaryType;
import com.byzaneo.generix.api.service.GisTranslationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * GIS Translation API Controller
 * Provides endpoints for importing and exporting translation dictionaries
 */
@RestController
@RequestMapping("/api/gis/v2/translations")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "GIS Translation Service", description = "Operations for translation dictionary management")
@SecurityRequirement(name = "BearerAuth")
public class GisTranslationController {

    private final GisTranslationService gisTranslationService;

    @GetMapping("/{dictType}")
    @Operation(
        summary = "Retrieve the translation dictionary through a download of a CSV file",
        description = "Export translations as CSV file. If lang parameter is provided, only specified language is exported."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OK - Returning the dictionary file",
            headers = @io.swagger.v3.oas.annotations.headers.Header(
                name = "Content-Disposition",
                description = "Header specifying attached file name",
                schema = @Schema(type = "string", pattern = "attachment;filename=*")
            ),
            content = @Content(mediaType = "application/octet-stream")),
        @ApiResponse(responseCode = "400", description = "Bad Request - Invalid input or parameters"),
        @ApiResponse(responseCode = "403", description = "Forbidden: Access denied due to lack of permissions or incorrect token"),
        @ApiResponse(responseCode = "500", description = "Internal Server Error - An unexpected error occurred on the server"),
        @ApiResponse(responseCode = "503", description = "Service Unavailable")
    })
    @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
    public ResponseEntity<Resource> downloadDictionary(
            HttpServletRequest request,
            @Parameter(description = "Inform about the dictionary type", required = true)
            @PathVariable("dictType") DictionaryType dictType,
            @Parameter(description = "Selects translations related to only one language")
            @RequestParam(value = "lang", required = false) String lang) {

        log.info("Downloading dictionary for type: {}, language: {}", dictType, lang);

        Resource resource = gisTranslationService.exportTranslations(dictType, lang);
        
        String filename = dictType.name() + "_translations.csv";
        
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(resource);
    }

    @PostMapping("/{dictType}")
    @Operation(
        summary = "Create or update translations through the upload of a dictionary file",
        description = "Import translations from CSV file. If lang parameter is provided, only specified language is imported."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "OK - Returning the status of the import",
            content = @Content(mediaType = "application/json",
                schema = @Schema(implementation = DictionaryImportResponse.class))),
        @ApiResponse(responseCode = "400", description = "Bad Request - Invalid input or parameters"),
        @ApiResponse(responseCode = "403", description = "Forbidden: Access denied due to lack of permissions or incorrect token"),
        @ApiResponse(responseCode = "409", description = "Conflict"),
        @ApiResponse(responseCode = "500", description = "Internal Server Error - An unexpected error occurred on the server"),
        @ApiResponse(responseCode = "503", description = "Service Unavailable")
    })
    @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'UPDATE')")
    public ResponseEntity<DictionaryImportResponse> uploadDictionary(
            HttpServletRequest request,
            @Parameter(description = "Inform about the dictionary type", required = true)
            @PathVariable("dictType") DictionaryType dictType,
            @Parameter(description = "Selects translations related to only one language")
            @RequestParam(value = "lang", required = false) String lang,
            @Parameter(description = "CSV file to upload", required = true)
            @RequestParam("file") MultipartFile file) {

        log.info("Uploading dictionary for type: {}, language: {}, file: {}",
                dictType, lang, file.getOriginalFilename());

        DictionaryImportResponse response = gisTranslationService.importTranslations(dictType, lang, file);
        
        return ResponseEntity.status(201).body(response);
    }
}
