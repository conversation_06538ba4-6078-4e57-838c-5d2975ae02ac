/**
 * 
 */
package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.security.bean.User;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import javax.ws.rs.core.Response;
import java.io.*;

/**
 * <AUTHOR> GHOZZI <<EMAIL>>
 * @company Generix group
 * @date 17 april 2025
 */
public interface TranslationService {

  String SERVICE_NAME = "gnxTranslationService";

  Response exportCSVTranslation(File tmpFile);

  Response importTranslationsData(User user, String locale, Attachment file);

  Response exportTranslationsWithLang(String requestedLangs);
  Response importTranslationsWithLang(String requestedLangs, InputStream uploadedInputStream);


}
