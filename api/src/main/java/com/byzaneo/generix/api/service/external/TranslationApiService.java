package com.byzaneo.generix.api.service.external;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import io.swagger.annotations.*;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import java.io.InputStream;
import java.util.UUID;

@Api(tags = "TranslationApiService", description = "Endpoints for managing Translation")
@Path(TranslationApiService.SERVICE_PATH)
public interface TranslationApiService {

  String SERVICE_NAME = "gnxRestTranslationApiService";
  String SERVICE_PATH = "v2/environments/translations";

  @GET
  @Path("/{dictType}")
  @Produces("application/octet-stream")
  @ApiOperation(
      value = "Retrieve the translation dictionary through a download of a CSV file",
      notes = "Downloads the translation dictionary as a CSV based on type and optional language filter. Supports both 'frontend' and 'errors' dictionary types."
  )
  @ApiResponses({
      @ApiResponse(code = 200, message = "OK - Returning the dictionary file"),
      @ApiResponse(code = 400, message = "Bad request", response = ErrorDTO.class),
      @ApiResponse(code = 403, message = "Forbidden", response = ErrorDTO.class),
      @ApiResponse(code = 500, message = "Internal server error", response = ErrorDTO.class),
      @ApiResponse(code = 503, message = "Service unavailable", response = ErrorDTO.class)
  })
  Response downloadDictionary(
      @Context HttpServletRequest request,
      @HeaderParam("requestId") UUID requestId,
      @PathParam("dictType") @ApiParam(required = true, allowableValues = "errors,frontend") String dictType,
      @QueryParam("lang") @ApiParam(required = false, value = "Single ISO 639-1 language code") String lang
  );



  @POST
  @Path("/{dictType}")
  @Consumes(MediaType.MULTIPART_FORM_DATA)
  @Produces(MediaType.APPLICATION_JSON)
  @ApiOperation(
      value = "Import a translation dictionary from a CSV file",
      notes = "Allows importing translations by uploading a CSV file. Supports both 'frontend' and 'errors' dictionary types. Headers must include 'module', 'key', and language columns."
  )
  @ApiResponses({
      @ApiResponse(code = 200, message = "Import completed", response = DictionaryImportResponse.class),
      @ApiResponse(code = 400, message = "Bad request", response = ErrorDTO.class),
      @ApiResponse(code = 403, message = "Forbidden", response = ErrorDTO.class),
      @ApiResponse(code = 500, message = "Internal server error", response = ErrorDTO.class),
      @ApiResponse(code = 503, message = "Service unavailable", response = ErrorDTO.class)
  })
  Response importDictionary(
      @Context HttpServletRequest request,
      @HeaderParam("requestId") UUID requestId,
      @PathParam("dictType") @ApiParam(required = true, allowableValues = "errors,frontend") String dictType,
      @QueryParam("lang") @ApiParam(required = false, value = "Single ISO 639-1 language code") String lang,
      @FormDataParam("file") @ApiParam(value = "CSV file to upload", required = true) InputStream fileInputStream,
      @FormDataParam("file") FormDataContentDisposition fileDetail
  );
}
