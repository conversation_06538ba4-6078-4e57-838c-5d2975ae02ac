/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.api.dto.translation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Error tracking for failed import operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportError {
    
    /**
     * Line number where the error occurred
     */
    private Integer lineNumber;
    
    /**
     * Error message describing what went wrong
     */
    private String errorMessage;
}
