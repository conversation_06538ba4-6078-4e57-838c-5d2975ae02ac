package com.byzaneo.generix.api.service.external.impl;

import com.byzaneo.generix.api.service.external.TranslationApiService;
import com.byzaneo.generix.api.service.external.delegators.TranslationServiceDelegator;
import lombok.RequiredArgsConstructor;

import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.InputStream;
import java.util.UUID;

import static org.slf4j.LoggerFactory.getLogger;

@Service(TranslationApiService.SERVICE_NAME)
@RequiredArgsConstructor
public class TranslationApiServiceImpl implements TranslationApiService {
    private static final Logger log = getLogger(TranslationApiServiceImpl.class);
    private final TranslationServiceDelegator translationServiceDelegator;

    @Override
    @Transactional
    public Response downloadDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang) {
        return translationServiceDelegator.downloadDictionary(request ,requestId,dictType,lang );
    }

    @Override
    @Transactional
    public Response importDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang, InputStream fileInputStream, FormDataContentDisposition fileDetail) {
        return translationServiceDelegator.importDictionary(request, requestId, dictType, lang, fileInputStream, fileDetail);
    }
}
