/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.api.service.gis.impl;

import com.byzaneo.angular.service.ExceptionType;
import com.byzaneo.angular.service.RestJwtException;
import com.byzaneo.generix.api.service.gis.GisTranslationController;
import com.byzaneo.generix.api.service.gis.dto.TranslationImportReportDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Implementation of GIS Translation Controller for JAX-RS endpoints
 * Provides /v1/translations/{dictType} endpoints for translation management
 */
@Service("gisTranslationJaxrsController")
@RequiredArgsConstructor
@Slf4j
public class GisTranslationControllerImpl implements GisTranslationController {

    // For now, we'll delegate to the existing REST translation service
    // In a full implementation, this would use the same service as the Spring Boot controller
    
    @Override
    @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
    public Response downloadDictionary(HttpServletRequest request, String dictType, String lang) {
        log.info("Downloading dictionary for type: {}, language: {}", dictType, lang);
        
        try {
            // Validate dictionary type
            if (!"frontend".equals(dictType) && !"errors".equals(dictType)) {
                throw new RestJwtException("Invalid dictionary type. Must be 'frontend' or 'errors'", ExceptionType.BAD_REQUEST);
            }
            
            // For now, return a simple CSV with headers
            // In a full implementation, this would use the same logic as the Spring Boot controller
            String csvContent = generateSampleCsv(dictType, lang);
            byte[] csvData = csvContent.getBytes(StandardCharsets.UTF_8);
            
            String filename = dictType + "_translations.csv";
            
            return Response.ok(csvData, MediaType.APPLICATION_OCTET_STREAM)
                .header("Content-Disposition", "attachment; filename=" + filename)
                .header("Content-Length", csvData.length)
                .build();
                
        } catch (Exception e) {
            log.error("Error downloading dictionary", e);
            throw new RestJwtException("Failed to download dictionary: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'UPDATE')")
    public Response uploadDictionary(HttpServletRequest request, String dictType, String lang, Attachment file) {
        log.info("Uploading dictionary for type: {}, language: {}, file: {}", dictType, lang, file.getContentType());
        
        try {
            // Validate dictionary type
            if (!"frontend".equals(dictType) && !"errors".equals(dictType)) {
                throw new RestJwtException("Invalid dictionary type. Must be 'frontend' or 'errors'", ExceptionType.BAD_REQUEST);
            }
            
            // Validate file
            if (file == null || file.getDataHandler() == null) {
                throw new RestJwtException("No file provided", ExceptionType.BAD_REQUEST);
            }
            
            // Process the file
            TranslationImportReportDto report = processImportFile(dictType, lang, file);
            
            return Response.status(Response.Status.CREATED)
                .entity(report)
                .build();
                
        } catch (RestJwtException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error uploading dictionary", e);
            throw new RestJwtException("Failed to upload dictionary: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
        }
    }
    
    /**
     * Generate a sample CSV for demonstration purposes
     * In a full implementation, this would use the same service as the Spring Boot controller
     */
    private String generateSampleCsv(String dictType, String lang) {
        StringBuilder sb = new StringBuilder();
        
        if ("frontend".equals(dictType)) {
            sb.append("module;key;fr;en");
            if (lang != null && !lang.isEmpty() && !"fr".equals(lang) && !"en".equals(lang)) {
                sb.append(";").append(lang);
            }
            sb.append("\n");
            sb.append("common;hello;bonjour;hello");
            if (lang != null && !lang.isEmpty() && !"fr".equals(lang) && !"en".equals(lang)) {
                sb.append(";hola"); // Sample translation
            }
            sb.append("\n");
        } else if ("errors".equals(dictType)) {
            sb.append("module;key;fr;en");
            if (lang != null && !lang.isEmpty() && !"fr".equals(lang) && !"en".equals(lang)) {
                sb.append(";").append(lang);
            }
            sb.append("\n");
            sb.append("errors;ERR001;Erreur;Error");
            if (lang != null && !lang.isEmpty() && !"fr".equals(lang) && !"en".equals(lang)) {
                sb.append(";Error"); // Sample translation
            }
            sb.append("\n");
        }
        
        return sb.toString();
    }
    
    /**
     * Process the import file and return a report
     * In a full implementation, this would use the same service as the Spring Boot controller
     */
    private TranslationImportReportDto processImportFile(String dictType, String lang, Attachment file) throws IOException {
        List<TranslationImportReportDto.ImportErrorDto> errors = new ArrayList<>();
        int totalImported = 0;
        
        try (InputStream inputStream = file.getDataHandler().getInputStream()) {
            // Read the file content
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] data = new byte[1024];
            int nRead;
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            
            String content = buffer.toString(StandardCharsets.UTF_8);
            String[] lines = content.split("\n");
            
            if (lines.length < 2) {
                errors.add(new TranslationImportReportDto.ImportErrorDto(0, "File must contain at least a header and one data row"));
                return new TranslationImportReportDto(totalImported, errors);
            }
            
            // Validate header
            String header = lines[0].trim();
            if (!header.startsWith("module;key")) {
                errors.add(new TranslationImportReportDto.ImportErrorDto(1, "Header must start with 'module;key'"));
                return new TranslationImportReportDto(totalImported, errors);
            }
            
            // Process data rows (simplified validation)
            for (int i = 1; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.isEmpty()) continue;
                
                String[] parts = line.split(";");
                if (parts.length < 3) {
                    errors.add(new TranslationImportReportDto.ImportErrorDto(i + 1, "Row must have at least module, key, and one translation"));
                    continue;
                }
                
                // In a real implementation, this would validate and save the translation
                totalImported++;
            }
        }
        
        return new TranslationImportReportDto(totalImported, errors);
    }
}
