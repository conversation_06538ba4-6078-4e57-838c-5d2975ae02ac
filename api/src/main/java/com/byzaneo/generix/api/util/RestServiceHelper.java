package com.byzaneo.generix.api.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.exception.RestSecurityException;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.service.InstanceService;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.generix.xcbl.portal.task.XcblRteValidationEngine;
import com.byzaneo.security.api.Organization;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.spring.UserDetails;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.CustomLocaleMapper;
import com.fasterxml.jackson.databind.*;
import com.google.gson.*;
import com.google.gson.internal.bind.TypeAdapters;
import com.google.gson.stream.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.saml.SAMLCredential;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import javax.validation.*;
import javax.ws.rs.core.Response;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.security.service.SecurityService.GroupName.ADMIN;
import static com.byzaneo.security.util.PrincipalHelper.isCompanyUser;
import static java.util.Arrays.asList;
import static java.util.Locale.getDefault;
import static java.util.Optional.ofNullable;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.security.core.context.SecurityContextHolder.getContext;

/**
 * Helper for Rest Service exposed to users.
 *
 * <AUTHOR> Boutour
 * @date 05/08/2015
 */
public class RestServiceHelper {
  private static final Logger logger = getLogger(RestServiceHelper.class);

  private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
  private static Validator validator = validatorFactory.getValidator();

  private static final Gson gson = new GsonBuilder().serializeNulls()
          .setPrettyPrinting()
          .create();
  public static DocumentStatusService documentStatusServiceBean = getBean(DocumentStatusService.class, DocumentStatusService.SERVICE_NAME);

  public static final ModelMapper modelMapper = new ModelMapper();
  public static final String LABEL_FAMILY = "gnxxcblinvlbls";

  private static InstanceService instanceService;

  private static XcblRteValidationEngine xcblRteValidationEngine;


  /**
   * Check if Principal user of Spring's context rely to the specified instance.
   *
   * @param instance Instance
   * @return Credential if authorization is ok
   * @throws com.byzaneo.generix.api.exception.RestSecurityException If User has no access to Instance
   */
  public static Credential checkInstanceUser(HttpServletRequest request, final Instance instance, boolean isCompanyOnly) {
    notNull(instance, "Environment not found (check instance code)");

    final User user = getAndCheckPrincipalUser(request);
    final Group org = user.getPrimaryGroup();

    if (isCompanyOnly) {
      isTrue(org instanceof Organization && instance.getGroup()
                      .equals(org),
              "Authenticated user's organizaton is not managing environment: " + instance.getCode());
    }
    else {
      boolean isAdministrator = CollectionUtils.containsAny(PrincipalHelper.getGroupNameList(user.getGroups()), asList(ADMIN.toString()));
      isTrue(isAdministrator || org instanceof Organization && (instance.getGroup()
                      .equals(org) ||
                      instance.getGroup()
                              .equals(org.getParent())),
              "Authenticated user's partner is not allowed on environment: " + instance.getCode());
    }

    return new Credential(user, instance, (Organization) org);
  }

  /**
   * Check if Principal user of Spring's context rely to the specified instance.
   *
   * @return Credential if authorization is ok
   * @throws com.byzaneo.generix.api.exception.RestSecurityException If User is not administrator
   */
  public static Credential checkAdminUser(HttpServletRequest request) {

    final User user = getAndCheckPrincipalUser(request);
    final Group org = user.getPrimaryGroup();

    boolean isAdministrator = CollectionUtils.containsAny(PrincipalHelper.getGroupNameList(user.getGroups()), asList(ADMIN.toString()));
    isTrue(isAdministrator, "Authenticated user is not administrator");

    return new Credential(user, null, (Organization) org);
  }

  public static boolean hasBearerToken(HttpServletRequest request) {
    String authHeader = request.getHeader("Authorization");
    return authHeader != null && authHeader.toLowerCase()
        .startsWith("bearer ");
  }

  public static void checkTechnicalUserInfo(TechnicalUser technicalUser, String instanceCode, boolean isCompanyOnly) {
    boolean isAdministrator = false;
    if (instanceCode == null) {
      isAdministrator = technicalUser.getPrimaryGroup()
              .equals(ADMIN.toString());
      isTrue(isAdministrator, "Authenticated user is not administrator");
    }
    else {
      Instance instance = ofNullable(instanceCode).map(code -> code.toUpperCase(getDefault()))
              .map(getInstanceService()::getInstanceByCode)
              .orElseThrow(() -> new RestSecurityException("Environment not found (Check the instance code)"));
      final Group org = technicalUser.getPrimaryGroup();
      if (isCompanyOnly) {
        isTrue(org instanceof Organization && instance.getGroup()
                        .equals(org),
                "Authenticated user's organizaton is not managing environment: " + instance.getCode());
      }
      else {
        isAdministrator = technicalUser.getPrimaryGroup()
                .equals(ADMIN.toString());
        isTrue(isAdministrator || org instanceof Organization && (instance.getGroup()
                        .equals(org) ||
                        instance.getGroup()
                                .equals(org.getParent())),
                "Authenticated user's partner is not allowed on environment: " + instance.getCode());
      }
    }
  }

  /**
   * Check if Principal user of Spring's context rely to the given Organization. User can access to their own Partner, or Company that holds
   * Partner. Not siblings Partners. By the way, user on Company has access to every Partners hold by Company.
   *
   * @param checkedOrganization Organization checked (Company or Partner)
   * @throws RestSecurityException If User has no access to Organization
   */
  public static void checkGroupUser(HttpServletRequest request, final com.byzaneo.security.bean.Organization checkedOrganization) {
    if (!isGroupUser(request, checkedOrganization)) {
      throw new RestSecurityException(
              "Authenticated user's organizaton is not managing the checkedOrganization <" + checkedOrganization.getCode() + ">");
    }
  }

  /**
   * Check if Principal user of Spring's context rely to the given Organization. User can access to their own Partner, or Company that holds
   * Partner. Not siblings Partners. By the way, user on Company has access to every Partners hold by Company.
   *
   * @param checkedOrganization Organization checked (Company or Partner)
   * @return True is user has access, false otherwise
   */
  public static boolean isGroupUser(HttpServletRequest request, final com.byzaneo.security.bean.Organization checkedOrganization) {
    Assert.notNull(checkedOrganization, "Group is required");

    final Group userOrganization = getAndCheckPrincipalUser(request).getPrimaryGroup();
    if (userOrganization instanceof Partner && checkedOrganization instanceof Partner ||
            userOrganization instanceof Company && checkedOrganization instanceof Company) {
      return userOrganization.equals(checkedOrganization);
    }
    else if (checkedOrganization instanceof Company && userOrganization instanceof Partner) {
      return userOrganization.getParent()
              .equals(checkedOrganization);
    }
    else if (checkedOrganization instanceof Partner && userOrganization instanceof Company) {
      return userOrganization.equals(checkedOrganization.getParent());
    }

    logger.warn("Unable to determine access rights for organisation <" + checkedOrganization.getCode() + ">");
    return false;
  }

  public static File getRestTriggerFolder(String filename) {
    return new File(getConfigurationService().getFile(DATA_DIR, getTempDirectory()), "restTrigger" + File.separator + filename);
  }

  public static File getRestTriggerFilesFolder(String filename) {
    return getRestTriggerFolder(filename);
  }

  private static void notNull(final Object obj, final String message) {
    isTrue(obj != null, message);
  }

  private static void isTrue(final boolean condition, final String message) {
    if (!condition) {
      throw new RestSecurityException(message);
    }
  }

  private static User getAndCheckPrincipalUser(HttpServletRequest request) {
    try {
      // in case of SSO login the UserDetails object is set on getContext().getAuthentication().getDetails() and not on
      // getContext().getAuthentication().getPrincipal()
      Optional<Object> samlCredentials = ofNullable(getContext().getAuthentication()
              .getCredentials());
      if (samlCredentials.isPresent() && samlCredentials.get() instanceof SAMLCredential) {
        return ofNullable(((UserDetails) getContext()
                .getAuthentication()
                .getDetails())
                .getUser())
                .orElseThrow(() -> new RestSecurityException("User not found"));
      }
      //get the user from request instead of context if it exists (used for Swagger)
      String login = getUserFromRequest(request);
      if (login != null && !login.equals("REST_USER")) {
        AccountService accountService = getBean(AccountService.class, AccountService.SERVICE_NAME);
        User userByLoginOrOpenID = accountService.getUserByLoginOrOpenID(login);
        if (userByLoginOrOpenID.getSwitchUserLogin() != null) {
          userByLoginOrOpenID = accountService.getUserByLoginOrOpenID(userByLoginOrOpenID.getSwitchUserLogin());
        }
        if (userByLoginOrOpenID != null)
          return userByLoginOrOpenID;
      }
      return ofNullable(((UserDetails) getContext()
              .getAuthentication()
              .getPrincipal())
              .getUser())
              .orElseThrow(() -> new RestSecurityException("User not found"));

    }
    catch (RestSecurityException rse) {
      throw rse;
    }
    catch (Exception e) {
      throw new RestSecurityException(getRootCauseMessage(e));
    }
  }

  private RestServiceHelper() {
    throw new AssertionError();
  }

  public static <T> Map<String, String> checkValidity(T t) {
    Set<ConstraintViolation<T>> violations = validator.validate(t);
    if (CollectionUtils.isNotEmpty(violations)) {
      return violations.stream()
              .collect(
                      Collectors.toMap(v -> v.getPropertyPath()
                              .toString(), v -> v.getMessage()));
    }
    return null;
  }

  public static Response getResponse(Response.Status status, Object entity) {
    Gson gson = GsonHelper.getGson();
    return Response.status(status)
            .entity(entity instanceof String
                    ? entity
                    : GsonHelper.getGson()
                    .toJson(entity))
            .build();
  }

  public static Response getResponse(Response.Status status, Object entity, UUID requestId) {
    return Response.status(status)
            .entity(entity instanceof String ? entity : gson.toJson(entity))
            .header("Request-Id", requestId)
            .build();
  }
  public static Response getResponseWithRequestId(Response.Status status, Object entity, UUID requestId) {
    return Response.status(status)
            .entity(entity)
            .header("Request-Id", requestId)
            .build();
  }

  static class EmptyToNullTypeAdapter extends TypeAdapter<String> {

    @Override
    public void write(JsonWriter jsonWriter, String s) throws IOException {
      if (s != null && (s.isEmpty() || "null".equals(s))) {
        jsonWriter.nullValue();
      }
      else {
        TypeAdapters.STRING.write(jsonWriter, s);
      }
    }

    @Override
    public String read(JsonReader jsonReader) throws IOException {
      return TypeAdapters.STRING.read(jsonReader);
    }
  }

  public static CompanyDto convertNull(CompanyDto t) {
    Gson gson = createGson();
    return gson.fromJson(String.valueOf(gson.toJson(t)), CompanyDto.class);
  }

  static Gson createGson() {
    return new GsonBuilder().serializeNulls()
            .registerTypeAdapter(String.class,
                    new EmptyToNullTypeAdapter())
            .create();
  }

  public static List<SelectItem> getCountryItems() {
    return CustomLocaleMapper.getCountryItems();
  }

  /**
   * methode to get a username from HttpServletRequest. We do that in case of Swagger only This allows to let the admin test the API using
   * Swagger
   *
   * @param request
   * @return
   */
  public static String getUserFromRequest(HttpServletRequest request) {
    if (SecurityContextHolder.getContext()
            .getAuthentication()
            .getAuthorities()
            .stream()
            .filter(c -> c.getAuthority()
                    .equals("ROLE_ADMIN"))
            .findFirst()
            .orElse(null) !=
            null) {
      //used to let it work for Swagger
      if (request.getHeader("userName") != null) {
        return request.getHeader("userName");
      }
    }
    return SecurityContextHolder.getContext()
            .getAuthentication()
            .getName();
  }

  public static String getTechnicalUserIdFromRequest(HttpServletRequest request) {
    TechnicalUser technicalUser = (TechnicalUser) SecurityContextHolder.getContext()
            .getAuthentication()
            .getPrincipal();
    return technicalUser.getId();
  }

  public static String getJWTUsername(HttpServletRequest request) {
    String token = request.getHeader("Authorization");
    DecodedJWT decodedJWT = JWT.decode(token.substring(7));
    String username = decodedJWT.getSubject();
    return username;
  }

  public static InvoiceDTO transformToInvoiceDtoForRest(InvoiceIndex invoice, boolean fromAngular, Locale locale, String instanceCode) {
    InvoiceForRestDTO invoiceDTO = new InvoiceForRestDTO();
    invoiceDTO.setOwners(invoice.getOwners());
    invoiceDTO.setFrom(invoice.getFrom());
    invoiceDTO.setTo(invoice.getTo());
    invoiceDTO.setStage(InvoiceDTO.StageEnum.fromValue(invoice.getStage()));
    invoiceDTO.setArchiveStatus(InvoiceDTO.ArchiveStatusEnum.fromValue(invoice.getArchiveStatus()));
    invoiceDTO.setConsultStatus(InvoiceDTO.ConsultStatusEnum.fromValue(invoice.getConsultStatus()));
    invoiceDTO.setInvoiceTypeCoded(InvoiceDTO.InvoiceTypeCodedEnum.fromValue(invoice.getInvoiceTypeCoded()));
    invoiceDTO.setInvoiceCurrencyCoded(InvoiceDTO.CurrencyCodedEnum.fromValue(invoice.getInvoiceCurrencyCoded()));
    invoiceDTO.setBuyerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getBuyerPartyCountryCoded()));
    invoiceDTO.setSellerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getSellerPartyCountryCoded()));
    invoiceDTO.setBillToPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getBillToPartyCountryCoded()));
    invoiceDTO.setStatus(invoice.getStatusAsString());
    setInvoiceDto(invoice, invoiceDTO);
    invoiceDTO.setType(invoice.getType());
    invoiceDTO.setContractID(invoice.getContractID());
    invoiceDTO.setFreeText01(invoice.getFreeText01());
    invoiceDTO.setFreeText02(invoice.getFreeText02());
    invoiceDTO.setFreeText03(invoice.getFreeText03());
    invoiceDTO.setFreeText04(invoice.getFreeText04());
    invoiceDTO.setFreeText05(invoice.getFreeText05());
    invoiceDTO.setFreeText06(invoice.getFreeText06());
    invoiceDTO.setFreeText07(invoice.getFreeText07());
    invoiceDTO.setFreeText08(invoice.getFreeText08());
    invoiceDTO.setFreeText09(invoice.getFreeText09());
    invoiceDTO.setFreeText10(invoice.getFreeText10());
    invoiceDTO.setFreeText11(invoice.getFreeText11());
    invoiceDTO.setFreeText12(invoice.getFreeText12());
    invoiceDTO.setFreeText13(invoice.getFreeText13());
    invoiceDTO.setFreeText14(invoice.getFreeText14());
    invoiceDTO.setFreeText15(invoice.getFreeText15());
    invoiceDTO.setFreeText16(invoice.getFreeText16());
    invoiceDTO.setFreeText17(invoice.getFreeText17());
    invoiceDTO.setFreeText18(invoice.getFreeText18());
    invoiceDTO.setFreeText19(invoice.getFreeText19());
    invoiceDTO.setFreeText20(invoice.getFreeText20());
    invoiceDTO.setFreeText21(invoice.getFreeText21());
    invoiceDTO.setFreeText22(invoice.getFreeText22());
    invoiceDTO.setFreeText23(invoice.getFreeText23());
    invoiceDTO.setFreeText24(invoice.getFreeText24());
    invoiceDTO.setFreeText25(invoice.getFreeText25());
    invoiceDTO.setFreeText26(invoice.getFreeText26());
    invoiceDTO.setFreeText27(invoice.getFreeText27());
    invoiceDTO.setFreeText28(invoice.getFreeText28());
    invoiceDTO.setFreeText29(invoice.getFreeText29());
    invoiceDTO.setFreeText30(invoice.getFreeText30());
    invoiceDTO.setFreeText31(invoice.getFreeText31());
    invoiceDTO.setFreeText32(invoice.getFreeText32());
    invoiceDTO.setFreeText33(invoice.getFreeText33());
    invoiceDTO.setFreeText34(invoice.getFreeText34());
    invoiceDTO.setFreeText35(invoice.getFreeText35());
    return invoiceDTO;
  }


  private static void setInvoiceDto(InvoiceIndex invoice, InvoiceDTO invoiceDTO) {
    /*getMessage("gnxworkflowlbls" + ".delete_functionUsers_msg",
        "A function cannot be empty. Please select at least one name", getLocale());*/
    invoiceDTO.setReference(invoice.getReference());
    invoiceDTO.setEntityId(invoice.getEntityId());
    invoiceDTO.setEntityRef(invoice.getEntityRef());
    invoiceDTO.setCreationDate(invoice.getCreationDate());
    invoiceDTO.setUuid(invoice.getUuid());
    invoiceDTO.setModificationDate(invoice.getModificationDate());
    invoiceDTO.setInvoiceNumber(invoice.getInvoiceNumber());
    invoiceDTO.setInvoiceIssueDate(invoice.getInvoiceIssueDate());
    invoiceDTO.setPurchaseOrderDate(invoice.getPurchaseOrderDate());
    invoiceDTO.setBuyerOrderNumber(invoice.getBuyerOrderNumber());
    invoiceDTO.setAsnNumber(invoice.getAsnNumber());
    invoiceDTO.setAsnDate(invoice.getAsnDate());
    invoiceDTO.setInvoiceDueDate(invoice.getInvoiceDueDate());
    invoiceDTO.setInvoiceProcessDateTime(invoice.getInvoiceProcessDateTime());
    invoiceDTO.setInvoicePreparationDateTime(invoice.getInvoicePreparationDateTime());
    invoiceDTO.setBuyerPartyID(invoice.getBuyerPartyID());
    invoiceDTO.setBuyerPartyName(invoice.getBuyerPartyName());
    invoiceDTO.setSellerPartyID(invoice.getSellerPartyID());
    invoiceDTO.setSellerPartyName(invoice.getSellerPartyName());
    invoiceDTO.setSellerPartyTaxIdentifier(invoice.getSellerPartyTaxIdentifier());
    invoiceDTO.setSellerPartyOtherID(invoice.getSellerPartyOtherID());
    invoiceDTO.setBillToPartyID(invoice.getBillToPartyID());
    invoiceDTO.setBillToPartyName(invoice.getBillToPartyName());
    invoiceDTO.setBillToPartyTaxIdentifier(invoice.getBillToPartyTaxIdentifier());
    invoiceDTO.setBillToPartyOtherID(invoice.getBillToPartyOtherID());
    invoiceDTO.shipToPartyID(invoice.getShipToPartyID());
    invoiceDTO.shipToPartyName(invoice.getShipToPartyName());
    invoiceDTO.setInvoiceTotal(getDoubleValue(invoice.getInvoiceTotal()));
    invoiceDTO.setInvoiceTotalTAC(invoice.getInvoiceTotalTAC());
    invoiceDTO.setTaxableValue(getDoubleValue(invoice.getTaxableValue()));
    invoiceDTO.setTaxableValueTAC(invoice.getTaxableValueTAC());
    invoiceDTO.setTotalTaxAmount(getDoubleValue(invoice.getTotalTaxAmount()));
    invoiceDTO.setTotalTaxAmountTAC(invoice.getTotalTaxAmountTAC());
    invoiceDTO.setTotalAmountPayableTAC(invoice.getTotalAmountPayableTAC());
    invoiceDTO.setNature(invoice.getNature());
    invoiceDTO.setB2gRules(invoice.getB2gRules());
    invoiceDTO.setEreportingOption(invoice.getEreportingOption());
    invoiceDTO.setCountryRules1(invoice.getCountryRules1());
    invoiceDTO.setCountryRules2(invoice.getCountryRules2());
    invoiceDTO.setUseCase(invoice.getUseCase());
    invoiceDTO.setBillingFramework(invoice.getBillingFramework());
    invoiceDTO.setSovosId(invoice.getSovosId());
    invoiceDTO.setSovosStatus(invoice.getSovosStatus());
    invoiceDTO.setSovosTransactionId(invoice.getSovosTransactionId());
    invoiceDTO.setSovosStatusCode(invoice.getSovosStatusCode());
    invoiceDTO.setSovosNotificationId(invoice.getSovosNotificationId());
    invoiceDTO.setArchiveExpirationDate(invoice.getArchiveExpirationDate());
    invoiceDTO.setRossumId(invoice.getRossumId());
    invoiceDTO.setThirdParty(invoice.getThirdParty());
    transformInvoiceWorkflowIndexDTO(invoice,invoiceDTO);
    if (invoice.getSummaryBreakdownValueAddedTax() != null) {
      List<ValueAddedTaxDTO> addedTaxDTOs = invoice.getSummaryBreakdownValueAddedTax()
              .stream()
              .map(addedVal -> transformValueAddedTaxToValueAddedTaxDTO(addedVal))
              .collect(Collectors.toList());
      invoiceDTO.setSummaryBreakdownValueAddedTax(addedTaxDTOs);
    }
    if (invoice.getDetails() != null) {
      List<InvoiceDetailDTO> invoiceDetailDTOList = invoice.getDetails()
              .stream()
              .map(inv -> transformInvoiceDetailIndexToInvoiceDetailDTO(inv))
              .collect(Collectors.toList());
      invoiceDTO.setDetails(invoiceDetailDTOList);
    }
    invoiceDTO.setImmatpdp(invoice.getImmatpdp());
  }

  public static void transformInvoiceWorkflowIndexDTO(InvoiceIndex index,InvoiceDTO invoiceDTO){
    if(index != null && index.getWkfName()!=null){
      invoiceDTO.setWkfName(index.getWkfName());
      invoiceDTO.setWkfLastModificationDate(index.getWkfLastModificationDate());
      invoiceDTO.setWkfCompletionPercentage(index.getWkfCompletionPercentage());
      invoiceDTO.setWkfLockedBy(index.getWkfLockedBy());
      invoiceDTO.setWkfStep(index.getWkfStep());
      invoiceDTO.setWkfLockedById(index.getWkfLockedById());
      invoiceDTO.setWkfVersion(index.getWkfVersion());
      invoiceDTO.setWkfNumberOfSteps(index.getWkfNumberOfSteps());
      invoiceDTO.setWkfStepActions(index.getWkfStepActions());
      invoiceDTO.setWkfStepActors(index.getWkfStepActors());
      invoiceDTO.setWkfStepActorsList(index.getWkfStepActorsList());
    }
  }



  public static InvoiceDetailDTO transformInvoiceDetailIndexToInvoiceDetailDTO(InvoiceDetailIndex index) {
    InvoiceDetailDTO invoiceDetailDTO = new InvoiceDetailDTO();
    invoiceDetailDTO.setItemLineNumber(index.getItemLineNumber());
    invoiceDetailDTO.setItemProductIdentifier(index.getItemProductIdentifier());
    invoiceDetailDTO.setItemBuyerPartNumber(index.getItemBuyerPartNumber());
    invoiceDetailDTO.setItemSellerPartNumber(index.getItemSellerPartNumber());
    invoiceDetailDTO.setItemDescription(index.getItemDescription());
    invoiceDetailDTO.setItemInvoicedQuantityValue(getDoubleValue(index.getItemInvoicedQuantityValue()));
    invoiceDetailDTO.setItemQuantityUnitOfMeasurement(index.getItemQuantityUnitOfMeasurement());
    invoiceDetailDTO.setItemTaxPercent(getDoubleValue(index.getItemTaxPercent()));
    invoiceDetailDTO.setItemTotal(getDoubleValue(index.getItemTotal()));
    invoiceDetailDTO.setItemTotalTAC(index.getItemTotalTAC() == null ? index.getItemTotal() : index.getItemTotalTAC());
    if(index.getItemSubTotal() != null) {
      invoiceDetailDTO.setItemSubTotal(getDoubleValue(index.getItemSubTotal()));
    }
    invoiceDetailDTO.setItemGrossUnitPrice(getDoubleValue(index.getItemGrossUnitPrice()));
    invoiceDetailDTO.setItemNetUnitPrice(getDoubleValue(index.getItemNetUnitPrice()));
    return invoiceDetailDTO;
  }

  public static ValueAddedTaxDTO transformValueAddedTaxToValueAddedTaxDTO(com.byzaneo.xtrade.xcbl.bean.ValueAddedTax addedVal) {
    ValueAddedTaxDTO addedTaxDTO = new ValueAddedTaxDTO();
    addedTaxDTO.setRate(addedVal.getRate());
    addedTaxDTO.setTaxableAmount(getDoubleValue(addedVal.getTaxableAmount()));
    addedTaxDTO.setTaxAmount(getDoubleValue(addedVal.getTaxAmount()));
    addedTaxDTO.setTaxAmountTAC(addedVal.getTaxAmountTAC() == null ? addedVal.getTaxAmount() : addedVal.getTaxAmountTAC());
    addedTaxDTO.setTaxableAmountTAC(addedVal.getTaxableAmountTAC() == null ? addedVal.getTaxableAmount() : addedVal.getTaxableAmountTAC());
    return addedTaxDTO;
  }

  public static Double getDoubleValue(BigDecimal value) {
    if (value != null)
      return value.doubleValue();
    return null;
  }

  public static Response getResponseOnError(String errorCode, String errorMessage, Response.Status statut, UUID requestId) {
    ErrorDTO error = new ErrorDTO();
    error.setErrorCode(errorCode);
    error.setErrorMessage(errorMessage);
    return RestServiceHelper.getResponse(statut, error, requestId);
  }

  public static Response getResponseOnError(String errorCode, String errorMessage, Response.Status statut) {
    ErrorDTO error = new ErrorDTO();
    error.setErrorCode(errorCode);
    error.setErrorMessage(errorMessage);
    return RestServiceHelper.getResponse(statut, error);
  }


  private static String getType(String type) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonNode = objectMapper.readTree(type);

      String normValue = jsonNode.get("norm")
              .asText();
      return normValue;
    }
    catch (Exception e) {
      return type;
    }
  }

  public static Locale getLocaleFromRequest(HttpServletRequest request) {
    if (request == null || request.getLocale() == null) return Locale.ENGLISH;
    return new Locale(request.getLocale()
            .getLanguage());
  }

  public static String ensureXlsExtension(String fileName) {
    String xlsFileName = fileName;
    if (FilenameUtils.getExtension(fileName)
            .isEmpty()) {
      xlsFileName = fileName.concat(FileType.EXCEL.getExtension());
    }
    else if (!FilenameUtils.getExtension(fileName)
            .equals(FileType.EXCEL.getExtension())) {
      FilenameUtils.removeExtension(fileName);
      xlsFileName = fileName.concat(FileType.EXCEL.getExtension());
    }
    return xlsFileName;
  }

  public static Credential checkCredential(HttpServletRequest request, String instanceCode,
                                           boolean companyUserOnly) {
    if (instanceCode == null) return checkAdminUser(request);
    return checkInstanceUser(request, ofNullable(instanceCode).map(code -> code.toUpperCase(getDefault()))
            .map(getInstanceService()::getInstanceByCode)
            .orElseThrow(() -> new RestSecurityException("Environment not found (Check the instance code)")), companyUserOnly);
  }

  private static InstanceService getInstanceService() {
    if (instanceService == null) {
      instanceService = getBean(InstanceService.class, InstanceService.SERVICE_NAME);
    }
    return instanceService;
  }

  public static <S, T> List<T> mapList(List<S> source, Class<T> targetClass) {
    return source
            .stream()
            .map(element -> modelMapper.map(element, targetClass))
            .collect(Collectors.toList());
  }

  public static <S, T> T genericMap(S source, Class<T> targetClass) {
    return modelMapper.map(source, targetClass);
  }

  public static <S, T> List<T> mapList(List<S> source, Class<T> targetClass, ModelMapper customModelMapper) {
    return source
            .stream()
            .map(element -> customModelMapper.map(element, targetClass))
            .collect(Collectors.toList());
  }

  public static byte[] convertInputStreamToByteArray(InputStream inputStream) throws IOException {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    byte[] buffer = new byte[4096]; // Adjust the buffer size as needed
    int bytesRead;

    while ((bytesRead = inputStream.read(buffer)) != -1) {
      outputStream.write(buffer, 0, bytesRead);
    }

    return outputStream.toByteArray();

  }

  public static void verifyEnvironmentCode(TechnicalUser technicalUser, String envCode) {
    Instance instance = ofNullable(envCode).map(code -> code.toUpperCase(getDefault()))
            .map(getInstanceService()::getInstanceByCode)
            .orElseThrow(() -> new RestSecurityException("Environment not found (Check the instance code)"));
    final Group org = technicalUser.getPrimaryGroup();
    boolean isCompanyUser = PrincipalHelper.isCompanyTechnicalUser(technicalUser);
    if (isCompanyUser) {
      isTrue(org instanceof Organization && instance.getGroup()
                      .equals(org),
              "Authenticated user's organizaton is not managing environment: " + instance.getCode());
    }
    else {
      isTrue(org instanceof Organization && (instance.getGroup()
                      .equals(org) ||
                      instance.getGroup()
                              .equals(org.getParent())),
              "Authenticated user's partner is not allowed on environment: " + instance.getCode());
    }
  }

  public static void checkObjectNullability(Object object, String errorMsg,
                                            ExceptionType exceptionType) {
    if (object == null) {
      throw new RestJwtException(errorMsg, exceptionType);
    }
  }

  public static void checkEmptyString(String uuid, String errorMsg, ExceptionType exceptionType) {
    if (StringUtils.isEmpty(uuid)) {
      throw new RestJwtException(errorMsg, exceptionType);
    }
  }

  public static boolean isClientWithPerimeter(User user) {
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(
            user);
    Collection<String> perimeter = userOrgs.getCodes();
    return isCompanyUser(user) && perimeter.size() > 0;
  }

  public static XcblRteValidationEngine getXcblRteValidationEngine(String rteSource, Company company, String envCode) {
    Instance instance = getInstanceService().getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }
    if (xcblRteValidationEngine == null) {
      xcblRteValidationEngine = new XcblRteValidationEngine(rteSource, company, instance);
    }
    return xcblRteValidationEngine;
  }

  public static boolean isBackOfficeUserUser(User user) {
    return null == user.getPrimaryGroup();
  }


  public static enum RestDocumentType {
    ACCOUNTING_POSTING_SCENARIO,
    AAP_REF_SUPPLIER,
    ACCOUNTING_ENTRIES,
    ADVANCED_RECAP,
    ADVANCED_RECAP_DAILY,
  }

  public static User getAuthenticatedUser() {
    Authentication authentication = SecurityContextHolder.getContext()
        .getAuthentication();

    if (authentication == null || !(authentication.getPrincipal() instanceof UserDetails)) {
      return null;
    }

    return ((UserDetails) authentication.getPrincipal()).getUser();
  }

}


