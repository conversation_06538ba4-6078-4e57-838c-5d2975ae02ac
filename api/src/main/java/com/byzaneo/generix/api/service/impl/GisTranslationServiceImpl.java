/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.api.service.impl;

import com.byzaneo.angular.bean.I18NModule;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import com.byzaneo.generix.api.dto.translation.DictionaryType;
import com.byzaneo.generix.api.dto.translation.ImportError;
import com.byzaneo.generix.api.service.GisTranslationService;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.xtrade.bean.DocumentErrorTranslation;
import com.byzaneo.xtrade.bean.DocumentErrorTranslationId;
import com.byzaneo.xtrade.service.DocumentErrorTranslationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementation of GIS Translation Service for handling CSV import/export of translations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GisTranslationServiceImpl implements GisTranslationService {

    private final I18NService i18NService;
    private final I18nTranslationDAO i18nTranslationDAO;
    private final DocumentErrorTranslationService documentErrorTranslationService;

    private static final String DELIMITER = ";";
    private static final Set<String> PROTECTED_LANGUAGES = new HashSet<>(Arrays.asList("fr", "en"));
    
    // ISO 639-1 language codes
    private static final Set<String> VALID_LANGUAGE_CODES = new HashSet<>(Arrays.asList(
        "ab", "aa", "af", "ak", "sq", "am", "ar", "an", "hy", "as", "av", "ae", "ay", "az",
        "bm", "ba", "eu", "be", "bn", "bi", "bs", "br", "bg", "my", "ca", "ch", "ce", "ny",
        "zh", "cu", "cv", "kw", "co", "cr", "hr", "cs", "da", "dv", "nl", "dz", "en", "eo",
        "et", "ee", "fo", "fj", "fi", "fr", "fy", "ff", "gd", "gl", "lg", "ka", "de", "el",
        "kl", "gn", "gu", "ht", "ha", "he", "hz", "hi", "ho", "hu", "is", "io", "ig", "id",
        "ia", "ie", "iu", "ik", "ga", "it", "ja", "jv", "kn", "kr", "ks", "kk", "km", "ki",
        "rw", "ky", "kv", "kg", "ko", "kj", "ku", "lo", "la", "lv", "li", "ln", "lt", "lu",
        "lb", "mk", "mg", "ms", "ml", "mt", "gv", "mi", "mr", "mh", "mn", "na", "nv", "nd",
        "nr", "ng", "ne", "no", "nb", "nn", "ii", "oc", "oj", "or", "om", "os", "pi", "ps",
        "fa", "pl", "pt", "pa", "qu", "ro", "rm", "rn", "ru", "se", "sm", "sg", "sa", "sc",
        "sr", "sn", "sd", "si", "sk", "sl", "so", "st", "es", "su", "sw", "ss", "sv", "tl",
        "ty", "tg", "ta", "tt", "te", "th", "bo", "ti", "to", "ts", "tn", "tr", "tk", "tw",
        "ug", "uk", "ur", "uz", "ve", "vi", "vo", "wa", "cy", "wo", "xh", "yi", "yo", "za", "zu"
    ));

    @Override
    @Transactional(readOnly = true)
    public Resource exportTranslations(DictionaryType dictType, String language) {
        log.info("Exporting translations for dictType: {}, language: {}", dictType, language);

        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);

            if (dictType == DictionaryType.frontend) {
                exportFrontendTranslations(writer, language);
            } else if (dictType == DictionaryType.errors) {
                exportErrorTranslations(writer, language);
            }

            writer.flush();
            writer.close();

            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("Error exporting translations", e);
            throw new RuntimeException("Failed to export translations", e);
        }
    }

    @Override
    @Transactional
    public DictionaryImportResponse importTranslations(DictionaryType dictType, String language, MultipartFile file) {
        log.info("Importing translations for dictType: {}, language: {}", dictType, language);

        List<ImportError> errors = new ArrayList<>();
        int totalImported = 0;

        try (InputStream inputStream = file.getInputStream()) {

            if (dictType == DictionaryType.frontend) {
                totalImported = importFrontendTranslations(inputStream, language, errors);
            } else if (dictType == DictionaryType.errors) {
                totalImported = importErrorTranslations(inputStream, language, errors);
            }

        } catch (IOException e) {
            log.error("Error importing translations", e);
            errors.add(ImportError.builder()
                .lineNumber(0)
                .errorMessage("Failed to read CSV file: " + e.getMessage())
                .build());
        }

        return DictionaryImportResponse.builder()
            .totalImported(totalImported)
            .errors(errors)
            .build();
    }

    private void exportFrontendTranslations(OutputStreamWriter writer, String requestedLanguage) throws IOException {
        List<I18NTranslationDto> translations = i18NService.getTranslations();
        List<Locale> availableLocales = i18NService.getLocales();

        // Filter locales based on requested language
        List<Locale> exportLocales = filterLocales(availableLocales, requestedLanguage);

        // Write header
        StringBuilder headerLine = new StringBuilder();
        headerLine.append("module").append(DELIMITER).append("key");
        for (Locale locale : exportLocales) {
            headerLine.append(DELIMITER).append(locale.toString());
        }
        writer.write(headerLine.toString());
        writer.write("\n");

        // Group translations by module and key
        Map<String, Map<String, Map<Locale, String>>> groupedTranslations = groupFrontendTranslations(translations);

        // Write data rows
        for (Map.Entry<String, Map<String, Map<Locale, String>>> moduleEntry : groupedTranslations.entrySet()) {
            String moduleName = moduleEntry.getKey();
            for (Map.Entry<String, Map<Locale, String>> keyEntry : moduleEntry.getValue().entrySet()) {
                String key = keyEntry.getKey();
                Map<Locale, String> localeValues = keyEntry.getValue();

                StringBuilder row = new StringBuilder();
                row.append(escapeCsv(moduleName)).append(DELIMITER).append(escapeCsv(key));
                for (Locale locale : exportLocales) {
                    String value = localeValues.getOrDefault(locale, "");
                    row.append(DELIMITER).append(escapeCsv(value));
                }
                writer.write(row.toString());
                writer.write("\n");
            }
        }
    }

    private void exportErrorTranslations(OutputStreamWriter writer, String requestedLanguage) throws IOException {
        List<DocumentErrorTranslation> translations = documentErrorTranslationService.getErrorTranslationByLang(requestedLanguage);

        Map<String, Map<String, String>> errorCodeToLangMap = new TreeMap<>();
        Set<String> allLanguages = new TreeSet<>();

        for (DocumentErrorTranslation translation : translations) {
            String errorCode = translation.getId().getErrorCode();
            String lang = translation.getId().getLanguageCode();
            String label = translation.getErrorLabel();

            allLanguages.add(lang);
            errorCodeToLangMap
                .computeIfAbsent(errorCode, k -> new HashMap<>())
                .put(lang, label);
        }

        List<String> exportLangs = (requestedLanguage != null && !requestedLanguage.trim().isEmpty())
            ? Arrays.asList(requestedLanguage.trim())
            : new ArrayList<>(allLanguages);
        exportLangs.sort(String::compareTo);

        // Write header
        StringBuilder headerLine = new StringBuilder();
        headerLine.append("module").append(DELIMITER).append("key");
        for (String lang : exportLangs) {
            headerLine.append(DELIMITER).append(lang);
        }
        writer.write(headerLine.toString());
        writer.write("\n");

        // Write data rows
        for (Map.Entry<String, Map<String, String>> entry : errorCodeToLangMap.entrySet()) {
            String errorCode = entry.getKey();
            Map<String, String> langMap = entry.getValue();

            StringBuilder row = new StringBuilder();
            row.append("errors").append(DELIMITER).append(escapeCsv(errorCode)); // Module name for errors
            for (String lang : exportLangs) {
                String translation = langMap.getOrDefault(lang, "");
                row.append(DELIMITER).append(escapeCsv(translation));
            }
            writer.write(row.toString());
            writer.write("\n");
        }
    }

    private List<Locale> filterLocales(List<Locale> availableLocales, String requestedLanguage) {
        if (requestedLanguage == null || requestedLanguage.trim().isEmpty()) {
            return availableLocales;
        }

        String langCode = requestedLanguage.trim().toLowerCase();
        return availableLocales.stream()
            .filter(locale -> langCode.equalsIgnoreCase(locale.getLanguage()))
            .collect(Collectors.toList());
    }

    private Map<String, Map<String, Map<Locale, String>>> groupFrontendTranslations(List<I18NTranslationDto> translations) {
        Map<String, Map<String, Map<Locale, String>>> result = new TreeMap<>();

        for (I18NTranslationDto translation : translations) {
            I18NModule module = i18NService.findI18NModuleById(translation.getI18NModuleId());
            String moduleName = module != null ? module.getName() : "unknown";
            String code = translation.getCode();
            Locale locale = translation.getLocale();
            String value = translation.getNewValue() != null ? translation.getNewValue() : translation.getDefaultValue();

            result.computeIfAbsent(moduleName, k -> new TreeMap<>())
                  .computeIfAbsent(code, k -> new HashMap<>())
                  .put(locale, value);
        }

        return result;
    }

    private int importFrontendTranslations(InputStream inputStream, String requestedLanguage, List<ImportError> errors) {
        int totalImported = 0;
        int lineNumber = 0;

        try (LineIterator it = org.apache.commons.io.IOUtils.lineIterator(inputStream, "UTF-8")) {
            if (!it.hasNext()) {
                errors.add(ImportError.builder()
                    .lineNumber(0)
                    .errorMessage("CSV file is empty")
                    .build());
                return 0;
            }

            // Parse header
            lineNumber++;
            String headerLine = it.nextLine();
            String[] headers = StringUtils.splitPreserveAllTokens(headerLine, DELIMITER);

            if (headers.length < 2 || !"module".equalsIgnoreCase(headers[0].trim()) || !"key".equalsIgnoreCase(headers[1].trim())) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("CSV must start with 'module' and 'key' columns")
                    .build());
                return 0;
            }

            // Identify language columns
            List<String> languageColumns = new ArrayList<>();
            Map<String, Integer> langColumnIndex = new HashMap<>();

            for (int i = 2; i < headers.length; i++) {
                String langCode = headers[i].trim().toLowerCase();
                if (VALID_LANGUAGE_CODES.contains(langCode)) {
                    // Filter by requested language if specified
                    if (requestedLanguage == null || requestedLanguage.trim().isEmpty() ||
                        requestedLanguage.trim().equalsIgnoreCase(langCode)) {
                        languageColumns.add(langCode);
                        langColumnIndex.put(langCode, i);
                    }
                }
            }

            // Process data lines
            while (it.hasNext()) {
                lineNumber++;
                String line = it.nextLine().trim();
                if (line.isEmpty()) continue;

                String[] row = StringUtils.splitPreserveAllTokens(line, DELIMITER);

                try {
                    if (row.length < 2) {
                        errors.add(ImportError.builder()
                            .lineNumber(lineNumber)
                            .errorMessage("Invalid row format")
                            .build());
                        continue;
                    }

                    String moduleName = row[0].trim();
                    String key = row[1].trim();

                    if (StringUtils.isBlank(moduleName) || StringUtils.isBlank(key)) {
                        errors.add(ImportError.builder()
                            .lineNumber(lineNumber)
                            .errorMessage("Missing module or key")
                            .build());
                        continue;
                    }

                    // Find the module
                    Optional<I18NModule> moduleOpt = i18NService.findI18NModuleByName(moduleName);
                    if (!moduleOpt.isPresent()) {
                        errors.add(ImportError.builder()
                            .lineNumber(lineNumber)
                            .errorMessage("Unknown module: " + moduleName)
                            .build());
                        continue;
                    }

                    // Process each language column
                    for (String langCode : languageColumns) {
                        // Skip protected languages (fr, en)
                        if (PROTECTED_LANGUAGES.contains(langCode)) {
                            continue;
                        }

                        Integer columnIndex = langColumnIndex.get(langCode);
                        if (columnIndex >= row.length) {
                            continue; // Column not present in this row
                        }

                        String value = row[columnIndex].trim();
                        if (StringUtils.isBlank(value)) {
                            continue; // Skip empty values
                        }

                        // Check if translation key exists
                        Query query = new Query(new AndClause(
                            Clauses.equal("locale", langCode),
                            Clauses.equal("code", key)
                        ));
                        List<I18NTranslation> existingTranslations = i18nTranslationDAO.search(query);

                        if (existingTranslations.isEmpty()) {
                            // Check if the key exists in fr or en (required for creating new translations)
                            Query frQuery = new Query(new AndClause(
                                Clauses.equal("locale", "fr"),
                                Clauses.equal("code", key)
                            ));
                            Query enQuery = new Query(new AndClause(
                                Clauses.equal("locale", "en"),
                                Clauses.equal("code", key)
                            ));

                            List<I18NTranslation> frTranslations = i18nTranslationDAO.search(frQuery);
                            List<I18NTranslation> enTranslations = i18nTranslationDAO.search(enQuery);

                            if (frTranslations.isEmpty() && enTranslations.isEmpty()) {
                                errors.add(ImportError.builder()
                                    .lineNumber(lineNumber)
                                    .errorMessage("Translation key '" + key + "' does not exist in default languages (fr/en)")
                                    .build());
                                continue;
                            }

                            // Create new translation
                            I18NTranslation newTranslation = new I18NTranslation();
                            newTranslation.setI18NModule(moduleOpt.get());
                            newTranslation.setCode(key);
                            newTranslation.setLocale(new Locale(langCode));
                            newTranslation.setNewValue(value);
                            newTranslation.setNewValueChangedAt(new Date());

                            i18nTranslationDAO.store(newTranslation);
                            totalImported++;
                        } else {
                            // Update existing translation
                            I18NTranslation existingTranslation = existingTranslations.get(0);
                            existingTranslation.setNewValue(value);
                            existingTranslation.setNewValueChangedAt(new Date());

                            i18nTranslationDAO.merge(existingTranslation);
                            totalImported++;
                        }
                    }

                } catch (Exception e) {
                    log.error("Error processing line {}: {}", lineNumber, e.getMessage());
                    errors.add(ImportError.builder()
                        .lineNumber(lineNumber)
                        .errorMessage("Error processing line: " + e.getMessage())
                        .build());
                }
            }
        } catch (IOException e) {
            log.error("Error reading CSV file", e);
            errors.add(ImportError.builder()
                .lineNumber(lineNumber)
                .errorMessage("Error reading CSV file: " + e.getMessage())
                .build());
        }

        return totalImported;
    }

    private int importErrorTranslations(InputStream inputStream, String requestedLanguage, List<ImportError> errors) {
        int totalImported = 0;
        int lineNumber = 0;

        try (LineIterator it = org.apache.commons.io.IOUtils.lineIterator(inputStream, "UTF-8")) {
            if (!it.hasNext()) {
                errors.add(ImportError.builder()
                    .lineNumber(0)
                    .errorMessage("CSV file is empty")
                    .build());
                return 0;
            }

            // Parse header
            lineNumber++;
            String headerLine = it.nextLine();
            String[] headers = StringUtils.splitPreserveAllTokens(headerLine, DELIMITER);

            if (headers.length < 2 || !"module".equalsIgnoreCase(headers[0].trim()) || !"key".equalsIgnoreCase(headers[1].trim())) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("CSV must start with 'module' and 'key' columns")
                    .build());
                return 0;
            }

            // Identify language columns
            List<String> languageColumns = new ArrayList<>();
            Map<String, Integer> langColumnIndex = new HashMap<>();

            for (int i = 2; i < headers.length; i++) {
                String langCode = headers[i].trim().toLowerCase();
                if (VALID_LANGUAGE_CODES.contains(langCode)) {
                    // Filter by requested language if specified
                    if (requestedLanguage == null || requestedLanguage.trim().isEmpty() ||
                        requestedLanguage.trim().equalsIgnoreCase(langCode)) {
                        languageColumns.add(langCode);
                        langColumnIndex.put(langCode, i);
                    }
                }
            }

            // Process data lines
            while (it.hasNext()) {
                lineNumber++;
                String line = it.nextLine().trim();
                if (line.isEmpty()) continue;

                String[] row = StringUtils.splitPreserveAllTokens(line, DELIMITER);

                try {
                    if (row.length < 2) {
                        errors.add(ImportError.builder()
                            .lineNumber(lineNumber)
                            .errorMessage("Invalid row format")
                            .build());
                        continue;
                    }

                    String key = row[1].trim();

                    if (StringUtils.isBlank(key)) {
                        errors.add(ImportError.builder()
                            .lineNumber(lineNumber)
                            .errorMessage("Missing key")
                            .build());
                        continue;
                    }

                    // Process each language column
                    for (String langCode : languageColumns) {
                        // Skip protected languages (fr, en)
                        if (PROTECTED_LANGUAGES.contains(langCode)) {
                            continue;
                        }

                        Integer columnIndex = langColumnIndex.get(langCode);
                        if (columnIndex >= row.length) {
                            continue; // Column not present in this row
                        }

                        String value = row[columnIndex].trim();
                        if (StringUtils.isBlank(value)) {
                            continue; // Skip empty values
                        }

                        // Check if error key exists in fr or en
                        DocumentErrorTranslation referenceTranslation =
                            documentErrorTranslationService.getErrorTranslationById(
                                new DocumentErrorTranslationId(key, "fr"));

                        if (referenceTranslation == null) {
                            referenceTranslation = documentErrorTranslationService.getErrorTranslationById(
                                new DocumentErrorTranslationId(key, "en"));
                        }

                        if (referenceTranslation == null) {
                            errors.add(ImportError.builder()
                                .lineNumber(lineNumber)
                                .errorMessage("Unknown error key: " + key)
                                .build());
                            continue;
                        }

                        // Create or update error translation
                        DocumentErrorTranslation errorTranslation = new DocumentErrorTranslation(
                            key,
                            langCode,
                            value,
                            referenceTranslation.isStandardError()
                        );

                        documentErrorTranslationService.saveErrorTranslation(errorTranslation);
                        totalImported++;
                    }

                } catch (Exception e) {
                    log.error("Error processing line {}: {}", lineNumber, e.getMessage());
                    errors.add(ImportError.builder()
                        .lineNumber(lineNumber)
                        .errorMessage("Error processing line: " + e.getMessage())
                        .build());
                }
            }
        } catch (IOException e) {
            log.error("Error reading CSV file", e);
            errors.add(ImportError.builder()
                .lineNumber(lineNumber)
                .errorMessage("Error reading CSV file: " + e.getMessage())
                .build());
        }

        return totalImported;
    }

    /**
     * Escapes a string value for safe inclusion in a CSV field using semicolon (;) as a delimiter.
     */
    private String escapeCsv(String value) {
        if (value == null) {
            return "";
        }
        if (value.contains(DELIMITER) || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            value = value.replace("\"", "\"\"");
            return "\"" + value + "\"";
        }
        return value;
    }
}
