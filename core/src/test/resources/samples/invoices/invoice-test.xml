<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <InvoiceHeader>
        <InvoiceNumber>954-33-all-the-way</InvoiceNumber>
		<InvoiceIssueDate>2023-04-23T00:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>ORD_REC_TEST</core:BuyerOrderNumber>
				<core:SellerOrderNumber>999</core:SellerOrderNumber>
                <core:PurchaseOrderDate>2021-03-08T00:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>		
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>fr</core:LanguageCoded>
        </InvoiceLanguage>
        <InvoiceDates>
            <InvoiceDueDate>2022-10-25T00:00:00</InvoiceDueDate>
            <ActualShipDate>2022-10-10T00:00:00</ActualShipDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2023-07-21T14:55:10</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>DocumentReceivedDateTime</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
                <core:DateCoded>
                    <core:Date>2023-04-01T11:01:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Ident>006</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>CARREFOUR</core:Name1>
                    <core:Street>rue de chateaudun</core:Street>
                    <core:City>PARIS</core:City>
                </core:NameAddress>
				<core:PrimaryContact>
                    <core:ContactName>Contact 1 GCI</core:ContactName>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>0766518107</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                         <!--   <core:ContactNumberValue><EMAIL></core:ContactNumberValue> -->
							<core:ContactNumberValue><EMAIL></core:ContactNumberValue> 
						<!--	<core:ContactNumberValue><EMAIL></core:ContactNumberValue> -->
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Ident>002</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>SAS</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>LegalCapital</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>1496000 EUR</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>RCS BORDEAUX B775583248</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>775583248</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>BARILLA</core:Name1>
                    <core:Street>ZA LA HOUSSOYE AV INDUSTRIELLE</core:Street>
                    <core:PostalCode>59930</core:PostalCode>
                    <core:City>LA CHAPELLE ARMENTIERES</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
				<core:PrimaryContact>
                    <core:ContactName>Seller Contact</core:ContactName>
                    <core:ListOfContactNumber>
					      <core:ContactNumber>
                            <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue>0722345678</core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR86775583248</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>TEST</core:RegisteredName>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty>
                <core:PartyID>
                    <core:Ident>PART1FR</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>GENERIX EPERNAY</core:Name1>
                    <core:Street>13 QUAI DE MARNE</core:Street>
                    <core:PostalCode>51200</core:PostalCode>
                    <core:City>EPERNAY</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
				<core:PrimaryContact>
                    <core:ContactName>ShipTo Contact</core:ContactName>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue></core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
            </ShipToParty>
            <BillToParty>
                <core:PartyID>
                    <core:Ident>PART1FR</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>451321335</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>CARREFOUR</core:Name1>
                    <core:Street>013 Rue Du Picardie</core:Street>
                    <core:PostalCode>PC242940</core:PostalCode>
                    <core:City>EPERNAY</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
				 <core:PrimaryContact>
                    <core:ContactName>BillTo Contact</core:ContactName>
                    <core:ListOfContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue></core:ContactNumberValue>
                            <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                            <core:ContactNumberValue/>
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR90451321335</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>  
           <ListOfPartyCoded>
            <core:PartyCoded>
               <core:PartyID>
                  <core:Ident>BUYER_AG</core:Ident>
               </core:PartyID>
               <core:ListOfIdentifier>
                  <core:Identifier>
                     <core:Agency>
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                        <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                     </core:Agency>
                     <core:Ident>RN43525</core:Ident>
                  </core:Identifier>
                  <core:Identifier>
                     <core:Agency>
                        <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                     </core:Agency>
                     <core:Ident>12345678901234</core:Ident>
                  </core:Identifier>
               </core:ListOfIdentifier>
               <core:NameAddress>
                  <core:Name1>Aaa</core:Name1>
                  <core:Street>Adada</core:Street>
                  <core:StreetSupplement1>LALOOSE</core:StreetSupplement1>
                  <core:PostalCode>4242</core:PostalCode>
                  <core:City>Fada</core:City>
                  <core:Country>
                     <core:CountryCoded>GB</core:CountryCoded>
                  </core:Country>
               </core:NameAddress>
               <core:PrimaryContact>
                  <core:ContactName>GISCustomer</core:ContactName>
                  <core:ListOfContactNumber>
                     <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                     </core:ContactNumber>
                     <core:ContactNumber>
                        <core:ContactNumberValue>+3858275295131</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                     </core:ContactNumber>
                  </core:ListOfContactNumber>
               </core:PrimaryContact>
               <core:OtherContacts>
                  <core:Contact>
                     <core:ContactName>GISCustomer</core:ContactName>
                     <core:ListOfContactNumber>
                        <core:ContactNumber>
                           <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                           <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                           <core:ContactNumberValue>+3858275295131</core:ContactNumberValue>
                           <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                     </core:ListOfContactNumber>
                  </core:Contact>
               </core:OtherContacts>
               <core:PartyTaxInformation>
                  <core:TaxIdentifier>
                     <core:Agency>
                        <core:AgencyCoded>CEC</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                     </core:Agency>
                     <core:Ident>Fr2432525</core:Ident>
                  </core:TaxIdentifier>
               </core:PartyTaxInformation>
               <core:PartyRoleCoded>BuyersAgentOrRepresentative</core:PartyRoleCoded>
            </core:PartyCoded>
            <core:PartyCoded>
               <core:PartyID>
                  <core:Ident>PAY_TO</core:Ident>
               </core:PartyID>
               <core:ListOfIdentifier>
                  <core:Identifier>
                     <core:Agency>
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                        <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                     </core:Agency>
                     <core:Ident>RN4455</core:Ident>
                  </core:Identifier>
                  <core:Identifier>
                     <core:Agency>
                        <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                     </core:Agency>
                     <core:Ident>12345678901234</core:Ident>
                  </core:Identifier>
               </core:ListOfIdentifier>
               <core:NameAddress>
                  <core:Name1>TOTO</core:Name1>
                  <core:Street>RUE DU PICARDIE</core:Street>
                  <core:StreetSupplement1>Assss</core:StreetSupplement1>
                  <core:PostalCode>131414</core:PostalCode>
                  <core:City>LANCASHIRE</core:City>
                  <core:Country>
                     <core:CountryCoded>GB</core:CountryCoded>
                  </core:Country>
               </core:NameAddress>
               <core:PrimaryContact>
                  <core:ContactName>Aaa</core:ContactName>
                  <core:ListOfContactNumber>
                     <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                     </core:ContactNumber>
                     <core:ContactNumber>
                        <core:ContactNumberValue>+35875829529582</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                     </core:ContactNumber>
                  </core:ListOfContactNumber>
               </core:PrimaryContact>
               <core:OtherContacts>
                  <core:Contact>
                     <core:ContactName>Aaa</core:ContactName>
                     <core:ListOfContactNumber>
                        <core:ContactNumber>
                           <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                           <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                           <core:ContactNumberValue>+35875829529582</core:ContactNumberValue>
                           <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                     </core:ListOfContactNumber>
                  </core:Contact>
               </core:OtherContacts>
               <core:PartyTaxInformation>
                  <core:TaxIdentifier>
                     <core:Agency>
                        <core:AgencyCoded>CEC</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                     </core:Agency>
                     <core:Ident>FR1324242</core:Ident>
                  </core:TaxIdentifier>
               </core:PartyTaxInformation>
               <core:PartyRoleCoded>Payer</core:PartyRoleCoded>
            </core:PartyCoded>
            <core:PartyCoded>
               <core:PartyID>
                  <core:Ident>SELLER_AG</core:Ident>
               </core:PartyID>
               <core:ListOfIdentifier>
                  <core:Identifier>
                     <core:Agency>
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                        <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                     </core:Agency>
                     <core:Ident>RN43525421</core:Ident>
                  </core:Identifier>
                  <core:Identifier>
                     <core:Agency>
                        <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                        <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                     </core:Agency>
                     <core:Ident>12345678901234</core:Ident>
                  </core:Identifier>
               </core:ListOfIdentifier>
               <core:NameAddress>
                  <core:Name1>TOTO</core:Name1>
                  <core:Street>RUE DU PICARDIE</core:Street>
                  <core:StreetSupplement1>Assss</core:StreetSupplement1>
                  <core:PostalCode>131414</core:PostalCode>
                  <core:City>LANCASHIRE</core:City>
                  <core:Country>
                     <core:CountryCoded>GB</core:CountryCoded>
                  </core:Country>
               </core:NameAddress>
               <core:PrimaryContact>
                  <core:ContactName>LOR-006</core:ContactName>
                  <core:ListOfContactNumber>
                     <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                     </core:ContactNumber>
                     <core:ContactNumber>
                        <core:ContactNumberValue>+358275719519531</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                     </core:ContactNumber>
                  </core:ListOfContactNumber>
               </core:PrimaryContact>
               <core:OtherContacts>
                  <core:Contact>
                     <core:ContactName>LOR-006</core:ContactName>
                     <core:ListOfContactNumber>
                        <core:ContactNumber>
                           <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                           <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                        <core:ContactNumber>
                           <core:ContactNumberValue>+358275719519531</core:ContactNumberValue>
                           <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                        </core:ContactNumber>
                     </core:ListOfContactNumber>
                  </core:Contact>
               </core:OtherContacts>
               <core:PartyTaxInformation>
                  <core:TaxIdentifier>
                     <core:Agency>
                        <core:AgencyCoded>CEC</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                     </core:Agency>
                     <core:Ident>FR1324242</core:Ident>
                  </core:TaxIdentifier>
               </core:PartyTaxInformation>
               <core:PartyRoleCoded>SellersAgentOrRepresentative</core:PartyRoleCoded>
            </core:PartyCoded>
         </ListOfPartyCoded>			
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>.</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>CONFORMEMENT A LA LOI 92-1442 DU 31/12/92 RELATIVE AUX DELAIS DEPAIEMENT ENTRE LES ENTREPRISES,TOUT RETARD DE PAIEMENT TOTAL OUPARTIEL A L'ECHEANCE INDIQUEE SUR LA FACTURE ENTRAINERA L'APPLICATIONDE PENALITES ET CE,APRES UNE MISE EN DEMEURE RESTEE INFRUCTUEUSE. CESPENALITES SERONT FACTUREES A UN TAUX EGAL A TROIS FOIS LE TAUX D'INTERET LEGAL. EN CAS DE CHANGEMENT DE TAUX,C'EST LA DATE DEPUBLICATION DU NOUVEAU TAUX AU JO QUI DETERMINE SA DATE D'APPLICATIONPOUR TOUT PAIEMENT APRES LA DATE D'ECHEANCE UNE INDEMNITE FORFAITAIREDE 40 EUROS SERA APPLIQUEE</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>0</core:PaymentTermDescription>
                </core:PaymentTerm>
            </core:PaymentTerms>
            <core:PaymentMethod>
                <core:PaymentMeanCoded>PaymentToBankAccount</core:PaymentMeanCoded>
            </core:PaymentMethod>
        </InvoicePaymentInstructions>
        <ListOfStructuredNote>
            <core:StructuredNote>
                <core:GeneralNote>NUM TELE EXPEDITEUR : **********. NUM FAX EXPEDITEUR : 0320189019NUMERO INTERNE DONNEUR D ORDRE : **********</core:GeneralNote>
                <core:TextTypeCoded>Other</core:TextTypeCoded>
                <core:TextTypeCodedOther>SupplierRemarks</core:TextTypeCodedOther>
            </core:StructuredNote>
        </ListOfStructuredNote>
        <ListOfNameValueSet>
            <core:NameValueSet>
                <core:SetName>earlyPayment</core:SetName>
                <core:ListOfNameValuePair>
                    <core:NameValuePair>
                        <core:Name>paymentStatus</core:Name>
                        <core:Value>EP_ELIGIBLE</core:Value>
                    </core:NameValuePair>
                </core:ListOfNameValuePair>
            </core:NameValueSet>
            <core:NameValueSet>
                <core:SetName>FreeText</core:SetName>
                <core:ListOfNameValuePair>
                    <core:NameValuePair>
                        <core:Name>ID_GNX</core:Name>
                        <core:Value>63645</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText01</core:Name>
                        <core:Value>NO_ROUTE</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText10</core:Name>
                        <core:Value>RECEPTION_FOURNISSEUR</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText11</core:Name>
                        <core:Value>EDI</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText03</core:Name>
                        <core:Value>12879</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>FreeText05</core:Name>
                        <core:Value>BOUBEE BELLEVILLE.</core:Value>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>ID_FACTURE</core:Name>
                        <core:Value>000000000249314</core:Value>
                    </core:NameValuePair>
                </core:ListOfNameValuePair>
            </core:NameValueSet>
        </ListOfNameValueSet>
	     <CountrySpecificRequirements>
            <NotaFiscalType>NotaFiscalType</NotaFiscalType>
            <PaymentReferenceNumber>PaymentReferenceNumber</PaymentReferenceNumber>
            <OtherCountryRequirements>
                <CountryRequirement>
                    <Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                        <core:CountryCodedOther>CountryCodedOther</core:CountryCodedOther>
                    </Country>
                    <ListOfSpecificRequirement>
                        <SpecificRequirement>
                            <RequirementTypeCoded>BillingType</RequirementTypeCoded>
                            <RequirementTypeCodedOther>RequirementTypeCodedOther</RequirementTypeCodedOther>
                            <RequirementReference>
                                <core:RefNum>RefNum</core:RefNum>
                                <core:RefDate>2003-01-01T00:01:03</core:RefDate>
                            </RequirementReference>
                            <RequirementDetails>Aaa</RequirementDetails>
                        </SpecificRequirement>
                    </ListOfSpecificRequirement>
                </CountryRequirement>
            </OtherCountryRequirements>
        </CountrySpecificRequirements>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
						    <core:BuyerPartNumber>
                              <core:PartID>buyerpartnumber1</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>eanproduct1</core:ProductIdentifier>
                            </core:StandardPartNumber>                            
                        </core:PartNumbers>
                        <core:ItemDescription>description_product1</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>5</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>                   
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>                     
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>15</core:UnitPriceValue>
                            </core:UnitPrice>                           
                        </core:Price>
						   <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>20</core:UnitPriceValue>
                            </core:UnitPrice>                          
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>20.00</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                     <core:LineItemSubTotal>
                        <core:MonetaryAmount>10</core:MonetaryAmount>
                    </core:LineItemSubTotal>					
                    <core:LineItemTotal>
                        <core:MonetaryAmount>30</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
  <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>2</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>2</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
					    <core:BuyerPartNumber>
                                <core:PartID>buyerpartnumber2</core:PartID>
                            </core:BuyerPartNumber>
						    <core:SellerPartNumber>
                                <core:PartID>sellerpartnumber2</core:PartID>
                            </core:SellerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>eanproductA2</core:ProductIdentifier>
                            </core:StandardPartNumber>                            
                        </core:PartNumbers>
                        <core:ItemDescription>description_product2</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>10</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>                   
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>                     
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>10</core:UnitPriceValue>
                            </core:UnitPrice>                           
                        </core:Price>
						   <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>18</core:UnitPriceValue>
                            </core:UnitPrice>                          
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>20.00</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>     
                     <core:LineItemSubTotal>
                        <core:MonetaryAmount>10</core:MonetaryAmount>
                    </core:LineItemSubTotal>					
                    <core:LineItemTotal>
                        <core:MonetaryAmount>24</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>   
<InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>3</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>3</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
							<core:SellerPartNumber>
                                <core:PartID>sellerpartnumber3</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>buyerpartnumber3</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>eanproduct3</core:ProductIdentifier>
                            </core:StandardPartNumber>                            
                        </core:PartNumbers>
                        <core:ItemDescription>description_product3</core:ItemDescription>
                    </ItemIdentifiers>   
	<InvoicedQuantity>
                        <core:QuantityValue>10</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>            					
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>                     
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>15</core:UnitPriceValue>
                            </core:UnitPrice>                           
                        </core:Price>
						   <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>17</core:UnitPriceValue>
                            </core:UnitPrice>                          
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>20.00</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>                   
                    <core:LineItemTotal>
                        <core:MonetaryAmount>27</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>4</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>4</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
							<core:SellerPartNumber>
                                <core:PartID>sellerpartnumber4</core:PartID>
                            </core:SellerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Bar-CodedSerialNumber</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>eanproduct4</core:ProductIdentifier>
                            </core:StandardPartNumber>                            
                        </core:PartNumbers>
                        <core:ItemDescription>description_product4</core:ItemDescription>
                    </ItemIdentifiers>  
<InvoicedQuantity>
                        <core:QuantityValue>10</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>            					
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>                     
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>15</core:UnitPriceValue>
                            </core:UnitPrice>                           
                        </core:Price>
						   <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>17</core:UnitPriceValue>
                            </core:UnitPrice>                          
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>20.00</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>                   
                    <core:LineItemTotal>
                        <core:MonetaryAmount>27</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail> 							
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>74</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>60</core:MonetaryAmount>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>63</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>0</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>      
    </InvoiceSummary>
</Invoice>
