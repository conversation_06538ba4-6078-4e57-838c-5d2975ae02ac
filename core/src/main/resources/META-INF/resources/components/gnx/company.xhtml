<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite"
	xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
	xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx">
	<!-- INTERFACE -->
	<cc:interface name="company">
		<cc:attribute name="instance" type="com.byzaneo.generix.bean.Instance"
			required="false" />
		<cc:facet name="header" />
	</cc:interface>

	<!-- IMPLEMENTATION -->
	<cc:implementation>
	
		 <style type="text/css">
			.selectonemenu{
				min-width: 0px !important;
			}
        </style>
	
		<p:tabView id="cpyTabs" dynamic="true" cache="false"
			style="min-height:600px;">
			<p:ajax event="tabChange" immediate="true"
				listener="#{gnxCompanyHandler.onTabChange}" process="@none"
				update="@none" />
			<!-- PARTNERS -->
			<p:tab id="cPartnerTab">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-users" /> <h:outputText
								style="margin-left: 3px;" value="#{labels.partners}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cPartnerPanel" styleClass="psCPartnerPanel">
					<!-- partners' groups -->
					<p:dataTable id="cPartnerTable" var="partner"
						value="#{gnxCompanyHandler.partners}" rows="15" lazy="true"
						paginator="true" paginatorPosition="bottom"
						paginatorAlwaysVisible="true" sortBy="#{partner.name}"
						sortOrder="ascending"
						paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
						rowsPerPageTemplate="15,50,100,200,500"
						rowStyleClass="#{partner.isDisabled() ? 'disabled' : ''}"
						rendered="#{gnxCompanyHandler.logs==null and gnxCompanyHandler.partner==null}"
						widgetVar="wCpyPartnerTable" styleClass="datatable-hide-filters"
						emptyMessage="#{labels.no_records_found}">
						<cc:renderFacet name="header" />
						<f:facet name="header">
							<h:panelGrid columnClasses="right" width="100%">
								<h:panelGroup>
									<p:outputPanel styleClass="label label-info mrs" style="font-size:100% !important;"
										rendered="#{gnxCompanyHandler.importProcessAlive}">
										<i class="fa fa-hourglass-half fa-spin"></i>
										<span class="mls">#{gnxuilbls.ref_man_loading}</span>
									</p:outputPanel>
									<p:inputText id="globalFilter"
										onkeyup="PF('wCpyPartnerTable').filter()" style="width:150px" />
									<p:watermark for="globalFilter" value="#{labels.search}" />
									<p:defaultCommand target="globalFilter" />
									<p:commandButton icon="fa fa-upload" value="#{labels.export}"
										styleClass="mls"
										rendered="#{gnxSessionHandler.isGranted('General', 'EXECUTE')}"
										actionListener="#{gnxCompanyHandler.onExportAsExel()}"
										oncomplete="if (!args.validationFailed){jQuery('.jqsFile').click()}"/>
									<p:commandLink ajax="false" styleClass="jqsFile"
										style="visibility:hidden;">
										<p:fileDownload value="#{gnxCompanyHandler.downloadFileStream}" />
									</p:commandLink>
									<p:commandButton icon="fa fa-download" value="#{labels.import}"
										type="button" onclick="PF('wCPartnerUpDlg').show()"
										styleClass="mls"
										rendered="#{gnxSessionHandler.isGranted('General', 'EXECUTE')}"
										disabled="#{gnxCompanyHandler.inProgress or gnxCompanyHandler.importProcessAlive}" />
									<!-- back: Add popup dialog -->
									<p:commandButton icon="ui-icon-plusthick" value="#{labels.add}"
										actionListener="#{gnxCompanyHandler.onAddPartnerDialog}"
										process="@this" update="@none" styleClass="mls"
										disabled="#{gnxCompanyHandler.inProgress or gnxCompanyHandler.importProcessAlive}"
										rendered="#{gnxSessionHandler.backOfficeUser and gnxSessionHandler.isGranted('General', 'CREATE') }">
										<p:ajax event="dialogReturn"
											listener="#{gnxCompanyHandler.onSavedPartner}"
											process="@this" update="-messages @(.psCPartnerPanel)" />
									</p:commandButton>
									<!-- front: full screen - redirection to upsertpartner -->
									<p:button icon="ui-icon-plusthick" value="#{labels.add}"
										styleClass="mls" outcome="/ui/upsertpartner"
										disabled="#{gnxCompanyHandler.inProgress or gnxCompanyHandler.importProcessAlive}"
										rendered="#{not gnxSessionHandler.backOfficeUser and gnxSessionHandler.isGranted('General', 'CREATE') }">
										<f:param name="pid" value="new" />
										<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
										<f:param name="helpFragment"
											value="utilisateur_societe_ajouter_partenaire" />
									</p:button>
									<p:commandButton icon="ui-icon-mail-closed"
										value="#{labels.regenerate_all}"
										title="#{labels.regenerate_all_password}" type="button"
										onclick="PF('wCPartnerMailDlg').show()" styleClass="mls"
										rendered="#{gnxCompanyHandler.instance!=null and gnxSessionHandler.isGranted('Users', 'EXECUTE')}}" />
								</h:panelGroup>
							</h:panelGrid>
						</f:facet>
						<p:column style="width:30px;" styleClass="center">
							<p:rowToggler />
						</p:column>
						<p:column headerText="#{labels.organization_name}"
							sortBy="#{partner.name}" filterBy="#{partner.name}"
							styleClass="bold" style="width:10%;">
							<h:outputText value="#{partner.name}" />
						</p:column>
						<p:column headerText="#{labels.organization_code}"
							sortBy="#{partner.code}" filterBy="#{partner.code}"
							style="width:10%;" styleClass="center">
							<h:outputText value="#{partner.code}" />
						</p:column>
						<p:column headerText="#{labels.organization_fullname}"
							sortBy="#{partner.fullname}" filterBy="#{partner.fullname}"
							style="width:40%;">
							<h:outputText value="#{partner.fullname}" />
						</p:column>
						<p:column headerText="#{labels.organization_address_city}"
							sortBy="#{partner.location.address.city}" styleClass="nowrap">
							<h:outputText value="#{partner.location.address.city}" />
						</p:column>
						<p:column headerText="#{labels.organization_role}"
								  filterBy="#{gnxCompanyHandler.getPartnerDefaultRole(partner)}">
							<p:dataList value="#{gnxCompanyHandler.getPartnerDefaultRole(partner)}"
										var="prole" emptyMessage=""
										styleClass="commaList">
										<h:outputText value="#{prole.fullname}" title="#{prole.name}" />
							</p:dataList>
						</p:column>
						<p:column headerText="#{labels.creation}"
							sortBy="#{partner.creation}" styleClass="center">
							<h:outputText value="#{partner.creation}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}"
									type="date" dateStyle="medium"
									timeZone="#{secSessionHandler.timeZone}" />
							</h:outputText>
						</p:column>
						<p:column headerText="#{labels.modification}"
							sortBy="#{partner.modification}" styleClass="center">
							<h:outputText value="#{partner.modification}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}"
									type="date" dateStyle="medium"
									timeZone="#{secSessionHandler.timeZone}" />
							</h:outputText>
						</p:column>
						<p:column styleClass="nowrap right" style="width:200px;">
							<!-- back: Add popup dialog -->
							<p:commandButton id="role_openDialogButtonId" icon="fa fa-list-ul" title="#{labels.role}"
										process="@this" update="@none"
										actionListener="#{gnxCompanyHandler.onOpenRole(partner, 'partenaire')}"
										rendered="#{gnxSessionHandler.isGranted('Users', 'UPDATE')}">
										<p:ajax event="dialogReturn"
											listener="#{gnxCompanyHandler.onSavedRolePartner}" process="@this"
											update="-messages @(.psCPartnerPanel)" />
							</p:commandButton>
							<p:commandButton icon="ui-icon-plusthick"
								title="#{labels.add} #{labels.user}"
								actionListener="#{gnxCompanyHandler.onAddUser('contacts_partenaire', partner)}"
								immediate="true" process="@this" update="@none"
								rendered="#{gnxSessionHandler.backOfficeUser and gnxSessionHandler.isGranted('Users', 'CREATE')}">
								<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
									update="-messages @(.psCPartnerPanel)" />
							</p:commandButton>
							<!-- front: full screen - redirection to upsertuser -->
							<p:button icon="ui-icon-plusthick"
								title="#{labels.add} #{labels.user}" outcome="/ui/upsertuser"
								rendered="#{not gnxSessionHandler.backOfficeUser and gnxSessionHandler.isGranted('Users', 'CREATE')}">
								<f:param name="uid" value="new" />
								<f:param name="upid" value="#{partner.id}" />
								<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
								<f:param name="helpFragment" value="contacts_partenaire" />
								<f:param name="icode" value="#{gnxCompanyHandler.instance.code}" />
							</p:button>
							<!-- back: Add popup dialog -->
							<p:commandButton icon="ui-icon-pencil"
								title="#{labels.button_edit}"
								actionListener="#{gnxCompanyHandler.onEditPartnerDialog(partner)}"
								immediate="true" process="@this" update="@none"
								rendered="#{gnxSessionHandler.backOfficeUser and (gnxSessionHandler.isGranted('General', 'READ') or gnxSessionHandler.isGranted('General', 'UPDATE'))}">
								<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedPartner}" process="@this"
									update="-messages @(.psCPartnerPanel)" />
							</p:commandButton>
							<!-- front: full screen - redirection to upsertpartner -->
							<p:button icon="ui-icon-pencil" title="#{labels.button_edit}"
								outcome="/ui/upsertpartner"
								rendered="#{not gnxSessionHandler.backOfficeUser and (gnxSessionHandler.isGranted('General', 'READ') or gnxSessionHandler.isGranted('General', 'UPDATE'))}">
								<f:param name="pid" value="#{partner.id}" />
								<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
								<f:param name="helpFragment"
									value="utilisateur_societe_editer_partenaire" />
							</p:button>
							<p:commandButton icon="fa fa-database" aria-hidden="true"
								style="color:#3C82B4" title="#{labels.history}"
								actionListener="#{gnxCompanyHandler.onDisplayPartnerRevisions(partner)}"
								rendered="#{gnxSessionHandler.isCompanyUser() and gnxSessionHandler.isGranted('History', 'READ')}">
								<p:ajax event="dialogReturn"
									update="-messages @(.psCPartnerPanel)" />
							</p:commandButton>
							<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}"
								onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
								actionListener="#{gnxCompanyHandler.onRemovePartner(partner)}"
								process="@this" update="-messages @(.psCPartnerPanel)"
								rendered="#{gnxSessionHandler.isGranted('General', 'DELETE')}" />
						</p:column>
						<p:rowExpansion>
							<p:dataTable id="cUsersTable"
								value="#{gnxCompanyHandler.getUsers(partner, false)}" var="user"
								rows="50" paginator="true" paginatorPosition="bottom"
								paginatorAlwaysVisible="false"
								rowStyleClass="#{user.disabled ? 'disabled' : ''}"
								sortBy="#{user.login}" sortOrder="ascending"
								emptyMessage="#{labels.no_records_found}">
								<p:column headerText="#{labels.login}" styleClass="bold">
									<h:outputText value="#{user.login}" />
								</p:column>
								<p:column headerText="#{seclbls.firstname}">
									<h:outputText value="#{user.firstname}" />
								</p:column>
								<p:column headerText="#{seclbls.lastname}">
									<h:outputText value="#{user.lastname}" />
								</p:column>
								<p:column headerText="#{seclbls.email}" style="width:15%;">
									<h:outputText value="#{user.email}" />
								</p:column>
								<p:column headerText="#{labels.roles}"
									filterBy="#{gnxCompanyHandler.getUserRoles(user)}">
									<p:dataList value="#{gnxCompanyHandler.getUserRoles(user)}"
										var="urole" styleClass="commaList" emptyMessage="">
										<h:outputText value="#{urole.fullname}" title="#{urole.name}" />
									</p:dataList>
								</p:column>
								<p:column headerText="#{labels.number_connexion}"
									styleClass="center">
									<h:outputText value="#{user.loginCount}" />
								</p:column>
								<p:column headerText="#{labels.date_last_authentication}"
									styleClass="center">
									<h:outputText value="#{user.loginDate}">
										<f:convertDateTime locale="#{gnxSessionHandler.locale}"
											type="both" dateStyle="medium" timeStyle="short"
											timeZone="#{secSessionHandler.timeZone}" />
									</h:outputText>
								</p:column>
								<p:column styleClass="nowrap right" style="width:200px;">
									<p:commandButton id="role_openDialogButtonId" icon="fa fa-list-ul" title="#{labels.role}"
										process="@this" update="@none"
										actionListener="#{gnxCompanyHandler.onOpenRole(user, 'contacts_partenaire')}"
										rendered="#{gnxSessionHandler.isGranted('Users', 'UPDATE')}">
										<p:ajax event="dialogReturn"
											listener="#{gnxCompanyHandler.onSavedRole}" process="@this"
											update="-messages @(.psCPartnerPanel)" />
									</p:commandButton>
									<p:commandButton icon="ui-icon-mail-closed"
										title="#{labels.generate_new_password}"
										actionListener="#{gnxCompanyHandler.onReinitPassword(user)}"
										process="@this" update="-messages"
										onclick="if ( !#{gnxSessionHandler.backOfficeUser} ) { if ( !confirm('#{labels.generate_new_password_confirm}') ) { return false; } }"
										rendered="#{gnxSessionHandler.isGranted('Users', 'EXECUTE')}" />
									<!-- back: Add popup dialog -->
									<p:commandButton icon="ui-icon-copy"
										title="#{labels.duplicate}"
										actionListener="#{gnxCompanyHandler.onDuplicateUser(user, partner)}"
										process="@this" immediate="true" update="@none"
										rendered="#{gnxSessionHandler.backOfficeUser and gnxSessionHandler.isGranted('Users', 'CREATE')}">
										<p:ajax event="dialogReturn"
											listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
											update="-messages @(.psCPartnerPanel)" />
									</p:commandButton>
									<!-- front: full screen - redirection to upsertuser -->
									<p:button icon="ui-icon-copy" title="#{labels.duplicate}"
										outcome="/ui/upsertuser"
										rendered="#{not gnxSessionHandler.backOfficeUser and gnxSessionHandler.isGranted('Users', 'CREATE')}">
										<f:param name="uid" value="#{user.id}" />
										<f:param name="upid" value="#{partner.id}" />
										<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
										<f:param name="helpFragment"
											value="utilisateur_societe_dupliquer_contact_partenaire" />
										<f:param name="duplicate" value="true" />
									</p:button>
									<!-- back: Add popup dialog -->
									<p:commandButton icon="ui-icon-pencil"
										title="#{labels.button_edit}" disabled="#{gnxCompanyHandler.inProgress or gnxCompanyHandler.isImportProcessAlive()}"
										actionListener="#{gnxCompanyHandler.onOpenUserDialog('contacts_partenaire', partner, user)}"
										immediate="true" process="@this" update="@none"
										rendered="#{gnxSessionHandler.backOfficeUser and (gnxSessionHandler.isGranted('Users', 'UPDATE') or gnxSessionHandler.isGranted('Users', 'READ'))}">
										<p:ajax event="dialogReturn"
											listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
											update="-messages @(.psCPartnerPanel)" />
									</p:commandButton>
									<!-- front: full screen - redirection to upsertuser -->
									<p:button icon="ui-icon-pencil" title="#{labels.button_edit}"
										outcome="/ui/upsertuser" disabled="#{gnxCompanyHandler.inProgress or gnxCompanyHandler.isImportProcessAlive()}"
										rendered="#{not gnxSessionHandler.backOfficeUser and (gnxSessionHandler.isGranted('Users', 'UPDATE') or gnxSessionHandler.isGranted('Users', 'READ'))}">
										<f:param name="uid" value="#{user.id}" />
										<f:param name="upid" value="#{partner.id}" />
										<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
										<f:param name="helpFragment"
											value="utilisateur_societe_editer_contact_partenaire" />
										<f:param name="icode"
											value="#{gnxCompanyHandler.instance.code}" />
									</p:button>
									<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}"
										actionListener="#{gnxCompanyHandler.onRemoveUser(user)}"
										process="@this"
										onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
										update="-messages @widgetVar(wCpyPartnerTable)"
										disabled="#{gnxCompanyHandler.inProgress or gnxCompanyHandler.isImportProcessAlive()}"
										rendered="#{gnxSessionHandler.isGranted('Users', 'DELETE')}" />
								</p:column>
							</p:dataTable>
						</p:rowExpansion>
					</p:dataTable>
					<!-- logs -->
					<gnx:logs id="cPartnerImportLogsCC" title="#{labels.result}"
						value="#{gnxCompanyHandler.logs}"
						rendered="#{gnxCompanyHandler.logs!=null}">
						<f:facet name="actions">
							<p:button icon="ui-icon-seek-prev" value="#{labels.partners}"
								styleClass="mls" outcome="/ui/supplier/company"
								rendered="#{not (secSessionHandler.isAdministrator() or gnxSessionHandler.hasRole('MANAGER'))}" />
							<p:button icon="ui-icon-seek-prev" value="#{labels.partners}"
								styleClass="mls" outcome="/ui/admin/company"
								rendered="#{secSessionHandler.isAdministrator() or gnxSessionHandler.hasRole('MANAGER')}">
								<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
							</p:button>
						</f:facet>
					</gnx:logs>
				</p:outputPanel>
			</p:tab>
			<!-- CONTACTS -->
			<p:tab id="#{gnxCompanyHandler.getPartnerContactsTab()}">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-male" /> <h:outputText
								style="margin-left: 3px;" value="#{labels.contacts}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cPartnerUserPanel" styleClass="psCPartnerUserPanel">
					<!-- partners' users -->
					<p:dataTable id="cPartnerUserTable" var="user"
						value="#{gnxCompanyHandler.dataModel}" rows="15" paginator="true"
						paginatorPosition="bottom" paginatorAlwaysVisible="true"
						rowKey="#{user.id}" lazy="true" selectionMode="single"
						sortBy="#{user.login}" sortOrder="ascending"
						filteredValue="#{gnxCompanyHandler.filteredUsers}"
						widgetVar="wPartnerUserTable" styleClass="datatable-hide-filters"
						emptyMessage="#{labels.no_records_found}"
						paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
						rowsPerPageTemplate="15,50,100,200,500">
						<p:ajax event="rowSelect" immediate="true" process="@this"
							update="@none" listener="#{gnxCompanyHandler.onSelectPartnerUser}"
							oncomplete="jQuery('.jqsOpenPartnerUserDialog').click()" />
						<f:facet name="header">
							<h:panelGrid columnClasses="right" width="100%">
								<h:panelGroup>
									<p:inputText id="globalFilter"
										onkeyup="PF('wPartnerUserTable').filter()" style="width:150px" />
									<p:watermark for="globalFilter" value="#{labels.search}" />
								</h:panelGroup>
							</h:panelGrid>
						</f:facet>
						<p:column headerText="#{labels.partner}"
							sortBy="#{user.primaryGroup}"
							styleClass="bold">
							<h:outputText
								value="#{gnxCompanyHandler.getPrimaryGroup(user).fullname}" />
						</p:column>
						<p:column headerText="#{labels.login}" sortBy="#{user.login}">
							<h:outputText value="#{user.login}" />
						</p:column>
						<p:column headerText="#{seclbls.firstname}"
							sortBy="#{user.firstname}">
							<h:outputText value="#{user.firstname}" />
						</p:column>
						<p:column headerText="#{seclbls.lastname}"
							sortBy="#{user.lastname}">
							<h:outputText value="#{user.lastname}" />
						</p:column>
						<p:column headerText="#{seclbls.email}" sortBy="#{user.email}"
							style="width:15%;">
							<h:outputText value="#{user.email}" />
						</p:column>
						<p:column headerText="#{labels.roles}">
							<p:dataList value="#{gnxCompanyHandler.getUserRoles(user)}"
								var="urole" styleClass="commaList" emptyMessage="">
								<h:outputText value="#{urole.fullname}" title="#{urole.name}" />
							</p:dataList>
						</p:column>
						<p:column headerText="#{labels.number_connexion}"
							sortBy="#{user.loginCount}" styleClass="center">
							<h:outputText value="#{user.loginCount}" />
						</p:column>
						<p:column headerText="#{labels.date_last_authentication}"
							sortBy="#{user.loginDate}" styleClass="center">
							<h:outputText value="#{user.loginDate}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}"
									type="both" dateStyle="medium" timeStyle="short"
									timeZone="#{secSessionHandler.timeZone}" />
							</h:outputText>
						</p:column>
						<p:column styleClass="nowrap right" style="width:200px;">
							<p:commandButton id="ctc_role_openDialogButtonId" icon="fa fa-list-ul" title="#{labels.role}"
								process="@this" update="@none"
								actionListener="#{gnxCompanyHandler.onOpenRole(user, 'contacts_partenaire')}"
								rendered="#{gnxSessionHandler.isGranted('Users', 'UPDATE')}">
								<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedRole}" process="@this"
									update="-messages cPartnerUserTable" />
							</p:commandButton>
							<p:commandButton icon="ui-icon-mail-closed"
								title="#{labels.generate_new_password}"
								actionListener="#{gnxCompanyHandler.onReinitPassword(user)}"
								process="@this" update="-messages" styleClass="mls"
								onclick="if ( !#{gnxSessionHandler.backOfficeUser} ) { if ( !confirm('#{labels.generate_new_password_confirm}') ) { return false; } }"
								rendered="#{gnxCompanyHandler.instance!=null and gnxSessionHandler.isGranted('Users', 'EXECUTE')}" />
							<!-- back: Add popup dialog -->
							<p:commandButton icon="ui-icon-pencil"
								title="#{labels.button_edit}"
								actionListener="#{gnxCompanyHandler.onOpenUserDialog('contacts_partenaire', partner, user)}"
								immediate="true" process="@this" update="@none" styleClass="mls"
								rendered="#{gnxSessionHandler.backOfficeUser and (gnxSessionHandler.isGranted('Users', 'UPDATE') or gnxSessionHandler.isGranted('Users', 'READ'))}">
								<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
									update="-messages cPartnerUserTable" />
							</p:commandButton>
							<!-- front: full screen - redirection to upsertuser -->
							<p:button icon="ui-icon-pencil" title="#{labels.button_edit}"
								outcome="/ui/upsertuser"
								rendered="#{not gnxSessionHandler.backOfficeUser and (gnxSessionHandler.isGranted('Users', 'UPDATE') or gnxSessionHandler.isGranted('Users', 'READ'))}">
								<f:param name="uid" value="#{user.id}" />
								<f:param name="upid" value="#{partner.id}" />
								<f:param name="cpyid" value="#{gnxCompanyHandler.company.id}" />
								<f:param name="helpFragment"
									value="utilisateur_societe_editer_contact_partenaire" />
								<f:param name="icode" value="#{gnxCompanyHandler.instance.code}" />
							</p:button>
							<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}"
								actionListener="#{gnxCompanyHandler.onRemoveUser(user)}"
								process="@this" update="-messages cPartnerUserTable"
								onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
								styleClass="mls"
								rendered="#{gnxSessionHandler.isGranted('Users', 'DELETE')}" />
						</p:column>
					</p:dataTable>
					<h:panelGroup layout="block"
						style="width:1px;height:1px;visibility:hidden;">
						<p:commandButton id="ctc_user_openDialogButtonId" value=" " immediate="true" process="@this"
							update="@none"
							actionListener="#{gnxCompanyHandler.onOpenUserDialog('contacts_partenaire', null, null)}"
							styleClass="jqsOpenPartnerUserDialog">
							<p:ajax event="dialogReturn"
								listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
								update="-messages @(.psCPartnerUserPanel)" />
						</p:commandButton>
					</h:panelGroup>
				</p:outputPanel>
			</p:tab>
			<!-- PARTNER'S TECHNICAL CONTACTS -->
			<p:tab id="#{gnxCompanyHandler.getPartnerTechnicalContactsTab()}">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-male"></i> <h:outputText
								style="margin-left: 3px;" value="#{labels.partner_technical_users}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cPartnerTechnicalUserPanel" styleClass="psPartnerTechnicalUserPanel">
					<p:dataTable id="pTechnicalUserTable" var="techUser"
								 value="#{gnxCompanyHandler.technicalUsersDataModel}" rows="15" paginator="true"
								 paginatorPosition="bottom" paginatorAlwaysVisible="true"
								 lazy="true" selectionMode="single" sortBy="#{techUser.clientId}"
								 rowStyleClass="#{techUser.disabled ? 'disabled' : ''}"
								 widgetVar="wPartnerTechnicalUserTable" styleClass="datatable-hide-filters"
								 emptyMessage="#{labels.no_records_found}"
								 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								 currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
								 rowsPerPageTemplate="15,50,100,200,500">
						<p:ajax event="rowSelect" immediate="true" process="@this"
								update="@none" listener="#{gnxCompanyHandler.onSelectTechnicalUser}"
								oncomplete="jQuery('.jqsEditPartnerTechnicalUser').click()" />
						<f:facet name="header">
							<h:panelGrid columns="2" columnClasses="right" width="100%">
								<h:panelGroup>
									<p:inputText id="globalFilter"
												 onkeyup="PF('wPartnerTechnicalUserTable').filter()" style="width:150px" />
									<p:watermark for="globalFilter" value="#{labels.search}" />
									<p:commandButton icon="ui-icon-plusthick"
													 value="#{comlbls.add}"
													 actionListener="#{gnxCompanyHandler.onAddTechnicalUser}"
													 immediate="true" process="@this" update="@none"
													 styleClass="mls">
										<p:ajax event="dialogReturn"
												listener="#{gnxCompanyHandler.onSavedTechnicalUser}" process="@this"
												update="-messages @(.psPartnerTechnicalUserPanel)" />
									</p:commandButton>
								</h:panelGroup>
							</h:panelGrid>
						</f:facet>
						<p:column headerText="#{labels.technical_user_partner}" sortBy="#{techUser.primaryGroup}" styleClass="bold">
							<h:outputText value="#{techUser.primaryGroup.fullname}" />
						</p:column>
						<p:column headerText="#{labels.technical_user_clientId}" sortBy="#{techUser.clientId}">
							<h:outputText value="#{techUser.clientId}" />
						</p:column>
						<p:column headerText="#{labels.technical_roles}">
							<p:dataList value="#{gnxCompanyHandler.getTechnicalUserRoles(techUser)}"
										var="tuRole" styleClass="commaList">
								<h:outputText value="#{tuRole.fullname}" title="#{tuRole.name}" />
							</p:dataList>
						</p:column>
						<p:column styleClass="nowrap right" style="width:150px;">
							<p:commandButton icon="fa fa-list-ul" title="#{labels.role}"
											 process="@this" update="@none"
											 actionListener="#{gnxCompanyHandler.onOpenTechnicalUsersRole(techUser)}">
								<p:ajax event="dialogReturn"
										listener="#{gnxCompanyHandler.onSavedTechnicalUserRole}" process="@this"
										update="-messages @(.psPartnerTechnicalUserPanel)" />
							</p:commandButton>
							<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}"
											 actionListener="#{gnxCompanyHandler.onRemoveTechnicalUser(techUser)}"
											 process="@this" update="-messages @(.psPartnerTechnicalUserPanel)"
											 onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
											 styleClass="mls" />
						</p:column>
					</p:dataTable>
					<h:panelGroup layout="block"
								  style="width:1px;height:1px;visibility:hidden;">
						<p:commandButton id="editPartnerTechnicalUser"  value=" " immediate="true" process="@this"
										 update="@none"
										 actionListener="#{gnxCompanyHandler.onEditTechnicalUser(null)}"
										 styleClass="jqsEditPartnerTechnicalUser">
							<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedTechnicalUser}" process="@this"
									update="-messages @(.psPartnerTechnicalUserPanel)" />
						</p:commandButton>
					</h:panelGroup>
				</p:outputPanel>
			</p:tab>
			<!-- USERS -->
			<p:tab id="cUserTab"
				rendered="#{gnxCompanyHandler.instance==null and (not (gnxCompanyHandler.company.id==null or (not secSessionHandler.isAdministrator() and not gnxSessionHandler.hasRole('MANAGER'))))}">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-user"></i> <h:outputText
								style="margin-left: 3px;" value="#{labels.user_company}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cUserPanel" styleClass="psCUserPanel">
					<p:dataTable id="cUserTable" var="user"
						value="#{gnxCompanyHandler.dataModel}" rows="15" paginator="true"
						paginatorPosition="bottom" paginatorAlwaysVisible="true"
						rowKey="#{user.id}" lazy="true" selectionMode="single"
						rowStyleClass="#{user.disabled ? 'disabled' : ''}"
						sortBy="#{user.login}" sortOrder="ascending"
						filteredValue="#{gnxCompanyHandler.filteredUsers}"
						widgetVar="wUserTable" styleClass="datatable-hide-filters"
						emptyMessage="#{labels.no_records_found}"
						paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
								rowsPerPageTemplate="15,50,100,200,500">
						<p:ajax event="rowSelect" immediate="true" process="@this"
							update="@none" listener="#{gnxCompanyHandler.onSelectUser}"
							oncomplete="jQuery('.jqsOpenUserDialogCompany').click()" />
						<f:facet name="header">
							<h:panelGrid columns="2" columnClasses="right" width="100%">
								<!-- <h:outputText value="#{labels.users}" styleClass="title1" /> -->
								<h:panelGroup>
									<p:inputText id="globalFilter"
										onkeyup="PF('wUserTable').filter()" style="width:150px" />
									<p:watermark for="globalFilter" value="#{labels.search}" />
									<p:commandButton icon="ui-icon-plusthick"
										value="#{comlbls.add}"
										actionListener="#{gnxCompanyHandler.onAddUser('utilisateurs_societe', null)}"
										immediate="true" process="@this" update="@none"
										styleClass="mls">
										<p:ajax event="dialogReturn"
											listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
											update="-messages @(.psCUserPanel)" />
									</p:commandButton>
								</h:panelGroup>
							</h:panelGrid>
						</f:facet>
						<p:column headerText="#{labels.login}" sortBy="#{user.login}"
							filterBy="#{user.login}" style="width:10%;">
							<h:outputText value="#{user.login}" />
						</p:column>
						<p:column headerText="#{seclbls.firstname}"
							sortBy="#{user.firstname}" filterBy="#{user.firstname}"
							style="width:10%;">
							<h:outputText value="#{user.firstname}" />
						</p:column>
						<p:column headerText="#{seclbls.lastname}"
							sortBy="#{user.lastname}" filterBy="#{user.lastname}"
							style="width:10%;">
							<h:outputText value="#{user.lastname}" />
						</p:column>
						<p:column headerText="#{seclbls.email}" sortBy="#{user.email}"
							filterBy="#{user.email}" style="width:15%;">
							<h:outputText value="#{user.email}" />
						</p:column>
						<p:column headerText="#{labels.roles}"
							filterBy="#{gnxCompanyHandler.getUserRoles(user)}">
							<p:dataList value="#{gnxCompanyHandler.getUserRoles(user)}"
								var="urole" styleClass="commaList">
								<h:outputText value="#{urole.fullname}" title="#{urole.name}" />
							</p:dataList>
						</p:column>
						<p:column headerText="#{labels.number_connexion}"
							sortBy="#{user.loginCount}" styleClass="center"
							style="width:10%;">
							<h:outputText value="#{user.loginCount}" />
						</p:column>
						<p:column headerText="#{labels.date_last_authentication}"
							sortBy="#{user.loginDate}" styleClass="center" style="width:10%;">
							<h:outputText value="#{user.loginDate}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}"
									type="both" dateStyle="medium" timeStyle="short"
									timeZone="#{secSessionHandler.timeZone}" />
							</h:outputText>
						</p:column>
						<p:column styleClass="nowrap right" style="width:150px;">
							<p:commandButton icon="fa fa-list-ul" title="#{labels.role}"
								process="@this" update="@none"
								actionListener="#{gnxCompanyHandler.onOpenRole(user, 'utilisateurs_societe')}">
								<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedRole}" process="@this"
									update="-messages @(.psCUserPanel)" />
							</p:commandButton>
							<p:commandButton icon="ui-icon-mail-closed"
								title="#{labels.generate_new_password}"
								actionListener="#{gnxCompanyHandler.onReinitPassword(user)}"
								process="@this" update="-messages" styleClass="mls"
								rendered="#{gnxCompanyHandler.instance!=null}" />
							<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}"
								actionListener="#{gnxCompanyHandler.onRemoveUser(user)}"
								process="@this" update="-messages @(.psCUserPanel)"
								onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
								styleClass="mls" />
						</p:column>
					</p:dataTable>
					<h:panelGroup layout="block"
						style="width:1px;height:1px;visibility:hidden;">
						<p:commandButton id="user_openDialogButtonId"  value=" " immediate="true" process="@this"
							update="@none"
							actionListener="#{gnxCompanyHandler.onOpenUserDialog('utilisateurs_societe', null, null)}"
							styleClass="jqsOpenUserDialogCompany">
							<p:ajax event="dialogReturn"
								listener="#{gnxCompanyHandler.onSavedUser}" process="@this"
								update="-messages @(.psCUserPanel)" />
						</p:commandButton>
					</h:panelGroup>
				</p:outputPanel>
			</p:tab>
			<!-- CUSTOMER'S TECHNICAL USERS -->
			<p:tab id="cTechnicalUserTab" rendered="#{gnxCompanyHandler.renderTechnicalUsersTab() and gnxCompanyHandler.instance==null and (not (gnxCompanyHandler.company.id==null or (not secSessionHandler.isAdministrator() and not gnxSessionHandler.hasRole('MANAGER'))))}">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-user"></i> <h:outputText
								style="margin-left: 3px;" value="#{labels.customer_technical_users}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cTechnicalUserPanel" styleClass="psCTechnicalUserPanel">
					<p:dataTable id="cTechnicalUserTable" var="techUser"
								 value="#{gnxCompanyHandler.technicalUsersDataModel}" rows="15" paginator="true"
								 paginatorPosition="bottom" paginatorAlwaysVisible="true"
								 lazy="true" selectionMode="single" sortBy="#{techUser.clientId}"
								 rowStyleClass="#{techUser.disabled ? 'disabled' : ''}"
								 widgetVar="wTechnicalUserTable" styleClass="datatable-hide-filters"
								 emptyMessage="#{labels.no_records_found}"
								 paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								 currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
								 rowsPerPageTemplate="15,50,100,200,500">
						<p:ajax event="rowSelect" immediate="true" process="@this"
								update="@none" listener="#{gnxCompanyHandler.onSelectTechnicalUser}"
								oncomplete="jQuery('.jqsEditCompanyTechnicalUser').click()" />
						<f:facet name="header">
							<h:panelGrid columns="2" columnClasses="right" width="100%">
								<h:panelGroup>
									<p:inputText id="globalFilter"
												 onkeyup="PF('wTechnicalUserTable').filter()" style="width:150px" />
									<p:watermark for="globalFilter" value="#{labels.search}" />
									<p:commandButton icon="ui-icon-plusthick"
													 value="#{comlbls.add}"
													 actionListener="#{gnxCompanyHandler.onAddTechnicalUser}"
													 immediate="true" process="@this" update="@none"
													 styleClass="mls">
										<p:ajax event="dialogReturn"
												listener="#{gnxCompanyHandler.onSavedTechnicalUser}" process="@this"
												update="-messages @(.psCTechnicalUserPanel)" />
									</p:commandButton>
								</h:panelGroup>
							</h:panelGrid>
						</f:facet>
						<p:column headerText="#{labels.technical_user_clientId}" sortBy="#{techUser.clientId}">
							<h:outputText value="#{techUser.clientId}" />
						</p:column>
						<p:column headerText="#{labels.technical_roles}">
							<p:dataList value="#{gnxCompanyHandler.getTechnicalUserRoles(techUser)}"
										var="tuRole" styleClass="commaList">
								<h:outputText value="#{tuRole.fullname}" title="#{tuRole.name}" />
							</p:dataList>
						</p:column>
						<p:column styleClass="nowrap right" style="width:150px;">
							<p:commandButton icon="fa fa-list-ul" title="#{labels.role}"
											 process="@this" update="@none"
											 actionListener="#{gnxCompanyHandler.onOpenTechnicalUsersRole(techUser)}">
								<p:ajax event="dialogReturn"
										listener="#{gnxCompanyHandler.onSavedTechnicalUserRole}" process="@this"
										update="-messages @(.psCTechnicalUserPanel)" />
							</p:commandButton>
							<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}"
											 actionListener="#{gnxCompanyHandler.onRemoveTechnicalUser(techUser)}"
											 process="@this" update="-messages @(.psCTechnicalUserPanel)"
											 onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
											 styleClass="mls" />
						</p:column>
					</p:dataTable>
					<h:panelGroup layout="block"
								  style="width:1px;height:1px;visibility:hidden;">
						<p:commandButton id="editCTechnicalUser"  value=" " immediate="true" process="@this"
										 update="@none"
										 actionListener="#{gnxCompanyHandler.onEditTechnicalUser(null)}"
										 styleClass="jqsEditCompanyTechnicalUser">
							<p:ajax event="dialogReturn"
									listener="#{gnxCompanyHandler.onSavedTechnicalUser}" process="@this"
									update="-messages @(.psCTechnicalUserPanel)" />
						</p:commandButton>
					</h:panelGroup>
				</p:outputPanel>
			</p:tab>
			<!-- ROLES -->
			<p:tab id="cRoleTab"
				rendered="#{gnxCompanyHandler.instance==null and (not (gnxCompanyHandler.company.id==null or (not secSessionHandler.isAdministrator() and not gnxSessionHandler.hasRole('MANAGER'))))}">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-list-ul"></i> <h:outputText
								style="margin-left: 3px;" value="#{labels.roles}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cRolePanel" styleClass="psCRolePanel">
					<gnx:roles handler="#{gnxCompanyHandler}" />
					<gnx:logs id="cImportRoles" title="#{labels.result}"
							value="#{gnxCompanyHandler.logs}"
							rendered="#{gnxCompanyHandler.logs!=null}">
							<f:facet name="actions">
								<p:commandButton icon="ui-icon-seek-prev" 
										  value="#{labels.roles}"
										  styleClass="mls"  
										  process="@this" 
                                    	  update="@(.psCRolePanel)">
									 <f:setPropertyActionListener target="#{gnxCompanyHandler.logs}" value="#{null}" />
								</p:commandButton>
							</f:facet>
					</gnx:logs>
				</p:outputPanel>
			</p:tab>
			<!-- PERIMETER -->
			<p:tab id="cPerimeterTab"
				rendered="#{gnxCompanyHandler.instance==null and (not (gnxCompanyHandler.company.id==null or (not secSessionHandler.isAdministrator() and not gnxSessionHandler.hasRole('MANAGER'))))}">
				<f:facet name="title">
					<ul class="fa-ul" style="margin-left: 3px;">
						<li><i class="fa fa-expand"></i> <h:outputText
								style="margin-left: 3px;" value="#{labels.perimeters}" /></li>
					</ul>
				</f:facet>
				<p:outputPanel id="cPerimeterPanel" styleClass="psCPerimeterPanel">
					<gnx:perimeter handler="#{gnxCompanyHandler}" />
					<gnx:logs id="cImportPerimeters" title="#{labels.result}"
							value="#{gnxCompanyHandler.logs}"
							rendered="#{gnxCompanyHandler.logs!=null}">
							<f:facet name="actions">
								<p:commandButton icon="ui-icon-seek-prev" 
										  value="#{labels.perimeters}"
										  styleClass="mls"  
										  process="@this" 
                                    	  update="@(.psCPerimeterPanel)">
									 <f:setPropertyActionListener target="#{gnxCompanyHandler.logs}" value="#{null}" />
								</p:commandButton>
							</f:facet>
					</gnx:logs>
				</p:outputPanel>
			</p:tab>
		</p:tabView>

		<p:dialog id="cPartnerUpDlg" header="#{labels.partner_import_file}"
			widgetVar="wCPartnerUpDlg" width="700" height="160" resizable="false"
			modal="true" dynamic="true">
			<p:outputPanel styleClass="psImportPanel text-right responsive-buttons mt-4">
				<p:selectOneMenu id="cSelectOneInstance" value="#{gnxCompanyHandler.instanceCodeSelected}"
					styleClass="selectonemenu"
             		style="margin-top:3px;width:100px;position:absolute;left:450px;z-index:1"
             		rendered="#{gnxCompanyHandler.instance eq null || gnxCompanyHandler.instance eq ''}">
                	<f:selectItem itemValue="#{null}" itemLabel=""/>
               	 	<f:selectItems value="#{gnxCompanyHandler.instanceCodes}"/>
                	<p:ajax process="@this"/>
           		</p:selectOneMenu>
				<p:fileUpload  mode="advanced" multiple="true" auto="true"
					label="#{labels.import}" 
					fileUploadListener="#{gnxCompanyHandler.onUploadPartners}"
					process="@this" update="-messages @(.psCPartnerPanel)"
					oncomplete="if(args.success == true){PF('wImportPartnerProgress').start();PF('wCPartnerUpDlg').hide();}"
					allowTypes="/(\.|\/)(xls?x)$/"
					requiredMessage="#{labels.ui_required}"
					validatorMessage="#{labels.ui_validator}"
					converterMessage="#{labels.ui_converter}"
					fileLimitMessage="#{labels.ui_file_limit}"
					invalidFileMessage="#{labels.ui_invalid_file}"
					invalidSizeMessage="#{labels.ui_invalid_size}"
					widgetVar="wPrtnrloadIpt"/>
				<!-- Progress -->
				<p:outputPanel styleClass="psImportPartnerProgress">
					<p:progressBar id="importPartnerProgress" widgetVar="wImportPartnerProgress" ajax="true"
						value="#{gnxCompanyHandler.progress}" labelTemplate="{value}%" styleClass="animated"
						global="true" style="height: 1.7em;margin-top:10px;visibility:hidden;">
						<p:ajax event="complete" listener="#{gnxCompanyHandler.onComplete}"
							update="@(.psCPartnerPanel)" oncomplete="PF('wCPartnerUpDlg').hide()" />
					</p:progressBar>
				</p:outputPanel>
				<script>
		             (function() {
		                 setTimeout(PF('wPrtnrloadIpt').jq.find('input:file').attr('accept', '.xls, .xlsx'), 2000);
		             })();
	            </script>
			</p:outputPanel>
		</p:dialog>

		<p:dialog id="cPartnerMailDlg"
			header="#{labels.regenerate_all_password}"
			widgetVar="wCPartnerMailDlg" width="600" resizable="false"
			modal="true" dynamic="true">
			<p:ajax event="close" listener="#{gnxCompanyHandler.setLogs(null)}"
				update="cPartnerMailDlg" />
			<p:outputPanel styleClass="psCPartnerMailPnl">
				<gnx:logs title="#{labels.result}" value="#{gnxCompanyHandler.logs}"
					rendered="#{gnxCompanyHandler.logs!=null}" details="false" />
				<h:panelGroup rendered="#{gnxCompanyHandler.logs==null}">
					<h:panelGrid columnClasses="right">
						<h:panelGroup>
							<p:commandButton icon="ui-icon-mail-closed"
								value="#{labels.send}"
								actionListener="#{gnxCompanyHandler.onSend}"
								process="@(.psCPartnerMailPnl)"
								update="-messages @(.psCPartnerMailPnl)" styleClass="mls" />
							<p:commandButton icon="ui-icon-close" value="#{labels.close}"
								type="button" onclick="PF('wCPartnerMailDlg').hide()"
								styleClass="mls" />
						</h:panelGroup>
					</h:panelGrid>
					<p:selectOneRadio id="mailTypeOpt"
						value="#{gnxCompanyHandler.mailOption}" layout="custom">
						<f:selectItems value="#{gnxCompanyHandler.mailOptions}"
							var="mailOption" itemValue="#{mailOption}" />
					</p:selectOneRadio>
					<p:panelGrid id="mailGrid" styleClass="left">
						<p:row>
							<p:column style="width:20px;">
								<p:radioButton id="sendAllOpt" for="mailTypeOpt" itemIndex="0" />
							</p:column>
							<p:column>
								<h:outputText value="#{labels.all}" />
							</p:column>
						</p:row>
						<p:row>
							<p:column>
								<p:radioButton id="sendPartnersOpt" for="mailTypeOpt"
									itemIndex="1" />
							</p:column>
							<p:column>
								<p:autoComplete id="sendPartnersSelectionIpt"
									value="#{gnxCompanyHandler.selectedPartners}"
									completeMethod="#{gnxCompanyHandler.onCompletePartner}" var="p"
									maxResults="20" itemValue="#{p}" itemLabel="#{p.fullname}"
									converter="groupConverter" multiple="true"
									label="#{labels.partners}" placeholder="#{labels.partners}"
									pt:placeholder="#{labels.partners}">
									<p:column style="width:20px;text-align:center">
										<h:graphicImage library="components/gnx"
											name="/images/#{p.description}-16.png" width="16" height="16" />
									</p:column>
									<p:column>
										<h:outputText value="#{p.fullname} (#{p.name})" />
									</p:column>
								</p:autoComplete>
							</p:column>
						</p:row>
					</p:panelGrid>
				</h:panelGroup>
			</p:outputPanel>
		</p:dialog>
	</cc:implementation>
</ui:component>