<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:b="http://byzaneo.com/ui"
              xmlns:grt="http://xmlns.jcp.org/jsf/composite/components/gnx/routing"
              xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx"
>

    <cc:interface name="companyGroupFront">
        <cc:attribute name="value" type="com.byzaneo.security.bean.Group" required="true"/>
        <cc:attribute name="partner" type="java.lang.Boolean" default="false"/>
        <cc:attribute name="title" type="java.lang.String"/>
        <cc:attribute name="readonly" type="boolean" default="false"/>
        <cc:facet name="gactions"/>
    </cc:interface>

    <cc:implementation>

        <!--BANIERE-->
        <div class="banner-user">
            <div class="container">
                <div id="bloc_banner">
                    <div id="icon_banner">
                        <div class="img-society"></div>
                    </div>
                    <div id="info_society" class="titlesociety">
                        <h:outputText value="#{cc.attrs.title}" styleClass="#{cc.attrs.titleClass}"/><br/>
                        <a href="#{request.contextPath}/logout" class="btn btn-deconnexion"><h:outputText
                                value="#{labels.logout}" styleClass="lilogout"/></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- ~~ CONTENT~~ -->
        <h:panelGroup layout="block" id="cPartnerContent">
            <p:spacer height="55px" width="100%" rendered="#{gnxSessionHandler.backOfficeUser}"/>
            <p:tabView id="partnerInfoTabView" scrollable="true">

                <!-- ** GENERAL ** -->
                <p:tab id="generalTab" title="#{labels.general}"
                       rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('General', 'READ')}">
                    <p:messages id="partnerInfoMessages" showDetail="false" autoUpdate="true" closable="true"
                                escape="false"/>
                    <!-- CONTENT -->
                    <!--GENERAL-->
                    <div class="container">
                        <div class="row form-society">
                            <div class="col-sm-4 stitlegeneral ">
                                <h:outputText value="#{labels.general}" styleClass="title4"/>
                            </div>

                            <div class="col-sm-8 ">
                                <div class="container-fluid">
                                    <div class="row">
                                        <!--CODE-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group"
                                                           rendered="#{not cc.attrs.partner or cc.attrs.value.id!=null}">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cGroupName"
                                                               value="#{labels.organization_name}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cGroupName" value="#{cc.attrs.value.name}"
                                                                 required="true" readonly="#{cc.attrs.value.id!=null}"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validator validatorId="groupNameUnicityValidator"/>
                                                        <f:validator validatorId="xssValidator" />
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Name -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cfullname"
                                                               value="#{labels.organization_fullname}"/>
                                                <p:outputPanel styleClass="col-sm-12"><p:inputText id="cfullname"
                                                                                                   value="#{cc.attrs.value.fullname}"
                                                                                                   validator="xssValidator"
                                                                                                   required="true"
                                                                                                   styleClass="form-control"
                                                                                                   disabled="#{cc.attrs.readonly}"/></p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Profile -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group"
                                                           rendered="#{cc.attrs.partner and gnxSessionHandler.administrator}">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cProfileSlc"
                                                               value="#{labels.organization_profile}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:selectOneMenu id="cProfileSlc"
                                                                     value="#{cc.attrs.value.freeViewProfile}"
                                                                     widgetVar="wProfileSlc"
                                                                     disabled="#{cc.attrs.readonly}">
                                                        <p:ajax process="@this"
                                                                listener="#{gnxOrganizationDialogHandler.onChangeFreeViewProfile()}"
                                                                oncomplete="if(args.validationFailed){PF('wProfileSlc').selectValue('')}"/>
                                                        <f:selectItem itemValue="#{null}" itemLabel=""/>
                                                        <f:selectItems
                                                                value="#{gnxOrganizationDialogHandler.freeViewProfiles}"/>
                                                    </p:selectOneMenu>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Comment -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group" rendered="#{not cc.attrs.partner}">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cGroupDesc"
                                                               value="#{labels.organization_comment}"/>
                                                <p:outputPanel styleClass="col-sm-12"><p:inputTextarea id="cGroupDesc"
                                                                                                       value="#{cc.attrs.value.comment}"
                                                                                                       validator="xssValidator"
                                                                                                       rows="3"
                                                                                                       styleClass="form-control"
                                                                                                       disabled="#{cc.attrs.readonly}"/></p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr style="width:100%;"/>

                    <!--IDENTIFICATION-->
                    <div class="container">
                        <div class="row">
                            <div class="col-sm-4 stitleidentification">
                                <h:outputText value="#{labels.identification}" styleClass="title4"/>
                            </div>

                            <div class="col-sm-8 ">
                                <div class="container-fluid">
                                    <div class="row">
                                        <!--Identification-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cPartnerCode"
                                                               value="#{labels.organization_code}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerCode" value="#{cc.attrs.value.code}"
                                                    			 validator="xssValidator"
                                                                 required="true" styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateRegex pattern="[a-zA-Z0-9_]{1,128}"/>
                                                        <f:validator validatorId="codeUnicityValidator"/>
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- SIREN -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cPartnerSiret"
                                                               value="#{labels.organization_registration}"/>
                                                <p:outputPanel styleClass="col-sm-12"><p:inputText id="cPartnerSiret"
                                                                                                   value="#{cc.attrs.value.registration}"
                                                                                                   validator="xssValidator"
                                                                                                   styleClass="form-control"
                                                                                                   disabled="#{cc.attrs.readonly}"/></p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- TVA -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cPartnerTva"
                                                               value="#{labels.organization_vat}"/>
                                                <p:outputPanel styleClass="col-sm-12"><p:inputText id="cPartnerTva"
                                                                                                   value="#{cc.attrs.value.vat}"
                                                                                                   validator="xssValidator"
                                                                                                   styleClass="form-control"
                                                                                                   disabled="#{cc.attrs.readonly}"/></p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Référence -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cPartnerDuns"
                                                               value="#{labels.organization_duns}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerDuns" value="#{cc.attrs.value.duns}"
                                                    			 validator="xssValidator"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="128"/>
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Raison sociale -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerRegisterName"
                                                               value="#{labels.organization_registerName}"/>
                                                <p:outputPanel styleClass="col-sm-12"><p:inputText
                                                        id="cPartnerRegisterName"
                                                        value="#{cc.attrs.value.registeredName}"
                                                        validator="xssValidator"
                                                        styleClass="form-control"
                                                        disabled="#{cc.attrs.readonly}"/></p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr style="width:100%;"/>

                    <!--ADRESSE-->
                    <div class="container">
                        <div class="row">
                            <div class="col-sm-4 stitleadress">
                                <h:outputText value="#{seclbls.address}" styleClass="title4"/>
                            </div>

                            <div class="col-sm-8 ">
                                <div class="container-fluid">
                                    <div class="row">
                                        <!--Adresse-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="caddress"
                                                               value="#{labels.organization_address_streetName}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="caddress"
                                                                 value="#{cc.attrs.value.location.address.streetName}"
                                                                 validator="xssValidator"
                                                                 required="true" styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="128"/>
                                                    </p:inputText>
                                                    <p:inputText id="caddressComp"
                                                                 value="#{cc.attrs.value.location.address.addressComplement}"
                                                                 validator="xssValidator"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="128"/>
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!--Code Postale -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="czip"
                                                               value="#{labels.organization_address_postalCode}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="czip"
                                                                 value="#{cc.attrs.value.location.address.postalCode}"
                                                                 validator="xssValidator"
                                                                 required="true" styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="16"/>
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Ville -->
                                        <div class="col-sm-7 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="ccity"
                                                               value="#{labels.organization_address_city}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="ccity"
                                                                 value="#{cc.attrs.value.location.address.city}"
                                                                 validator="xssValidator"
                                                                 required="true" styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="128"/>
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!-- Pays -->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="ccountry"
                                                               value="#{labels.organization_address_country}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:selectOneMenu id="ccountry" style="width: 50%"
                                                                     value="#{cc.attrs.value.location.address.country}"
                                                                     required="true" converter="localeConverter"
                                                                     disabled="#{cc.attrs.readonly}">
                                                        <f:selectItem itemValue="#{null}" itemLabel=""/>
                                                        <f:selectItems value="#{gnxHandler.countryItems}"/>
                                                    </p:selectOneMenu>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr style="width:100%;"/>

                    <!--INFORMATION-->
                    <div class="container">
                        <div class="row">
                            <div class="col-sm-4 stitleinformation">
                                <h:outputText value="#{labels.information}" styleClass="title4"/>
                            </div>

                            <div class="col-sm-8 ">
                                <div class="container-fluid">
                                    <div class="row">
                                        <!--Contact-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group" rendered="false">
                                                <p:outputLabel styleClass="col-sm-12 control-label" for="cClientPartner"
                                                               value="#{labels.organization_client}"/>
                                                <p:outputPanel styleClass="col-sm-12"><p:selectBooleanCheckbox
                                                        id="cClientPartner" value="#{cc.attrs.value.client}"
                                                        styleClass="form-control"
                                                        disabled="#{cc.attrs.readonly}"/></p:outputPanel>
                                            </p:outputPanel>
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerOrderContact"
                                                               value="#{labels.organization_orderContact}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerOrderContact"
                                                                 value="#{cc.attrs.value.orderContact}"
                                                                 validator="xssValidator"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="256"/>
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!--Telephone-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerOrderPhone"
                                                               value="#{labels.organization_orderPhone}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerOrderPhone"
                                                                 value="#{cc.attrs.value.orderPhone}"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="64"/>
                                                        <f:attribute name="field" value="#{labels.organization_orderPhone}"/>
                                                        <f:validator validatorId="phoneNumberValidator"/>
                                                        <f:validator validatorId="xssValidator" />
                                     					</p:inputText>
                        								<p:watermark for="cPartnerOrderPhone" value="+xx (x)x xx xx xx xx" />
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!--Fax-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerOrderFax"
                                                               value="#{labels.organization_orderFax}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerOrderFax"
                                                                 value="#{cc.attrs.value.orderFax}"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}">
                                                        <f:validateLength maximum="64"/>
                                                        <f:attribute name="field" value="#{labels.organization_orderFax}"/>
                                                        <f:validator validatorId="phoneNumberValidator"/>
                                                        <f:validator validatorId="xssValidator" />
                                     					</p:inputText>
                        								<p:watermark for="cPartnerOrderFax" value="+xx (x)x xx xx xx xx" />
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!--Email-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerOrderEmail"
                                                               value="#{labels.organization_orderEmail}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerOrderEmail"
                                                                 value="#{cc.attrs.value.orderEmail}"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}"
                                                                 validatorMessage="#{labels.validator_email}">
                                                        <f:validateLength maximum="2048"/>
                                                        <f:validateRegex pattern="#{gnxHandler.getMultipleEmailsValidator()}"/>
                                                        <f:validator validatorId="xssValidator" />
                                                    </p:inputText>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!--Capital social-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerShareCapital"
                                                               value="#{labels.organization_shareCapital}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerShareCapital"
                                                                 value="#{cc.attrs.value.shareCapital}"
                                                                 validator="xssValidator"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}"/>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                        <!--Forme juridique-->
                                        <div class="col-sm-6 bloc-form">
                                            <p:outputPanel styleClass="form-group">
                                                <p:outputLabel styleClass="col-sm-12 control-label"
                                                               for="cPartnerLegalStructure"
                                                               value="#{labels.organization_legalStructure}"/>
                                                <p:outputPanel styleClass="col-sm-12">
                                                    <p:inputText id="cPartnerLegalStructure"
                                                                 value="#{cc.attrs.value.legalStructure}"
                                                                 validator="xssValidator"
                                                                 styleClass="form-control"
                                                                 disabled="#{cc.attrs.readonly}"/>
                                                </p:outputPanel>
                                            </p:outputPanel>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr style="width:100%;"/>


                    <!-- SAVE -->
                    <div class="container">
                        <p:panelGrid id="cPartnerHeader" rendered="#{not gnxSessionHandler.backOfficeUser}">
                            <p:row>
                                <p:column styleClass="right ">
                                    <cc:renderFacet name="gactions"/>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </div>
                    <!-- fixed -->
                    <p:outputPanel styleClass="dialog-header navbar navbar-default navbar-fixed-top"
                                   rendered="#{gnxSessionHandler.backOfficeUser}">
                        <p:outputPanel styleClass="navbar-container">
                            <p:outputPanel class="navbar-header pull-left">
                                <h:outputText value="#{cc.attrs.title}" styleClass="title1 navbar-brand"/>
                            </p:outputPanel>
                            <p:outputPanel class="navbar-form navbar-right">
                                <cc:renderFacet name="gactions"/>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:tab>

                <!-- ** FREETEXT (Configuration/Details) ** -->
                <p:tab id="freeViewTab" title="#{not cc.attrs.partner ? labels.freetext : labels.details}"
                       rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'READ') or gnxSessionHandler.isGranted('FreeText_Details', 'READ')}">
                    <p:messages id="partnerFVMessages" showDetail="false" autoUpdate="true" closable="true"
                                escape="false"/>
                    <!-- * FREE VIEW: CONFIGURATION * -->
                    <p:outputPanel id="fvconf" styleClass="psfvconf" rendered="#{not cc.attrs.partner}">
                        <!-- FREE VIEW: LIST -->
                        <p:outputPanel id="fvlists" rendered="#{gnxOrganizationDialogHandler.freeView==null}">
                            <p:panelGrid id="ptnPropViewHead">
                                <p:row>
                                    <p:column styleClass="right">
                                        <h:outputText value="#{labels.organization_freeViewProfile} "/>
                                    </p:column>
                                    <p:column style="width:160px;">
                                        <p:selectOneMenu id="cpyFreeViewProfile"
                                                         value="#{gnxOrganizationDialogHandler.freeViewProfile}"
                                                         styleClass="left mls" style="width:150px;">
                                            <p:ajax process="@this" update="ptnPropViewList"
                                                    listener="#{gnxOrganizationDialogHandler.onChangeFreeViewProfile()}"/>
                                            <f:selectItems value="#{gnxOrganizationDialogHandler.freeViewProfiles}"/>
                                        </p:selectOneMenu>
                                    </p:column>
                                    <p:column style="width:2%;vertical-align:top" styleClass="nowrap"
                                              rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'CREATE') or gnxSessionHandler.isGranted('FreeText_Details', 'DELETE')}">
                                        <p:commandButton icon="ui-icon-plusthick" title="#{labels.add}" type="button"
                                                         onclick="PF('wFVPNewDlg').show()"
                                                         rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'CREATE')}"
                                                         styleClass="mls"/>
                                        <p:commandButton icon="ui-icon-trash"
                                                         actionListener="#{gnxOrganizationDialogHandler.onRemoveFreeViewProfile}"
                                                         process="@this cpyFreeViewProfile"
                                                         update="ptnPropViewHead ptnPropViewPnl"
                                                         rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'DELETE')}"
                                                         onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
                                                         styleClass="mls"/>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <p:outputPanel id="ptnPropViewPnl" styleClass="psPtnPropViewPnl">
                                <!-- Profile Properties -->
                                <p:dataTable id="ptnPropViewList"
                                             value="#{gnxOrganizationDialogHandler.freeViewGroupProperties}"
                                             var="pv" rowKey="#{pv.name}"
                                             selectionMode="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE') ? 'single' : null}"
                                             selection="#{gnxOrganizationDialogHandler.freeView}"
                                             draggableRows="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"
                                             styleClass="datatable-noheader">
                                    <p:ajax event="rowSelect" process="@this" update="@(.psfvconf)"
                                            disabled="#{not gnxSessionHandler.administrator and not gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"/>
                                    <p:ajax event="rowReorder"
                                            listener="#{gnxOrganizationDialogHandler.onFreeViewReorder}"
                                            update="ptnPropViewList"
                                            disabled="#{not gnxSessionHandler.administrator and not gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"/>
                                    <p:column>
                                        <h:outputText value="#{pv.label}"
                                                      styleClass="#{pv.rendered ? null : 'disabled'} #{pv.required ? 'bold' : null}"/>
                                    </p:column>
                                    <p:column style="width:25px;cursor:pointer;cursor:hand;" styleClass="center"
                                              rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}">
                                        <h:panelGroup styleClass="fa fa-arrows-v fa-lg" style="color: #CCC;"/>
                                    </p:column>
                                </p:dataTable>
                            </p:outputPanel>
                        </p:outputPanel>
                        <!-- FREE VIEW: Profile Property Edition -->
                        <p:outputPanel id="ppvEditPnl" rendered="#{gnxOrganizationDialogHandler.freeView!=null}">
                            <p:outputPanel id="ppvEditGrd" styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-2 control-label" for="pvNameIpt"
                                               value="#{labels.name}"/>
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:inputText id="pvNameIpt" value="#{gnxOrganizationDialogHandler.freeView.label}"
                                    			 validator="xssValidator"
                                                 styleClass="form-control"
                                                 pt:placeholder="#{gnxOrganizationDialogHandler.freeView.name}"/>
                                </p:outputPanel>
                                <p:outputLabel styleClass="col-sm-2 control-label" for="pvRenderChk"
                                               value="#{labels.rendered}"/>
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:selectBooleanCheckbox id="pvRenderChk"
                                                             value="#{gnxOrganizationDialogHandler.freeView.rendered}"
                                                             styleClass="form-control"/>
                                </p:outputPanel>
                                <p:outputLabel styleClass="col-sm-2 control-label" for="pvRequiredChk"
                                               value="#{labels.required}"/>
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:selectBooleanCheckbox id="pvRequiredChk"
                                                             value="#{gnxOrganizationDialogHandler.freeView.required}"
                                                             styleClass="form-control"/>
                                </p:outputPanel>
                                <p:outputLabel styleClass="col-sm-2 control-label" for="pvRoChk"
                                               value="#{labels.readonly}"/>
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:selectBooleanCheckbox id="pvRoChk"
                                                             value="#{gnxOrganizationDialogHandler.freeView.readonly}"
                                                             styleClass="form-control"/>
                                </p:outputPanel>
                                <p:outputPanel styleClass="col-sm-12 right">
                                    <p:commandButton icon="ui-icon-check" value="#{labels.ok}"
                                                     process="@this ppvEditGrd" update="@(.psfvconf)">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxOrganizationDialogHandler.freeView}"/>
                                    </p:commandButton>
                                    <p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" immediate="true"
                                                     process="@this" update="@(.psfvconf)" styleClass="mls">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxOrganizationDialogHandler.freeView}"/>
                                    </p:commandButton>
                                </p:outputPanel>
                            </p:outputPanel>
                        </p:outputPanel>
                        <!-- FREE VIEW: NEW PROFILE -->
                        <p:dialog id="cpyFVProfileNewDlg" header="#{labels.profile}" modal="true" width="300"
                                  widgetVar="wFVPNewDlg"
                                  onShow="registerConfirmDialogHotkeys(PF('wFVPNewDlg'));">
                            <h:panelGrid columns="2" columnClasses=",right">
                                <h:panelGroup>
                                    <p:inputText id="cpyFvpName"
                                                 value="#{gnxOrganizationDialogHandler.freeViewProfileName}"
                                                 validator="xssValidator"
                                                 label="#{labels.name}"/>
                                    <p:watermark for="cpyFvpName" value="#{labels.name}"/>
                                </h:panelGroup>
                                <p:commandButton icon="ui-icon-check"
                                                 actionListener="#{gnxOrganizationDialogHandler.onAddFreeViewProfile}"
                                                 process="@this cpyFvpName"
                                                 update="partnerFVMessages ptnPropViewHead cpyFVProfileNewDlg @(.psPtnPropViewPnl)"
                                                 oncomplete="handleCloseDialog(xhr, status, args, PF('wFVPNewDlg'))"
                                                 ignoreAutoUpdate="true"/>
                            </h:panelGrid>
                        </p:dialog>
                    </p:outputPanel>
                    <!-- * FREE VIEW: PARTNER VISUALIZATION * -->
                    <h:outputText value="#{labels.complementary_info}"
                                  rendered="#{(cc.attrs.partner or gnxOrganizationDialogHandler.partnerOrganization) and gnxOrganizationDialogHandler.getFreeViewGroup(cc.attrs.value.freeViewProfile).isRendered()}"
                                  styleClass="title2"/>
                    <p:outputPanel styleClass="mbs"
                                   rendered="#{(cc.attrs.partner or gnxOrganizationDialogHandler.partnerOrganization) and gnxOrganizationDialogHandler.getFreeViewGroup(cc.attrs.value.freeViewProfile).isRendered()}">
                        <ui:repeat
                                value="#{gnxOrganizationDialogHandler.getFreeViewGroup(cc.attrs.value.freeViewProfile).getProperties()}"
                                var="fpv">
                            <p:outputPanel styleClass="form-group" rendered="#{fpv.rendered}">
                                <p:outputLabel styleClass="col-sm-2 control-label"
                                               value="#{fpv.label} #{fpv.required ? '*' : ''}"/>
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:inputText value="#{cc.attrs.value[fpv.name]}"
                                    			 validator="xssValidator"
                                                 styleClass="form-control"
                                                 label="#{fpv.label}"
                                                 required="#{fpv.required}"
                                                 requiredMessage="#{fpv.label} #{labels.property_required}"
                                                 readonly="#{fpv.readonly}"
                                                 disabled="#{!gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"
                                                 rendered="#{fpv.getName().contains('freeText') or fpv.getName().contains('freeDouble')}"/>
                                    <p:inputTextarea value="#{cc.attrs.value[fpv.name]}"
                                    				 validator="xssValidator"
                                                     styleClass="form-control"
                                                     label="#{fpv.label}"
                                                     required="#{fpv.required}"
                                                     requiredMessage="#{fpv.label} #{labels.property_required}"
                                                     readonly="#{fpv.readonly}"
                                                     rows="2"
                                                     disabled="#{!gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"
                                                     rendered="#{fpv.getName().contains('freeLongText')}"/>
                                    <p:selectOneMenu value="#{cc.attrs.value[fpv.name]}"
                                                     label="#{fpv.label}"
                                                     required="#{fpv.required}"
                                                     disabled="#{!gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"
                                                     rendered="#{fpv.getName().contains('freeBoolean')}">
                                        <f:selectItem itemValue="#{null}" itemLabel="#{labels.boolean_select}"
                                                      noSelectionOption="true"/>
                                        <f:selectItem itemValue="true" itemLabel="#{labels.boolean_true}"/>
                                        <f:selectItem itemValue="false" itemLabel="#{labels.boolean_false}"/>
                                    </p:selectOneMenu>
                                    <p:calendar value="#{cc.attrs.value[fpv.name]}"
                                                label="#{fpv.label}"
                                                locale="#{gnxSessionHandler.locale}"
                                                required="#{fpv.required}"
                                                requiredMessage="#{fpv.label} #{labels.property_required}"
                                                readonly="#{fpv.readonly}"
                                                disabled="#{!gnxSessionHandler.isGranted('FreeText_Details', 'UPDATE')}"
                                                rendered="#{fpv.getName().contains('freeDate')}"/>
                                </p:outputPanel>
                            </p:outputPanel>
                        </ui:repeat>
                    </p:outputPanel>
                </p:tab>
                <!-- ** SCOPED PARTNERS USERS ** -->
                <p:tab id="cPerimeterTab" title="#{labels.users}"
                       rendered="#{gnxSessionHandler.partnerUser and gnxSessionHandler.isGranted('Users', 'READ')}">
                    <gnx:userScope/>
                </p:tab>
                <!-- ** MESSAGING ** -->
                <p:tab id="cfgMessagingTab" title="#{labels.notification}"
                       rendered="#{gnxOrganizationDialogHandler.configurations['gnxMessagingService']!=null
                		and ( gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Notification', 'READ'))}">
                    <p:outputPanel styleClass="form-group">
                        <p:outputLabel styleClass="col-sm-2 control-label" for="cfgMsgCron" value="#{labels.schedule}"/>
                        <p:outputPanel styleClass="col-sm-10">
                            <b:inputCron id="cfgMsgCron"
                                         value="#{gnxOrganizationDialogHandler.configurations.gnxMessagingService.cron}"
                                         time="false" defaultPeriod="week" dayPeriod="false"
                                         lang="#{gnxSessionHandler.locale.language}"
                                         disabled="#{cc.attrs.readonly or not ( gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Notification', 'UPDATE')) }"/>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:tab>
                <!-- ** E-DOCUMENT ** -->
                <p:tab id="cfgEDocTab" title="#{edocsrvlbls.edocument}"
                       rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('eDocument', 'READ')}">
                    <p:outputPanel styleClass="psCfgEdocPanel">
                        <!-- EXCHANGE -->
                        <p:outputPanel id="cfgEDocExchange"
                                       rendered="#{gnxOrganizationDialogHandler.selectedExchange!=null}">
                            <p:messages id="cfgEDocMessages" showDetail="false" autoUpdate="true" closable="true"
                                        escape="false"/>
                            <h:outputLabel value="#{edocsrvlbls.exchange}" styleClass="title2"/>
                            <!-- Document Kind -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label" for="cfgEDocTypeS"
                                               value="#{edocsrvlbls.document_kind}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:autoComplete id="cfgEDocTypeS"
                                                    value="#{gnxOrganizationDialogHandler.selectedExchange.id.kind}"
                                                    completeMethod="#{gnxEDocumentAppHandler.onCompleteDocumentTypes}"
                                                    var="doctype" itemLabel="#{doctype}" itemValue="#{doctype}"
                                                    groupBy="#{gnxEDocumentAppHandler.getDocumentTypeGroup(doctype)}"
                                                    dropdown="true" cache="true" forceSelection="true"
                                                    inputStyle="width:305px;"
                                                    scrollHeight="250" required="true"
                                                    disabled="#{not gnxOrganizationDialogHandler.isNewExchange()}"/>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Recipient -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label"
                                               for="cfgEDocRecipient"
                                               value="#{gnxOrganizationDialogHandler.selectedExchange.id.direction == 'S' ? edocsrvlbls.sending_to : edocsrvlbls.receiving_from}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:selectOneMenu id="cfgEDocRecipient" filter="true"
                                                     required="true" style="width:100%" converter="groupConverter"
                                                     value="#{gnxOrganizationDialogHandler.selectedExchange.id.dematPartner}"
                                                     disabled="#{not gnxOrganizationDialogHandler.isNewExchange()}">
                                        <f:selectItems
                                                value="#{gnxOrganizationDialogHandler.getGroupsById(cc.attrs.value.id)}"
                                                var="group"
                                                itemValue="#{group}" itemLabel="#{group.fullname}"/>
                                    </p:selectOneMenu>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Period -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label"
                                               for="cfgEDocPeriodStart" value="#{edocsrvlbls.date_period}"/>
                                <p:calendar id="cfgEDocPeriodStart"
                                            value="#{gnxOrganizationDialogHandler.selectedExchange.start}"
                                            locale="#{gnxSessionHandler.locale}" styleClass="col-sm-3"
                                            timeZone="#{secSessionHandler.timeZone}"
                                            converter="dateXssConverter"
                                            required="true" pattern="dd-MM-yyyy HH:mm:ss" >
                                	<f:attribute name="label" value="#{edocsrvlbls.date_period}" />
                                	<f:attribute name="dateFormat" value="dd-MM-yyyy HH:mm:ss" />
                                </p:calendar>
                                <p:outputLabel styleClass="col-sm-1 control-label center"
                                               for="cfgEDocPeriodEnd" value="#{edocsrvlbls.to_end}"/>
                                <p:calendar id="cfgEDocPeriodEnd"
                                            value="#{gnxOrganizationDialogHandler.selectedExchange.end}"
                                            locale="#{gnxSessionHandler.locale}" styleClass="col-sm-3"
                                            timeZone="#{secSessionHandler.timeZone}"
                                            converter="dateXssConverter"
                                            pattern="dd-MM-yyyy HH:mm:ss">
                                	<f:attribute name="label" value="#{edocsrvlbls.date_period}" />
                                	<f:attribute name="dateFormat" value="dd-MM-yyyy HH:mm:ss" />
                                </p:calendar>
                            </p:outputPanel>
                            <!-- Actions -->
                            <p:panelGrid columns="1" columnClasses="right">
                                <h:panelGroup>
                                    <p:commandButton icon="ui-icon-check" value="#{labels.ok}"
                                                     actionListener="#{gnxOrganizationDialogHandler.onAddExchange()}"
                                                     process="@this cfgEDocExchange" update="@(.psCfgEdocPanel)"/>
                                    <p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" immediate="true"
                                                     process="@this" update="@(.psCfgEdocPanel)" styleClass="mls">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxOrganizationDialogHandler.selectedExchange}"/>
                                    </p:commandButton>
                                </h:panelGroup>
                            </p:panelGrid>
                        </p:outputPanel>
                        <!-- EXCHANGES -->
                        <p:dataTable id="cfgEDocExchanges"
                                     value="#{gnxOrganizationDialogHandler.getExchangesByGroupId(cc.attrs.value.id)}"
                                     var="exchange"
                                     emptyMessage="#{labels.no_records_found}"
                                     rendered="#{gnxOrganizationDialogHandler.selectedExchange==null}">
                            <f:facet name="header">
                                <h:panelGrid columns="2" columnClasses="left,right">
                                    <h:outputText value="#{edocsrvlbls.exchanges}" styleClass="title2"/>
                                    <p:menuButton value="#{edocsrvlbls.exchange_new}"
                                                  rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('eDocument', 'CREATE')}">
                                        <p:menuitem icon="fa fa-share" value="#{edocsrvlbls.sending}"
                                                    actionListener="#{gnxOrganizationDialogHandler.onNewExchange('S')}"
                                                    process="@this" update="@(.psCfgEdocPanel)"/>
                                        <p:menuitem icon="fa fa-reply" value="#{edocsrvlbls.receiving}"
                                                    actionListener="#{gnxOrganizationDialogHandler.onNewExchange('R')}"
                                                    process="@this" update="@(.psCfgEdocPanel)"/>
                                    </p:menuButton>
                                </h:panelGrid>
                            </f:facet>
                            <p:column style="width:30px" sortBy="#{exchange.id.direction}">
                                <h:panelGroup styleClass="fa fa-share" rendered="#{exchange.id.direction == 'S'}"/>
                                <h:panelGroup styleClass="fa fa-reply" rendered="#{exchange.id.direction == 'R'}"/>
                            </p:column>
                            <p:column headerText="#{edocsrvlbls.recipient}"
                                      sortBy="#{exchange.id.dematPartner.fullname}">
                                <h:outputText value="#{exchange.id.dematPartner.fullname}"/>
                            </p:column>
                            <p:column headerText="#{edocsrvlbls.document_kind}">
                                <h:outputText value="#{edocsrvlbls[exchange.id.kind]}"/>
                            </p:column>
                            <p:column headerText="#{edocsrvlbls.date_period}" styleClass="center nowrap">
                                <h:outputText value="#{exchange.start}">
                                    <f:convertDateTime locale="#{gnxSessionHandler.locale}"   timeZone="#{secSessionHandler.timeZone}" type="date"
                                                       dateStyle="medium"/>
                                </h:outputText>
                                <h:panelGroup styleClass="fa fa-caret-right" style="margin: 0 5px;"
                                              rendered="#{exchange.start!=null or exchange.end!=null}"/>
                                <h:outputText value="#{exchange.end}">
                                    <f:convertDateTime locale="#{gnxSessionHandler.locale}"  timeZone="#{secSessionHandler.timeZone}" type="date"
                                                       dateStyle="medium"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText=" " styleClass="right" style="width:120px"
                                      rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('eDocument', 'UPDATE') or gnxSessionHandler.isGranted('eDocument', 'DELETE')}">
                                <p:commandButton icon="ui-icon-pencil"
                                                 actionListener="#{gnxOrganizationDialogHandler.onSelectExchange(exchange)}"
                                                 process="@this" update="@(.psCfgEdocPanel)"
                                                 styleClass="mls"
                                                 rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('eDocument', 'UPDATE')}"/>
                            </p:column>
                        </p:dataTable>
                    </p:outputPanel>
                    <!-- TIMESTAMP SERVER -->
                    <p:fieldset id="cfgEDocTimestampServerPanel" styleClass="mbs"
                                legend="#{edocsrvlbls.exchange_ts_server}" toggleable="true" toggleSpeed="500"
                                style="margin-top:10px;"
                                rendered="#{gnxSessionHandler.administrator}">
                        <p:outputPanel id="cfgEDocTimestampServer" rendered="true">
                            <ui:param name="keyStoreItems"
                                      value="#{gnxKeyStoreAppHandler.getKeyStoreItemsForTIMESTAMP(gnxOrganizationDialogHandler.company, true)}"/>
                            <!-- url -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-2 control-label" for="cfgEDocTimestampServerUrl"
                                               value="#{edocsrvlbls.exchange_ts_server_url}" />
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:inputText id="cfgEDocTimestampServerUrl"
                                                 value="#{gnxOrganizationDialogHandler.configurations.gnxKeyStoreService.timestampServer.url}"
                                                 validator="xssValidator"
                                                 styleClass="form-control" disabled="#{cc.attrs.readonly}"/>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- keystore (identify us on tsp server) -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-2 control-label" for="cfgEDocTimestampServerUrl"
                                               value="#{edocsrvlbls.exchange_ts_keystore_name}"/>
                                <p:outputPanel styleClass="col-sm-4">
                                    <p:selectOneMenu id="cfgEDocTimestampServerKeyStoreName"
                                                     value="#{gnxOrganizationDialogHandler.configurations.gnxKeyStoreService.timestampServer.keyStoreName}"
                                                     label="#{labels.ks_friendlyname}" style="min-width:200px;"
                                                     disabled="#{cc.attrs.readonly}">
                                        <f:selectItem itemValue="#{null}" itemLabel=" "/>
                                        <f:selectItems value="#{keyStoreItems}"/>
                                    </p:selectOneMenu>
                                </p:outputPanel>
                                <p:outputLabel styleClass="col-sm-6" value="#{edocsrvlbls.exchange_ts_keystore_help}"/>
                            </p:outputPanel>
                            <!-- truststore (identify tsp server on our side) -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-2 control-label" for="cfgEDocTimestampServerUrl"
                                               value="#{edocsrvlbls.exchange_ts_truststore_name}"/>
                                <p:outputPanel styleClass="col-sm-4">
                                    <p:selectOneMenu id="cfgEDocTimestampServerTrustStoreName"
                                                     value="#{gnxOrganizationDialogHandler.configurations.gnxKeyStoreService.timestampServer.trustStoreName}"
                                                     label="#{labels.ks_friendlyname}" style="min-width:200px;"
                                                     disabled="#{cc.attrs.readonly}">
                                        <f:selectItem itemValue="#{null}" itemLabel=" "/>
                                        <f:selectItems value="#{keyStoreItems}"/>
                                    </p:selectOneMenu>
                                </p:outputPanel>
                                <p:outputLabel styleClass="col-sm-6"
                                               value="#{edocsrvlbls.exchange_ts_truststore_help}"/>
                            </p:outputPanel>
                            <!-- policyOID-  rfc 3161 param -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-2 control-label" for="cfgEDocTimestampServerUrl"
                                               value="#{edocsrvlbls.exchange_ts_policyoid}"/>
                                <p:outputPanel styleClass="col-sm-10">
                                    <p:inputText id="cfgEDocTimestampServerPolicyOid"
                                                 value="#{gnxOrganizationDialogHandler.configurations.gnxKeyStoreService.timestampServer.policyOid}"
                                                 validator="xssValidator"
                                                 styleClass="form-control" disabled="#{cc.attrs.readonly}"/>
                                </p:outputPanel>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:fieldset>
                </p:tab>
                <!-- ** KEYSTORES ** -->
                <p:tab id="cfgKSTabId" title="#{labels.keystores}"
                       rendered="#{gnxKeyStoreHandler.organizationId!=null and gnxSessionHandler.administrator}">
                    <p:outputPanel styleClass="psKeystoresTabPnl">
                        <p:outputPanel id="keystoresPanel"
                                       rendered="#{gnxKeyStoreHandler.keystore==null and gnxKeyStoreHandler.keystoreFile==null}"
                                       styleClass="psKeystoresPanel">
                            <p:messages id="generalk_pmessages" autoUpdate="true" closable="true" escape="false"/>
                            <p:dataTable id="keystoresTable" value="#{gnxKeyStoreHandler.keystores}" var="ks"
                                         rows="10" paginator="true" paginatorPosition="bottom"
                                         paginatorAlwaysVisible="true" rowKey="#{ks.id}"
                                         selection="#{gnxKeyStoreHandler.keystore}" selectionMode="single"
                                         sortBy="#{ks.reference}"
                                         sortOrder="ascending" filteredValue="#{gnxKeyStoreHandler.filteredKeystores}"
                                         widgetVar="wKeystoresTable" styleClass="datatable-hide-filters"
                                         emptyMessage="#{labels.no_records_found}"
                                         rowHover="#{gnxKeyStoreHandler.getKeystoreComment(ks)}">
                                <p:ajax event="rowSelect" immediate="true" process="@this"
                                        update="@(.psKeystoresTabPnl)"/>
                                <f:facet name="header">
                                    <h:panelGrid columnClasses="right" width="100%">
                                        <h:panelGroup>
                                            <p:inputText id="globalFilter" onkeyup="PF('wKeystoresTable').filter()"
                                                         style="width:150px"/>
                                            <p:defaultCommand target="globalFilter"/>
                                            <p:watermark for="globalFilter" value="#{labels.search}"/>
                                            <p:menuButton value="#{labels.import}" styleClass="mls"
                                                          rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                                <p:menuitem value="#{labels.import_cert_jks_pkcs12}"
                                                            actionListener="#{gnxKeyStoreHandler.onAddP12}"
                                                            process="@this"
                                                            update="@(.psKeystoresTabPnl) @(.importPanel)"/>
                                                <p:menuitem value="#{labels.import_cert_pkcs11}"
                                                            actionListener="#{gnxKeyStoreHandler.onAddP11}"
                                                            process="@this"
                                                            update="@(.psKeystoresTabPnl) @(.importPanel)"/>
                                                <p:menuitem value="#{labels.import_cert_pem_cer_crt}"
                                                            actionListener="#{gnxKeyStoreHandler.onAddNonStandard}"
                                                            process="@this"
                                                            update="@(.psKeystoresTabPnl) @(.importPanel)"/>
                                                <p:menuitem value="#{labels.import_cert_pkcs7}"
                                                            actionListener="#{gnxKeyStoreHandler.onAddP7}"
                                                            process="@this"
                                                            update="@(.psKeystoresTabPnl) @(.importPanel)"/>
                                            </p:menuButton>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </f:facet>
                                <p:column headerText="#{labels.ks_friendlyname}" sortBy="#{ks.reference}"
                                          filterBy="#{ks.reference}" filterMatchMode="contains" styleClass="bold">
                                    <h:outputText id="keyStoreFriendlyNameOutputText" value="#{ks.reference}"/>
                                </p:column>
                                <p:column headerText="#{labels.ks_entry_notbefore}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreEntryNotBefore(ks)}"
                                          styleClass="center">
                                    <h:outputText value="#{gnxKeyStoreHandler.getKeystoreEntryNotBefore(ks)}">
                                        <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date"
                                                           dateStyle="medium" timeZone="#{secSessionHandler.timeZone}"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{labels.ks_entry_notafter}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreEntryNotAfter(ks)}"
                                          styleClass="center">
                                    <h:outputText value="#{gnxKeyStoreHandler.getKeystoreEntryNotAfter(ks)}">
                                        <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date"
                                                           dateStyle="medium" timeZone="#{secSessionHandler.timeZone}"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{labels.ks_use_start_date}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreStartDateOfUse(ks)}"
                                          styleClass="center">
                                    <h:outputText value="#{gnxKeyStoreHandler.getKeystoreStartDateOfUse(ks)}">
                                        <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date"
                                                           dateStyle="medium" timeZone="#{secSessionHandler.timeZone}"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{labels.ks_use_end_date}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreEndDateOfUse(ks)}"
                                          styleClass="center">
                                    <h:outputText value="#{gnxKeyStoreHandler.getKeystoreEndDateOfUse(ks)}">
                                        <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date"
                                                           dateStyle="medium" timeZone="#{secSessionHandler.timeZone}"/>
                                    </h:outputText>
                                </p:column>
                                <p:column headerText="#{labels.ks_entry_subject}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreEntrySubjectCN(ks)}"
                                          filterBy="#{gnxKeyStoreHandler.getKeystoreEntrySubjectCN(ks)}"
                                          filterMatchMode="contains" styleClass="center">
                                    <h:outputText value="#{gnxKeyStoreHandler.getKeystoreEntrySubjectCN(ks)}"/>
                                </p:column>
                                <p:column headerText="#{labels.ks_hasprivatekey}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreEntryPrivateKey(ks)}"
                                          styleClass="center">
                                    <h:outputText rendered="#{gnxKeyStoreHandler.getKeystoreEntryPrivateKey(ks)}"
                                                  styleClass="ui-icon ui-icon-check"/>
                                    <h:outputText rendered="#{not gnxKeyStoreHandler.getKeystoreEntryPrivateKey(ks)}"
                                                  styleClass="ui-icon ui-icon-close"/>
                                </p:column>
                                <p:column headerText="#{labels.usage}"
                                          sortBy="#{gnxKeyStoreHandler.getKeystoreUsage(ks)}"
                                          filterBy="#{gnxKeyStoreHandler.getKeystoreUsage(ks)}"
                                          filterMatchMode="contains" styleClass="center">
                                    <h:outputText value="#{gnxKeyStoreHandler.getKeystoreUsage(ks)}"/>
                                </p:column>
                                <p:column styleClass="right nowrap">
                                    <p:commandButton title="#{labels.download}" icon="fa fa-upload" styleClass="mls"
                                                     immediate="true" process="@this" ajax="false"
                                                     actionListener="#{gnxKeyStoreHandler.onKeystoreDownload(ks)}"
                                                     rendered="#{gnxKeyStoreHandler.isDownloadable(ks)}"
                                                     style="width:22px;height:22px;">
                                        <p:fileDownload value="#{gnxKeyStoreHandler.keystoreContent}"/>
                                    </p:commandButton>
                                    <p:commandButton title="#{labels.delete}" icon="fa fa-trash-o"
                                                     actionListener="#{gnxKeyStoreHandler.onKeystoreRemove(ks)}"
                                                     process="@this" update="@(.psKeystoresPanel)"
                                                     onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
                                                     styleClass="mls" style="width:22px;height:22px;"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'DELETE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                                </p:column>
                            </p:dataTable>
                        </p:outputPanel>
                        <!-- ADD P11 KEYSTORE -->
                        <p:outputPanel id="keystore11Upload"
                                       rendered="#{gnxKeyStoreHandler.keystoreFile!=null and gnxKeyStoreHandler.keystoreFile.class.simpleName=='Pkcs11KeyStoreFile'}">
                            <p:panelGrid columns="2" columnClasses="left,right" style="margin-bottom:10px;">
                                <h:outputText value="#{labels.ks_upload}" styleClass="title2"/>
                                <h:panelGroup>
                                    <p:commandButton value="#{labels.save}"
                                                     actionListener="#{gnxKeyStoreHandler.onSaveP11}"
                                                     process="@this keystoreEditPanel_pkcs11"
                                                     update="keystoreEditPanel_pkcs11 @(.psKeystoresTabPnl)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                                    <p:commandButton value="#{labels.cancel}" styleClass="mls" immediate="true"
                                                     process="@this" update="@(.psKeystoresTabPnl)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxKeyStoreHandler.keystoreFile}"/>
                                    </p:commandButton>
                                </h:panelGroup>
                            </p:panelGrid>
                            <p:outputPanel id="keystoreEditPanel_pkcs11">
                                <p:messages id="addMessages_pkcs11" autoUpdate="true" closable="true" escape="false"/>
                                <!-- Friendly Name -->
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.ks_friendlyname}"
                                                   for="keystoreFriendlyName_pkcs11"/>
                                    <p:outputPanel styleClass="col-sm-9">
                                        <h:inputText id="keystoreFriendlyName_pkcs11"
                                                     value="#{gnxKeyStoreHandler.keystoreFile.friendlyName}"
                                                     validator="xssValidator"
                                                     styleClass="form-control" required="true"/>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <!-- Usage -->
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.usage}"
                                                   for="ksUsage_pkcs11"/>
                                    <p:outputPanel styleClass="col-sm-9">
                                        <p:selectOneMenu id="ksUsage_pkcs11"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.usage}">
                                            <f:selectItem itemValue="#{null}" itemLabel=" "/>
                                            <f:selectItems value="#{gnxKeyStoreAppHandler.keyStoreUsageItems}"/>
                                        </p:selectOneMenu>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <!-- Description -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.ks_comment}"
                                                       for="keystoreComment_pkcs11"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keystoreComment_pkcs11"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.comment}"
                                                         validator="xssValidator"
                                                         styleClass="form-control"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- Slot index -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label"
                                                       value="#{labels.ks_slotIndex}" for="keystoreSlotIndex"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keystoreSlotIndex"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.slotIndex}"
                                                         validator="xssValidator"
                                                         styleClass="form-control" required="true"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- Label -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.ks_label}" for="keystoreLabel"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keystoreLabel" value="#{gnxKeyStoreHandler.keystoreFile.label}" validator="xssValidator" styleClass="form-control" required="true" />
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- Library -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.ks_library}"
                                                       for="keyStoreLibrary"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keyStoreLibrary"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.library}"
                                                         validator="xssValidator"
                                                         styleClass="form-control" required="true"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- Password (pincode) -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label"
                                                       value="#{labels.ks_password_pkcs11}"
                                                       for="keystorePassword_pkcs11"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputSecret id="keystorePassword_pkcs11"
                                                           value="#{gnxKeyStoreHandler.keystoreFile.password}"
                                                           validator="xssValidator"
                                                           styleClass="form-control"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                            </p:outputPanel>
                        </p:outputPanel>
                        <!-- ADD P12 KEYSTORE -->
                        <p:outputPanel id="keystoreUpload"
                                       rendered="#{gnxKeyStoreHandler.keystoreFile!=null and gnxKeyStoreHandler.keystoreFile.class.simpleName=='KeyStoreFile'}">
                            <p:panelGrid columns="2" columnClasses="left,right" style="margin-bottom:10px;">
                                <h:outputText value="#{labels.ks_upload}" styleClass="title2"/>
                                <h:panelGroup>
                                    <p:commandButton value="#{labels.save}"
                                                     actionListener="#{gnxKeyStoreHandler.onSaveP12}"
                                                     process="@this keystoreEditPanel"
                                                     update="keystoreEditPanel @(.psKeystoresTabPnl)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                                    <p:commandButton value="#{labels.cancel}" styleClass="mls" immediate="true"
                                                     process="@this" update="@(.psKeystoresTabPnl)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxKeyStoreHandler.keystoreFile}"/>
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxKeyStoreHandler.keystoreUploaded}"/>
                                    </p:commandButton>
                                </h:panelGroup>
                            </p:panelGrid>
                            <p:outputPanel id="keystoreEditPanel">
                                <p:messages id="addMessages" autoUpdate="true" closable="true" escape="false"/>
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label"
                                                       value="#{labels.ks_friendlyname}" for="keystoreFriendlyName"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keystoreFriendlyName"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.friendlyName}"
                                                         validator="xssValidator"
                                                         styleClass="form-control"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- Usage -->
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.usage}"
                                                   for="ksUsage"/>
                                    <p:outputPanel styleClass="col-sm-9">
                                        <p:selectOneMenu id="ksUsage" value="#{gnxKeyStoreHandler.keystoreFile.usage}">
                                            <f:selectItem itemValue="#{null}" itemLabel=" "/>
                                            <f:selectItems value="#{gnxKeyStoreAppHandler.keyStoreUsageItems}"/>
                                        </p:selectOneMenu>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.ks_comment}"
                                                       for="keystoreComment"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keystoreComment"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.comment}"
                                                         validator="xssValidator"
                                                         styleClass="form-control"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- alias - optional, specify if ambiguous -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label"
                                                       value="#{labels.ks_entry_alias}" for="keyStoreAlias"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputText id="keyStoreAlias"
                                                         pt:placeholder="#{labels.ks_entry_alias_optional}"
                                                         value="#{gnxKeyStoreHandler.keystoreFile.alias}"
                                                         validator="xssValidator"
                                                         styleClass="form-control"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- password -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.ks_password}"
                                                       for="keystorePassword"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <h:inputSecret id="keystorePassword"
                                                           value="#{gnxKeyStoreHandler.keystoreFile.password}"
                                                           validator="xssValidator"
                                                           styleClass="form-control"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <!-- validity dates -->
                                <h:panelGroup layout="block" styleClass="form-group">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label"
                                                       value="#{labels.ks_use_period}" for="keystoreUsePeriod"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <b:inputDatePeriod id="keystoreUsePeriod" widgetVar="wKsUsagePeriod"
                                                               value="#{gnxKeyStoreHandler.keystoreFile.usePeriod}"
                                                               style="width:290px;margin-top:2px"
                                                               validator="xssValidator"
                                                               timePicker="true" locale="#{gnxSessionHandler.locale}">
                                            </b:inputDatePeriod>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="form-group importPanel">
                                    <p:outputPanel styleClass="form-group">
                                        <p:outputLabel styleClass="col-sm-3 control-label" value="#{labels.file}"
                                                       for="keystoreUploaded"/>
                                        <p:outputPanel styleClass="col-sm-9">
                                            <p:fileUpload id="keystoreUploaded" label="#{labels.import}"
                                                          fileUploadListener="#{gnxKeyStoreHandler.onUploadFile}"
                                                          auto="true" required="true"
                                                          allowTypes="/(\.|\/)#{gnxKeyStoreHandler.certificateFormat}$/"
                                                          update="ksFileName"/><br/>
                                            <h:outputText id="ksFileName"
                                                          value="#{gnxKeyStoreHandler.keystoreUploaded.fileName}"/>
                                        </p:outputPanel>
                                    </p:outputPanel>
                                </h:panelGroup>
                            </p:outputPanel>
                        </p:outputPanel>
                        <!-- VIEW KEYSTORE -->
                        <p:outputPanel id="keystoreDetailPanel" rendered="#{gnxKeyStoreHandler.keystore!=null}"
                                       styleClass="psKeystoresPanel">
                            <p:panelGrid style="margin-bottom:10px">
                                <p:row>
                                    <p:column><h:outputText value="#{gnxKeyStoreHandler.keystore.reference}"
                                                            styleClass="title2"/></p:column>
                                    <p:column styleClass="right">
                                        <p:commandButton id="prevButtonId" icon="ui-icon-seek-prev"
                                                         value="#{labels.back}" process="@this" immediate="true"
                                                         update="@(.psKeystoresTabPnl)" styleClass="mls">
                                            <f:setPropertyActionListener target="#{gnxKeyStoreHandler.keystore}"
                                                                         value="#{null}"/>
                                        </p:commandButton>
                                    </p:column>
                                </p:row>
                            </p:panelGrid>
                            <p:tabView id="keystoreDetailsTabs">
                                <p:tab id="keystoreDetailsTab">
                                    <f:facet name="title">
                                        <h:panelGroup styleClass="fa fa-shield fa-fw"/>
                                        <h:outputText value="#{labels.ks_keystoredetailstab}"/>
                                    </f:facet>
                                    <p:panelGrid style="margin-bottom:10px;" columns="2"
                                                 columnClasses="input-panel-label top,left">
                                        <p:outputLabel value="#{labels.ks_friendlyname}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.keystore.reference}"/>
                                        <p:outputLabel value="#{labels.ks_filename}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.getKeystoreDetailFilename()}"/>
                                        <p:outputLabel value="#{labels.ks_uploaddate}"/>
                                        <p:outputLabel
                                                value="#{gnxKeyStoreHandler.keystore.creationDate}"><f:convertDateTime
                                                pattern="dd/MM/yyyy HH:mm"
                                                timeZone="#{secSessionHandler.timeZone}"/></p:outputLabel>
                                        <p:outputLabel value="#{labels.ks_type}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.keystore.type}"/>
                                        <p:outputLabel value="#{labels.ks_comment}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.getKeystoreDetailComment()}"/>
                                        <p:outputLabel value="#{labels.ks_certificationpath}"/>
                                        <p:tree id="certificateChainTree"
                                                value="#{gnxKeyStoreHandler.getKeystoreCertificationPath()}" var="cert"><p:treeNode
                                                id="certificateChainTreeNode"><h:outputText
                                                value="#{cert}"/></p:treeNode></p:tree>
                                    </p:panelGrid>
                                </p:tab>
                                <p:tab id="entryDetailTab">
                                    <f:facet name="title">
                                        <h:panelGroup styleClass="fa fa-bookmark-o fa-fw"/>
                                        <h:outputText value="#{labels.ks_entrydetailstab}"/>
                                    </f:facet>
                                    <p:panelGrid style="margin-bottom:10px;" columns="2"
                                                 columnClasses="input-panel-label,left">
                                        <p:outputLabel value="#{labels.ks_entry_alias}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.getKeystoreEntryAlias()}"/>
                                        <p:outputLabel value="#{labels.ks_modulus_length}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.getKeyStorePublicKeyModulus()}"/>
                                        <p:outputLabel value="#{labels.ks_entry_version}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.getKeystoreEntryVersion()}"/>
                                        <p:outputLabel value="#{labels.ks_entry_subject}"/>
                                        <p:tree id="subjectTree" value="#{gnxKeyStoreHandler.getKeystoreEntrySubject()}"
                                                var="subject" style="width:auto;display:inline-block"><p:treeNode
                                                id="subjectTreeNode" styleClass="ui-icon-squaresmall-plus"><h:outputText
                                                value="#{subject}"/></p:treeNode></p:tree>
                                        <p:outputLabel value="#{labels.ks_entry_issuer}"/>
                                        <p:tree id="issuerTree" value="#{gnxKeyStoreHandler.getKeystoreEntryIssuer()}"
                                                var="issuer" style="width:auto;display:inline-block"><p:treeNode
                                                id="issuerTreeNode"><h:outputText
                                                value="#{issuer}"/></p:treeNode></p:tree>
                                        <p:outputLabel value="#{labels.ks_entry_serialnumber}"/>
                                        <p:outputLabel value="#{gnxKeyStoreHandler.getKeystoreEntrySerialNumber()}"/>
                                        <p:outputLabel value="#{labels.ks_entry_notbefore}"/>
                                        <p:outputLabel
                                                value="#{gnxKeyStoreHandler.getKeystoreEntryNotBefore()}"><f:convertDateTime
                                                pattern="dd/MM/yyyy HH:mm"
                                                timeZone="#{secSessionHandler.timeZone}"/></p:outputLabel>
                                        <p:outputLabel value="#{labels.ks_entry_notafter}"/>
                                        <p:outputLabel
                                                value="#{gnxKeyStoreHandler.getKeystoreEntryNotAfter()}"><f:convertDateTime
                                                pattern="dd/MM/yyyy HH:mm"
                                                timeZone="#{secSessionHandler.timeZone}"/></p:outputLabel>
                                        <p:outputLabel value="#{labels.ks_entry_signaturealgoname}"/>
                                        <p:outputLabel
                                                value="#{gnxKeyStoreHandler.getKeystoreEntrySignatureAlgoName()}"/>
                                        <p:outputLabel value="#{labels.ks_entry_fingerprintmd5}"/>
                                        <p:outputLabel
                                                value="#{gnxKeyStoreHandler.getKeystoreEntryFingerPrint('MD5')}"/>
                                        <p:outputLabel value="#{labels.ks_entry_fingerprintsha1}"/>
                                        <p:outputLabel
                                                value="#{gnxKeyStoreHandler.getKeystoreEntryFingerPrint('SHA-1')}"/>
                                        <!-- TODO EXTENSIONS -->
                                    </p:panelGrid>
                                </p:tab>
                                <p:tab id="publicKeyTab">
                                    <f:facet name="title">
                                        <h:panelGroup styleClass="fa fa-key fa-fw"/>
                                        <h:outputText value="#{labels.ks_publickeytab}"/>
                                    </f:facet>
                                    <p:panelGrid columns="1" style="margin-bottom:10px;width:550px;height:450px">
                                        <h:outputText value="-----BEGIN CERTIFICATE-----"/>
                                        <h:outputText value="#{gnxKeyStoreHandler.getKeystoreEntryPublicKey()}"/>
                                        <h:outputText value="-----END CERTIFICATE-----"/>
                                    </p:panelGrid>
                                </p:tab>
                            </p:tabView>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:tab>
                <!-- ** TRANSFORMATION ** -->
                <p:tab id="cfgTrfTab" title="#{labels.templates}"
                       rendered="#{gnxOrganizationDialogHandler.configurations['gnxTransformService']!=null and gnxSessionHandler.administrator}">
                    <p:messages id="cfgTrfMessages" showDetail="false" autoUpdate="true" closable="true"
                                escape="false"/>
                    <p:outputPanel styleClass="psCfgTrfPanel">
                        <!-- TRANSFORMATION -->
                        <p:outputPanel id="cfgTrf"
                                       rendered="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation!=null}">
                            <!-- Collection -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label" for="cfgTrfCol"
                                               value="#{edocsrvlbls.document_collection}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:autoComplete id="cfgTrfCol"
                                                    value="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation.collection}"
                                                    completeMethod="#{gnxEDocumentAppHandler.onCompleteCollections}"
                                                    var="col" itemLabel="#{col}" itemValue="#{col}"
                                                    groupBy="#{gnxEDocumentAppHandler.getCollectionGroup(col)}"
                                                    dropdown="true" cache="true" forceSelection="false"
                                                    inputStyle="width:305px;"
                                                    scrollHeight="250"/>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Document Type -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label" for="cfgTrfType"
                                               value="#{edocsrvlbls.document_kind}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:autoComplete id="cfgTrfType"
                                                    value="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation.documentType}"
                                                    completeMethod="#{gnxEDocumentAppHandler.onCompleteDocumentTypes}"
                                                    var="doctype" itemLabel="#{doctype}" itemValue="#{doctype}"
                                                    groupBy="#{gnxEDocumentAppHandler.getDocumentTypeGroup(doctype)}"
                                                    dropdown="true" cache="true" forceSelection="false"
                                                    inputStyle="width:305px;"
                                                    scrollHeight="250"/>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Document Sub-Type -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label" for="cfgTrfSubType"
                                               value="#{edocsrvlbls.document_subtype}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:autoComplete id="cfgTrfSubType"
                                                    value="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation.documentSubType}"
                                                    completeMethod="#{gnxEDocumentAppHandler.onCompleteDocumentSubTypes}"
                                                    var="docsubtype" itemLabel="#{docsubtype}" itemValue="#{docsubtype}"
                                                    dropdown="true" forceSelection="false" inputStyle="width:305px;"
                                                    scrollHeight="250"/>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Output -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label" for="cfgTrfOutput"
                                               value="#{labels.format}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:selectOneMenu id="cfgTrfOutput"
                                                     value="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation.output}"
                                                     style="width:335px;">
                                        <f:selectItem itemValue="#{null}" itemLabel="#{labels.trf_generic}"/>
                                        <f:selectItems value="#{gnxTransformAppHandler.birtOutputFileTypes}"/>
                                    </p:selectOneMenu>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Template -->
                            <p:outputPanel styleClass="form-group">
                                <p:outputLabel styleClass="col-sm-3 control-label" for="cfgTrfTemplate"
                                               value="#{labels.template}"/>
                                <p:outputPanel styleClass="col-sm-9">
                                    <p:selectOneMenu id="cfgTrfTemplate"
                                                     value="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation.templateUri}"
                                                     style="width:335px;" required="true">
                                        <f:selectItems
                                                value="#{gnxTransformAppHandler.getTemplateItems(gnxOrganizationDialogHandler.company, null, 'BIRT', null, true, true)}"/>
                                    </p:selectOneMenu>
                                </p:outputPanel>
                            </p:outputPanel>
                            <!-- Actions -->
                            <p:panelGrid columns="1" columnClasses="right">
                                <h:panelGroup>
                                    <p:commandButton icon="ui-icon-check" value="OK"
                                                     actionListener="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.onAddTransformation()}"
                                                     process="@this cfgTrf" update="@(.psCfgTrfPanel)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                                    <p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" immediate="true"
                                                     process="@this" update="@(.psCfgTrfPanel)" styleClass="mls"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation}"/>
                                    </p:commandButton>
                                </h:panelGroup>
                            </p:panelGrid>
                        </p:outputPanel>
                        <!-- TRANSFORMATIONS -->
                        <p:dataTable id="cfgTrfs"
                                     value="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformations}"
                                     var="trf"
                                     emptyMessage="#{labels.no_records_found}"
                                     rendered="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.transformation==null}">
                            <f:facet name="header">
                                <h:panelGrid columns="1" columnClasses="right">
                                    <p:commandButton value="#{labels.add}" icon="ui-icon-plusthick"
                                                     actionListener="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.onNewTransformation()}"
                                                     process="@this" update="@(.psCfgTrfPanel)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                                </h:panelGrid>
                            </f:facet>
                            <p:column headerText="#{edocsrvlbls.document_collection}" sortBy="#{trf.collection}"
                                      style="width:20%;" styleClass="nowrap">
                                <h:outputText value="#{trf.collection}" title="#{trf.templateUri}"/>
                            </p:column>
                            <p:column headerText="#{edocsrvlbls.document_kind}" sortBy="#{trf.documentType}"
                                      style="width:20%;" styleClass="nowrap">
                                <h:outputText value="#{trf.documentType}" title="#{trf.templateUri}"/>
                            </p:column>
                            <p:column headerText="#{edocsrvlbls.document_subtype}" sortBy="#{trf.documentSubType}"
                                      style="width:20%;" styleClass="nowrap">
                                <h:outputText value="#{trf.documentSubType}" title="#{trf.templateUri}"/>
                            </p:column>
                            <p:column headerText="#{labels.format}" sortBy="#{trf.output}" style="width:10%;"
                                      styleClass="nowrap center">
                                <h:outputText value="#{trf.output}" title="#{trf.templateUri}"/>
                            </p:column>
                            <p:column headerText=" " styleClass="right" style="width:120px">
                                <p:commandButton icon="ui-icon-pencil"
                                                 actionListener="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.onSelectTransformation(trf)}"
                                                 process="@this" update="@(.psCfgTrfPanel)"
                                                 styleClass="mls"
                                                 rendered="#{gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                                <p:commandButton icon="ui-icon-trash"
                                                 actionListener="#{gnxOrganizationDialogHandler.configurations.gnxTransformService.onRemoveTransformation(trf)}"
                                                 process="@this" update="cfgTrfs"
                                                 onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
                                                 styleClass="mls"
                                                 rendered="#{gnxSessionHandler.isGranted('Security', 'DELETE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                            </p:column>
                        </p:dataTable>
                    </p:outputPanel>
                </p:tab>
                <!-- ** CHANNELS ** -->
                <p:tab id="cfgRtgTab" title="#{labels.channels}"
                       rendered="#{gnxOrganizationDialogHandler.configurations['gnxRoutingService']!=null and gnxSessionHandler.administrator}">
                    <p:outputPanel styleClass="psCfgRtgPanel">
                        <p:messages id="cfgEpcMessages" showDetail="false" autoUpdate="true" closable="true"
                                    escape="false"/>
                        <!-- CHANNELS -->
                        <p:dataTable id="cfgEpcs"
                                     value="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoints}"
                                     var="epc"
                                     emptyMessage="#{labels.no_records_found}" sortBy="#{epc.name}"
                                     rendered="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint==null}">
                            <f:facet name="header">
                                <h:panelGrid columns="1">
                                    <p:panelGrid columns="2" style="width:auto;float:right">
                                        <p:selectOneMenu id="cfgEpcType"
                                                         value="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpointType}"
                                                         converter="classConverter" disabled="#{cc.attrs.readonly}"
                                                         rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                            <f:selectItems
                                                    value="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpointTypes}"
                                                    var="epct" itemValue="#{epct}"
                                                    itemLabel="#{labels['channel_'.concat(epct.simpleName)]}"/>
                                        </p:selectOneMenu>
                                        <p:commandButton value="#{labels.add}" icon="ui-icon-plusthick"
                                                         actionListener="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.onNewEndpoint}"
                                                         process="@this cfgEpcType" update="@(.psCfgRtgPanel)"
                                                         styleClass="mls" style="margin-top: -4px;"
                                                         disabled="#{cc.attrs.readonly}"
                                                         rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"
                                                         resetValues="true">
                                        </p:commandButton>
                                    </p:panelGrid>
                                </h:panelGrid>
                            </f:facet>
                            <p:column headerText="#{labels.name}" sortBy="#{epc.name}" styleClass="bold">
                                <h:outputText value="#{epc.name}"/>
                            </p:column>
                            <p:column headerText="#{labels.type}" sortBy="#{epc.class.simpleName}">
                                <h:outputText value="#{labels['channel_'.concat(epc.class.simpleName)]}"/>
                            </p:column>
                            <p:column headerText="#{labels.subtype}" sortBy="#{epc.authentication}">
                                <h:outputText value="#{epc.authentication}"/>
                            </p:column>
                            <p:column headerText="#{labels.description}" sortBy="#{epc.description}">
                                <h:outputText value="#{epc.description}"/>
                            </p:column>
                            <p:column headerText=" " styleClass="right">
                                <p:selectBooleanButton value="#{epc.global}" onIcon="fa fa-link"
                                                       offIcon="fa fa-chain-broken" onLabel="shared" offLabel="private"
                                                       disabled="#{epc.shortType == 'alias'}"
                                                       style="visibility: #{epc.shortType == 'alias' ? 'hidden' : 'inherit'}"
                                                       styleClass="ui-button-icon-only mls"
                                                       rendered="#{gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                    <p:ajax event="change" listener="#{gnxEndpointAppHandler.onGlobalToggled(epc)}"
                                            update="@this"/>
                                </p:selectBooleanButton>
                                <p:commandButton icon="ui-icon-pencil"
                                                 actionListener="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.onSelectEndpoint(epc)}"
                                                 process="@this" update="@(.psCfgRtgPanel)"
                                                 styleClass="mls"
                                                 rendered="#{gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"
                                                 resetValues="true"/>
                                <p:commandButton icon="ui-icon-trash"
                                                 actionListener="#{gnxEndpointAppHandler.onRemoveEndpoint(gnxOrganizationDialogHandler.configurations.gnxRoutingService, epc)}"
                                                 process="@this" update="cfgEpcs"
                                                 onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }"
                                                 styleClass="mls"
                                                 rendered="#{gnxSessionHandler.isGranted('Security', 'DELETE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"/>
                            </p:column>
                        </p:dataTable>
                        <!-- CHANNEL -->
                        <p:outputPanel id="cfgEpc"
                                       rendered="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint!=null}">
                            <!-- header -->
                            <p:panelGrid columns="2" columnClasses="left,right" style="margin-bottom:10px;">
                                <h:outputText
                                        value="#{labels['channel_'.concat(gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint.class.simpleName)]}"
                                        styleClass="title2"/>
                                <h:panelGroup>
                                    <p:commandButton icon="ui-icon-check" value="#{labels.ok}"
                                                     actionListener="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.onAddEndpoint()}"
                                                     process="@this cfgEpc" update="@(.psCfgRtgPanel)"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"
                                                     disabled="#{!gnxOrganizationDialogHandler.canUpsertEndpoint(gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint)}"/>
                                    <p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" immediate="true"
                                                     process="@this" update="@(.psCfgRtgPanel)" styleClass="mls"
                                                     rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                        <f:setPropertyActionListener value="#{null}"
                                                                     target="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint}"/>
                                    </p:commandButton>
                                </h:panelGroup>
                            </p:panelGrid>
                            <h:panelGroup layout="block"
                                          styleClass="ui-messages ui-messages-info ui-corner-all"
                                          rendered="#{!gnxOrganizationDialogHandler.canUpsertEndpoint(gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint)}">
                                <span class="ui-messages-warn-icon"></span>
                                <ul>
                                    <li><span class="ui-messages-warn-summary">
                                #{labels['channel_'.concat(gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint.shortType).concat('_only_one_endpoint_per_organization')]}
                                </span></li>
                                </ul>
                            </h:panelGroup>
                            <p:outputPanel
                                    rendered="#{gnxOrganizationDialogHandler.canUpsertEndpoint(gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint)}">
                                <!-- name -->
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-3 control-label" for="cfgEpcName"
                                                   value="#{labels.name}"/>
                                    <p:outputPanel styleClass="col-sm-9">
                                        <p:inputText id="cfgEpcName"
                                                     value="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint.name}"
                                                     styleClass="form-control"
                                                     required="true"
                                                     readonly="#{not empty gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint.name}">
                                            <f:validator validatorId="nameUnicityEndpointValidator"/>
                                            <f:validator validatorId="xssValidator" />
                                        </p:inputText>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <!-- description -->
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-3 control-label" for="cfgEpcDesc"
                                                   value="#{labels.description}"/>
                                    <p:outputPanel styleClass="col-sm-9">
                                        <p:inputTextarea id="cfgEpcDesc"
                                                         value="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint.description}"
                                                         validator="xssValidator"
                                                         styleClass="form-control" rows="2"/>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <!-- specifics -->
                                <grt:channelAuthentication
                                        value="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint}"
                                        organization="#{gnxOrganizationDialogHandler.organization}"
                                        rendered="#{gnxOrganizationDialogHandler.configurations.gnxRoutingService.endpoint.class.simpleName=='AuthenticationEndpointConfiguration'}"/>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:tab>
                <!-- ** HISTORY ** -->
                <p:tab id="cfgHistofTab" title="#{labels.history}"
                       rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('History', 'READ')}">
                    <p:outputPanel styleClass="psCfgHistoPanel">
                        <!-- HISTORY -->
                        <p:outputPanel id="cfgHisto">
                            <gnx:auditRevisions entity="#{cc.attrs.value}"/>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:tab>
                <!-- ** CLASSIFICATION PLAN ** -->
                <p:tab id="classificationPlanTab" title="#{labels.classificationPlan}"
                       rendered="#{gnxSessionHandler.administrator}">
                    <p:growl id="msgs"/>
                    <p:outputPanel styleClass="ClassificationPlanCfgPnl">
                        <p:messages id="cfgClassificationPlanMessages" showDetail="false" autoUpdate="true"
                                    closable="true"/>
                        <!-- Edit Classification Plan Menu -->
                        <p:outputPanel id="cfgAuditParams" rendered="#{auditParametersHandler.creatingOrEditing}">
                            <h:panelGrid columns="1">
                                <p:panelGrid columns="2" columnClasses="left,right"
                                             style="width:auto; float:right; margin-bottom:10px;">
                                    <h:panelGroup>
                                        <p:commandButton id="btn_add" icon="ui-icon-check" value="OK"
                                                         update="@(.ClassificationPlanTable) msgs @parent"
                                                         action="#{auditParametersHandler.reinit}"
                                                         rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}">
                                            <p:collector value="#{auditParametersHandler.auditParam}"
                                                         addTo="#{auditParametersHandler.auditParams}" unique="true"/>
                                        </p:commandButton>
                                        <p:commandButton id="previousButtonId" icon="ui-icon-seek-prev"
                                                         value="#{labels.back}" immediate="true" process="@this"
                                                         update="@(.ClassificationPlanCfgPnl)" styleClass="mls"
                                                         rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'UPDATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"
                                                         actionListener="#{auditParametersHandler.stopCreateOrEditClassificationParams}">
                                        </p:commandButton>
                                    </h:panelGroup>
                                </p:panelGrid>
                            </h:panelGrid>
                            <p:panel header="Plan de classement" style="margin-bottom:20px">
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-2 control-label" value="#{labels.label}"
                                                   for="label"/>
                                    <p:outputPanel styleClass="col-sm-8">
                                        <p:inputText id="label" value="#{auditParametersHandler.auditParam.label}"
                                        			 validator="xssValidator"
                                                     required="true" styleClass="form-control"/>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-2 control-label" value="#{labels.xPath}"
                                                   for="xPath"/>
                                    <p:outputPanel styleClass="col-sm-8">
                                        <p:inputText id="xPath" value="#{auditParametersHandler.auditParam.xPath}"
                                        			 validator="xssValidator"
                                                     required="true" styleClass="form-control"/>
                                    </p:outputPanel>
                                </p:outputPanel>
                                <p:outputPanel styleClass="form-group">
                                    <p:outputLabel styleClass="col-sm-2 control-label" value="#{labels.language}"
                                                   for="classifLanguage"/>
                                    <p:outputPanel styleClass="col-sm-8">
                                        <p:selectOneMenu id="classifLanguage"
                                                         value="#{auditParametersHandler.auditParam.locale}"
                                                         converter="localeConverter" filter="true" required="true"
                                                         styleClass="form-control">
                                            <f:selectItem noSelectionOption="true" itemValue="" itemLabel=""/>
                                            <f:selectItems value="#{comApplicationHandler.languageItems}"/>
                                        </p:selectOneMenu>
                                    </p:outputPanel>
                                </p:outputPanel>
                            </p:panel>
                        </p:outputPanel>
                        <!-- ResultTable -->
                        <p:dataTable id="cfgClassificationPlan" value="#{auditParametersHandler.auditParams}"
                                     var="adtparams" styleClass="ClassificationPlanTable"
                                     emptyMessage="#{labels.no_records_found}" sortBy="#{adtparams.label}">
                            <f:facet name="header">
                                <h:panelGrid columns="1">
                                    <p:panelGrid columns="2" style="width:auto; float:right"
                                                 rendered="#{not auditParametersHandler.creatingOrEditing}">
                                        <p:autoComplete id="classificationPlanEDocumentType"
                                                        value="#{auditParametersHandler.eDocumentKindSelected}"
                                                        completeMethod="#{gnxEDocumentAppHandler.onCompleteDocumentTypes}"
                                                        var="doctype" itemLabel="#{doctype}" itemValue="#{doctype}"
                                                        groupBy="#{gnxEDocumentAppHandler.getDocumentTypeGroup(doctype)}"
                                                        dropdown="true" cache="true" forceSelection="false"
                                                        inputStyle="width:305px;"
                                                        scrollHeight="250">
                                            <p:ajax event="itemSelect"
                                                    update="ptrForm-company-partnerInfoTabView-cfgClassificationPlan"
                                                    process="@this"/>
                                        </p:autoComplete>
                                        <p:commandButton value="#{labels.add}" icon="ui-icon-plusthick"
                                                         actionListener="#{auditParametersHandler.startCreateOrEditClassificationParams}"
                                                         process="@this classificationPlanEDocumentType"
                                                         update="@(.ClassificationPlanCfgPnl)" styleClass="mls"
                                                         style="margin-top: -4px;"
                                                         disabled="#{cc.attrs.readonly or not empty kind}"
                                                         rendered="#{gnxSessionHandler.isGranted('Security', 'CREATE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"
                                                         resetValues="true">
                                        </p:commandButton>
                                    </p:panelGrid>
                                </h:panelGrid>
                            </f:facet>
                            <p:column headerText="#{labels.label}" sortBy="#{adtparams.label}" styleClass="bold">
                                <h:outputText value="#{adtparams.label}"/>
                            </p:column>
                            <p:column headerText="#{labels.xPath}" sortBy="#{adtparams.xPath}" styleClass="bold">
                                <h:outputText value="#{adtparams.xPath}"/>
                            </p:column>
                            <p:column headerText="#{labels.language}" styleClass="bold">
                                <h:outputText value="#{adtparams.locale}"/>
                            </p:column>
                            <p:column headerText=" " styleClass="right" style="width:120px">
                                <p:commandButton icon="ui-icon-trash" styleClass="mls"
                                                 rendered="#{gnxSessionHandler.isGranted('Security', 'DELETE') or gnxSessionHandler.isGranted('Security', 'ADMIN')}"
                                                 update="@(.ClassificationPlanTable)"
                                                 process="@(.ClassificationPlanTable)">
                                    <p:collector value="#{adtparams}" removeFrom="#{auditParametersHandler.auditParams}"
                                                 unique="true"/>
                                </p:commandButton>
                            </p:column>
                        </p:dataTable>
                    </p:outputPanel>
                </p:tab>
                <!-- ** LOGISTIC ** -->
                <p:tab id="cfgssccofTab" title="#{labels.logistic}"
                       rendered="#{ (gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Logistic', 'READ')) and not cc.attrs.partner}">

                    <p:messages id="ssccTabMessages" showDetail="false" autoUpdate="true" closable="true"
                                escape="false"/>

                    <p:fieldset id="ssccPanel" styleClass="mbs" legend="#{labels.logistic_sscc_fieldset}"
                                toggleable="true" toggleSpeed="500">

                        <!-- COMPANY SETTING -->

                        <!-- SSCC AUTO GENERATION -->
                        <p:outputPanel styleClass="form-group">
                            <p:outputLabel styleClass="col-sm-4 control-label" for="autoGenerationOfSSCC"
                                           value="#{labels.logistic_sscc_auto}"/>
                            <p:outputPanel styleClass="col-sm-1" id="autogenSsccPanelId">
                                <p:selectBooleanCheckbox id="autoGenerationOfSSCC"
                                                         value="#{gnxOrganizationDialogHandler.company.autoGenerationOfSSCC}"
                                                         styleClass="form-control"
                                                         disabled="#{!(gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Logistic', 'UPDATE'))}">

                                    <p:ajax event="change"
                                            update="gs1 extension serialReference ssccNumber autogenSsccPanelId"/>
                                </p:selectBooleanCheckbox>
                            </p:outputPanel>
                        </p:outputPanel>
                        <!-- GS1 -->
                        <p:outputPanel styleClass="form-group"
                                       rendered="#{gnxOrganizationDialogHandler.canDefinedVisibility()}">
                            <p:outputLabel styleClass="col-sm-4 control-label" for="gs1"
                                           value="#{labels.logistic_gs1_company_prefix}"/>
                            <p:outputPanel styleClass="col-sm-7">
                                <p:inputText
                                        id="gs1"
                                        value="#{gnxOrganizationDialogHandler.company.gs1}"
                                        disabled="#{!(gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Logistic', 'UPDATE')) or (!gnxOrganizationDialogHandler.company.autoGenerationOfSSCC)}"
                                        required="#{gnxOrganizationDialogHandler.company.autoGenerationOfSSCC}"
                                        maxlength="10"
                                        validatorMessage="#{labels.logistic_gs1_error_format_message}"
                                        styleClass="form-control"
                                        style="width:270px">
                                    <p:ajax event="blur" listener="#{gnxOrganizationDialogHandler.generateSSCC()}"
                                            update="ssccNumber serialReference"/>
                                    <f:validateRegex pattern="[0-9]{7,10}"/>
                                </p:inputText>
                            </p:outputPanel>
                        </p:outputPanel>

                        <!-- EXTENSION -->
                        <p:outputPanel styleClass="form-group">
                            <p:outputLabel styleClass="col-sm-4 control-label" for="extension"
                                           value="#{labels.logistic_extension}"/>
                            <p:outputPanel styleClass="col-sm-7">
                                <p:selectOneMenu
                                        id="extension"
                                        value="#{gnxOrganizationDialogHandler.company.extension}"
                                        disabled="#{!(gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Logistic', 'UPDATE')) or (!gnxOrganizationDialogHandler.company.autoGenerationOfSSCC)}"
                                        required="#{gnxOrganizationDialogHandler.company.autoGenerationOfSSCC}"
                                        style="width:270px">
                                    <p:ajax event="change" listener="#{gnxOrganizationDialogHandler.generateSSCC()}"
                                            update="ssccNumber"/>
                                    <f:selectItem itemValue="#{null}" itemLabel=""/>
                                    <f:selectItems value="#{gnxOrganizationDialogHandler.possibleExtensions()}"/>
                                </p:selectOneMenu>
                            </p:outputPanel>
                        </p:outputPanel>

                        <!-- SERIAL REFERENCE -->
                        <p:outputPanel styleClass="form-group">
                            <p:outputLabel styleClass="col-sm-4 control-label" for="serialReference"
                                           value="#{labels.logistic_serial_reference}"/>
                            <p:outputPanel styleClass="col-sm-7">
                                <p:inputText
                                        id="serialReference"
                                        value="#{gnxOrganizationDialogHandler.company.serialReference}"
                                        disabled="#{!(gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Logistic', 'UPDATE')) or (!gnxOrganizationDialogHandler.company.autoGenerationOfSSCC)}"
                                        maxlength="9"
                                        required="#{gnxOrganizationDialogHandler.company.autoGenerationOfSSCC}"
                                        styleClass="form-control"
                                        style="width:270px"
                                        validatorMessage="#{labels.logistic_serial_reference_error_format_message}"
                                        converter="#{partnerSerialReferenceConverter}">
                                    <p:ajax event="blur" listener="#{gnxOrganizationDialogHandler.generateSSCC()}"
                                            update="ssccNumber @this"/>
                                    <f:validateRegex pattern="[0-9]{6,9}"/>
                                </p:inputText>
                            </p:outputPanel>
                        </p:outputPanel>

                        <!-- SSCC NUMBER - READ ONLY -->
                        <p:outputPanel styleClass="form-group">
                            <p:outputLabel styleClass="col-sm-4 control-label" for="ssccNumber"
                                           value="#{labels.logistic_sscc}"/>
                            <p:outputPanel styleClass="col-sm-7">
                                <p:inputText
                                        id="ssccNumber"
                                        value="#{gnxOrganizationDialogHandler.sscc}"
                                        validator="xssValidator"
                                        readonly="true"
                                        styleClass="form-control"
                                        style="width:270px"/>
                            </p:outputPanel>
                        </p:outputPanel>
                    </p:fieldset>
                </p:tab>
                <!-- ** BANKING PARTNER ** -->
                <p:tab id="bankingPartnerTab" title="#{labels.banking}"
                       rendered="#{gnxSessionHandler.administrator or gnxSessionHandler.isGranted('Banking_Partner', 'READ')}">
                    <p:outputPanel styleClass="psBankingPartnerPanel">
                        <p:outputPanel id="bankingPartner">
                            <gnx:bankingPartner value="#{cc.attrs.value}" partner="#{cc.attrs.partner}"/>
                        </p:outputPanel>
                    </p:outputPanel>
                </p:tab>
            </p:tabView>
        </h:panelGroup>
    </cc:implementation>
</ui:component>