error_fax_number_format_country_other = Het faxnummer moet met \"00\" beginnen en moet uit tenminste 5 tekens bestaan
error_failed_to_rename_resource = Her<PERSON><PERSON><PERSON> van bron is mislukt
error_start_date_after_end_date = Begindatum moet voor de einddatum zijn
exception_more_templates = Meer dan een ({0}) sjabloon gevonden in {1}
info_template_saved = Sjabloon {0} opgeslagen
exception_instance_not_specified = Omgeving van melding moet worden gespecificeerd
info_message_saved = Melding opgeslagen
confirm_sending = Weet u zeker dat u deze melding per e-mail aan alle ontvangers wilt sturen?
exception_invalid_xpath_type = [{0}] is geen tekenreeks
channel_named_already_used = Deze naam {0} duidt reeds een kanaal aan
error_message_content_length = Inhoud van de melding te hoog
info_message_enabled = Melding geactiveerd
general = Algemeen
exception_sending_mail = Uitzondering bij verzendien van mail(s) : {0}
error_could_not_create_tree = Kon boom niet aanmaken
info_message_deleted = Melding gewist
info_message_disabled = Melding gedeactiveerd
report_sent_date = Zenddatum: {0}
report_mail_nok = Niet in staat om mail te zenden aan {0}
exception_create_message = Onmogelijk om melding aan te maken ({0})
info_rte_disabled = De applicatie vergt een definitie voor de RTE_HOME eigenschap. Neem contact op met een administrator.
error_saving_message = Fout bij opslaan van melding ({0})
error_sending_message = Fout bij verzenden van melding ({0})
error_fax_number_format_country_fr = Een Frans faxnummer moet beginnen met \"0\" en uit 10 tekens bestaan
error_file_already_exists = Onmogelijke werking, deze naam is reeds in gebruik in de map.
error_no_writable_folder_selected = Geen beschrijfbare map geselecteerd
error_toggling_message = Fout bij omschakelen van melding ({0})
report_subject = Onderwerp : {0}
no_records_found = Geen records gevonden
report_sent_number = {0} mail(s) verzonden
error_deleting_template = Fout bij wissen van sjabloon ({0})
error_writing_file_content = Fout bij schrijven van de bestandsinhoud
label = Label
report_reply_to = Antwoord aan : {0}
exception_extracting_template = IO-uitzondering bij decomprimeren van sjabloon ({0})
error_saving_template = Fout bij opslaan van sjabloon ({0})
info_html_message_content = Sorry, u heeft ondersteuning HTML meldingsinhoud nodig om deze melding te lzen.
exception_no_template = Geen sjabloon gevonden in {0}
msg_period_validity = Begindatum moet voor de einddatum zijn
error_deleting_message = Fout bij wissen van melding ({0})
error_templating_message = Fout bij sjabloon van melding ({0})
exception_invalid_xpath_format = [{0}] is geen geldige XPath-uitdrukking: {1}
info_template_deleted = Sjabloon gewist
report_mail_ok = Mail met succes verzonden aan {0}
send = Verzenddatum
exception_template_owner = Eigenaar van sjabloon moet worden gespecificeerd
confirm_template = Het gebruik van de sjabloon vervangt het bestaande onderwerp en de hoofdtekst van deze melding. Bent u zeker dat u door wilt gaan?
exception_emitter_not_specified = De verzender van de melding moet worden gespecificeerd
error_processing_message = Fout bij de verwerking van het bericht ({0})
info_message_processed = Bericht succesvol verwerkt
validate_regex_login = Login dient tenminste 3 alfanummerieke, '_', '@' en '.' tekens te bevatten
validate_regex_orderEmail = Ongeldig mailformaat
validate_regex_email = Ongeldig mailformaat

#ARCHAVING
archmail_subject = Download link
archmail_greeting=Hallo {0},<br/><br/>
success_content=download je bestand op dit <a href=\"{0}\">adres</a>.Deze link verloopt over 48 uur.
failed_content=Het genereren van bestanden is mislukt. Neem contact op met uw beheerder.
archmail_regards=<br/><br/>Met vriendelijke groeten.
validate_regex_deliveryTime = Levertijd moet een getal zijn
validate_regex_link_identification = Linkidentificatie moet alfanumerieke tekens bevatten.
validate_regex_maximumAfternoonMonday = Het maximale maandagmiddaguur moet tussen 12:00 en 24:00 uur liggen.
validate_regex_maximumMorningSunday = Het maximale zondagochtenduur moet tussen 00:00 en 12:00 uur liggen.
validate_regex_maximumAfternoonThursday = Het maximale uur op donderdagmiddag moet tussen 12:00 en 24:00 uur liggen.
validate_regex_minimumMorningMonday = Minimaal maandagochtend tussen 00:00 en 12:00 uur.
contextual_validation_address = Bedrijfscode {0} komt niet overeen met {1} voor adres {2}
validate_regex_minimumAfternoonSunday = Minimaal zondagmiddaguur moet tussen 12:00 en 24:00 liggen
validate_regex_maxAmount = Het maximumbedrag moet een getal zijn
validate_regex_minimumMorningWednesday = Minimaal woensdagochtend tussen 00:00 en 12:00 uur.
formPageCancel = Annuleren
validate_regex_minimumMorningFriday = Minimaal vrijdagochtend tussen 00:00 en 12:00 uur.
error_failed_github_service = Github-service is niet beschikbaar.  Neem contact op met uw beheerder
contextual_validation_id_invalid = Ongeldig adres id {0}
validate_regex_maxWeight = Maximumgewicht moet een getal zijn
validate_regex_minimumAfternoonFriday = Minimaal vrijdagmiddaguur moet tussen 12:00 en 24:00 liggen
contextual_validation_address_subtype = Ongeldig subtype adres
validate_regex_vatCode = De btw-code moet alfanumerieke tekens bevatten.
validate_regex_maximumAfternoonFriday = Het maximale vrijdagmiddaguur moet tussen 12:00 en 24:00 uur liggen.
validate_regex_maximumAfternoonWednesday = Het maximale uur op woensdagmiddag moet tussen 12:00 en 24:00 uur liggen.
validate_regex_maximumMorningFriday = Het maximale uur op vrijdagochtend moet tussen 00:00 en 12:00 uur liggen.
validate_regex_maximumAfternoonTuesday = Maximaal dinsdagmiddaguur tussen 12:00 en 24:00 uur
validate_regex_currency = Valuta is niet geldig
validate_regex_countryString = De landcode is onbekend.
validate_regex_minimumAfternoonSaturday = Het minimumuur op zaterdagmiddag moet tussen 12:00 en 24:00 uur liggen.
contextual_validation_address_type = Ongeldig type adres
validate_regex_comment = De enige geaccepteerde waarden zijn \"ja\" en \"nee\".
validate_regex_tel = Telefoon mag alleen cijfers en '+' bevatten
validate_regex_partnerCode = De partneridentificatie moet bestaan en mag maximaal 123 alfanumerieke tekens bevatten.
validate_regex_carriageCost = Verzendkosten moeten een getal zijn
validate_regex_minVolume = Het minimumvolume moet een getal zijn
validate_regex_maximumMorningTuesday = Het maximale uur op dinsdagochtend is tussen 00:00 en 12:00 uur.
library = Bibliotheek
validate_regex_maximumAfternoonSunday = Het maximale zondagmiddaguur moet tussen 12:00 en 24:00 uur liggen.
validate_regex_maxUnits = Maximale eenheden moeten een getal zijn
validate_regex_bic = Bic moet minstens 8 tekens hebben
edit_email = E-mail van {0} op {1}
file_not_downloaded = Dit bestand is niet gedownload
validate_regex_minUnits = Minimale eenheden moeten een getal zijn
info_success_import_github_display_message = De bestanden zijn ge\u00EFmporteerd
validate_regex_iban = Iban is niet geldig
validate_regex_minParcel = Minimaal perceel moet een nummer zijn
announcement_content = Inhoud aankondiging
validate_regex_maximumMorningSaturday = Het maximale uur op zaterdagochtend is tussen 00:00 en 12:00 uur.
validate_regex_minWeight = Het minimumgewicht moet een getal zijn
error_invalid_status_code = De statuscode mag alleen karakters, cijfers en _ bevatten.
error_failed_get_github_files = Kan de laatste versie van de bibliotheek niet laden. Probeer het later nog eens of neem contact op met uw beheerder
error_failed_list_github_files = Mislukt tijdens het importeren van bibliotheekbestanden. Probeer het later nog eens of neem contact op met uw beheerder
validate_regex_maximumAfternoonSaturday = Het maximale uur op zaterdagmiddag moet tussen 12:00 en 24:00 uur liggen.
validate_regex_minimumAfternoonTuesday = Minimaal dinsdagmiddaguur moet tussen 12:00 en 24:00 liggen
email_content = Inhoud e-mail
validate_regex_minimumAfternoonThursday = Het minimumuur op donderdagmiddag moet tussen 12:00 en 24:00 uur liggen.
validate_regex_minAmount = Het minimumbedrag moet een getal zijn
error_start_hour_after_end_hour = Het beginuur moet voor het einduur liggen voor de toegestane periodes
validate_regex_city = De stad moet alfanumerieke tekens bevatten.
validate_regex_maxPallets = Het maximum aantal pallets moet een getal zijn
validate_addresses_partner_code = Het verwerkte adres en het gekoppelde adres moeten dezelfde partnercode hebben.
validate_regex_codeAl = De secundaire id moet alfanumerieke tekens '_' bevatten.
validate_regex_maximumMorningWednesday = Het maximale uur op woensdagochtend is tussen 00:00 en 12:00 uur.
validate_regex_postalCode = De postcode moet alfanumerieke tekens bevatten.
error_failed_pull_github_files = Mislukt tijdens het importeren. Probeer het later nog eens of neem contact op met uw beheerder
validate_regex_siret = Siret moet 14 alfanumerieke tekens bevatten.
create_email = Nieuwe e-mail
validate_regex_minimumMorningTuesday = Minimaal dinsdagochtend tussen 00:00 en 12:00 uur.
email_subject = Onderwerp e-mail
validate_regex_franco = Minimum voor gratis verzending moet een getal zijn
validate_regex_location_address_country = De landcode is onbekend.
announcement_subject = Onderwerp aankondiging
validate_regex_minimumAfternoonWednesday = Minimaal woensdagmiddaguur moet tussen 12:00 en 24:00 liggen
validate_link_address_type = {0} Het adrestype kan geen adreslink hebben.
validate_regex_minimumMorningThursday = Minimaal donderdagochtend tussen 00:00 en 12:00 uur.
contextual_validation_address_duplicate_id = Adres {0} bestaat al in de importlijst
validate_regex_minimumMorningSunday = Minimaal zondagochtend tussen 00:00 en 12:00 uur.
validate_regex_maximumMorningMonday = Het maximale uur op maandagochtend is tussen 00:00 en 12:00 uur.
info_success_import_github_files = Het bestand {0} is goed ge\u00EFmporteerd
validate_regex_minimumAfternoonMonday = Minimaal maandagmiddaguur moet tussen 12:00 en 24:00 liggen
validate_regex_disabled = De enige geaccepteerde waarden zijn \"ja\" en \"nee\".
validate_regex_maxVolume = Het maximale volume moet een getal zijn
formPageOk = Ok
validate_regex_minPallets = Minimale pallets moeten een aantal zijn
validate_regex_maximumMorningThursday = Het maximale uur op donderdagochtend is tussen 00:00 en 12:00 uur.
validate_regex_street = Straat moet alfanumerieke tekens bevatten.
validate_regex_locale = De locale code is onbekend.
validate_regex_identification = Identificatie moet alfanumerieke, '_'-tekens bevatten.
validate_regex_maxParcel = Het maximale perceel moet een getal zijn
validate_regex_rcs = Rcs moeten alfanumerieke tekens bevatten.
invalid_xml = Sjabloon niet in overeenstemming
validate_regex_name = De adresnaam moet alfanumerieke tekens bevatten.
validate_link_address = Het gekoppelde adres moet overeenkomen met een bestaand {0} adres.
validate_regex_minimumMorningSaturday = Minimaal op zaterdagochtend tussen 00:00 en 12:00 uur.