# locale: nl
quick_search=Snelle zoekloop
reset=Reset
search=Zoeken

switch_off=Schakel terug naar gebruiker
profile=Profiel
about_contact=Neem contact met ons op
logout=Afmelden
add=Voeg toe
print=Druk af
send=Stuur
remove=Verwijder
no_records_found=Geen records gevonden
ok=OK
cancel=Annuleer
save=Sla op
login=Login

general=Algemeen
allow_user_managing_tab=Sta gebruikers toe om dit tabblad te gebruiken
code=Code
name=Naam
identification=Identificatie
gcnIdentification=GCN Id (gid)
gcnSubscriberIdentification=Generix facturering Id
siret=Registratie
duns=Referentie
tva=BTW
information=Informatie
contact=Contact
phone=Telefoon
fax=Fax
email=E-mail
notification=Kennisgeving
enabled=geactiveerd
schedule=Plan
channels=Kanalen
type=Type
description=Omschrijving
users=Gebruikers
export=Downloaden
regenerate_all=Regenereer alle
number_connexion=Aantal verbindingen
date_last_authentication=Laatste verbinding
role=Rol
scope=Omvang
generate_new_password=<PERSON><PERSON> nieuw wachtwoord
generate_password=<PERSON><PERSON> wachtwoord
duplicate=<PERSON><PERSON><PERSON>
button_edit=Bewerk
user_new=Maak gebruiker aan
user_edit=Bewerk gebruiker
info_user_new_password=Er is een nieuw wachtwoord gestuurd aan {0}
info_user_removed=Gebruiker {0} verwijderd
info_user_saved=Gebruiker {0} opgeslagen
info_user_role_saved=Gebruiker rol opgeslagen
message=E-mail
subject=Onderwerp

# USER ACCOUNT MAILS
creation_mail_subject=Uw account is aangemaakt voor de site {0}
creation_mail_message_html=Hallo {0},<br/><br/>U vindt hieronder uw verbindingsinformatie met de  {1} website ({2}): <br/><br/> <li>Login: {3} </li><li> Wachtwoord: {4}</li><br/><br/>Tot binnenkort!
creation_mail_message_text=Hallo {0},\n\nU vindt hieronder uw bevestigingsinformatie met de {1} website ({2}): \n\n- Login: {3}\n- Wachtwoord: {4}\n\nTot binnenkort !

year = Jaar
ABANDONED_label = AFGEBROKEN
menu_message = E-mail
organization_location = _location
java_lang_Boolean = Booleaans
info_portlet_removed = Portlet inhoud verwijderd
themes = Onderwerpen
template_invalid_name = Naam moet tussen 3 en 18 kleine letters en/of cijfers bevatten
organization_address_addressComplement = _adress.adresToevoeging
company_view = Klant {1} ({0})
ANSWERED_ICON = fa fa-undo fa-fw fa-rotate-90
invalid_file_type = Ongeldig bestandstype
move_left = Beweeg naar links
alphanumeric = Alfanumeriek
DELIVERED_STYLE = status_green
AtLeastOneUser = Tenminste een gebruiker van de partner
task_adv_search_simple = Eenvoudig
autoGenerationOfSSCC = SSCC aangemaakt
ks_comment = Omschrijving
version = Versie
order_issuer = Orderverstrekker
docs_list = Documentenlijst
folder = Folder
configuration_page = Paginaconfiguratie
organization_autoGenerationOfSSCC = SSCC aanmaken
selection = Selectie is nodig
size = Grootte
stop = Stop
ks_hasprivatekey = Met priv\u00E9-sleutel
left = Links
logistic_sscc = SSCC
doc_select = Selecteer
domain_modif = Domein aanpassen
gcnSubscriberIdentification_error = Generix facturering Id moet maximaal 64 alfanummerieke tekens bevatten
error_no_company_specified = Geen klant gespecificeerd voor  partner bewerking: {0}
ARCHIVED_STYLE = status_green
infinite = Oneindig
UPDATED = Geactualiseerd
organization_email = _email
archiving = Archiveren
length_min = Minimale lengte
doc_update = Updaten
result = Resultaten
occurence = Voorkomend
login_validator = Login dient tenminste 3 alfanummerieke, '_', '@' en '.' tekens te bevatten
refuse = Weigeren
reminder_subject = Herinnering thema
STOPPED_icon = fa fa-exclamation-circle
campaigns_new = Nieuwe campagne
close = Sluit
linked_document_import_selection = Selecteer een of meerdere bestanden
connect = Sluit aan
validator_password = Ongeldig wachtwoord (tenminste 3 tekens).
preferences = Voorkeuren
report_partners_completion = PARTNERS VOLTOOIING
exception_no_backoffice_access = {0} maakt geen deel uit van een partner of klant en heeft geen toegang gekregen tot de back-office
ui_required = Waarde is vereist
error_save_user_no_env = Omgevingskeuze is vereist om de gebruiker te informeren
NONE_color = #d4d4d4
partner_import_error = Fout bij importeren partners : {0}
organization_fullname = Naam
doc_upload_error = Fout bij uploaden van document: {0} ({1})
SENT_ICON = fa fa-paper-plane-o
period_to = tot
keyword_$currentDate = Huidige datum
ui_validator = Waarde is niet geldig
error_removing_partner = Fout bij verwijderen van partner ({0})
error_saving_partner = Fout bij opslaan partner ({0})
doc_deleted = Document gewist
gcn_identification_group = Generix samenwerkingsnetwerk
good = goed
error_uploding_logo = Fout bij het laden van logo {0}
add_below = Voeg toe onder
entry_add_title = Voeg titel toe
organization_duns = Referentie
UNKNOWN_color = #d4d4d4
channel_save_ok = Bewaard kanaal.
doc_new_name = Nieuwe naam
info_host_saved = Domein {0} opgeslagen
exception_id_duplication = Vermenigvuldig identificatie: {0}
imenu_integration_recent_business = Recent proces
ks_friendlyname = Vriendelijke naam
generate_new_password_confirm = Bent u zeker dat u een nieuw wachtwoord genereert?
menu_messages = Mails
contact_user = Contact gebruiker
java_util_Date = Datum
warn_select_partner_notification = Selecteer a.u.b. de partners die een kennisgeving krijgen
campaign_documents = Documenten
encoding = Coderen
partner_field = Veld van partner
port = Poort
lang_english = Engels
organization_gs1 = GS1-ondernemingscode
ks_remove_error = Fout bij verwijderen van bestand : ({0})
mail_sent = Verstuurd
info_company_saved = Klant {0} opgeslagen
partner_import_no_user = Partner {0} heeft gebruiker
roles = Rollen
choose = Kies
create_notifications = Maak kennisgeving aan
paste = Coller
partner_delete_error = Fout bij wissen partner : {0}.
page_new = Nieuwe pagina
ks_filename = Bestandsnaam
user_blocked = Uw gebruikersaccount is geblokkeerd. Neem contact op met uw administrator
organization_freeDate01 = _freeDate01
error_removing_user = Fout bij verwijderen gebruiker ({0})
configuration_portal = Portaalconfiguratie
entry_title = Titer
INVOICED_STYLE = status_green
end = Fin
sort_order = Sorteer volgorde
ks_config_require_name = De KeyStore vriendelijke naam is nodig
organization_freeDate02 = _freeDate02
info_instance_toggled = {0} omgeving gewisseld
partner_import_already_existing_users = gebruiker(s) bestaat(bestaan) reeds
label = Label
keyword_$password = Gebruiker wachtwoord
modify = Pas aan
imenu_security = Partners
menu_process_manage = Procesbeheer
TO_REMOVE = Te verwijderen
ks_unexpected_error_determing_alias = Onverwachte fout bij bepaling van de alias
STOPPING_icon = fa fa-exclamation-circle
info_role_removed = Rol {0} verwijderd
family = Familie
OK_label = OK
backup = Backup
template_revision = Voer e-mailsjabloon in
partner_contacts = Contacten
channel = Kanaal
add_above = Voeg toe boven
APPROVED_icon = fa fa-check-circle-o
channel_other = Andere
menu_audit = Audit
ACCEPTED = Bevestigd
organization_phone = _phone
logistic_sscc_auto = SSCC aanmaken
report_not_completed = Niet voltooid
order = Order
organization_vat = BYW
WARN_icon = fa fa-exclamation-circle
period = Periode
trf_generic = generiek formaat
TO_REMOVE_ICON = fa fa-archive
upload_invalid_file = Ongeldig bestandsformaat
warn_host_already_used = Dit domein is reeds in gebruik
exception_backoffice_user = {0} is een back-office gebruiker
WARN_label = WAARSCHUWING
keyword_$login = Gebruiker login
organization_address_country_iSO3Country = ISO-3-landcode
ks_no_file_selected = Er moet een keystorebestand worden geselecteerd
ABANDONED_color = #cc1e00
exit = Uit
partner_deleted = Partner gewist: {0}.
task_adv_search_avancee = Uitgebreid
analyze = Analiseer
integration = Proces
info_partner_file_import = {0} ingevoerd
organization_logoLarge = _logoLarge
other = Andere
property_required = is nodig
exception_user_associated = {0} gebruiker(s) toegewezne
organization_address_country = Land
channel_duplicate_error = Fouten bij kanaalkopie
organization_serialReference = Seriereferentie
error_saving_role = Fout bij opslaan rol ({0})
DUPLICATE = Gedupliceerd
imenu_repository_documents = Documenten
campaign_date_start = Begindatum
ERROR_color = #d15b47
entry_import_faq = Voeg FAQ in
boolean_true = Waar
document_type = Documenttype
ks_entry_serialnumber = Serienummer
channel_test = Tester
reminder = Herinnering
STARTED_color = #bebfbb
exception_partner_subgroups = Partner {0} heeft {1} sub-groep(en)
permissions_dialog_page_title = Definieer toegangsrechten tot de pagina
menu_partners = Partners
channel_add_error_duplicatename = Er bestaat reeds een kanaal met deze naam. Dit kanaal kan niet gecre\u00EBerd worden.
removed = Verwijderd
disable = Deactiveer
error_saving_instance = Fout bij opslaan omgeving({0})
entry_add_question = Voeg een vraag toe
unread_message = ongelezen bericht(en)
completion_bounded = Vervollediging gebonden
organization_freeLongText01 = _freeLongText01
doc_downloads = Document downloads
organization_freeLongText02 = _freeLongText02
recent_mapping = Recente inventarisatie
create_mail = Maak mail aan
report_partners_status_by_month = PARTNERS STATUS EVOLUTIE
invalid_generix_billing_id = Ongeldig Generix facturering ID
menu_admin = Administratie
pages = Pagina's
sort_order_descending = Dalend
entry_radio = Radio
regenerate_all_password = Regenereer alle wachtwoorden
all = Alle
new = Nieuw
modif_contact_partner = Contact partner-aanpassing
upload_files = Upload bestanden
READ = Gelezen
collapse_all = Uitvallen alle
partner_delete_error_children_exist = Kon partner niet wissen met sub-partners: {0}.
report_informations = Informatie
length_max = Maximale lengte
exception_constraint_violation = {0} : {1}
freetext = Vrije tekst
organization_address_country_country = ISO-2-landcode
template_saved = Sjabloon opgeslagen
sort_by = Sorteer naar
ui_converter = Waarde omzettingsfout
deny_message = Omschrijf uw ontkenning a.u.b.
channel_deleted = Geannuleerd kanaal
error_removing_portlet = Fout bij verwijderen van portlet inhoud ({0})
exception_instance_not_found = Gebruiker {0} heeft geen omgeving geconfigureerd voor domein {2}
ks_error_could_not_write_certificate_file = Kon het documentbestand voor dit certificaat niet schrijven.
folder_in = Invoermap
entry_answer = Antwoord
permission_required = Selectie is nodig
select_one = Selecteer een
milestone = Geplande voltooiingsdatum
organization_address_country_displayCountry = Land
partner_import_file = Partners bestand invoer (*.xls, *.xlsx)
exception_task_type_not_found = Niet mogelijk om taaktype te vinden{0}
account_creation_confirm = Uw account is met succes aangemaakt. U ontvangt een bevestigingse-mail op het adres:
imenu_integration = Proces
mark_as_read = Markeren als gelezen
ks_publickeytab = Openbare sleutel-certificaat
partner_name = Partnernaam
freetext_details = Vrij tekstveld/gegevens
STARTED_icon = fa fa-times-circle-o
warn_language_mandatory = Taalconfiguratie is verplicht
exception_message = Er is een fout opgetreden. \nProbeer het opnieuw of neem contact op met uw administrator ({0})
organization_freeText09 = _freeText09
companies = Klanten
delete_select = Wilt u de geselecteerde items blijvend wissen ?
error_removing_page = Fout bij verwijderen van pagina ({0})
organization_freeText07 = _freeText07
organization_subscriberId = Generix facturering Id
ks_entry_alias_optional = Optioneel tenzij alias kan niet automatisch worden bepaald
organization_freeText08 = _freeText08
organization_freeText05 = _freeText05
readonly = Alleen-lezen
doc_uploaded = Document ge\u00FCpload: {0}
organization_freeText06 = _freeText06
organization_freeText03 = _freeText03
organization_freeText04 = _freeText04
organization_freeText01 = _freeText01
organization_freeText02 = _freeText02
ERROR = Fout
partner_view = Partner {1} ({0})
state = Toestand
exception_user_not_partner = Gebruiker {0} hoodgroep {1} is geen partner of klantgroep
FATAL_icon = fa fa-bomb
events = Gebeurtenissen
exception_import_portal_not_empty = Portaal is niet leeg voor omgeving {0}
hosts = Hosts
UNKNOWN_icon = fa fa-exclamation-circle
bad_account_or_password = Verkeerde account of wachtwoord
error_getting_portlet_content = Portlet {0}, Inhoud {1}: {2}
REMOVED_STYLE = standaard
monitoring = Activiteit
move_top = Beweeg bovenzijde
labels = Labels
menu_campaigns = Onboarding
dictionary = Economische woordenboeken
menu_notifications = Kennisgevingen
rendered = Overhandigd
recipients = Ontvangers
boolean_false = Vals
ERROR_STYLE_STAGE = status_red
logistic_gs1_error_format_message = De GS1-ondernemingscode moet tussen 7 en 10 cijfers bevatten
actions = Acties
edit_notifications = Bewerk kennisgeving
cut = Couper
contact_admin = Neem contact op met uw administrator.
other_variable = Andere beschikbaar
document = Document
upload_file_limit = Bestandsgrens bereikt
expand_all = Alle uitbreiden
label_search = Voer uw zoekloop hier in
lang_french = Frans
ERROR_label = FOUT
menu_security_companies = Klanten
ks_remove_linked_partner_integrity = Bewerking geblokkeerd : certificaat nog steed verbonden met deze groepen {0}
default = standaard
APPROVED_STYLE = info
organization_profile = Profiel
reminder_number = Herinnering aantal
upload_with_conflict_conflict_message = De volgende bestanden worden overschreven :
disabled = Gedeactiveerd
exception_invalid_gson = Ongeldig bestand Gson
report_partners_status = PARTNERS STATUS
warn_only_parent_empty = Alleen ouderpagina dient als leeg te worden gedefinieerd
organization_freeBoolean01 = _freeBoolean01
ERROR_STYLE = status_red
creation = Maak datum aan
ui_invalid_size = Ongeldige bestandsgrootte
menu_process = Proces
organization_freeBoolean02 = _freeBoolean02
validate = Valideer
UPDATED_STYLE = info
FAILED_color = #cc1e00
rte = RTE
template_import = Voer sjabloon in
languages = Talen
ks_modulus_length = Modulus-lengte (bits)
refresh = Opfrissen
partner_save_error = Fout bij opslaan partner : {0}.
ks_unrecognized = Ge\u00FCploade data kunnen niet worden geladen als PKCS11, PKCS12 of JKS keystore.\n - Controleer het wachtwoord.\n - Controleer uw file \n - Probeer uw \"Unlimited Strength Jurisdiction Policy Files\" van uw JRE te veranderen.
organization_address_postalCode = Postcode
tout = Alle
about_contact_email = E-mail van contact
STARTED_label = BEGONNEN
account_confirmation = Bevestiging aanmaken account.
channel_create_error = Fout bij kanaalcreatie.
warn_no_type_selected = Selecteer a.u.b. een taaktype
organization_client = _client
is_expired = is verlopen
ks_upload_select = Keystore (*.jks, *.p12, *.pfx)
partners = Partners
generate_gcn_subscriber = Genereer ID
entry_select_excel = Selecteer een Excel bestand (*.xls, *.xlsx)
organization_gcnSubscriber = Generix klant
xPath = xPath
campaign_name_short = Korte naam
documents = Documenten
administration = Administratie
FAILED_label = MISLUKT
accounting = Contact met klant
organization_parent = _parent
secure = Veilig (HTTP/S)
ks_entry_notafter = Geldig tot
ks_upload = Upload een keystore
menu_security = Veiligheid
APPROVED_color = #87b87f
view = Bekijk
domain_edit = Domein bewerken
test_upload = Upload een testbestand
keyword_$campaignvariable = Campagne vrije variabele
imenu_portal_einvoice = e-factuur
error_removing_template = Fout bij verwijderen sjabloon ({0})
version_technical = Technische referentie
menu_statistics = Rapportage
template_invalid_pathname = Archief bevat ongeldige tekens in padnaam
new_file = Nieuw bestand
channel_modify = Kanaal wijzigen
error_user_without_primary_group = Geen primaire groep gevonden voor vermenigvuldigen van gebruiker
APPROVED_label = OK
user_search = Verfijn uw resultaten door de naam van de gebruiker in te typen die de veranderingen heeft uitgevoerd
info_portlet_saved = Portlet inhoud opgeslagen
imenu_repository_templates = Sjablonen
add_left = Voeg links toe
logistic_gs1_company_prefix = GS1-ondernemingscode
warn_template_with_associated_host = Sommige domeinen zijn geassocieerd met een sjabloon
partner_add = Partner toevoegen
page = Pagina
installation_detail = Ge\u00EFnstalleerde software
parameters = Parameters
parameter = Parameter
task_types_revision = Visualisatie taakdefinitie
configuration_portlet = Portlet configuratie
import = Importeren
error_duplicate_template = Sjabloon {0} bestaat reeds
edition = Editie
campaign_description = Omschrijving
rte_file_or_folder_invalid_name = Naam dient een alfanummerieke reeks te zijn zonder spatie. '.', '_' en '-' worden geaccepteerd.
exception_multiple_principal_groups_user = Gebruiker {0} heeft meer dan een hoofdgroep ({1})
gcnIdentification_error = GCN Id moet maximal 64 alfanummerieke tekens bevatten
complementary_info = Aanvullende informatie
select_instance = Selecteer een omgeving
ks_error_could_not_find_nor_create_parent_for_friendly_name = Kon ouder voor vriendelijke naam niet vindene of aanmaken {0}
day_of_week = Weekdag
severity = Ernstigheid
REFUSED_STYLE = status_red
exception_removed_instance = Omgeving {0} dient te worden verwijderd
hours = Uren
info_partner_removed = Partner {0} verwijderd
yes = Jaar
TO_VALIDATE_ICON = fa fa fa-pause fa-fw
start = Start
OK_color = #87b87f
logistic = Logistiek
ks_cannot_determine_alias = Kan alias voor gebruik niet bepalen. Aliassen gevonden, met ; scheidingsteken : {0}
partner_code_validator = Partnercode dient rekening te houden met deze klant drie hoofdlettercodeformaat gevolgd door zijn identificatie
UNDEFINED_STYLE_STAGE = standaard
fullname = Volledige naam
confirmation_mail_message_text = Hallo {0},\n\nUw account is met succes aangemaakt. Knip en plak a.u.b. het volgende adres in uw favoriete browser om het aanmaken van uw account te bevestigen: {1}.\n\nTot binnenkort!=======
exception_user_no_associated_company = Gebruiker {0} partner groep {1} is niet toegewezen aan een klant
ks_library = Bibliotheek
usage = Gebruik
ks_cannot_load_keystore = Keystore kan niet worden geladen. Controleer parameters. Neem contact op met een administrator.
organization_orderContact = Contact
organization_address_city = Plaats
ks_entry_subject = Geleverd aan
organization_freeViewProfile = Profiel
required = Vereist
active_partners = MEEST ACTIEVE PARTNERS
docs_selected = Documenten geselecteerd
CLOSED_STYLE = standaard
doc_name = Naam van document
info_partner_saved_detail = Partner {0} opgeslagen : {1}
keystore = Keystore
switch_user = Schakel gebruiker
TIMEOUT_ICON = fa fa-remove fa-fw
javax_xml_datatype_XMLGregorianCalendar = XML datum
ks_no_slot_index_selected = Er moet een slot-index worden geselecteerd
dashboard = Instrumentenpaneel
entry_new = Nieuwe entry
error_switching_user = Fout bij wisselen van gebruikerr ({0})
warn_app_template_deleting={0} sjabloon kan niet worden gewist.
role_min = rol
linked_certificates = Gelinkte certificaten
organization_name = Code
menu_security_order_issuers = Orderverstrekkers
exception_file_upload_unknown_request = Onbekende aanvraag parameter: {0}
NONE_STYLE = status_yellow
menu_system = Systeem
style = Stijl
channel_add = Kanaal toevoegen
logistic_gs1_company_not_provided = Vul de GS1-ondernemingsprefix in
ks_provide_friendly_name = Er moet een vriendelijke naam worden ingesteld
roadmap = Roadmap
contact_us = CONTACT
confirm_file_delete = Weet u zeker dat u wilt verwijderen {0}
exception_portlet_cloning = Probleem bij clonen van portlet {0}
name_executable = Naam van uitvoerbare
channel_ftp_username = Gebruikersnaam
exception_duplicate_role = Vermenigvuldig rollen code: {0}
enabled_linked_document_import = Activeer invoer van gelinkt document
theme = Onderwerp
error_saving_permission = Fout bij opslaan toestemming ({0})
start_date = Begindatum
channel_name = Naam van het kanaal
editor = Editor
report_partners_completion_campaigns = PARTNERS VOLTOOIING
ks_alias_not_found = Alias niet gevonden : {0}. Gevonden alissen, met ; scheidingsteken : {1}
indexing = Indexing
menu_instances = Omgevingen
display = Geef weer
export_portal = Uitvoer portaal
warn_portlet_content_not_found = Inhoud portlets met id {0} niet gevonden
entry_answers = Antwoorden
exception_partner_associated = {0} partner(s) toegewezen
info_instance_saved = {0} omgeving opgeslagen
organization_address_address = _address.address
TIMEOUT_STYLE = status_red
exception_unavailable_instance = Omgeving {0} is momenteel niet beschikbaar
identification_validator = Identificatie mag alleen alfanummerieke of  '_' tekens bevatten
task_to_complete = Taak om te voltooien
partner_saved = Partner opgeslagen : {0}.
chat = Chat
exception_import_error_during_cloning = Fout in cloneportaal
CANCEL_ICON = fa fa-times
instance_code_validator = Code dient de drie cijfers of hoofdletterformaat aan te houden
AllUsers = Voor alle gebruikers van de partner
error_creating_template_archive = Fout bij aanmaken sjabloon archief ({0})
statistics = Rapportage
ABANDONED_icon = fa fa-exclamation-circle
keyword_$contact = Contact
replace = Vervang
READ_STYLE = info
partner_send_mail = Stuur account informatie
template_edit = Bewerken van de sjabloon
organization_orderPhone = Telefoon
day = Dag
from_address = Van adres
ks_error_could_not_extract_certificate = Het certificaat/de certificaten in het bestand kon(den) niet worden uitgepakt
ks_error_no_certificate_found=Geen certificaat(s) gevonden in het bestand
ks_uploaddate = Aanmaakdatum
FATAL_color = #cc1e00
ks_remove_timestamp_server_integrity = Bewerking geblokkeerd : verwerking nog steeds gereferentieerd in tijdstempel-server
ks_slotIndex = Slot-index
ks_label = Label
tasks_noselection = Geen geselecteerde taak
minutes = Minuten
error_saving_host = Fout bij opslaan domein ({0})
format = Formaat
recent_business = Recente transactie
archive = Archief
menu_domain = Domein
history = Historie
upload_invalid_size = Ongeldige bestandsgrootte
home_message = Versnel uw EDI/B2B roll-outs terwijl u uw kosten met GCI Community Management verlaagt
partner_new = Nieuwe partner
reminder_content = Herinnering inhoud
FATAL_label = FATAAL
error_profile_name_mandatory = Profielnaam is verplicht
deploy_process = Zit proces voort
imenu_repository = Opslag
CLOSED = Gesloten
ui_file_limit = Maximum aantal bestanden overschreden
imenu_general = Algemeen
insert = Voeg in
numeric = Numeriek
info_no_portlet_defined = Portlet is niet gezet voor deze pagina
error_exporting_instance = Uitvoer mislukt : {0}
campaign_name = Naam
test_send_mail = Mail automatisch zenden
move_up = Beweeg omhoog
SENT_PARTIALLY_STYLE = status_yellow
entry_new_answer = Optie
channel_type_select = Kanaaltype...
error_saving = Fout bij het opslaan ({0})
authentication = Authentificatie
keyword_$company = Klant
clear = Wis
organization_freeViewConfiguration = Vrije velden
organization_userGroupAssociations = _userGroupAssociations
warn_creation_succes = De aanmaak is gelukt
upload_with_conflict_title = Voer archief in
import_cert_pem_cer_crt = PEM/CER/CRT
organization_logoSmall = _logoSmall
organization_orderFax = Fax
error_saving_organization = Fout, het item bestaat reeds
move_down = Beweeg omlaag
APPROVED = Goedgekeurd
user = gebruiker(s) bestaat(bestaan) reeds
account = Mijn account
ANSWERED = Beantwoord
WARNING_label = WAARSCHUWING
ks_password_pkcs11 = Wachtwoord (PIN-code)
importInstance = Import (*.json)
SENT_PARTIALLY_ICON = fa fa-paper-plane
campaigns = Onboarding
organization_creation = _creation
STOPPED_color = #cc1e00
WARNING_STYLE = status_yellow
warn_partner_profile_deleted = Partnerprofiel mag niet worden gewist
entry_add = Voeg toe
exception_gcn_subscriber_id_duplication = Vermenigvuldig Generix-facturering Id: {0}
reminder_delay = Herinnering vertragingen
role_edit = Bewerk rol
DISABLED_label = GEDEACTIVEERD
FAILED_icon = fa fa-exclamation-circle
menu_security_users = Back office-gebruikers
confirm_instance_enable = Alle omgevingsdiensten worden geactiveerd (verwerken, onboarding...).\\n\\nBent  zeker dat u deze omgeving wilt activeren?
tasks_campaign = Taken van werkstroom
ks_error_no_keystore_folder = Fout - de keystoremap kon niet worden opgehaald
domain = Domein
channel_toggle_linked_chanel = Fout bij kanaalwijziging. Er bestaan nog partners met een alias op dit kanaal :<br/>{0}
entry_type = Type
max_size_of_linked_document = Max. grootte van gelinkte bestanden
schema = Schema
strong = Sterk
organization_logoMedium = _logoMedium
managed_by = Behandeld door
menu_security_groups = Groepen
preview_not_available = Vooraf bekijken niet beschikbaar
ERROR_icon = fa fa-times-circle-o
organization_code = Identificatie
menu_notification = Kennisgeving
ACCEPTED_WITH_AMENDMENT_STYLE = info
partner_parent = Ouder
PENDING_color = #bebfbb
error_saving_file = Fout bij opslaan van bestand ({0})
ERROR_ICON = fa fa-remove
java_lang_Integer = Geheel getal
completion = Vervollediging
mail_content = Inhoud
keyword_$campaigncontactphone = Campagne contact telefoon
warn_existing_resource = Deze bron bestaat reeds
ks_uploaded = Keystore ge\u00FCpload : {0}
REFUSED_ICON = fa fa-exclamation-triangle
confirm_task_result_reset = De taak/taken resultaten worden gewist. Wilt u doorgaan?
company_add = Voeg klant toe
import_cert_jks_pkcs12 = JKS/PKCS#12...
partner = Partner
confirmation_mail_message_html = Hallo {0},<br/><br/>Uw account is met succes aangemaakt. Bevestig a.u.b. uw e-mail door te klikken op de volgende link : <a href=\"{1}\">E-mailbevestiging</a>.<br/></br>Als alternatief kunt u het volgende adres in uw favoriete browser knippen en plakken: {1}.<br/><br/>See you soon!
CANCEL = Geannuleerd
error_removing_role = Fout bij verwijderen rol ({0})
menu_config = Configuratie
ks_entry_signaturealgoname = Handtekening algoritme naam
partner_import_already_existing_partners = partner(s) bestaat(bestaan) reeds
FATAL_STYLE = status_red
info_logo_uploaded = Logo geladen
language = Taal
organization_comment = Omschrijving
styleClass = Stijl-klassen
OK_icon = fa fa-check-circle-o
error_uploding_template = Fout bij het laden van sjabloon ({0})
ks_entry_fingerprintsha1 = Vingerafdruk (SHA-1)
add_right = Voeg rechts toe
ks_hasRSApublickey = RSA
task_adv_search = Uitgebreide zoekloop
WARNING_icon = fa fa-exclamation-circle
imenu_campaigns = Onboarding
exception_import_instance_code_null = Omgevingscode is nul
decimal_separator = Decimaal scheidingsteken
warn_locked_folder = Geblokkeerde map
docs_available = Documenten beschikbaar
entry_delete_question = Wis een vraag
error_sending_new_password = Er is een fout opgetreden bij het zenden van het nieuwe wachtwoord aan {0} ({1})
authentication_mail_template = Mailsjabloon
count = Totaal
error_saving_host_not_unique = Er bestaat reeds een domein voor deze naam
undefine = Ondefinieer
error_no_user_found = Geen gebruikerr gevonden met identifier: {0}
month = Maand
organization_id = _id
#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=Birt
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Counter
DocumentLineChart=LineChart
archiveimv3 = IMV3 archief
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated
CompleteEdition=CompleteEditionDeprecated
sources = Bronnen
back = Terug
title = Titel
content = Inhoud
timeout = Time-out
duration = Duur
doc_upload_disabled = U kunt deze actie niet uitvoeren omdat de taak geblokkeerd is (er kan een campagne bezig zijn)
entry_question = Vraag
alpha = Alfa
client = Klant
company = Klant
info_portal_saved = Portaal opgeslagen
new_message = Nieuwe melding
WARNING_color = #ffb752
clear_portlet_confirm = Bent u zeker dat u deze portlet-inhoud wilt wissen?
ks_entry_alias = Alias
length = Lengte
logistic_sscc_fieldset = SSCC-nummer
validator_email = Ongeldige e-mail
home = Home
ks_error_during_file_reading = Er heeft zich een fout voorgedaan bij het lezen van het bestand
channel_not_selected = Geen kanaal gekozen
warn_delete_fail = De onderdrukking is mislukt
ks_morethanoneentry = Uw keystore bevat meer dan een vermelding. Wij kunnen deze soort keystore niet laden
READ_ICON = fa fa-eye
prompt = Voer het wachtwoord in
TO_REMOVE_STYLE = status_yellow
error_duplicate_configuration = Configuratie bestaat al
logistic_serial_reference = Seriereferentie
imenu_portal_o2c = Opdracht aan kas
select = Selecteren
exception_instance_associated = {0} omgeving(en) toegewezen
ks_error_no_friendly_name_found = Vriendelijke naam niet gevonden in parameters en kan niet worden bepaald
error_removing_portlet_content = Fout bij wissen van de inhoud van de portlet ({0})
partner_imported = Partner ingevoerd : {0}.
shipment_date = Verzenddatum
ks_entrydetailstab = Certificaat
add_child_page = Voeg kinderpagina toe
channel_delete_linked_chanel = Fout bij kanaalverwijdering. Er bestaan nog partners met een alias op dit kanaal :<br/>{0}
organization_fax = _fax
ks_password = Wachtwoord
SENT_STYLE = info
PENDING_label = RAPPORT BEZIG\u2026
value_default = Standaard waarde
action = Actie
info_file_saved = Bestand opgeslagen
PENDING_STYLE = status_yellow
text = Tekst
portal = Portaal
DISABLED_icon = fa fa-minus-circle
tasks = Taken
ks_config_require_instance = De KeyStore-instantie is nodig
io = IO
partner_user_add = Voeg contact toe
templates = Sjablonen
permission_edit = Bewerk toestemmingen voor
ACCEPTED_STYLE = info
exception_code_duplication = Vermenigvuldig code: {0}
DISABLED_color = #d4d4d4
messaging = Melden
STOPPING_label = STOPT
field = Veld
info_company_removed = Klant {0} verwijderd
campaign_date_creation = Creatiedatum
messages = Meldingen
TIMEOUT = Time-out
doc_cancel = Annuleer
begin = Begin
ks_upload_error = Fout bij het uploaden van keystore : {0}
status = Status
template = Sjabloon
organization_orderEmail = E-mail
ACCEPTED_WITH_AMENDMENT = Bevestigd
alphanumeric_underscore = Alleen alfanumeriek en onderstreepje worden ondersteund
error_importing_instance = Fout tijdens invoer: {0}
organization_web = _web
notify = Kennisgeven
ACQUITTED_STYLE = status_green
ks_unexpected_multiple_keys = Onverwachte meervoudige toetsen in keystore
file = Bestand
library = Bibliotheek
java_lang_String = Tekst
folder_out = Uitvoermap
portlet = Portlet
warn_creation_fail = De aanmaak is mislukt
ANSWERED_STYLE = info
exception_role_has_users = Rol {0} heeft {1}toegewezen gebruiker(s)
classificationPlan = Classificatieplan
linked_document_import = Invoer-toevoeging
organization_children = _children
url = Url
keyword_$campaigncontactname = Campagne contact naam
warn_instance_code_already_used = Deze code is reeds in gebruik
organization_registration = Registratie
role_add = toevoegen
doc_remove_error = Fout bij verwijderen van document
doc_uploaded_success = Document ge\u00FCpload: {0}/{1}
exception_import_export_null = uivoer is nul
is_locked = is geblokkeerd
exception_user_more_companies = Gebruiker {0} partner groep {1} heeft meer dan een toegewezen klant ({2})
instance = Omgeving
template_new = Maak sjabloon aan
doc_rename_error = Document bestaat
menu_monitoring = Activiteit
NONE_icon = fa fa-minus-circle
logistic_missing_sscc_message = Vul de vereiste gegevens in
day_of_month = Maanddag
error_saving_user = Fout bij opslaan gebruiker ({0})
ks_use_start_date = Begin van gebruik
download = Download
ks_provide_library = Bibliotheek moet gezet zijn
clear_page_confirm = Bent u zeker dat u deze pagina-inhoud wilt wissen?
find = Zoek
info_role_saved = Rol {0} opgeslagen
line_comment = Regel commentaar
host = Host
menu_search = Zoeken
boolean_select = --Selecteer--
exception_import_instance_null = Omgeving in exportbestand is nul
workflow = werkstroom
error_changing_layout = Fout bij veranderen van pagina layout ({0})
warn_profile_already_exists = Profiel bestaat reeds
task = Taak
NONE_ICON = fa fa-envelope-o
company_edit = Bewerk klant {1} ({0})
channel_ftp_hostname = Hostnaam van de server
emitter = Emitter
info_no_portlet_content_defined = Portlet is op deze pagina gezet zonder inhoud
error_select_correct_user = Selecteer a.u.b. een correcte gebruiker
no = Nee
java_lang_Class = Klasse
ui_invalid_file = Ongeldig bestandtype
organization_modification = _modification
delete = Verwijderen
logistic_extension = Extensie
move_bottom = Beweeg bodem
REFUSED = Geweigerd
hour = Uur
ks_use_end_date = Einde gebruik
error_save_user_no_partner = Keuze van partner is vereist
ks_entry_fingerprintmd5 = Vingerafdruk (MD5)
account_creation = Aanmaken account
ks_keystoredetailstab = Keystore-details
imenu_repository_dictionary = Economisch woordenboek
exception_unknown_layout_type = Layout type onbekend
new_folder = Nieuwe map
show_triggers = Toon triggers
rigth = Rechts
ks_usage_mismatch = Gebruik {0} moet bij oudergebruik passen {1}
error_saving_portal = Fout bij opslaan portaal ({0})
info_partner_saved = Partner {0} opgeslagen
writer = Schrijver
entry_text = Tekst
report_no_data = GEEN DATA
about_short = Over TradeXpress Evolutie
about = Over TradeXpress Evolutie
DUPLICATE_STYLE = status_yellow
History = Geschiedenis
information_system = informatiesysteem
edit_mail = Bewerk mail
error_removing_row = Fout bij verwijderen rij ({0})
exception_failing_ACE_instanciation = Instanciate ACE van type mislukt: {0}
number_reviewed_messages = Aantal bekeken meldingen
deny = Ontken
edit = Bewerk
edit_record = Bewerk
entry_checkbox = Controlebox
error_editing_default_host = Standaard host is niet bewerkbaar
execute = Voer uit
accept = Accepteer
exception_more_than_one_instance = Toegewezen ebruiker {0} aan de klant {1} heeft meer dan een geconfigureerde omgeving voor domeinen {2} ({3})
info_host_removed = Domein {0} verwijderd
WARN_color = #ffb752
STOPPING_color = #cc1e00
exception_gcn_id_duplication = Vermenigvuldig GCN identificatie {0}
end_date = Einddatum
task_edit = Bewerk taak
contact_mode = Media
view_all = Bekijk alle
com_byzaneo_xtrade_api_DocumentStage = Staat
INVOICED = Gefactureerd
menu_security_partners = Partners
import_cert_pkcs11 = PKCS#11...
property_not_integer = dient een integer te zijn.
ks_type = Type
ks_use_period = Gebruikperiode
indexClassName = Indexklasse
variables = Variabelen
warn_select_role = Selecteer a.u.b. een rol
doc_upload_duplicate = Vermenigvuldig: {0}
started = Gestart
trf_required_template_uri = Docuementtransformatie sjabloon URI is vereist
confirm = Bevestig
trf_required_document_type = Documenttype is vereist in een documenttransformatieconfiguratie
gcn_subscriber_active = Generix klant
keyword_$url = Toepassing url
ks_entry_version = Versie
contacts = Contacten
country = Land
partner_edit = Partner bewerken {1} ({0})
organization_freeDouble02 = _freeDouble02
organization_freeDouble01 = _freeDouble01
channel_type_mandatory = U moet een kanaaltype kiezen voordat u op Toevoegen klikt.
doc_edit = bewerk een document
exception_duplicate_login = Login existiert bereits in AIO. Es kann nicht erstellt werden.
UNKNOWN_label = ONBEKEND
eDocument = eDocument
details = Details
confirmation_mail_subject = Bevestiging van aanmaken account
empty_page = Lege pagina
CORRECT_STYLE_STAGE = status_green
error_file_not_found = Bestand niet gevonden {0}
query = Filteren
user_logged = Reeds ingelogd
exception_user_not_specified = gebruiker is niet gespecificeerd
no_data = Geen  data
WARNING = waarschuwing
modification = Aanpassingsdatum
date_read = Lees datum
entry_remove = Verwijder
partner_field_configuration = Veldconfiguratie van partner
campaign = Campagne
error_exporting_partner = Fout bij uitvoeren partners ({0})
preview = Vooraf bekijken
partner_address = Adres
ACCEPTED_WITH_AMENDMENT_ICON = fa fa-check-circle-o
domain_create = Domein aanmaken
keystores = Keystores
sooner_shipment_date = Zo spoedig mogelijke datum
managed_by_order_issuer = Behandeld door OI
error_editing_portlet = Fout bij bewerken portlet ({0})
purge = Zuiveren
task_create = Maak taak aan
imenu_portal_p2p = Kopen om te betalen
edocument = E-document
organization_collaborativeId = GCN Id (gid)
TO_VALIDATE_STYLE = status_yellow
com_byzaneo_xtrade_api_DocumentStatus = Status
warn_locked_file = Geblokkeerd bestand
company_code_validator = Code dient het driehoofdletterformaat te respecteren
keyword_$campaigncontactemail = Campagne contact e-mail
layout = Lay-out
confirm_instance_disable = Alle omgevingsdiensten worden gedeactiveerd (verwerken, onboarding...).\\n\\nBent  zeker dat u deze omgeving wilt deactiveren?
exception_json_export_error = Fout bij JSon uitvoer: {0}
import_cert_pkcs7 = PKCS#7
comment = Commentaar
channel_add_type = Type kanaal toe te voegen
exception_task_change_parent = Fout bij veranderen ouder algoritme
exception_access_denied = Toegang geweigers
COMPLETED_icon = fa fa-check-circle-o
none = Geen
doc_upload = Upload
repository = Opslag
seconds = Seconden
client_order = Klantopdracht
enable = activeer
permissions = Toestemmingen
remindpassword = Herinnering wachtwoord
imenu_general_instance = Omgeving
sort_order_ascending = Stijgend
NONE = Ongelezen
FATAL = Systeemfout
java_math_BigDecimal = Decimaal
STOPPED_label = GESTOPT
channel_save_error_null = Fout bij het bewaren van het kanaal (het kanaal is null).
partner_comment = Commentaar
later_shipment_date = Op de meest recente verzenddatum
imenu_portal = Samenwerkend
exception_task_import_parents = Probleem bij invoer van taken ouder voor naam {0}
error_removing_host = Fout bij verwijderen van domein ({0})
logistic_serial_reference_error_format_message = De seriereferentie moet tussen 6 en 9 cijfers bevatten
ks_config_require_usage = Het KeyStore gebruik is nodig
ACCEPTED_ICON = fa fa-check
CANCEL_STYLE = standaard
exception_task_import = Probleem bij import taken voor naam {0}
ks_entry_issuer = Opsteller
organization_extension = Extensie
error_removing_company = Fout bij verwijderen van klant ({0})
ks_certificationpath = Keten van vertrouwen
NONE_label = GEEN
ks_error_multiple_parent_for_friendly_name = Inconsistente status: meerdere ouders gevonden voor vriendelijke naam {0}
info_file_removed = Bestand verwijderd
permissions_dialog_campaign_title = Voeg partners in de campagne toe
error_importing_partner = Fout bij invoeren partners ({0})
imenu_report = Activiteit
weak = zwak
TO_VALIDATE = te valideren
invalid_file_size = Ongeldige bestandsgrootte
copy = Kopieer
CAMPAIGN = ONBOARDING
PENDING = Concept
channel_desc = Omschrijving
warn_host_not_removed = {0} is NIET verwijderd ({1} bijbehorende omgeving(en) :CODE= {2})
filter = Filter
help = Help
task_types = Taaktypes
description_short = Korte omschrijving
menu_dashboard = Dashboard
organization_description = _description
rename = Hernoem
iframe_error_message = Geweigerd om document weer te geven omdat weergave door X-Frame-Opties verboden is
error_removing_file = Fout bij verwijderen van bestand ({0})
doc_uploaded_update = Ge\u00FCpdate document
date = Datum
exception_admin_url_portal = Typ een andere gebruikernaam in of neem contact op met uw administrator om uw toegangsportaal URL te krijgen
warn_user_missing = Gebruiker ontbreekt (zie uid parameter)
entry_edit_question = Bewerk een vraag
error_saving_portal_portlet_missing = Ontbrekende portlet configuratie
partner_identification = Identificatie
upload_file = Upload bestand
channel_ftp_port = Poort
menu_process_reports = Procesrapporten
page_noselection = Geen geselecteerde pagina
create = Aanmaken
organization_address_streetName = Adres
campaign_general = Algemeen
REMOVED = Verwijderd
user_company = Klantengebruikers
STARTING_label = START
task_last_completed = Vorige taak voltooid
SENT_PARTIALLY = Deels verzonden
channel_duplicate_ok = Gedupliceerd kanaal
move_rigth = Beweeg naar rechts
add_certificate = Voeg certificaat toe
ARCHIVED = Gearchiveerd
document_children_policy = Beleid kinderdocument
report_completed = Voltooid
ACQUITTED = Gevalideerd
STARTING_color = #bebfbb
SENT = Verzonden
exception_role_has_pages = Rol {0} heeft {1} toegewezen pagina(s)
DELIVERED = Afgeleverd
COMPLETED_color = #87b87f
week = week
configuration = Configuratie
instances = Omgevingen
exception_sending_mail_partner = Fout bij zenden van e-mail aan partner(s): {0}
site_optimized_for_ie9 = Site ge\u00F6ptimaliseerd voor Internet Explorer 9.
error = Fout
delete_select_single = Wilt u de geselecteerde items blijvend wissen ?
security = Veiligheid
PENDING_ICON = fa fa-eur
STARTING_icon = fa fa-times-circle-o
channel_type = Type
ks_entry_notbefore = Geldig vanaf
campaign_date_end = Einddatum
value = Waarde
COMPLETED_label = VOLTOOID
PENDING_icon = fa fa-cog fa-spin
info_company_created = Klant {0} aangemaakt
import_portal = Import portaal
Stage_CORRECT = Gedematerialiseerd
Stage_UNDEFINED = Onbepaald
Stage_UNKNOWN = Onbekend
Stage_ERROR = Fout
backoffice = Back Office
invoice = Factuurnummer
ks_no_key_found = Geen toets gevonden in keystore
organization_address_addressLine = Adres
Cascade=De child documenten verwijderen
Detach=De child documenten loskoppelen
Ignore=De parent documenten negeren
doc_purge_children_policy=Linked documents
portlet_asn_ship = ASN : Verzenden
portlet_referentiel_produits_general_description = Benchmark/Producten/Algemeen/Omschrijving
agreement = CGU
contextual_validation_user_scope_user = Gebruiker {0} bestaat niet
R = Ontvanger
S = Uitgever
agreement_file_not_found = De CGU {0} is onvindbaar voor de taal {1}
portlet_referentiel_taxes = Benchmark/Taksen
correct = Verbeteren
portlet_invoice_join = FACTUUR: Samenvoegen
users_email = Gebruikers/e-mail
contextual_validation_user_scope_partner_id = Het identificatienummer van de partner {0} en de veiligheidszone {1} moeten verschillend zijn voor de gebruiker {2}
organization_end = Einddatum
root = Root
organization_start = Begindatum
portal_general = Algemeen
contextual_validation_user_scope_partner = Partner {0} behoort niet tot {1}
demat_partner_dialog_title = Historiek van de wijzigingen
portlet_taxes_allowances = Parafiscale kortingen en taksen
process_msg = Behandelen
logistic_extension_required = Het karakterverlengingsobject is verplicht omdat de automatische generatie van SSCC geactiveerd is.
OK_STYLE = status_green
RESOLVED = Opgelost
OK = Ok
channel_auth_userSearchBase = Zoekbasis gebruiker
portlet_referentiel_produits_other = Benchmark/Producten/Andere
portlet_collection = Verzameling
portlet_order=Bestelling
DEMAT_ERR_STYLE = status_red
confirmScopeSelection = U verliest de selectie van de partnerveiligheidszone
logistic_serial_reference_required = Het volgnummer is verplicht omdat de automatische generatie van SSCC geactiveerd is.
TO_CORRECT = Te verbeteren
TO_CORRECT_STYLE = status_yellow
contextual_validation_user_scope_user_cpy = Gebruiker {0} behoort niet tot {1}
portlet_referentiel_produits_general_net_price = Benchmark/Producten/Algemeen/Omschrijving/Nettoprijs
portlet_referentiel_global_allowances_charges = Benchmark/Algemene kortingen en kosten
agreement_default_required = Het is verplicht de CGU's te koppelen aan de standaardtaal {0} (CGU checkbox)
SYNTAX_ERR_STYLE = status_red
SHIPPED_STYLE = status_green
portlet_invoice_diagnostic = FACTUUR: Onderzoeken
integrationnotification = Procesintegratierapport ontvangen
logistic_missing_mandatories_message = Gelieve alle vereiste gegevens in te geven omdat de automatische generatie van SSCC geactiveerd is
SHIPPED = Verstuurd
ignored = Genegeerd
logistic_gs1_company_required = De GS1 code is verplicht omdat de automatische generatie van SSCC geactiveerd is.
imenu_messaging = Berichten
organization_registerName = Bedrijfsnaam
agreement_instance_required = CGU omgeving verplicht
DEMAT_ERR = Fout D\u00E9mat
agreement_lang_required = Taal CGU is verplicht
portlet_referentiel_produits_remise_charge = Benchmark/Producten/Korting en kost
return = Retour
REFUSED_STYLE_STAGE = status_red
organization_shareCapital = Aandelenkapitaal
channel_auth_groupSearchBase = Zoekbasis groep
processed = Behandeld
channel_auth_groupSearchFilter = Zoekfilter groep
processing = Status
channel_auth_userDnPattern = DN-gebruiker patroon
RESOLVED_STYLE = status_green
portlet_referentiel_produits = Benchmark/Producten
channel_auth_userSearchFilter = Zoekfilter gebruiker
portlet_asn_import = ASN : Importeren
organization_legalStructure = Juridische structuur
disagree = Weigeren
RECIPIENT = Ontvanger
logistic_serial_reference_and_cnuf_error_format_message = De lengte van de CNUF en het volgnummer moet 16 karakters bedragen.
agree = Aanvaarden
Contract = RoquetteContract
add_description = Een omschrijving toevoegen
SYNTAX_ERR = Fout Syntaxis
unprocesses_message = niet verwerkte bericht(en)
ERR_XLEG = Fout Ex. L\u00E9g.
ERR_XLEG_STYLE = status_red
agreement_version_required = Versie CGU is verplicht
SENDER = Uitgever
contextual_validation_user_scope_role = Gebruiker {0} heeft de rol {1} niet
channel_AuthenticationEndpointConfiguration = Authenticatie

# Portlet Categories
EDOCUMENT=INVOICING SERVICES
RTE=EDI SERVICES
GCN=COLLABORATIVE NETWORK
SMARTPDF=SUPPLIER PORTALS
portlet_uploadpdf=UploadPdf
portlet_uploadpdfclient=UploadPdfClient
DOC=KPI

REFUSED_MANUALLY_STYLE = standaard
portlet_files = FILES
portlet_carousel=Carousel
menu_user_information = Mijn account
UNDEFINED_STYLE = standaard
company_society = Bedrijf
bank_account_bic = BulkTruck
profile_title = Mijn account
menurights = Rechtermenu
BLOCKED_STYLE = status_orange
deployment = Weergave
banking_partner = Bankpartner
UNKNOWN = Onbekend
NO_ROUTE_STYLE = status_yellow
UNKNOWN_ICON = fa fa-remove
INTEGRATED_STYLE = status_green
portlet_documentation = DOCUMENTATIE
date_to = naar
INTEGRATED = Ge\u00EFntegreerd
factor = Factor
portlet_switch_user = Gebruikersnaam
PAID = Betaald
IN_DISPUTE = In geschil
IN_DISPUTE_STYLE = status_yellow
UNKNOWN_STYLE = status_yellow
organization_bankAccount_date = Creatiedatum
organization_bankAccount_iban = IBAN
organization_bankAccount_currency = Valuta
organization_bankAccount_bic = BulkTruck
bank_account_currency = Valuta
FORCED = Verplichten
menu = Rechtermenu
PAID_STYLE = status_green
UNKNOWN_STYLE_STAGE = status_yellow
Unknown = ONBEKEND!
bank_account_iban = IBAN
menu_rights_users = Gebruikers
progress = Lopende...
IN_SUBMISSION_STYLE = status_yellow
organization_bankAccount_partner = Partner
TO_PAY_STYLE = status_green
FORCED_STYLE = standaard
organization_bankAccount_user = Aangemaakt door
portlet_faq = VGV
portlet_freetext= FreeText
notifications = Notificatie
menu_rights_roles = Rollen
menu_basket=Mand
SUBMITTED_STYLE = status_green
menu_rights_clients = Klanten
IMPORT_CORRECTION_STYLE = info
IN_SUBMISSION = Validering lopend
IN_VALIDATION_STYLE = status_orderlist_blue
VALIDATED = Gevalideerd
IN_DELIVERY_STYLE = status_orderlist_yellow
IN_PREPARATION_STYLE = status_orderlist_yellow
VALIDATED_STYLE = status_orderlist_green
user_lastname = Naam
portlet_pack = Pack
exception_duplicate_email = Dit e-mail adres is al gemaakt
client_number = Nr ${client}
banking = Bank
BLOCKED = geblokkeerd
user_email = Adres email
user_firstname = Voornaam
extensions=Extensions
file_name=File name :
last_modification=Last modification
extension_label=Recording n\u00B0
export_extension_file=Export file
export_extension_all_files=Export files
no_extensions_found = No extensions found for this database
extension_name=Extension Name
displayed_extension_name=Displayed extension name
display_extension=Display extension


self_register_login = Registreren
to_validate = te valideren
PARTIALLY_SHIPPED = Gedeeltelijk verzonden
bql_filter_title = Filters
REFUSED_MANUALLY = Handmatig geweigerd
partner_address_1 = Adres 1
partner_address_2 = Adres 2
clients_type_CUSTOMER = Klanten
partner_country = Land
customer_partner_name = Naam
return_to=Terug keren naar
update_user_parameters = Mijn parameters bijwerken
menu_security_user = Mijn wachtwoord
menu_company_information = Mijn bedrijfsinformatie
general_notifications = Mijn algemene meldingen
menu_user_parameters = Mijn parameters
menu_legal_information = Juridische informatie
menu_password_user = Mijn wachtwoord
menu_company_information_client = Mijn ${client} informatie
export_list = Exportlijst
company_number = ${client} n\u00B0
clientPartners = Mijn ${clients}
customer_clientPartners = Mijn ${clients}
menu_user_notifications = Mijn meldingen
update_user_notifications = Mijn meldingen bijwerken
activate_notifications = Mijn meldingen activeren
organization_optionReporting = Opties voor eReporting
company_information = Bedrijfsinformatie
communication = Communicatie
selection_or_enter = Een waarde kiezen of invoeren
IMPORT_CORRECTION = Importcorrectie
info_ordering_removed = Besteller {0} verwijderd
AAP_MISSING_AN_REF_STYLE = status_red
partner_user_send_pass_yes = Ja, ik stuur het op
authentication_reconfiguration = Herconfigureer
portlet_invoice_refused_manually = INVOICE: Handmatig afval
users_search_placeholder = Gebruiker zoeken op gebruiker, voornaam of achternaam
portlet_standardorderlist_actions_2 = STANDAARD BESTELLIJST: Actie 2
portlet_standardorderlist_actions_1 = STANDAARD BESTELLIJST: Actie 1
customer_partner_create = Voeg ${client}
portlet_standardorderlist_actions_6 = STANDAARD BESTELLIJST: Actie 6
client_type_EDI = partner
portlet_standardorderlist_actions_5 = STANDAARD BESTELLIJST: Actie 5
portlet_standardorderlist_actions_4 = STANDAARD BESTELLIJST: Actie 4
portlet_standardorderlist_actions_3 = STANDAARD BESTELLIJST: Actie 3
instance_type_SPECIFIC = Specifiek
REJECTED_STYLE = status_red
reconciliation_success = Afstemming succesvol uitgevoerd.
error_removing_client = Fout bij verwijderen client ({0})
accessTokenV1_username = Gebruikersnaam
portlet_standardorderlist_remove = STANDAARD BESTELLIJST: Verwijder
EP_ELIGIBLE = In aanmerking komende
BEING_SENT_CLIENT_STYLE = status_orange
info_partners = Selecteer hier de naam of de code van een entiteit.
manage_rules = Regels beheren
perimeter_edit = Omtrek bewerken voor
bql_instructions_title = Titel van de BQL-instructies
portlet_standardorderlist_actions_9 = STANDAARD BESTELLIJST: Actie 9
portlet_standardorderlist_actions_8 = STANDAARD BESTELLIJST: Actie 8
portlet_standardorderlist_actions_7 = STANDAARD BESTELLIJST: Actie 7
check_site_conditions = Ik ga akkoord met de Algemene voorwaarden
portlet_standardorderlist_export = STANDAARD BESTELLIJST: Exporteer
Noname = GEEN NAAM !
RCL_READY = Klaar voor rcl
deploy = Installeer
AAP_IN_PROGRESS_STYLE = status_yellow
PARTIALLY_ACCEPTED_WITH_AMENDMENTS = Gedeeltelijk bevestigd met wijzigingen
workflow_notifications = Mijn workflowmeldingen beheren
accessTokenV1_access_fail = Geen toegang tot deze bron : {0}.
cart_checkout = KASSA
a_day_past = minder dan een dag
filterSuccessfullySaved = Het filter is opgeslagen
authentication_partners_pairs = Partnerconfiguratie
APPROVED_PARTIALLY_STYLE_STAGE = status_yellow
RCL_SUCCESS_STYLE = status_green
add_ordering_party = Een besteller toevoegen
portlet_deadpool = Deadpool
counter_management_other_page_configuration = Gebruik instelling van andere pagina
cart_view = WINKELWAGEN BEKIJKEN
ROSSUM = ROSSUM
accounting_posting_management = Beheer
file_process_will_be_executed = Het proces wordt gestart
RCL_DISCREPANCY = Rcl discrepantie
clients_type_EDI = partners
element_order = Bestel
translation = Vertaling
accounting_posting_referentials_tax = Belastingen
partner_user_send_new_pass_header = Wachtwoord opnieuw instellen voor gebruiker
REFERENTIAL_OK = Referentieel ok
portlet_referentiel_produits_logistics_info = Verwijzingen/Producten/Logistieke informatie
menu_process_execution = Proces: Uitvoeringen
from_address_validator = Het e-mailadres is ongeldig. Er kan slechts \u00E9\u00E9n adres worden ingevuld.
sap_configuration_network = Netwerk
PARTIALLY_ACCEPTED = Gedeeltelijk bevestigd
eReporting_collection = Selecteer de collectie
AAP_READY_STYLE = standaard
ordering_identifier_exists = Een opdrachtgevende partij met de identificatie {0} bestaat al
angular_url = Angulaire URL
error_files_too_large = Maximale bestandsgrootte is overschreden (1,7 Gb)
checkbox_banner = Banner activeren
AAP_NA_STYLE = info
related_process = Gerelateerde processen zoeken
PAYMENT_SENT_STYLE_STAGE = status_green
IN_VALIDATION = In validatie
REFERENTIAL_KO = Referentieel ko
clear_query = Duidelijk
METADATA_KO_STYLE = status_red
info_client_saved = Klant {0} opgeslagen
clientId_secretKey = ClientId geheime sleutel
AAP_MANUAL_MODE = A/c Plaatsing in handmatige modus
date_last_authentication_no_date = Laatste aansluitingsdatum : Informatie niet beschikbaar
UNARCHIVABLE_STYLE = status_red
instance_type_INVOICE = Factuur
portlet_invoice_forced = INVOICE: Kracht
angular_template = Sjabloon Angular
error_password_link_generation = Er is een fout opgetreden tijdens het genereren van de wachtwoordvernieuwingslink
cart_totalAmount = Totaal :
rte_input_file_must_be_unique = Geen uniek invoerbestand
error_exporting_client = Fout bij het exporteren van clients ({0})
aws_batch = AWS batch
environment_error_max_characters_allowed = Maximaal 64 tekens zijn toegestaan
error_export_not_empty_portlet = U kunt niet exporteren: de portlet is niet leeg
WKF_REFUSED_STYLE = status_red
Stage_SENT = Uitgegeven door het platform
authentication_togetherConfiguration = Samen configureren
info_client_removed = Klant {0} verwijderd
cart_numberOfArticles = artikel(en) in de winkelwagen
pageIconPlaceholder = ex: fa fa-bar-chart
order_history_title = GESCHIEDENIS VAN MIJN BESTELLING NR :
AVAILABLE_STYLE_STAGE = status_green
rte_status_not_deployed = Het RTE-script is niet uitgerold
BEING_SENT_CLIENT = Wordt naar de klant gestuurd
compliance_life_cycle_factor_confidential_permissions = Vertrouwelijke factor
fileName = Bestandsnaam
error_importing_page = Fout bij importeren
identical_new_passoword = Het nieuwe wachtwoord is identiek aan het oude wachtwoord
portlet_invoice_remove = INVOICE: Verwijder
RECEIVED_STYLE_STAGE = status_green
workflow_tab = Werkstromen
AAP_MISSING_REF_STYLE = status_red
WKF_IN_PROGRESS_STYLE = status_yellow
user_creation_date = Aanmaakdatum
cannot_assign_role = Je hebt toestemming om een rol toe te kennen, maar er is geen rol geconfigureerd.
SUBMITTED = Ingediend
action_line = Actie on line
organization_vatRegime = Optie voor het btw-stelsel:
NO_ROUTE = Geen route
workflow_checkbox = De configuratie van workflowmeldingen door gebruikers autoriseren
RCL_NA_STYLE = info
APPROVED_B2G_STYLE_STAGE = status_green
notification_new_contact = E-mailmeldingen nieuw contact
customer_clientUsers = Mijn gebruikers
placeholder_database_name = De naam van de instantie-database
compliance_life_cycle_partially_approve_permissions = Gedeeltelijk goedkeuren
OTHER = ANDERE
contact_recipients = Ontvangers van de contactmail
compliance_life_cycle_cash_in_permissions = Geld in
reconciliation_empty_seller_party = Afstemming niet mogelijk: leverancierscode ontbreekt.
template_type = Type sjabloon
assignable_roles = Toewijsbare rollen
accounting_posting_referentials = Verwijzingen
portlet_referentiel_addresses = Referenties/Adressen
not_valid = Niet geldig
partner_client_name = ${client} naam
Stage_APPROVED = Goedgekeurd
error_export_empty_page = U kunt geen lege pagina exporteren;
Stage_DISPUTED = Betwist
no_records_found_loreal_order = Er zijn geen artikelen die overeenkomen met uw zoekopdracht. <br/> We nodigen u uit om deze aan te passen.
authentication_preDomain = Pre-domein
AWAITING_VALIDATION_STYLE = status_orange
compliance_life_cycle_factor_permissions = Factor
accessTokenV1_tokenExpirationDate = Vervaldatum token
authenticationServer_tab = Authenticatieserver
rte_status_deployed = Het RTE-script is correct ge\u00EFmplementeerd
AAP_ERROR_STYLE = status_red
compliance_life_cycle_suspend_permissions = opschorten
compliance_life_cycle_refuse_permissions = Afval
manual_analytical_ventilation = Handmatige analytische ventilatie
accounting_posting_management_scenario = Scenario's
mark_doc_as_unread_warn = Alleen documenten met de status \"Gelezen\" kunnen worden gereset. Er wordt geen actie ondernomen.
file_exceeded_number = U kunt niet meer dan 10 documenten tegelijk uploaden
consultation_status = Wijzig de consultatiestatus van het document niet in\" Lees \"
customer_partner_edit = ${client}
info_import_processes = De processen zijn ge\u00EFmporteerd
document_statuses_added = {0} nieuwe status(sen) toegevoegd
dialog_message_sequence_edit = Berichtenreeks Bewerken
Development = Ontwikkeling
customer_partner_export = Exporteer ${client} bestand
rte_base_not_supported = RTE-collecties vallen (op dit moment) buiten de testscope
RCL_PRICES_DISCR = Rcl prijs discr.
client_type_SPECIFIC = partner
user_password_dialog_header = Wachtwoord toevoegen
REFERENTIAL_KO_STYLE = status_red
archive_storage_option = Archiefopslag
menu_user_customer_supplier_env_bank = Bankgegevens van mijn leveranciers
sap_configuration_serialization_format = Serialisatie-indeling
introduction_placeholder = Inleiding tekst Ex : \n De beschikbare documentatie is gecategoriseerd. Klik gerust op een categorie om de documenten te ontdekken die we ter beschikking stellen. Als een document ontbreekt, kunt u dit melden via de contactpagina.
self_register_placeholder_2_placeholder = Uniek verbindingsnummer veld van de zelfregistratie pagina
general_edi_service = Generix EDI-diensten
total_carts = Totaal mandjes :
placeholder_host_name = De hostnaam of het IP-adres van het systeem waarop de instantie draait
menu_process_trigger = Proces: Triggers
confirmOverrideFilter = Deze filter bestaat al. Wil je het vervangen?
add_new_status = Een nieuwe documentstatus toevoegen
customer_partner_edit_button = Update ${client}
notify_for_new_action_to_perform = Verwittigd worden telkens ik een actie moet uitvoeren op een factuur
customer_partner_add_user = Gebruiker toevoegen
exception_duplicate_perimeter = Dubbele perimeternaam: {0}
user_message_delete = De gebruiker wordt permanent verwijderd.
IN_PREPARATION = Voorbereiden
invidx_wkf_reinit = Herinitialisatie van informatie over workflows in Factuurindex
WKF_IN_PROGRESS = Wkf in uitvoering
counter_management_own_configuration = Specifieke instelling voor deze pagina defini\u00EBren
document_statuses_updated = {status is bijgewerkt
menu_process_deployment = Proces: Implementaties
RCL_INTRUDER_PROD_STYLE = status_orange
Production = Productie
PARTIALLY_ACCEPTED_STYLE = status_partially_accepted
DISPUTED_STYLE_STAGE = status_yellow
ie_recognition = Internet Explorer-herkenning
Acceptance = Acceptatie
warn_importing_multiLevel_iconPages = Het importeren kan niet omdat een \"pictogram\"-pagina maar \u00E9\u00E9n niveau kan hebben
portlet_standardorderlist_export_list = STANDAARD BESTELLIJST: Exportlijst
AAP_COMPLETED_AN_STYLE = status_green
to_prepare = In voorbereiding
rte_status_deployed_but_changed = Het RTE-script is gewijzigd sinds de implementatie ervan
sap_configuration_host = Gastheer
CONTROL_OK_STYLE = status_green
error_saving_perimeter = Fout bij opslaan omtrek ({0})
angular_version = Angular-versie
authenticationServer_configuration = Configuratie authenticatieserver
portlet_invoice_actions_10 = INVOICE: Actie 10
general_invoices_service = Generix Facturen Services
banner = Banner
partner_user_roles_save = wijzigen
api_permissions_roles = Rollen
host_name = Hostnaam
menu_user_customer_other_env_bank = Bankgegevens van mijn partners
error_existing_clientId = De clientId bestaat al in de realm, kies een andere clientId
select_validation_process = Post-validatieproces
EP_ACCEPTED = Verzoek geaccepteerd
estimated_delivery_date = Geschatte leverdatum :
ordering_party_delete_last_element = Wil je het laatste element verwijderen?
authenticationServer_btn = Verificatieserver configureren
delete_counter_error_message = Waarschuwing, het is niet mogelijk om deze teller te verwijderen omdat hij wordt gebruikt in minitellers.
info_category = Om de categorie\u00EBn te ordenen, is het nodig om de categorie van beneden naar boven te slepen.
importPageWarningText = De portlet waarnaar verwezen wordt bestaat niet en de link wordt niet gemaakt. Wilt u toch importeren?
error_password_generation = Er is een fout opgetreden tijdens het genereren van het wachtwoord
manage_periodic_reports = Beheer van periodieke rapporten
exception_remove_technicalUser_associated = De technische gebruiker(s) gekoppeld aan {0} konden niet worden verwijderd
banner_text = Tekst om weer te geven
CONTROL_TOTAL_KO = Bedrag controleert ko
self_register_placeholder_1 = Zelfregistratie Plaatshouder 1
self_register_placeholder_2 = Zelfregistratie Plaatshouder 2
authentication_defaultClientId = Standaard clientId
exception_technicalUsers_perimeterAssociation = Perimeter {0} heeft {1} technische gebruiker(s) geassocieerd
Category.name = Categorie
clients_type_INVOICE = partners
AAP_COMPLETED = Boeking voltooid
counter_management_title = Weer te geven tellers
authentication_partners = Partner
bank_account_name_required = De naam is verplicht
document_status = Documentstatus
user_number_connexion = Aansluiting(en)
receptionItem = ReceptionItemCodeField
api_permissions_users = Gebruikers
perimeters = Perimeters
AAP_MISSING_PO_STYLE = status_red
technical_user_partner = Partner
ROLE_TECHNICAL = Technische rol
Stage_UPLOADED = Ge\u00FCpload
confirmationTitle = Bevestiging
portlet_demat_partner_file = DematPartnerFile
SYSTEMATIC = Maak altijd het bestand
life_cycle = Levenscyclus
counter_management = Beheer van minitellers
outputFile_working_copy = Een [werk]kopie [van het uitvoerbestand] maken
element_contextmenu_config_form = Het formulier configureren
secret_api_key = SecretAPIKey
accounting_posting_entries = Boekingen
edit_technical_user = Technische gebruiker bewerken
bql_filter_details = BQL-query Factuur portlet filteren
Stage_APPROVED_B2G = Goedgekeurd (B2G)
portlet_order_actions_10 = ORDER: Actie 10
button_message_sequence = Berichtenreeks
listbox_message_sequence_available_messages = Beschikbare berichten
customer_partner_delete_yes = Ja, ik verwijder het
portlet_order_confirm_with_modification = ORDER: Bevestigen met wijziging
user_phone = Telefoon
error_invalid_date = {0} : de ingevoerde inhoud is geen datum : {1}
AAP_EXPORTED = A/c Uitgevoerde post
customer_partner_import_button = Het bestand importeren
warm_change_into_empty_when_other_exists = Je kunt deze lay-out pas in een lege lay-out veranderen als er andere lay-outs op deze pagina staan
filter_by_type = Filter op type
portlet_invoice_export_list = INVOICE: Exportlijst
workflow_functions = Functies
organization_VR1 = Onderneming onderworpen aan het normale maandelijkse re\u00EBle regime
organization_VR3 = Onderneming onderworpen aan het vereenvoudigde btw-belastingstelsel
organization_VR2 = Onderneming die voor de normale driemaandelijkse regeling heeft gekozen
organization_VR4 = Onderneming die profiteert van de franchiseregeling op basis van btw
error_creating_partnerConfig = Fout bij het maken van de authenticatieconfiguratie voor partner {0}
customer_partner_add_user_dialog_header = Gebruiker toevoegen aan ${client}
invoice_posting_manual_entry = Handmatige accountinvoer
portlet_indexdata_export = INDEXDATA: Exporteren
user_login_validator = De id van de gebruiker moet een e-mailadres zijn.
file_docs_uploaded = De documenten zijn ge\u00FCpload
select_duplicate_check = Dubbele controle
invoice_posting = Factuur posten
compliance_life_cycle_approve_permissions = Goedkeuren
cookie = Cookie
no_processed = Niet verwerkt
warm_add_empty_when_other_exits = Je kunt geen lege dispositie toevoegen als er al een dispositie bestaat
instance_type = Type portaal
portlet_invoice_actions_3 = INVOICE: Actie 3
portlet_invoice_actions_4 = INVOICE: Actie 4
portlet_invoice_actions_1 = INVOICE: Actie 1
portlet_invoice_actions_2 = INVOICE: Actie 2
portlet_invoice_actions_7 = INVOICE: Actie 7
Stage_APPROVED_PARTIALLY = Gedeeltelijk goedgekeurd
portlet_invoice_actions_8 = INVOICE: Actie 8
portlet_invoice_actions_5 = INVOICE: Actie 5
triggername_ACTION_10 = Actie 10
portlet_invoice_actions_6 = INVOICE: Actie 6
COMPLETED_STYLE_STAGE = status_yellow
workflow_delegations = Delegaties
RCL_QTY_DISCR = Rcl qty discr.
UNARCHIVABLE = Onnavolgbaar
role_type = Type
user_firstname_placeholder = Je voornaam
portlet_standardorderlist_actions_10 = STANDAARD BESTELLIJST: Actie 10
IN_DELIVERY = Bij levering
portlet_invoice_actions_9 = INVOICE: Actie 9
Accounting_Active = Actief
idp_alias_for_usurpation = IDP alias voor usurpatie
kpis = KPI's
edit_library = Een bibliotheek bewerken
RCL_READY_STYLE = standaard
partner_user_delete = Gebruiker verwijderen
url_tracking = URL-tracking inschakelen voor elke gebruikte log
my_bank_accounts = Mijn bankrekeningen
partner_user_message_send_new_pass_msg = Er wordt een e-mail met een link naar de gebruiker gestuurd, waarmee hij zijn wachtwoord kan wijzigen.
invidx_wkf_replayed = Herhaalde workflows : {0}
api_security = Beveiliging
Api_configuration= configuratie
customer_partner_import_header = Importeer ${client} bestand (*.xls, *.xlsx)
library_created_success = De documentatie is gemaakt
portlet_orderresponse_import = ORDER RESPONS: Importeren
api_permissions_get_perimeters = Perimeters
periodic_reports = Periodieke rapporten
END_PROCESS = Einde proces
contextual_validation_partner_role = Rol moet bestaan
MISSING_PROCESSINGWAY = Ontbrekende verwerkingsmethode
clientUsers = Mijn gebruikers
save_and_send_renewal_link = Vernieuwingslink opslaan en verzenden
authentication_classicConfiguration = Klassieke configuratie
document_status_updated = De documentstatus {0} is bijgewerkt
element_order_response = Reactie op bestelling
rte_test_properties_placeholder = Eigenschappen:
in_delivery = Bij levering
rte_forbidden_value = init.tst: onjuiste waarde voor eigenschap {0}
EP_REFUSED = Verzoek geweigerd
accounting_posting_referentials_product = Producten
AAP_EXPORTED_STYLE = status_deep_blue
Stage_PAYMENT_RECEIVED = Ontvangen betaling
AAP_TRANSMITTED_STYLE = status_green
customer_partner_create_button = Voeg ${client}
delete_button_message_sequence = Verwijder
regex = Regex
ordering_name = Naam
jsf_templates = Sjablonen Jsf
portlet_invoice_add = INVOICE: Toevoegen
sap_configuration = SAP
not_notified_if_absent_and_have_replacement = Geen melding te krijgen als ik afwezig ben en iemand anders mij vervangt
uploaded_since = Ge\u00FCpload op
error_localizations_import_bad_structure = De bestandsstructuur voldoet niet aan de verwachte indeling
customer_partner_message_delete = De ${client} wordt permanent verwijderd.
exception_technicalRole_association = Rol {0} heeft {1} technische gebruiker(s) gekoppeld
WKF_READY_STYLE = standaard
mark_docs_as_unread_warn = Sommige documenten hebben niet de status \"Gelezen\". Er is geen actie ondernomen op deze bestellingen.
error_export_portlet_rte_collection = Je kunt de portletverzameling niet exporteren.
database_name = Naam database
to_edit = Bewerk
partner_user_search_placeholder = Zoek gebruiker op n\u00B0, ${client} naam, gebruiker, voornaam of achternaam
portlet_invoice_export = INVOICE: Exporteren
Stage_REFUSED = Geweigerd
template_name = Naam sjabloon
portlet_payment_actions_10 = BETALING: Actie 10
info_import_portal = Het portaal is ge\u00EFmporteerd
edit_status = Een documentstatus bewerken
AAP_NA = A/c Posting niet van toepassing
scope_customer = Beperkte reikwijdte
AAP_MISSING_RCP = A/c Boeking - ontbrekend Rcpt
AAP_ERROR = A/c Boeking foutief
com_byzaneo_xtrade_api_DocumentConsultStatus = Status raadplegen
active_carts = wagen(s) actief
menu_user_addresses = Adressenboek van mijn bedrijf
authentication_defaultClientIdJSF = Standaard clientIdJSF
doc_status_code = Statuscode
general_customers_service = Generix klantenservices
RCL_IMPOSSIBLE_STYLE = status_red
category_field_empty = het categorieveld moet worden ingevuld.
partner_company = Klant
info_user_role_duplicated = De rol {0} werd gedupliceerd
AAP_MISSING_REF = A/c Boeking - ontbreekt Verwijzen
Stage_SUSPENDED = In de wacht
customer_partner_userNumber = Aantal gebruikers
no_processes_were_found = De RTE wordt door geen enkel proces aangeroepen
invalid_role = Je hebt niet de rechten om toegang te krijgen tot de bron.
info_perimeter_saved = Perimeter {0} opgeslagen
Stage_OPENED = Geopend
error_export_shadow_portlet = Je kunt deze pagina niet exporteren omdat deze ten minste \u00E9\u00E9n \"Shadow\" portlet bevat
rte_status_unknown = De bestandsstatus kon niet worden bepaald
status_import_file = Documentstatus importeren (*.xlsx)
save_register = Ik ga akkoord met registratie
error_saving_client = Fout bij opslaan client ({0})
error_deletingCompanyWithAuthConfig = Dit bedrijf is gekoppeld aan de authenticatieserverconfiguratie. Verwijder eerst de configuratie met betrekking tot dit bedrijf voordat u het verwijdert.
AAP_TRANSMITTED = A/c Posting verzonden
contact_subject = Onderwerp van de contact-e-mail
user_email_placeholder = E-mailadres dat zal worden gebruikt om toegang te krijgen tot de account
SIGNATURE_KO_STYLE = status_red
info_technicalUser_role_saved = Technische gebruikersrol opgeslagen
orderItem = OrderItemCodeField
Stage_PAYMENT_SENT = Uitgegeven betaling
filter_sending = Verzenden
technical_user_selectPartner = Selecteer een partner
AAP_MISSING_PO = A/c Boeking - ontbrekende PO
error_existingAuth_clientId = De clientId {0} bestaat al in het rijk, kies een andere clientId
SENT_STYLE_STAGE = status_green
error_saving_invoice_entry_preferences = Fout bij het opslaan van voorkeuren voor factuurinvoer
sso_error = Er is een fout opgetreden tijdens het verifi\u00EBren.
Stage_RECEIVED = Ontvangen door het platform
authentication_defaultClientIdAngular = Standaard clientIdAngular
triggername_ACTION_2 = Actie 2
triggername_ACTION_1 = Actie 1
triggername_ACTION_4 = Actie 4
triggername_ACTION_3 = Actie 3
triggername_ACTION_6 = Actie 6
triggername_ACTION_5 = Actie 5
triggername_ACTION_8 = Actie 8
triggername_ACTION_7 = Actie 7
triggername_ACTION_9 = Actie 9
authentication_clientIdJSF = ClientIdJSF
compliance_life_cycle_payment_sent_permissions = Betaling verzonden
info_addressSearch = Het is niet mogelijk om op landveld te zoeken
imenu.portal = Samenwerken
APAUTOMATION = AP AUTOMATISERING
in_validation = In validatie
error_remove_partnerConfig = Authenticatieconfiguratie voor partner {0} is niet verwijderd.
authentication_selectPartner = Selecteer een partner
counter_management_pages = Pagina's
portlet_library = Bibliotheek
ordering_partners = Partners
error_importing_Languages = Er is een fout opgetreden tijdens het importeren van het lokalisatiebestand van portlets
bank_account_iban_error_exist = Sorry, maar er is al een IBAN gekoppeld aan de geselecteerde valuta. Verwijder de IBAN voordat u een nieuwe toevoegt.
rte_collection = Collectie
invalid_password = Ongeldig wachtwoord. Voer een nieuw wachtwoord in.
ok_switch = Ok
validated = Gevalideerd
customer_partner_delete_no = Nee, ik denk
menu_user_invoice_entry_preferences = De invoervoorkeuren voor facturen van mijn bedrijf
RCL_DISCREPANCY_STYLE = status_orange
partner_user_roles = Rol voor gebruiker
error_updating_user_keycloak = Mislukt bij het bijwerken van keycloak gebruiker {0}
scope_partner = Uitgebreide reikwijdte
SMTP_ERROR_STYLE = status_red
portlet_payment_accept_reject_early_payment = Verkoper Vervroegde betaling
AAP_MANUAL_MODE_STYLE = status_yellow
WKF_NA_STYLE = info
authentication_addPartnerConfiguration = Partnerconfiguratie toevoegen
jsf_template = Sjabloon Jsf
select_legal_controls_label = Wettelijke controles in wetgeving toepassen
exception_import_page_null = Pagina in exportbestand is nul
api_permissions_get_partners = Partners
info_import_page = Pagina importeren ok
portlet_referentiel_exchange_rates = Referentiekoersen/wisselkoersen
Stage_AVAILABLE = Beschikbaar
partner_user_send_new_pass = Wachtwoord opnieuw instellen
organization_eReporting = Optie voor eRapportage:
portlet_standardorderlist_duplicate = STANDAARD BESTELLIJST: Duplicaat
bank_account_bic_error_required = De BIC-code is verplicht
error_creating_companyConfig = Fout bij het maken van authenticatieconfiguratie voor bedrijf {0}
users_number_exceeded = Aantal gebruikers voor ${client} is overschreden. Verwijder inactieve gebruikers of neem contact op met support
client_type_SUPPLIER = leverancier
instance_type_INVOICE_PDP = Factuur - PDP
requested_delivery_date = Gevraagde leverdatum :
Stage_NOT_APPROVED_B2G = Niet goedgekeurd (B2G)
organization_referenceCurrency = Referentievaluta
ROLE = Portaal rol
rte_test_results_header = Rte-testresultaten
imputation_status_unprocessed = Status indien geen toerekeningsscenario van toepassing
reconciliation = Afstemming
user_email_address = E-mail
METADATA_KO = Metadata ko
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE = status_deep_blue
rte_test_fail_message = Rte-test uitgevoerd met mislukking
APPROVED_STYLE_STAGE = status_green
reconciliation_info = Configuratie van de zoeksleutel die wordt gebruikt om rijafstemming uit te voeren
AAP_READY = Klaar voor A/c-boeking
locale_not_defined = Je taal is niet gedefinieerd in het profiel van je partner. Vul deze taal in
REFERENTIAL_OK_STYLE = status_green
portlet_invoice_view_history = INVOICE: Controle
counter_management_counter = Tegen
no_files_choosen = Geen bestanden gekozen
SIGNATURE_OK_STYLE = status_green
clients_type_SPECIFIC = partners
order_number = Bestelnummer
partner_postal_code = Postcode
info_perimeter_removed = Perimeter {0} verwijderd
warn_deleting_own_user = Het is niet mogelijk om de gebruiker {0} te verwijderen. Dit is je eigen gebruiker.
customer_partner_number = ${client} nr.
rte_studio = RTE Studio
portlet_standardorderlist_refuse = STANDAARD BESTELLIJST: Handmatig afval
bql_instructions_documents = Document
portlet_invoice_correct = Factuur: Correct
reconciliation_gap_found = Afstemming gedaan, verschillen gevonden.
select_rte_label = RTE bron van controles
WKF_READY = Klaar voor wkf
select_rte_source = Selecteer
CONTROL_TOTAL_KO_STYLE = status_red
invalid_file_accent_char = Het bestand kan geen tekens met accenten bevatten
customer_partner_connectionCode = Unieke aansluiting Nr.
self_register_placeholder_1_placeholder = Nummerveld ${client} van de zelfregistratiepagina
comment_upload_file = Voeg een commentaarlink toe aan je bijlage
info_parameters_saved = Gebruikersparameters opgeslagen
RCL_IMPOSSIBLE = Rcl niet mogelijk
add_page = Een pagina toevoegen
connection_code = Uniek verbindingsnummer
organization_role = Rol
element_arrow_optional = Optioneel
add_user_mail_button = Gebruiker toevoegen + Wachtwoord resetten
element_shipping_advice = Verzendadvies
sap_configuration_expiration_check = Verlopen controleperiode
exception_perimeter_has_users = Perimieter {0} heeft {1} gebruiker(s) geassocieerd
ap_automation_aged_balance = Ouderdomsbalans
ordering_party_exit_without_saving = Wil je afsluiten zonder het diagram op te slaan?
workflow_monitoring = Bewaking
instance_type_EDI = EDI
portlet_payment_suggest_early_payment = Koper Vervroegde betaling
add_sso_role_mapping = Een roltoewijzing toevoegen
WKF_NA = Wkf niet van toepassing
accessTokenV1_password = Wachtwoord
error_saving_company = Fout bij het opslaan van bedrijf.
page_display_option = Niet weergeven in het menu
customer_partner_import = Importeer ${client} bestand
customer_technical_users = Technische gebruikers van klanten
sap_configuration_repository_optimization = Archief rondreis optimalisatie
self_register = Zelfregistratie
AAP_IN_PROGRESS_AN_STYLE = status_yellow
error_invalid_row_length = {0},{1}] Ongeldige rijlengte verwacht ({2}) gevonden ({3})
localisation = Lokalisatie portaal
sap_configuration_user = Gebruiker
reconciliation_empty_order_number = Afstemming niet mogelijk: ordernummer ontbreekt.
doc_update_information = Mijn gegevens bijwerken
error_invalid_number = {0} : De ingevoerde inhoud is geen getal : {1}
bank_accounts = Bankrekeningen
quantityValue_validator = De waarde van de hoeveelheid is ongeldig
ap_automation_tab = AP automatisering
info_partner_role_saved = Partnerrol opgeslagen
error_input_content_not_valid = De ingevoerde inhoud is ongeldig : {0}
error_switching_user_no_idp = Om te kunnen wisselen, moet de IDP alias voor usurpation geconfigureerd zijn op de primaire groep van de gebruiker.
CUSTOMERS = KLANTENPORTALEN
error_creating_user_keycloak = Login existiert bereits in KC. Es kann nicht erstellt werden.
sap_configuration_client = Klant
authentication_clientId = ClientId
Accounting_Inactive = Inactief
REFERENTIAL_ERROR = Fout in directory
menu_rights_perimeters = Perimeters
DEPRECATED = GEDEELTE
companyinformation = Mijn bedrijfsinformatie
error_export_empty_portlet = Je kunt geen lege portlet exporteren;
portlet_invoice_archive_delete = INVOICE ARCHIVE: Verwijderen
AAP_COMPLETED_STYLE = status_green
bank_account_delete_confirm = Weet je zeker dat je de IBAN voor de valuta wilt verwijderen?
portlet_invoice_modify = INVOICE: Wijzigen
period_specific_to_february = Periode specifiek voor februari
arn = IAM-rol ARN
angular_templates = Angular-sjablonen
AAP_MISSING_AN_REF = A/n Posting ontbreekt Verwijs
menu_ordering_party = Bestellende partij
add_button_message_sequence = Voeg  toe
accessTokenV1_token = Penning
sap_configuration_expiration_time = Vervaltijd
client_type_CUSTOMER = klant
BEING_PROCESSED_STYLE = status_orange
portlet_invoice_archive_extend_archiving_duration = INVOICE ARCHIVE: Duur archivering verlengen
PARTIALLY_SHIPPED_ICON = fa fa-paper-plane
sso_role_mapping = Rollen in kaart brengen
canNotSaveFilter = U kunt dat filter niet opslaan omdat de lokale opslag vol is
AAP_EXPORTED_TIMELINE = Boekhouden: exporteren van boekingen
exception_not_existing_partner = Partner {0} bestaat niet
absence = Afwezigheid
run_rte = Ren
accounting_posting_tab = Boekhoudkundige boeking
error_creating_clientId = Fout bij het aanmaken van clientId {0}
portlet_reception_remove = RECADV: Verwijderen
customer_partner_address_city = Stad
general_supplier_service = Generix Leveranciersdiensten
AWAITING_VALIDATION = In afwachting van validatie
quicksight = QuickSight
select_legal_controls_source = Selecteer
error_view_secret_key = Fout bij het bekijken van geheime sleutel
CONTROL_OK = Besturing ok
SMTP_ERROR = Smtp-fout
menu_user_partner_bank = Bankgegevens van mijn bedrijf
accounting_posting_referentials_supplier = Leveranciers
client_type_INVOICE = partner
compliance_life_cycle_in_dispute_permissions = In geschil
user_mobile = Mobiel
TO_PAY = Betalen
organization_ER2 = Ik vraag Generix om geen facturatiegegevens te extraheren, ik geef er de voorkeur aan om Generix alle eReporting-gegevens in een aparte feed te leveren.
user_login = Gebruiker
organization_ER1 = Ik vraag Generix om de internationale B2B-factureringsgegevens te extraheren en beloof om in een aparte stroom de factureringsgegevens te verstrekken van facturen die niet via het PDP zijn gepasseerd.
info_technicalUser_saved = Technische gebruiker {0} opgeslagen
partner_city = Stad
error_exporting_page = Export mislukt
accounting_posting_management_setting = Instellingen
ordering_number_of_partners = Aantal partners
info_perimeters = Selecteer hier de naam van een groep entiteiten.
menu_company_addresses = Adressen van bedrijven
partner_user_roles_perimeters = Rol en perimeter voor gebruiker
jsf_url = JSF URL
contact_validator = Het e-mailadres van de contactpersoon is ongeldig. Meerdere adressen zijn toegestaan, gescheiden door ';'.
placeholder_tcp = De TCP-poort waarop de instantie luistert
portlet_orderresponse_print = BESTELLINGSRESPONS: Afdrukken
document_status_removed = De documentstatus {0} is verwijderd
bank_account_iban_error_notValid = Het IBAN is ongeldig
period_except_february = Periode behalve februari
customer_partner_name_for_user = ${client} naam
field_missing = Het veld \"{0}\" ontbreekt.
client_api_id = ClientAPIId
PAYMENT_RECEIVED_STYLE_STAGE = status_green
cron_expression = Cron uitdrukking
no_message_display = Dit bericht niet meer tonen
add_library = Een bibliotheek toevoegen
connections = Verbindingen
filter_direction = Doorstroomrichting portlet
save_button_message_sequence = Sla
partner_user_create = Gebruiker toevoegen
permission_missing = Je hebt onvoldoende rechten om deze functie te gebruiken
bank_account_bic_error_notValid = De BIC-code moet 8 tot 11 tekens bevatten.
RCL_INTRUDER_PROD = Rcl - Inbraakproduct
portlet_payment_actions_8 = BETALING: Actie 8
portlet_payment_actions_9 = BETALING: Actie 9
portlet_payment_actions_2 = BETALING: Actie 2
portlet_payment_actions_3 = BETALING: Actie 3
portlet_payment_actions_1 = BETALING: Actie 1
use_ordering_party_configuration = Gebruik de configuratie van de besteller
portlet_payment_actions_6 = BETALING: Actie 6
portlet_payment_actions_7 = BETALING: Actie 7
portlet_payment_actions_4 = BETALING: Actie 4
invalid_file_special_char = Het bestand kan geen speciale tekens bevatten
portlet_payment_actions_5 = BETALING: Actie 5
error_creating_authConfig = Fout bij het maken van de serverauthenticatieconfiguratie
document_status_added = De documentstatus {0} is toegevoegd
portlet_invoice_open = INVOICE: Open
library_document_introduction = De beschikbare documentatie is gecategoriseerd. Klik gerust op een categorie om de documenten te ontdekken die we tot je beschikking stellen.
duplicate_mapping = Deze mapping bestaat al.
RCL_NA = Rcl niet van toepassing
element_not_removed = Eerste element kon niet worden verwijderd
document_status_exists = De documentstatus {0} bestaat al
portlet_invoice_archive_bulk_export = INVOICE ARCHIVE: Bulk export
SIGNATURE_KO = Handtekening ko
info_limited_extended_scope = Geef in deze velden aan tot welke entiteiten deze gebruiker toegang heeft. U kunt de naam van een of meerdere entiteiten of de naam van een groep entiteiten invullen.
partners_ordering_party = Partners van de besteller
portlet_standardorderlist_validate = STANDAARD BESTELLIJST: Handmatig valideren
warn_kpi_invalid = De KPI-service kan niet worden gestart. Probeer het later nog eens of neem contact op met uw beheerder
error_remove_companyConfig = Authenticatieconfiguratie voor bedrijf {0} is niet verwijderd.
error_saving_technicalUser = Fout bij het opslaan van technische gebruiker {0}
exception_role_has_sso = Rol {0} heeft sso rolmapping(en) geassocieerd
sap_configuration_language = Lang
AAP_MISSING_RCP_STYLE = status_red
customer_partner_delete = Verwijder ${client}
OPENED_STYLE_STAGE = status_green
user_last_authentication = Laatste verbinding
portlet_invoice_import = INVOICE: Importeren
RCL_QTY_DISCR_STYLE = status_orange
error_removing_perimeter = Fout bij het verwijderen van de omtrek ({0})
error_token_exchange_disabled = Gebruiker wisselen is niet beschikbaar
ordering_identifier = Identificatiecode
END_PROCESS_STYLE = status_green
ereporting = Rapportage
instance_type_CUSTOMER = Klant
SIGNATURE_OK = Handtekening ok
generate_reset_link_expired = De wachtwoordvernieuwingslink is verlopen.
accessTokenV1_url = URL
WKF_VALIDATED = Wkf gevalideerd
accessTokenV1_use = Gebruik
one_hour_past = minder dan een uur
edit_ordering_party = Partij bestellen Bewerken
control_ean = Lengte van EAN-codeveld controleren
workflow_status_unprocessed = Status indien geen workflow van toepassing
Stage_COMPLETED = Voltooid
workflow_management = Beheer
no_order_from_asn = Geen bestelling gevonden van de geselecteerde vooraankondiging
NO_GENERATION_IF_EXISTS = Geen bestand als het al bestaat
exception_export_null = Geen export gevonden in bestand
quick_search_loreal_order = Snel zoeken op Merk, artikel, sign code, EAN
default_test_rte = Standaard test
import_role_header = Rollenbestand importeren (*.xls, *.xlsx)
info_perimeter_duplicated = De omtrek {0} werd gedupliceerd
technical_user_clientId = ClientId
generate_reset_link_invalid = Ongeldige wachtwoordvernieuwingslink. Neem contact op met uw beheerder.
eReporting_frequency = Frans eReporting frequentie
doc_status_style = Statusstijl
partner_user_add_role = Geef een rol
exception_exchange_associated = {0} uitwisseling(en) toegestaan geassocieerd
error_creating_authConfig_missing_config = Om de authenticatieconfiguratie te maken, moeten de authenticatieparameters van de groep worden ingesteld (Realm name, ClientAPIId, SecretAPIKey en IDP alias voor usurpation)
UPLOADED_STYLE_STAGE = status_green
info_invoice_entry_preferences_saved = Voorkeuren voor factuurinvoer opgeslagen
text_to_double_message = moet een getal zijn dat uit \u00E9\u00E9n of meer cijfers bestaat.
roles_bo = Back Office Rollen
self_register_ok = Registratie succesvol uitgevoerd! <br>Een wachtwoord vernieuwingslink is naar u verzonden per e-mail.
transactions = Transactie
clients_type_SUPPLIER = leveranciers
api_permissions_get_invoice = Facturen
api_permissions_translation = Vertalingen
filter_receiving = ontvangen
ordering_description = Beschrijving
sap_configuration_password = Wachtwoord
error_deletingPartnerWithAuthConfig = Deze partner is gekoppeld aan de authenticatieserverconfiguratie. Verwijder eerst de configuratie voor deze partner voordat u deze verwijdert.
partner_user = Gebruiker
NOT_APPROVED_B2G_STYLE_STAGE = status_red
payments = Betalingen
instance_type_SUPPLIER = Leverancier
angular_page = Angular pagina
WKF_REFUSED = Wkf geweigerd
EP_PERFORMED = Betaling uitgevoerd
exception_not_match_companyCode = {0} kwam niet overeen met een bedrijfscode
add_user_button = Gebruiker toevoegen
tcp_port = TCP-poort
ap_automation_aged_balance_setting = Weergave
portlet_referentiel_carrier = Referentieel/Drager
REFERENTIAL_ERROR_STYLE = status_red
user_password_confirm = Wachtwoord bevestigen
author = Auteur
info_technicalUser_removed = Technische gebruiker {0} verwijderd
invidx_wkf_reinit_btn = Herinitialisatie
menu_user_customer_client_env_bank = Bankgegevens van mijn klanten
self_register_ko = Er is een probleem opgetreden tijdens de registratie, bedankt voor het proberen later
warn_portlet_localization_lang_not_supported = {0} taal niet ondersteund door de omgeving
perimeter = Perimeter
site_conditions_link = Algemene voorwaarden
user_lastname_placeholder = Je achternaam
BEING_PROCESSED = Wordt verwerkt
subtype = Subtype
info_ordering_saved = Besteller {0} opgeslagen
REPLACE_IF_EXISTS = Oudere bestanden vervangen
individual_tests_rte = E\u00E9n test
menu_invoice_entry_preferences = Voorkeuren voor factuurinvoer
page_submenu_warning = Het gebruik van een menu van 3 niveaus kan weergaveproblemen veroorzaken. Vergeet niet om de weergave van het menu op de voorkant te controleren.
Stage_REJECTED = Afgewezen
control = Controleer
contextual_validation_partner_e_reporting = eRapport moet ER1, ER2 of null zijn
partner_with_code_missing = ${client} nummer en/of registratienummer bestaan niet
bql = BQL
portlet_invoice_ocr_verify = INVOICE: OCR controleren
MISSING_PROCESSINGWAY_STYLE = status_red
existing_templates = Deze sjablonen bestaan al in de omgeving, selecteer de sjablonen die u wilt overschrijven met de nieuwe versie in dit procesarchief. Als je de bestaande sjablonen niet wilt overschrijven, selecteer dan geen sjabloon en valideer de import om het proces te importeren.
AAP_COMPLETED_AN = A/n Plaatsing voltooid
library_edited_succes = De documentatie is bewerkt
info_portlet_localization_import = De lokalisatie van het portaal is succesvol afgerond. {0} bericht(en) toegevoegd en {1} gewijzigd
REJECTED = Afgewezen
partner_technical_users = Technische contacten
error_importing_portlet = Pagina is niet leeg, je kunt hier geen json importeren
info_notifications_saved = Gebruikersmeldingen opgeslagen
reconciliation_start = Verzoening begonnen.
EP_INELIGIBLE = Niet-subsidiabel
EP_SUBMITTED = Aanvraag in behandeling
users_creation = Gebruikers/wachtwoord aanmaken (geen link)
invalid_file_csv_type = De extensie van het bestand moet CSV zijn
rte_property_unknown = init.tst: onbekende {0} eigenschap
REJECTED_STYLE_STAGE = status_red
menu_security_technical_users = Back Office Technische Gebruikers
add_technical_user = Cre\u00EBer technische gebruiker
database_kpi = KPI-database
sap_configuration_sysnr = Sysnr
bank_accounts_dlg_header = Bankrekening toevoegen
error_file_too_large = Maximale bestandsgrootte is overschreden (2 Gb)
portlet_contact_us = Neem contact met ons op
exception_perimeter_has_partners = Perimieter {0} heeft {1} geassocieerde partner(s)
portlet_invoice_print = INVOICE: Afdrukken
user_empty_client = Kies een ${client}...
AAP_IN_PROGRESS_AN = A/n Boeking in uitvoering
eReporting_tab = E-rapportage
element_checkbox_title_mandatory = Verplicht
info_ordering_model_saved = Model besteller Gered
RCL_PRICES_DISCR_STYLE = status_orange
by_date = {0}
imenu_integration_recent_mapping = Recente kartering
general_invoice_pdp_service = Generix Factuurdiensten
compliance_life_cycle_subrogate_permissions = Subrogaat
imenu_portlet = Portlets
bank_account_deleting_error = Fout bij het verwijderen van bankrekening {0}
realm_name = Naam koninkrijk
dedicated_instance = Toegewijd
workflow_absences = Afwezigheden
keycloak = Sleutelhanger
add_icon_page = Een pictogrampagina toevoegen
sap_configuration_pool_size = Capaciteit zwembad
bank_account_iban_error_required = Het IBAN is verplicht
save_success = De hele bibliotheek werd gered.
SUSPENDED_STYLE_STAGE = status_yellow
workflow_checkBox_help = Als het vakje is aangevinkt, kan de gebruiker in zijn profiel kiezen of hij op de hoogte wil worden gebracht van elk nieuw document.
warn_import_pt_collection = Als de portaalsite \"collectieportlets\" gebruikt, is een configuratie van die portlets verplicht.
reconciliation_order_not_found = Afstemming niet mogelijk: ordernummer {0} niet gevonden.
technical_roles = Technische rol
customer_partner_show_users = Gebruikers gekoppeld aan client
reconciliation_status_unprocessed = Status indien geen afstemming van toepassing
view_secretKey = Geheime sleutel bekijken
portlet_orderresponse_export = ORDER RESPONS: Exporteren
RCL_SUCCESS = Geslaagd rcl
validity_date = Geldigheidsdatum:
ordering_messages_sequence = Berichtenreeks
authentication_configurationType = Type configuratie
taxOriginal = EDI-bestand instellen als fiscaal origineel
action_global = Wereldwijde actie
portlet_order_mark_as_unread = ORDER: Markeren als ongelezen
compliance_life_cycle_cdv_complete_permissions = CDV: Compleet
portlet_invoice_view_attachment = Factuur: Bijlage bekijken
cancel_button_message_sequence = Annuleren
customer_partner_search_placeholder = Zoek ${client} op nummer, naam of stad
error_remove_technicalUser = Technische gebruiker niet verwijderd
authentication_clientIdAngular = ClientIdAngular
portlet_order_actions_9 = ORDE: Actie 9
portlet_order_actions_8 = ORDE: Actie 8
element_invoice = Factuur
workflow_template = Sjabloon
tools = Gereedschap
dialog_message_sequence_add = Berichtenreeks Toevoegen
RESENT_STYLE = status_green
advSearch = Geavanceerd zoeken
connection_id = Uniek identificatienummer
invoiceItem = InvoiceItemCodeField
accessTokenV1_getTokenBtn = Een token krijgen
ACCOUNTINGPOSTING = BOEKHOUDKUNDIGE VERWERKING
contextual_validation_partner_vat_regime = Vat Regime moet VR1, VR2, VR3, VR4 of nul zijn.
api_messages = Berichten
portlet_checklistsmanagement = Beheer van checklists
PARTIALLY_SHIPPED_STYLE = status_yellow
warn_localizations_import_portlet_not_found = Portlet {0} niet gevonden in de portaaldefinitie
AAP_IN_PROGRESS = Lopende boekingen
RESENT = Opnieuw
portlet_order_actions_1 = ORDER: Actie 1
Preproduction = Pre-productie
portlet_order_actions_3 = ORDER: Actie 3
portlet_order_actions_2 = ORDER: Actie 2
portlet_order_actions_5 = ORDER: Actie 5
portlet_order_confirm = ORDER: Bevestig
portlet_order_actions_4 = ORDER: Actie 4
portlet_order_actions_7 = ORDE: Actie 7
portlet_order_actions_6 = ORDE: Actie 6
exception_task_properties_not_found = Onmogelijk om taak {0} te importeren, een stijging van versie is noodzakelijk
edit_user_button = Gebruiker bijwerken
self_register_bottom_msg = Zodra de registratie is voltooid, ontvang je een e-mail met je tijdelijke wachtwoord. Dit wachtwoord moet worden gewijzigd wanneer u voor het eerst toegang krijgt tot de account.
library_deleted_success = De documentatie is verwijderd
rte_no_init_test_file = Geen init.tst standaardbestand gevonden
rte_test_success_message = Rte-test succesvol uitgevoerd
filter_indifferent = Onverschillig
WKF_VALIDATED_STYLE = status_green
warn_partner_missing = Gebruiker ontbreekt (zie parameter pcode)
partner_user_send_pass_no = Nee, ik denk
category = Categorie
idp_invalid_tokenUrl_error = De eigenschap \u201CToken URL\u201D is niet geldig. Deze moet het patroon volgen: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/token<br/>
adminClient_authorizationEnabled_disabled_error = De client met clientId {0} heeft de eigenschap 'Authorization Enabled' niet ingeschakeld
search_by_project = Een project zoeken
idp_linkingOnly_disabled_error = De eigenschap \u201CAlleen accountkoppeling\u201D is niet ingeschakeld<br/>
idp_invalid_firstLoginFlow_error = De eigenschap \u201CFirst Login Flow\u201D moet de waarde \u201C{0}\u201D hebben<br/>
error_switching_user_no_authConfig = De gebruikersfunctionaliteit van de switch is niet beschikbaar: Er is geen verificatieconfiguratie toegevoegd
keycloak_realm_required = Keycloak realm is vereist
double_filter_error = Het is niet mogelijk om beide filters tegelijkertijd te gebruiken
keycloak_client_id_required = Keycloak client ID is vereist
switchUser_enabled_info = De benodigde configuraties om de functionaliteit voor switch-gebruikers in te schakelen zijn toegevoegd voor client {0} in realm {1}
idp_invalid_syncMode_error = De eigenschap \u201CSync Mode\u201D moet de waarde \u201C{0} hebben\u201D<br/>
channel_coded_message = Ge\u00EBncrypteerd bericht
idp_configurations_error = Fouten bij de IDP-configuratie:<br/>
error_reconfigureClientsForSwitching = Fout bij het configureren van clients voor schakelen
keycloak_client_secret_required = Keycloak client geheim is vereist
serviceAccountUser_tokenExchangePermission_error = De client met clientId {0} heeft niet de benodigde rechten om te controleren of de permissie 'token-uitwisseling' is ingeschakeld. Controleer of de rol {1} is ingesteld in Service Account Rollen
idp_permissions_disabled_error = Om de configuratie van de switch-gebruikersfunctionaliteit mogelijk te maken, moeten de machtigingen voor de IDP met alias {0} geactiveerd zijn.
no_idp_configured_for_usurpation_error = Er is geen IDP met de alias {0} geconfigureerd voor usurpation in realm {1}.
invalid_adminClient_error = Er is geen client met clientId {0} op realm {1}.
CORRECT = Gedematerialiseerd
idp_missingConfigOnPartner_warn = De IDP die vereist is voor het configureren van de switch-gebruikersfunctionaliteit is niet toegevoegd aan de authenticatie-eigenschappen van partner {0}. Als u het toevoegen van de authenticatieconfiguratie voor deze partner bevestigt, worden de benodigde configuraties voor de functionaliteit voor de schakelgebruiker niet opgenomen.
idp_missingConfigOnCustomer_warn = De IDP die vereist is voor het configureren van de functionaliteit voor switchgebruikers is niet toegevoegd aan de authenticatie-eigenschappen van de klant op deze omgeving. Als u het toevoegen van de authenticatieconfiguratie bevestigt, worden de benodigde configuraties voor de switch-gebruikersfunctionaliteit niet opgenomen.
idp_invalid_issuerUrl_error = De eigenschap \u201CIssuer\u201D is ongeldig. Het moet het patroon volgen: {auth_base_url}/realms/{bo_realm}<br/>
UNDEFINED = Onbepaald
idp_invalid_mapper_error = Om de configuratie van de functionaliteit voor switch-gebruikers mogelijk te maken, moet de mapper die op de IDP met alias {0} is gedefinieerd, \u201CSync Mode Override\u201D op \u201Cinherit\u201D hebben staan, \u201CMapper Type\u201D op \u201CHardcoded Role\u201D en \u201CRole\u201D op \u201Cimpersonation\u201D.
impersonate_disabled_error = Om de configuratie van de switch-gebruikersfunctionaliteit mogelijk te maken, moeten de machtigingen voor gebruikers in de realm {0} geactiveerd zijn.
no_idp_onGroupAuthConfig = Er is geen IDP alias ingesteld in de authenticatieconfiguraties van groep (partner/bedrijf)
no_impersonation_role_error = Om de configuratie van de switch-gebruikersfunctionaliteit in te schakelen, moet er een rol met de naam \u201Cimpersonation\u201D zijn in de realm {0}
discard = Gooi weg
duplicate_clientId_error = Het is niet mogelijk om twee clients aan te maken met dezelfde clientId {0}. Zorg ervoor dat u verschillende waarden gebruikt
idp_hideOnLoginPage_disabled_error = De eigenschap \u201CVerbergen op aanmeldpagina\u201D is niet ingeschakeld<br/>
send_renewal_link = Vernieuwingslink voor wachtwoord sturen
no_impersonationRolePolicy_error = Om de configuratie van de switch-gebruikersfunctionaliteit in te schakelen, moet er een role-type policy zijn met de \u201Cimpersonation\u201D rol gemarkeerd als vereist, toegevoegd aan de \u201Cimpersonate\u201D machtiging op Users
serviceAccountUser_noAdminRole_error = De service account gebruiker van client {0} heeft niet de benodigde rechten om acties uit te voeren op de realm {1} (andere clients, gebruikers, etc. beheren). Controleer of de rol {2} is ingesteld in Service Account Rollen.
channel_MDN = MDN
idp_no_mapper_error = Om de configuratie van de switch-gebruikersfunctionaliteit in te schakelen, moet er een mapper met de rol \u201Cimpersonation\u201D gedefinieerd zijn op de IDP met de alias {0}.
idp_invalid_userInfoUrl_error = De \u201CUser Info URL\u201D eigenschap is niet geldig. Deze moet het patroon volgen: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/userinfo<br/>
tokenExchange_disabled_error = De tokenuitwisselingsfunctie is niet ingeschakeld op realm {0}. Om pagina's om te leiden van angular naar JSF en ook om de functionaliteit voor het wisselen van gebruiker te gebruiken, moet deze toestemming worden ingeschakeld
idp_invalid_clientAuthMethod_error = De eigenschap \u201CClient Authentication\u201D moet de waarde \u201CClient secret sent as post\u201D hebben.<br/>
trigger_already_exists_warning = De trigger {0} bestaat al in die omgeving. Deze wordt niet ge\u00EFmporteerd met dit proces.
idp_invalid_authorizationUrl_error = De eigenschap \u201CAutorisatie URL\u201D is ongeldig. Deze moet het patroon volgen: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/auth<br/>
search_by_process = Een proces zoeken
refresh_button = Filter resetten
impersonationRolePolicy_error = Om de configuratie van de switch-gebruikersfunctionaliteit mogelijk te maken, moet aan de role-type policy \u201C{0}\u201D de rol \u201Cimpersonation\u201D worden toegewezen en als vereist worden gemarkeerd.
portlet_demat_partner_file_daily = DematPartnerFileDaily
auth_reconfigureClientsForUsurpation = Klanten herconfigureren voor usurpatie
idp_disabled_error = De IDP met alias {0} is niet ingeschakeld<br/>
idp_invalid_clientId_error = Client ID is niet geldig. Controleer de eigenschap auth.server.backoffice.clientId
templateUri = e-mailsjabloon