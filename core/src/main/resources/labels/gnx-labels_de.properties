cancel = Abbrechen
year = Jahr
ABANDONED_label = ABGEBROCHEN
menu_message = E-Mail
organization_location = _location
info_portlet_removed = Der Inhalt des Portlets wurde gel\u00F6scht.
themes = Themen
template_invalid_name = Der Vorlagenname muss zwischen 3 und 18 Kleinbuchstaben und/oder Zahlen enthalten.
organization_address_addressComplement = Adresszusatz
company_view = Kunde {1} ({0})
invalid_file_type = Ung\u00FCltiger Dateityp
move_left = Nach links verschieben
alphanumeric = Alphanumerisch
AtLeastOneUser = Mindestens ein Benutzer eines Partners
task_adv_search_simple = Einfach
ks_comment = Beschreibung
version = Version
order_issuer = Auftraggeber
docs_list = Liste der Dokumente
folder = Ordner
configuration_page = Konfiguration der Seite
selection = Ausw\u00E4hlen
size = Gr\u00F6\u00DFe
stop = Stoppen
ks_hasprivatekey = Mit privatem Schl\u00FCssel
left = Links
doc_select = Ausw\u00E4hlen
domain_modif = Eine Domain bearbeiten
gcnSubscriberIdentification_error = Die Generix-Rechnungs-ID darf maximal 64\u00A0alphanumerische Zeichen enthalten
error_no_company_specified = Kein Kunde f\u00FCr die Bearbeitung des Partners {0} angegeben.
role = Rolle
infinite = Unendlich
organization_email = _email
archiving = Archivierung
length_min = Minimale L\u00E4nge
doc_update = Aktualisieren
doc_update_information=Meine Informationen aktualisieren
menurights = Maximale Dateigr\u00F6\u00DFe f\u00FCr Anh\u00E4nge
companyinformation= Meine Unternehmensinformationen
result = Ergebnisse
occurence = Anzahl der Vorkommen
login_validator = Der Login muss mindestens 3 alphanumerische Zeichen, '_', '-', '@' oder '.' enthalten.
refuse = Ablehnen
reminder_subject = Betreff der Erinnerung
STOPPED_icon = fa fa-exclamation-circle
campaigns_new = Neue Kampagne
close = Schlie\u00DFen
linked_document_import_selection = Eine oder mehrere Dateien ausw\u00E4hlen und importieren
connect = Verbinden
validator_password = Falsches Passwort (mindestens 3\u00A0Zeichen).
preferences = Einstellungen
report_partners_completion = KAMPAGNENSTATUS
exception_no_backoffice_access = {0} geh\u00F6rt weder zu einem Partner noch zu einem Kunden und hat keinen Zugang zum Backoffice.
ui_required = Pflichtangabe
error_save_user_no_env = Es muss eine Umgebung ausgew\u00E4hlt werden, um den Benutzer benachrichtigen zu k\u00F6nnen.
NONE_color = #d4d4d4
partner_import_error = Fehler beim Importieren von Partnern: {0}
organization_fullname = Name
communication=Kommunikation
doc_upload_error = Fehler beim Importieren des Dokuments: {0} ({1})
information = Informationen
period_to = bis
keyword_$currentDate = Heutiges Datum
ui_validator = Ung\u00FCltige Angabe
error_removing_partner = Fehler beim L\u00F6schen des Partners ({0}).
error_saving_partner = Fehler beim Speichern des Partners ({0}).
doc_deleted = Gel\u00F6schtes Dokument
gcn_identification_group = Generix-Kollaborationsnetzwerk
good = gut
error_uploding_logo = Laden des Logos {0} fehlgeschlagen.
add_below = Unten hinzuf\u00FCgen
entry_add_title = Einen Titel hinzuf\u00FCgen
organization_duns = RCS-RCM
export = Exportieren
UNKNOWN_color = #d4d4d4
channel_save_ok = Gespeicherter Kanal.
doc_new_name = Neuer Name
info_host_saved = Domain {0} gespeichert
add = Hinzuf\u00FCgen
exception_id_duplication = Die ID existiert bereits: {0}.
imenu_integration_recent_business = J\u00FCngste Prozesse
ks_friendlyname = Nutzungsname
generate_new_password_confirm = M\u00F6chten Sie eine E-Mail zum Zur\u00FCcksetzen des Passworts generieren?
menu_messages = E-Mails
contact_user = Kontakt
warn_select_partner_notification = Bitte w\u00E4hlen Sie die zu benachrichtigenden Partner aus.
campaign_documents = Dokumente
encoding = Verschl\u00FCsselung
users = Benutzer
partner_field = Eingabefelder
port = Port
lang_english = Englisch
ks_remove_error = Fehler beim L\u00F6schen der Datei: {0}.
mail_sent = Gesendet
info_company_saved = Kunde {0} gespeichert
partner_import_no_user = Der Partner {0} hat keinen Benutzer
roles = Rollen
choose = Ausw\u00E4hlen
create_notifications = Eine Anzeige erstellen
paste = Einf\u00FChren
partner_delete_error = Fehler beim L\u00F6schen eines Partners: {0}.
remove = L\u00F6schen
page_new = Neue Seite
ks_filename = Zertifikatname
user_blocked = Ihr Konto wurde deaktiviert. Bitte wenden Sie sich an Ihren Administrator.
organization_freeDate01 = _freeDate01
error_removing_user = Fehler beim L\u00F6schen des Benutzers ({0}).
configuration_portal = Konfiguration des Portals
scope = Bereich
entry_title = Titel
end = Ende
sort_order = Sortierrichtung
ks_config_require_name = Der KeyStore-Name ist ein Pflichtfeld.
organization_freeDate02 = _freeDate02
info_instance_toggled = Die Umgebung {0} wurde umgeschaltet.
partner_import_already_existing_users = bereits vorhandene(r) Benutzer
no_records_found = Kein Eintrag gefunden
no_extensions_found = Keine Erweiterungsdatei f\u00FCr diese Datenbank gefunden
label = Bezeichnung
keyword_$password = Passwort des Benutzers
message = E-Mail
modify = Bearbeiten
imenu_security = Partner
menu_process_manage = Verwaltung
ks_unexpected_error_determing_alias = Unerwarteter Fehler bei der Bestimmung des Alias.
STOPPING_icon = fa fa-exclamation-circle
generate_password = Link zum Zur\u00FCcksetzen des Passworts generieren
info_role_removed = Rolle {0} gel\u00F6scht
family = Familie
OK_label = OK
backup = Speichern
template_revision = E-Mail-Vorlage importieren
info_user_role_saved = Benutzerrolle gespeichert
partner_contacts = Kontakte
channel = Kanal
add_above = Oben hinzuf\u00FCgen
APPROVED_icon = fa fa-check-circle-o
channel_other = Andere
menu_audit = Audit
organization_phone = _phone
report_not_completed = Nicht abgeschlossen
order = Bestell-Nr.
organization_vat = Umsatzsteuer-Identifikationsnummer (USt-IdNr.)
WARN_icon = fa fa-exclamation-circle
period = Zeitraum
trf_generic = generisches Format
upload_invalid_file = Ung\u00FCltiges Dateiformat
warn_host_already_used = Die Domain wird bereits verwendet.
exception_backoffice_user = {0} ist ein Backoffice-Benutzer.
WARN_label = WARNUNG
info_user_new_password = Ein Link zur Zur\u00FCcksetzung des Passworts wurde an {0} gesendet.
keyword_$login = Anmeldedaten des Benutzers
ks_no_file_selected = Ein Zertifikat muss ausgew\u00E4hlt werden.
ABANDONED_color = #cc1e00
exit = Schlie\u00DFen
partner_deleted = L\u00F6schen der Partner: {0}.
quick_search = Schnellsuche
task_adv_search_avancee = Erweitert
analyze = Analysieren
integration = Prozess: Aktivit\u00E4t
info_partner_file_import = {0} importiert
organization_logoLarge = _logoLarge
other = Andere
property_required = ist erforderlich.
save = Registrieren
exception_user_associated = {0} verkn\u00FCpfte(r) Benutzer
login = Login
organization_address_country = Land
channel_duplicate_error = Fehler beim Kopieren des Kanals
error_saving_role = Fehler beim Speichern der Rolle ({0}).
imenu_repository_documents = Dokumente
campaign_date_start = Startdatum
ERROR_color = #d15b47
entry_import_faq = FAQ importieren
boolean_true = Wahr
regenerate_all = Alle zur\u00FCcksetzen
ks_entry_serialnumber = Seriennummer
channel_test = Testen
reminder = Erinnerung
STARTED_color = #bebfbb
exception_partner_subgroups = Der Partner {0} hat {1} Untergruppe(n).
permissions_dialog_page_title = Zugriffsrechte f\u00FCr die Seite festlegen
menu_partners = Partner
channel_add_error_duplicatename = Es existiert bereits ein Kanal mit diesem Namen. Dieser Kanal kann nicht erstellt werden.
removed = Gel\u00F6scht
disable = Deaktivieren
error_saving_instance = Fehler beim Speichern der Umgebung ({0}).
entry_add_question = Eine Frage hinzuf\u00FCgen
unread_message = ungelesene Nachricht(en)
completion_bounded = Fortschritt begrenzt
organization_freeLongText01 = _freeLongText01
doc_downloads = Anzahl der Downloads
organization_freeLongText02 = _freeLongText02
recent_mapping = J\u00FCngste Zuordnungen
create_mail = Eine E-Mail erstellen
report_partners_status_by_month = ENTWICKLUNG DES PARTNERSTATUS
invalid_generix_billing_id = Die eingegebene Generix-Rechnungs-ID ist ung\u00FCltig
menu_admin = Verwaltung
pages = Seiten
sort_order_descending = Absteigend
entry_radio = Radio
regenerate_all_password = Alle Passw\u00F6rter zur\u00FCcksetzen
all = Alle
new = Neu
modif_contact_partner = \u00C4nderung des Partnerkontakts
upload_files = Hochladen von Dateien
collapse_all = Alles reduzieren
partner_delete_error_children_exist = Ein Partner kann nicht gel\u00F6scht werden, solange er untergeordnete Partner hat: {0}.
report_informations = Informationen
length_max = Maximale L\u00E4nge
exception_constraint_violation = {0}: {1}
freetext = Freie Felder
template_saved = Vorlage gespeichert
sort_by = Sortierung nach
ui_converter = Konvertierungsproblem
deny_message = Bitte begr\u00FCnden Sie Ihre Ablehnung.
channel_deleted = Kanal gel\u00F6scht
error_removing_portlet = Fehler beim L\u00F6schen des Portlets ({0}).
creation_mail_message_html = Hallo {0},<br/><br/>nachfolgend finden Sie Ihre Anmeldedaten f\u00FCr die Website von {1} ({2}):<br/><br/><li>Benutzer: {3} </li><li>Link zum Zur\u00FCcksetzen des Passworts: {4\\}</li><br/><br/>Bis bald!
exception_instance_not_found = Der Benutzer {0} hat keine Umgebung f\u00FCr die Domain {1} konfiguriert.
ks_error_could_not_write_certificate_file = Die zugeh\u00F6rige Datei f\u00FCr dieses Zertifikat kann nicht geschrieben werden.
folder_in = Eingangsordner
button_edit = Bearbeiten
entry_answer = Antwort
permission_required = Pflichtauswahl
select_one = W\u00E4hlen Sie eine Option aus
milestone = Voraussichtliches Ausf\u00FChrungsdatum
partner_import_file = Import einer Partnerdatei (*.xls, *.xlsx)
exception_task_type_not_found = Aufgabentyp {0} konnte nicht gefunden werden
account_creation_confirm = Ihr Konto wurde eingerichtet. Sie erhalten in K\u00FCrze eine Best\u00E4tigungs-E-Mail an folgende Adresse:
imenu_integration = Prozess
mark_as_read = Als gelesen markieren
ks_publickeytab = \u00D6ffentlicher Zertifikatsschl\u00FCssel
partner_name = Name des Partners
STARTED_icon = fa fa-times-circle-o
warn_language_mandatory = Die Sprachkonfiguration ist erforderlich.
exception_message = Es ist ein Fehler aufgetreten. \n Bitte versuchen Sie es sp\u00E4ter noch einmal oder wenden Sie sich an Ihren Administrator.\n ({0})
organization_freeText09 = _freeText09
companies = Kunden
creation_mail_subject = Erstellung Ihres Benutzerkontos f\u00FCr die Website von {0}.
delete_select = M\u00F6chten Sie die ausgew\u00E4hlten Elemente dauerhaft l\u00F6schen?
error_removing_page = Fehler beim L\u00F6schen der Seite ({0}).
organization_freeText07 = _freeText07
organization_subscriberId = Generix-Rechnungs-ID
identification = Kennung
ks_entry_alias_optional = Optional, sofern der Alias nicht automatisch ermittelt werden kann.
organization_freeText08 = _freeText08
organization_freeText05 = _freeText05
readonly = Nur lesen
doc_uploaded = Importiertes Dokument: {0}
organization_freeText06 = _freeText06
organization_freeText03 = _freeText03
organization_freeText04 = _freeText04
organization_freeText01 = _freeText01
organization_freeText02 = _freeText02
partner_view = Partner {1} ({0})
state = Status
exception_user_not_partner = Der Benutzer {0} in der Gruppe {1} ist weder ein Partner noch ein Kunde.
FATAL_icon = fa fa-bomb
events = Trigger
tva = Umsatzsteuer (USt)
exception_import_portal_not_empty = Das Portal ist nicht leer f\u00FCr die Umgebung {0}.
hosts = Hosts
UNKNOWN_icon = fa fa-exclamation-circle
bad_account_or_password = Unbekannter Benutzername oder falsches Passwort.
error_getting_portlet_content = Portlet {0}, Inhalt {1}: {2}
monitoring = Aktivit\u00E4t
move_top = Ganz nach oben verschieben
labels = Bezeichnungen
menu_campaigns = Onboarding
dictionary = Berufsw\u00F6rterb\u00FCcher
menu_notifications = Ank\u00FCndigungen
rendered = Anzeigen
recipients = Empf\u00E4nger
boolean_false = Falsch
duns = RCS-RCM
date_last_authentication = Letzte Anmeldung
actions = Aktionen
edit_notifications = Anzeige bearbeiten
cut = Ausschneiden
contact_admin = Bitte wenden Sie sich an Ihren Administrator.
other_variable = Freie Variable
document = Dokument
upload_file_limit = Maximale Anzahl an Dateien erreicht
expand_all = Alles bereitstellen
label_search = Pr\u00E4zisieren Sie Ihre Suche
info_user_removed = Benutzer {0} gel\u00F6scht
lang_french = Franz\u00F6sisch
ERROR_label = FEHLER
menu_security_companies = Kunden
ks_remove_linked_partner_integrity = Vorgang blockiert: Das Zertifikat ist noch mit diesen Gruppen verkn\u00FCpft: {0}.
default = Fehler
organization_profile = Profil
reminder_number = Anzahl der Erinnerungen
upload_with_conflict_conflict_message = Folgende Dateien werden \u00FCberschrieben:
disabled = Deaktiviert
exception_invalid_gson = Ung\u00FCltige Gson-Datei
report_partners_status = PARTNERSTATUS
warn_only_parent_empty = Nur eine \u00FCbergeordnete Seite kann leer sein.
organization_freeBoolean01 = _freeBoolean01
creation = Erstellungsdatum
ui_invalid_size = Falsche Dateigr\u00F6\u00DFe
menu_process = Prozess
organization_freeBoolean02 = _freeBoolean02
validate = Best\u00E4tigen
FAILED_color = #cc1e00
rte = RTE
translation= \u00DCbersetzung
template_import = Vorlage importieren
languages = Sprachen
ks_modulus_length = Modull\u00E4nge (Bits)
refresh = Aktualisieren
partner_save_error = Fehler beim Speichern des Partners: {0}.
ks_unrecognized = Die importierten Daten k\u00F6nnen nicht als PKCS11-, PKCS12- oder JKS-KeyStore geladen werden.\n \u2013 \u00DCberpr\u00FCfen Sie das Passwort.\n \u2013 \u00DCberpr\u00FCfen Sie den Dateityp.\n \u2013 Versuchen Sie, die Dateien \u201EUnlimited Strength Jurisdiction Policy Files\u201C in Ihrer JRE zu \u00E4ndern.
organization_address_postalCode = Postleitzahl
tout = Alle
about_contact_email = E-Mail-Adresse des Kontakts
STARTED_label = BEGONNEN
account_confirmation = Best\u00E4tigung der Kontoeinrichtung
channel_create_error = Fehler bei der Erstellung des Kanals.
warn_no_type_selected = Bitte w\u00E4hlen Sie den Aufgabentyp aus.
organization_client = _client
is_expired = ist abgelaufen.
ks_upload_select = Keystore (*.jks, *.p12, *.pfx)
partners = Partner
generate_gcn_subscriber = Kennung generieren
entry_select_excel = Eine Excel-Datei ausw\u00E4hlen (*.xls, *.xlsx).
organization_gcnSubscriber = Generix-Kunde
campaign_name_short = Kurzer Name
documents = Dokumente
administration = Verwaltung
FAILED_label = FEHLGESCHLAGEN
accounting = Kundenkontakt
organization_parent = _parent
secure = Gesichert (HTTP/S)
ks_entry_notafter = G\u00FCltig bis
ks_upload = Keystore importieren
menu_security = Sicherheit
APPROVED_color = #87b87f
view = Ansehen
domain_edit = Eine Domain bearbeiten
test_upload = Laden einer Testdatei.
keyword_$campaignvariable = Freie Variable
imenu_portal_einvoice = E-Rechnung
error_removing_template = Fehler beim L\u00F6schen der Vorlage ({0}).
version_technical = Technische Referenz
menu_statistics = Berichterstattung
template_invalid_pathname = Das Archiv enth\u00E4lt Dateien mit fehlerhaften Namen
new_file = Neue Datei
channel_modify = Kanal bearbeiten
duplicate = Duplizieren
error_user_without_primary_group = Es wurde keine Hauptgruppe f\u00FCr den Benutzer gefunden.
APPROVED_label = OK
user_search = Verfeinern Sie Ihre Suche, indem Sie den Namen des Autors der \u00C4nderungen eingeben.
info_portlet_saved = Der Inhalt des Portlets wurde gespeichert.
imenu_repository_templates = Vorlagen
switch_off = Angemeldet als
add_left = Links hinzuf\u00FCgen
warn_template_with_associated_host = Bestimmte Bereiche sind mit einer Vorlage verkn\u00FCpft.
partner_add = Partner hinzuf\u00FCgen
name = Name
page = Seite
installation_detail = Installierte Software
parameters = Einstellungen
parameter = Parameter
task_types_revision = Anzeige der Aufgabenbeschreibung
configuration_portlet = Konfiguration des Portlets
import = Importieren
error_duplicate_template = Die Vorlage {0} existiert bereits.
description = Beschreibung
edition = Ausgabe
campaign_description = Beschreibung
rte_file_or_folder_invalid_name = Der Name darf nur alphanumerische Zeichen ohne Leerzeichen enthalten. Die Zeichen \u201E.\u201C, \u201E_\u201C und \u201E-\u201C sind zul\u00E4ssig.
exception_multiple_principal_groups_user = Der Benutzer {0} hat mehr als eine Hauptgruppe ({1}).
gcnIdentification_error = Die GCN-ID darf maximal 64\u00A0alphanumerische Zeichen enthalten.
complementary_info = Zus\u00E4tzliche Informationen
select_instance = Umgebung ausw\u00E4hlen
ks_error_could_not_find_nor_create_parent_for_friendly_name = Es konnte kein \u00FCbergeordnetes Element f\u00FCr den Nutzungsnamen {0} gefunden oder erstellt werden.
day_of_week = Tage der Woche
severity = Schweregrad
exception_removed_instance = Die Umgebung {0} wurde entfernt.
hours = Stunden
info_partner_removed = Partner {0} gel\u00F6scht
yes = Ja
start = Starten
OK_color = #87b87f
ks_cannot_determine_alias = Der verwendete Alias kann nicht bestimmt werden. Gefundene Aliase, mit Trennzeichen ;: {0}
partner_code_validator = Der Code muss aus dem Kundencode (bestehend aus 3\u00A0Gro\u00DFbuchstaben), gefolgt von einem \u201A-\u2018 und einer Identifikationsnummer, bestehen.
fullname = Vollst\u00E4ndiger Name
confirmation_mail_message_text = Hallo {0},\n\nIhr Konto wurde eingerichtet. Bitte f\u00FCgen Sie die folgende URL in Ihrem bevorzugten Browser ein, um die Einrichtung Ihres Kontos zu best\u00E4tigen: {1}.\n\nBis bald!=======
exception_user_no_associated_company = Der Benutzer {0} des Partners {1} ist keinem Kunden zugeordnet.
ks_library = Bibliothek
usage = Nutzung
ks_cannot_load_keystore = Keystore kann nicht geladen werden. \u00DCberpr\u00FCfen Sie die Einstellungen. Wenden Sie sich an einen Administrator.
organization_orderContact = Kontakt
organization_address_city = Stadt
ks_entry_subject = Geliefert an
organization_freeViewProfile = Profil
required = Obligatorisch
active_partners = AKTIVE PARTNER
docs_selected = Ausgew\u00E4hlte Dokumente
logout = Abmeldung
doc_name = Name des Dokuments
info_partner_saved_detail = Partner {0} gespeichert: {1}
keystore = Keystore
switch_user = Identit\u00E4ts\u00FCbernahme
fax = Fax
ks_no_slot_index_selected = Ein Slot-Index muss ausgew\u00E4hlt werden.
dashboard = Dashboard
entry_new = Neuer Eintrag
error_switching_user = Fehler bei der Identit\u00E4ts\u00FCbernahme ({0}).
warn_app_template_deleting=Die Vorlage {0} kann nicht gel\u00F6scht werden.
role_min = Rolle
linked_certificates = Verkn\u00FCpfte Zertifikate
organization_name = Code
menu_security_order_issuers = Auftraggeber
exception_file_upload_unknown_request = Unbekannter Abfrageparameter: {0}
phone = Telefon
menu_system = System
reset = Zur\u00FCcksetzen
style = Stil
channel_add = Kanal hinzuf\u00FCgen
ks_provide_friendly_name = Der Nutzungsname muss angegeben werden.
channel_arkhineo_depositCert_req = Deposit certificate
roadmap = Roadmap
contact_us = KONTAKT
confirm_file_delete = M\u00F6chten Sie {0} wirklich l\u00F6schen?
exception_portlet_cloning = Problem beim Klonen von Portlet {0}.
name_executable = Name der ausf\u00FChrbaren Datei
enabled = Aktiviert
channel_ftp_username = Benutzername
exception_duplicate_role = Doppelte Rolle: {0}.
enabled_linked_document_import = Import von angeh\u00E4ngten Dokumenten aktivieren
theme = Thema
error_saving_permission = Fehler beim Speichern der Berechtigung ({0}).
start_date = Startdatum
channel_name = Name des Kanals
editor = Herausgeber
report_partners_completion_campaigns = ONBOARDING
ks_alias_not_found = Alias nicht gefunden: {0}. Aliase gefunden, mit Trennzeichen; : {1}
indexing = Neuindexierung
menu_instances = Umgebungen
display = Anzeige
export_portal = Portal exportieren
warn_portlet_content_not_found = Der Inhalt des Portlets mit der ID {0} wurde nicht gefunden.
entry_answers = Antworten
exception_partner_associated = {0} verkn\u00FCpfte(r) Partner
info_instance_saved = Die Umgebung {0} wurde gespeichert.
organization_address_address = _address.address
exception_unavailable_instance = Die Website {0} ist vor\u00FCbergehend nicht verf\u00FCgbar.
identification_validator = Die Kennung darf nur alphanumerische Zeichen oder \u201A_\u2018 enthalten.
task_to_complete = Zu erledigende Aufgabe
partner_saved = Partner speichern: {0}.
chat = Chat
exception_import_error_during_cloning = Fehler beim Duplizieren des Portals
instance_code_validator = Der Code muss aus 3\u00A0Ziffern oder Buchstaben in Gro\u00DFbuchstaben bestehen.
AllUsers = Alle Benutzer eines Partners
error_creating_template_archive = Fehler beim Erstellen der Vorlage ({0}).
statistics = Statistiken
ABANDONED_icon = fa fa-exclamation-circle
keyword_$contact = Kontakt
replace = Ersetzen
partner_send_mail = Verbindungsinformationen senden
allow_user_managing_tab = Benutzern erlauben, diese Registerkarte zu verwalten
notification = Benachrichtigung
template_edit = Bearbeiten der Vorlage
organization_orderPhone = Telefon
day = Tag
from_address = Sender
ks_uploaddate = Erstellungsdatum
FATAL_color = #cc1e00
ks_remove_timestamp_server_integrity = Vorgang blockiert: Das Zertifikat wird noch in einem Zeitstempel-Server referenziert.
ks_slotIndex = Slot-Index
ks_label = Kennung
tasks_noselection = Keine ausgew\u00E4hlte Aufgabe
minutes = Minuten
error_saving_host = Fehler beim Speichern der Domain ({0}).
format = Format
recent_business = J\u00FCngste Gesch\u00E4fte
archive = Archiv
menu_domain = Domains
history = Historie
upload_invalid_size = Ung\u00FCltige Dateigr\u00F6\u00DFe
home_message = Beschleunigen Sie Ihre EDI- und B2B-Bereitstellungen und erzielen Sie Kosteneinsparungen mit GCI Community Management
partner_new = Neuer Partner
reminder_content = Erinnerungstext
FATAL_label = KRITIK
error_profile_name_mandatory = Der Profilname ist ein Pflichtfeld.
deploy_process = Den Prozess bereitstellen
deployment=Bereitstellung
imenu_repository = Stammdaten
about_contact = Kontaktieren Sie uns
ui_file_limit = Maximale Anzahl an Dateien erreicht
imenu_general = Allgemein
insert = Einf\u00FCgen
numeric = Numerisch
info_no_portlet_defined = Das Portlet wurde f\u00FCr diese Seite nicht definiert.
error_exporting_instance = Fehler beim Exportieren der Umgebung: {0}.
campaign_name = Name
general = Allgemein
test_send_mail = E-Mail an mich selbst senden
move_up = Nach oben verschieben
entry_new_answer = Option
channel_type_select = Kanaltyp\u00A0...
authentication = Authentifizierung
keyword_$company = Name des Unternehmens, das die Kampagne startet
clear = Bereinigen
organization_freeViewConfiguration = Freie Felder
info_user_saved = Benutzer {0} gespeichert
organization_userGroupAssociations = _userGroupAssociations
warn_creation_succes = Die Ressource wurde erfolgreich angelegt
upload_with_conflict_title = Ein Archiv importieren
organization_logoSmall = _logoSmall
organization_orderFax = Fax
error_saving_organization = Fehler: Das Element existiert bereits.
move_down = Nach unten verschieben
user = Benutzer
account = Mein Konto
gcnSubscriberIdentification = Generix-Rechnungs-ID
channel_MDN = MDN
WARNING_label = WARNUNG
ks_password_pkcs11 = Passwort (PIN-Code)
siret = SIREN
importInstance = Importieren (*.json)
campaigns = Onboarding
organization_creation = _creation
STOPPED_color = #cc1e00
warn_partner_profile_deleted = Das Profil eines Partners darf nicht gel\u00F6scht werden.
entry_add = Hinzuf\u00FCgen
profile = Profil
exception_gcn_subscriber_id_duplication = Die Generix-Rechnungs-ID existiert bereits: {0}.
reminder_delay = Zeitintervall
role_edit = Rolle bearbeiten
DISABLED_label = DEAKTIVIERT
FAILED_icon = fa fa-exclamation-circle
menu_security_users = Backoffice-Benutzer
confirm_instance_enable = Alle Dienste der Umgebung werden aktiviert (Integration, Onboarding\u00A0...) M\u00F6chten Sie diese Umgebung wirklich aktivieren?
tasks_campaign = Aufgaben-Workflow
ks_error_no_keystore_folder = Fehler\u00A0\u2013 Der Keystore-Ordner konnte nicht bestimmt werden.
domain = Domain
channel_toggle_linked_chanel = Fehler beim Bearbeiten des Kanals. Es gibt noch Partner mit einem Alias in diesem Kanal:<br/>{0}
entry_type = Typ
max_size_of_linked_document = Maximale Gr\u00F6\u00DFe der angeh\u00E4ngten Dateien
schema = Schema
strong = stark
organization_logoMedium = _logoMedium
managed_by = Verwaltet von
menu_security_groups = Gruppen
preview_not_available = Vorschau nicht verf\u00FCgbar
ERROR_icon = fa fa-times-circle-o
organization_code = ID
search = Suchen
menu_notification = Ank\u00FCndigung
partner_parent = Elternteil
PENDING_color = #bebfbb
error_saving_file = Fehler beim Speichern der Datei ({0}).
completion = Fortschritt
mail_content = Hauptteil
keyword_$campaigncontactphone = Telefonnummer des Ansprechpartners f\u00FCr die Kampagne
warn_existing_resource = Die Ressource existiert bereits
ks_uploaded = Keystore importiert: {0}
confirm_task_result_reset = Die Ergebnisse dieser Aufgabe werden gel\u00F6scht. M\u00F6chten Sie fortfahren?
company_add = Kunde hinzuf\u00FCgen
import_cert_jks_pkcs12 = JKS/PKCS#12...
partner = Partner
confirmation_mail_message_html = Hallo {0},<br/><br/>Ihr Konto wurde eingerichtet. Bitte best\u00E4tigen Sie Ihre E-Mail-Adresse, indem Sie auf den folgenden Link klicken: <a href=\"{1}\">Best\u00E4tigungs-E-Mail</a>.<br/></br> Alternativ k\u00F6nnen Sie die folgende URL in Ihrem bevorzugten Browser einf\u00FCgen: {1}.<br/><br/>Bis bald!
error_removing_role = Fehler beim L\u00F6schen der Rolle ({0}).
menu_config = Konfiguration
ks_entry_signaturealgoname = Name des Signaturalgorithmus
partner_import_already_existing_partners = bereits bestehende(r) Partner
info_logo_uploaded = Logo geladen
language = Sprache
organization_comment = Beschreibung
styleClass = Stiltypen
OK_icon = fa fa-check-circle-o
error_uploding_template = Laden der Vorlage fehlgeschlagen ({0}).
ks_entry_fingerprintsha1 = Digitaler Fingerabdruck (SHA-1)
contact = Kontakt
add_right = Rechts hinzuf\u00FCgen
ks_hasRSApublickey = RSA
task_adv_search = Standardsuche
WARNING_icon = fa fa-exclamation-circle
imenu_campaigns = Onboarding
exception_import_instance_code_null = Der Umgebungscode ist null.
decimal_separator = Dezimaltrennzeichen
warn_locked_folder = Ordner gesperrt
docs_available = Verf\u00FCgbare Dokumente
entry_delete_question = Eine Frage l\u00F6schen
error_sending_new_password = Beim Versand des Links zur Zur\u00FCcksetzung des Passworts {0} ({1}) ist ein Fehler aufgetreten.
generate_new_password = E-Mail zum Zur\u00FCcksetzen des Passworts generieren
authentication_mail_template = E-Mail-Benachrichtigung neuer Kontakt
count = Gesamt
error_saving_host_not_unique = Eine Domain mit demselben Namen existiert bereits
undefine = Nicht elektronisch
error_no_user_found = Es wurden keine Benutzer mit der ID {0} gefunden.
month = Monat
organization_id = _id

#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=BIRT
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Z\u00E4hler
DocumentLineChart=LineChart
archiveimv3 = IMV3-Archiv
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated
CompleteEdition=CompleteEditionDeprecated
sources = Quellen
back = Zur\u00FCck
title = Titel
content = Inhalt
timeout = Zeit\u00FCberschreitung
duration = Dauer
doc_upload_disabled = Sie k\u00F6nnen diese Aktion nicht ausf\u00FChren, weil die Aufgabe gesperrt ist (m\u00F6glicherweise l\u00E4uft gerade eine Kampagne).
entry_question = Frage
alpha = Alphabetisch
client = Kunde
company = Kunde
info_portal_saved = Das Portal wurde gespeichert.
new_message = Neue Nachricht
WARNING_color = #ffb752
clear_portlet_confirm = M\u00F6chten Sie den Inhalt dieses Portlets wirklich bereinigen?
ks_entry_alias = Alias
length = L\u00E4nge
validator_email = Ung\u00FCltige E-Mail-Adresse
home = Startseite
schedule = Frequenz
channel_not_selected = Kein Kanal ausgew\u00E4hlt
print = Drucken
warn_delete_fail = L\u00F6schen fehlgeschlagen
ks_morethanoneentry = Ihr Keystore enth\u00E4lt mehr als ein Zertifikat. Dieser Keystore-Typ wird nicht unterst\u00FCtzt.
prompt = Geben Sie das Passwort ein
imenu_portal_o2c = Order-to-Cash
select = Auswahl
exception_instance_associated = {0} verkn\u00FCpfte Umgebung(en)
ks_error_no_friendly_name_found = Der Nutzungsname wurde in den Einstellungen nicht gefunden und kann nicht bestimmt werden.
error_removing_portlet_content = Fehler beim L\u00F6schen des Portlet-Inhalts ({0}).
partner_imported = Importierte Partner: {0}.
shipment_date = Versanddatum
ks_entrydetailstab = Zertifikat
add_child_page = Untergeordnete Seite hinzuf\u00FCgen
channel_delete_linked_chanel = Fehler beim L\u00F6schen des Kanals. Es gibt noch Partner mit einem Alias in diesem Channel:<br/>{0}
organization_fax = _fax
ks_password = Passwort
PENDING_label = BERICHT WIRD GENERIERT\u00A0...
value_default = Standardwert
action = Aktion
info_file_saved = Datei gespeichert
text = Text
portal = Portal
DISABLED_icon = fa fa-minus-circle
tasks = Aufgaben
ks_config_require_instance = Die KeyStore-Umgebung ist ein Pflichtfeld.
io = DO
partner_user_add = Kontakt hinzuf\u00FCgen
templates = Vorlagen
permission_edit = Berechtigungen bearbeiten f\u00FCr
exception_code_duplication = Der Code existiert bereits: {0}.
DISABLED_color = #d4d4d4
messaging = Nachrichtendienst
STOPPING_label = STOPP
field = ein Feld
info_company_removed = Kunde {0} gel\u00F6scht
campaign_date_creation = Erstellungsdatum
messages = E-Mails
doc_cancel = Abw\u00E4hlen
begin = Start
ks_upload_error = Fehler beim Importieren der Datei: {0}
status = Status
template = Vorlage
organization_orderEmail = E-Mail
alphanumeric_underscore = Nur alphanumerische Zeichen und Unterstriche sind erlaubt.
error_importing_instance = Fehler beim Importieren der Umgebung: {0}.
organization_web = _web
notify = Benachrichtigen
ks_unexpected_multiple_keys = Mehrere Schl\u00FCssel im Keystore, unerwarteter Fall.
file = Datei
library = Bibliothek
folder_out = Ausgabeordner
portlet = Portlets
warn_creation_fail = Erstellung fehlgeschlagen
exception_role_has_users = Dem Bereich {0} ist/sind {1} Benutzer zugeordnet.
linked_document_import = Anlage importieren
organization_children = _children
url = URL
keyword_$campaigncontactname = Name des Ansprechpartners f\u00FCr die Kampagne
warn_instance_code_already_used = Der Umgebungscode wird bereits verwendet.
organization_registration = SIREN
role_add = Hinzuf\u00FCgen von
doc_remove_error = Fehler beim L\u00F6schen des Dokuments
doc_uploaded_success = Importierte(s) Dokument(e): {0}/{1}
exception_import_export_null = Der Export ist null.
is_locked = ist gesperrt.
exception_user_more_companies = Der Benutzer {0} des Partners {1} hat mehr als einen verkn\u00FCpften Kunden ({2}).
instance = Umgebung
template_new = Vorlage erstellen
doc_rename_error = Bereits vorhandenes Dokument
menu_monitoring = Aktivit\u00E4t
subject = Thema
NONE_icon = fa fa-minus-circle
day_of_month = Tage des Monats
error_saving_user = Fehler beim Speichern des Benutzers ({0}).
ks_use_start_date = Nutzbar seit
download = Herunterladen
ks_provide_library = Die Bibliothek muss angegeben werden.
clear_page_confirm = M\u00F6chten Sie den Inhalt dieser Seite wirklich l\u00F6schen?
find = Suchen
info_role_saved = Rolle {0} gespeichert
line_comment = Zeile kommentieren
host = Host
menu_search = Suchen
boolean_select = --Auswahl--
exception_import_instance_null = Die Umgebung der Exportdatei ist null.
workflow = Workflow
error_changing_layout = Fehler beim Wechsel des Layouts ({0}).
warn_profile_already_exists = Dieses Profil existiert bereits.
task = Aufgabe
company_edit = Kunden {1} bearbeiten ({0})
channel_ftp_hostname = Hostname des Servers
emitter = Sender
info_no_portlet_content_defined = Das Portlet wurde ohne Inhalt f\u00FCr diese Seite definiert.
error_select_correct_user = Bitte w\u00E4hlen Sie einen g\u00FCltigen Benutzer aus.
no = Nicht
code = Code
ui_invalid_file = Falscher Dateityp
user_edit = Benutzer bearbeiten
organization_modification = _modification
delete = L\u00F6schen
move_bottom = Ganz nach unten verschieben
hour = Stunde
ks_use_end_date = Nutzbar bis
error_save_user_no_partner = Es muss ein Partner ausgew\u00E4hlt werden
ok = OK
ks_entry_fingerprintmd5 = Digitaler Fingerabdruck (MD5)
account_creation = Ein Konto einrichten
ks_keystoredetailstab = Details zum KeyStore
imenu_repository_dictionary = Fachw\u00F6rterbuch
exception_unknown_layout_type = Unbekannter Layouttyp
new_folder = Neuer Ordner
show_triggers = Trigger anzeigen
rigth = Rechts
ks_usage_mismatch = Die Nutzung {0} muss mit der \u00FCbergeordneten Nutzung {1} \u00FCbereinstimmen.
error_saving_portal = Fehler beim Speichern des Portals ({0}).
info_partner_saved = Partner {0} gespeichert
writer = Redakteur
entry_text = Text
report_no_data = KEINE DATEN
about_short = \u00DCber
about = \u00DCber TradeXpress Evolution
information_system = Systeminformation
edit_mail = E-Mail bearbeiten
error_removing_row = Fehler beim L\u00F6schen der Zeile ({0}).
exception_failing_ACE_instanciation = Beim Instanziieren der ACE f\u00FCr {0} ist ein Fehler aufgetreten.
number_reviewed_messages = Anzahl der \u00FCberpr\u00FCften Nachrichten
deny = Ablehnen
edit = Ausgabe
edit_record = Bearbeiten
entry_checkbox = K\u00E4stchen zum Ankreuzen
error_editing_default_host = Die Standarddom\u00E4ne kann nicht ge\u00E4ndert werden
execute = Ausf\u00FChren
accept = Akzeptieren
exception_more_than_one_instance = Der Benutzer {0}, der mit dem Kunden {1\\} verkn\u00FCpft ist, hat mehr als eine f\u00FCr die Dom\u00E4ne {2} ({3}) konfigurierte Umgebung.
info_host_removed = Domain {0} gel\u00F6scht
WARN_color = #ffb752
STOPPING_color = #cc1e00
exception_gcn_id_duplication = Die GCN-ID existiert bereits: {0}.
end_date = Enddatum
task_edit = Eine Aufgabe bearbeiten
contact_mode = Medien
view_all = Alles ansehen
menu_security_partners = Partner
import_cert_pkcs11 = PKCS#11 (RGS-2*)...
property_not_integer = muss eine ganze Zahl sein.
ks_type = Typ
ks_use_period = Zeitraum der Nutzung
email = E-Mail
creation_mail_message_text = Hallo {0},\n\nnachfolgend finden Sie Ihre Anmeldedaten f\u00FCr die Website von {1\\} ({2}):\n\nBenutzer: {3}\nLink zum Zur\u00FCcksetzen des Passworts: {4}\n\nBis bald!
variables = Variablen
warn_select_role = Bitte w\u00E4hlen Sie eine Rolle aus.
doc_upload_duplicate = Dupliziert: {0}
started = Start
number_connexion = Anzahl der Verbindungen
trf_required_template_uri = Vorlagen-URI ist ein Pflichtfeld.
confirm = Best\u00E4tigen
trf_required_document_type = Der Dokumenttyp oder seine Sammlung ist erforderlich.
gcn_subscriber_active = Generix-Kunde
keyword_$url = URL der Anwendung
ks_entry_version = Version
contacts = Kontakte
country = Land
partner_edit = Partner bearbeiten {1} ({0})
organization_freeDouble02 = _freeDouble02
organization_freeDouble01 = _freeDouble01
channel_type_mandatory = Sie m\u00FCssen einen Kanaltyp ausw\u00E4hlen, bevor Sie auf Hinzuf\u00FCgen klicken.
doc_edit = Ein Dokument bearbeiten
exception_duplicate_login = Login existiert bereits in AIO. Es kann nicht erstellt werden.
UNKNOWN_label = UNBEKANNT
details = Details
confirmation_mail_subject = Best\u00E4tigung der Kontoeinrichtung.
empty_page = Leere Seite
error_file_not_found = Datei nicht gefunden: {0}.
query = Filtern
user_logged = Bereits angemeldet
exception_user_not_specified = Der Benutzer ist nicht angegeben.\nexception_backoffice_user={0} ist ein Backoffice-Benutzer.
no_data = Keine Daten
modification = \u00C4nderungsdatum
date_read = Lesedatum
channels = Kan\u00E4le
entry_remove = L\u00F6schen
partner_field_configuration = Freie Eingabefelder Partner
campaign = Kampagne
error_exporting_partner = Fehler beim Exportieren von Partnern ({0}).
preview = Vorschau
partner_address = Postanschrift
domain_create = Eine Domain erstellen
keystores = Keystores
sooner_shipment_date = Fr\u00FChestes Versanddatum
managed_by_order_issuer = Verwaltet von DO
error_editing_portlet = Fehler bei der Bearbeitung des Inhalts des Portlets ({0}).
purge = Bereinigung
task_create = Eine Aufgabe erstellen
imenu_portal_p2p = Purchase-to-Pay
organization_collaborativeId = GCN-ID (GID)
warn_locked_file = Datei gesperrt
gcnIdentification = GCN-ID (GID)
company_code_validator = Der Code muss aus drei Buchstaben in Gro\u00DFbuchstaben bestehen.
keyword_$campaigncontactemail = E-Mail des Ansprechpartners f\u00FCr die Kampagne
layout = Anordnung
confirm_instance_disable = Alle Dienste der Umgebung werden deaktiviert (Integration, Onboarding\u00A0...). M\u00F6chten Sie die Umgebung wirklich deaktivieren?
exception_json_export_error = Fehler beim JSON-Export: {0}
comment = Kommentar
channel_add_type = Typ des hinzuzuf\u00FCgenden Kanals
exception_task_change_parent = Fehler im changeParent-Algorithmus.
exception_access_denied = Zugriff verweigert
COMPLETED_icon = fa fa-check-circle-o
none = Keine
doc_upload = Laden
repository = Stammdaten
type = Typ
user_new = Benutzer anlegen
seconds = Sekunden
client_order = Bestell-Nr.
enable = Aktivieren
permissions = Berechtigungen
remindpassword = Passwort-Erinnerung
imenu_general_instance = Umgebung
sort_order_ascending = Aufsteigend
STOPPED_label = GESTOPPT
channel_save_error_null = Fehler beim Speichern des Kanals (der Kanal ist null).
partner_comment = Kommentar
later_shipment_date = Sp\u00E4testes Versanddatum
imenu_portal = Kollaborativ
exception_task_import_parents = Problem beim Importieren von taskParent f\u00FCr {0}.
error_removing_host = Fehler beim L\u00F6schen der Domain ({0}).
ks_config_require_usage = KeyStore muss verwendet werden.
exception_task_import = Problem beim Importieren von Aufgaben f\u00FCr {0}.
ks_entry_issuer = Geliefert von
error_removing_company = Fehler beim L\u00F6schen des Kunden ({0}).
ks_certificationpath = Zertifizierungspfad
NONE_label = KEINE
ks_error_multiple_parent_for_friendly_name = Inkonsistenter Status: Mehrere \u00FCbergeordnete Elemente f\u00FCr den Nutzungsnamen {0} gefunden.
info_file_removed = Datei gel\u00F6scht
permissions_dialog_campaign_title = Partner zur Kampagne hinzuf\u00FCgen
error_importing_partner = Fehler beim Importieren von Partnern ({0}).
imenu_report = Aktivit\u00E4t
weak = niedrig
invalid_file_size = Ung\u00FCltige Dateigr\u00F6\u00DFe
copy = Kopieren
CAMPAIGN = ONBOARDING
channel_desc = Beschreibung
warn_host_not_removed = {0} wurde NICHT gel\u00F6scht ({1} zugeh\u00F6rige Umgebung(en): CODE= {2}).
filter = Filter
help = Hilfe
task_types = Aufgabentypen
description_short = Kurze Beschreibung
menu_dashboard = Dashboard
organization_description = _description
rename = Umbenennen
iframe_error_message = Die Website kann nicht angezeigt werden, da dies nicht erlaubt ist.
error_removing_file = Fehler beim L\u00F6schen der Datei ({0}).
doc_uploaded_update = Aktualisiertes Dokument
date = Datum
exception_admin_url_portal = Bitte geben Sie einen anderen Benutzernamen ein oder wenden Sie sich an Ihren Administrator, um die URL f\u00FCr den Zugriff auf Ihr Portal zu erhalten.
warn_user_missing = Der Benutzer wurde nicht gefunden (bitte den Parameter \u201EuuID\u201C \u00FCberpr\u00FCfen).
entry_edit_question = Eine Frage bearbeiten
error_saving_portal_portlet_missing = Die Konfiguration eines Portlets fehlt.
partner_identification = Kennung
upload_file = Laden der Datei
channel_ftp_port = Port
menu_process_reports = Berichte
page_noselection = Keine Seite ausgew\u00E4hlt
create = Erstellen
organization_address_streetName = Adresse
campaign_general = Allgemein
user_company = Kundenbenutzer
STARTING_label = START
task_last_completed = Letzte abgeschlossene Aufgabe
channel_duplicate_ok = Duplizierter Kanal
move_rigth = Nach rechts verschieben
add_certificate = Zertifikat hinzuf\u00FCgen
document_children_policy = Verwaltung hierarchischer Verkn\u00FCpfungen
report_completed = Abgeschlossen
STARTING_color = #bebfbb
send = Senden
exception_role_has_pages = Die Rolle {0} ist mit {1} Seite(n) verkn\u00FCpft.
COMPLETED_color = #87b87f
week = Woche
configuration = Konfiguration
instances = Umgebungen
exception_sending_mail_partner = Beim Senden von E-Mails an den/die Partner'' ist ein Fehler aufgetreten: {0}.
site_optimized_for_ie9 = Diese Website ist f\u00FCr Internet Explorer 9 optimiert.
error = Fehler
delete_select_single = M\u00F6chten Sie das ausgew\u00E4hlte Element dauerhaft l\u00F6schen?
security = Sicherheit
STARTING_icon = fa fa-times-circle-o
channel_type = Typ
ks_entry_notbefore = G\u00FCltig seit
campaign_date_end = Enddatum
value = Wert
COMPLETED_label = BEENDET
PENDING_icon = fa fa-cog fa-spin
info_company_created = Kunde {0} angelegt
import_portal = Portal importieren
backoffice = Backoffice
invoice = Rechnungs-Nr.
ks_no_key_found = Im KeyStore wurde kein Schl\u00FCssel gefunden.
Cascade=Untergeordnete Dokumente bereinigen
Detach=Untergeordnete Dokumente trennen
Ignore=Untergeordnete Dokumente ignorieren
doc_purge_children_policy=Hierarchische Verkn\u00FCpfungen
extensions=Erweiterungen
file_name=Dateiname:
last_modification=Letzte \u00C4nderung
extension_label=Eintrag Nr.
export_extension_file=Datei exportieren
export_extension_all_files=Dateien exportieren
extension_name=Name der Erweiterung
displayed_extension_name=Angezeigter Name f\u00FCr die Erweiterung
display_extension=Erweiterung anzeigen

portlet_uploadpdf=UploadPdf
portlet_uploadpdfclient=UploadPdfClient
process_msg = Verarbeiten
organization_registerName = Firmenname
organization_shareCapital = Grundkapital
processing = Status
organization_legalStructure = Rechtsstruktur
unprocesses_message = nicht verarbeitete Nachricht(en)

ACCEPTED_WITH_AMENDMENT = Best\u00E4tigt mit \u00C4nderungen
INVOICED = Fakturiert
NONE = Ungelesen
SENT_PARTIALLY = Teilweise versendet
ACQUITTED = Beglichen
SENT = Versendet

READ = Gelesen
CANCEL = Storniert
REFUSED = Abgelehnt
PENDING = Entwurf

DOC=KPI
UPDATED = Aktualisiert
self_register_login = Registrieren Sie sich
ACCEPTED = Best\u00E4tigt
DUPLICATE = Dupliziert
RESOLVED = Gel\u00F6st
organization_address_country_displayCountry = Land
UNKNOWN = Unbekannt
to_validate = Validierung l\u00E4uft
TO_CORRECT = Zu korrigieren
IN_DISPUTE = Strittig
CLOSED = Geschlossen
APPROVED = Genehmigt
PARTIALLY_SHIPPED = Teilweise gesendet
ANSWERED = Beantwortet
BLOCKED = Geblockt
TIMEOUT = Abgelaufen
java_lang_String = Text
REFUSED_STYLE_STAGE = status_red
Unknown = UNBEKANNT!
bql_filter_title = Filter
WARNING = Warnung
TO_VALIDATE = Validierung ausstehend
SYNTAX_ERR = Syntaxfehler
DELIVERED = Geliefert
Stage_CORRECT = Elektronisch
Stage_UNDEFINED = Nicht elektronisch
Stage_ERROR = Fehler
Stage_UNKNOWN = Unbekannt
organization_address_addressLine = Adresse
TO_REMOVE = Zu l\u00F6schen
REFUSED_MANUALLY = Manuell abgelehnt
OK = OK
ERROR = Fehler
partner_address_1 = Adresse\u00A01
partner_address_2 = Adresse\u00A02
INTEGRATED = Integriert
customer_partner_address_city = Stadt
partner_city = Stadt
clients_type_CUSTOMER = Kunden
partner_country = Land
SHIPPED = Gesendet
FORCED = Erzwungen
customer_partner_name = Name
UNKNOWN_STYLE_STAGE = status_yellow
FATAL = Systemfehler
REMOVED = Gel\u00F6scht
ARCHIVED = Archiviert
user_lastname = Name
customer_partner_create = ${client} hinzuf\u00FCgen
menu_user_information = Mein Konto
menu_company_information = Meine Unternehmensinformationen
user_creation_date = Erstellungsdatum
customer_clientUsers = Meine Benutzer
customer_partner_edit = ${client}
customer_partner_export = Eine Datei ${client} exportieren
customer_partner_edit_button = ${client} aktualisieren
customer_partner_add_user = Benutzer hinzuf\u00FCgen
menu_user_parameters = Meine Einstellungen
user_number_connexion = Verbindung(en)
customer_partner_delete_yes = Ja, l\u00F6schen
user_phone = Telefon
customer_partner_import_button = Datei importieren
customer_partner_add_user_dialog_header = Benutzer zu ${client} hinzuf\u00FCgen
user_login_validator = Die Benutzerkennung muss eine E-Mail-Adresse sein.
menu_company_information_client = Meine Informationen ${client}
user_firstname_placeholder = Ihr Vorname
customer_partner_import_header = Eine Datei ${client} importieren (*.xls, *.xlsx)
customer_partner_create_button = ${client} hinzuf\u00FCgen
partner_user_search_placeholder = Benutzer nach Nr., Name ${client}, Benutzername, Vorname oder Nachname suchen
partner_company = Unternehmen
customer_partner_userNumber = Anzahl der Benutzer
clientPartners = Meine ${clients}
customer_clientPartners = Meine ${clients}
menu_user_notifications = Meine Benachrichtigungen
customer_partner_delete_no = Nein, vorerst nicht
partner_postal_code = Postleitzahl
customer_partner_number = ${client} Nr.
profile_title = Mein Konto
add_user_mail_button = Benutzer hinzuf\u00FCgen + Link zum Zur\u00FCcksetzen des Passworts
customer_partner_import = Eine Datei ${client} importieren
user_mobile = Handy
user_login = Benutzer
customer_partner_name_for_user = Name ${client}
partner_user_create = Benutzer hinzuf\u00FCgen
customer_partner_delete = Den ${client} l\u00F6schen
user_last_authentication = Letzte Anmeldung
user_firstname = Vorname
add_user_button = Benutzer hinzuf\u00FCgen
user_lastname_placeholder = Ihr Nachname
customer_partner_search_placeholder = Suche nach einem ${client} nach Nr., Name oder Stadt
menu_security_user = Mein Passwort
partner_client_name = Name ${client}
company_number = ${client} Nr.
date_to = bis
return_to = Zur\u00FCck zu
introduction_placeholder = Beispielhafter Einleitungstext: \n Die verf\u00FCgbare Dokumentation ist nach Kategorien geordnet. Klicken Sie auf eine Kategorie, um die bereitgestellten Dokumente einzusehen. Falls ein Dokument fehlt, k\u00F6nnen Sie uns dies \u00FCber die Kontaktseite mitteilen.
invalid_file_accent_char =   Die Datei darf keine Zeichen mit Akzent enthalten
library_document_introduction = Die verf\u00FCgbare Dokumentation ist nach Kategorien sortiert. W\u00E4hlen Sie eine Kategorie, um die bereitgestellten Dokumente anzuzeigen.
category = Kategorie
update_user_parameters = Meine Einstellungen aktualisieren
confirmOverrideFilter = Der Filter existiert bereits. M\u00F6chten Sie ihn \u00FCberschreiben?
user_message_delete = Der Benutzer wird endg\u00FCltig gel\u00F6scht.
partner_user_roles_save = Bearbeiten
confirmationTitle = Best\u00E4tigung
partner_user_delete = Benutzer l\u00F6schen
partner_user_roles = Benutzerrolle
company_society = Unternehmen
comment_upload_file = Einen Kommentar zu Ihrer Datei hinzuf\u00FCgen
partner_user_add_role = Rolle zuweisen
user_email = E-Mail-Adresse
partner_user = Benutzer
partner_user_send_new_pass = Passwort zur\u00FCcksetzen
fileName = Dateiname
general_notifications = Meine allgemeinen Benachrichtigungen
menu_legal_information = Rechtliche Informationen
menu_password_user = Mein Passwort
export_list = Liste exportieren
com_byzaneo_xtrade_api_DocumentStatus = Status
update_user_notifications = Meine Benachrichtigungen aktualisieren
activate_notifications = Meine Benachrichtigungen aktivieren
organization_optionReporting = E-Reporting-Optionen
company_information = Unternehmensinformationen
selection_or_enter = Wert ausw\u00E4hlen oder eingeben
IMPORT_CORRECTION = Korrekturimport
info_ordering_removed = Auftraggeber {0} gel\u00F6scht
AAP_MISSING_AN_REF_STYLE = status_red
partner_user_send_pass_yes = Ja, ich m\u00F6chte es senden
authentication_reconfiguration = Erneut konfigurieren
portlet_invoice_refused_manually = RECHNUNG: Manuell ablehnen
users_search_placeholder = Benutzer nach Benutzername, Vorname oder Nachname suchen
portlet_asn_ship = ASN: Versenden
portlet_standardorderlist_actions_2 = STANDARDBESTELLLISTE: Aktion 2
portlet_standardorderlist_actions_1 = STANDARDBESTELLLISTE: Aktion 1
ANSWERED_ICON = fa fa-undo fa-fw fa-rotate-90
portlet_standardorderlist_actions_6 = STANDARDBESTELLLISTE: Aktion 6
client_type_EDI = Partner
portlet_standardorderlist_actions_5 = STANDARDBESTELLLISTE: Aktion 5
portlet_standardorderlist_actions_4 = STANDARDBESTELLLISTE: Aktion 4
portlet_standardorderlist_actions_3 = STANDARDBESTELLLISTE: Aktion 3
DELIVERED_STYLE = status_green
instance_type_SPECIFIC = Spezifisch
REJECTED_STYLE = status_red
reconciliation_success = Abgleich abgeschlossen.
organization_autoGenerationOfSSCC = SSCC generiert
error_removing_client = Fehler beim L\u00F6schen des Kunden ({0}).
ARCHIVED_STYLE = status_green
accessTokenV1_username = Name des Benutzers
portlet_standardorderlist_remove = STANDARDBESTELLLISTE: L\u00F6schen
EP_ELIGIBLE = Geeignet
BEING_SENT_CLIENT_STYLE = status_orange
info_partners = W\u00E4hlen Sie hier den Namen oder den Code einer Entit\u00E4t aus.
R = Empf\u00E4nger
manage_rules = Regeln verwalten
S = Sender
agreement_file_not_found = Die AGB {0} wurden in der Sprache {1} nicht gefunden.
perimeter_edit = Bereich bearbeiten f\u00FCr
SMARTPDF = LIEFERANTENPORTALE
portlet_referentiel_taxes = Stammdaten/Steuern
bql_instructions_title = Titel der BQL-Anweisungen
portlet_standardorderlist_actions_9 = STANDARDBESTELLLISTE: Aktion 9
portlet_standardorderlist_actions_8 = STANDARDBESTELLLISTE: Aktion 8
portlet_standardorderlist_actions_7 = STANDARDBESTELLLISTE: Aktion 7
check_site_conditions = Ich akzeptiere die Nutzungsbedingungen der Website
portlet_standardorderlist_export = STANDARDBESTELLLISTE: Exportieren
SENT_ICON = fa fa-paper-plane-o
correct = Korrigieren
Noname = KEIN NAME!
portlet_invoice_join = RECHNUNG: Zusammenf\u00FChren
RCL_READY = Bereit f\u00FCr Bericht
deploy = Einsetzen
AAP_IN_PROGRESS_STYLE = status_yellow
PARTIALLY_ACCEPTED_WITH_AMENDMENTS = Teilweise best\u00E4tigt mit \u00C4nderungen
workflow_notifications = Meine Workflow-Benachrichtigungen verwalten
portlet_files = DATEIEN
accessTokenV1_access_fail = Zugriff auf diese Ressource nicht m\u00F6glich: {0}.
cart_checkout = BEST\u00C4TIGEN
a_day_past = weniger als ein Tag
filterSuccessfullySaved = Der Filter wurde gespeichert
authentication_partners_pairs = Partnerkonfiguration
APPROVED_PARTIALLY_STYLE_STAGE = status_yellow
RCL_SUCCESS_STYLE = status_green
users_email = Benutzer/E-Mail
contextual_validation_user_scope_partner_id = Die ID-Nummern f\u00FCr den Partner {0} und den Bereich {1} m\u00FCssen f\u00FCr den Benutzer {2} unterschiedlich sein.
organization_end = Enddatum
add_ordering_party = Auftraggeber hinzuf\u00FCgen
portlet_deadpool = Deadpool
counter_management_other_page_configuration = Einstellungen einer anderen Seite verwenden
cart_view = WARENKORB ANZEIGEN
root = Wurzelverzeichnis
ROSSUM = ROSSUM
accounting_posting_management = Verwaltung
file_process_will_be_executed = Der Prozess wird ausgef\u00FChrt
UNDEFINED_STYLE = Standard
RCL_DISCREPANCY = Berichtsabw.
clients_type_EDI = Partner
element_order = Bestellung
bank_account_bic = BIC
accounting_posting_referentials_tax = Steuern
partner_user_send_new_pass_header = Passwort f\u00FCr den Benutzer zur\u00FCcksetzen
REFERENTIAL_OK = Stammdaten ok
portlet_referentiel_produits_logistics_info = Stammdaten/Produkte/Logistikinformationen
menu_process_execution = Prozess: Ausf\u00FChrungen
from_address_validator = Die E-Mail-Adresse des Absenders ist ung\u00FCltig. Es kann nur eine Adresse angegeben werden.
sap_configuration_network = Netzwerk
organization_address_country_iSO3Country = ISO-3-Code des Landes
PARTIALLY_ACCEPTED = Teilweise best\u00E4tigt
demat_partner_dialog_title = \u00C4nderungsverlauf
eReporting_collection = Auswahl der Sammlung
AAP_READY_STYLE = Standard
ordering_identifier_exists = Es existiert bereits ein Auftraggeber mit der Kennung {0}
angular_url = Angular-URL
error_files_too_large = Die Dateigr\u00F6\u00DFe \u00FCberschreitet die maximal zul\u00E4ssige Gr\u00F6\u00DFe von 1,7\u00A0GB
checkbox_banner = Banner aktivieren
BLOCKED_STYLE = status_orange
AAP_NA_STYLE = Info
document_type = Dokumententyp
related_process = Nach verkn\u00FCpften Prozessen suchen
PAYMENT_SENT_STYLE_STAGE = status_green
IN_VALIDATION = Validierung l\u00E4uft
REFERENTIAL_KO = Stammdaten fehlgeschlagen
clear_query = L\u00F6schen
METADATA_KO_STYLE = status_red
info_client_saved = Kunde {0} gespeichert
clientId_secretKey = Geheimer Schl\u00FCssel ClientId
AAP_MANUAL_MODE = Manuelle Verrechn.
date_last_authentication_no_date = Datum der letzten Anmeldung: Informationen nicht verf\u00FCgbar
UNARCHIVABLE_STYLE = status_red
instance_type_INVOICE = Rechnung
portlet_invoice_forced = RECHNUNG: Erzwingen
angular_template = Angular-Vorlage
error_password_link_generation = Bei der Generierung des Links zur Zur\u00FCcksetzung des Passworts ist ein Fehler aufgetreten.
cart_totalAmount = Insgesamt:
rte_input_file_must_be_unique = Die Eingabedatei muss eindeutig sein
error_exporting_client = Fehler beim Exportieren von Kunden ({0}).
organization_address_country_country = ISO-2-Code des Landes
aws_batch = Batch AWS
environment_error_max_characters_allowed = Maximal 64\u00A0Zeichen sind erlaubt
error_export_not_empty_portlet = Sie k\u00F6nnen nicht exportieren: Das Portlet ist nicht leer.
WKF_REFUSED_STYLE = status_red
Stage_SENT = Von der Plattform ausgestellt
authentication_togetherConfiguration = Gemeinsame Konfiguration
info_client_removed = Kunde {0} gel\u00F6scht
cart_numberOfArticles = Artikel(n) im Warenkorb
pageIconPlaceholder = z.\u00A0B.: fa fa-bar-chart
order_history_title = BESTELLVERLAUF NR.:
AVAILABLE_STYLE_STAGE = status_green
rte_status_not_deployed = Das RTE-Skript ist nicht bereitgestellt
BEING_SENT_CLIENT = Sendung an Kunden l\u00E4uft
compliance_life_cycle_factor_confidential_permissions = Vertrauliches Factoring
error_importing_page = Fehler beim Importieren der Seite
identical_new_passoword = Das neue Passwort ist identisch mit dem alten Passwort
portlet_invoice_remove = RECHNUNG: L\u00F6schen
RECEIVED_STYLE_STAGE = status_green
workflow_tab = Workflows
AAP_MISSING_REF_STYLE = status_red
WKF_IN_PROGRESS_STYLE = status_yellow
cannot_assign_role = Sie k\u00F6nnen eine Rolle zuweisen, aber es ist keine Rolle konfiguriert
SUBMITTED = \u00DCberwiesen
logistic_gs1_error_format_message = Der einheitliche nationale Lieferantencode (GS1) muss aus 7 bis 10 Ziffern bestehen.
action_line = Aktion auf der Linie
organization_vatRegime = Option f\u00FCr das Umsatzsteuerregime:
NO_ROUTE = Route nicht vorhanden
workflow_checkbox = Benutzern erlauben, Workflow-Benachrichtigungen selbst zu konfigurieren
RCL_NA_STYLE = Info
UNKNOWN_ICON = fa fa-remove
APPROVED_B2G_STYLE_STAGE = status_green
notification_new_contact = E-Mail-Benachrichtigung neuer Kontakt
ERROR_STYLE = status_red
UPDATED_STYLE = Info
placeholder_database_name = Name der Instanzdatenbank
compliance_life_cycle_partially_approve_permissions = Teilweise genehmigen
OTHER = ANDERE
contact_recipients = Empf\u00E4nger der Kontakt-E-Mail
IN_DELIVERY_STYLE = status_orderlist_yellow
INTEGRATED_STYLE = status_green
compliance_life_cycle_cash_in_permissions = Kassieren
portlet_referentiel_produits_other = Stammdaten/Produkte/Sonstiges
reconciliation_empty_seller_party = Abgleich nicht m\u00F6glich: Lieferantencode fehlt.
template_type = Vorlagentyp
assignable_roles = Zurechenbare Rollen
accounting_posting_referentials = Stammdaten
portlet_documentation = DOKUMENTATION
portlet_referentiel_addresses = Stammdaten/Adressen
not_valid = Nicht g\u00FCltig
logistic_gs1_company_prefix = GS1-Unternehmenscode (CNUF)
Stage_APPROVED = Genehmigt
error_export_empty_page = Sie k\u00F6nnen keine leere Seite exportieren;
Stage_DISPUTED = Strittig
no_records_found_loreal_order = Es wurden keine passenden Artikel gefunden. <br/> Bitte \u00E4ndern Sie Ihre Suche.
authentication_preDomain = Vor-Domain
AWAITING_VALIDATION_STYLE = status_orange
compliance_life_cycle_factor_permissions = Factoring
accessTokenV1_tokenExpirationDate = Ablaufdatum des Tokens
authenticationServer_tab = Authentifizierungsserver
rte_status_deployed = Das TEN-Skript wurde erfolgreich bereitgestellt
AAP_ERROR_STYLE = status_red
compliance_life_cycle_suspend_permissions = Aussetzen
REFUSED_STYLE = status_red
compliance_life_cycle_refuse_permissions = Ablehnen
manual_analytical_ventilation = Manuelle analytische Aufschl\u00FCsselung
TO_VALIDATE_ICON = fa fa fa-pause fa-fw
accounting_posting_management_scenario = Szenarien
IN_PREPARATION_STYLE = status_orderlist_yellow
mark_doc_as_unread_warn = Es k\u00F6nnen nur Dokumente mit dem Status \u201EGelesen\u201C zur\u00FCckgesetzt werden. Keine Aktion ausgef\u00FChrt.
file_exceeded_number = Sie k\u00F6nnen nicht mehr als 10\u00A0Dokumente gleichzeitig importieren
GCN = KOLLABORATIVES NETZWERK
consultation_status = \u00C4ndern Sie den Ansichtsstatus des Dokuments nicht auf \u201EGelesen\u201C.
info_import_processes = Die Prozesse wurden importiert
document_statuses_added = {0} neue Status wurde(n) hinzugef\u00FCgt
logistic_serial_reference_required = Die Sequenznummer ist ein Pflichtfeld, da die automatische Generierung von SSCCs aktiviert ist.
dialog_message_sequence_edit = Nachrichtensequenz\u00A0\u2013 Bearbeiten
CLOSED_STYLE = Standard
Development = Entwicklung
TO_CORRECT_STYLE = status_yellow
rte_base_not_supported = RTE-Sammlungen sind (vorerst) vom Testbereich ausgeschlossen.
RCL_PRICES_DISCR = Berichtsabw. \u2013 Preis
client_type_SPECIFIC = Partner
user_password_dialog_header = Passwort hinzuf\u00FCgen
portlet_order = Bestellung
REFERENTIAL_KO_STYLE = status_red
archive_storage_option = Archivspeicherung
menu_user_customer_supplier_env_bank = Bankverbindung meiner Lieferanten
sap_configuration_serialization_format = Serialisierungsformat
self_register_placeholder_2_placeholder = Eindeutiges Anmeldefeld der Selbstregistrierungsseite
general_edi_service = Generix-EDI-Service
total_carts = Gesamtbetrag der Warenk\u00F6rbe:
placeholder_host_name = Hostname oder IP-Adresse des Systems, auf dem die Instanz l\u00E4uft
menu_process_trigger = Prozess: Trigger
add_new_status = Dokumentenstatus hinzuf\u00FCgen
logistic_gs1_company_not_provided = Bitte geben Sie den einheitlichen nationalen Lieferantencode ein.
notify_for_new_action_to_perform = Bei jeder anstehenden Aktion zu einer Rechnung benachrichtigt werden
contextual_validation_user_scope_user_cpy = Benutzer {0} geh\u00F6rt nicht zu {1}.
exception_duplicate_perimeter = Duplizierter Bereich: {0}.
IN_PREPARATION = Vorbereitung l\u00E4uft
invidx_wkf_reinit = Zur\u00FCcksetzen der Informationen \u00FCber Workflows im Rechnungsindex
WKF_IN_PROGRESS = Wkf im Gange
CANCEL_ICON = fa fa-times
counter_management_own_configuration = Spezifische Einstellungen f\u00FCr diese Seite festlegen
document_statuses_updated = {0} Status wurde(n) aktualisiert
menu_process_deployment = Prozess: Bereitstellung
RCL_INTRUDER_PROD_STYLE = status_orange
Production = Produktion
PARTIALLY_ACCEPTED_STYLE = status_partially_accepted
UNKNOWN_STYLE = status_yellow
ks_error_could_not_extract_certificate = Das in der Datei enthaltene(n) Zertifikat(e) kann/k\u00F6nnen nicht extrahiert werden.
client_number = Nr. ${client}
portlet_referentiel_global_allowances_charges = Stammdaten/Globale Rabatte und Zuschl\u00E4ge
DISPUTED_STYLE_STAGE = status_yellow
ie_recognition = Internet-Explorer-Erkennung
Acceptance = Rezept
warn_importing_multiLevel_iconPages = Der Import kann nicht durchgef\u00FChrt werden, da eine \u201ESymbol\u201C-Seite nur eine Ebene haben kann
portlet_standardorderlist_export_list = STANDARDBESTELLLISTE: Liste exportieren
AAP_COMPLETED_AN_STYLE = status_green
to_prepare = Vorbereitung l\u00E4uft
banking = Bank
rte_status_deployed_but_changed = Das RTE-Skript wurde seit der Bereitstellung ge\u00E4ndert
sap_configuration_host = Host
CONTROL_OK_STYLE = status_green
error_saving_perimeter = Fehler beim Speichern des Bereichs ({0}).
SENT_PARTIALLY_STYLE = status_yellow
angular_version = Angular-Version
error_saving = Fehler beim Speichern ({0})
authenticationServer_configuration = Konfiguration des Authentifizierungsservers
portlet_invoice_actions_10 = RECHNUNG: Aktion\u00A010
general_invoices_service = Generix-Rechnungsservice
agreement_default_required = Die AGB m\u00FCssen standardm\u00E4\u00DFig in der Sprache {0} (Registerkarte AGB) angezeigt werden.
banner = Banner
import_cert_pem_cer_crt = PEM/CER/CRT
api_permissions_roles = Rollen
host_name = Domain
menu_user_customer_other_env_bank = Bankverbindung meiner Partner
error_existing_clientId = Diese clientId existiert bereits f\u00FCr dieses Realm, bitte w\u00E4hlen Sie eine andere clientId aus.
select_validation_process = Post-Validierungsprozess
EP_ACCEPTED = Antrag angenommen
estimated_delivery_date = Voraussichtliches Lieferdatum:
SENT_PARTIALLY_ICON = fa fa-paper-plane
ordering_party_delete_last_element = M\u00F6chten Sie das letzte Element l\u00F6schen?
authenticationServer_btn = Authentifizierungsserver konfigurieren
EDOCUMENT = RECHNUNGSSERVICES
delete_counter_error_message = Achtung: Dieser Z\u00E4hler kann nicht gel\u00F6scht werden, da er in Miniaturz\u00E4hlern verwendet wird.
WARNING_STYLE = status_yellow
info_category = Zum ordnen der Kategorien muss die unterste Kategorie per Drag\u00A0&\u00A0Drop nach oben verschoben werden.
importPageWarningText = Das referenzierte Portlet existiert nicht, der Link zu diesem Portlet wird nicht erstellt. M\u00F6chten Sie trotzdem importieren?
error_password_generation = Bei der Generierung des Passworts ist ein Fehler aufgetreten.
manage_periodic_reports = Verwaltung periodischer Berichte
exception_remove_technicalUser_associated = Der/Die technische/n Benutzer, der/die mit {0} verkn\u00FCpft ist/sind, konnte/n nicht gel\u00F6scht werden.
banner_text = Anzeigetext
CONTROL_TOTAL_KO = Betragskontrollen fehlgeschlagen
self_register_placeholder_1 = Selbstregistrierung Platzhalter\u00A01
self_register_placeholder_2 = Selbstregistrierung Platzhalter\u00A02
authentication_defaultClientId = Standard-ClientId
exception_technicalUsers_perimeterAssociation = Dem Bereich {0} ist/sind {1} technische/r Benutzer zugeordnet.
Category.name = Kategorie
clients_type_INVOICE = Partner
organization_bankAccount_iban = IBAN
AAP_COMPLETED = Verrechnung abgeschlossen
counter_management_title = W\u00E4hlen Sie die anzuzeigenden Z\u00E4hler aus
authentication_partners = Partner
bank_account_name_required = Name ist ein Pflichtfeld
document_status = Dokumentenstatus
ACCEPTED_WITH_AMENDMENT_STYLE = Info
ERROR_ICON = fa fa-remove
receptionItem = ReceptionItemCodeField
java_lang_Integer = Ganzzahl
api_permissions_users = Benutzer
organization_bankAccount_bic = BIC
REFUSED_ICON = fa fa-exclamation-triangle
perimeters = Bereiche
logistic_missing_mandatories_message = Bitte geben Sie alle erforderlichen Informationen ein, da die automatische Generierung von SSCC aktiviert ist.
AAP_MISSING_PO_STYLE = status_red
portlet_freetext = FreeText
technical_user_partner = Partner
ROLE_TECHNICAL = Technische Rolle
Stage_UPLOADED = Eingereicht
portlet_demat_partner_file = DematPartnerFile
SYSTEMATIC = Systematische Generierung
life_cycle = Lebenszyklus
counter_management = Verwaltung von Miniaturz\u00E4hlern
FATAL_STYLE = status_red
outputFile_working_copy = Kopie erstellen [Arbeitskopie] [Ausgabedatei]
element_contextmenu_config_form = Formular konfigurieren
secret_api_key = SecretAPIKey
accounting_posting_entries = Buchungen
ignored = Ignoriert
edit_technical_user = Technischen Benutzer \u00E4ndern
logistic_gs1_company_required = Der GS1-Code ist ein Pflichtfeld, da die automatische Generierung von SSCC aktiviert ist.
bql_filter_details = BQL-Abfrage zur Filterung der Rechnungs-Portlet
Stage_APPROVED_B2G = Angestrebt
portlet_order_actions_10 = BESTELLUNG: Aktion 10
bank_account_currency = W\u00E4hrung
button_message_sequence = Reihenfolge der Nachrichten
listbox_message_sequence_available_messages = Verf\u00FCgbare Nachrichten
portlet_order_confirm_with_modification = BESTELLUNG: Best\u00E4tigen mit \u00C4nderung
agreement_instance_required = AGB-Umgebung ist ein Pflichtfeld
error_invalid_date = {0}: Der eingegebene Wert ist kein Datum: {1}
AAP_EXPORTED = Verrechnung exportiert
warm_change_into_empty_when_other_exists = Diese Anordnung kann nicht in eine leere Anordnung ge\u00E4ndert werden, solange andere Anordnungen vorhanden sind
filter_by_type = Nach Typ filtern
portlet_invoice_export_list = RECHNUNG: Liste exportieren
workflow_functions = Funktionen
organization_VR1 = Unternehmen unterliegt dem normalen monatlichen Steuerregime
organization_VR3 = Unternehmen mit vereinfachtem Umsatzsteuerregime
organization_VR2 = Unternehmen mit viertelj\u00E4hrlicher Steueroption im normalen Steuerregime
organization_VR4 = Unternehmen mit Umsatzsteuer-Befreiungsregelung
error_creating_partnerConfig = Fehler beim Erstellen der Authentifizierungskonfiguration f\u00FCr den Partner {0}
DEMAT_ERR = Digitalisierungsfehler
agreement_lang_required = Die Sprache der AGB ist ein Pflichtfeld
invoice_posting_manual_entry = Manuelle Erfassung der Konten
TO_REMOVE_STYLE = status_yellow
logistic_serial_reference = Sequenznummer
portlet_indexdata_export = INDEXDATA: Exportieren
file_docs_uploaded = Die Dokumente wurden importiert
select_duplicate_check = \u00DCberpr\u00FCfung auf Duplikate
invoice_posting = Rechnungsverbuchung
PENDING_STYLE = status_yellow
compliance_life_cycle_approve_permissions = Genehmigen
cookie = Cookie
ACCEPTED_STYLE = Info
no_processed = Nicht verarbeitet
warm_add_empty_when_other_exits = Sie k\u00F6nnen keine leere Anordnung hinzuf\u00FCgen, wenn bereits eine Anordnung existiert
instance_type = Portaltyp
portlet_invoice_actions_3 = RECHNUNG: Aktion\u00A03
portlet_invoice_actions_4 = RECHNUNG: Aktion\u00A04
portlet_invoice_actions_1 = RECHNUNG: Aktion\u00A01
portlet_invoice_actions_2 = RECHNUNG: Aktion\u00A02
portlet_invoice_actions_7 = RECHNUNG: Aktion\u00A07
Stage_APPROVED_PARTIALLY = Teilweise genehmigt
portlet_invoice_actions_8 = RECHNUNG: Aktion\u00A08
portlet_invoice_actions_5 = RECHNUNG: Aktion\u00A05
triggername_ACTION_10 = Aktion 10
portlet_invoice_actions_6 = RECHNUNG: Aktion\u00A06
COMPLETED_STYLE_STAGE = status_yellow
workflow_delegations = Vertretungen
ANSWERED_STYLE = Info
RCL_QTY_DISCR = Berichtsabw.\u00A0\u2013 Menge
UNARCHIVABLE = Nicht archivierbar
role_type = Typ
menu = Maximale Dateigr\u00F6\u00DFe f\u00FCr Anh\u00E4nge
portlet_standardorderlist_actions_10 = STANDARDBESTELLLISTE: Aktion 10
IN_DELIVERY = Wird ausgeliefert
portlet_invoice_actions_9 = RECHNUNG: Aktion\u00A09
Accounting_Active = Aktiv
idp_alias_for_usurpation = IDP-Alias f\u00FCr Identit\u00E4ts\u00FCbernahme
kpis = KPIs
portlet_referentiel_produits_remise_charge = Stammdaten/Produkte/Rabatt und Zuschlag
return = Zur\u00FCck
edit_library = Eine Bibliothek bearbeiten
RCL_READY_STYLE = Standard
url_tracking = URL-Tracking f\u00FCr jeden angemeldeten Benutzer aktivieren
my_bank_accounts = Meine Bankkonten
partner_user_message_send_new_pass_msg = Der Benutzer erh\u00E4lt eine E-Mail mit einem Link, \u00FCber den er sein Passwort \u00E4ndern kann.
invidx_wkf_replayed = Wiederholte Workflows: {0}
api_security = Sicherheit
api_configuration = Konfiguration
library_created_success = Die Dokumentation wurde erstellt
portlet_orderresponse_import = ORDER RESPONSE: Importieren
api_permissions_get_perimeters = Bereiche
periodic_reports = Periodische Berichte
java_lang_Class = Klasse
END_PROCESS = Verarbeitung abgeschlossen
contextual_validation_partner_role = Die Rolle muss existieren
PAID_STYLE = status_green
MISSING_PROCESSINGWAY = processingWay nicht vorhanden
clientUsers = Meine Benutzer
processed = Verarbeitet
save_and_send_renewal_link = Speichern und Link zum Zur\u00FCcksetzen des Passworts senden
authentication_classicConfiguration = Klassische Konfiguration
document_status_updated = Der Status des Dokuments {0} wurde aktualisiert
element_order_response = Antwort auf die Bestellung
History = RoquetteHistory
rte_test_properties_placeholder = Eigenschaften:
in_delivery = Wird ausgeliefert
rte_forbidden_value = init.tst: Die Eigenschaft {0} ist nicht korrekt angegeben
EP_REFUSED = Antrag abgelehnt
accounting_posting_referentials_product = Produkte
AAP_EXPORTED_STYLE = status_deep_blue
Stage_PAYMENT_RECEIVED = Eingezogen
AAP_TRANSMITTED_STYLE = status_green
delete_button_message_sequence = L\u00F6schen
regex = Regex
ordering_name = Name
jsf_templates = Jsf-Vorlagen
portlet_invoice_add = RECHNUNG: Hinzuf\u00FCgen
sap_configuration = SAP
com_byzaneo_xtrade_api_DocumentStage = Status
not_notified_if_absent_and_have_replacement = Nicht benachrichtigt werden, wenn ich abwesend bin und vertreten werde
uploaded_since = Hochgeladen von
indexClassName = Indextyp
error_localizations_import_bad_structure = Die Struktur der Datei entspricht nicht dem erwarteten Format
customer_partner_message_delete = Der ${client} wird endg\u00FCltig gel\u00F6scht.
exception_technicalRole_association = Die Rolle {0} ist mit {1} technischem/n Benutzer(n) verkn\u00FCpft
WKF_READY_STYLE = Standard
mark_docs_as_unread_warn = Einige Dokumente haben nicht den Status \u201EGelesen\u201C. F\u00FCr diese Auftr\u00E4ge wurde keine Aktion ausgef\u00FChrt.
error_export_portlet_rte_collection = Sie k\u00F6nnen das Portlet \u201ECollection\u201C nicht exportieren.
database_name = Name der Datenbank
to_edit = Bearbeiten
portlet_invoice_export = RECHNUNG: Exportieren
Stage_REFUSED = Abgelehnt
channel_auth_userDnPattern = Benutzer-DN-Muster
template_name = Name der Vorlage
portlet_payment_actions_10 = ZAHLUNG: Aktion 10
info_import_portal = Das Portal wurde importiert
progress = In Bearbeitung\u00A0...
edit_status = Einen Status f\u00FCr die Dokumente bearbeiten
AAP_NA = Verrechn. \u2013 Fehl. Obj.
RESOLVED_STYLE = status_green
scope_customer = Eingeschr\u00E4nkter Bereich
AAP_MISSING_RCP = Verrechn. \u2013 Fehl. Wareneingang
portlet_referentiel_produits = Stammdaten/Produkte
AAP_ERROR = Verrechnungsfehler
com_byzaneo_xtrade_api_DocumentConsultStatus = Beratungsstatus
eDocument = E-Dokument
CORRECT_STYLE_STAGE = status_green
active_carts = aktiver Warenkorb\u00A0/aktive Warenk\u00F6rbe
portlet_asn_import = ASN: Importieren
menu_user_addresses = Adressbuch meines Unternehmens
authentication_defaultClientIdJSF = ClientIdJSF standardm\u00E4\u00DFig
doc_status_code = Statuscode
general_customers_service = Generix-Kundenservice
RCL_IMPOSSIBLE_STYLE = status_red
category_field_empty = Kategorie ist ein Pflichtfeld.
info_user_role_duplicated = Die Rolle {0} wurde dupliziert
AAP_MISSING_REF = Verrechn. \u2013 Fehl. Ref.
Stage_SUSPENDED = Gesperrt
no_processes_were_found = Der RTE wird in keinem Prozess aufgerufen
invalid_role = Sie haben nicht keine Berechtigung, auf die Ressource zuzugreifen.
organization_bankAccount_partner = Partner
info_perimeter_saved = Bereich {0} gespeichert
Stage_OPENED = \u00DCbernahme
error_export_shadow_portlet = Diese Seite kann nicht exportiert werden, da sie mindestens ein \u201EShadow\u201C-Portlet enth\u00E4lt.
TO_VALIDATE_STYLE = status_yellow
rte_status_unknown = Zu bestimmen
status_import_file = Import einer Datei mit Dokumentstatus (.xls, .xlsx)
FORCED_STYLE = Standard
import_cert_pkcs7 = PKCS#7
save_register = Ich best\u00E4tige meine Registrierung
disagree = Ablehnen
error_saving_client = Fehler beim Speichern des Kunden ({0}).
RECIPIENT = Empf\u00E4nger
error_deletingCompanyWithAuthConfig = Dieses Unternehmen ist mit der Konfiguration des Authentifizierungsservers verkn\u00FCpft. Bitte entfernen Sie vor dem L\u00F6schen die Konfiguration dieses Unternehmens.
AAP_TRANSMITTED = Verrechnung \u00FCbermittelt
contact_subject = Betreff der Kontakt-E-Mail
user_email_placeholder = E-Mail-Adresse f\u00FCr die Anmeldung
SIGNATURE_KO_STYLE = status_red
logistic_serial_reference_and_cnuf_error_format_message = Die L\u00E4nge des CNUF und der Sequenznummer muss 16 Zeichen betragen.
java_math_BigDecimal = Dezimalzahl
portlet_faq = FAQ
info_technicalUser_role_saved = Rolle als technischer Benutzer gespeichert
orderItem = OrderItemCodeField
Stage_PAYMENT_SENT = Zahlung erfolgt
filter_sending = Sendung
agree = Akzeptieren
technical_user_selectPartner = W\u00E4hlen Sie einen Partner aus
AAP_MISSING_PO = Verrechn. \u2013 Fehl. Best.
ACCEPTED_ICON = fa fa-check
error_existingAuth_clientId = Die clientId {0} existiert bereits in der Dom\u00E4ne, bitte w\u00E4hlen Sie eine andere clientId aus
SENT_STYLE_STAGE = status_green
Contract = RoquetteContract
organization_extension = Erweiterungszeichen
error_saving_invoice_entry_preferences = Fehler beim Speichern von Rechnungspr\u00E4ferenzen
sso_error = Beim Authentifizierungsversuch ist ein Fehler aufgetreten.
add_description = Beschreibung hinzuf\u00FCgen
Stage_RECEIVED = Von der Plattform erhalten
authentication_defaultClientIdAngular = ClientIdAngular standardm\u00E4\u00DFig
triggername_ACTION_2 = Aktion 2
triggername_ACTION_1 = Aktion 1
triggername_ACTION_4 = Aktion 4
triggername_ACTION_3 = Aktion 3
triggername_ACTION_6 = Aktion 6
triggername_ACTION_5 = Aktion 5
triggername_ACTION_8 = Aktion 8
triggername_ACTION_7 = Aktion 7
SUBMITTED_STYLE = status_green
triggername_ACTION_9 = Aktion 9
menu_rights_clients = Kunden
authentication_clientIdJSF = ClientIdJSF
compliance_life_cycle_payment_sent_permissions = Zahlung erfolgt
info_addressSearch = Es ist nicht m\u00F6glich, nach L\u00E4nderfeldern zu suchen
imenu.portal = Kollaborativ
APAUTOMATION = AP-AUTOMATION
IMPORT_CORRECTION_STYLE = Info
in_validation = Validierung l\u00E4uft
ERR_XLEG = Gesetzl: Pr\u00FCfungsfehler
error_remove_partnerConfig = L\u00F6schen der Authentifizierungskonfiguration f\u00FCr den Partner {0} fehlgeschlagen
authentication_selectPartner = W\u00E4hlen Sie einen Partner aus
counter_management_pages = Seiten
portlet_library = Bibliothek
ordering_partners = Partner
contextual_validation_user_scope_role = Benutzer {0} hat nicht die Rolle {1}
error_importing_Languages = Beim Importieren der Portal-Lokalisierungsdatei ist ein Fehler aufgetreten
bank_account_iban_error_exist = Mit dieser W\u00E4hrung ist leider bereits eine IBAN verkn\u00FCpft. Bitte l\u00F6schen Sie die IBAN, bevor Sie eine andere eingeben.
rte_collection = Sammlung
invalid_password = Das Passwort ist ung\u00FCltig. Bitte geben Sie ein neues Passwort ein.
ok_switch = Best\u00E4tigen
validated = Validiert
menu_user_invoice_entry_preferences = Rechnungseinstellungen meines Unternehmens
IN_SUBMISSION = Auszahlung l\u00E4uft
RCL_DISCREPANCY_STYLE = status_orange
channel_AuthenticationEndpointConfiguration = Authentifizierung
error_updating_user_keycloak = Aktualisierung des Benutzers keycloak {0} fehlgeschlagen
scope_partner = Erweiterter Bereich
SMTP_ERROR_STYLE = status_red
portlet_payment_accept_reject_early_payment = Vorauszahlung Verk\u00E4ufer
AAP_MANUAL_MODE_STYLE = status_yellow
WKF_NA_STYLE = Info
authentication_addPartnerConfiguration = Partnerkonfiguration hinzuf\u00FCgen
java_lang_Boolean = Boolean
jsf_template = Jsf-Vorlage
select_legal_controls_label = Gesetzliche Pr\u00FCfungen anwenden
exception_import_page_null = Die Exportseite ist null.
api_permissions_get_partners = Partner
info_import_page = Seite importieren OK
portlet_referentiel_exchange_rates = Stammdaten/Wechselkurse
portlet_referentiel_produits_general_description = Stammdaten/Produkte/Allgemein/Beschreibung
Stage_AVAILABLE = Zur Verf\u00FCgung gestellt
agreement = AGB
organization_eReporting = E-Reporting-Option:
portlet_standardorderlist_duplicate = STANDARDBESTELLLISTE: Duplizieren
autoGenerationOfSSCC = SSCC generiert
bank_account_bic_error_required = Der BIC-Code ist ein Pflichtfeld
error_creating_companyConfig = Fehler beim Erstellen der Authentifizierungskonfiguration f\u00FCr das Unternehmen {0}
logistic_sscc = SSCC
users_number_exceeded = Sie \u00FCberschreiten die Anzahl der Benutzer f\u00FCr einen ${client}. Bitte l\u00F6schen Sie inaktive Benutzer oder wenden Sie sich an den Support.
client_type_SUPPLIER = Lieferant
instance_type_INVOICE_PDP = Rechnung\u00A0\u2013 PDP
requested_delivery_date = Gew\u00FCnschtes Lieferdatum:
Stage_NOT_APPROVED_B2G = Nicht angestrebt
contextual_validation_user_scope_user = Der Benutzer {0} existiert nicht
organization_referenceCurrency = Referenzw\u00E4hrung
REFUSED_MANUALLY_STYLE = Standard
ROLE = Rolle im Portal
rte_test_results_header = RTE-Testergebnisse
imputation_status_unprocessed = Status, wenn kein Buchungsszenario anwendbar ist
reconciliation = Abgleich
user_email_address = E-Mail
METADATA_KO = Metadaten fehlgeschlagen
IN_VALIDATION_STYLE = status_orderlist_blue
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE = status_deep_blue
rte_test_fail_message = RTE-Test fehlgeschlagen
APPROVED_STYLE_STAGE = status_green
reconciliation_info = Konfiguration des Suchschl\u00FCssels zur Durchf\u00FChrung des Zeilenabgleichs
AAP_READY = Bereit f\u00FCr Verrechnung
locale_not_defined = Die Sprache Ihres Partnerdatensatzes ist nicht festgelegt, bitte geben Sie eine Sprache an
REFERENTIAL_OK_STYLE = status_green
portlet_invoice_view_history = RECHNUNG: Pr\u00FCfen
java_util_Date = Datum
counter_management_counter = Z\u00E4hler
no_files_choosen = Keine Datei ausgew\u00E4hlt
SIGNATURE_OK_STYLE = status_green
organization_gs1 = GS1-Unternehmenscode
clients_type_SPECIFIC = Partner
order_number = Bestell-Nr.
info_perimeter_removed = Bereich {0} gel\u00F6scht
INVOICED_STYLE = status_green
warn_deleting_own_user = Der Benutzer {0} kann nicht gel\u00F6scht werden, da es Ihr eigener Benutzer ist.
rte_studio = RTE-Studio
portlet_pack = Paket
organization_start = Startdatum
portlet_standardorderlist_refuse = STANDARDBESTELLLISTE: Manuell ablehnen
bql_instructions_documents = Dokument
portlet_invoice_correct = RECHNUNG: Korrigieren
reconciliation_gap_found = Abgleich abgeschlossen, Abweichungen festgestellt.
contextual_validation_user_scope_partner = Partner {0} geh\u00F6rt nicht zu {1}.
select_rte_label = RTE-Quelle f\u00FCr Kontrollen
WKF_READY = Bereit f\u00FCr Wkf
logistic_sscc_auto = Automatische SSCC
select_rte_source = Ausw\u00E4hlen
CONTROL_TOTAL_KO_STYLE = status_red
TO_REMOVE_ICON = fa fa-archive
customer_partner_connectionCode = Eindeutige Anmelde-Nr.
self_register_placeholder_1_placeholder = Feld Nr. ${client} auf der Selbstregistrierungsseite
info_parameters_saved = Benutzereinstellungen gespeichert
RCL_IMPOSSIBLE = Berichterstell. unm\u00F6glich
add_page = Seite hinzuf\u00FCgen
connection_code = Eindeutige Anmelde-Nr.
portlet_taxes_allowances = Rabatte und parafiskalische Steuern
organization_role = Rolle
element_arrow_optional = Optional
organization_serialReference = Sequenznummer
element_shipping_advice = Versandavis
banking_partner = Partnerbank
sap_configuration_expiration_check = Ablauf der Pr\u00FCffrist
exception_perimeter_has_users = Dem Bereich {0} sind {1} Benutzer zugeordnet.
ap_automation_aged_balance = F\u00E4lligkeits\u00FCbersicht
ordering_party_exit_without_saving = M\u00F6chten Sie ohne Speichern des Diagramms verlassen?
workflow_monitoring = Monitoring
instance_type_EDI = EDI
portlet_payment_suggest_early_payment = Vorauszahlung K\u00E4ufer
add_sso_role_mapping = Rollenzuordnung hinzuf\u00FCgen
WKF_NA = Wkf nicht zutreffend
logistic_extension_required = Das Erweiterungszeichen ist erforderlich, da die automatische Generierung von SSCC aktiviert ist.
accessTokenV1_password = Passwort
OK_STYLE = status_green
error_saving_company = Fehler beim Speichern des Kunden.
page_display_option = Nicht im Men\u00FC anzeigen
customer_technical_users = Technische Benutzer des Kunden
sap_configuration_repository_optimization = Optimierung des Repository-Roundtrips
self_register = Selbstregistrierung
AAP_IN_PROGRESS_AN_STYLE = status_yellow
error_invalid_row_length = [{0},{1}] Anzahl der erwarteten ({2}) und gefundenen ({3}) fehlerhaften Felder.
localisation = Portal-Standort
sap_configuration_user = Benutzer
reconciliation_empty_order_number = Abgleich nicht m\u00F6glich: Bestell-Nr. fehlt.
error_invalid_number = {0} : Die Eingabe ist keine Zahl: {1}
bank_accounts = Bankkonten
quantityValue_validator = Der Wert der Menge ist ung\u00FCltig
ap_automation_tab = AP-Automation
info_partner_role_saved = Partnerrolle gespeichert
exception_duplicate_email = Diese E-Mail-Adresse wurde bereits erstellt
freetext_details = Freie Felder\u00A0/ Details
error_input_content_not_valid = Der eingegebene Inhalt ist ung\u00FCltig: {0}
error_switching_user_no_idp = F\u00FCr eine Identit\u00E4ts\u00FCbernahme muss der entsprechende IDP-Alias in der Hauptgruppe des Benutzers konfiguriert sein.
CUSTOMERS = KUNDENPORTALE
error_creating_user_keycloak = Login existiert bereits in KC. Es kann nicht erstellt werden.
sap_configuration_client = Kunde
VALIDATED = Validiert
authentication_clientId = ClientId
Accounting_Inactive = Inaktiv
NO_ROUTE_STYLE = status_yellow
REFERENTIAL_ERROR = Verzeichnisfehler
REMOVED_STYLE = Standard
menu_rights_perimeters = Bereiche
DEPRECATED = VERALTET
ERROR_STYLE_STAGE = status_red
channel_auth_userSearchBase = Benutzersuchbasis
error_export_empty_portlet = Sie k\u00F6nnen kein leeres Portal exportieren;
portlet_invoice_archive_delete = RECHNUNGSARCHIV: Entfernen
AAP_COMPLETED_STYLE = status_green
bank_account_delete_confirm = M\u00F6chten Sie die IBAN f\u00FCr die W\u00E4hrung wirklich entfernen?
APPROVED_STYLE = Info
portlet_invoice_modify = RECHNUNG: \u00C4ndern
period_specific_to_february = Spezifische Periodizit\u00E4t f\u00FCr Februar
arn = ARN der IAM-Rolle
angular_templates = Angular-Vorlagen
AAP_MISSING_AN_REF = J\u00E4hrl. Verrechn.\u00A0\u2013 Fehl. Ref.
menu_ordering_party = Auftraggeber
add_button_message_sequence = Hinzuf\u00FCgen
xPath = xPath
accessTokenV1_token = Token
sap_configuration_expiration_time = Ablaufzeit
client_type_CUSTOMER = Kunde
BEING_PROCESSED_STYLE = status_orange
portlet_invoice_archive_extend_archiving_duration = RECHNUNGSARCHIV: Archivierungsdauer verl\u00E4ngern
PARTIALLY_SHIPPED_ICON = fa fa-paper-plane
sso_role_mapping = Rollenzuordnung
canNotSaveFilter = Sie k\u00F6nnen diesen Filter nicht speichern, weil der Speicherort voll ist
portlet_collection = Sammlung
AAP_EXPORTED_TIMELINE = Kontierung: Export der Buchungen
exception_not_existing_partner = Der Partner {0} existiert nicht
absence = Abwesenheit
run_rte = Ausf\u00FChren
accounting_posting_tab = Kontierung
error_creating_clientId = Fehler beim Erstellen der clientId {0}
portlet_reception_remove = RECADV: L\u00F6schen
factor = Factor
general_supplier_service = Generix-Lieferantenservices
AWAITING_VALIDATION = Validierung ausstehend
quicksight = QuickSight
select_legal_controls_source = Ausw\u00E4hlen
error_view_secret_key = Fehler bei der Anzeige des Geheimschl\u00FCssels
portlet_switch_user = Identit\u00E4ts\u00FCbernahme
PAID = Bezahlt
DEMAT_ERR_STYLE = status_red
logistic = Logistik
CONTROL_OK = Kontrollen OK
SMTP_ERROR = SMTP-Fehler
menu_user_partner_bank = Bankverbindungen meines Unternehmens
UNDEFINED_STYLE_STAGE = Standard
accounting_posting_referentials_supplier = Lieferanten
confirmScopeSelection = Sie werden Ihre Auswahl des Partnerbereichs verlieren
client_type_INVOICE = Partner
compliance_life_cycle_in_dispute_permissions = Strittig
TO_PAY = Zahlbar
organization_ER2 = Ich bitte Generix, keine Rechnungsdaten zu extrahieren, sondern werde alle E-Reporting-Daten separat bereitstellen.
organization_ER1 = Ich bitte Generix, die internationalen B2B-Rechnungsdaten zu extrahieren, und verpflichte mich, die Rechnungsdaten der Belege, die nicht \u00FCber die PDP liefen, in einem separaten Datenstrom bereitzustellen.
TIMEOUT_ICON = fa fa-remove fa-fw
info_technicalUser_saved = Technischer Benutzer {0} gespeichert
javax_xml_datatype_XMLGregorianCalendar = XML-Datum
error_exporting_page = Fehler beim Exportieren der Seite.
accounting_posting_management_setting = Einstellungen
ordering_number_of_partners = Anzahl der Partner
info_perimeters = W\u00E4hlen Sie hier den Namen einer Gruppe von Entit\u00E4ten aus.
menu_company_addresses = Unternehmensadressen
partner_user_roles_perimeters = Rolle und Bereich des Benutzers
jsf_url = URL JSF
NONE_STYLE = status_yellow
contact_validator = Die E-Mail-Adresse des Kontakts ist ung\u00FCltig. Mithilfe eines Kommas (,) als Trennzeichen k\u00F6nnen mehrere Adressen eingegeben werden.
menu_basket = Warenkorb
placeholder_tcp = TCP-Port, auf dem die Instanz lauscht
portlet_orderresponse_print = ORDER RESPONSE: Drucken
document_status_removed = Der Status des Dokuments {0} wurde entfernt
bank_account_iban_error_notValid = Die IBAN ist ung\u00FCltig
IN_DISPUTE_STYLE = status_yellow
period_except_february = Periodizit\u00E4t au\u00DFer Februar
TIMEOUT_STYLE = status_red
field_missing = Das Feld \"{0}\" fehlt.
client_api_id = ClientAPIId
portlet_referentiel_produits_general_net_price = Stammdaten/Produkte/Allgemein/Nettopreis
PAYMENT_RECEIVED_STYLE_STAGE = status_green
cron_expression = Cron-Ausdruck
no_message_display = Diese Nachricht nicht mehr anzeigen
READ_STYLE = Info
add_library = Bibliothek hinzuf\u00FCgen
connections = Verbindungen
filter_direction = Datenflussrichtung der Portlet
save_button_message_sequence = Speichern
permission_missing = Sie haben nicht die erforderlichen Berechtigungen, um auf diese Funktion zuzugreifen
bank_account_bic_error_notValid = Der BIC-Code besteht aus 8 oder 11 Zeichen
RCL_INTRUDER_PROD = Bericht \u2013 Unbekanntes Produkt
portlet_payment_actions_8 = ZAHLUNG: Aktion 8
portlet_payment_actions_9 = ZAHLUNG: Aktion 9
portlet_payment_actions_2 = ZAHLUNG: Aktion 2
portlet_payment_actions_3 = ZAHLUNG: Aktion 3
portlet_payment_actions_1 = ZAHLUNG: Aktion 1
use_ordering_party_configuration = Einstellungen des Auftraggebers verwenden
portlet_payment_actions_6 = ZAHLUNG: Aktion 6
portlet_payment_actions_7 = ZAHLUNG: Aktion 7
portlet_payment_actions_4 = ZAHLUNG: Aktion 4
invalid_file_special_char = Die Datei darf keine Sonderzeichen enthalten
portlet_payment_actions_5 = ZAHLUNG: Aktion 5
organization_bankAccount_date = Erstellungsdatum
error_creating_authConfig = Fehler beim Erstellen der Konfiguration f\u00FCr die Serverauthentifizierung
document_status_added = Der Status des Dokuments {0} wurde hinzugef\u00FCgt
portlet_invoice_open = RECHNUNG: \u00D6ffnen
duplicate_mapping = Diese Zuordnung existiert bereits.
RCL_NA = Bericht \u2013 nicht zutreffend
element_not_removed = Das erste Element kann nicht gel\u00F6scht werden
SYNTAX_ERR_STYLE = status_red
document_status_exists = Der Status des Dokuments {0} existiert bereits
portlet_invoice_archive_bulk_export = RECHNUNGSARCHIV: Massenexport
SIGNATURE_KO = Signatur fehlgeschlagen
info_limited_extended_scope = Geben Sie in diesen Feldern die Entit\u00E4ten an, auf die dieser Benutzer Zugriff haben soll. Sie k\u00F6nnen den Namen einer oder mehrerer Entit\u00E4ten oder den Namen einer Entit\u00E4tsgruppe eingeben.
portlet_carousel = Karussell
partners_ordering_party = Partner des Auftraggebers
SHIPPED_STYLE = status_green
portlet_standardorderlist_validate = STANDARDBESTELLLISTE: Manuell best\u00E4tigen
warn_kpi_invalid = Der KPI-Dienst kann nicht gestartet werden. Bitte versuchen Sie es sp\u00E4ter erneut oder wenden Sie sich an Ihren Administrator.
error_remove_companyConfig = L\u00F6schen der Authentifizierungskonfiguration f\u00FCr das Unternehmen fehlgeschlagen
error_saving_technicalUser = Fehler bei der Registrierung des technischen Benutzers {0}.
exception_role_has_sso = Die Rolle {0} hat SSO-Rollen-Zuordnungen.
sap_configuration_language = Sprache
organization_bankAccount_currency = W\u00E4hrung
AAP_MISSING_RCP_STYLE = status_red
portlet_invoice_diagnostic = RECHNUNG: Diagnostizieren
OPENED_STYLE_STAGE = status_green
integrationnotification = Bericht \u00FCber die Prozessintegration abrufen
portlet_invoice_import = RECHNUNG: Importieren
RCL_QTY_DISCR_STYLE = status_orange
error_removing_perimeter = Fehler beim L\u00F6schen des Bereichs ({0}).
error_token_exchange_disabled = Die \u00DCbernahmefunktion ist nicht verf\u00FCgbar.
ordering_identifier = ID
END_PROCESS_STYLE = status_green
ereporting = E-Reporting
instance_type_CUSTOMER = Kunde
SIGNATURE_OK = Signatur OK
generate_reset_link_expired = Der Link zum Zur\u00FCcksetzen des Passworts ist abgelaufen.
imenu_messaging = Nachrichtendienst
accessTokenV1_url = URL
WKF_VALIDATED = Wkf validiert
accessTokenV1_use = Verwendung
one_hour_past = weniger als eine Stunde
edit_ordering_party = Auftraggeber bearbeiten
control_ean = L\u00E4ngenpr\u00FCfung des EAN-Code-Feldes
workflow_status_unprocessed = Status, wenn kein Workflow anwendbar ist
Stage_COMPLETED = Ausgef\u00FCllt
workflow_management = Verwaltung
no_order_from_asn = Keine Bestellung aus der ausgew\u00E4hlten Vorabversandbenachrichtigung gefunden
NO_GENERATION_IF_EXISTS = Wird nicht generiert, wenn die Datei existiert
exception_export_null = Kein Export in der Datei gefunden
quick_search_loreal_order = Schnellsuche nach Marke, Artikel, Filialcode, EAN
default_test_rte = Standardtest
logistic_sscc_fieldset = SSCC-Nummer
import_role_header = Import einer Rollendatei (*.xls, *.xlsx)
info_perimeter_duplicated = Der Bereich {0} wurde dupliziert
ks_error_during_file_reading = Beim Lesen der Datei ist ein Fehler aufgetreten.
technical_user_clientId = ClientId
generate_reset_link_invalid = Ung\u00FCltiger Link zum Zur\u00FCcksetzen des Passworts. Bitte wenden Sie sich an Ihren Administrator.
READ_ICON = fa fa-eye
error_duplicate_configuration = Die Konfiguration existiert bereits
eReporting_frequency = H\u00E4ufigkeit der automatischen \u00DCbermittlung von E-Reporting an die franz\u00F6sische Regierung
doc_status_style = Stilstatus
SENT_STYLE = Info
exception_exchange_associated = {0} erlaubte verkn\u00FCpfte \u00DCbertragung(en)
error_creating_authConfig_missing_config = Die Authentifizierungskonfiguration kann erst erstellt werden, wenn die Authentifizierungsparameter der Gruppe festgelegt sind (Realm-Name, ClientAPIId, SecretAPIKey und IDP-Alias f\u00FCr Spoofing)
UPLOADED_STYLE_STAGE = status_green
info_invoice_entry_preferences_saved = Meine Rechnungspr\u00E4ferenzen aktualisieren
text_to_double_message = muss eine Dezimalzahl sein.
roles_bo = Backoffice-Rollen
self_register_ok = Die Registrierung war erfolgreich!<br/>Ihnen wurde per E-Mail ein Link zur Zur\u00FCcksetzung des Passwort gesendet.
transactions = Transaktion
clients_type_SUPPLIER = Lieferanten
api_permissions_get_invoice = Rechnungen
api_permissions_translation = Übersetzungen
filter_receiving = Empfang
ordering_description = Beschreibung
sap_configuration_password = Passwort
error_deletingPartnerWithAuthConfig = Dieser Partner ist mit der Konfiguration des Authentifizierungsservers der Umgebung verkn\u00FCpft, er kann nicht gel\u00F6scht werden, ohne zuvor die entsprechende Konfiguration im Authentifizierungsserver zu entfernen
NOT_APPROVED_B2G_STYLE_STAGE = status_red
payments = Zahlungen
instance_type_SUPPLIER = Lieferant
ACQUITTED_STYLE = status_green
angular_page = Angular-Seite
WKF_REFUSED = Wkf abgelehnt
classificationPlan = Ablageplan
EP_PERFORMED = Zahlung erfolgt
exception_not_match_companyCode = {0} entspricht keinem Unternehmenscode
tcp_port = TCP-Port
logistic_missing_sscc_message = Bitte geben Sie alle erforderlichen Informationen ein
ap_automation_aged_balance_setting = Anzeige
portlet_referentiel_carrier = Stammdaten/Spediteur
REFERENTIAL_ERROR_STYLE = status_red
user_password_confirm = Passwort best\u00E4tigen
author = Autor
info_technicalUser_removed = Technischer Benutzer {0} gel\u00F6scht
invidx_wkf_reinit_btn = Zur\u00FCcksetzen
menu_user_customer_client_env_bank = Bankverbindung meiner Kunden
self_register_ko = Bei der Registrierung Ihres Kontos ist ein Problem aufgetreten. Bitte versuchen Sie es sp\u00E4ter erneut.
warn_portlet_localization_lang_not_supported = Die Sprache {0} wird von der Umgebung nicht unterst\u00FCtzt
perimeter = Bereich
NONE_ICON = fa fa-envelope-o
site_conditions_link = Nutzungsbedingungen der Website
logistic_extension = Erweiterungszeichen
BEING_PROCESSED = In Bearbeitung
subtype = Subtyp
info_ordering_saved = Auftraggeber {0} gespeichert
REPLACE_IF_EXISTS = Generierung mit Ersatz
individual_tests_rte = Individueller Test
channel_auth_groupSearchBase = Gruppensuchbasis
menu_invoice_entry_preferences = Voreinstellungen f\u00FCr die Rechnungserfassung
page_submenu_warning = Die Verwendung eines Men\u00FCs mit 3 Ebenen kann zu Anzeigefehlern f\u00FChren. Bitte \u00FCberpr\u00FCfen Sie die Men\u00FCanzeige im Frontend.
Stage_REJECTED = Abgelehnt
control = Kontrollieren
contextual_validation_partner_e_reporting = eReporting muss ER1, ER2 oder null sein
partner_with_code_missing = Die Nummer ${Client} und/oder die Registrierungsnummer existieren nicht
bql = BQL
portlet_invoice_ocr_verify = RECHNUNG: Videocoder
MISSING_PROCESSINGWAY_STYLE = status_red
DUPLICATE_STYLE = status_yellow
existing_templates = Die folgenden Vorlagen existieren bereits in der Umgebung. Bitte w\u00E4hlen Sie diejenigen aus, die Sie mit der neuen Version aus diesem Archiv \u00FCberschreiben m\u00F6chten. Wenn Sie die vorhandenen Vorlagen nicht \u00FCberschreiben m\u00F6chten, w\u00E4hlen Sie keine Vorlage aus und best\u00E4tigen Sie den Import, um den Prozess zu importieren.
AAP_COMPLETED_AN = Verrechnung letztes Jahr
bank_account_iban = IBAN
channel_auth_groupSearchFilter = Gruppensuchfilter
library_edited_succes = Die Dokumentation wurde ge\u00E4ndert
info_portlet_localization_import = Der Import des Lokalisierungsportals ist abgeschlossen.{0} Nachricht(en) hinzugef\u00FCgt und {1} bearbeitet
menu_rights_users = Benutzer
REJECTED = Abgelehnt
partner_technical_users = Technische Kontakte
error_importing_portlet = Der JSON-Import ist nur auf einer leeren Seite m\u00F6glich.
info_notifications_saved = Gespeicherte Benutzerbenachrichtigungen
reconciliation_start = Abgleich gestartet.
EP_INELIGIBLE = Ungeeignet
EP_SUBMITTED = Antrag l\u00E4uft
users_creation = Erstellung von Benutzern/Passw\u00F6rtern (kein Link)
invalid_file_csv_type = Das Dateiformat muss CSV sein
rte_property_unknown = init.tst: Die Eigenschaft {0} ist unbekannt.
RTE = EDI-SERVICES
REJECTED_STYLE_STAGE = status_red
menu_security_technical_users = Technische Backoffice-Benutzer
add_technical_user = Technischen Benutzer erstellen
database_kpi = KPI-Datenbank
sap_configuration_sysnr = Sysnr
bank_accounts_dlg_header = Bankverbindung hinzuf\u00FCgen
error_file_too_large = Die Dateigr\u00F6\u00DFe \u00FCberschreitet die maximal zul\u00E4ssige Gr\u00F6\u00DFe von 2\u00A0GB
portlet_contact_us = Kontaktieren Sie uns
exception_perimeter_has_partners = Dem Bereich {0} ist/sind {1} Partner zugeordnet.
portlet_invoice_print = RECHNUNG: Drucken
user_empty_client = W\u00E4hlen Sie einen ${client}\u00A0...
AAP_IN_PROGRESS_AN = Verrechn. laufendes Jahr
eReporting_tab = E-Reporting
ks_error_no_certificate_found = Kein Zertifikat in der Datei gefunden.
element_checkbox_title_mandatory = Obligatorisch
info_ordering_model_saved = Gespeicherte Partnervorlage
RCL_PRICES_DISCR_STYLE = status_orange
by_date = der {0}
imenu_integration_recent_mapping = J\u00FCngste Zuordnungen
general_invoice_pdp_service = Generix-Rechnungsservice
compliance_life_cycle_subrogate_permissions = Subrogieren
imenu_portlet = Portlets
bank_account_deleting_error = Fehler beim L\u00F6schen des Bankkontos {0}
channel_auth_userSearchFilter = Benutzersuchfilter
realm_name = Realm-Name
dedicated_instance = Speziell
workflow_absences = Abwesenheiten
keycloak = Keycloak
ACCEPTED_WITH_AMENDMENT_ICON = fa fa-check-circle-o
add_icon_page = Seitensymbol hinzuf\u00FCgen
sap_configuration_pool_size = Pool-Kapazit\u00E4t
IN_SUBMISSION_STYLE = status_yellow
bank_account_iban_error_required = IBAN ist ein Pflichtfeld
save_success = Die gesamte Bibliothek wurde gespeichert
TO_PAY_STYLE = status_green
edocument = E-Dokument
SUSPENDED_STYLE_STAGE = status_yellow
workflow_checkBox_help = Falls dieses K\u00E4stchen aktiviert ist, kann der Benutzer in seinem Profil ausw\u00E4hlen, ob er bei jedem neuen Dokument benachrichtigt werden m\u00F6chte
warn_import_pt_collection = Wenn das Portal \u201ECollection\u201C-Portlets verwendet, ist eine Konfiguration dieser Portlets erforderlich
reconciliation_order_not_found = Abgleich nicht m\u00F6glich: Befehl Nr.{0} nicht gefunden.
technical_roles = Technische Rolle
customer_partner_show_users = Mit dem Kunden verbundene Benutzer
reconciliation_status_unprocessed = Status, wenn kein Abgleich anwendbar
view_secretKey = Geheimschl\u00FCssel anzeigen
portlet_orderresponse_export = ORDER RESPONSE: Exportieren
RCL_SUCCESS = Positiver Bericht
validity_date = Ablaufdatum:
ordering_messages_sequence = Nachrichtenabfolge
authentication_configurationType = Konfigurationstyp
taxOriginal = Legt die EDI-Datei als Steueroriginal fest.
action_global = Globale Aktion
organization_bankAccount_user = Erstellt von
VALIDATED_STYLE = status_orderlist_green
portlet_order_mark_as_unread = BESTELLUNG: Als ungelesen markieren
compliance_life_cycle_cdv_complete_permissions = CDV: Erg\u00E4nzen
portlet_invoice_view_attachment = RECHNUNG: Anhang anzeigen
cancel_button_message_sequence = Verlassen
logistic_serial_reference_error_format_message = Die Sequenznummer muss aus 6 bis 9 Ziffern bestehen.
error_remove_technicalUser = L\u00F6schen des technischen Benutzers fehlgeschlagen
CANCEL_STYLE = Standard
notifications = Benachrichtigungen
authentication_clientIdAngular = ClientIdAngular
portlet_order_actions_9 = BESTELLUNG: Aktion 9
portlet_order_actions_8 = BESTELLUNG: Aktion 8
element_invoice = Rechnung
menu_rights_roles = Rollen
workflow_template = Vorlage
tools = Tools
dialog_message_sequence_add = Nachrichtensequenz\u00A0\u2013 Hinzuf\u00FCgen
RESENT_STYLE = status_green
advSearch = Erweiterte Suche
connection_id = Eindeutige Identifikationsnummer
invoiceItem = InvoiceItemCodeField
accessTokenV1_getTokenBtn = Token abrufen
ACCOUNTINGPOSTING = BUCHHALTUNGSEINTRTAG
contextual_validation_partner_vat_regime = Das Mehrwertsteuersystem muss VR1, VR2, VR3, VR4 oder null sein
api_messages = Nachrichten
portlet_checklistsmanagement = Verwaltung der Kontrolllisten
ERR_XLEG_STYLE = status_red
PARTIALLY_SHIPPED_STYLE = status_yellow
warn_localizations_import_portlet_not_found = Portlet {0} wurde in der Portaldefinition nicht gefunden
agreement_version_required = Die Version der AGB ist ein Pflichtfeld
AAP_IN_PROGRESS = Laufende Verrechn.
SENDER = Sender
RESENT = Zur\u00FCckgeschickt
portlet_order_actions_1 = BESTELLUNG: Aktion 1
Preproduction = Vorproduktion
portlet_order_actions_3 = BESTELLUNG: Aktion 3
portlet_order_actions_2 = BESTELLUNG: Aktion 2
portlet_order_actions_5 = BESTELLUNG: Aktion 5
portlet_order_confirm = BESTELLUNG: Best\u00E4tigen
portlet_order_actions_4 = BESTELLUNG: Aktion 4
portlet_order_actions_7 = BESTELLUNG: Aktion 7
portlet_order_actions_6 = BESTELLUNG: Aktion 6
exception_task_properties_not_found = Der Import der Aufgabe {0} ist nicht m\u00F6glich, ein Versionsupgrade ist erforderlich
edit_user_button = Benutzer aktualisieren
self_register_bottom_msg = Nach Abschluss der Registrierung erhalten Sie eine E-Mail mit Ihrem tempor\u00E4ren Passwort. Dieses Passwort muss bei der ersten Anmeldung ge\u00E4ndert werden.
PENDING_ICON = fa fa-eur
library_deleted_success = Die Dokumentation wurde gel\u00F6scht
rte_no_init_test_file = Es wurde keine standardm\u00E4\u00DFige init.tst-Datei gefunden
rte_test_success_message = RTE-Test erfolgreich durchgef\u00FChrt
filter_indifferent = Gleichg\u00FCltig
WKF_VALIDATED_STYLE = status_green
warn_partner_missing = Der Partner wurde nicht gefunden (bitte den Parameter \u201Epcode\u201C \u00FCberpr\u00FCfen).
partner_user_send_pass_no = Nein, vorerst nicht
idp_invalid_tokenUrl_error = Die Eigenschaft \u201EToken URL\u201C ist ung\u00FCltig. Sie muss dem Muster {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/token folgen<br/>
adminClient_authorizationEnabled_disabled_error = Der Client mit clientId {0} hat die Eigenschaft 'Authorization Enabled' nicht aktiviert.
search_by_project = Suche nach einem Projekt
idp_linkingOnly_disabled_error = Die Eigenschaft \u201ENur Kontoverkn\u00FCpfung\u201C ist nicht aktiviert<br/>
idp_invalid_firstLoginFlow_error = Die Eigenschaft \u201EFirst Login Flow\u201C muss den Wert \u201E{0}\u201C haben<br/>
reminderNb = Anzahl der Tage bis zur Erinnerung
error_switching_user_no_authConfig = Die Funktion zum Benutzerwechsel ist nicht verf\u00FCgbar: Es wurde keine Authentifizierungskonfiguration hinzugef\u00FCgt.
keycloak_realm_required = Der Keycloak-Bereich ist erforderlich
double_filter_error = Es ist nicht m\u00F6glich, beide Filter gleichzeitig zu verwenden.
keycloak_client_id_required = Die Keycloak-Client-ID ist erforderlich
switchUser_enabled_info = Die erforderlichen Konfigurationen zur Aktivierung der Switch-User-Funktionalit\u00E4t wurden f\u00FCr den Client {0} im Bereich {1} hinzugef\u00FCgt.
idp_invalid_syncMode_error = Die Eigenschaft \u201ESync Mode\u201C muss den Wert \u201E{0}\u201C haben<br/>
channel_coded_message = Verschl\u00FCsselte Nachricht
idp_configurations_error = Fehler bei der IDP-Konfiguration:<br/>
error_reconfigureClientsForSwitching = Fehler beim Konfigurieren der Clients f\u00FCr den Wechsel
keycloak_client_secret_required = Das Keycloak-Client-Secret ist erforderlich
serviceAccountUser_tokenExchangePermission_error = Der Client mit der Client-ID {0} verf\u00FCgt nicht \u00FCber die erforderlichen Rechte, um zu \u00FCberpr\u00FCfen, ob die Berechtigung \u201EToken-Austausch\u201C aktiviert wurde. Stellen Sie sicher, dass die Rolle {1} in den Rollen des Dienstkontos festgelegt ist
idp_permissions_disabled_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu aktivieren, m\u00FCssen die Berechtigungen f\u00FCr den IDP mit dem Alias {0} aktiviert sein.
no_idp_configured_for_usurpation_error = Kein IDP mit dem Alias {0} ist f\u00FCr die Usurpation in Bereich {1} konfiguriert.
invalid_adminClient_error = Es gibt keinen Kunden mit der Kunden-ID {0} im Bereich {1}
CORRECT = Dematerialisiert
idp_missingConfigOnPartner_warn = Der f\u00FCr die Konfiguration der Benutzerumschaltfunktion erforderliche IDP wurde nicht zu den Authentifizierungseigenschaften des Partners {0} hinzugef\u00FCgt. Wenn Sie das Hinzuf\u00FCgen der Authentifizierungskonfiguration f\u00FCr diesen Partner best\u00E4tigen, werden die erforderlichen Konfigurationen f\u00FCr die Benutzerumschaltungsfunktion nicht einbezogen.
idp_missingConfigOnCustomer_warn = Der f\u00FCr die Konfiguration der Benutzerumschaltfunktion erforderliche IDP wurde nicht zu den Authentifizierungseigenschaften des Kunden in dieser Umgebung hinzugef\u00FCgt. Wenn Sie das Hinzuf\u00FCgen der Authentifizierungskonfiguration best\u00E4tigen, werden die erforderlichen Konfigurationen f\u00FCr die Benutzerumschaltfunktion nicht einbezogen
idp_invalid_issuerUrl_error = Die Eigenschaft \u201EIssuer\u201C ist ung\u00FCltig. Sie muss dem Muster {auth_base_url}/realms/{bo_realm} folgen<br/>
portal_general = Allgemein
UNDEFINED = Nicht elektronisch
idp_invalid_mapper_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu aktivieren, muss der auf dem IDP mit dem Alias {0} definierte Mapper \u201ESync Mode Override\u201C auf \u201Einherit\u201C, \u201EMapper Type\u201C auf \u201EHardcoded Role\u201C und \u201ERole\u201C auf \u201Eimpersonation\u201C gesetzt haben
impersonate_disabled_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu erm\u00F6glichen, m\u00FCssen die Berechtigungen f\u00FCr Benutzer im Bereich {0} aktiviert werden.
no_idp_onGroupAuthConfig = In den Authentifizierungskonfigurationen der Gruppe (Partner/Unternehmen) ist kein IDP-Alias festgelegt
no_impersonation_role_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu aktivieren, muss eine Rolle mit dem Namen \u201EIdentit\u00E4tswechsel\u201C im Bereich {0} vorhanden sein
discard = Entsorgen
duplicate_clientId_error = Es ist nicht m\u00F6glich, zwei Clients mit derselben clientId {0} zu erstellen. Bitte stellen Sie sicher, dass Sie unterschiedliche Werte verwenden.
idp_hideOnLoginPage_disabled_error = Die Eigenschaft \u201EAuf Anmeldeseite ausblenden\u201C ist nicht aktiviert<br/>
send_renewal_link = Link zur Passwortverl\u00E4ngerung senden
no_impersonationRolePolicy_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu aktivieren, muss eine rollenbasierte Richtlinie mit der Rolle \u201EIdentit\u00E4tswechsel\u201C als erforderlich markiert und der Berechtigung \u201EIdentit\u00E4tswechsel\u201C f\u00FCr Benutzer hinzugef\u00FCgt werden
serviceAccountUser_noAdminRole_error = Der Benutzer des Dienstkontos des Clients {0} verf\u00FCgt nicht \u00FCber die erforderlichen Rechte, um Aktionen auf dem Bereich {1} (andere Clients, Benutzer usw. verwalten) auszuf\u00FChren. Bitte stellen Sie sicher, dass die Rolle {2} in den Rollen des Dienstkontos festgelegt ist
idp_no_mapper_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu aktivieren, muss ein Mapper mit der Rolle \u201EIdentit\u00E4tswechsel\u201C auf dem IDP mit dem Alias {0} definiert sein.
idp_invalid_userInfoUrl_error = Die Eigenschaft \u201EUser Info URL\u201C ist ung\u00FCltig. Sie muss dem Muster {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/userinfo folgen<br/>
tokenExchange_disabled_error = Die Token-Austausch-Funktion ist auf dem Realm {0} nicht aktiviert. Um Seiten von Angular zu JSF umzuleiten und auch die Funktion zum Benutzerwechsel zu nutzen, muss diese Berechtigung aktiviert werden.
idp_invalid_clientAuthMethod_error = Die Eigenschaft \u201EClient Authentication\u201C muss den Wert \u201EClient secret sent as post\u201C haben<br/>
trigger_already_exists_warning = Der Trigger {0} ist in dieser Umgebung bereits vorhanden. Er wird bei diesem Vorgang nicht importiert.
idp_invalid_authorizationUrl_error = Die Eigenschaft \u201EAutorisierungs-URL\u201C ist ung\u00FCltig. Sie muss dem Muster {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/auth folgen<br/>
search_by_process = Suche nach einem Prozess
refresh_button = Filter zur\u00FCcksetzen
impersonationRolePolicy_error = Um die Konfiguration der Benutzerumschaltungsfunktion zu erm\u00F6glichen, muss der rollentypbezogenen Richtlinie \u201E{0}\u201C die Rolle \u201EIdentit\u00E4tswechsel\u201C zugewiesen und als erforderlich markiert werden.
portlet_demat_partner_file_daily = DematPartnerFileDaily
auth_reconfigureClientsForUsurpation = Konfigurieren Sie Clients zur Usurpation um
idp_disabled_error = Der IDP mit dem Alias {0} ist nicht aktiviert<br/>
idp_invalid_clientId_error = Die Client-ID ist ung\u00FCltig. Bitte \u00FCberpr\u00FCfen Sie die Eigenschaft auth.server.backoffice.clientId
templateUri = E-Mail-Vorlage