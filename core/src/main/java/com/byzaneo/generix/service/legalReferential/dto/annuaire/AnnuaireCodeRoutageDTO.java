package com.byzaneo.generix.service.legalReferential.dto.annuaire;



import com.byzaneo.generix.service.legalReferential.dto.enumeration.Statut;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnnuaireCodeRoutageDTO implements Serializable {

    private static final long serialVersionUID = 4240029952710935602L;

    private AnnuaireAdresseDTO adresse;
    private Statut etatAdministratif;
    private Boolean gestionEngagementJuridique;
    private String identifiantRoutage;
    private String libelleCodeRoutage;
    private String typeIdentifiantRoutage;

    public AnnuaireCodeRoutageDTO() {}

    public AnnuaireCodeRoutageDTO(
        AnnuaireAdresseDTO adresse,
        Statut etatAdministratif,
        Boolean gestionEngagementJuridique,
        String identifiantRoutage,
        String libelleCodeRoutage,
        String typeIdentifiantRoutage
    ) {
        this.adresse = adresse;
        this.etatAdministratif = etatAdministratif;
        this.gestionEngagementJuridique = gestionEngagementJuridique;
        this.identifiantRoutage = identifiantRoutage;
        this.libelleCodeRoutage = libelleCodeRoutage;
        this.typeIdentifiantRoutage = typeIdentifiantRoutage;
    }

    public AnnuaireAdresseDTO getAdresse() {
        return this.adresse;
    }

    public void setAdresse(AnnuaireAdresseDTO adresse) {
        this.adresse = adresse;
    }

    public Statut getEtatAdministratif() {
        return this.etatAdministratif;
    }

    public void setEtatAdministratif(Statut etatAdministratif) {
        this.etatAdministratif = etatAdministratif;
    }

    public Boolean getGestionEngagementJuridique() {
        return this.gestionEngagementJuridique;
    }

    public void setGestionEngagementJuridique(Boolean gestionEngagementJuridique) {
        this.gestionEngagementJuridique = gestionEngagementJuridique;
    }

    public String getIdentifiantRoutage() {
        return this.identifiantRoutage;
    }

    public void setIdentifiantRoutage(String identifiantRoutage) {
        this.identifiantRoutage = identifiantRoutage;
    }

    public String getLibelleCodeRoutage() {
        return this.libelleCodeRoutage;
    }

    public void setLibelleCodeRoutage(String libelleCodeRoutage) {
        this.libelleCodeRoutage = libelleCodeRoutage;
    }

    public String getTypeIdentifiantRoutage() {
        return this.typeIdentifiantRoutage;
    }

    public void setTypeIdentifiantRoutage(String typeIdentifiantRoutage) {
        this.typeIdentifiantRoutage = typeIdentifiantRoutage;
    }
}
