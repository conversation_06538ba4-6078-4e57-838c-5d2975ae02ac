package com.byzaneo.generix.service.legalReferential;

import com.byzaneo.angular.service.ExceptionType;
import com.byzaneo.angular.service.RestJwtException;
import com.byzaneo.commons.util.GsonHelper;
import com.byzaneo.generix.bean.legalReferential.*;
import com.byzaneo.generix.service.legalReferential.dto.AnnuaireAddressLineRequest;
import com.byzaneo.generix.service.legalReferential.dto.AnnuaireRequestMapper;
import com.byzaneo.generix.service.legalReferential.dto.AnnuaireResponseMapper;
import com.byzaneo.generix.service.legalReferential.dto.annuaire.AnnuaireAddressLineDTO;
import com.byzaneo.generix.service.legalReferential.dto.response.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpTimeoutException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import javax.ws.rs.core.UriBuilder;

@Service(LegalReferentialService.SERVICE_NAME)
public class LegalReferentialServiceImpl implements LegalReferentialService {
  protected static final Logger log = LoggerFactory.getLogger(LegalReferentialServiceImpl.class);
  private static final String TIMEOUT_MESSAGE = "Connection timed out:";
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH.mm.ss.SSS");
    private static final String ANNUAIRE_PUBLIC_BASE_PATH = "/ppf/annuaire-public";
  private static final String TYPE_IDENTIFIANT_ROUTAGE_CODE = "0224";
  @Value("${legalRef.uri:}")
  private String legalRefUri;

  @Value("${spring.security.oauth2.client.registration.legalRef.client-id:}")
  private String clientId;

  @Value("${spring.security.oauth2.client.provider.legalRef.token-uri:}")
  private String tokenUri;

  @Value("${spring.security.oauth2.client.provider.legalRef.clientSecret:}")
  private String clientSecret;

  @Value("${legalRef.timeout:30}")
  private Long timeout;
  @Value("${legalRef.retries:3}")
  private int retries;

  @Value("${legalRef.matriculePlatforme:0002}")
  private String maticulePlatform;

  private boolean enabled = true;

  private AccessTokenResponse token;
  private final ExcelUtils excelUtils;

  public LegalReferentialServiceImpl(ExcelUtils excelUtils) {
    this.excelUtils = excelUtils;
  }

  @Override
  public List<CustomAddressLineDTO> searchReferenceDetails(Map<String, String> queryParams)
          throws ConnectTimeoutException {
    return searchReferenceDetails(queryParams, new AddressLineRequestBody(50, 0)).getAddresses();
  }

  @Override
  public CustomAddressPageDTO searchReferenceDetails(Map<String, String> queryParams, AddressLineRequestBody request)
          throws ConnectTimeoutException {
    try {
            log.info("Start Get searchReferenceDetails : " + SDF.format(Calendar.getInstance().getTime()));
      CustomAddressPageDTO customAddressPageDTO = searchReferenceDetailsFromLegalRef(getToken(), queryParams, request, retries);
            log.info("End Get searchReferenceDetails : " + SDF.format(Calendar.getInstance().getTime()));
      return customAddressPageDTO;
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public SearchAnnuaireRoutingCodeResponse searchRoutingCodeBySiret(Map<String, String> queryParams, AddressLineRequestBody request)
          throws ConnectTimeoutException {
    try {
            log.info("Start Get searchReferenceDetails : " + SDF.format(Calendar.getInstance().getTime()));
      SearchAnnuaireRoutingCodeResponse searchAnnuaireRoutingCodeResponse = searchRoutingCodeBySiret(getToken(), queryParams, request, retries);
            log.info("End Get searchReferenceDetails : " + SDF.format(Calendar.getInstance().getTime()));
      return searchAnnuaireRoutingCodeResponse;
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public SearchAnnuaireSiretResponse searchSiretDetailsBySiren(Map<String, String> queryParams, AddressLineRequestBody request)
          throws ConnectTimeoutException {
    try {
            log.info("Start Get searchReferenceDetails : " + SDF.format(Calendar.getInstance().getTime()));
      SearchAnnuaireSiretResponse searchAnnuaireSiretResponse = searchSiretBySiren(getToken(), queryParams, request, retries);
            log.info("End Get searchReferenceDetails : " + SDF.format(Calendar.getInstance().getTime()));
      return searchAnnuaireSiretResponse;
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  public AddressLineExportResponseBody exportReferenceDetails(Map<String, String> queryParams, AddressLineRequestBody request,
                                                              Integer maxListExportRange)
          throws ConnectTimeoutException {
    try {
      return exportReferenceDetailsFromLegalRef(getToken(), queryParams, request, retries, maxListExportRange);
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  private SearchAnnuaireSiretResponse searchSiretBySiren(String accessToken, Map<String, String> queryParams,
                                                         AddressLineRequestBody requestBody, int retryNumber) throws HttpTimeoutException, ConnectException {
    try {
      if (requestBody.getLimit() == null || requestBody.getLimit() < 1)
        requestBody.setLimit(Integer.MAX_VALUE);
      if (requestBody.getOffset() == null || requestBody.getOffset() < 0)
        requestBody.setOffset(0);

      AnnuaireSiretRequestFiltresDTO annuaireSiretRequestFiltresDTO = AnnuaireRequestMapper.mapToAnnuaireRequest(queryParams, requestBody);
      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search Siret with requestId {}", requestId);
      HttpResponse<String> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
                    .send(HttpRequest.newBuilder(new URI(legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/siret/recherche"))
                      .POST(HttpRequest.BodyPublishers.ofString(GsonHelper.getGson().toJson(annuaireSiretRequestFiltresDTO)))
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        SearchAnnuaireSiretResponse newResponse = objectMapper.readValue(responseBody, SearchAnnuaireSiretResponse.class);
        return newResponse;
      } else if (retryNumber != 0 && statusCode == 401) {
        // we retrieve a new token in case something bad happened to it
        log.info("token expired, retry to send {}", retries + 1 - retryNumber);
        token = null;
        return searchSiretBySiren(getToken(), queryParams, requestBody, retryNumber - 1);
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return searchSiretBySiren(accessToken, queryParams, requestBody, retryNumber - 1);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return searchSiretBySiren(accessToken, queryParams, requestBody, retryNumber - 1);
      } else
        throw e;

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  private SearchAnnuaireRoutingCodeResponse searchRoutingCodeBySiret(String accessToken, Map<String, String> queryParams,
                                                                     AddressLineRequestBody requestBody, int retryNumber) throws HttpTimeoutException, ConnectException {
    try {
      if (requestBody.getLimit() == null || requestBody.getLimit() < 1)
        requestBody.setLimit(Integer.MAX_VALUE);
      if (requestBody.getOffset() == null || requestBody.getOffset() < 0)
        requestBody.setOffset(0);

      boolean isSearchBySiren = isNotBlank(queryParams.getOrDefault("siren", ""));
      if (isSearchBySiren) {
        if (isBlank(queryParams.getOrDefault("siret", ""))) {
          queryParams.put("siret", queryParams.get("siren"));
        }
        queryParams.remove("siren");
      }

      AnnuaireSiretRequestFiltresDTO annuaireSiretRequestFiltresDTO = AnnuaireRequestMapper.mapToAnnuaireRequest(queryParams, requestBody);
      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search Routing Code with requestId {}", requestId);
      HttpResponse<String> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
                    .send(HttpRequest.newBuilder(new URI(legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/code-routage/recherche"))
                      .POST(HttpRequest.BodyPublishers.ofString(GsonHelper.getGson().toJson(annuaireSiretRequestFiltresDTO)))
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (HttpStatus.valueOf(statusCode).is2xxSuccessful()) {
        ObjectMapper objectMapper = new ObjectMapper();
        SearchAnnuaireRoutingCodeResponse newResponse = objectMapper.readValue(responseBody, SearchAnnuaireRoutingCodeResponse.class);
        if (CollectionUtils.isNotEmpty(newResponse.getResultats()) && isSearchBySiren) {
          newResponse.setResultats(newResponse.getResultats().stream().filter(item -> item.getSiret().startsWith(queryParams.get("siret"))).collect(Collectors.toList()));
        }
        return newResponse;
      } else if (retryNumber != 0 && statusCode == 401) {
        // we retrieve a new token in case something bad happened to it
        log.info("token expired, retry to send {}", retries + 1 - retryNumber);
        token = null;
        return searchRoutingCodeBySiret(getToken(), queryParams, requestBody, retryNumber - 1);
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return searchRoutingCodeBySiret(accessToken, queryParams, requestBody, retryNumber - 1);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return searchRoutingCodeBySiret(accessToken, queryParams, requestBody, retryNumber - 1);
      } else
        throw e;

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  private CustomAddressPageDTO searchReferenceDetailsFromLegalRef(String accessToken, Map<String, String> queryParams,
                                                                  AddressLineRequestBody requestBody, int retryNumber) throws HttpTimeoutException, ConnectException {
    try {

      if (requestBody.getLimit() == null || requestBody.getLimit() < 1)
        requestBody.setLimit(Integer.MAX_VALUE);
      if (requestBody.getOffset() == null || requestBody.getOffset() < 0)
        requestBody.setOffset(0);

      AnnuaireAddressLineRequest annuaireRequest = AnnuaireRequestMapper.mapToAnnuaireAddressLineRequest(queryParams, requestBody);
      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search Address Line with requestId {}", requestId);

      HttpResponse<String> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
                    .send(HttpRequest.newBuilder(new URI(legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/ligne-annuaire/recherche"))
                      .POST(HttpRequest.BodyPublishers.ofString(GsonHelper.getGson().toJson(annuaireRequest)))
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200 || statusCode == 206) {
        ObjectMapper objectMapper = new ObjectMapper();
        AnnuaireAddressLineResponse newResponse = objectMapper.readValue(responseBody, AnnuaireAddressLineResponse.class);
        CustomAddressPageDTO page = AnnuaireResponseMapper.mapToCustomAddressPageDTO(newResponse);
        return page;
      } else if (retryNumber != 0 && statusCode == 401) {
        // we retrieve a new token in case something bad happened to it
        log.info("token expired, retry to send {}", retries + 1 - retryNumber);
        token = null;
        return searchReferenceDetailsFromLegalRef(getToken(), queryParams, requestBody, retryNumber - 1);
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return searchReferenceDetailsFromLegalRef(accessToken, queryParams, requestBody, retryNumber - 1);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return searchReferenceDetailsFromLegalRef(accessToken, queryParams, requestBody, retryNumber - 1);
      } else
        throw e;

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  private AddressLineExportResponseBody exportReferenceDetailsFromLegalRef(String accessToken, Map<String, String> queryParams,
                                                                           AddressLineRequestBody requestBody, int retryNumber, Integer maxListExportRange) throws HttpTimeoutException, ConnectException {
    try {
      String queryString = queryParams.entrySet()
              .stream()
              .filter(entry -> isNotBlank(entry.getValue()))
              .map(entry -> {
                try {
                  return entry.getKey() + "=" + URLEncoder.encode(entry.getValue(), "UTF-8");
                } catch (UnsupportedEncodingException e) {
                  throw new RuntimeException(e);
                }
              })
              .collect(Collectors.joining("&"));

      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search Address Line with requestId {}", requestId);

            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/ligne-annuaire/recherche?" + queryString;

      if (requestBody.getOffset() == null || requestBody.getOffset() < 0)
        requestBody.setOffset(0);

      Map<String, String> searchQueryParams = new HashMap<>();
      searchQueryParams.putAll(queryParams);
      searchQueryParams.remove("format");
      CustomAddressPageDTO customAddressPageDTO = searchReferenceDetails(searchQueryParams, requestBody);
      boolean exportLimitExceeded = customAddressPageDTO.getAddresses()
              .size() > maxListExportRange;
      if (exportLimitExceeded) {
        requestBody.setLimit(maxListExportRange);
      }

      HttpResponse<byte[]> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
              .send(HttpRequest.newBuilder(new URI(url))
                      .POST(HttpRequest.BodyPublishers.ofString(GsonHelper.getGson()
                              .toJson(requestBody)))
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofByteArray());

      int statusCode = response.statusCode();
      if (statusCode == 200) {
        String contentDisposition = response.headers()
                .firstValue("Content-Disposition")
                .orElse("query.csv");
        String fileName = contentDisposition.replaceFirst("(?i)^.*filename=\"?([^\"]+)\"?.*$", "$1");
        AddressLineExportResponseBody addressLineExportResponseBody = new AddressLineExportResponseBody();
        addressLineExportResponseBody.setFilename(fileName);
        addressLineExportResponseBody.setBytes(response.body());
        addressLineExportResponseBody.setExportLimitExceeded(exportLimitExceeded);
        return addressLineExportResponseBody;
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", internal error",
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return exportReferenceDetailsFromLegalRef(accessToken, queryParams, requestBody, retryNumber - 1, maxListExportRange);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return exportReferenceDetailsFromLegalRef(accessToken, queryParams, requestBody, retryNumber - 1, maxListExportRange);
      } else
        throw e;

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public CustomAddressLineDTO getAddressLine(String id) throws ConnectTimeoutException, RestJwtException {
    try {
            log.info("Start Get by AddressLine: " + id + " " + SDF.format(Calendar.getInstance().getTime()));
      CustomAddressLineDTO customAddressLineDTO = getAddressLine(getToken(), id, retries);
            log.info("End Get by AddressLine: " + SDF.format(Calendar.getInstance().getTime()));
      return customAddressLineDTO;
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public AnnuaireAddressLineRequestResponse createAnnuaireAddresseLine(AnnuaireAdressLineRequestBodyDto annuaireAdressLineRequestBodyDto)
          throws RestJwtException {

            log.info("Start Creating new AddressLine:" + SDF.format(Calendar.getInstance().getTime()));
      validateRequestForAdresseLine(annuaireAdressLineRequestBodyDto);
      AnnuaireAddressLineRequestResponse addressLine = createNewAddresseLine(getToken(), annuaireAdressLineRequestBodyDto, retries);
            log.info("End Get by AddressLine: " + SDF.format(Calendar.getInstance().getTime()));
      return addressLine;
    }

    @Override
    public AnnuaireAddressLineRequestResponse updateAnnuaireAddresseLine(AnnuaireAdressLinePutRequestBody annuaireAdressLinePutRequestBody,Long instanceId)
            throws RestJwtException  {
            log.info("Start Updateting new AddressLine:" + SDF.format(Calendar.getInstance().getTime()));
            AnnuaireAddressLineRequestResponse addressLine = updateAddresseLine(getToken(), instanceId,annuaireAdressLinePutRequestBody,retries);
            log.info("End Get by AddressLine: " + SDF.format(Calendar.getInstance().getTime()));
            return addressLine;
    }

    @Override
    public ResponseEntity<Void> deleteAnnuaireAddresseLine(Long instanceId)
            throws RestJwtException  {
            log.info("Start Delete AddressLine:" + SDF.format(Calendar.getInstance().getTime()));
            deleteAddresseLine(getToken(), instanceId,retries);
            log.info("End Delete AddressLine: " + SDF.format(Calendar.getInstance().getTime()));
            return  ResponseEntity.noContent().build();
    }

  @Override
  public AnnuaireCodeRoutageResponse createAnnuaireRoutingCode(AnnuaireCodeRoutageRequestDto annuaireCodeRoutageRequestDto)
          throws RestJwtException {

            log.info("Start Creating new routing Code:" + SDF.format(Calendar.getInstance().getTime()));
      validateRequestRoutingcode(annuaireCodeRoutageRequestDto.getSiret(), annuaireCodeRoutageRequestDto.getTypeIdentifiantRoutage(), annuaireCodeRoutageRequestDto.getIdentifiantRoutage());
      AnnuaireCodeRoutageResponse routingCode = createNewRoutingCode(getToken(), annuaireCodeRoutageRequestDto, retries);
            log.info("End Get by AddressLine: " + SDF.format(Calendar.getInstance().getTime()));
      return routingCode;
    }

  public void validateRequestForAdresseLine(AnnuaireAdressLineRequestBodyDto annuaireAdressLineRequestBodyDto) {
    String siren = annuaireAdressLineRequestBodyDto.getInformationAdressage().getSiren();
    if (StringUtils.isEmpty(siren)) {
      throw new RestJwtException("siren_is_mandatory.", ExceptionType.BAD_REQUEST);
    }


    String suffixeAdressage = annuaireAdressLineRequestBodyDto.getInformationAdressage().getSuffixeAdressage();
    String siret = annuaireAdressLineRequestBodyDto.getInformationAdressage().getSiret();
    if (!StringUtils.isEmpty(suffixeAdressage) && !StringUtils.isEmpty(siret)) {
      throw new RestJwtException("suffix_and_SIRET_are_mutually_exclusive.", ExceptionType.BAD_REQUEST);
    }

    if (!StringUtils.isEmpty(suffixeAdressage) && suffixeAdressage.length() > 100) {
      throw new RestJwtException("suffix_size_must_be_less_than_100_characters.", ExceptionType.BAD_REQUEST);
    }

    String identifiantRoutage = annuaireAdressLineRequestBodyDto.getInformationAdressage().getIdentifiantRoutage();
    if (!StringUtils.isEmpty(identifiantRoutage) && StringUtils.isEmpty(siret)) {
      throw new RestJwtException("routing_code_cannot_be_present_without_siret.", ExceptionType.BAD_REQUEST);
    }

    String dateDebutEffet = annuaireAdressLineRequestBodyDto.getPeriodeEffet().getDateDebutEffet();
    String dateFinEffet = annuaireAdressLineRequestBodyDto.getPeriodeEffet().getDateFinEffet();
    if (dateDebutEffet != null && dateFinEffet != null) {
      try {
        LocalDate beginDate = LocalDate.parse(dateDebutEffet);
        LocalDate endDate = LocalDate.parse(dateFinEffet);

        if (beginDate.isAfter(endDate)) {
          throw new RestJwtException("start_date_must_be_earlier_than_the_end_date.", ExceptionType.BAD_REQUEST);
        }
      } catch (DateTimeParseException e) {
        throw new RestJwtException("invalid_date_format.", ExceptionType.BAD_REQUEST);
      }
    }
  }

  public void validateRequestRoutingcode(String siret, String typeIdentifiantRoutage, String identifiantRoutage) {
    if (StringUtils.isEmpty(siret)) {
      throw new RestJwtException("SIRET_is_mandatory.", ExceptionType.BAD_REQUEST);
    }

    if (StringUtils.isEmpty(typeIdentifiantRoutage)) {
      throw new RestJwtException("TypeIdentifiantRoutage_is_mandatory.", ExceptionType.BAD_REQUEST);
    }
    if (!typeIdentifiantRoutage.equals(TYPE_IDENTIFIANT_ROUTAGE_CODE)) {
      throw new RestJwtException("TypeIdentifiantRoutage_must_be_224.", ExceptionType.BAD_REQUEST);
    }

    if (StringUtils.isEmpty(identifiantRoutage)) {
      throw new RestJwtException("IdentifiantRoutage is mandatory.", ExceptionType.BAD_REQUEST);
    }
  }

  AnnuaireCodeRoutageResponse createNewRoutingCode(String accessToken, AnnuaireCodeRoutageRequestDto annuaireCodeRoutageRequestDto,
                                                   int retryNumber) {
    try {
      if (annuaireCodeRoutageRequestDto == null) {
        return null;
      }
      HttpRequestHandler requestHandler = new HttpRequestHandler();
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/code-routage";
      annuaireCodeRoutageRequestDto.getAdresse().nettoyerNullStrings();
      return requestHandler.sendHttpRequest(
              HttpMethod.POST,
              url,
              accessToken,
              annuaireCodeRoutageRequestDto,
              AnnuaireCodeRoutageResponse.class,
              retryNumber,
              timeout,
              tokenProvider,
              (newToken, remainingRetries) -> createNewRoutingCode(newToken, annuaireCodeRoutageRequestDto, remainingRetries) // Retry logic
      );
    } catch (Exception e) {
      log.error("Unexpected error during request: {}", e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
    }
  }

  private AnnuaireAddressLineRequestResponse createNewAddresseLine(
          String accessToken,
          AnnuaireAdressLineRequestBodyDto requestBody,
          int retryNumber
  ) {
    if (requestBody == null) {
      log.warn("Request body is null");
      return null;
    }
    requestBody.getInformationAdressage().setMatriculePlateforme(maticulePlatform);
    HttpRequestHandler requestHandler = new HttpRequestHandler();

    try {
      String url = buildAnnuaireUrl(requestBody);
      return requestHandler.sendHttpRequest(
              HttpMethod.POST,
              url,
              accessToken,
              requestBody,
              AnnuaireAddressLineRequestResponse.class,
              retryNumber,
              timeout,
              tokenProvider,
              (newToken, remainingRetries) -> createNewAddresseLine(newToken, requestBody, remainingRetries)
      );
    } catch (Exception e) {
      log.error("Unexpected error during request: {}", e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
    }
  }


    private AnnuaireAddressLineRequestResponse updateAddresseLine(
            String accessToken,
            Long instanceId,
            AnnuaireAdressLinePutRequestBody requestBody,
            int retryNumber
    ) {
        if (requestBody == null) {
            log.warn("Request body is null");
            return null;
        }
        requestBody.setMatriculePlateforme(maticulePlatform);

        HttpRequestHandler requestHandler = new HttpRequestHandler();

        try {
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/ligne-annuaire/id-instance/"+instanceId;
            return requestHandler.sendHttpRequest(
                    HttpMethod.PUT,
                    url,
                    accessToken,
                    requestBody,
                    AnnuaireAddressLineRequestResponse.class,
                    retryNumber,
                    timeout,
                    tokenProvider,
                    (newToken, remainingRetries) -> updateAddresseLine(newToken,instanceId, requestBody, remainingRetries)
            );
        } catch (Exception e) {
            log.error("Unexpected error during request: {}", e.getMessage(), e);
            throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
        }
    }


    private void deleteAddresseLine(
            String accessToken,
            Long instanceId,
            int retryNumber
    ) {
        if (instanceId == null) {
            log.warn("Instance ID is null");
            throw new IllegalArgumentException("Instance ID cannot be null");
        }

        HttpRequestHandler requestHandler = new HttpRequestHandler();

        try {
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/ligne-annuaire/id-instance/" + instanceId;
            requestHandler.sendHttpRequest(
                    HttpMethod.DELETE,
                    url,
                    accessToken,
                    null,
                    Void.class,
                    retryNumber,
                    timeout,
                    tokenProvider,
                    (newToken, remainingRetries) -> {
                        deleteAddresseLine(newToken, instanceId, remainingRetries);
                        return null;
                    }
            );
        }
        catch (Exception e) {
            log.error("Unexpected error during request: {}", e.getMessage(), e);
            throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
        }
    }


  private String buildAnnuaireUrl(AnnuaireAdressLineRequestBodyDto requestBody) {
    List<String> rattachements = new ArrayList<>();
    informationAdressageDTO info = requestBody.getInformationAdressage();

    if (info.getSiren() != null) rattachements.add(info.getSiren());
    if (info.getSiret() != null) rattachements.add(info.getSiret());
    if (info.getIdentifiantRoutage() != null) {
      rattachements.add(URLEncoder.encode(info.getIdentifiantRoutage(), StandardCharsets.UTF_8).replace("+", "%20"));
    }

        return legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/ligne-annuaire?Accept-Language=fr&" +
            rattachements.stream().map(r -> "PPF-Rattachements=" + r).collect(Collectors.joining("&"));
  }

  @Override
  public SirenInformation getSiren(String id) throws ConnectTimeoutException {
    try {
            log.info("Start Get by siren: " + id + " " + SDF.format(Calendar.getInstance().getTime()));
      SirenInformation sirenInformation = getSiren(getToken(), id, retries);
            log.info("End Get by siren: " + SDF.format(Calendar.getInstance().getTime()));
      return sirenInformation;
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public AnnuaireRoutingCodeWithSirenAndSiret getRoutingCode(String idSiret, String codeRoutage) throws ConnectTimeoutException {
    try {
            log.info("Start Get by routingCode: " + idSiret + " " + " " + codeRoutage + SDF.format(Calendar.getInstance().getTime()));
      AnnuaireRoutingCodeWithSirenAndSiret routingCodeInformation = getRoutingCodeInformation(getToken(), idSiret, codeRoutage, retries);
            log.info("End Get by routingCode: " + idSiret + " " + " " + codeRoutage + SDF.format(Calendar.getInstance().getTime()));
      return routingCodeInformation;
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }


  private AnnuaireRoutingCodeWithSirenAndSiret getRoutingCodeInformation
          (String accessToken, String idsiret, String codeRoutage, int retries) {
    try {
      if (isBlank(idsiret))
        return null;
      HttpRequestHandler requestHandler = new HttpRequestHandler();

            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/code-routage/siret:"
              + URLEncoder.encode(idsiret, StandardCharsets.UTF_8.toString()) + "/code:"
              + URLEncoder.encode(codeRoutage, StandardCharsets.UTF_8.toString()).replace("+", "%20");

      return requestHandler.sendGetRequest(
              url,
              accessToken,
              AnnuaireRoutingCodeWithSirenAndSiret.class,
              retries,
              timeout,
              tokenProvider,
              (newToken, remainingRetries) -> getRoutingCodeInformation(getToken(), idsiret, codeRoutage, retries)
      );
    } catch (Exception e) {
      log.error("Unexpected error during request: {}", e.getMessage(), e);
      if (e.getMessage().equals("Routing code not found"))
        return null;
      throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
    }
  }

  @Override
  public SiretInformation getSiret(String id) throws ConnectTimeoutException {
    try {
            log.info("Start Get by siret: " + id + " " + SDF.format(Calendar.getInstance().getTime()));
      SiretInformation siretInformation = getSiret(getToken(), id, retries);
            log.info("End Get by siret: " + SDF.format(Calendar.getInstance().getTime()));
      return siretInformation;
    } catch (ConnectException | HttpTimeoutException e) {
      throw new ConnectTimeoutException(e.getMessage(), e);
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }


  private SirenInformation getSiren(String accessToken, String id, int retryNumber)
          throws ConnectException, HttpTimeoutException {
    try {
      if (isBlank(id))
        return null;
      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search Siren with requestId {}", requestId);
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/siren/code-insee:" + id;
      HttpResponse<String> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
              .send(HttpRequest.newBuilder(URI.create(url))
                      .GET()
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        AnnuaireSirenResponse sirenResponseBody = objectMapper.readValue(responseBody, objectMapper.getTypeFactory()
                .constructType(AnnuaireSirenResponse.class));
        SirenInformation siren = new SirenInformation();
        siren.setSiren(sirenResponseBody.getSiren());
        siren.setTypeEntite(sirenResponseBody.getTypeEntite().toString());
        siren.setRaisonSociale(sirenResponseBody.getRaisonSociale());

        return siren;
      } else if (statusCode == 404) {
        log.info("no siren found for " + id);
        return null;
      } else if (retryNumber != 0 && statusCode == 401) {
        // we retrieve a new token in case something bad happened to it
        log.info("token expired, retry to send {}", retries + 1 - retryNumber);
        token = null;
        return getSiren(getToken(), id, retryNumber - 1);
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return getSiren(accessToken, id, retryNumber - 1);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return getSiren(accessToken, id, retryNumber - 1);
      } else
        throw e;

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  private SiretInformation getSiret(String accessToken, String id, int retryNumber)
          throws ConnectException, HttpTimeoutException {
    try {
      if (isBlank(id))
        return null;
      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search siret with requestId {}", requestId);
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/siret/code-insee:" + id;
      HttpResponse<String> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
              .send(HttpRequest.newBuilder(URI.create(url))
                      .GET()
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        AnnuaireSiretResponse siretResponseBody = objectMapper.readValue(responseBody, objectMapper.getTypeFactory()
                .constructType(AnnuaireSiretResponse.class));
        SiretInformation siret = new SiretInformation();
        siret.setSiret(siretResponseBody.getSiret());
        siret.setCodePostal(siretResponseBody.getAdresse().getCodePostal());
        siret.setLibellePays(siretResponseBody.getAdresse().getLibellePays());
        siret.setLibelleAdresse1(siretResponseBody.getAdresse().getLigneAdresse1());
        siret.setLibelleAdresse2(siretResponseBody.getAdresse().getLigneAdresse2());
        siret.setLibelleAdresse3(siretResponseBody.getAdresse().getLigneAdresse3());
        return siret;
      } else if (statusCode == 404) {
        log.info("no siret found for " + id);
        return null;
      } else if (retryNumber != 0 && statusCode == 401) {
        // we retrieve a new token in case something bad happened to it
        log.info("token expired, retry to send {}", retries + 1 - retryNumber);
        token = null;
        return getSiret(getToken(), id, retryNumber - 1);
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return getSiret(accessToken, id, retryNumber - 1);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return getSiret(accessToken, id, retryNumber - 1);
      } else
        throw e;

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  private AnnuaireSiretResponse getSiretWithDetails(String accessToken, String id, int retryNumber) {
    try {
      if (isBlank(id))
        return null;
      HttpRequestHandler requestHandler = new HttpRequestHandler();
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/siret/code-insee:" + id;
      return requestHandler.sendGetRequest(
              url,
              accessToken,
              AnnuaireSiretResponse.class,
              retryNumber,
              timeout,
              tokenProvider,
              (newToken, remainingRetries) -> getSiretWithDetails(getToken(), id, retryNumber)
      );
    } catch (Exception e) {
      log.error("Unexpected error during request: {}", e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
    }
  }

  private CustomAddressLineDTO getAddressLine(String accessToken, String id, int retryNumber)
          throws ConnectException, HttpTimeoutException {
    try {
      if (isBlank(id))
        return null;
      String requestId = RequestIdAspect.getRequestId();
      log.info("REST request to search Address Line with requestId {}", requestId);

            String url = UriBuilder
                    .fromUri(legalRefUri)
                    .path(ANNUAIRE_PUBLIC_BASE_PATH + "/v1/ligne-annuaire/code:{id}")
                    .build(id)
                    .toString();
      HttpResponse<String> response = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(timeout))
              .build()
              .send(HttpRequest.newBuilder(URI.create(url))
                      .GET()
                      .header("Content-Type", "application/json")
                      .header("Request-Id", requestId)
                      .headers("Authorization", "Bearer " + accessToken)
                      .timeout(Duration.ofSeconds(timeout))
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        AnnuaireAddressLineDTO annuaireAddressLineDTO = objectMapper.readValue(responseBody, objectMapper.getTypeFactory()
                .constructType(AnnuaireAddressLineDTO.class));
        CustomAddressLineDTO customReference = AnnuaireResponseMapper.mapToCustomAddressLineDTO(annuaireAddressLineDTO);
        return customReference;
      } else if (statusCode == 404) {
        log.info("no address line found for id " + id);
        return null;
      } else if (retryNumber != 0 && statusCode == 401) {
        // we retrieve a new token in case something bad happened to it
        log.info("token expired, retry to send {}", retries + 1 - retryNumber);
        token = null;
        return getAddressLine(getToken(), id, retryNumber - 1);
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }
    } catch (ConnectException e) {
      if (e.getMessage()
              .startsWith(TIMEOUT_MESSAGE))
        if (retryNumber != 0) {
          log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
          return getAddressLine(accessToken, id, retryNumber - 1);
        } else throw e;
      else
        throw new RestJwtException(e.getMessage(),
                ExceptionType.INTERNAL_ERROR);

    } catch (HttpTimeoutException e) {
      if (retryNumber != 0) {
        log.info("request timed out, retry to send {}", retries + 1 - retryNumber);
        return getAddressLine(accessToken, id, retryNumber - 1);
      } else
        throw e;

    } catch (Exception | Error e) {
      log.error("Error: ", e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public String getToken() {
    try {
      if (token != null && token.getTokenExpirationDate()
              .after(new Date())) {
        log.debug("retrieving the token for legal referential");
        return token.getAccessToken();
      }

      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.append("client_id=")
              .append(clientId);
      if (token == null || token.getTokenRefreshExpirationDate()
              .before(new Date())) {
        log.debug("retrieving a new token for legal referential");
        stringBuilder
                .append("&grant_type=client_credentials")
                .append("&client_secret=")
                .append(clientSecret);
      } else {
        log.debug("retrieving a refresh token for legal referential");
        stringBuilder
                .append("&grant_type=refresh_token")
                .append("&refresh_token=")
                .append(token.getRefreshToken());
      }

      String body = stringBuilder.toString();
      HttpResponse<String> response = HttpClient.newBuilder()
              .build()
              .send(HttpRequest.newBuilder(new URI(tokenUri + "/protocol/openid-connect/token"))
                      .POST(HttpRequest.BodyPublishers.ofString(body))
                      .header("Content-Type", "application/x-www-form-urlencoded")
                      .build(), HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        Calendar calendar = Calendar.getInstance();
        AccessTokenResponse accessTokenResponse = objectMapper.readValue(responseBody, AccessTokenResponse.class);
        token = accessTokenResponse;
        calendar.add(Calendar.SECOND, token.getExpiresIn() - 60);
        token.setTokenExpirationDate(calendar.getTime());
        calendar.setTime(new Date());
        calendar.add(Calendar.SECOND, token.getRefreshExpiresIn() - 60);
        token.setTokenRefreshExpirationDate(calendar.getTime());
        return accessTokenResponse.getAccessToken();
      } else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
                ExceptionType.INTERNAL_ERROR);
      }

    } catch (Exception | Error e) {
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  Function<Integer, String> tokenProvider = retryNumber -> {
    return getToken();
  };


  @Override
  public AnnuaireCodeRoutageResponse updateAnnuaireRoutingCode(String instanceID, AnnuaireCodeRoutageRequestDto annuaireCodeRoutageRequestDto)
          throws RestJwtException {
    try {
            log.info("Start Creating new routing Code:" + SDF.format(Calendar.getInstance().getTime()));
      validateRequestRoutingcode(annuaireCodeRoutageRequestDto.getSiret(), annuaireCodeRoutageRequestDto.getTypeIdentifiantRoutage(), annuaireCodeRoutageRequestDto.getIdentifiantRoutage());
      AnnuaireCodeRoutageResponse routingCode = updateRoutingCode(getToken(), instanceID, annuaireCodeRoutageRequestDto, retries);
            log.info("End Get by AddressLine: " + SDF.format(Calendar.getInstance().getTime()));
      return routingCode;
    } catch (Exception | Error e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
              ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public List<AnnuaireCodeRoutageResponse> updateListAnnuaireRoutingCode(ListAnnuaireCodeRoutageRequestDto annuaireCodeRoutageRequestDtos) throws RestJwtException {
        log.info("Start update list routing Code:" + SDF.format(Calendar.getInstance().getTime()));
    List<AnnuaireCodeRoutageResponse> listAnnuaireCodeRoutageResponse = new ArrayList<>();
    for (AnnuaireCodeRoutageRequestDto routingCode : annuaireCodeRoutageRequestDtos.getItems()) {
      validateRequestRoutingcode(routingCode.getSiret(), routingCode.getTypeIdentifiantRoutage(), routingCode.getIdentifiantRoutage());
      listAnnuaireCodeRoutageResponse.add(updateRoutingCode(getToken(), routingCode.getInstanceId(), routingCode, retries));
    }
        log.info("End update list routing Code: " + SDF.format(Calendar.getInstance().getTime()));
    return listAnnuaireCodeRoutageResponse;
  }

  private AnnuaireCodeRoutageResponse updateRoutingCode(String accessToken, String instanceID, AnnuaireCodeRoutageRequestDto annuaireCodeRoutageRequestDto, int retryNumber) {
    try {
      if (annuaireCodeRoutageRequestDto == null) {
        return null;
      }
      HttpRequestHandler requestHandler = new HttpRequestHandler();
            String url = legalRefUri + ANNUAIRE_PUBLIC_BASE_PATH + "/v1/code-routage/code-routage/" + instanceID;
      if (annuaireCodeRoutageRequestDto.getAdresse() != null) {
        annuaireCodeRoutageRequestDto.getAdresse().nettoyerNullStrings();
      }
      return requestHandler.sendHttpRequest(
              HttpMethod.PUT,
              url,
              accessToken,
              annuaireCodeRoutageRequestDto,
              AnnuaireCodeRoutageResponse.class,
              retryNumber,
              timeout,
              tokenProvider,
              (newToken, remainingRetries) -> updateRoutingCode(newToken, instanceID, annuaireCodeRoutageRequestDto, remainingRetries) // Retry logic
      );
    } catch (Exception e) {
      log.error("Unexpected error during request: {}", e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
    }
  }

  private AnnuaireActualisationResponseDTO updateLegalRefAddressesAndCodes(String accessToken, String siren, AnnuaireActualisationRequestDTO payload, int retryNumber) {
    try {
      if (payload == null) {
        return null;
      }
      HttpRequestHandler requestHandler = new HttpRequestHandler();
      String url = legalRefUri + "/ppf/annuaire-public/v1/lignes-annuaire-et-codes-routage";
      Map<String, String> headers = null;
      if (siren != null) {
        headers = Map.of("Siren-Demandeur", siren);
      }

      return requestHandler.sendHttpRequest(
              HttpMethod.POST,
              url,
              accessToken,
              headers,
              payload,
              AnnuaireActualisationResponseDTO.class,
              retryNumber,
              timeout,
              tokenProvider,
              (newToken, remainingRetries) -> updateLegalRefAddressesAndCodes(newToken, siren, payload, remainingRetries) // Retry logic
      );
    } catch (Exception e) {
      log.error("Unexpected error during request: {}", e.getMessage(), e);
      if (e instanceof RestJwtException) {
        throw new RestJwtException(e.getMessage(), ((RestJwtException) e).getException());
      }
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }


  @Override
  public AnnuaireActualisationResponseDTO updateMultipleAddressLinesRoutingCodes(String siren, Attachment inputFile) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH.mm.ss.SSS");
    log.info("Start update multiple address lines and routing codes:" + sdf.format(Calendar.getInstance().getTime()));
    // 1. convert to payload
    AnnuaireActualisationRequestDTO payload = this.excelUtils.convertAddressLinesRoutingCodesExcelToJson(inputFile);
    // 2. call Legalref
    if (CollectionUtils.isNotEmpty(payload.getCodesRoutage()) || CollectionUtils.isNotEmpty(payload.getLignesAdressage())) {
      AnnuaireActualisationResponseDTO response = this.updateLegalRefAddressesAndCodes(getToken(), siren, payload, retries);
      log.info("End update multiple address lines and routing codes: " + sdf.format(Calendar.getInstance().getTime()));
      return response;
    }
    log.info("End update multiple address lines and routing codes: " + sdf.format(Calendar.getInstance().getTime()));
    throw new RestJwtException("legalref.validation.requestBodyStructureInvalid", ExceptionType.BAD_REQUEST);
  }

  public File downloadAddressLinesRoutingCodesCsvFileTemplate(String type) throws IOException {
    String fileName = type + "-template.csv";

    // Create file in temporary directory.
    File tmpFile = new File(FileUtils.getTempDirectory(), fileName);

    this.excelUtils.exportAsCsv(type, tmpFile);

    return tmpFile;
  }
}


