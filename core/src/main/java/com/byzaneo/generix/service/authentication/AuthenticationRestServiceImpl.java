package com.byzaneo.generix.service.authentication;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.byzaneo.angular.service.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.service.InstanceService;
import com.byzaneo.generix.util.AuthenticationRestServiceHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.bean.authentication.*;
import com.byzaneo.security.service.authentication.AuthenticationRestService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.ws.rs.core.MediaType;
import java.net.*;
import java.net.http.*;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.generix.util.AuthenticationRestServiceHelper.IDP_TOKEN_EXCHANGE_CLIENT_POLICY;
import static com.byzaneo.generix.util.AuthenticationRestServiceHelper.IMPERSONATION_ROLE;
import static com.byzaneo.generix.util.AuthenticationRestServiceHelper.REALM_ADMIN_ROLE;
import static com.byzaneo.generix.util.AuthenticationRestServiceHelper.createProtocolMappers;
import static com.byzaneo.security.service.SecurityService.PrincipalType.COMPANY;
import static com.byzaneo.security.service.SecurityService.PrincipalType.PARTNER;
import static java.lang.String.format;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.cxf.common.util.CollectionUtils.isEmpty;

/**
 * This is a service used to call the Keycloak Admin REST APIs
 */
@Service(AuthenticationRestService.SERVICE_NAME)
public class AuthenticationRestServiceImpl implements AuthenticationRestService {

  protected static final Logger log = LoggerFactory.getLogger(AuthenticationRestServiceImpl.class);

  @Value("${auth.server.baseurl:}")
  private String baseUrl;

  @Value("${auth.server.admin.baseurl:}")
  private String adminBaseUrl;

  @Value("${auth.server.BO.realm:}")
  private String adminRealm;
  @Value("${auth.server.BO.clientIdAPI:}")
  private String adminClientId;
  @Value("${auth.server.BO.secretKeyAPI:}")
  private String adminSecretKey;
  @Value("${auth.server.backoffice.clientId:}")
  private String clientIdBO;
  @Value("${auth.server.backoffice.clientSecret:}")
  private String clientSecretBO;
  @Value("${app.name:}")
  private String applicationName;
  private Map<String, AccessToken> accessTokenCache = new HashMap<>();
  private final static String ACCESS_TOKEN_ENDPOINT = "%s/realms/%s/protocol/openid-connect/token";
  private final static String CLIENTS_ENDPOINT = "%s/realms/%s/clients";
  private final static String USERS_ENDPOINT = "%s/realms/%s/users";
  private final static String AUTHZ_PERM_ENDPOINT = "%s/realms/%s/clients/%s/authz/resource-server/permission/scope/%s";
  private final static String IDP_ENDPOINT = "%s/realms/%s/identity-provider/instances/%s";
  private final static String AUTHZ_POLICY_ENDPOINT = "%s/realms/%s/clients/%s/authz/resource-server/policy";

  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private InstanceService instanceService;

  @Autowired
  @Qualifier(AuthenticationRestService.SERVICE_NAME)
  private AuthenticationRestService authenticationRestService;

  /**
   * This method puts a keycloak user in the corresponding realm (based on an AIOUser, His parent (if PARTNER without realm - tthe COMPANY)
   * and the other parameters
   *
   * @param aioUser
   * @param principal        - partner or company
   * @param generatePassword - should keycloak to sent a password change mail?
   * @param enabled          /that refers to the keycloak user
   */
  public boolean putUserToKeycloak(User aioUser, Group principal, boolean generatePassword, boolean enabled) {
    boolean kcSuccess = false;
    if (aioUser == null)
      return kcSuccess;

    UserRepresentation userRepresentation = new UserRepresentation(aioUser, enabled);
    Map<String, Object> userAttrs = new HashMap<>();
    userAttrs.put("AIO-APP-NAME", applicationName);
    if ("PARTNER".equals(principal.getDescription())) {
      userAttrs.put("AIO-CLIENT", principal.getParent()
          .getCode());
      userAttrs.put("AIO-PARTNER", principal.getCode());
    }
    else if ("COMPANY".equals(principal.getDescription())) {
      userAttrs.put("AIO-CLIENT", principal.getCode());
    }
    userRepresentation.setAttributes(userAttrs);
    Map<String, String> kcClientElements = this.determineKeycloakClientElementsFromGroup(principal);
    if (kcClientElements.isEmpty())
      return kcSuccess;
    List<UserRepresentation> existingUserOKc = authenticationRestService.getKeyCloakUsersByQuery(kcClientElements,
        "username=" + aioUser.getLogin());
    boolean isInsertNew = aioUser.getKeycloakUserId() == null && (existingUserOKc == null || existingUserOKc.isEmpty());

    if (isInsertNew) {
      // Any exception that may appear AFTER the successful of keycloak user into the realm will result
      // in skipping the insertion of the aio User into the database which is bad because next time
      // when we try to insert the new user (not inserted in the BD yet but only in Keycloak)
      // will result in a keycloak error so again, the new user can't be correctly inserted
      // So try / catch / finally -> deletes the Keycloak user in case of exception AFTER the successful insertion
      boolean badExceptionOccured = false;
      try {
        // insert in KC
        kcSuccess = this.createKeyCloakUser(userRepresentation, kcClientElements);
        if (!kcSuccess) {
          log.error("Couldn't insert the user {} in keycloak realm {}", userRepresentation.getUsername(),
              kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME));
          return false;
        }
        else {
          List<UserRepresentation> kcUsersByUsername = this.getKeyCloakUsersByQuery(kcClientElements,
              "username=" + userRepresentation.getUsername());
          if (CollectionUtils.isEmpty(kcUsersByUsername)) {
            log.error("user {} not found in keycloak realm {}", userRepresentation.getUsername(),
                kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME));
            return false;
          }
          aioUser.setKeycloakUserId(kcUsersByUsername.get(0)
              .getId());
          userRepresentation.setId(kcUsersByUsername.get(0)
              .getId());
        }
      }
      catch (Exception badException) {
        badExceptionOccured = true;
        log.warn(String.format("Can't insert user %s : %s", userRepresentation.getUsername(), badException.getMessage()), badException);
      }
      finally {
        if (badExceptionOccured) {
          List<UserRepresentation> originalUsers = getKeyCloakUsersByQuery(kcClientElements,
              "username=" + userRepresentation.getUsername());
          if (!isEmpty(originalUsers) && isNotBlank(originalUsers.get(0)
              .getId()))
            this.deleteKeyCloakUser(originalUsers.get(0)
                .getId(), kcClientElements);
          kcSuccess = false;
        }
      }
    }
    else {
      try {
        //in this case we have the user on keycloack but not in AIO
        // the ids should be configured
        if (aioUser.getKeycloakUserId() == null && existingUserOKc != null && !existingUserOKc.isEmpty()) {
          aioUser.setKeycloakUserId(existingUserOKc.get(0)
              .getId());
          userRepresentation.setId(existingUserOKc.get(0)
              .getId());
        }
        // update in KC
        kcSuccess = this.updateKeyCloakUser(userRepresentation, kcClientElements, aioUser);
      }
      catch (Exception badException) {
        log.warn(String.format("Can't update user %s : %s", userRepresentation.getUsername(), badException.getMessage()), badException);
      }
    }
    if (kcSuccess && generatePassword) {
      boolean passwordGenerated = this.sendUpdatePasswordToTheUser(userRepresentation.getId(),
          kcClientElements);
      if (!passwordGenerated)
        log.warn("Keycloak user {} created but no 'reset password' mail was sent ", userRepresentation.getUsername());
    }
    return kcSuccess;
  }

  /**
   * If COMPANY gets the info If PARTNER gets the info. If the PARTNER doesn't have it, gets from parent, which is a COMPAMY
   *
   * @param principal - the group from which we find the keycloak information (kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey)
   * @return a map with the three infos if these are found, an empty map if couldn't find
   */
  @Override
  public Map<String, String> determineKeycloakClientElementsFromGroup(Group principal) {
    Map<String, String> result = new HashMap<String, String>();

    String kcAuthClientAPIId = null;
    String kcAuthRealmName = null;
    String kcAuthSecretAPIKey = null;
    if (Objects.equals(COMPANY.toString(), principal.getDescription()) || Objects.equals(PARTNER.toString(), principal.getDescription())) {
      kcAuthClientAPIId = principal.getKcAuthClientAPIId();
      kcAuthRealmName = principal.getKcAuthRealmName();
      kcAuthSecretAPIKey = principal.getKcAuthSecretAPIKey();
    }

    else {
      // no COMPANY and no PARTNER? Not good, can't continue
      return result;
    }

    if (Objects.equals(PARTNER.toString(), principal.getDescription()) &&
        (StringUtils.isBlank(kcAuthClientAPIId) || StringUtils.isBlank(kcAuthRealmName) || StringUtils.isBlank(kcAuthSecretAPIKey))) {
      Group parent = principal.getParent(); // should be a Company
      if (Objects.equals(COMPANY.toString(), parent.getDescription())) {
        kcAuthClientAPIId = parent.getKcAuthClientAPIId();
        kcAuthRealmName = parent.getKcAuthRealmName();
        kcAuthSecretAPIKey = parent.getKcAuthSecretAPIKey();
      }
    }

    if (isNotBlank(kcAuthClientAPIId) && isNotBlank(kcAuthRealmName) &&
        isNotBlank(kcAuthSecretAPIKey)) {
      result.put(UserRepresentation.KC_AUTH_CLIENT_API_ID, kcAuthClientAPIId);
      result.put(UserRepresentation.KC_AUTH_REALM_NAME, kcAuthRealmName);
      result.put(UserRepresentation.KC_AUTH_SECRET_API_KEY, kcAuthSecretAPIKey);
    }

    return result;
  }

  /**
   * Creates a new keycloak user
   *
   * @param kcClientElements - kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey
   */
  @Override
  public boolean createKeyCloakUser(UserRepresentation requestBody, Map<String, String> kcClientElements) {
    try {
      return createUserResponse(requestBody, kcClientElements);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private boolean createUserResponse(UserRepresentation requestBody, Map<String, String> kcClientElements) throws Exception {
    String url = format(USERS_ENDPOINT, adminBaseUrl, kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME));
    String data = GsonHelper.getGson()
        .toJson(requestBody);
    this.getHttpResponseFromKCEndpoint(url, "POST", data, kcClientElements);
    return true;
  }

  /**
   * Updates an existing keycloak user
   *
   * @param kcClientElements             - kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey
   * @param aioUserThatHadntKeicloakUser if we had an AIO User (probably exiting before keycloak implementation) that hadn't an keycloak
   *                                     equivalent user we do insert in keycloak instead of update and (hopefully) the AIO User will be
   *                                     updated into the sec_user table
   */
  public boolean updateKeyCloakUser(UserRepresentation updatedUserRepresentation, Map<String, String> kcClientElements,
      User aioUserThatHadntKeicloakUser) {
    try {
      UserRepresentation originalUser = this.getKeyCloakUserById(kcClientElements, updatedUserRepresentation.getId());
      if (originalUser == null) {
        // this will be the case with existing users that worked in the old AIO authentication mechanism user/pass and not Keycloak
        // so we need to do an insert
        log.warn("Keycloak user {} doesn't exist", updatedUserRepresentation.getUsername());
        boolean created = this.createKeyCloakUser(updatedUserRepresentation, kcClientElements);
        if (created) {
          List<UserRepresentation> kcUsersByUsername = this.getKeyCloakUsersByQuery(kcClientElements,
              "username=" + updatedUserRepresentation.getUsername());
          if (CollectionUtils.isEmpty(kcUsersByUsername)) {
            log.error("user {} not found in keycloak realm {}", updatedUserRepresentation.getUsername(),
                kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME));
            created = false;
          }
          else {
            updatedUserRepresentation.setId(kcUsersByUsername.get(0)
                .getId());
            if (aioUserThatHadntKeicloakUser != null)
              aioUserThatHadntKeicloakUser.setKeycloakUserId(kcUsersByUsername.get(0)
                  .getId());
          }
        }
        return created;
      }
      originalUser.setFirstName(updatedUserRepresentation.getFirstName());
      originalUser.setLastName(updatedUserRepresentation.getLastName());
      originalUser.setEmail(updatedUserRepresentation.getEmail());
      originalUser.setRequiredActions(updatedUserRepresentation.getRequiredActions());
      originalUser.setAttributes(updatedUserRepresentation.getAttributes());

      return this.updateUserResponse(originalUser, kcClientElements);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private boolean updateUserResponse(UserRepresentation requestBody, Map<String, String> kcClientElements)
      throws Exception {
    String url = format(USERS_ENDPOINT + "/%s", adminBaseUrl, kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME),
        requestBody.getId());
    String data = GsonHelper.getGson()
        .toJson(requestBody);
    this.getHttpResponseFromKCEndpoint(url, "PUT", data, kcClientElements);
    return true;
  }

  /**
   * Sends an email to reset current password (pay attention, the oder version is deprecated)
   *
   * @param kcClientElements - kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey
   */
  @Override
  public boolean sendUpdatePasswordToTheUser(String kcId, Map<String, String> kcClientElements) {
    try {
      UserRepresentation existingUser = this.getKeyCloakUserById(kcClientElements, kcId);
      if (existingUser == null) {
        log.error("Keycloak user {} doesn't exist", kcId);
        return false;
      }
      String url = format(USERS_ENDPOINT + "/%s/execute-actions-email", adminBaseUrl,
          kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME),
          existingUser.getId());
      String data = "[\"UPDATE_PASSWORD\"]";

      this.getHttpResponseFromKCEndpoint(url, "PUT", data, kcClientElements);
      return true;

    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public Boolean checkTokenExchangePermissionEnabled() {
    try {
      String bo_user_access_token = getAccessToken(clientIdBO, clientSecretBO, adminRealm);

      HttpResponse<String> response = getHttpResponse(bo_user_access_token, adminBaseUrl + "/serverinfo",
          "GET", null);

      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        List<String> previewFeaturesList = objectMapper.convertValue(
            jsonNode.get("profileInfo")
                .get("previewFeatures"),
            new TypeReference<List<String>>() {
            }
        );
        if(!previewFeaturesList.contains("TOKEN_EXCHANGE") && !previewFeaturesList.contains("ADMIN_FINE_GRAINED_AUTHZ")){
          error("labels.tokenExchange_disabled_error", adminRealm);
          return false;
        }
        return true;
      }
      else if (statusCode == 403) {
        error("labels.serviceAccountUser_tokenExchangePermission_error", clientIdBO, REALM_ADMIN_ROLE);
        accessTokenCache.remove(adminRealm);
        return false;
      }
      else {
        log.error("Check token exchange permission enabled Failed: " + "Error code = " + statusCode + ", error message = " + responseBody);
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      accessTokenCache.remove(adminRealm);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Gets a list of zero or more keycloak users based on the web query which is something like this: username=admin-rest-user (can have
   * several sections joined by '&' )
   *
   * @param kcClientElements - kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey
   * @param webQuery         - the web query to be used
   */
  @Override
  public List<UserRepresentation> getKeyCloakUsersByQuery(Map<String, String> kcClientElements, String webQuery) {
    try {
      String url = format(USERS_ENDPOINT + "?%s", adminBaseUrl, kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME), webQuery);
      HttpResponse<String> response = getHttpResponseFromKCEndpoint(url, "GET", null, kcClientElements);
      UserRepresentation[] result = GsonHelper.getGson()
          .fromJson(response.body(), UserRepresentation[].class);
      return Arrays.asList(result);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private HttpResponse<String> getHttpResponseFromKCEndpoint(String url, String method, String requestBody,
      Map<String, String> kcClientElements) {
    try {
      HttpResponse<String> response = sendRequest(url, method, requestBody, kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME),
          kcClientElements.get(UserRepresentation.KC_AUTH_CLIENT_API_ID), kcClientElements.get(UserRepresentation.KC_AUTH_SECRET_API_KEY));
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
      }
      return response;
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Gets a keycloak user based on its ID
   *
   * @param kcClientElements - kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey
   */
  @Override
  public UserRepresentation getKeyCloakUserById(Map<String, String> kcClientElements, String id) {
    if (StringUtils.isBlank(id))
      return null;
    try {
      String url = format(USERS_ENDPOINT + "/%s", adminBaseUrl, kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME), id);
      HttpResponse<String> response = this.getHttpResponseFromKCEndpoint(url, "GET", null, kcClientElements);
      UserRepresentation result = GsonHelper.getGson()
          .fromJson(response.body(), UserRepresentation.class);
      return result;
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Deletes a keycloak user from the realm
   *
   * @param kcClientElements - kcAuthClientAPIId, kcAuthRealmName,kcAuthSecretAPIKey
   */
  @Override
  public void deleteKeyCloakUser(String kcId, Map<String, String> kcClientElements) {
    try {
      UserRepresentation originalUser = this.getKeyCloakUserById(kcClientElements, kcId);
      if (originalUser == null) {
        log.warn("Keycloak user {} doesn't exist, can't delete", kcId);
        return;
      }
      String url = format(USERS_ENDPOINT + "/%s", adminBaseUrl, kcClientElements.get(UserRepresentation.KC_AUTH_REALM_NAME), kcId);
      this.getHttpResponseFromKCEndpoint(url, "DELETE", null, kcClientElements);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Create a new client in the realm
   */
  @Override
  public boolean createBOClient(ClientRepresentation requestBody) {
    return createClient(adminRealm, adminClientId, adminSecretKey, requestBody);
  }

  @Override
  public KeycloakClient createClientWithRoles(ClientRepresentation clientRepresentation, List<RoleRepresentation> roleRepresentationList) {
    if (!createClient(adminRealm, adminClientId, adminSecretKey, clientRepresentation)) {
      // client already exists, do nothing
      return null;
    }

    String clientUuid = getClientId(adminRealm, adminClientId, clientRepresentation.getClientId(), adminSecretKey);
    String clientSecret = getClientSecretByClientUuid(adminRealm, adminClientId, adminSecretKey, clientUuid);

    try {
      for (RoleRepresentation roleRepresentation : roleRepresentationList) {
        createRoleForClientResponse(adminRealm, adminClientId, adminSecretKey, clientUuid, roleRepresentation);
      }
      Set<String> roles = roleRepresentationList.stream()
          .map(RoleRepresentation::getName)
          .collect(Collectors.toSet());

      return KeycloakClient.builder()
          .realm(adminRealm)
          .clientId(clientRepresentation.getClientId())
          .clientSecret(encrypteClientSecret(clientSecret))
          .roles(roles)
          .build();
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public boolean createRoleForClientResponse(String realm, String adminClientId, String secretKey, String clientUuid,
      RoleRepresentation requestBody) throws Exception {
    HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/roles", adminBaseUrl, realm, clientUuid), "POST",
        GsonHelper.getGson()
            .toJson(requestBody), realm, adminClientId, secretKey);
    int statusCode = response.statusCode();
    if (HttpStatus.valueOf(statusCode)
        .is2xxSuccessful())
      return true;
    else if (statusCode == 409)
      return false;
    else {
      throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public boolean createClient(String realm, String adminClientId, String secretKey, ClientRepresentation requestBody) {
    try {
      requestBody.setProtocol("openid-connect");
      requestBody.setPublicClient(false);
      requestBody.setServiceAccountsEnabled(true);

      return createClientResponse(realm, adminClientId, secretKey, requestBody);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public boolean createClientResponse(String realm, String adminClientId, String secretKey, ClientRepresentation requestBody) throws Exception {
    HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT, adminBaseUrl, realm), "POST", GsonHelper.getGson()
        .toJson(requestBody), realm, adminClientId, secretKey);
    int statusCode = response.statusCode();
    if (HttpStatus.valueOf(statusCode)
        .is2xxSuccessful())
      return true;
    else if (statusCode == 409)
      return false;
    else {
      throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Update the client
   */
  @Override
  public void updateBOClient(String clientId, ClientRepresentation requestBody) {
    updateClient(adminRealm, adminClientId, adminSecretKey, clientId, requestBody);
  }

  @Override
  public void updateClient(String realm, String adminClientId, String secretKey, String clientId, ClientRepresentation requestBody) {
    try {
      String id = getClientId(realm, adminClientId, clientId, secretKey);
      requestBody.setId(id);
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s", adminBaseUrl, realm, id), "PUT",
          GsonHelper.getGson()
              .toJson(requestBody), realm, clientId, secretKey);
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Delete the client
   */
  @Override
  public void deleteBOClient(String clientId) {
    deleteClient(adminRealm, adminClientId, adminSecretKey, clientId);
  }

  @Override
  public void deleteClient(String realm, String adminClientId, String secretKey, String clientId) {
    try {
      String id = getClientId(realm, adminClientId, clientId, secretKey);
      deleteClientResponse(realm, clientId, secretKey, id);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public void deleteClientResponse(String realm, String clientId, String secretKey, String clientUuid) throws Exception {
    HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s", adminBaseUrl, realm, clientUuid), "DELETE", null, realm,
        clientId, secretKey);
    int statusCode = response.statusCode();
    if (!HttpStatus.valueOf(statusCode)
        .is2xxSuccessful())
      throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
          ExceptionType.INTERNAL_ERROR);
  }

  /**
   * Get the client secret
   */
  @Override
  public String getBOClientSecret(String clientId) {
    return getClientSecretByClientId(adminRealm, adminClientId, adminSecretKey, clientId);
  }

  @Override
  public String getClientSecretByClientId(String realm, String adminClientId, String secretKey, String clientId) {
      String id = getClientId(realm, adminClientId, clientId, secretKey);
    return getClientSecretByClientUuid(realm, clientId, secretKey, id);
  }

  @Override
  public String getClientSecretByClientUuid(String realm, String adminClientId, String secretKey, String clientUuid) {
    try {
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/client-secret", adminBaseUrl, realm, clientUuid),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        return jsonNode.get("value")
            .asText();
      }
      else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  /**
   * Get id of client with clientId belonging to the realm
   */
  private String getClientId(String realm, String adminClientId, String targetClientId, String secretKey) {
    try {
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "?clientId=%s", adminBaseUrl, realm, targetClientId),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        JsonNode clientId = jsonNode.findValue("id");
        return clientId != null ? clientId.asText() : null;
      }
      else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private HttpResponse<String> sendRequest(String endpoint, String method, String requestBody, String realm, String clientId,
      String clientSecret)
      throws Exception {
    String accessToken = getAccessToken(clientId, clientSecret, realm);
    HttpResponse<String> response = getHttpResponse(accessToken, endpoint, method, requestBody);

    if (response.statusCode() == 401) {
      this.accessTokenCache.remove(realm);
      String refreshedAccessToken = getAccessToken(clientId, clientSecret, realm);
      response = getHttpResponse(refreshedAccessToken, endpoint, method, requestBody);
    }
    return response;
  }

  private HttpResponse<String> getHttpResponse(String accessToken, String endpoint, String method, String requestBody)
      throws Exception {
    HttpRequest.Builder requestBuilder = HttpRequest.newBuilder(new URI(endpoint))
        .header("Authorization", "Bearer " + accessToken);

    if (requestBody != null) {
      requestBuilder = requestBuilder.header("Content-Type", MediaType.APPLICATION_JSON)
          .method(method, HttpRequest.BodyPublishers.ofString(requestBody));
    }
    else {
      requestBuilder = requestBuilder.method(method, HttpRequest.BodyPublishers.noBody());
    }
    HttpRequest request = requestBuilder.build();

    return HttpClient.newBuilder()
        .build()
        .send(request, HttpResponse.BodyHandlers.ofString());
  }

  @Override
  public String getAccessToken(String clientId, String clientSecret, String realm) {
    AccessToken realmToken = accessTokenCache.get(realm);
    if (realmToken == null || Instant.now()
        .isAfter(realmToken.getAccessTokenExpiry())) {
      obtainAccessToken(clientId, clientSecret, realm);
    }
    return accessTokenCache.get(realm)
        .getToken();
  }

  private void obtainAccessToken(String clientId, String clientSecret, String realm) {
    try {
      StringBuilder body = new StringBuilder();
      body.append("client_id=")
          .append(clientId)
          .append("&client_secret=")
          .append(clientSecret)
          .append("&grant_type=client_credentials");

      HttpRequest request = HttpRequest.newBuilder(new URI(format(ACCESS_TOKEN_ENDPOINT, baseUrl, realm)))
          .POST(HttpRequest.BodyPublishers.ofString(body.toString()))
          .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED)
          .build();
      HttpResponse<String> response = HttpClient.newBuilder()
          .build()
          .send(request, HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        AccessToken accessToken = objectMapper.readValue(responseBody, AccessToken.class);
        accessToken.setAccessTokenExpiry(Instant.now()
            .plusSeconds(accessToken.getExpiresIn())
            .minusSeconds(60));
        accessTokenCache.put(realm, accessToken);
      }
      else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public String getUserAccessToken(String adminClientId, String adminSecretKey, String adminRealm, String code, String redirectUri,
      boolean isConfidentialAccess) {
    try {
      StringBuilder body = new StringBuilder();
      body.append("client_id=")
          .append(adminClientId)
          .append("&code=")
          .append(code)
          .append("&redirect_uri=")
          .append(URLEncoder.encode(redirectUri, StandardCharsets.UTF_8))
          .append("&grant_type=authorization_code");
      if (isConfidentialAccess)
        body.append("&client_secret=")
            .append(adminSecretKey);
      HttpRequest request = HttpRequest.newBuilder(new URI(format(ACCESS_TOKEN_ENDPOINT, baseUrl, adminRealm)))
          .POST(HttpRequest.BodyPublishers.ofString(body.toString()))
          .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED)
          .build();
      HttpResponse<String> response = HttpClient.newBuilder()
          .build()
          .send(request, HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        return jsonNode.get("access_token")
            .asText();
      }
      else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private String exchangeUserTokenAcrossRealmsAndClients(Map<String, String> authServerConfiguration, User switchUser) {
    StringBuilder body = new StringBuilder();
    try {

      String bo_user_access_token = getAccessToken(clientIdBO, clientSecretBO, adminRealm);

      body.append("grant_type=urn:ietf:params:oauth:grant-type:token-exchange");
      body.append("&client_id=" + authServerConfiguration.get("clientId"));
      body.append("&subject_token=" + bo_user_access_token);
      body.append("&subject_token_type=" + "urn:ietf:params:oauth:token-type:access_token");
      body.append("&audience=" + authServerConfiguration.get("clientId"));
      body.append("&requested_subject=" + switchUser.getLogin());
      body.append("&requested_token_type=" + "urn:ietf:params:oauth:token-type:access_token");
      body.append("&subject_issuer=" + authServerConfiguration.get("idpAlias"));

      HttpRequest request = HttpRequest.newBuilder(
              new URI(format(ACCESS_TOKEN_ENDPOINT, baseUrl, authServerConfiguration.get("realm"))))
          .POST(HttpRequest.BodyPublishers.ofString(body.toString()))
          .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED)
          .build();
      HttpResponse<String> response = HttpClient.newBuilder()
          .build()
          .send(request, HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        return jsonNode.get("access_token")
            .asText();
      }
      else {
        log.error("Token Exchange Failed: " + "Error code = " + statusCode + ", error message = " + responseBody);
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public String getTargetUserPatientToken(Map<String, String> authServerConfiguration, User switchUser) {
    StringBuilder body = new StringBuilder();
    try {

      String bo_user_exchanged_token = this.exchangeUserTokenAcrossRealmsAndClients(authServerConfiguration, switchUser);

      body.append("grant_type=urn:ietf:params:oauth:grant-type:token-exchange");
      body.append("&client_id=" + authServerConfiguration.get("clientId"));
      body.append("&subject_token=" + bo_user_exchanged_token);
      body.append("&subject_token_type=" + "urn:ietf:params:oauth:token-type:access_token");
      body.append("&requested_subject=" + switchUser.getLogin());
      body.append("&requested_token_type=" + "urn:ietf:params:oauth:token-type:access_token");

      HttpRequest request = HttpRequest.newBuilder(
              new URI(format(ACCESS_TOKEN_ENDPOINT, baseUrl, authServerConfiguration.get("realm"))))
          .POST(HttpRequest.BodyPublishers.ofString(body.toString()))
          .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED)
          .build();
      HttpResponse<String> response = HttpClient.newBuilder()
          .build()
          .send(request, HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        return jsonNode.get("access_token")
            .asText();
      }
      else {
        log.error("Token Impersonation Failed: " + "Error code = " + statusCode + ", error message = " + responseBody);
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public String exchangeUserTokenAcrossClients(Map<String, String> authServerConfiguration, String startingUserToken) {
    StringBuilder body = new StringBuilder();
    try {

      body.append("grant_type=urn:ietf:params:oauth:grant-type:token-exchange");
      body.append("&client_id=" + authServerConfiguration.get("clientId"));
      body.append("&subject_token=" + startingUserToken);
      body.append("&subject_token_type=" + "urn:ietf:params:oauth:token-type:access_token");
      body.append("&requested_token_type=" + "urn:ietf:params:oauth:token-type:access_token");

      HttpRequest request = HttpRequest.newBuilder(
              new URI(format(ACCESS_TOKEN_ENDPOINT, baseUrl, authServerConfiguration.get("realm"))))
          .POST(HttpRequest.BodyPublishers.ofString(body.toString()))
          .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED)
          .build();
      HttpResponse<String> response = HttpClient.newBuilder()
          .build()
          .send(request, HttpResponse.BodyHandlers.ofString());
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (statusCode == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        return jsonNode.get("access_token")
            .asText();
      }
      else {
        log.error(
            "exchangeUserTokenAcrossClients/Token Exchange Failed: " + "Error code = " + statusCode + ", error message = " + responseBody);
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public void deleteUser(String username) {
    try {
      deleteUser(clientIdBO, clientSecretBO, adminRealm, username);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  private void deleteUser(String realm, String adminClientId, String secretKey, String username) throws Exception {
    String id = getUserId(realm, adminClientId, secretKey, username);
    HttpResponse<String> response = sendRequest(format(USERS_ENDPOINT + "/%s", adminBaseUrl, adminRealm, id), "DELETE", null, realm,
        adminClientId, secretKey);
    int statusCode = response.statusCode();
    if (!HttpStatus.valueOf(statusCode)
        .is2xxSuccessful())
      throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(), ExceptionType.INTERNAL_ERROR);
  }

  private String getUserId(String realm, String adminClientId, String secretKey, String username) {
    try {
      HttpResponse<String> response = sendRequest(format(USERS_ENDPOINT + "?username=%s", adminBaseUrl, adminRealm, username),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        return jsonNode.findValue("id")
            .asText();
      }
      else {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody, ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public void assignClientPolicyToTokenExchangePermission(String realm, String adminClientId, String secretKey, String angClientId, String jsfClientId, String idpAlias) {
    String adminClientUuid = getClientId(realm, adminClientId, adminClientId, secretKey);
    String angClientUuid = getClientId(realm, adminClientId, angClientId, secretKey);
    //Client policy for token exchange permission on angClientId
    enableClientPermissions(realm, adminClientId, angClientUuid, secretKey);
    addClientPolicyOnTokenExchange(realm, adminClientId, adminClientUuid, secretKey, angClientId, angClientUuid, jsfClientId, idpAlias, false);
    addMappersToClient(realm, adminClientId, secretKey, angClientId);
    //Client policy for token exchange permission on IDP
    if(idpAlias != null)
      addClientPolicyOnTokenExchange(realm, adminClientId, adminClientUuid, secretKey, angClientId, angClientUuid, jsfClientId, idpAlias,true);
  }

  @Override
  public void deleteClientsAndPolicies(String realm, String adminClientId, String secretKey, String idpAlias, String angClientId, String jsfClientId)
      throws Exception {
    String angClientUuid = getClientId(realm, adminClientId, angClientId, secretKey);
    String adminUuid = getClientId(realm, adminClientId, adminClientId, secretKey);
    removeTokenExchangeAssociatedPolicy(realm, adminClientId, secretKey, angClientUuid, adminUuid, angClientId);
    if(idpAlias != null)
      removePolicyFromIDPTokenExchange(realm, adminClientId, secretKey, idpAlias, angClientUuid, adminUuid);
    List<String> targetClientsUuid = new ArrayList<>(Arrays.asList(angClientUuid, getClientId(realm, adminClientId, jsfClientId, secretKey)));
    for(String targetClientUuid : targetClientsUuid) {
      deleteClientResponse(realm, adminClientId, secretKey, targetClientUuid);
    }
  }

  private void addMappersToClient(String realm, String adminClientId, String secretKey, String angClientId) {
    try {
      List<ProtocolMapperRepresentation> protocolMappers = createProtocolMappers();
      String targetClientUuid = getClientId(realm, adminClientId, angClientId, secretKey);
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/protocol-mappers/add-models", adminBaseUrl, realm, targetClientUuid), "POST",
          GsonHelper.getGson()
              .toJson(protocolMappers), realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private void addClientPolicyOnTokenExchange(String realm, String adminClientId, String adminClientUuid, String secretKey,
      String angClientId, String angClientUuid, String jsfClientId,
      String idpAlias, boolean isIDPPermission) {
    try {
      String tokenExchangePermissionId = getTokenExchangePermissionId(realm, adminClientId, secretKey,
          isIDPPermission ? idpAlias : angClientUuid, isIDPPermission);
      if (isIDPPermission) {
        ClientPolicyRepresentation clientPolicyRepresentation = getIDPTokenExchangeClientPolicy(realm, adminClientId, adminClientUuid,
            secretKey, tokenExchangePermissionId);
        if (clientPolicyRepresentation != null) {
          clientPolicyRepresentation.addClient(angClientUuid);
          updateIDPTokenExchangeClientPolicy(realm, adminClientId, adminClientUuid, secretKey, clientPolicyRepresentation);
          return;
        }
      }
      ClientPolicyRepresentation clientPolicy = createClientPolicy(realm, adminClientId, adminClientUuid, angClientId, angClientUuid,
            jsfClientId, secretKey, isIDPPermission);
      ScopePermissionRepresentation permissionRepresentation = getTokenExchangePermissionDetails(realm, adminClientId, adminClientUuid,
          tokenExchangePermissionId, secretKey);
      permissionRepresentation.addPolicy(clientPolicy.getId());
      HttpResponse<String> response = sendRequest(format(AUTHZ_PERM_ENDPOINT, adminBaseUrl, realm, adminClientUuid, tokenExchangePermissionId),
          "PUT",
          GsonHelper.getGson()
              .toJson(permissionRepresentation), realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private ScopePermissionRepresentation getTokenExchangePermissionDetails(String realm, String adminClientId, String clientUuid, String tokenExchangePermId, String secretKey) {
    try {
      HttpResponse<String> response = sendRequest(format(AUTHZ_PERM_ENDPOINT, adminBaseUrl, realm, clientUuid, tokenExchangePermId),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        return GsonHelper.getGson()
            .fromJson(response.body(), ScopePermissionRepresentation.class);
      else
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private List<ClientPolicyRepresentation> getTokenExchangeAssociatedPolicies(String realm, String adminClientId, String clientUuid, String tokenExchangePermId, String secretKey) {
    try {
      HttpResponse<String> response = sendRequest(format(AUTHZ_PERM_ENDPOINT + "/associatedPolicies", adminBaseUrl, realm, clientUuid, tokenExchangePermId),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      return Arrays.asList(GsonHelper.getGson()
          .fromJson(response.body(), ClientPolicyRepresentation[].class));
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private ClientPolicyRepresentation createClientPolicy(String realm, String adminClientId, String adminClientUuid, String angClientId,
      String angClientUuid, String jsfClientId, String secretKey, boolean isIDPPermission) {
    try {
      String targetClientUuid = isIDPPermission ? angClientUuid : getClientId(realm, adminClientId, jsfClientId, secretKey);
      ClientPolicyRepresentation requestBody = AuthenticationRestServiceHelper.createClientPolicy(angClientId, jsfClientId,
          targetClientUuid, isIDPPermission);
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/authz/resource-server/policy/client", adminBaseUrl, realm, adminClientUuid), "POST",
          GsonHelper.getGson()
              .toJson(requestBody), realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      if (HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        return GsonHelper.getGson()
            .fromJson(response.body(), ClientPolicyRepresentation.class);
      else
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private void updateIDPTokenExchangeClientPolicy(String realm, String adminClientId, String clientUuid, String secretKey, ClientPolicyRepresentation clientPolicy) {
    try {
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/authz/resource-server/policy/client/%s", adminBaseUrl, realm, clientUuid, clientPolicy.getId()), "PUT",
          GsonHelper.getGson()
              .toJson(clientPolicy), realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private ClientPolicyRepresentation getIDPTokenExchangeClientPolicy(String realm, String adminClientId, String clientUuid, String secretKey, String tokenExchangePermissionId) {
    try {
      ClientPolicyRepresentation clientPolicy = getTokenExchangeAssociatedPolicies(realm, adminClientId, clientUuid,
          tokenExchangePermissionId, secretKey).stream()
          .filter(ap -> IDP_TOKEN_EXCHANGE_CLIENT_POLICY.equals(ap.getName()))
          .findFirst()
          .orElse(null);
      if(clientPolicy == null)
        return null;
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/authz/resource-server/policy/client/%s", adminBaseUrl, realm, clientUuid, clientPolicy.getId()),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      return GsonHelper.getGson()
          .fromJson(response.body(), ClientPolicyRepresentation.class);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private void enableClientPermissions(String realm, String adminClientId, String uuid, String secretKey) {
    try {
      String requestBody = "{\"enabled\": true}";
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "/%s/management/permissions", adminBaseUrl, realm, uuid), "PUT",
          requestBody, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private String getTokenExchangePermissionId(String realm, String clientId, String secretKey, String uuid, boolean isIDPPermission) {
    try {
      HttpResponse<String> response = sendRequest(adminBaseUrl + "/realms/" + realm + (isIDPPermission
          ? "/identity-provider/instances/"
          : "/clients/") + uuid + "/management/permissions", "GET", null, realm, clientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonNode = objectMapper.readTree(responseBody);
      return Optional.ofNullable(jsonNode).map(jn -> jn.findValue("scopePermissions"))
          .map(jn -> jn.get("token-exchange"))
          .map(JsonNode::asText)
          .orElse(null);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private void removePolicyFromIDPTokenExchange(String realm, String adminClientId, String secretKey, String idpAlias, String angClientUuid,
      String adminUuid) {
    try {
      String idpTokenExchangePermissionId = getTokenExchangePermissionId(realm, adminClientId, secretKey, idpAlias, true);
      if(idpTokenExchangePermissionId == null) {
        return;
      }
      ClientPolicyRepresentation clientPolicyRepresentation = getIDPTokenExchangeClientPolicy(realm, adminClientId, adminUuid, secretKey,
          idpTokenExchangePermissionId);
      if (clientPolicyRepresentation != null) {
        if (clientPolicyRepresentation.getClients()
            .size() == 1 && clientPolicyRepresentation.getClients()
            .stream()
            .anyMatch(client -> client.equals(angClientUuid))) {
          HttpResponse<String> response = sendRequest(format(AUTHZ_PERM_ENDPOINT, adminBaseUrl, realm, adminUuid, idpTokenExchangePermissionId),
              "DELETE", null, realm, adminClientId, secretKey);
          int statusCode = response.statusCode();
          if (!HttpStatus.valueOf(statusCode)
              .is2xxSuccessful())
            throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
                ExceptionType.INTERNAL_ERROR);
        }
        else {
          clientPolicyRepresentation.getClients()
              .remove(angClientUuid);
          updateIDPTokenExchangeClientPolicy(realm, adminClientId, adminUuid, secretKey, clientPolicyRepresentation);
        }
      }
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private void removeTokenExchangeAssociatedPolicy(String realm, String adminClientId, String secretKey, String angClientUuid, String clientUuid, String angClientId) {
    try {
      String clientTokenExchangePermissionId = getTokenExchangePermissionId(realm, adminClientId, secretKey, angClientUuid, false);
      if(clientTokenExchangePermissionId == null){
        return;
      }
      HttpResponse<String> response = sendRequest(format(AUTHZ_PERM_ENDPOINT, adminBaseUrl, realm, clientUuid, clientTokenExchangePermissionId),
          "DELETE", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + response.body(),
            ExceptionType.INTERNAL_ERROR);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }


  private String encrypteClientSecret(String clientSecret) {
    if (!StringUtils.isBlank(clientSecret)) {
      return new String(Base64Helper.encode(clientSecret.getBytes()));
    }
    return clientSecret;
  }

  @Override
  public ClientRepresentation getClientRepresentation(String realm, String adminClientId, String targetClientId, String secretKey) {
    try {
      HttpResponse<String> response = sendRequest(format(CLIENTS_ENDPOINT + "?clientId=%s", adminBaseUrl, realm, targetClientId),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
      return Arrays.stream(GsonHelper.getGson()
              .fromJson(response.body(), ClientRepresentation[].class))
          .findFirst()
          .orElse(null);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public IdentityProviderRepresentation getIdentityProvider(String realm, String adminClientId, String secretKey, String idpAlias){
    try {
      HttpResponse<String> response = sendRequest(format(IDP_ENDPOINT, adminBaseUrl, realm, idpAlias),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if(statusCode == 404)
        return null;
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
      return GsonHelper.getGson()
              .fromJson(response.body(), IdentityProviderRepresentation.class);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public List<IdentityProviderMapperRepresentation> getIdentityProviderMappers(String realm, String adminClientId, String secretKey, String idpAlias){
    try {
      HttpResponse<String> response = sendRequest(format(IDP_ENDPOINT + "/mappers", adminBaseUrl, realm, idpAlias),
          "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful()) {
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      }
      return Arrays.asList(GsonHelper.getGson()
          .fromJson(response.body(), IdentityProviderMapperRepresentation[].class));
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public boolean isIDPTokenExchangePermissionEnabled(String realm, String adminClientId, String secretKey, String idpAlias) {
    return getTokenExchangePermissionId(realm, adminClientId, secretKey, idpAlias, true) != null;
  }

  private String getRealmImpersonationRoleId(String realm, String adminClientId, String secretKey) {
    try {
      HttpResponse<String> response = sendRequest(format("%s/realms/%s/roles", adminBaseUrl, realm), "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonArray = objectMapper.readTree(responseBody);
      for (JsonNode jsonNode : jsonArray) {
        if (jsonNode.has("name") && jsonNode.get("name")
            .asText()
            .equalsIgnoreCase(IMPERSONATION_ROLE)) {
          return jsonNode.get("id")
              .asText();
        }
      }
      return null;
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private String getImpersonatePermissionId(String realm, String adminClientId, String secretKey) {
    try {
      HttpResponse<String> response = sendRequest(format("%s/realms/%s/users-management-permissions", adminBaseUrl, realm), "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonNode = objectMapper.readTree(responseBody);
      return Optional.ofNullable(jsonNode).map(jsn -> jsn.findValue("scopePermissions"))
          .map(jsn -> jsn.get("impersonate"))
          .map(JsonNode::asText)
          .orElse(null);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  private RolePolicyRepresentation getImpersonationRolePolicy(String realm, String adminClientId, String secretKey, String adminClientUuid,
      String rolePolicyId) {
    try {
      HttpResponse<String> response = sendRequest(
          format(AUTHZ_POLICY_ENDPOINT + "/role/%s", adminBaseUrl, realm, adminClientUuid, rolePolicyId), "GET", null, realm,
          adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      return GsonHelper.getGson()
          .fromJson(response.body(), RolePolicyRepresentation.class);
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public boolean checkUsersImpersonationRole(String realm, String adminClientId, String secretKey) {
    try {
      String adminClientUuid = getClientId(realm, adminClientId, adminClientId, secretKey);
      String realmImpersonationRoleId = getRealmImpersonationRoleId(realm, adminClientId, secretKey);
      if(realmImpersonationRoleId == null){
        error("labels.no_impersonation_role_error", realm);
        return false;
      }
      String impersonatePermissionId = getImpersonatePermissionId(realm, adminClientId, secretKey);
      if(impersonatePermissionId == null){
        error("labels.impersonate_disabled_error", realm);
        return false;
      }
      HttpResponse<String> response = sendRequest(
          format(AUTHZ_POLICY_ENDPOINT + "/%s/associatedPolicies", adminBaseUrl, realm, adminClientUuid,
              impersonatePermissionId), "GET", null, realm, adminClientId, secretKey);
      int statusCode = response.statusCode();
      String responseBody = response.body();
      if (!HttpStatus.valueOf(statusCode)
          .is2xxSuccessful())
        throw new RestJwtException("Error code = " + statusCode + ", error message = " + responseBody,
            ExceptionType.INTERNAL_ERROR);
      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonArray = objectMapper.readTree(responseBody);
      List<String> policyIds = new ArrayList<>();
      for (JsonNode jsonNode : jsonArray) {
        if (jsonNode.has("type") && jsonNode.get("type")
            .asText()
            .equals("role")) {
          policyIds.add(jsonNode.get("id")
              .asText());
        }
      }
      if(policyIds.isEmpty()){
        error("labels.no_impersonationRolePolicy_error");
        return false;
      }
      for (String policyId : policyIds) {
        RolePolicyRepresentation rolePolicy = getImpersonationRolePolicy(realm, adminClientId, secretKey, adminClientUuid, policyId);
        boolean isImpersonationRolePolicy = rolePolicy.getRoles()
            .stream()
            .anyMatch(rp -> rp.getRequired() && rp.getId()
                .equals(realmImpersonationRoleId));
        if(!isImpersonationRolePolicy){
          error("labels.impersonationRolePolicy_error", rolePolicy.getName());
          return false;
        }
      }
      return true;
    }
    catch (Exception e) {
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public void addClientsOnIDPTokenExchangePolicy(String realm, String adminClientId, String secretKey, String idpAlias,
      String clientId) {
    String adminClientUuid = getClientId(realm, adminClientId, adminClientId, secretKey);
    String clientUuid = getClientId(realm, adminClientId, clientId, secretKey);
    addClientPolicyOnTokenExchange(realm, adminClientId, adminClientUuid, secretKey, clientId, clientUuid, null, idpAlias, true);
  }

  @Override
  public boolean hasServiceAccountUserRealmAdminRole(String realm, String adminClientId, String secretKey) {
    try {
      DecodedJWT decodedJWT = JWT.decode(getAccessToken(adminClientId, secretKey, realm));
      LinkedHashMap<String, Object> realmManagementRoles = decodedJWT.getClaims()
          .containsKey("resource_access")
          ? (LinkedHashMap<String, Object>) decodedJWT.getClaim("resource_access")
          .asMap()
          .get("realm-management")
          : null;
      List<String> roles = realmManagementRoles != null ? (List<String>) realmManagementRoles.get("roles") : null;
      if (roles == null || !roles.contains(REALM_ADMIN_ROLE)) {
        accessTokenCache.remove(realm);
        return false;
      }
    }
    catch (Exception e){
      accessTokenCache.remove(realm);
      log.error(e.getMessage(), e);
      throw new RestJwtException(e.getMessage(),
          ExceptionType.INTERNAL_ERROR);
    }
    return true;
  }

  @Override
  public String generatePasswordResetUrl(User user, String serverName, String redirectUri, boolean isAngular) {
    Map<String, String> kcClientElements = authenticationRestService.determineKeycloakClientElementsFromGroup(user.getPrimaryGroup());
    String kcUserId = authenticationRestService.getKeyCloakUsersByQuery(kcClientElements, "username=" + user.getLogin())
        .get(0)
        .getId();

    UserRepresentation userRepresentation = getKeyCloakUserById(kcClientElements, kcUserId);
    userRepresentation.setRequiredActions(List.of("UPDATE_PASSWORD"));
    updateKeyCloakUser(userRepresentation, kcClientElements, null);

    Instance instance = instanceService.findInstanceByHostName(serverName);
    Map<String, String> authServerConfiguration = instanceService.getAuthenticationServerConfigurationByUser(instance, user, isAngular);

    return UriComponentsBuilder.fromHttpUrl(authServerConfiguration.get("url"))
        .pathSegment("realms", authServerConfiguration.get("realm"), "protocol", "openid-connect", "auth")
        .queryParam("client_id", authServerConfiguration.get("clientId"))
        .queryParam("redirect_uri", redirectUri)
        .queryParam("response_type", "code")
        .queryParam("scope", "openid")
        .toUriString();
  }

}


