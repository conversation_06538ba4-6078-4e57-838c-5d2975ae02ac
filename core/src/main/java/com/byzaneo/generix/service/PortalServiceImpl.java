package com.byzaneo.generix.service;

import com.byzaneo.commons.api.Event;
import com.byzaneo.commons.event.annotation.Subscribe;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.commons.util.SpringContextHelper;
import com.byzaneo.generix.api.TaskTransferListener;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.exception.CounterManagementException;
import com.byzaneo.generix.ui.DocumentCounterManagement;
import com.byzaneo.generix.util.CounterManagementHelper;
import com.byzaneo.portal.bean.*;
import com.byzaneo.portal.layout.*;
import com.byzaneo.portal.layout.columns.ColumnsLayout;
import com.byzaneo.portal.layout.columns.ColumnsLayout.Direction;
import com.byzaneo.portal.layout.grid.*;
import com.byzaneo.portal.util.PortalHelper;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.api.*;
import com.byzaneo.task.bean.TaskDefinition;
import com.byzaneo.task.util.*;
import com.byzaneo.xtrade.bean.CounterManagement;
import com.byzaneo.xtrade.service.CounterManagementService;
import com.google.common.collect.Iterables;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.persistence.PostLoad;
import java.io.*;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.I18NHelper.fromJsonLabelSet;
import static com.byzaneo.generix.ui.instance.PortalISHandler.PageLayout.none;
import static com.byzaneo.portal.util.PortalHelper.getPortlets;
import static com.byzaneo.portal.util.PortalHelper.removePortletsByContentId;
import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static java.lang.System.currentTimeMillis;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.UUID.randomUUID;
import static java.util.function.Predicate.isEqual;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.contains;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.apache.commons.lang3.CharEncoding.UTF_8;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Oct 11, 2012
 * @since 2.0
 */
@Service(PortalService.SERVICE_NAME)
public class PortalServiceImpl implements PortalService {
    public static final Logger log = getLogger(PortalServiceImpl.class);

    private static final String[] LINK_KEYS = { "actionId", "actionCreateId", "invoiceActionId", "asnActionId", "orderReponseActionId",
        "actionEditDraftId", "invoiceComplianceId", "invoiceControlId", "portalDetailId", "orderListId", "sourceTaskId" };

    private static final Pattern REGEX_UUID = Pattern.compile(
        "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

    // SERVICES
    @Autowired
    @Qualifier(com.byzaneo.portal.service.PortalService.SERVICE_NAME)
    private com.byzaneo.portal.service.PortalService portalService;

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private SecurityService securityService;

    @Autowired
    @Qualifier(TaskService.SERVICE_NAME)
    private TaskService taskService;

    @Autowired
    @Qualifier(CounterManagementService.SERVICE_NAME)
    private transient CounterManagementService counterManagementService;

    /*
     * -- LIFE CYCLE --
     */

    /** @see com.byzaneo.commons.service.Initializable#init() */
    @Override
    @PostConstruct
    public void init() throws Exception {
        final long start = currentTimeMillis();
        log.info("STARTING PORTAL SERVICE...");

        // - MIGRATIONS -
        // GNX-324 [PTL] Portal Service - Grid Layout support
        // @since 2.5 this.migrationGnx324();
        // GNX-612 [PTL] Restructuration des "task root libraries" du portail
        // @since 2.5 executeIssueScript(this.configService, this.portalDao, "GNX-612");

        log.info("PORTAL SERVICE STARTED in {}ms.", currentTimeMillis() - start);
    }

    /*
     * -- PORTAL MANAGEMENT --
     */

    /** @see com.byzaneo.generix.service.PortalService#getPortal(com.byzaneo.generix.bean.Instance) */
    @Override
    @Transactional
    public Portal getPortal(Instance instance, boolean loadPortletContent) {
        if (instance == null) {
            return null;
        }

        List<Portal> portals = this.portalService.getPortals(instance.getCode());
        Portal r;
        if (isEmpty(portals)) {
            r = new Portal();
            r.setOwner(instance.getCode());
            r.setName(fromJsonLabelSet(instance.getName(), instance.getConfiguration()
                .getDefaultLanguage(), JSFHelper.getLocale(), "Environment"));
            r.setDefinition(new PortalDefinition());
        }
        else {
            if (portals.size() > 1) {
                log.warn("More than one (%s) portal found for the instance '%s'",
                    portals.size(), instance.getCode());
            }
            r = portals.get(portals.size() - 1);
            if (loadPortletContent) {
                // populates portlets' content (task)
                for (Portlet portlet : getPortlets(r)) {
                    if (portlet != null && isNotBlank(portlet.getContentId())) {
                        try {
                            portlet.setContent(taskService.getTask(Long.valueOf(portlet.getContentId())));
                        }
                        catch (Exception e) {
                            log.error("Error loading portlet task: " + portlet, e);
                        }
                    }
                }
            }
        }

        return r;
    }

    /** @see com.byzaneo.generix.service.PortalService#export(Page, String...) */
    @Override
    @Transactional(readOnly = true)
    public InputStream export(Page page, String... serviceNames) {
        PageExport export = new PageExport();
        export.setPage(page);

        Map<String, InstanceInfoService> services = SpringContextHelper.getContext()
            .getBeansOfType(InstanceInfoService.class);
        consumeServicesByName(services, iis -> export(export), serviceNames);
        try {
            String json = getGson().toJson(export);
            return new ByteArrayInputStream(json.getBytes(UTF_8));
        }
        catch (Exception e) {
            log.error("Error during JSon export of %s : %s ", page.getName(), e);
            throw new ServiceException("labels.exception_json_export_error", e.getMessage());
        }
    }

    /**
     * Apply a {@code Consumer} to each service named as specified.
     *
     * @param services services mapped by their names.
     * @param consumer consumer to apply to filtered services.
     * @param serviceNames names of the services to filter. <strong>If empty, ALL services are consumed.</strong>
     */
    private <T> void consumeServicesByName(Map<String, T> services, Consumer<T> consumer, String... serviceNames) {
        services.entrySet()
            .stream()
            .filter(entry -> isEmpty(serviceNames) || contains(serviceNames, entry.getKey()))
            .map(Entry::getValue)
            .forEach(consumer);
    }

    @Override
    @Transactional
    public Portal getPortal(Instance instance) {
        return getPortal(instance, false);
    }

    /** @see com.byzaneo.generix.service.PortalService#save(com.byzaneo.portal.bean.Portal) */
    @Override
    @Transactional
    public Portal save(Portal portal) {
        if (portal == null) {
            return null;
        }
        // checks all portlets get content
        final List<Portlet> portlets = getPortlets(portal);
        for (Portlet portlet : portlets) {
            if (portlet == null || isBlank(portlet.getContentId())) {
                throw new TaskException("Task not configured in portlet: %s", portlet);
            }
        }
        return this.portalService.save(portal);
    }

    /** @see com.byzaneo.generix.service.PortalService#removePortal(Portal) */
    @Override
    @Transactional
    public void removePortal(Portal portal) {
        this.portalService.remove(portal);
    }

    /*
     * -- PAGES --
     */

    @Override
    @Transactional(readOnly = true)
    public List<Page> getGrantedPages(Portal portal, User u, boolean forMenuItem) {
        if (portal.getDefinition() == null || isEmpty(portal.getDefinition()
            .getPages())) {
            return emptyList();
        }

        // @since GNX-660
        if(forMenuItem) {
            return portal.getDefinition()
                .getPages()
                .stream()
                .map(p -> getGrantedPage(p, u, forMenuItem))
                .filter(Objects::nonNull)
                .collect(toList());
        }
        else {
            return portal.getDefinition()
                .getPages()
                .stream()
                .map(p -> getGrantedPage(p, u, forMenuItem))
                .filter(Objects::nonNull)
                .filter(p -> !p.isToBeDisplayedOption())
                .collect(toList());
        }
    }

    /**
     * Get granted page with granted children
     */
    private Page getGrantedPage(Page page, User u, boolean forMenuItem) {

        List<Page> grantedPages = this.securityService.getGrantedResources(u, Arrays.asList(page));
        if (grantedPages.isEmpty()) {
            return null;
        }
        Page result = grantedPages.get(0);
        if (!page.getChildren()
            .isEmpty()) {
            List<Page> children;
            if (forMenuItem) {
                children = result.getChildren()
                    .stream()
                    .map(p -> getGrantedPage(p, u, forMenuItem))
                    .filter(Objects::nonNull)
                    .collect(toList());
            }
            else {
                children = result.getChildren()
                    .stream()
                    .map(p -> getGrantedPage(p, u, forMenuItem))
                    .filter(Objects::nonNull)
                    .filter(p -> !p.isToBeDisplayedOption())
                    .collect(toList());
            }
            result.setChildren(children);

        }
        return result;
    }

    /** @see com.byzaneo.generix.service.PortalService#clear(Portal, com.byzaneo.portal.bean.Page) */
    @Override
    @Transactional
    public void clear(Portal portal, Page page) {
        if (page == null || CollectionUtils.isEmpty(page.getLayouts())) {
            return;
        }

        page.getLayouts()
            .forEach(l ->
            {
                l.getPortlets()
                    .forEach(this::removePortletContent);
            });
        page.getLayouts()
            .clear();
        page.getLayouts()
            .add(new EmptyLayout());
        this.portalService.save(portal);
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#removePage(Portal, Page, Page)
     */
    @Override
    @Transactional
    public Portal removePage(Portal portal, Page parentPage, Page page) {
        if (portal == null || page == null) {
            return portal;
        }

        if (parentPage == null) {
            portal.getDefinition()
                .getPages()
                .remove(page);
        }
        else {
            parentPage.getChildren()
                .remove(page);
        }

        return this.portalService.save(portal);
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#removePageContent(Page)
     */
    @Override
    @Transactional
    public void removePageContent(Page page, String owner) {
        // Cascading
        page.getChildren()
            .forEach(p -> this.removePageContent(p, owner));

        // delete mini-counters from FO if configured for this page
        CounterManagement cm = counterManagementService.findByPageId(owner, page.getId());
        if (cm != null) {
            counterManagementService.removeCounterManagement(cm.getId());
        }

        page.getLayouts()
            .forEach(l -> l.getPortlets()
                .forEach(this::removePortlet));
    }

    /*
     * -- LAYOUT --
     */

    /* -- GRID -- */

    /** @see com.byzaneo.generix.service.PortalService#gridAddRow(com.byzaneo.portal.layout.grid.GridLayout, int, boolean) */
    @Override
    @Deprecated
    public void gridAddRow(GridLayout layout, int rowIndex, boolean above) {
        if (layout != null) {
            layout.addRow(rowIndex, above);
        }
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#gridRemoveRow(com.byzaneo.portal.bean.Portal, com.byzaneo.portal.layout.grid.GridLayout,
     *      int)
     */
    @Override
    @Transactional
    @Deprecated
    public Row gridRemoveRow(Portal portal, GridLayout layout, int rowIndex) {
        if (layout == null) {
            return null;
        }
        final Row row = layout.removeRow(rowIndex);
        if (row == null) {
            return null;
        }
        boolean save = row.getColumns()
            .stream()
            .map(Column::getPortlet)
            .map(this::removePortletContent)
            .reduce(Boolean::logicalOr)
            .orElse(false);
        if (save) {
            this.portalService.save(portal);
        }
        return row;
    }

    /** @see com.byzaneo.generix.service.PortalService#gridAddColumn(com.byzaneo.portal.layout.grid.GridLayout, int, int, boolean) */
    @Override
    @Deprecated
    public void gridAddColumn(GridLayout layout, int rowIndex, int colIndex, boolean left) {
        if (layout != null) {
            layout.addColumn(rowIndex, colIndex, left);
        }
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#gridRemoveColumn(com.byzaneo.portal.bean.Portal,
     *      com.byzaneo.portal.layout.grid.GridLayout, int, int)
     */
    @Override
    @Transactional
    @Deprecated
    public Column gridRemoveColumn(Portal portal, GridLayout layout, int rowIndex, int colIndex) {
        if (layout == null) {
            return null;
        }
        final Column col = layout.removeColumn(rowIndex, colIndex);
        if (col != null) {
            this.removePortletContent(portal, col.getPortlet());
        }
        return col;
    }

    /* -- COLUMNS -- */

    /**
     * @see com.byzaneo.generix.service.PortalService#columnsAddPortlet(com.byzaneo.portal.layout.columns.ColumnsLayout, int, int, boolean)
     */
    @Override
    public void columnsAddPortlet(ColumnsLayout layout, int colIndex, int rowIndex, boolean above) {
        if (layout != null) {
            layout.addPortlet(colIndex, rowIndex, above);
        }
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#columnsRemovePortlet(com.byzaneo.portal.bean.Portal,
     *      com.byzaneo.portal.layout.columns.ColumnsLayout, int, int)
     */
    @Override
    @Transactional
    public void columnsRemovePortlet(Portal portal, ColumnsLayout layout, int colIndex, int rowIndex) {
        if (layout == null) {
            return;
        }
        this.removePortletContent(portal, layout.removePortlet(colIndex, rowIndex));
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#columnsMovePortlet(com.byzaneo.portal.layout.columns.ColumnsLayout, int, int,
     *      com.byzaneo.portal.layout.columns.ColumnsLayout.Direction)
     */
    @Override
    public void columnsMovePortlet(ColumnsLayout layout, int colIndex, int rowIndex, Direction direction) {
        if (layout != null) {
            layout.movePortlet(rowIndex, colIndex, direction);
        }
    }

    /*
     * -- PORTLETS --
     */

    /** @see com.byzaneo.generix.service.PortalService#getPortlet(com.byzaneo.generix.bean.Instance, java.lang.String) */
    @Override
    @Transactional(readOnly = true)
    public Portlet getPortlet(Instance instance, String contentId) {
        final List<Portlet> portlets = getPortlets(getPortal(instance, true));
        return portlets.stream()
            .filter(portlet -> portlet.getContentId() != null && portlet.getContentId()
                .equals(contentId))
            .findFirst()
            .orElse(null);
    }

    /** @see com.byzaneo.generix.service.PortalService#savePortletContent(Portal, com.byzaneo.portal.bean.Portlet, java.lang.Object) */
    @Override
    @Transactional
    public boolean savePortletContent(Portal portal, Portlet portlet, Object task) {
        if (task == null || portlet == null) {
            return false;
        }
        final TaskDefinition def = getDefinition(this.taskService.save(task));
        portlet.setContent(task);
        portlet.setContentId(def.getId()
            .toString());
        portlet.setContentType(def.getType());
        portlet.setMode("view");
        portlet.setName(def.getTitle());
        this.portalService.save(portal);
        return true;
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#removePortletContent(com.byzaneo.portal.bean.Portal, com.byzaneo.portal.bean.Portlet)
     * @see com.byzaneo.generix.service.PortalService#savePortletContent(Portal, Portlet, Object) where contentId is set to be
     *      TaskDefinition.getId().toString()
     */
    @Override
    @Transactional
    public boolean removePortletContent(Portal portal, Portlet portlet) {
        checkIfDocumentCounterIsUsed(portlet);
        if (this.removePortletContent(portlet)) {
            this.portalService.save(portal);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Portal changeLayoutByIndex(Portal portal, Page page, Layout newLayout, int layoutIndex) {
        if (portal == null || page == null || newLayout == null) {
            return portal;
        }

        final Collection<Portlet> portlets = newLayout.addPortlets(page.getLayouts()
            .get(layoutIndex)
            .getPortlets());

        // sets page new layout
        page.getLayouts()
            .set(layoutIndex, newLayout);

        // no portlet to migrate
        if (isEmpty(portlets)) {
            return portal;
        }
        // migrates portlet
        boolean save = portlets.stream()
            .map(this::removePortletContent)
            .reduce(Boolean::logicalOr)
            .orElse(false);
        return save ? this.portalService.save(portal) : portal;
    }

    @Override
    @Transactional
    public Portal addLayoutToPage(Portal portal, Page page, Layout newLayout) {
        if (portal == null || page == null || newLayout == null) {
            return portal;
        }
        if (page.getLayouts()
            .size() == 1 && page.getLayouts()
            .get(0)
            .equals(none)) {
            page.getLayouts()
                .set(0, newLayout);
        }
        else
            page.getLayouts()
                .add(newLayout);

        return portal;
    }

    /**
     * @see com.byzaneo.generix.service.PortalService#savePortletContent(Portal, Portlet, Object) where contentId is set to be
     *      TaskDefinition.getId().toString()
     */
    private boolean removePortlet(Portlet portlet) {
        String contentId;
        if (portlet == null || (contentId = portlet.getContentId()) == null) {
            return false;
        }
        checkIfDocumentCounterIsUsed(portlet);
        // removes content & portlet
        this.taskService.remove(portlet.getContent());
        this.taskService.remove(this.taskService.getTask(Long.valueOf(contentId)));
        // reset content
        resetPortletContent(portlet);
        return true;
    }

    private boolean removePortletContent(Portlet portlet) {
        String contentId;
        if (portlet == null || (contentId = portlet.getContentId()) == null) {
            return false;
        }
        // removes content
        if (portlet.getContent() != null) {
            this.taskService.remove(portlet.getContent());
        }
        else {
            this.taskService.remove(this.taskService.getTask(Long.valueOf(contentId)));
        }
        // reset content
        resetPortletContent(portlet);
        return true;
    }

    private void resetPortletContent(Portlet portlet) {
        portlet.setContent(null);
        portlet.setContentId(null);
        portlet.setContentType(null);
        portlet.setMode(null);
        portlet.setName(null);
    }

    /*
     * -- INFO --
     */

    @Override
    public String getName() {
        return "Portal";
    }

    @Override
    public void export(InstanceExport export) {
        log.info("Start Export Portal");
        final Portal portal = this.getPortal(export.getInstance(), true);
        final List<Portlet> portlets = getPortlets(portal);
        final List<TaskDefinition> taskDefs = portlets.stream()
            .map(Portlet::getContent)
            .map(TaskHelper::getDefinition)
            .collect(toList());
        export.getTasksByServiceName()
            .put(SERVICE_NAME, taskDefs);

        Gson GSON = PortalHelper.GSON;
        export.getJsonByServiceName()
            .put(SERVICE_NAME, GSON.toJson(portal, Portal.class));
        log.info("End Export Portal");
    }

    @Override
    /** @see com.byzaneo.generix.service.PortalService#export(PageExport) */
    public void export(PageExport export) {
        log.info("Start Export Page");
        try {
            List<Portlet> portlets = export.getPage()
                .getLayouts()
                .stream()
                .map(l -> l.getPortlets())
                .flatMap(x -> x.stream())
                .collect(Collectors.toList());

            List<Page> children = export.getPage()
                .getChildren();

            if (!isEmpty(children)) {
                portlets.addAll(getRecursivlyChildrenPortlets(export.getPage()));
            }

            final List<TaskDefinition> taskDefs = portlets.stream()
                .map(Portlet::getContent)
                .map(task ->
                {
                    if (task instanceof TaskTransferListener)
                        ((TaskTransferListener) task).prepareTaskForExport();
                    return task;
                })
                .map(TaskHelper::getDefinition)
                .collect(toList());
            export.getTasksByServiceName()
                .put(SERVICE_NAME, taskDefs);

            Gson GSON = PortalHelper.GSON;
            export.setPageJSON(GSON.toJson(export.getPage(), Page.class));

        }
        finally {
            log.info("End Export Page");
        }
    }

    private List<Portlet> getRecursivlyChildrenPortlets(Page parent) {
        List<Portlet> portlets = new ArrayList<>();

        for (Page page : parent.getChildren()) {
            if (!page.getChildren()
                .isEmpty()) {
                portlets.addAll(getRecursivlyChildrenPortlets(page));
            }
            portlets.addAll(page.getLayouts()
                .stream()
                .map(l -> l.getPortlets())
                .flatMap(x -> x.stream())
                .collect(Collectors.toList()));
        }
        return portlets;
    }

    private void changeParentTask(List<TaskDefinition> listTaskDefinitionOld, Map<Long, TaskDefinition> listTaskDefinitionNew)
        throws ServiceException {
        for (TaskDefinition taskOld : listTaskDefinitionOld) {
            if (taskOld == null || taskOld.getParent() == null || taskOld.getParent()
                .getId() == null) {
                continue;
            }

            Long idTask = taskOld.getId();
            Long idParentOld = taskOld.getParent()
                .getId();
            TaskDefinition taskNewParent = listTaskDefinitionNew.get(idParentOld);
            TaskDefinition taskDef = listTaskDefinitionNew.get(idTask);
            if (taskNewParent == null || taskDef == null) {
                throw new ServiceException("labels.exception_task_change_parent", "Error of algo changeParent", null);
            }
            taskDef.setParent(taskNewParent);
            Object task = this.taskService.getTask(taskDef.getId());
            this.taskService.save(task);
        }
    }

    /**
     * @param taskDef
     * @param instance destination's instance
     * @return new Task
     */
    @Override
    public Object cloneTask(TaskDefinition taskDef, Instance instance)
        throws Exception {
        if (taskDef == null) {
            return null;
        }
        String typeTask = taskDef.getType();
        if (typeTask == null) {
            log.warn("Task were no type found in Export");
            if (taskDef.getTitle() != null) {
                log.info("Task " + taskDef.getTitle() + " are not a good type");
            }
            return null;
        }
        TaskType taskTypeImport = TaskTypeHelper.getType(typeTask);

        Object taskNew = this.taskService.newTask(instance, taskTypeImport);
        TaskDefinition def = getDefinition(taskNew);
        if (def == null) {
            throw new ServiceException("labels.exception_task_type_not_found", "Impossible to find task type :" + typeTask, null, typeTask);
        }
        def.setDefinition(taskDef.getDefinition());
        def.setDescription(taskDef.getDescription());
        def.setTitle(taskDef.getTitle());
        def.setLocked(false);
        def.setShowTitle(taskDef.isShowTitle());
        def.setShowDescription(taskDef.isShowDescription());
        def.setUuid(taskDef.getUuid());
        TaskHelper.updateTask(taskNew);
        TaskHelper.updateDefinition(taskNew);
        TaskHelper.invokeCallbackMedhods(taskNew, PostLoad.class);
        return taskNew;
    }

    /**
     * @param task task à chercher
     * @param instance instance du user
     * @return renvoie null si pas trouve, sinon renvoie l'object Task
     */
    @Transactional(readOnly = true)
    public Object searchTaskInPortal(Object task, Instance instance) {
        List<TaskType> taskTypesPortal = this.taskService.getTypes();

        try {
            TaskDefinition def = TaskHelper.getDefinition(task);
            return taskTypesPortal.stream()
                .filter(tt -> TaskHelper.getType(task)
                    .getType()
                    .equals(tt.getType()))
                .flatMap(tt -> taskService.getTasks(instance, tt)
                    .stream())
                .map(TaskHelper::getDefinition)
                .filter(isEqual(def))
                .findFirst()
                .orElse(null);
        }
        catch (Exception e) {
            log.warn("Problem in test Present Task in portal", e);
        }
        return null;
    }

    private Map<Long, TaskDefinition> importationTaskInPortal(
        InstanceExport xport,
        List<TaskDefinition> listTaskDefinitionExport)
        throws Exception {

        if (listTaskDefinitionExport == null || listTaskDefinitionExport.isEmpty() || listTaskDefinitionExport.get(0) == null) {
            return emptyMap();
        }
        final Map<Long, TaskDefinition> listTaskDefinitionNew = new HashMap<>();
        for (TaskDefinition taskDefinitionExport : listTaskDefinitionExport) {
            if (taskDefinitionExport == null) {
                continue;
            }
            Long oldid = taskDefinitionExport.getId();
            if (listTaskDefinitionNew.get(oldid) != null) {
                continue;
            }
            Object taskNew = cloneTask(taskDefinitionExport,
                xport.getInstance());
            if (taskNew instanceof TaskTransferListener) {
                ((TaskTransferListener) taskNew).prepareTaskForImport(xport.getInstance());
            }
            /* import Parent */
            if (taskDefinitionExport.getParent() != null) {
                Long oldidParent = taskDefinitionExport.getParent()
                    .getId();
                if (listTaskDefinitionNew.get(oldidParent) == null) {

                    Object taskNewParent = cloneTask(taskDefinitionExport.getParent(),
                        xport.getInstance());
                    if (taskNewParent == null) {
                        throw new ServiceException("labels.exception_task_import_parents",
                            "Problem of import tasksParent for name" + taskDefinitionExport.getParent()
                                .getTitle(),
                            null, taskDefinitionExport.getParent()
                            .getTitle());
                    }
                    Object taskInPortalParent = searchTaskInPortal(taskNewParent,
                        xport.getInstance());
                    if (taskInPortalParent == null) {
                        this.taskService.save(taskNewParent);
                        listTaskDefinitionNew.put(oldidParent, getDefinition(taskNewParent));
                    }
                    else {
                        listTaskDefinitionNew.put(oldidParent, getDefinition(taskInPortalParent));
                    }

                }
            }
            if (taskNew == null) {
                throw new ServiceException("labels.exception_task_import",
                    "Problem of import tasks for name" + taskDefinitionExport.getTitle(),
                    null, taskDefinitionExport.getTitle());
            }
            this.taskService.save(taskNew);
            listTaskDefinitionNew.put(oldid,
                getDefinition(taskNew));
        }
        changeParentTask(listTaskDefinitionExport, listTaskDefinitionNew);
        updateTaskDefinitionLinks(listTaskDefinitionNew);
        return listTaskDefinitionNew;
    }

    private boolean updateTaskDefinitionLinks(Map<Long, TaskDefinition> listTaskDefinitionNew) {
        boolean allLinksAreValid = true;

        for (Map.Entry<Long, TaskDefinition> entry : listTaskDefinitionNew.entrySet()) {
            TaskDefinition taskDefinition = entry.getValue();
            String definition = taskDefinition.getDefinition();
            if (definition != null) {
                JSONObject json = new JSONObject(definition);
                for (String linkKey : LINK_KEYS) {
                    if (json.has(linkKey)) {
                        TaskDefinition currentLinkedTaskDef = listTaskDefinitionNew.get(Long.parseLong(json.get(linkKey)
                            .toString()));
                        if (currentLinkedTaskDef != null) {
                            String uuid = currentLinkedTaskDef.getUuid();
                            if (!isBlank(uuid) && REGEX_UUID.matcher(uuid)
                                .matches()) {
                                Optional<Entry<Long, TaskDefinition>> linkTaskDefinition = listTaskDefinitionNew.entrySet()
                                    .stream()
                                    .filter(td -> uuid.equals(td.getValue()
                                        .getUuid()))
                                    .findFirst();
                                if (linkTaskDefinition.isPresent()) {
                                    json.put(linkKey, linkTaskDefinition.get()
                                        .getValue()
                                        .getId());
                                }
                                else {
                                    allLinksAreValid = false;
                                    json.remove(linkKey);
                                }
                            }
                        }
                        else {
                            allLinksAreValid = false;
                            json.remove(linkKey);
                        }
                        taskDefinition.setDefinition(json.toString());
                        entry.setValue(taskDefinition);
                    }
                }
            }
        }
        return allLinksAreValid;
    }

    private Map<Long, TaskDefinition> importationTaskInPage(
        PageExport xport, Instance instance,
        List<TaskDefinition> listTaskDefinitionExport,
        boolean save)
        throws Exception {

        if (CollectionUtils.isEmpty(listTaskDefinitionExport) || listTaskDefinitionExport.get(0) == null) {
            return emptyMap();
        }
        final Map<Long, TaskDefinition> listTaskDefinitionNew = new HashMap<>();
        for (TaskDefinition taskDefinitionExport : listTaskDefinitionExport) {
            if (taskDefinitionExport == null) {
                continue;
            }
            Long oldid = taskDefinitionExport.getId();
            if (listTaskDefinitionNew.get(oldid) != null) {
                continue;
            }
            Object taskNew = cloneTask(taskDefinitionExport, instance);
            if (taskNew instanceof TaskTransferListener) {
                ((TaskTransferListener) taskNew).prepareTaskForImport(instance);
            }
            /* import Parent */
            if (taskDefinitionExport.getParent() != null) {
                Long oldidParent = taskDefinitionExport.getParent()
                    .getId();
                if (listTaskDefinitionNew.get(oldidParent) == null) {
                    Optional<TaskDefinition> parentFound = listTaskDefinitionExport.stream()
                        .filter(td -> oldidParent.equals(td.getId()))
                        .findFirst();
                    if (parentFound.isPresent()) {
                        TaskDefinition parent = taskDefinitionExport.getParent();
                        parent.setDefinition(parentFound.get()
                            .getDefinition());
                        taskDefinitionExport.setParent(parent);
                    }
                    Object taskNewParent = cloneTask(taskDefinitionExport.getParent(),
                        instance);
                    if (taskNewParent == null) {
                        throw new ServiceException("labels.exception_task_import_parents",
                            "Problem of import tasksParent for name" + taskDefinitionExport.getParent()
                                .getTitle(),
                            null, taskDefinitionExport.getParent()
                            .getTitle());
                    }
                    if (save) {
                        this.taskService.save(taskNewParent);
                    }
                    listTaskDefinitionNew.put(oldidParent, getDefinition(taskNewParent));
                }
            }
            if (taskNew == null) {
                throw new ServiceException("labels.exception_task_import",
                    "Problem of import tasks for name" + taskDefinitionExport.getTitle(),
                    null, taskDefinitionExport.getTitle());
            }
            if (save) {
                this.taskService.save(taskNew);
            }

            if (taskNew instanceof TaskDefinition) {
                listTaskDefinitionNew.put(oldid, (TaskDefinition) taskNew);
            }
            else {
                listTaskDefinitionNew.put(oldid, getDefinition(taskNew));
            }
        }
        if (save) {
            changeParentTask(listTaskDefinitionExport, listTaskDefinitionNew);
        }
        return listTaskDefinitionNew;
    }

    private Portlet clonePortlet(Portlet portlet,
        Map<Long, TaskDefinition> listTaskDefinitionNew)
        throws ServiceException {
        Portlet portletNew = new Portlet();
        if (portlet == null) {
            log.warn("Portlet null found, remplace with Portal empty");
            return portletNew;
        }

        portletNew.setName(portlet.getName());
        portletNew.setStyle(portlet.getStyle());
        portletNew.setStyleClass(portlet.getStyleClass());
        portletNew.setMode(portlet.getMode());

        if (portlet.getContentId() != null && listTaskDefinitionNew != null && !listTaskDefinitionNew.isEmpty()) {
            TaskDefinition taskDefinitionNew = listTaskDefinitionNew.get(Long.valueOf(portlet.getContentId()));
            if (taskDefinitionNew == null) {
                throw new ServiceException("labels.exception_portlet_cloning",
                    "Error Algorithme in clone Portlet name " + portlet.getName(), null,
                    portlet.getName());
            }
            portletNew.setContentId(taskDefinitionNew.getId()
                .toString());

        }
        else {
            log.warn("Portlet as no ContentId");
        }
        return portletNew;
    }

    private Layout cloneColumnsLayout(Layout layoutImport,
        Map<Long, TaskDefinition> listTaskDefinitionNew)
        throws ServiceException {
        ColumnsLayout layoutNew = new ColumnsLayout();
        // clone header
        com.byzaneo.portal.layout.columns.Column headerImport = ((ColumnsLayout) layoutImport).getHeader();
        if (headerImport != null) {
            layoutNew.setHeader(cloneColumn(headerImport, listTaskDefinitionNew));
        }

        // clone columns
        List<com.byzaneo.portal.layout.columns.Column> columnsImport = ((ColumnsLayout) layoutImport).getColumns();
        if (columnsImport != null) {
            List<com.byzaneo.portal.layout.columns.Column> columnsNew = columnsImport.stream()
                .map(columnImport -> cloneColumn(columnImport, listTaskDefinitionNew))
                .collect(toList());
            layoutNew.setColumns(columnsNew);
        }
        return layoutNew;
    }

    private com.byzaneo.portal.layout.columns.Column cloneColumn(com.byzaneo.portal.layout.columns.Column column,
        Map<Long, TaskDefinition> listTaskDefinitionNew) throws ServiceException {
        List<Portlet> clonedPortlets = column.getPortlets()
            .stream()
            .map(portletImport -> clonePortlet(portletImport, listTaskDefinitionNew))
            .collect(toList());
        return new com.byzaneo.portal.layout.columns.Column(
            column.getDevice(),
            column.getWidth(),
            column.getStyleClass(),
            column.getStyle(),
            clonedPortlets);
    }

    @Deprecated
    private Layout cloneGridLayout(GridLayout layout, Map<Long, TaskDefinition> listTaskDefinitionNew)
        throws ServiceException {
        GridLayout layoutNew = new GridLayout();
        /* pour chaque row on parcours les portlets */

        List<Row> rowsImport = layout.getRows();
        List<Row> rowsNew = new ArrayList<>();
        for (Row rowImport : rowsImport) {
            Row rowNew = new Row();
            rowNew.setStyle(rowImport.getStyle());
            rowNew.setStyleClass(rowImport.getStyleClass());
            List<Column> columsImport = rowImport.getColumns();
            List<Column> columsImportNew = new ArrayList<>();
            for (Column columImport : columsImport) {
                Column columNew = new Column();
                columNew.setColspan(columImport.getColspan());
                columNew.setRowspan(columImport.getRowspan());
                columNew.setStyle(columImport.getStyle());
                columNew.setStyleClass(columImport.getStyleClass());
                /* creation de la portlet */
                Portlet portletImport = columImport.getPortlet();
                Portlet portletNew = clonePortlet(portletImport,
                    listTaskDefinitionNew);
                columNew.setPortlet(portletNew);
                /********************** portels **********/
                columsImportNew.add(columNew);
            }
            rowNew.setColumns(columsImportNew);
            rowsNew.add(rowNew);
        }
        layoutNew.setRows(rowsNew);
        return layoutNew;
    }

    /**
     * @param pageImport page to clone
     * @param pageParent page parent cloner si elle existe
     * @param listTaskDefinitionNew table of tasks key : old number ID of task, value = definition of new task in page.
     * @return
     * @throws Exception
     */
    private Page importPage(Page pageImport, Page pageParent,
        Map<Long, TaskDefinition> listTaskDefinitionNew)
        throws ServiceException {
        Page pageNew = new Page();
        pageNew.setStyle(pageImport.getStyle());
        pageNew.setStyleClass(pageImport.getStyleClass());
        pageNew.setName(pageImport.getName());
        pageNew.setFontAwesomeIcon(pageImport.getFontAwesomeIcon());
        pageNew.setShowFontAwesomeIcon(pageImport.isShowFontAwesomeIcon());
        pageNew.setToBeDisplayedOption(pageImport.isToBeDisplayedOption());
        pageNew.setQuery(pageImport.getQuery());
        pageNew.setDisplayPageOnAngular(pageImport.isDisplayPageOnAngular());
        if (pageParent != null) {
            pageNew.setParent(pageParent);
        }
        if (pageImport.getChildren() != null && !pageImport.getChildren()
            .isEmpty()) {
            List<Page> listPagesFille = clonePages(pageImport.getChildren(),
                listTaskDefinitionNew);
            pageNew.setChildren(listPagesFille);
        }
        /* Creation of layout */
        Layout layoutNew;
        List<Layout> layoutsImport = pageImport.getLayouts();

        for (Layout layoutImport : layoutsImport) {

            if (layoutImport instanceof GridLayout) {
                layoutNew = cloneGridLayout((GridLayout) layoutImport, listTaskDefinitionNew);

            }
            else if (layoutImport instanceof SimpleLayout) {
                layoutNew = new SimpleLayout();
                final List<Portlet> portletsImport = layoutImport.getPortlets();
                // SimpleLayout carries a single portlet
                Portlet portletNew = portletsImport.stream()
                    .filter(Objects::nonNull)
                    .map(portletImport -> clonePortlet(portletImport, listTaskDefinitionNew))
                    .findFirst()
                    .orElse(null);
                ((SimpleLayout) layoutNew).setPortlet(portletNew);
            }
            else if (layoutImport instanceof ColumnsLayout) {
                layoutNew = cloneColumnsLayout(layoutImport, listTaskDefinitionNew);
            }
            else if (layoutImport instanceof EmptyLayout) {
                layoutNew = new EmptyLayout();
            }
            else {
                throw new ServiceException("labels.exception_unknown_layout_type", "Layout type unknown", null);
            }
            pageNew.getLayouts()
                .add(layoutNew);
            int i = 0;
            for (Portlet portlet : layoutImport
                .getPortlets()) {
                // add portlets for last added layout
                Iterables.getLast(pageNew.getLayouts())
                    .getPortlets()
                    .get(i)
                    .setContentType(portlet.getContentType());
                i++;
            }
        }
        return pageNew;
    }

    private List<Page> clonePages(List<Page> listPagesImport, Map<Long, TaskDefinition> listTaskDefinitionNew) throws ServiceException {
        if (listPagesImport == null || listPagesImport.isEmpty()) {
            log.warn("Error during page importation");
            return emptyList();
        }
        return listPagesImport.stream()
            .map(pageImport -> importPage(pageImport, null, listTaskDefinitionNew))
            .collect(toList());
    }

    private Portal clonePortal(Portal portal, Instance instance,
        Map<Long, TaskDefinition> listTaskDefinitionNew)
        throws Exception {
        Portal portalNew = this.getPortal(instance, true);
        portalNew.setName(portal.getName());
        portalNew.setOwner(instance.getCode());
        portalNew.setStyle(portal.getStyle());
        portalNew.setStyleClass(portal.getStyleClass());

        /* creation des pages */
        PortalDefinition portalDefinitionImport = portal.getDefinition();
        List<Page> listPagesImport = portalDefinitionImport.getPages();
        List<Page> listPagesNew = clonePages(listPagesImport, listTaskDefinitionNew);
        repairShortcutTaskLinks(listPagesNew, listTaskDefinitionNew);

        /* sauvegarde des pages modifies */
        PortalDefinition portalDefinitionNew = new PortalDefinition();
        portalDefinitionNew.setPages(listPagesNew);
        portalNew.setDefinition(portalDefinitionNew);
        return portalNew;
    }

    private void repairShortcutTaskLinks(List<Page> listPagesNew, Map<Long, TaskDefinition> listTaskDefinitionNew) {
        for (TaskDefinition taskDefinition : listTaskDefinitionNew.values())
            if (taskDefinition.getType()
                .contains(":Shortcuts:")) {
                String definition = taskDefinition.getDefinition();
                if (definition != null) {
                    JSONObject def = new JSONObject(definition);
                    JSONArray items = (JSONArray) def.get("shortcutsItems");
                    for (Object item : items) {
                        JSONObject jsonObject = (JSONObject) item;
                        if (jsonObject.has("target")) {
                            String target = (String) jsonObject.get("target");
                            String[] ids = target.split("/");
                            if (ids.length != 2)
                                continue;// don't block the import
                            Long taskId = Long.parseLong(ids[1]);
                            TaskDefinition linkedTaskDefinition = listTaskDefinitionNew.get(taskId);
                            if (linkedTaskDefinition != null) {
                                Page page = findPageForPortlet(listPagesNew, linkedTaskDefinition.getId()
                                    .toString());
                                if (page != null)
                                    jsonObject.put("target", page.getId() + "/" + linkedTaskDefinition.getId()
                                        .toString());
                            }
                        }
                    }
                    taskDefinition.setDefinition(def.toString());
                }
            }
    }

    private Page findPageForPortlet(List<Page> pages, String contentId) {
        return pages.stream()
            .map(page -> findPageForPortlet(page, contentId))
            .filter(Objects::nonNull)
            .findFirst()
            .orElse(null);
    }

    private Page findPageForPortlet(Page page, String contentId) {
        if (page.getLayouts()
            .stream()
            .map(Layout::getPortlets)
            .flatMap(List::stream)
            .anyMatch(task -> contentId.equals(task.getContentId())))
            return page;
        if (page.getChildren()
            .size() != 0)
            return findPageForPortlet(page.getChildren(), contentId);
        return null;
    }

    @Override
    public boolean portalIsEmpty(Instance instance) {
        List<Portal> portals = this.portalService.getPortals(instance.getCode());
        // Vérification que le portal est vide avant importation pour le moment
        return !portals.isEmpty() && (portals.size() > 1 || portals.get(0) != null && portals.get(0)
            .getDefinition() != null && portals.get(0)
            .getDefinition()
            .getPages() != null &&
            !portals.get(0)
                .getDefinition()
                .getPages()
                .isEmpty());
    }

    @Override
    @Transactional
    public void importation(InstanceExport export) throws ServiceException {
        try {
            log.info("Start importation task Service");
            if (export == null) {
                throw new ServiceException("labels.exception_export_null", "Export is null", null);
            }

            Instance instance = export.getInstance();
            if (instance == null) {
                throw new ServiceException("labels.exception_import_instance_null", "Instance in import is null", null);
            }

            if (this.portalIsEmpty(instance)) {
                throw new ServiceException("labels.exception_import_portal_not_empty", "Portal is not empty for " + instance.getCode(),
                    null,
                    instance.getCode());
            }

            /* recuperation du Owner dans l'import */
            String codeNew = export.getInstance()
                .getCode();
            if (codeNew == null) {
                throw new ServiceException("labels.exception_import_instance_code_null", "Code Instance is null", null);
            }

            log.info("Start import Task on Service Portal");

            /* Creation des tasks */
            List<TaskDefinition> listTaskDefinitionExport = export.getTasksByServiceName()
                .get(SERVICE_NAME);
            /*
             * Validation qu'on peut creer le portal si il y a pas de task a importer, on pas de portal
             */
            if (listTaskDefinitionExport == null) {
                log.warn("No definition task Found");
                listTaskDefinitionExport = new ArrayList<>();
            }
            else if (listTaskDefinitionExport.isEmpty()) {
                log.info("No tasks in portal");
            }
            else if (listTaskDefinitionExport.get(0) == null) {
                log.warn("No task found in Gson");
            }

            this.checkAndRepairParentsForDocumentCounters(listTaskDefinitionExport);

            /* contient la liste des tasks inserer */
            Map<Long, TaskDefinition> listTaskDefinitionNew = this.importationTaskInPortal(export,
                listTaskDefinitionExport);

            if (listTaskDefinitionNew.isEmpty()) {
                log.warn("No task insert , must be no task in json");
            }

            log.info("End importation task Service");

            log.info("Start import Portal");
            /* Creer des portails */
            Gson GSON = PortalHelper.GSON;
            Portal portalImport = GSON.fromJson(export.getJsonByServiceName()
                .get(SERVICE_NAME), Portal.class);

            if (portalImport == null) {
                log.warn("Impossible to load Portal JSON or Portal is empty in GSon");
                portalImport = new Portal();
            }

            Portal portalNew = clonePortal(portalImport, export.getInstance(), listTaskDefinitionNew);
            if (portalNew == null) {
                throw new ServiceException("labels.exception_import_error_during_cloning", "Error in clone Portal", null);
            }
            if (!listTaskDefinitionNew.isEmpty() && !portalNew.getDefinition()
                .getPages()
                .isEmpty()) {
                this.save(portalNew);
            }
            else {
                log.warn("No info in portal");
            }
            log.info("End import Portal");
        }
        catch (ServiceException e) {
            log.error("Error in importation", e);
            throw e;
        }
        catch (Exception e) {
            log.error("Error in importation", e);
            throw new ServiceException(e);
        }
        finally {
            log.info("End importation portal Service");
        }
    }

    /**
     *  AIO-20165; AIO-20213
     *  We need to import as correctly as possible the older exports of the environments (versions pre 5.93)
     *  We can find some unwanted cases when DocumentCounters are linked to portlets (Invoices, Orders etc.) in a strange way:
     *  the "parent" field contains the correct uuid but the id is wrong.
     *  For example (see file attached to AIO-20165) the portlet :
     *  - type : "portal/doc:DocumentCounter:1.0.0"
     *  - uuid : "a9034ee2-915c-400e-b7ec-6116e3f48010"
     *  - id : 27123
     *  Have a parent (link) to the portlet :
     *  - type : "portal/xcbl:Invoice:1.0.0"
     *  - uuid: "8683dd82-6e08-4f97-a150-10df5cb776a5"
     *  - id : 27122
     *  BUT :
     *  there is NO portlet having the id = 27122 AND
     *  the invoice portlet having the uuid = 8683dd82-6e08-4f97-a150-10df5cb776a5 has the id = 26717
     * <p>
     *  So we need to examine the list of task definitions BEFORE being transformed for import,
     *  we check if the "parent" member of DocumentCounter portlets have the correct correspondence uuid <=> id :
     *  - if we have a portlet with the specified uuid, we update the id field to correspond
     *  - if we don't have a portlet having the specified uuid, we set the "parent" field to null
     * <p>
     *  Note: At some point into the future when we are certain that we don't have old exports anymore (pre 5.93) we should remove this method
     *
     * @param listTaskDefinitionToCheck - the list of task definition to be checked and repaired
     */
    private void checkAndRepairParentsForDocumentCounters(List<TaskDefinition> listTaskDefinitionToCheck) {
        if (CollectionUtils.isEmpty(listTaskDefinitionToCheck)) {
            return;
        }
        for (TaskDefinition taskDefinition : listTaskDefinitionToCheck) {
            if (StringUtils.isBlank(taskDefinition.getType()) || !StringUtils.containsIgnoreCase(taskDefinition.getType(),
                CounterManagementHelper.DOCUMENT_COUNTER_NAME)) {
                continue;
            }
            TaskDefinition linkedDefinition = taskDefinition.getParent();
            if (linkedDefinition == null) {
                continue;
            }

            TaskDefinition originalDefinition = listTaskDefinitionToCheck.stream().filter(taskDef -> StringUtils.equals(taskDef.getUuid(), linkedDefinition.getUuid())).findFirst().orElse(null);
            if (originalDefinition == null) {
                //we don't have a definition having this uuid -> set the parent to null
                taskDefinition.setParent(null);
                continue;
            }
            if (!Objects.equals(originalDefinition.getId(), linkedDefinition.getId())) {
                //we set the correct id
                linkedDefinition.setId(originalDefinition.getId());
            }
        }
    }

    @Override
    public PageExport importation(InputStream pageIn, String... serviceNames) {
        log.info("Start Page importation Service");

        try (final Reader reader = new InputStreamReader(pageIn, "UTF-8")) {

            Gson GSON = PortalHelper.GSON;
            PageExport export = GSON.fromJson(reader, PageExport.class);

            if (export == null) {
                throw new ServiceException("labels.exception_export_null", "Export is null", null);
            }

            Page page = GSON.fromJson(export.getPageJSON(), Page.class);

            if (page == null) {
                throw new ServiceException("labels.exception_import_page_null", "Page in import is null", null);
            }

            page.setId(randomUUID().toString());
            export.setPage(page);

            return export;

        }
        catch (IOException e) {
            log.error("Error during the import : %s", e.getMessage());
        }
        finally {
            log.info("End Page importation Service");
        }
        return null;
    }

    /*
     * -- EVENTS --
     */

    /**
     * Method handler for task deletion
     *
     * @param eventTaskDelete
     * @since GNX-613
     */
    @Subscribe(topic = "persistent/TaskDefinition/delete")
    @Transactional
    public void onEventTaskDelete(Event eventTaskDelete) {
        log.info("Received {}", eventTaskDelete);
        // delete portlet(s)
        for (Portal portal : this.portalService.getAllPortals()) {
            if (removePortletsByContentId(portal, eventTaskDelete.<TaskDefinition> getSource()
                .getId()
                .toString())) {
                this.portalService.save(portal);
                log.debug("\t Portal {} updated after portlet deletion.", portal.getName());
            }
        }
    }

    @Override
    public Page importOnPage(Page page, Instance instance, InputStream in) throws Exception {

        PageExport export = importation(in);
        List<TaskDefinition> listTaskDefinitionExport = export.getTasksByServiceName()
            .get(PortalService.SERVICE_NAME);
        Map<Long, TaskDefinition> listTaskDefinitionNew = importationTaskInPage(export, instance, listTaskDefinitionExport, true);
        updateTaskDefinitionLinks(listTaskDefinitionNew);
        return importPage(export.getPage(), page.getParent(), listTaskDefinitionNew);

    }

    public boolean checkImportedPageLinks(Page page, Instance instance, InputStream in) throws Exception {
        PageExport export = importation(in);
        List<TaskDefinition> listTaskDefinitionExport = export.getTasksByServiceName()
            .get(PortalService.SERVICE_NAME);
        Map<Long, TaskDefinition> listTaskDefinitionNew = importationTaskInPage(export, instance, listTaskDefinitionExport, false);
        return updateTaskDefinitionLinks(listTaskDefinitionNew);
    }

    @Override
    @Transactional
    public void onRemovePageAndContent(Portal portal, Page page, Page parent) {
        // Remove Page
        portal = this.removePage(portal, parent, page);
        // Remove Portlet
        this.removePageContent(page, portal.getOwner());
        // Remove portal if necessary
        if (portal.getDefinition()
            .getPages()
            .isEmpty()) {
            this.removePortal(portal);
            portal = null;
        }
    }

    private void checkIfDocumentCounterIsUsed(Portlet portlet) {
        if (portlet.getContent() instanceof DocumentCounterManagement) {
            if (counterManagementService.isDocumentCounterUsed(Long.valueOf(portlet.getContentId()))) {
                throw new CounterManagementException("Can not delete counter.");
            }
        }
    }

}
