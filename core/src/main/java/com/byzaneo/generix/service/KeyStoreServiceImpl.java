package com.byzaneo.generix.service;

import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.generix.api.AbstractConfigurableService;
import com.byzaneo.generix.bean.KeyStoreConfiguration;
import com.byzaneo.generix.bean.TimestampServer;
import com.byzaneo.generix.exception.*;
import com.byzaneo.generix.keystore.*;
import com.byzaneo.generix.util.AESEncryptionHelper;
import com.byzaneo.generix.util.CLibrary;
import com.byzaneo.generix.util.KeyStoreHelper;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Organization;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.service.DocumentService;
import com.sun.jna.NativeLong;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.security.auth.callback.Callback;
import javax.security.auth.callback.CallbackHandler;
import javax.security.auth.callback.PasswordCallback;
import javax.security.auth.login.LoginException;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateEncodingException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.byzaneo.commons.bean.PropertyType.Enum;
import static com.byzaneo.generix.keystore.KeyStoreFileResult.ResultMessage.*;
import static com.byzaneo.generix.keystore.KeyStoreFileVisitor.ALIAS_PARAM;
import static com.byzaneo.generix.keystore.KeyStoreFileVisitor.CERTIFICATE_DOCUMENT_PARAM;
import static com.byzaneo.generix.service.KeyStoreService.KeyStoreType.JKS;
import static com.byzaneo.generix.service.KeyStoreService.KeyStoreType.PKCS12;
import static com.byzaneo.generix.util.KeyStoreHelper.*;
import static com.byzaneo.xtrade.api.DocumentStatus.NONE;
import static com.byzaneo.xtrade.api.DocumentType.FOLDER;
import static java.util.Collections.emptyList;
import static java.util.Collections.sort;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.select;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service(KeyStoreService.SERVICE_NAME)
public class KeyStoreServiceImpl extends AbstractConfigurableService<KeyStoreConfiguration> implements KeyStoreService {

  private static final Logger log = LoggerFactory.getLogger(KeyStoreServiceImpl.class);

  // SERVICES
  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private SecurityService securityService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configurationService;

  // PROVIDERS
  @Autowired
  @Qualifier(Pkcs11ProviderHolder.COMPONENT_NAME)
  private Pkcs11ProviderHolder pkcs11ProviderHolder;

  // PROPERTIES
  @Value("${keystore.dir:}")
  private String keystoreFolder;

  private File sslDirectory;

  /*
   * -- LIFE CYCLE --
   */

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public void init() {
    final long start = System.currentTimeMillis();
    log.info("STARTING KEYSTORE SERVICE...");

    // add Bouncy Castle provider
    final BouncyCastleProvider provider = new BouncyCastleProvider();
    Security.addProvider(provider);
    log.info("\t- Security provider: {}", provider.getInfo());

    // SSL directory
    this.sslDirectory = new File(getKeystoreFolder(), "ssl");
    this.sslDirectory.mkdirs();
    log.info("\t- SSL Directory: {}", this.sslDirectory);

    log.info("KEYSTORE SERVICE STARTED in {}ms.", System.currentTimeMillis() - start);
  }

  private File getKeystoreFolder() {
    if (StringUtils.isBlank(keystoreFolder)) {
      return new File(configurationService.getFile(ConfigurationKey.DATA_DIR, FileUtils.getTempDirectory()), "keystore");
    }
    else {
      return new File(keystoreFolder);
    }
  }

  @Override
  protected String getVariableName() {
    return KeyStoreConfiguration.class.getSimpleName();
  }

  @Override
  protected String getServiceName() {
    return KeyStoreService.SERVICE_NAME;
  }

  @Override
  protected Supplier<KeyStoreConfiguration> getConfigurationSupplier() {
    return KeyStoreConfiguration::new;
  }

  /*
   * -- ACCESSOR --
   */

  /** @see com.byzaneo.generix.service.KeyStoreService#getSSLDirectory() */
  @Override
  public File getSSLDirectory() {
    return sslDirectory;
  }

  /*
   * -- CRUD --
   */

  /** @see com.byzaneo.generix.service.KeyStoreService#save(com.byzaneo.xtrade.bean.Document) */
  @Override
  @Transactional
  public Document save(Document keyStoreDocument) {
    return this.documentService.saveDocument(keyStoreDocument);
  }

  /** @see com.byzaneo.generix.service.KeyStoreService#remove(com.byzaneo.xtrade.bean.Document, String) */
  @SuppressWarnings("squid:S1226")
  @Override
  public void remove(Document keyStoreDocument, String organizationId) {
    try {
      if (keyStoreDocument == null || isBlank(organizationId)) {
        return;
      }
      Document parent = keyStoreDocument.getParent();
      Set<Document> children;
      if (parent != null && (children = parent.getChildren()) != null && children.size() == 1 &&
          CERTIFICATE_FAMILY_PARENT_TYPE.equals(parent.getType())) {
        removeParent(keyStoreDocument, organizationId, parent, children);
      }
      else {
        log.info("removing certificate id {}", keyStoreDocument.getId());
        keyStoreDocument = documentService.getDocument(keyStoreDocument.getId());// refresh?
        documentService.removeDocument(keyStoreDocument, true);
      }
      removeKeystoreFolderByDocumentId(String.valueOf(keyStoreDocument.getId()));
    }
    catch (RuntimeException e) {
      log.error("Error removing keystore", e);
      throw e;
    }
  }

  private void removeKeystoreFolderByDocumentId(String documentId) {
    if (StringUtils.isNotEmpty(documentId) && FileHelper.deleteFile(new File(sslDirectory, documentId))) {
      log.debug("Document {} keystore folder deleted successfully", documentId);
    }
    else {
      log.error("Failed to delete Document {} keystore folder", documentId);
    }
  }

  @SuppressWarnings("squid:S1226")
  void removeParent(Document keyStoreDocument, String organizationId, Document parent, Set<Document> children) {
    Document onlyChild = children.iterator()
        .next();
    if (onlyChild.equals(keyStoreDocument)) {
      // refresh parent from persistence layer
      parent = documentService.getDocument(parent.getId());

      // check if this certificate family is referenced in a timestamp server config for this organization
      String friendlyName = parent.getReference();
      checkTimeStampServerIntegrity(friendlyName, organizationId);

      log.info("removing certificate id {} with parent id {}", keyStoreDocument.getId(), parent.getId());
      documentService.removeDocument(onlyChild, true);
      documentService.removeDocument(parent, true);// this should cascade on the child
    }
    else {
      // inconsistency !
      log.error("inconsistency detected for certificate id {} with parent id {}", keyStoreDocument.getId(), parent.getId());
    }
  }

  /**
   * @param friendlyName
   * @param organizationId throws ReferencedTimestampServerException if friendlyName is referenced in a timestamp server for this
   *                       organizationId
   */
  void checkTimeStampServerIntegrity(String friendlyName, String organizationId) {
    if (friendlyName == null) {
      return;
    }
    Group group = securityService.getGroup(organizationId);
    // final Organization group = securityService.getOrganizationByCode(organizationId, organizationId);//TODO : will not work if the
    // organization is not a company..
    if (group == null) {
      // no integrity check can be performed - is the organization a company? see above if organizations
      // that are not companies can be found
      log.warn("checkTimeStampServerIntegrity() - organization not found for id (" + organizationId + ")");
      return;
    }
    KeyStoreConfiguration curConfiguration = getConfiguration(group);
    TimestampServer timeStampServer = curConfiguration.getTimestampServer();
    if (timeStampServer == null) {
      return;
    }
    String ksKey = timeStampServer.getKeyStoreName();
    String tsKey = timeStampServer.getTrustStoreName();
    String ksName = toKSFamilyName(ksKey);
    String tsName = toKSFamilyName(tsKey);

    if (friendlyName.equals(ksName) || friendlyName.equals(tsName)) {
      throw new ReferencedTimestampServerException();
    }
    // else no problem, simply return
  }

  @SuppressWarnings("serial")
  public static class ReferencedTimestampServerException extends RuntimeException {
  }

  /*
   * -- DOCUMENT MANAGEMENT --
   */

  /** @see com.byzaneo.generix.service.KeyStoreService#uploadKeyStore(com.byzaneo.generix.keystore.AbstractKeyStoreFile) */
  @Override
  @Transactional
  public KeyStoreFileResult uploadKeyStore(AbstractKeyStoreFile params) {
    KeyStoreFileResult result = new KeyStoreFileResult();
    params.accept(new KeyStoreFileCheckerVisitor(),
        result,
        null);

    if (result.isError()) {
      return result;
    }

    try {
      Map<String, Object> additionalParams = new HashMap<>();
      ToKeyStoreResult toKeyStoreResult = params.accept(new ToKeyStoreVisitor(this), result, additionalParams);
      Map<String, Certificate> certificates = toKeyStoreResult.getCertificates();

      if (certificates != null) {

        for (Map.Entry<String, Certificate> certificate : certificates.entrySet()) {
          final String alias = certificate.getKey();
          ((KeyStoreFile) params).setAlias(alias);

          KeyStore keyStore = KeyStoreHelper.createStandardEmptyKeystore(params.getPassword());
          keyStore.setCertificateEntry(alias, certificate.getValue());

          try (FileOutputStream fos = new java.io.FileOutputStream(((KeyStoreFile) params).getInputFilePath());) {
            keyStore.store(fos, params.getPassword() != null ? params.getPassword()
                .toCharArray() : new char[0]);
          }
          result = proceedTreatment(keyStore, result, toKeyStoreResult, params, additionalParams);
        }
      }
      else {
        KeyStore keyStore = toKeyStoreResult.getKeyStore();
        result = proceedTreatment(keyStore, result, toKeyStoreResult, params, additionalParams);
      }

    }
    catch (Exception e) {
      log.error("could not load keystore ", e);
      return result.error(COULD_NOT_LOAD_KEYSTORE);
    }

    return result;
  }

  public KeyStoreFileResult proceedTreatment(KeyStore keyStore, KeyStoreFileResult result, ToKeyStoreResult toKeyStoreResult,
      AbstractKeyStoreFile params, Map<String, Object> additionalParams) {

    if (result.isError()) {
      return result;
    }

    KeyStoreType keyStoreType = toKeyStoreResult.getKeyStoreType();

    if (keyStore == null || keyStoreType == null) {
      // no keystore could be parsed from the input
      return result.error(UNRECOGNIZED_KEYSTORE);
    }

    // get the parent document or create it
    DetermineFriendlyNameVisitor determineFriendlyNameVisitor = new DetermineFriendlyNameVisitor();
    String keyStoreFriendlyName = params.accept(determineFriendlyNameVisitor, result, null);
    if (StringUtils.isBlank(keyStoreFriendlyName)) {
      return result.error(NO_FRIENDLY_NAME_FOUND);
    }

    Document parentKeyStoreDocument;
    Document keyStoreFolder = params.getKeyStoreFolder();
    UsageType usage = params.getUsage();
    try {
      parentKeyStoreDocument = getOrCreateParent(keyStoreFriendlyName, keyStoreFolder, usage);
    }
    catch (MultipleParentsFoundException e) {
      log.error("MultipleParentsFoundException for friendlyName " + keyStoreFriendlyName + " and folder id " + keyStoreFolder.getId());
      return result.error(MULTIPLE_PARENT_FOR_FRIENDLY_NAME, keyStoreFriendlyName);
    }
    if (parentKeyStoreDocument == null) {
      // could not find nor create the parent Document for this certificate family (aka friendly name)
      return result.error(COULD_NOT_FIND_NOR_CREATE_PARENT_FOR_FRIENDLY_NAME);
    }
    String parentUsageModelProperty = getNotNullModelProperty(parentKeyStoreDocument, KEYSTORE_USAGE);
    UsageType parentUsage = parentUsageModelProperty == null ? null : UsageType.valueOf(parentUsageModelProperty);
    if (usage != parentUsage) {
      log.error("new usage : " + usage + " does not match parent usage : " + parentUsage);
      return result.error(USAGE_MISMATCH, usage, parentUsage);
    }

    // create a child Document for the certificate
    Document certificate = new Document();
    certificate.setParent(parentKeyStoreDocument);
    parentKeyStoreDocument.addChild(certificate);

    certificate.setLinCount(getKeyStoreSize(keyStore));
    certificate.setReference(keyStoreFriendlyName);
    certificate.setOwners(KEYSTORE_OWNERS);
    certificate.setType(keyStoreType.name());

    additionalParams.put(KeyStoreFileVisitor.KEYSTORE_PARAM, keyStore);
    String alias = null;
    DetermineAliasVisitor determineAliasVisitor = new DetermineAliasVisitor();
    try {
      alias = params.accept(determineAliasVisitor, result, additionalParams);
      if (StringUtils.isEmpty(alias)) {
        throw new CannotDetermineAliasException("");
      }
    }
    catch (CannotDetermineAliasException e) {
      String aliases = e.getAliases();
      log.error("tryCreateDocumentFromKeyStore() - CannotDetermineAliasException - found aliases (;-separated) : {}", aliases, e);
      return result.error(CANNOT_DETERMINE_ALIAS, aliases);
    }
    catch (AliasNotFoundException e) {
      String specifiedAlias = e.getAlias();
      String aliases = e.getAliases();
      log.error("tryCreateDocumentFromKeyStore() - AliasNotFoundException - not found : {}.Found aliases (;-separated) : {}", alias,
          aliases, e);
      return result.error(ALIAS_NOT_FOUND, specifiedAlias, aliases);
    }
    catch (UnexpectedMultipleKeysException e) {
      log.error("tryCreateDocumentFromKeyStore() - multiple aliases found, expected only one", e);
      return result.error(UNEXPECTED_MULTIPLE_KEYS);
    }
    catch (Exception e) {
      log.error("tryCreateDocumentFromKeyStore() - Unexpected error while determining alias", e);
      return result.error(UNEXPECTED_ERROR_DETERMINING_ALIAS);
    }

    // add needed properties
    additionalParams.put(CERTIFICATE_DOCUMENT_PARAM, certificate);
    additionalParams.put(ALIAS_PARAM, alias);
    AddCertificatePropertiesVisitor addCertificatePropertiesVisitor = new AddCertificatePropertiesVisitor();
    params.accept(addCertificatePropertiesVisitor, result, additionalParams);

    // add DocumentFile
    AddDocumentFileVisitor addDocumentFileVisitor = new AddDocumentFileVisitor(this);
    certificate = params.accept(addDocumentFileVisitor, result, additionalParams);
    if (result.isError()) {
      return result;
    }

    // save certificate Document
    documentService.saveDocument(certificate);

    return result;
  }

  /*
   * -- SEARCH --
   */

  /** @see com.byzaneo.generix.service.KeyStoreService#getKeyStoreDocuments(com.byzaneo.xtrade.bean.Document) */
  @Override
  @Transactional
  public List<Document> getKeyStoreDocuments(Document keyStoreRootFolder) {
    if (keyStoreRootFolder == null || keyStoreRootFolder.getChildCount() == 0) {
      return emptyList();
    }

    // get the certificate families, denoted by a common keyStoreFriendlyName
    // 1. get the children of the keyStoreFolder
    Set<Document> children = keyStoreRootFolder.getChildren();

    // 2.this predicate will filter the Documents that represent a certificate family
    Predicate<Object> certificateParentPredicate = object -> {
      if (!(object instanceof Document)) {
        return false;
      }
      Document curDocument = (Document) object;
      String type = curDocument.getType();
      return CERTIFICATE_FAMILY_PARENT_TYPE.equals(type)/* || "PKCS11".equals(type) */;
    };

    Set<Document> matchingParents = new HashSet<>();
    select(children, certificateParentPredicate, matchingParents);

    // 3. for every family, get the certificate Documents
    List<Document> r = new ArrayList<>();
    List<Date> dates = new ArrayList<>();
    List<Integer> filesSizes = new ArrayList<>();
    for (Document parent : matchingParents) {
      // keystores.add(curParent);
      r.addAll(parent.getChildren());
      // activate the lazy loading of the properties
      // these lists are meant to create a side effect using the loaded infos,
      // so that the JVM does not skip this code and the effective loading with it (uncertain if the loading itself is sufficient)
      for (Document d : parent.getChildren()) {
        d.getFiles()
            .size();
      }
      dates.add(getNotNullModelProperty(parent, KEYSTORE_ENTRY_NOT_BEFORE));
      filesSizes.add(parent.getFiles()
          .size());
    }
    log.debug("size of dates : " + dates.size() + " size of kids : " + filesSizes.size());

    return r;
  }

  /** @see com.byzaneo.generix.service.KeyStoreService#getRootSSLFolder(java.lang.String, boolean) */
  @Override
  @Transactional
  public Document getRootSSLFolder(String organizationId, boolean persistIfNotFound) {
    Document r = this.documentService.getRootFolder(organizationId, DOCUMENT_SSL_ROOT_REFERENCE, false);
    if (r == null) {
      r = new Document(DOCUMENT_SSL_ROOT_REFERENCE, organizationId, FOLDER.toString(), null, null, NONE);
      if (persistIfNotFound) {
        r = this.documentService.saveDocument(r);
      }
    }
    return r;
  }

  /** @see com.byzaneo.generix.service.KeyStoreService#searchCertificates(com.byzaneo.security.bean.Organization) */
  @Override
  @Transactional
  public List<DocumentFile> searchCertificates(Organization organization) {
    final List<Document> families = this.searchKeyStoreFamiliesByOrganization(organization, true);
    return families.stream()
        .flatMap(family -> family.getChildren()
            .stream())
        .filter(ks -> (JKS.toString()
            .equals(ks.getType()) ||
            PKCS12.toString()
                .equals(ks.getType())) &&
            ks.getFiles()
                .size() == 1)
        .map(Document::getFirstDocumentFile)
        .collect(toList());
  }

  /** @see com.byzaneo.generix.service.KeyStoreService#searchKeyStoreFamiliesByOrganization(Organization, boolean) */
  @Override
  @Transactional
  public List<Document> searchKeyStoreFamiliesByOrganization(Organization organization, boolean rsaOnly) {
    return this.searchKeyStoreFamiliesByOrganization(organization, rsaOnly, null);
  }

  /**
   * @see com.byzaneo.generix.service.KeyStoreService#searchKeyStoreFamiliesByOrganization(com.byzaneo.security.bean.Organization, boolean,
   *     com.byzaneo.generix.service.KeyStoreService.UsageType)
   */
  @Override
  @Transactional(readOnly = true)
  public List<Document> searchKeyStoreFamiliesByOrganization(Organization organization, boolean rsaOnly, UsageType usage) {
    return searchKeyStoreFamiliesByOrganization(organization, rsaOnly, usage, null);
  }

  /**
   * @see com.byzaneo.generix.service.KeyStoreService#searchKeyStoreFamiliesByOrganization(com.byzaneo.security.bean.Organization, boolean,
   *     com.byzaneo.generix.service.KeyStoreService.UsageType)
   */
  @Override
  @Transactional(readOnly = true)
  public List<Document> searchKeyStoreFamiliesByOrganization(Organization organization, boolean rsaOnly, UsageType usage,
      final Consumer<Document> action) {
    if (organization == null) {
      return emptyList();
    }
    Document keyStoreFolder = this.getRootSSLFolder(organization.getId(), false);
    Set<Document> children = keyStoreFolder.getChildren();
    Set<Document> families = children == null ? new HashSet<>() : children;
    if (rsaOnly) {
      families = new HashSet<>();
      for (Document child : children) {
        // child is the parent of a certificate family
        Set<Document> certificates = child.getChildren();// the actual certificates
        if (!filterRsa(certificates).isEmpty()) {
          families.add(child);

          if (action != null) {
            action.accept(child);
          }
        }
      }
    }

    if (usage != null) {
      Set<Document> filtered = new HashSet<>();
      // XXX not sure its the right document to check for usage property --> [npi] yes it is, usage is set on parent during parent creation
      for (Document family : families) {
        if (usage.toString()
            .equals(getNotNullModelProperty(family, KEYSTORE_USAGE))) {
          filtered.add(family);
        }

        if (action != null) {
          action.accept(family);
        }
      }
      families = filtered;
    }

    List<Document> r = new ArrayList<>(families);
    sort(r);
    return r;
  }

  /*
   * -- JKS --
   */

  /** @see com.byzaneo.generix.service.KeyStoreService#getJavaKeyStore(com.byzaneo.xtrade.bean.Document) */
  @Override
  public KeyStoreWrapper getJavaKeyStore(Document ksDocument) {
    KeyStoreWrapper ret = new KeyStoreWrapper();
    if (ksDocument == null) {
      return ret;
    }
    try {
      String type = ksDocument.getType();
      KeyStoreType keyStoreType = KeyStoreType.valueOf(type);
      String keyStorePassword = getKeyStorePassword(ksDocument);
      File keyStoreFile = ksDocument.getFirstFile();// NB : this File can be null
      ToUploadKeyStoreParamsResult uploadKeyStoreParams = toUploadKeyStoreParams(ksDocument, keyStoreFile, keyStoreType, keyStorePassword);
      AbstractKeyStoreFile params = uploadKeyStoreParams.getParams();
      Map<String, Object> additionalParams = uploadKeyStoreParams.getAdditionalParams();
      ToKeyStoreVisitor toKeyStoreVisitor = new ToKeyStoreVisitor(this);
      ToKeyStoreResult toKeyStoreResult = params.accept(toKeyStoreVisitor,
          new KeyStoreFileResult(),
          additionalParams);
      KeyStore jKeyStore = toKeyStoreResult.getKeyStore();
      ret.setKeyStore(jKeyStore);
      if (params instanceof Pkcs11KeyStoreFile) {
        ret.setCertificate(((Pkcs11KeyStoreFile) params).getCert());
      }
    }
    catch (Exception e) {
      log.error("KeyStoreServiceImpl.getJavaKeyStore() - failure for Document id: " + ksDocument.getId(), e);
      ret.setLoadException(e);
    }
    return ret;
  }

  /** @see com.byzaneo.generix.service.KeyStoreService#getKeyStoreEntry(java.security.KeyStore, java.lang.String) */
  @Override
  public X509Certificate getKeyStoreEntry(KeyStore keyStore, String entryAlias) {
    if (keyStore == null) {
      return null;
    }
    try {
      Certificate cert = keyStore.getCertificate(entryAlias);
      if (cert != null && cert instanceof X509Certificate) {
        return (X509Certificate) cert;
      }
    }
    catch (KeyStoreException e) {
      // ignored keyStore has been loaded before
      log.error("Unexpected KeyStoreException : ", e);
      return null;
    }
    return null;
  }

  /*
   * -- CERTIFICATE MANAGEMENT --
   */

  /** @see com.byzaneo.generix.service.KeyStoreService#ensurePkcs11Provider(java.lang.String, java.lang.Integer, java.lang.String) */
  @Override
  public Provider ensurePkcs11Provider(String keyStoreLibrary, Integer keyStoreSlotIndex, String keyStorePassword)
      throws ClassNotFoundException, NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
    String pkcs11ConfigSettings = buildPkcs11ConfigSettings(keyStoreLibrary, keyStoreSlotIndex);
    String cacheKey = makeCacheKey(keyStorePassword, pkcs11ConfigSettings);
    if (pkcs11ProviderHolder.containsKey(cacheKey)) {
      Provider pkcs11Provider = pkcs11ProviderHolder.get(cacheKey);
      // verify pincode
      checkPkcs11ProviderLogin(pkcs11Provider, keyStorePassword);
      return pkcs11Provider;
    }
    // no provider in cache for this configuration, build and register a new one
    byte[] pkcs11ConfigBytes = pkcs11ConfigSettings.getBytes();
    ByteArrayInputStream confStream = new ByteArrayInputStream(pkcs11ConfigBytes);
    @SuppressWarnings("unchecked")
    Class<? extends Provider> pkcs11ProviderClass = (Class<? extends Provider>) Class.forName("sun.security.pkcs11.SunPKCS11");
    Constructor<? extends Provider> constructor = pkcs11ProviderClass.getConstructor(InputStream.class);
    Provider pkcs11Provider = constructor.newInstance(confStream);
    Security.addProvider(pkcs11Provider);
    pkcs11ProviderHolder.put(cacheKey, pkcs11Provider);

    // verify pincode
    checkPkcs11ProviderLogin(pkcs11Provider, keyStorePassword);

    return pkcs11Provider;
  }

  @Override
  public X509Certificate checkPKCS11LoginToHsmAndGetValidCertificate(String libraryPath, String password,
      int slotIndex, String label) throws PKCS11KeyHandlerException {
    CLibrary lib = null;
    X509Certificate x509Certificate = null;
    Map<String, NativeLong> sessionVariables = new HashMap<>();
    try {
      addConnectionToLibrary(libraryPath);
      lib = initializeJnaLibrary(libraryPath);

      getSlotIdFromIndex(lib, slotIndex, sessionVariables);

      openNewSessionForSlotId(lib, sessionVariables);

      sessionLogin(lib, password, sessionVariables);

      intializeSearchForCertificateInsideSession(lib, label, sessionVariables);

      x509Certificate = getCertificateFromSession(lib, sessionVariables);
    }
    catch (PKCS11KeyHandlerException e) {
      log.error(e.getMessage());
    }
    finally {
      finalizeLibraryIfLastConnection(lib, libraryPath);
    }
    return x509Certificate;
  }

  @SuppressWarnings("squid:S1226")
  @Override
  public Map<String, Document> mapFriendlyNameToKeyStore(Collection<Document> keyStores) {
    keyStores = keyStores == null ? new HashSet<>() : keyStores;
    return keyStores.stream()
        .collect(toMap(
            Document::getReference,
            identity()));
  }

  @Override
  public String getCertificatePublicKey(Document keyStoreDocument) {
    KeyStoreWrapper keyStoreWrapper = getJavaKeyStore(keyStoreDocument);
    KeyStore jsKeystore = keyStoreWrapper.getKeyStore();
    X509Certificate keyStoreEntry = getKeyStoreEntry(jsKeystore,
        keyStoreDocument.getNotNullModel()
            .getPropertyValue(KeyStoreService.KEYSTORE_ENTRY_ALIAS));
    if (keyStoreEntry == null && keyStoreWrapper.getCertificate() != null) {
      // PKCS11 the certificate is stored inside the wrapper
      keyStoreEntry = keyStoreWrapper.getCertificate();
    }
    try {
      return new Base64(64).encodeToString(keyStoreEntry.getEncoded());
    }
    catch (CertificateEncodingException e) {
      log.error("getCertificatePublicKey() - error ", e);
      return null;
    }
  }

  @Override
  public X509Certificate getX509CertificatePublicKey(Document keyStoreDocument) {
    KeyStoreWrapper keyStoreWrapper = getJavaKeyStore(keyStoreDocument);
    KeyStore jsKeystore = keyStoreWrapper.getKeyStore();
    return getKeyStoreEntry(jsKeystore,
        keyStoreDocument.getNotNullModel()
            .getPropertyValue(KeyStoreService.KEYSTORE_ENTRY_ALIAS));
  }

  /*
   * -- PRIVATES --
   */

  /**
   * convenience method, return the size fo a KeyStore without declaring a checked KeyStoreException (wraps it in a RuntimeException).
   *
   * @param keyStore - should not be null otherwise NullPointerException
   * @return
   */
  int getKeyStoreSize(KeyStore keyStore) {
    try {
      return keyStore.size();
    }
    catch (Exception e) {
      // no KeyStoreException should be thrown if the KeyStore has already been loaded
      throw new RuntimeException(e);
    }
  }

  /**
   * get or create if it does not exist the parent document for a certificate "family", ie the certificates sharing a common "friendly
   * name". These certificates are stored as DocumentFiles under this Document parent.
   *
   * @param keyStoreFriendlyName
   * @param keyStoreFolder
   * @param usage
   * @return
   */
  Document getOrCreateParent(final String keyStoreFriendlyName, Document keyStoreFolder, UsageType usage)
      throws MultipleParentsFoundException {
    Set<Document> children = keyStoreFolder.getChildren();
    children = children == null ? new HashSet<>() : children;

    // Predicate to find the parent Document for the given friendly name
    Predicate<Object> fnDocumentPredicate = object -> {
      if (!(object instanceof Document)) {
        return false;
      }
      Document curDocument = (Document) object;
      String curFriendlyName = curDocument.getReference();
      return keyStoreFriendlyName.equals(curFriendlyName);
    };

    Set<Document> matchingParents = new HashSet<>();
    select(children, fnDocumentPredicate, matchingParents);

    int matchingParentsSize = matchingParents.size();
    if (matchingParentsSize == 1) {
      // nominal case
      return matchingParents.iterator()
          .next();
    }

    if (matchingParentsSize > 1) {
      // inconsistent state
      throw new MultipleParentsFoundException();
    }

    if (matchingParentsSize == 0) {
      // no certificate yet for this family, create the parent doc
      return createParent(keyStoreFriendlyName, keyStoreFolder, usage);
    }

    return null;
  }

  /**
   * create and save the parent document for the certificate family denoted by keyStoreFriendlyName
   *
   * @param keyStoreFriendlyName
   * @param keyStoreFolder
   * @param usage
   * @return
   */
  Document createParent(String keyStoreFriendlyName, Document keyStoreFolder, UsageType usage) {
    Document keyStoreDoc = new Document();
    keyStoreDoc.setReference(keyStoreFriendlyName);
    keyStoreDoc.setOwners(KEYSTORE_OWNERS);
    keyStoreDoc.setType(CERTIFICATE_FAMILY_PARENT_TYPE);

    // usage
    if (usage != null) {
      keyStoreDoc.addModel(KEYSTORE_USAGE, Enum, null, usage);
    }

    keyStoreDoc.setParent(keyStoreFolder);
    keyStoreFolder.addChild(keyStoreDoc);

    // save document
    keyStoreDoc = documentService.saveDocument(keyStoreDoc);// do not save now, bug

    return keyStoreDoc;
  }

  /**
   * Build the configuration for a pkcs11 token
   *
   * @param keyStoreLibrary
   * @param keyStoreSlotIndex
   * @return
   */
  String buildPkcs11ConfigSettings(String keyStoreLibrary, Integer keyStoreSlotIndex) {
    return "name = SmartCard" + System.lineSeparator() + "library = " + keyStoreLibrary + System.lineSeparator() + "slotListIndex = " +
        keyStoreSlotIndex;
  }

  /**
   * key for the cache of pkcs11 provider. key is a hash of token password and token configuration
   *
   * @return
   */
  String makeCacheKey(String password, String pkcs11ConfigSettings) {
    StringBuilder key = password == null ? new StringBuilder() : new StringBuilder(password);
    key.append(pkcs11ConfigSettings == null ? "" : pkcs11ConfigSettings);
    return key.toString();
  }

  /**
   * Check that the provided password is correct for pkcs11Provider. This operation performs a logout/login, hence simultaneous use of the
   * provider for other purposes may result in errors.
   *
   * @param pkcs11Provider
   * @param keyStorePassword
   */
  void checkPkcs11ProviderLogin(Provider pkcs11Provider, final String keyStorePassword) {
    AuthProvider authProvider = (AuthProvider) pkcs11Provider;
    try {
      // perform login/out so as to check pwd
      CallbackHandler callbackHandler = callbacks -> {
        for (Callback c : callbacks) {
          if (c instanceof PasswordCallback) {
            ((PasswordCallback) c).setPassword(keyStorePassword.toCharArray());
            return;
          }
        }
        throw new RuntimeException("No password callback");
      };
      authProvider.logout();
      authProvider.login(null, callbackHandler);
    }
    catch (LoginException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * return the plaintext password for the given keystore Document
   *
   * @param ksDocument
   * @return
   */
  @Override
  public String getKeyStorePassword(Document ksDocument) {
    String encodedPassword = ksDocument.getFirstDocumentFile()
        .getActionName();
    return encodedPassword == null ? "" : AESEncryptionHelper.decrypt(encodedPassword);
  }

  /**
   * build a UploadKeyStoreParams matching the given parameters
   *
   * @param ksDocument
   * @param keyStoreFile
   * @param keyStoreType
   * @param keyStorePassword
   * @return
   */
  ToUploadKeyStoreParamsResult toUploadKeyStoreParams(Document ksDocument, File keyStoreFile, KeyStoreType keyStoreType,
      String keyStorePassword) {
    ToUploadKeyStoreParamsResult ret = new ToUploadKeyStoreParamsResult();
    Map<String, Object> additionalParams = new HashMap<>();
    additionalParams.put(KeyStoreFileVisitor.KEYSTORE_TYPE_PARAM, keyStoreType);
    ret.setAdditionalParams(additionalParams);
    switch (keyStoreType) {
    case PKCS11: {
      Pkcs11KeyStoreFile params = toUploadPkcs11KeyStoreParams(ksDocument, keyStorePassword);
      ret.setParams(params);
      return ret;
    }
    case P7B:
    case CRT:
    case CER:
    case PEM:
    case PKCS12:
    case JKS: {
      KeyStoreFile params = toUploadFileKeyStoreParams(keyStoreFile, keyStorePassword);
      ret.setParams(params);
      return ret;
    }
    default: {
      log.error("KeyStoreServiceImpl.toUploadKeyStoreParams() - unexpected keystore type : " + keyStoreType);
      throw new IllegalArgumentException("unexpected keystore type : " + keyStoreType);
    }
    }
  }

  KeyStoreFile toUploadFileKeyStoreParams(File keyStoreFile, String keyStorePassword) {
    KeyStoreFile params = new KeyStoreFile();
    params.setInputFilePath(keyStoreFile.getAbsolutePath());
    params.setPassword(keyStorePassword);
    return params;
  }

  Pkcs11KeyStoreFile toUploadPkcs11KeyStoreParams(Document ksDocument, String keyStorePassword) {
    Pkcs11KeyStoreFile params = new Pkcs11KeyStoreFile();
    params.setLibrary(ksDocument.getNotNullModel()
        .getPropertyValue(PKCS11_LIBRARY));
    params.setSlotIndex(new Integer(ksDocument.getNotNullModel()
        .getPropertyValue(PKCS11_SLOT_INDEX)));
    params.setLabel(ksDocument.getNotNullModel()
        .getPropertyValue(KEYSTORE_ENTRY_ALIAS));
    params.setPassword(keyStorePassword);
    return params;
  }

  /*
   * -- INNERS --
   */

  static class ToUploadKeyStoreParamsResult {
    private AbstractKeyStoreFile params;

    private Map<String, Object> additionalParams = new HashMap<>();

    public AbstractKeyStoreFile getParams() {
      return params;
    }

    public void setParams(AbstractKeyStoreFile params) {
      this.params = params;
    }

    public Map<String, Object> getAdditionalParams() {
      return additionalParams;
    }

    public void setAdditionalParams(Map<String, Object> additionalParams) {
      this.additionalParams = additionalParams;
    }
  }

  /**
   * Given a key with '@' separator, return the rightmost part
   *
   * @param ksKey
   * @return
   */
  @Override
  public String toKSFamilyName(String ksKey) {
    if (ksKey == null) {
      return null;
    }
    String[] parts = ksKey.split("@");
    if (parts.length < 2) {
      return ksKey;
    }
    return parts[parts.length - 1];
  }

  void setSecurityService(SecurityService securityService) {
    this.securityService = securityService;
  }

  void setDocumentService(DocumentService documentService) {
    this.documentService = documentService;
  }

  @Override
  public Set<Document> searchKeyStoresByOrganizationAndFriendlyName(Organization organization, String friendlyName) {
    Set<Document> ret = new HashSet<>();
    if (organization == null || friendlyName == null) {
      return ret;
    }
    List<Document> keyStoreFamilies = searchKeyStoreFamiliesByOrganization(organization, false, null);
    keyStoreFamilies = keyStoreFamilies == null ? new ArrayList<>() : keyStoreFamilies;
    keyStoreFamilies.stream()
        .filter(family -> friendlyName.equals(family.getReference()))
        .forEach(family -> ret.addAll(family.getChildren()));
    return ret;
  }
}
