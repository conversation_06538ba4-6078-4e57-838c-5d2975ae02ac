package com.byzaneo.generix.bean.chorus.service;

import java.io.Serializable;

public class Parametres implements Serializable {

  private String dateCreation;

  private String dateDebutValidite;

  private boolean numeroEngagement;

  // Getter Methods

  public String getDateCreation() {
    return dateCreation;
  }

  public String getDateDebutValidite() {
    return dateDebutValidite;
  }

  public boolean getNumeroEngagement() {
    return numeroEngagement;
  }

  // Setter Methods

  public void setDateCreation(String dateCreation) {
    this.dateCreation = dateCreation;
  }

  public void setDateDebutValidite(String dateDebutValidite) {
    this.dateDebutValidite = dateDebutValidite;
  }

  public void setNumeroEngagement(boolean numeroEngagement) {
    this.numeroEngagement = numeroEngagement;
  }

}
